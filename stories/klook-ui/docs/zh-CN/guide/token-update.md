# Design Token更新日志

## 1.38.3 版本更新

背景：https://klook.larksuite.com/docx/VqNEdUHm1oomzuxSxaHubLOKsse

```所有更新的token，在原有token的名字上附加v2后缀命名```

:::demo
```html
<div>
  <div v-for="(item, itemIndex) in changeList" :key="itemIndex">
    <h3>{{ item.name }}</h3>
    <div class="token-update-item" key="header" style="margin-bottom: 16px;padding:0">
      <h4>Token</h4>
      <h4>Now</h4>
      <h4>Before</h4>
    </div>
    <div v-for="(subItem, subItemIndex) in item.list" :key="subItemIndex" class="token-update-item">
      <klk-badge :value="subItem.before ? 'Update' : 'New'" :color="subItem.before ? '' : '#16aa77'">
        <span style="padding: 0 12px">{{ subItem.value }}</span>
      </klk-badge>
      <span :class="`token-${subItem.value.slice(1)}`">{{ subItem.desc  }} <br/> {{ subItem.desc }}</span>
      <span :class="`token-${subItem.before ? subItem.before.slice(1) : ''}`">
        {{ subItem.before ? subItem.desc : '-'  }}<br/>{{ subItem.before ? subItem.desc : ''  }}
      </span>
    </div>
  </div>
</div>
```
:::

<script>
export default {
  name: 'TokenUpdateOne',
  data() {
    const changeList = [
      {
        name: 'Heading更新',
        list: [
          { type: 'token', value: '$fontSize-heading-xs-v2', desc: 'Heading标题', before: '$fontSize-heading-xs' },
          { type: 'token', value: '$fontSize-heading-xxs', desc: 'Heading标题' },
          { type: 'token', value: '$fontSize-heading-xxxs', desc: 'Heading标题' },
          { type: 'token', value: '$fontSize-heading-xxxxs', desc: 'Heading标题' },
          { type: 'mixin', value: '$font-heading-xs-v2', before: '$font-heading-xs', desc: '楼层内部标题' },
          { type: 'mixin', value: '$font-heading-xxs', before: '', desc: '楼层内部标题' },
          { type: 'mixin', value: '$font-heading-xxxs', before: '', desc: '楼层内部标题' },
          { type: 'mixin', value: '$font-heading-xxxxs', before: '', desc: '楼层内部标题' }
        ]
      },
      {
        name: 'Body更新',
        list: [
          { type: 'token', value: '$fontSize-body-xs', desc: 'Heading标题' },
          { type: 'mixin', value: '$font-body-m-bold-v2', before: '$font-body-m-bold', desc: '突出正文' },
          { type: 'mixin', value: '$font-body-m-regular-v2', before: '$font-body-m-regular', desc: '标准正文' },
          { type: 'mixin', value: '$font-body-s-bold-v2', before: '$font-body-s-bold', desc: '突出的次级正文' },
          { type: 'mixin', value: '$font-body-s-regular-v2', before: '$font-body-s-regular', desc: '突出的次级正文' },
          { type: 'mixin', value: '$font-body-xs-bold', before: '', desc: '再次级正文突出' },
          { type: 'mixin', value: '$font-body-xs-regular', before: '', desc: '再次级正文' }
        ]
      },
      {
        name: '新增段落文本',
        list: [
          { type: 'token', value: '$fontSize-paragraph-m', before: '', desc: '段落文本' },
          { type: 'token', value: '$fontSize-paragraph-s', before: '', desc: '段落文本' },
          { type: 'token', value: '$fontSize-paragraph-xs', before: '', desc: '段落文本' },
          { type: 'mixin', value: '$font-paragraph-xs-bold', before: '', desc: '突出的正文' },
          { type: 'mixin', value: '$font-paragraph-xs-regular', before: '', desc: '正文' },
          { type: 'mixin', value: '$font-paragraph-m-bold', before: '', desc: '突出的标准正文' },
          { type: 'mixin', value: '$font-paragraph-m-regular', before: '', desc: '标准正文' },
          { type: 'mixin', value: '$font-paragraph-s-bold', before: '', desc: '突出的次级正文' },
          { type: 'mixin', value: '$font-paragraph-s-regular', before: '', desc: '次级正文' }
        ]
      }
    ]
    return { changeList: changeList }
  }
}
</script>

<style lang="scss" scoped>
h3 {
  margin-bottom: 16px
}

.token-update-item {
  width: 100%;
  display: flex;
  align-items: center;
  padding: 24px 0;
  text-align: center;

  h4 {
    padding: 0;
    margin: 0;
  }

  .klk-badge, h4:first-child {
    flex: 0 0 30%;
    margin-right: 50px;
  }

  span, h4 {
    flex: 1 1 25%;
  }
}
.token-fontSize-heading-xs {
  font-size: $fontSize-heading-xs;
}

.token-fontSize-heading-xs-v2 {
  font-size: $fontSize-heading-xs-v2;
}
.token-fontSize-heading-xxs {
  font-size: $fontSize-heading-xxs;
}
.token-fontSize-heading-xxxs {
  font-size: $fontSize-heading-xxxs;
}
.token-fontSize-heading-xxxxs {
  font-size: $fontSize-heading-xxxxs;
}
.token-fontSize-body-xs {
  font-size: $fontSize-body-xs;
}
.token-fontSize-paragraph-m {
  font-size: $fontSize-paragraph-m;
}
.token-fontSize-paragraph-s {
  font-size: $fontSize-paragraph-s;
}
.token-fontSize-paragraph-xs {
  font-size: $fontSize-paragraph-xs;
}

.token-font-heading-xs-v2 {
  @include font-heading-xs-v2;
}

.token-font-heading-xs {
  @include font-heading-xs;
}

.token-font-heading-xxs {
  @include font-heading-xxs;
}
.token-font-heading-xxxs {
  @include font-heading-xxxs;
}
.token-font-heading-xxxxs {
  @include font-heading-xxxxs;
}
.token-font-body-m-bold-v2 {
  @include font-body-m-bold-v2;
}
.token-font-body-m-bold {
  @include font-body-m-bold;
}
.token-font-body-m-regular-v2 {
  @include font-body-m-regular-v2;
}
.token-font-body-m-regular {
  @include font-body-m-regular;
}
.token-font-body-s-regular-v2 {
  @include font-body-s-regular-v2;
}
.token-font-body-s-regular {
  @include font-body-s-regular;
}

.token-font-body-s-bold-v2 {
  @include font-body-s-bold-v2;
}
.token-font-body-s-bold {
  @include font-body-s-bold;
}
.token-font-body-xs-bold {
  @include font-body-xs-bold;
}
.token-font-body-xs-regular {
  @include font-body-xs-regular;
}
.token-font-paragraph-xs-bold {
  @include font-paragraph-xs-bold;
}
.token-font-paragraph-xs-regular {
  @include font-paragraph-xs-regular;
}
.token-font-paragraph-m-bold {
  @include font-paragraph-m-bold;
}
.token-font-paragraph-m-regular {
  @include font-paragraph-m-regular;
}
.token-font-paragraph-s-bold {
  @include font-paragraph-s-bold;
}
.token-font-paragraph-s-regular {
  @include font-paragraph-s-regular;
}
</style>


