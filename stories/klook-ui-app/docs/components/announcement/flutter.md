# announcement 通知公告组件



## 设计稿
- [Figma 组件设计稿](https://www.figma.com/file/XMouFNlF64wUIuSk4YnLyi/2024Q1-Klook-ui-%E7%BB%84%E4%BB%B6%E4%BC%98%E5%8C%96?type=design&node-id=573-12185&mode=design&t=8yo1LUK6VMSnlPok-0)


## 效果图
![](../../assets/announcement/flutter/announcement_01.jpg)


## 可配置项：
1. [announcementDataList] - 公告数据
2. [maxLines] - 最大显示行数 1,2,3

## 用法

```dart
 Announcement(
  announcementDataList: [
    AnnouncementData(
      content: '公告内容',
      onClicked: () {},
    ),
  ],
  maxLines: 2,
 )
```


## 属性

Announcement
| 参数名 & 类型 & 是否可选                          | 参数说明 |
| --------------------------------------------   | --------|
| announcementDataList\<List\<AnnouncementData>> |  公告列表数据 |         
| maxLines\<int>                                 |  最多显示行数： 1,2,3 ；默认1行 |    

AnnouncementData
| 参数名 & 类型 & 是否可选                          | 参数说明 |
| --------------------------------------------   | --------|
| content\<String>                               |  公告内容 |         
| onClicked\<VoidCallback?>                      |  点击回调。 如果为 null，组件显示该公告内容时，不显示箭头指引 |    


```
开发者信息：

* 作者：jie.li
* 邮箱：<EMAIL>
```