# Skeleton 骨架图

统一配置了公共的默认颜色和高亮颜色

## 设计稿
[Figma 组件设计稿](https://www.figma.com/file/5kU1ysjfp0pOKhU1pqNcWh/Slider_%E6%BB%91%E5%9D%97?node-id=1%3A36)


## 效果图
![](../../assets/skeleton/flutter/skeleton_01.jpg)
![](../../assets/skeleton/flutter/skeleton_02.jpg)
![](../../assets/skeleton/flutter/skeleton_03.jpg)


## 用法

### 简单用法【指定宽高】

```dart
Skeleton(children: [
    Padding(padding: EdgeInsets.all(8)),
    SkeletonItem.block(
      width: 200,
      height: 100,
    ),
    Padding(padding: EdgeInsets.all(8)),
    SkeletonItem.circle(
      radius: 80,
    )
])
```

### 简单用法【指定高度】

```dart
Skeleton(children: [
    Padding(padding: EdgeInsets.all(8)),
    SkeletonItem.block(
      height: 100,
    ),
    Padding(padding: EdgeInsets.all(8)),
    SkeletonItem.circle(
      radius: 80,
    )
])
```

### 复杂用法
```dart
Skeleton(children: [
      const SkeletonItem.block(
        height: 188,
      ),
      const SizedBox(height: 14),
      const SkeletonItem.block(height: 18),
      const SizedBox(height: 14),
      SkeletonItem.block(
        height: 18,
        width: _ScreenUtil.width * 0.7,
      ),
      const SizedBox(height: 14),
      SkeletonItem.block(
        height: 18,
        width: _ScreenUtil.width * 0.7,
      ),
])
```

## 属性

Skeleton
| 参数名 & 类型 & 是否可选  | 参数说明 |
| ---------------------- | --------|
| animated\<bool> | 是否开启动画，默认`true` |         
| color\<Color?> | 动画颜色 |
| children\<List\<Widget>> | 内容，一般是内容组件[SkeletonItem]或者占位组件[SizedBox]、[Padding] |

```
开发者信息：

* 作者：rongan.lin
* 邮箱：<EMAIL>
```