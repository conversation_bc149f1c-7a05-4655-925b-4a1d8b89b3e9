# DatetimePicker 日期时间选择器

通过滚动进行选择的列表选择器，时间选择支持日期、年月、时分等维度的选择。同时可以对每个维度的滚动选项进行过滤

## 设计稿
[Figma 组件设计稿](https://www.figma.com/file/7MciDt7QiRk759PqbR576T/Datetime-Picker_%E6%97%B6%E9%97%B4%E9%80%89%E6%8B%A9%E5%99%A8?node-id=198%3A2092)


## 效果图
完整日期时间选择
![](../../assets/datetime_picker/flutter/datetime_picker_01.jpg)

日期选择
![](../../assets/datetime_picker/flutter/datetime_picker_02.jpg)

年份选择
![](../../assets/datetime_picker/flutter/datetime_picker_03.jpg)

年月选择
![](../../assets/datetime_picker/flutter/datetime_picker_04.jpg)

年月日小时选择
![](../../assets/datetime_picker/flutter/datetime_picker_05.jpg)

时间选择
![](../../assets/datetime_picker/flutter/datetime_picker_06.jpg)

日期选择过滤
![](../../assets/datetime_picker/flutter/datetime_picker_07.jpg)

日期选择-加格式化
![](../../assets/datetime_picker/flutter/datetime_picker_08.jpg)

## 用法

### 完整日期时间选择
```dart
DateTime? selectedDate =
  await showDateTimePicker(
    context: context,
    initialDate: DateTime(2021, 01, 01, 0, 0),
    firstDate: DateTime(2021, 01, 01, 0, 0).subtract(const Duration(days: 365 * 2)),
    lastDate: DateTime(2021, 01, 01, 0, 0).add(const Duration(days: 365 * 4)),
    confirmText: "确认",
    titleText: "完整日期时间选择",
    componentProvider: DateTimeComponents.dateTime());
```

### 完整日期时间选择-分钟过滤
```dart
DateTime? selectedDate =
  await showDateTimePicker(
    context: context,
    initialDate: DateTime(2021, 01, 01, 0, 0),
    firstDate: DateTime(2021, 01, 01, 0, 0).subtract(const Duration(days: 365 * 2)),
    lastDate: DateTime(2021, 01, 01, 0, 0).add(const Duration(days: 365 * 4)),
    confirmText: "确认",
    titleText: "完整日期时间选择-分钟过滤",
    componentProvider: DateTimeComponents.dateTime(),
    predicate: (DateTimeComponent component, List<int> items) {
       if (component is Minute) {
         return items.where((element) => element % 5 == 0).toList();
       }
       return items;
    },
 );
```

## 属性

<!--  介绍Widget的所有属性，描述其作用，以及使用时的注意事项等等。  -->

| 参数名 & 类型 & 是否可选  | 参数说明 |
| ---------------------- | --------|
| initialDate\<DateTime> | 初始日期 |         
| firstDate\<DateTime> | 最小日期 |
| lastDate\<DateTime> | 最大日期 |
| predicate\<DateTimeComponentPredicate?> | 可选日期过滤|
| formatter\<DateTimeComponentFormatter> | 格式化|
| titleText\<String> | 标题文字 |
| components\<List\<DateTimeComponent>> | 日期components，有: 年、月、日、时、分 |
| confirmText\<String> | 确认标题 |



```
开发者信息：

* 作者：rongan.lin
* 邮箱：<EMAIL>
```