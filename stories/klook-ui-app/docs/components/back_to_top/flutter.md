# BackToTop 页面滚动到顶部组件




## 组件相关文档
- [Figma 组件设计稿](https://www.figma.com/file/UozSTc0DrLD1anFqddZlXg/BackToTop-%E5%9B%9E%E5%88%B0%E9%A1%B6%E9%83%A8?node-id=4-17&t=gzRCZf8ZjzOiMsQs-0)
- [PRD](https://klook.larksuite.com/docx/No2ddCd0yoNGZcxxR6LufPA8s3e)


## 效果图
![](../../assets/back_to_top/flutter/back_to_top_01.jpg)

## 用法

### 通过 BackToTopContainer
> 对 [BackToTop] 的封装，内置了一个 [Stack] 、[BackToTop]，可以通过 [properties] 来配置 [BackToTop] 的属性这个时候，[BackToTop] 属性的参照物是滚动视图，如果参照物是其它视图（比如：屏幕），则需要使用 [BackToTop] 组件 [scrollBuilder] 滚动组件构建器 [properties] [BackToTop] 配置项


使用Demo如下
```dart
class BackToTopDemo extends StatefulWidget {
  const BackToTopDemo({Key? key}) : super(key: key);

  @override
  State<BackToTopDemo> createState() => _BackToTopDemoState();
}

class _BackToTopDemoState extends State<BackToTopDemo> {
  final ScrollController _scrollController = ScrollController();

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: BackToTopContainer(
        scrollBuilder: (BuildContext context, BackToTopProperties properties) {
          return ListView.builder(
              controller: _scrollController,
              itemBuilder: (context, index) {
                return Container(
                    height: 100,
                    color: Colors.primaries[index % Colors.primaries.length],
                    child: Center(
                      child: Text('$index'),
                    ));
              },
              itemCount: 100);
        },
        onTap: () {
          // 点击回调
          _scrollController.animateTo(0,
              duration: const Duration(milliseconds: 300), curve: Curves.ease);
        },
      ),
    );
  }
}
```

### 通过 BackToTopButton

BackToTopButton 为纯 UI 视图，不带任何逻辑，内部icon默认大小：24x24，默认 padding：EdgeInsets.all(12)

```dart
  GestureDetector(
      onTap: (){ 
        // do something
       },
      child: const BackToTopButton(),
    );
```

## 属性

BackToTopContainer
| 参数名 & 类型 & 是否可选  | 参数说明 |
| ---------------------- | --------|
| properties\<BackToTopProperties> | [BackToTop] 配置项 |         
| scrollBuilder\<ScrollBuilder> | 滚动组件构建器 |
| onTap\<GestureTapCallback?> | 点击事件，如果需要实现点击回到顶部的功能，需要实现此方法 |


BackToTopProperties
| 参数名 & 类型 & 是否可选  | 参数说明 |
| ---------------------- | --------|
| showHeight\<double> | 滚动时触发显示该组件的高度(offset)，默认值：1700 |         
| fromTheBottom\<double> | 距离底部的值，默认值：48 |
| fromTheSide\<double?> | 距离侧边的距离（在 LTR 环境，是距离右边；在 RTL 环境，是距离左边），默认值：12 |


BackToTop
| 参数名 & 类型 & 是否可选  | 参数说明 |
| ---------------------- | --------|
| properties\<BackToTopProperties> | [BackToTop] 的配置项|         
| onTap\<GestureTapCallback?> | 点击事件 |


BackToTopButton
| 参数名 & 类型 & 是否可选  | 参数说明 |
| ---------------------- | --------|
| iconName\<String> | [KlookIcon] 中 icon 名称|         
| iconSize\<Size?> | 内部 icon 大小 |
| padding\<EdgeInsetsGeometry> |  内部 padding |


```
开发者信息：

* 作者：huanyu.li
* 邮箱：<EMAIL>
```