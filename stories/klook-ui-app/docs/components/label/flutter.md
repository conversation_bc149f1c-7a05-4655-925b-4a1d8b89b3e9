# Label 标签组件

一个用于给其他视图添加标签的小控件，比如给卡片添加标签

Label 首先会分为三种类型，primary、secondary、outlined，分别由 `Label.primary`、`Label.secondary`、`Label.outlined` 构造得到，三种类型相较:

- `Label.primary` 仅支持背景颜色配置
- `Label.secondary` 支持背景和文字颜色配置
- `Label.outlined` 支持文字和边框颜色配置

## 设计稿
- [Figma 组件设计稿](https://www.figma.com/file/CrnpxK0ExFKW60fegQ77V0/%F0%9F%A7%A9-Klook-UI?node-id=11445%3A37405)
- [PRD](https://klook.larksuite.com/wiki/wikus9njhbUiBuJUM5aqjU01HKd)


## 效果图
![](../../assets/label/flutter/label_01.jpg)
![](../../assets/label/flutter/label_02.jpg)


## 用法

### Label.primary

```dart
Label.primary(
  bgColor: colorScheme.colorBrandPrimary,
  title: 'primary-small',
  size: LabelSize.small,
),

Label.primary(
  leading: _icon(),
  bgColor: colorScheme.colorBrandPrimary,
  title: 'primary-normal',
  size: LabelSize.normal,
),
```

### Label.secondary

```dart
Label.secondary(
  bgColor: colorScheme.colorBackgroundWidgetDark,
  textColor: colorScheme.colorBrandPrimary,
  title: 'secondary-small',
  size: LabelSize.small,
),

Label.secondary(
  bgColor: colorScheme.colorBackgroundWidgetDark,
  textColor: colorScheme.colorBrandPrimary,
  title: 'secondary-normal',
  size: LabelSize.normal,
),
```

### Label.outlined
```dart
Label.outlined(
  textColor: colorScheme.secondary,
  borderColor: colorScheme.secondary,
  title: 'outlined-small',
  size: LabelSize.small,
),


Label.outlined(
  textColor: colorScheme.secondary,
  borderColor: colorScheme.secondary,
  title: 'outlined-normal',
  size: LabelSize.normal,
),
```

### BrandLabel
`BrandLabel`是带有klook品牌logo的`Label`,它内部定制了bgcolor、leading、textcolor

```dart
BrandLabel.primary(
   title: 'Book on Klook',
   size: BrandLabelSize.small,
),

BrandLabel.secondary(
   title: 'Book on Klook',
   size: BrandLabelSize.small,
),
```

### CornerLabel
角标用于顶部开始和顶部结束放置的标签

```dart
CornerLabel.rank(
  value: '21',
  position: CornerLabelPosition.topEnd,
  size: CornerLabelSize.small,
),

CornerLabel.highlight(
  value: 'Book on Klook',
  position: CornerLabelPosition.topEnd,
  size: CornerLabelSize.small,
),

```


## 属性

Label
| 参数名 & 类型 & 是否可选  | 参数说明 |
| ---------------------- | --------|
| leading\<Widget?>      |  头部组件 |         
| trailing\<Widget?>     |  尾部组件 |         
| textColor\<Color?>     |  颜色    |         
| bgColor\<Color?>       |  背景颜色 |         
| borderColor\<Color?>   |  边框颜色 |         
| title\<String>         |  标签文本内容 |         
| size\<LabelSize>       |  标签大小，只支持两种尺寸，分别是 [LabelSize.small]、[LabelSize.normal]  |         


BrandLabel
| 参数名 & 类型 & 是否可选  | 参数说明 |
| ---------------------- | --------|
| title\<String>         |  标签内容 |         
| size\<BrandLabelSize>  |  标签尺寸， 只支持两种尺寸，分别是 [BrandLabelSize.small]、[BrandLabelSize.normal] |     

```
开发者信息：

* 作者：rongan.lin
* 邮箱：<EMAIL>
```