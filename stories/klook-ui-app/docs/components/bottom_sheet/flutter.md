# BottomSheet组件

设计定义了两种类型的弹框组件，分别是模态和非模态，每种类型里又细分为可拖动和不可拖动。具体样式可以查看 [设计稿](https://www.figma.com/file/jNk0ZnlHuREpC3dAdWFDNs/Bottom-Sheet_%E5%BA%95%E9%83%A8%E6%B5%AE%E5%B1%82?node-id=573%3A360)。

---

## 具体使用

根据样式封装了四个直接调用的方法，使用时可直接调用对应的方法来实现相应的功能。具体如下：

### 弹出模态不可拖动的sheet,通过关闭按钮收起

```dart
/// 展示不可拖动的模态BottomSheet，由顶部区域(标题+左关闭按钮+右按钮)、内容区域、底部区域三部分组成。
/// [title] 顶部区域标题
/// [maxHeight]    最大高度,默认是屏幕高度*0.9
/// [rightAction] 顶部区域右组件
/// [bodyContent] 中间内容区域，当内容超出时支持滚动查看
/// [bottomAction] 底部区域
/// [backgroundColor] 背景颜色
Future<T?> showUnDraggableModalBottomSheet<T>({
  required BuildContext context,
  required Widget bodyContent,
  double? maxHeight,
  String? title,
  Widget? rightAction,
  Widget? bottomAction,
  Color? backgroundColor,
}) {
  return showModalBottomSheet<T>(
    context: context,
    isScrollControlled: true,
    enableDrag: false,
    isDismissible: false,
    backgroundColor: Colors.transparent,
    builder: (BuildContext context) {
      return CommonModalBottomSheet(
        canDrag: false,
        maxHeight: maxHeight,
        title: title,
        rightBtnItem: rightAction,
        bodyContent: bodyContent,
        bottomAction: bottomAction,
        backgroundColor: backgroundColor,
      );
    },
  );
}
```

![](../../assets/bottom_sheet/flutter/noDrag_modal_rightIcon.jpeg)



### 弹出模态可拖动的sheet,通过拖拽或者点击遮罩收起

```dart
/// 展示通用的可拖动的模态BottomSheet，由顶部拖拽区域、内容区域、底部区域三部分组成。
/// [bodyContent] 内容区域，当内容超出时支持滚动查看
/// [maxHeight]    最大高度,默认是屏幕高度*0.9
/// [bottomAction] 底部区域
/// [backgroundColor] 背景颜色
Future<T?> showDraggableModalBottomSheet<T>({
  required BuildContext context,
  required Widget bodyContent,
  double? maxHeight,
  Widget? bottomAction,
  Color? backgroundColor,
}) {
  return showModalBottomSheet<T>(
    context: context,
    isScrollControlled: true,
    enableDrag: true,
    isDismissible: true,
    backgroundColor: Colors.transparent,
    builder: (BuildContext context) {
      return CommonModalBottomSheet(
        canDrag: true,
        maxHeight: maxHeight,
        bodyContent: bodyContent,
        bottomAction: bottomAction,
        backgroundColor: backgroundColor,
      );
    },
  );
}
```



![](../../assets/bottom_sheet/flutter/drag_modal.jpeg)



### 非模态不可拖动的常驻sheet，无法收起

```dart
/// 在child上面展示不可拖动的悬浮的常驻BottomSheet。由内容区域、底部区域组成。
/// [fixedHeight] 固定高度，为null则按bodyContent实际高度显示
/// [maxHeight]   最大高度,默认是屏幕高度*0.9
/// [bodyContent] 内容区域，当内容超出maxHeight时支持滚动查看
/// [bottomAction] 底部区域
/// [child]       组件
/// [backgroundColor] 背景颜色
CommonStackBottomSheet buildUnDraggableModelessBottomSheet({
  Key? key,
  double? fixedHeight,
  double? maxHeight,
  required Widget bodyContent,
  Widget? bottomAction,
  required Widget child,
  Color? backgroundColor,
}) {
  return CommonStackBottomSheet(
      key: key,
      canDrag: false,
      fixedHeight: fixedHeight,
      maxHeight: maxHeight,
      bodyContent: bodyContent,
      bottomAction: bottomAction,
      backgroundColor: backgroundColor,
      child: child);
}
```



![](../../assets/bottom_sheet/flutter/noDrag_stack.jpeg)



### 非模态可拖动收缩和展开的sheet

```dart
/// 在child上面展示可拖动可收缩的悬浮的常驻BottomSheet,由顶部拖拽区域、内容区域、底部区域三部分组成。
/// [shrinkHeight] 收缩起来的高度,默认是拖拽栏的高度20
/// [isExpand]     默认是否展开
/// [maxHeight]    最大高度,默认是屏幕高度*0.9
/// [bodyContent]  内容区域，当内容超出maxHeight时支持滚动查看
/// [bottomAction] 底部区域
/// [child]        组件
/// [backgroundColor] 背景颜色
CommonStackBottomSheet buildDraggableModelessBottomSheet({
  Key? key,
  double? shrinkHeight,
  bool isExpand = false,
  double? maxHeight,
  required Widget bodyContent,
  Widget? bottomAction,
  required Widget child,
  Color? backgroundColor,
}) {
  return CommonStackBottomSheet(
      key: key,
      canDrag: true,
      shrinkHeight: shrinkHeight,
      isExpand: isExpand,
      maxHeight: maxHeight,
      bodyContent: bodyContent,
      bottomAction: bottomAction,
      backgroundColor: backgroundColor,
      child: child);
}
```



* 收缩时样式（收缩高度请根据实际需求自行设置好，安全区域高度不用考虑在内）

![shrink](../../assets/bottom_sheet/flutter/drag_stack_shrink.jpeg)

* 展开时样式（展开的高度默认是实际内容的高度，但不会超过maxHeight）

![expand](../../assets/bottom_sheet/flutter/drag_stack_expand.jpeg)


```
开发者信息：

* 作者：wei.chen
* 邮箱：<EMAIL>
```