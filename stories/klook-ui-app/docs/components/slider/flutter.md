# Slider 滑块选择器
> 仅支持整数

通过拖动滑块在一个固定区间内进行选择，有单滑块、双滑块两种类型，当未设置滑块初始值时，默认选中从最小值 - 最大值的全区间


## 设计稿
[Figma 组件设计稿](https://www.figma.com/file/5kU1ysjfp0pOKhU1pqNcWh/Slider_%E6%BB%91%E5%9D%97?node-id=1%3A36)


## 效果图
![](../../assets/slider/flutter/slider_01.jpg)


## 用法

### 单滑块
构造方法
```dart
  CommonSlider.single({
    Key? key,
    String? title,
    EdgeInsetsGeometry? padding,
    ValueLabelBuilder? valueLabelBuilder,
    int? currentValue,
    required int min,
    required int max,
    int stepSize = 1,
    ValueChanged<int>? onChangeEnd,
  }) 
```

使用demo
```dart
 CommonSlider.single(
          currentValue: sliderValue,
          min: 0,
          max: 1000,
          title: "未设置值，默认选中最小值-最大值",
          valueLabelBuilder: (curValue) {
            return '￥0 - ￥$curValue';
          },
          onChangeEnd: (curValue) {
            setState(() {
              sliderValue = curValue;
            });
          },
        )
```


### 双滑块
构造方法
```dart
  CommonSlider.double({
    Key? key,
    String? title,
    EdgeInsetsGeometry? padding,
    ValuesLabelBuilder? valuesLabelBuilder,
    CommonRangeValues? currentValues,
    required int min,
    required int max,
    int stepSize = 1,
    ValueChanged<CommonRangeValues>? onChangeEnd,
  }) 
```

使用demo
```dart
 CommonSlider.double(
          currentValues: CommonRangeValues(curStartValue, curEndValue),
          min: 0,
          max: 100,
          stepSize: 10,
          title: "步长单位为10",
          valuesLabelBuilder: (values) {
            return '￥${values.start} - ￥${values.end}';
          },
          onChangeEnd: (values) {
            setState(() {
              curStartValue = values.start;
              curEndValue = values.end;
            });
          },
        )
```

## 属性

CommonSlider.single
| 参数名 & 类型 & 是否可选  | 参数说明 |
| ---------------------- | --------|
| title\<String?> | 标题 |         
| padding\<EdgeInsetsGeometry?> | 内容 Padding |
| valueLabelBuilder\<ValueLabelBuilder?> | 区间值构造器，可以通过此方法自定义区间展示 |
| currentValue\<int?> | 当前值 |
| min\<int> | 滑杆最小值 |
| max\<int> | 滑杆最大值 |
| stepSize\<int> | 步长 |
| onChangeEnd\<ValueChanged<int>?> | 滑杆值改变回调 |


CommonSlider.double
| 参数名 & 类型 & 是否可选  | 参数说明 |
| ---------------------- | --------|
| title\<String?> | 标题 |         
| padding\<EdgeInsetsGeometry?> | 内容 Padding |
| ValuesLabelBuilder\<valuesLabelBuilder?> | 区间值构造器，可以通过此方法自定义区间展示 |
| currentValue\<int?> | 当前值 |
| min\<int> | 滑杆最小值 |
| max\<int> | 滑杆最大值 |
| stepSize\<int> | 步长 |
| onChangeEnd\<ValueChanged<int>?> | 滑杆值改变回调 |

```
开发者信息：

* 作者：jie.li
* 邮箱：<EMAIL>
```