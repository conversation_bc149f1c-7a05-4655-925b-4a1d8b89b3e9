# Hotel Card 酒店卡片

:::demo
```html
<div>
  <div class="demo-wrapper">
    <klk-form class="demo-form">
      <klk-form-item label="切换screenPlatform或者拉动屏幕宽度">
        <klk-radio-group v-model="screenPlatform">
          <klk-radio group-value="mobile">mobile</klk-radio>
          <klk-radio group-value="tablet">tablet</klk-radio>
          <klk-radio group-value="desktop">desktop</klk-radio>
        </klk-radio-group>
      </klk-form-item>
    </klk-form>
  </div>
      
    <hotel-search-list
      :key="platform + cardScene"
      :screen-platform="screenPlatform"
      :poi-info="poiInfo"
      :list-result="MockListData.result"
      :get-hotel-suggest="getHotelSuggest"
      :get-destination-list="getDestinationList"
      :get-hotel-filter="getHotelFilter"
      @get-list-data="getListData"
      @click="click">
        <!-- <hotel-list-selector slot="selector" slot-scope="props" v-bind="props"></hotel-list-selector> -->
      </hotel-search-list>
  </div>

  
</div>
```
:::

## 卡片类型


### 展示信息无部分



## Props 属性

| 参数       | 介绍                                                                                                              | 类型                         | 可选值              | 默认值 |
| ---------- | ----------------------------------------------------------------------------------------------------------------- | ---------------------------- | ------------------- | ------ |
| screenPlatform   | 响应式platform                                                                                                          | String                       | mobile, desktop     | 无     |   
| top   | 顶部pin时候的偏移距离                                                                                                          | String                       | mobile, desktop     | 无     |   
| getHotelSuggest   |  获取hotel 推荐suggest处理函数                                                                                                           | String                       | mobile, desktop     | 无     |
| getDestinationList   | 获取目的地处理函数                                                                                                          | Function                       |     | 无     |
| dateRange  | selector的日期最大范围                                                                                             | String                       | 365（@hotelModule.State 目前写死，不调用接口）     | 无     |
| listResult   | 列表数据                                                                                                         | ListResult（参考结构定义） | 无                  | 无     |
| poiInfo   | poiInfo数据                                                                                                         | PoiInfo | 无                  | 无     |


onClick 内部会 $emit 一个 click 事件,  不会跳转

### 数据结构
```javascript
  export type PoiInfo = {
    stype: 'place' | string // 目前这个页面都是place
    city_name: string
    country_name?: string 
    city_id: string | number
    svalue: string
  }
  

  // 列表接口直接返回即可
  export interface ListResult {
    search_id?: string
    report: any,
    current_page: number,
    total_count: number,
    content: any[]
  }
```


## 插槽 Slot

## emit

| 名称 | 参数 |介绍|
|------|------|------|
| get-list-data | { cardNum: num }| 渲染后抛出的相关数据 |


## 引入
@klook/hotel-card@2.2.7-beta.1
@klook/hotel-response@0.0.1-beta.5
@klook/hotel-search-list@0.0.1-beta.5


```
import '@klook/hotel-search-list/lib/index.css'
import '@klook/hotel-response/lib/index.css'

import '@klook/hotel-card/dist/cjs/index.css'
import '@klook/hotel-selector/lib/index.css'

import HotelSearchList from '@klook/hotel-search-list'
import { HotelResponseCardSwiper } from '@klook/hotel-response'
import Card from '@klook/hotel-card'

Vue.use(HotelSearchList)

components: {
    HotelResponseCardSwiper
}

 <hotel-search-list
      :screen-platform="screenPlatform"
      :poi-info="poiInfo"
      :list-result="MockListData.result"
      ></hotel-search-list>
<hotel-response-card-swiper
      :screen-platform="screenPlatform"
      :iht-attrs="ihtAttrs"
      :title="title"
      :list="cardList"
      ></hotel-response-card-swiper>

```



<script lang="ts">

// import HotelCard, { formatCardData } from '@/packages/klook-hotel-card/src/index.ts'
import '@klook/hotel-card/dist/cjs/index.css'
import HotelSearchList from '@/packages/klook-hotel-search-list/src/index.ts'
import MockListData, { MockFilter, MockListNoData}  from '@/packages/klook-hotel-search-list/mock/mock.js'
// import '@klook/hotel-promotion-tag/dist/cjs/index.css' // 这样以后可以只升级hotel-promotion-tag而不用升级hotel-card了
import MockSelector from '@/packages/klook-hotel-selector/demo/mock.json'
import ResponseMixin  from '@/packages/klook-hotel-response/mock/responsive-mixin.js';
import HotelListSelector from  '@/packages/klook-hotel-search-list/mock/demo-selector.tsx'

export default {
  mixins: [ResponseMixin],
  components: {
    HotelSearchList,
    HotelListSelector
  },
  data() {
    return {
      cardScene: 'listHorizontal',
      cardLayout: '',
      type: 'normal',
      MockListData,
      MockFilter,
      MockListNoData,
      screenPlatform: 'mobile',
      platform: 'mobile',
      dateRange: 365,
      emptyList: {
        "success": true,
        "error": {
            "code": "",
            "message": ""
        },
        "result": {
          content: []
        }
      },
      poiInfo: {
        stype: 'place',
        svalue: '123',
        city_name: 'city_name',
        country_name: 'country_name'
      },
      getHotelSuggest: (keyword) => new Promise(resolve => setTimeout(resolve, 500, MockSelector.suggest3)),
      getDestinationList: (position) => new Promise(resolve => resolve(MockSelector.destinations)),
      getHotelFilter: (data) => new Promise(resolve => setTimeout(resolve, 500, MockFilter.result)),
    }
  },
  watch: {
    platform (newVal) {
      this.initEvent()
    },
    cardScene (newVal) {
      this.initEvent()
    },
    cardLayout (newVal) {
      this.initEvent()
    },
  },
  mounted() {
  },
  methods: {
    initEvent() {
      this.$nextTick(() => {
      })
    },
    click(e) {
      console.log('custom click card')
    },
    viewMap(mapProps) {
      console.log('viewMap', mapProps)
    },
    getListData(data) {
      console.log('getListData', data)
    }
  }
}
</script>

<style lang="scss">
.markdown-body img {
  background-color: initial;
}
p {
  margin: 0;
}
.demo-wrapper {
  display: flex;
  align-items: flex-start;
}
.demo-form {

  .klk-form-item {
    flex-direction: row;
    align-items: center;

    label {
      margin-right: 12px;
      width: 100px;
    }
  }

  .klk-form-item-content {
    min-width: 300px;
  }

  .tag-form-item {
    flex-direction: column;
    align-items: flex-start;
  }

  .tag-wrapper {
    display: flex;
    align-items: center;
    flex-wrap: wrap;

    &-item {
      display: flex;
      align-items: center;
      padding: 4px 10px;

      svg {
        margin-left: 6px;
        cursor: pointer;
      }
    }
  }
}


.hotel-search-list {
  background-color : #f5f5f5;
  padding: 50px;
}
</style>