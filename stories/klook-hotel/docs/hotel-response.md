# Hotel Card 酒店卡片

:::demo
```html
<div>
  <div class="demo-wrapper">
    <klk-form class="demo-form">
      <klk-form-item label="切换screenPlatform或者拉动屏幕宽度">
        <klk-radio-group v-model="screenPlatform">
          <klk-radio group-value="mobile">mobile</klk-radio>
          <klk-radio group-value="tablet">tablet</klk-radio>
          <klk-radio group-value="desktop">desktop</klk-radio>
        </klk-radio-group>
      </klk-form-item>
    </klk-form>

  </div>
    <hotel-response-card-swiper
      ref="card"
      :platform="platform"
      :screen-platform="screenPlatform"
      :iht-attrs="ihtAttrs"
      :title="title"
      :list="cardList"
      @click="click"></hotel-response-card-swiper>
  </div>

  
</div>
```
:::

## 卡片类型


### 展示信息无部分

## Props 属性

| 参数       | 介绍                                                                                                              | 类型                         | 可选值              | 默认值 |
| ---------- | ----------------------------------------------------------------------------------------------------------------- | ---------------------------- | ------------------- | ------ |
| 真实platform   | 设备平台                                                                                                          | String                       | mobile, desktop     | 无     |
| list  | 列表数据                                                                                                         | ListResult（参考结构定义） | 无                  | 无     |
| title   | 标题据                                                                                                         | PoiInfo | 无                  | 无     |
| ihtAttrs   | 埋点数据，见ihtAttrs结构                                                                                                         | PoiInfo | 无                  | 无     |



onClick 内部会 $emit 一个 click 事件,  不会跳转

### 数据结构
```javascript
  
  interface ihtAttrs {
    cardModule: String
  }
  // CardInfo数据，不是后端返回的 {data, type}
  export interface list =  CardInfo[]
```


## 插槽 Slot

## emit

| 名称 | 参数 |介绍|
|------|------|------|




:::

<script lang="ts">

// import HotelCard, { formatCardData } from '@/packages/klook-hotel-card/src/index.ts'

import { HotelResponseCardSwiper } from '@/packages/klook-hotel-response/src/index.ts'
import MockListData, { MockFilter }  from '@/packages/klook-hotel-search-list/mock/mock.js'
// import '@klook/hotel-promotion-tag/dist/cjs/index.css' // 这样以后可以只升级hotel-promotion-tag而不用升级hotel-card了
import ResponseMixin  from '@/packages/klook-hotel-response/mock/responsive-mixin.js';

export default {
  mixins: [ResponseMixin],
  components: {
    HotelResponseCardSwiper
  },
  data() {
    return {
      cardLayout: '',
      type: 'normal',
      ihtAttrs: {
        cardModule: 'cardModule'
      },
      title: 'test title',
      cardList: MockListData.result.content.filter(item => item.type === 'card').map(item => item.data),
      // cardList: MockListData.result.content,
      // screenPlatform: 'desktop',
      platform: 'mobile',
      dateRange: 365,
      getHotelSuggest: (keyword) => new Promise(resolve => setTimeout(resolve, 500, MockSelector.suggest3)),
      getDestinationList: (position) => new Promise(resolve => resolve(MockSelector.destinations)),
      getHotelFilter: (data) => new Promise(resolve => setTimeout(resolve, 500, MockFilter.result)),
    }
  },
  watch: {
    platform (newVal) {
      this.initEvent()
    },
    cardScene (newVal) {
      this.initEvent()
    },
    cardLayout (newVal) {
      this.initEvent()
    },
  },
  mounted() {
  },
  methods: {
    initEvent() {
      this.$nextTick(() => {
      })
    },
    click(e) {
      console.log('custom click card')
    },
    viewMap(mapProps) {
      console.log('viewMap', mapProps)
    }
  }
}
</script>

<style lang="scss">
.markdown-body img {
  background-color: initial;
}
p {
  margin: 0;
}
.demo-wrapper {
  display: flex;
  align-items: flex-start;
}
.demo-form {

  .klk-form-item {
    flex-direction: row;
    align-items: center;

    label {
      margin-right: 12px;
      width: 100px;
    }
  }

  .klk-form-item-content {
    min-width: 300px;
  }

  .tag-form-item {
    flex-direction: column;
    align-items: flex-start;
  }

  .tag-wrapper {
    display: flex;
    align-items: center;
    flex-wrap: wrap;

    &-item {
      display: flex;
      align-items: center;
      padding: 4px 10px;

      svg {
        margin-left: 6px;
        cursor: pointer;
      }
    }
  }
}


.hotel-search-list {
  background-color : #f5f5f5;
  padding: 50px;
}
</style>