# RefundEntry 订单入口组件

> 版本请查看[knpm](https://knpm.klook.io/-/web/detail/@klook/klook-order-refund-entry)

```
import KlkOrderRefundEntry from '@klook/klook-order-refund-entry'
import '@klook/klook-order-refund-entry/dist/esm/index.css'
```

## 示例

:::demo

```html
<div style="width: 100%; padding: 20px; background: #f5f5f5">
  <div>
    <klk-order-refund-entry
      :refund-status="lastRefundStatus"
      @click="handleClick"
    ></klk-order-refund-entry>
  </div>
</div>
```

:::

## 参数 Props

| 参数          | 介绍       | 类型          | 必传 | 默认值 |
| ------------- | ---------- | ------------- | ---- | ------ |
| refund-status | 退款单状态 | 对应 v1/bookingapiserv/myorders/order_detail 接口 last_refund_status 模块 | true | -      |

```javascript
interface refund_status {
  text: string;
  text_color: string;
  desc: string;
  icon: string;
  status: string;
}
```

## Events

| 名称 | 介绍 | 参数 |
|-----|------|-----|
| click | 点击回调 | (status: refund_status)  |

<script lang="ts">
import KlkOrderRefundEntry from '@/packages/klook-order-refund-entry/src';
import KlkButton from '@/packages/klook-ui/src/components/button'

export default {
  data() {
    return {
      lastRefundStatus: {
          // 退款状态文案
          // 当 status = Processing 时, 业务线可自定义该字段文案
          "text": "Refunded failed",
          // 退款状态文案
          "text_color": "#0f0",
          // 退款状态描述文案
          // 当取消申请单存在退货成功的商品时会给默认文案
          // 其余情况订单不会给对应文案, 业务线可自行定义文案
          // 没有文案则不返回该字段给前端
          "desc": "3 refunds",
          // 退款状态icon
          "icon": "",
          // 退款状态, 枚举
          // Refunded 退款成功
          // Processing 退款处理中
          // RefundFailed 退款失败
          "status": "RefundFailed"
      }
    }
  },
  methods: {
    handleClick(status) {
      console.log('click', status)
    }
  },
  components:{
    KlkOrderRefundEntry,
    KlkButton
  }
}
</script>
