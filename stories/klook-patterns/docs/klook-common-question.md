# CommonQuestion 常见问题
> 版本请查看[knpm](https://knpm.klook.io/-/web/detail/@klook/klook-common-question)

```
import KlkCommonQuestion from '@klook/klook-common-question'
import '@klook/klook-common-question/dist/esm/index.css'
```

## 示例
:::demo
```html
<div style="width: 100%;">
  <div>
    <klk-common-question
      language="en-US"
      platform="desktop"
      :question-list="questionList"
      @question-click="handleQuestionClick"
    />
  </div>
</div>
```
:::

## 参数 Props

| 参数 | 介绍 | 类型 | 必传 | 默认值 |
|------|------|------|------|------|
|language|语言|Data.language|true|en|
|platform|平台|mobile/desktop|true|mobile|
|question-list|问题|question[]|true|-|

``` javascript
interface question {
  "question": string,
  "answer": string,
  "deeplink": string,
  "faq_id": string, // 埋点用
}
```

## Events

| 名称 | 介绍 | 参数 |
|-----|------|-----|
| question-click | question点击回调（自定义点击回调，当answer和deeplink都为空时才生效）  | (item: string, index: number)  |


<script lang="ts">
import KlkCommonQuestion from '@/packages/klook-common-question/src';

export default {
  data() {
    return {
      questionList: [
        {
          "faq_id": "11111",
          "question": "问题1问题1问题1问题1问题1问题1问题1问题1问题1问题1问题1问题1问题1问题1问题1问题1",
          "answer": "答案1"
        },
        {
          "faq_id": "22222",
          "question": "问题2",
          "deeplink": "https://www.baidu.com"
        },
        {
          "faq_id": "33333",
          "question": "问题3",
          "answer": "答案3",
        }]
    }
  },
  methods: {
    handleQuestionClick(question, index) {
      console.log(question, index)
    }
  },
  components:{
    KlkCommonQuestion,
  }
}
</script>
