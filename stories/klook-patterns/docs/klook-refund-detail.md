# RefundDetail 退款详情

> 版本请查看[knpm](https://knpm.klook.io/-/web/detail/@klook/klook-refund-detail)

```
import KlkRefundDetail from '@klook/klook-refund-detail'
import '@klook/klook-refund-detail/dist/esm/index.css'
```

## 示例

:::demo

```html
<div style="width: 100%;">
  <div>
    <klk-button @click="visible = true">Show</klk-button>
    <klk-refund-detail
      language="en-US"
      platform="mobile"
      :visible.sync="visible"
      :refund-details="refundDetails"
    />
  </div>
</div>
```

:::

## 参数 Props

| 参数               | 介绍                        | 类型           | 必传  | 默认值 |
| ------------------ | --------------------------- | -------------- | ----- | ------ |
| language           | 语言                        | Data.language  | true  | en     |
| booking-ref-no     | 订单 No,埋点用              | string         | true  | -      |
| platform           | 平台                        | mobile/desktop | true  | mobile |
| visible            | 是否可见，可通过 .sync 绑定 | boolean        | true  | -      |
| refund-details | 退款数据列表                | 退款详情接口数据，前端透传 | true  | -     |

<script lang="ts">
import KlkRefundDetail from '@/packages/klook-refund-detail/src';
import KlkButton from '@/packages/klook-ui/src/components/button'

export default {
  data() {
    return {
      visible: false,
      refundDetails: {
        "refund_details": [
            [
                {
                    "refund_id": 526184148,
                    "refund_no": "76fbc99220194164463024e4d2b87193",
                    "refund_status": {
                        "text": "已退款",
                        "text_color": "#08B371",
                        "desc": "退款正在进行中，完成退款所需时间可能因预订时使用的支付方式而异",
                        "icon": "https://res.klook.com/image/upload/v1701141098/UED_new/Platform/platform_bookingDetail_23Q4/icon_check_circle_filled.svg",
                        "status": "Refunded"
                    },
                    "refund_total_amount_text": "退款金额：",
                    "refund_total_amount_value": "HK$ 2,896.0",
                    "refund_price_details": [
                        {
                            "title_text": "支付金额",
                            "content_text": "HK$ 2,896.0",
                            "price_info": {
                                "price_type": "PriceTypeRefundPrice",
                                "amount": 289600,
                                "currency": "HKD"
                            },
                            "annotations": null
                        }
                    ],
                    "refund_asset_details": [
                        {
                            "title_text": "支付优惠券",
                            "content_text": "退还数量： 1",
                            "desc": null,
                            "action_type": 1,
                            "link": "https://t5.fat.klook.io/zh-CN/coupons",
                            "annotations": null,
                            "price_info": {
                                "price_type": "PriceTypePaymentCoupon",
                                "amount": 500,
                                "currency": "HKD"
                            }
                        },
                        {
                            "title_text": "积分",
                            "content_text": "118",
                            "desc": null,
                            "action_type": 1,
                            "link": "https://t5.fat.klook.io/zh-CN/credits",
                            "annotations": null,
                            "price_info": {
                                "price_type": "PriceTypeCredit",
                                "amount": 1180,
                                "currency": "HKD"
                            }
                        },
                        {
                            "title_text": "礼品卡",
                            "content_text": "HK$ 30.0",
                            "desc": null,
                            "action_type": 2,
                            "link": null,
                            "annotations": [
                                {
                                    "title_text": "礼品卡退款说明",
                                    "content_text": "使用礼品卡支付的金额已退还入账"
                                }
                            ],
                            "price_info": {
                                "price_type": "PriceTypeGiftCard",
                                "amount": 3000,
                                "currency": "HKD"
                            },
                        },
                        {
                            "title_text": "EdenPass折抵",
                            "content_text": "NT$ 30",
                            "desc": null,
                            "action_type": 0,
                            "link": null,
                            "annotations": [],
                            "price_info": {},
                            "refund_additional_desc": "点数已以<span style='color: #f09b0a'>Klook EdenPass券</span>退回Klook账户，可在EdenRed中下单使用"
                        }
                    ],
                    "refunded_unit": [
                        {
                            "variant_id": 134891581472768,
                            "unit_count": null,
                            "title": "taiwan lvbao, Deluxe Single room - Desert View",
                            "subtitle": "1 x Deluxe Single room - Desert View \n2 x Deluxe Single room - Desert View"
                        }
                    ],
                    "refund_details_basic": [
                        {
                            "title_text": "退款原因",
                            "content_text": "其他原因"
                        },
                        {
                            "title_text": "申请退款时间",
                            "content_text": "2024-01-15 12:05:42"
                        },
                        {
                            "title_text": "退款完成时间",
                            "content_text": "2024-01-15 12:07:08"
                        }
                    ]
                }
            ]
        ],
        "refund_annotations": [
            {
                "title_text": "退款说明",
                "content_text": "1. 如未成功全额退款，礼券包优惠券及支付优惠券恕不退还\n2. 平台优惠券恕不退还"
            }
        ]
    },
      refundData: [
       {
         "status_icon": "https://res.klook.com/image/upload/v1639116270/brand%20refresh%20category%20icon/app%20mweb/category_stays_l1_hotels_more_bg48.png",
         "status_title":"退款中",
         "status_title_color":"#08B371",
         "status_content":"Depending on your payment option, you will receive your refund in 3-7 business days", 
         "details": [
              {
                "item": "名称1",                //左边标题
                "content": "内容1",             //右边内容
                "content_detail": "详情1",      //右边内容下面详情
                "content_type": "show"         //右边内容的展示类型。show-纯展示(默认)，copy-可拷贝
               },

               {
                "item": "名称2",                //左边标题
                "content": "内容2",             //右边内容
                "content_type": "copy" 
               },
               {
                "item": "名称3",                //左边标题
                "content": "内容2",             //右边内容
                "content_type": "info",        //info按钮
                "info": "info按钮点击展开的详情"  //弹框展示的具体详情
               }
         ]
       },
       
       {
         "status_icon": "https://res.klook.com/image/upload/v1641525089/brand%20refresh%20category%20icon/app%20mweb/icon_category_trains_xm_colorful_bg48.png",
         "status_title": "已退款",
         "status_title_color": "#F09B0A",
         "status_content":"Depending on your payment option, you will receive your refund in 3-7 business days", 
         "details": [
               {
                "item": "名称1",                //左边标题
                "content": "内容1",             //右边内容
                "content_detail": "详情1",      //右边内容下面详情
                "content_type": "show"         //右边内容的展示类型。show-纯展示(默认)，copy-可拷贝
               },

               {
                "item": "名称2",                //左边标题
                "content": "内容2",             //右边内容
                "content_type": "copy" 
               }
         ]
       }
]
    }
  },
  components:{
    KlkRefundDetail,
    KlkButton
  }
}
</script>
