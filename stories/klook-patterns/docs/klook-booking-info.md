# BookingInfo 预订信息

> 版本请查看[knpm](https://knpm.klook.io/-/web/detail/@klook/klook-booking-info)

```
import KlkBookingInfo from '@klook/klook-booking-info'
import '@klook/klook-booking-info/dist/esm/index.css'
```

## 示例

:::demo

```html
<div style="width: 100%;">
  <div>
    <klk-booking-info
      language="en-US"
      platform="mobile"
      :booking-basic-info="bookingBasicInfo"
    />
  </div>
</div>
```

:::

## 参数 Props

| 参数         | 介绍     | 类型           | 必传  | 默认值 |
| ------------ | -------- | -------------- | ----- | ------ |
| language     | 语言     | Data.language  | true  | en     |
| platform     | 平台     | mobile/desktop | true  | mobile |
| booking-ref-no | 订单号   | string         | true  | -      |
| booking-basic-info   | 预定商品信息列表 |  对应 v1/bookingapiserv/myorders/order_detail 接口 booking_basic_info 模块     | true  | []      |

<script lang="ts">
import KlkBookingInfo from '@/packages/klook-booking-info/src';


export default {
  data() {
    return {
      bookingBasicInfo: [
          {
            // ex: Booking ref. ID
            "title_text": "Booking ref. ID",
            // ex: BK12122
            "content_text": "BK12122",
            // 可操作类型
            // 0 不可操作, 不返回该字段
            // 1 可跳转
            // 2 可显示浮层说明
            // 3 可复制
            "action_type": 3,
            // 对应action_type的动作值
            // action_type=1, 则为跳转链接, 订单根据platform来下发链接
            // 没有则不返回该字段
            "link": "",
            // action_type=2, 则为说明文案
            // 没有则不返回该字段
            "annotations": [
                {
                    "title_text": "",
                    "content_text": ""
                }
            ]
          },
          {
            // ex: Booking ref. ID
            "title_text": "Supplier booking No.",
            // ex: BK12122
            "content_text": "BK12122",
            // 可操作类型
            // 0 不可操作, 不返回该字段
            // 1 可跳转
            // 2 可显示浮层说明
            // 3 可复制
            "action_type": 0,
            // 对应action_type的动作值
            // action_type=1, 则为跳转链接, 订单根据platform来下发链接
            // 没有则不返回该字段
            "link": "",
            // action_type=2, 则为说明文案
            // 没有则不返回该字段
            "annotations": [
                {
                    "title_text": "",
                    "content_text": ""
                }
            ]
          }
      ]
    }
  },
  components:{
    KlkBookingInfo
  }
}
</script>
