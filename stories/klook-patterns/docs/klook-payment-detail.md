# PaymentDetail 支付明细

> 版本请查看[knpm](https://knpm.klook.io/-/web/detail/@klook/klook-payment-detail)

```
import KlkPaymentDetail from '@klook/klook-payment-detail'
import '@klook/klook-payment-detail/dist/esm/index.css'
```

## 示例

:::demo

```html
<div style="width: 100%;">
  <div>
    <klk-button @click="visible = true">Show</klk-button>

    <klk-payment-detail
      :visible.sync="visible"
      booking-ref-no="123"
      language="en-US"
      platform="mobile"
      :payment-details="payment_details"
      @custom-button-click="onCustomButtonClick"
    >
      <template v-slot:price-detail>
        <klk-order-table-atomic
          :data="orderDetailData">
        <klk-order-table-atomic>
      </template>

      <template v-slot:bottom-component>
        <div>
          bottom component slot
        </div>
      </klk-payment-detail>
  </div>
</div>
```

:::

## 参数 Props

| 参数                 | 介绍                                                 | 类型           | 必传  | 默认值 |
| -------------------- | ---------------------------------------------------- | -------------- | ----- | ------ |
| bookingRefNo         | 订单 No, 埋点用                                      | string         | true  | -      |
| language             | 语言                                                 | Data.language  | true  | en     |
| visible              | 是否可见，可通过 .sync 绑定                          | boolean        | -     | false  |
| platform             | 平台                                                 | mobile/desktop | -     | mobile |
| payment-details      | 对应 v1/bookingapiserv/myorders/payment/detail 接口返回 | -      | false | -      |

## Events

| 名称                | 介绍                       | 参数                          |
| ------------------- | -------------------------- | ----------------------------- |
| custom-button-click | 业务线自定义按钮点击的回调 | (item: string, index: number) |

## 插槽 Slot

| 参数        | 介绍                                                      | 类型                     | 可选值 | 默认值 |
| ----------- | --------------------------------------------------------- | ------------------------ | ------ | ------ |
| price-detail | price detail 插槽（可使用 OrderTableAtomic 原子组件布局） | 自定义 price detail 标签 | 无     | 无     |
bottom-component 插槽 | 自定义插槽 | 无     | 无     | 无 |

<script lang="ts">
import KlkPaymentDetail from '@/packages/klook-payment-detail/src';
import KlkOrderTableAtomic from '@/packages/klook-order-table-atomic/src';
import KlkButton from '@/packages/klook-ui/src/components/button'

export default {
  data() {
    return {
      visible: false,
      orderDetailData: [
        {
          title: 'Lable',
          content: 'HK\$ 7,500',
          detailAlign: 'left',
          detail: '2 * Room',
          copyable: true,
        },
        {
          title: 'Lable',
          content: 'HK\$ 7,500',
          detailAlign: 'left',
          detail: '2 * Room',
        }
      ],
      payment_details: {
        // 总支付金额文案
        "sub_total_text": "Subtotal",
        // 总支付金额, 用户选择货币
        "sub_total_amount": "HK$7,511.0",
        // 支付金额明细, 业务线传, 订单不给默认值
        "payment_price_details":[
            {
                // 费用项名称, 前端可直接展示
                "title_text": "",
                // 费用项副标题, 前端可直接展示
                //  ex: 2 * Room
                "subtitle_text": "",
                // 费用项金额, 需要包括货币,金额，正负
                // ex: - HK$ 1,211.8
                "content_text": ""
            }
        ],
        // 价格明细
        // 订单会按照产品约定的排序返回
        "price_details":[
            {
                // 费用项名称
                "title_text": "title",
                // 费用项金额, 需要包括货币,金额，正负
                // ex: - HK$ 1,211.8
                "content_text": "content",
                // 金额明细的原始内容
                // 不用于前端展示
                "price_info": {
                    // 费用类型,枚举
                    // PriceTypeCoupon 优惠券（含品类优惠券、Value pack、支付优惠券）
                    // PriceTypeCredit 积分
                    // PriceTypeGiftCard 礼品卡
                    // PriceTypeTwGuoLv 国旅券
                    // PriceTypeKlkGuolv KLK国旅券
                    // PriceTypeTwYiFun 艺fun券
                    // PriceTypeCultureMoney 成年礼金,台湾文化币
                    // PriceTypeManual 手动退款
                    "price_type": "",
                    // 金额*100
                    "amount": "",
                    // 货币
                    "currency": ""
                }
            },
            {
                // 费用项名称
                "title_text": "title2",
                // 费用项金额, 需要包括货币,金额，正负
                // ex: - HK$ 1,211.8
                "content_text": "content2",
                // 金额明细的原始内容
                // 不用于前端展示
                "price_info": {
                    // 费用类型,枚举
                    // PriceTypeCoupon 优惠券（含品类优惠券、Value pack、支付优惠券）
                    // PriceTypeCredit 积分
                    // PriceTypeGiftCard 礼品卡
                    // PriceTypeTwGuoLv 国旅券
                    // PriceTypeKlkGuolv KLK国旅券
                    // PriceTypeTwYiFun 艺fun券
                    // PriceTypeCultureMoney 成年礼金,台湾文化币
                    // PriceTypeManual 手动退款
                    "price_type": "",
                    // 金额*100
                    "amount": "",
                    // 货币
                    "currency": ""
                }
            },
            {
                // 费用项名称
                "title_text": "title3",
                // 费用项金额, 需要包括货币,金额，正负
                // ex: - HK$ 1,211.8
                "content_text": "content3",
                // 金额明细的原始内容
                // 不用于前端展示
                "price_info": {
                    // 费用类型,枚举
                    // PriceTypeCoupon 优惠券（含品类优惠券、Value pack、支付优惠券）
                    // PriceTypeCredit 积分
                    // PriceTypeGiftCard 礼品卡
                    // PriceTypeTwGuoLv 国旅券
                    // PriceTypeKlkGuolv KLK国旅券
                    // PriceTypeTwYiFun 艺fun券
                    // PriceTypeCultureMoney 成年礼金,台湾文化币
                    // PriceTypeManual 手动退款
                    "price_type": "",
                    // 金额*100
                    "amount": "",
                    // 货币
                    "currency": ""
                }
            }
        ],
        // 计算优惠后的总(待)支付金额文案
        "total_after_discount_text": "Total",
        // 计算优惠后的总(待)支付金额
        "total_after_discount_value": "HK7,511.0",
        // 业务线补充金额, 业务线自行传值, 订单不给默认值
        "extra_pay_amount":[
            {
                // 费用项名称
                "title_text": "Pay Now",
                // 费用项金额, 包括货币和正负
                // ex: -HKD$10
                "content_text": 'HK7,511.0'
            },
            {
                // 费用项名称
                "title_text": "Pay Pick-up",
                // 费用项金额, 包括货币和正负
                // ex: -HKD$10
                "content_text": 'HK7,511.0'
            }
        ],
        // 实际支付金额文案
        "actual_payment_amount_text": "Paid Amount",
        // 实际支付金额
        "actual_payment_amount_value": "$1,223.0",
        // 支付补充说明, 展示在实际金额下方, 业务线传值, 订单没有默认值
        "payment_desc_text": "desc text desc text desc text desc text desc text desc text desc text desc text desc text desc text desc text desc text",
        // 支付相关按钮, 业务线传值, 订单没有默认值
        "payment_details_buttons":[
            {    
                // 按钮文案
                "button_text":"View Receipt",
                // 跳转链接
                "button_link":""
            }
        ]
      }
    }
  },
  methods: {
    onCustomButtonClick(item, index) {
      console.log('onCustomButtonClick', item, index)
    }
  },
  components:{
    KlkPaymentDetail,
    KlkOrderTableAtomic,
    KlkButton
  }
}
</script>
