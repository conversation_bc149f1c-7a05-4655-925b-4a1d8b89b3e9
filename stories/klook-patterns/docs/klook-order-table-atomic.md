# OrderTableAtomic 订单明细原子组件
> 版本请查看[knpm](https://knpm.klook.io/-/web/detail/@klook/klook-order-table-atomic)

```
import KlkOrderTableAtomic from '@klook/klook-order-table-atomic'
import '@klook/klook-order-table-atomic/dist/esm/index.css'
```

## 示例
:::demo
```html
<div style="width: 100%;">
  <div>
    <klk-order-table-atomic :data="data"></klk-order-table-atomic>
  </div>
</div>
```
:::

## 参数 Props

| 参数 | 介绍 | 类型 | 必传 | 默认值 |
|------|------|------|------|------|
|data|原子数据|atomicItem[]|true|-|

``` javascript
interface atomicItem {
  title: string,
  content: string,
  detail: string,
  copyable: boolean,
}
```

<script lang="ts">
import KlkOrderTableAtomic from '@/packages/klook-order-table-atomic/src';
import KlkButton from '@/packages/klook-ui/src/components/button'

export default {
  data() {
    return {
      data: [
        {
          title: 'Lable',
          content: 'HK\$ 7,500',
          detail: '2 * Room\nasda\nasdgfgfg',
          copyable: true,
        },
        {
          title: 'Lable',
          content: 'HK\$ 7,500',
          detail: '2 * Room',
        }
      ]
    }
  },
  components:{
    KlkOrderTableAtomic,
    KlkButton
  }
}
</script>
