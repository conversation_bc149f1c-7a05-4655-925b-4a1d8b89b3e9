# TW 发票组件

包含票据开具组件和票据改开组件

> 包名：@klook/klk-invoice-component


## 项目使用

```bash
npm install @klook/klk-invoice-component
```

<!-- InvoiceIssuance（票据开具组件 适用场景： 结算页/下单页）
InvoiceModification（票据改开组件 适用场景： 订单详情） -->
```javascript
import {
  InvoiceIssuance,
  InvoiceModification
} from "@klook/klk-invoice-component";
import "@klook/klk-invoice-component/dist/esm/index.css";
```


## InvoiceIssuance
票据开具组件

### 示例
:::demo

```html
<div style="width: 100%;">
  <div>
    <invoice-issuance
      ref="invoiceIssuanceRef"
      :platform="platform"
      :language="language"
      :basic-info="basicInfo"
      @saveSuccess="saveSuccess"
      @saveFail="saveFail"
    />
  </div>
</div>
```
:::
### InvoiceIssuance Props

| 参数             | 介绍          | 类型                   | 是否必传   | 默认值
| --------------- | ------------  | --------------------  | -------  | ------
| basic-info      | 票据开具组件的基础信息  | BasicInfoTypes   | 是      | null
| platform            | 平台            | mobile/desktop       | 否  | desktop
| language       | 语言      | Data.language               | 否     | en

```typescript
export interface BasicInfoTypes {
    "invoice_type": number; // 类型 1:代收转付 2:统一发票
    "has_receipt": boolean; // 是否包含收据 包含：true 不包含：false
    "email": string; // 邮箱
    "carrier_content": string;
    "carrier_type": number;
    "company_code": string;
    "company_name": string;
    "customer_type": number;
    "show_carrier": boolean;
}
```

### InvoiceIssuance Event
| 参数             | 介绍          | 回调参数             
| --------------- | ------------  | --------------------  
| close            | 弹窗内点击关闭按钮的回调  | 
| cancel          | 弹窗内点击取消按钮的回调   | 
| saveSuccess       | 点击保存按钮 校验通过时触发（组件不会请求接口）      |  callbackTypes 
| saveFail       | 点击保存按钮 校验不通过时触发（组件不会请求接口，生单时由业务线调接口验证）     |    callbackTypes

```typescript
interface callbackTypes {
    success: Boolean; // saveSuccess: true , saveFail: false
    result: {
        invoice_type: number; // 发票类型 ：代收转付：1 统一发票：2
        customer_type: number; // 个人：1 ， 公司：2
        email: String, // 发送邮箱
        carrier_type: number; // 载具类型：1: 手机条码载具  2:自然人凭证载具
        carrier_content: String, // 载具内容
        company_name: String, // 公司名称
        company_code: String // 公司统一编码  
    }
}
```

### InvoiceIssuance methods
| 参数             | 介绍          | 接受参数     | 类型             
| --------------- | ------------  | ---------- | ----------
| getInvoiceInfo    | 获取用户输入的form表单数据 | - | callbackTypes
| updateTravelerInfoEmail | 更新发票的邮箱。（如果组件已存在邮箱，则不更新）| email: string | 

### InvoiceIssuance 外部依赖
组件会依赖一些项目内的方法，需要通过 window 传递，**和 platform header/footer 组件所需依赖相同**
| 参数             | 介绍          | 类型             
| --------------- | ------------  | ----------
| i18n            | 多语言  | 
| tracker          | 埋点   | ITracker

```typescript
export interface ITracker {
  sendMixpanel: Function;
  inhouse: any;
  gtm: { sendGTMCustomEvent: Function };
}
```

## InvoiceModification
票据改开组件

### 示例
:::demo

```html
<div style="width: 100%;">
  <invoice-modification
    ref="invoiceModificationRef"
    :platform="platform"
    :language="language"
    :invoice-detail="invoiceDetail"
    @submitSuccess="submitSuccess"
    @submitFail="submitFail"
  ></invoice-modification>
</div>
```
:::

### InvoiceModification Props

| 参数             | 介绍          | 类型                   | 是否必传   | 默认值
| --------------- | ------------  | --------------------  | -------  | ------
| order-no      | 订单号  | string   | 是      | 
| platform            | 平台            | mobile/desktop       | 否  | desktop
| language       | 语言      | Data.language               | 否     | en
| invoice-detail       | 票据详情信息 订单后端返回给业务线后端的票据详情信息      | invoiceDetailTypes          | 是     | null

```typescript
// invoice details resp
interface invoiceDetailTypes {
    order_no: string; // 订单编号
    user_id: number; // 用户id
    invoice_type: number; // 发票开具类型 1:代收转付   2:统一发票
    is_show: Boolean; // 是否展示详情， true：展示  false：不展示
    customer_type_info: CustomerTypeInfo; // 客户信息
    status: {
        id: number; // 发票状态id， 未知：0， 开具中：1  ，开具完成：5 ，开具失败：10 ，部分开具：15
        desc_info: Common; // 状态描述信息
    }; // 状态信息
    tips: Common; // 文案信息
    details: {
        title: Common; // 标题信息
        contentList: {
            field_name: Common; // 字段名称信息
            field_val: Common; // 字段值信息
        }[]; // 内容列表
    }[]; // 发票详情信息
}

interface CustomerTypeInfo {
    customer_title: Common; // 客户主题信息
    customer_value: {
        common: Common;
        customer_type: number; //  1: 个人      2:统编
        is_edit: Boolean; // 是否展示修改 true：展示    fale：不展示
    }; // 客户类型值
}

interface Common {
    text: string; // 文本值
    text_color: string; // 文本颜色
    text_mask: string; // 脱敏文本信息
    name: string; // 字段值，前端可根据name进行字段判断
}

```

### InvoiceModification Event
| 参数             | 介绍          | 回调参数             
| --------------- | ------------  | --------------------  
| close            | 弹窗内点击关闭按钮的回调  | 
| cancel          | 弹窗内点击取消按钮的回调   | 
| submitSuccess       | 点击提交按钮 接口请求成功后才会触发（组件会请求票据改开接口）      |  ResTypes
| submitFail       | 点击提交按钮 接口请求失败后才会触发 （组件会请求票据改开接口）     | ResTypes


```typescript
interface ResTypes {
    success: boolean, // 成功： true  失败： false
    result: {
        ...
    },
    error: {
        ...
    }
}
```

### InvoiceModification 外部依赖
组件会依赖一些项目内的方法，需要通过 window 传递，**和 platform header/footer 组件所需依赖相同**
| 参数             | 介绍          | 类型             
| --------------- | ------------  | ----------
| $axios  | ajax 请求库 | { $get: Function; $post: Function } |
| i18n            | 多语言  | 
| tracker          | 埋点   | ITracker

```typescript
export interface ITracker {
  sendMixpanel: Function;
  inhouse: any;
  gtm: { sendGTMCustomEvent: Function };
}
```




<script lang="ts">
import {
  InvoiceIssuance,
  InvoiceModification
} from "@/packages/klk-invoice-component/src";
export default {
    components: {
        InvoiceIssuance,
        InvoiceModification
    },
  data(){
    return {
        platform: 'desktop',
        language: 'zh-CN',
        basicInfo: {
            invoice_type: 2,
            email: '<EMAIL>',
            has_receipt: false,
            show_carrier: false
        },
        invoiceDetail: {
            order_no: '1800337320',
            user_id: 0,
            invoice_type: 0,
            is_show: true,
            customer_type_info: {
            customer_title: {
                text: '類型',
                text_color: '#757575',
                text_mask: '',
                name: ''
            },
            customer_value: {
                text: '個人',
                text_color: '#212121',
                text_mask: '',
                name: '',
                customer_type: 0,
                is_edit: true,
                is_overdue: true,
                overdue_info: {
                text: 'overdue',
                text_color: '#212121'
                }
            }
            },
            status: {
            text: '已開立',
            text_color: '#08b371',
            text_mask: '',
            name: '',
            id: 5,
            desc_info: {
                text: '發票／收據已成功開立，並傳送至留存之電子信箱',
                text_color: '#757575',
                text_mask: '',
                name: ''
            }
            },
            status_tips: {
                "text": "Incorrect Business ID number. To issue a untified invoice, please update it by <span style=\'font-weight:bold;\'>2024-11-05</span>.",
                    "bg_color": "#FCF3DE"
            },
            tips: {
            text: '',
            text_color: '',
            text_mask: '',
            name: ''
            },
            details: [
            {
                title: {
                text: '寄送方式',
                text_color: '#212121',
                text_mask: '',
                name: ''
                },
                content_list: [
                {
                    field_name: {
                    text: '電子信箱',
                    text_color: '#757575',
                    text_mask: '',
                    name: 'email_name'
                    },
                    field_val: {
                    text: '<EMAIL>',
                    text_color: '#212121',
                    text_mask: '',
                    name: 'email_val'
                    }
                },
                {
                    field_name: {
                    text: '手機條碼',
                    text_color: '#757575',
                    text_mask: '',
                    name: ''
                    },
                    field_val: {
                    text: '1212121',
                    text_color: '#212121',
                    text_mask: '',
                    name: 'email_val'
                    }
                }
                ]
            },
            {
                title: {
                text: '發票／收據資訊',
                text_color: '#212121',
                text_mask: '',
                name: ''
                },
                content_list: [
                {
                    field_name: {
                    text: '類型',
                    text_color: '#757575',
                    text_mask: '',
                    name: ''
                    },
                    field_val: {
                    text: '個人',
                    text_color: '#212121',
                    text_mask: '',
                    name: ''
                    },
                    "field_tips": {
                       "text": "Incorrect Business ID number. To issue a untified invoice, please update it by <span style=\'font-weight:bold;\'>2024-11-05</span>.",
                        "bg_color": "#FCF3DE"
                    }
                },
                {
                    field_name: {
                    text: '',
                    text_color: '#757575',
                    text_mask: '',
                    name: ''
                    },
                    field_val: {
                    text: '8798798',
                    text_color: '#212121',
                    text_mask: '',
                    name: 'email_val'
                    }
                },
                {
                    field_name: {
                    text: '代收轉付收據號碼',
                    text_color: '#757575',
                    text_mask: '',
                    name: ''
                    },
                    field_val: {
                    text: 'TEST_MOCK_00001',
                    text_color: '#212121',
                    text_mask: '',
                    name: ''
                    }
                },
                {
                    field_name: {
                    text: '申請時間',
                    text_color: '#757575',
                    text_mask: '',
                    name: ''
                    },
                    field_val: {
                    text: '2024-06-25 02:37:26',
                    text_color: '#212121',
                    text_mask: '',
                    name: ''
                    }
                }
                ]
            }
            ]
        },

    }
  },
  methods: {
    saveSuccess(res) {
        console.log('save success', res)
    },
    saveFail(res) {
        console.log('save fail', res)
    },
    submitSuccess(res) {
        console.log('submit success', res)
        this.invoiceDetail.status.text = 'changed status'
    },
    submitFail(res) {
        console.log('submit fail', res)
    },
    getInvoiceInfo() {
        const res = (this.$refs.invoiceIssuanceRef as any).getInvoiceInfo()
        console.log('get invoice info!!', res)
    }
  }
}
</script>