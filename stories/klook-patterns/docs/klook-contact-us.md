# ContactUs 联系我们

> 版本请查看[knpm](https://knpm.klook.io/-/web/detail/@klook/klook-contact-us)

```
import KlkContactUs from '@klook/klook-contact-us'
import '@klook/klook-contact-us/dist/esm/index.css'
```

## 示例

:::demo

```html
<div style="width: 100%;">
  <div>
    <klk-button @click="visible = true">Show</klk-button>
    <klk-contact-us
      language="en-US"
      @contact-item-click="handleContactClick"
      platform="desktop"
      :visible.sync="visible"
      :bottom-sheet-props="bottomSheetProps"
      :partner-contact-list="partnerContactList"
      :klook-contact-list="klookContactList"
      :custom-contacts="customContacts"
    />
  </div>
</div>
```

:::

## 参数 Props

| 参数 | 介绍  | 类型  | 必传  | 默认值 |
| ------- | ------- | -------------- | ----- | ------ |
| language           | 语言 | Data.language  | true  | en     |
| bookingRefNo       | 订单 No, 埋点用     | string         | true  | -      |
| visible            | 是否可见，可通过 .sync 绑定               | boolean        | true  | false  |
| platform           | 平台   | desktop/mobile | true  | mobile |
| partnerContactList | 联系合作伙伴       | contactItem[]  | -     | -      |
| klookContactList   | 联系 Klook     | contactItem[]  | -     | -      |
| customContacts     | 自定义模块     | CustomContacts  | -     | -      |
| bottomSheetProps   | 自定义参数，透传[BottomSheet](https://design.klook.io/?path=/story/klook-ui-%E5%9F%BA%E7%A1%80%E7%BB%84%E4%BB%B6%E5%BA%93-bottomsheet--bottom-sheet-doc)组件 props | - | false | -      |

```javscript
interface contactItem {
  icon: string,
  title: string,
  detail: string,
  type: string, // show-展示 / phone-电话 / weChat-klook官方公众号 / email-邮箱
  copyable: boolean,
  deeplink: string,
}

interface CustomContacts {
  title: string,
  contact_list: contactItem[]
}
```


## Events

| 名称               | 介绍           | 参数   |
| ------------------ | -------------- | ------ |
| contact-item-click | 联系人点击回调 | (item) |

<script lang="ts">
import KlkContactUs from '@/packages/klook-contact-us/src';
import KlkButton from '@/packages/klook-ui/src/components/button'

export default {
  data() {
    return {
      visible: false,
      bottomSheetProps: {
        transfer: true,
      },
      customContacts: {
        title: '自定义',
        contact_list: [
          {
            icon: "",
            title: "+8613888888888",
            detail: "电话服务时间：周一到周五 10:00-18:00\n支持语言：英文 中文",
            type: "phone",
            copyable: true,
            deeplink: ""
          },
          {
            icon: "",
            title: "+8613888888888",
            detail: "08:00~22:00 online",
            type: "phone",
            copyable: true,
            deeplink: "https://www.baidu.com"
          },
          {
            icon: "",
            title: "<EMAIL>",
            detail: "请用下单邮件联系并提供订单号",
            type: "email",
            copyable: true,
            deeplink: ""
          }
        ]
      },
      partnerContactList: [
        {
          icon: "",
          title: "+8613888888888",
          detail: "电话服务时间：周一到周五 10:00-18:00\n支持语言：英文 中文",
          type: "phone",
          copyable: true,
          deeplink: ""
        },
        {
          icon: "",
          title: "+8613888888888",
          detail: "08:00~22:00 online",
          type: "phone",
          copyable: true,
          deeplink: "https://www.baidu.com"
        },
        {
          icon: "",
          title: "<EMAIL>",
          detail: "请用下单邮件联系并提供订单号",
          type: "email",
          copyable: true,
          deeplink: ""
        },
      ],
      klookContactList: [
        {
          "icon": "https://res.klook.com/image/upload/v1703227034/UED_new/Platform/platform_bookingDetail_23Q4/icon_brand_wechat_circle.svg",
          "title": "关注Klook公众号",
          "detail": "服务时间：周一至周日 08:00 - 22:00",
          "type": "weChat",
          "copyable": true,
          "deeplink": "",
          "contact_source": null
        },
        {
          icon: "",
          title: "在线联系客服",
          detail: "24小时在线",
          type: "show",
          copyable: false,
          deeplink: "asd"
        },
        {
          icon: "",
          title: "Klook 度假玩乐精选",
          detail: "通过微信公众号联系klook",
          type: "weChat",
          deeplink: "ss"
        },
      ]
    }
  },
  methods: {
    handleContactClick(item) {
      console.log(item)
    }
  },
  components:{
    KlkContactUs,
    KlkButton
  }
}
</script>
