## 演示

### 基础徽章

在基础场景下，徽章显示环保认证图标、描述文本，并在桌面端展示右侧的箭头图标。点击徽章可跳转到指定链接。

:::demo

```html
<klk-eco-badge
  type="desktop"
  imageUrl="https://res.klook.com/image/upload/icon_eco_badge_ckeng4.png"
  text="Certified sustainable partner"
  link="#"
  width="80%"
  backgroundColor="#ffebcd"
  fontWeight="bold"
  :showArrow="false"
/>
```

:::

### 桌面端场景

桌面端场景下，徽章最多展示一行文字，超出部分显示省略号，右侧显示箭头图标。

:::demo

```html
<klk-eco-badge
  type="desktop"
  imageUrl="https://res.klook.com/image/upload/icon_eco_badge_ckeng4.png"
  text="This is a very long text that should be truncated with an ellipsis when it exceeds one line on desktop."
  link="#"
/>
```

:::

### 移动端场景

移动端场景下，徽章最多展示两行文字，超出部分显示省略号，不显示箭头图标。

:::demo

```html
<klk-eco-badge
  type="mobile"
  imageUrl="https://res.klook.com/image/upload/icon_eco_badge_ckeng4.png"
  text="This is a very long text that should be truncated with an ellipsis when it exceeds two lines on mobile."
  link="#"
/>
```

:::

## API

### KlookEcoBadge Props

| 名称            | 类型    | 默认值                          | 说明                     |
| --------------- | ------- | ------------------------------- | ------------------------ |
| type            | string  | 'desktop'                       | 场景类型，决定展示样式   |
| imageUrl        | string  | ''                              | 徽章图标的 URL           |
| text            | string  | 'Certified sustainable partner' | 徽章的描述文本           |
| link            | string  | '#'                             | 点击徽章后跳转的链接地址 |
| width           | string  | '100%'                          | 徽章的宽度               |
| backgroundColor | string  | '#e4f7f6'                       | 徽章的背景颜色           |
| showArrow       | boolean | true                            | 是否显示                 |
