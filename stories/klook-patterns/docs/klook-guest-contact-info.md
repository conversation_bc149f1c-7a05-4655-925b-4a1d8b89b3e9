# klkGuestContact

## 示例
### klkGuestContactEmail


:::demo
```html
<div>
  <!-- :rules="formRules" -->
<klk-form ref="form" :model="contactInfo" style="max-width: 420px;">
  <klk-form-item label="邮箱" required prop="email" :rules="formRules.email">
    <klk-guest-contact-email ref="email" :hack-dynamic-autocomplete="hackDynamicAutocomplete" :email-suffix-list="emailSuffixList" v-model="contactInfo.email" @blur="emailBlur" @change="emailChange" @input="emailInput" email-tip="emailTip" autocomplete="email" name="email" @select="selectEmail"></klk-guest-contact-email>
  </klk-form-item>
</div>
```
:::


### 提交表单
:::demo
```html
<div>
  <klk-input v-model="contactInfo.lastName" placeholder="lastName" name="lastName" autocomplete="family-name"></klk-input>
  <klk-input v-model="contactInfo.firstName" placeholder="firstName" name="firstName" autocomplete="given-name" ></klk-input>
  <klk-input v-model="contactInfo.tel" placeholder="tel" name="tel" autocomplete="tel-national"></klk-input>
  <klk-input v-model="contactInfo.email" placeholder="email" name="email" autocomplete="email"></klk-input>

  <klk-button @click="submit">submitForm2Browser</klk-button>
 <!-- <klk-guest-contact-email-form-item v-model="email"></klk-guest-contact-email-form-item> -->
</div>
```
:::

### 使用
#### 初始化
```javascript
import { initGuestContactInfo } from '@klook/guest-contact-info'


mounted() {
   // 在表单模块部分, 在调用set和get之前调用
   initGuestContactInfo('Hotel' | 'Insurance' | 'Hotel' | 'Car' | 'TTD' | 'Transfer')
}
```

#### 提交表单数据
```javascript
import { submitForm2Browser, getGuestAndContactInfo } from '@klook/guest-contact-info'


payBtn() {
  // 校验通过后
  await submitLocalForm2Browser()
}

aysnc submitLocalForm2Browser() {
    const localData = getGuestAndContactInfo()
    const guest = localData?.guestList?.[0]
    submitForm2Browser({
      lastName: guest?.lastName,
      firstName: guest?.firstName,
      email: localData?.emailInfo?.email,
      tel: localData?.phoneInfo?.phoneNum
    })
}
```

## 详细配置

### props

| 参数              | 介绍                                                           | 类型                                           | 可选值                                             | 默认值         |
| ----------------- | -------------------------------------------------------------- | ---------------------------------------------- | -------------------------------------------------- | -------------- |
| platform          | 平台                                                           | string                                         | mobile, desktop                                    |                |

## 事件


## slot


<script lang="ts">
// import '@klook/guest-contact-info/dist/esm/index.css'

import { KlkGuestContactEmail, KlkGuestContactEmailFormItem, setGuestAndContactInfo, getGuestAndContactInfo, submitForm2Browser  } from '@/packages/klook-guest-contact-info/src/index.ts'

export default {
  components: {
    KlkGuestContactEmail,
    KlkGuestContactEmailFormItem
  },
  data() {
    return {
      hackDynamicAutocomplete: false,
      formRules: {
        email: [
            {
                "required": 1,
                "message": "请输入",
                "trigger": [
                    "blur"
                ]
            },
            {
                "message": "请填写有效电子邮箱",
                "trigger": [
                    "blur"
                ],
                "pattern": /^(?=\s*\w+(?:\.{0,1}[\w-]+)*@[a-zA-Z0-9]+(?:[-.][a-zA-Z0-9]+)*\.[a-zA-Z]+\s*$).{1,150}$/
            }
        ]
      },
      contactInfo: {
        email: '<EMAIL>',
        lastName: '',
        firstName: '',
        tel: ''
      }
    }
  },
  computed: {
    emailSuffixList() {
      return [
        "gmail.com",
        "yahoo.com",
        "hotmail.com",
        "aol.com",
        "fastmail.com",
        "gmx.com",
        "gawab.com",
        "icloud.com",
        "live.com",
        "mac.com",
        "msn.com",
        "mail.com",
        "outlook.com",
        "protonmail.com",
        "zoho.com"
      ]
    },
  },
  methods: {
    submit() {
      submitForm2Browser(this.contactInfo)
    },

    parse(data) {
      return JSON.stringify(this[data], null, 8)
    },
    emailInput(a) {
      console.log('emailInput', a, this.contactInfo)
    },
  
    emailChange(a) {
      console.log('emailChange', a, this.contactInfo)
    },

    emailBlur(a) {
      console.log('emailBlur', a, this.contactInfo)
    },
    selectEmail(item) {
      console.log('selectEmail', item, this.contactInfo)
      // this.$refs.form.validate('email')
    }
  }
}
</script>

<style lang="scss">

</style>
