# Review 评价
> 版本请查看[knpm](https://knpm.klook.io/-/web/detail/@klook/klook-review)

```
import KlkReview from '@klook/klook-review'
import '@klook/klook-review/dist/esm/index.css'
```

## 示例
:::demo
```html
<div style="width: 100%;">
  <div>
    <klk-review
      v-model="star"
      platform="mobile"
      language="en-US"
      :is-multidimensional="false"
      deeplink="https://uat2.fat.klook.io/zh-CN/review/?booking_ref_no=VFS531965&from=BookingListPage&type=unreview"
    />

  </div>
</div>
```
:::

## 参数 Props

| 参数 | 介绍 | 类型 | 必传 | 默认值 |
|------|------|------|------|------|
|language|语言|Data.language|true|en|
|platform|平台|mobile/desktop|true|mobile|
|value / v-model |评价分绑定值|number|true|0|
|is-multidimensional|是否多纬度评价|string|-|false|
|deeplink |跳转的评价deeplink|sting|true|-|


## Events

| 名称 | 介绍 | 参数 |
|-----|------|-----|


<script lang="ts">
import KlkReview from '@/packages/klook-review/src';
// import KlkButton from '@/packages/klook-ui/src/components/button'

export default {
  data() {
    return {
      star: 0,
    }
  },
  methods: {
    onReviewClick() {
      console.log('onReviewClick')
    },
    onReviewDetailClick() {
      console.log('onReviewDetailClick')
    },
    reviewChange(star) {
      console.log(star)
    }
  },
  components:{
    KlkReview,
    // KlkButton
  }
}
</script>
