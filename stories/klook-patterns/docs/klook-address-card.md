# AddressCard 地址卡片
> 版本请查看[knpm](https://knpm.klook.io/-/web/detail/@klook/klook-address-card)

```
import KlkAddressCard from '@klook/klook-address-card'
import '@klook/klook-address-card/dist/esm/index.css'
```

## 示例
:::demo
```html
<div style="width: 100%;">
  <div>
    <klk-address-card
      :visible.sync="visible"
      booking-ref-no="1234"
      language="en-US"
      address-name-order="昆明君乐酒店"
      address-detail-order="Grand Park KunmingGrand Park KunmingGrand Park KunmingGrand"
      address-name-destination="洪化桥20号"
      address-detail-destination="20 Hong Hua Qiao20 Hong Hua Qiao"
    />

  </div>
  <klk-button @click="visible = true">Show</klk-button>
</div>
```
:::

## 参数 Props

| 参数 | 介绍 | 类型 | 必传 | 默认值 |
|------|------|------|------|------|
|language|语言|Data.language|true|en|
|visible.sync|是否展示|boolean|false|false|
|show-save-button|是否展示保存按钮|boolean|false|true|
|show-copy|是否展示复制|boolean|false|true|
|booking-ref-no|订单No,埋点用|string|true|-|
|address-name-order|地点名称(下单语言)|string|true|-|
|address-detail-order|地点信息(下单语言)|string|true|-|
|address-name-destination|地点名称(目的地语言)|string|true|-|
|address-detail-destination|地点信息(目的地语言)|string|true|-|

## Events

| 名称 | 介绍 | 参数 |
|-----|------|-----|
| close | 点击关闭按钮 | null |


<script lang="ts">
import KlkAddressCard from '@/packages/klook-address-card/src';
import KlkButton from '@/packages/klook-ui/src/components/button'

export default {
  data() {
    return {
      visible: false,
    }
  },
  components:{
    KlkAddressCard,
    KlkButton
  }
}
</script>
