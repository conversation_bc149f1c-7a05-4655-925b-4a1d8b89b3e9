# 响应式业务组件

因为poi页是响应式，这个业务包主要用来放poi页里面用到的响应式组件

> 包名：@klook/klook-responsive 版本0.0.2-beta.1

## Swiper

### 示例

:::demo
```html
<div >
    <klk-res-swiper :controller-offset="12">
        <klk-res-swiper-item
          class="klk-col-md-3 klk-col-lg-3 klk-col-xl-4 klk-col-sm-1-2"
          v-for="(item, index) in 8"
          :key="index"
        >
          <div
            class="klk-card-swiper-demo-item"
          >{{ index }}</div>
        </klk-res-swiper-item>
    </klk-res-swiper>
</div>

<style>
</style>
```
:::


### 使用Swiper

```js
import { CardSwiper, CardSwiperItem } from '@klook/klook-responsive'
import '@klook/klook-responsive/dist/cjs/index.css'

new Vue ({
  components: {
    'KlkResSwiper': CardSwiper,
    'KlkResSwiperItem': CardSwiperItem
  }
})

```

### CardSwiper Props
| 参数        | 介绍          | 类型      | 可选值  | 是否必传 | 默认值 |
| ---------- | ------------  | -------  | ------- | ------- | ------ |
| controller-offset       | 箭头和卡片的距离           | number | string   | -      | 否       | 10

### CardSwiperItem Class
| 参数        | 介绍          | 可选值  | 是否必传 | 默认值 |
| ---------- | ------------  | ------- | ------- | ------ |
| klk-col-sm       | 在小于等于767px的窗口下显示几个卡片           |  klk-col-sm-1-2, klk-col-sm-1-5, klk-col-sm-2-2, klk-col-sm-2-5      | 否       | 1-2
| klk-col-md       | 在768px到991px的窗口下显示几个卡片           |  klk-col-md-1, klk-col-md-2, klk-col-md-3, klk-col-md-4      | 否       | 3
| klk-col-lg       | 在992px到1199px的窗口下显示几个卡片           |  klk-col-lg-1, klk-col-lg-2, klk-col-lg-3, klk-col-lg-4      | 否       | 3
| klk-col-xl       | 在大于等于1200的窗口下显示几个卡片           |  klk-col-xl-1, klk-col-xl-2, klk-col-xl-3, klk-col-xl-4      | 否       | 3

<script lang="ts">
export default {
  data(){
    return {
      value1: 340
    }
  },

  computed: {
    containerStyle() {
      return {
        width: this.value1 ? `${this.value1}px` : '100%',
      }
    },
  },

  methods: {
    
  }
}

</script>

<style lang="scss" >
.demo-container {
  display: flex;
  justify-content: space-between;

  .demo-form {
    width: 40%;
    flex-shrink: 0;
  }

  .demo-component {
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    width: 28%;
    flex-shrink: 0;
  }

  .demo-image-component {
    width: 38%;
    flex-shrink: 0;
    background: #E5E5E5;
  }

  .demo-horizontal-component {
    width: 60%;
    flex-shrink: 0;
    background: #fff;
    padding: 15px;
  }
}
.klk-card-swiper-demo-item {
  height: 150px;
  background-color: #fa541c;
  border-radius: 2px;
  color: white;
  font-size: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}
</style>
