# KlkUserReview 组件

> Package: @klook/klk-user-review
> Version: 0.0.1

## 概述
C端评论相关的组件，包含
- KlkMiniReview / klk-mini-review：迷你评论入口
- KlkReview / klk-review：普通评论概览楼层入口
- KlkReviewDetail / klk-review-detail：评论列表，用于页面中平铺展示所有评论
- KlkReviewCard / klk-review-card：评论卡片，用于在页面中展示单个评论信息
- KlkReviewRating / klk-review-rating：评论评分展示

```
import { KlkMiniReview, KlkReviewRating, KlkReview, KlkReviewCard, KlkReviewDetail } from '@klook/klk-user-review'
import '@klook/klk-user-review/dist/esm/index.css'
```

## KlkMiniReview

### 示例

:::demo
```html
<div class="demo-container">
  <klk-form class="demo-horizontal-form">
    <klk-form-item label="Theme">
      <klk-radio-group v-model="mockMiniRatingInfoProps.theme">
        <klk-radio group-value="dark">dark(default)</klk-radio>
        <klk-radio group-value="light">light</klk-radio>
      </klk-radio-group>
    </klk-form-item>
    <klk-form-item label="Direction">
      <klk-radio-group v-model="mockMiniRatingInfoProps.direction">
        <klk-radio group-value="left">left(default)</klk-radio>
        <klk-radio group-value="right">right</klk-radio>
      </klk-radio-group>
    </klk-form-item>
    <klk-form-item label="Show Detail">
      <klk-switch v-model="mockMiniRatingInfoProps.showDetail" />
    </klk-form-item>
    <klk-form-item label="Clickable">
      <klk-switch v-model="mockMiniRatingInfoProps.clickable" />
    </klk-form-item>
    <klk-form-item label="Enable Review Schema">
      <klk-switch v-model="mockMiniRatingInfoProps.enableReviewSchema" @change="updateReviewSchemaJson(mockMiniRatingInfo)"/>
    </klk-form-item>
    <klk-form-item label="MiniReview Schema Json" v-if="mockMiniRatingInfoProps.enableReviewSchema">
      <klk-input
        v-model="reviewSchemaJson"
        style="word-break: break-all;"
        :rows="10"
        type="textarea"
      />
    </klk-form-item>
  </klk-form>
  <div class="demo-horizontal-component">
    <div class="demo-vertical-block">
      <div class="demo-component-sub-title">desktop</div>
      <klk-mini-review
        platform="desktop"
        key="klk-mini-review-desktop"
        language="en_US"
        :theme="mockMiniRatingInfoProps.theme"
        :direction="mockMiniRatingInfoProps.direction"
        :show-detail="mockMiniRatingInfoProps.showDetail"
        :clickable="mockMiniRatingInfoProps.clickable"
        :enable-review-schema="mockMiniRatingInfoProps.enableReviewSchema"
        :rating-info="mockMiniRatingInfo"
        :extra-data="mockExtraData"
        :login-option="mockLogin"
        @book="goBooking"
        @showMore="showMore"
        ref="miniReviewRef"
      />

      <div v-if="mockMiniRatingInfoProps.enableReviewSchema" style="margin-top: 16px;">
        <p style="font-weight: bold;">head() 返回的 Schema 数据：</p>
        <pre style="background:#f6f8fa;padding:8px 12px;border-radius:4px;max-width:100%;overflow:auto;">
          {{ reviewSchemaJson }}
        </pre>
      </div>
    </div>
    <div class="demo-vertical-block">
      <div class="demo-component-sub-title">mobile</div>
      <klk-mini-review
        platform="mobile"
        key="klk-mini-review-mobile"
        language="en_US"
        :theme="mockMiniRatingInfoProps.theme"
        :direction="mockMiniRatingInfoProps.direction"
        :show-detail="mockMiniRatingInfoProps.showDetail"
        :clickable="mockMiniRatingInfoProps.clickable"
        :enable-review-schema="mockMiniRatingInfoProps.enableReviewSchema"
        :rating-info="mockMiniRatingInfo"
        :extra-data="mockExtraData"
        :login-option="mockLogin"
        @book="goBooking"
        @showMore="showMore"
        ref="miniReviewRef"
      />
    </div>
  </div>
</div>
```
:::

### 参数说明
| 参数                  | 介绍                                       | 类型                           | 必传 | 默认值                               |
|---------------------|------------------------------------------|------------------------------| ---- |-----------------------------------|
| platform            | 平台类型                                     | string                        | 否   |                                   |
| language            | 语言                                       | string                        | 否   |                                   |
| theme               | 配置颜色                                     | 'dark' \| 'light'             | 否   | 'dark'                            |
| direction           | 配置布局方向                                   | 'left' \| 'right'             | 否   | 'left'                            |
| showDetail          | 配置是否打开评论详情                               | boolean                       | 否   | false                             |
| ratingInfo          | 评分信息                                     | IRatingInfo                   | 否   | null                              |
| clickable           | 用于区分样式，是否为可点击样式 | boolean                       | 否   | false                             |
| enableReviewSchema  | 配置是否开启seo评论结构化数据挂载                       | boolean                       | 否   | false                             |
| extraData           | 调用接口获取数据时携带的额外业务参数信息                     | IExtraData                    | 是   | { aggregate_id: '', template_id: '' } |
| loginOption         | 传入登陆方法，用于当用户点击有帮助按钮后的登陆处理，如果不传入该参数，则会默认使用打开登陆页的方式进行跳转| ILoginOptions                 | 否   | null                              |

### 类型定义说明

```typescript
// 评分信息
interface IRatingInfo {
  rating_info: {
    avg_rating: number,
    max_rating: number,
    rating_desc?: string,
    rating_icon: string,
    review_count_desc: string
    review_count: number
  }
}

interface IExtraData {
  template_id: string,
  aggregate_id: string,
  [key: string]: any
}

interface ILoginOptions {
  onLogin: (next: () => void) => void
}
```


## KlkReview

### 示例
:::demo
```html
<div class="demo-vertical-container">
  <klk-form class="demo-vertical-form">
    <klk-form-item label="Title">
      <klk-input
        v-model="mockOverviewDataProps.title"
        style="word-break: break-all;"
      />
    </klk-form-item>
    <klk-form-item label="Loading">
      <klk-switch v-model="mockOverviewDataProps.loading" />
    </klk-form-item>
    <klk-form-item label="Show Detail">
      <klk-switch v-model="mockOverviewDataProps.showDetail" />
    </klk-form-item>
    <klk-form-item label="Hide Show More">
      <klk-switch v-model="mockOverviewDataProps.hideShowMore" />
    </klk-form-item>
    <klk-form-item label="Enable Review Schema">
      <klk-switch v-model="mockOverviewDataProps.enableReviewSchema" @change="updateReviewSchemaJson(mockOverviewData)"/>
    </klk-form-item>
    <klk-form-item label="Review Schema Json" v-if="mockOverviewDataProps.enableReviewSchema">
      <klk-input
        v-model="reviewSchemaJson"
        style="word-break: break-all;"
        :rows="10"
        type="textarea"
      />
    </klk-form-item>
  </klk-form>
  <div class="demo-vertical-component">
     <klk-tabs>
      <klk-tab-pane label="desktop" class="demo-component">
        <klk-review
          key="klk-review-desktop"
          platform="desktop"
          language="en_US"
          :title="mockOverviewDataProps.title"
          :loading="mockOverviewDataProps.loading"
          :show-detail="mockOverviewDataProps.showDetail"
          :hide-show-more="mockOverviewDataProps.hideShowMore"
          :review-data="mockOverviewData"
          :extra-data="mockExtraData"
          :login-option="mockLogin"
          @book="goBooking"
        />
      </klk-tab-pane>
      <klk-tab-pane label="mobile" class="demo-component">
        <klk-review
          key="klk-review-mobile"
          platform="mobile"
          language="en_US"
          :title="mockOverviewDataProps.title"
          :loading="mockOverviewDataProps.loading"
          :show-detail="mockOverviewDataProps.showDetail"
          :hide-show-more="mockOverviewDataProps.hideShowMore"
          :review-data="mockOverviewData"
          :extra-data="mockExtraData"
          :login-option="mockLogin"
          @book="goBooking"
        />
      </klk-tab-pane>
    </klk-tabs>
    <div v-if="mockOverviewDataProps.enableReviewSchema" style="margin-top: 16px;">
      <p style="font-weight: bold;">head() 返回的 Schema 数据：</p>
      <pre style="background:#f6f8fa;padding:8px 12px;border-radius:4px;max-width:100%;overflow:auto;">
        {{ reviewSchemaJson }}
      </pre>
    </div>
  </div>
</div>
```
:::

### 参数说明
| 参数                  | 介绍                                                                                                                                               | 类型                           | 必传 | 默认值                               |
|---------------------|--------------------------------------------------------------------------------------------------------------------------------------------------|------------------------------| ---- |-----------------------------------|
| platform            | 平台类型                                                                                                                                             | string                        | 否   |                                   |
| language            | 语言                                                                                                                                               | string                        | 否   |                                   |
| title               | 楼层标题，不传值不展示                                                                                                                                      | string                        | 否   |                                   |
| loading             | overview模块骨架屏样式，若数据是通过异步获取的，可以传入该参数，否则无需使用                                                                                                                                     | boolean                       | 否   | false                             |
| reviewData          | 入口数据，当评论数少于x条，平均分少于x时（条件参考[PRD](https://klook.larksuite.com/docx/D8mRdEbe1onpO3xeGDGuT5nBsVn)），rating_info返回null，sub_rating_list返回[]，前端不展示顶部评分信息 | IReviewData                   | 是   |                                   |
| extraData           | 调用接口获取数据时携带的额外业务参数信息                                                                                                                             | IExtraData                    | 是   | { aggregate_id: '', template_id: '' } |
| enableReviewSchema  | 是否开启seo评论结构化数据挂载                                                                                                               | boolean                       | 否   | false                             |
| loginOption         | 传入登陆方法，用于当用户点击有帮助按钮后的登陆处理，如果不传入该参数，则会默认使用打开登陆页的方式进行跳转                                                                                                                                             | ILoginOptions                 | 否   | null                              |
| showDetail         | 点击see more 能否打开评论详情列表                                                                                                                            | boolean                 | 否   | true|
| hideShowMore         | 是否隐藏查看更多评论按钮                                                                                                                            | boolean                 | 否   | false|


### 类型定义说明

```typescript
interface IReviewData extends IReviewRating {
  ai_summary_info: AiReviewData,
  filter: {
    filter_list: IFilterItem[],
    sort_list: ISortItem[]
  },
  first_page_review_limit: number,
  reviews: {
    title: string,
    total: number,
    current_page: number,
    has_next: boolean,
    each_page_count: number,
    review_list: IReviewItem[]
  },
  track_info: {
    extra: {
      aggregate_id: string,
      aggregate_key: string
    }
  }
}

interface IReviewRating extends IRatingInfo {
  sub_rating_list: ISubRating[],
  merchant_info?: {
    merchant_logo: string,
    merchant_name: string
  }
}

interface IRatingInfo {
  rating_info: {
    avg_rating: number,
    max_rating: number,
    rating_desc?: string,
    rating_icon: string,
    review_count_desc: string
    review_count: number
  }
}

interface ISubRating {
  sub_rating_desc: string,
  sub_rating: number
}

interface AiReviewData {
  icon: string,
  title: string,
  mini_info: AiReviewMiniInfoData,
  hide_helpful_action?: Boolean,
  summary_list: AiReviewSummaryItem[],
  platform?: string,
  helpful_api?: string
}

interface AiReviewSummaryItem {
  id: number,
  type: string,
  title: string,
  content: string,
  helpful_status: number
}

interface AiReviewMiniInfoData {
  tips: string,
  pop_info: AiReviewPopInfo,
}

interface AiReviewPopInfo {
  title: string,
  content: string
}

interface IReviewItem {
  review_id: string,
  user_info: IUserInfo,
  review_content: string,
  translate_content: string,
  show_translation?: boolean,
  has_liked?: boolean,
  liked_count?: number,
  product_info?: {
    product_name: string,
    id: string,
    is_booking_now?: boolean,
  },
  review_image_list?: IReviewImage[],
  reply?: {
    reply_from: string,
    reply_content: string
  }
}

interface IUserInfo {
  user_name: string,
  user_avatar: string,
  user_rating: number,
  rating_desc: string,
  rating_time: string,
}

interface IReviewImage {
  id: string
  resize_url: string
  url: string
}

```


## KlkReviewDetail

### 示例
:::demo
```html
<div class="demo-vertical-container">
  <klk-form class="demo-vertical-form">
    <klk-form-item label="Platform">
      <klk-radio-group v-model="mockDetailDataProps.platform">
        <klk-radio group-value="desktop">desktop</klk-radio>
        <klk-radio group-value="mobile">mobile</klk-radio>
      </klk-radio-group>
    </klk-form-item>
    <klk-form-item label="Title">
      <klk-input
        v-model="mockDetailDataProps.title"
        style="word-break: break-all;"
      />
    </klk-form-item>
    <klk-form-item label="Loading">
      <klk-switch v-model="mockDetailDataProps.loading" />
    </klk-form-item>
    <klk-form-item label="Enable Review Schema">
      <klk-switch v-model="mockDetailDataProps.enableReviewSchema" @change="updateReviewSchemaJson(mockOverviewData)"/>
    </klk-form-item>
    <klk-form-item label="Review Schema Json" v-if="mockDetailDataProps.enableReviewSchema">
      <klk-input
        v-model="reviewSchemaJson"
        style="word-break: break-all;"
        :rows="10"
        type="textarea"
      />
    </klk-form-item>
  </klk-form>
  <div class="demo-vertical-component">
    <klk-review-detail
      :key="`klk-review-detail-${mockDetailDataProps.platform}`"
      :platform="mockDetailDataProps.platform"
      language="en_US"
      :display="mockDetailDataProps.display"
      :title="mockDetailDataProps.title"
      :loading="mockDetailDataProps.loading"
      :extra-data="mockExtraData"
      :login-option="mockLogin"
      :review-data="mockOverviewData"
      :enable-review-schema="mockDetailDataProps.enableReviewSchema"
      @book="goBooking"
    >
      <template v-slot:review-footer="{ reviewInfo }">
        这是一个插槽，reviewID：<div>{{ reviewInfo.review_id }}</div>
      </template>
    </klk-review-detail>
    <div v-if="mockDetailDataProps.enableReviewSchema" style="margin-top: 16px;">
      <p style="font-weight: bold;">head() 返回的 Schema 数据：</p>
      <pre style="background:#f6f8fa;padding:8px 12px;border-radius:4px;max-width:100%;overflow:auto;">
        {{ reviewSchemaJson }}
      </pre>
    </div>
  </div>
</div>
```
:::

### 参数说明
| 参数                  | 介绍                                                               | 类型                           | 必传 | 默认值                                   |
|---------------------|------------------------------------------------------------------|------------------------------|----|---------------------------------------|
| platform            | 平台类型                                                             | string                        | 否  |                                       |
| language            | 语言                                                               | string                        | 否  |
| renderMode          | 渲染模式，有overview入口时为csr                                            | 'ssr' \| 'csr'                | 否  | 'ssr'                                 |
| display             | 评论详情展示方式，inline表示作为楼层展示，modal表示会在弹层内展示，展示方式会决定图片的预览模式以及评论列表的翻页效果 | 'modal' \| 'inline'           | 否  | 'inline'                              |
| title               | 楼层标题，不传值不展示                                                      | string                        | 否  |                                       |
| enableReviewSchema  | 是否开启seo评论结构化数据挂载                                             | boolean                       | 否  | false                                 |
| reviewData          | 评论数据，ssr时为对应的数据结构（页面overview数据+filter+列表首屏数据），csr时为不需要传递                                   | IReviewData                   | 是  |                                       |
| loading             | overview模块骨架屏样式，若数据是通过异步获取的，可以传入该参数，否则无需使用                       | boolean                       | 否  | false                                 |
| extraData           | 额外数据                                                             | IExtraData                    | 是  | { aggregate_id: '', template_id: '' } |
| loginOption         | 传入登陆方法，用于当用户点击有帮助按钮后的登陆处理，如果不传入该参数，则会默认使用打开登陆页的方式进行跳转                                                             | ILoginOptions                 | 否  | null                                  |
| sourceReviewId         | （仅对弹层modal类型生效）传入时，会自动跳转到该reviewid的位置，不传入则会跳转到评论列表模块                                                      | string                 | 否  | ''                                    |
| isInitPosition         | （仅对弹层modal类型生效）传入时，控制是否有默认滚动定位行为                                                      | boolean                 | 否  | true                                    |


### 类型定义说明

```typescript
interface IReviewData extends IReviewRating {
  ai_summary_info: AiReviewData,
  filter: {
    filter_list: IFilterItem[],
    sort_list: ISortItem[]
  },
  first_page_review_limit: number,
  reviews: {
    title: string,
    total: number,
    current_page: number,
    has_next: boolean,
    each_page_count: number,
    review_list: IReviewItem[]
  },
  track_info: {
    extra: {
      aggregate_id: string,
      aggregate_key: string
    }
  }
}

interface IReviewRating extends IRatingInfo {
  sub_rating_list: ISubRating[],
  merchant_info?: {
    merchant_logo: string,
    merchant_name: string
  }
}

interface IRatingInfo {
  rating_info: {
    avg_rating: number,
    max_rating: number,
    rating_desc?: string,
    rating_icon: string,
    review_count_desc: string
    review_count: number
  }
}

interface ISubRating {
  sub_rating_desc: string,
  sub_rating: number
}

interface AiReviewData {
  icon: string,
  title: string,
  mini_info: AiReviewMiniInfoData,
  hide_helpful_action?: Boolean,
  summary_list: AiReviewSummaryItem[],
  platform?: string,
  helpful_api?: string
}

interface AiReviewSummaryItem {
  id: number,
  type: string,
  title: string,
  content: string,
  helpful_status: number
}

interface AiReviewMiniInfoData {
  tips: string,
  pop_info: AiReviewPopInfo,
}

interface AiReviewPopInfo {
  title: string,
  content: string
}

interface IReviewItem {
  review_id: string,
  user_info: IUserInfo,
  review_content: string,
  translate_content: string,
  show_translation?: boolean,
  has_liked?: boolean,
  liked_count?: number,
  product_info?: {
    product_name: string,
    id: string,
    is_booking_now?: boolean,
  },
  review_image_list?: IReviewImage[],
  reply?: {
    reply_from: string,
    reply_content: string
  }
}

interface IUserInfo {
  user_name: string,
  user_avatar: string,
  user_rating: number,
  rating_desc: string,
  rating_time: string,
}

interface IReviewImage {
  id: string
  resize_url: string
  url: string
}

```

## KlkReviewCard

### 示例

:::demo
```html
<div class="demo-container">
  <klk-form class="demo-horizontal-form">
    <klk-form-item label="PreviewMode(仅在full模式下生效)">
      <klk-radio-group v-model="mockCardDataProps.previewMode">
        <klk-radio group-value="modal">modal(default)</klk-radio>
        <klk-radio group-value="inline">inline</klk-radio>
        <klk-radio group-value="fullscreen">fullscreen</klk-radio>
      </klk-radio-group>
    </klk-form-item>
    <klk-form-item label="Type">
      <klk-radio-group v-model="mockCardDataProps.type">
        <klk-radio group-value="brief">brief</klk-radio>
        <klk-radio group-value="full">full(default)</klk-radio>
      </klk-radio-group>
    </klk-form-item>
  </klk-form>
  <div class="demo-horizontal-component">
    <klk-review-card
      :review-info="mockCardData"
      :preview-mode="mockCardDataProps.previewMode"
      :type="mockCardDataProps.type" />
  </div>
</div>
```
:::

### 参数说明

| 参数                  | 介绍                                                                                | 类型                           | 必传 | 默认值                               |
|---------------------|-----------------------------------------------------------------------------------|------------------------------| ---- |-----------------------------------|
| reviewInfo          | 评论简略信息，只展示简略信息，如同overview组件中的相关评论。最全的评论卡片信息包括：用户信息、商品信息、评论内容、评论图片。一般用于页面楼层中（如相关评论） | IReviewItem                   | 是   |                                   |
| type                | 表示卡片是否为简略模式，简略模式不可交互（图片预览、展开更多 、翻译），默认full                                        | 'full' \| 'brief'             | 否   | 'full'                           |
| previewMode         | 图片预览模式，仅在type=full下必传                                                    | string                        | 否   | 'default'                         |

### 类型定义说明

```typescript
interface IReviewItem {
  review_id: string,
  user_info: IUserInfo,
  review_content: string,
  translate_content: string,
  show_translation?: boolean,
  has_liked?: boolean,
  liked_count?: number,
  product_info?: {
    product_name: string,
    id: string,
    is_booking_now?: boolean,
  },
  review_image_list?: IReviewImage[],
  reply?: {
    reply_from: string,
    reply_content: string
  }
}

interface IUserInfo {
  user_name: string,
  user_avatar: string,
  user_rating: number,
  rating_desc: string,
  rating_time: string,
}

interface IReviewImage {
  id: string
  resize_url: string
  url: string
}

```
## KlkReviewRating

### 示例

:::demo
```html
<div class="demo-vertical-container">
  <klk-form class="demo-vertical-form">
    <klk-form-item label="Size">
      <klk-radio-group v-model="mockRatingDataProps.size">
        <klk-radio group-value="small">small(default)</klk-radio>
        <klk-radio group-value="large">large</klk-radio>
      </klk-radio-group>
    </klk-form-item>
  </klk-form>
  <div class="demo-vertical-component">
    <klk-review-rating
      :rating-info="mockRatingData"
      :size="mockRatingDataProps.size"
    />
  </div>
</div>
```
:::

### 参数说明
| 参数                  | 介绍                        | 类型                           | 必传 | 默认值                               |
|---------------------|---------------------------|------------------------------| ---- |-----------------------------------|
| ratingInfo          | 评分信息                     | IReviewRating                 | 否   | null                              |
| size                | 尺寸大小                     | 'small' \| 'large'            | 是   | 'small'                           |


### 类型定义说明

```typescript
interface IReviewRating extends IRatingInfo {
  sub_rating_list: ISubRating[],
  merchant_info?: {
    merchant_logo: string,
    merchant_name: string
  }
}

interface IRatingInfo {
  rating_info: {
    avg_rating: number,
    max_rating: number,
    rating_desc?: string,
    rating_icon: string,
    review_count_desc: string
    review_count: number
  }
}

interface ISubRating {
  sub_rating_desc: string,
  sub_rating: number
}
```


## 外部依赖
```json
{
  "peerDependencies": {
    "@klook/klook-icons": "^0.18.0",
    "@klook/klook-ui": "^1.38.14",
    "vue": "2.x",
    "vue-property-decorator": "^8.3.0",
    "@klook/site-config": "^1.10.0",
    "@klook/klk-traveller-utils": "^1.8.17",
    "swiper": "^4.0.7",
    "vue-awesome-swiper": "^3.1.3",
    "@klook/galileo-vue": "^1.2.11"
  }
}
```
组件会依赖一些项目内的方法:

| 名称 | 介绍         | 类型                                  | 说明                                           |
|----|------------|-------------------------------------|----------------------------------------------|
| $axios | ajax 请求库   | { $get: Function; $post: Function } | 用于组件内部发送请求获取评论数据                             |
| $t | 多语言        | Function                            | 用于组件内多语言文本转换，支持传递参数                          |
| $alert | 弹窗提示       | Function                            | 用于显示弹窗提示信息                                   |
| $toast | 无干扰的消息提示   | Function                            | 用于显示交互反馈，如点赞成功提示                             |
| $href | 路由跳转方法     | Function                            | 用于页面跳转，如点击产品名称跳转到产品详情页                       |
| $store | 状态管理       | Vue Store                           | 用于获取平台、语言和登录状态等信息                            |
| $inhouse | in-house埋点 | Fuction                             |                                              |
| v-galileo-click-tracker | galileo埋点  | Vue指令| 用于统计用户点击行为，功能实现依赖于 `@klook/galileo-vue` 库的引用注册 |
| v-lazy | 懒加载  | Vue指令| 用于懒加载图片 |

## 依赖文案
组件内使用了多个多语言标识符：

```json
{
  "208981": "Reviews",
  "208984": "Traveler reviews",
  "208985": "See all reviews",
  "208986": "Sort by:",
  "208987": "Filter by:",
  "28270": "Sort",
  "6938": "Service provided by {0}",
  "10063": "No results found. Please clear your filters and try again.",
  "12691": "Loading Failed, please try again later.",
  "reviews.review_for": "Review for:",
  "6307": "See less",
  "see_more": "See more",
  "activity.v2.translate.btn": "Translate review to English",
  "activity.v2.translate.show.original": "Show original (Translated review)",
  "my_reviews.helpful_with_num": "{0} found this helpful",
  "my_reviews.helpful_text": "Helpful",
  "78605": "Couldn't load. Pull up to reload.",
  "car_rental_home_loading": "Loading...",
  "16268": "You've Klooked to the end",
  "ptp_review_klook_reply": "Reply from Klook",
  "close": "Close",
  "global.reload": "Reload",
  "12346": "Loading failed. Please refresh to try again.",
  "activity.v2.mobile.activity.review": "Reviews"
}
```


<script lang="ts">
import {
  KlkMiniReview,
  KlkAIReviewCard,
  KlkReviewRating,
  KlkReviewCard,
  KlkReviewDetail,
  KlkReview } from '@/packages/klk-user-review/src/index.ts';

export default {
  data() {
    return {
      mockMiniRatingInfoProps: {
        platform: 'desktop',
        theme: 'dark',
        direction: 'left',
        showDetail: true,
        clickable: true,
        enableReviewSchema: false
      },
      mockMiniRatingInfo: {
        rating_info: {
          avg_rating: 4.3,
          max_rating: 5,
          rating_desc: 'Great',
          review_count: 246,
          review_count_desc: '246 reviews',
          rating_icon: 'https://res.klook.com/image/upload/v1747637588/UED_new/Platform/platform_AI_review_2409/B.png'
        },
        track_info: {
          extra: {
            aggregate_id: '44017',
            aggregate_key: 'product_id'
          }
        }
      },
      mockOverviewDataProps: {
        platform: 'desktop',
        showDetail: true,
        hideShowMore: false,
        title: 'Overall Reviews',
        loading: false,
        enableReviewSchema: false,
        reviewData: this.mockOverviewData,
        extraData: this.mockExtraData,
      },
      mockDetailDataProps: {
        platform: 'desktop',
        name: 'list',
        renderMode: 'ssr',
        display: 'inline',
        title: 'Detailed Reviews',
        enableReviewSchema: false,
        loading: false,
        extraData: this.mockExtraData,
        reviewData: this.mockOverviewData,
        sourceReviewId: ''
      },
      mockCardDataProps: {
        previewMode: 'modal',
        type: 'full'
      },
      mockCardData: null,
      mockExtraData: {
        aggregate_id: '11',
        template_id: '123'
      },
      mockLogin: {
        onLogin: (next) => {
          // Mock login action 替换成登陆弹窗事件
          Promise.resolve(true).then(() => {
            next()
          })
        }
      },
      mockOverviewData: {
        rating_info: {
          avg_rating: 4.3,
          max_rating: 5,
          rating_desc: 'Great',
          review_count: 246,
          review_count_desc: '246 reviews',
          rating_icon: 'https://res.klook.com/image/upload/v1747637588/UED_new/Platform/platform_AI_review_2409/B.png'
        },
        sub_rating_list: [
          {
            sub_rating_desc: 'Food',
            sub_rating: 4.2
          },
          {
            sub_rating_desc: 'Services offered',
            sub_rating: 4.4
          },
          {
            sub_rating_desc: 'Value',
            sub_rating: 4.6
          },
          {
            sub_rating_desc: 'Atmosphere',
            sub_rating: 4.6
          }
        ],
        merchant_info: {
          merchant_logo: 'https://res.klook.com/image/upload/logo3_rz65yu.png',
          merchant_name: 'TTD merchant'
        },
        ai_summary_info: {
          title: 'Reviews summary',
          icon: 'https://res.klook.com/image/upload/v1726038608/UED_new/Platform/platform_AI_review_2409/icon_ai_review.png',
          mini_info: {
            tips: 'From real reviews, powered by AI',
            pop_info: {
              title: "About Klook's reviews summary",
              content: 'AI reviews are based on real customer reviews and trusted third-party data. To read actual customer reviews, please use the filters below.'
            }
          },
          hide_helpful_action: false,
          summary_list: [
            {
              id: 7997,
              type: 'overall_impression',
              title: 'Overall impression',
              content: 'Spacious and clean rooms with comfortable beds. Convenient access to Disneyland Park via free shuttle. Enjoyable atmosphere with Disney-themed activities and character meet-and-greets. Friendly and helpful staff provide excellent service.',
              helpful_status: 0
            },
            {
              id: 7998,
              type: 'sub_service',
              title: 'Location',
              content: 'Walking distance to Disneyland Park. Free shuttle service to and from the park. Easy access to public transportation.',
              helpful_status: 0
            },
            {
              id: 7999,
              type: 'sub_service',
              title: 'Services offered',
              content: 'Friendly and efficient staff. Multilingual assistance available. Free shuttle service to Disneyland Park and other Disney hotels. Luggage storage available.',
              helpful_status: 0
            },
            {
              id: 8000,
              type: 'sub_service',
              title: 'Cleanliness',
              content: 'Rooms consistently clean. Comfortable beds and modern amenities highlighted. Clean and spacious public areas.',
              helpful_status: 0
            },
            {
              id: 8001,
              type: 'sub_service',
              title: 'Atmosphere & facilities',
              content: 'Immersive Disney decor and ambiance. Spacious rooms with comfortable beds. Delicious dining options, including character breakfasts. Exclusive Disney-themed activities and entertainment.',
              helpful_status: 0
            }
          ]
        },
        first_page_review_limit: 8,
        filter: {
          filter_list: [
            {
              filter_desc: 'All',
              filter_key: 'all',
              filter_value: 'all',
              default_selected: true,
              filter_desc_en: 'All',
              is_only_single_select: true
            },
            {
              filter_desc: 'With photos',
              filter_key: 'only_photo',
              filter_value: 'only_photo',
              default_selected: false,
              filter_desc_en: 'With photos',
              is_only_single_select: false
            },
            {
              filter_desc: 'English only',
              filter_key: 'read_lang',
              filter_value: 'read_lang',
              default_selected: false,
              filter_desc_en: 'English only',
              is_only_single_select: false
            }
          ],
          sort_list: [
            {
              desc: 'Most relevant reviews',
              default_selected: true,
              key: 'sort_most_relevant',
              desc_en: 'most_relevant'
            },
            {
              desc: 'Newest',
              default_selected: false,
              key: 'sort_review_time_desc',
              desc_en: 'review_time_desc'
            },
            {
              desc: 'Rating: highest to lowest',
              default_selected: false,
              key: 'sort_score_high_to_low',
              desc_en: ''
            },
            {
              desc: 'Rating: lowest to highest',
              default_selected: false,
              key: 'sort_score_low_to_high',
              desc_en: 'score_low_to_high'
            }
          ]
        },
        reviews: {
          title: 'Traveler reviews',
          total: 246,
          current_page: 1,
          each_page_count: 10,
          has_next: true,
          review_list: [
            {
              "review_id": *********,
              "user_info": {
                "user_name": "Klook User",
                "user_avatar": "https://cdn.test.klooktest.com/upload/img200X200/dd012b97--e-k.jpg",
                "user_rating": "4.0",
                "rating_desc": "Great",
                "rating_time": "2025-06-03T03:30:44.000Z"
              },
              "review_content": "轻松快捷地入场。只需点击您在klook上收到的链接，出示您收到的二维码即可。您可以进入并探索该地区。乘船需要等待很长时间，我们没能体验，但我们两次体验了寻宝游戏肉桂卷3D，玩得很开心。观看了一场小型戏剧表演，可惜只有日语，所以我们听不懂他们在说什么。我会推荐给那些真正喜欢三丽鸥卡通人物（就像我女儿一样）和可爱东西的人。 也适合拍摄色彩缤纷、可爱的照片。",
              "translate_content": "轻松快捷地入场。只需点击您在klook上收到的链接，出示您收到的二维码即可。您可以进入并探索该地区。乘船需要等待很长时间，我们没能体验，但我们两次体验了寻宝游戏肉桂卷3D，玩得很开心。观看了一场小型戏剧表演，可惜只有日语，所以我们听不懂他们在说什么。我会推荐给那些真正喜欢三丽鸥卡通人物（就像我女儿一样）和可爱东西的人。 也适合拍摄色彩缤纷、可爱的照片。",
              "has_reply": false,
              "show_translation": false,
              "has_liked": false,
              "liked_count": 0,
              "product_info": {
                "id": 93478,
                "product_name": "bytime-test——93478——tag——Variant Setting",
                "deeplink": "",
                "is_booking_now": true
              },
              "review_image_list": [
                {
                  "id": 10888,
                  "resize_url": "https://cdn.klook.com/user_review/andyTest/1946834/d1b88076-58f0-4dec-5892-c397e0c2dc8b.250*0.jpeg",
                  "url": "https://cdn.klook.com/user_review/andyTest/1946834/d1b88076-58f0-4dec-5892-c397e0c2dc8b.1200*0.jpeg"
                },
                {
                  "id": 12325,
                  "resize_url": "https://cdn.test.klooktest.com/user_review/andyTest/1948472/d091a9dd-9475-48ea-6b10-1a13c6b7eca2.0*250.jpeg",
                  "url": "https://cdn.test.klooktest.com/user_review/andyTest/1948472/d091a9dd-9475-48ea-6b10-1a13c6b7eca2.0*1200.jpeg"
                },
              ]
            },
            {
              "review_id": 236776001,
              "user_info": {
                "user_name": "Name ****",
                "user_avatar": "https://cdn.test.klooktest.com/upload/img200X200/eedd97aa--e-n.jpg",
                "user_rating": "4.0",
                "rating_desc": "Great",
                "rating_time": "2023-09-21T06:54:23.000Z"
              },
              "review_content": "就是看到积分开锁的圣诞节发开锁交电费卡时间的开发建设的咖啡机啊收快递放假啊开锁的积分卡三等奖时代峰峻可看房就撒开的房间空间看教练看到积分卡",
              "translate_content": "I just saw the points unlocked during Christmas, the development and construction of the coffee machine, the time to pay the electricity bill card, the holiday to collect the express delivery, the unlocked points card, the third prize, Feng Jun can show the room, open the room space, watch the coach, see the point card",
              "has_reply": false,
              "show_translation": true,
              "has_liked": false,
              "liked_count": 0,
              "product_info": {
                "id": 91691,
                "product_name": "jo——group——91691-combo2.1-1-timeslot nn",
                "deeplink": "",
                "is_booking_now": true
              },
              "review_image_list": [
                {
                  "id": 10831,
                  "resize_url": "https://cdn.klook.com/user_review/andyTest/1946834/d1b88076-58f0-4dec-5892-c397e0c2dc8b.250*0.jpeg",
                  "url": "https://cdn.klook.com/user_review/andyTest/1946834/d1b88076-58f0-4dec-5892-c397e0c2dc8b.1200*0.jpeg"
                }
              ]
            },
            {
              "review_id": 250014835,
              "user_info": {
                "user_name": "Klook User",
                "user_avatar": "https://cdn.test.klooktest.com/upload/img200X200/dd012b97--e-k.jpg",
                "user_rating": "4.0",
                "rating_desc": "Great",
                "rating_time": "2025-06-03T03:31:08.000Z"
              },
              "review_content": "通过 klook 获取门票并进入公园非常容易！里面太可爱了！！！我们从上午 11 点到下午 6 点一直玩到最后，但还是没能玩到/看到所有的东西！！表演很精彩，特色食物很可爱，商店你一定要克制自己哈哈。如果你喜欢三丽鸥，你一定会喜欢这个地方！",
              "translate_content": "通过 klook 获取门票并进入公园非常容易！里面太可爱了！！！我们从上午 11 点到下午 6 点一直玩到最后，但还是没能玩到/看到所有的东西！！表演很精彩，特色食物很可爱，商店你一定要克制自己哈哈。如果你喜欢三丽鸥，你一定会喜欢这个地方！",
              "has_reply": false,
              "show_translation": false,
              "has_liked": false,
              "liked_count": 0,
              "product_info": {
                "id": 93478,
                "product_name": "bytime-test——93478——tag——Variant Setting",
                "deeplink": "",
                "is_booking_now": true
              },
              "review_image_list": null
            },
            {
              "review_id": 250014766,
              "user_info": {
                "user_name": "test_first_name **************",
                "user_avatar": "https://cdn.test.klooktest.com/upload/img200X200/01cfc55cf8e142965241051b0676d363.jpg",
                "user_rating": "2.0",
                "rating_desc": "Fair",
                "rating_time": "2025-05-26T08:36:46.000Z"
              },
              "review_content": "Klook 没有问题。对 Klook 印象非常好。三丽鸥则完全是另一回事。过度拥挤，管制过度。三丽鸥彩虹乐园就像是政府经营的迪士尼。一点也不好玩。不能玩任何游乐设施，而且因为我们没有支付额外费用而被阻挡看表演。整个地方拥挤而狭小，人们在演出开始前一个多小时就开始排队，区域被封锁。虽然有通道，但如果使用它们，你必须一直移动，而且如果你看表演，实际上会被骂，因为你不被允许从“特权”座位观看。我们付了 13,000 日元，却没能看到一场演出，也没坐过一个游乐设施。哦是的，甚至在餐厅吃饭也需要等待。停车费 1.5 小时也要 2000 日元。非常贵，非常失望。",
              "translate_content": "Very average very average very average very average very average very average very average very average very average very average very average very average very average very average very average very average very average very average very average very average very average very average very average very average very average very average very average very average very average very average very average very average very average",
              "has_reply": true,
              "reply": {
                "reply_from": "Reply from Klook",
                "id": 566,
                "content": "你好，很抱歉没有让你感到满意，你好，很抱歉没有让你感到满意，你好，很抱歉没有让你感到满意，你好，很抱歉没有让你感到满意，你好，很抱歉没有让你感到满意，你好，很抱歉没有让你感到满意，你好，很抱歉没有让你感到满意，你好，很抱歉没有让你感到满意，你好，很抱歉没有让你感到满意，你好，很抱歉没有让你感到满意，你好，很抱歉没有让你感到满意，你好，很抱歉没有让你感到满意，你好，很抱歉没有让你感到满意，你好，很抱歉没有让你感到满意，你好，很抱歉没有让你感到满意，你好，很抱歉没有让你感到满意，你好，很抱歉没有让你感到满意，你好，很抱歉没有让你感到满意，你好，很抱歉没有让你感到满意，你好，很抱歉没有让你感到满意，你好，很抱歉没有让你感到满意，你好，很抱歉没有让你感到满意，你好，很抱歉没有让你感到满意，你好，很抱歉没有让你感到满意，你好，很抱歉没有让你感到满意，你好，很抱歉没有让你感到满意，你好，很抱歉没有让你感到满意，你好，很抱歉没有让你感到满意，你好，很抱歉没有让你感到满意，你好，很抱歉没有让你感到满意，你好，很抱歉没有让你感到满意，你好，很抱歉没有让你感到满意，你好，很抱歉没有让你感到满意，你好，很抱歉没有让你感到满意，你好，很抱歉没有让你感到满意，你好，很抱歉没有让你感到满意，你好，很抱歉没有让你感到满意，你好，很抱歉没有让你感到满意，你好，很抱歉没有让你感到满意，你好，很抱歉没有让你感到满意，你好，很抱歉没有让你感到满意，你好，很抱歉没有让你感到满意，你好，很抱歉没有让你感到满意，你好，很抱歉没有让你感到满意，你好，很抱歉没有让你感到满意，你好，很抱歉没有让你感到满意，你好，很抱歉没有让你感到满意，你好，很抱歉没有让你感到满意，sorry",
                "translate_content": "Hello, I&#39;m sorry that I didn&#39;t satisfy you, ... I&#39;m sorry that I didn&#39;t satisfy you, hello, ...",
                "review_time": "2025-05-26 08:36:46",
                "language": "zh_CN",
                "translate_language": "en_US"
              },
              "show_translation": true,
              "has_liked": true,
              "liked_count": 2,
              "product_info": {
                "id": 93478,
                "product_name": "bytime-test——93478——tag——Variant Setting",
                "deeplink": "",
                "is_booking_now": true
              },
              "review_image_list": [
                {
                  "id": 14324,
                  "resize_url": "https://cdn.test.klooktest.com/user_review/andyTest/1948472/23edb37e-f788-49e0-554c-8bce756509a0.250*0.jpeg",
                  "url": "https://cdn.test.klooktest.com/user_review/andyTest/1948472/23edb37e-f788-49e0-554c-8bce756509a0.1200*0.jpeg"
                },
                {
                  "id": 14325,
                  "resize_url": "https://cdn.test.klooktest.com/user_review/andyTest/1948472/d091a9dd-9475-48ea-6b10-1a13c6b7eca2.0*250.jpeg",
                  "url": "https://cdn.test.klooktest.com/user_review/andyTest/1948472/d091a9dd-9475-48ea-6b10-1a13c6b7eca2.0*1200.jpeg"
                },
                {
                  "id": 14326,
                  "resize_url": "https://cdn.test.klooktest.com/user_review/andyTest/1948472/1e755dec-c0da-4a4f-69d7-93e691929edb.250*0.jpeg",
                  "url": "https://cdn.test.klooktest.com/user_review/andyTest/1948472/1e755dec-c0da-4a4f-69d7-93e691929edb.1200*0.jpeg"
                }
              ]
            },
            {
              "review_id": 236776140,
              "user_info": {
                "user_name": "j *",
                "user_avatar": "https://cdn.test.klooktest.com/upload/img200X200/3f0de454--e-j.jpg",
                "user_rating": "3.0",
                "rating_desc": "Average",
                "rating_time": "2023-11-15T14:10:54.000Z"
              },
              "review_content": "dddsdfsdfdsfsdfdsfdsfdsfdsfdsfdsfsdfsdfdsdddsdfsdfdsfsdfdsfdsfdsfdsfdsfdsfsdfsdfdsdddsdfsdfdsfsdfdsfdsfdsfdsfdsfdsfsdfsdfdsdddsdfsdfdsfsdfdsfdsfdsfdsfdsfdsfsdfsdfdsdddsdfsdfdsfsdfdsfdsfdsfdsfdsfdsfsdfsdfds",
              "translate_content": "dddsdfsdfdsfsdfdsfdsfdsfdsfdsfdsfsdfsdfdsdddsdfsdfdsfsdfdsfdsfdsfdsfdsfdsfsdfsdfdsdddsdfsdfdsfsdfdsfdsfdsfdsfdsfdsfsdfsdfdsdddsdfsdfdsfsdfdsfdsfdsfdsfdsfdsfsdfsdfdsdddsdfsdfdsfsdfdsfdsfdsfdsfdsfdsfsdfsdfds",
              "has_reply": false,
              "show_translation": false,
              "has_liked": false,
              "liked_count": 1,
              "product_info": {
                "id": 91691,
                "product_name": "jo——group——91691-combo2.1-1-timeslot nn",
                "deeplink": "",
                "is_booking_now": true
              },
              "review_image_list": null
            },
            {
              "review_id": 236776141,
              "user_info": {
                "user_name": "Siti *************",
                "user_avatar": "https://cdn.test.klooktest.com/upload/img200X200/4765f71c--e-s.jpg",
                "user_rating": "2.0",
                "rating_desc": "Fair",
                "rating_time": "2023-11-15T14:17:31.000Z"
              },
              "review_content": "香港货真价实就是男神饿好多好多绝对继续难道不是绝对绝对绝对今生今世较低好想继续就是今生今世不断不断亟待解决大家都开心开心继续开继续继续",
              "translate_content": "Hong Kong is the real thing. The male god is hungry. There are so many. Absolutely continue. Isn’t it absolutely, absolutely, absolutely? This life is lower. I want to continue. It is this life. It needs to be solved. Everyone is happy. Keep going. Keep going.",
              "has_reply": false,
              "show_translation": true,
              "has_liked": false,
              "liked_count": 1,
              "product_info": {
                "id": 91691,
                "product_name": "jo——group——91691-combo2.1-1-timeslot nn",
                "deeplink": "",
                "is_booking_now": true
              },
              "review_image_list": null
            },
            {
              "review_id": 250014694,
              "user_info": {
                "user_name": "YITONG *****",
                "user_avatar": "https://cdn.test.klooktest.com/upload/img200X200/08867fe9--e-y.jpg",
                "user_rating": "3.0",
                "rating_desc": "Average",
                "rating_time": "2024-12-14T05:44:05.000Z"
              },
              "review_content": "除非你是三丽鸥人物的超级粉丝，否则不值得一游。这里离东京太远了，除非你住在新宿区，否则坐火车要 40 分钟。你必须再次付费才能乘坐火车和快速到达。这里没什么有趣的东西",
              "translate_content": "Machine audit failed the test",
              "has_reply": false,
              "show_translation": true,
              "has_liked": false,
              "liked_count": 0,
              "product_info": {
                "id": 0,
                "product_name": "",
                "deeplink": "",
                "is_booking_now": false
              },
              "review_image_list": null
            }
          ]
        },
        track_info: {
          extra: {
            aggregate_id: '44017',
            aggregate_key: 'product_id'
          }
        }
      },
      reviewSchemaJson: '',
      mockRatingDataProps: {
        size: 'small'
      },
      mockRatingData: {
        rating_info: {
          avg_rating: 4.7,
          max_rating: 5,
          rating_desc: 'fantastic',
          review_count: 5000,
          review_count_desc: 'Base on 5K reviews',
          rating_icon: 'https://res.klook.com/image/upload/v1747637588/UED_new/Platform/platform_AI_review_2409/B.png'
        },
        sub_rating_list: [
          {
            sub_rating_desc: 'Service',
            sub_rating: 4
          },
          {
            sub_rating_desc: 'Experience',
            sub_rating: 5
          },
          {
            sub_rating_desc: 'Guide',
            sub_rating: 4.8
          },
          {
            sub_rating_desc: 'Qualilty of transfer',
            sub_rating: 4.8
          }
        ],
        merchant_info: {
          merchant_name: 'Klook',
          merchant_logo: 'https://res.klook.com/image/upload/logo3_rz65yu.png'
        }
      }
    }
  },
  mounted() {
    if (this.mockOverviewData && this.mockOverviewData.reviews &&
        this.mockOverviewData.reviews.review_list &&
        this.mockOverviewData.reviews.review_list[0]) {
      this.mockCardData = this.mockOverviewData.reviews.review_list[0]
    }
  },
  methods: {
    showMore() {
      console.log('showMore clicked')
    },
    goBooking() {
      console.log('goBooking clicked')
    },
    updateReviewSchemaJson(reviewData) {
      this.reviewSchemaJson = this.generateSchemaData(reviewData).script[0].json
    },
    generateSchemaData(reviewData) {
      const schema = {
        '@context': 'https://schema.org',
        aggregateRating: {
          '@type': 'AggregateRating',
          ratingValue: String(reviewData?.rating_info.avg_rating || ''),
          bestRating: String(reviewData?.rating_info.max_rating || ''),
          worstRating: '1',
          reviewCount: String(reviewData?.rating_info.review_count || '')
        }
      };

      return {
        script: [{
          type: 'application/ld+json',
          json: JSON.stringify(schema)
        }]
      };
    }
  },
  components: {
    KlkMiniReview,
    KlkReviewRating,
    KlkReview,
    KlkReviewCard,
    KlkReviewDetail
  }
}
</script>
<style lang="scss" >
.demo-container {
  display: flex;
  justify-content: space-between;
}
.demo-vertical-container {
  display: flex;
  justify-content: space-between;
  flex-direction: column;

}
.demo-horizontal-form {
  width: 40%;
  flex-shrink: 0;
}
.demo-vertical-form {

}

.demo-horizontal-component {
  width: 60%;
  flex-shrink: 0;
  background: #fff;
  padding: 15px;
  border-radius: 8px;
  border: 1px dashed #E5E5E5;
  margin-left: 20px;
}
.demo-vertical-component {
  padding: 15px;
  border-radius: 8px;
  border: 1px dashed #E5E5E5;
}
.demo-vertical-block {
  margin-bottom: 10px;
}
.demo-component {
  width: 100%;
}
.klk-review--mobile {
  max-width: 500px;
}
</style>
