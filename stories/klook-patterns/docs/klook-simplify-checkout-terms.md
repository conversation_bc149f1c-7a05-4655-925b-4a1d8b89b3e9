# simplify checkout条款组件
> 版本请查看[knpm](https://knpm.klook.io/-/web/detail/@klook/klook-simplify-checkout-terms)

```
import SimplifyCheckoutTerms from '@klook/klook-simplify-checkout-terms'
import '@klook/klook-simplify-checkout-terms/dist/esm/index.css'
import '@klook/klook-special-terms/dist/esm/index.css'

<SimplifyCheckoutTerms 
  ref="simplifyCheckoutTerms1" 
  scene="simplify_checkout" 
  mobile="852-111111"
  email="<EMAIL>"
  platform="mobile" 
  language="en-US">
</SimplifyCheckoutTerms>
```

## 参数 Props

| 参数 | 介绍 | 类型 | 必传 | 默认值 |
|------|------|------|------|------|
|scene|场景|string|'simplify_checkout' 'simplify_checkout_T&C', 获取条款数据的参数|-|
|mobile|手机号|string|false|-|
|email|邮箱|string|false|-|
|platform|平台|string|false|-|
|language|语言|Data.language|false|-|

## Events

| 名称 | 介绍 | 参数 |
|-----|------|-----|
| validator | 校验 | () => string |


<script lang="ts">
import KlkSimplifyCheckoutTerms from '@/packages/klook-simplify-checkout-terms/src'
// import '@klook/klook-simplify-checkout-terms/dist/esm/index.css'
// import '@klook/klook-special-terms/dist/esm/index.css'

export default {
  data() {
    return {
    }
  },
  components:{
    KlkSimplifyCheckoutTerms,
  },
  mounted() {
    // console.log('SimplifyCheckoutTerms', SimplifyCheckoutTerms)
  }
}
</script>
