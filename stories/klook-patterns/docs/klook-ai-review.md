# AIReview 组件

> Package: @klook/ai-review
> Version: 0.0.1

## 概述
AI评论总结组件，用于显示 AI 生成的评论摘要。

```
import KlkAiReview from '@klook/ai-review'
import '@klook/ai-review/dist/esm/index.css'
```
## 示例

:::demo

```html
<div style="width: 100%; padding: 20px; background: #f5f5f5">
  <div>
    <klk-button @click="visible = true">Show</klk-button>
    <klk-drawer :visible.sync="visible" direction="right">
      <klk-ai-review
        style="width: 100%; padding: 60px; "
        :title="title"
        :icon="icon"
        :mini-info="miniInfo"
        :summary-lists="summaryLists"
        platform="desktop"
        :hide-helpful-action="hideHelpfulAction"
      />

    </klk-drawer>
  </div>

</div>
```
:::


## 参数 Props
| 参数                  | 介绍                        | 类型                           | 必传 | 默认值                               |
|---------------------|---------------------------|------------------------------| ---- |-----------------------------------|
| icon                | 图标链接                      | `string`                     | 是   | -                                 |
| title               | 模块标题                      | `string`                     | 是   | -                                 |
| miniInfo            | 简要提示：包括提示文本和弹窗信息          | `MiniInfo`                   | 是   | -                                 |
| hide_helpful_action | 控制显示点赞点踩按钮的配置，默认显示        | `Boolean`                    | 否   | `false`                           |
| summary_list        | AI Review Summary 的具体维度信息 | `Array<AiReviewSummaryItem>` | 是   | -                                 |
| platform            | 平台信息                      | `string`                     | 否   | -                                 |
| helpfulApi          | 接口地址 | `string`                     | 否   |  `/v1/tocintersrv/review/summary/helpful` | -                                 |
### 类型定义说明：

```typescript
interface MiniInfo {
  tips: string;
  pop_info: {
    title: string;
    content: string;
  }
}

interface AiReviewSummaryItem {
  id: number;
  type: string;
  title: string;
  content: string;
  helpful_status: number; // 0: 未反馈, 1: 点赞, 2: 点踩
}
```
### 子组件 AIReviewCard

#### 参数 Props
用于显示单个评论摘要。

| 参数            | 介绍                               | 类型                    | 必传 | 默认值   |
| ----------------- | ------------------------------------ |-----------------------| ---- |-------|
| summary           | AI Review Summary 的某一维度信息 | `AiReviewSummaryItem` | 是  | -     |
| hideHelpfulAction | 控制显示点赞点踩按钮的配置，默认显示 | `Boolean`             | 否  | `false` |
| helpfulApi          | 接口地址 | `string`                     | 否   |  `/v1/tocintersrv/review/summary/helpful` | -                                 |

#### 事件 Events

| 事件名称         | 描述                               | 参数              |
| -------------------- | ------------------------------------ | ------------------- |
| hideHelpfulAction    | 控制显示点赞点踩按钮的配置，默认显示 | Boolean             |


## 外部依赖
组件会依赖一些项目内的方法，需要通过 window 传递:

| 名称 | 介绍        | 类型                                |
|----| ----------- | ----------------------------------- |
| $axios | ajax 请求库 | { $get: Function; $post: Function } |

同时请保证项目中有this.$toast方法:

| 名称     | 介绍       | 类型                                |
|--------|----------| ----------------------------------- |
| $toast | 无干扰的消息提示 |  |


<script lang="ts">
import KlkAiReview from '@/packages/klk-ai-review/src';
import KlkButton from '@/packages/klook-ui/src/components/button'

export default {
  data() {
    return {
      visible: false,
      icon: 'https://res.klook.com/image/upload/v1726038608/UED_new/Platform/platform_AI_review_2409/icon_ai_review.png',
      title: 'Reviews Summary',
      miniInfo: {
        "tips": 'From real reviews, powered by AI',
        "pop_info": {
          "title": 'Hi, I am klook AI review assistant',
          "content": 'AI reviews are based on real customer reviews and trusted third-party data.\nFor the check-in experience, you can click on the review tags under "All" and "With Pictures and Videos".\nSign and filter to view. The review content is based on personal experience and may have deviations.'
        }
      },
      hideHelpfulAction: false,
      summaryLists: [
        {
          id: 123,
          type: 'overall',
          title: 'this is a long long long long long long long long long long long long overall 1',
          content: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco  ullamco…',
          helpful_status: 1
        },
        {
          id: 124,
          type: 'overall',
          title: 'overall 2',
          content: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco  ullamco…',
          helpful_status: 0
        },
        {
          id: 125,
          type: 'overall',
          title: 'overall 3',
          content: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. ',
          helpful_status: 2
        }
      ]
    }
  },
  components: {
    KlkAiReview
  }
}
</script>
