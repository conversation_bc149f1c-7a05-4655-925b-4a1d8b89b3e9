<template>
  <div
    :class="badgeClass"
    @click="handleClick"
    :style="{
      width: width,
      backgroundColor: backgroundColor,
    }"
  >
    <div class="klk-eco-badge-content">
      <img
        v-if="imageUrl"
        class="klk-eco-badge-icon"
        :src="imageUrl"
        alt="Eco icon"
      />
      <span class="klk-eco-badge-text" :style="{ fontWeight: fontWeight }">{{
        text
      }}</span>
    </div>
    <div class="klk-eco-badge-arrow" v-if="showArrow">
      <IconNext theme="outline" size="16" :fill="[$colorTextPrimary]" />
    </div>
  </div>
</template>

<script>
import { IconNext } from "@klook/klook-icons";
export default {
  name: "klookEcoBadge",
  components: {
    IconNext,
  },
  props: {
    type: {
      type: String,
      default: "desktop", // 支持 'desktop' 或 'mobile'
      validator: (value) => ["desktop", "mobile"].includes(value),
    },
    imageUrl: {
      type: String,
      required: true,
      default: "https://res.klook.com/image/upload/eco-badge_zmu34u.png", // 徽章图标的URL
    },
    text: {
      type: String,
      required: true,
      default: "Certified sustainable partner", // 徽章的文本
    },
    link: {
      type: String,
      required: true,
      default: "#", // 点击后跳转的链接
    },
    width: {
      type: String,
      default: "100%", // 徽章的宽度，默认为100%
    },
    backgroundColor: {
      type: String,
      default: "#E3F9FA", // 背景颜色，默认为浅绿色
    },
    showArrow: {
      type: Boolean,
      default: true, // 是否显示箭头图标，默认为true
    },
    fontWeight: {
      type: [String, Number],
      default: 500, // 文本的字体粗细，默认为500
    },
  },
  computed: {
    badgeClass() {
      return `klk-eco-badge klk-eco-badge-${this.type}`;
    },
  },
  methods: {
    handleClick() {
      if (this.link) {
        window.location.href = this.link;
      }
    },
  },
};
</script>

<style lang="scss">
@import '~@klook/klook-ui/dist/klook-ui.css';
.klk-eco-badge {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  border-radius: 8px;
  color: #333;
  cursor: pointer;
  justify-content: space-between;
  gap: 8px;

  .klk-eco-badge-content {
    display: flex;
    align-items: center;
    flex: 1;
    overflow: hidden;
  }

  .klk-eco-badge-icon {
    width: 30px;
    height: 30px;
    margin-right: 8px;
    background-color: inherit;
    flex-shrink: 0; /* 确保图标不会被压缩 */
  }

  .klk-eco-badge-text {
    font-size: 14px;
    font-weight: 600;
    color: #212121;
  }

  .klk-eco-badge-arrow {
    flex-shrink: 0; /* 确保箭头不会被压缩 */
  }

  &-desktop {
    .klk-eco-badge-text {
      display: -webkit-box;
      overflow: hidden; /* 确保文本超出部分被隐藏 */
      text-overflow: ellipsis;
      white-space: nowrap;
      width: 100%;
      display: block;
    }
  }

  &-mobile {
    .klk-eco-badge-text {
      display: -webkit-box;
      white-space: pre-wrap;
      word-wrap: break-word;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
}
</style>
