const cloudinary = require('cloudinary').v2
const version = require('./package.json').version
const FILE_PATHS = ['./dist/umd/index.js', './dist/umd/index.css']
const folder = `/login_sdk/${version}`;

cloudinary.config({
  cloud_name: 'klook',
  api_key: '334392432387773',
  api_secret: 'NJXxV8fuO8wFg2SYnAsuP35XbGQ',
})

console.log('sdk version', version)

for (const file of FILE_PATHS) {
  cloudinary.uploader.upload(file, {
    folder,
    resource_type: 'raw',
    overwrite: true,
    use_filename: true,
    unique_filename: false,
    invalidate: true
  }, (error, result) => {
    if (error) {
      console.error(`Upload failed: ${JSON.stringify(error)}`);
    } else {
      console.log('-------------------------------------------------------------------------------')
      console.log(`[${file}] Cloudinary path: https://res.klook.com/raw/upload/${result.public_id}`)
      console.log('-------------------------------------------------------------------------------')
      console.log(`Upload successful: ${JSON.stringify(result, null, 2)}`);
    }
  })
}
