//@ts-nocheck
import { showCnInquire, showCnInquireV2, showGlobalInquire } from "@/components/cn-split";
import baseMixin from "../mixin/base";
import { EVENTS } from "@/common/const-data";

export default {
  mixins: [baseMixin],
  methods:{
    async handleMigrationUserFlow(resp: any,needSignup = true ){
      if(resp.error.code === "0011") {
        const { migration_no,auth_token } = resp.error.data
        window.OAuthEvents.$emit(EVENTS.SIMPLIFY_CHECKOUT_SHOW_SDK)
        this.history.push("/migrationWait", {
          migration_no,
          auth_token
        })
        return true
      }

      if(resp.error.code === "0012") {
        const { auth_token, display_style } = resp.error.data
        // 后端控制是否展示迁移选项
        if (display_style === 'back_original') {
          return await showCnInquireV2();
        }

        const value = await showCnInquire()

        if(value === 0) {
          this.ajaxPostJSON('/v1/userapisrv/public/login/migration/migrate', {
            auth_token
          }).then((res)=>{
            if(res.success && res.result){
              const { migration_no,auth_token } = res.result
              window.OAuthEvents.$emit(EVENTS.SIMPLIFY_CHECKOUT_SHOW_SDK)
              this.history.push("/migrationWait", {
                migration_no,
                auth_token,
              })
            } else {
              if (res.error.message) {
                this?.$toast(res.error.message)
              }
            }
          })
        }

        if(value === 1){
          // 当前展示语言
          window.location.href = "https://www.klook.com/en-US/"
        }

        if(value === 2){
          const resp = await this.ajaxPostJSON('/v1/userapisrv/public/login/migration/register', { auth_token })
          if(resp.success){
            window.OAuthEvents.$emit(
              "success",{
                action: resp.result.type,
                userInfo: resp.result
              })
          } else {
            if (resp.error.message) {
              this?.$toast(resp.error.message)
            }
          }
        }
        return true
      }

      if(resp.error.code === "0013") {
        const { auth_token } = resp.error.data
        const result = await showGlobalInquire(needSignup)
        if(result){
          const resp = await this.ajaxPostJSON('/v1/userapisrv/public/login/migration/register', { auth_token })
          if(resp.success){
            window.OAuthEvents.$emit(
              "success",{
                action: resp.result.type,
                userInfo: resp.result
              })
          } else {
            if (resp.error.message) {
              this?.$toast(resp.error.message)
            }
          }
        }else {
          window.location.href = 'https://www.klook.cn/zh-CN/'
          window.OAuthEvents.$emit("cancel", { path: this.path });
        }
        return true
      }

      return false
    }
  }
}
