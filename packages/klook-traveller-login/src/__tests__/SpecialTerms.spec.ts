import { mount, createLocalVue } from '@vue/test-utils';
import SpecialTerms from '../components/special-terms.vue';

describe('SpecialTerms Component', () => {
  let wrapper: any;
  let localVue;

  beforeEach(() => {
    localVue = createLocalVue();
    wrapper = mount(SpecialTerms, {
      localVue,
      provide: {
        bizName: 'test',
        purpose: 'test',
        platform: 'desktop'
      }
    });

    wrapper.vm.$refs = {
      tncConfirm: {},
      crmConfirm: {}
    }
  });

  it('should render the component', () => {
    expect(wrapper.exists()).toBe(true);
  });

  it('should have the correct initial data', () => {
    expect(wrapper.vm.termList).toEqual([]);
    expect(wrapper.vm.checkList).toEqual([]);
    expect(wrapper.vm.displayTerms).toEqual([]);
    expect(wrapper.vm.checkAll).toEqual(false);
    expect(wrapper.vm.checkRequired).toEqual(false);
    expect(wrapper.vm.isWarning).toEqual(false);
    expect(wrapper.vm.showConsentDialog).toEqual(false);
    expect(wrapper.vm.showMustDialog).toEqual(false);
    expect(wrapper.vm.preConsent).toEqual(null);
    expect(wrapper.vm.preConsentHint).toEqual('');
  });

  it('should have the correct initial data', () => {
    expect(wrapper.vm.termList).toEqual([]);
    expect(wrapper.vm.checkList).toEqual([]);
    expect(wrapper.vm.displayTerms).toEqual([]);
    expect(wrapper.vm.checkAll).toBe(false);
    expect(wrapper.vm.checkRequired).toBe(false);
    expect(wrapper.vm.isWarning).toBe(false);
    expect(wrapper.vm.showConsentDialog).toBe(false);
    expect(wrapper.vm.showMustDialog).toBe(false);
    expect(wrapper.vm.preConsent).toBe(null);
    expect(wrapper.vm.preConsentHint).toBe('');
    expect(wrapper.vm.callback).toEqual(expect.any(Function));
  });

  it('should handle initCheckList method', () => {
    const terms = [
      { term_id: 1, default_check_status: true },
      { term_id: 2, default_check_status: false },
    ];

    wrapper.vm.initCheckList(terms);

    expect(wrapper.vm.termList).toEqual([
      { term_id: 1, default_check_status: true, isChecked: true },
      { term_id: 2, default_check_status: false, isChecked: false },
    ]);
    expect(wrapper.vm.displayTerms).toEqual([]);
    expect(wrapper.vm.checkList).toEqual([1]);
  });

  it('should handle validator method', async () => {
    wrapper.vm.termList = [
      { term_id: 1, required: true, isChecked: false },
    ];

    await wrapper.vm.validator(() => {});

    expect(wrapper.vm.showMustDialog).toBe(true);
  });

  it('should handle checkAllFn method', () => {
    wrapper.vm.checkAllFn(true);

    expect(wrapper.vm.checkAll).toBe(true);
    expect(wrapper.vm.checkList).toEqual([]);
  });

  it('should handle changeBox method', () => {
    wrapper.vm.termList = [
      { term_id: 1, isChecked: true },
    ];

    wrapper.vm.changeBox(true);

    expect(wrapper.vm.checkAll).toBe(true);
  });

  it('should handle requiredNotCheck method', () => {
    wrapper.vm.termList = [
      { term_id: 1, isChecked: false },
    ];

    expect(wrapper.vm.requiredNotCheck(1)).toBe(true);
  });

  it('should handle batchCheck method', () => {
    wrapper.vm.termList = [
      { term_id: 1, isChecked: false },
      { term_id: 2, isChecked: true },
    ];

    wrapper.vm.batchCheck(true);

    expect(wrapper.vm.termList[0].isChecked).toBe(true);
    expect(wrapper.vm.termList[1].isChecked).toBe(true);
    expect(wrapper.vm.checkList).toEqual([1, 2]);
  });

  it('should handle setCheckList method', () => {
    wrapper.vm.termList = [
      { term_id: 1, isChecked: false },
      { term_id: 2, isChecked: true },
    ];

    wrapper.vm.setCheckList();

    expect(wrapper.vm.checkList).toEqual([2]);
  });

  it('should handle getCheckedTermIds method', () => {
    wrapper.vm.termList = [
      { term_id: 1, isChecked: false },
      { term_id: 2, isChecked: true },
      { term_id: 3, isChecked: true },
    ];
    wrapper.vm.displayTerms = [
      { term_id: 4, isChecked: true },
    ];

    wrapper.vm.changeBox()

    expect(wrapper.vm.getCheckedTermIds()).toEqual('2,3,4');
  });

  it('should handle handlePreConsent method', () => {
    const terms = [
      {
        term_id: 1,
        pre_consent_display_term_ids: [2, 3],
        pre_consent_hint: 'Pre-consent hint',
      },
      { term_id: 2 },
      { term_id: 3 },
    ];

    wrapper.vm.handlePreConsent(terms);

    expect(wrapper.vm.preConsent).toEqual({
      termId: 1,
      preConsentDisplayTermIds: [2, 3],
    });
    expect(wrapper.vm.preConsentHint).toEqual('Pre-consent hint');
  });

  it('should handle onCancel method', () => {
    wrapper.vm.preConsent = { termId: 1 };
    wrapper.vm.termList = [
      { term_id: 1, isChecked: true },
    ];

    wrapper.vm.onCancel();

    expect(wrapper.vm.termList[0].isChecked).toBe(false);
    expect(wrapper.vm.showConsentDialog).toBe(false);
  });

  it('should handle onConfirm method', () => {
    wrapper.vm.preConsent = { termId: 1, preConsentDisplayTermIds: [2, 3] };
    wrapper.vm.termList = [
      { term_id: 1, isChecked: false },
      { term_id: 2, isChecked: false },
      { term_id: 3, isChecked: false },
    ];

    wrapper.vm.onConfirm();

    expect(wrapper.vm.termList[1].isChecked).toBe(true);
    expect(wrapper.vm.termList[2].isChecked).toBe(true);
    expect(wrapper.vm.showConsentDialog).toBe(false);
  });
});
