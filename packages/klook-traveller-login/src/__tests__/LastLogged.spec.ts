import { shallowMount } from '@vue/test-utils';
import LastLogged from '../components/last-logged/index.vue';

describe('LastLogged Component', () => {
  let wrapper: any;

  beforeEach(() => {
    wrapper = shallowMount(LastLogged, {
      provide: {
        bizName: 'test',
        purpose: 'test',
      },
      propsData: {
        lastLoginInfo: {
          login_method: 'google',
          login_id_mask: '<EMAIL>',
        },
      },
    });
  });

  afterEach(() => {
    wrapper.destroy();
  });

  test('should render the component correctly', () => {
    expect(wrapper.find('.klk-login-last-logged').exists()).toBe(true);
    expect(wrapper.find('.klk-login-last-logged-info').exists()).toBe(true);
    expect(wrapper.find('.klk-login-last-logged-name').text()).toEqual('<EMAIL>');
  });
});
