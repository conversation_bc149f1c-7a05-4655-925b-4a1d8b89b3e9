import { OAuthData, OAuth } from '../index';
import { IOptions } from '../../types/index';
import EventBus from '../common/event-bus';
import { Toast } from '@klook/klook-ui';

declare var global: { [key: string]: any };

const t = function(key: string) { return key };

describe('OAuthData function', () => {
  let cons: OAuthData
  beforeEach(() => {
    const options: IOptions = {
      currency: 'USD',
      bizName: 'test',
      purpose: 'test',
      market: 'global',
      subProcess: {},
      language: '',
      platform: 'desktop'
    };

    cons = new OAuthData(options);
  })
  test('should set default values for platform and language', () => {
    expect(cons.platform).toEqual('desktop');
    expect(cons.language).toEqual('en');
  });

  test('should set given values for currency and bizName', () => {
    const options: IOptions = {
      currency: 'EUR',
      bizName: 'myBiz',
      purpose: 'test',
      market: 'global',
      subProcess: {},
      language: '',
      platform: 'desktop'
    };

    const cons = new OAuthData(options);

    expect(cons.currency).toEqual('EUR');
    expect(cons.bizName).toEqual('myBiz');
  });

  test('should set eventBus on window if not already present', () => {
    Object.defineProperty(global, 'window', {
      value: {},
      writable: true
    });

    const options: IOptions = {
      currency: 'USD',
      bizName: 'test',
      purpose: 'test',
      market: 'global',
      subProcess: {},
      language: '',
      platform: 'desktop'
    };

    const cons = new OAuthData(options);

    expect(window).toHaveProperty('OAuthEvents', EventBus);
  });

  test('should call setOAuthState method', () => {
    const options: IOptions = {
      currency: 'USD',
      bizName: 'test',
      purpose: 'test',
      market: 'global',
      subProcess: {},
      language: '',
      platform: 'desktop'
    };

    jest.spyOn(OAuthData.prototype, 'setOAuthState');
    const cons = new OAuthData(options);

    expect(cons.setOAuthState).toHaveBeenCalledTimes(1);
  });
});
