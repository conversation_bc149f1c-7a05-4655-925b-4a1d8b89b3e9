import { shallowMount } from '@vue/test-utils';
import EdmPop from '../components/edm-pop/edm-pop.vue';

describe('EdmPop Component', () => {
  let wrapper: any;

  beforeEach(() => {
    wrapper = shallowMount(EdmPop, {
      propsData: {
        bizName: 'testBizName',
        purpose: 'testPurpose',
      },
    });

    wrapper.vm.$refs = {
      specialTerms: {
        initCheckList: jest.fn(),
        validator: jest.fn()
      },
    }
  });

  afterEach(() => {
    wrapper.destroy();
  });

  test('should render the component', () => {
    expect(wrapper.exists()).toBe(true);
  });

  test('should have the correct props', () => {
    expect(wrapper.props('bizName')).toEqual('testBizName');
    expect(wrapper.props('purpose')).toEqual('testPurpose');
  });

  test('should show the modal when showModal method is called', async () => {
    wrapper.vm.showModal();
    await wrapper.vm.$nextTick();

    expect(wrapper.vm.visible).toBe(true);
  });

  test('should handle the close event and emit close', () => {
    wrapper.vm.handleClose();

    expect(wrapper.emitted('close')).toHaveLength(1);
  });

});
