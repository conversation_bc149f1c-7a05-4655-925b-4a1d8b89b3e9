import Vue from 'vue';
import mixin from '../mixin/base';
import { CHECKED_TERMS_KEY, TERM_LISTS_KEY } from '@/common/const-data';

describe('Vue Mixin', () => {
  let vm: any;

  beforeEach(() => {
    // 创建一个新的 Vue 实例，并应用混入
    vm = new Vue({
      mixins: [mixin]
    });

  });

  afterEach(() => {
    vm.$destroy();
  });

  test('initHistory method should create memory history and set listeners', () => {
    vm.initHistory();
    expect(vm.history).toBeDefined();
    expect(vm.historyUnlisten).toBeDefined();
  });

  test('resetHistory method should reset history data', () => {
    vm.resetHistory();
    expect(vm.history).toBeUndefined();
    expect(vm.location).toBeUndefined();
  });

  test('initLogin method should handle success and failure cases', async () => {
    // 模拟 ajaxGet 成功的响应
    vm.ajaxGet = jest.fn().mockResolvedValueOnce({ success: true, result: { login_ways: [], pwd_complex_list: [], terms: [] } });
    await vm.initLogin();
    expect(vm._store.initLoginWays).toEqual([]);
    expect(vm._store.initPwdRegList).toEqual([]);
    expect(vm._store.initTerms).toEqual([]);
    expect(vm._store.initLoading).toBe(false);

    // 模拟 ajaxGet 失败的响应
    // vm.ajaxGet = jest.fn().mockRejectedValueOnce(new Error('Request failed'));
    // await vm.initLogin();
    // 可以根据您的需求添加更多断言来处理失败情况
  });

  test('clearTerms method should remove local storage items', () => {
    localStorage.setItem(CHECKED_TERMS_KEY, 'terms');
    localStorage.setItem(TERM_LISTS_KEY,'lists');
    vm.clearTerms();
    expect(localStorage.getItem(CHECKED_TERMS_KEY)).toBeNull();
    expect(localStorage.getItem(TERM_LISTS_KEY)).toBeNull();
  });

  test('cacheCheckedTermIds method should set local storage items', () => {
    vm.cacheCheckedTermIds('terms', ['list']);
    expect(localStorage.getItem(CHECKED_TERMS_KEY)).toEqual('terms');
    expect(localStorage.getItem(TERM_LISTS_KEY)).toEqual('["list"]');
  });

  test('updateEmailSuffixList method should update store.emailSuffixList', () => {
    vm.updateEmailSuffixList(['suffix']);
    expect(vm._store.emailSuffixList).toEqual(['suffix']);
  });

  test('updateSendVerifyCodeParams method should merge params', () => {
    vm.updateSendVerifyCodeParams({ newParam: 'value' });
    expect(vm._store.sendVerifyCodeParams.newParam).toEqual('value');
  });

  test('updateVerifyPanelParams method should merge params', () => {
    vm.updateVerifyPanelParams({ newPanelParam: 'panelValue' });
    expect(vm._store.verifyPanelParams.newPanelParam).toEqual('panelValue');
  });

  test('reportSentryEvent method should report event if subSentry is available', () => {
    vm.reportSentryEvent('event');
    expect(window.__OAUTH_STATE__.subSentry.reportEvent).toHaveBeenCalledTimes(1);
  });

  test('reportRequestError method should report error event', () => {
    vm.reportRequestError({ url: 'testUrl', params: { param: 'value' }, requestId: 'id', error: { code: '123', message: 'Error' } });
    // 可以添加更多断言来验证 sentry 事件的报告内容
  });

  test('ajaxPost method should make post request and handle errors', () => {
    vm.ajaxPost('url', { data: 'data' });
    expect(window.$axios.$post).toHaveBeenCalledTimes(1);
  });

  test('ajaxPostJSON method should make post request and handle errors', () => {
    vm.ajaxPostJSON('url', { data: 'data' });
    expect(window.$axios.$post).toHaveBeenCalledTimes(2);
  });

  test('ajaxGet method should make get request and handle errors', () => {
    vm.$axios = { $get: jest.fn() };
    vm.ajaxGet('url', { params: { param: 'value' } });
    expect(window.$axios.$get).toHaveBeenCalledTimes(1);
  });

  test('genRequestId method should generate request id', () => {
    window.__OAUTH_STATE__.initUuid = 'uuid';
    expect(vm.genRequestId()).toMatch(/uuid_[0-9a-f]{6}/);
  });
});