import { shallowMount, createLocalVue, config } from '@vue/test-utils';
import AuthButton from '../components/auth-button.vue';
import ThirdPartyLogo from '../components/third-party-logo.vue'

describe('AuthButton', () => {
  let wrapper: any;

  beforeEach(() => {
    const localVue = createLocalVue();
    wrapper = shallowMount(AuthButton, {
      localVue,
      propsData: {
        type: 'apple',
        size: 'large',
      },
      provide: {
        bizName: 'bizName',
        purpose: 'purpose',
      },
    });
  });

  afterEach(() => {
    wrapper.destroy();
  });

  test('should render the component with correct classes and styles', () => {
    expect(wrapper.find('.third-party-item').exists()).toBe(true);
    expect(wrapper.find('.third-party-item_apple').exists()).toBe(true);
    expect(wrapper.find('.third-party-item--large').exists()).toBe(true);
  });

  test('should emit "click" event on click', () => {
    wrapper.find('.third-party-item').trigger('click');
    expect(wrapper.emitted('click')).toHaveLength(1);
  });

  test('should have correct "data-spm-module" attribute', () => {
    const expectedDataSpmModule = `AccountMergeChannelLIST?ext=${JSON.stringify({
      ChannelName: 'Apple',
      BizName: wrapper.vm.bizName,
      Purpose: wrapper.vm.purpose,
    })}`;
    expect(wrapper.attributes('data-spm-module')).toEqual(expectedDataSpmModule);
  });

  test('should render the ThirdPartyLogo component with correct props', () => {
    const thirdPartyLogo = wrapper.findComponent(ThirdPartyLogo);
    expect(thirdPartyLogo.exists()).toBe(true);
    expect(thirdPartyLogo.props('type')).toEqual('apple');
    expect(thirdPartyLogo.props('size')).toEqual('large');
    expect(thirdPartyLogo.props('fill')).toEqual(wrapper.vm.currentOption.color);
  });

  test('should not render the last-used-tips if isLastUsed is false', () => {
    wrapper.setProps({ type: 'google' });
    expect(wrapper.find('.last-used-tips').exists()).toBe(false);
  });
});
