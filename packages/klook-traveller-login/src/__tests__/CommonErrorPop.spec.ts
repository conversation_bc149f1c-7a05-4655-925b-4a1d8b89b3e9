import { shallowMount } from '@vue/test-utils';
import ThirdPartyList from '../components/third-party-login/index.vue';
import AsyncLoadComponent from '../components/async-load-comp'

declare var global: { [key: string]: any };

describe('ThirdPartyList.vue', () => {
  let wrapper: any;

  beforeEach(() => {
    Object.defineProperty(global, 'window', {
      value: {
        ...window,
        OAuthEvents: {
          $on: jest.fn()
        }
      },
      writable: true
    });
    wrapper = shallowMount(ThirdPartyList, {
      propsData: {
        userTypes: [{login_method: "google"}],
        size: 'large',
        activeType: 'otp',
      },
      provide: {
        bizName: 'bizName',
        purpose: 'purpose',
      }
    });
  });

  afterEach(() => {
    wrapper.destroy();
  });

  test('should render component correctly', () => {
    expect(wrapper.find('.third-party-list').exists()).toBe(true);
  });

  test('should have correct classes based on size prop', () => {
    expect(wrapper.find('.third-party-list--large').exists()).toBe(true);
  });

  test('should get correct spm extra data', () => {
    const comp = { type: { login_method: 'google' } };
    const index = 0;

    const extraData = wrapper.vm.getSpmExtra(comp, index);

    expect(extraData.BizName).toEqual(wrapper.vm.bizName);
    expect(extraData.Purpose).toEqual(wrapper.vm.purpose);
    expect(extraData.LoginMethod).toEqual('Google');
    expect(extraData.listIndex).toEqual(index);
    expect(extraData.listLen).toEqual(wrapper.vm.loginWays.length);
  });
});
