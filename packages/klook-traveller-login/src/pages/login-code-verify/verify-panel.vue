<template>
  <klk-form class="verify-panel">
    <klkUserKitVerifyCodeInputPanel
      ref="verifyCodeInputPanel"
      :language="language"
      :title="__t('111413')"
      :description="__t('111414')"
      :has-help-me-btn="!hideGetHelp"
      :has-change-btn="!isFromLast"
      :code-only="sendCodeOnly"
      :type="type === 'email' ? 'email' : 'sms'"
      v-bind="{
        sendVerifyCodeParams: this.sendVerifyCodeParams,
        verifyAttrs: this.verifyPanelParams,
      }"
      @verifySuccess="verifySuccess"
      @changeOtpMethod="changeOtpMethod"
      @helpBtnClick="helpBtnClick"
    />
  </klk-form>
</template>

<script>
import openPage from '@klook/klk-traveller-utils/lib/openPage.js'
import CrossTabComm from '@klook/klk-traveller-utils/lib/crossTabComm'
import cnMigrationMixin from "@/mixin/cn-migration.ts";
import { Form as klkForm  } from "@klook/klook-ui";
import { klkUserKitVerifyCodeInputPanel } from '@klook/user-kit'
import apis from "@/common/apis";
export default {
  mixins:[cnMigrationMixin],
  inject: ["isKR","platform", "language"],
  data(){
    return {
      type: '',
      isFromLast: false,
      channel: null,
    }
  },
  components: {
    klkUserKitVerifyCodeInputPanel,
    klkForm
  },
  computed: {
    hideGetHelp() {
        return !!window.__OAUTH_STATE__?.options?.config?.hideGetHelp
    },
    sendCodeOnly() {
      return !!window.__OAUTH_STATE__?.options?.config?.sendCode
    },
  },
  methods:{
    addLinkLoginListener() {
      this.channel = new CrossTabComm('email-link-login');
      this.channel.receiveMessage((message) => {
        console.log('=====收到跨tab消息: 登录成功', message)
        window.OAuthEvents.$emit(
          "success",
          message
        );
      });
    },
    async verifySuccess(token){
      const api = this.type === 'email' ? apis.loginByEmailOtp : apis.loginByMobileOtp;
      const res = await this.ajaxPostJSON(api, {
        auth_token: token
      }, true)
      if(res.success){
        window.OAuthEvents.$emit(
          "success",{
            action: res.result.type,
            userInfo: res.result
          })
      }else {
        const flag = await this.handleMigrationUserFlow(res, (!(this.type === 'email' && this.isKR)))
        if(flag) return;
        this.reportSentryEvent(
          '[api] - login error | request-error',
          {
            module: 'verify pannel',
            module_type: 'api',
            action: 'api',
            code: res.error?.code + '',
            message: res.error?.message || 'unknown'
          },
          'internal_error'
        )
        //TODO 这里因为使用了高阶组件包装所以拿不到组件的真实的ref，后续实现类似于react forwardRef
        this.$refs.verifyCodeInputPanel.$children[0] && this.$refs.verifyCodeInputPanel.$children[0].$children[0].handleError(res.error);
      }
    },
    changeOtpMethod(){
      this.history.back();
    },
    helpBtnClick() {
      openPage({ url: `/${window.__OAUTH_STATE__.langPath}faq/category-55/?ref_source=HelpCenterCategoryPage`, target: '_blank' })
    },
  },
  mounted(){
    // 如果是通过邮件链接登录，需要监听登录成功事件
    if(this.type === 'email'){
      this.addLinkLoginListener()
    }
  },
  created() {
    this.type = this.historyState.type;
    this.isFromLast = this.historyState.isFromLast;
  },
  beforeDestroy() {
    this.channel && this.channel.closeChannel();
  },
}
</script>
