<template>
  <div :class="['cn-migration',{'cn-migration--mobile': platform !== 'desktop'}]" >
    <img class="cn-migration_bg" src="https://res.klook.com/image/upload/v1713947852/UED_new/Foundation/General/ill-CN.jpg" alt="migration">
  </div>
</template>

<script>
import { Button } from "@klook/klook-ui";
import baseMixin from "@/mixin/base.ts";
export default {
  name: "in-migration",
  mixins:[baseMixin],
  inject: ["platform"],
  components: {
    klkButton: Button
  },
  data(){
    return {
      loop: false,
      loading: false
    }
  },
  methods: {
    async checkMigrationStatus(){
      try {
        const res = await this.ajaxPostJSON('/v1/userapisrv/public/login/migration/login', {
          migration_no: this.historyState.migration_no,
          auth_token: this.historyState.auth_token
        })
        if(res.success){
          this.updateUserTerms("login");
          window.OAuthEvents.$emit(
            "success",
            {
              action: "login",
              userInfo: res.result
            }
          )
        }else {
          setTimeout(() =>{
            this.checkMigrationStatus()
          },10*1000)
        }
      }catch (e) {
        this.loop = false
      }
    },
    handleClick(){
      // 手动点击刷新暂时保留
      if(this.loop) return;
      this.checkMigrationStatus()
    }
  },
  mounted(){
    this.loop = true
    this.checkMigrationStatus()
  }
}
</script>

<style scoped lang="scss">
.cn-migration {
  &--mobile{
    margin: 0 -24px;
  }
  .cn-migration_bg {
    width: 100%;
  }
}
</style>
