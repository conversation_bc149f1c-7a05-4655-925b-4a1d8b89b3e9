<template>
  <div :class="{'klk-login-home': true, 'click-disabled': buttonLoading}">
    <div class="klk-login-home-top">
    <div class="klk-login-home_title">{{homeTitle}}</div>
    <!--这里当第一个推荐的登录方式是mobile或者email时展示一种样式 -->
    <template v-if="!showAllMethods">
      <div
        :data-spm-module="`${homeTopModule.spm}?ext=${JSON.stringify(homeTopModule.extra)}`">
        <klk-form
          ref="formRef"
          :model="inputForm"
          class="input-panel"
          @submit.native.prevent
          @keyup.native.enter="$refs.submitBtn.$el.click()"
        >
          <klkUserFormItem
            v-if="showLastLogin && lastLoginInfo"
            v-bind="{ downErrorMessage, upErrorMessage }"
          >
            <LastLogged :last-login-info="lastLoginInfo"/>
          </klkUserFormItem>
          <template v-else>
            <klkUserKitPhoneInputFormItem
              :language="language"
              v-if="activeMethod === 'mobile'"
              :form-item-attrs="{ prop: 'phone', downErrorMessage, upErrorMessage }"
              :area-code.sync="inputForm.areaCode"
              :phone-num.sync="inputForm.phoneNum"
              :input-attrs="{ platform: platform, language: language, placeholder: __t('115115') }"
              data-spm-item="Input"
              v-galileo-click-tracker="{ spm: homeTopModule.spm + '.Input', componentName: 'klook-traveller-login' }"
            />
            <klkUserKitEmailInputFormItem
              v-if="activeMethod ===  'email'"
              :language="language"
              v-model="inputForm.email"
              data-spm-item="Input"
              v-galileo-click-tracker="{ spm: homeTopModule.spm + '.Input', componentName: 'klook-traveller-login' }"
              :form-item-attrs="{ prop: 'email', downErrorMessage, upErrorMessage }"
              :input-attrs="{ getEmailSuffix, value: inputForm.email, placeholder: __t('115114') }"
            />
          </template>

          <klkUserKitPasswordInputFormItem
            v-if="['mobile','email'].includes(activeMethod) && activeType !==  'otp'"
            v-model="inputForm.password"
            class="klk-login-password"
            error-type="default"
            :form-item-attrs="{
              prop: 'password',
              errorMsg: 'true'
            }"
            :input-attrs="{
              placeholder: __t('115113'),
              formErrorShake,
              passwordRegList: pwdRegList ,
              defaultPwVisible: false
            }"
            data-spm-item="PasswordInput"
            v-galileo-click-tracker="{ spm: homeTopModule.spm + '.PasswordInput', componentName: 'klook-traveller-login' }"
            @shakeAnimationend="shakeAnimationend"
          />
        </klk-form>

        <klk-button
          ref="submitBtn"
          class="submit-box"
          type="primary"
          block
          :loading="buttonLoading"
          :data-spm-item="isOtp ? 'SendVerificationCode' : 'Login'"
          v-galileo-click-tracker="{ spm: isOtp ? 'SendVerificationCode' : 'Login', componentName: 'klook-traveller-login', autoTrackSpm: true }"
          @click.native.prevent="handleSubmit"
        >
          {{ isOtp ? __t('111408') : __t('sign_log_in') }}
        </klk-button>

        <div v-if="['mobile','email'].includes(activeMethod)" class="remember-forget-password">
          <div
            class="login-modal-link"
            :data-spm-item="`ChangeMethod?ext=${JSON.stringify({
              ClickType: !isOtp ? 'OTP' : 'Password',
            })}`"
            v-galileo-click-tracker="{ spm: homeTopModule.spm + '.ChangeMethod', componentName: 'klook-traveller-login' }"
            @click="activeTypeSwitch"
          >
            {{ isOtp ? __t("13039") : __t("30462") }}
          </div>
          <ForgetPasswordLink
            v-if="!hideForgetPassword"
            class="login-modal-link"
            data-spm-item="ForgetPassword"
            v-galileo-click-tracker="{ spm: homeTopModule.spm + '.ForgetPassword', componentName: 'klook-traveller-login' }"
            v-show="activeType !== 'code'"
            :type="activeMethod === 'mobile' ? 'phone' : activeMethod"
            :rcv="activeMethod === 'email' ? inputForm.email : inputForm.areaCode + '-' + inputForm.phoneNum"
          >{{ __t("5763") }}</ForgetPasswordLink>
        </div>
      </div>

      <div v-if="otherWays && otherWays.length && (!showLastLogin || !lastLoginInfo)" class="other-login-method_wrapper">
        <div class="other-login-method">
          <span> {{ __t("15617") }} </span>
        </div>
        <ThirdPartyVerify size="small" :userTypes="otherWays" :before-login="handleBeforeLogin" @click="handleClickMethod"  :active-type="activeType" />
        <p v-if="!moreOption" class="other-login-method_more" @click="handleMore" :data-spm-module="`MoreOptions?ext=${JSON.stringify({
          Source: homeTopModule.spm,
          Scene: capitalizeFirstLetter(activeMethod === 'mobile' ? 'phone' : activeMethod),
        })}`" data-spm-virtual-item="__virtual" v-galileo-click-tracker="{ spm: 'MoreOptions', componentName: 'klook-traveller-login' }">
            {{ __t('172940') }}
        </p>
      </div>
      <div v-else class="other-login-method_btn" @click="handleShowAllMethods" :data-spm-module="`MoreOptions?ext=${JSON.stringify({
          Source: homeTopModule.spm,
          Scene: capitalizeFirstLetter(activeMethod === 'mobile' ? 'phone' : activeMethod),
        })}`" data-spm-virtual-item="__virtual" v-galileo-click-tracker="{ spm: 'MoreOptions', componentName: 'klook-traveller-login' }">
        <span>{{ __t('172949') }}</span>&nbsp
        <IconNextCircle theme="outline" size="16" :fill="$colorTextDisabled" />
      </div>
    </template>
    <!--这里当第一个推荐的登录方式不是mobile或者email时展示一种样式 -->
    <div v-else class="klk-login-home-all-methods">
      <ThirdPartyVerify :userTypes="moreOption ? loginWays : loginWays.slice(0, 3)" @click="handleClickMethod"  :before-login="handleBeforeLogin" :active-type="showAllMethods ? '' : activeType " />
      <p v-if="!moreOption" class="other-login-method_more" @click="handleMore" :data-spm-module="`MoreOptions?ext=${JSON.stringify({
        Source: 'FirstLogin',
        Scene: capitalizeFirstLetter(loginWays && loginWays[0] && loginWays[0].login_method),
      })}`" data-spm-virtual-item="__virtual" v-galileo-click-tracker="{ spm: 'MoreOptions', componentName: 'klook-traveller-login' }">
          {{ __t('172940') }}
      </p>
    </div>
    </div>
    <SpecialTerms ref="specialTerms" />
  </div>
</template>

<script>

import {
  klkUserKitSendVerifyCodeBtn,
  klkUserKitPhoneInputFormItem,
  klkUserKitEmailInputFormItem,
  klkUserKitPasswordInputFormItem,
  klkUserKitSendVerifyEmailMixin,
  klkUserFormItem
} from '@klook/user-kit';
import IconNextCircle from '@klook/klook-icons/lib/IconNextCircle';
import { $colorTextDisabled } from '@klook/klook-ui/lib/utils/design-token-esm';
import { Button as klkButton, Input as klkInput, Form as klkForm  } from "@klook/klook-ui";
import cnMigrationMixin from "@/mixin/cn-migration.ts";
import ThirdPartyVerify from "@/components/third-party-login/index.vue";
import LastLogged from "@/components/last-logged/index.vue";
import ForgetPasswordLink from "@/components/forget-password-link.vue";
import SpecialTerms from "@/components/special-terms.vue";
import '@klook/user-kit/dist/esm/index.css'
import { showCommonErrorPop } from "@/components/common-error-pop";
import { capitalizeFirstLetter } from "@/common/utils";

export default {
  name: "HomeSection",
  inject: ["platform", "language", "bizName", "purpose"],
  props:{
    loginWays:{
      type:Array,
      default:()=>[]
    },
    terms:{
      type:Array,
      default:()=>[]
    },
    pwdRegList:{
      type:Array,
      default:()=>[]
    }
  },
  data() {
    return {
      showLastLogin: true,
      activeMethod: '',
      activeType: '',
      inputForm:{
        areaCode: '',
        phoneNum: '',
        email: '',
        password: ''
      },
      downErrorMessage: '',
      upErrorMessage: '',
      formErrorShake: false,
      buttonLoading: false,
      showAllMethods: false,
      lastLoginInfo: null,
      moreOption: false,
      $colorTextDisabled: $colorTextDisabled,
    }
  },
  components: {
    SpecialTerms,
    ForgetPasswordLink,
    klkUserKitSendVerifyCodeBtn,
    klkUserKitPhoneInputFormItem,
    klkUserKitEmailInputFormItem,
    klkUserFormItem,
    klkUserKitPasswordInputFormItem,
    klkButton,
    klkInput,
    klkForm,
    ThirdPartyVerify,
    LastLogged,
    IconNextCircle
  },
  computed:{
    hideForgetPassword() {
      return !!window.__OAUTH_STATE__?.options?.config?.hideForgetPassword
    },
    sendCodeOnly() {
      return !!window.__OAUTH_STATE__?.options?.config?.sendCode
    },
    recommendWay(){
      return this.loginWays.find((item)=>item.login_method === this.activeMethod)
    },
    otherWays(){
      const list =
        this.loginWays.filter((item)=>item.login_method !== this.activeMethod)
      return this.moreOption
        ? list
        : list.slice(0, 3)
    },
    homeTitle(){
      if(this.lastLoginInfo){
        return this.__t('172947')
      }
      if(this.activeType === 'pwd'){
        return this.__t('172945')
      }
      return this.__t('935')
    },
    homeTopModule() {
      const baseExtra = {
        BizName: this.bizName,
        Purpose: this.purpose,
      }
      if (this.lastLoginInfo) {
        return {
          spm: 'LastLogin',
          extra: {
            ...baseExtra,
            LoginType: capitalizeFirstLetter(this.lastLoginInfo.login_method)
          }
        }
      } else if (!this.showAllMethods) {
        if (this.activeType === 'pwd') {
          return {
            spm: 'EntryPassword',
            extra: {
              ...baseExtra,
              Scene: this.activeMethod === 'mobile' ? 'phone' : this.activeMethod,
            }
          }
        } else {
          return {
            spm: 'EntryAccount',
            extra: {
              ...baseExtra,
              Scene: this.activeMethod === 'mobile' ? 'phone' : this.activeMethod,
              Source: 'LoginSignup'
            }
          }
        }
      } else {
        return {
          spm: 'EntryAccount',
          extra: {
            ...baseExtra,
            Scene: this.activeMethod === 'mobile' ? 'phone' : this.activeMethod,
            Source: 'LoginSignup'
          }
        }
      }
    },
    isOtp() {
      return this.activeType === 'otp';
    }
  },
  mixins: [klkUserKitSendVerifyEmailMixin, cnMigrationMixin],
  methods: {
    capitalizeFirstLetter,
    handleMore(){
      this.moreOption = true
    },
    handleShowAllMethods(){
      this.moreOption = true
      this.showAllMethods = true;
      this.lastLoginInfo = null;
    },
    initLastLoginInfo(){
      try {
        const obj = this._store.lastLogin;

        if(obj){
          const supportMethodList = this.loginWays.map((item) =>  item.login_method)
          if(supportMethodList.includes(obj.login_method)){
            this.lastLoginInfo = obj
          }
        }
      } catch (e) {
        console.error(e)
      }
    },
    getCodeParams(term_ids) {
      const newParams = {}
      if (this.activeMethod === 'email') {
        newParams.type = 3
        newParams.rcv = this.inputForm.email
        newParams.payload = {
          email: this.inputForm.email,
          term_ids: (term_ids || '').split(',').map((id)=> +id ),
          invite_code: window.__OAUTH_STATE__.options.inviteCode || ''
        }
        newParams.page_data = { redirect_url: window.location.href }
      } else {
        newParams.type = 1
        newParams.rcv = `${this.inputForm.areaCode}-${this.inputForm.phoneNum}`
        newParams.payload = {
          mobile: `${this.inputForm.areaCode}-${this.inputForm.phoneNum}`,
          term_ids: (term_ids || '').split(',').map((id)=> +id ),
          invite_code: window.__OAUTH_STATE__.options.inviteCode || ''
        }
      }
      newParams.action = 'login_register'
      newParams._rc = ''
      return { ...this.sendVerifyCodeParams, ...newParams }
    },
    getPwdParams(term_ids){
      const newParams = {}
      if (this.activeMethod === 'email') {
        newParams.email = this.inputForm.email
        newParams.pwd = this.inputForm.password
        newParams.invite_code = window.__OAUTH_STATE__.options.inviteCode || ''
      } else {
        newParams.mobile = `${this.inputForm.areaCode}-${this.inputForm.phoneNum}`
        newParams.pwd = this.inputForm.password
        newParams.invite_code = window.__OAUTH_STATE__.options.inviteCode || ''
      }
      newParams.term_ids = (term_ids || '').split(',').map((id)=> +id )
      newParams.action = 'login_register'
      newParams._rc = ''
      return newParams
    },
    activeTypeSwitch(){
      this.clearError()
      setTimeout(() => {
        // 处理埋点module问题，点击的埋点module取的应该是切换时的module
        this.isOtp ? this.activeType = "pwd" :  this.activeType = "otp"
      }, 0)
    },
    handleBeforeLogin() {
      return new Promise((resolve) => {
        if (this.$refs.specialTerms) {
          this.$refs.specialTerms.validator((valid, termIds) => {
            if (valid) {
              this.cacheCheckedTermIds(termIds);
              resolve(true);
            } else {
              this.reportSentryEvent(
                '[validate] - term invalid | invalid-form-submit',
                {
                  module: 'term verify',
                  module_type: 'validate',
                  action: 'ui',
                }
              )
              resolve(false);
            }
          });
        } else {
          resolve(true);
        }
      });
    },
    handleSubmit(){
      this.clearError()
      this.$refs.specialTerms.validator(async (valid, termIds) => {
        if (valid) {
          this.cacheCheckedTermIds(termIds);
          const validRes = await this.validateFunc()
          if (validRes) {
            if(['mobile', 'email'].includes(this.activeMethod)){
              this.buttonLoading = true
              if(this.isOtp){
                try {
                  const codeParamsAttrs =  this.getCodeParams(termIds)
                  const { res, verifyUseRid } = await this.sendVerifyEmail(codeParamsAttrs, (this.activeMethod === 'email' && !this.sendCodeOnly) ?  'codeAndLink' : 'code')
                  this.buttonLoading = false
                  if (res && res.success) {
                    // 手机号发送otp
                    const verifyPanelData = {
                      rcv: codeParamsAttrs.rcv,
                      verifyUseRid,
                      otp_token: res.result.otp_token,
                      next_timestamp_sec: res.result.next_timestamp_sec,
                      support_type: res.result.support_type || []
                    }
                    // 设置verify面板所需字段
                    this.updateVerifyPanelParams(verifyPanelData)
                    // 更新sendVerifyCodeParams，verify面板重发需要使用
                    this.updateSendVerifyCodeParams(this.getCodeParams(termIds))
                    this.history.push('/loginVerifyCode', {
                      type: this.activeMethod,
                      isFromLast: !!this.lastLoginInfo
                    })
                  } else {
                    this.sendVerifyCodeError(res.error || null)
                  }
                } catch (error) {
                  this.buttonLoading = false
                  this.sendVerifyCatch(error?.message || error)
                }
              }
              if (this.activeType === 'pwd'){
                try {
                  const pwdParamsAttrs = this.getPwdParams(termIds)
                  // 账号密码流程
                  const { res, verifyUseRid } = await this.sendVerifyPassword(pwdParamsAttrs, this.activeMethod === 'email' ?  'email' : 'mobile')
                  this.buttonLoading = false
                  if(res.success){
                    window.OAuthEvents.$emit("success",{
                      action: res.result.type,
                      userInfo: res.result
                    })
                  }else {
                    const flag = await this.handleMigrationUserFlow(res, false)
                    if(flag) return
                    this.sendVerifyCodeError(res.error || null)
                  }
                } catch (error) {
                  this.buttonLoading = false
                  this.sendVerifyCatch(error?.message || error)
                }
              }
            }else {
              // 三方登录方式
              const AuthClass = (await import(`../../common/${this.activeMethod}/index.ts`)).default
              await new AuthClass(this.handleMigrationUserFlow, showCommonErrorPop).signin()
            }
          }
        } else {
          this.reportSentryEvent(
            '[validate] - term invalid | invalid-form-submit',
            {
              module: 'term verify',
              module_type: 'validate',
              action: 'ui',
            },
            'internal_error'
          )
        }
      });
    },
    handleClickMethod(type){
      this.clearError()
      if(['mobile', 'email'].includes(type))  {
        this.activeMethod = type
        this.activeType = this.recommendWay.default_login_type || ''
        this.showAllMethods = false
      }
    },
    shakeAnimationend() {
      this.formErrorShake = false
    },
    async getEmailSuffix() {
      if (this.emailSuffixList.length) { return this.emailSuffixList }
      return []
    },
    async validateFunc() {
      this.clearError()
      const formRef = this.$refs.formRef
      return await formRef.validate()
    },
    clearError() {
      this.upErrorMessage = ''
      this.downErrorMessage = ''
    },
    sendVerifyCatch(error) {
      let msg = this.__t('111839')
      if (error === 'networkError') {
        msg += '[geetest]'
      }
      this.sendVerifyCodeError({
        message: msg,
      })

      this.inhouse.track("custom", "body", {
        spm: `Login_Signup.SendVerifyCodeError`,
        ext: {
          Error: error,
          ErrorMsg: msg || this.__t('111839'),
        }
      });
    },
    sendVerifyCodeError(error){
      if (error?.code === '-9098') {
        return;
      }
      if (error) {
        if (error.code === '2999') {
          this.downErrorMessage = error.message
        }else {
          this.upErrorMessage = error.message || this.__t('111839')
        }
      } else {
        this.upErrorMessage = this.__t('111839')
      }
      this.reportSentryEvent(
        '[verify] - email/mobile error | business-error',
        {
          module: 'home',
          module_type: 'business',
          action: 'ui',
          message: error?.message || this.__t('111839')
        },
        'internal_error'
      )
    },
    handleLoginMethod() {
      const loginMethod = this._store.loginMethod;
      if (!loginMethod) {
        return;
      }
      if (loginMethod === 'all') {
        this.showAllMethods = true;
      }
      if (['email', 'mobile'].includes(loginMethod)) {
        this.showAllMethods = false;
        this.showLastLogin = false;
        this.activeMethod = loginMethod;
        this.activeType = 'otp';
      }
    }
  },
  mounted(){
    // 拉取密码正则
    // 现根据登录方式列表设置默认的推荐值
    if(this.loginWays.length){
      this.activeMethod = this.loginWays[0].login_method
      if(this.loginWays[0].default_login_type){
        this.activeType = this.loginWays[0].default_login_type
      }
    }
    // 如果登录方式不大于5个，则默认显示所有登录方式
    if(this.loginWays.length <= 5){
      this.moreOption = true
    }
    if(!['mobile','email'].includes(this.activeMethod)){
      this.showAllMethods = true
    }
    //  根据当前支持的登录列表计算出是否需要展示上一次的登录
    this.initLastLoginInfo()
    // 根据上一次登录的信息设置默认的推荐值
    if(this.lastLoginInfo){
      this.showAllMethods = false
      this.activeMethod = this.lastLoginInfo.login_method
      if(this.lastLoginInfo.default_login_type){
        this.activeType = this.lastLoginInfo.default_login_type
      }else {
        this.activeType = ''
      }
      if(this.lastLoginInfo.login_method === 'email'){
        this.inputForm.email = this.lastLoginInfo.login_id
      }
      if(this.lastLoginInfo.login_method === 'mobile'){
        const [areaCode,phoneNum] = (this.lastLoginInfo.login_id || '').split('-')
        this.inputForm.areaCode = areaCode
        this.inputForm.phoneNum = phoneNum
      }
    }
    //初始化条款
    this.$refs.specialTerms.initCheckList(this.terms)
    this.handleLoginMethod();
  }
};
</script>

<style lang="scss">
@import "../../style/home.scss";
.klk-login-home {
  height: 100%;
  display: flex;
  flex-direction: column;

  &-top {
    flex: 1;
  }
  .klk-login-home_title {
      @include font-heading-m();

      color:  $color-text-primary;
  }
  .klk-login-password {
    margin-top: -20px;
  }
  .remember-forget-password {
    display: flex;
    cursor: pointer;
    justify-content: space-between;
    align-items: stretch;
    color: #757577;
    text-align: right;
    margin-top: 16px;
    font-size: 14px;

    &::v-deep .klk-checkbox-label {
      color: #757575;
    }

    a {
      text-decoration: none;
      color: #212121;
      text-decoration: underline;
    }
  }
  .other-login-method_btn {
    @include font-body-s-regular();

    display: flex;
    cursor: pointer;
    padding: 11px 0;
    margin-top: 21px;
    justify-content: center;
    align-items: center;
    color: $color-text-secondary;
  }

  .other-login-method_more {
    @include font-body-s-regular();

    cursor: pointer;
    // margin-top: 24px;
    color: $color-text-primary;
    text-align: center;
    text-decoration: underline;
  }
  .submit-box {
    margin-top: 16px;
  }

  .other-login-method_wrapper {
    margin-bottom: 32px;
  }

  .klk-login-term-list .tnc-list {
    margin-bottom: 0;
  }
}
</style>
