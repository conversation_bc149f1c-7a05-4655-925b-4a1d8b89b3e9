<template>
  <klk-form class="simplify-checkout-verify-panel">
    <klkUserKitVerifyCodeInputPanel
      ref="verifyCodeInputPanel"
      :language="language"
      :title="__t('111413')"
      :description="__t('111414')"
      :has-help-me-btn="true"
      :has-change-btn="false"
      :code-only="true"
      :type="type === 'email' ? 'email' : 'sms'"
      v-bind="{
        sendVerifyCodeParams: this.sendVerifyCodeParams,
        verifyAttrs: this.verifyPanelParams,
      }"
      @verifySuccess="verifySuccess"
      @changeOtpMethod="changeOtpMethod"
      @helpBtnClick="helpBtnClick"
    />
  </klk-form>
</template>

<script>
import openPage from "@klook/klk-traveller-utils/lib/openPage.js";
import cnMigrationMixin from "@/mixin/cn-migration.ts";
import { Form as klkForm } from "@klook/klook-ui";
import { klkUserKitVerifyCodeInputPanel } from "@klook/user-kit";
import apis from "@/common/apis";
import { capitalizeFirstLetter } from "@/common/utils";

export default {
  name: "SimplifyCheckoutVerifyPanel",
  mixins: [cnMigrationMixin],
  inject: ["isKR", "platform", "language"],
  data() {
    return {
      type: "",
      verifyData: {}
    };
  },
  components: {
    klkUserKitVerifyCodeInputPanel,
    klkForm,
  },
  methods: {
    async verifySuccess(token) {
      const api =
        this.type === "email" ? apis.simplifyCheckoutVerifyEmail : apis.simplifyCheckoutVerifyMobile;
      const res = await this.ajaxPostJSON(
        api,
        {
          login_token: this.verifyData.verify_token,
          otp_token: token,
        },
        true
      );
      if (res.success) {
        window.OAuthEvents.$emit("success", {
          action: res.result.type,
          channelName: capitalizeFirstLetter(this.type),
          userInfo: res.result,
        });
      } else {
        const flag = await this.handleMigrationUserFlow(
          res,
          !(this.type === "email" && this.isKR)
        );
        if (flag) return;
        //TODO 这里因为使用了高阶组件包装所以拿不到组件的真实的ref，后续实现类似于react forwardRef
        this.$refs.verifyCodeInputPanel.$children[0] &&
          this.$refs.verifyCodeInputPanel.$children[0].$children[0].handleError(
            res.error
          );
        window.OAuthEvents.$emit("fail", res);
      }
    },
    changeOtpMethod() {
      this.history.back();
    },
    helpBtnClick() {
      openPage({
        url: `/${window.__OAUTH_STATE__.langPath}faq/category-55/?ref_source=HelpCenterCategoryPage`,
        target: "_blank",
      });
    },
  },
  mounted() {
    
  },
  created() {
    this.type = this.historyState.type;
    this.verifyData = this.historyState.data;
  },
};
</script>
