<template>
  <div
    :class="`simplify-checkout-verify simplify-checkout-verify-${platform}`"
    :data-spm-module="`FraudPopup?ext=${JSON.stringify({
      VerificationType: verificationType,
    })}`"
    v-loading="isLoading"
    data-klk-loading-show-loading-bg="false"
    data-klk-loading-show-overlay="false"
  >
    <div class="simplify-checkout-verify-title">{{ __t("177920") }}</div>
    <div class="simplify-checkout-verify-content">
      {{ __t("177921") }}
    </div>
    <klk-form ref="formRef">
      <klkUserFormItem v-bind="{ downErrorMessage, upErrorMessage }">
        <div>
          <div
            v-if="email"
            @click="handleVerify('email')"
            data-spm-item="Email"
            v-galileo-click-tracker="{ spm: 'FraudPopup.Email', componentName: 'klook-traveller-login' }"
            class="simplify-checkout-verify-item simplify-checkout-verify-email"
          >
            <div class="simplify-checkout-verify-logo">
              <ThirdPartyLogo type="email" size="small" />
            </div>
            <span class="simplify-checkout-verify-name">{{ email }}</span>
          </div>

          <div
            v-if="mobile"
            @click="handleVerify('mobile')"
            data-spm-item="Phone"
            v-galileo-click-tracker="{ spm: 'FraudPopup.Phone', componentName: 'klook-traveller-login' }"
            class="simplify-checkout-verify-item simplify-checkout-verify-mobile"
          >
            <div class="simplify-checkout-verify-logo">
              <ThirdPartyLogo type="mobile" size="small" />
            </div>
            <span class="simplify-checkout-verify-name">{{ mobile }}</span>
          </div>
        </div>
      </klkUserFormItem>
    </klk-form>
  </div>
</template>

<script>
import cnMigrationMixin from "@/mixin/cn-migration.ts";
import { Form as klkForm } from "@klook/klook-ui";
import {
  klkUserKitVerifyCodeInputPanel,
  klkUserKitSendVerifyEmailMixin,
  klkUserFormItem,
} from "@klook/user-kit";
import ThirdPartyLogo from "@/components/third-party-logo.vue";
import "@klook/user-kit/dist/esm/index.css";

export default {
  name: "SimplifyCheckoutVerify",
  mixins: [cnMigrationMixin, klkUserKitSendVerifyEmailMixin],
  inject: ["isKR", "platform", "language"],
  data() {
    return {
      isLoading: false,
      type: "",
      verifyData: {},
      payload: {},
      email: "",
      mobile: "",
      downErrorMessage: "",
      upErrorMessage: "",
    };
  },
  components: {
    klkUserKitVerifyCodeInputPanel,
    klkUserFormItem,
    klkForm,
    ThirdPartyLogo,
  },
  methods: {
    async handleVerify(type) {
      this.isLoading = true;
      this.clearError();
      try {
        const codeParamsAttrs = this.getCodeParams(type);
        const { res, verifyUseRid } = await this.sendVerifyEmail(
          codeParamsAttrs,
          "code"
        );

        this.isLoading = false;

        if (!res.success) return this.sendVerifyCodeError(res.error || null);
        // 手机号发送otp
        const verifyPanelData = {
          rcv: codeParamsAttrs.rcv,
          verifyUseRid,
          otp_token: res.result.otp_token,
          next_timestamp_sec: res.result.next_timestamp_sec,
          support_type: res.result.support_type || [],
        };
        // 设置verify面板所需字段
        this.updateVerifyPanelParams(verifyPanelData);
        // 更新sendVerifyCodeParams，verify面板重发需要使用
        this.updateSendVerifyCodeParams(this.getCodeParams(type));
        this.history.push("/simplifyCheckoutVerifyPanel", {
          type: type,
          data: this.verifyData,
        });
      } catch (e) {
        this.isLoading = false;
      }
    },
    getCodeParams(activeMethod) {
      const newParams = {};
      const payload = this.payload;
      if (activeMethod === "email") {
        newParams.type = 3;
        newParams.rcv = this.email;
        newParams.payload = payload;
        newParams.page_data = { redirect_url: window.location.href };
      } else {
        newParams.type = 1;
        newParams.rcv = this.mobile;
        newParams.payload = payload;
      }
      newParams.action = "simplify_checkout";
      newParams._rc = ''
      return { ...this.sendVerifyCodeParams, ...newParams };
    },
    clearError() {
      this.upErrorMessage = "";
      this.downErrorMessage = "";
    },
    sendVerifyCodeError(error) {
      if (error?.code === '-9098') {
        return;
      }
      if (error) {
        if (error.code === "2999") {
          this.downErrorMessage = error.message;
        } else {
          this.upErrorMessage = error.message;
        }
      } else {
        this.upErrorMessage = this.__t("111839");
      }
    },
  },
  mounted() {
    console.log(">>>>>>>>historyState", this.historyState);
    this.type = this.historyState.type;
    this.payload = this.historyState.payload;
    this.verifyData = this.historyState.data || {};
    this.mobile = this.historyState.mobile;
    this.email = this.historyState.email;
    if (this.type) {
      this.handleVerify(this.type);
    }
  },
  computed: {
    verificationType() {
      if (this.type === "email") {
        return "Email";
      } else {
        return this.type ? "Phone" : "Email,Phone";
      }
    },
  },
};
</script>

<style lang="scss">
.simplify-checkout-verify {
  .simplify-checkout-verify-title {
    @include font-heading-m();
    color: $color-text-primary;
  }

  .simplify-checkout-verify-content {
    margin: 32px 0;
    font-size: 16px;
    color: #000;
  }

  &-mobile {
    .simplify-checkout-verify-content {
      margin-top: 12px;
    }
  }

  .simplify-checkout-verify-item {
    padding: 12px 16px;
    height: 48px;
    position: relative;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    border-radius: 12px;
    margin-bottom: 12px;
    border: 1px solid $color-border-normal;
  }

  .simplify-checkout-verify-logo {
    position: absolute;
    display: flex;
    align-items: center;
    justify-content: center;
    left: 0;
    top: 2px;
    width: 24px;
    height: 24px;
    padding: 10px;
    box-sizing: content-box;
  }

  .simplify-checkout-verify-name {
    font-family: sans-serif;
    width: calc(100% - 28px);
    display: flex;
    align-items: center;
    font-weight: 600;
    font-size: 16px;
    text-align: center;
    justify-content: center;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}
</style>
