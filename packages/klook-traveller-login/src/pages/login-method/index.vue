<template>
  <div class="klook-traveller-login_login-method">
    <SimplifyCheckoutThirdPartyLogin
      :platform="platform"
      :userTypes="renderLoginWays"
      @click="handleClickMethod"
      :before-login="handleBeforeLogin"
    />

    <div
      v-if="loginWays.length > max"
      @click="showLoginDialog"
      class="klook-traveller-login_login-method-more-options"
    >
      {{ __t("172940") }}
    </div>

    <div>
      <klk-modal
        :class="`login-method-terms login-method-terms-${platform}`"
        v-if="platform === 'desktop'"
        :open.sync="showTerms"
        :closable="true"
        :show-default-footer="false"
        :scrollable="false"
        :width="436"
        :title="__t('177449')"
        @close="showTerms = false"
      >
        <div>
          <SpecialTerms ref="specialTerms" confirmType="warning" />

          <div class="login-method-terms_footer">
            <klk-button block @click="onConfirm">
              {{ __t("177451") }}
            </klk-button>
            <klk-button block type="outlined" @click="showTerms = false">
              {{ __t("177452") }}
            </klk-button>
          </div>
        </div>
      </klk-modal>

      <klk-bottom-sheet
        :class="`login-method-terms login-method-terms-${platform}`"
        show-close
        v-if="showTerms && platform === 'mobile'"
        :visible.sync="showTerms"
        header-divider
        :title="__t('177453')"
      >
        <div class="login-method-terms_title">{{ __t("177449") }}</div>
        <SpecialTerms ref="specialTerms" confirmType="warning" />

        <div class="login-method-terms_footer">
          <klk-button block @click="onConfirm">
            {{ __t("177451") }}
          </klk-button>
          <klk-button block type="outlined" @click="showTerms = false">
            {{ __t("177452") }}
          </klk-button>
        </div>
      </klk-bottom-sheet>
    </div>
  </div>
</template>

<script>
import baseMixin from "@/mixin/base.ts";
import { Modal, BottomSheet } from "@klook/klook-ui";
import apis from "@/common/apis.ts";
import SimplifyCheckoutThirdPartyLogin from "@/components/third-party-login/simplify-checkout.vue";
import { EVENTS } from "@/common/const-data";
import SpecialTerms from "@/components/special-terms.vue";
import { showCommonErrorPop } from "@/components/common-error-pop";
import cnMigrationMixin from "@/mixin/cn-migration.ts";

export default {
  name: "LoginMethod",
  mixins: [baseMixin, cnMigrationMixin],
  components: {
    KlkModal: Modal,
    KlkBottomSheet: BottomSheet,
    SimplifyCheckoutThirdPartyLogin,
    SpecialTerms,
  },
  props: {
    platform: {
      type: "desktop" | "mobile",
      default: "desktop",
    },
    max: {
      type: Number,
      default: 4,
    },
  },
  provide() {
    return {
      platform: this.platform,
      bizName: "",
      purpose: "",
    };
  },
  data() {
    return {
      showTerms: false,
      loginWays: [],
      terms: [],
      clickMethod: "",
      allDisplayOnly: false,
      renderLoginWays: [],
    };
  },
  mounted() {
    // this.initLogin();
    this.init();
  },
  methods: {
    async init() {
      try {
        const res = await this.ajaxGet(apis.loginInit);
        this.loginWays = res.result.login_ways || [];
        this.renderLoginWays = this.loginWays.slice(0, Number(this.max));

        this.allDisplayOnly = res.result.terms.every(
          (item) => item.display_only === true
        );
        this.terms = res.result.terms;
      } catch (e) {}
    },
    onConfirm() {
      this.$refs.specialTerms.validator(async (valid, termsId) => {
        if (valid) {
          this.showTerms = false;
          const AuthClass = (
            await import(`../../common/${this.clickMethod}/index.ts`)
          ).default;
          await new AuthClass(this.handleMigrationUserFlow, showCommonErrorPop).signin()
        }
      });
    },
    handleClickMethod(method) {
      this.clickMethod = method;
      if (["email", "mobile"].includes(method)) {
        window.OAuthEvents.$emit(EVENTS.SIMPLIFY_CHECKOUT_SHOW_ALL, {
          method: method,
        });
      }

      window.OAuthEvents.$emit(EVENTS.SIMPLIFY_CHECKOUT_LOGIN_METHOD);
    },
    handleBeforeLogin() {
      if (this.allDisplayOnly) {
        return Promise.resolve(true);
      };
      this.showTerms = true;
      this.$nextTick(() => {
        this.$refs.specialTerms.initCheckList(this.terms);
      });
    },
    showLoginDialog() {
      window.OAuthEvents.$emit(EVENTS.SIMPLIFY_CHECKOUT_SHOW_ALL, {
        method: "all",
      });
    },
  },
};
</script>

<style lang="scss">
.klook-traveller-login_login-method {
  min-height: 48px;
  .third-party-list {
    display: flex;
    justify-content: space-between;

    .third-party-item {
      width: 160px;
      padding: 12px 16px;
      height: 48px;

      .third-party-name {
        width: calc(100% - 20px);
        margin-left: 20px;
      }
    }

    &--mobile {
      .third-party-item {
        width: 76px;

        .third-party-logo {
          position: static;
          padding: 0;
        }
      }
    }
  }

  &-more-options {
    @include font-body-m-regular();
    cursor: pointer;
    user-select: none;
    text-decoration: underline;
    margin-top: 16px;
    text-align: center;
    font-size: 16px;
  }
}

.login-method-terms {
  &-desktop {
    .klk-login-term-list {
      margin-top: 22px;
    }
  }

  &-mobile {
    .klk-login-term-list {
      margin-top: 16px;
    }

    .login-method-terms_title {
      margin-top: 16px;
      font-size: 16px;
      font-weight: 400;
    }
  }

  .login-method-terms_footer {
    .klk-button {
      margin-bottom: 12px;
    }
  }
}
</style>
