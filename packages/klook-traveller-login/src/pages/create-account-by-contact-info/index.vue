<template>
  <div></div>
</template>

<script>
import baseMixin from "@/mixin/base.ts";
import { Modal, BottomSheet } from "@klook/klook-ui";
import apis from "@/common/apis.ts";
import SimplifyCheckoutThirdPartyLogin from "@/components/third-party-login/simplify-checkout.vue";
import { EVENTS } from "@/common/const-data";
import SpecialTerms from "@/components/special-terms.vue";
import { klkUserKitSendVerifyEmailMixin } from "@klook/user-kit";

export default {
  name: "CreateAccount",
  mixins: [klkUserKitSendVerifyEmailMixin, baseMixin],
  components: {
    KlkModal: Modal,
    KlkBottomSheet: BottomSheet,
    SimplifyCheckoutThirdPartyLogin,
    SpecialTerms,
  },
  props: {
    platform: {
      type: "desktop" | "mobile",
      default: "desktop",
    },
    areaCode: {
      type: String,
      default: "",
    },
    firstName: {
      type: String,
      default: "",
    },
    lastName: {
      type: String,
      default: "",
    },
    localFirstName: {
      type: String,
      default: "",
    },
    localLastName: {
      type: String,
      default: "",
    },
    mobile: {
      type: String,
      default: "",
    },
    email: {
      type: String,
      default: "",
    },
    termsArr: {
      type: Array,
      default: () => {
        return [];
      },
    },
    scene: {
      type: String,
      default: "",
    },
  },
  provide() {
    return {
      platform: this.platform,
      bizName: "",
      purpose: "",
    };
  },
  data() {
    return {
      showTerms: false,
      loginWays: [],
      clickMethod: "",
      renderLoginWays: [],
      termsIdArr: [],
    };
  },
  mounted() {
    this.handleTermsId();
    this.simplifyCheckoutLogin();
  },
  methods: {
    handleTermsId() {
      // ["337,309", "306,344"]; => ["337", "309", "306", "344"]
      if (this.termsArr && this.termsArr.length) {
        let flatArr = this.termsArr.reduce((acc, item) => {
          let splitItems = item
            .split(",")
            .filter((i) => i)
            .map((subItem) => subItem.trim());
          return acc.concat(splitItems);
        }, []);
        this.termsIdArr = Array.from(new Set(flatArr));
      }
    },
    // 调用联系信息创建用户接口， 如果风控
    simplifyCheckoutLogin() {
      const termsIds = this.termsIdArr.join(",") || "";
      this.ajaxPostJSON(apis.simplifyCheckoutLogin, {
        area_code: this.areaCode,
        first_name: this.firstName,
        last_name: this.lastName,
        local_first_name: this.localFirstName,
        local_last_name: this.localLastName,
        email: this.email,
        mobile: this.mobile,
      }).then(async (resp) => {
        const errCode = resp?.error?.code;
        const errData = resp?.error?.data;
        if (resp.success) {
          this.updateTerms(termsIds);
          window.OAuthEvents.$emit("success", {
            action: resp.result.type,
            userInfo: resp.result,
          });
          return;
        }

        // 需要验证
        // 3004 需要选择验证
        // 3002 手机号验证
        // 3003 邮箱验证
        if (["3002", "3003", "3004"].includes(errCode)) {
          let params = {
            payload: {
              first_name: this.firstName,
              last_name: this.lastName,
              local_first_name: this.localFirstName,
              local_last_name: this.localLastName,
              area_code: this.areaCode,
              email: this.email,
              mobile: this.mobile,
              term_ids: (termsIds || "")
                .split(",")
                .filter((t) => t)
                .map((id) => +id),
              invite_code: window.__OAUTH_STATE__.options.inviteCode || "",
            },
          };
          if (errCode === "3004") {
            params = {
              ...params,
              email: this.email,
              mobile: this.mobile,
              data: errData,
              termsIds: termsIds,
            };
          } else {
            const activeMethod = errCode === "3002" ? "mobile" : "email";
            let ext = {};
            if (activeMethod === "mobile") {
              ext.mobile = this.mobile;
            }
            if (activeMethod === "email") {
              ext.email = this.email;
            }
            params = {
              ...params,
              type: activeMethod,
              termsIds: termsIds,
              data: errData,
              ...ext,
            };
          }

          window.OAuthEvents.$emit(EVENTS.SIMPLIFY_CHECKOUT_SHOW_CREATE);
          this.history.push("/simplifyCheckoutVerify", params);
        } else {
          window.OAuthEvents.$emit("fail", resp);
          window.OAuthEvents.$emit("error", resp);
        }
      }).catch(e => {
        window.OAuthEvents.$emit("error", e);
      });
    },
    updateTerms(termIds) {
      if (!termIds) return;
      this.ajaxPostJSON(apis.updateTerms, {
        scene: this.scene || 'simplify_checkout_v2',
        term_ids: termIds,
      });
    },
  },
};
</script>

<style lang="scss"></style>
