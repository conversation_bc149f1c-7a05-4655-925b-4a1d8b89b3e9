import { SimplifyLoginStatus } from '../../types/index';
import apis from "@/common/apis";

export async function checkLogin (): Promise<SimplifyLoginStatus> {
  if (!window.$axios || !window.$axios.$get) {
    throw new Error('$axios is not defined');
  }
  const get = (window.$axios.$get).bind(window.$axios);
  let isLoggedIn = false
  let isLoggedBefore = false
  let lastLoginInfo = {}
  try {
    const lastLoginReq = get(apis.loginInit, {
      params: {
        last_login_data: localStorage.getItem('last_login_data')
      }
    })
    const checkLoginStatusReq = get(apis.getSimpleProfile)
    const [lastLoginResp, checkLoginStatusResp] = await Promise.all([lastLoginReq, checkLoginStatusReq])
    if (lastLoginResp.success && lastLoginResp.result?.last_login?.login_method) {
      isLoggedBefore = true
      lastLoginInfo = lastLoginResp.result.last_login as any || {}
    }
    if (checkLoginStatusResp.success) {
      isLoggedIn = true
    }
  } catch (e) {}
  
  return {
    isLoggedIn,
    isLoggedBefore,
    lastLoginInfo,
  }
}