import Vue, { CreateElement } from "vue";
import CreateAccount from "../pages/create-account-by-contact-info/index.vue";

export function createAccountByContactInfoFn(options: {
  props: Record<string, any>;
}) {
  const CreateAccountCtor = Vue.extend({
    render(h: CreateElement) {
      return h(CreateAccount, {
        props: options.props,
      });
    },
  });

  const instance = new CreateAccountCtor().$mount();
  const wrapper = document.body;
  if (!wrapper) {
    throw new Error(`未找到元素`);
  }
  wrapper.appendChild(instance.$el);
}
