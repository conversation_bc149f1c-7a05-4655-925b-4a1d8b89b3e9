
import Vue, { CreateElement } from "vue";
import LoginMethod from '../pages/login-method/index.vue'

export function loginMethodRenderFn(options: { el: string, props: Record<string, any> } ) {
  const LoginMethodCtor = Vue.extend({
    render(h: CreateElement) {
      return h(LoginMethod, {
        props: options.props
      })
    }
  })

  const instance = new LoginMethodCtor().$mount();
  const wrapper = document.querySelector(options.el);
  if (!wrapper) {
    throw new Error(`未找到${options.el}元素`)
  }
  wrapper.appendChild(instance.$el)
}