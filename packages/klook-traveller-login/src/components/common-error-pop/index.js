import Vue from 'vue'
import ErrorPop from './common-error-pop.vue'
import baseMixin from '@/mixin/base'

export function showCommonErrorPop(option, parent) {
  return new Promise((resolve) => {
    const ErrorPopConstructor = Vue.extend(ErrorPop)
    const errorPop = new ErrorPopConstructor({
      ...(parent ? { parent } : {}),
      el: document.createElement('div'),
      propsData: {
        option: option.data,
        errorCode: option.code
      },
      mixins: [baseMixin]
    })
    document.body.appendChild(errorPop.$el)
    setTimeout(()=>{
      errorPop.visible = true
    })
  })
}
