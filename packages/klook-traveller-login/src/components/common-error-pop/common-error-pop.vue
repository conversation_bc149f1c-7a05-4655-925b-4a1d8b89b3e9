<template>
  <klk-modal
    ref="commonErrorModal"
    :overlay-closable="false"
    id="common-error-modal"
    class="common-error-modal"
    :open.sync="visible"
    :data-spm-page="`LoginSignupErrorPopup?trg=manual&ext=${JSON.stringify({
      SdkVersion: sdkVersion,
      BizName: bizName,
      Purpose: purpose,
      DisplayTitle: option.title,
      ErrorCode: errorCode,
    })}`"
  >
    <div class="klk-modal-content">
      <h3 class="common-error-modal_title">{{ option.title }}</h3>
      <h4 class="common-error-modal_sub-title">{{ option.sub_title }}</h4>
    </div>
    <div slot="footer">
      <template v-for="button in option.detail">
        <div
          :data-spm-module="`${capitalizeFirstLetter(
            button.action
          )}?ext=${JSON.stringify(buttonSpmExtra(button))}`"
          data-spm-virtual-item="__virtual"
          v-galileo-click-tracker="{ spm: capitalizeFirstLetter(button.action), componentName: 'klook-traveller-login' }"
        >
          <klk-button
            v-if="button.style === 'highlight'"
            type="primary"
            block
            @click="handleButtonClick(button)"
          >
            {{ button.text }}
          </klk-button>
          <div
            v-else
            class="common-error-modal_link-button"
            @click="handleButtonClick(button)"
          >
            {{ button.text }}
          </div>
        </div>
      </template>
    </div>
  </klk-modal>
</template>
<script>
import { Modal as klkModal, Button as klkButton } from "@klook/klook-ui";
import { capitalizeFirstLetter } from "@/common/utils";

export default {
  name: "EdmPop",
  components: {
    klkModal,
    klkButton,
  },
  props: {
    option: {
      type: Object,
      default: () => ({}),
    },
    errorCode: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      sdkVersion: null,
      visible: false,
      forgetData: null,
    };
  },
  methods: {
    capitalizeFirstLetter,
    handleButtonClick(option) {
      if (option.action === "deeplink") {
        window.location.href = option.action_detail.deeplink;
      }
      if (option.action === "forgetpwd") {
        let forgetData = null;
        if (option.action_detail.type === "email") {
          forgetData = { email: option.action_detail.login_id || "", type: "email" };
        } else {
          if (option.action_detail.login_id) {
            const [countryCode, phoneNum] =
              option.action_detail.login_id.split("-");
            forgetData = { countryCode, phoneNum,  type: "phone"  };
          } else {
            forgetData = { countryCode: "", phoneNum: "",  type: "email" };
          }
        }
        this.history.push("/forgetPwdInput", forgetData);
      }
      this.visible = false;
    },
    buttonSpmExtra(button) {
      return {
        Type: button.type,
        Text: button.text,
        Style: button.style,
        Action: button.action,
        Deeplink: button?.action_detail?.deeplink,
      };
    },
  },
  mounted() {
    this.sdkVersion = window.__OAUTH_STATE__ && window.__OAUTH_STATE__.version
    this.$refs.commonErrorModal.overlayZIndex = 2000;

    setTimeout(() => {
      const inhouse = this.inhouse
      inhouse && inhouse.track('pageview', '#common-error-modal', {
        force: true
      })
    }, 100)
  },
};
</script>

<style lang="scss">
.common-error-modal_title {
  @include font-heading-s();
  color: $color-text-primary;
}
.common-error-modal_sub-title {
  @include font-body-s-regular();
  color: $color-text-primary;
  margin: 12px 0;
}
.common-error-modal_link-button {
  cursor: pointer;
  margin-top: 12px;
  text-align: center;
  text-decoration: underline;
}
</style>
