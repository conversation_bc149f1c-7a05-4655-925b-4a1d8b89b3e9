<template>
  <span class="klk-login-third-party-logo">
    <component :is="logoComp" :size="logoSize" :fill="fill" />
  </span>
</template>

<script>
import facebookLogoDefault from "../assets/icon_facebook_default.vue";
import facebookLogoFill from "../assets/icon_facebook_fill.vue";

import kakaoLogoDefault from "../assets/icon_kakao_default.vue";
import kakaoLogoFill from "../assets/icon_kakao_fill.vue";

import naverLogoDefault from "../assets/icon_naver_default.vue";
import naverLogoFill from "../assets/icon_naver_fill.vue";

import wechatLogoDefault from "../assets/icon_wechat_default.vue";
import wechatLogoFill from "../assets/icon_wechat_fill.vue";

import appleLogoDefault from "../assets/icon_apple_default.vue";
import appleLogoFill from "../assets/icon_apple_fill.vue";

import googleLogoDefault from "../assets/icon_google_default.vue";
import googleLogoFill from "../assets/icon_google_fill.vue";

import emailLogoDefault from "../assets/icon_email_default.vue";
import emailLogoFill from "../assets/icon_email_fill.vue";

import mobileLogoDefault from "../assets/icon_mobile_default.vue";
import mobileLogoFill from "../assets/icon_mobile_fill.vue";

export default {
  name: 'ThirdPartyLogo',
  components: {
    appleLogoDefault,
    appleLogoFill,
    facebookLogoDefault,
    facebookLogoFill,
    kakaoLogoDefault,
    kakaoLogoFill,
    naverLogoDefault,
    naverLogoFill,
    wechatLogoDefault,
    wechatLogoFill,
    googleLogoDefault,
    googleLogoFill,
    emailLogoFill,
    emailLogoDefault,
    mobileLogoDefault,
    mobileLogoFill
  },
  props: {
    type: {
      type: String,
      default: "",
    },
    size: {
      type: String,
      default: "",
    },
    logoSize: {
      type: Number,
      default: 24
    },
    fill:{
      type: String,
      default: "#000",
    }
  },
  computed: {
    logoComp() {
      return `${this.type}Logo${this.size === 'small'? 'Fill' : 'Default'}`
    }
  }
}

</script>

<style>
.klk-login-third-party-logo {
  display: inline-flex;
  align-items: center;
}
</style>
