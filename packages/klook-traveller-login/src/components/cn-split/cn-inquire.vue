<template>
  <klk-modal
    ref="cnInquireModal"
    :overlay-closable="false"
    overlay-color="rgba(0, 0, 0, 1)"
    class="cn-inquire-modal"
    :title="__t('167132')"
    :open.sync="visible"
  >
    <div class="klk-modal-content">
      <klk-radio-group v-model="ownership" @change="onChange">
        <template v-for="option in options">
          <div v-if="option.show" :key="option.value" class="cn-inquire_option">
            <div
              :class="[ 'cn-inquire_option-wrapper', { 'cn-inquire_option-wrapper--active': ownership === option.value }]"
            >
              <klk-radio :group-value="option.value">
                <p class="cn-inquire_option-title" v-html="option.title"></p>
              </klk-radio>
            </div>
            <p v-show="ownership === option.value" v-html="replaceToComLink(option.desc)" class="cn-inquire_option-desc">
            </p>
          </div>
        </template>
      </klk-radio-group>
    </div>
    <div slot="footer">
      <klk-button :disabled="ownership === null" block @click="handleConfirm">
        {{ __t('167140') }}
      </klk-button>
    </div>
  </klk-modal>
</template>
<script>
import { Modal as klkModal, Button as klkButton  }  from "@klook/klook-ui";
import {
  RadioGroup as klkRadioGroup,
  Radio as klkRadio,
} from "@klook/klook-ui/es/radio/index.js";
import baseMixin from "@/mixin/base";

export default {
  name: 'CnInquire',
  mixins: [baseMixin],
  components: {
    klkModal,
    klkButton,
    klkRadioGroup,
    klkRadio
  },
  data() {
    return {
      visible: false,
      ownership: null,
    }
  },
  computed:{
    options(){
      return [
        {
          title: this.__t('167134'),
          desc: this.__t('167332'),
          value: 0,
          show: true
        },
        {
          title: this.__t('167136'),
          desc: this.__t('167333'),
          value: 1,
          show: true
        },
        {
          title: this.__t('167138'),
          desc: this.__t('167334'),
          value: 2,
          show: false
        }
      ]
    }
  },
  methods: {
    replaceToComLink(str){
      return str.replace('Klook.com','<a href="https://www.klook.com/en-US/" target="_blank">Klook.com</a>')
    },
    handleConfirm() {
      this.$emit('confirm', this.ownership)
      const spmMap = ["CN","Global","GlobalnCN"];
      const inhouse = this.inhouse
      inhouse && inhouse.track('custom', 'body', {
        spm: "LoginDataMigrationPopup_CN.Confirm",
        Choose: spmMap[this.ownership],
        DisplayStyle: 'migration_and_back_original'
      })
    },
    onChange(value){
      const spmMap = ["CN","Global","GlobalnCN"];
      const inhouse = this.inhouse
      inhouse && inhouse.track('custom', 'body', {
        spm: `LoginDataMigrationPopup_CN.${spmMap[value]}`,
        DisplayStyle: 'migration_and_back_original'
      })
    }
  },
  mounted(){
    this.$refs.cnInquireModal.overlayZIndex = 2000
  }
}
</script>

<style lang="scss">
@import "./cn-inquire.scss";
</style>
