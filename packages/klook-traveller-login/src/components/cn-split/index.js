import Vue from 'vue'
import CnInquire from './cn-inquire.vue'
import CnInquireV2 from './cn-inquire-v2.vue'
import GlobalInquire from './global-inquire.vue'

const CnInquireConstructor = Vue.extend(CnInquire)
const CnInquireV2Constructor = Vue.extend(CnInquireV2)
const GlobalInquireConstructor = Vue.extend(GlobalInquire)

export function showCnInquire() {
  return new Promise((resolve) => {
    // 设置组件的属性
    const cnInquire = new CnInquireConstructor({
      el: document.createElement('div')
    })
    document.body.appendChild(cnInquire.$el)
    setTimeout(()=>{
      cnInquire.visible = true
    },0)
    const inhouse = window.__in_house
    inhouse && inhouse.track('custom', 'body', {
      spm: "LoginDataMigrationPopup_CN"
    })
    // 设置组件的回调函数
    cnInquire.$on('confirm', function (option) {
      cnInquire.visible = false
      document.body.removeChild(cnInquire.$el)
      cnInquire.$destroy()
      resolve(option)
    })
  })
}

export function showCnInquireV2() {
  return new Promise((resolve) => {
    // 设置组件的属性
    const cnInquire = new CnInquireV2Constructor({
      el: document.createElement('div')
    })
    document.body.appendChild(cnInquire.$el)
    setTimeout(()=>{
      cnInquire.visible = true
    },0)
    const inhouse = window.__in_house
    inhouse && inhouse.track('custom', 'body', {
      spm: "LoginDataMigrationPopup_CN"
    })
    // 设置组件的回调函数
    cnInquire.$on('confirm', function (option) {
      cnInquire.visible = false
      document.body.removeChild(cnInquire.$el)
      cnInquire.$destroy()
      resolve(option)
    })
  })
}

export function showGlobalInquire(needSignup) {
  return new Promise((resolve) => {
    const globalInquire = new GlobalInquireConstructor({
      el: document.createElement('div'),
      propsData: { needSignup }
    }).$mount()
    document.body.appendChild(globalInquire.$el)
    setTimeout(()=>{
      globalInquire.visible = true
    })
    // 设置组件的回调函数
    globalInquire.$on('confirm', function (option) {
      globalInquire.visible = false
      document.body.removeChild(globalInquire.$el)
      globalInquire.$destroy()
      resolve(option)
    })
  })
}
