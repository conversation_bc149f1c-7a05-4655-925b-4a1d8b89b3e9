<template>
  <klk-modal
    ref="cnInquireModal"
    :overlay-closable="false"
    overlay-color="rgba(0, 0, 0, 1)"
    class="cn-inquire-modal"
    :title="__t('203484')"
    :open.sync="visible"
  >
    <div class="klk-modal-content">
      <div class="cn-inquire-v2-desc">
        <p v-html="replaceToComLink(__t('203485'))"></p>
      </div>
    </div>
    <div slot="footer">
      <klk-button block @click="handleConfirm">
        {{ __t('203486') }}
      </klk-button>
    </div>
  </klk-modal>
</template>
<script>
import { Modal as klkModal, Button as klkButton  }  from "@klook/klook-ui";
import {
  RadioGroup as klkRadioGroup,
  Radio as klkRadio,
} from "@klook/klook-ui/es/radio/index.js";
import baseMixin from "@/mixin/base";

export default {
  name: 'CnInquireV2',
  mixins: [baseMixin],
  components: {
    klkModal,
    klkButton,
    klkRadioGroup,
    klkRadio
  },
  data() {
    return {
      visible: false,
      ownership: null,
    }
  },
  methods: {
    replaceToComLink(str){
      return str.replace('Klook.com','<a href="https://www.klook.com/en-US/" target="_blank">Klook.com</a>')
    },
    handleConfirm() {
      window.location.href = "https://www.klook.com/en-US/"
      // this.$emit('confirm', this.ownership)
      const inhouse = this.inhouse
      inhouse && inhouse.track('custom', 'body', {
        spm: "LoginDataMigrationPopup_CN.Confirm",
        Choose: 'Global',
        DisplayStyle: 'back_original'
      })
    },
  },
  mounted(){
    this.$refs.cnInquireModal.overlayZIndex = 2000
  }
}
</script>

<style lang="scss">
@import "./cn-inquire.scss";
</style>
