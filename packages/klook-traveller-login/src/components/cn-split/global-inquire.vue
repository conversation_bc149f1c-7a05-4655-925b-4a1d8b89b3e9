<template>
  <klk-modal
    :overlay-closable="false"
    ref="GlobalInquire"
    class="global-inquire-modal"
    overlay-color="rgba(0, 0, 0, 1)"
    :title="__t('167124')"
    :open.sync="visible"
  >
    <div class="klk-modal-content">
      <p class="global-inquire_content" v-html="replaceToCnLink(__t('167125'))"></p>
      <p v-if="needSignup" class="global-inquire_content" v-html="replaceToCnLink(__t('167126'))"></p>
    </div>
    <div slot="footer">
      <klk-button  type="primary"  block @click="handleConfirm(0)">
        {{ __t('167128') }}
      </klk-button>
      <div v-if="needSignup" class="global-inquire_link-button" @click="handleConfirm(1)">
        {{ __t('167127') }}
      </div>
    </div>
  </klk-modal>
</template>
<script>
import baseMixin from "@/mixin/base";
import { Modal as klkModal, But<PERSON> as klkButton  }  from "@klook/klook-ui";
export default {
  name: 'GlobalInquire',
  components: {
    klkModal,
    klkButton
  },
  mixins: [baseMixin],
  data() {
    return {
      visible: false
    }
  },
  props:{
    needSignup:{
      type: Boolean,
      default: true,
    }
  },
  methods: {
    replaceToCnLink(str){
      return str.replace('Klook.cn','<a href="https://www.klook.cn/zh-CN/" target="_blank">Klook.cn</a>')
    },
    handleConfirm(ownership) {
      this.$emit('confirm', ownership)
    }
  },
  mounted(){
    this.$refs.GlobalInquire.overlayZIndex = 2000
  }
}
</script>

<style lang="scss">

.global-inquire_content {
  @include font-body-s-regular();

  margin-bottom: 12px;
  color: $color-text-primary;

  a {
    text-decoration: none;
    color: $color-text-link;
  }
}

.global-inquire_link-button {
  margin-top: 12px;
  text-align: center;
  text-decoration: underline;
  cursor: pointer;
}

</style>
