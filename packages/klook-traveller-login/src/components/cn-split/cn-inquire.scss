.cn-inquire_title {
  @include font-body-s-regular();
  margin-bottom: 16px;
  color: $color-text-secondary;
}

.cn-inquire_option {
  margin-bottom: 16px;
}
.cn-inquire_option-wrapper {
  border: 1px solid $color-border-dim;
  border-radius: $radius-xl;
  padding: 12px 16px;

  &--active {
    border-color: $color-brand-primary;
  }
}
.cn-inquire_option-title {
  @include font-body-m-regular();
  color: $color-text-primary;

  a {
    text-decoration: none;
    color: $color-text-link;
  }
}

.cn-inquire_option-desc {
  @include font-body-s-regular();
  margin-top: 4px;
  padding: 12px 16px;
  border: 1px solid $color-border-dim;
  border-radius: $radius-xl;
  background-color: $color-neutral-100;
  color: $color-text-secondary;

  a {
     text-decoration: none;
     color: $color-text-link;
  }
}

.cn-inquire-v2-desc {
  margin-bottom: 16px;

  a {
    text-decoration: none;
    color: $color-text-link;
  }
}