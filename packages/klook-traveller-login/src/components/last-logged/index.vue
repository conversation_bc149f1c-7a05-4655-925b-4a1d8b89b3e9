<template>
  <div class="klk-login-last-logged">
    <div class="klk-login-last-logged-text">{{ __t('172948') }}</div>

    <div class="klk-login-last-logged-info">
      <ThirdPartyLogo :type="lastLoginInfo.login_method" size="small" />
      <span class="klk-login-last-logged-name">{{ lastLoginInfo.login_id_mask || name[lastLoginInfo.login_method] || ''}}</span>
    </div>
  </div>
</template>

<script>
import {
  Form as klkForm,
  FormItem as klkFormItem,
} from "@klook/klook-ui/es/form/index.js";
import baseMixin from "@/mixin/base.ts";
import ThirdPartyLogo from "@/components/third-party-logo";

export default {
  name: "LastLogged",
  mixins: [baseMixin],
  props:{
    lastLoginInfo: Object
  },
  computed:{
    name(){
      return {
        apple:this.__t('13363'),
        google: this.__t('14251'),
        facebook :this.__t('13393'),
        kakao: this.__t('13242'),
        naver: this.__t('31000'),
        wechat: this.__t('5756')
      }
    }
  },
  inject: ["bizName", "purpose"],
  components: {
    klkForm,
    klkFormItem,
    ThirdPartyLogo
  },
};
</script>

<style lang="scss" scoped>
@import "../../style/home.scss";

.klk-login-last-logged {
  @include font-body-s-regular();
  color: $color-text-placeholder;
  background-color: $color-bg-2;
  border-radius: $radius-xl;
  padding: 16px;

  .klk-login-last-logged-text {
    text-align: center;
    margin-bottom: 4px;
  }

  .klk-login-last-logged-name {
    @include font-body-m-bold();
    color: $color-text-primary;
    margin-left: 4px;
  }

  .klk-login-last-logged-info {
    display: flex;
    justify-content: center;
    word-break: break-all;
  }
}
</style>
