<template>
  <div v-if="displayText" :class="['klk-login-last-logged', `klk-login-last-logged-${platform}`]">
    <div class="klk-login-last-logged-info">
      <ThirdPartyLogo :type="lastLoginInfo.login_method" size="small" :logoSize="16" />
      <div class="klk-login-last-logged-info-text">
        <span class="klk-login-last-logged-name">{{ displayText }}</span>
        <span>({{ __t('30455') }})</span>
      </div>
    </div>
  </div>
</template>

<script>
import {
  Form as klkForm,
  FormItem as klkFormItem,
} from "@klook/klook-ui/es/form/index.js";
import baseMixin from "@/mixin/base.ts";
import ThirdPartyLogo from "@/components/third-party-logo";

export default {
  name: "LastLogged",
  mixins: [baseMixin],
  props: {
    platform: String,
    lastLoginInfo: {
      type: Object,
      default: () => {}
    },
  },
  computed: {
    name() {
      return {
        apple: this.__t('13363'),
        google: this.__t('14251'),
        facebook: this.__t('13393'),
        kakao: this.__t('13242'),
        naver: this.__t('31000'),
        wechat: this.__t('5756')
      }
    },
    displayText() {
      return this.lastLoginInfo.display_text || this.lastLoginInfo.login_id_mask ||
          this.name[this.lastLoginInfo.login_method] ||
          ''
    }
  },
  data() {
    return {}
  },
  components: {
    klkForm,
    klkFormItem,
    ThirdPartyLogo
  },
  async mounted() {
    console.log('last-logged-render', this.lastLoginInfo)
  }
};
</script>

<style lang="scss" scoped>
@import "../../style/home.scss";

.klk-login-last-logged {
  color: $color-text-placeholder;
  font-size: 16px;

  .klk-login-last-logged-text {
    text-align: center;
    margin-bottom: 4px;
  }

  .klk-login-last-logged-name {
    @include font-body-m-bold();
    color: $color-text-primary;
    margin-left: 4px;
    font-size: inherit;
  }

  .klk-login-last-logged-info {
    display: flex;
    justify-content: center;
    align-items: center;
    word-break: break-all;
  }

  .klk-login-last-logged-info-text {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    font-size: 14px;

    span {
      min-width: 0;
      margin-left: 8px;
    }
  }

  &-mobile {
    font-size: 14px;

    .klk-login-last-logged-info .klk-login-last-logged-info-text {
      font-size: 12px;
    }
  }
}
</style>
