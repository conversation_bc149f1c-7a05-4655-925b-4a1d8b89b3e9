import Vue from 'vue'
import EdmPop from './edm-pop.vue'
const EdmPopConstructor = Vue.extend(EdmPop)

export function showEdmPop(props) {
  return new Promise((resolve) => {
    const edmPop = new EdmPopConstructor({
      el: document.createElement('div'),
      data:{
        visible: false,
        checkList: [],
      },
      propsData: props
    })

    document.body.appendChild(edmPop.$el)
    setTimeout(async ()=>{
      const show = await edmPop.initTerm()
      show ? edmPop.showModal() : resolve()
    })

    // 设置组件的回调函数
    edmPop.$on('confirm', function () {
      edmPop.visible = false
      document.body.removeChild(edmPop.$el)
      edmPop.$destroy()
      resolve()
    })
    edmPop.$on('close',function (){
      edmPop.visible = false
      document.body.removeChild(edmPop.$el)
      edmPop.$destroy()
      resolve()
    })
  })
}
