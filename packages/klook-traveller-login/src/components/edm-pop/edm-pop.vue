<template>
  <klk-modal
    ref="edmPopModal"
    :overlay-closable="false"
    id="edm-pop-modal"
    class="edm-pop-modal"
    :open.sync="visible"
    :closable="true"
    overlay-color="rgba(0, 0, 0, 1)"
    @close="handleClose"
    :data-spm-page="`LoginSignupEDMPopup?trg=manual&ext=${JSON.stringify({
      SdkVersion: sdkVersion,
      BizName: bizName,
      Purpose: purpose,
    })}`"
  >
    <div class="klk-modal-content">
      <div class="edm-pop-modal_main-banner-wrapper">
        <img class="edm-pop-modal_main-banner" src="https://res.klook.com/image/upload/v1720145351/y5rwavoah71saurkduys.png" alt="image">
      </div>
      <h3 class="edm-pop-modal_title">{{ __t('172954') }}</h3>
      <SpecialTerms ref="specialTerms" />
    </div>
    <div slot="footer">
      <klk-button
        block
        @click="handleConfirm"
        :data-spm-module="`Confirm?ext=${JSON.stringify({
          BizName: bizName,
          Purpose: purpose,
          OptionIn: checkList
        })}`"
        data-spm-virtual-item="__virtual"
        v-galileo-click-tracker="{ spm: 'Confirm', componentName: 'klook-traveller-login' }"
      >
        {{ __t('167140') }}
      </klk-button>
    </div>
  </klk-modal>
</template>
<script>
import { Modal as klkModal, Button as klkButton }  from "@klook/klook-ui";
import SpecialTerms from "@/components/special-terms.vue";
import baseMixin from "@/mixin/base";
import apis from "@/common/apis";

export default {
  name: 'EdmPop',
  mixins: [baseMixin],
  components: {
    klkModal,
    klkButton,
    SpecialTerms
  },
  props: ['bizName', 'purpose'],
  data() {
    return {
      sdkVersion: null,
      termList: [],
      checkList: '',
      visible: false
    }
  },
  methods: {
    async initTerm(){
      //初始化条款
      const res = await this.ajaxGet(apis.edmPop)
      if(res.success && res.result.terms &&  res.result.terms.length > 0){
        this.termList = res.result.terms
        return true
      }
      return false
    },
    showModal(){
      this.visible = true
      this.$nextTick(()=>{
        this.$refs.specialTerms.initCheckList(this.termList)
      })
    },
    handleClose(){
      this.$emit('close')
    },
    handleConfirm(){
      this.$refs.specialTerms.validator(async (valid, termIds) => {
        if (valid) {
            if(termIds){
              this.checkList = termIds
              await this.ajaxPostJSON(apis.updateTerms, {
                scene: "edm_popup",
                term_ids: this.checkList,
              })
            }
            this.$emit('confirm')
        }
      })
    }
  },
  mounted(){
    this.sdkVersion = window.__OAUTH_STATE__ && window.__OAUTH_STATE__.version
    this.$refs.edmPopModal.overlayZIndex = 2000

    setTimeout(() => {
      const inhouse = this.inhouse
      inhouse && inhouse.track('pageview', '#edm-pop-modal', {
        force: true
      })
    }, 100)
  }
}
</script>

<style lang="scss">
.edm-pop-modal_main-banner-wrapper {
  display: flex;
  justify-content: center;
}
.edm-pop-modal_main-banner {
  width: 160px;
  height: auto;
  margin: 0 auto;
}

.edm-pop-modal_title {
  @include font-heading-xs();

  margin-top: 16px;
  margin-bottom: 20px;
  color: $color-text-primary
}
</style>
