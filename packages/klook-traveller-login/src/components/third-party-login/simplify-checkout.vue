<template>
  <div :class="['third-party-list', `third-party-list--${platform}`, `third-party-list--${size}`]">
    <AsyncLoadComponent
      v-for="(comp, index) in loginWays"
      :key="index"
      :component="comp.component"
      :before-login="beforeLogin"
      :showLastUsed="false"
      :showName="platform === 'desktop'"
      size="small"
      @click="handleMethod"
      :data-spm-module="`Method_LIST?idx=${index}&len=${
        loginWays.length
      }&ext=${JSON.stringify(getSpmExtra(comp, index))}`"
      data-spm-virtual-item="__virtual"
      v-galileo-click-tracker="{ spm: 'Method_LIST', componentName: 'klook-traveller-login' }"
    ></AsyncLoadComponent>
  </div>
</template>

<script>
import baseMixin from "@/mixin/base.ts";
import { capitalizeFirstLetter } from "@/common/utils";
import mixin from "./mixin.ts";

export default {
  name: "SimplifyCheckoutThirdPartyLogin",
  mixins: [baseMixin, mixin],
  props: {
    platform: {
      type: 'desltop' | 'mobile',
      default: "desktop",
    },
    userTypes: {
      type: Array,
      default: () => [],
    },
    size: {
      type: String,
      default: "large",
    },
    beforeLogin: {
      type: Function,
    }
  },
  methods: {
    capitalizeFirstLetter,
    handleMethod(type) {
      this.$emit("click", type);
    },
    getSpmExtra(comp, index) {
      const LoginMethod = comp?.type?.login_method || "";
      let extraObj = {
        BizName: this.bizName,
        Purpose: this.purpose,
        LoginMethod: capitalizeFirstLetter(LoginMethod),
        listIndex: index,
        listLen: this.loginWays && this.loginWays.length,
      };

      if (this.activeType) {
        if (this.activeType === "otp") {
          extraObj.VerificationType = "OTP";
        } else {
          extraObj.VerificationType = "Password";
        }
      }

      return extraObj;
    },
  },
};
</script>

<style lang="scss"></style>
