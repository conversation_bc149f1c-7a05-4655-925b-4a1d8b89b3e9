<template>
  <svg :width="size" :height="size" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g clip-path="url(#clip0_149_49031)">
      <rect width="24" height="24" rx="12" fill="black"/>
      <path d="M12 -1C9.42888 -1 6.91546 -0.237431 4.77761 1.19103C2.63977 2.61949 0.973519 4.64985 -0.0104228 7.02529C-0.994365 9.40073 -1.25181 12.0144 -0.750203 14.5361C-0.248594 17.0579 0.989545 19.3742 2.80763 21.1922C4.62572 23.0103 6.9421 24.2487 9.46386 24.7503C11.9856 25.2519 14.5995 24.9944 16.9749 24.0104C19.3504 23.0265 21.3807 21.3602 22.8092 19.2224C24.2376 17.0846 25.0001 14.571 25.0001 11.9999C25.0001 8.55207 23.6304 5.24551 21.1925 2.80753C18.7545 0.36956 15.4479 -1 12 -1ZM19.0518 14.3156C17.8556 15.726 16.2121 16.6838 14.3953 17.0292C13.3017 17.272 12.1747 17.3256 11.0629 17.1882C10.9772 17.1754 10.8897 17.1803 10.8058 17.202C10.7219 17.2238 10.6432 17.2622 10.5745 17.3151C9.70613 17.9181 8.83295 18.5211 7.95494 19.1242C7.80247 19.2282 7.63859 19.3143 7.4665 19.381C7.21323 19.4823 7.10468 19.4098 7.13724 19.1457C7.16143 18.9282 7.2075 18.7138 7.27473 18.5055C7.48821 17.7819 7.71615 17.0581 7.92238 16.3851C7.30834 15.9829 6.71539 15.5494 6.14587 15.0863C4.85057 13.9429 4.10161 12.5138 4.17036 10.7445C4.20726 10.0071 4.39373 9.28505 4.71841 8.62201C5.04309 7.95896 5.4992 7.36877 6.05903 6.88749C7.32932 5.73051 8.92218 4.9884 10.6251 4.76006C12.6717 4.41145 14.776 4.726 16.6313 5.65753C17.9182 6.26712 18.9822 7.26401 19.6742 8.50859C20.1721 9.42594 20.3792 10.473 20.2681 11.5109C20.1569 12.5487 19.7327 13.5281 19.0518 14.3192V14.3156Z" fill="#FEE500"/>
      <path d="M16.0524 11.4717C15.9962 11.5863 15.956 11.708 15.933 11.8335C15.933 12.094 15.933 12.3546 15.933 12.6151C15.933 12.9407 15.7738 13.1363 15.4988 13.1652C15.2238 13.1942 15.0465 13.0458 15.0103 12.684C14.9742 12.3222 15.0103 12.0327 15.0103 11.7035C15.0103 11.016 15.0103 10.3285 15.0103 9.64106C15.0103 9.31543 15.1551 9.10918 15.4301 9.08385C15.705 9.05852 15.9149 9.22509 15.9366 9.57605C15.9547 9.87273 15.9366 10.1729 15.9366 10.4696L15.9981 10.5057L16.9533 9.5181C17.0392 9.42554 17.1298 9.33722 17.2247 9.2538C17.2642 9.21601 17.3109 9.18665 17.362 9.16724C17.4132 9.14782 17.4677 9.13871 17.5223 9.14074C17.577 9.14276 17.6307 9.15575 17.6802 9.1789C17.7298 9.20204 17.7742 9.2351 17.8108 9.27571C17.8544 9.31307 17.8893 9.35924 17.9133 9.41139C17.9373 9.46354 17.9497 9.52042 17.9497 9.57781C17.9497 9.63521 17.9373 9.69173 17.9133 9.74388C17.8893 9.79603 17.8544 9.84255 17.8108 9.87991C17.5177 10.1802 17.2174 10.4733 16.9171 10.77L16.7869 10.9109C17.1668 11.4102 17.5431 11.8988 17.9121 12.3981C17.9655 12.4613 18.0009 12.5374 18.0145 12.619C18.0281 12.7005 18.0194 12.7843 17.9894 12.8613C17.9594 12.9384 17.9092 13.0062 17.844 13.0571C17.7788 13.108 17.7011 13.1403 17.619 13.1507C17.5272 13.1644 17.4334 13.1501 17.3496 13.1101C17.2659 13.0701 17.1959 13.0063 17.1487 12.9264C16.7977 12.4524 16.454 11.9963 16.0524 11.4717Z" fill="#FEE500"/>
      <path d="M8.58083 12.7163C8.64957 12.4956 8.70746 12.2677 8.79068 12.0507C9.1127 11.204 9.44195 10.361 9.7712 9.51796C9.81167 9.38897 9.89192 9.27595 10.0005 9.19536C10.109 9.11477 10.2403 9.07066 10.3754 9.06922C10.5149 9.06313 10.6523 9.1035 10.7663 9.18406C10.8803 9.26461 10.9642 9.3806 11.005 9.51407C11.3379 10.3643 11.6707 11.2148 11.9964 12.0687C12.0685 12.2645 12.1265 12.4653 12.17 12.6693C12.1863 12.713 12.1933 12.7593 12.1907 12.8057C12.188 12.8522 12.1758 12.8978 12.1547 12.9393C12.1336 12.9808 12.1042 13.0172 12.0682 13.0467C12.0322 13.0762 11.9905 13.0981 11.9457 13.1107C11.6816 13.2084 11.4862 13.1793 11.3668 12.9803C11.2886 12.8415 11.2255 12.6948 11.1787 12.5425C11.1688 12.4809 11.1353 12.4256 11.0853 12.3884C11.0353 12.3513 10.9727 12.3352 10.9109 12.3436C10.5491 12.3436 10.2054 12.3436 9.85441 12.3436C9.79661 12.3361 9.73815 12.3513 9.69105 12.3856C9.64394 12.4199 9.61177 12.4707 9.60114 12.528C9.552 12.6869 9.48525 12.8401 9.40215 12.9842C9.27551 13.1904 9.07651 13.1867 8.86666 13.1216C8.77637 13.1032 8.69652 13.051 8.6434 12.9757C8.59028 12.9004 8.56791 12.8076 8.58083 12.7163ZM10.4189 10.2921H10.361L9.9304 11.515H10.8494L10.4189 10.2921Z" fill="#FEE500"/>
      <path d="M8.08518 9.97033C8.08518 10.4696 8.08518 10.9472 8.08518 11.4176C8.08518 11.7794 8.10689 12.1412 8.10689 12.503C8.10195 12.637 8.07882 12.7696 8.03815 12.8974C8.00805 12.9822 7.94931 13.0539 7.87205 13.1002C7.79478 13.1464 7.70383 13.1643 7.61482 13.1507C7.52983 13.161 7.44391 13.1408 7.37236 13.0938C7.30082 13.0468 7.24827 12.976 7.22406 12.8938C7.1768 12.734 7.15243 12.5682 7.1517 12.4016C7.1517 11.754 7.18064 11.1063 7.18788 10.4586C7.18788 10.3031 7.18788 10.1437 7.15893 9.96644C7.01421 9.96644 6.89119 9.96644 6.76817 9.96644C6.61656 9.96592 6.46527 9.95283 6.3159 9.92687C6.26368 9.91952 6.21351 9.90166 6.16847 9.87422C6.12343 9.84679 6.08447 9.81019 6.05398 9.76716C6.02349 9.72414 6.00211 9.6753 5.99115 9.62371C5.9802 9.57213 5.9799 9.51889 5.99027 9.46718C5.99766 9.35649 6.04714 9.25281 6.12855 9.17745C6.20996 9.10209 6.31713 9.06074 6.42806 9.06191C7.1324 9.06191 7.83794 9.05483 8.54469 9.04036C8.6664 9.04018 8.78775 9.05365 8.9065 9.08028C9.00717 9.09882 9.09817 9.15179 9.16367 9.23045C9.22917 9.30911 9.26503 9.40828 9.26503 9.51064C9.26503 9.613 9.22917 9.71218 9.16367 9.79084C9.09817 9.8695 9.00717 9.92282 8.9065 9.94136C8.74905 9.95761 8.5908 9.9647 8.43253 9.96291L8.08518 9.97033Z" fill="#FEE500"/>
      <path d="M13.3821 12.2351C13.5847 12.2351 13.7439 12.2351 13.9321 12.2351C14.1158 12.2289 14.2997 12.2375 14.482 12.2606C14.5313 12.2641 14.5793 12.2774 14.6232 12.3001C14.667 12.3229 14.7057 12.3544 14.7369 12.3927C14.7681 12.431 14.7911 12.4756 14.8045 12.5231C14.8179 12.5706 14.8215 12.6204 14.8149 12.6694C14.8153 12.7599 14.7817 12.8472 14.7207 12.9142C14.6597 12.9812 14.5759 13.023 14.4857 13.0312C14.278 13.0571 14.069 13.0701 13.8597 13.0708C13.5413 13.0708 13.2229 13.0708 12.9045 13.0708C12.8479 13.0802 12.7898 13.0772 12.7344 13.0619C12.679 13.0466 12.6277 13.0196 12.5839 12.9824C12.5401 12.9452 12.505 12.8989 12.481 12.8467C12.4571 12.7945 12.4448 12.7374 12.445 12.68C12.4204 12.5317 12.4083 12.3819 12.4088 12.2316C12.4088 11.3343 12.4305 10.4224 12.4378 9.53953C12.4378 9.3333 12.4993 9.14508 12.7019 9.10528C12.8742 9.06379 13.0557 9.0872 13.2121 9.17065C13.267 9.22983 13.3097 9.29912 13.3376 9.37488C13.3656 9.45064 13.3782 9.53128 13.3749 9.61197C13.393 10.3754 13.3749 11.1389 13.3749 11.9023L13.3821 12.2351Z" fill="#FEE500"/>
    </g>
    <defs>
      <clipPath id="clip0_149_49031">
        <rect width="24" height="24" rx="12" fill="white"/>
      </clipPath>
    </defs>
  </svg>
</template>

<script>
import svgMixins from '@/mixin/svg-mixins';
export default {
  mixins: [svgMixins]
}
</script>