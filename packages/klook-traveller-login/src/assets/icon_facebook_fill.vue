<template>
  <svg :width="size" :height="size" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g clip-path="url(#clip0_149_49029)">
      <path d="M24.4857 12.08C24.4861 9.62798 23.7884 7.22702 22.4752 5.16189C21.162 3.09675 19.2884 1.45393 17.0762 0.427671C14.8641 -0.59859 12.4062 -0.96516 9.9935 -0.628542C7.58084 -0.291924 5.31454 0.733531 3.46305 2.32675C1.61156 3.91998 0.252444 6.01418 -0.453329 8.361C-1.1591 10.7078 -1.18195 13.209 -0.519187 15.5685C0.143581 17.928 1.46421 20.047 3.28627 21.6742C5.10833 23.3014 7.35549 24.3688 9.7616 24.75V15.7818H6.50461V12.0761H9.7616V9.24942C9.7616 6.02087 11.6502 4.25246 14.5616 4.25246C15.5174 4.266 16.4706 4.35597 17.4123 4.5215V7.65686H15.8195C14.2337 7.65686 13.7313 8.6539 13.7313 9.6691V12.0761H17.2698L16.7032 15.7818H13.742V24.75C16.7403 24.274 19.4714 22.7362 21.4436 20.4134C23.4159 18.0906 24.4997 15.1357 24.5 12.08" fill="#1877F2"/>
    </g>
    <defs>
      <clipPath id="clip0_149_49029">
        <rect width="24" height="24" rx="12" fill="white"/>
      </clipPath>
    </defs>
  </svg>
</template>

<script>
import svgMixins from '@/mixin/svg-mixins';
export default {
  mixins: [svgMixins]
}
</script>
