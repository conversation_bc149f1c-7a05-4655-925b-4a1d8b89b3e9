<template>
  <svg :width="size" :height="size" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect width="24" height="24" rx="12" fill="#FF5B00"/>
    <path fill-rule="evenodd" clip-rule="evenodd" d="M8.875 6C6.66586 6 4.875 7.79086 4.875 10V14C4.875 16.2091 6.66586 18 8.875 18H15.125C17.3341 18 19.125 16.2091 19.125 14V10C19.125 7.79086 17.3341 6 15.125 6H8.875ZM7.15917 8.56117C6.91681 8.33519 6.53714 8.34848 6.31117 8.59085C6.08519 8.83322 6.09848 9.21288 6.34085 9.43886L10.3649 13.1907C11.3136 14.0752 12.7869 14.0683 13.7273 13.1748L17.6633 9.43498C17.9035 9.20672 17.9132 8.82695 17.685 8.58672C17.4567 8.3465 17.0769 8.33679 16.8367 8.56505L12.9007 12.3049C12.4204 12.7613 11.6678 12.7648 11.1832 12.313L7.15917 8.56117Z" fill="white"/>
  </svg>
</template>

<script>
import svgMixins from '@/mixin/svg-mixins';
export default {
  mixins: [svgMixins]
}
</script>