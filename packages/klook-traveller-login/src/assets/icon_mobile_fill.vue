<template>
  <svg :width="size" :height="size" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect width="24" height="24" rx="12" fill="#F09B0A"/>
    <path fill-rule="evenodd" clip-rule="evenodd" d="M7.94055 3.8125C6.65847 3.8125 5.61914 4.85183 5.61914 6.13391V17.741C5.61914 19.023 6.65847 20.0624 7.94055 20.0624H15.6786C16.9607 20.0624 18 19.023 18 17.741V6.13391C18 4.85183 16.9607 3.8125 15.6786 3.8125H7.94055ZM7.16606 6.13391C7.16606 5.70655 7.5125 5.36011 7.93986 5.36011H15.6779C16.1053 5.36011 16.4517 5.70655 16.4517 6.13391V13.8719C16.4517 14.2993 16.1053 14.6458 15.6779 14.6458H7.93986C7.5125 14.6458 7.16606 14.2993 7.16606 13.8719V6.13391ZM12.9702 16.967C12.9702 17.608 12.4505 18.1277 11.8095 18.1277C11.1685 18.1277 10.6488 17.608 10.6488 16.967C10.6488 16.326 11.1685 15.8063 11.8095 15.8063C12.4505 15.8063 12.9702 16.326 12.9702 16.967Z" fill="white"/>
  </svg>
</template>

<script>
import svgMixins from '@/mixin/svg-mixins';
export default {
  mixins: [svgMixins]
}
</script>
