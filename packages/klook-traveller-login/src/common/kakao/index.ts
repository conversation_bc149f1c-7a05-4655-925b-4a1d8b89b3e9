import { loadScript, awaitWrap, add<PERSON>ara<PERSON>, jumpToThirdParty } from "@/common/utils";
import { kakao } from "@/config";
import thirdPartyLogin from "../third-party-login";
import apis from "@/common/apis";
import {AccountExistData, handleMigrationUserFlowFunc, PromiseFun} from '../../../types/user';

// https://developers.kakao.com/docs/latest/en/kakaologin/rest-api#request-token-response
interface ISignIn {
  token_type: string;
  access_token: string;
  expires_in: number;
  refresh_token: string;
  refresh_token_expires_in: number;
  scope?: string;
  id_token?: string;
}

export default class KakaoBase extends thirdPartyLogin {
  newWindow: Window | undefined | null;
  initPromise: null | Promise<any> = null
  type = "Kakao";

  constructor(handleMigrationUserFlow?: handleMigrationUserFlowFunc, showCommonErrorPop?: PromiseFun) {
    super(handleMigrationUserFlow,showCommonErrorPop);
    this.initPromise = this.init()
  }

  init() {
    return new Promise((resolve) => {
      loadScript(
        "https://cdn.klook.com/s/dist_web/desktop/js/kakao_min.js",
        () => {
          window.Kakao.init(kakao.clientId);
          resolve("");
        }
      );
    });
  }

  async verify(accountExistData: AccountExistData) {
    window.Kakao.Auth.login({
      success: (authObj: ISignIn) => {
        this.login(
          apis.verifyKakao,
          {
            user_id_token: accountExistData.user_id_token,
            access_token: authObj.access_token,
          },
          true,
          accountExistData.need_set_password
        );
      },
      fail: (e: any) => {
        this.dispatchFail(e)
      }
    });
  }

  getAuthObj(): Promise<Pick<ISignIn, 'access_token'>> {
    const redirectURI = location.origin + "/auth/callback/kakao";

    return new Promise((resolve, reject) => {
      this.newWindow = jumpToThirdParty(
        redirectURI,
        (evt) => {
          if (evt.origin === location.origin && evt.data.access_token) {
            resolve(evt.data);
          } else {
            console.log(evt.origin, location.origin);
          }
        },
        (err: any) => {
          reject(err);
        }
      );
    });
  }

  async signin() {
    try {
      this.onReady();
      const authObj = await this.getAuthObj();
      if (authObj.access_token) {
        await this.login(apis.loginByKakao, {
          auth_token: authObj.access_token,
          ...addParams()
        });
      } else {
        this.handleError(this.translate('111840'), 2000)
      }
    } catch (e: any) {
      this.dispatchFail(e)
    }
  }

  async initClientAsync(): Promise<{ data: any, channelName: string }> {
    return new Promise((resolve, reject) => {
      window.Kakao.Auth.login({
        success: async (authObj: ISignIn) => {
          const data = {
            access_token: authObj.access_token
          };
          resolve({ data, channelName: this.type })
        },
        fail: (e: any) => {
          reject(new Error(e));
        }
      });

    });
  }

  // 仅用于获取登录的token
  async getAccessToken() {
    if(!this.initPromise){
      this.initPromise = this.init()
    }
    await this.initPromise
    return this.initClientAsync();
  }
}
