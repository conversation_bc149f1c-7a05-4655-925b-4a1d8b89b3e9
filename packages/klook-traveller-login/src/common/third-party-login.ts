import { awaitWrap } from "@/common/utils";
import { IRes, AccountExistData, ThirdPartyLoginParam, handleMigrationUserFlowFunc, PromiseFun } from "../../types/user";
// @ts-ignore
import { translate } from "../common/utils";
import { sendGeneralLog } from "./logger";

type StatusEnum = "Success" | "Failure" | "Cancel"

export default class thirdPartyLogin {
  __axios: any;
  type = "";
  handleMigrationUserFlow: any = null
  showCommonErrorPop: any = null
  translate: any = translate
  constructor(handleMigrationUserFlow?: handleMigrationUserFlowFunc, showCommonErrorPop?: PromiseFun) {
    if (!window.$axios) {
      throw Error("window.$axios is required");
    }
    if (handleMigrationUserFlow) {
      this.handleMigrationUserFlow = handleMigrationUserFlow
    }
    if(showCommonErrorPop){
      this.showCommonErrorPop = showCommonErrorPop
    }
    this.__axios = window.$axios;
  }

  onReady() {
    sendGeneralLog('I', {
      action: 'third_party_ready',
    }, {
      type: this.type,
    })
  }

  /**
   * 第三方登录接口
   * @param url 第三方登录接口地址
   * @param data
   * @param isVerify 是否第三方验证
   * @param needSetPassword 是否需要设置密码
   */
  async login(
    url: string,
    data: ThirdPartyLoginParam,
    isVerify = false,
    needSetPassword = false
  ) {
    const [err, resp] = await awaitWrap<any>(this.ajaxPost(url, data, true));
    if (err || !resp?.success) {
      //处理cn拆站流程
      if(this.handleMigrationUserFlow && resp){
        const flag = await this.handleMigrationUserFlow(resp)
        if(flag) return;
      }

      if(resp && ["3099","3100"].includes(resp.error?.code)){
        if(this.showCommonErrorPop){
          this.showCommonErrorPop(resp.error)
        }
        return;
      }

      this.handleError(
        resp?.error.message || this.translate('111839'),
        resp?.error.code
      );

      return;
    }

    this.sendAuthInhouse('Success');
    window.OAuthEvents.$emit("success",{
      action: resp.result.type,
      userInfo: resp.result,
      channelName: this.type,
    })
  }

  /**
   *
   * @param params
   * @returns Promise<accountExistResp.result>
   */
  handleError(errorMessage: string, code?: string | number) {
    window.OAuthEvents.$emit("thirdPartyLogin/fail", errorMessage, code);
    this.dispatchFail(errorMessage, code);
  }

  ajaxGet(url: string, params: Record<string, any>): Promise<IRes<any>> {
    if (!this.__axios && !this.__axios.$get) {
      return Promise.reject("window.$axios.$get is required!!!");
    }
    return this.__axios.$get(url, { params });
  }

  ajaxPost(url: string, data: Record<string, any>, CSRF = false): Promise<IRes<any>> {
    if (!this.__axios && !this.__axios.$post) {
      return Promise.reject("window.$axios.$post is required!!!");
    }
    return this.__axios.$post(url, data, { CSRF });
  }

  dispatchFail(error: string | Object, code?: string | number) {
    console.log('error', error)
    let errorMessage = error;
    if (Object.prototype.toString.call(error) === "[object Object]") {
      // @ts-ignore
      errorMessage = JSON.stringify(error);
    }

    window.OAuthEvents.$emit("fail", {
      channelName: this.type,
      code: code || "",
      message: errorMessage || "",
    });

    // sentry上报
    try {
      this.reportSentryEvent(
        '[verify] - third party verify fail | business-fail',
        {
          channel_name: this.type,
          module: 'third party verify',
          module_type: 'verify',
          action: 'ui',
          code: code + '',
          // @ts-ignore
          message: (errorMessage || "unknown").slice(0, 200),
        },
        'internal_error'
      )
    } catch {}

    // logquery上报
    sendGeneralLog('E', {
      action: 'third_party_fail_log',
      errMsg: errorMessage,
    }, {
      type: this.type,
    })

    // inhouse上报
    let verifyStatus = this.getVerifyStatus(error);
    this.sendAuthInhouse(verifyStatus, errorMessage as string);
  }
  
  get inhouse() {
    let defaultInhouse = {
      track: (...args: any) => {
        console.warn('inhouse.track 不存在', args);
      }
    };

    // @ts-ignore
    return window.tracker.inhouse || window.__in_house || window.$tetris.tracker.inhouse || defaultInhouse;
  }

  sendAuthInhouse(status: StatusEnum, message?: string) {
    const { bizName = '', purpose = '', sourceSPM = '' } = window.__OAUTH_STATE__.options;
    this.inhouse?.track("custom", "body", {
      spm: "ThirdPartyStatusRequestAuthorization",
      ChannelName: this.type,
      Method: "SDK",
      BizName: bizName,
      Purpose: purpose,
      SourceSPM: sourceSPM,
      UserType: (this.type || '').toLowerCase(),
      ValidateType: "third_party",
      Status: status,
      ErrorMessage: message || '',
    })
  }

  reportSentryEvent(...args: any[]) {
    const subSentry = window.__OAUTH_STATE__.subSentry;
    if (subSentry && subSentry.reportEvent) {
      subSentry.reportEvent(...args)
    } else {
      console.warn('no sentry instance found')
    }
  }

  getVerifyStatus(error: any): StatusEnum {
    // 微信 facebook naver 没有错误回调
    let result: StatusEnum = "Cancel";
    switch (this.type) {
      case "Google":
        if (error?.type !== 'popup_closed') {
          result = "Failure";
        }
        break;
      case "Apple":
        if (error?.error !== 'popup_closed_by_user') {
          result = "Failure";
        }
        break;
      default:
    }
    return result
  }
}
