import { jumpToThirdParty, awaitWrap, addParams } from "@/common/utils";
import { naver } from "@/config";
import thirdPartyLogin from "../third-party-login";
import apis from "@/common/apis";
import {AccountExistData, handleMigrationUserFlowFunc, PromiseFun} from "../../../types/user";

interface ISignIn {
  access_token: string;
}

/**
 * Naver开发文档 https://developers.naver.com/docs/login/web/web.md
 */
export default class NaverBase extends thirdPartyLogin {
  newWindow: Window | undefined | null;
  type = "Naver";

  constructor(handleMigrationUserFlow?: handleMigrationUserFlowFunc, showCommonErrorPop?: PromiseFun) {
    super(handleMigrationUserFlow,showCommonErrorPop);
  }

  getAuthObj(): Promise<ISignIn> {
    const clientId = naver.clientId;
    const redirectURI = location.origin + "/auth/callback/naver";
    const state = "login";
    const url =
      "https://nid.naver.com/oauth2.0/authorize?response_type=token&client_id=" +
      clientId +
      "&redirect_uri=" +
      redirectURI +
      "&state=" +
      state;

    return new Promise((resolve, reject) => {
      this.newWindow = jumpToThirdParty(
        url,
        (evt) => {
          if (evt.origin === location.origin && evt.data.access_token) {
            resolve(evt.data);
          } else {
            console.log(evt.origin, location.origin);
          }
        },
        (err: any) => {
          reject(err);
        }
      );
    });
  }
  async verify(accountExistData: AccountExistData) {
    try {
      const authObj = await this.getAuthObj();
      this.login(
        apis.verifyNaver,
        {
          user_id_token: accountExistData.user_id_token,
          access_token: authObj.access_token,
        },
        true,
        accountExistData.need_set_password
      );
    } catch (e: any) {
      this.dispatchFail(e);
    }
  }

  async signin() {
    try {
      this.onReady();
      const authObj = await this.getAuthObj();
      if(authObj.access_token){
        await this.login(apis.loginByNaver, {
          access_token: authObj.access_token,
          ...addParams()
        });
      }else {
        this.handleError(this.translate('111840'), 2000)
      }

    } catch (e: any) {
      this.dispatchFail(e);
    }
  }

  async getAccessToken() {
    const authObj = await this.getAuthObj();
    const data = {
      access_token: authObj.access_token
    };
    return { data, channelName: this.type }
}
}
