import { loadScript, awaitWrap, addParams } from "@/common/utils";
import { facebook } from "@/config";
import thirdPartyLogin from "../third-party-login";
import apis from "@/common/apis";
import {AccountExistData, handleMigrationUserFlowFunc, PromiseFun} from "../../../types/user";

interface ISignIn {
  authResponse: {
    accessToken: string;
    userID: string;
    expiresIn: number;
    signedRequest: string;
    graphDomain: string;
    data_access_expiration_time: number;
  };
  status: string;
}

/**
 * Facebook开发文档 https://developers.facebook.com/docs/javascript/quickstart
 */
export default class FacebookBase extends thirdPartyLogin {
  initPromise: null | Promise<any> = null
  type = "Facebook";

  constructor(handleMigrationUserFlow?: handleMigrationUserFlowFunc, showCommonErrorPop?: PromiseFun) {
    super(handleMigrationUserFlow, showCommonErrorPop);
    this.initPromise = this.init();
  }

  init() {
    return new Promise((resolve) => {

      window.fbAsyncInit = () => {
        window.FB.init({
          appId: facebook.appId,
          cookie: true,
          xfbml: true,
          version: "v11.0",
        });
        resolve("");
      };

      loadScript("https://connect.facebook.net/en_US/sdk.js", () => {
        resolve("");
      });
    });
  }

  async getAuthObj(): Promise<ISignIn> {
    return new Promise((resolve) => {
      window.FB.login(
        (response: any) => {
          resolve(response);
        },
        {
          scope: "email",
        }
      );
    });
  }

  async verify(accountExistData: AccountExistData) {
    if(!this.initPromise){
      this.initPromise = this.init()
    }
    await this.initPromise
    try {
      const authObj = await this.getAuthObj();
      this.login(
        apis.verifyFacebook,
        {
          user_id_token: accountExistData.user_id_token,
          access_token: authObj.authResponse.accessToken,
        },
        true,
        accountExistData.need_set_password
      );
    } catch (e: any) {
      this.dispatchFail(e);
    }
  }

  async signin() {
    if(!this.initPromise){
      this.initPromise = this.init()
    }
    await this.initPromise;
    this.onReady();
    const authObj = await this.getAuthObj();

    if (authObj.status === "connected") {
      try {
        if(authObj && authObj.authResponse.accessToken){
          await this.login(apis.loginByFacebook, {
            access_token: authObj.authResponse.accessToken,
            ...addParams()
          });
        }else {
          this.handleError(this.translate('111840'), 2000)
        }

      } catch (e: any) {
        this.dispatchFail(e);
      }
    } else {
      this.dispatchFail(authObj);
    }
  }

  // 仅用于获取登录的token
  async getAccessToken() {
    if(!this.initPromise){
      this.initPromise = this.init()
    }
    await this.initPromise
    const authObj = await this.getAuthObj();

    if (authObj?.status === "connected") {
      const data = {
        access_token: authObj?.authResponse?.accessToken
      };
      return { data, channelName: this.type }
    } else {
      throw new Error("User cancelled login or did not fully authorize.");
    }

  }
}
