import Logquery, { LogLevel } from '@klook/logquery';

/**
 * 
 * @param level 日志等级 I|E
 * @param message 上报的日志对象
 * @param extra 附加信息
 */
export const sendGeneralLog = (level: LogLevel, message: Record<string, any>, extra: Record<string, any> = {}) => {
  try {
    const { platform = 'desktop' } = window?.__OAUTH_STATE__?.options;
    const { keplerId = '', platform: backupPlatform } = window.__KLOOK__?.state?.klook || {}

    const logquery = new Logquery({
      url: process.env.LOGQUERY_URL || 'https://log.klook.com/v2/frontlogsrv/log/web',
      headers: { 'X-Platform': platform || backupPlatform || 'desktop' },
    })

    const logMessage = {
      ...message,
      version: window.__OAUTH_STATE__?.version,
      sessionId: window.__OAUTH_STATE__?.initUuid,
      extra: {
        ...window?.__OAUTH_STATE__?.options,
        ...extra,
        keplerId,
        url: window?.location?.href,
        userAgent: window?.navigator?.userAgent,
      }
    }
    logquery.general({
      timestamp: Date.now(),
      isMasked: true,
      level: level,
      message: {
        subtype: `login_sdk:internal`,
        message: JSON.stringify(logMessage),
      }
    })
  } catch (e) {
    console.log(e)
  }
}
