import { loadScript, awaitWrap, addParams } from "@/common/utils";
import { apple, cnApple } from "@/config";
import thirdPartyLogin from "../third-party-login";
import apis from "@/common/apis";
import { AccountExistData, handleMigrationUserFlowFunc, PromiseFun } from '../../../types/user';

interface ISignIn {
  authorization: {
    code: string;
    id_token: string;
    state: string;
  };
  user: {
    email: string;
    name: {
      firstName: string;
      lastName: string;
    };
  };
}

/**
 * Apple开发文档 https://developer.apple.com/documentation/sign_in_with_apple/sign_in_with_apple_js/configuring_your_webpage_for_sign_in_with_apple
 */
export default class AppleAuth extends thirdPartyLogin {
  type = "Apple";
  initPromise: null | Promise<any> = null

  constructor(handleMigrationUserFlow?: handleMigrationUserFlowFunc, showCommonErrorPop?: PromiseFun) {
    super(handleMigrationUserFlow,showCommonErrorPop);
    this.initPromise = this.init();
  }

  init() {
    return new Promise((resolve) => {
      loadScript(
        "https://appleid.cdn-apple.com/appleauth/static/jsapi/appleid/1/en_US/appleid.auth.js",
        () => {
          const host = location.protocol + "//" + location.host;
          const appApi = "/v3/usrcsrv/user/apple/auth";
          const redirectURI = host + appApi;
          const isCN = window?.__OAUTH_STATE__?.isCN || false;
          window.AppleID.auth.init({
            clientId: isCN ? cnApple.clientId : apple.clientId,
            scope: "name email",
            response_type: "code",
            redirectURI,
            usePopup: true,
          });
          resolve("");
        }
      );
    });
  }

  async getAuthObj(): Promise<ISignIn> {
    const data = await window.AppleID.auth.signIn();
    return data;
  }

  async verify(accountExistData: AccountExistData) {
    if(!this.initPromise){
      this.initPromise = this.init()
    }
    await this.initPromise
    try {
      const authObj = await this.getAuthObj();
      if(authObj?.authorization?.code) {
        this.login(
          apis.verifyApple,
          {
            user_id_token: accountExistData.user_id_token,
            access_token: authObj.authorization.code,
          },
          true,
          accountExistData.need_set_password
        );
      }else {
        this.handleError(this.translate('111840'), 2000)
      }
    } catch (e: any) {
      this.dispatchFail(e)
    }
  }

  async signin() {
    if(!this.initPromise){
      this.initPromise = this.init()
    }
    await this.initPromise
    this.onReady();
    try {
      const authObj = await this.getAuthObj();
      if(authObj?.authorization?.code) {
        await this.login(apis.loginByApple, {
          access_token: authObj.authorization.code,
          ...addParams()
        });
      }
    } catch (e: any) {
      this.dispatchFail(e)
    }
  }

  // 仅用于获取登录的token
  async getAccessToken() {
    if(!this.initPromise){
      this.initPromise = this.init()
    }
    await this.initPromise
    const { authorization = {} }: any = await this.getAuthObj();
    const data = {
      access_token: authorization.code
    };
    return { data, channelName: this.type }
  }
}
