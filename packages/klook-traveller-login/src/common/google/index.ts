import { loadScript, awaitWrap, addParams } from "@/common/utils";
import { google } from "@/config";
import thirdPartyLogin from "../third-party-login";
import apis from "@/common/apis";
import { AccountExistData, handleMigrationUserFlowFunc, PromiseFun } from '../../../types/user';
import { sendGeneralLog } from "../logger";

interface ISignIn {
  authuser: string;
  code: string;
  hd: string;
  prompt: string;
  scope: string;
}

/**
 * Google开发文档 https://developers.google.com/identity/sign-in/web/reference#gapiauth2initparams
 */
export default class GoogleBase extends thirdPartyLogin {
  GoogleClient: any;
  signSuccessCb?: Function;
  signFailCb?: Function;
  initPromise: null | Promise<any> = null
  type = "Google";

  constructor(handleMigrationUserFlow?: handleMigrationUserFlowFunc, showCommonErrorPop?: PromiseFun) {
    super(handleMigrationUserFlow,showCommonErrorPop);
    this.initPromise = this.init();
  }

  init() {
    return new Promise((resolve) => {

      if (!window.google || !window.google.accounts) {
        loadScript("https://accounts.google.com/gsi/client", () => {
          this.initClient();
          resolve("");
        })
      } else {
        // 初始化授权回调
        this.initClient();
        resolve("");
      }
    });
  }

  initClient(options: { success?: Function, fail?: Function } = {}) {
    this.GoogleClient = window.google.accounts.oauth2.initCodeClient({
      client_id: google.clientId,
      scope: 'openid email',
      ux_mode: 'popup',
      callback: options.success || this.signSuccessCb,
      error_callback: options.fail || this.signFailCb
    });
  }

  async verify(accountExistData: AccountExistData) {
    if(!this.initPromise){
      this.initPromise = this.init()
    }
    await this.initPromise
    this.onReady();
    try {
      this.initClient({
        success: (authObj: ISignIn) => {
          this.login(
            apis.verifyGoogle,
            {
              user_id_token: accountExistData.user_id_token,
              google_code: authObj.code,
            },
            true,
            accountExistData.need_set_password
          );
        },
        fail: (err: any) => {
          this.dispatchFail(err?.message || err)
        }
      })
      this.GoogleClient.requestCode();
    } catch (e: any) {
      this.dispatchFail(e)
    }
  }

  async signin() {
    if(!this.initPromise){
      this.initPromise = this.init()
    }
    await this.initPromise
    this.onReady();
    try {
      this.initClient({
        success: async (authObj: ISignIn) => {
          if(authObj.code){
            await this.login(apis.loginByGoogle, {
              access_token: authObj.code,
              ...addParams()
            });
          }else {
            this.handleError(this.translate('111840'), 2000)
          }
        },
        fail: (err: any) => {
          this.dispatchFail({
            type: err?.type,
            message: err?.message
          })
        }
      })

      this.GoogleClient.requestCode();
    } catch (e: any) {
      this.dispatchFail(e)
    }
  }

  async initClientAsync(): Promise<{ data: any, channelName: string }> {
    return new Promise((resolve, reject) => {
      this.initClient({
        success: async (authObj: ISignIn) => {
          const data = {
            google_code: authObj.code,
            access_token: authObj.code
          };
          resolve({ data, channelName: this.type });
        },
        fail: (err: any) => {
          reject(new Error(err?.message || err));
        }
      });

      this.GoogleClient.requestCode();
    });
  }

  // 仅用于获取登录的token
  async getAccessToken() {
    if(!this.initPromise){
      this.initPromise = this.init()
    }
    await this.initPromise
    return this.initClientAsync();
  }
}
