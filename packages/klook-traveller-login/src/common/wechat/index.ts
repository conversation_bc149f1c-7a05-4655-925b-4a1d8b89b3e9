import {jumpToThirdParty, awaitWrap, addParams} from "@/common/utils";
import thirdPartyLogin from "../third-party-login";
import apis from "@/common/apis";
import {AccountExistData, handleMigrationUserFlowFunc, PromiseFun} from "../../../types/user";

interface ISignIn {
  access_token: string;
}
/**
 * 微信登录，https://developers.weixin.qq.com/doc/oplatform/Website_App/WeChat_Login/Wechat_Login.html
 */
export default class WechatBase extends thirdPartyLogin {
  newWindow: Window | undefined | null;
  type = "Wechat";

  constructor(handleMigrationUserFlow?: handleMigrationUserFlowFunc, showCommonErrorPop?: PromiseFun) {
    super(handleMigrationUserFlow,showCommonErrorPop);
  }

  getAuthObj(type = 'login'): Promise<ISignIn> {
    const redirectURI = location.origin + "/auth/callback/wechat";
    let url = `${location.origin}/v1/userserv/public/user/login/wechat/qrcode?refer_url=${redirectURI}`;

    // 暴露 getAccessToken 使用的方法传递了bind，使用绑定接口
    if(type === "bind") {
      url = `${location.origin}/v1/userserv/public/user/bind/wechat/qrcode?refer_url=${redirectURI}`;
    }

    // cn 站需区分appid，通过 market 字段来标识
    const isCN = window?.__OAUTH_STATE__?.isCN || false;
    const market = window?.__OAUTH_STATE__?.market || '';

    if(isCN) {
      url = `${url}&market=${market}`
    }

    return new Promise((resolve, reject) => {
      this.newWindow = jumpToThirdParty(
        url,
        (evt) => {
          if (evt.origin === location.origin && evt.data.access_token) {
            resolve(evt.data);
          } else {
            console.log(evt.origin, location.origin);
          }
        },
        (err: any) => {
          reject(err);
        }
      );
    });
  }

  async verify(accountExistData: AccountExistData) {
    try {
      const authObj = await this.getAuthObj();

      this.login(
        apis.verifyWechat,
        {
          user_id_token: accountExistData.user_id_token,
          access_token: authObj.access_token,
        },
        true,
        accountExistData.need_set_password
      );
    } catch (e: any) {
      this.dispatchFail(e);
    }
  }

  async signin() {
    try {
      this.onReady();
      const authObj = await this.getAuthObj();
      if(authObj.access_token){
        await this.login(apis.loginByWechat, {
          access_token: authObj.access_token,
          ...addParams()
        });
      }else {
        this.handleError(this.translate('111840'), 2000)
      }
    } catch (e: any) {
      this.dispatchFail(e);
    }
  }

  async getAccessToken() {
    const authObj = await this.getAuthObj();
    const data = {
      access_token: authObj.access_token
    };
    return { data, channelName: this.type }
  }

  async bind() {
    const { access_token = '', wechat_state = ''}: any = await this.getAuthObj('bind');
    const data = {
      access_token,
      wechat_state
    };
    return { data, channelName: this.type }
  }
}
