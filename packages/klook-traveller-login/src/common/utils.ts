// @ts-ignore
import MD5 from "blueimp-md5";
import { uaConfigList } from "@/config";
import {CHECKED_TERMS_KEY} from "@/common/const-data";

export const md5 = MD5;

export const addParams = function () {
  const termIds = (localStorage.getItem(CHECKED_TERMS_KEY)|| '').split(',').map((id)=> +id );
  if(window.__OAUTH_STATE__?.options?.inviteCode) {
    return { invite_code: window.__OAUTH_STATE__.options.inviteCode,  term_ids: termIds }
  }else {
    return { term_ids: termIds }
  }
}

export const loadScript = function (url: string, cb?: () => void) {
  const head =
    document.getElementsByTagName("head")[0] || document.documentElement;
  const script = document.createElement("script");
  script.src = url;

  script.onload = function () {
    if (typeof cb === "function") {
      cb();
    }
    script.onload = null;
    if (head && script.parentNode) {
      head.removeChild(script);
    }
  };
  head.insertBefore(script, head.firstChild);
};

// 判断是特殊webview
function removeElements(a: any, b: any) {
  for (let i = 0; i < b.length; i++) {
    const index = a.indexOf(b[i]);
    if (index !== -1) {
      a.splice(index, 1);
    }
  }
}

export const thirdPartyWebviewBrowser = function(list: number[]) {
  if (!window) { return list }
  const ua = (window.navigator.userAgent || '').toLowerCase()
  uaConfigList.some((item) => {
    if(ua.includes(item.uaWord)) {
      removeElements(list, item.exclusion)
      return true
    }
  })
}

export const jumpToThirdParty = function (
  url: string,
  onMessage: (evt: MessageEvent) => any,
  onFail?: (error: any) => any
) {
  if (!url) return;

  const newWindow = window.open(
    url,
    "_blank",
    "menubar=1, resizable=1, width=600, height=555"
  );
  // 防止重复监听事件触发多次回调
  if (!window.OAuth?.onMessageListener) {
    window.addEventListener("message", (evt) => {
      onMessage(evt);
      if (evt.data.access_token) {
        window.OAuth.accessToken = evt.data.access_token;
        newWindow?.close();
      }
      if (evt.data?.type === 'error') {
        const errorObj = evt.data?.error || {};
        onFail?.({ "error": errorObj?.error, "message": errorObj?.message });
        newWindow?.close();
      }
      window.OAuth.onMessageListener = false;
    });
    window.OAuth.onMessageListener = true;

    // 监听窗口被关闭
    const newWindowInterval = setInterval(() => {
      if (newWindow?.closed && !window.OAuth.accessToken) {
        onFail?.({ "error": "popup_closed_by_user" });
        window.OAuth.onMessageListener = false;
        clearInterval(newWindowInterval);
      }
    }, 1000);
  }
  return newWindow;
};

export const translate = function () {
  const i18n = window.i18n;
  if (!i18n || !i18n.t) return window.OAuth?.translate?.apply(null, arguments);
  return i18n.t.apply(i18n, arguments);
};

export const localStorageEx = {
  setItem(key: string, value: any, expires: number) {
    window.localStorage.setItem(
      key,
      JSON.stringify({
        localData: value,
        expires: new Date().getTime() + expires,
      })
    );
  },
  getItem(key: string) {
    try {
      const storage = window.localStorage.getItem(key);
      if (!storage) return "";
      const data = JSON.parse(storage);
      const curTime = new Date().getTime();

      if (data) {
        if (curTime > data.expires) {
          window.localStorage.removeItem(key);
        } else {
          return data.localData;
        }
      }
    } catch (e) {
      console.error(e);
    }
  },
  removeItem(key: string) {
    window.localStorage.removeItem(key);
  },
};

export const getLangPreferCountryCode = function (lang: string) {
  const langMap: Record<string, Array<string>> = {
    "zh-TW": ["TW", "HK", "MO", "SG", "CN", "TH", "MY", "AU", "US", "KR"],
    "zh-HK": ["HK", "TW", "MO", "SG", "CN", "TH", "MY", "AU", "US", "KR"],
    "zh-CN": ["CN", "HK", "SG", "MY", "KR", "US", "CA", "AU", "TH"],
    en: ["DE", "FR", "RU", "NL", "BE", "CH", "SE", "ES", "BR", "AE"],
    "en-US": ["US", "MX", "CA", "BR", "CO", "AG", "PE", "VE", "CL", "KR"],
    "en-AU": ["AU", "NZ", "PG", "SG", "ID", "MY", "IN", "AE", "GB", "US"],
    "en-NZ": ["NZ", "AU", "PG", "SG", "ID", "MY", "IN", "AE", "GB", "US"],
    "en-GB": ["GB", "DK", "FI", "IS", "NO", "SE", "IE", "BE", "NL", "CH"],
    "en-IN": ["IN", "LK", "PK", "BD", "MY", "SG", "NP", "AE", "GB", "US"],
    "en-SG": ["SG", "MY", "PH", "TH", "ID", "IN", "AU", "HK", "CA", "GB"],
    "en-CA": ["CA", "US", "HK", "GB", "FR", "LB", "AE", "SG", "PH", "CN"],
    "en-HK": ["HK", "TW", "MO", "SG", "CN", "TH", "MY", "AU", "US", "KR"],
    "en-PH": ["PH", "SG", "MY", "TH", "ID", "IN", "AU", "HK", "CA", "US"],
    "en-MY": ["MY", "SG", "PH", "TH", "ID", "IN", "AU", "HK", "AE", "GB"],
    ko: ["KR", "CN", "JP", "US", "TW", "HK", "PH", "SG", "TH", "MY"],
    th: ["TH", "SG", "HK", "PH", "KR", "MY", "IN", "AU", "ID", "CA"],
    vi: ["VN", "SG", "HK", "PH", "KR", "MY", "IN", "AU", "ID", "CA"],
    id: ["ID", "SG", "HK", "MY", "PH", "TW", "KR", "CN", "TH", "US"],
    ja: ["JP", "US", "GB", "AU", "CA", "SG", "CN", "HK", "TW", "KR"],
    de: ["DE", "AU", "CH", "BE", "DK", "SE", "GB", "NL", "NO", "IS"],
    it: ["IT", "AL", "US", "CH", "CA", "MT", "BE", "HR", "SM", "MC"],
    fr: ["FR", "CA", "BE", "CH", "LU", "SE", "NL", "GB", "MC", "MA"],
    ru: ["RU", "UA", "KZ", "BY", "KG", "EE", "LV", "LT", "IL", "MD"],
    es: ["ES", "MX", "AR", "CO", "PE", "VE", "CL", "EC", "CR", "PA"],
  };
  return langMap[lang] || langMap["en"];
};

export const emailValid = function (email: string) {
  const reg = /^[a-zA-Z0-9_-]+(\.([a-zA-Z0-9_-])+)*@[a-zA-Z0-9_-]+[.][a-zA-Z0-9_-]+([.][a-zA-Z0-9_-]+)*$/;
  return reg.test(email);
};

export const passwordValid = function (password: string) {
  const reg = /(?=.*[0-9])(?=.*[a-zA-Z])(?=.*[\W])(?=.*[\S])^[0-9A-Za-z\S\W]{8,20}$/g;
  return reg.test(password);
};

export const phoneValid = function (phone: string, countryCode: string) {
  const reg = countryCode === "86" ? /^\d{11}$/ : /^\d{6,18}$/;
  return reg.test(phone);
};

/**
 * 包装async/await异常处理
 * @param promise
 */
export const awaitWrap = <T, U = any>(
  promise: Promise<T>
): Promise<[U | null, T | null]> => {
  return promise
    .then<[null, T]>((data: T) => [null, data])
    .catch<[U, null]>((err) => [err, null]);
};

// 简化节流
export const throttle = function (fn: Function, gap: number) {
  let timerId: any = null;
  return function (...rest: any) {
    if (timerId === null) {
      fn(...rest)
      timerId = setTimeout(() => {
        timerId = null;
      }, gap)
    }
  }
}


// 复制文案
export const copyText = function (text: string, cb: Function) {
  if(document.execCommand){
    var input = document.createElement("input");
    document.body.appendChild(input);
    input.setAttribute("value", text);
    input.select();
    try {
      document.execCommand("copy");
      cb();
    } catch {}
    document.body.removeChild(input);
  } else {
    navigator.clipboard?.writeText(text).then(() => {
      cb();
    });
  }
}

export const dataToBase64 = function (data: any) {
  const utf8Bytes: any = new TextEncoder().encode(JSON.stringify(data))
  return window.btoa(
    String.fromCharCode.apply(null, utf8Bytes)
  )
  .replace(/\+/g, '-')
  .replace(/\//g, '_')
}

export const capitalizeFirstLetter = function (str: string) {
  return str.charAt(0).toUpperCase() + str.slice(1);
}
