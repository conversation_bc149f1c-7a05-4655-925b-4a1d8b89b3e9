//@ts-nocheck
import ChoosePanel from './components/choose-panel.vue'
import VerifyPanel from './components/verify-panel.vue'
import FraudErrorPanel from './components/fraud-error-panel.vue'
import baseMixin from "@/mixin/base.ts";
import { dataToBase64 } from "@/common/utils";

export default {
  name: 'Base2Fa',
  components: {
    ChoosePanel,
    VerifyPanel,
    FraudErrorPanel
  },
  mixins: [baseMixin],
  props: {
    language: String,
    platform: String,
    langPath: String,
    validateTypeList: Array,
    rid: String,
    twoFaToken: String,
    promptIntervalDays: Number,
    isFraud: Boolean
  },
  provide() {
    return {
      language: this.language,
      platform: this.platform,
      langPath: this.langPath
    };
  },
  data() {
    return {
      showPopWindow: true,
      activeStep: 'ChoosePanel',
      selectedVerifyMethod: -999,
      selectedLoginId: '',
      selectedLoginIdMask: '',
      doNotPrompt: false,
      methodSpmNameMap: {
        1: "Email",
        2: "Phone",
        3: "Password",
      }
    }
  },
  computed: {
    activeStepOptions() {
      return this.getOptions(this.activeStep)
    },
    spmMethodList() {
      return this.validateTypeList.map((method) => this.methodSpmNameMap[method.validate_type]).toString()
    },
    modalSpm() {
      // 只有选择验证方式面板才会在modal层埋module，因为包含footer
      return this.activeStep === 'ChoosePanel'
      ? `${this.activeStepOptions.spmModuleName}?ext=${JSON.stringify({ MethodList: this.spmMethodList })}`
      : ''
    }
  },
  methods: {
    getOptions(activeStep: string) {
      const stepOptions: any = {
        ChoosePanel: {
          componentName: 'ChoosePanel',
          spmModuleName: 'VerificationMethod',
          props: {
            verifyMethods: this.validateTypeList,
            selectedVerifyMethod: this.selectedVerifyMethod,
            selectedLoginId: this.selectedLoginId,
            selectedLoginIdMask: this.selectedLoginIdMask,
            promptIntervalDays: this.promptIntervalDays,
            rid: this.rid,
            twoFaToken: this.twoFaToken,
            methodSpmNameMap: this.methodSpmNameMap
          },
          on: {
            updateVerifyMethod: (method) => { this.updateVerifyMethod(method) },
            nextStep: () => { this.nextStep() },
            closePopWindow: () => { this.closePopWindow() }
          }
        },
        VerifyPanel: {
          componentName: 'VerifyPanel',
          spmModuleName: this.selectedVerifyMethod === 3 ? 'EntryPassword' : 'VerificationCode',
          props: {
            selectedVerifyMethod: this.selectedVerifyMethod,
            rid: this.rid,
            twoFaToken: this.twoFaToken
          },
          on: {
            verifySuccess: () => { this.verifySuccess() },
          }
        },
        FraudErrorPanel: {
          componentName: 'FraudErrorPanel',
          spmModuleName: 'FraudDetect',
          on: {
            closePopWindow: () => { this.closePopWindow() }
          }
        }
      }
      return stepOptions[activeStep]
    },

    updateVerifyMethod(method: any) {
      this.selectedVerifyMethod = method.validate_type
      this.selectedLoginId = method.login_id
      this.selectedLoginIdMask = method.login_id_mask
    },

    nextStep() {
      this.activeStep = 'VerifyPanel'
    },

    closePopWindow() {
      this.$inhouse.track('custom', 'body', {
        spm: `SecurityVerificationMethod.${this.activeStepOptions.spmModuleName || ''}.Close`
      })
      this.showPopWindow = false
      this.$emit('close')
    },

    backToChoosePanel() {
      this.$inhouse.track('custom', 'body', {
        spm: `SecurityVerificationMethod.${this.activeStepOptions.spmModuleName || ''}.Return`
      })
      this.activeStep = 'ChoosePanel'
    },

    verifySuccess() {
      this.$toast(this.__t("119983"))
      window.OAuthEvents.$emit('twoFaVerify:verified',
        dataToBase64({
          twofa: {
            token: this.twoFaToken,
            rid: this.rid,
            no_prompt: this.doNotPrompt
          }

        })
      )
      this.closePopWindow()
    }
  },
  created() {
    if (this.isFraud) {
      this.activeStep = 'FraudErrorPanel'
    }
  },
}
