<template>
  <div :class="`choose-panel-${platform}`">
    <div class="title">{{ __t("119970") }}</div>
    <div class="verify-account">
      <div v-if="!onlyMethod" class="choose-one">{{ __t("120195") }}</div>
      <div class="verify-methods">
        <div
          v-for="(method, index) in methods"
          :key="index"
          class="verify-method"
          :class="{
            'verify-method--selected':
              !onlyMethod && isChecked(method.validate_type),
            'verify-method--only': onlyMethod,
            'verify-method--not-only': !onlyMethod,
          }"
          :data-spm-item="`Method_LIST
            ?idx=${index}
            &len=${methods.length}
            &ext=${JSON.stringify({
              Method: methodSpmNameMap[method.validate_type],
              listIndex: index,
              listLen: methods.length,
            })}
          `"
          v-galileo-click-tracker="{ spm: 'Method_LIST', componentName: 'klook-traveller-login' }"
          @click="onSelect(method)"
        >
          <div class="verify-method-content">
            <component
              :is="method.icon"
              theme="outline"
              size="20"
              :fill="colorTextPrimary"
              class="icon"
            />
            <div class="desc">
              <div class="type">{{ method.method_name }}</div>
              <div class="rcv" v-html="method.desc"></div>
            </div>
          </div>

          <div v-if="!onlyMethod" class="verify-method-radio">
            <IconCheckCircle
              v-if="isChecked(method.validate_type)"
              theme="filled"
              size="24"
              :fill="colorBrandPrimary"
            />
            <div v-else class="empty-circle-wrapper">
              <div class="empty-circle" />
            </div>
          </div>
        </div>
      </div>

      <klk-link
        :href="`/${this.langPath}account/security`"
        target="_blank"
        color="#212121"
        class="login-methods-link"
        data-spm-item="EditMethod"
        v-galileo-click-tracker="{ spm: 'EditMethod', componentName: 'klook-traveller-login', autoTrackSpm: true }"
        @click="closePopWindow"
      >
        {{ __t("119976") }}
      </klk-link>
    </div>
  </div>
</template>

<script>
import {
  IconMobile,
  IconMail,
  IconLock,
  IconCheckCircle,
} from "@klook/klook-icons";
import {
  $colorTextPrimary,
  $colorBrandPrimary,
} from "@klook/klook-ui/lib/utils/design-token-esm";
import baseMixin from "@/mixin/base.ts";

export default {
  name: "ChoosePanel",
  components: {
    IconMobile,
    IconMail,
    IconLock,
    IconCheckCircle,
  },
  mixins: [baseMixin],
  inject: ["platform", "langPath"],
  props: {
    verifyMethods: {
      type: Array,
      default: () => [],
    },
    selectedVerifyMethod: {
      type: Number,
      default: -999,
    },
    methodSpmNameMap: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      colorTextPrimary: $colorTextPrimary,
      colorBrandPrimary: $colorBrandPrimary,
      methods: [],
    };
  },
  computed: {
    onlyMethod() {
      return this.verifyMethods.length === 1;
    },
  },
  methods: {
    isChecked(method) {
      return method === this.selectedVerifyMethod;
    },

    onSelect(method) {
      this.$emit("updateVerifyMethod", method);
    },

    getMethodInfo(method) {
      const infoMap = {
        1: {
          icon: "IconMail",
          method_name: this.__t("119971"),
          desc: this.__t("119972", {
            login_method: `<span class="bold"> ${method.login_id_mask}</span>`,
          }),
        },
        2: {
          icon: "IconMobile",
          method_name: this.__t("119973"),
          desc: this.__t("119972", {
            login_method: `<span class="bold"> ${method.login_id_mask}</span>`,
          }),
        },
        3: {
          icon: "IconLock",
          method_name: this.__t("119974"),
          desc: this.__t("119975"),
        },
      };
      return infoMap[method.validate_type];
    },

    closePopWindow() {
      this.$emit("closePopWindow");
    },
  },
  created() {
    if (this.selectedVerifyMethod === -999) {
      const initialMethod = this.verifyMethods.length
        ? this.verifyMethods[0]
        : {};
      this.$emit("updateVerifyMethod", initialMethod);
    }

    this.methods = this.verifyMethods.map((method) => {
      return {
        ...method,
        ...this.getMethodInfo(method),
      };
    });
  },
};
</script>


<style lang="scss" scoped>
.title {
  @include font-heading-s;
}

.choose-panel-desktop {
  .verify-account {
    margin: 32px 0;
  }

  .verify-method {
    &:hover {
      border-color: $color-brand-primary;
    }

    &--only:hover {
      border-color: $color-border-active;
      margin-top: 0;
    }
  }
}

.choose-panel-mobile {
  .verify-account {
    margin-top: 24px;
  }
}

.choose-one {
  @include font-body-m-regular;
}

.verify-methods {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.verify-method {
  display: flex;
  justify-content: space-between;
  margin-top: 16px;
  padding: 16px;
  width: 100%;
  border-radius: $radius-xl;
  background: $color-bg-1;
  border: 1px solid $color-border-normal;

  &--selected {
    border-color: $color-brand-primary;
  }

  &--only {
    border-color: $color-border-active;
    margin-top: 0;
  }

  &--not-only {
    cursor: pointer;
  }

  &:last-child {
    margin-bottom: 16px;
  }
}

.verify-method-content {
  display: flex;

  .desc {
    margin-left: 12px;

    .rcv {
      @include font-body-s-regular;
      color: $color-text-secondary;
      margin-top: 4px;

      ::v-deep .bold {
        @include font-body-s-bold;
      }
    }
  }
}

.verify-method-radio {
  flex-shrink: 0;
  margin: auto 0 auto 8px;

  .empty-circle-wrapper {
    width: 24px;
    height: 24px;
  }

  .empty-circle {
    width: 21px;
    height: 21px;
    border-radius: $radius-pill;
    border: 1px solid $color-text-disabled;
  }
}
</style>
