<template>
  <div :class="`fraud-error-panel-${platform}`" data-spm-module="FraudDetect">
    <div class="fraud-text">{{ __t("119968") }}</div>
    <div class="footer">
      <klk-button
        :block="platform === 'mobile'"
        :size="platform === 'mobile' ? 'normal' : 'small'"
        class="gotit-btn"
        data-spm-item="Confirm"
        v-galileo-click-tracker="{ spm: 'FraudDetect.Confirm', componentName: 'klook-traveller-login' }"
        @click="closeModal"
      >
        {{ __t("119969") }}
      </klk-button>
    </div>
  </div>
</template>

<script>
import baseMixin from "@/mixin/base.ts";

export default {
  name: "FraudErrorPanel",
  inject: ["platform"],
  mixins: [baseMixin],
  methods: {
    closeModal() {
      this.$emit("closePopWindow");
    },
  },
};
</script>

<style lang="scss" scoped>
.fraud-text {
  @include font-body-s-regular();
}

.footer {
  margin-top: 24px;
}

.fraud-error-panel-desktop {
  .footer {
    display: flex;
    justify-content: flex-end;
  }
}
</style>
