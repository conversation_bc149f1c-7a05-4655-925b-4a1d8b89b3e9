<template>
  <div :class="`pop-window-header-${platform}`">
    <IconClose
      v-if="activeStep === 'ChoosePanel' && platform === 'mobile'"
      class="header-action-btn"
      theme="outline"
      size="24"
      :fill="colorTextPrimary"
      @click.native="closeBtnClick"
    />

    <IconBack
      v-if="activeStep === 'VerifyPanel'"
      class="header-action-btn"
      theme="outline"
      size="24"
      :fill="colorTextPrimary"
      @click.native="backBtnClick"
    />
  </div>
</template>

<script>
import { IconClose, IconBack } from "@klook/klook-icons";
import { $colorTextPrimary } from "@klook/klook-ui/lib/utils/design-token-esm";

export default {
  name: "PopWindowHeader",
  components: {
    IconClose,
    IconBack,
  },
  inject: ["platform"],
  props: {
    activeStep: {
      type: String,
      default: "ChoosePanel"
    }
  },
  data() {
    return {
      colorTextPrimary: $colorTextPrimary,
    };
  },
  methods: {
    closeBtnClick() {
      this.$emit("closeBtnClick");
    },

    backBtnClick() {
      this.$emit("backToChoosePanel");
    },
  },
};
</script>

<style lang="scss" scoped>
.pop-window-header-desktop {
  .header-action-btn {
     margin-bottom: 20px;
  }
}

.header-action-btn {
  cursor: pointer;
}
</style>
