<template>
  <klk-form
    class="verify-panel"
    :model="verifyForm"
    @submit.native.prevent
    @keyup.native.enter="onEnterPress"
  >
    <div v-if="selectedVerifyMethod === 3" data-spm-module="EntryPassword">
      <div class="title">{{ __t("119979") }}</div>
      <klkUserKitPasswordInputFormItem
        v-model="verifyForm.password"
        :form-item-attrs="{
          prop: 'password',
          downErrorMessage,
          upErrorMessage,
        }"
        :input-attrs="{
          placeholder: __t('115113'),
          defaultPwVisible: false,
          disabled: validatingPassword,
        }"
        data-spm-item="Input"
        v-galileo-click-tracker="{ spm: 'EntryPassword.Input', componentName: 'klook-traveller-login' }"
      />
      <klk-button
        ref="pwdSubmitBtn"
        class="submit-btn"
        type="primary"
        block
        :loading="validatingPassword"
        data-spm-item="Confirm"
        v-galileo-click-tracker="{ spm: 'EntryPassword.Confirm', componentName: 'klook-traveller-login' }"
        @click.native.prevent="validatePassword"
      >
        {{ __t("119980") }}
      </klk-button>
    </div>

    <klkUserKitVerifyCodeInputPanel
      v-else
      :style="{ paddingBottom: `${panelPaddingBtm}px` }"
      :title="__t('111413')"
      :description="__t('111414')"
      :has-help-me-btn="false"
      :has-change-btn="false"
      :language="language"
      page-spm="SecurityVerificationMethod"
      v-bind="mergedAttrs"
      v-on="$listeners"
      @focus="onInputFocus"
      @blur="onInputBlur"
    />
  </klk-form>
</template>

<script>
import {
  klkUserKitVerifyCodeInputPanel,
  klkUserKitPasswordInputFormItem,
} from "@klook/user-kit";
import "@klook/user-kit/dist/esm/index.css";
import apis from "@/common/apis";
import baseMixin from "@/mixin/base.ts";

export default {
  name: "VerifyPanel",
  components: {
    klkUserKitVerifyCodeInputPanel,
    klkUserKitPasswordInputFormItem,
  },
  mixins: [baseMixin],
  inject: ["platform", "language"],
  props: {
    selectedVerifyMethod: {
      type: Number,
      default: -999,
    },
    rid: {
      type: String,
      default: "",
    },
    twoFaToken: {
      type: String,
      default: "",
    },
  },
  computed: {
    mergedAttrs() {
      return {
        ...this.$attrs,
        bindMethod: this.selectedVerifyMethod === 1 ? 1 : 6,
        sendVerifyCodeParams: this.sendVerifyCodeParams,
        verifyAttrs: this.verifyPanelParams,
      };
    },
  },
  data() {
    return {
      upErrorMessage: "",
      downErrorMessage: "",
      validatingPassword: false,
      verifyForm: {
        password: "",
      },
      initialViewportHeight: 0,
      panelPaddingBtm: 0,
    };
  },
  methods: {
    onEnterPress() {
      // 密码才需要处理，otp输入6位数直接提交
      if (this.selectedVerifyMethod === 3) {
        this.$refs.pwdSubmitBtn.$el.click();
      }
    },

    trackVerifySpm(error) {
      // 提交密码验证报错上报
      this.inhouse.track("custom", "body", {
        spm: "SecurityVerificationMethod.EntryPassword.VerificationError",
        ext: {
          ErrorCode: error?.code || "",
          ErrorText: error?.message || this.__t("111839")
        },
      });
    },

    async validatePassword() {
      try {
        this.validatingPassword = true;
        const { success, error } = await this.ajaxPostJSON(apis.validatePwd, {
          password: this.verifyForm.password,
          action: "2fa",
          twofa: {
            token: this.twoFaToken,
            rid: this.rid,
          },
        });
        if (success) {
          this.$emit("verifySuccess");
        } else {
          this.handleError(error || null);
        }
      } catch (err) {
        this.handleError(null);
      } finally {
        this.validatingPassword = false;
      }
    },

    handleError(error) {
      this.clearError();
      this.trackVerifySpm(error);
      if (error) {
        if (error.code === "2999") {
          this.downErrorMessage = error.message;
        } else {
          this.upErrorMessage = error.message;
        }
      } else {
        this.upErrorMessage = this.__t("111839");
      }
    },

    clearError() {
      this.upErrorMessage = "";
      this.downErrorMessage = "";
    },

    //  支持验证码面板键盘收起时，弹窗高度不变
    onInputFocus() {
      if (this.platform === "mobile") {
        window.visualViewport.addEventListener("resize", this.onWindowResize);
      }
    },

    onInputBlur() {
      if (this.platform === "mobile") {
        window.visualViewport.removeEventListener("resize", this.onWindowResize);
      }
    },

    onWindowResize() {
      const visualViewportHeight = window.visualViewport.height;
      const keyboardHeight = this.initialViewportHeight - visualViewportHeight;
      this.panelPaddingBtm = keyboardHeight;
    },
  },
  mounted() {
    this.initialViewportHeight = window.innerHeight;
  },
};
</script>

<style lang="scss" scoped>
.title {
  @include font-heading-m;
}

.submit-btn {
  margin-top: 8px;
}
</style>
