<template>
  <div :class="`choose-panel-footer-${platform}`">
    <klk-checkbox v-model="doNotPromptValue">
      {{ doNotPromptText }}
    </klk-checkbox>
    <klk-button
      block
      class="next-btn"
      text-overflow="ellipsis"
      :loading="sendingOtp"
      :data-spm-item="`Next?ext=${JSON.stringify({
        SelectedMethod: methodSpmNameMap[selectedVerifyMethod],
        DontAskAgain: doNotPrompt,
      })}`"
      v-galileo-click-tracker="{ spm: 'Next', componentName: 'klook-traveller-login', autoTrackSpm: true }"
      @click="nextStep"
    >
      {{ __t("119978") }}
    </klk-button>
  </div>
</template>

<script>
import { Checkbox, Button } from "@klook/klook-ui";
import baseMixin from "@/mixin/base.ts";
import { klkUserKitSendVerifyEmailMixin } from "@klook/user-kit";

export default {
  name: "ChoosePanelFooter",
  components: {
    KlkCheckbox: Checkbox,
    KlkButton: Button,
  },
  mixins: [baseMixin, klkUserKitSendVerifyEmailMixin],
  inject: ["platform"],
  props: {
    selectedVerifyMethod: {
      type: Number,
      default: -999,
    },
    selectedLoginId: {
      type: String,
      default: "",
    },
    selectedLoginIdMask: {
      type: String,
      default: "",
    },
    promptIntervalDays: {
      type: Number,
      default: 90,
    },
    rid: {
      type: String,
      default: "",
    },
    twoFaToken: {
      type: String,
      default: "",
    },
    doNotPrompt: {
      type: Boolean,
      default: false,
    },
    methodSpmNameMap: {
      type: Object,
      default: () => ({}),
    },
  },
  computed: {
    doNotPromptValue: {
      get() {
        return this.doNotPrompt;
      },
      set(value) {
        this.$emit("update:doNotPrompt", value);
      },
    },
    doNotPromptText() {
      return this.__t("119977", { num: this.promptIntervalDays });
    },
  },
  data() {
    return {
      sendingOtp: false,
    };
  },
  methods: {
    async sendOtp() {
      const actionName = "2fa";
      const newParams = {
        payload: {
          twofa: {
            token: this.twoFaToken,
            rid: this.rid,
          },
        },
      };

      if (this.selectedVerifyMethod === 1) {
        newParams.type = 3;
        newParams.payload.email = this.selectedLoginId;
      } else {
        newParams.type = 1;
        newParams.payload.mobile = this.selectedLoginId;
      }

      newParams.action = actionName;
      newParams.rcv = this.selectedLoginId;
      newParams._rc = '';

      const newSendVerifyCodeParams = {
        ...this.sendVerifyCodeParams,
        ...newParams,
      };

      try {
        this.sendingOtp = true;
        const { res, verifyUseRid } = await this.sendVerifyEmail(
          newSendVerifyCodeParams,
          "code"
        );

        if (res.success) {
          const verifyPanelData = {
            rcv: this.selectedLoginIdMask,
            verifyUseRid,
            otp_token: res.result.otp_token,
            next_timestamp_sec: res.result.next_timestamp_sec,
            support_type: res.result.support_type,
          };
          // 设置verify面板所需字段
          this.updateVerifyPanelParams(verifyPanelData);
          // 更新sendVerifyCodeParams，verify面板重发需要使用
          this.updateSendVerifyCodeParams(newSendVerifyCodeParams);
          this.$emit("nextStep");
        } else {
          if (res.error.code !== '-9098' && res.error.message) {
            this.$toast(res.error.message);
          }
        }
      } catch (err) {
        this.$toast(this.__t("111839"));
      } finally {
        this.sendingOtp = false;
      }
    },

    async nextStep() {
      if ([1, 2].includes(this.selectedVerifyMethod)) {
        // 1,2: 邮箱/手机otp
        await this.sendOtp();
      } else {
        // 密码
        this.$emit("nextStep");
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.choose-panel-footer-desktop {
  padding-top: 8px;
}

.next-btn {
  margin-top: 12px;
}
</style>
