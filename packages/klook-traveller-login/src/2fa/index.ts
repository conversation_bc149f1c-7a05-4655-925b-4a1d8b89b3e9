import { TwoFaVerifyOptions } from '../../types/index';
import { translate as mergeTranslate } from '@klook/klk-traveller-utils/lib/klookLocalesMerge';
import apis from '@/common/apis';
import { translate, dataToBase64 } from "@/common/utils";
import Toast from '@klook/klook-ui/lib/toast'
import Loading from '@klook/klook-ui/lib/loading'

const __t: any = translate

export default async function need2fa(this: TwoFaVerifyOptions) {
    let that = this

    import(`../../locales/${that.language}.json`).then(async (localLang) => {
        if (!window.i18n) {
            window.OAuth.translate = mergeTranslate(localLang.default);
        } else {
            window.i18n.mergeLocaleMessage(that.language, localLang.default);
        }
        const loadingComp = Loading.newInstance({
            showOverlay: false,
            showLoadingBg: true
        })

        try {
            loadingComp.show()
            const ajaxPost = (window.$axios?.$post || window.$axios?.post)?.bind(window.$axios);
            const { result, success, error } = await ajaxPost(apis.need2fa, { action: that.action });
            const params = { ...that, ...result }

            if (success) {
                if (result.need_twofa) {
                    await showDialog(params, false)
                } else {
                    window.OAuthEvents.$emit('twoFaVerify:verified',
                        dataToBase64({
                            twofa: {
                                token: result.twofa_token,
                                rid: result.rid,
                                no_prompt: false
                            }
                        })
                    )
                    offVerifyEvent()
                }
            } else {
                offVerifyEvent()
                if (error.code === '9011') {
                    await showDialog(params, true)
                } else {
                    Toast(error.message || __t('111839'))
                }
            }
        } catch (error) {
            offVerifyEvent()
            Toast(__t('111839'))
        } finally {
            loadingComp.remove(() => {})
        }
    })
}

function offVerifyEvent() {
    window.OAuthEvents.$off("twoFaVerify:verified");
}

async function showDialog(params: any, isFraud: boolean = false) {
    const dialog = await (await import('./initial')).default.call({ ...params, is_fraud: isFraud })
    dialog.show()
}