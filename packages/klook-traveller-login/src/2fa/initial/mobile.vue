<template>
  <div data-spm-page="SecurityVerificationMethod">
    <klk-modal
      v-if="activeStep === 'FraudErrorPanel'"
      :open.sync="showPopWindow"
      :append-body="false"
      :show-default-footer="false"
      :overlay-closable="false"
      @close="closePopWindow"
    >
      <component
        :is="activeStepOptions.componentName"
        :do-not-prompt.sync="doNotPrompt"
        v-bind="activeStepOptions.props"
        v-on="activeStepOptions.on"
      />
    </klk-modal>

    <klk-bottom-sheet
      v-else
      class="twofa-bottom-sheet"
      :visible.sync="showPopWindow"
      :mask-closable="false"
      :can-pull-close="false"
      :data-spm-module="modalSpm"
      @close="closePopWindow"
    >
      <template slot="header">
        <PopWindowHeader
          :active-step="activeStep"
          @closeBtnClick="closePopWindow"
          @backToChoosePanel="backToChoosePanel"
        />
      </template>

      <component
        :is="activeStepOptions.componentName"
        v-bind="activeStepOptions.props"
        v-on="activeStepOptions.on"
      />

      <template v-if="activeStep === 'ChoosePanel'" slot="footer">
        <ChoosePanelFooter
          :do-not-prompt.sync="doNotPrompt"
          v-bind="activeStepOptions.props"
          v-on="activeStepOptions.on"
        />
      </template>
    </klk-bottom-sheet>
  </div>
</template>

<script>
import { Modal, BottomSheet } from "@klook/klook-ui";
import ChoosePanelFooter from "../components/choose-panel-footer.vue";
import PopWindowHeader from "../components/pop-window-header.vue";
import Base2Fa from "../base-2fa.ts";

export default {
  name: "TwoFactorAuth",
  components: {
    KlkModal: Modal,
    KlkBottomSheet: BottomSheet,
    ChoosePanelFooter,
    PopWindowHeader,
  },
  mixins: [Base2Fa],
};
</script>

<style lang="scss" scoped>
.twofa-bottom-sheet {
  ::v-deep .klk-bottom-sheet-footer {
    box-shadow: $shadow-normal-3;
  }
}
</style>
