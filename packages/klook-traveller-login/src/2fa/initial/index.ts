import Vue, { CreateElement } from "vue";
import DesktopComp from "./desktop.vue";
import MobileComp from "./mobile.vue";
import { TwoFaVerifyOptions } from "../../../types/index";

interface TwoFaTemplateParams extends TwoFaVerifyOptions {
    validate_type_list: any[];
    rid: string;
    twofa_token: string;
    prompt_interval_days: number;
    is_fraud: boolean;
}

let instance: any;

export default function render(this: TwoFaTemplateParams) {
    let that = this;

    const langPath = (lang: string) => {
        if (lang === "en") {
            return "";
        } else {
            return `${lang}/`;
        }
    }

    const removeInstance = () => {
        window.OAuthEvents.$off("twoFaVerify:verified");
        instance?.$el?.parentNode?.removeChild(instance.$el);
        instance?.$destroy();
        instance = null;
    }

    return {
        show: function () {
            if (instance) return instance;
            const Comp = that.platform === "desktop" ? DesktopComp : MobileComp;
            const DialogCtor = Vue.extend({
                render(h: CreateElement) {
                    return h(Comp, {
                        on: {
                            close: () => {
                                removeInstance();
                            }
                        },
                        props: {
                            language: that.language,
                            platform: that.platform,
                            langPath: langPath(that.language),
                            validateTypeList: that.validate_type_list,
                            rid: that.rid,
                            twoFaToken: that.twofa_token,
                            promptIntervalDays: that.prompt_interval_days,
                            isFraud: that.is_fraud
                        },
                    });
                },
            });

            instance = new DialogCtor().$mount();
            document.body.appendChild(instance.$el);
            return instance;
        },
    };
}
