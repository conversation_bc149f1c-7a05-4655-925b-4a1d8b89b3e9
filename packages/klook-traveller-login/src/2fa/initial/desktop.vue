<template>
  <div data-spm-page="SecurityVerificationMethod">
    <klk-modal
      :open.sync="showPopWindow"
      :append-body="false"
      :show-default-footer="false"
      scrollable
      closable
      :overlay-closable="false"
      :data-spm-module="modalSpm"
      @close="closePopWindow"
    >
      <template slot="header">
        <PopWindowHeader
          :active-step="activeStep"
          @closeBtnClick="closePopWindow"
          @backToChoosePanel="backToChoosePanel"
        />
      </template>

      <component
        :is="activeStepOptions.componentName"
        v-bind="activeStepOptions.props"
        v-on="activeStepOptions.on"
      />

      <template v-if="activeStep === 'ChoosePanel'" slot="footer">
        <ChoosePanelFooter
          :do-not-prompt.sync="doNotPrompt"
          v-bind="activeStepOptions.props"
          v-on="activeStepOptions.on"
        />
      </template>
    </klk-modal>
  </div>
</template>

<script>
import { Modal } from "@klook/klook-ui";
import ChoosePanelFooter from "../components/choose-panel-footer.vue";
import PopWindowHeader from "../components/pop-window-header.vue";
import Base2Fa from "../base-2fa.ts";

export default {
  name: "TwoFactorAuth",
  components: {
    KlkModal: Modal,
    ChoosePanelFooter,
    PopWindowHeader,
  },
  mixins: [Base2Fa],
};
</script>
