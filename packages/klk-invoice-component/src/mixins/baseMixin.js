export default {
    props: {
        platform: {
            type: String,
            default: 'desktop',
        }
    },
    computed: {
        state() {
            return this?.$store?.state || null;
        },
        realPlatform() {
            return this.platform || this.state?.klook?.platform || "desktop";
        },
        realInhouse() {
            return this.$inhouse || (typeof window !== 'undefined' && window?.tracker?.inhouse) || null;
        },
        realAxios() {
            return this.$axios || (typeof window !== 'undefined' && window?.$axios);
        },
    }
};
