import { genText } from "../../src/locale/index.js"
import messages from "../../locales/index.js"

export default {
    provide() {
        return {
            __t: this.getTranslate(),
        };
    },
    methods: {
        getTranslate() {
            return this.__t;
        },
    },
    beforeCreate() {
        const locales = messages;
        const lang = this.$attrs.language || this.$store?.state?.klook?.language || "en";
        this.__t = locales[lang] ? genText(locales[lang]) : genText(locales["en"]);
    }
}