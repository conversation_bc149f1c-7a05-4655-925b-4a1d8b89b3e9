<template>
  <div class="klk-invoice-issuance">
    <div class="invoice-info-group">
      <div class="invoice-label">{{ __t("167546") }}</div>
      <div class="invoice-value">
        {{ customerType === 2 ? __t("167548") : __t("167547") }}
      </div>
    </div>
    <div class="invoice-info-group email">
      <div class="invoice-label">{{ __t("167549") }}</div>
      <div class="invoice-value">{{ email }}</div>
    </div>
    <div
      class="invoice-info-text"
      :class="{ 'invoice-info-text--error': !email }"
    >
      {{ email ? __t("167551") : __t("167553") }}
    </div>
    <k-button
      type="outlined"
      size="small"
      class="invoice-button"
      :data-spm-module="`InvoiceReceipt?ext=${JSON.stringify({
        InvoiceReceiptTypeID: customerType,
        ExistingEmail: !!email,
      })}`"
      data-spm-virtual-item="__virtual"
      v-galileo-click-tracker="{ spm: 'InvoiceReceipt', componentName: 'klk-invoice-component' }"
      @click="showForm = true"
      >{{ email ? __t("167550") : __t("167552") }}</k-button
    >

    <invoice-form
      ref="invoiceFormEntryRef"
      :platform="realPlatform"
      :showForm="showForm"
      :basicInfo="basicInfo"
      :invoiceInfo="invoiceInfo.result"
      @close="handleClose"
      @cancel="handleCancel"
      @saveSuccess="saveSuccess"
      @saveFail="saveFail"
    />
  </div>
</template>

<script>
import * as Button from '@klook/klook-ui/lib/button'
import '@klook/klook-ui/lib/button/style'

import baseMixin from "../mixins/baseMixin";
import localeMixin from "../mixins/localeMixin";
import InvoiceForm from "./components/invoice-form.vue";

export default {
  name: "InvoiceIssuance",
  components: {
    InvoiceForm,
    kButton: Button.default
  },
  mixins: [baseMixin, localeMixin],
  props: {
    basicInfo: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      showForm: false,
      invoiceInfo: {
        success: false,
        result: {
          customer_type: 1,
          email: "",
          carrier_type: 0,
          carrier_content: "",
          company_name: "",
          company_code: "",
        },
      },
    };
  },
  computed: {
    email() {
      return this.invoiceInfo.result.email;
    },
    customerType() {
      return this.invoiceInfo.result.customer_type;
    }
  },
  methods: {
    // 判断是否为初始化状态
     getInvoiceInfo() {
      // basicInfo.invoice_type 接口返回默认的开票类型
      this.invoiceInfo.result.invoice_type = this.basicInfo.invoice_type || 1;
      // customer_type 用户自己选择的开票类型
      // 如果为1，则是个人开票，不返回company_code/company_name
      if(this.invoiceInfo.result.customer_type === 1){
        return {
          success: this.invoiceInfo.success,
          result:{
            ...this.invoiceInfo.result,
            company_code: '',
            company_name: ''
          }
        }
      }
      return this.invoiceInfo;
    },
    handleClose() {
      this.showForm = false;
      this.$emit("close");
    },
    handleCancel() {
      this.showForm = false;
      this.$emit("cancel");
    },
    saveSuccess(res) {
      this.showForm = false;
      this.invoiceInfo = {
        success: res.success,
        result: { ...res.result },
      };
      this.$emit("saveSuccess", this.invoiceInfo);
    },
    saveFail(res) {
      this.$emit("saveFail", res);
    },
    updateTravelerInfoEmail(email) {
      // 仅针对 customer_type为1做处理
      if(this.invoiceInfo.result.customer_type === 1 && !this.invoiceInfo.result.email && email){
        this.invoiceInfo.result.email = email
        this.invoiceInfo.success = true
      }
    }
  },
  created() {
    for (let key in this.invoiceInfo.result) {
      if (this.basicInfo[key] !== undefined) {
        this.invoiceInfo.result[key] = this.basicInfo[key];
      }
    }
    if (!this.invoiceInfo.result.customer_type) {
      this.invoiceInfo.result.customer_type = 1;
    }
    this.invoiceInfo.success = this.basicInfo.email ? true : false;
  },
};
</script>

<style lang="scss" scoped>
.invoice-info-group {
  display: flex;

  &.email {
    margin-top: 8px;
  }
}

.invoice-label {
  @include font-body-m-regular();
  color: $color-text-secondary;
}

.invoice-value {
  @include font-body-m-regular();
  color: $color-text-primary;
  margin-left: 8px;
}

.invoice-email {
  margin-top: 8px;
}

.invoice-info-text {
  @include font-body-m-regular();
  color: $color-text-secondary;
  margin-top: 8px;

  &--error {
    color: $color-error;
  }
}

.invoice-button {
  margin-top: 8px;
}
</style>
