<template>
  <component
    v-if="showForm"
    :is="isMobile ? 'k-bottom-sheet' : 'k-modal'"
    :class="`invoice-form-${realPlatform}`"
    v-bind="compAttrs"
    :data-spm-page="`InvoiceReceipt_Edit?ext=${JSON.stringify({
      ExistingEmail: !!invoiceForm.email,
    })}`"
    @close="handleClose"
  >
    <k-form
      ref="invoiceFormRef"
      :model="invoiceForm"
      :rules="rules"
      class="invoice-form-content"
    >
      <!-- 开票类型: 1 - 个人，2 - 统编 -->
      <k-form-item prop="customer_type">
        <k-radio-group
          v-model="invoiceForm.customer_type"
          class="cust-type-radio-group"
        >
          <div
            class="cust-type-radio"
            :data-spm-module="`InvoiceReceiptType_LIST?idx=0&len=2&ext=${JSON.stringify(
              {
                InvoiceReceiptTypeID: 1,
              }
            )}`"
            data-spm-virtual-item="__virtual"
            v-galileo-click-tracker="{ spm: 'InvoiceReceiptType_LIST', componentName: 'klk-invoice-component' }"
            @click="selectCustomerType(1)"
          >
            <k-radio
              :group-value="1"
              :class="isPersonalCustomer && 'cust-type-radio--selected'"
            >
              {{ __t("167547") }}
            </k-radio>
          </div>
          <div
            class="cust-type-radio"
            :data-spm-module="`InvoiceReceiptType_LIST?idx=1&len=2&ext=${JSON.stringify(
              {
                InvoiceReceiptTypeID: 2,
              }
            )}`"
            data-spm-virtual-item="__virtual"
            v-galileo-click-tracker="{ spm: 'InvoiceReceiptType_LIST', componentName: 'klk-invoice-component' }"
            @click="selectCustomerType(2)"
          >
            <k-radio
              :group-value="2"
              :class="!isPersonalCustomer && 'cust-type-radio--selected'"
            >
              {{ __t("167548") }}
            </k-radio>
          </div>
        </k-radio-group>
      </k-form-item>

      <div class="invoice-fill-info">
        <!-- 无论是个人或者统编开票类型，都需要填写邮箱 -->
        <k-form-item :label="__t('167549')" prop="email">
          <k-input
            v-model="invoiceForm.email"
            :placeholder="__t('167555')"
          ></k-input>
        </k-form-item>

        <!-- 个人发票类型 -->
        <div v-if="isPersonalCustomer">
          <!-- 统一发票 (invoice_type), 载具二选一 -->
          <k-checkbox
            v-if="basicInfo.show_carrier && basicInfo.invoice_type === 2"
            v-model="showCarrier"
            class="show-carrier-checkbox"
            :data-spm-module="`SendToCarrier?ext=${JSON.stringify({
              Status: showCarrier ? 'BeSelected' : 'BeCanceled',
            })}`"
            data-spm-virtual-item="__virtual?trg=manual"
            v-galileo-click-tracker="{ spm: 'SendToCarrier', componentName: 'klk-invoice-component' }"
            @change="handleShowCarrierClick"
            >{{ __t("167556") }}</k-checkbox
          >
          <div v-if="showCarrier" class="carrier-type-box">
            <k-form-item prop="carrier_type">
              <k-radio-group
                class="carrier-type-radio-group"
                v-model="invoiceForm.carrier_type"
                @change="handleCarrierTypeChange"
              >
                <k-radio
                  :group-value="1"
                  :data-spm-module="`SendToCarrier_LIST?idx=0&len=2&ext=${JSON.stringify(
                    {
                      CarrierTypeID: 1,
                    }
                  )}`"
                  data-spm-virtual-item="__virtual"
                  v-galileo-click-tracker="{ spm: 'SendToCarrier_LIST', componentName: 'klk-invoice-component' }"
                  >{{ __t("167557") }}
                </k-radio>
                <k-form-item
                  v-if="isMobileCarrier"
                  prop="carrier_content"
                  class="carrier-content-item"
                >
                  <k-input
                    v-model="invoiceForm.carrier_content"
                    :placeholder="__t('167558')"
                    :maxlength="8"
                  />
                </k-form-item>
                <k-radio
                  :group-value="2"
                  :data-spm-module="`SendToCarrier_LIST?idx=1&len=2&ext=${JSON.stringify(
                    {
                      CarrierTypeID: 2,
                    }
                  )}`"
                  data-spm-virtual-item="__virtual"
                  v-galileo-click-tracker="{ spm: 'SendToCarrier_LIST', componentName: 'klk-invoice-component' }"
                  >{{ __t("167559") }}
                </k-radio>
                <k-form-item
                  v-if="!isMobileCarrier"
                  prop="carrier_content"
                  class="carrier-content-item"
                >
                  <k-input
                    v-model="invoiceForm.carrier_content"
                    :placeholder="__t('167560')"
                    :maxlength="16"
                  />
                </k-form-item>
              </k-radio-group>
            </k-form-item>
          </div>
        </div>

        <!-- 统编发票类型 -->
        <div v-else>
          <k-form-item :label="__t('167561')" prop="company_name">
            <k-input
              v-model="invoiceForm.company_name"
              :placeholder="__t('167562')"
            ></k-input>
          </k-form-item>
          <k-form-item :label="__t('167563')" prop="company_code">
            <k-input
              v-model="invoiceForm.company_code"
              :placeholder="__t('167564')"
              :maxlength="8"
            ></k-input>
          </k-form-item>
        </div>
      </div>

      <!-- tips文案 -->
      <div v-if="invoiceForm.customer_type === 2" class="tips">
        {{ __t("167565") }}
      </div>
      <div v-if="basicInfo.has_receipt" class="tips">{{ __t("167566") }}</div>
    </k-form>

    <div slot="footer" class="form-footer">
      <k-button
        type="outlined"
        class="form-footer-cancel"
        :data-spm-module="`CancelBtn?ext=${JSON.stringify({
          InvoiceReceiptTypeID: invoiceForm.customer_type,
        })}`"
        data-spm-virtual-item="__virtual"
        v-galileo-click-tracker="{ spm: 'CancelBtn', componentName: 'klk-invoice-component' }"
        @click="handleCancel"
      >
        {{ __t("167567") }}
      </k-button>
      <k-button
        type="primary"
        class="form-footer-confirm"
        :data-spm-module="`SaveBtn?ext=${JSON.stringify({
          InvoiceReceiptTypeID: invoiceForm.customer_type,
        })}`"
        data-spm-virtual-item="__virtual"
        v-galileo-click-tracker="{ spm: 'SaveBtn', componentName: 'klk-invoice-component' }"
        @click="handleSubmit"
        >{{ __t("167568") }}</k-button
      >
    </div>
  </component>
</template>

<script>

import * as Checkbox from '@klook/klook-ui/lib/checkbox'
import * as Button from '@klook/klook-ui/lib/button'
import * as Modal from '@klook/klook-ui/lib/modal'
import * as Input from '@klook/klook-ui/lib/input'
import * as Form from '@klook/klook-ui/lib/form'
import * as BottomSheet from '@klook/klook-ui/lib/bottom-sheet'
import * as Radio from '@klook/klook-ui/lib/radio'

import '@klook/klook-ui/lib/modal/style'
import '@klook/klook-ui/lib/radio/style'
import '@klook/klook-ui/lib/form/style'
import '@klook/klook-ui/lib/button/style'
import '@klook/klook-ui/lib/checkbox/style'
import '@klook/klook-ui/lib/bottom-sheet/style'

import baseMixin from '../../mixins/baseMixin';

export default {
  name: "InvoiceForm",
  inject: ['__t'],
  mixins: [baseMixin],
  components:{
    kCheckbox: Checkbox.default,
    kRadio: Radio.Radio,
    kRadioGroup: Radio.RadioGroup,
    kModal: Modal.default,
    kForm: Form.Form,
    kFormItem: Form.FormItem,
    kInput: Input.default,
    kButton: Button.default,
    kBottomSheet: BottomSheet.BottomSheet
  },
  props: {
    showForm: {
      type: Boolean,
      default: false,
    },
    basicInfo: {
      type: Object,
      default: () => ({}),
    },
    invoiceInfo: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      showCarrier: false,
      invoiceForm: {
        customer_type: 1,
        email: "",
        carrier_type: 0,
        carrier_content: "",
        company_name: "",
        company_code: "",
      },
    };
  },
  watch: {
    showForm(val) {
      if (val) {
        this.invoiceForm = { ...this.invoiceInfo };
        this.showCarrier = this.invoiceForm.carrier_content ? true : false;
      }
    },
  },
  computed: {
    isMobile() {
      return this.realPlatform === "mobile";
    },
    compAttrs() {
      const def = { title: this.__t("167554") };
      return this.isMobile
        ? {
            ...def,
            showClose: true,
            visible: this.showForm,
          }
        : {
            ...def,
            open: this.showForm,
            scrollable: true,
          };
    },
    isMobileCarrier() {
      return this.invoiceForm.carrier_type === 1;
    },
    isPersonalCustomer() {
      return this.invoiceForm.customer_type === 1;
    },
    rules() {
      return {
        email: [
          {
            required: true,
            message: this.__t("171536"),
          },
          {
            pattern:
              /^[a-zA-Z0-9_-]+(\.([a-zA-Z0-9_-])+)*@[a-zA-Z0-9_-]+[.][a-zA-Z0-9_-]+([.][a-zA-Z0-9_-]+)*$/,
            message: this.__t("171536"),
          },
        ],
        company_name: [
          {
            required: !this.isPersonalCustomer,
            message: this.__t("167562"),
          },
        ],
        company_code: [
          {
            required: !this.isPersonalCustomer,
            message: this.__t("167564"),
          },
          {
            pattern: /^[0-9]{8}$/,
            message: this.__t("167564"),
          },
        ],
        carrier_content: [
          {
            required: this.showCarrier,
            message: this.isMobileCarrier
              ? this.__t("171537")
              : this.__t("171538"),
          },
          ...(this.isMobileCarrier
            ? [
                {
                  pattern: /^\/[0-9A-Z+-.]{7}$/,
                  message: this.__t("171537"),
                },
              ]
            : [
                {
                  pattern: /^[A-Za-z]{2}[0-9]{14}$/,
                  message: this.__t("171538"),
                },
              ]),
        ],
      };
    },
  },
  methods: {
    selectCustomerType(id) {
      this.invoiceForm.customer_type = id;
    },
    handleShowCarrierClick(val) {
      if (val) {
        this.invoiceForm.carrier_type = 1;
      } else {
        this.invoiceForm.carrier_type = 0;
        this.invoiceForm.carrier_content = "";
      }
      if (this.realInhouse) {
        this.realInhouse.updateBinding(
          document.querySelector(".show-carrier-checkbox"),
          {
            ext: {
              Status: val ? "BeSelected" : "BeCanceled",
            },
          }
        );
        setTimeout(() => {
          this.realInhouse.track("action", ".show-carrier-checkbox");
        }, 0);
      }
    },
    handleCarrierTypeChange() {
      this.invoiceForm.carrier_content = "";
    },
    resetFields() {
      this.$refs.invoiceFormRef.resetFields();
    },
    handleClose() {
      this.resetFields();
      this.$emit("close");
    },
    handleCancel() {
      this.resetFields();
      this.$emit("cancel");
    },
    formatInvoiceForm() {
      if (this.invoiceForm.customer_type === 1) {
        // 个人
        this.invoiceForm.company_name = "";
        this.invoiceForm.company_code = "";
      } else {
        // 公司
        this.invoiceForm.carrier_type = 0;
        this.invoiceForm.carrier_content = "";
      }

      return this.invoiceForm;
    },
    handleSubmit() {
      this.$refs.invoiceFormRef.validate((pass, failObj) => {
        this.trackSaveStatus(pass, failObj);
        if (pass) {
          this.$emit("saveSuccess", {
            success: true,
            result: this.formatInvoiceForm(),
          });
        } else {
          this.$emit("saveFail", {
            success: false,
            result: this.formatInvoiceForm(),
          });
        }
      });
    },
    trackSaveStatus(pass, failObj) {
      this.realInhouse &&
        this.realInhouse.track("custom", "body", {
          spm: "InvoiceReceipt_Edit.SaveStatus",
          ext: {
            InvoiceReceiptTypeID: this.invoiceForm.customer_type,
            SaveStatus: pass ? "Success" : "Fail",
            FailAttribute: !pass
              ? this.getCommaSeparatedFields(failObj)
              : undefined,
          },
        });
    },
    getCommaSeparatedFields(failObj) {
      return Object.values(failObj)
        .map((item) => {
          const field = item[0]?.field;
          if (field === "carrier_content") {
            return this.invoiceForm.carrier_type === 1
              ? "mobile_barcode_carrier"
              : "natural_person_carrier";
          }
          return field;
        })
        .filter((field) => field)
        .join(", ");
    },
  },
  created() {
    this.invoiceForm.email = this.basicInfo.email;
  },
};
</script>

<style lang="scss" scoped>

.invoice-form-desktop {
  @media (any-hover: hover) {
    ::v-deep .klk-input:hover .klk-input-inner {
        border-color: $color-brand-primary;
    }
  }
}

.invoice-form-mobile{
  ::v-deep .klk-input-is-focus .klk-input-inner{
    border-color: $color-brand-primary;
  }
}

.cust-type-radio-group {
  display: flex;
  justify-content: space-between;
}

.cust-type-radio {
  width: 100%;
  border: 1px solid $color-border-dim;
  border-radius: $radius-xl;
  padding: 4px 16px;
  cursor: pointer;

  &:first-of-type {
    margin-right: 16px;
  }

  &--selected {
    border-color: $color-brand-primary;
  }
}

.show-carrier-checkbox {
  ::v-deep .klk-checkbox-label {
    @include font-body-m-regular();
    color: $color-text-primary;
  }
}

.carrier-type-box {
  background-color: $color-bg-2;
  border-radius: $radius-xl;
  padding: 16px;
  margin-top: 12px;

  .carrier-content-item {
    margin-left: 32px;
  }
}

.carrier-type-radio-group {
  display: flex;
  flex-direction: column;
}

.tips {
  @include font-body-s-regular();
  color: $color-text-secondary;
  margin-top: 16px;
}

.invoice-form-mobile {
  .form-footer {
    display: flex;
    justify-content: flex-end;
  }

  .form-footer-confirm,
  .form-footer-cancel {
    flex: 1;
  }

  .form-footer-confirm {
    margin-left: 8px;
  }
}

.invoice-form-desktop {
  .invoice-form-content {
    margin-top: 14px;
  }

  .form-footer {
    margin-top: 24px;
    display: flex;
    justify-content: flex-end;
  }

  .form-footer-confirm {
    margin-left: 16px;
  }
}
</style>
