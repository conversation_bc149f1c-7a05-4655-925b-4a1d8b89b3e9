<template>
  <component
    v-if="showDetailsModal"
    :is="isMobile ? 'k-bottom-sheet' : 'k-modal'"
    v-bind="compAttrs"
    v-on="compListeners"
    :class="`invoice-details-${realPlatform}`"
    :data-spm-page="`InvoiceReceipt_Info?ext=${JSON.stringify({
      InvoiceReceiptStatusID: (invoiceDetail.status && invoiceDetail.status.id) || '',
    })}`"
    @close="handleClose"
  >
    <div class="klk-invoice-details">
      <div v-if="invoiceDetail.status" class="status-card">
        <div
          class="status-title"
          :style="{ color: invoiceDetail.status.text_color }"
        >
          {{ invoiceDetail.status.text }}
        </div>
        <div
          v-if="invoiceDetail.status.desc_info"
          class="status-desc"
          :style="{ color: invoiceDetail.status.desc_info.text_color }"
        >
          {{ invoiceDetail.status.desc_info.text }}
        </div>
      </div>

      <div
        v-for="(detail, index) in invoiceDetail.details"
        :key="index"
        class="invoice-info"
      >
        <div
          :class="isMobile ? 'title-mobile' : 'title-desktop'"
          :style="{ color: detail.title.text_color }"
        >
          {{ detail.title.text }}
        </div>
        <div
          v-for="(item, index) in detail.content_list"
          :key="index"
          class="info-content-wrapper"
        >
          <div class="info-content"> 
            <div
              class="info-label"
              :style="{ color: item.field_name.text_color }"
            >
              {{ item.field_name.text }}
            </div>
            <div class="info-value" :style="{ color: item.field_val.text_color }">
              <div>
                {{
                  item.field_name.name === "email_name" && showMaskedEmail
                    ? item.field_val.text_mask
                    : item.field_val.text
                }}
              </div>
              <KlkMaskIcon
                v-if="item.field_name.name === 'email_name'"
                ref="mask"
                class="invoice-mask-icon"
                :size="16"
                :on-change="handleMaskChange"
              />
            </div>
          </div>
          <div 
            v-if="item.field_tips"
            class="info-tips"
            :style="{
              'background-color': item.field_tips.bg_color
            }"
          >
            <span 
              class="info-tips-triangle"  
              :style="{
                'background-color': item.field_tips.bg_color
              }"
            >
            </span>
            <div v-html="item.field_tips.text"></div>
          </div>
        </div>
      </div>

      <div
        v-if="invoiceDetail.tips"
        class="tips"
        :style="{ color: invoiceDetail.tips.text_color }"
      >
        {{ invoiceDetail.tips.text }}
      </div>
    </div>

    <k-button slot="footer" :block="isMobile" @click="handleClose">{{
      __t("167589")
    }}</k-button>
  </component>
</template>

<script>
import baseMixin from "../../mixins/baseMixin";

import * as Button from '@klook/klook-ui/lib/button'
import * as Modal from '@klook/klook-ui/lib/modal'
import * as BottomSheet from '@klook/klook-ui/lib/bottom-sheet'

import '@klook/klook-ui/lib/modal/style'
import '@klook/klook-ui/lib/button/style'
import '@klook/klook-ui/lib/bottom-sheet/style'

export default {
  name: "InvoiceDetails",
  inject: ['__t'],
  components: {
    kModal: Modal.default,
    kButton: Button.default,
    kBottomSheet: BottomSheet.BottomSheet,
    KlkMaskIcon: () =>
      Promise.all([
        import("@klook/klk-mask-icon"),
        import("@klook/klk-mask-icon/esm/index.css"),
      ]).then(([component]) => component),
  },
  mixins: [baseMixin],
  props: {
    showDetailsModal: {
      type: Boolean,
      default: false,
    },
    invoiceDetail: {
      type: Object,
      default: () => ({}),
    },
  },
  computed: {
    isMobile() {
      return this.realPlatform === "mobile";
    },
    compAttrs() {
      const def = { title: this.__t("167576") };
      return this.isMobile
        ? {
            ...def,
            showClose: true,
            visible: this.showDetailsModal,
          }
        : {
            ...def,
            open: this.showDetailsModal,
            scrollable: true
          };
    },
    compListeners() {
      return this.isMobile
        ? {}
        : {
            "on-confirm": this.handleClose,
          };
    },
  },
  data() {
    return {
      showMaskedEmail: true,
    };
  },
  methods: {
    handleMaskChange({ status }) {
      this.showMaskedEmail = status === 1 ? false : true;
    },
    handleClose() {
      this.showMaskedEmail = true;
      this.$emit("close");
    },
  },
};
</script>

<style lang="scss" scoped>
.status-card {
  border-radius: $radius-xl;
  padding: 16px;
  background-color: $color-bg-2;

  .status-title {
    @include font-heading-s();
  }

  .status-desc {
    @include font-body-m-regular();
    margin-top: 12px;
  }
}

.invoice-info {
  margin-top: 20px;

  .title-desktop {
    @include font-heading-xs();
    margin-bottom: 8px;
  }

  .title-mobile {
    @include font-body-m-bold();
  }
}


.info-content-wrapper {
  .info-tips {
    padding: 8px;
    border-radius: $radius-s;
    font-size: 14px;
    line-height: 21px;
    margin-top: 8px;
    position: relative;

    .info-tips-triangle {
      position: absolute;
      top: -6px;
      right: 10px;
      width: 12px;
      height: 12px;
      transform: rotate(45deg);
    }
  }
}
.info-content {
  @include font-body-m-regular();
  display: flex;
  justify-content: space-between;
  margin-top: 16px;

  .info-value {
    display: flex;
    align-items: center;
  }

  .invoice-mask-icon {
    margin-left: 8px;
  }
}

.tips {
  @include font-body-s-regular();
  margin-top: 20px;
}

.invoice-details-desktop {
  ::v-deep .klk-modal-footer {
    display: flex;
    justify-content: flex-end;
    padding-top: 24px;
  }
}
</style>
