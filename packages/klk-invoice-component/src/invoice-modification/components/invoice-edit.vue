<template>
  <component
    v-if="showEditModal"
    :is="isMobile ? 'k-bottom-sheet' : 'k-modal'"
    :class="`invoice-edit-${realPlatform}`"
    v-bind="compAttrs"
    :data-spm-page="`InvoiceReceipt_Modify?ext=${JSON.stringify({
      InvoiceReceiptStatusID: (invoiceDetail.status && invoiceDetail.status.id) || '',
    })}`"
    @close="handleClose"
  >
    <k-form ref="invoiceEditFormRef" :model="invoiceEditForm" :rules="rules">
      <div v-if="!isMobile" class="tips-desktop">{{ __t("167565") }}</div>

      <div class="invoice-fill-info">
        <k-form-item :label="__t('167549')" prop="email">
          <k-input
            v-model="invoiceEditForm.email"
            :placeholder="__t('167555')"
            :disabled="submittingForm"
          ></k-input>
        </k-form-item>

        <k-form-item :label="__t('167561')" prop="company_name">
          <k-input
            v-model="invoiceEditForm.company_name"
            :placeholder="__t('167562')"
            :disabled="submittingForm"
          ></k-input>
        </k-form-item>

        <k-form-item :label="__t('167563')" prop="company_code">
          <k-input
            v-model="invoiceEditForm.company_code"
            :placeholder="__t('167564')"
            :maxlength="8"
            :disabled="submittingForm"
          ></k-input>
        </k-form-item>
      </div>
    </k-form>

    <div slot="footer" class="form-footer">
      <div v-if="isMobile" class="tips-mobile">{{ __t("167565") }}</div>

      <div class="form-footer-buttons">
        <k-button
          type="outlined"
          class="form-footer-cancel"
          :disabled="submittingForm"
          :data-spm-module="`CancelBtn?ext=${JSON.stringify({
            InvoiceReceiptTypeID: customerType,
          })}`"
          data-spm-virtual-item="__virtual"
          v-galileo-click-tracker="{ spm: 'CancelBtn', componentName: 'klk-invoice-component' }"
          @click="handleCancel"
        >
          {{ __t("167567") }}
        </k-button>
        <k-button
          type="primary"
          class="form-footer-confirm"
          :loading="submittingForm"
          :data-spm-module="`SubmitBtn?ext=${JSON.stringify({
            InvoiceReceiptTypeID: customerType,
          })}`"
          data-spm-virtual-item="__virtual"
          v-galileo-click-tracker="{ spm: 'SubmitBtn', componentName: 'klk-invoice-component' }"
          @click="handleSubmit"
          >{{ __t("167568") }}</k-button
        >
      </div>
    </div>
  </component>
</template>

<script>

import * as Button from '@klook/klook-ui/lib/button'
import * as Modal from '@klook/klook-ui/lib/modal'
import * as Input from '@klook/klook-ui/lib/input'
import * as Form from '@klook/klook-ui/lib/form'
import * as BottomSheet from '@klook/klook-ui/lib/bottom-sheet'

import '@klook/klook-ui/lib/modal/style'
import '@klook/klook-ui/lib/input/style'
import '@klook/klook-ui/lib/button/style'
import '@klook/klook-ui/lib/bottom-sheet/style'
import '@klook/klook-ui/lib/form/style'

import baseMixin from "../../mixins/baseMixin";

export default {
  name: "InvoiceEdit",
  components: {
    kModal: Modal.default,
    kForm: Form.Form,
    kFormItem: Form.FormItem,
    kInput: Input.default,
    kButton: Button.default,
    kBottomSheet: BottomSheet.BottomSheet
  },
  inject: ["__t"],
  mixins: [baseMixin],
  props: {
    showEditModal: {
      type: Boolean,
      default: false,
    },
    invoiceDetail: {
      type: Object,
      default: () => ({}),
    },
  },
  computed: {
    isMobile() {
      return this.realPlatform === "mobile";
    },
    compAttrs() {
      const def = { title: this.__t("167586") };
      return this.isMobile
        ? {
            ...def,
            showClose: true,
            visible: this.showEditModal,
          }
        : {
            ...def,
            open: this.showEditModal,
            scrollable: true,
          };
    },
    rules() {
      return {
        email: [
          {
            required: true,
            message: this.__t("171536"),
          },
          {
            pattern:
              /^[a-zA-Z0-9_-]+(\.([a-zA-Z0-9_-])+)*@[a-zA-Z0-9_-]+[.][a-zA-Z0-9_-]+([.][a-zA-Z0-9_-]+)*$/,
            message: this.__t("171536"),
          },
        ],
        company_name: [
          {
            required: true,
            message: this.__t("167562"),
          },
        ],
        company_code: [
          {
            required: true,
            message: this.__t("167564"),
          },
          {
            pattern: /^[0-9]{8}$/,
            message: this.__t("167564"),
          },
        ],
      };
    },
    customerType() {
      return this.invoiceDetail.customer_type_info.customer_value.customer_type;
    },
  },
  data() {
    return {
      submittingForm: false,
      invoiceEditForm: {
        company_name: "",
        company_code: "",
        email: "",
      },
    };
  },
  methods: {
    handleClose() {
      this.$refs.invoiceEditFormRef.resetFields();
      this.$emit("close");
    },
    handleCancel() {
      this.$refs.invoiceEditFormRef.resetFields();
      this.$emit("cancel");
    },
    handleSubmit() {
      this.$refs.invoiceEditFormRef.validate(async (pass, failObj) => {
        if (pass) {
          try {
            this.submittingForm = true;
            this.invoiceEditForm = {
              ...this.invoiceEditForm,
              order_no: this.invoiceDetail.order_no,
            };
            const res = await this.realAxios.$post(
              "/v1/order/invoices/change_invoice",
              this.invoiceEditForm
            );
            this.submittingForm = false;
            if (res.success) {
              this.trackSubmitStatus(true);
              this.$toast({
                message: this.__t("171541"),
                icon: "icon_feedback_success",
                iconColor: "white",
              });
              this.$emit("submitSuccess", res);
            } else {
              this.trackSubmitStatus(false);
              res.error?.code === "01005001005"
                ? this.$toast(this.__t("173479"))
                : this.$toast(this.__t("171539"));
              this.$emit("submitFail", res);
            }
          } catch (error) {
            this.trackSubmitStatus(false);
            this.submittingForm = false;
            this.$toast(this.__t("171539"));
          }
        } else {
          this.trackSubmitStatus(false, failObj);
        }
      });
    },
    trackSubmitStatus(success, failObj) {
      this.realInhouse &&
        this.realInhouse.track("custom", "body", {
          spm: "InvoiceReceipt_Modify.SubmitStatus",
          ext: {
            InvoiceReceiptTypeID: this.customerType,
            SubmitStatus: success ? "Success" : "Fail",
            FailAttribute:
              !success && failObj
                ? this.getCommaSeparatedFields(failObj)
                : undefined,
          },
        });
    },
    getCommaSeparatedFields(failObj) {
      return Object.values(failObj)
        .map((item) => item[0]?.field)
        .filter((field) => field)
        .join(", ");
    },
  },
  created() {
    this.invoiceEditForm.email =
      (this.invoiceDetail.details || [])
        .flatMap((detail) => detail.content_list)
        .find((content) => content.field_name.name === "email_name")?.field_val
        .text || "";
  },
};
</script>

<style lang="scss" scoped>
.tips-desktop {
  @include font-body-m-regular();
  color: $color-text-secondary;
  margin-bottom: 24px;
}

.tips-mobile {
  @include font-body-m-regular();
  background-color: $color-caution-background;
  color: $color-text-primary;
  padding: 12px 16px;
}

.invoice-edit-mobile {
  ::v-deep .klk-bottom-sheet-inner .klk-bottom-sheet-footer {
    padding: 0;
  }
}

.invoice-edit-mobile {
  .form-footer-buttons {
    display: flex;
    justify-content: flex-end;
    padding: 16px 20px 8px;
  }

  .form-footer-confirm,
  .form-footer-cancel {
    flex: 1;
  }

  .form-footer-confirm {
    margin-left: 8px;
  }
}

.invoice-edit-desktop {
  .form-footer {
    margin-top: 24px;
    display: flex;
    justify-content: flex-end;
  }

  .form-footer-confirm {
    margin-left: 16px;
  }
}
</style>
