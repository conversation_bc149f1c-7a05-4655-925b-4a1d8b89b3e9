<template>
  <div class="klk-invoice-modification">
    <div
      class="invoice-info-card"
      :data-spm-module="`InvoiceReceipt?ext=${JSON.stringify({
        InvoiceReceiptStatusID: (invoiceDetail.status && invoiceDetail.status.id) || '',
      })}`"
      data-spm-virtual-item="__virtual"
      v-galileo-click-tracker="{ spm: 'InvoiceReceipt', componentName: 'klk-invoice-component' }"
    >
      <div class="invoice-info-title">{{ __t("167545") }}</div>

      <!-- 状态 -->
      <div v-if="invoiceDetail.status" class="invoice-info-status">
        <div class="status-label">{{ __t("173260") }}</div>
        <div
          class="status-text"
          :style="{ color: invoiceDetail.status.text_color }"
        >
          {{ invoiceDetail.status.text }}
        </div>
      </div>

      <!-- 类型 -->
      <div class="invoice-info-type">
        <div
          class="content-label"
          :style="{
            color: invoiceDetail.customer_type_info.customer_title.text_color,
          }"
        >
          {{ invoiceDetail.customer_type_info.customer_title.text }}
        </div>
        <div class="content-value">
          <div
            :style="{
              color: invoiceDetail.customer_type_info.customer_value.text_color,
            }"
          >
            {{ invoiceDetail.customer_type_info.customer_value.text }}
          </div>
          <div
            v-if="invoiceDetail.customer_type_info.customer_value.is_edit"
            class="content-action edit"
            :data-spm-module="`ModifyBtn?ext=${JSON.stringify({
              InvoiceReceiptStatusID: (invoiceDetail.status && invoiceDetail.status.id) || '',
            })}`"
            data-spm-virtual-item="__virtual"
            v-galileo-click-tracker="{ spm: 'ModifyBtn', componentName: 'klk-invoice-component' }"
            @click="showEditModal = true"
          >
            {{ __t("167573") }}
          </div>
          <component
            v-if="invoiceDetail.customer_type_info.customer_value.is_overdue"
            :is="overdueComp"
            v-bind="overdueCompAttrs"
          >
            <IconInformation
              class="content-action"
              theme="outline"
              size="16"
              :fill="colorTextPrimary"
              @click.native="openExpirePopup"
            />
          </component>
        </div>
      </div>

      <!-- 开票失败提示 -->
      <div
        v-if="invoiceDetail.status_tips"
        class="invoice-failure-tips"
        :style="{
          'background-color': invoiceDetail.status_tips.bg_color
        }"
      >
        <span
          class="invoice-failure-tips-triangle"
          :style="{
            'background-color': invoiceDetail.status_tips.bg_color
          }"
        >
        </span>
        <div v-html="invoiceDetail.status_tips.text"></div>
      </div>

      <!-- 详情弹窗入口 -->
      <div class="invoice-details-entry">
        <span @click="showDetailsModal = true">
          {{ __t("173261") }}
        </span>
      </div>
    </div>
    <k-bottom-sheet
      :visible.sync="showExpiredPopup"
      :title="__t('171650')"
      show-close
      @close="showExpiredPopup = false"
    >
      {{ overdueText }}
    </k-bottom-sheet>
    <invoice-details
      :show-details-modal="showDetailsModal"
      :platform="realPlatform"
      :invoice-detail="invoiceDetail"
      @close="handleClose"
    />
    <invoice-edit
      :show-edit-modal="showEditModal"
      :platform="realPlatform"
      :invoice-detail="invoiceDetail"
      @close="handleClose"
      @cancel="handleCancel"
      @submitSuccess="submitSuccess"
      @submitFail="submitFail"
    />
  </div>
</template>

<script>
import { IconNext, IconInformation } from "@klook/klook-icons";
import { $colorTextPrimary } from "@klook/klook-ui/lib/utils/design-token-esm";

import * as Poptip from '@klook/klook-ui/lib/poptip'
import * as BottomSheet from '@klook/klook-ui/lib/bottom-sheet'

import '@klook/klook-ui/lib/poptip/style'
import '@klook/klook-ui/lib/bottom-sheet/style'

import baseMixin from "../mixins/baseMixin"
import localeMixin from "../mixins/localeMixin"
import InvoiceDetails from "./components/invoice-details.vue";
import InvoiceEdit from "./components/invoice-edit.vue";

export default {
  name: "InvoiceModification",
  components: {
    IconNext,
    IconInformation,
    InvoiceDetails,
    InvoiceEdit,
    kBottomSheet: BottomSheet.BottomSheet,
    kPoptip: Poptip.default
  },
  mixins: [baseMixin, localeMixin],
  props: {
    orderNo: {
      type: String,
      default: "",
    },
    invoiceDetail: {
      type: Object,
      default: null,
    },
  },
  computed: {
    isMobile() {
      return this.realPlatform === "mobile";
    },
    overdueComp() {
      return this.isMobile ? "div" : "k-poptip";
    },
    overdueCompAttrs() {
      return this.isMobile
        ? {}
        : {
            dark: true,
            content: this.overdueText,
            preventOverflow: true,
            placement: "left",
          };
    },
    overdueText() {
      return (
        this.invoiceDetail.customer_type_info.customer_value.overdue_info
          ?.text || ""
      );
    },
  },
  data() {
    return {
      colorTextPrimary: $colorTextPrimary,
      showExpiredPopup: false,
      showDetailsModal: false,
      showEditModal: false,
    };
  },
  methods: {
    openExpirePopup() {
      if (this.isMobile) {
        this.showExpiredPopup = true;
      }
    },
    handleClose() {
      this.showDetailsModal = false;
      this.showEditModal = false;
      this.$emit("close");
    },
    handleCancel() {
      this.showEditModal = false;
      this.$emit("cancel");
    },
    async submitSuccess(res) {
      this.showEditModal = false;
      this.$emit("submitSuccess", res);
    },
    submitFail(res) {
      this.$emit("submitFail", res);
    },
  },
};
</script>

<style lang="scss" scoped>
.invoice-info-card {
  width: 100%;
  background-color: $color-bg-1;
  border-radius: $radius-xl;
  padding: 16px;
}

.invoice-failure-tips {
  padding: 8px;
  border-radius: $radius-s;
  font-size: 14px;
  line-height: 21px;
  margin-top: 8px;
  position: relative;

  .invoice-failure-tips-triangle {
    position: absolute;
    top: -6px;
    right: 30px;
    width: 12px;
    height: 12px;
    transform: rotate(45deg);
  }
}

.invoice-info-status,
.invoice-info-type {
  @include font-body-m-regular();
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.invoice-info-title {
  @include font-heading-xs();
  color: $color-text-primary;
}

.invoice-info-status {
  margin: 24px 0 8px;

  .status-label {
    color: $color-text-secondary;
  }
}

.content-value {
  display: flex;
  align-items: center;
}

.invoice-details-entry {
  @include font-body-m-regular();
  color: $color-text-primary;
  margin-top: 16px;

  span {
    text-decoration: underline;
    cursor: pointer;
  }
}

.content-action {
  margin-left: 8px;
  cursor: pointer;

  &.edit {
    text-decoration: underline;
  }
}
</style>
