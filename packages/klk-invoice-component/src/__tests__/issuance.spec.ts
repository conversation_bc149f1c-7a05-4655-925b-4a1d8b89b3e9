import { shallowMount } from '@vue/test-utils'
import InvoiceIssuance from '../invoice-issuance/index.vue'

describe('InvoiceIssuance', () => {
  let wrapper

  beforeEach(() => {
    wrapper = shallowMount(InvoiceIssuance, {
      propsData: {
        basicInfo: {
          customer_type: 1,
          email: '',
          carrier_type: 1,
          carrier_content: '',
          company_name: '',
          company_code: ''
        }
      }
    })
  })

  afterEach(() => {
    wrapper.destroy()
  })

  test('should render the component', () => {
    expect(wrapper.exists()).toBe(true)
  })

  test('should have correct initial data', () => {
    expect(wrapper.vm.showForm).toBe(false)
    expect(wrapper.vm.invoiceInfo.success).toBe(false)
    expect(wrapper.vm.invoiceInfo.result.email).toEqual('')
    expect(wrapper.vm.invoiceInfo.result.customer_type).toEqual(1)
    expect(wrapper.vm.invoiceInfo.result.carrier_content).toEqual('')
    expect(wrapper.vm.invoiceInfo.result.company_name).toEqual('')
    expect(wrapper.vm.invoiceInfo.result.company_code).toEqual('')
  })

  // close 事件
  test('should handle close event', () => {
    wrapper.vm.handleClose()
    expect(wrapper.vm.showForm).toBe(false)
    // 检查是否触发了
    expect(wrapper.emitted('close')).toHaveLength(1)
  })

  //  cancel 事件
  test('should handle cancel event', () => {
    wrapper.vm.handleCancel()
    expect(wrapper.vm.showForm).toBe(false)
    // 检查是否触发了
    expect(wrapper.emitted('cancel')).toHaveLength(1)
  })

  // 获取数据 getInvoiceInfo customer_type为1的时候，不返回 company_name company_code
  test('should handle getInvoiceInfo event', () => {
    wrapper.setData({
      invoiceInfo: {
        success: false,
        result: {
          customer_type: 1,
          email: '<EMAIL>',
          carrier_type: 1,
          carrier_content: 'update carrier_content',
          company_name: 'update company_name',
          company_code: 'update company_code'
        }
      }
    })
    const res = wrapper.vm.getInvoiceInfo()
    expect(wrapper.vm.showForm).toBe(false)
    expect(res.success).toBe(false)
    expect(res.result.email).toEqual('<EMAIL>')
    expect(res.result.customer_type).toEqual(1)
    expect(res.result.carrier_type).toEqual(1)
    expect(res.result.carrier_content).toEqual('update carrier_content')
    expect(res.result.company_name).toEqual('')
    expect(res.result.company_code).toEqual('')
  })

  // 获取数据 getInvoiceInfo customer_type
  test('should handle getInvoiceInfo event', () => {
    wrapper.setData({
      invoiceInfo: {
        success: false,
        result: {
          customer_type: 2,
          email: '<EMAIL>',
          carrier_type: 1,
          carrier_content: 'update carrier_content',
          company_name: 'update company_name',
          company_code: 'update company_code'
        }
      }
    })
    const res = wrapper.vm.getInvoiceInfo()
    expect(wrapper.vm.showForm).toBe(false)
    expect(res.success).toBe(false)
    expect(res.result.email).toEqual('<EMAIL>')
    expect(res.result.customer_type).toEqual(2)
    expect(res.result.carrier_type).toEqual(1)
    expect(res.result.carrier_content).toEqual('update carrier_content')
    expect(res.result.company_name).toEqual('update company_name')
    expect(res.result.company_code).toEqual('update company_code')
  })

  // 检查保存成功的事件
  test('should handle saveSuccess event', () => {
    const res = {
      success: true,
      result: {
        customer_type: 1,
        email: '<EMAIL>',
        carrier_type: 0,
        carrier_content: 'xxxx',
        company_name: 'test',
        company_code: 'test111'
      }
    }

    wrapper.vm.saveSuccess(res)
    expect(wrapper.vm.showForm).toBe(false)
    expect(wrapper.vm.invoiceInfo.success).toBe(true)
    expect(wrapper.vm.invoiceInfo.result.email).toEqual('<EMAIL>')
    expect(wrapper.vm.invoiceInfo.result.customer_type).toEqual(1)
    expect(wrapper.vm.invoiceInfo.result.carrier_type).toEqual(0)
    expect(wrapper.vm.invoiceInfo.result.carrier_content).toEqual('xxxx')
    expect(wrapper.vm.invoiceInfo.result.company_name).toEqual('test')
    expect(wrapper.vm.invoiceInfo.result.company_code).toEqual('test111')
    // 检查是否触发了 saveSuccess 事件
    expect(wrapper.emitted('saveSuccess')).toHaveLength(1)
  })

  // 检查是否触发了 saveFail 事件
  test('should handle saveFail event', () => {
    // 打开页面，输入数据
    wrapper.setData({
      showForm: true,
      invoiceInfo: {
        success: false,
        result: {
          customer_type: 1,
          email: '<EMAIL>',
          carrier_type: 1,
          carrier_content: 'update carrier_content',
          company_name: 'update company_name',
          company_code: 'update company_code'
        }
      }
    })

    const res = {
      // 模拟保存失败的结果
      success: false,
      result: {
        customer_type: 1,
        email: '<EMAIL>',
        carrier_type: 1,
        carrier_content: 'update carrier_content',
        company_name: 'update company_name',
        company_code: 'update company_code'
      }
    }

    wrapper.vm.saveFail(res)
    expect(wrapper.emitted('saveFail')).toHaveLength(1)
    expect(wrapper.vm.showForm).toBe(true)
    expect(wrapper.vm.invoiceInfo.success).toBe(false)
    expect(wrapper.vm.invoiceInfo.result.email).toEqual('<EMAIL>')
    expect(wrapper.vm.invoiceInfo.result.customer_type).toEqual(1)
    expect(wrapper.vm.invoiceInfo.result.carrier_type).toEqual(1)
    expect(wrapper.vm.invoiceInfo.result.carrier_content).toEqual('update carrier_content')
    expect(wrapper.vm.invoiceInfo.result.company_name).toEqual('update company_name')
    expect(wrapper.vm.invoiceInfo.result.company_code).toEqual('update company_code')
  })

  test('should update traveler info email', () => {
    const email = '<EMAIL>'
    wrapper.vm.updateTravelerInfoEmail(email)
    expect(wrapper.vm.showForm).toBe(false)
    // 检查 invoiceInfo 中 email 是否更新
    expect(wrapper.vm.invoiceInfo.result.email).toEqual(email)
  })
})
