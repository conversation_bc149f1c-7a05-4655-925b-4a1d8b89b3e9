import { shallowMount } from '@vue/test-utils'
import InvoiceModification from '../invoice-modification/index.vue'

describe('InvoiceModification', () => {
  let wrapper

  beforeEach(() => {
    wrapper = shallowMount(InvoiceModification, {
      propsData: {
        platform: 'mobile',
        orderNo: 'testOrderNo',
        invoiceDetail: {
          status: {
            id: 1,
            text: 'Test Status',
            text_color: 'red'
          },
          customer_type_info: {
            customer_title: {
              text: 'Test Customer Title',
              text_color: 'blue'
            },
            customer_value: {
              text: 'Test Customer Value',
              text_color: 'green',
              is_edit: true,
              is_overdue: false
            }
          }
        }
      }
    })
  })

  afterEach(() => {
    wrapper.destroy()
  })

  test('should render the component', () => {
    expect(wrapper.exists()).toBe(true)
  })

  test('should have the correct invoice info card data attributes', () => {
    const invoiceInfoCard = wrapper.find('.invoice-info-card')
    expect(invoiceInfoCard.attributes('data-spm-module')).toEqual(
      `InvoiceReceipt?ext=${JSON.stringify({
        InvoiceReceiptStatusID: 1
      })}`
    )
    expect(invoiceInfoCard.attributes('data-spm-virtual-item')).toEqual('__virtual')
  })

  test('should display the correct status text and color', () => {
    const statusText = wrapper.find('.status-text')
    expect(statusText.text()).toEqual('Test Status')
    expect(statusText.element.style.color).toEqual('red')
  })

  test('should display the correct customer type info', () => {
    const customerTitle = wrapper.find('.content-label')
    const customerValue = wrapper.find('.content-value div')
    expect(customerTitle.text()).toEqual('Test Customer Title')
    expect(customerTitle.element.style.color).toEqual('blue')
    expect(customerValue.text()).toEqual('Test Customer Value')
    expect(customerValue.element.style.color).toEqual('green')
  })

  test('should have the edit button and click event', () => {
    const editButton = wrapper.find('.edit')
    expect(editButton.exists()).toBe(true)
    editButton.trigger('click')
    expect(wrapper.vm.showEditModal).toBe(true)
  })

  test('should have the details entry and click event', () => {
    const detailsEntry = wrapper.find('.invoice-details-entry span')
    expect(detailsEntry.exists()).toBe(true)
    detailsEntry.trigger('click')
    expect(wrapper.vm.showDetailsModal).toBe(true)
  })

  // close 事件
  test('should handle close event', () => {
    wrapper.vm.handleClose()
    expect(wrapper.vm.showDetailsModal).toBe(false)
    expect(wrapper.vm.showEditModal).toBe(false)
    // 检查是否触发了
    expect(wrapper.emitted('close')).toHaveLength(1)
  })

  //  cancel 事件
  test('should handle cancel event', () => {
    wrapper.vm.handleCancel()
    expect(wrapper.vm.showEditModal).toBe(false)
    // 检查是否触发了
    expect(wrapper.emitted('cancel')).toHaveLength(1)
  })

  // submitSuccess
  test('should handle submitSuccess event', async () => {
    await wrapper.vm.submitSuccess()
    expect(wrapper.vm.showEditModal).toBe(false)
    // 检查是否触发了
    expect(wrapper.emitted('submitSuccess')).toHaveLength(1)
  })

  // submitFail
  test('should handle submitFail event', async () => {
    await wrapper.vm.submitFail()
    expect(wrapper.vm.showEditModal).toBe(false)
    // 检查是否触发了
    expect(wrapper.emitted('submitFail')).toHaveLength(1)
  })

  // openExpirePopup realPlatform
  test('should handle openExpirePopup event', () => {
    // 默认没有打开
    expect(wrapper.vm.showExpiredPopup).toBe(false)
    wrapper.vm.openExpirePopup()
    expect(wrapper.vm.showExpiredPopup).toBe(true)
  })
})
