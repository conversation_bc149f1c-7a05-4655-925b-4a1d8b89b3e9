import { definedConfig } from '@klook/klook-builder/lib/builder/helper'
import pkg from './package.json'

export default definedConfig({
  entry: './src/index.ts',
  type: 'rollup',
  // @ts-ignore
  external(id) {
    const externalList = Object.keys(pkg.peerDependencies)
    return externalList.some((externalItem) => {
      const regStr = new RegExp(`(^${externalItem}$)|(^${externalItem}/.*)`)
      return regStr.test(id)
    })
  },
  // 如果你需要将打包后的文件放到其他地方，可以通过 outputPath 指定
  // outputPath: '/Users/<USER>/Documents/projects/user-center/node_modules/@klook/klk-invoice-component/dist',

  // 通过对象形式可以覆盖公共配置
  format: {
    esm: {},
    commonjs: {}
  }
})
