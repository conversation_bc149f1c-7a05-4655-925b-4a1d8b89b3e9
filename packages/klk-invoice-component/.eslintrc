{"root": true, "extends": ["@nuxtjs/eslint-config-typescript"], "rules": {"camelcase": "off", "vue/no-v-html": "off", "nuxt/no-cjs-in-config": "off", "vue/html-self-closing": "off", "vue/space-infix-ops": "error", "vue/mustache-interpolation-spacing": "error", "vue/singleline-html-element-content-newline": "off", "@typescript-eslint/type-annotation-spacing": "error", "import/newline-after-import": "error", "no-undef": "off", "space-before-function-paren": ["error", {"anonymous": "always", "named": "never", "asyncArrow": "always"}], "multiline-ternary": "off", "no-empty": "off", "array-callback-return": "off", "vue/valid-v-show": "off", "vue/no-lone-template": "off", "vue/v-slot-style": "off", "import/named": "off", "vue/multi-word-component-names": "warn", "no-use-before-define": "warn", "vue/no-useless-template-attributes": "warn", "prefer-regex-literals": "warn", "no-useless-escape": "warn"}}