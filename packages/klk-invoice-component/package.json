{"name": "@klook/klk-invoice-component", "version": "1.1.5", "description": "A Common Component based on Vue of Klook", "author": "simon.huang,florence.lee", "homepage": "https://design.klook.io", "main": "dist/commonjs/index.js", "module": "dist/esm/index.js", "typings": "types/index.d.ts", "unpkg": "dist/klk-invoice-component.js", "jsdelivr": "dist/klk-invoice-component.js", "files": ["dist", "lib", "src", "types", "locales"], "license": "UNLICENSED", "publishConfig": {"registry": "https://knpm.klook.io", "access": "public"}, "scripts": {"build": "klk-builder build", "build:umd": "klk-builder build -c=\"builder.umd.config.ts\"", "watch": "klk-builder build -w", "lint": "NODE_ENV=production eslint --ext .js,.vue src", "test": "NODE_ENV=test jest -i --updateSnapshot", "test:coverage": "NODE_ENV=test jest -i --coverage --updateSnapshot", "prepush": "yarn run lint", "prepublishOnly": "bash prepublishOnly.sh", "commit": "npx git-cz", "commitmsg": "commitlint -E GIT_PARAMS"}, "devDependencies": {"@babel/core": "^7.17.9", "@babel/plugin-transform-runtime": "^7.17.0", "@babel/preset-env": "^7.16.11", "@klook/klk-mask-icon": "^0.0.3", "@klook/klook-builder": "^0.0.12", "@klook/klook-icons": "^0.13.1", "@klook/klook-ui": "^1.36.0", "@types/jest": "^26.0.0", "@vue/test-utils": "^1.0.0-beta.32", "babel-jest": "^29.7.0", "jest": "^25.5.4", "ts-jest": "^26.1.0", "vue": "2.6.11", "vue-jest": "^3.0.4", "babel-core": "^7.0.0-bridge.0"}, "peerDependencies": {"@klook/klk-mask-icon": "^0.0.3", "@klook/klk-traveller-utils": "^1.7.0", "@klook/klook-icons": "^0.13.1", "@klook/klook-ui": "^1.36.0", "vue": "2.6.11", "vue-property-decorator": "^8.3.0"}}