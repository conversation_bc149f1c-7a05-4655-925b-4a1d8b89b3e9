<template>
  <div class="klook-order-table-atomic" v-if="data.length">
    <div class="klook-order-table-atomic-item" v-for="(item, index) in data" :key="index">
      <div class="klook-order-table-atomic-left">
        <div class="label">{{ item.title }}</div>
        <div class="detail">{{ item.detail }}</div>
      </div>
      <div class="klook-order-table-atomic-right">
        <div class="content">{{ item.content }}
          <klk-poptip :content="__t('109148')" placement="top" dark>
            <span v-if="item.copyable" @click="copy(item.content)">
              <IconCopy class="copy" size="20" fill="#212121"></IconCopy>
            </span>
          </klk-poptip>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import {
  Vue,
  Component,
  Prop,
  Provide,
} from 'vue-property-decorator'
import { IconCopy } from '@klook/klook-icons';
import { genText } from "./locale/index";
import messages from "../locales/index.js";
import KlkPoptip from '@klook/klook-ui/lib/poptip'

@Component({
  name: "OrderTableAtomic",
  components: {
    IconCopy,
    KlkPoptip
  }
})
export default class OrderTableAtomic extends Vue {
  // start from here
  @Prop({
    default: () => []
  })
  data!: Array<any>
  @Provide() __t: any = this.getTranslate()

  getTranslate() {
    return this.__t;
  }

  beforeCreate(this: any) {
    const locales = messages as any;
    const lang = this.$attrs.language || 'en';
    this.__t = locales[lang]
      ? genText(locales[lang])
      : genText(locales["en"]);
  }

  copy(text: string) {
    if (navigator.clipboard) {
      // clipboard api 复制
      navigator.clipboard.writeText(text);
    } else {
      var textarea = document.createElement('textarea');
      document.body.appendChild(textarea);
      // 隐藏此输入框
      textarea.style.position = 'fixed';
      textarea.style.clip = 'rect(0 0 0 0)';
      textarea.style.top = '10px';
      // 赋值
      textarea.value = text;
      // 选中
      textarea.select();
      // 复制
      document.execCommand('copy', true);
      // 移除输入框
      document.body.removeChild(textarea);
    }

    this?.$toast(this.__t('109149'));
  }
}
</script>

<style lang="scss">
.klook-order-table-atomic-item {
  display: flex;
  justify-content: space-between;
  padding: 8px 0;

  .klook-order-table-atomic-left {
    width: 50%;
    font-size: 16px;
    color: #757575;

    .detail {
      font-size: 12px;
      white-space: pre-wrap;
    }
  }

  .klook-order-table-atomic-right {
    max-width: 50%;
    text-align: right;
  }

  .content {
    color: #212121;
    margin-bottom: 4px;
    font-size: 16px;

    .copy {
      cursor: pointer;
      vertical-align: middle;
    }
  }
}
</style>
