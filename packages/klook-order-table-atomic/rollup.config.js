import replace from 'rollup-plugin-replace'
import typescript from 'rollup-plugin-typescript2'
import external from 'rollup-plugin-peer-deps-external';
import vue from 'rollup-plugin-vue';
import json from '@rollup/plugin-json';
import postcss from 'rollup-plugin-postcss'
const babel = require('rollup-plugin-babel');

import {
  terser
} from 'rollup-plugin-terser'

const {
  name,
  version,
  author
} = require('./package.json')

const banner =
  `/**
  * v${version}
  * (c) ${new Date().getFullYear()} ${author}
  */`
const configs = {
  umd: {
    output: 'dist/umd/index.js',
    format: 'umd',
    target: 'es5',
    env: 'production'
  },
  umdMin: {
    output: 'dist/umd/index.min.js',
    format: 'umd',
    target: 'es5',
    plugins: {
      post: [terser()]
    },
    env: 'production'
  },
  esm: {
    output: 'dist/esm/index.js',
    format: 'esm',
    target: 'es2015',
    env: 'production',
    genDts: true
  },
  cjs: {
    output: 'dist/cjs/index.js',
    format: 'cjs',
    target: 'es2015'
  }
}

const genTsPlugin = (configOpts) => typescript({
  useTsconfigDeclarationDir: true,
  tsconfigOverride: {
    compilerOptions: {
      target: configOpts.target,
      declaration: configOpts.genDts
    }
  },
  abortOnError: false
})

const genPlugins = (configOpts) => {
  const plugins = [external()]
  if (configOpts.env) {
    plugins.push(replace({
      'process.env.NODE_ENV': JSON.stringify(configOpts.env)
    }))
  }
  plugins.push(replace({
    'process.env.MODULE_FORMAT': JSON.stringify(configOpts.format)
  }))
  if (configOpts.plugins && configOpts.plugins.pre) {
    plugins.push(...configOpts.plugins.pre)
  }
  plugins.push(genTsPlugin(configOpts))

  plugins.push(json());
  plugins.push(vue({
    css: false,
    // normalizer: '~vue-runtime-helpers/dist/normalize-component.js',
    template: {
      isProduction: true,
      optimizeSSR: configOpts.isSSR
    },
    style:{
      postcssPlugins:[
        require('autoprefixer')({
          overrideBrowserslist: [
            '> 1%',
            'last 5 versions',
            'ios >= 7',
            'android > 4.4',
            'not ie < 10'
          ]
        }),
        require('cssnano')({
          safe: true
        })
      ]
    }
  }))

  plugins.push(postcss({
    extract: true,
    plugins: [
      require('autoprefixer')()
    ]
  }))

  if (configOpts.plugins && configOpts.plugins.post) {
    plugins.push(...configOpts.plugins.post)
  }
  return plugins
}

const genConfig = (configOpts) => ({
  input: 'src/index.js',
  output: {
    banner,
    file: configOpts.output,
    format: configOpts.format,
    name: name,
    sourcemap: false,
    exports: 'named',
    globals: configOpts.globals,
  },
  plugins: genPlugins(configOpts)
})

const genAllConfigs = (configs) => (Object.keys(configs).map(key => genConfig(configs[key])))

export default genAllConfigs(configs)
