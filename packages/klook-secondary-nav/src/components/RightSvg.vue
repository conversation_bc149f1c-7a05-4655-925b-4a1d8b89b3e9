<template>
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="16"
    height="16"
    fill="currentColor"
    viewBox="0 0 16 16"
  >
    <path
      fill-rule="evenodd"
      d="M5.505 14.329a.7.7 0 00.99 0l5.786-5.787a.7.7 0 000-.99L6.495 1.766a.7.7 0 10-.99.99l5.292 5.291-5.292 5.292a.7.7 0 000 .99z"
      clip-rule="evenodd"
    />
  </svg>
</template>
<script lang="ts">
import { Component, Vue } from "vue-property-decorator";
@Component({
  name: "RightSvg"
})
export default class RightSvg extends Vue {}
</script>
