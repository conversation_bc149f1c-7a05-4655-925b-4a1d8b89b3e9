<template>
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M9.73214 2.20748C10.0318 1.92148 10.5065 1.93252 10.7925 2.23214C11.0525 2.50453 11.067 2.92164 10.8424 3.21011L10.7679 3.29252L5.835 8L10.7679 12.7075C11.0402 12.9675 11.0741 13.3835 10.8632 13.6821L10.7925 13.7679C10.5325 14.0402 10.1165 14.0741 9.81793 13.8632L9.73214 13.7925L4.23214 8.54252C3.95076 8.27392 3.92518 7.84114 4.1554 7.54269L4.23214 7.45748L9.73214 2.20748Z"
    ></path>
  </svg>
</template>
<script lang="ts">
import { Component, Vue } from "vue-property-decorator";
@Component({
  name: "LeftSvg"
})
export default class LeftSvg extends Vue {}
</script>
