<template>
  <div
    v-if="!isEmptyObject(secondaryNav)"
    class="header-secondary-nav j-header-secondary-nav"
  >
    <div
      class="header-secondary-nav_categories j-header-secondary-nav_categories"
    >
      <div class="item-categories j-item-categories">
        <slot></slot>
      </div>
      <!-- all Categories-->
      <klk-dropdown
        :max-width="1160"
        :max-height="660"
        :hide-divider="true"
        :checkable="true"
        :arrow="false"
        class="item-categories header-secondary-nav_all-categories j-item-categories"
        :class="{
          'item-categories-left':
            secondaryNav.all_category.sub_menu_list.length > 4
        }"
      >
        <a
          :href="
            secondaryNav.all_category.type === 'deep_link'
              ? replaceUrlHost(secondaryNav.all_category.deep_link)
              : 'javascript:;'
          "
          :target="
            secondaryNav.all_category.type === 'deep_link' ? '_blank' : '_self'
          "
          @mouseenter="allCategories(secondaryNav.all_category.business_name)"
        >
          <span>{{ secondaryNav.all_category.title }}</span>
        </a>
        <klk-dropdown-menu slot="list">
          <klk-dropdown-item
            v-for="(item, index) in secondaryNav.all_category.sub_menu_list"
            :key="index"
          >
            <div
              :data-spm-module="
                `CategoryBar_L2_LIST?trg=manual&ext=${encodeURIComponent(
                  JSON.stringify({
                    BusinessName_L1: secondaryNav.all_category.business_name,
                    BusinessName_L2: item.business_name
                  })
                )}`
              "
            >
              <a
                :href="
                  item.type === 'deep_link'
                    ? replaceUrlHost(item.deep_link)
                    : 'javascript:;'
                "
                :class="{ have_link: item.type === 'deep_link' }"
                class="categories-title"
                :target="item.type === 'deep_link' ? '_blank' : '_self'"
                :data-spm-item="`__default?typ=entry`"
                v-galileo-click-tracker="{ spm: 'CategoryBar_L2_LIST', componentName: 'klook-secondary-nav' }"
              >
                <img
                  v-if="item.icon_src"
                  v-lazy.container="item.icon_src"
                  :alt="item.title"
                  class="icon"
                  width="16"
                  height="16"
                />
                {{ item.title }}
              </a>
            </div>
            <div v-for="(i, index1) in item.sub_menu_list" :key="index1">
              <div
                :data-spm-module="
                  `CategoryBar_L2_LIST?trg=manual&ext=${encodeURIComponent(
                    JSON.stringify({
                      BusinessName_L1: secondaryNav.all_category.business_name,
                      BusinessName_L2: i.business_name
                    })
                  )}`
                "
              >
                <a
                  v-if="i.title"
                  class="item-title"
                  :class="{ have_link: i.type === 'deep_link' }"
                  :href="
                    i.type === 'deep_link'
                      ? replaceUrlHost(i.deep_link)
                      : 'javascript:;'
                  "
                  :target="i.type === 'deep_link' ? '_blank' : '_self'"
                  :data-spm-item="`__default?typ=entry`"
                  v-galileo-click-tracker="{ spm: 'CategoryBar_L2_LIST', componentName: 'klook-secondary-nav' }"
                  >{{ i.title }}
                  <div class="right-arrow-icon"></div>
                </a>
              </div>
              <div v-if="i.sub_menu_list && i.sub_menu_list.length" class="item">
                <a
                  v-for="(t, index2) in i.sub_menu_list"
                  :key="index2"
                  :href="
                    t.type === 'deep_link'
                      ? replaceUrlHost(t.deep_link)
                      : 'javascript:;'
                  "
                  :target="t.type === 'deep_link' ? '_blank' : '_self'"
                  :class="{ have_link: t.type === 'deep_link' }"
                  :data-spm-module="
                    `CategoryBar_L2_LIST?trg=manual&ext=${encodeURIComponent(
                      JSON.stringify({
                        BusinessName_L1:
                          secondaryNav.all_category.business_name,
                        BusinessName_L2: t.business_name
                      })
                    )}`
                  "
                >
                  <i :data-spm-item="`__default?typ=entry`" v-galileo-click-tracker="{ spm: 'CategoryBar_L2_LIST', componentName: 'klook-secondary-nav' }">{{ t.title }}</i></a
                >
              </div>
            </div>
          </klk-dropdown-item>
          <klk-dropdown-item class="scroll">
            <span v-html="langData && langData.scroll_down"></span>
          </klk-dropdown-item>
        </klk-dropdown-menu>
      </klk-dropdown>
      <!-- vertical category -->
      <klk-dropdown
        v-for="(item, index) in secondaryNav.vertical_category"
        :key="index"
        :max-width="320"
        max-height="unset"
        :hide-divider="true"
        :checkable="true"
        :arrow="false"
        class="item-categories vertical-category j-item-categories"
        :class="{
          'item-categories_active':
            categoryActive[index] && categoryActive[index].active,
          hidden: index + 2 === moreIndex,
          'not-item': !item.sub_menu_list || !item.sub_menu_list.length
        }"
      >
        <a
          :href="
            item.type === 'deep_link'
              ? replaceUrlHost(item.deep_link)
              : 'javascript:;'
          "
          :data-spm-module="
            `CategoryBar_L1?ext=${encodeURIComponent(
              JSON.stringify({
                BusinessName: item.business_name
              })
            )}`
          "
        >
          <span
            :data-spm-item="
              `__default?typ=entry&idx=${index}&len=${secondaryNav.vertical_category.length}`
            "
            v-galileo-click-tracker="{ spm: 'CategoryBar_L1', componentName: 'klook-secondary-nav' }"
          >
            {{ item.title }}
          </span>
        </a>
        <klk-dropdown-menu v-if="item.sub_menu_list" slot="list">
          <klk-dropdown-item
            v-for="(i, index1) in item.sub_menu_list"
            :key="index1"
            class="categories"
            :data-spm-module="
              `CategoryBar_L2_LIST?ext=${encodeURIComponent(
                JSON.stringify({
                  BusinessName_L1: item.business_name,
                  BusinessName_L2: i.business_name
                })
              )}`
            "
            :class="{
              'item-categories_active':
                categoryActive[index] && categoryActive[index][index1]
            }"
          >
            <a
              :data-spm-item="
                `__default?typ=entry&idx=${index1}&len=${item.sub_menu_list.length}`
              "
              v-galileo-click-tracker="{ spm: 'CategoryBar_L2_LIST', componentName: 'klook-secondary-nav' }"
              :href="
                i.type === 'deep_link'
                  ? replaceUrlHost(i.deep_link)
                  : 'javascript:;'
              "
            >
              {{ i.title }}
            </a>
          </klk-dropdown-item>
        </klk-dropdown-menu>
      </klk-dropdown>
      <!-- more -->
      <klk-dropdown
        class="item-categories vertical-category more j-item-categories"
        :class="{ moreShow: moreShow }"
        placement="bottom-start"
      >
        <a href="JavaScript:;" class="more-con"><i></i></a>
        <klk-dropdown-menu slot="list">
          <klk-dropdown-item
            v-for="(item, index) in secondaryNav.vertical_category"
            :key="index"
            class="categories"
            :class="{ show: index + 2 === moreIndex }"
          >
            <a
              :href="
                item.type === 'deep_link'
                  ? replaceUrlHost(item.deep_link)
                  : 'javascript:;'
              "
              :class="{ 'not-cursor': item.type !== 'deep_link' }"
              class="more-title"
              :data-spm-module="
                `CategoryBar_L1?ext=${encodeURIComponent(
                  JSON.stringify({
                    BusinessName: item.business_name
                  })
                )}`
              "
            >
              <span
                :data-spm-item="
                  `__default?typ=entry&idx=${index}&len=${secondaryNav.vertical_category.length}`
                "
                v-galileo-click-tracker="{ spm: 'CategoryBar_L1', componentName: 'klook-secondary-nav' }"
              >
                {{ item.title }}
              </span>
            </a>

            <p
              v-for="(i, index1) in item.sub_menu_list"
              :key="index1"
              class="more-item"
              :class="{ 'not-cursor': i.type !== 'deep_link' }"
              :data-spm-module="
                `CategoryBar_L2_LIST?ext=${encodeURIComponent(
                  JSON.stringify({
                    BusinessName_L1: item.business_name,
                    BusinessName_L2: i.business_name
                  })
                )}`
              "
            >
              <a
                :data-spm-item="
                  `__default?typ=entry&idx=${index1}&len=${item.sub_menu_list.length}`
                "
                v-galileo-click-tracker="{ spm: 'CategoryBar_L2_LIST', componentName: 'klook-secondary-nav' }"
                :href="
                  i.type === 'deep_link'
                    ? replaceUrlHost(i.deep_link)
                    : 'javascript:;'
                "
              >
                {{ i.title }}
              </a>
            </p>
          </klk-dropdown-item>
        </klk-dropdown-menu>
      </klk-dropdown>
    </div>

    <!-- operational_category  -->
    <div
      v-if="
        secondaryNav.operational_category &&
          secondaryNav.operational_category.length
      "
      class="header-secondary-nav_swiper swiper-container j-header-secondary-nav_swiper"
    >
      <div class="swiper-button-next">
        <RightSvg />
      </div>
      <div class="swiper-button-prev">
        <LeftSvg />
      </div>
      <div class="swiper-wrapper">
        <a
          v-for="(item, index) in secondaryNav.operational_category"
          :key="index"
          class="swiper-slide item-operate j-item-operate"
          :class="{ 'item-operate_first': index === 0 }"
          :href="
            item.type === 'deep_link'
              ? replaceUrlHost(item.deep_link)
              : 'javascript:;'
          "
          :data-spm-module="
            `CategoryBar_Operations?ext=${encodeURIComponent(
              JSON.stringify({
                BusinessName_L1: item.business_name
              })
            )}`
          "
        >
          <img
            v-if="item.icon_src"
            :src="item.icon_src"
            class="item-operate_icon"
            alt="image"
          />
          <span
            :data-spm-item="
              `__default?typ=entry&idx=${index}&len=${secondaryNav.operational_category.length}`
            "
            v-galileo-click-tracker="{ spm: 'CategoryBar_Operations', componentName: 'klook-secondary-nav' }"
          >
            {{ item.title }}
          </span>
          <img
            v-if="item.corner_marker_url"
            :src="item.corner_marker_url"
            class="item-operate_corner_icon"
            alt="image"
            height="17"
          />
        </a>
      </div>
    </div>
  </div>
</template>
<script lang="ts">
import { Component, Vue, Prop } from "vue-property-decorator";
import Swiper from "swiper";
import "swiper/dist/css/swiper.css";
import replaceUrlHost from '@klook/klk-traveller-utils/lib/replaceUrlHost';
import isEmptyObject from '@klook/klk-traveller-utils/lib/isEmptyObject';
import KlkDropdown from "@klook/klook-ui/lib/dropdown/index.js";
import LeftSvg from "./components/LeftSvg.vue";
import RightSvg from "./components/RightSvg.vue";

@Component({
  name: "KlookSecondaryNav",
  components: {
    KlkDropdown,
    LeftSvg,
    RightSvg
  }
})
export default class KlookSecondaryNav extends Vue {
  @Prop({
    default: () => {
      return {};
    }
  })
  secondaryNav!: any;

  @Prop({ type: Object, default: () => {} })
  langData!: any;

  $swiper: any = null;
  showScroll: boolean = false;
  moreShow: boolean = false;
  moreIndex: number = 0;
  operateMaxWidth: any = 0;
  homeItemActive: boolean = false;
  categoryActive: Array<any> = [];
  replaceUrlHost: Function = replaceUrlHost;

  mounted() {
    this.allCategoriesScroll();
    this.$nextTick()
      .then(() => {
        this.setWidth();
      })
      .then(() => {
        // more（...） 里面有个动画执行时间为0.25s 会影响Swiper，所以需要加一个大于/等于0.25s的延迟
        setTimeout(() => {
          this.initSwiper();
        }, 300);
      });
  }

  setWidth() {
    this.moreIndex = this.secondaryNav?.vertical_category?.length + 2 || 0;
    this.moreShow = false;

    const second_nav = document.getElementsByClassName(
      "j-header-secondary-nav"
    )[0] as HTMLElement;
    if (!second_nav) {
      return;
    }
    const nav = second_nav.getElementsByClassName(
      "j-header-secondary-nav_categories"
    )[0] as HTMLElement;
    const operate = second_nav.getElementsByClassName(
      "j-header-secondary-nav_swiper"
    )[0] as HTMLElement;
    const navItem = nav.getElementsByClassName("j-item-categories");
    const windowWidth = second_nav.clientWidth;
    let operateItemWidth = 0; // + margin
    if (operate) {
      const operateItem = operate.getElementsByClassName("j-item-operate");
      if (operateItem) {
        for (let j = 0; j < operateItem.length; j++) {
          const element = operateItem[j];
          operateItemWidth += element.clientWidth; // 运营位长度
        }
      }
      operate.className = operate.className + " swiper-container";
    }
    const navWidth = nav.clientWidth;
    const navMaxWidth = windowWidth * 0.7;
    const operateMaxWidth = windowWidth * 0.3;
    const operateMargin = 52;
    let maxWidth = navMaxWidth - operateMargin;
    const operateSurplus = operateMaxWidth - operateItemWidth - operateMargin;
    if (operateSurplus > 0) {
      maxWidth += operateSurplus;
      if (maxWidth > windowWidth - 200) {
        // 距离最右侧至少200
        maxWidth = windowWidth - 200;
      }
    }
    if (navWidth > navMaxWidth) {
      for (let i = 0; i < navItem.length; i++) {
        const element = navItem[i];
        if (maxWidth - element.clientWidth > 0) {
          maxWidth = maxWidth - element.clientWidth;
          this.moreShow = false;
        } else {
          this.moreIndex = i;
          this.moreShow = true;
          this.operateMaxWidth =
            operateMaxWidth + maxWidth - operateMargin + "px";
          return;
        }
      }
    } else {
      this.moreIndex = navItem.length;
    }
  }

  isEmptyObject(obj: any) {
    return isEmptyObject(obj);
  }

  allCategories(business_name: string = "") {
    this.$nextTick(() => {
      const allCategories = document.querySelector(
        ".header-secondary-nav_all-categories"
      ) as any;
      const list = allCategories.querySelector(".klk-dropdown-list") as any;
      const menu = allCategories.querySelector(".klk-dropdown-menu") as any;
      const scroll = allCategories.querySelector(".scroll") as any;
      if (business_name) {
        list.scrollTop = 0;
      }
      this.showScroll = !(
        Math.ceil(list.scrollTop + list.clientHeight) >= menu.clientHeight
      );
      scroll.style.display = this.showScroll ? "block" : "none";
    });
  }

  allCategoriesScroll() {
    this.$nextTick(() => {
      const allCategories = document.querySelector(
        ".header-secondary-nav_all-categories"
      ) as any;
      if (allCategories) {
        const list = allCategories.querySelector(".klk-dropdown-list") as any;
        if (list) {
          list.onscroll = () => this.allCategories();
        }
      }
    });
  }

  initSwiper() {
    const containerEl = document.querySelector(
      ".j-header-secondary-nav_swiper"
    ) as any;
    this.$swiper = new Swiper(containerEl, {
      slidesPerView: "auto",
      observer: true,
      navigation: {
        nextEl: ".j-header-secondary-nav_swiper .swiper-button-next",
        prevEl: ".j-header-secondary-nav_swiper .swiper-button-prev"
      }
    });
  }
}
</script>
<style lang="scss">
$prefix: ".header-secondary-nav";
#{$prefix} {
  box-sizing: border-box;
  // clear: both;
  height: 55px;
  display: flex;
  align-items: center;
  width: 100%;
  clear: both;
  overflow: inherit;
  position: relative;
  background-color: $color-bg-widget-normal;
  z-index: 999;

  .klk-dropdown-list {
    border-radius: $radius-none $radius-none $radius-s $radius-s;
  }

  i {
    font-style: normal;
  }

  &_categories {
    display: flex;
    padding-right: 10px;
    overflow-x: hidden;
    overflow-y: visible;
  }

  &_swiper {
    flex: 1;
    height: 100%;
    // margin: 0 10px 0 0;
    &::after {
      content: "";
      display: block;
      position: absolute;
      width: 1px;
      height: 16px;
      margin-top: -6px;
      background-color: $color-bg-widget-darker-3;
      top: 50%;
      z-index: 10;
    }

    .swiper-wrapper {
      align-items: center;
    }
  }

  .destination-box_button {
    display: flex;
    align-items: center;

    @include font-body-s-semibold;
  }

  .item-categories,
  .klk-dropdown-reference a {
    width: auto;
    height: 40px;
    /* stylelint-disable */
    border-radius: 120px;
    /* stylelint-enable */
    display: flex;
    align-items: center;

    &.more {
      width: 0;
      visibility: hidden;
      transition: all 0.25s ease-in-out;

      .more-con i::after {
        visibility: hidden;
        transition: all 0.25s ease-in-out;
      }

      .categories {
        &:hover {
          color: $color-text-primary !important;
        }

        a:hover {
          &.not-cursor {
            color: $color-text-primary;
            cursor: default;
          }

          color: $color-brand-primary;
          cursor: pointer;
        }

        .more-title {
          font-size: $fontSize-body-s;
          color: $color-text-primary;
          padding: 16px 20px;
          font-weight: $fontWeight-semibold;
        }

        .more-item a {
          padding: 0 20px 16px 20px;
          line-height: 20px;
        }

        &:not(:last-child) {
          /* stylelint-disable */
          box-shadow: inset 0 -0.5px 0 #eee;
          /* stylelint-enable */
        }
      }

      &.moreShow {
        width: 44px;
        visibility: visible;

        i::after {
          visibility: visible;
        }
      }

      .more-con,
      .klk-poptip,
      .klk-poptip-reference {
        width: 100%;
      }

      .more-con {
        padding: 0 14px;
        display: flex;
        justify-content: space-between;
      }

      .categories {
        display: none;

        &.show,
        &.show ~ .categories {
          display: block;
        }
      }

      .more-con i::after,
      .more-con::before,
      .more-con::after {
        display: block;
        content: "";
        width: 3px;
        height: 3px;
        background: $color-common-black;
        border-radius: $radius-circle;
      }
    }

    &:hover {
      background: $color-bg-page;

      .icon img {
        transform: translate(-16px, 0);
      }
    }
  }

  .item-categories {
    @include font-body-s-semibold;

    flex-shrink: 0;

    &.hidden,
    &.hidden ~ .item-categories:not(.more) {
      width: 0;
      overflow: hidden;
      display: none;
    }

    .categories {
      span {
        padding: 0;
      }
    }

    .icon {
      overflow: hidden;
      display: flex;

      img {
        filter: drop-shadow(16px 0 $color-brand-primary);
      }
    }

    span {
      padding-left: 4px;
    }

    &_active {
      color: $color-brand-primary;

      > span {
        color: $color-brand-primary;
      }

      .klk-dropdown-reference a {
        position: relative;

        &::after {
          content: "";
          display: block;
          height: 4px;
          background: $color-brand-primary;
          width: 100%;
          position: absolute;
          bottom: 1px;
          border-radius: $radius-s $radius-s $radius-none $radius-none;
        }
      }

      .icon img {
        transform: translate(-16px, 0);
      }
    }

    &.vertical-category {
      .klk-dropdown-item {
        padding: 0;

        &:hover {
          background: none;
          color: $color-brand-primary;
        }

        span {
          display: block;
        }

        a {
          display: block;
          padding: 12px 20px;
          line-height: 18px;
          min-width: 120px;
        }
      }
    }
  }

  .item-operate {
    @include font-body-s-semibold;

    width: auto;
    display: flex;
    align-items: center;
    height: 40px;
    padding: 0 10px;
    /* stylelint-disable */
    border-radius: 120px;
    /* stylelint-enable */

    &_corner_icon {
      position: absolute;
      left: 100%;
      top: -6px;
      transform: translate(-38px, 0);
      z-index: 1;
    }

    &_first {
      margin-left: 10px;
      position: relative;
    }

    &:hover {
      background: $color-bg-page;
    }

    &_icon {
      margin-right: 6px;
      width: 24px;
      height: 24px;
    }
  }
  #{$prefix}_home {
    margin-right: 41px;
    white-space: nowrap;
    position: relative;

    &::before {
      content: "";
      display: block;
      position: absolute;
      width: 1px;
      height: 12px;
      margin-top: -6px;
      background-color: $color-bg-widget-darker-3;
      right: -16px;
      top: 50%;
    }

    &.item-categories_active {
      &::after {
        content: "";
        display: block;
        height: 4px;
        background: $color-brand-primary;
        width: 100%;
        position: absolute;
        bottom: 1px;
        border-radius: $radius-s $radius-s $radius-none $radius-none;
      }
    }
  }

  #{$prefix}_all-categories {
    .klk-poptip-popper {
      .klk-poptip-popper-inner {
        margin: 0;
        padding: 0;
      }

      .klk-dropdown-menu {
        display: flex;

        .klk-dropdown-item:hover {
          background-color: $color-bg-widget-normal;
        }

        .klk-dropdown-item {
          padding: 25px 20px 20px 38px;
          width: 232px;
          box-sizing: border-box;
          cursor: auto;

          &:not(:nth-last-child(2)) {
            border-right: 1px solid $color-border-dim !important;
          }

          a {
            cursor: auto;
          }

          .have_link {
            cursor: pointer;

            &:hover {
              color: $color-brand-primary;

              svg {
                color: $color-brand-primary;
              }
            }
          }

          .item-title {
            // display: block;
            margin-bottom: 20px;
            line-height: 18px;
            display: flex;
            align-items: center;

            .right-arrow-icon {
              margin: 0 0 0 4px;
              position: relative;
              transform: rotate(90deg) translate(7px, 0);
              width: 14px;
              width: 14px;
              height: 14px;

              &::after, &::before{
                content: "";
                position: absolute;
                width: 1.4px;
                height: 8px;
                display: block;
                background: $color-text-primary;
                border-radius: 2px;
                transform-origin: 0.5px 0.5px;
              }

              &::after{
                transform: rotate(-45deg);
              }

              &::before{
                transform: rotate(45deg);
              }
            }
          }

          .categories-title {
            font-size: $fontSize-body-m;
            color: $color-text-primary;
            display: flex;
            padding-bottom: 20px;
            position: relative;
            line-height: initial;

            img {
              left: -28px;
              display: block;
              position: absolute;
              top: 2px;
            }
          }
        }

        .scroll {
          position: absolute;
          bottom: 0;
          width: 100%;
          text-align: center;
          background: rgba(255, 255, 255, 0.96); // stylelint-disable-line
          border-radius: $radius-none $radius-none $radius-s $radius-s;
          padding: 6px 0 16px 0;
          overflow: initial;
          color: $color-brand-primary;
          font-weight: $fontWeight-regular;

          span {
            padding: 0;
          }

          &::after {
            content: "";
            display: block;
            width: 100%;
            height: 24px;
            position: absolute;
            top: -24px;
            /* stylelint-disable */
            background: linear-gradient(
              359.97deg,
              rgba(255, 255, 255, 0.96) 0.03%,
              rgba(255, 255, 255, 0) 70.82%
            );
            /* stylelint-enable */
          }
        }

        .item {
          font-size: $fontSize-body-s;
          color: $color-text-secondary;
          line-height: 18px;
          font-weight: $fontWeight-regular;
          margin-top: -10px;
          margin-bottom: 12px;

          a {
            display: inline-block;
            margin-bottom: 8px;
            position: relative;
            padding-right: 12px;
            margin-right: 12px;
          }
        }
      }
    }
  }

  .swiper-button-next,
  .swiper-button-prev {
    display: flex;
    align-items: center;
    width: 36px;
    color: $color-text-primary;
    background: $color-bg-widget-normal;
    height: 100%;
    top: 0;
    margin-top: 0;

    &::after {
      display: none;
      font-size: $fontSize-caption-m;
      color: $color-text-primary;
    }

    &::before {
      content: "";
      position: absolute;
      height: 100%;
      width: 16px;
      background: linear-gradient(
        270deg,
        $color-bg-widget-normal 0%,
        $color-transparent 100%
      );
    }
  }

  .swiper-button-prev {
    left: 0;
    padding-left: 10px;

    &::before {
      right: -16px;
      transform: rotate(180deg);
    }
  }

  .swiper-button-next {
    right: 0;
    padding-right: 10px;

    &::before {
      left: -16px;
    }
  }

  .swiper-button-disabled {
    display: none;
  }

  .klk-dropdown-reference {
    a {
      span {
        padding: 0 10px;
        white-space: nowrap;
      }
    }
  }

  .klk-dropdown {
    .klk-poptip-popper {
      left: auto !important;
      top: 55px !important;
      border-radius: $radius-l;
      transform: translate3d(0, 0, 0) !important;
      overflow: hidden;
      /* stylelint-disable */
      box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);
      /* stylelint-enable */
    }

    .klk-dropdown-item {
      border: none !important;
    }

    &.header-secondary-nav_all-categories {
      .klk-poptip-popper {
        left: 0 !important;
      }
    }
  }

  .item-categories-left.klk-dropdown {
    .klk-poptip-popper {
      left: 0 !important;
    }
  }
  .not-item .klk-poptip-popper {
    display: none !important;
  }
}

@media only screen and (max-width: 1365px) {
  #{$prefix} {
    #{$prefix}_all-categories
      .klk-poptip-popper
      .klk-dropdown-menu
      .klk-dropdown-item {
      width: 232px;

      &.scroll {
        width: 100%;
      }
    }
  }
}
</style>
