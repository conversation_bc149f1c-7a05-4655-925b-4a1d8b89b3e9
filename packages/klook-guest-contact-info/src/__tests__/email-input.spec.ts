import { shallowMount, createLocalVue, Wrapper } from '@vue/test-utils';
import EmailInput from '../components/email-input/email-input.vue';

describe('EmailInput', () => {
  let wrapper: Wrapper<any>;
  const emailSuffixList = ['gmail.com', 'yahoo.com', 'hotmail.com'];

  beforeEach(() => {
    const localVue = createLocalVue();
    wrapper = shallowMount(EmailInput, {
      localVue,
      propsData: {
        emailSuffixList,
        showDefaultIcon: true,
        appendText: 'Optional',
        emailTip: 'Please enter your email'
      },
      stubs: {
        KlkAutoComplete: true,
        IconMail: true
      }
    });
  });

  afterEach(() => {
    wrapper.destroy();
  });

  test('should render the component correctly', () => {
    expect(wrapper.find('.contact-email').exists()).toBe(true);
    expect(wrapper.find('.contact-email-tip').exists()).toBe(true);
    expect(wrapper.find('.contact-email-tip').text()).toBe('Please enter your email');
  });

  test('should not show IconMail when showDefaultIcon is false', async () => {
    await wrapper.setProps({ showDefaultIcon: false });
    expect(wrapper.findComponent({ name: 'IconMail' }).exists()).toBe(false);
  });

  test('should show append text when provided', () => {
    expect(wrapper.find('.append-text').text()).toBe('Optional');
  });

  test('should not show append text when not provided', async () => {
    await wrapper.setProps({ appendText: '' });
    expect(wrapper.find('.append-text').exists()).toBe(false);
  });

  describe('getSuggestList method', () => {
    test('should return empty array when input does not contain @', async () => {
      const callback = jest.fn();
      await wrapper.vm.getSuggestList('test', callback);
      expect(callback).toHaveBeenCalledWith([]);
    });

    test('should return filtered suggestions when input contains @', async () => {
      const callback = jest.fn();
      await wrapper.vm.getSuggestList('test@g', callback);
      
      const expectedSuggestions = [
        {
          value: '<EMAIL>',
          attrsDom: {
            'data-spm-item': expect.stringContaining('EmailSuggest_LIST')
          }
        }
      ];
      
      expect(callback).toHaveBeenCalledWith(expect.arrayContaining(expectedSuggestions));
    });

    test('should return top 4 suggestions when input only has @', async () => {
      const callback = jest.fn();
      await wrapper.vm.getSuggestList('test@', callback);
      
      expect(callback).toHaveBeenCalledWith(expect.arrayContaining([
        expect.objectContaining({ value: '<EMAIL>' }),
        expect.objectContaining({ value: '<EMAIL>' }),
        expect.objectContaining({ value: '<EMAIL>' })
      ]));
    });
  });
});
