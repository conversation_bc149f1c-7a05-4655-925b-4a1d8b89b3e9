import {
    genSafeData,
    // getGuestAndContactInfo,
    setGuestAndContactInfo,
    // setPhoneInGuestContactInfo,
    // setEmailInGuestContactInfo,
    validateFields
} from '../utils';
// import { PhoneInfo, EmailInfo } from '../../types/types';

// 定义验证结果的类型
type ValidationResult = {
    valid: boolean;
    errors?: Array<{ field: string; message: string }>;
    fields?: Record<string, Array<string>>;
};

describe('Utils', () => {
    // 模拟 localStorage
    const mockLocalStorage = {
        store: {} as Record<string, string>,
        getItem(key: string) {
            console.log('mockLocalStorage setItem', key, this.store);
            return this.store[key] || null;
        },
        setItem(key: string, value: string) {
            console.log('mockLocalStorage setItem', key, value, this.store);
            this.store[key] = value.toString();
        },
        clear() {
            this.store = {};
        }
    };

    beforeEach(() => {
        // 重置模拟的 localStorage
        mockLocalStorage.clear();
        // 替换全局的 localStorage
        Object.defineProperty(window, 'localStorage', {
            value: mockLocalStorage
        });
    });

    describe('genSafeData', () => {
        test('should filter xss values', () => {
            const input = {
                name: 'John<script>alert("xss")</script>',
                email: '<EMAIL><img src="x" onerror="alert(1)">'
            };
            const result = genSafeData(input);
            // xssFilters.inHTMLData 会转换特殊字符
            expect(result.name).not.toContain('<script>');
            expect(result.email).not.toContain('<img');
        });

        test('should handle nested objects', () => {
            const input = {
                user: {
                    name: 'John<script>alert("xss")</script>',
                    contact: {
                        email: '<EMAIL><img src="x">'
                    }
                }
            };
            const result = genSafeData(input);
            expect(result.user.name).not.toContain('<script>');
            expect(result.user.contact.email).not.toContain('<img');
        });

        test('should handle arrays', () => {
            const input = {
                users: [
                    { name: 'John<script>alert(1)</script>' },
                    { name: 'Jane<img src="x">' }
                ]
            };
            const result = genSafeData(input);
            expect(result.users[0].name).not.toContain('<script>');
            expect(result.users[1].name).not.toContain('<img');
        });

        test('should handle null and undefined values', () => {
            const input = {
                name: null,
                age: undefined,
                email: '<EMAIL>'
            };
            const result = genSafeData(input);
            expect(result).toEqual({
                email: '<EMAIL>'
            });
        });
    });

    describe('Guest Contact Info Storage', () => {
        // test('should set and get guest contact info', () => {
        //   const testData = {
        //     firstName: 'John',
        //     lastName: 'Doe',
        //     email: '<EMAIL>'
        //   };

        //   setGuestAndContactInfo(testData);
        //   const result = getGuestAndContactInfo();

        //   expect(result).toEqual(testData);
        // });

        // test('should merge with existing data when setting', () => {
        //   const initialData = {
        //     firstName: 'John',
        //     lastName: 'Doe'
        //   };

        //   const newData = {
        //     email: '<EMAIL>'
        //   };

        //   setGuestAndContactInfo(initialData);
        //   setGuestAndContactInfo(newData);

        //   const result = getGuestAndContactInfo();
        //   expect(result).toEqual({
        //     ...initialData,
        //     ...newData
        //   });
        // });

        // test('should set phone info in guest contact info', () => {
        //   const phoneInfo: PhoneInfo = {
        //     phoneNum: '1234567890',
        //     countryCode: '+1',
        //     regionCode: 'US'
        //   };

        //   setPhoneInGuestContactInfo(phoneInfo);
        //   const result = getGuestAndContactInfo();

        //   expect(result.phoneInfo).toEqual(phoneInfo);
        // });

        // test('should set email info in guest contact info', () => {
        //   const emailInfo: EmailInfo = {
        //     email: '<EMAIL>'
        //   };

        //   setEmailInGuestContactInfo(emailInfo);
        //   const result = getGuestAndContactInfo();

        //   expect(result.emailInfo).toEqual(emailInfo);
        // });

        test('should handle localStorage errors gracefully', () => {
            // 模拟 localStorage 错误
            jest.spyOn(mockLocalStorage, 'setItem').mockImplementation(() => {
                throw new Error('Storage full');
            });

            const testData = {
                firstName: 'John',
                lastName: 'Doe'
            };

            // 不应抛出错误
            expect(() => setGuestAndContactInfo(testData)).not.toThrow();

            // 获取数据时应返回空对象
            // const result = getGuestAndContactInfo();
            // expect(result).toEqual({});
        });

        // test('should handle data expiration', () => {
        //     const testData = {
        //         firstName: 'John',
        //         lastName: 'Doe'
        //     };

        //     // 设置一个很短的过期时间（1毫秒）
        //     setGuestAndContactInfo(testData, 1);

        //     // 等待数据过期
        //     return new Promise(resolve => {
        //         setTimeout(() => {
        //             const result = getGuestAndContactInfo();
        //             expect(result).toEqual({});
        //             resolve(true);
        //         }, 2);
        //     });
        // });
    });

    describe('validateFields', () => {
        test('should validate fields successfully', async () => {
            const data = {
                name: 'John',
                email: '<EMAIL>'
            };

            const rules = {
                name: { type: 'string', required: true },
                email: { type: 'email', required: true }
            };

            const result = await validateFields(data, rules) as ValidationResult;
            expect(result.valid).toBe(true);
            expect(result.errors).toBeUndefined();
        });

        test('should return validation errors for invalid data', async () => {
            const data = {
                name: '',
                email: 'invalid-email'
            };

            const rules = {
                name: { type: 'string', required: true },
                email: [{
                    required: true
                  }, {
                    pattern: /^(?=\s*\w+(?:\.{0,1}[\w-]+)*@[a-zA-Z0-9]+(?:[-.][a-zA-Z0-9]+)*\.[a-zA-Z]+\s*$).{1,150}$/
                  }]
            };

            const result = await validateFields(data, rules) as ValidationResult;
            expect(result.valid).toBe(false);
            expect(result.errors).toBeDefined();
            expect(result.errors?.length).toBeGreaterThan(0);
            expect(result.fields).toBeDefined();
        });
    });
});  
