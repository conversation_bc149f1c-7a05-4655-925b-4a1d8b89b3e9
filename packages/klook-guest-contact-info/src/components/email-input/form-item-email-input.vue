<template>
  <klk-form-item
    v-bind="$attrs"
    v-on="$listeners"
  >
    <KlkUserEmailInput
      v-bind="$attrs"
      v-on="$listeners"
      @input="emailChange"
    />
  </klk-form-item>
</template>

<script>
import KlkUserEmailInput from './email-input.vue'

export default {
  name: 'KlkUserFormItemEmailInput',
  components: {
    KlkUserEmailInput,
  },
  props: {
    // formItemAttrs: {
    //   type: Object,
    //   default: () => { }
    // },
    inputAttrs: {
      type: Object,
      default: () => { }
    },
    inputListeners: {
      type: Object,
      default: () => { }
    },

  },
  data() {
    return {
      defaultValidator: { validator: this.validateEmail, trigger: 'submit' },
      emailInput: ''
    }
  },
  computed: {
    formRules() {
      return [...(this.formItemAttrs.rules || []), this.defaultValidator]
    }
  },
  created() {
    this.emailInput = this.inputAttrs.value
  },
  methods: {
  }
}
</script>
