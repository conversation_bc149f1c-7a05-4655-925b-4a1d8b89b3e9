<template>
  <div class="contact-email">
    <klk-auto-complete v-galileo-click-tracker:select="galileoClickTracker"  clearable ref="emailInput" v-bind="attrs" v-on="$listeners" @select="selectEmail" @input="input"
      :fetch-suggestions="getSuggestList">
      <IconMail v-show="showDefaultIcon" slot="prepend" class="prepend-icon" theme="outline" size="20"
        :fill="colorTextPrimary" />
      <span v-if="appendText" slot="append" class="append-text">{{ appendText }}</span>
    </klk-auto-complete>
    <div v-if="emailTip" class="contact-email-tip">
      {{ emailTip }}
    </div>
    <slot name="email-tip"></slot>
  </div>
</template>

<script>
import { $colorTextPrimary } from "@klook/klook-ui/lib/utils/design-token-esm";
import IconMail from "@klook/klook-icons/lib/IconMail";
import { AutoComplete } from "@klook/klook-ui"

export default {
  name: "KlkUserEmailInput",
  components: {
    IconMail,
    KlkAutoComplete: AutoComplete
  },
  props: {
    emailSuffixList: {
      type: Array,
      default: null,
    },
    emailTip: {
      type: String,
      default: '',
    },
    showDefaultIcon: {
      type: Boolean,
      default: false,
    },
    appendText: {
      type: String,
      default: ''
    },
    hackFocus: {
      type: Boolean,
      default: true
    },
    hackDynamicAutocomplete: { // newWeb项目上输入@首次动态修改后的autocomplete总是延迟生效
      type: Boolean,
      default: false
    },
    spmModule: {
      type: String,
      default: ''
    } // 上级的spm
  },
  data() {
    return {
      colorTextPrimary: $colorTextPrimary,
    }
  },
  computed: {
    galileoClickTracker() {
      return {
        spm: `${this.spmModule ? `${this.spmModule}.` : ''}EmailSuggest_LIST`
      }
    },
    attrs() {
      return Object.assign({},
        this.$attrs,
        this.getAutoCompleteAttrs()
      )
    }
  },
  methods: {
    formatList(list, username) {
      return list.map((suggest, index) => {
        return {
          value: `${username}@${suggest}`,
          attrsDom: {
            "data-spm-item": `EmailSuggest_LIST?ext=${JSON.stringify({
              SuggestEmail: suggest,
              Index: index,
              Length: list.length,
            })}`,
          },
        };
      });
    },
    async getSuggestList(emailInput, cb) {
      // 输入@ 之前不联想
      if (!(emailInput || '').includes("@") || this.emailSuffixList.length === 0) {
        cb([]);
        return;
      }

      // 调后端接口获取邮箱域名联想列表
      // const emailSuffixList = await this.getEmailSuffix();

      const [username, emailSuffix] = emailInput.split("@");
      const filteredRes = emailSuffix
        ? this.emailSuffixList.flatMap((suffix) => suffix.startsWith(emailSuffix) && suffix !== emailSuffix.toLowerCase()
          ? suffix
          : []
        ) // @后默认展示的列表，后端返回的前4个
        : this.emailSuffixList.slice(0, 4); // @后加更多字母，展示符合的联想后缀

      // 配置埋点
      const suggestList = this.formatList(filteredRes, username)
      cb(suggestList);
    },
    selectEmail(item) {
      this.$emit('select', item);
      this.$emit('input', item);
      const emailInput = this.getCurrentEmailInputEl()
      // hack 选中后，自动聚焦到输入框再失焦(因为klk-auto-complete组件没有将下拉和input的焦点label for绑定 ， 但是现在表单都用的blur校验)
      if (emailInput && this.hackFocus) {
        emailInput.focus()
        setTimeout(() => {
          emailInput.blur();
        }, 0);
      }
    },
    getAutoCompleteAttrs() {
      if (this.hackDynamicAutocomplete) {
        return {}
      }
      // 动态修改autocomplete属性(兼容传递html原生属性，和组件属性属性autoComplete)
      const isShowSuffixTip = this.getShowSuffixTip(this.$attrs.value)
      return Object.assign({}, this.$attrs.autocomplete && isShowSuffixTip ? {
        autocomplete: 'off'
      } : {},
      this.$attrs.autoComplete && isShowSuffixTip ? {
        autoComplete: 'off'
      } : {})
    },
    getShowSuffixTip(value = '') {
      return value?.includes('@') && this.emailSuffixList?.length > 0;
    },
    getCurrentEmailInputEl() {
      return this.$el.querySelector('input[name="email"]');
    },
    input(value = '') {
      if (this.hackDynamicAutocomplete) {
        const isShowSuffixTip = this.getShowSuffixTip(value)
        const emailInput = this.getCurrentEmailInputEl()
        emailInput && emailInput.setAttribute('autocomplete', isShowSuffixTip ? 'off' : 'email')
      }
      this.$emit('input', value)
    }
  },

  mounted() {
  },
};
</script>

<style lang="scss" scoped>
.contact-email {
  .contact-email-tip {
    color: $color-text-primary;
    @include font-paragraph-s-regular;
    margin-top: 16px;
  }
}
</style>
