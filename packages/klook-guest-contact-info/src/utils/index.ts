import AsyncValidatorSchema from 'async-validator'
import localStorageEx from './localstorage.js'
const localStorage = localStorageEx()
import xssFilters from 'xss-filters'
let localStorageKey = ''
import { FormKey, GuestContactInfo, PhoneInfo, EmailInfo, BusinessType, GuestInfo } from '../../types/types'
const realTypeof = (obj: any): string => Object.prototype.toString.call(obj).slice(8, -1).toLocaleLowerCase()

export const hiddenGuestContactForm = 'hiddenGuestContactForm'

export const initGuestContactInfo = (businessType: BusinessType) => {
  localStorageKey = `guestAndContactInfo${businessType}`
}

// key目前是标准的lastName firstName email tel (只要手机号不要区号)
export const submitForm2Browser = async (formData: { [key in FormKey]?: any }) => {
  return new Promise((resolve) => {
    try {
      const removeForm = createForm()
      const button = document.createElement('button');
      button.textContent = '';
      // 设置按钮的 form 属性（关联表单的 id）
      button.setAttribute('form', hiddenGuestContactForm);
      button.setAttribute('type', 'submit');
      // 设置按钮不可见且不占据空间，但仍可点击
      button.style.opacity = '0'; // 完全透明
      button.style.position = 'absolute'; // 确保按钮不占据布局空间
      document.body.appendChild(button);

      // let dynamicFormAttrsInput: HTMLElement[] = [] // 找得到在优先在原有属性上操作(放弃，因为ttd很多都是在浮层)
      let dynamicAddFormInput: HTMLElement[] = []; // 动态添加的关联form的input

      // 给指定的input加上对应的form属性，用于表单提交
      (Object.keys(formData) as FormKey[]).forEach((key) => {
        const formValue = formData[key]
        if (formValue) {
          const input = document.createElement("input")
          input.type = "text";
          input.name = key;
          input.value = formData[key]
          input.setAttribute('form', hiddenGuestContactForm);
          input.style.opacity = '0';
          input.style.position = 'absolute';
          document.body.appendChild(input);
          dynamicAddFormInput.push(input)
          // const originInputs = document.querySelectorAll(`input[name="${key}"]`)
          // Array.prototype.find.call(originInputs, (input) => {
          //   if (input.value.trim() === formValue.trim()) {
          //     const oldForm = input.getAttribute('form')
          //     if (!input.dataset) { input.dataset = {} }
          //     oldForm && (input.dataset.oldForm = oldForm)
          //     input.setAttribute('form', hiddenGuestContactForm)
          //     dynamicFormInput.push(input)
          //     return true
          //   }
          // })
        }
      })

      button.click()

      setTimeout(() => {
        button.remove()
        // 恢复input的form属性
        // dynamicFormInput && dynamicFormInput.forEach(input => {
        //   const oldForm = input.dataset.oldForm
        //   if (oldForm) {
        //     input.setAttribute('form', oldForm)
        //   } else {
        //     input.removeAttribute('form')
        //   }
        // })
        dynamicAddFormInput && dynamicAddFormInput.forEach(input => {
          input.remove()
        })
        removeForm()
      }, 100)
      resolve(true)
    } catch (error) {
      resolve(true)
    }
  })
}

/** 如果没有修改的可以不用传 */
export const setGuestAndContactInfo = (data: { [key: string]: any }, time?: number) => {
  if (!localStorageKey) {
    return
  }
  try {
    const lastData = getGuestAndContactInfo()
    localStorage.setItem(localStorageKey, Object.assign({}, lastData, genSafeData(data)), time || 356 * 7 * 24 * 60 * 1000)
  } catch (error) {
  }
}

export const getGuestAndContactInfo = (): GuestContactInfo => {
  if (!localStorageKey) {
    return {}
  }
  try {
    return genSafeData(localStorage.getItem(localStorageKey))
  } catch (error) {
    return {}
  }
}


export const validateFields = (data: { [key: string]: any }, rules: any) => {
  const validator = new AsyncValidatorSchema(rules)

  return validator.validate(data).then(() => {
    return {
      valid: true
    }
  }).catch(({ errors, fields }) => {
    return {
      valid: false,
      errors,
      fields
    }
  })
}

export function genSafeData(data: { [key: string]: any }): { [key: string]: any } {
  // 防止xss攻击
  const safeData = Object.keys(data)
    .reduce((acc, v) => {
      if (realTypeof(data[v]) === 'object') {
        return {
          ...acc,
          [v]: genSafeData(data[v])
        }
      } else if (realTypeof(data[v]) === 'array') {
        return {
          ...acc,
          [v]: data[v].map((item: { [key: string]: any }) => genSafeData(item))
        }
      } else if (data[v] === null || typeof data[v] === 'undefined') {
        return {
          ...acc
        }
      }
      return {
        ...acc,
        [v]: xssFilters.inHTMLData(data[v])
      }
    }, {})

  return safeData
}

// 这里兼容某些业务场景要直接用上一次的guestinfo对象的例如国家码和本地name和title
export const setGuestInfoInGuestContactInfo = (guestInfo: GuestInfo, isMerge = false) => {
  const lastGuestContactInfo = getGuestAndContactInfo()
  const lastGuestInfo = lastGuestContactInfo.guestInfo || {}
  lastGuestContactInfo.guestInfo = isMerge ? {
    ...lastGuestInfo,
    ...guestInfo
  } : guestInfo
  setGuestAndContactInfo(lastGuestContactInfo)
}

export const setPhoneInGuestContactInfo = (phoneInfo: PhoneInfo) => {
  const lastGuestContactInfo = getGuestAndContactInfo()
  lastGuestContactInfo.phoneInfo = phoneInfo
  setGuestAndContactInfo(lastGuestContactInfo)
}

export const setEmailInGuestContactInfo = (emailInfo: EmailInfo) => {
  const lastGuestContactInfo = getGuestAndContactInfo()
  lastGuestContactInfo.emailInfo = emailInfo
  setGuestAndContactInfo(lastGuestContactInfo)
}

function createForm() {
  const form = document.createElement('form');
  form.id = hiddenGuestContactForm;  // 设置表单id属性
  form.hidden = true;   // 表单隐藏不展示
  document.body.append(form);
  const preventDefault = (e: SubmitEvent) => {
    e.preventDefault();
  }
  form.addEventListener(
    'submit',
    preventDefault
  );
  return () => {
    form.removeEventListener(
      'submit',
      preventDefault
    );
    form.remove()
  }
}



