module.exports = {
  preset: 'ts-jest',
  globals: {
    __DEV__: true,
    __VERSION__: require('./package.json').version,
  },
  watchPathIgnorePatterns: ['/node_modules/', '/dist/', '/.git/'],
  moduleFileExtensions: ['ts', 'js', 'json', 'vue'],
  transform: {
    "^.+\\.vue$": "vue-jest",
    '^.+\\.(ts|tsx)?$': 'ts-jest',
    '^.+\\.(js|ts|tsx)$': 'babel-jest'
  },
  rootDir: __dirname,
  testMatch: ['<rootDir>/src/**/__tests__/**/*spec.[jt]s?(x)'],
  testPathIgnorePatterns: ['/node_modules/'],
  moduleNameMapper: {
    "^@/(.*)$": "<rootDir>/src/$1",
    // "^@vue/test-utils": "<rootDir>/node_modules/@vue/test-utils/dist/vue-test-utils.cjs.js",
    "^@klook/klook-ui": "<rootDir>/node_modules/@klook/klook-ui",
    // "^@klook/klk-traveller-utils": "<rootDir>/node_modules/@klook/klk-traveller-utils/lib",
    '\\.(css|scss|less)$': '<rootDir>/__mocks__/styleMock.js',
  },
  setupFiles: ['<rootDir>/setup-jest.js'],
}