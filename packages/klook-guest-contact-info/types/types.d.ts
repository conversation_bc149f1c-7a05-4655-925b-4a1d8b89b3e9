export type GuestInfo = {
  lastName?: string
  firstName?: string
  [key: string]: any 
} // ttd的有很多自定义属性

export type PhoneInfo = {
  phoneNum: string
  countryCode: string
  regionCode: string
}

export type EmailInfo = {
  email: string
}

export type GuestContactInfo = {
  guestInfo?: GuestInfo
  guestList?: GuestInfo[]
  emailInfo?: EmailInfo
  phoneInfo?: PhoneInfo
  [key: string]: any // 其他业务线自定义数据
}

export type FormKey = 'lastName' | 'firstName' | 'email' | 'tel'

export type MockSubmitFormType = 'frame' | 'form' 

export type BusinessType = 'Insurance' | 'Hotel' | 'Car' | 'CR' | 'PTP' | 'PAT' | 'TTD' | 'Transfer' | 'Ticket' | 'Other' | 'CEG'

