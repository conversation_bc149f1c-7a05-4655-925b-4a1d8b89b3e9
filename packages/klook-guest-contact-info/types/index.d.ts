import Vue from "vue";
import { <PERSON><PERSON><PERSON>, GuestContactInfo, PhoneInfo, EmailInfo, GuestInfo, BusinessType } from './types'

export * from './types'

export declare const hiddenGuestContactForm: string
export declare class KlkGuestContactEmail extends Vue {
    emailSuffixList: string[]
}

export declare class KlkGuestContactEmailFormItem extends Vue {
    emailSuffixList: string[]
}

export declare function initGuestContactInfo(businessType: BusinessType): void

export declare function submitForm2Browser(formData: { [key in FormKey]?: any }): Promise<unknown>

export declare function setGuestAndContactInfo(data: {
    [key: string]: any;
}, time?: number): void

export declare function getGuestAndContactInfo():  GuestContactInfo

export declare function validateFields(data: {
    [key: string]: any;
}, rules: any): Promise<{
    valid: boolean;
} | {
    valid: boolean;
    errors: any;
    fields: any;
}>

export declare function genSafeData(data: {
    [key: string]: any;
}): {
    [key: string]: any;
}

export declare function setPhoneInGuestContactInfo(phoneInfo: PhoneInfo): void

export declare function setEmailInGuestContactInfo(emailInfo: EmailInfo) : void

export declare function setGuestInfoInGuestContactInfo(setGuestInfoInGuestContactInfo: GuestInfo, isMerge?: boolean): void