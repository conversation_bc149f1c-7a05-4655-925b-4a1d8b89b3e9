window.__OAUTH_STATE__ = { 
  subSentry: { reportEvent: jest.fn() },
  version: '1.0.0',
  options: {},
  market: 'global',
  isCN: false,
  isKR: false,
  language: 'en-US',
  langPath: 'en-US',
  initUuid: 'aaaaaa'
};

const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn()
}


Object.defineProperty(global, 'window', {
  value: {
    ...window,
    OAuthEvents: {
      $on: jest.fn(),
      $emit: jest.fn()
    },
    $axios: {
      $post: jest.fn(() => Promise.resolve({
        success: true,
        result: {}
      })),
      $get: jest.fn(() => Promise.resolve({}))
    },
    localStorage: localStorageMock
  },
  writable: true
});
