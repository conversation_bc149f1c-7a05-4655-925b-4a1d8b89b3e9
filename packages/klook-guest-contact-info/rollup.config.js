import replace from 'rollup-plugin-replace'
import typescript from 'rollup-plugin-typescript2'
import vue from 'rollup-plugin-vue';
import babel from 'rollup-plugin-babel'
import nodeResolve from 'rollup-plugin-node-resolve'
import commonjs from 'rollup-plugin-commonjs'
import cssnano from 'cssnano'
import postcss from 'rollup-plugin-postcss'
import autoprefixer from 'autoprefixer'
import external from 'rollup-plugin-peer-deps-external';

const postcssPlugins = [autoprefixer(), cssnano()]
const input = [
  './src/index.ts'
]

import clear from 'rollup-plugin-clear'

clear({ targets: ['lib', 'esm'] })

const {
  name,
  version,
  author
} = require('./package.json')

const banner = '/* eslint-disable */'
  /**
  * v${version}
  * (c) ${new Date().getFullYear()} ${author}
  */
const configs = {
  esm: {
    dir: 'dist/esm',
    format: 'esm',
    target: 'es2015',
    env: 'production',
    genDts: true,
  },
  cjs: {
    dir: 'dist/cjs',
    format: 'cjs',
    target: 'es2015',
    isSSR: true
  }
}

// const externals = [
//   'vue',
//   'vue-property-decorator',
//   'dayjs',
//   'lodash',
//   '@klook/image',
//   '@klook/klook-traveller-login',
//   'xss-filters',
//   '@klook/klook-icons',
//   '@klook/klook-ui',
//   'async-validator'
// ]

const genTsPlugin = (configOpts) => typescript({
  useTsconfigDeclarationDir: true,
  tsconfigOverride: {
    exclude: ['**/__tests__', 'test-dts'],
    compilerOptions: {
      target: configOpts.target,
      declaration: configOpts.genDts
    }
  },
  abortOnError: false
})

const genPlugins = (configOpts) => {
  const plugins = [
    external(),
    nodeResolve({
      extensions: ['.mjs', '.js', '.jsx', '.vue']
    }),
    commonjs({
      include: /node_modules/
    }),
    genTsPlugin(configOpts),
    babel({
      runtimeHelpers: true,
      exclude: 'node_modules/**',
      extensions: ['.js', '.jsx', '.ts', '.tsx'],
      presets: ["@vue/babel-preset-jsx"]
    }),
    replace({
      'process.env.MODULE_FORMAT': JSON.stringify(configOpts.format)
    }),
    postcss({
      extract: 'index.css',
      plugins: postcssPlugins
    }),
    vue({
      css: false,
      normalizer: '~vue-runtime-helpers/dist/normalize-component.js',
      template: {
        isProduction: true,
        // optimizeSSR: configOpts.isSSR
      },
      style: {
        postcssPlugins: postcssPlugins.slice(0, 1),
        preprocessOptions: {
          scss: { data: '@import "node_modules/@klook/klook-ui/lib/styles/token/index.scss";' }
        }
      }
    })
  ]
  if (configOpts.env) {
    plugins.push(replace({
      'process.env.NODE_ENV': JSON.stringify(configOpts.env)
    }))
  }
  if (configOpts.plugins && configOpts.plugins.pre) {
    plugins.push(...configOpts.plugins.pre)
  }

  if (configOpts.plugins && configOpts.plugins.post) {
    plugins.push(...configOpts.plugins.post)
  }

  return plugins
}

const genConfig = (configOpts) => ({
  input,
  output: {
    banner,
    dir: configOpts.dir,
    // file: configOpts.output,
    format: configOpts.format,
    name: name,
    sourcemap: false,
    exports: 'named',
    globals: configOpts.globals,
  },
  // external: externals,
  plugins: genPlugins(configOpts)
})

const genAllConfigs = (configs) => (Object.keys(configs).map(key => genConfig(configs[key])))

export default genAllConfigs(configs)
