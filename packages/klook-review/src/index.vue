<template>
  <div :class="['review-wrap', `review-wrap-${platform}`]" :data-spm-module="`CmptReview`"
    data-spm-virtual-item="__virtual">
    <div class="review-container">
      <div class="review-left">
        <div class="review-title mb-2">{{ isReviewed ? __t('109019') : __t('194830') }}</div>

        <div v-if="isMultidimensional && !isReviewed">
          <klk-button type="primary" @click="goReview">{{ __t('109151') }}</klk-button>
        </div>
        <div v-else class="review-star">
          <template v-if="!isReviewed">
            <div class="score-stars-content">
              <span :style="{ padding: `0 ${gap}px` }" @click.stop.prevent="selectStar(index)"
                @mouseenter="starEnter(index)" @mouseleave="starLeave()" class="star-item review-status"
                v-for="(item, index) in total" :class="{ 'stared': isStared(index) }" :key="index"
                :data-spm-item="`ReviewRate?ext=${JSON.stringify({ RatingScore: index + 1 })}`">
                <IconStar size="24" fill="#ccc" theme="filled" class="star-icon status-unstar"></IconStar>
                <IconStar size="24" fill="#F09B0A" theme="filled" class="star-icon status-stared"></IconStar>
              </span>
            </div>
          </template>

          <div class="score-stars-content" ref="scoreWrap" v-if="isReviewed">
            <span class="score-stars-box score-stars-light" :style="{ width: width }">
              <IconStar size="24" fill="#F09B0A" theme="filled" :style="{ padding: `0 ${gap}px` }"
                v-for="(i, index) in total" :key="index" class="score-stars-star-icon"></IconStar>
            </span>

            <span class="score-stars-box score-stars-dark">
              <IconStar size="24" fill="#ccc" theme="filled" :style="{ padding: `0 ${gap}px` }"
                v-for="(i, index) in total" :key="index" class="score-stars-star-icon">
              </IconStar>
            </span>
          </div>
        </div>
        <div class="review-detail mt-2" v-if="isReviewed"
          :data-spm-item="`ReviewDetail?ext=${JSON.stringify({ RatingScore: score })}`">
          <span @click="reviewDetailClick" class="review-detail-link">{{ __t('109152') }}</span>
        </div>
      </div>
      <div class="review-right review-status" :class="{ 'stared': isReviewed }">
        <reviewed class="status-stared" />
        <unreview class="status-unstar" />
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import {
  Vue,
  Component,
  Prop,
  Provide
} from 'vue-property-decorator'
import KlkButton from '@klook/klook-ui/lib/button/index.js'
import star from './svg/star.vue';
import stared from './svg/stared.vue';
import reviewed from './svg/reviewed.vue';
import unreview from './svg/unreview.vue';
import { genText } from "./locale/index";
import messages from "../locales/index.js";
import { IconStar } from '@klook/klook-icons'

@Component({
  name: "KlookReview",
  components: {
    KlkButton,
    star,
    stared,
    reviewed,
    unreview,
    IconStar
  }
})
export default class KlookReview extends Vue {
  // start from here
  @Prop()
  value!: number

  @Prop({
    default: 'mobile'
  })
  platform!: 'desktop' | 'mobile'

  @Prop({
    default: false
  })
  isMultidimensional?: boolean

  @Prop({
    default: 5
  })
  readonly total!: number

  @Prop({
    required: true,
  })
  readonly deeplink!: string

  hoverStar = 0;
  score = 0;
  gap = 6;
  width = '0%';

  mounted() {
    this.$nextTick(() => {
      this.initWidth();
    })
  }

  initWidth() {
    // return (this.score / this.total) * 100 + '%'
    // 如果图标之间有间隔且间隔较大时，小数的显示会有偏移;
    const value = this.value || 0;
    if (value >= this.total) return this.width = '100%';
    const starWidth = 24;
    const containerWidth = (this.$refs.scoreWrap as HTMLElement)?.getBoundingClientRect()?.width || 168;

    const baseRate = (starWidth / containerWidth) * Math.floor(value);
    const gapRate = ((1 + (Math.floor(value) * 2)) * this.gap) / containerWidth;
    const additionalRate = (starWidth / containerWidth) * (value - Math.floor(value));

    this.width = (baseRate + gapRate + additionalRate) * 100 + '%'
  }

  @Provide() __t: any = this.getTranslate()

  getTranslate() {
    return this.__t;
  }

  beforeCreate(this: any) {
    const locales = messages as any;
    const lang = this.$attrs.language || 'en';
    this.__t = locales[lang]
      ? genText(locales[lang])
      : genText(locales["en"]);
  }

  reviewDetailClick() {
    if (this.deeplink) {
      location.href = `${this.deeplink}`;
    }
    this.$emit('review-detail-click');
  }

  goReview() {
    if (this.deeplink) {
      location.href = `${this.deeplink}`;
    }
    this.$emit('review-click');
  }

  selectStar(index: number) {
    if (this.isReviewed) return;
    const star = index + 1;
    // this.score = star;
    // this.$emit('input', star);
    // this.$emit('change', star);
    if (this.deeplink) {
      location.href = `${this.deeplink}&rate=${star}`;
    }
  }

  starEnter(index: number) {
    this.hoverStar = ++index;
  }

  starLeave() {
    this.hoverStar = 0;
  }

  isStared(index: number) {
    return this.value > index || (!this.isReviewed && this.hoverStar > index);
  }

  get isReviewed() {
    return !!this.value;
  }
}
</script>

<style lang="scss">
.review-wrap {
  width: 100%;
  border-radius: 12px;
  box-shadow: 0px 0px 12px 0px rgba(0, 0, 0, 0.03);

  .mb-2 {
    margin-bottom: 10px;
  }

  .mt-2 {
    margin-top: 10px;
  }

  &.review-wrap-desktop {

    .mb-2 {
      margin-bottom: 12px;
    }

    .mt-2 {
      margin-top: 12px;
    }

    .review-left {
      padding: 20px;
    }
  }

  .review-container {
    display: flex;
  }

  .review-left {
    flex: 1;
    display: flex;
    padding-right: 20px;
    flex-direction: column;
    padding: 16px;

    .review-title {
      font-size: 16px;
      font-weight: 600;
      color: #212121;
    }

    .star-item {
      cursor: pointer;
      display: inline-block;

      &:last-child {
        margin-right: 0 !important;
      }

      img {
        cursor: pointer;
      }
    }
  }

  .review-status {
    display: flex;
    align-items: center;

    .status-stared {
      display: none;
    }

    &.stared {
      .status-stared {
        display: inline-block;
      }

      .status-unstar {
        display: none;
      }
    }
  }

  .review-detail {
    font-size: 16px;
    line-height: 1;
  }

  .review-detail-link {
    cursor: pointer;
    color: #212121;
    text-decoration: underline;
  }

  .review-star {
    .score-stars-content {
      display: inline-block;
      position: relative;
      margin-left: -6px;

      .review-detail {
        margin-bottom: 10px;
      }

      .score-stars-star-icon {
        box-sizing: content-box;

        &:last-child {
          margin-right: 0 !important;
        }
      }

      .score-stars-light {
        position: absolute;
        overflow: hidden;
        white-space: nowrap;
      }
    }

  }
}
</style>
