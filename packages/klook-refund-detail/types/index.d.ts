import Vue from 'vue'

export interface Refund_statu {
	text: string;
	text_color: string;
	desc: string;
	icon: string;
	status: string;
}

export interface Annotation {
	title_text: string;
	content_text: string;
}

export interface Price_info {
	price_type: string;
	amount: string;
	currency: string;
}

export interface Refund_price_detail {
	title_text: string;
	content_text: string;
	annotations: Annotation[];
	price_info: Price_info;
}

export interface Annotation {
	title_text: string;
	content_text: string;
}

export interface FormItemData {
	title_text: string;
	content_text: string;
	desc?: string;
	action_type?: number;
	link?: string;
	annotations?: Annotation[];
	price_info?: Price_info;
	refund_additional_desc?: string;
}

export interface Refunded_unit {
	variant_id: number;
	title: string;
	subtitle: string;
}

export interface Refund_details_basic {
	title_text: string;
	content_text: string;
}

export interface RefundAnnotation {
	title_text: string;
	content_text: string;
}

export interface RefundDetail {
	refund_id: number;
	refund_no: string;
	refund_status: Refund_statu;
	refund_total_amount_text: string;
	refund_total_amount_value: string;
	refund_price_details: Refund_price_detail[];
	refund_asset_details: FormItemData[];
	refunded_unit: Refunded_unit[];
	refund_details_basic: Refund_details_basic[];
}

export interface RefundDetails {
	refund_details: RefundDetail[][];
	refund_annotations: RefundAnnotation[];
}

export default class klkRefundDetail extends Vue {
  /** Install component into Vue */
  static install (vue: typeof Vue): void
}
