<template>
  <div>
    <div class="refund-detail-card" v-for="(refundItems, index) in data" :key="index">
      <div class="refund-detail-list" v-for="(refundItem, index) in refundItems" :key="index">
        <div class="refund-detail-header">
          <div class="refund-detail-status">
            <div class="refund-detail-icon">
              <img :src="refundItem.refund_status.icon" alt="">
            </div>
            <div class="refund-detail-status-title" :style="{ 'color': refundItem.refund_status.text_color }">{{
              refundItem.refund_status.text }}</div>
          </div>
          <div class="refund-detail-status-content">{{ refundItem.refund_status.desc }}</div>
        </div>
        <div class="refund-detail-main">

          <div class="klook-refund-detail_form-item form-item form-item-bold">
            <div class="label"> {{ refundItem.refund_total_amount_text }}</div>
            <div class="info">
              <div class="content"> {{ refundItem.refund_total_amount_value }}</div>
            </div>
          </div>

          <div v-if="refundItem.refund_price_details && refundItem.refund_price_details.length"
            class="refund-detail-price-detail refund-detail-price-detail-arrow">
            <template v-for="(data, index) in refundItem.refund_price_details">
              <form-item :data="data"></form-item>
            </template>
          </div>

          <div v-if="refundItem.refund_asset_details && refundItem.refund_asset_details.length"
            class="refund-detail-asset-detail">
            <template v-for="(data, index) in refundItem.refund_asset_details">
              <form-item :data="data"
                :data-spm-module="`RefundAsset_LIST?ext=${JSON.stringify({ RefundStatus: refundItem.refund_status.status, PriceType: data.price_info && data.price_info.price_type })}&oid=refund_${refundItem.refund_id}&idx=${index}&len=${refundItem.refund_asset_details.length}`"
                data-spm-virtual-item="__virtual"></form-item>
              <template v-if="data.refund_additional_desc">
                <div class="refund-detail-addition-info" v-html="data.refund_additional_desc"></div>
              </template>
            </template>
          </div>

          <div v-if="refundItem.refunded_unit && refundItem.refunded_unit.length" class="refund-detail-price-detail">
            <div v-for="refundUnit in refundItem.refunded_unit" class="refunded-unit-item">
              <div class="refunded-unit-title">{{ refundUnit.title }}</div>
              <div class="refunded-unit-sub-title">{{ refundUnit.subtitle }}</div>
            </div>
          </div>

          <template v-for="(data, index) in refundItem.refund_details_basic">
            <form-item :data="data"></form-item>
          </template>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import {
  Vue,
  Component,
  Prop,
  Inject
} from 'vue-property-decorator'
import KlkIcon from '@klook/klook-ui/lib/icon';
import KlkPoptip from '@klook/klook-ui/lib/poptip'
import { IconCopy, IconInformation } from '@klook/klook-icons';
import FormItem from './components/form-item.vue';
import Base from './base';
import { RefundDetail } from '../types/index';

@Component({
  name: "KlookRefundDetailList",
  components: {
    KlkIcon,
    IconCopy,
    KlkPoptip,
    IconInformation,
    FormItem
  }
})
export default class KlookRefundDetailList extends Base {
  @Prop()
  data!: RefundDetail[][]

  @Inject() providedPlatform!: string;
}
</script>

<style lang="scss">
.refund-detail-card {
  margin-bottom: 16px;
  border-radius: 16px;
  padding: 16px;
  background-color: #fff;
}

.refund-detail-list {
  padding-bottom: 16px;

  .info-content {
    color: #212121;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
  }

  .refund-detail-header {
    border-bottom: 1px solid #eee;
    padding-bottom: 12px;
    margin-bottom: 12px;

    .refund-detail-status {
      display: flex;
      align-items: center;

      &-content {
        padding-left: 44px;
        padding-top: 7px;
      }
    }

    .refund-detail-icon {
      margin-right: 12px;
      width: 32px;
      height: 32px;

      img {
        width: 100%;
        height: 100%;
      }
    }

    .refund-detail-status-title {
      font-weight: 600;
      font-size: 16px;
    }

    .refund-detail-status-content {
      font-weight: 400;
      font-size: 14px;
      line-height: 1.5;
    }
  }

  .refund-detail-main {

    .refund-detail-price-detail {
      background-color: #f5f5f5;
      border-radius: 16px;
      padding: 16px;
      margin-bottom: 8px;
      position: relative;
      z-index: 10;

      &-arrow {
        &::before {
          content: '';
          position: absolute;
          border-left: 7px solid transparent;
          border-right: 7px solid transparent;
          top: -7px;
          border-bottom: 7px solid #f5f5f5;
          right: 20px;
          margin-left: -7px;
        }
      }
    }

    .refund-detail-price-detail {
      font-size: 16px;
      color: #212121;

      .refunded-unit-item {
        margin-bottom: 12px;

        &:last-child {
          margin-bottom: 0;
        }

        .refunded-unit-title {
          font-weight: 600;
          margin-bottom: 4px;
        }

        .refunded-unit-sub-title {
          font-weight: 400;
          white-space: pre-wrap;
        }
      }
    }

    .refund-detail-asset-detail {
      margin-bottom: 16px;
    }

    .refund-detail-addition-info {
      background-color: #fcf3de;
      font-size: 14px;
      line-height: 1.5;
      font-weight: 400;
      color: $color-text-primary;
      padding: 8px;
      border-radius: $radius-s;

      span {
        color: $color-caution;
      }
    }
  }
}
</style>
