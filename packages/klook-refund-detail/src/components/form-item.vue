<template>
  <div class="klook-refund-detail_form-item">
    <div class="label">{{ data.title_text }}</div>
    <div class="info">
      <div class="content" @click="itemClick" :style="{ 'cursor': data.action_type === 1 ? 'pointer' : '' }">{{
        data.content_text }}
        <template v-if="data.action_type === 1 && data.link">
          <IconNext></IconNext>
        </template>

        <template v-if="data.action_type === 2 && data.annotations && data.annotations.length">
          <span v-if="providedPlatform === 'mobile'" @click="showInfo = true">
            <IconInformation class="icon-btn" fill="#212121" />
          </span>

          <klk-poptip v-else placement="bottom-end" trigger="click">
            <div slot="content">
              <div v-for="item in data.annotations" class="klook-refund-detail-poptip-container">
                <div class="klook-refund-detail_poptip-title">{{ item.title_text }}</div>
                <div class="klook-refund-detail_poptip-content">{{ item.content_text }}</div>
              </div>
            </div>
            <IconInformation class="icon-btn" size="20" fill="#212121" />
          </klk-poptip>
        </template>

        <klk-poptip :content="__t('109148')" placement="top" dark>
          <span @click="copy(data.content_text)" v-if="data.action_type === 4">
            <IconCopy class="icon-btn" size="20" fill="#212121" />
          </span>
        </klk-poptip>
      </div>
      <div v-if="data.desc" class="content-detail">{{ data.desc }}</div>
    </div>

    <klk-bottom-sheet class="refund-detail-bg-white" :visible.sync="showInfo">
      <div v-for="item in data.annotations" class="klook-refund-detail-poptip-container">
        <div class="klook-refund-detail_poptip-title">{{ item.title_text }}</div>
        <div class="klook-refund-detail_poptip-content">{{ item.content_text }}</div>
      </div>
    </klk-bottom-sheet>
  </div>
</template>

<script lang="ts">
import {
  Component,
  Prop,
  Inject
} from 'vue-property-decorator'
import { IconNext, IconCopy, IconInformation } from '@klook/klook-icons';
import KlkPoptip from '@klook/klook-ui/lib/poptip';
import { FormItemData } from '../../types/index';
import Base from '../base';

@Component({
  name: "FormItem",
  components: {
    IconNext,
    IconCopy,
    IconInformation,
    KlkPoptip
  }
})
export default class FormItem extends Base {
  @Prop({
    default: () => { }
  })
  data!: FormItemData;

  @Inject() providedPlatform!: string;

  showInfo = false;

  close() {
    this.showInfo = false
  }

  itemClick() {
    if (this.data.action_type === 1 && this.data.link) {
      window.open(this.data.link);
    }
  }

  copy(text: string) {
    if (navigator.clipboard) {
      // clipboard api 复制
      navigator.clipboard.writeText(text);
    } else {
      var textarea = document.createElement('textarea');
      document.body.appendChild(textarea);
      // 隐藏此输入框
      textarea.style.position = 'fixed';
      textarea.style.clip = 'rect(0 0 0 0)';
      textarea.style.top = '10px';
      // 赋值
      textarea.value = text;
      // 选中
      textarea.select();
      // 复制
      document.execCommand('copy', true);
      // 移除输入框
      document.body.removeChild(textarea);
    }

    this?.$toast(this.__t('109149'));
  }
}
</script>

<style lang="scss">
.klook-refund-detail_form-item {
  display: flex;
  justify-content: space-between;
  padding: 8px 0;

  &.form-item-bold {
    font-size: 20px;
    font-weight: 600;

    .label {
      color: #212121;
    }
  }

  .label {
    max-width: 50%;
    font-size: 16px;
    color: #757575;
  }

  .info {
    max-width: 50%;
    text-align: right;
  }

  .content {
    color: #212121;
    font-size: 16px;

    .icon-btn {
      cursor: pointer;
      // vertical-align: middle;
    }

    .klk-poptip {
      vertical-align: middle;
    }
  }

  .content-detail {
    font-size: 14px;
    color: #757575;
    margin-top: 4px;
  }
}
</style>
