<template>
  <div>
    <klk-drawer :data-spm-page="`CmptRefundDetail?oid=booking_${bookingRefNo}`" class="klook-refund-detail-wrap"
      direction="right" :visible="visible" @close="onVisibleChange(false)" @open="onVisibleChange(true)">
      <div class="refund-header">
        <klk-icon class="header-btn" @click="onVisibleChange(false)" type="icon_navigation_close_m" color="#212121"
          size="24"></klk-icon>

        <div class="title">{{ __t('109028') }}</div>

        <template v-if="refundDetails.refund_annotations && refundDetails.refund_annotations.length">
          <klk-poptip slot="header-right" placement="bottom-end" trigger="click">
            <div slot="content">
              <div v-for="item in refundDetails.refund_annotations" class="poptip-container">
                <div class="klook-refund-detail_poptip-title">{{ item.title_text }}</div>
                <div class="klook-refund-detail_poptip-content">{{ item.content_text }}</div>
              </div>
            </div>
            <klk-icon :data-spm-module="`Description`" data-spm-virtual-item="__virtual" class="header-btn"
              type="icon_tips_tips_xs" size="24" color="#212121"></klk-icon>
          </klk-poptip>
        </template>
      </div>

      <!-- refund pop tips -->

      <refund-list :data="refundDetails.refund_details">
        <template v-for="(item, key) in $slots" v-slot:[key]>
          <slot :name="key"></slot>
        </template>
      </refund-list>
    </klk-drawer>
  </div>
</template>

<script lang="ts">
import {
  Vue,
  Component,
  Prop,
  Watch
} from 'vue-property-decorator'
import { Icon as KlkIcon } from '@klook/klook-ui';
import { Drawer as KlkDrawer } from '@klook/klook-ui';
import RefundList from '../list.vue';
import Base from '../base';
import { RefundDetails } from '../../types/index';

@Component({
  name: "KlookRefundDetailDesktop",
  components: {
    KlkIcon,
    KlkDrawer,
    RefundList,
  }
})
export default class KlookRefundDetailDesktop extends Base {
  @Prop({
    default: false
  }) visible!: boolean;

  @Prop()
  title!: string;

  @Prop()
  bookingRefNo!: string

  @Prop()
  refundDetails!: RefundDetails

  close() {
    this.$emit('update:visible', false);
  }

  onVisibleChange(val: boolean) {
    this.$emit('visibleChange', val)
  }
}
</script>

<style lang="scss">
.klook-refund-detail-wrap {

  .klk-drawer-content {
    background-color: #f5f5f5;
    min-width: 40%;
    padding: 0 32px;
    width: 40%;
    min-width: 420px;
    max-width: 750px;
  }

  .refund-header {
    position: sticky;
    z-index: 99;
    top: 0;
    display: flex;
    align-items: center;
    background-color: #f5f5f5;
    padding: 20px 0;

    .title {
      font-size: 20px;
      font-weight: 600;
      color: #212121;
      flex: 1;
      text-align: center;
    }

    .header-btn {
      cursor: pointer;
    }
  }
}
</style>
