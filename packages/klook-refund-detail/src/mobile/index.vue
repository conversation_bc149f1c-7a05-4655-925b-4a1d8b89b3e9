<template>
  <div class="klook-refund-detail-wrap">
    <klk-bottom-sheet :data-spm-page="`CmptRefundDetail?oid=${bookingRefNo}`" :title="__t('109028')" :visible="visible"
      @close="onVisibleChange(false)" @open="onVisibleChange(true)">
      <klk-icon @click="onVisibleChange(false)" slot="header-left" type="icon_navigation_close_m" size="24"
        color="#212121"></klk-icon>

      <!-- refund pop tips -->
      <template v-if="refundDetails.refund_annotations && refundDetails.refund_annotations.length">
        <klk-icon slot="header-right" @click="showTips = true" type="icon_tips_tips_xs" size="24"
          color="#212121"></klk-icon>
      </template>

      <refund-list :data="refundDetails.refund_details">
        <template v-for="(item, key) in $slots" v-slot:[key]>
          <slot :name="key"></slot>
        </template>
      </refund-list>
    </klk-bottom-sheet>

    <klk-bottom-sheet class="refund-detail-bg-white" :visible.sync="showTips">
      <div v-for="item in refundDetails.refund_annotations" class="klook-refund-detail-poptip-container">
        <div class="klook-refund-detail_poptip-title">{{ item.title_text }}</div>
        <div class="klook-refund-detail_poptip-content">{{ item.content_text }}</div>
      </div>
    </klk-bottom-sheet>
  </div>
</template>

<script lang="ts">
import {
  Vue,
  Component,
  Prop,
  Watch
} from 'vue-property-decorator'
import { Icon as KlkIcon } from '@klook/klook-ui';
import { BottomSheet as KlkBottomSheet } from '@klook/klook-ui';
import RefundList from '../list.vue';
import Base from '../base';
import { RefundDetails } from 'types';

@Component({
  name: "KlookRefundDetailMobile",
  components: {
    KlkIcon,
    KlkBottomSheet,
    RefundList,
  }
})
export default class KlookRefundDetailMobile extends Base {
  @Prop({
    default: false
  }) visible!: boolean;

  @Prop()
  bookingRefNo!: string;

  @Prop()
  refundDetails!: RefundDetails

  showTips = false;

  close() {
    this.$emit('update:visible', false);
  }

  onVisibleChange(val: boolean) {
    this.$emit('visibleChange', val);
  }
}
</script>

<style lang="scss">
.klook-refund-detail-wrap {

  .klk-bottom-sheet-inner,
  .klk-bottom-sheet-header {
    background-color: #f5f5f5 !important;
  }

  .klk-bottom-sheet-inner {
    min-height: 40%;
  }

  .refund-detail-bg-white.klk-bottom-sheet {

    .klk-bottom-sheet-inner,
    .klk-bottom-sheet-header {
      background-color: #fff !important;
    }
  }
}
</style>
