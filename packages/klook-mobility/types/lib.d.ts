declare interface Window {
  tracker: {
    // sendMixpanel: Function
    sendMixpanel: ({ name: string, props: any }) => void;
    inhouse: any;
    gtm: {
      sendGTMCustomEvent: Function;
    };
  };

  $axios: {
    $get: Function;
    $post: Function;
  };

  __KLOOK__: {
    state: {
      auth: any;
      klook: {
        language: string;
        platform: string;
      };
    };
  }

  Cookies: any;
}
