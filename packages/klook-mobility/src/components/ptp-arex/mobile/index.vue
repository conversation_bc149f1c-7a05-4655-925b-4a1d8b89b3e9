<template>
  <div :class="`${className}`">
    <h2 :class="`${className}-header`">
      <span :class="`${className}-header-title`">
        <span>
          {{
            arex_data.display_status_key
          }}
        </span>
        <span :style="{color: arex_data.active_status == 'active' ? '#08B371' : '#F09B0A'}">
          {{
            arex_data.display_status_value
          }}
        </span>
      </span>
    </h2>
    <klk-divider
      v-if="arex_data.active_status === 'inactive'"
      class="common-line"
    ></klk-divider>

    <!-- 尚未占座状态 -->
    <div
      v-if="arex_data.active_status === 'inactive'"
      :class="`${className}-init-part`"
    >
      <div :class="`${className}-init-part-title`">
        {{ arex_data.active_title }}
      </div>
      <div :class="`${className}-init-part-desc`">
        {{ arex_data.active_content }}
      </div>
      <klk-button
        :class="`${className}-init-part-btn`"
        size="small"
        type="primary"
        @click="handleShowModal"
        >{{ $t("120019-goActive") }}</klk-button
      >
    </div>
    <!-- 尚未占座状态 -->

    <!-- 已占座状态 -->
    <div
      v-else-if="arex_data.active_status === 'active'"
      :class="`${className}-ticket-detail`"
    >
      <div :class="`${className}-ticket-detail-base-common`">
        <div :class="`${className}-ticket-detail-base-common-title`">
          {{ arex_data.departure_date }}
        </div>
        <div :class="`${className}-ticket-detail-base-common-desc`">
          <div :class="`${className}-ticket-detail-base-common-desc-item`">
            {{ arex_data.departure_station_code }} -
            {{ arex_data.arrival_station_code }}
          </div>
          <div :class="`${className}-ticket-detail-base-common-desc-item`">
            {{ $t("113086") }}：{{ arex_data.departure_time }} -
            {{ arex_data.arrival_time }}
          </div>
        </div>
      </div>
      <div :class="[`${className}-ticket-detail-qrcode`]">
        <div :class="`${className}-ticket-detail-qrcode-tips`">
          {{ $t("123797") }}
        </div>
        <klk-carousel
          :autoplay="false"
          controller-position="inside"
          hide-indicators
        >
          <klk-carousel-item
            v-for="(item, index) in arex_data.ticket_details"
            :key="index"
          >
            <div :class="[`${className}-ticket-detail-qrcode-item`]">
              <img v-if="item.voucher_url" :src="item.voucher_url" />
              <div :class="`${className}-ticket-detail-qrcode-item-seat-no`">
                {{ $t("122846") }} {{ item.train_number }}
              </div>
              <div :class="`${className}-ticket-detail-qrcode-item-seat-no`">
                {{ $t("122845") }} {{ item.car_number }}
              </div>
              <div :class="`${className}-ticket-detail-qrcode-item-seat-no`">
                {{ $t("176728") }} {{ item.seat_no }}
              </div>
              <div
                :class="`${className}-ticket-detail-qrcode-item-voucher-label`"
                @click="handleCopy(item.voucher_number)"
              >
                {{ $t("122847") }}
              </div>
              <!-- <div :class="`${className}-ticket-detail-qrcode-item-voucher-no`">
                {{ item.voucher_number }}
              </div> -->
            </div>
          </klk-carousel-item>
        </klk-carousel>
      </div>
    </div>
    <!-- 已占座状态 -->
    <client-only>
      <div
        v-show="showSelectSeat"
        :class="[`${className}-selectSeat`]"
      >
        <div :class="[`${className}-selectSeat-header`]">
          <div :class="[`${className}-selectSeat-header-icon`]" @click="handleCloseModal">
            <IconBack theme="outline" size="24" :fill="[$tokensObj['$color-text-primary']]" />
          </div>
          {{ modalTitle }}
        </div>
        <seat-mobile-modal
          :class="[`${className}-selectSeat-content`]"
          :seat-data="selectSeatData"
          :schedule-date="scheduleDate"
          :show-confirm-modal="showConfirmModal"
          :confirm-btn-disable="confirmBtnDisable"
          :tips="tips"
          :date-desc="date_desc"
          :date-tip="date_tip"
          :date="date"
          :time-list="timeList"
          :confirm-btn-loading="confirmBtnLoading"
          @close="handleCloseModal"
          @handle-change-date="handleChangeDate"
          @handle-set-seat-form="handleSetSeatForm"
          @submit="handleSubmit"
          @handle-double-confirm="handleDoubleConfirm"
          @cancel-confirm="cancelConfirm"
        />
      </div>

    </client-only>
  </div>
</template>

<script lang="ts">
import { Component } from "vue-property-decorator";
import SeatMobileModal from "../components/modal/index.vue";
import { IconBack } from "@klook/klook-icons";
import { PtpArexBase } from "../base";

@Component({ components: { SeatMobileModal,IconBack } })
export default class BookingDetailsPtpArex extends PtpArexBase {
  static displayName = "MobileExperiencePtp_arex";

  get className() {
    return "arex-mobile";
  }

  handleCloseModal() {
    this.showSelectSeat = false;
  }

  handleShowModal() {
    this.scheduleDate = ''
    this.showSelectSeat = true;
  }
}
</script>

<style lang="scss" scoped>
.arex-mobile {
  margin: 12px 16px;
  padding: 0 16px;
  background-color: #fff;
  border-radius: 16px;
  box-sizing: border-box;

  &-selectSeat {
    position: fixed;
    width: 100vw;
    height: calc(100vh - 48px);
    z-index: 999;
    top: 48px;
    left: 0;
    &-header {
      position: relative;
      text-align: center;
      min-height: 44px;
      z-index: 100;
      top: 0;
      left: 0;
      width: 100%;
      text-align: center;
      @include font-body-m-bold;
      color: $color-text-primary;
      align-items: center;
      justify-content: center;
      background: white;
      border-bottom: 1px solid $color-border-dim;
      align-content: center;
      &-icon {
        position: absolute;
        align-content: center;
        left: 12px;
        top: 0;
        bottom: 0;
        margin: auto 0;
        width: 40px;
        height: 40px;
      }
    }
    &-content {
      padding: 16px 20px 0;
      height: 100%
    }
  }



  .common-line {
    margin: 0;
  }
  &-header {
    display: flex;
    align-content: center;
    align-items: center;
    justify-content: space-between;
    padding: 16px 0;
    font-weight: bold;
    &-title {
      color: $color-text-primary;
      @include font-heading-xs();
    }
  }
  &-init-part {
    padding: 8px 0 16px;
    &-title {
      @include font-body-m-bold();
      color: $color-text-primary;
      margin-bottom: 4px;
    }
    &-desc {
      color: $color-text-primary;
      @include font-body-m-bold();
      font-weight: 400;
    }
    &-btn {
      margin-top: 12px;
    }
  }

  &-ticket-detail {
    &-base-common {
      border-radius: $radius-xl;
      border: 1px solid $color-border-normal;
      &-title {
        @include font-body-m-bold();
        color: $color-text-primary;
        padding: 8px 16px;
        background: $color-bg-2;
        border-top-right-radius: $radius-xl;
        border-top-left-radius: $radius-xl;
      }
      &-desc {
        padding: 12px 16px;
        &-item {
          margin-bottom: 8px;
          &:last-child {
            margin-bottom: 0;
          }
        }
      }
    }
    &-qrcode {
      margin-top: 24px;
      padding-bottom: 16px;
      width: 100%;
      &-tips {
        color: $color-text-primary;
        @include font-body-m-bold();
        padding-left: 32px;
        padding-right:32px;
        margin-bottom: 4px;
        text-align: center;
      }
      &-item {
        text-align: center;
        &:nth-child(3n + 3) {
          margin-right: 0;
        }
        img {
          height: 120px;
          width: 120px;
          margin-bottom: 4px;
        }
        &-seat-no {
          color: $color-text-primary;
          @include font-body-m-bold();
          margin-bottom: 4px;
        }
        &-voucher-label {
          color: $color-text-primary;
          border-radius: 8px;
          border-color: $color-border-active;
          padding: 4px 12px;
          @include font-caption-1();
          border: 1px solid;
          width: max-content;
          margin: auto;
          cursor: pointer;
        }
        &-voucher-no {
          color: $color-text-primary;
          @include font-body-m-regular();
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }
  }
}
</style>
