<template>
  <div
    v-show="Array.isArray(timeList) && timeList.length"
    :class="`${className}`"
    @click.native="(e) => e.stopPropagation()"
  >
    <div :class="`${className}-item`">
      <div v-for="(time,index) in timeList" :key="index" :class="[`${className}-item-time-item`,time.departure_time === seatData.select_train_time.departure_time && `${className}-item-time-item-select`]" @click="handleSelectTime(time)">
        {{ time.display_departure_time }}
        <div v-show="time.departure_time === seatData.select_train_time.departure_time" :class="`${className}-item-time-item-icon`">
          <img src="https://res.klook.com/image/upload/v1701850084/UED_new/Transport%20Tickets/%E9%A6%96%E9%A1%B5_2306%28Pre-sale%E4%BC%98%E5%8C%96%29/icon_check_outlined_1.svg" />
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator'
import { IconAlarm } from '@klook/klook-icons'

interface seatDataType {
  date?: string
  select_train_time: travelDateType;
}
interface travelDateType {
  travel_date?: string
  departure_station_code?: string
  departure_time: string
  display_arrival_time?: string
  display_departure_time: string
  arrival_time?: string
  arrival_station_code?: string
  train_number?: string
  display_travel_date?: string
  driving_date?: string
}

@Component({ components: { IconAlarm } })
export default class SeatForm extends Vue {
    static displayName = 'ArexSeatForm'
    @Prop() seatData: seatDataType = {
      select_train_time: {
        departure_time: ''
      }
    }
    @Prop() timeList: any[] = []

    get className() {
      return 'seat-form'
    }

    handleSelectTime(time: any) {
      this.$emit('handle-set-seat-form', { select_train_time: time })
    }
}
</script>

<style lang="scss" scoped>
.seat-form {

  &-item {
    padding: 0;
    display: flex;
    flex-wrap: wrap;
    &-time {
      &-item {
        position: relative;
        width: calc(33.33% - 12px);
        margin-right: 12px;
        border-radius: $radius-l;
        border: 1px solid $color-border-normal;
        color: $color-text-primary;
        @include font-body-m-regular();
        margin-top: 12px;
        text-align: center;
        padding: 10px 0;
        cursor: pointer;
        overflow: hidden;
        &-select {
          border: 1px solid $color-brand-primary;
          color: $color-brand-primary;
        }
        &-icon {
          position: absolute;
          z-index: 1;
          bottom: 0px;
          right: 0px;
          border: 11px solid transparent;
          border-bottom-color: $color-brand-primary;
          border-right-color: $color-brand-primary;
          img {
            position: absolute;
            right: -10px;
            top: -2px;
            width: 12px;
            height: 12px;
          }
        }
        &:nth-child(3n) {
          margin-right: 0;
        }
      }
    }

    &-title {
      @include font-body-m-bold ();
      position: relative;
      color: $color-text-primary;
      padding-left: 16px;
      margin-bottom: 12px;
      &::before {
        position: absolute;
        left: 0;
        top: 0;
        bottom: 0;
        margin: auto;
        content: '';
        width: 4px;
        background: $color-brand-primary;
        border-radius: 3px;
        height: 16px;
      };
    }

  }
}
</style>
