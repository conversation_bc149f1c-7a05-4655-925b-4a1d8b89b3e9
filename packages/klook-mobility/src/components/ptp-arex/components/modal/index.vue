<template>
  <div :class="`${className}`">
    <div :class="`${className}-content`">
      <div :class="`${className}-content-tips`" v-if="tips">
        <IconCautionCircle theme="outline" size="16" fill="#2073F9" />
        <div :class="`${className}-content-tips-content`">
          {{ tips }}
        </div>
      </div>
      <div :class="`${className}-content-subtitle`">{{ $t('175209') }}</div>
      <div :class="`${className}-content-date`">
        <klk-input :readonly="true" :value="scheduleDate" :class="`${className}-content-date-input`" :placeholder="$t('175210')" append-icon="icon_time_calendar" @focus="showDate = true" @click.native="(e) => e.stopPropagation()" />
      </div>
      <div v-if="Array.isArray(timeList) && timeList.length" :class="`${className}-content-subtitle`">{{ $t('175211') }}</div>
      <div :class="`${className}-form`">
        <div :class="`${className}-item`">
          <div
            v-for="(time, index) in timeList"
            :key="index"
            :class="[
              `${className}-item-time-item`,
              time.departure_time === seatData.select_train_time.departure_time &&
                `${className}-item-time-item-select`,
            ]"
            @click="handleSelectTime(time)"
          >
            {{ time.display_departure_time }}
            <div
              v-show="time.departure_time === seatData.select_train_time.departure_time"
              :class="`${className}-item-time-item-icon`"
            >
              <img
                src="https://res.klook.com/image/upload/v1701850084/UED_new/Transport%20Tickets/%E9%A6%96%E9%A1%B5_2306%28Pre-sale%E4%BC%98%E5%8C%96%29/icon_check_outlined_1.svg"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
    <div :class="`${className}-footer`">
      <klk-button
        size="normal"
        type="primary"
        :class="`${className}-footer-btn`"
        :disabled="confirmBtnDisable"
        :loading="confirmBtnLoading"
        @click="handleSubmit"
      >
      {{ $t('120345-confirm') }}
      </klk-button>
    </div>
    <client-only>
      <klk-modal
        v-if="showConfirmModal && seatData && seatData.select_train_time"
        :open.sync="showConfirmModal"
        :overlay-closable="false"
        @on-confirm="handleDoubleConfirm"
        @on-cancel="cancelConfirm"
      >
        <div :class="`${className}-confirm-modal`">
          <div :class="`${className}-confirm-modal-title`">
            {{ $t('175212') }}
          </div>
          <div :class="`${className}-confirm-modal-item`">
            
            <p>
              {{ $t('175213', { MMM: seatData.select_train_time.display_travel_date,d:'',YYYY:'' }) }}
            </p>
            <p>
              {{ $t('175214', { 'HH1': `${seatData.select_train_time.display_departure_time}`,'mm1': '','HH2': `${ seatData.select_train_time.display_arrival_time}`,mm2: '' }) }}
            </p>
          </div>
        </div>
      </klk-modal>
      <klk-bottom-sheet
        v-if="showDate"
        show-close
        :visible.sync="showDate"
        :title="$t('12761')"
        height="70%"
      >
        <klk-date-picker
          ref="date"
          :date="selectDate"
          type="date"
          :min-date="new Date()"
          :max-date="maxDate"
          vertical-scroll
          :class="`${className}-content-date-datepicker`"
          @select="handleChangeDate"
        />
      </klk-bottom-sheet>
    </client-only>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from "vue-property-decorator";
import { IconCautionCircle } from '@klook/klook-icons'
import dayjs from 'dayjs'


interface seatDataType {
  date?: string
  select_train_time: travelDateType;
}

interface travelDateType {
  travel_date?: string
  departure_station_code?: string
  departure_time: string
  display_arrival_time?: string
  display_departure_time: string
  arrival_time?: string
  arrival_station_code?: string
  train_number?: string
  display_travel_date?: string
  driving_date?: string
}

@Component({ components: {IconCautionCircle} })
export default class SeatMobileModal extends Vue {
  static displayName = "ArexSeatForm";
  @Prop() seatData: seatDataType = {
      select_train_time: {
        departure_time: ''
      }
    }
  @Prop() confirmBtnDisable: boolean = false;
  @Prop() confirmBtnLoading: boolean = false;
  @Prop() showConfirmModal: boolean = false;

  
  @Prop() timeList: any[] = [];
  @Prop() tips: string = "";
  @Prop() dateDesc: string = "";
  @Prop() date: string = "";
  @Prop() dateTip: string = "";
  @Prop() scheduleDate: string = "";

  showDate: boolean = false
  
  get className() {
    return "seat-mobile-modal";
  }

  get maxDate() {
    return dayjs().add(2, 'month').toDate()
  }

  get selectDate() {
    if (!this.scheduleDate) {
      return dayjs().toDate()
    }
    return dayjs(this.scheduleDate).toDate()
  }

  handleSubmit() {
    if (this.confirmBtnLoading) {
      return;
    }
    this.$emit("submit", this.seatData);
  }

  handleDoubleConfirm() {
    this.$emit('handle-double-confirm')

  }

  cancelConfirm() {
    this.$emit('cancel-confirm')
  }


  handleChangeDate(value: string) {
    this.$emit('handle-change-date', value)
    this.showDate = false
  }

  handleSelectTime(time: any) {
    this.seatData = {
      ...this.seatData,
      select_train_time: time
    }
    this.$emit('handle-set-seat-form', this.seatData)
  }
}
</script>

<style lang="scss" scoped>
.seat-mobile-modal {
  width: 100%;
  height: 100%;
  background-color: #fff;

  &-confirm-modal {
    &-item {
      p {
        @include font-body-m-regular();
        color: $color-text-primary;
        margin-bottom: 8px;
      }
    }
    &-title {
      @include font-heading-xs();
      color: $color-text-primary;
      margin-bottom: 12px;
    }
  }

  ::v-deep .klk-bottom-sheet-body {
    padding-left: 0;
    padding-bottom: 0;
    padding-right: 0;
  }

  &-footer {
    left: 0;
    bottom: 0;
    right: 0;
    height: 60px;
    width: 100%;
    position: fixed;
    background: white;
    &-btn {
      width: 100%;
      margin: 8px 20px;
      width: calc(100% - 40px);
      display: flex;
      justify-content: center;
    }
  }

  &-content {
    height: calc(100% - 40px - 60px);
    overflow-y: scroll;
    &-tips {
      padding: 16px;
      margin-bottom: 24px;
      border-radius: $radius-xl;
      background: $color-info-background;
      display: flex;
      &-content {
        @include font-body-m-regular();
        margin-left: 8px;
        color: $color-info;
        line-height: unset;
      }
    }
    &-title {
      @include font-body-m-bold;
      color: $color-text-primary;
      margin-bottom: 20px;
    }
    &-subtitle {
      @include font-body-m-bold;
      display: flex;
      margin-bottom: 12px;
      color: $color-text-primary;
      &::before {
        display: inline-block;
        content: "";
        background: #f56c6c;
        border-radius: 3px;
        margin: auto 0;
        margin-right: 8px;
        width: 4px;
        height: 1em;
      }
    }
    &-date {
      margin-bottom: 44px;
      &-datepicker {
        width: 100% !important;
      }
    }
  }

  &-form {
  }

  &-item {
    padding: 0 0 72px;
    display: flex;
    flex-wrap: wrap;
    &-time {
      &-item {
        position: relative;
        width: calc(33.3% - 12px);
        margin-right: 12px;
        border-radius: $radius-l;
        border: 1px solid $color-border-normal;
        color: $color-text-primary;
        @include font-body-m-regular();
        margin-top: 12px;
        text-align: center;
        padding: 10px 0;
        cursor: pointer;
        overflow: hidden;
        &-select {
          border: 1px solid $color-brand-primary;
          color: $color-brand-primary;
        }
        &-icon {
          position: absolute;
          z-index: 1;
          bottom: 0px;
          right: 0px;
          border: 11px solid transparent;
          border-bottom-color: $color-brand-primary;
          border-right-color: $color-brand-primary;
          img {
            position: absolute;
            right: -10px;
            top: -2px;
            width: 12px;
            height: 12px;
          }
        }
        &:nth-child(3n) {
          margin-right: 0;
        }
      }
    }
  }
}
</style>
