<template>
  <div :class="`${className}-booking-details-desktop ptp-arex-payment-box`">
    <h2 :class="`${className}-top-group`">
      <span :class="`${className}-top-group-title`">
        <span>
          {{
            arex_data.display_status_key
          }}
        </span>
        <span :style="{color: arex_data.active_status == 'active' ? '#08B371' : '#F09B0A'}">
          {{
            arex_data.display_status_value
          }}
        </span>
      </span>
    </h2>
    <klk-divider class="common-line"></klk-divider>
    <!-- 尚未占座状态 -->
    <div
      v-if="arex_data.active_status === 'inactive'"
      :class="`${className}-init-part`"
    >
      <div :class="`${className}-init-part-title`">
        {{ arex_data.active_title }}
      </div>
      <div :class="`${className}-init-part-desc`">
        {{ arex_data.active_content }}
      </div>
      <klk-button
        :class="`${className}-init-part-btn`"
        size="large"
        type="primary"
        @click="handleClickActive"
      >
        {{ $t("120019-goActive") }}
      </klk-button>
    </div>
    <!-- 尚未占座状态 -->
    <!-- 占座成功状态 -->
    <div
      v-else-if="arex_data.active_status === 'active'"
      :class="`${className}-ticket-detail`"
    >
      <div :class="`${className}-ticket-detail-base-common`">
        <div :class="`${className}-ticket-detail-base-common-title`">
          {{ arex_data.departure_date }}
        </div>
        <div :class="`${className}-ticket-detail-base-common-desc`">
          <div :class="`${className}-ticket-detail-base-common-desc-item`">
            {{ arex_data.departure_station_code }} -
            {{ arex_data.arrival_station_code }}
          </div>
          <div :class="`${className}-ticket-detail-base-common-desc-item`">
            {{ $t("113086") }}：{{ arex_data.departure_time }} -
            {{ arex_data.arrival_time }}
          </div>
        </div>
      </div>
      <div>
        <div>
          <div :class="`${className}-ticket-detail-qrcode-tip`">
            {{ $t("123797") }}
          </div>
          <div
            :class="[
              `${className}-ticket-detail-qrcode`,
              arex_data.ticket_details.length >= 4 &&
                `${className}-ticket-detail-qrcode-morecss`,
            ]"
          >
            <div
              v-for="(item, index) in currentQRCode"
              :key="index"
              :class="[`${className}-ticket-detail-qrcode-item`,currentQRCode.length <= 2 && `${className}-ticket-detail-qrcode-oneItem`]"
            >
              <img v-if="item.voucher_url" :src="item.voucher_url" />
              <div :class="`${className}-ticket-detail-qrcode-item-seat-no`">
                {{ $t("122846") }} {{ item.train_number }}
              </div>
              <div :class="`${className}-ticket-detail-qrcode-item-seat-no`">
                {{ $t("122845") }} {{ item.car_number }}
              </div>
              <div :class="`${className}-ticket-detail-qrcode-item-seat-no`">
                {{ $t("176728") }} {{ item.seat_no }}
              </div>
              <div
                :class="`${className}-ticket-detail-qrcode-item-voucher-label`"
                @click="handleCopy(item.voucher_number)"
              >
                {{ $t("122847") }}
              </div>
            </div>
          </div>
          <div
            v-if="
              arex_data.ticket_details && arex_data.ticket_details.length > 6
            "
            @click="handleMore"
            :class="[
              `${className}-btn`,
              !showMore && `${className}-showMoreBtn`,
            ]"
          >
            <IconChevronDown
              theme="outline"
              size="24"
              :fill="$colorTextPrimary"
            />
          </div>
        </div>
      </div>
    </div>
    <!-- 占座成功状态 -->
    <!-- 选座弹层 -->

    <klk-drawer :visible.sync="showSelectSeat" direction="right">
      <div :class="`${className}-selectSeatDrawer`">
        <div :class="`${className}-selectSeatDrawer-header`">
          <div
            :class="`${className}-selectSeatDrawer-header-close`"
            @click="handleCloseDrawer"
          >
            <IconClose
              theme="outline"
              size="24"
              :fill="[$tokensObj['$color-text-primary']]"
            />
          </div>
          <div :class="`${className}-selectSeatDrawer-header-title`">
            {{ modalTitle }}
          </div>
        </div>
        <div :class="`${className}-selectSeatDrawer-content`">
          <div v-if="tips" :class="`${className}-selectSeatDrawer-content-tips`">
            <IconInformation theme="outline" size="18" fill="#2073F9" />
            <div :class="`${className}-selectSeatDrawer-content-tips-content`">
              {{ tips }}
            </div>
          </div>
          <div :class="`${className}-selectSeatDrawer-content-subtitle`">{{ $t('175209') }}</div>
          <div :class="`${className}-selectSeatDrawer-content-date`">
            <klk-input :readonly="true" :value="scheduleDate" :class="`${className}-selectSeatDrawer-content-date-input`" :placeholder="$t('175210')" append-icon="icon_time_calendar" @focus="handleOpenDate" @click.native="(e) => e.stopPropagation()" />
            <klk-date-picker
              v-if="showDate"
              :date="selectDate"
              ref="date"
              type="date"
              :max-date="maxDate"
              :min-date="new Date()"
              view-switchable
              :class="`${className}-selectSeatDrawer-content-date-datepicker`"
              @select="handleChangeDate"
              @click.native="(e) => e.stopPropagation()"
            />
          </div>

          <div v-if="Array.isArray(timeList) && timeList.length" :class="`${className}-selectSeatDrawer-content-subtitle`">{{ $t('175211') }}</div>
          <seat-form
            :time-list="timeList"
            :seat-data="selectSeatData"
            @handle-set-seat-form="handleSetSeatForm"
          />
        </div>
        <div :class="`${className}-selectSeatDrawer-footer`">
          <klk-button
            size="large"
            type="primary"
            :class="`${className}-selectSeatDrawer-footer-btn`"
            :disabled="confirmBtnDisable"
            :loading="confirmBtnLoading"
            @click="handleConfirm"
          >
            {{ $t("120345-confirm") }}
          </klk-button>
        </div>
      </div>
    </klk-drawer>
    <klk-modal
      v-if="showConfirmModal && selectSeatData && selectSeatData.select_train_time"
      :open.sync="showConfirmModal"
      :overlay-closable="false"
      @on-confirm="handleDoubleConfirm"
      @on-cancel="cancelConfirm"
    >
      <div :class="`${className}-confirm-modal`">
        <div :class="`${className}-confirm-modal-title`">
          {{ $t('175212') }}
        </div>
        <div :class="`${className}-confirm-modal-item`">
          <p>
            {{ $t('175213', { MMM: selectSeatData.select_train_time.display_travel_date,d:'',YYYY:'' }) }}
          </p>
          <p>
            {{ $t('175214', { 'HH1': `${selectSeatData.select_train_time.display_departure_time}`,'mm1': '','HH2': `${ selectSeatData.select_train_time.display_arrival_time}`,mm2: '' }) }}
          </p>
        </div>
      </div>
    </klk-modal>
  </div>
</template>

<script lang="ts">
import { Component, Watch } from "vue-property-decorator";
import { IconClose, IconChevronDown,IconInformation } from "@klook/klook-icons";
import SeatForm from "../components/seat-form/desktop.vue";
import { PtpArexBase } from "../base";
import dayjs from 'dayjs'

@Component({ components: { IconClose, SeatForm, IconChevronDown,IconInformation } })
export default class BookingDetailsPtpArexDesktop extends PtpArexBase {
  static displayName = "DesktopExperiencePtp_arex";



  @Watch('showDate')
  showDateChange() {
    if (this.showDate) {
      window.addEventListener('click', this.openModal)
    } else {
      window.removeEventListener('click', this.openModal)
    }
  }

  get selectDate() {
    if (!this.scheduleDate) {
      return dayjs().toDate()
    }
    return dayjs(this.scheduleDate).toDate()
  }

  beforeDestroy() {
    window.removeEventListener('click', this.openModal)
  }

  get className() {
    return "ptp-arex";
  }

  get maxDate() {
    return dayjs().add(2, 'month').toDate()
  }

  handleOpenDate() {
    this.showDate = true
  }

  openModal(e: any) {
    this.showDate = false
  }

  handleCloseDrawer() {
    this.showSelectSeat = false;
  }


  handleClickActive() {
    this.selectSeatData = {
      select_train_time: {
        departure_time :'',
        display_departure_time: ''
      }
    }
    this.scheduleDate = ''
    this.originData = {};
    this.showSelectSeat = true;
  }

  handleConfirm() {
    if (this.confirmBtnDisable || this.confirmBtnLoading) {
      return;
    }
    this.handleSubmit();
  }

}
</script>

<style lang="scss" scoped>
.ptp-arex {
  &-confirm-modal {
    &-item {
      p {
        @include font-body-m-regular();
        color: $color-text-primary;
        margin-bottom: 8px;
      }
    }
    &-title {
      @include font-heading-xs();
      color: $color-text-primary;
      margin-bottom: 12px;
    }
  }
  &-btn {
    cursor: pointer;
    padding: 6px 7px;
    border-radius: 50%;
    background-color: $color-bg-4;
    margin: auto;
    margin-top: 32px;
    width: fit-content;
  }
  &-showMoreBtn {
    transform: rotate(180deg);
  }
  &-selectSeatDrawer {
    width: 576px;
    height: 100%;
    overflow: hidden;
    &-footer {
      border-top: 1px solid $color-bg-4;
      left: 0;
      bottom: 0;
      right: 0;
      height: 88px;
      width: 100%;
      &-btn {
        margin: 20px 32px;
        width: calc(100% - 40px);
        display: flex;
        justify-content: center;
      }
    }
    &-content {
      height: calc(100% - 66px - 88px);
      overflow-y: scroll;
      padding: 0 32px;
      padding-bottom: 20px;
      &-subtitle {
        @include font-body-m-bold;
        display: flex;
        margin-bottom: 12px;
        color: $color-text-primary;
        &::before {
          display: inline-block;
          content: "";
          background: #f56c6c;
          border-radius: 3px;
          margin: auto 0;
          margin-right: 8px;
          width: 4px;
          height: 1em;
        }
      }
      &-date {
        width: 100%;
        margin-bottom: 44px;
        position: relative;
        &-input {
          min-height: 44px;
        }
        &-datepicker {
          width: 340px;
          top: 100%;
          margin-top: 4px;
          position: absolute;
          right: 32px;
          z-index: 999;
          border: 1px solid rgba(0, 0, 0, 0.1);
        }
      }
      &-title {
        @include font-body-m-bold;
        margin-bottom: 20px;
        color: $color-text-primary;
      }
      &-tips {
        margin-bottom: 32px;
        padding: 16px;
        border-radius: $radius-xl;
        background: $color-info-background;
        display: flex;
        &-content {
          @include font-body-m-regular();
          margin-left: 8px;
          color: $color-info;
          line-height: unset;
        }
      }
    }
    &-header {
      position: relative;
      height: 66px;
      width: 100%;
      left: 0;
      top: 0;
      right: 0;
      padding: 20px 32px;
      &-close {
        position: absolute;
        left: 32px;
        top: 20px;
        cursor: pointer;
      }
      &-title {
        color: $color-text-primary;
        @include font-heading-xs();
        text-align: center;
      }
    }
  }
  &-booking-details-desktop {
    width: 100%;
    max-width: 1160px;
    margin: 0 auto 24px auto;
    background-color: #fff;
    box-shadow: 0px 0px 12px rgba(0, 0, 0, 0.03);
    border-radius: 16px;
    ::v-deep .klk-drawer-content {
      overflow: hidden;
    }
  }
  &-init-part {
    &-title {
      @include font-body-m-bold();
      color: $color-text-primary;
      margin-bottom: 8px;
    }
    &-desc {
      color: $color-text-primary;
      @include font-body-m-bold();
      font-weight: 400;
    }
    &-btn {
      margin-top: 16px;
    }
  }

  &-ticket-detail {
    &-base-common {
      border-radius: $radius-xl;
      border: 1px solid $color-border-normal;
      &-title {
        @include font-body-m-bold();
        color: $color-text-primary;
        padding: 8px 16px;
        background: $color-bg-2;
        border-top-right-radius: $radius-xl;
        border-top-left-radius: $radius-xl;
      }
      &-desc {
        padding: 12px 16px;
        &-item {
          margin-bottom: 8px;
          &:last-child {
            margin-bottom: 0;
          }
        }
      }
    }
    &-qrcode {
      display: flex;
      flex-wrap: wrap;
      margin-top: -12px;
      justify-content: center;
      &-tip {
        text-align: center;
        margin-top: 20px;
        color: $color-text-primary;
        @include font-body-m-bold();
      }
      &-morecss {
        justify-content: unset;
      }
      &-oneItem {
        margin-right: 0 !important;
      }
      &-item {
        margin-top: 12px;
        margin-right: 70px;
        text-align: center;
        width: calc((100% - 140px) / 3);
        &:nth-child(3n + 3) {
          margin-right: 0;
        }
        img {
          height: 120px;
          width: 120px;
          margin-bottom: 4px;
        }
        &-seat-no {
          color: $color-text-primary;
          @include font-body-m-bold();
          margin-bottom: 4px;
        }
        &-voucher-label {
          color: $color-text-primary;
          border-radius: 8px;
          border-color: $color-border-active;
          padding: 4px 12px;
          border: 1px solid;
          width: max-content;
          margin: auto;
          @include font-caption-1();
          cursor: pointer;
        }
        &-voucher-no {
          color: $color-text-primary;
          @include font-body-m-regular();
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }
  }
  &-payment-box {
    padding: 30px;
  }
  &-top-group {
    display: flex;
    justify-content: space-between;
    align-items: center;
    &-title {
      @include font-heading-s();
      color: $color-text-primary;
    }
  }
  .common-line {
    margin: 24px 0 24px 0;
  }
}
</style>
