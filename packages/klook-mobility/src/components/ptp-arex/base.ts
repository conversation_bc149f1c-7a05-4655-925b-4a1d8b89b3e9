import { Component, Prop, Vue, Watch } from 'vue-property-decorator'
import { copyToClipboard } from '../../utils'
import Toast from '@klook/klook-ui/lib/toast'
import dayjs from 'dayjs'
import isEmpty from 'lodash/isEmpty'

export namespace HomePage {

  export interface Page {
    meta: Meta
    track: Track
    body: PageBody
  }

  export interface Meta {
    type: string
    track: Track
    placeholder?: string,
    name: string
  }

  export interface Track {
  }

  export interface PageBody {
    sections: SectionItem[]
  }

  export interface FieldsConfigIF {
    key: string
    value: any
  }

  export interface SectionItem {
    fields: FieldsConfigIF[]
    meta: Meta
    data: SectionData
    body: SectionBody
  }
  export interface AllFieldsIF {
    activity_package_promotion?: number,
    activity_custom_config_hide_credit?: number,
    activity_custom_config_hide_price_guarantee?: number,
  }

  export interface SectionData {
    title: string
    sub_title: string
    more_title: string
    more_deep_link: string
    bottom_button_title: string
    bottom_button_deep_link: string
    is_navigation: boolean
  }

  export interface SectionBody {
    tabs?: BodyContent[]
    content?: BodyContent
  }

  export interface BodyContent {
    handler: string
    data_type: string
    load_type: string
    src?: string
    track: Track
    data: BodyContentData
  }

  export interface BannerSection {
    search_placeholder: string,
    cart_number: number,
    title: string,
    subtitle: string,
    banners: {
      type: string,
      deep_link: string,
      video: {
        full_button_title: string,
        preview_video_src: string,
        full_video_src: string,
        preview_image_src: string
      },
      image: {
        src: string
      }
    }[],
  }

  export interface CampaignSection {
  }

  export interface themeListSection {
    showing_theme_count: number,
    more_title: string,
    themes: {
      title: string,
      subtitle: string,
      deep_link: string,
      background_src: string
    }[]
  }

  export interface fnbCard {
    card_tags: any,
    fnb_cost: string,
    title: string,
    deep_link: string,
    cuisines: [],
    average_price: string,
    location: string,
    score: string,
    participants_format: string,
    review_total: string,
    image_list: string[],
    package_title: string,
    currency: Data.Currency,
    sell_price: {
      currency: string,
      symbol: string,
      amount_display: string
    },
    reservation_info: {
      day_type: number,
      earliest_reservation_date: string,
      earliest_reservation_time: string,
      has_reservation: boolean,
      package_id: number,
      sold_out: boolean
    },
    package_info: {
      discount: number,
      has_package: boolean,
      market_price: string,
      name: string,
      package_id: number,
      sell_price: string,
      sold_out: boolean
    }
  }

  export interface fnbEntranceSection {
    items: fnbCard[]
  }

  export interface activities {
    showing_count: number,
    more_title: string,
    items: activityCard[],
    card_list: activityCard[]
  }

  export type BodyContentData = BannerSection
    & CampaignSection
    & themeListSection
    & topDestination
    & activities
    & fnbEntranceSection
    & promote
    & verticalEntrance
    & nearbySection
    & seoLink[]

  export interface promote {
    title: string,
    title_color: string,
    background_src: string,
    button_title: string,
    button_background_color: string,
    button_title_color: string,
    type: string,
    deep_link: string
  }

  export interface nearbySection {
    items: activityCard[],
    more_button_deep_link: string,
    more_button_text: string,
    title: string,
    view_all_deep_link: string,
    view_all_text: string
  }

  export interface verticalEntrance {
    menus: verticalMenu[]
  }

  export interface verticalMenu {
    icon_src: string,
    title: string,
    type: 'deep_link' | 'menu_list',
    deep_link: string,
    sub_menu: submenu,
    business_name: string
  }

  export interface submenu {
    title: string,
    items: {
      title: string,
      deep_link: string,
      tag_title: string
    }[]
  }

  export interface topDestination {
    title: string,
    background_src: string,
    deep_link: string
  }

  export interface tag {
    background_color: string,
    text_color: string,
    text: string,
    [key: string]: any
  }

  export interface price {
    currency: string,
    symbol: string,
    amount_display: string
  }

  export interface activityCard {
    activity_id: number,
    country_id?: number,
    city_id: number,
    start_time: string,
    location_title: string,
    deep_link: string,
    image_src: string,
    have_video: boolean,
    title: string,
    tags: tag[],
    review_star: string,
    review_hint: string,
    market_price: price,
    sell_price: price,
    available_hint: string,
    can_immediately: boolean,
    distance?: string,
    free_text?: string,
    what_we_love?: string,
    icon_info?: {
      image_src: string,
      tips_title: string,
      tips_desc: string,
      type: 'new_activity' | 'editor_choice'
    }
  }

  export interface seoLink {
    heading: string,
    linking: {
      key_word: string,
      url: string
    }[]
  }

  export interface selectSeatType {
    date?: string;
    select_train_time?: travelDateType;
  }

  export interface stationType {
    code: string;
    name: string;
  }
}
interface travelDateType {
  travel_date?: string
  departure_station_code?: string
  departure_time: string
  display_arrival_time?: string
  display_departure_time: string
  arrival_time?: string
  arrival_station_code?: string
  train_number?: string
  display_travel_date?: string
  driving_date?: string
}


@Component
export class PtpArexBase extends Vue {
  @Prop() hideSection!: Function
  @Prop() setSectionState!: Function
  @Prop() state!: string
  @Prop() section!: HomePage.SectionItem

  arex_data: any = {}
  originData: HomePage.selectSeatType = {}

  selectSeatData: HomePage.selectSeatType = {
    select_train_time: {
      departure_time: ''
    }
  }

  departureStation: HomePage.stationType[] | undefined = []

  confirmBtnLoading: boolean = false

  scheduleDate: string = ''

  // 二次确认弹窗
  showConfirmModal: boolean = false
    
  // 是否展示 展开全部 按钮
  showMore: boolean = false

  showDate: boolean = false
 
  showSelectSeat: boolean = false

  timeList: travelDateType[] = []
  tips: string = ''
  modalTitle: string = ''

  date_tip: string = ''
  date_desc: string = ''
  date: string = ''


  @Watch('showSelectSeat')
  showSelectSeatChange(val: boolean) {
    if (!val) {
      this.timeList = []
      this.selectSeatData = {
        select_train_time : {
          departure_time: ''
        }
      }
    }
  }

  @Watch('scheduleDate', {immediate: true})
  scheduleDateChange(val: string) {
    this.timeList = []
    this.selectSeatData = {
      select_train_time : {
        departure_time: ''
      }
    }
    this.getTimeList()
  }

 
  // 楼层数据
  get sectionData() {
    return this.section || {}
  }

  get sectionBody(): HomePage.SectionBody {
    return this.sectionData.body || {}
  }

  get sectionContent(): HomePage.BodyContent {
    return (this.sectionBody as any).content || {}
  }

  get businessData(): any {
    return (this.sectionContent as any).data || {}
  }

  get isAsync() {
    return this.sectionContent.load_type === 'async'
  }

  get bookingRefNo() {
    return this.businessData.booking_no || ''
  }


  get confirmBtnDisable() {
    let disable: boolean = false

    if (!(this.selectSeatData?.select_train_time?.departure_time)) {
      disable = true
    }
    if (!this.scheduleDate) {
      disable = true
    }
    return disable
  }


  get currentQRCode() {
    const qrcodeArr = this.arex_data?.ticket_details || []
    return this.showMore ? qrcodeArr.slice(0, 6) : qrcodeArr
  }

  initShowMore() {
    const qrcodeArr = this.arex_data?.ticket_details || []
    if (qrcodeArr.length > 6) {
      this.showMore = true
    }
    else {  
      this.showMore = false
    } 
  }

  handleChangeDate(value: string) {
    this.scheduleDate = dayjs(value).format('YYYY-MM-DD')
    this.showDate = false
  }


  handleMore() {
    this.showMore = !this.showMore
  }

  cancelConfirm() {
    this.showConfirmModal = false
    this.confirmBtnLoading = false
  }

  handleSetSeatForm(data: any) {
    this.selectSeatData = data
  }

  handleSubmit() {
    this.confirmBtnLoading = true
    this.showConfirmModal = true
  }

  handleDoubleConfirm(data: any) {
    this.showConfirmModal = false
    this.confirmActive(this.selectSeatData.select_train_time)
  }


  async getArexStatus() {
    try {
      const res = await this.$axios.get('/v1/np2pbffserv/booking/detail', {
        params: { booking_ref_no: this.bookingRefNo, provider_id: '1208' },
      });
      if (res.data && res.data.result) {
        this.arex_data = res.data.result
        this.$nextTick(() => {
          this.initShowMore()
        })
      }
    } catch (error: any) {
      error.message && this.$alert(error.message)

    }
  }

  // 复制功能
  async handleCopy(text: string) {
    const result = await copyToClipboard(text)

    if (result) {
      Toast(this.$t('copy_success'))
    }
  }


  async getTimeList() {
    try {
      const res = await this.$axios.post('/v2/np2pbffserv/schedule', { provider_id: '1208',date: this.scheduleDate,booking_ref_no: this.bookingRefNo });
      if (res.data && !isEmpty(res.data.result)) {
        const { train_list = [], tips, title, date_tip, date_desc, date } = res.data.result
        this.timeList = train_list
        this.tips = tips
        this.modalTitle = title
        this.date_tip = date_tip
        this.date_desc = date_desc
        this.date = date
      } else {
        this.$alert(res.data?.error?.message)
      }
    } catch (error: any) {
      error.message && this.$alert(error.message)

    }
  }

  async confirmActive(select_train_time: any) {
    try {
      const res = await this.$axios.post('/v1/np2pbffserv/ticket/active', { booking_ref_no: this.bookingRefNo, provider_id: '1208', select_train_time });
      console.log('jolon res',res)
      if (res.data && res.data.success) {
        // 激活成功
        this.confirmBtnLoading = false
        this.showSelectSeat = false
        this.getArexStatus()
      } else {
        this.confirmBtnLoading = false
        this.$alert(res.data?.error?.message || '网络错误，请重试', this.$t('120020'))
      }
    } catch (error: any) {
      error.message && this.$alert(error.message)
      console.log('jolon error',error)
      this.confirmBtnLoading = false
    }
  }


  mounted() {
    this.getArexStatus()
    this.getTimeList()
  }
}
