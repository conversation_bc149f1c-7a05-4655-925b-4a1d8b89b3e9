import localStorageEx from './localstorage'

const l = localStorageEx()

const PRIVATE_TRANSFER_COUNTRY = 'privateTransferCountry'
const ONE_DAY = 86400 * 1000

function getAntiSpiderHead(vm: any) {
  if (vm.$store.state.klook.host === 'www.klook.cn') {
    return {}
  }
  return {
    script: []
  }
}

// 接送机首页head配置
export function homeHead(vm: any) {
  const d = String(vm.$t('6903-airport_transfers_seo_home_meta_desc'))
  const t = String(vm.$t('6902-airport_transfers_seo_home_title'))
  const description = vm.$description(d)
  const headImage = 'https://res.klook.com/image/upload/fl_lossy.progressive,q_85/c_fill,,w_1920,h_460/v1569233407/banner/ake1fohnsklk9ywmlynd.jpg'

  let langPath = ''
  if (vm.$store.state.klook.language !== 'en') {
    langPath = `/${vm.$store.state.klook.language}`
  }

  const antiSpiderHead = getAntiSpiderHead(vm)
  return {
    bodyAttrs: {
      'data-spm-page': `Mobility_Aggregate?oid=categorytree_46&ext=${encodeURIComponent(JSON.stringify({ object_id: 'category_13' }))}`
    },
    title: t,
    meta: [
      description,
      vm.$setMeta({ property: 'og:locale', content: `${vm.$store.state.klook.language}` }),
      vm.$setMeta({ property: 'og:type', content: 'product.group' }),
      vm.$setMeta({ property: 'og:title', content: t }),
      vm.$setMeta({ property: 'og:description', content: d }),
      vm.$setMeta({ property: 'og:url', content: `https://${vm.$store.state.klook.host}${langPath}/airport-transfers/` }),
      vm.$setMeta({ property: 'og:image', content: `${headImage}` }),
      vm.$setMeta({ property: 'og:site_name', content: 'seo.site.name' }, true),

      vm.$setMeta({ name: 'twitter:title', content: t }),
      vm.$setMeta({ name: 'twitter:description', content: d }),
      vm.$setMeta({ name: 'twitter:site', content: ' @KlookTravel' }),
      vm.$setMeta({ name: 'twitter:image', content: `${vm.headImage}` })
    ],
    link: vm.$canonical(`/${langPath}airport-transfers/`),
    ...antiSpiderHead
  }
}

// OTA配置数据
export function appDownloadConfig() {
  return {
    location: {
      desktop: 'AirportTransfer-VerticalMedium',
      mobile: 'AirportTransfer-VerticalTop'
    }
  }
}

// 获取和设置客源国
export function getSourceCountryCode(vm: any) {
  const auth = window.__KLOOK__.state.auth
  const platform = window.__KLOOK__.state.klook.platform
  let countryCode = ''
  if (l.getItem(PRIVATE_TRANSFER_COUNTRY)) {
    countryCode = l.getItem(PRIVATE_TRANSFER_COUNTRY)
  } else {
    vm.$axios.$get('/v1/transferairportapisrv/source_country_code')
      .then((res: any) => {
        if (res && res.data && res.data.result) {
          countryCode = res.data.result
          l.setItem(PRIVATE_TRANSFER_COUNTRY, countryCode, ONE_DAY)
        }
      })
      .catch((error: any) => {
        vm.$logquery.service({
          timestamp: Date.now(),
          level: 'E',
          tag: 'client',
          uid: auth.user ? auth.user.globalId : '',
          message: '(airport transfer source country code error)'
        }, {
          headers: { 'X-Platform': platform }
        })
      })
  }
}

// 组件之间交互的方法
export function eventBind(vm: any, eo: any) {
  const homeRef = vm.$refs['airport-transfer__home'] as any
  if ('publicTransferMobile' in eo) {
    const search_form_mobile = vm.$refs['airport-transfer__airport_transfer_search_form'] as any
    const search_word = eo.publicTransferMobile.searchWord
    search_form_mobile.eventListener(search_word)
  }
  if ('publicTransfer' in eo) {
    const search_form = vm.$refs['airport-transfer__airport_transfer_search_form'] as any
    const airport_info = eo.publicTransfer.airportInfo
    search_form.eventListener(airport_info)
  }
  if ('searchFormMobile' in eo) {
    const public_transfer_mobile = homeRef.$refs['airport-transfer__home_public_transfer'] as any
    const airport_info1 = eo.searchFormMobile.airportInfo
    public_transfer_mobile && public_transfer_mobile[0].eventListener(airport_info1)
  }
  if ('searchForm' in eo) {
    const public_transfer = homeRef.$refs['airport-transfer__home_public_transfer'] as any
    const airport_info2 = eo.searchForm.airportInfo
    public_transfer && public_transfer[0].eventListener(airport_info2)
  }
}
