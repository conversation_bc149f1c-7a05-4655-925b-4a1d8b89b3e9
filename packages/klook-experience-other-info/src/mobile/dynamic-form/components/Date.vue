<template>
  <div v-click-outside="close" class="dynamic-form-date">
    <k-input
      :value="dateText"
      :append-icon="arrowIcon"
      readonly
      :disabled="disabled"
      :placeholder="data.hint || ''"
      style-type="lined"
      @click.native="handleFocus"
    />
    <kDateTimePicker
      ref="datePicker"
      :open.sync="showDatePicker"
      :value="calendarDate"
      :title="data.name"
      :type="dateType"
      :first-day-of-week="1"
      :min-date="min"
      :max-date="max"
      @change="handleChange"
    />
  </div>
</template>

<script>
import dayjs from 'dayjs'
import ClickOutside from 'vue-click-outside'
import Input from '@klook/klook-ui/lib/input'
import DateTimePicker from '@klook/klook-ui/lib/datetime-picker'
import locale from '@klook/klook-ui/lib/locale'

import '@klook/klook-ui/lib/styles/components/icon.scss'
import '@klook/klook-ui/lib/styles/components/input.scss'
import '@klook/klook-ui/lib/styles/components/datetime-picker.scss'
import '@klook/klook-ui/lib/styles/components/picker.scss'

import { isServer, getKlkLanguage } from '@src/utils'
import { getStandardDateFormat } from '@src/utils/datetime'

if (!isServer) {
  const lang = getKlkLanguage()
  const _lang = /en/.test(lang) ? 'en' : lang
  import(`@klook/klook-ui/lib/locale/lang/${_lang}`).then(lang => {
    locale.use(lang)
  }).catch(err => console.log(lang, _lang, err))
}

export default {
  name: 'DynamicFormDate',
  components: {
    KInput: Input,
    kDateTimePicker: DateTimePicker,
  },
  inject: {
    $formItem: {
      default: null,
    },
  },
  directives: {
    ClickOutside
  },
  props: {
    value: {
      type: String,
      default: '',
    },
    data: {
      type: Object,
      default: () => ({}),
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    shouldCheckDateValid: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      showDatePicker: false,
    }
  },
  computed: {
    dateType() {
      const type = this.data.style.type
      const typeMap = {
        1: 'date',
        2: 'datetime',
        9: 'time',
      }
      return typeMap[type]
    },
    dateText() {
      const o = {
        date: 1,
        datetime: 2,
        // time: 24,
      }
      if (this.dateType === 'time') {
        return this.value
      }
      return this.value
        ? getStandardDateFormat(
            dayjs(this.value).format('YYYY-MM-DD'),
            this.$t.bind(this),
            getKlkLanguage(this),
            2,
          )
        : ''
    },
    min() {
      const { type, min_date } = this.data.style.match_rule || {}
      return type === 1 && min_date ? new Date(min_date) : new Date('1000-01-01')
    },
    max() {
      const { type, max_date } = this.data.style.match_rule || {}
      return type === 1 && max_date ? new Date(max_date) : new Date('3000-01-01')
    },
    calendarDate() {
      if (this.value) {
        return dayjs(this.value).toDate()
      }

      const { default_date } = this.data.style.match_rule || {}
      if (default_date) {
        const defaultDate = this.validateDate(default_date) ? default_date : this.min
        return dayjs(defaultDate).toDate()
      }

      return null
    },
    arrowIcon() {
      return this.showDatePicker ? 'icon_navigation_chevron_up_xs' : 'icon_navigation_chevron_down_xs'
    },
  },
  watch: {
    value: {
      immediate: true,
      handler() {
        // 检查是否合法，不合法则重置为空
        // 自助修改订单不需要检查有效性，用户从面板上选择即可
        if (
          this.shouldCheckDateValid &&
          this.value &&
          (this.dateType === 'date' || this.dateType === 'datetime') &&
          !this.validateDate(this.value)
        ) {
          this.update('')
        }
      },
    },
  },
  methods: {
    handleChange(date) {
      const formatStyle = {
        date: 'YYYY-MM-DD',
        datetime: 'YYYY-MM-DD HH:mm',
        time: 'HH:mm',
      }
      if (date) {
        const dateText = dayjs(date).format(formatStyle[this.dateType])
        this.update(dateText)
      }
      this.showDatePicker = false
    },
    handleFocus() {
      if (!this.disabled) {
        this.showDatePicker = true
      }
    },
    close(e) {
      this.showDatePicker = false
    },
    validateDate(dateText) {
      return !dayjs(dateText).isBefore(this.min, 'day') && !dayjs(dateText).isAfter(this.max, 'day')
    },
    update(dateText) {
      this.$emit('input', dateText)
      this.$emit('change', dateText)

      // fix: date组件会在input blur的时候校验，这个时候数据还没有改变，所以这里再校验一次
      if (this.$formItem) {
        this.$formItem.$emit('change')
      }
    },
  },
}
</script>

<style lang="scss">
.dynamic-form-date {
  padding-right: 6px;

  .klk-input-inner .klk-input-append svg {
    color: $color-text-secondary;
    font-size: $fontSize-body-m;
  }

  .klk-input-disabled .klk-input-append svg {
    color: $color-text-disabled;
  }
}
</style>

<style lang="scss" scoped>
.dynamic-form-date {
  position: relative;

  .klk-date-picker {
    position: absolute;
    left: 0;
    top: 50px;
    border: 1px solid $color-border-normal;
    /* stylelint-disable */
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    z-index: 88;
  }
}
</style>
