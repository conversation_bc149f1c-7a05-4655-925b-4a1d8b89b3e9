<template>
  <div class="dynamic-form-checkbox">
    <!-- <k-checkbox v-model="checkVal" :disabled="disabled" @change="val => $emit('change', val)" />
    <p
      v-if="data.hint"
      class="dynamic-form-checkbox-hint"
      @click="handleClick"
    >
      {{ data.hint }}
    </p> -->
    <k-checkbox
      v-model="checkVal"
      size="small"
      :disabled="disabled"
      @change="val => $emit('change', val)"
    >
      <span v-if="data.hint" class="dynamic-form-checkbox-hint" @click="handleClick">
        {{ data.hint }}
      </span>
    </k-checkbox>
  </div>
</template>

<script>
import Checkbox from '@klook/klook-ui/lib/checkbox'
import '@klook/klook-ui/lib/styles/components/checkbox.scss'

export default {
  name: 'DynamicFormCheckbox',
  components: {
    KCheckbox: Checkbox,
  },
  inject: {
    $formItem: {
      default: null,
    },
  },
  props: {
    value: {
      type: String,
      default: '0',
    },
    data: {
      type: Object,
      default: () => ({}),
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    checkVal: {
      get() {
        return this.value === '1' || this.value === true
      },
      set(val) {
        this.$emit('input', val ? '1' : '0')
      },
    },
  },
  methods: {
    handleClick() {
    //   this.checkVal = !this.checkVal

    //   if (this.$formItem) {
    //     this.$formItem.$emit('change')
    //   }
    },
  },
}
</script>

<style lang="scss">
.dynamic-form-checkbox {
  .klk-checkbox {
    margin: 0;

    .klk-checkbox-base {
      box-sizing: border-box;
    }

    .klk-checkbox-label {
      height: 100%;
      overflow-y: auto;
    }
  }
}
</style>

<style lang="scss" scoped>
.dynamic-form-checkbox {
  display: flex;
  max-height: 126px;
  padding: 16px 0 15px 15px;
  background: $color-bg-widget-darker-1;
  line-height: 20px;
  overflow-y: auto;
  font-size: $fontSize-body-m;

  .klk-checkbox {
    margin-right: 8px;
  }

  &-hint {
    cursor: pointer;
  }
}
</style>
