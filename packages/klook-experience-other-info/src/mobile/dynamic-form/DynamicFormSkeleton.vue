<template>
  <div class="dynamic-form-skeleton">
    <div class="row">
      <div class="item">
        <BaseSkeleton
          skeleton-gap="4px"
          skeleton-width="100px"
          skeleton-height="16px"
        />
        <BaseSkeleton
          skeleton-width="100%"
          skeleton-height="44px"
        />
      </div>
    </div>
    <div class="row">
      <div class="item">
        <BaseSkeleton
          skeleton-gap="4px"
          skeleton-width="100px"
          skeleton-height="16px"
        />
        <BaseSkeleton
          skeleton-width="100%"
          skeleton-height="44px"
        />
      </div>
    </div>
  </div>
</template>

<script>
import BaseSkeleton from '@src/utils/components/Skeleton.vue'

export default {
  name: 'DynamicFormSkeleton',
  components: {
    BaseSkeleton
  }
}
</script>

<style lang="scss" scoped>
.row {
  margin-bottom: 24px;
}
</style>
