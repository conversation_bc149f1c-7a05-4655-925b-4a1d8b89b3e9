<template>
  <p v-if="show">
    <slot :label="item.name" :value="value" :required="required" />
  </p>
</template>

<script>
export default {
  name: 'DynamicFormPreview',
  props: {
    item: {
      type: Object,
      default: () => ({}),
    },
  },
  computed: {
    type() {
      const { style } = this.item || {}
      return Number(style.type)
    },
    show() {
      // 平铺组件不需要展示
      return [0, 1, 2, 3, 4, 5, 7, 8, 9].includes(this.type)
    },
    value() {
      const content = this.item.content || ''
      const operation = this.item.operation || []

      // select、multi_select
      // TODO: 多语言分隔符（因为自助修改订单没有这个场景，所以产品没有给出显示规则）
      if ([3, 4].includes(this.type)) {
        return operation.map(v => v.name).join(', ')
      }

      // checkbox
      // TODO: 因为自助修改订单没有这个场景，所以产品没有给出显示规则
      if (this.type === 5) {
        return content === '1' ? true : false
      }

      // mobile
      if (this.type === 8) {
        const selection = operation[0] || {}
        let countryCode = selection ? selection.country_number : ''

        // countryCode 兼容处理
        if (!countryCode && selection && selection.name) {
          try {
            countryCode = selection.name.split('+')[1].split(')')[0]
          } catch (error) {
            countryCode = ''
          }
        }

        if (!countryCode && !content) {
          return ''
        }

        return `${countryCode}-${content}`
      }

      // text、date、datetime、input、time
      return content
    },
    required() {
      const { required } = this.item.style || {}
      return !!required
    }
  },
}
</script>
