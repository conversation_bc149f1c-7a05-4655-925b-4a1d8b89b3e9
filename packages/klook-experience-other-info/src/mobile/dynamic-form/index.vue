<template>
  <div v-if="componentList.length > 0" class="dynamic-form-wrap mobile">
    <!-- checkbox、textarea、phone需要占满一行 -->
    <div
      v-for="item in componentList"
      :key="item.data.id"
      class="dynamic-form-module"
      :class="{ 'is-no-name': item.data.name === '' }"
    >
      <DynamicFormTitle :data="item.data" :phone-verified="phoneVerified" />

      <!-- 独立组件，在里面有formItem -->
      <components
        v-if="SOLE_TYPES.includes(item.data.style.type)"
        v-bind="$attrs"
        :is="item.component"
        :data="item.data"
        :prop="item.prop"
        @change="val => handleChange(item.data, val)"
        @input="val => handleInput(item.data, val)"
        @verifiedChange="handleVerifiedChange"
      />

      <DynamicFormItem
        v-else
        :data="item.data"
        :prop="item.prop"
        :value-type="getValueKey(item)"
        :spm-amount="item.spmAmount"
      >
        <components
          v-bind="$attrs"
          :is="item.component"
          v-model="item.data[getValueKey(item)]"
          :data="item.data"
          @change="val => handleChange(item.data, val)"
          @input="val => handleInput(item.data, val)"
        />
      </DynamicFormItem>
    </div>
  </div>
</template>

<script>
// 此组件的作用：
// 1.清洗数据，然后分发给各个组件
// 2.如果是group类型的表单，则打平成数组的形式
// 数据结构：{ type: 组件类型, data: 表单数据, prop: 表单检验的prop }
import {
  OPERATION_TYPES,
  FULL_LINE_TYPES,
  WRAPPER_TYPES,
  MWEB_TYPE_MAP,
  SOLE_TYPES,
  SPM_AMOUNT_TYPES,
  GROUP_TYPES,
  shouldCheckOperations
} from '@src/utils'
import DynamicFormTitle from './DynamicFormTitle.vue'
import DynamicFormItem from './DynamicFormItem.vue'
import DynamicFormInput from './components/Input.vue'
import DynamicFormDate from './components/Date.vue'
import DynamicFormSelect from './components/Select.vue'
import DynamicFormMultiSelect from './components/MultiSelect.vue'
import DynamicFormCheckbox from './components/Checkbox.vue'
import DynamicFormTiled from './components/Tiled.vue'
import DynamicFormTextarea from './components/Textarea.vue'
import DynamicFormMobile from './components/Mobile.vue'
import DynamicFormMulti from './components/Multi.vue'
// import DynamicFormDatetime from './components/Datetime.vue'
// import DynamicFormTimePicker from './components/TimePicker.vue'

// 引入这个动画css解决组件卡顿的问题
import '@klook/klook-ui/lib/styles/transitions.scss'

export default {
  name: 'DynamicForm',
  components: {
    DynamicFormTitle,
    DynamicFormItem,
    DynamicFormInput,
    DynamicFormDate,
    DynamicFormSelect,
    DynamicFormMultiSelect,
    DynamicFormCheckbox,
    DynamicFormTiled,
    DynamicFormTextarea,
    DynamicFormMobile,
    DynamicFormMulti,
    // DynamicFormDatetime,
    // DynamicFormTimePicker
  },
  props: {
    // other-info 单个表单的数据
    data: {
      type: Object,
      default: null,
    },
    // 表单检验的最上层 prop
    prop: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      SOLE_TYPES,
      FULL_LINE_TYPES,
      componentList: [],
      phoneVerified: false,
    }
  },
  watch: {
    data: {
      immediate: true,
      handler(val) {
        if (val) {
          this.componentList = this.getComponentList()
        }
      },
    },
  },
  methods: {
    getValueKey(item) {
      return OPERATION_TYPES.includes(item.data.style.type) ? 'operation' : 'content'
    },
    getComponentList() {
      const { data, prop } = this
      const result = []

      // 没数据则返回空节点
      if (!this.isValid(data)) {
        return result
      }

      result.push({
        data,
        prop,
        type: data.style.type,
        component: MWEB_TYPE_MAP[data.style.type],
      })

      // 进行深度优先遍历，然后打平数据
      // 注意：遍历的时候需要 ‘touch’ 需要的数据，便于响应式变化
      this.traverseChild(result, data, prop)

      // 把 wrapper 类型的表单剔除掉
      // 注意这里是通过is-group来实现tiled的平铺的，实际上外层并没有tiled
      return result.filter(item => !WRAPPER_TYPES.includes(item.type))
    },
    isValid(item) {
      // 判断 item 是一个节点
      // 为了性能，只在开发环境检查，生产环境跳过检查
      if (process.env.NODE_ENV !== 'dev') {
        return true
      }

      const validType = Object.keys(MWEB_TYPE_MAP)
      return item && item.style && item.style.type && validType.includes(item.style.type)
    },
    traverseChild(arr, data, prop) {
      if (shouldCheckOperations(data)) {
        // 注意，is_group 的含义是是否还有隐藏的other-info需要展示
        // 展示 operation 里面的内容
        data.operation &&
          data.operation.forEach((selection, index) => {
            if (this.isValid(selection)) {
              const newProp = `${prop}.operation[${index}]`
              const amount = SPM_AMOUNT_TYPES.includes(data.style.type) ? data.operation.length : null
              arr.push({
                data: selection,
                prop: newProp,
                type: selection.style.type,
                component: MWEB_TYPE_MAP[selection.style.type],
                spmAmount: amount,
              })
              this.traverseChild(arr, selection, newProp)
            }
          })
      }
    },
    handleChange(data, val) {
      this.componentList = this.getComponentList()
      this.$emit('change', data, val)
    },
    handleInput(data, val) {
      this.$emit('input', data, val)
    },
    handleVerifiedChange(val) {
      this.phoneVerified = val
    },
  },
}
</script>

<style lang="scss">
/* 因为项目全局引入了css样式，对ui组件有影响，在此处覆盖 */
.dynamic-form-wrap.mobile {
  select:hover,
  select:focus,
  input[type='text']:hover,
  input[type='password']:hover,
  input[type='text']:focus,
  input[type='password']:focus,
  textarea:focus {
    border: none !important;
  }

  input::-webkit-outer-spin-button,
  input::-webkit-inner-spin-button {
    -webkit-appearance: none;
  }

  input[type='number'] {
    -moz-appearance: textfield;
  }

  input {
    /* stylelint-disable */
    font-size: inherit;
  }

  .klk-select .klk-bottom-sheet .klk-bottom-sheet-body .klk-input input[type='text'] {
    padding: 0 16px;
  }

  button {
    height: auto;
  }

  .klk-select-multiple {
    margin-bottom: 0 !important;
    scrollbar-width: none;
    -ms-overflow-style: none;

    ::-webkit-scrollbar {
      display: none;
    }
  }

  // 覆盖原生样式
  input {
    /* stylelint-disable CssSyntaxError */
    color: currentColor;
  }

  .klk-checkbox-base {
    box-sizing: border-box;
  }

  .klk-input-words-count {
    bottom: 12px !important;
  }

  .klk-select {
    .klk-poptip-popper {
      background-color: $color-bg-widget-normal;
    }
  }

  .klk-input textarea {
    height: 76px !important;
  }

  .klk-form-item:not(:last-child) {
    margin-bottom: 0;
  }

  // 如果没有标题，则减小与上面表单的间距
  .is-no-name {
    margin-top: -14px;
  }
}
</style>

<style lang="scss" scoped>
.dynamic-form {
  &-module {
    margin-bottom: 24px;
  }
}
</style>
