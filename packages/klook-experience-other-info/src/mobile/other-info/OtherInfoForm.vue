<template>
  <!--公共表单，为绑定值未拆模块-->
  <div class="other-info-form-mobile">
    <p v-if="data.name" class="other-info-form-mobile-title">
      {{ data.name }}
    </p>
    <TravellerSelector
      v-if="!!customInfo.traveler_is_show && travellerList.length > 0"
      :section-type="sectionType"
      :traveller-list="travellerList"
      class="other-info-form-mobile-selector"
      @chooseData="chooseTraveller"
    />
    <k-form :model="otherInfoData" ref="form" class="other-info-form-mobile-content">
      <DynamicForm
        v-for="(item, index) in (otherInfoData.data || [])"
        :key="`${item.field_key}-${item.id}`"
        :data="item"
        :prop="`data[${index}]`"
        :disabled="disabled"
        :original-phone="originalPhone"
        :should-check-date-valid="shouldCheckDateValid"
        @change="handleChange"
        @input="handleInput"
      />
      <div
        v-if="data.bottom_info && !hideBottomInfo && checkboxContent"
        class="other-info-form-mobile-checkbox js-spm-checkbox-in-form"
        :data-spm-module="
          `${inhouseType}?ext=${JSON.stringify({
            ModuleType: sectionType,
            ClickType: true,
          })}`
        "
        data-spm-virtual-item="__virtual?trg=manual"
      >
        <k-checkbox :value="data.bottom_info.is_checkbox_selected" @change="checkBoxChange">
          {{ checkboxContent }}
        </k-checkbox>
      </div>
    </k-form>

    <ShipmentModal
      v-if="isShipment && shipmentList.length > 0"
      :visible.sync="shipmentVisible"
      :shipment-list="shipmentList"
      :other-info-list="data.info_items"
      :id="selectedShipmentId"
      @chooseData="chooseShipment"
    />
  </div>
</template>

<script>
import { Form } from '@klook/klook-ui/lib/form'
import Checkbox from '@klook/klook-ui/lib/checkbox'
import Alert from '@klook/klook-ui/lib/alert'

import '@klook/klook-ui/lib/styles/components/icon.scss'
import '@klook/klook-ui/lib/styles/components/form.scss'
import '@klook/klook-ui/lib/styles/components/checkbox.scss'
import '@klook/klook-ui/lib/styles/components/alert.scss'

import { SHIPMENT_TYPE } from '@src/utils'
import inhouseMixin from '@src/utils/mixins/inhouse'
import ShipmentModal from '@src/mobile/components/ShipmentModal.vue'
import TravellerSelector from '@src/mobile/components/TravellerSelector.vue'
import DynamicForm from '@src/mobile/dynamic-form/index.vue'

export default {
  name: 'OtherInfoForm',
  mixins: [inhouseMixin],
  components: {
    KForm: Form,
    KCheckbox: Checkbox,
    KAlert: Alert,
    TravellerSelector,
    ShipmentModal,
    DynamicForm
  },
  props: {
    sectionType: {
      type: Number,
      default: 1,
    },
    data: {
      type: Object,
      default: null,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    travellerList: {
      type: Array,
      default: () => [],
    },
    shipmentList: {
      type: Array,
      default: () => [],
    },
    // 日历组件是否自动检测日期是否有效，如果无效则置空
    shouldCheckDateValid: {
      type: Boolean,
      default: true,
    },
    // hoc 的传值
    otherInfoData: {
      type: Object,
      default: null,
    },
  },
  data() {
    return {
      shipmentVisible: false,
      selectedTravellerId: null,
      selectedShipmentId: null,
    }
  },
  computed: {
    id() {
      const { custom_info } = this.data || {}
      const { address_id_use = 0, traveler_id_use = 0 } = custom_info || {}
      return this.isShipment ? address_id_use : traveler_id_use
    },
    isShipment() {
      return this.sectionType === SHIPMENT_TYPE
    },
    customInfo() {
      return this.data.custom_info || {}
    },
    inhouseType() {
      return this.id ? 'UpdateInfo' : 'CreaterInfo'
    },
    hideBottomInfo() {
      // 当邮寄信息列表超过 30 个时，不显示新建
      if (this.isShipment) {
        return this.shipmentList.length >= 30 && !this.id
      }

      // 当出行人信息 hide_create_checkbox 为 true，则不显示新建
      const { bottom_info } = this.data || {}
      const { hide_create_checkbox } = bottom_info || {}
      return hide_create_checkbox && !this.id
    },
    checkboxContent() {
      const { save_content, update_content } = this.data.bottom_info || {}
      return this.id ? update_content : save_content
    },
    originalPhone() {
      const target = (this.data.info_items || []).find(item => item.style.type === 8)
      const content = (target || {}).content || ''
      const operation = (target || {}).operation || []
      return content && operation.length > 0 ? `${operation[0].field_key}-${content}` : null
    },
  },
  watch: {
    otherInfoData() {
      this.$nextTick(() => {
        if (this.selectedTravellerId || this.selectedShipmentId) {
          this.validate()
        } else {
          this.clearValidate()
        }
      })
    },
  },
  methods: {
    openShipment() {
      this.shipmentVisible = true
      return this.shipmentVisible
    },
    validate() {
      return this.$refs.form.validate().catch((err) => {
        console.warn(err)
        return false
      })
    },
    clearValidate() {
      this.$refs.form.clearValidate()
    },
    handleChange() {
      this.$emit('change')
    },
    handleInput() {
      // 为了性能，这里不建议emit数据
      this.$emit('input')
    },
    chooseTraveller(item) {
      this.selectedTravellerId = item.traveller_id
      this.$set(this.data.custom_info, 'traveler_id_use', item.traveller_id)
      this.$emit('chooseTraveller', item)
      // 每次切换的时候需要把同步checkbox选中，如果hide则要取消选中
      this.updateBottomInfo(!this.hideBottomInfo)
    },
    chooseShipment(item) {
      this.selectedShipmentId = item.address_id
      this.$set(this.data.custom_info, 'address_id_use', item.address_id)
      this.$emit('chooseShipment', item)
      // 每次切换的时候需要把同步checkbox选中，如果hide则要取消选中
      this.updateBottomInfo(!this.hideBottomInfo)
    },
    updateBottomInfo(val) {
      if (this.data.bottom_info) {
        this.data.bottom_info.is_checkbox_selected = !!val
      }
    },
    checkBoxChange(val) {
      this.updateBottomInfo(val)
      this.updateCheckBoxSpm(val)
    },
    updateCheckBoxSpm(ClickType = true) {
      const spm = `${this.inhouseType}?ext=${JSON.stringify({
        ModuleType: this.sectionType,
        ClickType,
      })}`
      this.updateDataSpm('data-spm-module', '.js-spm-checkbox-in-form', spm)
      this.sendDataSpm('virtualAction', '.js-spm-checkbox-in-form')
    },
  },
}
</script>

<style lang="scss">
.other-info-form-mobile {
  &-checkbox {
    .klk-checkbox-base {
      box-sizing: border-box;
    }
  }
}
</style>

<style lang="scss" scoped>
.other-info-form-mobile {
  padding-bottom: 16px;

  &:first-child {
    margin-top: 0;
  }

  &-title {
    margin-bottom: 16px;
    font-weight: $fontWeight-semibold;
    font-size: $fontSize-body-m;
    line-height: 21px;
    color: $color-text-primary;
  }

  &-selector {
    margin-bottom: 16px;
  }

  &-content {
    margin-top: 24px;

    &:first-child {
      margin-top: 0;
    }
  }

  &-checkbox {
    margin-bottom: 16px;
    width: 100%;
  }
}
</style>
