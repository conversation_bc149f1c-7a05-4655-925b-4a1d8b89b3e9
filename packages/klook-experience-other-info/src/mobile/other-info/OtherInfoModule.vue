<template>
  <div
    v-if="data"
    class="other-info-module-mobile"
    :class="{ 'is-traveller': isTraveller }"
    :data-spm-module="spmModule"
  >
    <div class="other-info-module-mobile-header">
      <SectionTitle>
        {{ data.title }}
      </SectionTitle>
      <div>
        <div
          v-if="shouldShowShipment"
          class="other-info-module-mobile-header-address"
          data-spm-module="ChooseAddress"
          data-spm-virtual-item="__virtual"
          @click="handleOpenShipment"
        >
          {{ $t('28652') }}
        </div>
      </div>
    </div>

    <p v-if="data.grey_tips" class="other-info-module-mobile-tips">
      <span>{{ data.grey_tips }}</span>
      <span>{{ data.right_title || '' }}</span>
    </p>

    <k-alert class="other-info-module-mobile-warning" v-if="data.tips" type="warning" show-icon>
      {{ data.tips }}
    </k-alert>

    <OtherInfoFormMulti
      ref="otherInfoFormMulti"
      :data="data.form_infos"
      :section-type="data.section_type"
      :traveller-list="travellerList"
      :shipment-list="shipmentList"
      v-bind="$attrs"
      @change="handleChange"
      @input="$emit('input')"
    />
  </div>
</template>

<script>
import Alert from '@klook/klook-ui/lib/alert'
import '@klook/klook-ui/lib/styles/components/alert.scss'

import { SHIPMENT_TYPE, TRAVELLER_TYPE } from '@src/utils'
import OtherInfoFormMulti from '@src/mobile/form-multi'
import SectionTitle from '@src/mobile/components/SectionTitle.vue'
import childEmitMixin from '@src/utils/mixins/child-emit'

export default {
  name: 'OtherInfoModule',
  inheritAttrs: false,
  mixins: [childEmitMixin],
  components: {
    KAlert: Alert,
    OtherInfoFormMulti,
    SectionTitle,
  },
  props: {
    data: {
      type: Object,
      default: null,
    },
    travellerList: {
      type: Array,
      default: () => [],
    },
    shipmentList: {
      type: Array,
      default: () => [],
    },
  },
  computed: {
    spmModule() {
      return `OtherInfo?ext=${JSON.stringify({ InfoType: this.data.section_type })}`
    },
    shouldShowShipment() {
      return SHIPMENT_TYPE === this.data.section_type && this.shipmentList.length > 0
    },
    isTraveller() {
      return TRAVELLER_TYPE === this.data.section_type
    },
  },
  methods: {
    async validate() {
      let isValid = await this.$refs.otherInfoFormMulti.validate()
      return isValid
    },
    async getValidateKeys() {
      const keyList = await this.$refs.otherInfoFormMulti.getValidateKeys()
      return keyList
    },
    getModifiedData() {
      const formInfos = this.$refs.otherInfoFormMulti.getModifiedData()
      return {
        ...this.data,
        form_infos: formInfos,
      }
    },
    handleChange(formData) {
      this.$emit('change', {
        ...this.data,
        form_infos: formData,
      })
    },
    handleOpenShipment() {
      this.$refs.otherInfoFormMulti.openShipment()
    },
  },
}
</script>

<style lang="scss" scoped>
.other-info-module-mobile {
  margin-top: 16px;
  padding: 0 16px;
  background-color: $color-bg-widget-normal;

  &.is-traveller {
    .other-info-module-mobile-tips {
      @include font-caption-m-regular;

      margin: -14px 0 16px 12px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      color: $color-text-primary;
    }
  }

  &-header {
    padding-top: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 18px;

    &-address {
      color: $color-info;
      font-weight: $fontWeight-regular;
      font-size: $fontSize-body-m;
      line-height: 22px;
      cursor: pointer;
    }
  }

  &-tips {
    margin: -8px 0 16px 0;
    font-size: $fontSize-caption-m;
    line-height: 16px;
    color: $color-text-placeholder;
  }

  &-warning {
    margin-bottom: 16px;
  }
}
</style>
