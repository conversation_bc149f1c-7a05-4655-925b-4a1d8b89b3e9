export { default as OtherInfoForm } from './form'
export { default as OtherInfoFormMulti } from './form-multi'
export { default as OtherInfoModule } from './module'
export { default as OtherInfoModuleMulti } from './module-multi'

export { default as OtherInfoCheckout } from './other-info/OtherInfoCheckout.vue'
export { default as OtherInfoPreview } from './dynamic-form/DynamicFormPreview.vue'

export { default as codeManager } from '@src/utils/code'

// export function install(Vue) {
//   Vue.component('ExperienceDynamicForm', ExperienceDynamicForm)
// }
