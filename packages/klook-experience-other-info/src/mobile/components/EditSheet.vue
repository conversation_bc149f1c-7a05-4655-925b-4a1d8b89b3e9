<template>
  <k-bottom-sheet
    :visible.sync="sheetVisible"
    :title="title"
    :can-pull-close="false"
    :min-pull-distance="0"
    show-close
    transfer
    direction="right"
    class="edit-sheet"
    v-on="$attrs"
  >
    <AlertTips ref="alertTips" top="56px" />

    <!-- <div
      class="edit-sheet-content"
      :style="{ 'width': width }"
      @touchstart.stop="handleTouchStart"
      @touchmove.stop="handleTouchMove"
      @touchend.stop="handleTouchEnd"
    >
      <slot></slot>
    </div> -->
    <div class="edit-sheet-content">
      <slot></slot>
    </div>

    <k-button
      slot="footer"
      type="primary"
      size="small"
      block
      :loading="loading"
      @click="$emit('confirm')"
    >
      {{ $t('72653') }}
    </k-button>
  </k-bottom-sheet>
</template>

<script>
import BottomSheet from '@klook/klook-ui/lib/bottom-sheet'
import Button from '@klook/klook-ui/lib/button'

import '@klook/klook-ui/lib/styles/components/bottom-sheet.scss'
import '@klook/klook-ui/lib/styles/components/button.scss'

import AlertTips from '@src/utils/components/AlertTips.vue'

export default {
  name: 'EditSheet',
  components: {
    AlertTips,
    KButton: Button,
    KBottomSheet: BottomSheet,
  },
  props: {
    title: {
      type: String,
      default: ''
    },
    visible: {
      type: Boolean,
      default: false
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    sheetVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    }
  },
  methods: {
    // handleTouchStart(e) {
    //   this.$emit('touchstart')
    // },
    // handleTouchMove(e) {
    //   this.$emit('touchmove')
    // },
    // handleTouchEnd(e) {
    //   this.$emit('touchend')
    // },
    alert(text) {
      const alertElement = this.$refs.alertTips
      if (alertElement) {
        alertElement.open(text)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.edit-sheet {
  &-content {
    position: relative;
  }
}
</style>
