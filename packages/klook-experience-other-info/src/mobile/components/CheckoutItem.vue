<template>
  <div
    v-if="data"
    class="checkout-item"
    :class="{ 'no-name': !data.name }"
  >
    <p v-if="data.name" class="checkout-item-title">
      {{ data.name }}
    </p>

    <!-- checkbox -->
    <div
      v-if="data.style_type === 5"
      class="checkout-item-checkbox-wrap"
    >
      <k-checkbox
        class="checkout-item-checkbox"
        size="small"
        :value="data.content === 'YES'"
        disabled
      >
        {{ data.description }}
      </k-checkbox>
    </div>

    <!-- K-V 结构 -->
    <p v-else class="checkout-item-text">
      {{ data.content }}
    </p>
  </div>
</template>

<script>
import Checkbox from '@klook/klook-ui/lib/checkbox';
import '@klook/klook-ui/lib/styles/components/checkbox.scss';

export default {
  name: 'CheckoutItem',
  components: {
    KCheckbox: Checkbox,
  },
  props: {
    data: {
      type: Object,
      default: null
    }
  }
}
</script>

<style lang="scss">
.checkout-item {
  .klk-checkbox-base {
    box-sizing: border-box;
  }
}
</style>

<style lang="scss" scoped>
.checkout-item {
  &.no-name {
    margin-top: 8px;
  }

  &-title {
    margin-bottom: 4px;
    font-size: $fontSize-caption-m;
    line-height: 16px;
    color: $color-text-secondary;
  }

  &-checkbox-wrap {
    padding: 16px 14px;
    max-height: 112px;
    overflow-y: scroll;
    font-size: $fontSize-body-s;
    line-height: 20px;
    cursor: not-allowed;
    color: $color-text-disabled;
    background-color: $color-bg-widget-darker-1;
  }

  &-checkbox {
    margin: 0;
  }

  &-text {
    word-break: break-word;
    font-size: $fontSize-body-m;
    line-height: 20px;
    color: $color-text-primary;
  }
}
</style>
