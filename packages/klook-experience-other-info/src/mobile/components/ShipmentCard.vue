<template>
  <div
    v-if="data"
    class="shipment-card"
    :class="{ 'is-checked': checked, 'is-disabled': disabled }"
    :data-spm-item="`Detail?ext=${JSON.stringify({ Default: !!isDefault })}`"
  >
    <div v-if="isDefault" class="shipment-card-labels">
      <span class="shipment-card-label">
        {{ $t('28655') }}
      </span>
    </div>
    <div class="shipment-card-title">
      <span class="shipment-card-name">
        {{ name }}
      </span>
      <span class="shipment-card-phone">
        {{ phone }}
      </span>
    </div>
    <p class="shipment-card-address">
      {{ addressInfo }}
    </p>
    <p class="shipment-card-address">
      {{ addressCity }}
    </p>
    <CheckedSvg
      v-if="checked && !disabled"
      class="shipment-card-checked"
    >
  </div>
</template>

<script>
import codeManager from '@src/utils/code'
import CheckedSvg from '@src/imgs/card-checked.svg'

export default {
  name: 'ShipmentCard',
  components: {
    CheckedSvg
  },
  props: {
    disabled: {
      type: Boolean,
      default: false
    },
    checked: {
      type: Boolean,
      default: false
    },
    isDefault: {
      type: Boolean,
      default: false
    },
    data: {
      type: Object,
      default: null
    }
  },
  computed: {
    name() {
      const { consignee = '' } = this.data || {}
      return consignee
    },
    phone() {
      const { mobile = '' } = this.data || {}
      return mobile
    },
    addressInfo() {
      const { address = '' } = this.data || {}
      return address
    },
    addressCity () {
      const { country = '', city = '', postal_code = '' } = this.data || {}
      const { countryCodeList } = codeManager
      const targetCountry = countryCodeList[country]
      return `${city}, ${targetCountry.name}, ${postal_code}`
    }
  }
}
</script>

<style lang="scss" scoped>
.shipment-card {
  position: relative;
  padding: 16px;
  border: 1px solid $color-border-dim;
  border-radius: $radius-l;
  cursor: pointer;
  background-color: $color-bg-widget-normal;

  &-labels {
    padding-left: 10px;
    position: absolute;
    top: 0;
    right: 0;
    display: flex;
    width: 100%;
    justify-content: flex-end;
  }

  &-label {
    margin-left: 6px;
    padding: 2px 4px;
    max-width: 100%;
    color: $color-brand-primary;
    text-align: center;
    font-weight: $fontWeight-regular;
    font-size: $fontSize-caption-s;
    line-height: 14px;
    background-color: $color-brand-primary-background;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;

    &:last-child {
      border-radius: $radius-none $radius-l $radius-none $radius-l;
    }
  }

  &-title {
    display: flex;
    align-items: center;
    flex-wrap: wrap;

    .shipment-card-name {
      margin-right: 8px;
      margin-bottom: 8px;
      font-weight: $fontWeight-regular;
      line-height: 22px;
      font-size: $fontSize-body-m;
      color: $color-text-primary;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .shipment-card-phone {
      margin-bottom: 8px;
      font-weight: $fontWeight-regular;
      font-size: $fontSize-caption-m;
      line-height: 16px;
      color: $color-text-secondary;
      white-space: nowrap;
    }
  }

  &-address {
    font-weight: $fontWeight-regular;
    font-size: $fontSize-caption-m;
    line-height: 16px;
    color: $color-text-primary;
  }

  &-checked {
    position: absolute;
    right: -1px;
    bottom: -1px;
    pointer-events: none;
  }

  &.is-checked {
    border-color: $color-brand-primary;
  }

  &.is-disabled {
    cursor: not-allowed;
    border-color: $color-border-dim;

    .shipment-card-label {
      background: $color-bg-page;
      color: $color-text-disabled;
    }

    .shipment-card-name {
      color: $color-text-disabled;
    }

    .shipment-card-phone {
      color: $color-text-disabled;
    }

    .shipment-card-address {
      color: $color-text-disabled;
    }
  }
}
</style>
