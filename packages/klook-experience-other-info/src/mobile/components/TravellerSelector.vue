<template>
  <div
    class="traveller-selector-mobile"
    :data-spm-module="
      `FrequentInfo?ext=${JSON.stringify({
        InfoType: sectionType,
        Amount: travellerList.length,
        Position: selectedPosition,
        FillinType: selectedFillinType,
        UserType: selectedUserType,
      })}`
    "
  >
    <k-tag-select
      v-model="choosedId"
      size="small"
      ref="travelTagSelect"
      style="width: 100%;"
      @change="handleTagChange"
    >
      <k-tag-select-item
        v-for="(item, index) in travellerList"
        :key="`${item.traveller_id}-${item.name}`"
        :name="item.traveller_id"
        :data-spm-item="
          `Traveler?ext=${JSON.stringify({
            Amount: travellerList.length,
            Position: index,
            UserType: getInhouseUserType(item),
          })}`
        "
        class="traveller-selector-mobile-item"
        @click.native="$emit('manualCheck', item.traveller_id)"
      >
        {{ item.name }}
      </k-tag-select-item>
      <k-tag-select-item
        :key="0"
        :name="0"
        :data-spm-item="
          `Traveler?ext=${JSON.stringify({
            Amount: travellerList.length,
            Position: travellerList.length,
            UserType: 'Add',
          })}`
        "
        @click.native="$emit('manualCheck', 0)"
      >
        <div class="traveller-selector-mobile-add">
          <AddSvg class="traveller-selector-mobile-add-icon" />
          <span class="traveller-selector-mobile-add-text">{{ $t('17443') }}</span>
        </div>
      </k-tag-select-item>
    </k-tag-select>
  </div>
</template>

<script>
import { TagSelect, TagSelectItem } from '@klook/klook-ui/lib/tag-select'

import '@klook/klook-ui/lib/styles/components/icon.scss'
import '@klook/klook-ui/lib/styles/components/tag.scss'
import '@klook/klook-ui/lib/styles/components/tag-select.scss'

import { TRAVELLER_TYPE, CONTACT_TYPE } from '@src/utils'
import AddSvg from '@src/imgs/traveler-add.svg'

export default {
  name: 'TravellerSelector',
  components: {
    AddSvg,
    KTagSelect: TagSelect,
    KTagSelectItem: TagSelectItem
  },
  props: {
    sectionType: {
      type: Number,
      default: 1,
    },
    travellerList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      choosedId: -1,
      isDefaultCheckType: true,
      isDefaultChecked: false
    }
  },
  computed: {
    isTraveller() {
      return this.sectionType === TRAVELLER_TYPE
    },
    isContact() {
      return this.sectionType === CONTACT_TYPE
    },
    selectedUser() {
      if (this.choosedId === 0) {
        return { traveller_id: 0 }
      }

      const choosedUser = this.travellerList.find(item => item.traveller_id === this.choosedId)
      return choosedUser ? choosedUser : null // null是指没有选
    },
    selectedFillinType() {
      if (!this.selectedUser) {
        return 'Null'
      }

      if (this.isDefaultCheckType) {
        return 'Default'
      }

      if (this.selectedUser.traveller_id === 0) {
        return 'Blank'
      }

      return 'User'
    },
    selectedUserType() {
      if (!this.selectedUser) {
        return 'Null'
      }

      if (this.selectedUser.traveller_id === 0) {
        return 'Add'
      }

      return this.getInhouseUserType(this.selectedUser)
    },
    selectedPosition() {
      // 没有选则为 -1
      if (!this.selectedUser) {
        return -1
      }

      // add则为最后一个
      if (this.selectedUser.traveller_id === 0) {
        return this.travellerList.length
      }

      return this.travellerList.map(item => item.traveller_id).indexOf(this.selectedUser.traveller_id)
    },
  },
  watch: {
    travellerList: {
      immediate: true,
      handler() {
        // 默认选中只发生一次
        if (this.isDefaultChecked) {
          return
        }

        const { isTraveller, isContact, travellerList } = this
        const key = isTraveller ? 'is_last_traveller_used' : isContact ? 'is_last_contact_used' : ''
        if (travellerList.length > 0 && key) {
          // 判断是否设置了默认选中(只判断前三个)
          for (let i = 0; i < 3; i++) {
            if (!this.isDefaultChecked && travellerList[i] && travellerList[i]['default_traveller']) {
              this.tagCheck(travellerList[i])
              this.isDefaultCheckType = true // 重置为default
              this.isDefaultChecked = true
              break
            }
          }

          // 判断 is_last_traveller_used 和 is_last_contact_used(只判断前三个)
          for (let i = 0; i < 3; i++) {
            if (!this.isDefaultChecked && travellerList[i] && travellerList[i][key]) {
              this.tagCheck(travellerList[i])
              this.isDefaultCheckType = true // 重置为default
              this.isDefaultChecked = true
              break
            }
          }

          // 如果是出行人的话，就做特殊处理：如果没有默认选中，则默认选中第一个
          if (isTraveller && !this.isDefaultChecked && travellerList.length > 0) {
            this.tagCheck(travellerList[0])
            this.isDefaultCheckType = true // 重置为default
            this.isDefaultChecked = true
          }
        }
      },
    },
  },
  methods: {
    getInhouseUserType(item) {
      if (item.default_traveller) {
        return 'Myself'
      }

      if (this.isTraveller && item.is_last_traveller_used) {
        return 'Default'
      }

      if (this.isContact && item.is_last_contact_used) {
        return 'Default'
      }

      return 'Other'
    },
    tagCheck(data) {
      this.choosedId = data.traveller_id
      this.$emit('chooseData', data)
      this.isDefaultCheckType = false
    },
    handleTagChange(travellerId) {
      const target = this.travellerList.find(item => item.traveller_id === travellerId)
      if (target) {
        this.tagCheck(target)
      } else {
        this.tagCheck({ traveller_id: 0 })
      }
    },
    checkTravellerId(travellerId) {
      this.choosedId = travellerId
    }
  },
}
</script>

<style lang="scss">
.traveller-selector-mobile {
  .klk-tag-select {
    padding-left: 0;

    .klk-tag-select-items {
      margin-bottom: -12px;
    }

    .klk-tag-select-item {
      padding: 8px 12px;
      /* styleling-disable-nuxt-line */
      height: 38px;
      /* styleling-disable-nuxt-line */
      line-height: 20px;
      font-weight: $fontWeight-semibold;
      border-radius: $radius-m;
      border: 1px solid transparent;
      background-color: $color-bg-widget-darker-2;

      &:hover {
        background-color: $color-bg-widget-darker-3;
      }

      &.klk-tag-select-item-active,
      &.klk-tag-select-item-active:hover {
        color: $color-brand-primary;
        /* styleling-disable-nuxt-line */
        background-color: rgba(255, 91, 0, 0.12);
        border-color: $color-brand-primary;

        .traveller-selector-mobile-add-icon {
          color: $color-brand-primary;
        }
      }
    }

    .klk-tag-select-right {
      height: 48px;
    }

    .klk-tag-select-toggle-btn {
      .klk-icon {
        font-size: 16px;

        svg {
          font-size: 16px;
        }
      }
    }
  }
}
</style>

<style lang="scss" scoped>
.traveller-selector-mobile {
  width: 100%;

  &-add {
    position: relative;
  }

  &-add-icon {
    position: absolute;
    left: 0;
    top: 50%;
    margin-top: -8px;
    box-sizing: content-box;
    width: 1em;
    height: 1em;
    /* stylelint-disable */
    fill: currentColor;
    font-size: $fontSize-body-m;
  }

  &-add-text {
    margin-left: 20px;
  }

  &-item {
    max-width: 200px;
    overflow:hidden;
    white-space: nowrap;
    text-overflow:ellipsis;
  }
}
</style>
