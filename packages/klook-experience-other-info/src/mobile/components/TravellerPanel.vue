<template>
  <div class="traveller-panel" :class="{ 'is-error': !!errorText }">
    <div class="traveller-panel-wrap" @click="$emit('edit')">
      <div class="traveller-panel-content">
        <DynamicFormPreview
          v-for="(item, itemIndex) in flattenedData"
          :key="`${itemIndex}-${item.id}`"
          :item="item"
          class="traveller-panel-row"
        >
          <template slot-scope="slotProps">
            <span
              :style="{
                'max-width': maxWidth === 0 ? 'auto' : `${maxWidth}px`,
                'width': minWidth === 0 ? 'auto' : `${minWidth}px`
              }"
              class="traveller-panel-row-label"
            >
              {{ slotProps.label }}
            </span>
            <span
              class="traveller-panel-row-value"
              :class="{ 'is-grey': !slotProps.value }"
            >
              {{ getText(slotProps) }}
            </span>
          </template>
        </DynamicFormPreview>
      </div>
      <div class="traveller-panel-right">
        <div class="traveller-panel-edit">
          <EditXsSvg class="traveller-panel-edit-icon" />
        </div>
      </div>
    </div>
    <div v-if="errorText" class="traveller-panel-error">
      <div>
        <ErrorSvg class="traveller-panel-error-icon" />
      </div>
      <p class="traveller-panel-error-text">
        {{ errorText }}
      </p>
    </div>
  </div>
</template>

<script>
import DynamicFormPreview from '@src/web/dynamic-form/DynamicFormPreview.vue'
import EditXsSvg from '@src/imgs/edit-xs.svg'
import ErrorSvg from '@src/imgs/error.svg'
import { isServer, traverseDynamicFormItem, validateFormItem } from '@src/utils'

export default {
  name: 'TravellerPanel',
  components: {
    EditXsSvg,
    ErrorSvg,
    DynamicFormPreview
  },
  props: {
    data: {
      type: Array,
      default: null
    },
    travellerList: {
      type: Array,
      default: null,
    }
  },
  data() {
    return {
      minWidth: 0,
      maxWidth: 0,
      errorType: 0 // 0: 没有；1：add；2：empty；3：valid
    }
  },
  computed: {
    flattenedData() {
      const res = []
      traverseDynamicFormItem(this.data || [], formItem => res.push(formItem))
      return res
    },
    errorText() {
      switch (this.errorType) {
        case 0:
          return ''
        case 1:
          return this.$t('72648')
        case 2:
          return this.$t('72649')
        case 3:
          return this.$t('72650')
        default:
          return ''
      }
    }
  },
  watch: {
    flattenedData: {
      immediate: true,
      handler(val) {
        if (val && val.length > 0) {
          this.minWidth = 0

          this.$nextTick(() => {
            this.computeMaxWidth()
            this.computeWidth()
          })
        }
      }
    }
  },
  mounted() {
    this.createObserver()
  },
  methods: {
    clearValidate() {
      this.errorType = 0
    },
    validate() {
      this.clearValidate()

      if (this.travellerList && this.travellerList.length === 0) {
        this.errorType = 1
        return Promise.reject()
      }

      traverseDynamicFormItem(this.data || [], formItem => {
        const { isEmpty, isInvalid } = validateFormItem(formItem)
        const { required } = formItem.style || {}

        if (isInvalid) {
          this.errorType = 3
        }

        if (required && isEmpty) {
          this.errorType = 2
        }
      })

      return this.errorType === 0 ? Promise.resolve(true) : Promise.reject()
    },
    getText({ value, required }) {
      if (value === '') {
        return required ? this.$t('72640') : this.$t('72639')
      }

      return value
    },
    computeWidth() {
      if (!isServer && this.minWidth === 0) {
        const labelDoms = this.$el.querySelectorAll('.traveller-panel-row-label') || []
        const widthList = [].map.call(labelDoms, labelDom => labelDom.offsetWidth || 0)
        this.minWidth = Math.max(...widthList)

        // fix: 修复offsetWidth有小数点的情况
        if (this.minWidth > 0) {
          this.minWidth += 1
        }
      }
    },
    computeMaxWidth() {
      if (!isServer && this.maxWidth === 0) {
        const wrapDom = this.$el.querySelector('.traveller-panel-wrap') || {}
        const width = wrapDom.offsetWidth
        this.maxWidth = width > 45 ? (width - 45) / 2 : 0
      }
    },
    createObserver() {
      // 解决购物车 display:none 获取不到宽度的问题
      if ('IntersectionObserver' in window) {
        const elementObserver = new IntersectionObserver((entries) => {
          entries.forEach((entry) => {
            // 如果元素可见
            if (entry.intersectionRatio > 0) {
              this.computeMaxWidth()
              this.computeWidth()
            }
          })
        })

        elementObserver.observe(this.$el)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.traveller-panel {
  &-wrap {
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
    cursor: pointer;
  }

  &-row {
    margin-top: 8px;
    display: flex;
    align-items: flex-start;

    &:first-child {
      margin-top: 0;
    }

    &-label {
      @include font-body-s-regular;

      margin-right: 24px;
      display: inline-block;
      color: $color-text-primary;
    }

    &-value {
      @include font-body-s-semibold;

      display: inline-block;
      flex: 1;
      color: $color-text-primary;
      word-break: break-word;

      &.is-grey {
        color: $color-text-placeholder;
      }
    }
  }

  &-right {
    margin-left: 24px;
    display: flex;
    align-items: flex-end;
  }

  &-edit {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    height: 20px;

    &-icon {
      margin-right: 4px;
      width: 16px;
      height: 16px;
      color: $color-text-link;
    }

    &-text {
      @include font-body-s-regular;
      color: $color-text-link;
    }
  }

  &-error {
    margin-top: 8px;
    display: flex;
    align-items: center;
    justify-content: flex-start;

    &-icon {
      margin-right: 6px;
      width: 16px;
      height: 16px;
    }
    &-text {
      @include font-body-s-regular;
      color: $color-error;
    }
  }
}
</style>
