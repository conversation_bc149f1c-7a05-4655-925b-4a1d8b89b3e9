<template>
  <div
    v-if="isModalVisible"
    v-no-scroll="isModalVisible"
    class="shipment-modal-mobile"
    data-spm-page="Shipment"
  >
    <div :data-spm-module="spmModule" class="shipment-modal-mobile-wrap">
      <div class="shipment-modal-mobile-header">
        <span class="shipment-modal-mobile-header-back" @click="handleClose">
          <ArrowLeftSvg />
        </span>
        <h2 class="shipment-modal-mobile-header-title">
          {{ $t('28652') }}
        </h2>
      </div>

      <div class="shipment-modal-mobile-content">
        <k-alert
          v-if="unavailableShipmentList.length > 0"
          type="warning"
          show-icon
          class="shipment-modal-mobile-alert"
        >
          {{ $t('30597') }}
        </k-alert>
        <div v-if="availableShipmentList.length > 0">
          <h4 class="shipment-modal-mobile-title">
            {{ $t('28656') }}
          </h4>
          <ShipmentCard
            v-for="(shipmentInfo, shipmentIndex) in availableShipmentList"
            :key="shipmentIndex"
            :checked="shipmentInfo.address_id === choosedId"
            :data="shipmentInfo.showed"
            :isDefault="shipmentInfo.default"
            class="shipment-modal-mobile-card"
            @click.native="handleCardClick(shipmentInfo)"
          />
        </div>
        <div v-if="unavailableShipmentList.length > 0">
          <h4 class="shipment-modal-mobile-title">
            {{ $t('28657') }}
          </h4>
          <ShipmentCard
            v-for="(shipmentInfo, shipmentIndex) in unavailableShipmentList"
            :key="shipmentIndex"
            :data="shipmentInfo.showed"
            :isDefault="shipmentInfo.default"
            class="shipment-modal-mobile-card"
            disabled
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import Alert from '@klook/klook-ui/lib/alert'
import Modal from '@klook/klook-ui/lib/modal'
import '@klook/klook-ui/lib/styles/components/alert.scss'
import '@klook/klook-ui/lib/styles/components/modal.scss'

import ArrowLeftSvg from '@src/imgs/arrow-left.svg'
import noScroll from '@src/utils/no-scroll.js'
import ShipmentCard from './ShipmentCard.vue'

export default {
  name: 'ShipmentModal',
  directives: {
    'no-scroll': noScroll,
  },
  components: {
    KModal: Modal,
    KAlert: Alert,
    ShipmentCard,
    ArrowLeftSvg,
  },
  props: {
    id: {
      type: Number,
      default: null
    },
    visible: {
      type: Boolean,
      default: false,
    },
    shipmentList: {
      type: Array,
      default: () => [],
    },
    otherInfoList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      choosedData: null,
    }
  },
  computed: {
    isModalVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      },
    },
    choosedId() {
      const { choosedData } = this
      return (choosedData && choosedData.address_id) || null
    },
    isAllCountryAvailable() {
      // 如果option_all_type为1的话，就使用本地的全量国家数据，这个时候所有国家都是合法的
      const country = this.otherInfoList.find(item => item.field_key === 'shipment_nationality')
      return country && country.style && country.style.option_all_type === 1
    },
    countryList() {
      const country = this.otherInfoList.find(item => item.field_key === 'shipment_nationality')
      return (country && country.options) || []
    },
    countryCodeList() {
      return this.countryList.map(item => item.field_key)
    },
    availableShipmentList() {
      const { isAllCountryAvailable, shipmentList, countryCodeList } = this

      if (isAllCountryAvailable) {
        return shipmentList
      }

      return shipmentList.filter(item => countryCodeList.includes(item.showed.country))
    },
    unavailableShipmentList() {
      const { isAllCountryAvailable, shipmentList, countryCodeList } = this

      if (isAllCountryAvailable) {
        return []
      }

      return shipmentList.filter(item => !countryCodeList.includes(item.showed.country))
    },
    spmModule() {
      const { shipmentList, availableShipmentList, unavailableShipmentList } = this
      return `Address?ext=${JSON.stringify({
        TotalAmount: shipmentList.length,
        AvailableAmount: availableShipmentList.length,
        Tips: unavailableShipmentList.length > 0,
      })}`
    },
  },
  watch: {
    availableShipmentList: {
      immediate: true,
      handler() {
        const { availableShipmentList } = this
        if (availableShipmentList.length > 0) {
          this.handleCardClick(availableShipmentList[0])
        }
      },
    },
  },
  methods: {
    handleCardClick(data) {
      this.choosedData = data

      const { address_id } = this.choosedData || {}
      if (address_id && this.id !== address_id) {
        this.$emit('chooseData', this.choosedData || {})
      }
      this.handleClose()
    },
    handleClose() {
      this.isModalVisible = false
    },
  },
}
</script>

<style lang="scss" scoped>
.shipment-modal-mobile {
  padding: 48px 0 0;
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: $color-bg-page;
  z-index: 20;

  &-wrap {
    padding: 0 16px;
    width: 100%;
    height: 100%;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
  }

  &-header {
    padding: 0 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    width: 100%;
    height: 48px;
    background-color: $color-bg-widget-normal;
    /* stylelint-disable */
    box-shadow: inset 0 -1px 0 0 rgba(0, 0, 0, 0.12);
    z-index: 11;

    &-back {
      position: absolute;
      left: 10px;
      top: 12px;
    }

    &-title {
      font-weight: $fontWeight-semibold;
      font-size: $fontSize-body-m;
      line-height: 20px;
    }
  }

  &-alert {
    margin-top: 12px;
    font-size: $fontSize-body-s;
  }

  &-title {
    margin-top: 24px;
    color: $color-text-primary;
    font-size: $fontSize-caption-m;
    line-height: 16px;
  }

  &-card {
    margin-top: 12px;
  }
}
</style>
