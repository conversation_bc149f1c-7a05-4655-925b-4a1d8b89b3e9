<template>
  <k-drawer
    :visible.sync="drawerVisible"
    direction="right"
    class="edit-drawer"
    v-on="$attrs"
  >
    <div class="edit-drawer-header">
      <h3>{{ title }}</h3>
      <div @click="drawerVisible = false">
        <CloseSvg class="edit-drawer-close"/>
      </div>
    </div>

    <AlertTips ref="alertTips" top="56px" />

    <div class="edit-drawer-content" :style="{ 'width': drawerWidth }">
      <slot></slot>
    </div>

    <div class="edit-drawer-footer">
      <div style="display: flex;">
        <k-button
          type="outlined"
          size="small"
          style="margin-right: 24px;"
          @click="drawerVisible = false"
        >
          {{ $t('71853') }}
        </k-button>
        <k-button
          type="primary"
          size="small"
          :loading="loading"
          @click="$emit('confirm')"
        >
          {{ $t('72653') }}
        </k-button>
      </div>
    </div>
  </k-drawer>
</template>

<script>
import Drawer from '@klook/klook-ui/lib/drawer'
import Button from '@klook/klook-ui/lib/button'

import '@klook/klook-ui/lib/styles/components/drawer.scss'
import '@klook/klook-ui/lib/styles/components/button.scss'

import AlertTips from '@src/utils/components/AlertTips.vue'
import CloseSvg from '@src/imgs/close.svg'

export default {
  name: 'EditDrawer',
  components: {
    CloseSvg,
    AlertTips,
    KButton: Button,
    KDrawer: Drawer,
  },
  props: {
    title: {
      type: String,
      default: ''
    },
    drawerWidth: {
      type: String,
      default: 'auto'
    },
    visible: {
      type: Boolean,
      default: false
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    drawerVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    }
  },
  methods: {
    alert(text) {
      const alertElement = this.$refs.alertTips
      if (alertElement) {
        alertElement.open(text)
      }
    }
  }
}
</script>

<style lang="scss">
.edit-drawer {
  .klk-drawer-content {
    overflow: hidden;
  }
  button {
    height: auto;
  }
}
</style>

<style lang="scss" scoped>
.edit-drawer {
  &-close {
    width: 20px;
    height: 20px;
    cursor: pointer;
  }

  &-header {
    padding: 0 32px;
    position: absolute;
    top: 0;
    left: 0;

    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    height: 56px;
    border-bottom: 1px solid $color-border-normal;
    background: $color-bg-widget-normal;
    z-index: 8000;

    h3 {
      font-weight: $fontWeight-bold;
    }
  }

  &-content {
    position: relative;
    padding: 80px 32px 64px;
    height: 100%;
    overflow-y: auto;
  }

  &-footer {
    padding: 0 32px;
    position: absolute;
    bottom: 0;
    left: 0;

    display: flex;
    align-items: center;
    justify-content: flex-end;
    width: 100%;
    height: 60px;
    border-top: 1px solid $color-border-normal;
    background: $color-bg-widget-normal;
  }
}
</style>
