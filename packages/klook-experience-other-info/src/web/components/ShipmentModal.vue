<template>
  <k-modal
    :open.sync="isModalVisible"
    :show-cancel-button="false"
    class="shipment-modal"
    :title="$t('28652')"
    size="normal"
    width="600"
    closable
    scrollable
    append-body
    @close="handleClose"
    @on-confirm="handleConfirm"
  >
    <div data-spm-page="Shipment">
      <div :data-spm-module="spmModule">
        <k-alert v-if="unavailableShipmentList.length > 0" type="warning" show-icon>
          {{ $t('30597') }}
        </k-alert>
        <div v-if="availableShipmentList.length > 0">
          <h4 class="shipment-modal-title">
            {{ $t('28656') }}
          </h4>
          <ShipmentCard
            v-for="(shipmentInfo, shipmentIndex) in availableShipmentList"
            :key="shipmentIndex"
            :checked="shipmentInfo.address_id === choosedId"
            :data="shipmentInfo.showed"
            :isDefault="shipmentInfo.default"
            class="shipment-modal-card"
            @click.native="handleCardClick(shipmentInfo)"
          />
        </div>
        <div v-if="unavailableShipmentList.length > 0">
          <h4 class="shipment-modal-title">
            {{ $t('28657') }}
          </h4>
          <ShipmentCard
            v-for="(shipmentInfo, shipmentIndex) in unavailableShipmentList"
            :key="shipmentIndex"
            :data="shipmentInfo.showed"
            :isDefault="shipmentInfo.default"
            class="shipment-modal-card"
            disabled
          />
        </div>
      </div>
    </div>
  </k-modal>
</template>

<script>
import Alert from '@klook/klook-ui/lib/alert'
import Modal from '@klook/klook-ui/lib/modal'
import '@klook/klook-ui/lib/styles/components/alert.scss'
import '@klook/klook-ui/lib/styles/components/modal.scss'

import ShipmentCard from './ShipmentCard.vue'

export default {
  name: 'ShipmentModal',
  components: {
    KModal: Modal,
    KAlert: Alert,
    ShipmentCard,
  },
  props: {
    id: {
      type: Number,
      default: null
    },
    visible: {
      type: Boolean,
      default: false,
    },
    shipmentList: {
      type: Array,
      default: () => [],
    },
    otherInfoList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      choosedData: null,
    }
  },
  computed: {
    isModalVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      },
    },
    choosedId() {
      const { choosedData } = this
      return (choosedData && choosedData.address_id) || null
    },
    isAllCountryAvailable() {
      // 如果option_all_type为1的话，就使用本地的全量国家数据，这个时候所有国家都是合法的
      const target = this.otherInfoList.find(item => item.field_key === 'shipment_nationality')
      return target && target.style && target.style.option_all_type === 1
    },
    countryList() {
      const country = this.otherInfoList.find(item => item.field_key === 'shipment_nationality')
      return (country && country.options) || []
    },
    countryCodeList() {
      return this.countryList.map(item => item.field_key)
    },
    availableShipmentList() {
      const { isAllCountryAvailable, shipmentList, countryCodeList } = this

      if (isAllCountryAvailable) {
        return shipmentList
      }

      return shipmentList.filter(item => countryCodeList.includes(item.showed.country))
    },
    unavailableShipmentList() {
      const { isAllCountryAvailable, shipmentList, countryCodeList } = this

      if (isAllCountryAvailable) {
        return []
      }

      return shipmentList.filter(item => !countryCodeList.includes(item.showed.country))
    },
    spmModule() {
      const { shipmentList, availableShipmentList, unavailableShipmentList } = this
      return `Address?ext=${JSON.stringify({
        TotalAmount: shipmentList.length,
        AvailableAmount: availableShipmentList.length,
        Tips: unavailableShipmentList.length > 0,
      })}`
    },
  },
  watch: {
    availableShipmentList: {
      immediate: true,
      handler() {
        const { availableShipmentList } = this
        if (availableShipmentList.length > 0) {
          this.handleCardClick(availableShipmentList[0])
          this.handleConfirm()
        }
      },
    },
  },
  methods: {
    handleConfirm() {
      // 如果这里的id和外面的相同，则不改变外面的
      const { address_id } = this.choosedData || {}
      if (address_id && this.id !== address_id) {
        this.$emit('chooseData', this.choosedData || {})
      }
      this.handleClose()
    },
    handleCardClick(data) {
      this.choosedData = data
    },
    handleClose() {
      this.isModalVisible = false
    },
  },
}
</script>

<style lang="scss">
// 设置在 html 上用于禁止页面滚动
.klk-lock-body-scroll {
  body {
    width: 100%;
    position: fixed !important;
    height: 100% !important;
  }
}
</style>

<style lang="scss" scoped>
.shipment-modal {
  &-title {
    margin: 36px 0 12px 0;
  }

  &-card {
    margin-bottom: 12px;
  }
}
</style>
