<template>
  <div v-if="data && data.length > 0" class="other-info-form-multi">
    <component
      v-for="(item, index) in data"
      :is="componentName"
      :key="index"
      :data="item"
      :section-type="sectionType"
      v-bind="$attrs"
      @add="data => $emit('add', index, data)"
      @remove="$emit('remove', index)"
      @change="data => handleChange(index, data)"
      @input="$emit('input')"
    />
  </div>
</template>

<script>
import OtherInfoForm from '@src/web/form'
import OtherInfoFormTraveller from '@src/web/form-traveller'
import { TRAVELLER_TYPE } from '@src/utils'

export default {
  name: 'OtherInfoFormMulti',
  inheritAttrs: false,
  components: {
    OtherInfoForm,
    OtherInfoFormTraveller,
  },
  props: {
    data: {
      type: Array,
      default: () => [],
    },
    sectionType: {
      type: Number,
      default: 1,
    },
    // 目前的new feature只是：新的出行人信息卡片。（在自助修改订单不使用这个new feature）
    useNewFeature: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    isTraveller() {
      return this.sectionType === TRAVELLER_TYPE
    },
    componentName() {
      return this.useNewFeature && this.isTraveller ? 'OtherInfoFormTraveller' : 'OtherInfoForm'
    },
  },
  methods: {
    handleChange(index, data) {
      const formInfos = [...this.data]
      formInfos.splice(index, 1, data)
      this.$emit('change', formInfos)
    },
  },
}
</script>
