<template>
  <div v-if="data" class="other-info-checkout">
    <div
      v-for="(section, sectionIndex) in data"
      :key="sectionIndex"
      class="other-info-checkout-wrap"
    >
      <div v-if="section.title" class="other-info-checkout-title">
        {{ section.title }}
      </div>
      <div
        v-for="(info, infoIndex) in (section.form_infos || [])"
        :key="infoIndex"
        class="other-info-checkout-section"
      >
        <div v-if="info.name" class="other-info-checkout-section-title">
          {{ info.name }}
        </div>
        <div class="other-info-checkout-section-content">
          <template>
            <CheckoutItem
              v-for="(item, itemIndex) in (info.info_items || [])"
              :key="itemIndex"
              :data="item"
              class="other-info-checkout-section-item"
            />
          </template>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import CheckoutItem from '@src/web/components/CheckoutItem.vue'

export default {
  name: 'OtherInfoCheckout',
  components: {
    CheckoutItem,
  },
  props: {
    data: {
      type: Array,
      default: null,
    }
  },
  computed: {
    realTitle () {
      const { title } = this.data || {}
      return title || this.title || ''
    }
  }
}
</script>

<style lang="scss" scoped>
.other-info-checkout {
  width: 100%;

  &-wrap {
    margin-bottom: 24px;
    padding-top: 32px;
    width: 100%;
    border-top: 1px solid $color-border-normal;
  }

  &-title {
    font-weight: $fontWeight-bold;
    font-size: $fontSize-body-l;
    line-height: 24px;
    color: $color-text-primary;
  }

  &-section {
    width: 100%;
  }

  &-section-title {
    margin-top: 24px;
    font-weight: $fontWeight-semibold;
    font-size: $fontSize-body-m;
    line-height: 22px;
    color: $color-text-primary;
  }

  &-section-content {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
  }

  &-section-item {
    margin-top: 16px;
  }
}
</style>
