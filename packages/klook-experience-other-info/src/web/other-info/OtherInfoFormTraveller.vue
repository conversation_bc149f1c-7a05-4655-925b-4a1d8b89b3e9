<template>
  <!--公共表单，为绑定值未拆模块-->
  <div class="other-info-traveller">
    <p v-if="data.name" class="other-info-traveller-title">
      {{ data.name }}
    </p>

    <TravellerSelector
      ref="travellerSelector"
      :section-type="sectionType"
      :traveller-list="travellerListCached"
      class="other-info-traveller-selector"
      @chooseData="chooseTraveller"
      @manualCheck="handleManualClick"
    />

    <TravellerPanel
      ref="travellerPanel"
      :data="otherInfoData.data"
      class="other-info-traveller-panel"
      @edit="openDrawer"
    />

    <EditDrawer
      :visible.sync="drawerVisible"
      :loading="isLoading"
      :title="drawerTitle"
      direction="right"
      ref="editDrawer"
      class="other-info-traveller-drawer"
      @confirm="handleConfirm"
    >
      <DynamicFormSkeleton v-if="showSkeleton" />
      <k-form
        v-else
        :model="drawerData"
        :style="{ width: wrapperWidth }"
        ref="form"
        class="other-info-traveller-drawer-form"
      >
        <DynamicForm
          v-for="(item, index) in drawerData.data || []"
          :key="`${item.field_key}-${item.id}`"
          :data="item"
          :prop="`data[${index}]`"
          :disabled="disabled"
          :original-phone="originalPhone"
          :should-check-date-valid="shouldCheckDateValid"
          @change="handleChange"
          @input="handleInput"
        />
      </k-form>
    </EditDrawer>
  </div>
</template>

<script>
import cloneDeep from 'lodash.clonedeep'
import { Form } from '@klook/klook-ui/lib/form'
import Checkbox from '@klook/klook-ui/lib/checkbox'
import Alert from '@klook/klook-ui/lib/alert'

import '@klook/klook-ui/lib/styles/components/icon.scss'
import '@klook/klook-ui/lib/styles/components/form.scss'
import '@klook/klook-ui/lib/styles/components/checkbox.scss'
import '@klook/klook-ui/lib/styles/components/alert.scss'

import TravellerSelector from '@src/web/components/TravellerSelector.vue'
import TravellerPanel from '@src/web/components/TravellerPanel.vue'
import EditDrawer from '@src/web/components/EditDrawer.vue'
import DynamicForm from '@src/web/dynamic-form/index.vue'
import DynamicFormSkeleton from '@src/web/dynamic-form/DynamicFormSkeleton.vue'
import inhouseMixin from '@src/utils/mixins/inhouse'
import eventBus from '@src/utils/event-bus'
import { TRAVELLER_TYPE, cacheTravellerInfo, getTravellerInfoFromCache, getUpdateTravellerListParams } from '@src/utils'
import { getNewTravellerId } from '@src/utils/traveller-cache'

/* other-info 出行人特殊展示和交互 */
export default {
  name: 'OtherInfoFormTraveller',
  mixins: [inhouseMixin],
  provide() {
    return {
      wrapperWidth: this.wrapperWidth,
      componentWidth: this.componentWidth,
    }
  },
  components: {
    KForm: Form,
    KCheckbox: Checkbox,
    KAlert: Alert,
    TravellerSelector,
    DynamicForm,
    TravellerPanel,
    EditDrawer,
    DynamicFormSkeleton,
  },
  props: {
    sectionType: {
      type: Number,
      default: 1,
    },
    data: {
      type: Object,
      default: null,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    travellerList: {
      type: Array,
      default: () => [],
    },
    // 日历组件是否自动检测日期是否有效，如果无效则置空
    shouldCheckDateValid: {
      type: Boolean,
      default: true,
    },
    updateTravellerListFunc: {
      type: Function,
      default: null,
    },
    // hoc 的传值
    otherInfoData: {
      type: Object,
      default: null,
    },
    // 新增的时候需要加一些额外的表单
    addRequiredFormData: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      wrapperWidth: '616px',
      componentWidth: '298px',

      isLoading: false,
      travellerListInited: false,
      travellerListCached: [],
      shipmentVisible: false,
      selectedTravellerId: null,

      drawerVisible: false,
      drawerData: {
        data: [],
      },
    }
  },
  watch: {
    travellerList: {
      immediate: true,
      handler(newVal) {
        if (newVal && newVal.length && !this.travellerListInited) {
          this.travellerListInited = true
          this.travellerListCached = cloneDeep(newVal)
        }
      },
    },
    otherInfoData() {
      this.$nextTick(() => {
        this.validate()
        // if (this.selectedTravellerId) {
        //   this.validate()
        // } else {
        //   this.clearValidate()
        // }
      })
    },
  },
  computed: {
    isTraveller() {
      return this.sectionType === TRAVELLER_TYPE
    },
    drawerTitle() {
      return this.selectedTravellerId === 0 ? this.$t('72651') : this.$t('72652')
    },
    showSkeleton() {
      return !this.drawerData.data || this.drawerData.data.length === 0
    },
    originalPhone() {
      const target = (this.data.info_items || []).find(item => item.style.type === 8)
      const content = (target || {}).content || ''
      const operation = (target || {}).operation || []
      return content && operation.length > 0 ? `${operation[0].field_key}-${content}` : null
    },
    additionalFormData() {
      const { addRequiredFormData, sectionType } = this

      if (!addRequiredFormData) {
        return []
      }

      // 使用 sectionType 判断
      if (addRequiredFormData[sectionType]) {
        return addRequiredFormData[sectionType]
      }

      // 对于出行人信息，判断booking级别和unit级别
      const travellerType = this.data && this.data.id === 0 ? 'booking' : 'unit'
      if (this.isTraveller && addRequiredFormData[travellerType]) {
        return addRequiredFormData[travellerType]
      }

      return []
    },
    tempAdditionalFormData() {
      return this.selectedTravellerId === 0 ? this.additionalFormData : []
    },
  },
  mounted() {
    eventBus.$on('add-traveller', this.handleAddTravellerFromBus)
  },
  beforeDestroy() {
    eventBus.$off('add-traveller')
  },
  methods: {
    openDrawer() {
      this.drawerVisible = true
      if (this.showSkeleton) {
        // 因为渲染国家选项的时候创建了很多html，会阻塞ui渲染，所以给一个延时
        setTimeout(() => {
          this.setDrawerData()
        }, 300)
      } else {
        this.setDrawerData()
      }
    },
    setDrawerData() {
      this.drawerData.data = cloneDeep([...this.tempAdditionalFormData, ...this.otherInfoData.data])
      this.$nextTick(() => {
        this.scrollDrawerToTop()

        if (this.selectedTravellerId) {
          this.validateForm()
          this.scrollToFormError()
        } else {
          this.$refs.form.clearValidate()
        }
      })
    },
    scrollDrawerToTop() {
      const drawerContentDom = this.$el.querySelector('.edit-drawer-content')
      drawerContentDom && (drawerContentDom.scrollTop = 0)
    },
    updateBottomInfo(val) {
      if (this.data.bottom_info) {
        this.$set(this.data.bottom_info, 'is_checkbox_selected', !!val)
      }
    },
    updateCustomInfo(field, id) {
      if (this.data && this.data.custom_info) {
        this.$set(this.data.custom_info, field, id)
      }
    },
    chooseTraveller(item) {
      this.selectedTravellerId = item.traveller_id
      this.updateCustomInfo('traveler_id_use', item.traveller_id)
      item = this.updateTravellerList(item)
      this.$emit('chooseTraveller', item)
    },
    updateTravellerList(item) {
      // 非新增的情况需要做2件事：
      // 1.拿sessionstorage里面的数据更新item
      // 2.更新travellerListCached
      const { traveller_id } = item
      const idx = this.travellerListCached.findIndex(ele => ele.traveller_id === traveller_id)

      if (idx >= 0) {
        item = getTravellerInfoFromCache(item)
        this.travellerListCached.splice(idx, 1, item)
      }

      return item
    },
    validate() {
      return this.$refs.travellerPanel.validate().catch(err => {
        console.warn(err)
        return false
      })
    },
    validateForm() {
      return this.$refs.form.validate().catch(err => {
        console.warn(err)
        return false
      })
    },
    scrollToFormError() {
      this.$nextTick(() => {
        const formEl = this.$refs.form.$el
        const errorDom = formEl && formEl.querySelector('.dynamic-form-item.klk-form-item-has-error')
        errorDom &&
          errorDom.scrollIntoView({
            block: 'center',
            behavior: 'smooth',
          })
      })
    },
    handleManualClick(id) {
      // 选择新建之后需要校验，并且拉出抽屉
      setTimeout(async () => {
        if (id === 0) {
          this.openDrawer()
        }
      }, 50)
    },
    clearValidate() {
      this.$refs.travellerPanel.clearValidate()
    },
    handleChange() {
      this.$emit('change')
    },
    handleInput() {
      this.$emit('input')
    },
    handleAddTravellerFromBus(data) {
      const { traveller_id } = data
      if (traveller_id) {
        const target = this.travellerListCached.find(item => item.traveller_id === traveller_id)
        if (!target) {
          this.travellerListCached.push(cloneDeep(data))
        }
      }
    },
    async handleSave() {
      if (!this.updateTravellerListFunc) {
        return
      }

      this.isLoading = true
      const tempTravellerId = this.selectedTravellerId || 0
      const res = await this.updateTravellerListFunc({
        traveller_id: tempTravellerId,
        items: getUpdateTravellerListParams(this.drawerData.data)
      })
      this.isLoading = false

      if (res.success) {
        const resData = res.result
        const additionalFormLength = this.tempAdditionalFormData.length

        // 如果后端返回的traveller_id为0，则表示后端报错了，这个时候要给一个假id
        if (resData.traveller_id === 0) {
          resData.traveller_id = getNewTravellerId()
        }

        cacheTravellerInfo(resData)

        const { traveller_id } = resData
        const target = this.travellerListCached.find(item => item.traveller_id === traveller_id)
        if (target) {
          this.updateTravellerList(target)
        } else {
          eventBus.$emit('add-traveller', resData)
          // 选中新增的这个数据
          this.selectedTravellerId = traveller_id
          this.$refs.travellerSelector.checkTravellerId(traveller_id)
          this.updateCustomInfo('traveler_id_use', traveller_id)
        }

        this.drawerVisible = false
        // this.otherInfoData.data = cloneDeep(this.drawerData.data.slice(this.tempAdditionalFormData.length))
        // 这里为了性能，暂时不用深复制
        this.otherInfoData.data = this.drawerData.data.slice(additionalFormLength)
        this.$nextTick(() => {
          this.validate()
        })
      }
    },
    drawerAlert(text) {
      const drawerElement = this.$refs.editDrawer
      if (drawerElement) {
        drawerElement.alert(text)
      }
    },
    async handleConfirm() {
      const isValid = await this.validateForm()

      if (isValid) {
        return this.handleSave()
      }

      setTimeout(() => {
        this.drawerAlert(this.$t('72688'))
        const errorDom = this.$el.querySelector('.dynamic-form-item.klk-form-item-has-error')
        errorDom &&
          errorDom.scrollIntoView({
            block: 'center',
            behavior: 'smooth',
          })
      }, 20)
    },
  },
}
</script>

<style lang="scss" scoped>
.other-info-traveller {
  margin-bottom: 16px;
  padding: 24px;
  border-radius: $radius-l;
  border: 1px solid $color-border-normal;

  &:last-child {
    margin-bottom: 24px;
  }

  &-title {
    @include font-body-s-bold;
    margin-bottom: 8px;
    color: $color-text-primary;
  }

  &-panel {
    margin-top: 20px;
  }

  &-drawer-form {
    margin-top: 24px;
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;

    &:first-child {
      margin-top: 0;
    }
  }
}
</style>
