<template>
  <div class="dynamic-form-skeleton">
    <div :style="{ width: wrapperWidth }" class="row">
      <div class="item">
        <BaseSkeleton skeleton-gap="4px" skeleton-width="100px" skeleton-height="20px" />
        <BaseSkeleton :skeleton-width="componentWidth" skeleton-height="40px" />
      </div>
    </div>
    <div :style="{ width: wrapperWidth }" class="row flex">
      <div class="item">
        <BaseSkeleton skeleton-gap="4px" skeleton-width="100px" skeleton-height="20px" />
        <BaseSkeleton :skeleton-width="componentWidth" skeleton-height="40px" />
      </div>
      <div class="item">
        <BaseSkeleton skeleton-gap="4px" skeleton-width="100px" skeleton-height="20px" />
        <BaseSkeleton :skeleton-width="componentWidth" skeleton-height="40px" />
      </div>
    </div>
  </div>
</template>

<script>
import BaseSkeleton from '@src/utils/components/Skeleton.vue'

export default {
  name: 'DynamicFormSkeleton',
  inject: ['wrapperWidth', 'componentWidth'],
  components: {
    BaseSkeleton
  }
}
</script>

<style lang="scss" scoped>
.row {
  margin-bottom: 24px;
}

.flex {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
</style>
