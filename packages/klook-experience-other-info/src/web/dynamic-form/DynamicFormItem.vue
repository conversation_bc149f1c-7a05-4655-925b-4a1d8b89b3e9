<template>
  <k-form-item
    class="dynamic-form-item"
    ref="formItem"
    :rules="genRules()"
    :prop="`${prop}.${valueType}`"
    :data-spm-item="spm"
  >
    <slot />
  </k-form-item>
</template>

<script>
import { FormItem } from '@klook/klook-ui/lib/form'
import '@klook/klook-ui/lib/styles/components/form.scss'
import { formRules } from '@src/utils/form-rules'

export default {
  name: 'DynamicFormItem',
  components: {
    KFormItem: FormItem,
  },
  props: {
    prop: {
      type: String,
      default: '',
    },
    data: {
      type: Object,
      default: () => ({}),
    },
    // 值在哪个字段里面，默认content，也有可能是operation
    valueType: {
      type: String,
      default: 'content',
    },
    // 适用于埋点
    spmAmount: {
      type: Number,
      default: null,
    },
  },
  computed: {
    spm() {
      const { data, valueType, spmAmount } = this
      const operationLength = data.operation ? data.operation.length : 0
      const fillIn = valueType === 'operation' ? operationLength : data[valueType]
      const amount = spmAmount !== null ? spmAmount : valueType === 'operation' ? operationLength : 0
      return `DetailInfoTab?ext=${JSON.stringify({
        FillIn: Boolean(fillIn),
        TabName: data.field_key,
        Amount: amount,
      })}`
    },
  },
  methods: {
    genRules() {
      return formRules.call(this)
    },
    validate() {
      // 提供在内部validate的能力，暂时没用到
      this.$refs.formItem.validate('change')
    },
  },
}
</script>

<style lang="scss" scoped></style>
