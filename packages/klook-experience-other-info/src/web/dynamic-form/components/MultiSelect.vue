<template>
  <div class="dynamic-form-multi-select">
    <k-select
      v-model="selectVal"
      multiple
      filterable
      :style-type="styleType"
      :max-height="300"
      :disabled="disabled"
      :placeholder="data.hint"
      :class="{ 'is-disabled': disabled }"
      style="width: 100%;"
      @change="handleChange"
    >
      <k-option
        v-for="(select, index) in selectDataList"
        :key="`${index}-${select.id}`"
        :value="select.field_key"
        :label="select[labelType] || select.name"
      />
    </k-select>
  </div>
</template>

<script>
import { Select, Option } from '@klook/klook-ui/lib/select'
import '@klook/klook-ui/lib/styles/components/select.scss'
import codeManager from '@src/utils/code'

export default {
  name: 'DynamicFormMultiSelect',
  components: {
    KSelect: Select,
    KOption: Option,
  },
  props: {
    value: {
      type: Array,
      default: () => [],
    },
    data: {
      type: Object,
      default: () => ({}),
    },
    styleType: {
      type: String,
      default: 'outlined',
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    // select 里面 label 使用的字段，值的范围：name，areaCode
    // 目前只在 mobile 的时候使用 areaCode
    labelType: {
      type: String,
      default: 'name',
    },
  },
  computed: {
    selectDataList() {
      const type = this.data.style.option_all_type
      const { countryList, languageList } = codeManager

      if (type === 1) {
        return countryList
      } else if (type === 2) {
        return languageList
      } else {
        return this.data.options
      }
    },
    listMap() {
      return this.selectDataList.reduce((acc, cur) => {
        acc[cur.field_key] = cur
        return acc
      }, {})
    },
    selectVal: {
      get() {
        return this.value.map(item => item.field_key)
      },
      set(val) {
        const newVal = val.join(',')
        const oldVal = this.value.map(item => item.field_key).join(',')

        // 多选组件有坑，需要加上这个判断
        if (newVal !== oldVal) {
          this.$emit(
            'input',
            val.map(key => this.listMap[key]),
          )
        }
      },
    },
  },
  methods: {
    handleChange(val) {
      this.$emit(
        'change',
        val.map(key => this.listMap[key]),
      )
    },
  },
}
</script>

<style lang="scss" scoped></style>
