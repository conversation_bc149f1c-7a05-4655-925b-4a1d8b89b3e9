<template>
  <div class="dynamic-form-select">
    <k-select
      v-model="selectVal"
      :style-type="styleType"
      :disabled="disabled"
      :placeholder="data.hint"
      style="width: 100%;"
      filterable
      @change="handleChange"
    >
      <template v-if="isNeedRecommend">
        <k-option-group :label="$t('48265-frequently_used')">
          <k-option
            v-for="(select, index) in recommendObj.recommendList"
            :key="`${index}-${select.id}`"
            :value="select.field_key"
            :label="select[labelType] || select.name"
          />
        </k-option-group>
        <k-option-group :label="$t('country.otherCountriesOrDistricts')">
          <k-option
            v-for="(select, index) in recommendObj.otherList"
            :key="`${index}-${select.id}`"
            :value="select.field_key"
            :label="select[labelType] || select.name"
          />
        </k-option-group>
      </template>
      <template v-else>
        <k-option
          v-for="(select, index) in selectDataList"
          :key="`${index}-${select.id}`"
          :value="select.field_key"
          :label="select[labelType] || select.name"
        />
      </template>
    </k-select>
  </div>
</template>

<script>
import { Select, Option, OptionGroup } from '@klook/klook-ui/lib/select'
import '@klook/klook-ui/lib/styles/components/select.scss'
import codeManager from '@src/utils/code'
import getLangPreferCountryCode from '@src/utils/prefer-country'
import { isServer, getKlkLanguage } from '@src/utils'

export default {
  name: 'DynamicFormSelect',
  components: {
    KSelect: Select,
    KOption: Option,
    KOptionGroup: OptionGroup,
  },
  props: {
    value: {
      type: Array,
      default: () => [],
    },
    data: {
      type: Object,
      default: () => ({}),
    },
    styleType: {
      type: String,
      default: 'outlined',
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    // select 里面 label 使用的字段，值的范围：name，areaCode
    // 目前只在 mobile 的时候使用 areaCode
    labelType: {
      type: String,
      default: 'name',
    },
  },
  computed: {
    isNeedRecommend() {
      // country 的时候要加上推荐和搜索
      return this.data.style.option_all_type === 1
    },
    selectDataList() {
      const type = this.data.style.option_all_type
      const { countryList, languageList } = codeManager

      if (type === 1) {
        return countryList
      } else if (type === 2) {
        return languageList
      } else {
        return this.data.options
      }
    },
    recommendObj() {
      let recommendList = []
      let otherList = []
      const { countryList } = codeManager

      // 目前暂时把推荐的业务逻辑写在了组件内部，如果还要支持其它推荐的话，就需要抽出来了
      if (this.isNeedRecommend && !isServer) {
        const preferLangList = getLangPreferCountryCode(getKlkLanguage(this))
        recommendList = countryList
          .filter(option => preferLangList.includes(option.country_code))
          .sort((a, b) => preferLangList.indexOf(a.country_code) - preferLangList.indexOf(b.country_code))
        otherList = countryList.filter(option => !preferLangList.includes(option.country_code))
      }

      return { recommendList, otherList }
    },
    selectVal: {
      get() {
        return this.value && this.value.length ? this.value[0].field_key : ''
      },
      set(val) {
        const target = this.selectDataList.find(item => item.field_key === val)
        this.$emit('input', target ? [target] : [])
      },
    },
  },
  methods: {
    handleChange(val) {
      this.$emit('change', val ? [val] : [])
    },
  },
}
</script>

<style lang="scss">
.dynamic-form-select {
  h1 {
    margin: 0;
  }
}
</style>

<style lang="scss" scoped></style>
