<template>
  <div class="dynamic-form-multi">
    <!-- <DynamicFormItem
      v-for="(item, index) in (data.operation || [])"
      :key="`${index}-${item.id}`"
      :data="item"
      :prop="`${prop}.operation[${index}]`"
      ref="formItem"
      :value-type="getValueKey(item)"
      :spm-amount="data.operation.length"
      class="multi-wrap"
    >
      <component
        v-model="item[getValueKey(item)]"
        :data="item"
        :disabled="disabled"
        :is="getComponentName(item)"
        class="multi-item"
        @change="handleChange"
        @input="handleInput"
      />
    </DynamicFormItem> -->
  </div>
</template>

<script>
// import { OPERATION_TYPES, WEB_TYPE_MAP } from '@src/utils'
// import DynamicFormItem from '../DynamicFormItem.vue'
// import DynamicFormCheckbox from './Checkbox.vue'

/**
 * 这个组件完全和 tiles 组件重复了，但是后端要另起一个type，没办法
 */

export default {
  name: 'DynamicFormMulti',
  // components: {
  //   DynamicFormItem,
  //   DynamicFormCheckbox
  // },
  // props: {
  //   // 由于有dynamic-form-item，所以需要传prop
  //   prop: {
  //     type: String,
  //     default: '',
  //   },
  //   data: {
  //     type: Object,
  //     default: () => ({}),
  //   },
  //   disabled: {
  //     type: Boolean,
  //     default: false,
  //   },
  // },
  // methods: {
  //   getComponentName(item) {
  //     // 注意：这里暂时只支持checkbox，如果要支持其它组件，请自己引入
  //     return WEB_TYPE_MAP[item.style.type]
  //   },
  //   getValueKey(data) {
  //     return OPERATION_TYPES.includes(data.style.type) ? 'operation' : 'content'
  //   },
  //   handleChange(val) {
  //     this.$emit('change', val)
  //   },
  //   handleInput(val) {
  //     this.$emit('input', val)
  //   }
  // }
}
</script>

<style lang="scss" scoped>
// .dynamic-form-multi {
//   width: 100%;

//   .multi-wrap {
//     .multi-item {
//       margin-top: 16px;
//     }

//     &:first-child {
//       .multi-item {
//         margin-top: 0;
//       }
//     }
//   }
// }
</style>
