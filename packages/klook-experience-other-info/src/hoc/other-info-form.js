import cloneDeep from 'lodash.clonedeep'
import inhouseMixin from '@src/utils/mixins/inhouse'
import childEmitMixin from '@src/utils/mixins/child-emit'

import {
  TRAVELLER_TYPE,
  CONTACT_TYPE,
  cleanDynamicFormList,
  copyOperation,
  replaceMobile,
  formatOtherInfoItems,
  backFillData,
  checkDynamicFormList,
  diffDynamicFormList,
} from '@src/utils'

export default function otherInfoFormHoc(WrapperComponent) {
  return {
    name: 'OtherInfoFormHoc',
    inheritAttrs: false,
    mixins: [inhouseMixin, childEmitMixin],
    props: {
      sectionType: {
        type: Number,
        default: 1,
      },
      data: {
        type: Object,
        default: null,
      },
      // 只渲染表单就传这个，不传 data，如果同时传的话，会优先使用 data
      // 在使用这个字段的时候，emit出来的也是一个数组
      formData: {
        type: Array,
        default: null,
      },
    },
    data() {
      return {
        choosedTravellerData: null,
        choosedShipmentData: null,
        otherInfoDataCached: null, // 缓存，目前用于新建的联系人的值
        otherInfoData: {
          data: [],
        },
      }
    },
    computed: {
      isContact() {
        return this.sectionType === CONTACT_TYPE
      },
      isTraveller() {
        return this.sectionType === TRAVELLER_TYPE
      },
      useFormData() {
        return !!this.formData && !this.data
      },
      otherInfoItems() {
        return this.useFormData ? this.formData || [] : this.data.info_items || []
      },
      preProcessedOtherInfoItems() {
        let result = cloneDeep(this.otherInfoItems)

        // 数据预处理：有些字段为空的时候后端没有返回，但是这些字段又是v-model的值，所以要加上
        formatOtherInfoItems(result)

        // 数据预处理：对于联系人信息，mobile里面只有field_key，这里加上areaCode
        replaceMobile(result)

        // 数据预处理：把平铺类型的表单的options复制到operation里面去
        // 数据预处理，把多选checkbox的options复制到operation里面去
        copyOperation(result)

        return result
      },
    },
    watch: {
      otherInfoItems: {
        immediate: true,
        handler() {
          this.generateOtherInfoData()
        },
      },
      choosedTravellerData() {
        this.generateOtherInfoData()
      },
      choosedShipmentData() {
        this.generateOtherInfoData()
      },
    },
    render(h) {
      const data = this.useFormData ? { info_items: this.formData || [] } : this.data
      const args = {
        attrs: {
          ...this.$props,
          ...this.$attrs,
          data,
          otherInfoData: this.otherInfoData,
        },
        on: {
          ...this.$listeners,
          chooseTraveller: this.chooseTraveller,
          chooseShipment: this.chooseShipment,
          change: this.handleChange,
          input: this.handleInput,
        },
        scopedSlots: this.$scopedSlots,
        ref: 'wrapped',
      }

      return data ? h(WrapperComponent, args) : null
    },
    methods: {
      openShipment() {
        this.$refs.wrapped.openShipment()
      },
      validate() {
        return this.$refs.wrapped.validate()
      },
      chooseTraveller(item) {
        this.choosedTravellerData = item
      },
      chooseShipment(item) {
        this.choosedShipmentData = item
      },
      handleChange() {
        this.$emit('change', this.getModifiedData())
      },
      handleInput() {
        this.$emit('input')
      },

      /**
       * other-info 处理
       */
      generateOtherInfoData() {
        // 新建的情况要使用缓存
        if (
          this.choosedTravellerData &&
          this.choosedTravellerData.traveller_id === 0 &&
          !!this.otherInfoDataCached
        ) {
          this.otherInfoData = this.otherInfoDataCached
          return
        }

        let result = cloneDeep(this.preProcessedOtherInfoItems)

        // 回填数据-邮寄信息
        if (this.choosedShipmentData && this.choosedShipmentData.address_id) {
          result = backFillData(result, this.choosedShipmentData.items)
        }

        // 回填数据-出行人列表
        if (this.choosedTravellerData && this.choosedTravellerData.traveller_id) {
          result = backFillData(result, this.choosedTravellerData.items)
        }

        // 处理出行人新建的情况
        if (this.choosedTravellerData && this.choosedTravellerData.traveller_id === 0) {
          if (this.isContact) {
            // 对于联系人信息，this.data里面有默认值，此时如果选中添加的话，需要清空里面的值
            cleanDynamicFormList(result)
          }

          this.otherInfoDataCached = { data: result }
        }

        // 这里由于klk-form表单的model不能传array，所以用data包一层
        this.otherInfoData = { data: result }

        // 触发一次change事件
        this.handleChange()
      },
      getModifiedData() {
        if (this.useFormData) {
          return this.shimItems(this.otherInfoData.data)
        }

        // 提交数据的时候要处理 fakeTravellerId 的情况
        let customInfo = this.data && this.data.custom_info
        if (customInfo && customInfo.traveler_id_use < 0) {
          customInfo = {
            ...customInfo,
            traveler_id_use: 0
          }
        }

        return {
          ...this.data,
          info_items: this.shimItems(this.otherInfoData.data),
        }
      },
      shimItems(items) {
        // 修建 items，把options设置为空
        const copy = cloneDeep(items || [])
        copy.forEach(item => {
          item.options = []
          item.operation = this.shimItems(item.operation)
        })
        return copy
      },
      checkIsModified() {
        return checkDynamicFormList(this.preProcessedOtherInfoItems, this.otherInfoData.data)
      },
      getDiffItems() {
        const result = []
        diffDynamicFormList(this.preProcessedOtherInfoItems, this.otherInfoData.data, result)
        return {
          ...this.data,
          info_items: result,
        }
      }
    },
  }
}
