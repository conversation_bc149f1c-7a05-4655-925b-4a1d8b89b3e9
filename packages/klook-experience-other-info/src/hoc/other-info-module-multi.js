import { getTrackingInfos } from '@src/utils'
import childEmitMixin from '@src/utils/mixins/child-emit'

export default function otherInfoModuleMultiHoc(WrapperComponent) {
  return {
    name: 'OtherInfoModuleMultiHoc',
    inheritAttrs: false,
    mixins: [childEmitMixin],
    props: {
      data: {
        type: Array,
        default: () => [],
      },
    },
    data() {
      return {
        moduleList: [],
      }
    },
    render(h) {
      const args = {
        attrs: {
          ...this.$props,
          ...this.$attrs,
        },

        on: {
          ...this.$listeners,
          add: this.addModule,
          remove: this.removeModule,
        },

        scopedSlots: this.$scopedSlots,
        ref: 'wrapped',
      }

      return h(WrapperComponent, args)
    },
    methods: {
      addModule(index, data) {
        this.moduleList[index] = data
      },
      removeModule(index) {
        this.moduleList[index] = null
      },
      async validate() {
        let isValid = true
        for (const moduleItem of this.moduleList) {
          if (moduleItem && !(await moduleItem.validate())) {
            isValid = false
          }
        }
        return isValid
      },
      async getValidateKeys() {
        let keyList = []
        for (let i = 0; i < this.moduleList.length; i += 1) {
          const moduleItem = this.moduleList[i]
          const childKeyList = moduleItem ? await moduleItem.getValidateKeys() : []
          if (childKeyList.length > 0) {
            const sectionType = this.data[i].section_type || 1
            keyList = keyList.concat(childKeyList.map(childKey => `${sectionType}-${childKey}`))
          }
        }
        return keyList
      },
      getModifiedData() {
        return this.moduleList.map(moduleItem => (moduleItem ? moduleItem.getModifiedData() : null))
      },
      getTrackingInfos() {
        // 埋点需要，查找出没填和报错的other-info，以xxxx-{xxxx}的形式聚集在一起
        try {
          const modifiedData = this.getModifiedData()
          return getTrackingInfos(modifiedData)
        } catch (error) {
          console.log('other-info tracking error', error)
          return null
        }
      },
    },
  }
}
