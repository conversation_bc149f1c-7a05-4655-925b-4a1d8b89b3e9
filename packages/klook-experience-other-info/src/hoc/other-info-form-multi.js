import childEmitMixin from '@src/utils/mixins/child-emit'

export default function otherInfoFormMultiHoc(WrapperComponent) {
  return {
    name: 'OtherInfoFormMultiHoc',
    inheritAttrs: false,
    mixins: [childEmitMixin],
    data() {
      return {
        formList: [],
      }
    },
    render(h) {
      const args = {
        attrs: {
          ...this.$props,
          ...this.$attrs,
        },

        on: {
          ...this.$listeners,
          add: this.addForm,
          remove: this.removeForm,
        },

        scopedSlots: this.$scopedSlots,
        ref: 'wrapped',
      }

      return h(WrapperComponent, args)
    },
    methods: {
      addForm(index, data) {
        this.formList[index] = data
      },
      removeForm(index) {
        this.formList[index] = null
      },
      async validate() {
        let isValid = true
        for (const formItem of this.formList) {
          if (formItem && !(await formItem.validate())) {
            isValid = false
          }
        }
        return isValid
      },
      async getValidateKeys() {
        const keyList = []
        for (let i = 0; i < this.formList.length; i += 1) {
          const formItem = this.formList[i]
          const valid = formItem ? await formItem.validate() : true
          if (!valid) {
            keyList.push(i)
          }
        }
        return keyList
      },
      getModifiedData() {
        return this.formList.map(formItem => (formItem ? formItem.getModifiedData() : null))
      },
      checkIsModified() {
        return this.formList.some(formItem => (formItem ? formItem.checkIsModified() : false))
      },
      getDiffItems() {
        return this.formList.reduce((accu, curr) => {
          const formInfo = curr ? curr.getDiffItems() : {}
          if (formInfo.info_items && formInfo.info_items.length > 0) {
            accu.push(formInfo)
          }
          return accu
        }, [])
      },
      openShipment() {
        return this.formList.some(formItem => (formItem ? formItem.openShipment() : false))
      },
    },
  }
}
