import countryCode from './country'
import languageCode from './language'
import languageMap from './map'
import { isServer, getKlkLanguage } from '@src/utils'

class CodeManager {
  constructor() {
    // 语言码
    this._languageCode = languageCode
    this.languageList = this.parseCode('language')

    // 国家码兜底
    if (!isServer) {
      this.isCountryDefault = true
      this._countryCode = countryCode
      this.countryList = countryCode
      this.countryList.forEach(item => {
        item.name = JSON.parse(JSON.stringify(item.i18ns[languageMap[getKlkLanguage()]] || item.i18ns['en_US']))
        item.areaCode = `${item.name} (+${item.country_number})`
        item.field_key = item.country_code
        delete item.i18ns
      })
      this.countryCodeList = this.parseCountryCode()
    }
  }

  initCountryCode(countryCode) {
    if (this.isCountryDefault && countryCode && countryCode.length > 0) {
      this._countryCode = countryCode
      this.countryList = this.parseCode('country')
      this.countryCodeList = this.parseCountryCode()
      this.isCountryDefault = false
    }
  }

  parseCode(type) {
    const data = type === 'country' ? this._countryCode : this._languageCode

    if (!isServer) {
      if (type === 'country') {
        data.forEach(item => {
          item.areaCode = `${item.name} (+${item.country_number})`
          item.field_key = item.country_code
        })
      } else {
        data.forEach(item => {
          item.name = JSON.parse(
            JSON.stringify(item.i18ns[languageMap[getKlkLanguage()]] || item.i18ns['en_US']),
          )
          item.areaCode = ''
          item.field_key = item.code5
          delete item.i18ns
        })
      }
    }

    return data
  }

  parseCountryCode() {
    return this.countryList.reduce((accu, curr) => {
      accu[curr.country_code] = curr
      return accu
    }, {})
  }
}

export default new CodeManager()
