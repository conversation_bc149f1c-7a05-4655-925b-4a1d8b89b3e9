<template>
  <div :style="styles" class="skeleton"></div>
</template>

<script>
export default {
  props: {
    skeletonWidth: {
      type: String,
      default: ''
    },
    skeletonGap: {
      type: String,
      default: ''
    },
    skeletonHeight: {
      type: [Number, String],
      default: 14
    }
  },
  computed: {
    styles() {
      const styles = {}

      if (this.skeletonWidth) {
        styles.width = this.skeletonWidth
      }

      if (this.skeletonHeight) {
        styles.height = this.skeletonHeight
      }

      if (this.skeletonGap) {
        styles.marginBottom = this.skeletonGap
      }

      return styles
    }
  }
}
</script>

<style lang="scss" scoped>
@keyframes skeleton-loading {
  0% {
    background-position: 100% 50%;
  }

  100% {
    background-position: 0 50%;
  }
}

.skeleton {
  background-size: 400% 100%;
  animation: skeleton-loading 1.4s ease infinite;
  border-radius: $radius-s;
  margin-bottom: 8px;
  background-image: linear-gradient(90deg, #e9e9e9 25%, #e3e3e3 40%, #e9e9e9 65%);
}
</style>
