export default function getLangPreferCountryCode(lang) {
  var langMap = {
    'zh-TW': ['TW', 'HK', 'MO', 'SG', 'CN', 'TH', 'MY', 'AU', 'US', 'KR'],
    'zh-HK': ['HK', 'TW', 'MO', 'SG', 'CN', 'TH', 'MY', 'AU', 'US', 'KR'],
    'zh-CN': ['CN', 'HK', 'SG', 'MY', 'KR', 'US', 'CA', 'AU', 'TH'],
    en: ['DE', 'FR', 'RU', 'NL', 'BE', 'CH', 'SE', 'ES', 'BR', 'AE'],
    'en-US': ['US', 'MX', 'CA', 'BR', 'CO', 'AG', 'PE', 'VE', 'CL', 'KR'],
    'en-AU': ['AU', 'NZ', 'PG', 'SG', 'ID', 'MY', 'IN', 'AE', 'GB', 'US'],
    'en-NZ': ['NZ', 'AU', 'PG', 'SG', 'ID', 'MY', 'IN', 'AE', 'GB', 'US'],
    'en-GB': ['GB', 'DK', 'FI', 'IS', 'NO', 'SE', 'IE', 'BE', 'NL', 'CH'],
    'en-IN': ['IN', 'LK', 'PK', 'BD', 'MY', 'SG', 'NP', 'AE', 'GB', 'US'],
    'en-SG': ['SG', 'MY', 'PH', 'TH', 'ID', 'IN', 'AU', 'HK', 'CA', 'GB'],
    'en-CA': ['CA', 'US', 'HK', 'GB', 'FR', 'LB', 'AE', 'SG', 'PH', 'CN'],
    'en-HK': ['HK', 'TW', 'MO', 'SG', 'CN', 'TH', 'MY', 'AU', 'US', 'KR'],
    'en-PH': ['PH', 'SG', 'MY', 'TH', 'ID', 'IN', 'AU', 'HK', 'CA', 'US'],
    'en-MY': ['MY', 'SG', 'PH', 'TH', 'ID', 'IN', 'AU', 'HK', 'AE', 'GB'],
    ko: ['KR', 'CN', 'JP', 'US', 'TW', 'HK', 'PH', 'SG', 'TH', 'MY'],
    th: ['TH', 'SG', 'HK', 'PH', 'KR', 'MY', 'IN', 'AU', 'ID', 'CA'],
    vi: ['VN', 'SG', 'HK', 'PH', 'KR', 'MY', 'IN', 'AU', 'ID', 'CA'],
    id: ['ID', 'SG', 'HK', 'MY', 'PH', 'TW', 'KR', 'CN', 'TH', 'US'],
    ja: ['JP', 'US', 'GB', 'AU', 'CA', 'SG', 'CN', 'HK', 'TW', 'KR'],
    de: ['DE', 'AU', 'CH', 'BE', 'DK', 'SE', 'GB', 'NL', 'NO', 'IS'],
    it: ['IT', 'AL', 'US', 'CH', 'CA', 'MT', 'BE', 'HR', 'SM', 'MC'],
    fr: ['FR', 'CA', 'BE', 'CH', 'LU', 'SE', 'NL', 'GB', 'MC', 'MA'],
    ru: ['RU', 'UA', 'KZ', 'BY', 'KG', 'EE', 'LV', 'LT', 'IL', 'MD'],
    es: ['ES', 'MX', 'AR', 'CO', 'PE', 'VE', 'CL', 'EC', 'CR', 'PA'],
  }
  return langMap[lang] || []
}
