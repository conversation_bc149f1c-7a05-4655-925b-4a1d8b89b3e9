import codeManager from '@src/utils/code'
import { validateDynamicFormItem } from '@src/utils/form-rules'
import { getTravellerCache, setTravellerCache } from '@src/utils/traveller-cache'

export const isServer = typeof window === 'undefined'

/**
 * TODO: 这里有一个已知的问题， 就是日期的格式有横杠有斜杠， 目前在判断的时候不会区分
 * 所以就算日期相同， 但是一个是斜杠一个是横杠， 就会不一样
 */
export const CONTENT_TYPES = [0, 1, 2, 5, 7, 9]
export const OPERATION_TYPES = [3, 4]
export const CONTENT_AND_OPERATION_TYPES = [8]
export const FULL_LINE_TYPES = [2, 5, 7, 8, 10]
export const SOLE_TYPES = [8, 10]
export const WRAPPER_TYPES = [6]
export const GROUP_TYPES = [10] // TODO：临时增加，后面要去掉
export const OPERATION_COPY_TYPES = [6, 10]
export const SPM_AMOUNT_TYPES = [10] // 使用父组件而不是自己的operation的length
export const TRAVELLER_TYPE = 1
export const SHIPMENT_TYPE = 2
export const CONTACT_TYPE = 4
export const CHECKBOX_CHECKED_VALUES = [1, '1', true]
export const TRAVELLER_INFO_CACHE_KEY = 'TRAVELLER_INFO_CACHE_KEY'

export const WEB_TYPE_MAP = {
  0: 'DynamicFormInput',
  1: 'DynamicFormDate',
  2: 'DynamicFormDatetime',
  3: 'DynamicFormSelect',
  4: 'DynamicFormMultiSelect',
  5: 'DynamicFormCheckbox',
  6: 'DynamicFormTiled',
  7: 'DynamicFormTextarea',
  8: 'DynamicFormMobile',
  9: 'DynamicFormTimePicker',
  10: 'DynamicFormMulti',
}
export const MWEB_TYPE_MAP = {
  0: 'DynamicFormInput',
  1: 'DynamicFormDate',
  2: 'DynamicFormDate',
  3: 'DynamicFormSelect',
  4: 'DynamicFormMultiSelect',
  5: 'DynamicFormCheckbox',
  6: 'DynamicFormTiled',
  7: 'DynamicFormTextarea',
  8: 'DynamicFormMobile',
  9: 'DynamicFormDate',
  10: 'DynamicFormMulti',
}

/**
 * 检查是否应该 check operation
 * 1.is_group 为 true
 */
export function shouldCheckOperations(item) {
  const { type } = item.style || {}
  return item.is_group || GROUP_TYPES.includes(type)
}

/**
 * 轻量级的单个动态表单比较方法，目前只比较field_key、 content、 operation、 is_group
 * bff有时会自己造数据，所以不能用id而使用field_key，field_key在同一层级下是唯一的
 * @return {Boolean} 是否发生了改变
 */
export function checkDynamicFormItem(originalDataItem, dataItem) {
  // 这里用于 语言码 和 国家码 的比较
  if (originalDataItem.field_key !== dataItem.field_key) {
    return true
  }

  if (originalDataItem.content !== dataItem.content) {
    return true
  }

  const operation = dataItem.operation || []
  const originalOperation = originalDataItem.operation || []

  // 这里不能加is_group，因为select等组件需要比较operation
  if (operation.length > 0 || originalOperation.length > 0) {
    return checkDynamicFormList(originalOperation, operation)
  }

  return false
}

/**
 * 轻量级的动态表单数组比较方法
 * @return {Boolean} 是否发生了改变
 */
export function checkDynamicFormList(originalDataList, dataList) {
  // 为了性能，不考虑排序
  if (originalDataList.length !== dataList.length) {
    return true
  }

  for (let i = 0, j = originalDataList.length; i < j; ++i) {
    if (checkDynamicFormItem(originalDataList[i], dataList[i])) {
      return true
    }
  }

  return false

  // 考虑排序的写法
  // let result = false
  // dataList.forEach((item) => {
  //   if (result) return

  //   const originalItem = originalDataList.find(ori => ori.field_key === item.field_key)

  //   if (originalItem) {
  //     result = checkDynamicFormItem(originalItem, item)
  //   } else {
  //     result = true
  //   }
  // })

  // originalDataList.forEach((originalItem) => {
  //   if (result) return

  //   const item = dataList.find(item => originalItem.field_key === item.field_key)

  //   if (!item) {
  //     result = true
  //   }
  // })

  // return result
}

/**
 * 轻量级的动态表单比较方法，目前只比较id、content、operation
 * 用来比较item
 * 返回一个扁平的数组，里面的元素是单个动态表单元素
 * @return {Array}
 */
export function diffDynamicFormItem(originalDataItem, dataItem, result) {
  let hasPushed = false // 是否已经pushed的标记，防止二次push

  if (originalDataItem.field_key !== dataItem.field_key || originalDataItem.content !== dataItem.content) {
    result.push(dataItem)
    hasPushed = true
  }

  const operation = dataItem.operation || []
  const originalOperation = originalDataItem.operation || []

  // 用id序列化operation
  const selection = operation
    .map(item => item.field_key)
    .sort()
    .join('-')
  const originalSelection = originalOperation
    .map(item => item.field_key)
    .sort()
    .join('-')

  if (!hasPushed && originalSelection !== selection) {
    result.push(dataItem)
  }

  if (shouldCheckOperations(dataItem) && (operation.length > 0 || originalOperation.length > 0)) {
    diffDynamicFormList(originalOperation, operation, result)
  }
}

/**
 * 轻量级的动态表单比较方法，目前只比较id、content、operation
 * 用来比较list
 * 返回一个扁平的数组，里面的元素是单个动态表单元素
 * @return {Array}
 */
export function diffDynamicFormList(originalDataList, dataList, result) {
  if (originalDataList.length === 0 && dataList.length === 0) return

  // 处理子元素
  dataList.forEach(item => {
    const originalItem = originalDataList.find(ori => ori.field_key === item.field_key)

    if (originalItem) {
      diffDynamicFormItem(originalItem, item, result)
    } else {
      result.push(item)
    }
  })

  originalDataList.forEach(originalItem => {
    const item = dataList.find(item => originalItem.field_key === item.field_key)

    if (!item) {
      result.push(originalItem)
    }
  })
}

/**
 * 清空表单数据(注意，这个方法是有副作用的)
 * @return {Array}
 */
export function cleanDynamicFormList(dataList) {
  dataList.forEach(item => {
    const { type } = item.style

    if (CONTENT_TYPES.includes(type)) {
      // 只有 content 需要清空
      item.content = ''
    } else if (OPERATION_TYPES.includes(type)) {
      // 只有 operation 需要清空
      item.operation = []
    } else if (CONTENT_AND_OPERATION_TYPES.includes(type)) {
      // content 和 operation 都需要清空
      item.content = ''
      item.operation = []
    }

    // item.operation && item.is_group && cleanDynamicFormList(item.operation)
    shouldCheckOperations(item) && cleanDynamicFormList(item.operation || [])
  })

  return dataList
}

/**
 * 遍历表单
 * @return {Array}
 */
export function traverseDynamicFormItem(dataList, callback) {
  dataList.forEach(item => {
    callback(item)
    shouldCheckOperations(item) && traverseDynamicFormItem(item.operation || [], callback)
  })
}

export function validateFormItem(formItem) {
  const { type } = formItem.style || {}
  const isContentEmpty = !formItem.content
  const isOperationEmpty = !formItem.operation || formItem.operation.length === 0

  let isEmpty = false
  let isInvalid = false
  if (CONTENT_TYPES.includes(type)) {
    isEmpty = isEmpty || isContentEmpty
    isInvalid = isInvalid || !validateDynamicFormItem(formItem.content, formItem).success
  } else if (OPERATION_TYPES.includes(type)) {
    isEmpty = isEmpty || isOperationEmpty
    isInvalid = isInvalid || !validateDynamicFormItem(formItem.operation, formItem).success
  } else if (CONTENT_AND_OPERATION_TYPES.includes(type)) {
    isEmpty = isEmpty || isContentEmpty || isOperationEmpty
    isInvalid = isInvalid || !validateDynamicFormItem(formItem.content, formItem).success
    isInvalid = isInvalid || !validateDynamicFormItem(formItem.operation, formItem).success
  }
  return { isEmpty, isInvalid }
}

export function getTrackingInfos(sectionOtherInfos) {
  // 查找出没填和报错的other-info，以xxxx-{xxxx}的形式聚集在一起
  const emptyInfos = []
  const invalidInfos = []
  sectionOtherInfos.forEach(sectionOtherInfo => {
    sectionOtherInfo.form_infos.forEach(formInfo => {
      traverseDynamicFormItem(formInfo.info_items || [], formItem => {
        const { isEmpty, isInvalid } = validateFormItem(formItem)

        if (isEmpty) {
          emptyInfos.push(`${sectionOtherInfo.section_type}-{${formItem.field_key}}`)
        }

        if (isInvalid) {
          invalidInfos.push(`${sectionOtherInfo.section_type}-{${formItem.field_key}}`)
        }
      })
    })
  })
  return { emptyInfos, invalidInfos }
}

export function hasOtherInfoData(data) {
  let item = (data || {}).structured_other_info
  if (!item) {
    return false
  }
  const { extra_info, preposition_other_info, traveler_other_info } = item
  const hasTravelerOtherInfo =
    (traveler_other_info || {}).booking_traveler_info || (traveler_other_info || {}).unit_traveler_info
  return !!(extra_info || preposition_other_info || hasTravelerOtherInfo)
}

export function otherInfoItem(data) {
  let item = (data || {}).structured_other_info
  const { extra_info, preposition_other_info, traveler_other_info } = item
  return {
    extraInfo: extra_info || [],
    prepositionOtherInfo: preposition_other_info || [],
    travelerOtherInfo: traveler_other_info || {},
  }
}

export function formRequired(formList) {
  return formList.find(item => {
    const { style, content } = item
    return content === '' && style.required
  })
}

export function isOtherInfoComplete(data) {
  const info = otherInfoItem(data)
  const extraInfoComplete = !!formRequired(info.extraInfo)
  if (extraInfoComplete) {
    return false
  }

  const travelerOtherInfoComplete = info.travelerOtherInfo.unit_traveler_info.find(item => {
    return !!formRequired(item.other_info_item)
  })
  if (travelerOtherInfoComplete) {
    return false
  }
  return true
}

/**
 * 把 option 中的数据复制到 operation 中去
 * (由于平铺类型的数据在options里面而不在operation里面，所以要先复制过去)
 */
export function copyOperation(data) {
  const list = data || []
  list.forEach(item => {
    const { style, options, operation } = item
    if (style && OPERATION_COPY_TYPES.includes(style.type)) {
      item.operation = item.options
    }

    copyOperation(options)
    copyOperation(operation)
  })
}

/**
 * 替换 mobile 里面的国家码（因为联系人信息的数据是后端自己造的）
 * （目前只替换第一层的 mobile）
 */
export function replaceMobile(data) {
  const list = data || []
  const { countryList } = codeManager
  list.forEach(item => {
    const { style = {}, operation = [] } = item || {}
    if (style.type === 8 && operation && operation.length > 0 && !operation[0].areaCode) {
      const countryKey = item.operation[0].field_key
      const target = countryList.find(country => country.country_code === countryKey)

      if (target) {
        item.operation.splice(0, 1, target)
      }
    }
  })
}

/*
 * 反填内容遍历代码
 * 后端数据结构层级过深问题，此处代码修改请慎重。
 * （重要）这里会reset数据，先把content和operation置空，然后再回填
 */
export function backFillData(treeData, listData, cache) {
  const treeList = treeData || []
  const { countryList, languageList } = codeManager

  // 把listData转成map结构来提升性能
  if (!cache) {
    cache = new Map()

    listData.forEach(item => {
      const { field_key } = item

      if (field_key) {
        const cachedList = cache.get(field_key)
        if (!cachedList) {
          cache.set(field_key, [item])
        } else {
          cachedList.push(item)
        }
      }
    })
  }

  treeList.forEach(treeItem => {
    // 复制operation
    if (OPERATION_COPY_TYPES.includes(treeItem.style.type)) {
      treeItem.operation = treeItem.options
      backFillData(treeItem.operation, listData, cache)
      return
    }

    // reset 数据
    treeItem.operation = []
    treeItem.content = ''

    if (treeItem.is_group) {
      // is_group的情况下
      // 1.要回填它下面的所有项
      // 2.选择有效的项(目前不考虑group多选的情况)（可能不是第一个）
      const options = treeItem.options || []
      const listOptions = cache.get(treeItem.field_key) || []
      // const listOptions = listData.filter(listItem => listItem.field_key === treeItem.field_key)
      backFillData(options, listData, cache)

      for (let option of options) {
        const target = listOptions.find(
          listOption =>
            listOption.options_value &&
            listOption.options_value.length > 0 &&
            listOption.options_value[0].field_key === option.field_key,
        )

        if (target) {
          treeItem.operation.push(option)
          break
        }
      }
    } else {
      // 非is_group的情况下，直接回填第一个有效的项即可
      // const listItem = listData.find(item => item.field_key === treeItem.field_key)
      const cachedList = cache.get(treeItem.field_key)
      const listItem = cachedList ? cachedList[0] : null

      if (listItem && listItem.options_value && listItem.options_value.length > 0) {
        let options = []

        if (treeItem.style.option_all_type === 1) {
          options = countryList
        } else if (treeItem.style.option_all_type === 2) {
          options = languageList
        } else {
          options = treeItem.options || []
        }

        listItem.options_value.forEach(listItemOption => {
          const targetOption = options.find(option => option.field_key === listItemOption.field_key)
          if (targetOption) {
            treeItem.operation.push(targetOption)
          }
        })
      }

      // 回填到 content 里面去
      if (listItem && listItem.content) {
        treeItem.content = listItem.content
        // 对没有手机号，按照要求特殊处理
        if (treeItem.style.type === 8 && treeItem.operation.length === 0) {
          treeItem.content = ''
        }
      }
    }

    // 校验数据，如果失败则清空
    const { isInvalid } = validateFormItem(treeItem)
    if (isInvalid) {
      treeItem.operation = []
      treeItem.content = ''
    }
  })
  return treeData
}

export function setDefaultValue(data, key, value) {
  if (!data || !key) {
    return
  }

  if ([undefined, null].includes(data[key])) {
    data[key] = value
  }
}

// 有些字段为空的时候后端没有返回，但是这些字段又是v-model的值，所以要加上
export function formatOtherInfoItems(data) {
  const list = data || []
  list.forEach(item => {
    setDefaultValue(item, 'content', '')
    setDefaultValue(item, 'style', {})
    setDefaultValue(item, 'options', [])
    setDefaultValue(item, 'operation', [])
  })
}

export function mergeTravellerInfo(newData, oldData) {
  // 把 newData 合并到 oldData 里面去
  if (!newData) {
    return oldData
  }

  if (!oldData) {
    return {
      ...newData,
      items: newData.items ? [...newData.items] : [],
    }
  }

  // 思路是把 oldItems 转成一个 map，然后遍历 newItems，修改 oldItems 的第一项(忽略后面相同field_key的)
  // 并且把 oldItems 里面没有的项收集起来，加到 oldItems 的前面
  const newItems = newData.items || []
  const oldItems = oldData.items || []
  const map = new Map()
  const addList = []

  oldItems.forEach((oldItem, index) => {
    const { field_key } = oldItem
    if (field_key && !map.has(field_key)) {
      map.set(field_key, index)
    }
  })

  newItems.forEach(newItem => {
    const { field_key } = newItem
    const idx = map.get(field_key)
    if (idx === undefined) {
      addList.push(newItem)
    } else {
      oldItems[idx] = newItem
    }
  })

  return {
    ...oldData,
    name: newData.name || oldData.name,
    items: [...addList, ...oldItems],
  }
}

export function cacheTravellerInfo(data) {
  const { traveller_id } = data

  // 如果是 0 的时候不缓存
  if (traveller_id === 0) {
    return
  }

  try {
    const info = mergeTravellerInfo(data, getTravellerCache(traveller_id))
    setTravellerCache(traveller_id, info)
    console.log(info)
  } catch (error) {
    console.log('cacheTravellerInfo 出错：', error)
  }
}

export function getTravellerInfoFromCache(data) {
  const { traveller_id } = data

  try {
    return mergeTravellerInfo(getTravellerCache(traveller_id), data)
  } catch (error) {
    console.log('getTravellerInfoFromCache 出错：', error)
    return data
  }
}

export function getKlkLanguage(vm) {
  // nuxt server
  if (vm && vm.$i18n && vm.$i18n.locale) {
    return vm.$i18n.locale
  }

  // new web + next client
  if (typeof window !== 'undefined') {
    if (window.KLK_LANG) {
      return window.KLK_LANG
    }

    if (
      window.__KLOOK__ &&
      window.__KLOOK__.state &&
      window.__KLOOK__.state.klook &&
      window.__KLOOK__.state.klook.language
    ) {
      return window.__KLOOK__.state.klook.language
    }
  }

  return 'en_US'
}

// 通讯录接口传参
export function getUpdateTravellerListParams(data) {
  const params = []
  traverseDynamicFormItem(data || [], (item) => params.push(item))
  return params.map(item => ({
    id: item.id,
    field_key: item.field_key,
    is_group: item.is_group,
    content: item.content,
    extra_info: item.extra_info,
    operation: (item.operation || []).map(item => ({
      id: item.id,
      field_key: item.field_key
    })),
  }))
}
