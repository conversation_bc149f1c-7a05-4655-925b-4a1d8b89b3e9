export default [
  {
    section_type: 4,
    title: '联系信息',
    tips: '',
    grey_tips: '如你的预订有任何变动，我们将及时通知你',
    form_infos: [
      {
        id: 0,
        name: '',
        info_items: [
          {
            id: 10,
            field_key: 'local_last_name',
            name: '姓',
            hint: '请填写',
            hover: '',
            id_type: 0,
            is_group: false,
            style: {
              type: 0,
              required: 1,
              match_rule: {
                type: 3,
                regex: '^(?!\\s)(.(?!\\s$)){1,128}$',
                min_date: '',
                max_date: '',
                default_date: '',
                date_option: 0,
                min_num: 0,
                max_num: 0,
                min_len: 0,
                max_len: 0,
              },
              option_all_type: 0,
              keyboard_type: 0,
            },
            default: 0,
            options: [],
            version: 0,
            content: '',
            operation: [],
            other_info_snapshot_no: 'local_last_name',
          },
          {
            id: 11,
            field_key: 'local_first_name',
            name: '名',
            hint: '请填写',
            hover: '',
            id_type: 0,
            is_group: false,
            style: {
              type: 0,
              required: 1,
              match_rule: {
                type: 3,
                regex: '^(?!\\s)(.(?!\\s$)){1,128}$',
                min_date: '',
                max_date: '',
                default_date: '',
                date_option: 0,
                min_num: 0,
                max_num: 0,
                min_len: 0,
                max_len: 0,
              },
              option_all_type: 0,
              keyboard_type: 0,
            },
            default: 0,
            options: [],
            version: 0,
            content: '',
            operation: [],
            other_info_snapshot_no: 'local_first_name',
          },
          {
            id: 4,
            field_key: 'nationality',
            name: '国家/地区',
            hint: '请选择',
            hover: '',
            id_type: 0,
            is_group: false,
            style: {
              type: 3,
              required: 1,
              match_rule: {
                type: 0,
                regex: '',
                min_date: '',
                max_date: '',
                default_date: '',
                date_option: 0,
                min_num: 0,
                max_num: 0,
                min_len: 0,
                max_len: 0,
              },
              option_all_type: 1,
              keyboard_type: 0,
            },
            default: 0,
            options: [],
            version: 0,
            content: '',
            operation: [
              {
                id: 0,
                field_key: 'CN',
                name: '',
                hover: '',
                id_type: 0,
                is_group: false,
                style: null,
                default: 0,
                options: [],
                version: 0,
                content: '',
                operation: [],
                other_info_snapshot_no: 'CN',
              },
            ],
            other_info_snapshot_no: 'nationality',
          },
          {
            id: 3,
            field_key: 'mobile',
            name: '手机号码',
            hint: '请填写',
            hover: '',
            id_type: 0,
            is_group: false,
            style: {
              type: 8,
              required: 1,
              verify_state: 1,
              match_rule: {
                type: 3,
                regex: '^[0-9]{6,17}$',
                min_date: '',
                max_date: '',
                default_date: '',
                date_option: 0,
                min_num: 0,
                max_num: 0,
                min_len: 0,
                max_len: 0,
              },
              option_all_type: 1,
              keyboard_type: 2,
            },
            default: 0,
            options: [],
            version: 0,
            content: '13610169004',
            operation: [
              {
                id: 0,
                field_key: 'CN',
                name: '',
                hover: '',
                id_type: 0,
                is_group: false,
                style: null,
                default: 0,
                options: [],
                version: 0,
                content: '',
                operation: [],
                other_info_snapshot_no: 'CN',
              },
            ],
            other_info_snapshot_no: 'mobile',
          },
          {
            id: 5,
            field_key: 'email',
            name: '电子邮箱（用于接收订单更新信息）',
            hint: '请填写',
            hover: '',
            id_type: 0,
            is_group: false,
            style: {
              type: 0,
              required: 1,
              match_rule: {
                type: 3,
                regex: '^\\s*\\w+(?:\\.{0,1}[\\w-]+)*@[a-zA-Z0-9]+(?:[-.][a-zA-Z0-9]+)*\\.[a-zA-Z]+\\s*$',
                min_date: '',
                max_date: '',
                default_date: '',
                date_option: 0,
                min_num: 0,
                max_num: 0,
                min_len: 0,
                max_len: 0,
              },
              option_all_type: 0,
              keyboard_type: 3,
            },
            default: 0,
            options: [],
            version: 0,
            content: '<EMAIL>',
            operation: [],
            other_info_snapshot_no: 'email',
          },
        ],
        custom_info: {
          traveler_is_show: true,
          traveler_id_use: 0,
          address_id_use: 0,
        },
        bottom_info: {
          is_checkbox_selected: true,
          save_content: '保存信息可于下次使用',
          update_content: '更新帐户中的出行人信息',
        },
        extra_info: {},
      },
    ],
    extra_info: null,
  },
]
