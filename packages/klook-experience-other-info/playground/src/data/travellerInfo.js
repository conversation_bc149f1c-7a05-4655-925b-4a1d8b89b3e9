export default [
  {
    section_type: 1,
    title: '出行人信息',
    tips: '',
    grey_tips: '',
    form_infos: [
      {
        id: 0,
        name: '',
        info_items: [
          {
            id: 10010002,
            field_key: 'local_last_name',
            name: '姓',
            hint: '请填写',
            hover: '',
            id_type: 0,
            is_group: false,
            style: {
              type: 0,
              required: 1,
              match_rule: {
                type: 3,
                regex: '^\\S.{0,126}$',
                min_date: '',
                max_date: '',
                default_date: '',
                date_option: 0,
                min_num: 0,
                max_num: 0,
                min_len: 1,
                max_len: 127,
              },
              option_all_type: 0,
              keyboard_type: 0,
            },
            default: 0,
            options: [],
            version: 0,
            content: '',
            operation: [],
            other_info_snapshot_no: '',
          },
          {
            id: 10010003,
            field_key: 'local_first_name',
            name: '名',
            hint: '请填写',
            hover: '',
            id_type: 0,
            is_group: false,
            style: {
              type: 0,
              required: 1,
              match_rule: {
                type: 3,
                regex: '^\\S.{0,126}$',
                min_date: '',
                max_date: '',
                default_date: '',
                date_option: 0,
                min_num: 0,
                max_num: 0,
                min_len: 1,
                max_len: 127,
              },
              option_all_type: 0,
              keyboard_type: 0,
            },
            default: 0,
            options: [],
            version: 0,
            content: '',
            operation: [],
            other_info_snapshot_no: '',
          },
          {
            id: 10010004,
            field_key: 'person_title',
            name: '称谓',
            hint: '请选择',
            hover: '',
            id_type: 0,
            is_group: false,
            style: {
              type: 3,
              required: 1,
              match_rule: {
                type: 0,
                regex: '',
                min_date: '',
                max_date: '',
                default_date: '',
                date_option: 0,
                min_num: 0,
                max_num: 0,
                min_len: 0,
                max_len: 0,
              },
              option_all_type: 0,
              keyboard_type: 0,
            },
            default: 0,
            options: [
              {
                id: 4019,
                field_key: 'person_title_mr',
                name: '先生',
                hover: '',
                id_type: 0,
                is_group: false,
                style: {
                  type: 0,
                  required: 0,
                  match_rule: {
                    type: 0,
                    regex: '',
                    min_date: '',
                    max_date: '',
                    default_date: '',
                    date_option: 0,
                    min_num: 0,
                    max_num: 0,
                    min_len: 0,
                    max_len: 0,
                  },
                  option_all_type: 0,
                  keyboard_type: 0,
                },
                default: 0,
                options: [],
                version: 0,
                content: '',
                operation: [],
                other_info_snapshot_no: '',
              },
              {
                id: 4020,
                field_key: 'person_title_mrs',
                name: '女士',
                hover: '',
                id_type: 0,
                is_group: false,
                style: {
                  type: 0,
                  required: 0,
                  match_rule: {
                    type: 0,
                    regex: '',
                    min_date: '',
                    max_date: '',
                    default_date: '',
                    date_option: 0,
                    min_num: 0,
                    max_num: 0,
                    min_len: 0,
                    max_len: 0,
                  },
                  option_all_type: 0,
                  keyboard_type: 0,
                },
                default: 0,
                options: [],
                version: 0,
                content: '',
                operation: [],
                other_info_snapshot_no: '',
              },
              {
                id: 4018,
                field_key: 'person_title_ms',
                name: '小姐',
                hover: '',
                id_type: 0,
                is_group: false,
                style: {
                  type: 0,
                  required: 0,
                  match_rule: {
                    type: 0,
                    regex: '',
                    min_date: '',
                    max_date: '',
                    default_date: '',
                    date_option: 0,
                    min_num: 0,
                    max_num: 0,
                    min_len: 0,
                    max_len: 0,
                  },
                  option_all_type: 0,
                  keyboard_type: 0,
                },
                default: 0,
                options: [],
                version: 0,
                content: '',
                operation: [],
                other_info_snapshot_no: '',
              },
            ],
            version: 0,
            content: '',
            operation: [],
            other_info_snapshot_no: '',
          },
          {
            id: 10010005,
            field_key: 'nationality',
            name: '国家/地区',
            hint: '请选择',
            hover: '',
            id_type: 0,
            is_group: false,
            style: {
              type: 3,
              required: 1,
              match_rule: {
                type: 0,
                regex: '',
                min_date: '',
                max_date: '',
                default_date: '',
                date_option: 0,
                min_num: 0,
                max_num: 0,
                min_len: 0,
                max_len: 0,
              },
              option_all_type: 1,
              keyboard_type: 0,
            },
            default: 0,
            options: [],
            version: 0,
            content: '',
            operation: [],
            other_info_snapshot_no: '',
          },
          {
            id: 10010006,
            field_key: 'birthday',
            name: '出生日期',
            hint: '请选择',
            hover: '',
            id_type: 0,
            is_group: false,
            style: {
              type: 1,
              required: 1,
              match_rule: {
                type: 1,
                regex: '',
                min_date: '1920-01-01',
                max_date: '2021-12-30',
                default_date: '2021-12-30',
                date_option: 1,
                min_num: 0,
                max_num: 0,
                min_len: 0,
                max_len: 0,
              },
              option_all_type: 0,
              keyboard_type: 0,
            },
            default: 0,
            options: [],
            version: 0,
            content: '',
            operation: [],
            other_info_snapshot_no: '',
          },
          {
            id: 10010007,
            field_key: 'age',
            name: '年龄',
            hint: '请填写',
            hover: '',
            id_type: 0,
            is_group: false,
            style: {
              type: 0,
              required: 1,
              match_rule: {
                type: 2,
                regex: '',
                min_date: '',
                max_date: '',
                default_date: '',
                date_option: 0,
                min_num: 0,
                max_num: 100,
                min_len: 0,
                max_len: 0,
              },
              option_all_type: 0,
              keyboard_type: 1,
            },
            default: 0,
            options: [],
            version: 0,
            content: '',
            operation: [],
            other_info_snapshot_no: '',
          },
          {
            id: 10010008,
            field_key: 'mobile',
            name: '手机号码',
            hint: '请填写',
            hover: '',
            id_type: 0,
            is_group: false,
            style: {
              type: 8,
              required: 1,
              match_rule: {
                type: 3,
                regex: '^[0-9]{6,17}$',
                min_date: '',
                max_date: '',
                default_date: '',
                date_option: 0,
                min_num: 0,
                max_num: 0,
                min_len: 6,
                max_len: 17,
              },
              option_all_type: 1,
              keyboard_type: 2,
            },
            default: 0,
            options: [],
            version: 0,
            content: '',
            operation: [],
            other_info_snapshot_no: '',
          },
          {
            id: 10010009,
            field_key: 'email',
            name: '邮箱',
            hint: '请填写',
            hover: '',
            id_type: 0,
            is_group: false,
            style: {
              type: 0,
              required: 1,
              match_rule: {
                type: 3,
                regex: '^\\s*\\w+(?:\\.{0,1}[\\w-]+)*@[a-zA-Z0-9]+(?:[-.][a-zA-Z0-9]+)*\\.[a-zA-Z]+\\s*$',
                min_date: '',
                max_date: '',
                default_date: '',
                date_option: 0,
                min_num: 0,
                max_num: 0,
                min_len: 3,
                max_len: 50,
              },
              option_all_type: 0,
              keyboard_type: 3,
            },
            default: 0,
            options: [],
            version: 0,
            content: '',
            operation: [],
            other_info_snapshot_no: '',
          },
          {
            id: 3310,
            field_key: 'identification_type',
            name: '证件类型',
            hint: '请选择',
            hover: '',
            id_type: 1,
            is_group: true,
            style: {
              type: 3,
              required: 1,
              match_rule: {
                type: 0,
                regex: '',
                min_date: '',
                max_date: '',
                default_date: '',
                date_option: 0,
                min_num: 0,
                max_num: 0,
                min_len: 0,
                max_len: 0,
              },
              option_all_type: 0,
              keyboard_type: 0,
            },
            default: 0,
            options: [
              {
                id: 3311,
                field_key: 'identification_info_for_passport',
                name: '护照',
                hover: '',
                id_type: 1,
                is_group: true,
                style: {
                  type: 6,
                  required: 1,
                  match_rule: {
                    type: 0,
                    regex: '',
                    min_date: '',
                    max_date: '',
                    default_date: '',
                    date_option: 0,
                    min_num: 0,
                    max_num: 0,
                    min_len: 0,
                    max_len: 0,
                  },
                  option_all_type: 0,
                  keyboard_type: 0,
                },
                default: 0,
                options: [
                  {
                    id: 10010011,
                    field_key: 'identification_info_id_for_passport',
                    name: '证件号码（护照）',
                    hint: '请填写',
                    hover: '',
                    id_type: 0,
                    is_group: false,
                    style: {
                      type: 0,
                      required: 1,
                      match_rule: {
                        type: 3,
                        regex: '^[a-zA-Z0-9]{1,32}$',
                        min_date: '',
                        max_date: '',
                        default_date: '',
                        date_option: 0,
                        min_num: 0,
                        max_num: 0,
                        min_len: 1,
                        max_len: 32,
                      },
                      option_all_type: 0,
                      keyboard_type: 0,
                    },
                    default: 0,
                    options: [],
                    version: 0,
                    content: '',
                    operation: [],
                    other_info_snapshot_no: '',
                  },
                ],
                version: 0,
                content: '',
                operation: [],
                other_info_snapshot_no: '',
              },
            ],
            version: 0,
            content: '',
            operation: [
              {
                id: 3311,
                field_key: 'identification_info_for_passport',
                name: '护照',
                hover: '',
                id_type: 1,
                is_group: true,
                style: {
                  type: 6,
                  required: 1,
                  match_rule: {
                    type: 0,
                    regex: '',
                    min_date: '',
                    max_date: '',
                    default_date: '',
                    date_option: 0,
                    min_num: 0,
                    max_num: 0,
                    min_len: 0,
                    max_len: 0,
                  },
                  option_all_type: 0,
                  keyboard_type: 0,
                },
                default: 0,
                options: [
                  {
                    id: 10010011,
                    field_key: 'identification_info_id_for_passport',
                    name: '证件号码（护照）',
                    hint: '请填写',
                    hover: '',
                    id_type: 0,
                    is_group: false,
                    style: {
                      type: 0,
                      required: 1,
                      match_rule: {
                        type: 3,
                        regex: '^[a-zA-Z0-9]{1,32}$',
                        min_date: '',
                        max_date: '',
                        default_date: '',
                        date_option: 0,
                        min_num: 0,
                        max_num: 0,
                        min_len: 1,
                        max_len: 32,
                      },
                      option_all_type: 0,
                      keyboard_type: 0,
                    },
                    default: 0,
                    options: [],
                    version: 0,
                    content: '',
                    operation: [],
                    other_info_snapshot_no: '',
                  },
                ],
                version: 0,
                content: '',
                operation: [],
                other_info_snapshot_no: '',
              },
            ],
            other_info_snapshot_no: '',
          },
          {
            id: 3325,
            field_key: 'contact_way_no',
            name: '通讯应用',
            hint: '请选择',
            hover: '',
            id_type: 1,
            is_group: true,
            style: {
              type: 3,
              required: 1,
              match_rule: {
                type: 0,
                regex: '',
                min_date: '',
                max_date: '',
                default_date: '',
                date_option: 0,
                min_num: 0,
                max_num: 0,
                min_len: 0,
                max_len: 0,
              },
              option_all_type: 0,
              keyboard_type: 0,
            },
            default: 0,
            options: [
              {
                id: 10010067,
                field_key: 'contact_way_no_whatsapp',
                name: 'WhatsApp',
                hint: '请填写',
                hover: '',
                id_type: 0,
                is_group: false,
                style: {
                  type: 0,
                  required: 1,
                  match_rule: {
                    type: 0,
                    regex: '',
                    min_date: '',
                    max_date: '',
                    default_date: '',
                    date_option: 0,
                    min_num: 0,
                    max_num: 0,
                    min_len: 0,
                    max_len: 50,
                  },
                  option_all_type: 0,
                  keyboard_type: 0,
                },
                default: 0,
                options: [],
                version: 0,
                content: '',
                operation: [],
                other_info_snapshot_no: '',
              },
              {
                id: 10010068,
                field_key: 'contact_way_no_line',
                name: 'LINE',
                hint: '请填写',
                hover: '',
                id_type: 0,
                is_group: false,
                style: {
                  type: 0,
                  required: 1,
                  match_rule: {
                    type: 0,
                    regex: '',
                    min_date: '',
                    max_date: '',
                    default_date: '',
                    date_option: 0,
                    min_num: 0,
                    max_num: 0,
                    min_len: 0,
                    max_len: 50,
                  },
                  option_all_type: 0,
                  keyboard_type: 0,
                },
                default: 0,
                options: [],
                version: 0,
                content: '',
                operation: [],
                other_info_snapshot_no: '',
              },
              {
                id: 10010069,
                field_key: 'contact_way_no_wechat',
                name: '微信',
                hint: '请填写',
                hover: '',
                id_type: 0,
                is_group: false,
                style: {
                  type: 0,
                  required: 1,
                  match_rule: {
                    type: 0,
                    regex: '',
                    min_date: '',
                    max_date: '',
                    default_date: '',
                    date_option: 0,
                    min_num: 0,
                    max_num: 0,
                    min_len: 0,
                    max_len: 50,
                  },
                  option_all_type: 0,
                  keyboard_type: 0,
                },
                default: 0,
                options: [],
                version: 0,
                content: '',
                operation: [],
                other_info_snapshot_no: '',
              },
              {
                id: 10010070,
                field_key: 'contact_way_no_kakatalk',
                name: 'Kakao Talk',
                hint: '请填写',
                hover: '',
                id_type: 0,
                is_group: false,
                style: {
                  type: 0,
                  required: 1,
                  match_rule: {
                    type: 0,
                    regex: '',
                    min_date: '',
                    max_date: '',
                    default_date: '',
                    date_option: 0,
                    min_num: 0,
                    max_num: 0,
                    min_len: 0,
                    max_len: 50,
                  },
                  option_all_type: 0,
                  keyboard_type: 0,
                },
                default: 0,
                options: [],
                version: 0,
                content: '',
                operation: [],
                other_info_snapshot_no: '',
              },
              {
                id: 10010071,
                field_key: 'contact_way_no_chaton',
                name: 'ChatON',
                hint: '请填写',
                hover: '',
                id_type: 0,
                is_group: false,
                style: {
                  type: 0,
                  required: 1,
                  match_rule: {
                    type: 0,
                    regex: '',
                    min_date: '',
                    max_date: '',
                    default_date: '',
                    date_option: 0,
                    min_num: 0,
                    max_num: 0,
                    min_len: 0,
                    max_len: 50,
                  },
                  option_all_type: 0,
                  keyboard_type: 0,
                },
                default: 0,
                options: [],
                version: 0,
                content: '',
                operation: [],
                other_info_snapshot_no: '',
              },
              {
                id: 10010072,
                field_key: 'contact_way_no_viber',
                name: 'Viber',
                hint: '请填写',
                hover: '',
                id_type: 0,
                is_group: false,
                style: {
                  type: 0,
                  required: 1,
                  match_rule: {
                    type: 0,
                    regex: '',
                    min_date: '',
                    max_date: '',
                    default_date: '',
                    date_option: 0,
                    min_num: 0,
                    max_num: 0,
                    min_len: 0,
                    max_len: 50,
                  },
                  option_all_type: 0,
                  keyboard_type: 0,
                },
                default: 0,
                options: [],
                version: 0,
                content: '',
                operation: [],
                other_info_snapshot_no: '',
              },
              {
                id: 10010073,
                field_key: 'contact_way_no_bbm',
                name: 'BBM',
                hint: '请填写',
                hover: '',
                id_type: 0,
                is_group: false,
                style: {
                  type: 0,
                  required: 1,
                  match_rule: {
                    type: 0,
                    regex: '',
                    min_date: '',
                    max_date: '',
                    default_date: '',
                    date_option: 0,
                    min_num: 0,
                    max_num: 0,
                    min_len: 0,
                    max_len: 50,
                  },
                  option_all_type: 0,
                  keyboard_type: 0,
                },
                default: 0,
                options: [],
                version: 0,
                content: '',
                operation: [],
                other_info_snapshot_no: '',
              },
              {
                id: 10010074,
                field_key: 'contact_way_no_telegram',
                name: 'Telegram',
                hint: '请填写',
                hover: '',
                id_type: 0,
                is_group: false,
                style: {
                  type: 0,
                  required: 1,
                  match_rule: {
                    type: 0,
                    regex: '',
                    min_date: '',
                    max_date: '',
                    default_date: '',
                    date_option: 0,
                    min_num: 0,
                    max_num: 0,
                    min_len: 0,
                    max_len: 50,
                  },
                  option_all_type: 0,
                  keyboard_type: 0,
                },
                default: 0,
                options: [],
                version: 0,
                content: '',
                operation: [],
                other_info_snapshot_no: '',
              },
              {
                id: 10010075,
                field_key: 'contact_way_no_skype',
                name: 'Skype',
                hint: '请填写',
                hover: '',
                id_type: 0,
                is_group: false,
                style: {
                  type: 0,
                  required: 1,
                  match_rule: {
                    type: 0,
                    regex: '',
                    min_date: '',
                    max_date: '',
                    default_date: '',
                    date_option: 0,
                    min_num: 0,
                    max_num: 0,
                    min_len: 0,
                    max_len: 50,
                  },
                  option_all_type: 0,
                  keyboard_type: 0,
                },
                default: 0,
                options: [],
                version: 0,
                content: '',
                operation: [],
                other_info_snapshot_no: '',
              },
              {
                id: 10010076,
                field_key: 'contact_way_no_kik',
                name: 'Kik',
                hint: '请填写',
                hover: '',
                id_type: 0,
                is_group: false,
                style: {
                  type: 0,
                  required: 1,
                  match_rule: {
                    type: 0,
                    regex: '',
                    min_date: '',
                    max_date: '',
                    default_date: '',
                    date_option: 0,
                    min_num: 0,
                    max_num: 0,
                    min_len: 0,
                    max_len: 50,
                  },
                  option_all_type: 0,
                  keyboard_type: 0,
                },
                default: 0,
                options: [],
                version: 0,
                content: '',
                operation: [],
                other_info_snapshot_no: '',
              },
              {
                id: 10010077,
                field_key: 'contact_way_no_groupme',
                name: 'GroupMe',
                hint: '请填写',
                hover: '',
                id_type: 0,
                is_group: false,
                style: {
                  type: 0,
                  required: 1,
                  match_rule: {
                    type: 0,
                    regex: '',
                    min_date: '',
                    max_date: '',
                    default_date: '',
                    date_option: 0,
                    min_num: 0,
                    max_num: 0,
                    min_len: 0,
                    max_len: 50,
                  },
                  option_all_type: 0,
                  keyboard_type: 0,
                },
                default: 0,
                options: [],
                version: 0,
                content: '',
                operation: [],
                other_info_snapshot_no: '',
              },
            ],
            version: 0,
            content: '',
            operation: [],
            other_info_snapshot_no: '',
          },
        ],
        custom_info: {
          traveler_is_show: true,
          traveler_id_use: 0,
          address_id_use: 0,
        },
        bottom_info: {
          is_checkbox_selected: true,
          save_content: '保存信息可于下次使用',
          update_content: '更新帐户中的出行人信息',
        },
        extra_info: {},
      },
    ],
    extra_info: null,
  },
  {
    section_type: 2,
    title: '邮寄信息',
    tips: '',
    grey_tips: '',
    form_infos: [
      {
        id: 0,
        name: '',
        info_items: [
          {
            id: 10020100,
            field_key: 'shipment_nationality',
            name: '国家/地区',
            hint: '请选择',
            hover: '',
            id_type: 0,
            is_group: false,
            style: {
              type: 3,
              required: 1,
              match_rule: {
                type: 0,
                regex: '',
                min_date: '',
                max_date: '',
                default_date: '',
                date_option: 0,
                min_num: 0,
                max_num: 0,
                min_len: 0,
                max_len: 0,
              },
              option_all_type: 1,
              keyboard_type: 0,
            },
            default: 0,
            options: [],
            version: 0,
            content: '',
            operation: [],
            other_info_snapshot_no: '',
          },
          {
            id: 10020101,
            field_key: 'shipment_city',
            name: '城市',
            hint: '请填写',
            hover: '',
            id_type: 0,
            is_group: false,
            style: {
              type: 0,
              required: 1,
              match_rule: {
                type: 3,
                regex: '',
                min_date: '',
                max_date: '',
                default_date: '',
                date_option: 0,
                min_num: 0,
                max_num: 0,
                min_len: 0,
                max_len: 100,
              },
              option_all_type: 0,
              keyboard_type: 0,
            },
            default: 0,
            options: [],
            version: 0,
            content: '',
            operation: [],
            other_info_snapshot_no: '',
          },
          {
            id: 10020102,
            field_key: 'shipment_address_district',
            name: '地址',
            hint: '街道地址',
            hover: '',
            id_type: 0,
            is_group: false,
            style: {
              type: 0,
              required: 1,
              match_rule: {
                type: 3,
                regex: '',
                min_date: '',
                max_date: '',
                default_date: '',
                date_option: 0,
                min_num: 0,
                max_num: 0,
                min_len: 0,
                max_len: 250,
              },
              option_all_type: 0,
              keyboard_type: 0,
            },
            default: 0,
            options: [],
            version: 0,
            content: '',
            operation: [],
            other_info_snapshot_no: '',
          },
          {
            id: 10020103,
            field_key: 'shipment_address_detail',
            name: '',
            hint: '大厦、楼层、房号等（选填）',
            hover: '',
            id_type: 0,
            is_group: false,
            style: {
              type: 0,
              required: 0,
              match_rule: {
                type: 3,
                regex: '',
                min_date: '',
                max_date: '',
                default_date: '',
                date_option: 0,
                min_num: 0,
                max_num: 0,
                min_len: 0,
                max_len: 250,
              },
              option_all_type: 0,
              keyboard_type: 0,
            },
            default: 0,
            options: [],
            version: 0,
            content: '',
            operation: [],
            other_info_snapshot_no: '',
          },
          {
            id: 10020104,
            field_key: 'shipment_postal_code',
            name: '邮编（港澳地区请填0）',
            hint: '请填写',
            hover: '',
            id_type: 0,
            is_group: false,
            style: {
              type: 0,
              required: 1,
              match_rule: {
                type: 3,
                regex: '',
                min_date: '',
                max_date: '',
                default_date: '',
                date_option: 0,
                min_num: 0,
                max_num: 0,
                min_len: 0,
                max_len: 15,
              },
              option_all_type: 0,
              keyboard_type: 0,
            },
            default: 0,
            options: [],
            version: 0,
            content: '',
            operation: [],
            other_info_snapshot_no: '',
          },
          {
            id: 10020105,
            field_key: 'shipment_consignee',
            name: '全名',
            hint: '请填写',
            hover: '',
            id_type: 0,
            is_group: false,
            style: {
              type: 0,
              required: 1,
              match_rule: {
                type: 3,
                regex: '',
                min_date: '',
                max_date: '',
                default_date: '',
                date_option: 0,
                min_num: 0,
                max_num: 0,
                min_len: 0,
                max_len: 100,
              },
              option_all_type: 0,
              keyboard_type: 0,
            },
            default: 0,
            options: [],
            version: 0,
            content: '',
            operation: [],
            other_info_snapshot_no: '',
          },
          {
            id: 10020106,
            field_key: 'shipment_mobile',
            name: '手机号码',
            hint: '请填写',
            hover: '',
            id_type: 0,
            is_group: false,
            style: {
              type: 8,
              required: 1,
              match_rule: {
                type: 3,
                regex: '^[0-9]{6,17}$',
                min_date: '',
                max_date: '',
                default_date: '',
                date_option: 0,
                min_num: 0,
                max_num: 0,
                min_len: 6,
                max_len: 17,
              },
              option_all_type: 1,
              keyboard_type: 2,
            },
            default: 0,
            options: [],
            version: 0,
            content: '',
            operation: [],
            other_info_snapshot_no: '',
          },
        ],
        custom_info: {
          traveler_id_use: 0,
          address_id_use: 0,
        },
        bottom_info: {
          is_checkbox_selected: true,
          save_content: '保存信息可于下次使用',
          update_content: '更新帐户中的邮寄信息',
        },
        extra_info: null,
      },
    ],
    extra_info: null,
  },
  {
    section_type: 0,
    title: '其他信息',
    tips: '',
    grey_tips: '',
    form_infos: [
      {
        id: 0,
        name: '',
        info_items: [
          {
            id: 10010010,
            field_key: 'lang_preference',
            name: '偏好语言',
            hint: '请选择',
            hover: '偏好语言的填写指引-zh',
            id_type: 0,
            is_group: false,
            style: {
              type: 3,
              required: 1,
              match_rule: {
                type: 0,
                regex: '',
                min_date: '',
                max_date: '',
                default_date: '',
                date_option: 0,
                min_num: 0,
                max_num: 0,
                min_len: 0,
                max_len: 0,
              },
              option_all_type: 2,
              keyboard_type: 0,
            },
            default: 0,
            options: [],
            version: 0,
            content: '',
            operation: [],
            other_info_snapshot_no: '',
          },
          {
            id: 10010081,
            field_key: 'participant_type_people',
            name: '参与人数',
            hint: '请输入1 - 100的数字',
            hover: 'zh-参与人数的填写指引',
            id_type: 0,
            is_group: false,
            style: {
              type: 0,
              required: 1,
              match_rule: {
                type: 2,
                regex: '',
                min_date: '',
                max_date: '',
                default_date: '',
                date_option: 0,
                min_num: 1,
                max_num: 100,
                min_len: 0,
                max_len: 0,
              },
              option_all_type: 0,
              keyboard_type: 1,
            },
            default: 0,
            options: [],
            version: 0,
            content: '',
            operation: [],
            other_info_snapshot_no: '',
          },
        ],
        custom_info: null,
        bottom_info: null,
        extra_info: null,
      },
    ],
    extra_info: null,
  },
]
