import { resolve } from 'path';
import commonjsPlugin from 'rollup-plugin-commonjs';
import resolvePlugin from 'rollup-plugin-node-resolve';
import vuePlugin from 'rollup-plugin-vue';
import aliasPlugin from '@rollup/plugin-alias';
import svgPlugin from 'rollup-plugin-vue-inline-svg';
import babel from 'rollup-plugin-babel';
import postcss from 'rollup-plugin-postcss';
import autoprefixer from 'autoprefixer';
import MagicString from 'magic-string';

const {
  name,
  version,
  author
} = require('./package.json');
const banner =
  `/**
  * v${version}
  * (c) ${new Date().getFullYear()} ${author}
  */`;

export default [
  {
    input: 'src/index.js',
    output: generateOutput('lib/index.js', 'cjs'),
    external: generateExternal(),
    plugins: generatePlugins(),
  },
  {
    input: 'src/index.js',
    output: generateOutput('lib/index.esm.js', 'esm'),
    external: generateExternal(),
    plugins: generatePlugins(),
  },
  {
    input: 'src/index.js',
    output: generateOutput('lib/index.ssr.esm.js', 'esm'),
    external: generateExternal(),
    plugins: generatePlugins(true),
  },
  {
    input: 'src/web/index.js',
    output: generateOutput('lib/web/index.js', 'cjs'),
    external: generateExternal(),
    plugins: generatePlugins(),
  },
  {
    input: 'src/web/index.js',
    output: generateOutput('lib/web/index.esm.js', 'esm'),
    external: generateExternal(),
    plugins: generatePlugins(),
  },
  {
    input: 'src/web/index.js',
    output: generateOutput('lib/web/index.ssr.esm.js', 'esm'),
    external: generateExternal(),
    plugins: generatePlugins(true),
  },
  {
    input: 'src/mobile/index.js',
    output: generateOutput('lib/mobile/index.js', 'cjs'),
    external: generateExternal(),
    plugins: generatePlugins(),
  },
  {
    input: 'src/mobile/index.js',
    output: generateOutput('lib/mobile/index.esm.js', 'esm'),
    external: generateExternal(),
    plugins: generatePlugins(),
  },
  {
    input: 'src/mobile/index.js',
    output: generateOutput('lib/mobile/index.ssr.esm.js', 'esm'),
    external: generateExternal(),
    plugins: generatePlugins(true),
  },
];

function generateOutput(path, type) {
  return {
    banner,
    name,
    file: path,
    format: type,
    exports: 'named',
    ...(type === 'cjs' ? { inlineDynamicImports: true } : {}),
  };
}

function generatePlugins(isSSr) {
  return [
    aliasPlugin({
      entries: [
        {
          find: '@src',
          replacement: resolve(__dirname, 'src'),
        },
      ],
    }),
    // imagePlugin(),
    svgPlugin(),
    resolvePlugin({
      mainFields: ['module', 'jsnext', 'main'],
      browser: true,
      extensions: ['.mjs', '.js', '.json', '.node', '.ts'],
    }),
    commonjsPlugin({
      include: /node_modules/,
      extensions: ['.js', '.ts', '.vue', '.json'],
    }),
    vuePlugin({
      css: !isSSr,
      template: {
        isProduction: true,
        // optimizeSSR: isSSr, // 貌似会在浏览器端报找不到 _ssrNode 的错误
      },
      style: {
        postcssPlugins: [
          autoprefixer(),
          require('cssnano')({
            safe: true,
          }),
        ],
        preprocessOptions: {
          scss: {
            data: '@import "../klook-ui/src/styles/token/index.scss";',
          },
        },
      },
    }),
    isSSr &&
      shimDepsPlugin({
        '.vue': {
          pattern: /import '@klook\/klook-ui\/lib\/styles\/components\/(alert|select|icon|poptip|form|input|date-picker|checkbox|button|tag|tag-select|bottom-sheet|datetime-picker|picker|modal|drawer)\.scss'/g,
          replacement: '',
        },
        'dynamic-form': {
          src: `import '@klook/klook-ui/lib/styles/transitions.scss'`,
          replacement: '',
        },
      }),
    babel({
      include: ['src/**', 'node_modules/**'],
      extensions: ['.js', '.vue'],
    }),
    postcss({
      extract: true,
      extensions: ['.css', '.less', '.scss'],
      plugins: [autoprefixer()],
    }),
  ];
}

function generateExternal() {
  return [
    'vue',
    '@klook/klook-ui/lib/alert',
    '@klook/klook-ui/lib/locale',
    '@klook/klook-ui/lib/select',
    '@klook/klook-ui/lib/poptip',
    '@klook/klook-ui/lib/icon',
    '@klook/klook-ui/lib/form',
    '@klook/klook-ui/lib/select',
    '@klook/klook-ui/lib/input',
    '@klook/klook-ui/lib/date-picker',
    '@klook/klook-ui/lib/checkbox',
    '@klook/klook-ui/lib/button',
    '@klook/klook-ui/lib/tag',
    '@klook/klook-ui/lib/tag-select',
    '@klook/klook-ui/lib/bottom-sheet',
    '@klook/klook-ui/lib/datetime-picker',
    '@klook/klook-ui/lib/modal',
    '@klook/klook-ui/lib/drawer',
    '@klook/klook-ui/lib/button',
    '@klook/klook-ui/lib/styles/components/alert.scss',
    '@klook/klook-ui/lib/styles/transitions.scss',
    '@klook/klook-ui/lib/styles/components/select.scss',
    '@klook/klook-ui/lib/styles/components/icon.scss',
    '@klook/klook-ui/lib/styles/components/poptip.scss',
    '@klook/klook-ui/lib/styles/components/form.scss',
    '@klook/klook-ui/lib/styles/components/input.scss',
    '@klook/klook-ui/lib/styles/components/date-picker.scss',
    '@klook/klook-ui/lib/styles/components/checkbox.scss',
    '@klook/klook-ui/lib/styles/components/button.scss',
    '@klook/klook-ui/lib/styles/components/tag.scss',
    '@klook/klook-ui/lib/styles/components/tag-select.scss',
    '@klook/klook-ui/lib/styles/components/bottom-sheet.scss',
    '@klook/klook-ui/lib/styles/components/datetime-picker.scss',
    '@klook/klook-ui/lib/styles/components/picker.scss',
    '@klook/klook-ui/lib/styles/components/modal.scss',
    '@klook/klook-ui/lib/styles/components/drawer.scss',
    '@klook/klook-ui/lib/styles/components/button.scss',
  ]
}

function shimDepsPlugin(deps) {
  const transformed = {};

  return {
    name: 'shim-deps',
    transform(code, id) {
      const magicString = new MagicString(code);

      for (const file in deps) {
        if (id.replace(/\\/g, '/').includes(file)) {
          const { src, replacement, pattern } = deps[file];

          if (src) {
            const pos = code.indexOf(src);
            if (pos >= 0) {
              transformed[file] = true;
              magicString.overwrite(pos, pos + src.length, replacement);
              console.log(`shimmed(${file}): ${id}`);
            }
          }

          if (pattern) {
            let match;
            while ((match = pattern.exec(code))) {
              transformed[file] = true;
              const start = match.index;
              const end = start + match[0].length;
              magicString.overwrite(start, end, replacement);
            }
            console.log(`shimmed(${file}): ${id}`);
          }
        }
      }

      return {
        code: magicString.toString(),
        map: magicString.generateMap({ hires: true }),
      };
    },
    buildEnd(err) {
      if (!err) {
        for (const file in deps) {
          if (!transformed[file]) {
            this.error(`Did not find "${file}" which is supposed to be shimmed, was the file renamed?`);
          }
        }
      }
    },
  };
}
