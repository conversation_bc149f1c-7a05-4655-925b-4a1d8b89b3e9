{"name": "@klook/klook-experience-other-info", "version": "0.1.19", "description": "A Common Component based on Vue of Klook", "author": "yang zhou", "homepage": "https://design.klook.io", "main": "lib/index.js", "module": "lib/index.esm.js", "keywords": ["vue", "component", "ui", "framework"], "files": ["lib"], "license": "UNLICENSED", "publishConfig": {"registry": "https://knpm.klook.io", "access": "public"}, "scripts": {"clean": "rimraf lib && rimraf packages/*/lib && rimraf test/**/coverage", "dev": "NODE_ENV=production rollup --config ./rollup.config.js --watch src", "build": "npm run clean && NODE_ENV=production rollup --config ./rollup.config.js", "lint": "NODE_ENV=production eslint --ext .js,.vue src", "stylelint": "stylelint './src/**/*.{html,vue,css,sass,scss}' --fix", "test": "NODE_ENV=test jest -i --updateSnapshot", "test:coverage": "NODE_ENV=test jest -i --coverage --updateSnapshot", "prepush": "yarn run lint", "prepublishOnly": "bash prepublishOnly.sh", "commit": "npx git-cz", "commitmsg": "commitlint -E GIT_PARAMS"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.{ts,js,vue}": ["npm run lint", "git add ."], "*.{html,vue,css,sass,scss}": ["npm run stylelint", "git add ."]}, "devDependencies": {"@babel/core": "^7.4.0", "@babel/plugin-transform-runtime": "^7.4.0", "@babel/preset-env": "^7.4.2", "@klook/stylelint-config": "0.0.13", "@rollup/plugin-alias": "^3.1.5", "@rollup/plugin-image": "^2.1.0", "@types/jest": "^26.0.0", "@types/webpack-env": "^1.14.0", "@vue/test-utils": "^1.0.0-beta.32", "autoprefixer": "^9.8.6", "cssnano": "^4.1.10", "husky": "^7.0.4", "jest": "^25.5.4", "lint-staged": "^12.0.2", "magic-string": "^0.25.7", "node-sass": "^6.0.1", "postcss": "^8.3.6", "rimraf": "^3.0.2", "rollup": "^1.19.4", "rollup-plugin-babel": "^4.4.0", "rollup-plugin-buble": "^0.19.8", "rollup-plugin-commonjs": "^10.0.2", "rollup-plugin-node-resolve": "^5.2.0", "rollup-plugin-postcss": "^4.0.1", "rollup-plugin-replace": "^2.2.0", "rollup-plugin-terser": "^5.1.1", "rollup-plugin-typescript2": "^0.22.1", "rollup-plugin-vue": "^5.1.4", "rollup-plugin-vue-inline-svg": "^1.1.2", "stylelint": "^13.13.1", "stylelint-config-standard": "^22.0.0", "stylelint-scss": "^3.21.0", "terser": "^4.1.3", "ts-jest": "^26.1.0", "ts-node": "^8.10.2", "typescript": "^3.1.1", "vue-jest": "^3.0.4", "vue-property-decorator": "^8.2.2", "vue-template-compiler": "2.6.11"}, "dependencies": {"dayjs": "^1.10.6", "lodash.clonedeep": "^4.5.0", "vue-click-outside": "^1.1.0"}, "peerDependencies": {"@klook/klook-ui": "^1.26.3", "vue": "2.6.11"}, "pnpm": {"overrides": {"vue": "2.6.11", "vue-template-compiler": "2.6.11"}}}