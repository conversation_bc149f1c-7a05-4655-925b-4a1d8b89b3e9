# klook-experience-other-info

## 项目说明

1.本项目 web 和 mweb 的组件是分开打包的，建议按需引入。

```js
// web 端按需引入
import { OtherInfoFormMulti } from '@klook/klook-experience-other-info/lib/web'
// mweb 端按需引入
import { OtherInfoFormMulti } from '@klook/klook-experience-other-info/lib/mobile'

// 不按需引入
import { web: { OtherInfoFormMulti } } from '@klook/klook-experience-other-info'
import { mweb: { OtherInfoFormMulti } } from '@klook/klook-experience-other-info'
```

2.本项目只对外支持引入form组件，不支持引入其它组件。但支持引入各种粒度的form组件，按需引入即可：

```
OtherInfoForm: 单个form，自带出行人选择器和邮寄信息选择器
OtherInfoFormMulti: 多个form
OtherInfoModule: 单个module，带标题的多个module
OtherInfoModuleMulti: 多个module
```

3.在 nuxt 项目上面应该引入 esm 文件：

```
// web 端按需引入
import { OtherInfoFormMulti } from '@klook/klook-experience-other-info/lib/web/index.esm.js'
// mweb 端按需引入
import { OtherInfoFormMulti } from '@klook/klook-experience-other-info/lib/mobile/index.esm.js'

// 不按需引入
import { web: { OtherInfoFormMulti } } from '@klook/klook-experience-other-info/index.esm.js'
import { mweb: { OtherInfoFormMulti } } from '@klook/klook-experience-other-info/index.esm.js'
```

4.web端修改宽度：

```
// 使用 wrapperWidth 和 componentWidth
<OtherInfoForm
  wrapperWidth="720px"
  componentWidth="350px"
/>
```

## 如何接入

1.首先判断是否有用到国家码，如果有用到的话需要先在外部 vue 文件的里面传入国家码。（注意，在 nuxt web上面由于 ssr 的原因，需要**在 mounted 而不是 created 钩子**里面初始化国家码）

```js
// new web
import { codeManager } from '@klook/klook-experience-other-info/lib/web'
async created() {
  const allCountryCodes = await klook.getAllCountryCode()
  codeManager.initCountryCode(allCountryCodes)
}

// nuxt web
import { codeManager } from '@klook/klook-experience-other-info/lib/web'
async mounted() {
  const allCountryCode = await this.$getAllCountryCode()
  codeManager.initCountryCode(allCountryCodes)
}
```

2.引入适当的组件 OtherInfoForm、OtherInfoFormMulti、OtherInfoModule、OtherInfoModuleMulti 传入数据渲染即可。

## 开发

1.首先把本项目链接到全局去。在本项目的根目录下面运行：

```bash
npm link
```

2.以开发方式运行此项目，此时会自动开启 rollup 打包并 watch src 文件夹的改动。在本项目的根目录下面运行：

```bash
npm run dev
```

3.在要开发的项目里面链接这个包。在 new web 或者 nuxt web 的根目录下运行：

```bash
npm link @klook/klook-experience-other-info
```

4.在项目里面可以引入这个包了。

## 其它

1.查看这个包是否被链接到全局去了：

```bash
npm ls -g --depth=0
```

2.在开发的项目里面删除这个包的链接。在 new web 或者 nuxt web 的根目录下运行：

```bash
npm unlink @klook/klook-experience-other-info
```

3.删除这个包在全局的链接。在本项目的根目录下面运行：

```bash
npm unlink
```
