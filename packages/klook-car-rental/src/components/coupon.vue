<!--suppress ALL -->
<template>
  <div v-if="couponInfo && couponInfo.coupon_list && couponInfo.coupon_list.length > 0" class="coupon-card coupon-card-desktop">
    <h2>{{ couponInfo.coupon_title }}</h2>
    <klk-card-swiper :item-width="373" :item-gap="20">
      <klk-card-swiper-item v-for="(item, index) in couponInfo.coupon_list" :key="index">
        <div class="coupon-card-item">
          <div class="coupon-card-item__left">
            <div class="coupon-card-item__title">{{ item.title }}</div>
            <div class="coupon-card-item__desc">{{ item.desc }}</div>
          </div>
          <div class="coupon-card-item__right">
            <template v-if="item.type === 1 && item.code">
              <div class="coupon-card-item__promo">{{ $t('54088-promo_code') }}</div>
              <div class="coupon-card-item__code">{{ item.code }}</div>
            </template>
            <div
              :class="{'redeemed': item.coupon_status === 'usable' || item.coupon_status === 'all_out', 'no-start': item.coupon_status === 'not_start'}"
              class="coupon-card-item__button"
              :data-spm-module="`CouponsCenter_LIST?oid=coupon_${item.batch_id}&ext=${JSON.stringify({ Status: item.coupon_status})}`"
              v-galileo-click-tracker="galileoClick1"
              data-spm-virtual-item="__virtual?typ=entry"
              @click="reedomCoupon(item)"
            >
              <!-- <SvgIcon
                v-if="item.coupon_status === 'usable'"
                name="desktop-car-rental#icon_feedback_success_fill_xs"
                width="16"
                height="16"
              ></SvgIcon> -->
              <img v-if="item.coupon_status === 'usable'" src="https://res.klook.com/image/upload/web3.0/icon_feedback_success_fill_xs_nrg8ou.svg"  width="16" height="16">
              {{ item.button_text }}
            </div>
          </div>
        </div>
      </klk-card-swiper-item>
    </klk-card-swiper>
  </div>
</template>

<script lang="ts">
import { Component } from 'vue-property-decorator'
import Base from "../common/base";
import { getStandardDateFormat } from '../common/datetime'
import { getSourceCountryCode } from '../common/utils'
import { WebClick_71 } from '../../share/galileo/auto-click'

@Component
export default class couponCardList extends Base {

  couponInfo: any = null
  countryCode: any = null

  coupon: any = {
    reedom: '/v1/transfercarrentalapisrv/coupon/redeem',
    query: '/v1/transfercarrentalapisrv/coupon/query_list'
  }

  mounted() {
    getSourceCountryCode(this, this.handleCouponList)
  }

  async login() {
    const { loginWithSDK } = await import('@klook/klook-traveller-login')

    if (!await loginWithSDK({
      isMP: false,
      isKlookApp: false,
      platform: this.$store.getters.platform,
      language: this.$store.getters.language,
      currency: this.$store.getters.currencySymbol,
      bizName: 'CarRental',
      purpose: 'CarRentalHomePage',
      success: () => {
        window.location.reload()
      }
    })) {
      // this.$logger.error('租车调用登录SDK返回不支持')
    }
  }

  handleCouponList(countryCode: any) {
    this.countryCode = countryCode
    const params = {
      resource_path: 'web-home',
      source_country_code: countryCode,
      zone: -new Date().getTimezoneOffset() / 60
    }
    return this._axios
      .$get(this.coupon.query, { params })
      .then((res: any) => {
        if (res && res.result) {
          this.couponInfo = res.result
        }
      })
      .catch((error: any) => {
        this.$toast(error.message)
      })
  }

  reedomCoupon(item: any) {
    if (item.coupon_status === 'pending') {
      const data = {
        type: item.type, // 1=promocode券,2=program券
        batch_id: item.batch_id || null,
        program_id: item.program_id || null,
        resource_path: 'web-home',
        source_country_code: this.countryCode
      }
    const headers = { 'Content-Type': 'application/json; charset=UTF-8' }
    return this._axios
      .$post(this.coupon.reedom, data, { headers, timeout: 15000, throwError: true })
      .then((res: any) => {
        if (res && res.result) {
          this.$toast({
            message: this.$t('54021-success'),
            duration: 2000
          })
          getSourceCountryCode(this, this.handleCouponList)
        }
      })
      .catch((error: any) => {
          if (error.code === '4001') {
            this.login()
          } else {
            this.$toast(error.message)
            setTimeout(() => {
              getSourceCountryCode(this, this.handleCouponList)
            }, 1000)
          }
        })
    } else if (item.coupon_status === 'not_start') {

      const formatted = getStandardDateFormat(new Date(item.start_time_utc), this.$t.bind(this), this.curLocale, 2)
      this.$toast({
        message: this.$t('54028-not-start', { date: formatted }),
        duration: 2000
      })
    }
  }

    get galileoClick1() {
        return { spm: WebClick_71, autoTrackSpm: true }
    }
}
</script>

<style lang="scss" scoped>
.coupon-card-item {
  width: 373px;
  padding: 6px;
  box-shadow: 0px 1px 6px rgba(0, 0, 0, 0.08);
  border-radius: $radius-l;
  display: flex;
  flex-direction: row;

  &__left {
    width: 245px;
    height: 82px;
    background: linear-gradient(278.25deg, #FF9C00 -0.13%, #FFBE56 98.74%);
    position: relative;
    padding: 12px;
    border-radius: $radius-m;

    &.redeemed {
      background: linear-gradient(97.77deg, #FFAF65 0%, #FF7043 100%);
    }

    &.sold-out {
      background: linear-gradient(97.77deg, #FF8A65 0%, #E64340 100%);
    }

    &::before {
      content: "";
      background: radial-gradient(#fff 0,#fff 4px,transparent 5px);
      background-position: right 8px top 0;
      background-size: 15px 15px;
      bottom: 4px;
      display: block;
      position: absolute;
      right: 0;
      top: 4px;
      width: 10px;
    }
  }

  &__title {
    @include font-body-l-bold;
    color: $color-text-primary-onDark;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  &__desc {
    @include font-caption-m-semibold;
    color: $color-text-primary-onDark;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    margin-top: 2px;
  }

  &__right {
    display: flex;
    width: 128px;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    // padding-top: 7px;
  }

  &__promo {
    @include font-caption-m-regular;
    color: $color-text-secondary;
  }

  &__code {
    @include font-caption-m-semibold;
    color: $color-text-primary;
  }

  &__button {
    @include font-caption-m-semibold;
    background-color: #FF5722;
    border-radius: $radius-s;
    color: $color-text-primary-onDark;
    width: 78px;
    height: 28px;
    line-height: 28px;
    text-align: center;
    margin-top: 8px;

    &.no-start {
      opacity: 0.7;
    }

    &.redeemed {
      @include font-caption-m-semibold;
      color: $color-text-disabled;
      background-color: unset;
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: center;

      svg {
        margin-right: 4px;
      }
    }
  }
}
</style>
<style lang="scss">
.coupon-card-desktop {
  width: 1160px;
  margin: 0 auto;

  h2 {
    @include font-heading-m;
    padding: 0 0 32px;
  }

  .klk-card-swiper-items-wrapper {
    max-width: none;
    padding: 15px;
    margin: -15px;
  }
}
</style>
