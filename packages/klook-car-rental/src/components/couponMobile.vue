<template>
  <div v-if="couponInfo && couponInfo.coupon_list && couponInfo.coupon_list.length > 0" class="coupon-card">
    <h2>{{ couponInfo.coupon_title }}</h2>
    <klk-card-swiper class="coupon-swiper" item-width="43.2vw" :item-gap="10" :scroll-mode="true">
      <klk-card-swiper-item v-for="(item, index) in couponInfo.coupon_list" :key="index">
        <div
          class="coupon-card-item"
          :class="{'disabled': item.coupon_status === 'usable' || item.coupon_status === 'all_out', 'no-start': item.coupon_status === 'not_start'}"
        >
          <div class="coupon-card-item__title">{{ item.title }}</div>
          <div
            :class="{'disabled': item.coupon_status === 'usable' || item.coupon_status === 'all_out'}"
            :data-spm-module="`CouponsCenter_LIST?oid=coupon_${item.batch_id}&ext=${JSON.stringify({ Status: item.coupon_status})}`"
            v-galileo-click-tracker="galileoClick1"
            data-spm-virtual-item="__virtual?typ=entry"
            class="coupon-card-item__button"
            @click="reedomCoupon(item)"
          >
            {{ item.button_text }}
          </div>
          <!-- <SvgIcon
            name="mobile-car-rental#coupon-bg"
            width="77"
            height="92"
          ></SvgIcon> -->
          <img src="https://res.klook.com/image/upload/web3.0/coupon-bg_odzrfc.svg" width="77" height="92">
        </div>
      </klk-card-swiper-item>
    </klk-card-swiper>
  </div>
</template>

<script lang="ts">
import { Component } from 'vue-property-decorator'
import Base from "../common/base";
import { getStandardDateFormat } from '../common/datetime'
import { getSourceCountryCode } from '../common/utils'
import { WebClick_61 } from '../../share/galileo/auto-click'

@Component
export default class couponCardList extends Base {

  couponInfo: any = null
  countryCode: any = null

  coupon: any = {
    reedom: '/v1/transfercarrentalapisrv/coupon/redeem',
    query: '/v1/transfercarrentalapisrv/coupon/query_list'
  }

  mounted() {
    getSourceCountryCode(this, this.handleCouponList)
  }

  handleCouponList(countryCode: any) {
    this.countryCode = countryCode
    const params = {
      resource_path: 'mweb-home',
      source_country_code: countryCode,
      zone: -new Date().getTimezoneOffset() / 60
    }
    return this._axios
      .$get(this.coupon.query, { params })
      .then((res: any) => {
        if (res && res.result) {
          this.couponInfo = res.result
        }
      })
      .catch((error: any) => {
        this.$toast(error.message)
      })
  }

  async login() {
    const { loginWithSDK } = await import('@klook/klook-traveller-login')

    if (!await loginWithSDK({
      isMP: false,
      isKlookApp: false,
      platform: this.$store.getters.platform,
      language: this.$store.getters.language,
      currency: this.$store.getters.currencySymbol,
      bizName: 'CarRental',
      purpose: 'CarRentalHomePage',
      success: () => {
        window.location.reload()
      }
    })) {
      // this.$logger.error('租车调用登录SDK返回不支持')
    }
  }

  reedomCoupon(item: any) {
    if (item.coupon_status === 'pending') {
      const data = {
        type: item.type, // 1=promocode券,2=program券
        batch_id: item.batch_id || null,
        program_id: item.program_id || null,
        resource_path: 'mweb-home',
        source_country_code: this.countryCode
      }
    const headers = { 'Content-Type': 'application/json; charset=UTF-8' }
    return this._axios
      .$post(this.coupon.reedom, data, { headers, timeout: 15000, throwError: true })
      .then((res: any) => {
        if (res && res.result) {
          this.$toast({
            message: this.$t('54021-success'),
            duration: 2000
          })
          getSourceCountryCode(this, this.handleCouponList)
        }
      })
      .catch((error: any) => {
          if (error.code === '4001') {
            this.login()
          } else {
            this.$toast(error.message)
            setTimeout(() => {
              getSourceCountryCode(this, this.handleCouponList)
            }, 1000)
          }
        })
    } else if (item.coupon_status === 'not_start') {
      const formatted = getStandardDateFormat(new Date(item.start_time_utc), this.$t.bind(this), this.curLocale, 2)
      this.$toast({
        message: this.$t('54028-not-start', { date: formatted }),
        duration: 2000
      })
    }
  }

    get galileoClick1() {
        return { spm: WebClick_61, autoTrackSpm: true }
    }
}
</script>

<style lang="scss" scoped>
.coupon-card {

  h2 {
    @include font-body-l-bold;
    color: $color-text-primary;
    padding: 0 0 10px;
  }
}
.coupon-card-item {
  width: 43.2vw;
  height: 92px;
  background-color: $color-brand-secondary;
  border-radius: $radius-l;
  padding: 10px 12px 12px;
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: space-between;

  img {
    position: absolute;
    top: 0;
    right: 0;
  }

  &.no-start {
    opacity: 0.7;
  }

  &.disabled {
    background-color: $color-text-disabled;
  }

  &__title {
    @include font-body-s-bold;
    color: $color-text-primary-onDark;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    word-break: break-all;
  }

  &__button {
    @include font-caption-m-semibold;
    background-color: $color-bg-widget-normal;
    border-radius: $radius-xl;
    color: $color-brand-primary;
    width: fit-content;
    padding: 4px 8px;
    margin-top: 6px;

    &.disabled {
      color: $color-text-disabled;
    }
  }
}
</style>
<style lang="scss">
.coupon-swiper {

  .klk-card-swiper-items-wrapper {
    overflow: hidden;
  }
  .klk-card-swiper-items {
    width: 100%;
    display: flex;
    flex-wrap: nowrap;
    overflow-x: auto;
    overflow-y: hidden;
    padding: 0 0 20px;
    margin-bottom: -20px;
  }
}
</style>
