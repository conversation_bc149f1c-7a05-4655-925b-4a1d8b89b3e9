<template>
  <div v-if="data && data.recommend_city_list && data.recommend_city_list.length > 0" class="mb-popular-city">
    <h2 class="mb-popular-city__title">{{ data.title }}</h2>
    <klk-card-swiper class="mb-popular-city__swiper" item-width="41.9vw" :item-gap="10" :scroll-mode="true">
      <klk-card-swiper-item v-for="(item, index) in data.recommend_city_list" :key="index">
        <div
          class="mb-popular-city__item"
          :data-spm-module="`PopulayCities_LIST?oid=city__${item.city_id}`"
          v-galileo-click-tracker="galileoClick1"
          data-spm-virtual-item="__virtual?typ=entry"
        >
          <div class="mb-popular-city__image" :style="{'background-image': 'url(' + formatPicExtension(item.image_url) + ')'}">
            <span class="mb-popular-city__counts">{{ item.search_count }} {{ $t('49755-visitors') }}</span>
          </div>
          <div class="mb-popular-city__name">{{ item.city_name }}</div>
          <div class="mb-popular-city__price">
            <em>{{ $t('49756-From') }}</em>
            <span>{{ item.currency }} {{ item.min_price }}</span>
          </div>
          <a class="mb-popular-city__link" :href="item.link"></a>
        </div>
      </klk-card-swiper-item>
    </klk-card-swiper>
  </div>
</template>
<script lang="ts">
import { Component, Prop } from 'vue-property-decorator'
import Base from "../common/base";
import { WebClick_31 } from '../../share/galileo/auto-click'

@Component
export default class popularCity extends Base {
  @Prop({ type: Object, default: () => null }) data!: any

  formatPicExtension(url: string) {
    if (!url) { return '' }

    if (this.realWebp) {
      return url.replace(/.(jpg|png|jpeg)$/, '.webp')
    }

    return url
  }

    get galileoClick1() {
        return { spm: WebClick_31, autoTrackSpm: true }
    }
}
</script>
<style lang="scss">
.mb-popular-city {

  &__title {
    @include font-body-l-bold;
    color: $color-text-primary;
    padding: 0 0 10px;
  }

  &__item {
    height: 187px;
    border-radius: $radius-l;
    background-color: $color-bg-widget-normal;
    position: relative;
  }

  &__link {
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
  }

  &__image {
    width: 100%;
    height: 88px;
    border-radius: $radius-l $radius-l 0 0;
    position: relative;
    background-size: cover;
    background-position: center center;
  }

  &__counts {
    @include font-caption-m-regular;
    color: $color-text-primary-onDark;
    padding: 1.5px 6px;
    border-radius: $radius-pill;
    background-color: $color-bg-overlay-black-desktop;
    display: inline-block;
    position: absolute;
    bottom: 10px;
    left: 10px;
  }

  &__name {
    @include font-body-s-semibold;
    height: 40px;
    margin-top: 10px;
    padding: 0 12px;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    word-break: break-word;
  }

  &__price {
    display: flex;
    flex-direction: column;
    padding: 0 12px;

    em {
      @include font-caption-m-regular;
      color: $color-text-secondary;
      font-style: normal;
    }

    span {
      @include font-body-s-semibold;
      color: $color-text-primary;
      display: -webkit-box;
      -webkit-line-clamp: 1;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }
  }
}
</style>
<style lang="scss">
.mb-popular-city {

  .klk-card-swiper-items-wrapper {
    overflow: hidden;
  }
  .klk-card-swiper-items {
    width: 100%;
    display: flex;
    flex-wrap: nowrap;
    overflow-x: auto;
    overflow-y: hidden;
    padding: 0 0 20px;
    margin-bottom: -20px;
  }
}
</style>
