<template>
  <div v-if="data && data.recommend_city_list && data.recommend_city_list.length > 0" class="dt-popular-city">
    <h2 class="dt-popular-city__title">{{ data.title }}</h2>
    <div class="dt-popular-city__list">
      <klk-card-swiper :item-width="275" :item-gap="20">
        <klk-card-swiper-item v-for="(item, index) in data.recommend_city_list" :key="index">
          <div
            class="dt-popular-city__item"
            :data-spm-module="`PopulayCities_LIST?oid=city_${item.city_id}`"
            v-galileo-click-tracker="galileoClick1"
            data-spm-virtual-item="__virtual?typ=entry"
          >
            <div class="dt-popular-city__image" :style="{'background-image': 'url(' + formatPicExtension(item.image_url) + ')'}"></div>
            <div class="dt-popular-city__name">{{ item.city_name }}</div>
            <div class="dt-popular-city__price">
              <em>{{ $t('49756-From') }}</em>
              <span>{{ item.currency }} {{ item.min_price }}</span>
            </div>
            <div class="dt-popular-city__visitors">
              <div v-if="item.user_images && item.user_images.length > 0" class="dt-popular-city__visitors-avatars">
                <span v-for="(img, i) in item.user_images" :key="i" class="dt-popular-city__visitors-avatar">
                  <img :src="img" alt="">
                </span>
              </div>
              <div class="dt-popular-city__visitors-counts">{{ item.search_count }} {{ $t('49755-visitors') }}</div>
            </div>
            <a class="dt-popular-city__link" :href="item.link"></a>
          </div>
        </klk-card-swiper-item>
      </klk-card-swiper>
    </div>
  </div>
</template>
<script lang="ts">
import { Component, Prop } from 'vue-property-decorator'
import Base from "../common/base";
import { WebClick_41 } from '../../share/galileo/auto-click'

@Component
export default class popularCity extends Base {
  @Prop({ type: Object, default: () => null }) data!: any

  formatPicExtension(url: string) {
    if (!url) { return '' }
    if (this.realWebp) {
      return url.replace(/.(jpg|png|jpeg)$/, '.webp')
    }

    return url
  }

    get galileoClick1() {
        return { spm: WebClick_41, autoTrackSpm: true }
    }
}
</script>
<style lang="scss" scoped>
.dt-popular-city {
  width: 1160px;
  margin: 64px auto 0;

  &__list {
    margin-top: 32px;
  }

  &__title {
    font-size: $fontSize-heading-m;
    line-height: 36px;
    font-weight: $fontWeight-bold;
  }

  &__item {
    border-radius: $radius-l;
    height: 300px;
    box-shadow: 0 0 1px rgba(0, 0, 0, 0.5);
    transition: all .2s ease;
    transform-style: preserve-3d;
    position: relative;

    &:hover {
      box-shadow: 0px 4px 20px rgba(0, 0, 0, 0.08);
      transform: translateY(-4px);
      z-index: 2;
    }
  }

  &__link {
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
  }

  &__image {
    width: 100%;
    height: 155px;
    border-radius: $radius-l $radius-l 0 0;
    background-size: cover;
    background-position: center center;
  }

  &__name {
    @include font-body-m-semibold;
    height: 48px;
    margin-top: 16px;
    padding: 0 16px;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    word-break: break-word;
  }

  &__price {
    display: flex;
    flex-direction: column;
    padding: 0 16px;

    em {
      @include font-caption-m-regular;
      color: $color-text-secondary;
      font-style: normal;
    }

    span {
      @include font-body-m-semibold;
      color: $color-text-primary;
    }
  }

  &__visitors {
    margin-top: 10px;
    padding: 0 16px;
    display: flex;
    flex-direction: row;
    align-items: center;

    &-avatars {
      margin-right: 6px;
      width: 36px;
      height: 16px;
      position: relative;
    }

    &-avatar {
      border: 1px solid $color-border-active-onDark;
      border-radius: $radius-circle;
      width: 16px;
      height: 16px;
      overflow: hidden;
      position: absolute;
      left: 0;
      z-index: 11;

      &:nth-child(2) {
        left: 10px;
        z-index: 10;
      }

      &:nth-child(3) {
        left: 20px;
        z-index: 9;
      }

      img {
        width: 100%;
        height: 100%;
      }
    }

    &-counts {
      @include font-caption-m-regular;
      color: $color-text-secondary;
    }
  }
}
</style>
<style lang="scss">
.dt-popular-city {

  .klk-card-swiper-items-wrapper {
    max-width: none;
    padding: 15px;
    margin: -15px;
  }
}
</style>
