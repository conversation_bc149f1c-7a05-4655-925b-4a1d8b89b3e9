<template>
  <div v-if="data && data.vendor_list" :class="$style.dealer">
    <h2 :class="$style.title">{{ data.title }}</h2>
    <div :class="$style.dealerList">
      <a
        v-for="vendor in data.vendor_list"
        :key="vendor.vendor_id"
        :href="vendor.link"
        :data-spm-module="`SupplierLogo?oid=supplier_${vendor.vendor_id}`"
        v-galileo-click-tracker="galileoClick1"
        data-spm-virtual-item="__virtual?typ=entry"
        :class="$style.item"
      >
        <img :src="vendor.image_url">
      </a>
    </div>
  </div>
</template>
<script lang="ts">
import { Component, Prop } from 'vue-property-decorator'
import Base from "../common/base";
import { WebClick_21 } from '../../share/galileo/auto-click'

interface Dealer {
  title: string
  dealer_image_list: string[]
  vendor_list: any[]
}

@Component
export default class supplierList extends Base {
  @Prop({ type: Object, default: () => null }) data!: Dealer

    get galileoClick1() {
        return { spm: WebClick_21, autoTrackSpm: true }
    }
}
</script>

<style lang="scss" module>
.dealer {
  width: 1160px;
  margin: 0 auto;

  .title {
    @include font-heading-m;
    padding: 0 0 32px;
  }

  .dealerList {
    width: 100%;
    display: flex;
    justify-content: flex-start;

    .item {
      width: 136px;
      height: 70px;
      border: 1px solid $color-border-normal;
      overflow: hidden;
      padding: 11px;
      margin-right: 10px;
      border-radius: $radius-l;
      font-size: 0;

      > img {
        width: 112px;
        height: 46px;
        border-radius: $radius-m;
        overflow: hidden;
      }
    }
  }
}
</style>
