<template>
  <div :class="`promo-price promo-price-${direction} ${!isMobile ? 'desktop' : ''}`">
    <div class="promo-price-title">{{ item.discount_amount }}</div>
    <div v-if="direction === 'horizontal'" class="promo-price-divider">
      <div class="left-placeholder"></div>
      <div class="divider-seperator">
        <span class="divider-seperator__border"></span>
      </div>
      <div class="right-placeholder"></div>
    </div>
    <div class="promo-price-desc" :style="{ color: color }">
      <div class="desc-content">{{ item.discount_desc }}</div>
    </div>
  </div>
</template>

<script lang="ts">
import { Vue, Component, Prop } from 'vue-property-decorator'

@Component
export default class PromoPrice extends Vue {
  @Prop({ type: Object, default: () => ({}) }) item!: any
  @Prop({ type: Boolean, default: true }) isMobile!: boolean
  @Prop({ type: String, default: '#eee' }) color!: string
  @Prop({ type: String, default: 'horizontal' }) direction!: 'vertical' | 'horizontal'
}
</script>

<style lang="scss" scoped>
.promo-price {
  border-radius: $radius-m;
  overflow: hidden;
  height: 100%;

  .promo-price-title {
    color: $color-error;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    background: $color-error-background;
  }

  .promo-price-desc {
    position: relative;
    background: $color-error-background;

    .desc-content {
      color: $color-red-800;
    }
  }
}

.promo-price-vertical {
  text-align: center;
  padding: 8px 6px;
  display: flex;
  flex-direction: column;
  background: $color-error-background;
  border: 0.5px solid $color-orange-100;

  .promo-price-desc {
    @include font-caption-m-regular;
    flex: 1 1 0;
  }

  .promo-price-divider .placeholder {
    height: 8px
  }

  .promo-price-divider {
    flex: 0 0 auto;
    .left-placeholder, .right-placeholder {
      height: 8px;
    }

    .divider-seperator {
      margin: 0 5px;
      background: $color-error-background;
      padding: 0 3px;
      line-height: 0;
      font-size: 0;

      &__border {
        border-top: 1px dashed $color-orange-100;
        display: inline-block;
        width: 100%;
      }
    }

    .right-placeholder {
      background: radial-gradient(circle at 100% 0, transparent 5px, $color-error-background 0) 100% 0%/60% 100% no-repeat, radial-gradient(circle at 0 0, transparent 5px, $color-error-background 0) 0 100%/60% 100% no-repeat;
    }

    .left-placeholder {
      background: radial-gradient(circle at 100% 100%, transparent 5px, $color-error-background 0) 100% 0%/60% 100% no-repeat, radial-gradient(circle at 0 100%, transparent 5px, $color-error-background 0) 0 100%/60% 100% no-repeat;
    }
  }

  .promo-price-title {
    @include font-body-m-bold-v2;
    border-radius: $radius-m $radius-m 0 0;
    flex: 0 0 auto;
  }

  .desc-content {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    text-overflow: ellipsis;
    overflow: hidden;
    padding-top: 2px;
  }
}

.promo-price-horizontal {
  display: inline-flex;
  align-items: center;
  max-width: 520px;

  .promo-price-desc {
    @include font-paragraph-xs-regular;
    flex: 1 1 0;
    overflow: hidden;
    padding: 7px 12px 7px 4px;
    border-radius: 0 $radius-m $radius-m 0;
    display: flex;
    align-items: center;
    height: 100%;
  }

  .promo-price-divider {
    flex: 0 0 auto;
    height: 100%;
    display: flex;
    align-items: stretch;

    .left-placeholder, .right-placeholder {
      width: 6px;
      height: 100%;
    }

    .right-placeholder {
      background: radial-gradient(circle at 0 -10%, transparent 5px, $color-error-background 0) 100% 0%/100% 60% no-repeat, radial-gradient(circle at 0 110%, transparent 5px, $color-error-background 0) 0 100%/100% 60% no-repeat;
    }

    .left-placeholder {
      background: radial-gradient(circle at 100% -10%, transparent 5px, $color-error-background 0) 100% 0%/100% 60% no-repeat, radial-gradient(circle at 100% 110%, transparent 5px, $color-error-background 0) 0 100%/100% 60% no-repeat;
    }

    .divider-seperator {
      margin: 3px 0;
      background: $color-error-background;
      padding: 3px 0;

      &__border {
        border-right: 1px dashed $color-orange-100;
        display: inline-block;
        height: 100%;
      }
    }
  }

  .promo-price-title {
    @include font-body-xs-bold;
    flex: 0 0 auto;
    padding: 7px 4px 7px 12px;
    border-radius: $radius-m 0 0 $radius-m;
    display: flex;
    align-items: center;
    height: 100%;
  }

  .desc-content {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
}
.desktop {
  .coupon-entry__title {
    @include font-heading-xs;
    padding: 10.5px 4px 12px 12px;
  }
  .promo-price-title {
    @include font-body-m-bold-v2;
  }
  .promo-price-desc {
    padding: 10.5px 12px 12px 4px;
  }
  .desc-content {
    @include font-paragraph-s-regular;
  }
}
</style>
