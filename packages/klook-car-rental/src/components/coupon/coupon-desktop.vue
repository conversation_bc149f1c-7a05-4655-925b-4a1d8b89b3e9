<template>
  <div v-if="couponInfo && couponInfo.items && couponInfo.items.length > 0" class="coupon-wrap desktop">
    <div class="coupon-entry" @click="openCouponList">
      <div class="coupon-entry__header">
        <div class="coupon-entry__title">{{ couponInfo.title }}</div>
        <div class="coupon-entry__btn">
          <IconNext theme="outline" size="14" :fill="tokens['$color-brand-primary']" />
        </div>
      </div>
      <div class="coupon-entry__sub-title">{{ couponInfo.desc }}</div>
      <div class="coupon-entry__list">
        <klk-card-swiper
          item-width="auto"
          :item-gap="8"
          controller-position="intersect"
          @transitionend="onCardSwiperTransitionEnd"
        >
          <klk-card-swiper-item v-for="(item, index) in couponInfo.items" :key="index">
            <CouponCard :item="item" color="#ccc" :is-mobile="false" />
          </klk-card-swiper-item>
        </klk-card-swiper>
        <div class="card-mask card-mask-left"></div>
        <div class="card-mask card-mask-right"></div>
      </div>
    </div>
    <klk-modal
      :title="couponPopupTitle"
      width="900"
      :z-index="1900"
      :closable="true"
      :overlay-closable="false"
      :open.sync="showCouponList"
      :show-default-footer="false"
      @close="closeCouponList"
    >
      <div class="coupon-list-desktop">
        <NewCouponItem
          v-for="(item, index) in couponDataList"
          :key="index"
          ref="couponCard"
          class="coupon"
          :coupon-data="item"
          @onRedeemStatusUpdate="onRedeemStatusUpdate"
        ></NewCouponItem>
      </div>
    </klk-modal>
  </div>
</template>

<script lang="ts">
import { Component } from 'vue-property-decorator'
import CouponBase from "./coupon-base";

@Component
export default class CouponEntry extends CouponBase {
}
</script>

<style lang="scss" scoped>
.coupon-wrap {
  width: 100%;
  &.desktop {
    width: 1160px;
    margin: 0 auto;
  }
}
.coupon-entry {
  background: linear-gradient(176deg, #FFF1F7 5.59%, #FFFFFF 45.39%), linear-gradient(0deg, #FFFFFF, #FFFFFF);
  border: 1px solid $color-error-background;
  border-radius: $radius-l;
  padding: 11px;
  position: relative;

  &__header {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  &__title {
    @include font-body-m-bold;
    padding-top: 4px;
  }
  &__list {
    width: 100%;
    margin-top: 16px;
    position: relative;
  }
  &__btn {
    width: 18px;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: $color-orange-50;
    border-radius: $radius-circle;
    font-size: 0;
  }
}
.desktop {
  cursor: pointer;
  .coupon-entry {
    padding: 20px 24px;
    cursor: pointer;
    &__header {
      justify-content: flex-start;
    }
    &__title {
      @include font-heading-xs;
      padding-top: 0;
    }
    &__sub-title {
      @include font-body-m-regular;
      padding-top: 4px;
    }
    &__btn {
      width: 20px;
      height: 20px;
      margin-left: 12px;
    }
  }
  .card-mask {
    height: 100%;
    position: absolute;
    width: 52px;
    top: 0;
  }
  .card-mask-left {
    left: -22px;
    background: linear-gradient(90deg, rgba(255, 255, 255, 0.5) 39.58%, rgba(255, 255, 255, 0.3) 95.45%);
    display: none;
  }
  .card-mask-right {
    right: -22px;
    background: linear-gradient(270deg, rgba(255, 255, 255, 0.5) 39.58%, rgba(255, 255, 255, 0.3) 95.45%);
  }
}
.coupon-list {
  display: flex;
  flex-direction: column;
  gap: 16px;

  &-desktop {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    padding-top: 24px;
    .coupon-item {
      flex: calc(50% - 20px) 0 0;
    }
  }
}

.coupon-swiper {
  width: 100%;
  ::v-deep .klk-card-swiper-items-wrapper {
    overflow: hidden;
  }
  ::v-deep .klk-card-swiper-items {
    width: 100%;
    display: flex;
    flex-wrap: nowrap;
    overflow-x: auto;
    overflow-y: hidden;
    padding: 0 0 20px 0;
    margin-bottom: -20px;
  }
}
</style>
