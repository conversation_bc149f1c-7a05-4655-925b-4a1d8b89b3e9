import { Component } from 'vue-property-decorator'
import Base from "../../common/base";
import { NewCouponItem } from '@klook/promo-entrance'
import '@klook/promo-entrance/dist/promo-entrance.esm.css'
// @ts-ignore
import { tokens } from '@klook/klook-ui/lib/utils/design-token'
import { IconNext } from '@klook/klook-icons'
import CouponCard from './coupon-card.vue'
@Component({
  components: {
    CouponCard,
    IconNext,
    NewCouponItem
  }
})
export default class couponBase extends Base {
  tokens: any = tokens
  showCouponList: boolean = false
  // sentry错误类型
  sentryErrorType = 'business-error'
  coupon: any = {
    entryList: '/v1/transfercarrentalapisrv/coupon/get_entry',
    couponList: '/v1/transfercarrentalapisrv/coupon/get_coupon_card_list'
  }
  couponInfo: any = null
  couponDataList: any = null
  couponPopupTitle: string = ''
  couponList = null

  getCouponEntryInfo() {
    const params = {
      vertical_type: 'HOME'
    }
    this._axios
      .$post(this.coupon.entryList, params)
      .then((res: any) => {
        if (res.success && res.result) {
          this.couponInfo = res.result
        }
      })
      .catch((error: any) => {
        this.$toast(error.message)
      })
  }

  onCardSwiperTransitionEnd(x: number) {
    const absX = Math.abs(x)
    const leftMaskEl = this.$el.querySelector('.card-mask-left') as HTMLElement
    const rightMaskEl = this.$el.querySelector('.card-mask-right') as HTMLElement
    const cardSwiperItemsContainer = this.$el.querySelector('.klk-card-swiper-items') as HTMLElement
    leftMaskEl && (leftMaskEl.style.display = absX > 0 ? 'block' : 'none')

    if (rightMaskEl && cardSwiperItemsContainer) {
      rightMaskEl.style.display = absX < cardSwiperItemsContainer.scrollWidth - cardSwiperItemsContainer.offsetWidth ? 'block' : 'none'
    }
  }

  mounted() {
    this.getCouponEntryInfo()
  }

  openCouponList(e: MouseEvent) {
    const target = e.target as HTMLElement
    const prevEl = this.$el.querySelector('.klk-card-swiper-next-btn')
    const nextEl = this.$el.querySelector('.klk-card-swiper-prev-btn')
    if (prevEl?.contains(target) || nextEl?.contains(target)) {
      return
    }
    this.showCouponList = true
    this.getCouponList()
  }

  getCouponList() {
    const params = {
      vertical_type: 'HOME'
    }
    this._axios
      .$post(this.coupon.couponList, params)
      .then((res: any) => {
        if (res.success && res.result) {
          this.couponPopupTitle = res.result.title
          this.couponDataList = res.result.items
        }
      })
      .catch((error: any) => {
        this.$toast(error.message)
      })
  }

  closeCouponList() {
    this.showCouponList = false
  }

  onRedeemStatusUpdate(status: any) {
    if (status === 'to_use') {
      this.getCouponList()
    }
  }
}