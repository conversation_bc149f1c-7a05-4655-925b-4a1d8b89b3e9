<template>
  <div v-if="couponInfo && couponInfo.items && couponInfo.items.length > 0" class="coupon-wrap">
    <div class="coupon-entry" @click="openCouponList">
      <div class="coupon-entry__header">
        <div class="coupon-entry__title">{{ couponInfo.title }}</div>
        <div class="coupon-entry__btn">
          <IconNext theme="outline" size="12" :fill="tokens['$color-brand-primary']" />
        </div>
      </div>
      <div class="coupon-entry__list">
        <klk-card-swiper class="coupon-swiper" item-width="26.4vw" :item-gap="8" :scroll-mode="true">
          <klk-card-swiper-item v-for="(item, index) in couponInfo.items" :key="index">
            <CouponCard :item="item" color="#ccc" direction="vertical" />
          </klk-card-swiper-item>
        </klk-card-swiper>
      </div>
    </div>
    <klk-bottom-sheet
      :visible.sync="showCouponList"
      :show-close="true"
      :delay-time="1"
      :title="couponPopupTitle"
      @close="closeCouponList"
    >
      <div class="coupon-list">
        <NewCouponItem
          v-for="(item, index) in couponDataList"
          :key="index"
          ref="couponCard"
          class="coupon"
          :coupon-data="item"
          @onRedeemStatusUpdate="onRedeemStatusUpdate"
        ></NewCouponItem>
      </div>
    </klk-bottom-sheet>
  </div>
</template>

<script lang="ts">
import { Component } from 'vue-property-decorator'
import CouponBase from "./coupon-base";

@Component
export default class CouponEntry extends CouponBase {
}
</script>

<style lang="scss" scoped>
.coupon-wrap {
  width: 100%;
}
.coupon-entry {
  background: linear-gradient(176deg, #FFF1F7 5.59%, #FFFFFF 45.39%), linear-gradient(0deg, #FFFFFF, #FFFFFF);
  border: 1px solid $color-error-background;
  border-radius: $radius-l;
  padding: 11px;
  position: relative;

  &__header {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  &__title {
    @include font-body-m-bold;
    padding-top: 4px;
  }
  &__list {
    width: 100%;
    margin-top: 16px;
    position: relative;
  }
  &__btn {
    width: 18px;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: $color-orange-50;
    border-radius: $radius-circle;
    font-size: 0;
  }
}
.coupon-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.coupon-swiper {
  width: 100%;
  ::v-deep .klk-card-swiper-items-wrapper {
    overflow: hidden;
  }
  ::v-deep .klk-card-swiper-items {
    width: 100%;
    display: flex;
    flex-wrap: nowrap;
    overflow-x: auto;
    overflow-y: hidden;
    padding: 0 0 20px 0;
    margin-bottom: -20px;
  }
}
</style>
