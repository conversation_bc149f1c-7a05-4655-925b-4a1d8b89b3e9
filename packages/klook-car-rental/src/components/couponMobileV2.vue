<template>
  <div
    v-if="
      couponInfo && couponInfo.coupon_list && couponInfo.coupon_list.length > 0
    "
    class="coupon-card"
  >
    <h2 class="coupon-title">{{ couponInfo.coupon_title }}</h2>
    <div class="coupons">
      <div
        v-for="(item, index) in couponInfo.coupon_list"
        :key="index"
        class="coupons-item coupons-item-mobile"
      >
        <div class="coupon-details">
          <div
            :class="{
              'coupon-details__desc': true,
              'coupon-details__desc_inactive': isInActiveStyle(item.coupon_status),
            }"
          >
            {{ item.desc }}
          </div>
          <div v-if="item.code" class="coupon-details__sub-title">
            <!-- {{ $t('54088-promo_code') }} -->
            {{ $t('13766-promo_code') }}
            <span class="coupon-details__sub-title_span">{{ item.code }}</span>
          </div>
        </div>
        <div class="divide-box">
          <div class="divide-box__left" />
          <div class="divide-box__right" />
        </div>
        <div class="coupon-currency">
          <div
            :class="{
              'coupon-currency__to_redeem': true,
              'coupon-currency__to_redeem_small': item.title && item.title.length > 14,
              'coupon-currency__to_redeem_inactive': isInActiveStyle(
                item.coupon_status
              )
            }"
          >
            {{ item.title }}
          </div>
          <klk-button
            v-if="item.coupon_status === 'pending'"
            :class="{
              disabled:
                item.coupon_status === 'usable' ||
                item.coupon_status === 'all_out'
            }"
            :data-spm-module="`CouponsCenter_LIST?oid=coupon_${
              item.batch_id
            }&ext=${JSON.stringify({ Status: item.coupon_status })}`"
            v-galileo-click-tracker="galileoClick1"
            data-spm-virtual-item="__virtual?typ=entry"
            class="coupon-currency__button"
            size="mini"
            round
            @click="reedomCoupon(item)"
          >
            {{ item.button_text }}
          </klk-button>
          <div v-else-if="isInActiveStyle(item.coupon_status)" class="coupon-currency__button_inactive">{{ item.button_text }}</div>
          <div v-else-if="item.coupon_status === 'usable'" class="coupon-currency__button_usable">
            <!-- <SvgIcon
                name="desktop-car-rental#icon_feedback_success_fill_xs"
                width="16"
                height="16"
              ></SvgIcon> -->
              <img src="https://res.klook.com/image/upload/v1723019477/UED_new/_Icon_Logo/___KIcon_KIcons.icon_check_circle_outlined_size__16_colors__colorScheme.colorBrandPrimary.png" width="16" height="16">
            <span>{{ item.button_text }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Component } from 'vue-property-decorator'
import Base from "../common/base";
import { getStandardDateFormat } from '../common/datetime'
import { getSourceCountryCode } from '../common/utils'
import { WebClick_51 } from '../../share/galileo/auto-click'

@Component
export default class couponCardList extends Base {

  couponInfo: any = null
  countryCode: any = null

  coupon: any = {
    reedom: '/v1/transfercarrentalapisrv/coupon/redeem',
    query: '/v1/transfercarrentalapisrv/coupon/query_list'
  }

  mounted() {
    getSourceCountryCode(this, this.handleCouponList)
  }

  isInActiveStyle(status: string) {
    return ['all_out', 'not_start'].includes(status)
  }

  handleCouponList(countryCode: any) {
    this.countryCode = countryCode
    const params = {
      resource_path: 'mweb-home',
      source_country_code: countryCode,
      zone: -new Date().getTimezoneOffset() / 60
    }
    return this._axios
      .$get(this.coupon.query, { params })
      .then((res: any) => {
        if (res && res.result) {
          this.couponInfo = res.result
        }
      })
      .catch((error: any) => {
        this.$toast(error.message)
      })
  }

  async login() {
    const { loginWithSDK } = await import('@klook/klook-traveller-login')

    if (!await loginWithSDK({
      isMP: false,
      isKlookApp: false,
      platform: this.$store.getters.platform,
      language: this.$store.getters.language,
      currency: this.$store.getters.currencySymbol,
      bizName: 'CarRental',
      purpose: 'CarRentalHomePage',
      success: () => {
        window.location.reload()
      }
    })) {
      // this.$logger.error('租车调用登录SDK返回不支持')
    }
  }

  reedomCoupon(item: any) {
    if (item.coupon_status === 'pending') {
      const data = {
        type: item.type, // 1=promocode券,2=program券
        batch_id: item.batch_id || null,
        program_id: item.program_id || null,
        resource_path: 'mweb-home',
        source_country_code: this.countryCode
      }
    const headers = { 'Content-Type': 'application/json; charset=UTF-8' }
    return this._axios
      .$post(this.coupon.reedom, data, { headers, timeout: 15000, throwError: true })
      .then((res: any) => {
        if (res && res.result) {
          this.$toast({
            message: this.$t('54021-success'),
            duration: 2000
          })
          getSourceCountryCode(this, this.handleCouponList)
        }
      })
      .catch((error: any) => {
          if (error.code === '4001') {
            this.login()
          } else {
            this.$toast(error.message)
            setTimeout(() => {
              getSourceCountryCode(this, this.handleCouponList)
            }, 1000)
          }
        })
    } else if (item.coupon_status === 'not_start') {
      const formatted = getStandardDateFormat(new Date(item.start_time_utc), this.$t.bind(this), this.curLocale, 2)
      this.$toast({
        message: this.$t('54028-not-start', { date: formatted }),
        duration: 2000
      })
    }
  }

    get galileoClick1() {
        return { spm: WebClick_51, autoTrackSpm: true }
    }
}
</script>

<style lang="scss" scoped>
.coupon-card {
  .coupon-title {
    @include font-body-l-bold;
    color: $color-text-primary;
    // padding: 24px 20px 16px;
    margin-bottom: 16px;
  }
  .coupons {
    // padding: 0 20px;
    display: flex;
    gap: 12px;
    overflow-x: auto;
    overflow-y: hidden;
    scrollbar-width: none;
    -ms-overflow-style: none; /* IE 10+ */
    ::-webkit-scrollbar {
      display: none; /* Chrome Safari */
    }
    .coupons-item-mobile {
      flex: 0 0 auto;
      // width: calc(100vw - 70px);
    }
    .coupons-item {
      display: flex;
      border-radius: 16px;
      border-radius: $radius-xl;
      position: relative;
      overflow: hidden;
      .coupon-details {
        flex-direction: column;
        display: flex;
        justify-content: center;
        // width: calc(100% - 136px);
        width: 160px;
        padding: 16px 8px 16px 16px;
        background: #fff;
        border: 1px solid $color-orange-200;
        border-radius: 16px 0 0 16px;
        border-right: 0;
        margin-right: -1px;
        &__desc {
          // @include font-body-m-bold-v2;
          font-size: 16px;
          line-height: 1.5;
          font-weight: 600;
          color: $color-text-primary;
          // display: flex;
          display: -webkit-box;
          margin-bottom: 8px;
          -webkit-line-clamp: 3;
          -webkit-box-orient: vertical;
          text-overflow: ellipsis;
          overflow: hidden;
        }
        &__desc_inactive {
          color: $color-text-secondary;
        }
        &__sub-title {
          @include font-caption-m-regular;
          color: $color-text-secondary;
          margin-bottom: 8px;
          word-wrap: break-word;
          display: flex;
          flex-wrap: wrap;
          &_span {
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            text-overflow: ellipsis;
            overflow: hidden;
          }
        }
      }
      .divide-box {
        flex: 0 0 16px;
        display: flex;
        position: relative;
        z-index: 2;
        &::before {
          top: -8.5px;
          transform: rotate(45deg);
          content: '';
          border-radius: 50%;
          border-left: 1.2px solid transparent;
          border-bottom: 1.2px solid $color-orange-200;
          border-right: 1.2px solid $color-orange-200;
          border-top: 1.2px solid transparent;
          display: block;
          height: 16px;
          position: absolute;
          width: 16px;
          z-index: 2;
        }
        &::after {
          bottom: -8.5px;
          transform: rotate(225deg);
          content: '';
          border-radius: 50%;
          border-left: 1.2px solid transparent;
          border-bottom: 1.2px solid $color-orange-200;
          border-right: 1.2px solid $color-orange-200;
          border-top: 1.2px solid transparent;
          display: block;
          height: 16px;
          position: absolute;
          width: 16px;
          z-index: 2;
        }
        &__left {
          width: 8px;
          height: 100%;
          background: radial-gradient(circle at 100% 0, transparent 7px, #fff 0)
              0 0 / 100% 60% no-repeat,
            radial-gradient(circle at 100% 100%, transparent 7px, #fff 0) 0 100% /
              100% 60% no-repeat;
          position: relative;
          &::before {
            background-image: linear-gradient(
              180deg,
              transparent 5px,
              $color-orange-200 0
            );
            background-position: 0 0;
            background-repeat: repeat-y;
            background-size: 2px 10px;
            bottom: 9px;
            content: '';
            display: block;
            right: -0.5px;
            position: absolute;
            top: 9px;
            width: 1px;
          }
        }
        &__right {
          width: 8px;
          height: 100%;
          background: radial-gradient(
                circle at 0 0,
                transparent 7px,
                $color-brand-primary-light 0
              )
              0 0 / 100% 60% no-repeat,
            radial-gradient(
                circle at 0 100%,
                transparent 7px,
                $color-brand-primary-light 0
              )
              0 100% / 100% 60% no-repeat;
        }
      }
      .coupon-currency {
        border-radius: 0 16px 16px 0;
        border: 1px solid $color-orange-200;
        color: $color-brand-primary;
        border-left: 0;
        display: flex;
        flex-wrap: wrap;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        flex: 0 0 120px;
        width: 120px;
        position: relative;
        margin-left: -0.5px;
        padding: 16px 12px 16px 0;
        background: $color-brand-primary-light;
        &__button_inactive {
          @include font-body-s-semibold;
          color: $color-text-placeholder;
        }
        &__button_usable {
          display: flex;
          gap: 4px;
        }
        &__to_redeem {
          @include font-heading-xs;
          color: $color-brand-primary;
          margin-bottom: 8px;
          width: 100%;
          text-align: center;
          word-break: break-all;
        }
        &__to_redeem_small {
          font-size: 16px;
        }
        &__to_redeem_inactive {
          color: #ff8845;
        }
        &__button {
          text-overflow: ellipsis;
          overflow: hidden;
          max-width: 100%;
          border-color: $color-brand-primary;
          color: $color-text-reverse;
          background: $color-brand-primary;
        }
      }
    }
  }
}
</style>
