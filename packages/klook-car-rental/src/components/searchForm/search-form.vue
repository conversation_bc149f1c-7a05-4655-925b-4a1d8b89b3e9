<template>
  <div :class="$style.container">
    <div :class="[{'radius-left-top': desktopCalCulateComplete && showSearchFormRadiusLeftTop, 'radius-above': showSearchFormRadiusAbove, 'bottom-shadow': showBoxShadow}, $style.wrapper]">
      <!-- 是否异地还车勾选框 -->
      <div
        :data-spm-module="getModuleFormName('ReturnToDifferentLocation')"
        v-galileo-click-tracker.stop="galileoClick1"
        :data-spm-virtual-item="spmVirtual"
        :class="$style.checkbox"
      >
        <klk-checkbox
          v-model="checkbox.global_value"
          v-mixpanel:click="{ name: 'Search_Search_ReturnToDifferentLocation_Click' }"
          v-gtm:click="sendSearchGTM('Search_Search_ReturnToDifferentLocation_Click')"
          size="small"
          @change="showDropOffDesInput()"
        >
          <span>{{ $t ('car_rental_home_diff_desc') }}</span>
        </klk-checkbox>
      </div>
      <div :class="$style.form">
        <template>
          <!-- 同一地点取还车，poi搜索框 -->
          <div
            :data-spm-module="getModuleFormName('SearchPickup')"
            v-galileo-click-tracker="galileoClick2"
            :data-spm-virtual-item="spmEntry"
          >
            <home-input
              v-show="!checkbox.global_value"
              v-model="searchList.global_keyword"
              v-mixpanel:click="{ name: 'Search_Search_PickupLocation_Expand' }"
              v-gtm:click="sendSearchGTM('Search_Search_PickupLocation_Expand')"
              :input-width="globalDesWidth"
              :placeholder="pickUpDesText"
              :floatholder="pickUpFloatText"
              :class="$style.input"
              :type="'pick-up'"
              :input-default-value.sync="searchList.global_keyword"
              :border-radius="'left'"
              :word-limit="checkbox.global_value ? 20 : 50"
              @focus="showLocation('pick-up', true)"
            />
          </div>
          <!-- 不同地点取还车，取车poi搜索框 -->
          <div
            :data-spm-module="getModuleFormName('DifferentSearchPickup')"
            v-galileo-click-tracker="galileoClick3"
            :data-spm-virtual-item="spmEntry"
          >
            <home-input
              v-show="checkbox.global_value"
              v-model="searchList.global_keyword"
              v-mixpanel:click="{ name: 'Search_Search_PickupLocation_Expand' }"
              v-gtm:click="sendSearchGTM('Search_Search_PickupLocation_Expand')"
              :input-width="globalDesWidth"
              :placeholder="pickUpDesText"
              :floatholder="pickUpFloatText"
              :class="[$style.input]"
              :type="'pick-up'"
              :input-default-value.sync="searchList.global_keyword"
              :border-radius="'left'"
              :word-limit="checkbox.global_value ? 20 : 50"
              @focus="showLocation('pick-up', true)"
            />
          </div>
          <!-- 不同地点取还车，还车poi搜索框 -->
          <div
            :data-spm-module="getModuleFormName('DifferentSearchPickup')"
            v-galileo-click-tracker="galileoClick4"
            :data-spm-virtual-item="spmEntry"
          >
            <home-input
              v-show="checkbox.global_value"
              v-model="searchListDrop.global_keyword"
              v-mixpanel:click="{ name: 'Search_Search_DropoffLocation_Expand' }"
              v-gtm:click="sendSearchGTM('Search_Search_DropoffLocation_Expand')"
              :input-width="globalDesWidth"
              :placeholder="dropOffDesText"
              :floatholder="dropOffFloatText"
              :class="[$style.input, $style.inputRight]"
              :type="'drop-off'"
              :input-default-value.sync="searchListDrop.global_keyword"
              :border-radius="'right'"
              :word-limit="checkbox.global_value ? 20 : 50"
              @focus="showLocation('drop-off', true)"
            />
          </div>
          <div
            :data-spm-module="getModuleFormName('SearchDate')"
            v-galileo-click-tracker="galileoClick5"
            :data-spm-virtual-item="spmVirtual"
          >
            <home-input
              v-mixpanel:click="{ name: 'Search_Search_PickupDateTime_Expand' }"
              v-gtm:click="sendSearchGTM('Search_Search_PickupDateTime_Expand')"
              :input-default-value.sync="pickUpDateTimeText"
              :input-width="207"
              :placeholder="datepickerPlaceholder"
              :floatholder="pickUpTimeText"
              :class="[$style.marginLeft, $style.right_border_radius]"
              :type="'time'"
              :highlight="pickUpHighlight"
              :carmore-mention="globalMention"
              @focus="showPickUp = true;dateMode = 'pickup'"
            />
          </div>
          <!-- drop off date & time -->
          <div
            :data-spm-module="getModuleFormName('SearchDate')"
            v-galileo-click-tracker="galileoClick6"
            :data-spm-virtual-item="spmVirtual"
          >
            <home-input
              ref="dropoff"
              v-mixpanel:click="{ name: 'Search_Serach_DropoffDateTime_Expand' }"
              v-gtm:click="sendSearchGTM('Search_Serach_DropoffDateTime_Expand')"
              :input-default-value.sync="dropOffDateTimeText"
              :input-width="207"
              :placeholder="datepickerPlaceholder"
              :floatholder="dropOffTimeText"
              :class="$style.left_border_radius"
              :type="'time'"
              :input-left="diffDay ? 36 : 12"
              :highlight="dropOffHighlight"
              @focus="showDropOff = true;dateMode = 'dropoff'"
            />
          </div>
        </template>
        <template>
          <div
            :data-spm-module="getModuleFormName('SearchBtn')"
            v-galileo-click-tracker="galileoClick7"
            :data-spm-virtual-item="spmEntry"
          >
            <klk-button
              :class="$style.search"
              @click="search()"
            >
              {{ $t('car_rental_home_search') }}
            </klk-button>
          </div>
        </template>
        <div v-if="diffDay" :class="$style.day">
          <klk-poptip
            placement="top"
            :content="dayDescContent"
            :offset="13"
            :width="280"
          >
            <div :class="$style.center">
              {{ $t('car_rental_home_days_desc', [diffDay]) }}
            </div>
          </klk-poptip>
        </div>
        <div v-if="showSearchInfoTip" :class="$style.otherInfo" :style="searchInfoOffset">{{ searchInfoTip }}</div>
      </div>

      <!-- 驾驶员年龄选择 -->
      <div :class="$style.age">
        <div :class="$style.ageTitle" @mouseover="showPopTip = true" @mouseleave="showPopTip = false">
          <span>{{ $t('car_rental_home_driver_age') }}</span>
          <div :class="$style.ageIcon">
            <klk-poptip
              v-model="showPopTip"
              :placement="placeBottom"
              :content="ageTipContent"
              :offset="13"
              :width="300"
              :z-index="10000"
            >
              <!-- <SvgIcon name="desktop-car-rental#icon_tips_tips_s" width="16" height="16"></SvgIcon> -->
              <img src="https://res.klook.com/image/upload/web3.0/icon_tips_tips_s_azfefw.svg"  width="16" height="16">
            </klk-poptip>
          </div>
          <span :class="$style.ageDot">:</span>
        </div>
        <div
          :class="$style.ageSelector"
        >
          <template>
            <div
              :data-spm-module="getModuleFormName('ChangeDriverAge')"
              v-galileo-click-tracker="galileoClick8"
              :data-spm-virtual-item="spmVirtual"
            >
              <selector
                :list="ageList"
                :default="globalAge"
                :font="12"
                @choose="setAge($event, true)"
              ></selector>
            </div>
          </template>
        </div>
      </div>

      <date-time-picker
        v-if="showPickUp || showDropOff"
        :class="$style.datePicker"
        :pick-up-date.sync="globalPickUpDate"
        :drop-off-date.sync="globalDropOffDate"
        :date-mode="dateMode"
        @updateDateRange="updateDateRange"
        @updateDateTime="updateDateTime"
        @close="closeDateTimePicker"
      ></date-time-picker>

      <!-- 联想搜索地点 -->
      <search-list
        v-if="searchList.show || searchListDrop.show"
        :completePlaceListV3="completePlaceListV3"
        :class="$style.searchList"
        :loading="searchList.show ? searchList.loading : searchListDrop.loading"
        :keyword="searchList.show ? searchList[paramsKeyWord] : searchListDrop[paramsKeyWord]"
        :city="searchList.show ? searchList[paramsCity] : searchListDrop[paramsCity]"
        :left="searchList.show ? searchList.offset : searchListDrop.offset"
        :is-city="searchList.show ? searchList.is_city : searchListDrop.is_city"
        @close="closeSearchListByType(searchList.show ? 'pick-up' : 'drop-off')"
        @setPlace="setPlaceCommon($event, searchList.show ? 'pick-up' : 'drop-off', true)"
      />

      <!-- 热门地点推荐 -->
      <location-list
        v-if="loactionListProp.show || loactionListDropProp.show"
        :class="$style.locationList"
        :left="loactionListProp.show ? loactionListProp.offset : loactionListDropProp.offset"
        :global-city="globalCity"
        :recommended-city="recommendedCity"
        :is-city="loactionListProp.show ? loactionListProp.is_city : loactionListDropProp.is_city"
        :city-id="loactionListProp.show ? loactionListProp.city_id : loactionListDropProp.city_id"
        @setPlace="setPlaceCommon($event, loactionListProp.show ? 'pick-up' : 'drop-off', true)"
        @close="closeLocationList(loactionListProp.show ? 'pick-up' : 'drop-off')"
      />
    </div>
  </div>
</template>

<script lang="ts">
import debounce from 'lodash/debounce'
import dayjs from 'dayjs' // XXX: in safari, new Date('2018-08-23 11:12') will return Invalid Date.
import { Component, Watch, Prop } from 'vue-property-decorator'
import DateTimePicker from './date-time-picker.vue'
import LocationList from './location-list.vue'
import SearchList from './search-list.vue'
import Selector from './selector.vue'
import HomeInput from './home-input.vue'
import { getStandardDateFormat } from '../../common/datetime'
import localStorageEx from '../../common/localstorage'
import Base from "../../common/base";
import { getSourceCountryCode } from '../../common/utils'
import { WebClick_121, WebClick_122, WebClick_123, WebClick_124, WebClick_125, WebClick_126, WebClick_127, WebClick_128 } from '../../../share/galileo/auto-click'

const SEARCHLIST = 'searchList'
const SEARCHLISTDROP = 'searchListDrop'
const PICKUP = 'pick-up'
const DROPOFF = 'drop-off'
const ONE_DAY = 86400000
const carRentalGlobalHistory = 'carRentalGlobalHistory'
const CARMORE_MENTION = 'carmoreMention' // TODO:

interface dyObject {
  [propertyName: string]: number | string | boolean | undefined
}
interface checkboxObject extends dyObject {
  value?: boolean
  color?: string
  ripple?: boolean,
  global_value?: boolean
}
interface searchListObject extends dyObject {
  show: boolean,
  loading: boolean,
  keyword: string,
  city: string,
  global_keyword: string,
  global_city: string,
  offset: number,
  is_city: boolean
}
interface popTipObject extends Object {
  show: boolean,
  content: string,
  offset: number
}
interface locationListObject extends Object {
  show: boolean,
  offset: number,
  is_city: boolean,
  city_id: string
}
interface ageObject extends Object {
  label: string,
  value: string
}
interface locObject extends Object {
  lat: string,
  long: string,
  code: string
}
interface dateTimeObject extends Object {
  date: string,
  hour: string,
  minute: string
}

@Component({
  components: {
    HomeInput,
    DateTimePicker,
    LocationList,
    SearchList,
    Selector
  }
})
export default class SearchForm extends Base {
  @Prop({ type: Boolean, default: true }) updateUrl!: boolean
  @Prop({ type: String, default: '' }) currentPage!: string // home，dlp, result
  @Prop({ type: Array, default: () => [] }) categoryList!: any[]
  @Prop({ type: Boolean, default: true }) desktopCalCulateComplete!: true
  pickUpTimeText: string = ''
  dropOffTimeText: string = ''
  pickUpDesText: string = ''
  pickUpFloatText: string = ''
  dropOffDesText: string = ''
  dropOffFloatText: string = ''
  globalMention: string = ''
  globalPickUpDate: dateTimeObject = {
    date: '',
    hour: '',
    minute: ''
  }

  globalDropOffDate: dateTimeObject = {
    date: '',
    hour: '',
    minute: ''
  }

  dateTimeRange: dateTimeObject[] = []

  between3065 = 'BETWEEN 30-65'

  showPickUp: boolean = false
  showDropOff: boolean = false
  showAgeSelect: boolean = false
  timeLeftChange: boolean = false
  placeBottom: string = 'bottom'
  placeTop: string = 'top'
  ageList: any[] = []
  age: ageObject = {
    label: '',
    value: '30'
  }

  globalAge: ageObject = {
    label: '',
    value: '30'
  }

  showPopTip: boolean = false
  globalDesWidth: Number = 582
  placement: string = 'top'

  // checkbox
  checkbox: checkboxObject = {
    color: '#ff5722',
    ripple: false,
    global_value: false // 海外
  }

  // 本地取车信息
  searchList: searchListObject = {
    show: false,
    loading: false,
    keyword: '', // 本地poi
    city: '', // 本地城市
    global_keyword: '', // 海外poi
    global_city: '', // 海外城市
    offset: 24,
    is_city: false
  }

  // 本地还车信息
  searchListDrop: searchListObject = {
    show: false,
    loading: false,
    keyword: '', // 本地poi
    city: '', // 本地城市
    global_keyword: '', // 海外poi
    global_city: '', // 海外城市
    offset: 315,
    is_city: false
  }

  loactionListProp: locationListObject = {
    show: false,
    offset: 24,
    is_city: false,
    city_id: ''
  }

  loactionListDropProp: locationListObject = {
    show: false,
    offset: 306,
    is_city: false,
    city_id: ''
  }

  pickTip: popTipObject = {
    show: false,
    content: 'Please select your pick-up location',
    offset: 0
  }

  dropTip: popTipObject = {
    show: false,
    content: 'Please select your drop-off location',
    offset: 296
  }

  pickDateTip: popTipObject = {
    show: false,
    content: 'Please select your pick-up location',
    offset: 566
  }

  dropDateTip: popTipObject = {
    show: false,
    content: 'Please select your drop-off location',
    offset: 798
  }

  isUrlDLoad: boolean = false
  isSelectedValue: boolean = false
  selectedSkip: boolean = false
  dayDescContent: string = ''
  ageTipContent: string = ''

  // 选取日期模式（pickup / dropoff）
  dateMode: string = 'pickup'

  gtmPageName: string = 'Car Rental Vertical Page'

  datepickerPlaceholder: string = ''

  searchParams: any = {}

  globalSearchParams: any = {}

  setQueryOnce: boolean = false

  globalCity: any = {}

  recommendedCity: any = {}

  spmVirtual: string = '__virtual'

  spmEntry: string = '__virtual?typ=entry'

  autocomplete_V3_city_id: any = null

  // TODO: 获取数据
  carmoreMention: any = null

  //
  showBoxShadow: boolean = false
  // 用来判断搜索框上面两个角的圆角样式情况
  showSearchFormRadiusLeftTop: boolean = false
  showSearchFormRadiusAbove: boolean = false

  urlParams: any =  {
    pick: '',
    drop: '',
    diffLoc: '',
    pDate: '',
    dDate: '',
    age: '',
    lat: '',
    long: '',
    dLat: '',
    dLong: '',
    code: '',
    dCode: '',
    iata: '',
    dIata: '',
    pCityId: '',
    pCityName: '',
    pPoiId: '',
    dCityId: '',
    dCityName: '',
    dPoiId: ''
  }

  completePlaceListV3: any = null

  cityId: any = null

  countryCode: any = null

  get showSearchInfoTip() {
    const { pickTip, dropTip, pickDateTip, dropDateTip } = this
    return pickTip.show || dropTip.show || pickDateTip.show || dropDateTip.show
  }

  get searchInfoTip() {
    const { pickTip, dropTip, pickDateTip, dropDateTip } = this
    if (pickTip.show) {
      return pickTip.content
    } else if (dropTip.show) {
      return dropTip.content
    } else if (pickDateTip.show) {
      return pickDateTip.content
    } else {
      return dropDateTip.content
    }
  }

  get searchInfoOffset() {
    const { pickTip, dropTip, pickDateTip, dropDateTip } = this
    let offset: number
    if (pickTip.show) {
      offset = pickTip.offset
    } else if (dropTip.show) {
      offset = dropTip.offset
    } else if (pickDateTip.show) {
      offset = pickDateTip.offset
    } else {
      offset = dropDateTip.offset
    }
    return `left: ${offset}px;`
  }

  // 用于计算日期 diff 的日期文本
  get pickUpDateTime() {
    const unit = this.globalPickUpDate
    if (unit.date && unit.hour && unit.minute) {
      return `${unit.date} ${unit.hour}:${unit.minute}`
    } else {
      return ''
    }
  }

  // 用于计算日期 diff 的日期文本
  get dropOffDateTime() {
    const unit = this.globalDropOffDate
    if (unit.date && unit.hour && unit.minute) {
      return `${unit.date} ${unit.hour}:${unit.minute}`
    } else {
      return ''
    }
  }

  // 多语言格式化后、仅供显示的日期文本
  get pickUpDateTimeText() {
    if (!this.pickUpDateTime) {
      return ''
    }
    return this.formatDate(this.pickUpDateTime)
  }

  // 多语言格式化后、仅供显示的日期文本
  get dropOffDateTimeText() {
    if (!this.dropOffDateTime) {
      return ''
    }
    return this.formatDate(this.dropOffDateTime)
  }

  get diffDay() {
    if (this.pickUpDateTime && this.dropOffDateTime) {
      const _p = this.pickUpDateTime.replace(/-/g, '/')
      const _d = this.dropOffDateTime.replace(/-/g, '/')
      const diff = dayjs(_d).diff(_p)
      const date = dayjs(_d).diff(_p, 'day')
      const d = diff % ONE_DAY > 0 ? date + 1 : date
      return (diff > 0 && d > 0) ? `${d} ` : ''
    }
    return ''
  }

  get pickUpHighlight() {
    return this.showPickUp && !this.showDropOff
  }

  get dropOffHighlight() {
    return this.showDropOff
  }

  // 切换国内海外时的key值
  get paramsKeyWord() {
    return 'global_keyword'
  }

  get paramsCity() {
    return 'global_city'
  }

  get currentSearchParams() {
    return this.globalSearchParams
  }

  get paramsCheckBox() {
    return 'global_value'
  }

  created() {
    this.ageList = this.createAgeList()
    this.between3065 = this.$t('car_rental_home_driver_between', ['30-65']) as string
    this.ageTipContent = this.$t('car_rental_home_driver_age_tip') as string
    this.dayDescContent = this.$t('car_rental_home_date_rule_mention') as string
    this.age.label = this.between3065
    this.globalAge.label = this.between3065
    this.pickTip.content = this.$t('car_rental_home_select_pick_tip') as string
    this.dropTip.content = this.$t('car_rental_home_select_drop_tip') as string
    this.pickDateTip.content = this.$t('car_rental_home_select_pick_date_tip') as string
    this.dropDateTip.content = this.$t('car_rental_home_select_drop_date_tip') as string
    this.initData()
    // const l = localStorageEx()
    // this.carmoreMention = l.getItem(CARMORE_MENTION)
    // this.ageList = this.createAgeList()
    // this.initDateTime()
    // this.datepickerPlaceholder = this.$t('car_rental_home_select_hint') as string
    // this.setCityId() // checkCode 依赖 this.cityId
    // getSourceCountryCode(this, this.checkCode)
    // this.getCategoryList()
  }

  beforeMount() {
    if (this.currentPage === 'result') {
      this.gtmPageName = 'Car Rental Search Result Page'
    } else if (this.currentPage === 'dlp') {
      this.gtmPageName = 'Car Rental Dynamic Landing Page'
    }
  }

  getDateObjByStorage() {
    const storage = localStorage.getItem('__car_rental__srp_search_data')
    if (!storage || this.currentPage !== 'home') {
      return {}
    }
    const obj = JSON.parse(storage)?.value || {}
    // 缓存日期过期
    if (dayjs(obj.pDate).isBefore(dayjs().add(1, 'hour'))) {
      return {}
    }
    const [pDate, pTime] = obj?.pDate.split(' ')
    const [pHour, pMinute] = pTime?.split(':')
    const [dDate, dTime] = obj?.dDate.split(' ')
    const [dHour, dMinute] = dTime?.split(':')
    const pDays = dayjs(pDate).diff(dayjs().format('YYYY-MM-DD'), 'day')
    const dDays = dayjs(dDate).diff(dayjs().format('YYYY-MM-DD'), 'day')
    return {
      pDays,
      pHour,
      pMinute,
      dDays,
      dHour,
      dMinute
    }
  }

  mounted() {
    const l = localStorageEx()
    this.carmoreMention = l.getItem(CARMORE_MENTION)
    this.ageList = this.createAgeList()
    const dateObj = this.getDateObjByStorage()
    this.initDateTime(dateObj)
    this.datepickerPlaceholder = this.$t('car_rental_home_select_hint') as string
    this.setCityId() // checkCode 依赖 this.cityId
    getSourceCountryCode(this, this.checkCode)
    this.getCategoryList()
  }

  // 根据聚合页L1和L2的数量来展示搜索框上面的圆角样式
  getCategoryList() {
    // 非聚合页需要添加阴影样式
    if (this.currentPage !== 'home') {
      this.showBoxShadow = true
    }
    // srp dlp页面圆角逻辑
    if (!this.categoryList.length) {
      this.showSearchFormRadiusLeftTop = false
      this.showSearchFormRadiusAbove = false
      return
    }
    // 如果L1列表数量小于等于1时，搜索框展示四个圆角
    if (this.categoryList && this.categoryList.length && this.categoryList.length <= 1) {
      this.showSearchFormRadiusLeftTop = false
      this.showSearchFormRadiusAbove = false
      this.getCategorySubList()
    } else {
      this.showSearchFormRadiusLeftTop = true
      this.getCategorySubList()
    }
  }

  getCategorySubList() {
    this.categoryList && this.categoryList.forEach((item:any) => {
      if (item.default_select) {
        if (item?.sub_category_list && item.sub_category_list.length && item.sub_category_list.length <= 1) {
          this.showSearchFormRadiusLeftTop = true
        } else {
          this.showSearchFormRadiusAbove = true
        }
      }
    })
  }

  @Watch('searchList.global_city')
  globalPickCityChange(val: string) {
    this.setSearchList(val, PICKUP, 'global_city', true)
  }

  @Watch('searchList.global_keyword')
  globalValuePickChange(val: string) {
    this.setSearchList(val, PICKUP, 'global_keyword', false)
  }

  @Watch('searchListDrop.global_city')
  globalDropCityChange(val: string) {
    this.setSearchList(val, DROPOFF, 'global_city', true)
  }

  @Watch('searchListDrop.global_keyword')
  globalValueChange(val: string) {
    this.setSearchList(val, DROPOFF, 'global_keyword', false)
  }

  @Watch('checkbox.global_value')
  globalCheckboxValueChange(val: boolean) {
    this.updateUrlParam({ diffLoc: val })
    if (!val) {
      this.isUrlDLoad = true
      this.searchListDrop.global_keyword = ''
      this.searchListDrop.global_city = ''
      this.skipShowSearchList()
      this.updateUrlParam({
        drop: '',
        dCityId: '',
        dCityName: '',
        dPoiId: ''
      })
    }
    this.dropTip.show = false
  }

  // TODO:
  @Watch('carmoreMention')
  mentionChange(val: any) {
    if (val) {
      this.globalMention = this.carmoreMention
    }
  }

  checkCode(countryCode: any) {
    console.log('countryCode', countryCode)
    if (countryCode) {
      this.countryCode = countryCode
      if (this.currentPage === 'result' && !this.setQueryOnce && !this.cityId) {
        console.log('currentPage', this.currentPage, this.setQueryOnce, this.cityId)
        this.setQueryData()
        this.setQueryOnce = true
      }
      // 没有值则请求配置默认值,在有相关配置下，默认回显示某个客源国下面的热门城市
      // if (this.currentPage !== 'dlp' && !this.currentSearchParams.pick && !this.cityId) {
      //   this.getHomeConfig({ source_country_code: countryCode }).then((res: any) => {
      //     if (res && res.success) {
      //       this.setSearchConfig(res.result)
      //     }
      //   })
      // }
      // 获取全量洲/国家/城市
      this.getInternationalCity({ source_country_code: countryCode }).then((res: any) => {
        if (res && res.success) {
          this.globalCity = res.result
        }
      })
    }
  }
  
  // 解析URL city_id,默认选中poi
  setCityId() {
    const q: any = this.$route.query
    if (q.city_id) {
      this.cityId = q.city_id
      this.getPoiByUrlCityId(this.cityId)
        .then((res: any) => {
          this.setPlaceCommon(res.result, PICKUP, false)
        })
    }
  }

  // 根据city_id获取poi,接口返回最优poi
  getPoiByUrlCityId(city_id: any) {
    return this._axios
      .$get('/v1/transfercarrentalapisrv/poi/hot_city', { params: { city_id } })
      .then((res: Data.Res) => {
        return Promise.resolve(res)
      })
      .catch((error: any) => {
        return Promise.reject(error)
      })
  }

  // 点击获取google poi 数据
  getGooglePoiData({ placeId }: any) {
    return this._axios
      .$get('/v1/transfercarrentalapisrv/poi/detail_V3', { params: { placeId } })
      .then((res: Data.Res) => {
        return Promise.resolve(res)
      })
      .catch((error: any) => {
        return Promise.reject(error)
      })
  }

  // 选中city后获取poi list
  getCityPoiList({ city_id }: any) {
    return this._axios
      .$get('/v1/transfercarrentalapisrv/poi/get_city_poi', { params: { city_id } })
      .then((res: Data.Res) => {
        return Promise.resolve(res)
      })
      .catch((error: any) => {
        return Promise.reject(error)
      })
  }

  // 获取全量洲/国家/城市
  getInternationalCity({ source_country_code }: any) {
    return this._axios
      .$get('/v1/transfercarrentalapisrv/poi/overseas_city', { params: { source_country_code } })
      .then((res: Data.Res) => {
        return Promise.resolve(res)
      })
      .catch((error: any) => {
        return Promise.reject(error)
      })
  }

  // 获取默认配置poi
  // getHomeConfig({ source_country_code }: any ) {
  //   return this._axios
  //     .$get('/v1/transfercarrentalapisrv/poi/home_config', { params: { source_country_code } })
  //     .then((res: Data.Res) => {
  //       return Promise.resolve(res)
  //     })
  //     .catch((error: any) => {
  //       return Promise.reject(error)
  //     })
  // }

  // poi搜索
  getLocationCompleteV3({ is_domestic, search_word, source_country_code, city_id }: any) {
    return this._axios
      .$get('/v1/transfercarrentalapisrv/poi/autocomplete_V3', { params: { is_domestic, search_word, source_country_code, city_id } })
      .then((res: Data.Res) => {
        if (res.success && res.result) {
          const data = res.result
          this.completePlaceListV3 = data
        } else {
          this.completePlaceListV3 = {}
        }
        return Promise.resolve(search_word)
      })
      .catch(() => {
        this.completePlaceListV3 = {}
      })
  }

  getModuleFormName(moduleName: string, value?: string) {
    // const query = `?evt=click&ext=${JSON.stringify({ LocalOrGlobal: value })}`
    const query = `?evt=click`
    return `${moduleName}${query}`
  }

  // 设置配置取还车值
  setSearchConfig(data: any) {
    if (data && Array.isArray(data.tabs)) {
      data.tabs.forEach((tab: any) => {
        const item = tab.poi_info || {}
        this.setPlaceCommon(item, PICKUP, false)
      })
    }
  }

  // 更新日期范围
  updateDateRange(date: string[]) {
    this.globalPickUpDate.date = date[0]
    this.globalDropOffDate.date = date[1]
    this.updateUrlParam({
      pDate: this.pickUpDateTime,
      dDate: this.dropOffDateTime
    })
  }

  // 更新取还车时间
  updateDateTime(type: string, time: any) {
    const keyPick = 'globalPickUpDate'
    const keyDrop = 'globalDropOffDate'
    if (type === 'pickup') {
      this[keyPick].hour = time.hour
      this[keyPick].minute = time.minute
    } else {
      this[keyDrop].hour = time.hour
      this[keyDrop].minute = time.minute
    }
    this.updateUrlParam({
      pDate: this.pickUpDateTime,
      dDate: this.dropOffDateTime
    })
  }

  closeDateTimePicker() {
    if (this.pickUpDateTime && this.dropOffDateTime) {
      this.showPickUp = false
      this.showDropOff = false
    }
    if (this.pickUpDateTime && !this.dropOffDateTime && this.showPickUp && this.showDropOff) {
      const keyDrop = 'globalDropOffDate'
      this[keyDrop].date = dayjs(this.globalPickUpDate.date).add(3, 'day').format('YYYY-MM-DD')
      this.updateUrlParam({
        dDate: this.dropOffDateTime
      })
      this.showPickUp = false
      this.showDropOff = false
    }
  }

  initData() {
    this.pickUpTimeText = String(this.$t('car_rental_home_pick_up_date'))
    this.dropOffTimeText = String(this.$t('car_rental_home_drop_off_date'))
    this.pickUpDesText = String(this.$t('car_rental_home_pick_drop_loc_long'))
    this.pickUpFloatText = String(this.$t('car_rental_home_pick_drop_loc_short')) // short
    this.dropOffDesText = String(this.$t('car_rental_home_pick_drop_loc_long'))
    this.dropOffFloatText = String(this.$t('car_rental_home_drop_off_loc')) // short
  }

  setUrlParams(params: any) {
    const _urlParams = Object.assign(this.urlParams, params)
    return Promise.resolve(_urlParams)
  }
  initDateTime(dateObj?: any) {
    let addPickDate = 5, addDropDate = 8
    // 加个判断，避免把pDays=0或者dDays=0的情况过滤了
    if (dateObj?.pDays !== undefined && dateObj?.pDays !== null && typeof Number(dateObj.pDays) === 'number') {
      addPickDate = Number(dateObj.pDays)
    }
    if (dateObj?.dDays !== undefined && dateObj?.dDays !== null && typeof Number(dateObj.dDays) === 'number') {
      addDropDate = Number(dateObj.dDays)
    }
    const pick = {
      date: dayjs().add(addPickDate, 'day').format('YYYY-MM-DD'),
      hour: dateObj?.pHour || '10',
      minute: dateObj?.pMinute || '00'
    }
    const drop = {
      date: dayjs().add(addDropDate, 'day').format('YYYY-MM-DD'),
      hour: dateObj?.dHour || '10',
      minute: dateObj?.dMinute || '00'
    }
    this.globalPickUpDate = Object.assign({}, pick)
    this.globalDropOffDate = Object.assign({}, drop)
    this.updateUrlParam({
      pDate: this.pickUpDateTime,
      dDate: this.dropOffDateTime
    })
  }

  initCarMoreTime() {
    const pick = {
      date: this.globalPickUpDate.date,
      hour: '10',
      minute: '00'
    }
    const drop = {
      date: this.globalDropOffDate.date,
      hour: '10',
      minute: '00'
    }
    this.globalPickUpDate = pick
    this.globalDropOffDate = drop
    this.updateUrlParam({
      pDate: this.pickUpDateTime,
      dDate: this.dropOffDateTime
    })
  }

  setQueryData() {
    if (this.$route && this.$route.query &&
      Object.getOwnPropertyNames(this.$route.query).length > 0) {
      const q: any = this.$route.query
      const age = this.ageList.find((a: any) => a.value === q.age)
      if (age) {
        this.setAge(age, false)
      }
      // 若设置相对日期，则替换取还车时间为当前 + 相对日期区间
      if (q.dypdate && !isNaN(Number(q.dypdate))) {
        const dyp = Number(q.dypdate)
        q.pDate = `${dayjs().add(dyp, 'day').format('YYYY-MM-DD')} 10:00`
        q.dDate = `${dayjs().add((dyp + 3), 'day').format('YYYY-MM-DD')} 10:00`
      }
      if (q.pDate) {
        // 判断affiliate链接的取车时间是否小于当前时间
        if ((q.aid || q.adid) && dayjs(q.pDate).isBefore(dayjs().add(1, 'hour'))) {
          this.initDateTime()
        } else {
          const d = q.pDate.split(' ')
          if (Array.isArray(d) && d.length > 1) {
            const c = d[1].split(':')
            this.updatePickUpTime({
              date: d[0],
              hour: c[0],
              minute: c[1]
            })
          }
        }
      }
      if (q.dDate) {
        const d = q.dDate.split(' ')
        if (Array.isArray(d) && d.length > 1) {
          const c = d[1].split(':')
          this.updateDropOffTime({
            date: d[0],
            hour: c[0],
            minute: c[1]
          })
        }
      }
      console.log('123', q)
      if (q.diffLoc === 'true') {
        this.checkbox.global_value = true
        this.showDropOffDesInput()
      } else {
        this.checkbox.global_value = false
      }

      if (!q.drop) {
        this.isUrlDLoad = true
      } else if (q.drop && q.diffLoc === 'true') {
        const place = {
          latitude: q.dLat,
          longitude: q.dLong,
          country_code: q.dCode,
          iata_code: q.dIata,
          poi_name: q.drop,
          poi_id: q.dPoiId,
          city_name: q.dCityName,
          city_id: q.dCityId
        }
        this.setPlaceCommon(place, DROPOFF, false)
      }
      if (q.pick) {
        const place = {
          latitude: q.lat,
          longitude: q.long,
          country_code: q.code,
          iata_code: q.iata,
          poi_name: q.pick,
          poi_id: q.pPoiId,
          city_name: q.pCityName,
          city_id: q.pCityId
        }
        this.setPlaceCommon(place, PICKUP, false)
      }
    }
  }

  createAgeList() {
    const ageList:any = []
    for (let i = 18; i <= 99; i++) {
      const o = {
        value: String(i),
        label: String(i)
      }
      if (i < 30) {
        ageList.push(o)
      } else if (i === 30) {
        ageList.push({
          value: '30', // xxx: very bad..
          label: this.between3065
        })
      } else if (i > 65) {
        ageList.push(o)
      }
    }
    return ageList
  }

  debounceGetLocationComplete = debounce(this.getLocationComplete, 200)

  getCompleteListByWord(word: string, type: string, is_city: boolean) {
    this.showSearchListByType(type, is_city)
    const params = {
      is_domestic: 0,
      search_word: word,
      source_country_code: this.countryCode,
      city_id: this.autocomplete_V3_city_id
    }
    this.debounceGetLocationComplete(params, word)
  }

  getLocationComplete(params: any, word: string) {
    this.getLocationCompleteV3(params)
      .then((keyword: any) => {
        if (keyword === word) {
          this.searchList.loading = false
          this.searchListDrop.loading = false
        } else {
          this.completePlaceListV3 = {}
        }
      })
      .catch((keyword: any) => {
        if (keyword === word) {
          this.searchList.loading = false
          this.searchListDrop.loading = false
        } else {
          this.completePlaceListV3 = {}
        }
      })
  }

  showSearchListByType(type: string, is_city: boolean) {
    if (type === SEARCHLIST) {
      this.searchList.is_city = is_city
      this.searchList.show = true
      this.searchList.offset = 20
      this.searchList.loading = true
    } else {
      this.searchListDrop.is_city = is_city
      this.searchListDrop.show = true
      this.searchListDrop.offset = 315
      this.searchListDrop.loading = true
    }
  }

  updatePickUpTime(unit: any) {
    this.globalPickUpDate = unit
    this.showPickUp = false
    this.pickDateTip.show = false
    this.updateUrlParam({ pDate: `${unit.date} ${unit.hour}:${unit.minute}` })
  }

  updateDropOffTime(unit: any) {
    this.globalDropOffDate = unit
    this.showDropOff = false
    this.dropDateTip.show = false
    this.updateUrlParam({ dDate: `${unit.date} ${unit.hour}:${unit.minute}` })
  }

  async setPlaceCommon(place: any, type: string, setHistory: boolean) {
    if (place?.google_poi && place?.place_id) {
      const {
        result: {
          latitude = '',
          longitude = '',
          city_name = '',
          country_code = '' // autocomplete_V3 接口 与 detail_V3 接口返回 city_name 不一致，以detail_V3为准
        }
      } = await this.getGooglePoiData({ placeId: place.place_id })
      place = Object.assign(place, { latitude, longitude, city_name, country_code })
    }
    this.isSelectedValue = true
    this.changeTab(type)
    const searchProp = type === PICKUP ? SEARCHLIST : SEARCHLISTDROP
    const locationProp = type === PICKUP ? 'loactionListProp' : 'loactionListDropProp'
    this[searchProp][this.paramsKeyWord] = place?.poi_name || ''
    this[searchProp][this.paramsCity] = place?.city_name || ''
    this[searchProp].show = false
    this[locationProp].show = false
    if (type === PICKUP) {
      this.updateUrlParam({
        pick: place?.poi_name || '',
        lat: place?.latitude || '',
        long: place?.longitude || '',
        code: place?.country_code || '',
        iata: place?.iata_code || '',
        pCityId: place?.city_id || '',
        pCityName: place?.city_name || '',
        pPoiId: place?.poi_id || ''
      })
      this.pickTip.show = false
    } else {
      this.updateUrlParam({
        drop: place?.poi_name || '',
        dLat: place?.latitude || '',
        dLong: place?.longitude || '',
        dCode: place?.country_code || '',
        dIata: place?.iata_code || '',
        dCityId: place?.city_id || '',
        dCityName: place?.city_name || '',
        dPoiId: place?.poi_id || ''
      })
      this.dropTip.show = false
    }

    // 设置搜索结果
    if (setHistory && place?.poi_name) {
      this.setSearchHistory(place)
    }
    // 只选择城市，则打开poi选择
    if (place?.city_name && !place?.poi_name) {
      // 选择城市则填城市，展开城市列表
      this.showLocation(type, false)
    }
    // 选的是一个poi + 城市的组合
    if (place?.city_name && place?.poi_name) {
      this.skipShowSearchList()
    }
    // 兼容旧url
    if (!place?.poi_id && !place?.city_name && place?.poi_name) {
      this.skipShowSearchList()
    }
  }

  skipShowSearchList() {
    this.selectedSkip = true
    setTimeout(() => {
      this.selectedSkip = false
    }, 500)
  }

  sendPalceComplateTrack(mixpanelObj: any, key: string, value: string) {
    this.$sendMixpanel(mixpanelObj)
    this.$sendGTMCustomEvent(`${this.gtmPageName}|${mixpanelObj.name}|${key}|${value}`)
  }

  setAge(item: any, sendTrack: boolean) {
    let value = '30'
    this.globalAge.label = item.label
    value = this.globalAge.value = item.value
    this.showAgeSelect = false
    const mixpanelAge = value
    if (sendTrack) {
      this.$sendMixpanel({
        name: 'Search_Search_DriversAge_Click',
        props: { 'Driver Age': mixpanelAge }
      })
      this.$sendGTMCustomEvent(`${this.gtmPageName}|Search_Search_DriversAge_Click|DriverAge|${mixpanelAge}`)
    }
    this.updateUrlParam({ age: value })
  }

  showDropOffDesInput() {
    if (this.checkbox.global_value) {
      this.globalDesWidth = 286
      this.pickUpFloatText = String(this.$t('car_rental_home_pick_up_loc'))
    } else {
      this.globalDesWidth = 582
      this.pickUpFloatText = String(this.$t('car_rental_home_pick_drop_loc_short'))
    }
  }

  hideAgeListStatus() {
    this.showAgeSelect = false
  }

  closeLocationList(type: string) {
    if (type === PICKUP) {
      this.loactionListProp.show = false
    } else {
      this.loactionListDropProp.show = false
    }
  }

  showLocation(type: string, is_city: boolean) {
    const city_id = type === PICKUP ? this.currentSearchParams.pCityId : this.currentSearchParams.dCityId
    if (type === PICKUP) {
      this.loactionListProp.offset = 20
      this.loactionListProp.is_city = is_city
      this.loactionListProp.city_id = city_id ? String(city_id) : 'NAN'
      this.loactionListProp.show = true
    } else {
      this.loactionListDropProp.offset = 315
      this.loactionListDropProp.is_city = is_city
      this.loactionListDropProp.city_id = city_id ? String(city_id) : 'NAN'
      this.loactionListDropProp.show = true
    }
    if (!is_city) {
      this.getCityPoi(type, is_city)
    }
  }

  // 根据选中city获取city下poi
  getCityPoi(type: string, is_city: boolean) {
    this.recommendedCity = {}
    const city_id = type === PICKUP ? this.currentSearchParams.pCityId : this.currentSearchParams.dCityId
    const city = type === PICKUP ? this.searchList[this.paramsCity] : this.searchListDrop[this.paramsCity]
    const currentCity = type === PICKUP ? this.currentSearchParams.pCityName : this.currentSearchParams.dCityName
    if ((city === currentCity) && city_id) {
      this.getCityPoiList({ city_id }).then((res: any) => {
        if (res && res.success) {
          this.recommendedCity = res.result
          if (type === PICKUP) {
            this.loactionListProp.offset = 20
            this.loactionListProp.is_city = is_city
            this.loactionListProp.city_id = city_id ? String(city_id) : 'NAN'
            this.loactionListProp.show = true
          } else {
            this.loactionListDropProp.offset = 315
            this.loactionListDropProp.is_city = is_city
            this.loactionListDropProp.city_id = city_id ? String(city_id) : 'NAN'
            this.loactionListDropProp.show = true
          }
        }
      })
    }
  }

  closeSearchListByType(type: string) {
    if (type === PICKUP) {
      this.searchList.show = false
    } else {
      this.searchListDrop.show = false
    }
  }

  updateUrlParam(data: any) {
    if (this.updateUrl) {
      this.setUrlParams(data).then((query) => {
        this.$router.push({ query, replace: true })
      })
    } else {
      this.setSearchParams(data)
    }
    this.$emit('updateUrlParam', data)
  }

  setSearchParams(data: any) {
    this.globalSearchParams = Object.assign(this.globalSearchParams, data)
  }

  timeLeftEmit(e: boolean) {
    this.timeLeftChange = e
  }

  search() {
    // 聚合页搜索历史记录，记录是哪个tab
    this.$emit('handle-add-history')
    const q = this.updateUrl ? this.$route.query : this.currentSearchParams
    // check pick up location
    if (!this.searchList[this.paramsKeyWord] || (this.searchList[this.paramsKeyWord] !== q.pick)) {
      this.pickTip.show = true
      setTimeout(() => {
        this.pickTip.show = false
      }, 3000)
      return
    }
    // check drop off location
    if (this.checkbox[this.paramsCheckBox] && (!this.searchListDrop[this.paramsKeyWord] ||
      (this.searchListDrop[this.paramsKeyWord] !== q.drop))) {
      this.dropTip.show = true
      setTimeout(() => {
        this.dropTip.show = false
      }, 3000)
      return
    }
    // check pick up date & time or set default value
    if (!this.pickUpDateTime) {
      this.pickDateTip.show = true
      setTimeout(() => {
        this.pickDateTip.show = false
      }, 3000)
      return
    } else if (!q.pDate) {
      q.pDate = this.pickUpDateTime
      this.setUrlParams({
        pDate: this.pickUpDateTime
      })
    }
    // check drop off date & time or set default value
    if (!this.dropOffDateTime) {
      this.dropDateTip.show = true
      setTimeout(() => {
        this.dropDateTip.show = false
      }, 3000)
      return
    } else if (!q.dDate) {
      q.dDate = this.dropOffDateTime
      this.setUrlParams({
        dDate: this.dropOffDateTime
      })
    }
    // check age
    if (q && (Number(q.age) <= 17 || Number(q.age) > 99)) {
      q.age = '30'
    }

    let pickDate = q.pDate
    let dropDate = q.dDate
    try {
      pickDate = new Date(String(pickDate)).toISOString()
      dropDate = new Date(String(dropDate)).toISOString()
    } catch (error) {
      //
    }

    this.$sendMixpanel({
      name: 'Search_Search_Button_Click',
      props: {
        'Search Pickup Location': q.pick,
        'Search Dropoff Location': q.diffLoc ? q.drop : q.pick,
        'Is Return Different Location': q.diffLoc ? 'TRUE' : 'FALSE',
        'Search Pickup Date & Time': pickDate,
        'Search Dropoff Date & Time': dropDate,
        'Search Driver Age': q.age ? q.age : '30-65',
        'Vertical Type': 'Car Rental'
      }
    })

    this.$sendGTMCustomEvent(`${this.gtmPageName}|Search_Search_Button_Click`)
    // @Prop({ type: Boolean, default: true }) updateUrl!: boolean
    // @Prop({ type: String, default: '' }) currentPage!: string

    // 聚合页为组件内处理search事件，其他都是组件调用的地方处理
    if (this.currentPage === 'home') {
      this.homePageSearch(q)
    } else {
      this.$emit('search', q)
    }
  }

  // 默认聚合页搜错处理方法
  homePageSearch(data: any) {
    const paths = location.href.split('car-rentals')
    let search = '?'
    for (const key in data) {
      search += `${key}=${data[key]}&`
    }
    const url = `${paths[0]}car-rentals/results/${search.slice(0, -1)}`
    window.open(url, '_blank')
  }

  // poi dlp 页面search 方法
  poiDlpSearch(event: any) {
    this.$router.push({
      name: 'CarRentalResults',
      query: event
    })
  }

  formatDate(date: any) {
    const type = 6

    return getStandardDateFormat(date, this.$t.bind(this), this.curLocale, type)
  }

  sendSearchGTM(val: string) {
    return `${this.gtmPageName}|${val}`
  }

  changeTab(type?: string) {
    this.pickTip.show = false
    this.dropTip.show = false
    if (type) {
      const searchProp = type === PICKUP ? SEARCHLIST : SEARCHLISTDROP
      if (this[searchProp].show) {
        const t = type as string
        this.closeSearchListByType(t)
      }
    }
    this.showDropOffDesInput()
  }

  setSearchHistory(item: any) {
    const l = localStorageEx()
    const key = carRentalGlobalHistory
    const history_list = Array.isArray(l.getItem(key)) ? l.getItem(key) : []
    history_list.unshift(item)
    const list = history_list.filter((v: any, i: any, a: any) => a.findIndex((t: any) => (t.poi_id && v.poi_id && (t.poi_id === v.poi_id)) || (!t.poi_id && !v.poi_id && (t.poi_name === v.poi_name))) === i).slice(0, 3)
    l.setItem(key, list, ONE_DAY)
  }

  setSearchList(val: any, type: string, key: string, is_city: boolean) {
    const searchProp = type === PICKUP ? SEARCHLIST : SEARCHLISTDROP
    const locationProp = type === PICKUP ? 'loactionListProp' : 'loactionListDropProp'
    this.autocomplete_V3_city_id = type === PICKUP ? this.currentSearchParams.pCityId : this.currentSearchParams.dCityId

    // poi 和 city是一个接口，只有搜索poi才需要传city_id
    if (is_city) {
      this.autocomplete_V3_city_id = null
    }
    // typing 清空两种情，一种是点击外部清空
    if (!val) {
      if (!this.isUrlDLoad && !this.isSelectedValue && !this.selectedSkip) {
        this[locationProp].show = true
      } else {
        this.isUrlDLoad = false
        this.isSelectedValue = false
      }
      this[searchProp].show = false
    } else {
      // 有值的两种情况，typing 、选中一个选项（location、complete search）
      if (!this.isSelectedValue && !this.selectedSkip) {
        this.getCompleteListByWord(val, searchProp, is_city)
        this.isSelectedValue = false
        this[locationProp].show = false
      } else {
        this.isSelectedValue = false
      }
      this[searchProp][key] = val
    }
  }

    get galileoClick1() {
        return { spm: WebClick_121, enforce: 'post', autoTrackSpm: true }
    }

    get galileoClick2() {
        return { spm: WebClick_122, autoTrackSpm: true }
    }

    get galileoClick3() {
        return { spm: WebClick_123, autoTrackSpm: true }
    }

    get galileoClick4() {
        return { spm: WebClick_124, autoTrackSpm: true }
    }

    get galileoClick5() {
        return { spm: WebClick_125, autoTrackSpm: true }
    }

    get galileoClick6() {
        return { spm: WebClick_126, autoTrackSpm: true }
    }

    get galileoClick7() {
        return { spm: WebClick_127, autoTrackSpm: true }
    }

    get galileoClick8() {
        return { spm: WebClick_128, autoTrackSpm: true }
    }
}
</script>

<style lang="scss" module>
$content-width: 1160px;
$position-top: 164px;

.container {
  position: relative;
  z-index: 7;
  margin: 0 auto;
  width: $content-width;
}
.wrapper {
  width: $content-width;
  border-radius: $radius-l;
  background-color: $color-bg-widget-normal;
  padding: 20px;
  // box-shadow: $shadow-normal-5;
  margin: 0 auto;
}

.checkbox {
  margin-bottom: 20px;
  display: flex;
  align-items: center;

  input {
    width: 16px;
    height: 16px;
    border-radius: $radius-s;
    border: solid 1px $color-border-normal;
  }

  span {
    min-width: 16px;
    margin: 0 4px 0 0 !important;
  }
}

.inputRight {
  margin-left: 10px;
}

.form {
  display: flex;
  flex-wrap: nowrap;
  position: relative;

  .marginLeft {
    margin-left: 10px;
  }

  .search {
    margin-left: 10px;
    width: 108px;
    height: 58px;
  }

  .day {
    position: absolute;
    top: 50%;
    right: 301px;
    transform: translateY(-50%);
    border-radius: $radius-xl;
    border: 1px solid $color-info;
    padding: 4px 13px;
    color: $color-info;
    background-color: $color-bg-widget-normal;
    font-size: $fontSize-caption-m;
    line-height: 12px;
    display: flex;
    justify-content: center;

    :global(.klk-poptip-popper-inner) {
      margin: 12px 0;
      padding: 0 16px;
    }
    &::before {
      content: '';
      width: 4px;
      height: 1px;
      background-color: #4985e6;
      position: absolute;
      top: 50%;
      left: -4px;
      transform: translateY(-50%);
    }
    &::after {
      content: '';
      width: 4px;
      height: 1px;
      background-color: #4985e6;
      position: absolute;
      top: 50%;
      right: -4px;
      transform: translateY(-50%);
    }
  }

  .otherInfo {
    position: absolute;
    top: 10px;
    transform: translateY(-100%);
    padding: 12px;
    background: $color-common-black;
    color: $color-text-primary-onDark;
    font-size: $fontSize-caption-m;
    line-height: 1.5;
    border: solid 1px $color-border-active;
    border-radius: $radius-s;

    &::before {
      content: '';
      position: absolute;
      bottom: -8px;
      left: 28px;
      width: 0;
      height: 0;
      border-left: 10px solid transparent;
      border-right: 10px solid transparent;
      border-top: 9.6px solid $color-common-black;
    }
  }
}

.age {
  display: flex;
  align-items: center;
  margin-top: 20px;
  font-size: $fontSize-caption-m;

  .ageTitle {
    display: flex;
    align-items: center;

    .ageIcon {
      height: 16px;
      width: 16px;

      .tips_icon {
        color: #4a4a4a
      }
    }

    >span {
      margin-right: 3px;
      line-height: 20px;
    }

    .ageSelector {
      margin-left: 3px;
    }

    .ageDot {
      margin-left: 3px;
    }
  }

  :global(.klk-poptip-popper-inner) {
    margin: 12px 0;
    padding: 0 16px;
  }
}

.datePicker {
  position: absolute;
  left: 480px;
  z-index: 2;
  perspective: 1px;
}

.pickup {
  position: absolute;
  left: 550px;
  z-index: 2;
  perspective: 1px;
}

.locationList {
  position: absolute;
  left: 24px;
  z-index: 2;
  top: 124px;
}

.searchList {
  position: absolute;
  left: 24px;
  z-index: 2;
  top: 124px;
}

.right_border_radius {
  border-radius: 8px 0 0 8px !important;
}
.left_border_radius {
  border-radius: 0 8px 8px 0 !important;
}
</style>

<style lang="scss" scoped>
.radius-left-top {
  border-radius: 0 $radius-l $radius-l $radius-l;
}

.radius-above {
  border-radius: 0 0 $radius-l $radius-l;
}

.bottom-shadow {
  box-shadow: $shadow-normal-5;
}

</style>
