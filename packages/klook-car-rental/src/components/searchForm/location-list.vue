<template>
  <div
    v-if="showList"
    ref="listContentRef"
    v-click-outside="close"
    :data-spm-page="spmPage"
    class="list-container"
    :style="{'left': left + 'px'}"
  >
    <div class="left">
      <div
        v-for="(item, index) in navList"
        :key="index"
        class="area-item"
        :class="{active: index === activeGroupIndex}"
        @click="scrollToView(item, index)"
      >
        <div>{{ item.nav_name ? item.nav_name : item.name }}</div>
      </div>
    </div>
    <div class="right">
      <div
        ref="areaContent"
        class="area-content"
        @scroll="handleScroll()"
      >
        <div class="scroll-wrapper">
          <div
            v-for="(item, c_index) in locationList"
            :key="c_index"
            ref="areaItem"
            class="city-item"
            :class="getClassName(item)"
          >
            <div class="city-name-warp">
              <div class="city-name">{{ item.title }}</div>
              <img
                v-if="item.is_history"
                class="search-start_history-delete"
                width="16"
                height="16"
                data-spm-module="ClearHistory"
                v-galileo-click-tracker="galileoClick1"
                :data-spm-virtual-item="spmVirtual"
                src="https://res.klook.com/image/upload/web3.0/icon-remove_axnprh.svg"
                @click="clearHistory"
              />
            </div>
            <div class="city-place">
              <template
                v-for="(child_item, a_index) in item.list"
              >
                <div
                  v-if="child_item.city_name || child_item.poi_name"
                  :key="a_index"
                  class="place-item city-place-item"
                  :data-spm-module="getSpmModule(child_item, a_index, item.list.length, item.buried_pointed_desc)"
                  v-galileo-click-tracker="galileoClick2"
                  :data-spm-virtual-item="spmVirtual"
                  @click="setPlace({...child_item, index: a_index + 1})"
                >
                  <img v-if="child_item.image_url" :src="child_item.image_url">
                  {{ child_item.poi_name ? child_item.poi_name : child_item.city_name }}
                </div>
              </template>
            </div>
          </div>
        </div>
      </div>
      <div :style="{'opacity': `${showScrollTip ? 1 : 0}`, 'transform': `translateY(${showScrollTip ? '0%' : '100%'})`}" class="scroll-tip">
        {{ $t('car_rental_home_scroll_down') }}
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Prop } from 'vue-property-decorator'
import { CarRental } from '../../types/car-rental'
import localStorageEx from '../../common/localstorage'
import Base from "../../common/base";
import { WebClick_131, WebClick_132 } from '../../../share/galileo/auto-click'

const carRentalGlobalHistory = 'carRentalGlobalHistory'
const localStorage = localStorageEx()
const SEARCH_HISTORY_KEY = 'car_rental_POI_search_history_17805'
const SEARCH_HISTORY_DESC = 'SearchHistory_LIST'
const DOMESTIC_DESC = 'DomesticCities'
const POPULAR_CITY_DESC = 'PopularCity_LIST'

@Component
export default class LocationList extends Base {
  @Prop({ type: Number, default: 24 }) left!: Number // 热门城市边框边距
  @Prop({ type: Object, default: () => {} }) globalCity!: any
  @Prop({ type: Object, default: () => {} }) recommendedCity!: any
  @Prop({ type: Boolean, default: false }) isCity!: Boolean
  @Prop({ type: Boolean, default: false }) show!: Boolean
  @Prop({ type: String, default: '' }) cityId!: String

  activeGroupIndex = 0 // 当前
  selectedPlace!: CarRental.Place
  contentObserver: any = null
  observerItem: any = null
  showScrollTip: boolean = true
  historyList: any = []
  skipScroll: boolean = false
  spmVirtual: string = '__virtual'
  spmPage: string = 'CarRental_Location_POI?trg=manual'
  spmUpdated: boolean = false

  get cityList() {
    const data = this.globalCity
    const list = [] as any[]
    if (Array.isArray(this.historyList) && this.historyList.length > 0) {
      const item = {
        nav_name: this.$t('17805-history'),
        title: this.$t('17805-history'),
        list: [] as any[],
        is_history: true,
        key: SEARCH_HISTORY_KEY,
        buried_pointed_desc: SEARCH_HISTORY_DESC
      }
      this.historyList.forEach((history: any) => {
        item.list.push(history)
      })
      list.push(item)
    }
    if (data && Array.isArray(data.popular_city_list) && data.popular_city_list.length > 0) {
      const item = {
        nav_name: data.popular_city_list_title,
        title: data.popular_city_list_title,
        list: [] as any[],
        key: data.popular_city_list_key,
        buried_pointed_desc: POPULAR_CITY_DESC
      }
      data.popular_city_list.forEach((city: any) => {
        item.list.push(city)
      })
      list.push(item)
    }
    if (data && Array.isArray(data.continent_country_city_list)) {
      data.continent_country_city_list.forEach((continent: any) => {
        if (Array.isArray(continent.country_list)) {
          continent.country_list.forEach((country: any) => {
            const item = {
              nav_name: continent.continent_name,
              title: '',
              list: [] as any[],
              key: continent.continent_key,
              buried_pointed_desc: DOMESTIC_DESC
            }
            item.title = country.country_name
            if (Array.isArray(country.city_list)) {
              country.city_list.forEach((city: any) => {
                item.list.push(city)
              })
            }
            list.push(item)
          })
        }
      })
    }
    return list
  }

  get cityNavList() {
    const data = this.globalCity
    const list = [] as any[]
    if (Array.isArray(this.historyList) && this.historyList.length > 0) {
      list.push({ nav_name: this.$t('17805-history'), key: SEARCH_HISTORY_KEY, buried_pointed_desc: SEARCH_HISTORY_DESC })
    }
    if (data && Array.isArray(data.popular_city_list) && data.popular_city_list.length > 0) {
      list.push({ nav_name: data.popular_city_list_title, key: data.popular_city_list_key, buried_pointed_desc: POPULAR_CITY_DESC })
    }
    if (data && Array.isArray(data.continent_tag_list)) {
      data.continent_tag_list.forEach((tag: any) => {
        list.push({ ...tag, buried_pointed_desc: DOMESTIC_DESC })
      })
    }
    return list
  }

  get cityPoiList() {
    const list = [] as any[]
    const data = this.recommendedCity
    if (Array.isArray(this.historyList) && this.historyList.length > 0) {
      const item = {
        nav_name: this.$t('17805-history'),
        title: this.$t('17805-history'),
        list: [] as any[],
        is_history: true,
        key: SEARCH_HISTORY_KEY,
        buried_pointed_desc: SEARCH_HISTORY_DESC
      }
      this.historyList.forEach((history: any) => {
        item.list.push(history)
      })
      list.push(item)
    }
    if (data && Array.isArray(data.recommended_poi_info_list) && data.recommended_poi_info_list.length > 0) {
      data.recommended_poi_info_list.forEach((recommended: any) => {
        const item = {
          nav_name: recommended.title,
          title: recommended.title,
          buried_pointed_desc: recommended.buried_pointed_desc,
          list: [] as any[],
          key: recommended.key
        }
        if (Array.isArray(recommended.poi_info_list)) {
          recommended.poi_info_list.forEach((poi: any) => {
            item.list.push(poi)
          })
        }
        list.push(item)
      })
    }
    return list
  }

  get locationList() {
    return this.isCity ? this.cityList : this.cityPoiList
  }

  get navList() {
    return this.isCity ? this.cityNavList : this.cityPoiList
  }

  get showList() {
    const list = this.navList && this.locationList
    if (list && list.length > 0) {
      this.$nextTick(() => {
        if (!this.spmUpdated) {
          this.spmUpdated = true
          // this.updateSpm()
        }
      })
    }
    return Array.isArray(this.navList) && Array.isArray(this.locationList) && this.navList.length > 0 && this.locationList.length > 0
  }

  mounted() {
    const key = carRentalGlobalHistory
    if (Array.isArray(localStorage.getItem(key))) {
      this.historyList = localStorage.getItem(key)
    }
    this.createObserver()
  }

  updated() {
    if (this.contentObserver && this.observerItem) {
      this.contentObserver.unobserve(this.observerItem)
    }
    this.createObserver()
  }

  beforeDestory() {
    if (this.contentObserver) {
      this.observerItem && this.contentObserver.unobserve(this.observerItem)
      this.contentObserver.disconnect()
    }
    this.observerItem = null
    this.contentObserver = null
  }

  setPlace(place: CarRental.Place) {
    this.selectedPlace = place
    this.$emit('setPlace', place)
  }

  close() {
    this.$emit('close')
  }

  handleScroll() {
    if (!this.skipScroll && this.$refs && this.$refs.areaContent) {
      const el = this.$refs.areaContent as HTMLElement
      if (el && Array.isArray(this.navList)) {
        const targets = [] as any[]
        this.navList.forEach((item: any) => {
          const key = this.getClassName(item)
          const name = item.nav_name ? item.nav_name : item.name
          const targetElement = document.querySelector(`.${key}`) as HTMLElement
          const top = targetElement.offsetTop - 24
          targets.push({ name, targetElement, top })
        })
        if (el.scrollTop >= targets[targets.length - 1].top) {
          this.activeGroupIndex = targets.length - 1
          return
        }
        for (let i = 0; i < targets.length; i++) {
          if (el.scrollTop < targets[i].top) {
            this.activeGroupIndex = (i === 0) ? 0 : (i - 1)
            break
          }
        }
      }
    }
  }

  createObserver() {
    this.$nextTick(() => {
      const itemList: any = this.$refs.areaItem
      const area: any = this.$refs.areaContent
      if (Array.isArray(itemList) && area) {
        this.observerItem = itemList[itemList.length - 1]
      }
      try {
        this.contentObserver = new IntersectionObserver((entries: any) => {
          if (entries[0].intersectionRatio > 0) {
            this.showScrollTip = false
          } else {
            this.showScrollTip = true
          }
        }, {
          root: area
        })
        if (this.observerItem && this.contentObserver) {
          this.contentObserver.observe(this.observerItem)
        }
      } catch (err) {
        //
      }
    })
  }

  clearHistory() {
    this.historyList = []
    const key = carRentalGlobalHistory
    localStorage.removeItem(key)
  }

  getClassName(item: any) {
    return `${item.key}`
  }

  scrollToView(item: any, index: number) {
    console.log('iiiii', item)
    this.activeGroupIndex = index
    const key = this.getClassName(item)
    const targetElement = document.querySelector(`.${key}`) as HTMLElement
    if (this.$refs && this.$refs.areaContent && targetElement) {
      const el = this.$refs.areaContent as HTMLElement
      this.skipScroll = true
      el.scrollTo({
        top: targetElement.offsetTop - 24,
        behavior: 'smooth'
      })
      setTimeout(() => {
        this.skipScroll = false
      }, 500)
    }
  }

  // updateSpm() {
  //   // const last = 'Global'
  //   const type = this.isCity ? 'City' : 'Location'
  //   const poi_last = this.isCity ? '' : 'POI'
  //   const query = this.cityId ? `?oid=city_${this.cityId}&trg=manual` : '?trg=manual'
  //   // this.spmPage = `CarRental_${type}_${last}${poi_last}${query}`
  //   this.spmPage = `CarRental_${type}_${poi_last}${query}`
  //   this.$nextTick(() => {
  //     this.$inhouse.track('pageview', this.$refs.listContentRef, { force: true })
  //   })
  // }

  getSpmModule(item: any, index: number, len: number, desc: string) {
    const objectName = item.poi_id ? `poi_${item.poi_id}` : `city_${item.city_id ? item.city_id : 'NA'}`
    // const moduleName = this.isCity ? 'City_LIST' : 'POI_LIST'
    const moduleName = 'CarRental_City_POI'
    return `${moduleName}?oid=${objectName}&idx=${index}&len=${len}&evt=click&ext=${JSON.stringify({ AreaName: desc })}`
  }

    get galileoClick1() {
        return { spm: WebClick_131, autoTrackSpm: true }
    }

    get galileoClick2() {
        return { spm: WebClick_132, autoTrackSpm: true }
    }
}
</script>

<style lang="scss" scoped>
.list-container {
  width: 716px;
  height: 484px;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  background-color: $color-bg-widget-normal;
  z-index: 1;
  border-radius: $radius-s;
  box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.08);
  overflow: hidden;
  perspective: 1px;

  .left {
    width: 180px;
    height: 100%;
    background: $color-bg-page;
    padding-top: 8px;

    .area-item {
      width: 100%;
      height: 40px;
      padding: 12px 0 12px 16px;
      box-sizing: border-box;
      font-size: $fontSize-caption-m;
      line-height: 16px;
      display: flex;
      align-items: center;
      cursor: pointer;

      &.active {
        background: $color-bg-widget-normal !important;
        color: $color-brand-primary;
        position: relative;
        font-weight: $fontWeight-bold;
      }

      &:hover {
        background: $color-bg-widget-darker-3;
      }
    }
  }

  .right {
    flex: 1;
    height: 100%;
    overflow: hidden;
    position: relative;

    .area-content {
      width: 100%;
      height: 100%;
      overflow-y: scroll;
      scrollbar-width: none; /* Firefox 64 */
      -ms-overflow-style: none; /* Internet Explorer 11 */
      &::-webkit-scrollbar { /** WebKit */
        display: none;
      }

      overflow-x: hidden;
      padding: 24px 28px;
      box-sizing: border-box;

      .city-item {
        width: 100%;
        cursor: default;
        margin-top: 24px;

        &:first-child {
          margin-top: 0;
        }

        .city-name-warp {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 14px;

          .city-name {
            color: $color-text-secondary;
            box-sizing: border-box;
            font-size: $fontSize-caption-m;
            line-height: 1.5;
            font-weight: $fontWeight-bold;
          }
        }

        .city-place {
          font-weight: $fontWeight-bold;
          cursor: pointer;
          font-size: $fontSize-body-s;
          line-height: 20px;
          color: $color-text-primary;
          display: flex;
          flex-wrap: wrap;

          .place-item {
            margin-bottom: 10px;
            width: 226px;
            display: flex;
            align-items: center;
            word-break: break-word;

            > img {
              width: 16px;
              height: 16px;
              margin: 2px 6px 0 0;
            }

            &:hover {
              color: $color-brand-primary;
            }

            &:nth-last-of-type(-n+2) {
              margin-bottom: 0;
            }
          }

          .city-place-item {
            width: 141px;
            align-items: flex-start;
            margin-right: 24px;

            &:nth-child(3n) {
              margin-right: 0;
            }
          }
        }
      }
    }

    .scroll-tip {
      width: 100%;
      height: 50px;
      border-radius: $radius-s;
      background-image: linear-gradient(to bottom, rgba(255, 255, 255, 0.8), #fff);
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: $fontSize-body-s;
      color: $color-brand-primary;
      position: absolute;
      bottom: 0;
      left: 0;
      transition: all 0.25s ease-in-out;
      cursor: default;
    }
  }
}
</style>
