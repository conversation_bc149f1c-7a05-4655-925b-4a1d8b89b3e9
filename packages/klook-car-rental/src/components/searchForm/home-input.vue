<template>
  <div
    v-click-outside="handleStatus"
    class="home-input_wrapper"
    :style="inputStyles"
    :class="{ active: isHighlight, 'left-border-radius': borderRadius === 'left', 'right-border-radius': borderRadius === 'right' }"
    @click="focus"
  >
    <div
      v-if="!isClicked"
      class="home-input_wrapper-one"
      :class="{ cursor: type === 'time' }"
    >
      <template v-if="type === 'time'">
        <!-- <SvgIcon name="desktop-car-rental#rental-calendar" width="20" height="20"></SvgIcon> -->
        <!-- <img src="https://res.klook.com/image/upload/web3.0/rental-calendar_zmamg4.svg" width="20" height="20"> -->
        <div class="read-only-input">{{ inputValue ? inputValue : placeholder }}</div>
      </template>
      <template v-else>
        <input
          ref="titleInput"
          v-model="inputValue"
          class="home-input_wrapper-one-title"
          name="pickupSearchInput"
          autocomplete="off"
          aria-autocomplete="none"
          :class="{ cursor: type === 'time' }"
          :placeholder="isClicked ? placeholder : floatholder"
          type="text"
        />
        <div v-if="divide && !isHighlight" class="input-divide"></div>
      </template>
    </div>
    <div
      v-if="isClicked"
      class="home-input_wrapper-two"
      @mouseover="showPickWordTip = true"
      @mouseleave="showPickWordTip = false"
    >
      <div class="home-input_wrapper-two-label" :style="inputLeftStyles">{{ floatholder }}</div>
      <template v-if="type !== 'time'">
        <klk-poptip
          v-if="!isHighlight"
          :value="showWordTip"
          placement="top"
          :content="inputValue"
          :offset="offset"
          :max-width="369"
          :z-index="10000"
        >
          <div v-if="showWordTip" class="input-tip-warp"></div>
        </klk-poptip>
        <input
          ref="myInput"
          v-model="inputValue"
          :style="inputLeftStyles"
          type="text"
          name="returnSearchInput"
          autocomplete="off"
          aria-autocomplete="none"
          :placeholder="placeholder"
          @input="emitInputEvent"
          @focus="selectAllText($event)"
          @mouseup="preventMouseUp"
        />
        <div v-if="divide && !isHighlight" class="input-divide"></div>
      </template>
      <template v-else>
        <klk-poptip
          v-model="showCarmoreMention"
          class="closeable_poptip"
          :offset="offsetBottom"
          :content="carmoreMention.data"
          closable
          dark
          :width="360"
          placement="bottom-start"
          trigger="none"
        >
          <div v-if="carmoreMention" class="input-tip-warp"></div>
        </klk-poptip>

        <div :style="inputLeftStyles" class="read-second-only-input">{{ inputValue ? inputValue : placeholder }}</div>
      </template>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Prop, Emit, Watch } from 'vue-property-decorator'
import localStorageEx from '../../common/localstorage'
import Base from "../../common/base";

const CARMORE_MENTION_KEY = 'carmoreMentionKey'
const ONE_MONTH = 86400 * 1000 * 30
@Component
export default class HomeInput extends Base {
  @Prop({ type: Number, default: 250 }) inputWidth!: number
  @Prop({ type: Number, default: 58 }) inputHeight!: number
  @Prop({ type: String, default: '' }) placeholder!: string
  @Prop({ type: String, default: '' }) floatholder!: string
  @Prop({ type: String, default: '' }) type!: string
  @Prop({ type: String, default: '' }) inputDefaultValue!: string
  @Prop({ type: Number, default: 12 }) inputLeft!: number
  @Prop({ type: Boolean, default: false }) highlight!: boolean
  @Prop({ type: String, default: '' }) borderRadius!: string
  @Prop({ type: Boolean, default: false }) divide!: boolean
  @Prop({ type: Number, default: 0 }) wordLimit!: number
  @Prop({ type: String, default: '' }) carmoreMention!: any

  isClicked: boolean = false
  isActive: boolean = false
  inputValue: string = ''
  showPickWordTip: boolean = false
  offset: number = -10
  offsetBottom: number = 68
  showCarmoreMention: boolean = false

  get inputStyles() {
    return {
      width: this.inputWidth + 'px',
      height: this.inputHeight + 'px'
    }
  }

  get inputLeftStyles() {
    return {
      marginLeft: this.inputLeft + 'px'
    }
  }

  get isHighlight() {
    return (this.type !== 'time' && this.isActive) || (this.type === 'time' && this.highlight)
  }

  get tipsStyles() {
    return {
      width: this.inputWidth - 14 + 'px',
      height: this.inputHeight + 'px'
    }
  }

  get showWordTip() {
    return this.showPickWordTip && (this.wordLimit > 0) && this.inputValue && (this.inputValue.length >= this.wordLimit)
  }

  @Watch('inputDefaultValue')
  valuePickChange(val: string) {
    this.inputValue = val
  }

  @Watch('inputValue')
  valueChange(val: string) {
    if (val) {
      this.isClicked = true
    }
  }

  @Watch('carmoreMention')
  mentionChange(val: string) {
    if (val) {
      const l = localStorageEx()
      this.showCarmoreMention = !l.getItem(CARMORE_MENTION_KEY)
    }
  }

  @Watch('showCarmoreMention')
  showChange(val: boolean) {
    if (!val) {
      const l = localStorageEx()
      l.setItem(CARMORE_MENTION_KEY, 1, ONE_MONTH)
    }
  }

  focus() {
    this.isActive = true
    if (this.type !== 'time') {
      this.isClicked = true
      this.$nextTick(() => {
        // typescript是强类型语言，由于myInput类型不知道，
        // 所以定义一个any类型的el变量，否则无法使用focus方法
        const el: any = this.$refs.myInput
        el.focus()
      })
    }
    this.$emit('focus')
  }

  handleStatus() {
    if (this.isActive && this.type !== 'time') {
      this.isActive = false
      if (!this.inputValue) {
        this.isClicked = false
      }
    }
  }

  selectAllText(event: any) {
    this.$nextTick(() => {
      event.currentTarget && event.currentTarget.select()
    })
  }

  preventMouseUp(event: any) {
    event.preventDefault()
  }

  @Emit('input')
  emitInputEvent() {
    return this.inputValue
  }
}
</script>

<style lang="scss" scoped>
@mixin text-ellipsis($line-clamp: 1) {
  display: -webkit-box;
  -webkit-line-clamp: $line-clamp;
  -webkit-box-orient: vertical;
  text-overflow: ellipsis;
  overflow: hidden;
};
.home-input_wrapper {
  display: flex;
  align-items: center;
  color: $color-common-black;
  height: 58px;
  font-size: $fontSize-body-s;
  border-radius: $radius-m;
  background-color: $color-bg-page;
  border: 1px solid transparent;

  &:hover {
    background-color: $color-bg-widget-darker-3;
  }

  &.active {
    background-color: $color-bg-widget-normal;
    border: 1px solid $color-brand-primary;
  }

  &-one {
    display: flex;
    align-items: center;
    width: 100%;
    color: $color-text-secondary;
    margin: 0 12px;
    border-radius: $radius-m;

    >svg {
      min-width: 20px;
    }

    &-title {
      display: inline-block;
      height: 58px;
      width: 100%;
      border: none;
      outline: none;
      background-color: transparent;
      margin-left: 8px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      caret-color: transparent;

      &.cursor {
        cursor: pointer;
      }

      &::placeholder {
        color: $color-text-secondary;
        opacity: $opacity-solid;
        font-weight: $fontWeight-regular;
      }
    }

    &.cursor {
      cursor: pointer;
    }

    input {
      padding-left: 0;
    }

    .input-divide {
      height: 34px;
      width: 1px;
      background: $color-bg-widget-darker-3;
      position: relative;
      right: -13px;
    }
  }

  &-two {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    border-radius: $radius-m;
    position: relative;

    &-label {
      @include text-ellipsis(1);
      position: absolute;
      margin: 12px 0 0 12px;
      width: fit-content;
      height: 14px;
      font-size: $fontSize-caption-m;
      color: $color-text-secondary;
    }

    input {
      height: 100%;
      border: none;
      margin-left: 12px;
      background-color: transparent;
      outline: none;
      text-align: left;
      font-weight: $fontWeight-bold;
      caret-color: #ff5722;
      white-space: nowrap;
      text-overflow: ellipsis;
      padding: 20px 12px 0 0;
    }

    >input {
      &::placeholder {
        color: $color-text-secondary;
        font-weight: $fontWeight-regular;
        text-overflow: ellipsis;
        overflow: hidden;
      }
    }

    .input-divide {
      height: 34px;
      width: 1px;
      background: $color-bg-widget-darker-3;
      position: absolute;
      right: -1px;
      top: 11px;
    }
  }

  ::v-deep .closeable_poptip .klk-poptip-popper-inner {
    padding-right: 40px;
  }

  ::v-deep .home-input_wrapper .home-input_wrapper-two .klk-poptip-popper-inner {
    margin: 12px 0;
    padding: 0 16px;
    word-break: break-word;
  }

  .read-only-input {
    width: 100%;
    border: none;
    outline: none;
    background-color: transparent;
    margin-left: 8px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    caret-color: transparent;
    cursor: pointer;
  }

  .read-second-only-input {
    border: none;
    margin-left: 12px;
    background-color: transparent;
    outline: none;
    text-align: left;
    font-weight: $fontWeight-bold;
    padding-top: 20px;
    height: 100%;
    display: flex;
    align-items: center;
    cursor: pointer;
  }

  .input-tip-warp {
    position: absolute;
    height: 100%;
    width: 100%;
    cursor: pointer;
  }
}

.left-border-radius,
.right-border-radius {
  border-radius: $radius-m;
}
</style>
