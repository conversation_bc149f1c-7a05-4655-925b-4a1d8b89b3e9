<template>
  <div v-click-outside="closeTimePicker" :class="$style.picker">
    <klk-date-picker
      type="date-range"
      :date.sync="dateArray"
      double-panel
      :min-date="minDate"
      :max-date="maxDate"
      :min-range-gap="0"
      @select="onDateSelect"
    ></klk-date-picker>
    <div :class="$style.timePicker">
      <div :class="$style.pickup">
        <div :class="$style.name">{{ $t('car_rental_home_date_pickup') }}</div>
        <div :class="$style.selectContent">
          <c-selector
            :class="[$style.selector, {[$style.warning]: warning}]"
            :list="hourList"
            :default="{ label: String(pickupTime.hour), value: pickupTime.hour }"
            :font="14"
            @choose="updateTime($event, 'pickup', 'hour')"
          ></c-selector>
          <span :class="$style.colon">:</span>
          <c-selector
            :class="[$style.selector, {[$style.warning]: warning}]"
            :list="minuteList"
            :default="{ label: String(pickupTime.minute), value: pickupTime.minute }"
            :font="14"
            @choose="updateTime($event, 'pickup', 'minute')"
          ></c-selector>
        </div>
      </div>
      <div :class="$style.dropoff">
        <div :class="$style.name">{{ $t('car_rental_home_date_dropoff') }}</div>
        <div :class="$style.selectContent">
          <c-selector
            :class="[$style.selector, {[$style.warning]: warning}]"
            :list="hourList"
            :default="{ label: String(dropoffTime.hour), value: dropoffTime.hour }"
            :font="14"
            @choose="updateTime($event, 'dropoff', 'hour')"
          ></c-selector>
          <span :class="$style.colon">:</span>
          <c-selector
            :class="[$style.selector, {[$style.warning]: warning}]"
            :list="minuteList"
            :default="{ label: String(dropoffTime.minute), value: dropoffTime.minute }"
            :font="14"
            @choose="updateTime($event, 'dropoff', 'minute')"
          ></c-selector>
        </div>
      </div>
    </div>
    <div :class="$style.localTip">
      <!-- <SvgIcon name="desktop-car-rental#rental-tips-g" width="16" height="16"></SvgIcon> -->
      <img src="https://res.klook.com/image/upload/web3.0/rental-tips-g_cnjlet.svg" width="16" height="16">
      <span :class="$style.text">{{ $t('car_rental_home_all_local_time') }}</span>
    </div>
    <div v-show="showTimeWarning" :class="$style.warningTip">
      {{ $t('car_rental_home_select_date_before_tip') }}
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Prop } from 'vue-property-decorator'
import dayjs from 'dayjs'
import Base from "../../common/base";

import cSelector from './selector.vue'

@Component({
  components: {
    cSelector
  }
})

export default class DateTimePicker extends Base {
  @Prop({ type: Object, default: () => {} }) pickUpDate!: any
  @Prop({ type: Object, default: () => {} }) dropOffDate!: any
  @Prop({ type: String, default: 'pickup' }) dateMode!: string
  dateArray: Date[] = []
  currentArray: string[] = []
  changedFlag: boolean = false

  pickupTime: any = {
    hour: '10',
    minute: '00'
  }

  dropoffTime: any = {
    hour: '10',
    minute: '00'
  }

  minDate: Date = dayjs().subtract(1, 'day').toDate()
  maxDate: Date = dayjs().add(1, 'year').toDate()

  timeWarning: any = null
  showTimeWarning: boolean = false
  warning: boolean = false

  get hourList() {
    const list: any[] = []
    for (let i = 0; i < 24; i++) {
      if (i < 10) {
        list.push({
          label: '0' + i,
          value: i
        })
      } else {
        list.push({
          label: String(i),
          value: i
        })
      }
    }
    return list
  }

  get minuteList() {
    return [
      {
        label: '00',
        value: 0
      }, {
        label: '30',
        value: 30
      }
    ]
  }

  created() {
    this.dateArray = [
      dayjs(this.pickUpDate.date).toDate(),
      dayjs(this.dropOffDate.date).toDate()
    ]
    this.currentArray = [
      this.pickUpDate.date,
      this.dropOffDate.date
    ]
    this.pickupTime = {
      hour: this.pickUpDate.hour,
      minute: this.pickUpDate.minute
    }
    this.dropoffTime = {
      hour: this.dropOffDate.hour,
      minute: this.dropOffDate.minute
    }
    if (this.dateMode === 'dropoff') {
      this.minDate = dayjs(this.pickUpDate.date).toDate()
    }
  }

  beforeDestory() {
    this.timeWarning && clearTimeout(this.timeWarning)
  }

  onDateChange() {
    if (!this.changedFlag) {
      this.changedFlag = true
      return
    }
    const date = this.dateArray.map((item: Date) => dayjs(item).format('YYYY-MM-DD'))
    if (this.validateDateTime()) {
      this.$emit('updateDateRange', date)
    }
  }

  updateTime(item: any, type: string, key: string) {
    if (type === 'pickup') {
      this.pickupTime[key] = item.label
    } else {
      this.dropoffTime[key] = item.label
    }
    if (this.validateDateTime()) {
      const value = type === 'pickup' ? this.pickupTime : this.dropoffTime
      this.$emit('updateDateTime', type, value)
    } else {
      this.showTimeWarning = true
      this.timeWarning = setTimeout(() => {
        clearTimeout(this.timeWarning)
        this.timeWarning = null
        this.showTimeWarning = false
      }, 2000)
    }
  }

  validateDateTime() {
    const dateArray = this.currentArray.map((item: string, index: number) => {
      const date = item
      const time = index === 0 ? ` ${this.pickupTime.hour}:${this.pickupTime.minute}` : ` ${this.dropoffTime.hour}:${this.dropoffTime.minute}`
      return date + time
    })
    if (dayjs(dateArray[0]).isAfter(dayjs(dateArray[1])) || dayjs(dateArray[0]).isSame(dateArray[1])) {
      const pickUpTime = `${this.pickupTime.hour}:${this.pickupTime.minute}`
      const dropoffTime = `${this.dropoffTime.hour}:${this.dropoffTime.minute}`
      if (pickUpTime !== '23:30') {
        const t = dayjs(dateArray[0]).add(30, 'minute')
        this.dropoffTime.hour = String(t.get('hour')).padStart(2, '0')
        this.dropoffTime.minute = String(t.get('minute')).padStart(2, '0')
      } else if (pickUpTime === '23:30' && dropoffTime === '00:00') {
        this.dateArray[1] = dayjs(this.dateArray[0]).add(1, 'day').toDate()
        this.currentArray[1] = dayjs(this.dateArray[1]).format('YYYY-MM-DD')
      } else {
        const t = dayjs(dateArray[1]).subtract(30, 'minute')
        this.pickupTime.hour = String(t.get('hour')).padStart(2, '0')
        this.pickupTime.minute = String(t.get('minute')).padStart(2, '0')
      }
      this.$emit('updateDateTime', 'pickup', this.pickupTime)
      this.$emit('updateDateTime', 'dropoff', this.dropoffTime)
      return false
    } else {
      return true
    }
  }

  closeTimePicker() {
    if (this.warning) {
      return
    }
    this.$emit('close')
  }

  onDateSelect(date: Date) {
    if (this.currentArray[0] && this.currentArray[1]) {
      if (this.dateMode === 'pickup') {
        this.minDate = dayjs(date).toDate()
        this.currentArray[0] = dayjs(date).format('YYYY-MM-DD')
        this.currentArray[1] = ''
        this.$emit('setDropoffDateActive')
      } else {
        this.dateArray[1] = date
        this.currentArray[1] = dayjs(date).format('YYYY-MM-DD')
      }
    } else if (this.currentArray[0] && !this.currentArray[1]) {
      this.currentArray[1] = dayjs(date).format('YYYY-MM-DD')
    }
    this.validateDateTime()
    this.$emit('updateDateRange', this.currentArray)
    this.closeTimePicker()
  }
}
</script>

<style lang="scss" module>
.picker {
  background-color: $color-bg-widget-normal;
  width: fit-content;
  border-radius: $radius-s;
  box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.08);
}

.timePicker {
  height: 64px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: $color-bg-page;
  font-weight: $fontWeight-bold;

  .pickup,
  .dropoff {
    width: 50%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 60px 16px 28px;
    box-sizing: border-box;
  }

  .selectContent {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .colon {
    margin-left: 6px;
  }

  .selector {
    width: 80px;
    height: 32px;
    border: 1px solid $color-border-normal;
    border-radius: $radius-s;
    box-sizing: border-box;
    padding: 10px;

    &.warning {
      border-color: $color-error;
    }
  }
}

.localTip {
  height: 56px;
  box-sizing: border-box;
  padding: 20px 28px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  color: $color-text-secondary;
  line-height: 16px;

  .text {
    margin-left: 6px;
  }
}

.warningTip {
  width: 100%;
  height: 40px;
  line-height: 20px;
  padding: 10px 20px;
  border-radius: $radius-s;
  box-sizing: border-box;
  background-color: $color-error-background;
  color: $color-error;
  position: absolute;
  top: 0;
  left: 0;
}
</style>
