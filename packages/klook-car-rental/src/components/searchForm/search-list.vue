<template>
  <div
    v-click-outside="close"
    class="destinationList choose_container suggestStation"
    :style="{'left': left + 'px'}"
  >
    <ul
      v-if="!loading"
      ref="areaContent"
    >
      <template v-if="completePlaceListV3">
        <template v-if="completePlaceListV3.search_result_item_list && completePlaceListV3.search_result_item_list.length > 0">
          <li
            v-for="(item, index) in getList(completePlaceListV3.search_result_item_list)"
            :key="index"
            ref="areaItem"
            class="content"
            :class="{'cityItem': item.city_poi}"
            :data-spm-module="getSpmModule(item, index, completePlaceListV3.search_result_item_list)"
            v-galileo-click-tracker="galileoClick1"
            :data-spm-virtual-item="spmVirtual"
            @click="setPlace({...item, index: index + 1})"
          >
            <div class="mainText">
              <img :src="(item.is_city && !item.city_poi) ? item.city_image_url : item.image_url">
              <div class="locationName">{{ (item.is_city && !item.city_poi) ? item.city_name : item.poi_name }}</div>
            </div>
            <div class="secondaryText">{{ (item.is_city && !item.city_poi) ? item.city_path : item.address }}</div>
          </li>
          <div :style="{'display': `${showScrollTip ? 'flex' : 'none'}`, 'transform': `translateY(${showScrollTip ? '0%' : '100%'})`}" class="scroll-tip">
            {{ $t('car_rental_home_scroll_down') }}
          </div>
        </template>
        <template v-else>
          <li>{{ $t('car_rental_home_no_result', [ isCity ? city : keyword]) }}</li>
        </template>
      </template>
    </ul>
    <ul v-else>
      <li>
        {{ $t('car_rental_home_loading') }}
      </li>
    </ul>
  </div>
</template>

<script lang="ts">
import { Component, Prop } from 'vue-property-decorator'
import Base from "../../common/base";
import { WebClick_111 } from '../../../share/galileo/auto-click'

@Component
export default class SearchList extends Base {
  @Prop({ type: Boolean, default: false }) noResult!: Boolean
  @Prop({ type: Boolean, default: false }) loading!: Boolean
  @Prop({ type: Number, default: 24 }) left!: Number
  @Prop({ type: String, default: '' }) keyword!: String
  @Prop({ type: String, default: '' }) city!: String
  @Prop({ type: Boolean, default: false }) isCity!: Boolean
  @Prop({ type: Object, default: null }) completePlaceListV3!: any
  contentObserver: any = null
  observerItem: any = null
  showScrollTip: boolean = true
  spmVirtual: string = '__virtual'

  updated() {
    if (this.contentObserver && this.observerItem) {
      this.contentObserver.unobserve(this.observerItem)
    } else {
      this.$nextTick(() => {
        const itemList: any = this.$refs.areaItem
        const area: any = this.$refs.areaContent
        if (!itemList || !area) { return false }
        this.observerItem = itemList[itemList.length - 1]
        if (this.observerItem) {
          try {
            this.contentObserver = new IntersectionObserver((entries: any) => {
              if (entries[0].intersectionRatio > 0) {
                this.showScrollTip = false
              } else {
                this.showScrollTip = true
              }
            }, {
              root: area
            })
            this.contentObserver.observe(this.observerItem)
          } catch (err) {
            //
          }
        }
      })
    }
    this.$nextTick(() => {
      const itemList: any = this.$refs.areaItem
      if (!itemList) { return false }
      this.observerItem = itemList[itemList.length - 1]
      if (this.observerItem && this.contentObserver) {
        this.contentObserver.observe(this.observerItem)
      }
    })
  }

  beforeDestory() {
    if (this.contentObserver) {
      this.observerItem && this.contentObserver.unobserve(this.observerItem)
      this.contentObserver.disconnect()
    }
    this.observerItem = null
    this.contentObserver = null
  }

  setPlace(place: any) {
    this.$emit('setPlace', place)
  }

  close() {
    this.$emit('close')
  }

  getList(search_list: any) {
    const list = [] as any[]
    search_list.forEach((item: any) => {
      if (item.is_city) {
        list.push({ ...item })
        item.city_poi_info_list.forEach((poi: any) => {
          list.push({ ...poi, ...item, city_poi: true })
        })
      } else {
        const poi_info = { ...item, ...item.poi_info }
        list.push(poi_info)
      }
    })
    return list
  }

  getSpmModule(item: any, index: number, list: any[]) {
    const objectName = item.poi_id ? `poi_${item.poi_id}` : `city_${item.city_id ? item.city_id : 'NA'}`
    const SearchWord = this.isCity ? this.city : this.keyword
    const PlaceName = item.poi_name ? `${item.poi_name}` : `${item.city_name ? item.city_name : 'NA'}`
    const len = this.getList(list).length
    return `CarRental_SearchSuggest_LIST?oid=${objectName}&idx=${index}&len=${len}&evt=click&ext=${JSON.stringify({ SearchWord: encodeURIComponent(SearchWord as string), PlaceName: encodeURIComponent(PlaceName as string) })}`
  }

  getNoSuggestModule() {
    const SearchWord = this.keyword
    return `NoSearchResultSuggest_LIST?ext=${JSON.stringify({ SearchWord })}`
  }

    get galileoClick1() {
        return { spm: WebClick_111, autoTrackSpm: true }
    }
}
</script>

<style lang="scss" scoped>
  .destinationList {
    position: absolute;
    overflow-y: scroll;
    scrollbar-width: none; /* Firefox 64 */
    -ms-overflow-style: none; /* Internet Explorer 11 */
    &::-webkit-scrollbar { /** WebKit */
      display: none;
    }

    z-index: 10;

    ul {
      padding: 8px 0;

      li {
        font-size: $fontSize-body-s;
        line-height: 1.2;
        color: $color-text-primary;
        cursor: pointer;
        display: flex;
        align-items: center;
        padding: 8px 16px;

        &:hover {
          background-color: $color-bg-page;

          &.hot_tip {
            background-color: $color-bg-widget-normal;
          }
        }

        &.hot_tip {
          display: flex;
          position: relative;
          cursor: default;
          width: calc(100% - 40px);
          margin-left: 20px;
          box-sizing: border-box;
          padding: 20px 20px 16px 0;

          p {
            padding-right: 6px;
            background-color: $color-bg-widget-normal;
            height: 100%;
            position: relative;
            z-index: 1;
            color: $color-text-secondary;
          }

          &::after {
            content: '';
            display: block;
            position: absolute;
            left: 0;
            top: 50%;
            height: 1px;
            width: 100%;
            background-color: $color-bg-overlay-black-mobile;
            transform: translateY(-50%);
          }
        }

        svg {
          font-size: $fontSize-body-m;
          margin-right: 12px;
        }
      }

      .cityItem {
        padding: 8px 16px 8px 32px;
      }

      .title {
        font-weight: $fontWeight-bold;
        font-size: $fontSize-caption-m;
        line-height: 16px;
        color: $color-text-primary;
        padding: 8px 16px;
      }

      .tips {
        color: $color-text-secondary;
        padding: 8px 16px;
      }
    }

    .scroll-tip {
      width: 100%;
      height: 50px;
      border-radius: $radius-s;
      background-image: linear-gradient(to bottom, rgba(255, 255, 255, 0.8), #fff);
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: $fontSize-body-s;
      color: $color-brand-primary;
      position: absolute;
      bottom: 0;
      left: 0;
      transition: all 0.25s ease-in-out;
      cursor: default;
    }
  }

  .choose_container {
    border-radius: $radius-s;
    box-shadow: 0 1px 6px 0 rgba(0, 0, 0, 0.2);
    background-color: $color-bg-widget-normal;
  }

  .suggestStation {
    width: 480px;
    max-height: 464px;

    ul {
      width: 100%;
      max-height: 464px;
      overflow-y: auto;
      scrollbar-width: none; /* Firefox 64 */
      -ms-overflow-style: none; /* Internet Explorer 11 */
      &::-webkit-scrollbar { /** WebKit */
        display: none;
      }
    }

    .content {
      display: flex;
      flex-direction: column;
      align-items: flex-start !important;
      font-size: $fontSize-body-s;

      .mainText {
        display: flex;

        > img {
          height: 16px;
          width: 16px;
          margin-right: 12px;
        }
      }

      .cityItem {
        margin-left: 36px;
      }
    }

    .secondaryText {
      font-size: $fontSize-caption-m;
      line-height: 14px;
      margin-top: 4px;
      color: $color-text-disabled;
      margin-left: 28px;
      word-break: break-word;
    }
  }
</style>
