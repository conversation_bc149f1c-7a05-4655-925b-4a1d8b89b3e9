<template>
  <div v-if="data && data.vendor_list" :class="$style['module-box']">
    <h2>{{ data.title }}</h2>
    <div v-if="data && data.dealer_image_list" :class="$style['supplier-list']">
      <a
        v-for="vendor in data.vendor_list"
        :key="vendor.vendor_id"
        :href="vendor.link"
        :data-spm-module="`SupplierLogo?oid=supplier_${vendor.vendor_id}`"
        v-galileo-click-tracker="galileoClick1"
        data-spm-virtual-item="__virtual?typ=entry"
      >
        <img :src="vendor.image_url">
      </a>
      <i></i>
      <i></i>
      <i></i>
      <i></i>
      <i></i>
    </div>
  </div>
</template>
<script lang="ts">
import { Component, Prop } from 'vue-property-decorator'
import Base from "../common/base";
import { WebClick_11 } from '../../share/galileo/auto-click'

interface Dealer {
  title: string
  dealer_image_list: string[]
  vendor_list: any[]
}

@Component
export default class supplierList extends Base {
  @Prop({ type: Object, default: () => null }) data!: Dealer

    get galileoClick1() {
        return { spm: WebClick_11, autoTrackSpm: true }
    }
}
</script>

<style lang="scss" module>

.module-box {

  h2 {
    @include font-body-l-bold;
    color: $color-text-primary;
    padding: 0 0 10px;
  }
}

.supplier-list {
  padding: 7px;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  background-color: $color-bg-widget-normal;
  border-radius: $radius-l;

  a {
    padding: 5px;
    font-size: 0;
  }

  img {
    width: 70px;
    border-radius: $radius-s;
  }

  i {
    width: 78px;
  }
}
</style>
