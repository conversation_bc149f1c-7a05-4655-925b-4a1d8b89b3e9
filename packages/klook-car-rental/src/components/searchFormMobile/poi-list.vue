<template>
  <div
    ref="poiListPage"
    :data-spm-page="pageModule"
    class="poi-list-wrap"
  >
    <search-list
      :completePlaceListV3="completePlaceListV3"
      :loading="loading"
      :custom-module="customSpm"
      @focus-search-input="focusSearchInput"
      @get-complete-list="getCompleteList"
      @set-search-place="setSearchPlace($event)"
      @go-poi-list="goPoiList"
      @close-search-list="showSearchList = false"
      @close="closePopup"
    ></search-list>
    <div v-if="loadingPoiList" class="loading-wrap">
      <klk-loading></klk-loading>
    </div>
    <template v-if="!showSearchList && !loadingPoiList">
      <div v-if="poiList && poiList.city_name" class="poi-header">
        <h1 class="title">
          <img :src="poiList.city_image_url" width="24" height="24" alt="">
          <span>{{ poiList.city_name }}</span>
        </h1>
        <div
          class="change-btn"
          data-spm-module="ChangeCity?trg=manual"
          v-galileo-click-tracker="galileoClick1"
          data-spm-virtual-item="__virtual?typ=entry"
          @click="changeCity"
        >
          <span>{{ $t('17807-change_city') }}</span>
        </div>
      </div>
      <div v-if="searchHistoryList.length > 0" class="category-item">
        <h3 class="caregory-title">
          <span>{{ $t('17805-search_history') }}</span>
          <i
            data-spm-module="ClearHistory"
            v-galileo-click-tracker="galileoClick2"
            data-spm-virtual-item="__virtual?typ=entry"
            @click="removeHistory"
          >
            <!-- <SvgIcon name="mobile-car-rental#icon-remove" width="16" height="16" /> -->
            <img src="https://res.klook.com/image/upload/web3.0/icon-remove_axnprh.svg" width="16" height="16">
          </i>
        </h3>
        <ul
          class="caregory-list"
        >
          <li
            v-for="(item, index) in searchHistoryList"
            :key="index"
            class="poi-item"
            :data-spm-module="`SearchHistory_LIST?oid=poi_${item.poi_id}`"
            v-galileo-click-tracker="galileoClick3"
            data-spm-virtual-item="__virtual?typ=entry"
            @click="setSearchPlace(item)"
          >
            <i class="poi-icon">
              <img :src="item.image_url" width="20" height="20">
            </i>
            <div class="poi-content">
              <p class="name">{{ item.poi_name }}</p>
              <span class="info">{{ item.city_name }}</span>
            </div>
          </li>
        </ul>
      </div>
      <template v-if="poiList && poiList.recommended_poi_info_list && poiList.recommended_poi_info_list.length > 0">
        <div
          v-for="(item, index) in poiList.recommended_poi_info_list"
          :key="index"
          class="category-item"
        >
          <h3 class="caregory-title">
            <span>{{ item.title }}</span>
          </h3>
          <ul v-if="item.poi_info_list.length > 0" class="caregory-list">
            <li
              v-for="poi in item.poi_info_list"
              :key="poi.poi_id"
              class="poi-item"
              :data-spm-module="`${item.buried_pointed_desc}?oid=poi_${poi.poi_id}`"
              v-galileo-click-tracker="galileoClick4"
              data-spm-virtual-item="__virtual?typ=entry"
              @click="setSearchPlace(poi)"
            >
              <i class="poi-icon">
                <img :src="poi.image_url" width="20" height="20">
              </i>
              <div class="poi-content">
                <p class="name">{{ poi.poi_name }}</p>
                <span class="info">{{ poi.poi_address }}</span>
              </div>
            </li>
          </ul>
        </div>
      </template>
    </template>
  </div>
</template>

<script lang="ts">
import { Component, Prop } from 'vue-property-decorator'

import SearchList from './search-list.vue'
import { CarRental } from '../../types/car-rental'
import Base from "../../common/base";
import { WebClick_91, WebClick_92, WebClick_93, WebClick_94 } from '../../../share/galileo/auto-click'

@Component({
  components: {
    SearchList
  }
})
export default class CityDetial extends Base {
  @Prop({ type: Boolean, default: true }) loading!: boolean
  @Prop({ type: Boolean, default: true }) loadingPoiList!: boolean
  @Prop({ type: Boolean, default: true }) isLocalPoi!: boolean
  @Prop({ type: Number, default: null }) trackCityId!: number
  @Prop({ type: Object, default: () => null }) poiList!: any
  @Prop({ type: Object, default: null }) completePlaceListV3!: any
  @Prop({
    type: Object,
    default: () => {
      return {
        domestic: [],
        international: []
      }
    }
  }) searchHistory!: any

  showSearchList: boolean = false
  isDomestic: boolean = this.isLocalPoi

  get searchHistoryList() {
    return this.isDomestic ? this.searchHistory.domestic : this.searchHistory.international
  }

  get pageModule() {
    return `CarRental_Location_POI?trg=manual&oid=city_${this.trackCityId || null}`
  }

  get customSpm() {
    return 'CarRental_Location_POI.SearchBar.SearchInput'
  }

  mounted() {
    this.$nextTick(function () {
      this.$nextTick(function () {
        this.$inhouse.track('pageview', this.$refs.poiListPage, { force: true })
      })
    })
  }

  closePopup() {
    this.$emit('close')
  }

  // 更改城市
  changeCity() {
    this.$emit('change-city')
  }

  goPoiList(id: number) {
    this.$emit('go-poi-list', id)
  }

  // 清空历史记录
  removeHistory() {
    this.$emit('remove-history', this.isDomestic)
  }

  focusSearchInput() {
    this.showSearchList = true
  }

  getCompleteList(val: string) {
    if (val) {
      this.$inhouse.track('custom', '.poi-list-wrap', {
        objectId: 'city_' + this.trackCityId || 'null',
        spm: this.customSpm
      })
    }
    this.$emit('get-complete-list', { keyWord: val, isDomestic: this.isDomestic })
  }

  setSearchPlace(poi: CarRental.Poi) {
    this.$emit('set-search-place', poi)
  }

    get galileoClick1() {
        return { spm: WebClick_91, autoTrackSpm: true }
    }

    get galileoClick2() {
        return { spm: WebClick_92, autoTrackSpm: true }
    }

    get galileoClick3() {
        return { spm: WebClick_93, autoTrackSpm: true }
    }

    get galileoClick4() {
        return { spm: WebClick_94, autoTrackSpm: true }
    }
}
</script>

<style lang="scss" scoped>
.poi-list-wrap {
  padding-bottom: 16px;

  .loading-wrap {
    padding: 0 16px;
    margin-top: 124px;
    position: relative;
    height: 300px;
  }

  .poi-header {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    padding: 20px 16px 0;

    .title {
      font-size: $fontSize-body-m;
      font-weight: $fontWeight-bold;
      color: $color-text-primary;
      line-height: 22px;
      display: flex;
      flex-direction: row;
      align-items: center;

      span {
        margin-left: 8px;
      }
    }

    .change-btn {
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;

      span {
        font-size: $fontSize-caption-m;
        margin-left: 4px;
        color: $color-info;
      }
    }
  }

  .category-item {
    margin-top: 32px;
    padding: 0 16px;

    .caregory-title {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      font-size: $fontSize-caption-m;
      font-weight: $fontWeight-bold;
      color: $color-text-primary;
      line-height: 16px;
    }

    .caregory-list {
      padding-top: 6px;

      &.city {
        padding-top: 8px;
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
      }

      .poi-item {
        display: flex;
        flex-direction: row;
        padding: 10px 0;

        &:last-child {
          padding-bottom: 0;
        }

        .poi-icon {
          flex: 40px 0 0;
          width: 40px;
          height: 40px;
          display: flex;
          justify-content: center;
          align-items: center;
          background-color: $color-bg-page;
          border-radius: $radius-s;
        }

        .poi-content {
          margin-left: 12px;
          display: flex;
          flex-direction: column;
          justify-content: center;

          .name {
            font-size: $fontSize-body-s;
            line-height: 20px;
            color: $color-text-primary;
            font-weight: $fontWeight-bold;
            word-break: break-all;
          }

          .info {
            font-size: $fontSize-caption-m;
            line-height: 16px;
            color: $color-text-disabled;
            margin-top: 2px;
          }
        }
      }

      .city-item {
        padding: 6px 10px;
        font-size: $fontSize-body-s;
        line-height: 20px;
        margin-top: 8px;
        margin-right: 8px;
        border-radius: $radius-s;
        color: $color-text-primary;
        border: 0.5px solid $color-border-normal;
      }
    }
  }

  .poi-list-body {
    padding: 0 16px;
    margin-top: 56px;
    position: relative;

    .poi-list {
      li {
        &:last-child {
          padding-bottom: 0;
        }
      }

      .poi-item {
        display: flex;
        flex-direction: row;
        padding: 10px 0;

        .poi-icon {
          flex: 40px 0 0;
          width: 40px;
          height: 40px;
          display: flex;
          justify-content: center;
          align-items: center;
          background-color: $color-bg-page;
          border-radius: $radius-s;
        }

        .poi-content {
          margin-left: 12px;
          display: flex;
          flex-direction: column;

          .name {
            font-size: $fontSize-body-s;
            line-height: 20px;
            color: $color-text-primary;
            font-weight: $fontWeight-bold;
            word-break: break-all;
          }

          .info {
            font-size: $fontSize-caption-m;
            line-height: 16px;
            color: $color-text-disabled;
            margin-top: 2px;
          }
        }
      }
    }
  }

  ::v-deep .klk-input-inner {
    min-height: 36px;
    border: none;
    background-color: $color-bg-page;
    /* stylelint-disable */
    border-radius: 20px;
    /* stylelint-enable */
  }
}
</style>
