<template>
  <div ref="searchListWrap" class="search-list-wrap">
    <div class="search-input-bar">
      <i class="close-btn" @click="closePopup">
        <!-- <SvgIcon name="mobile-car-rental#icon-nav-close" width="16" height="16" /> -->
        <img 
          src="https://res.klook.com/image/upload/web3.0/icon-nav-close_awwjhu.svg" 
          width="16"  
          height="16"
        >
      </i>
      <div class="search-input">
        <input
          ref="searchInput"
          v-model="inputValue"
          :placeholder="$t('17801-search_input_placeholder')"
          clearable
          @input="handleInputChange"
          @focus="focusSearchInput"
        />
        <!-- <SvgIcon
          v-if="inputValue"
          name="mobile-car-rental#clear-icon"
          width="16"
          height="16"
          @click.native="clearInput"
        /> -->
        <img 
          v-if="inputValue" 
          src="https://res.klook.com/image/upload/web3.0/icon-nav-close_awwjhu.svg" 
          width="16" 
          height="16" 
          @click="clearInput"
        />
      </div>
    </div>
    <div v-if="loading" class="loading-wrap">
      <klk-loading></klk-loading>
    </div>
    <div v-else class="poi-list-wrap">
      <template v-if="showSearchList">
        <Klk-empty-panel
          v-if="noResult"
          :content="completePlaceListV3 && completePlaceListV3.tips && completePlaceListV3.tips.replace(/{([\d\w.]+)}/g, inputValue)"
          icon-src="https://res.klook.com/image/upload/web3.0/ill_spot_hero_transport_tickets_edrvrq.svg"
        >
        </Klk-empty-panel>
        <!-- <div v-if="hasLocalPoi" class="no-result-in-local">
          <span>{{ completePlaceListV3.tips }}</span>
        </div> -->
        <!-- <h3 v-if="hasLocalPoi" class="international-title">{{ completePlaceListV3.title }}</h3> -->
        <ul
          v-if="completePlaceListV3 && completePlaceListV3.search_result_item_list && completePlaceListV3.search_result_item_list.length > 0"
          class="poi-list"
          :class="{'no-pt': hasLocalPoi}"
        >
          <li
            v-for="(item, index) in completePlaceListV3.search_result_item_list"
            :key="index"
          >
            <div
              v-if="!item.is_city && item.poi_info"
              v-galileo-click-tracker="{ spm: getSpmModule(item.poi_info.poi_name, item.poi_info.poi_id, 'poi'), autoTrackSpm: true }"
              class="poi-item"
              :data-spm-module="getSpmModule(item.poi_info.poi_name, item.poi_info.poi_id, 'poi')"
              data-spm-virtual-item="__virtual"
              @click="setSearchPlace({...item.poi_info, index: index + 1})"
            >
              <i class="poi-icon">
                <img :src="item.poi_info.image_url" width="16" height="16" alt="">
              </i>
              <div class="poi-content">
                <p class="name">{{ item.poi_info.poi_name }}</p>
                <span class="info">{{ item.poi_info.address }}</span>
              </div>
            </div>
            <div
              v-else
              v-galileo-click-tracker="{ spm: getSpmModule(item.city_name, item.city_id, 'city'), autoTrackSpm: true }"
              class="poi-item"
              :data-spm-module="getSpmModule(item.city_name, item.city_id, 'city')"
              data-spm-virtual-item="__virtual"
              @click="goPoiList(item.city_id)"
            >
              <i class="poi-icon">
                <img :src="item.city_image_url" width="16" height="16" alt="">
              </i>
              <div class="poi-content">
                <p class="name">{{ item.city_name }}</p>
                <span class="info">{{ item.city_path }}</span>
              </div>
            </div>
            <ul v-if="item.city_poi_info_list.length > 0" class="poi-list sub-list">
              <li
                v-for="poi in item.city_poi_info_list"
                :key="poi.poi_id"
                v-galileo-click-tracker="{ spm: getSpmModule(poi.poi_name, poi.poi_id, 'poi'), autoTrackSpm: true }"
                class="poi-item"
                :data-spm-module="getSpmModule(poi.poi_name, poi.poi_id, 'poi')"
                data-spm-virtual-item="__virtual"
                @click="setSearchPlace({...poi, index: index + 1})"
              >
                <i class="poi-icon">
                  <img :src="poi.image_url" width="16" height="16" alt="">
                </i>
                <div class="poi-content">
                  <p class="name">{{ poi.poi_name }}</p>
                  <span class="info">{{ poi.address }}</span>
                </div>
              </li>
            </ul>
          </li>
        </ul>
      </template>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Prop, Watch, Ref } from 'vue-property-decorator'
import debounce from 'lodash/debounce'
import KlkEmptyPanel from '@klook/empty-panel'
import { CarRental } from '../../types/car-rental'
import Base from "../../common/base";

import '@klook/empty-panel/dist/esm/index.css'

@Component({
  components: {
    KlkEmptyPanel
  }
})
export default class SearchList extends Base {
  @Prop({ type: String, default: '' }) title!: String
  @Prop({ type: String, default: '' }) word!: String
  @Prop({ type: String, default: '' }) customModule!: String
  @Prop({ type: Boolean, default: true }) loading!: Boolean
  @Prop({ type: Object, default: null }) completePlaceListV3!: any
  @Ref() searchInput!: any
  @Ref() searchListWrap!: any

  inputValue: string = ''
  noResult: boolean = false
  hasLocalPoi: boolean = false
  showSearchList: boolean = true
  spmModule: string = 'CarRental_SearchSuggest_LIST?ext=' + JSON.stringify({ SearchWord: encodeURIComponent(this.inputValue) })

  get isLoading() {
    return !(this.completePlaceListV3 && this.completePlaceListV3.search_result_item_list && this.completePlaceListV3.search_result_item_list.length > 0)
  }

  getCompleteList(val: string) {
    this.showSearchList = true
    this.$emit('get-complete-list', val)
  }

  @Watch('inputValue')
  changeValue() {
    this.noResult = false
  }

  @Watch('completePlaceListV3')
  changeData() {
    const hasList = this.completePlaceListV3 && this.completePlaceListV3.search_result_item_list && this.completePlaceListV3.search_result_item_list.length > 0
    this.noResult = !hasList && !!this.inputValue
    if (hasList) {
      this.hasLocalPoi = !!this.completePlaceListV3.tips
    } else {
      this.hasLocalPoi = false
    }
    return this.completePlaceListV3
  }

  @Watch('hasLocalPoi')
  changInput(val: boolean) {
    this.spmModule = (val ? 'NoSearchResultSuggest_LIST' : 'CarRental_SearchSuggest_LIST') + '?ext=' + JSON.stringify({ SearchWord: encodeURIComponent(this.inputValue) })
  }

  mounted() {
    this.$emit('get-complete-list', this.inputValue)
  }

  handleValue = debounce(this.getCompleteList, 500)

  handleInputChange() {
    this.handleValue(this.inputValue)
  }

  focusSearchInput() {
    this.$emit('focus-search-input')
  }

  closePopup() {
    this.$emit('close')
  }

  setSearchPlace(place: CarRental.Poi) {
    this.showSearchList = false
    this.$emit('close-search-list')
    this.$emit('set-search-place', place)
  }

  clearInput() {
    this.inputValue = ''
    this.$emit('get-complete-list', this.inputValue)
    setTimeout(() => {
      this.searchInput.focus()
    }, 0)
  }

  goPoiList(id: number) {
    this.showSearchList = false
    this.$emit('close-search-list')
    this.$emit('go-poi-list', id)
  }

  getSpmModule(name: string, id: number, type: string) {
    return `${this.hasLocalPoi ? 'NoSearchResultSuggest_LIST' : 'CarRental_SearchSuggest_LIST'}?oid=${type}_${id}&ext=${JSON.stringify({ SearchWord: encodeURIComponent(this.inputValue), PlaceName: encodeURIComponent(name || 'NA') })}`
  }
}
</script>

<style lang="scss" scoped>
.search-list-wrap {
  max-height: 100%;
  overflow-y: scroll;

  .search-input-bar {
    width: 100%;
    height: 56px;
    padding: 0 16px;
    background-color: $color-bg-widget-normal;
    display: flex;
    flex-direction: row;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 10;

    .close-btn {
      flex: 16px 0 0;
      margin: 26px 16px 14px 0;
    }

    .search-input {
      flex: 1;
      background-color: $color-bg-page;
      /* stylelint-disable */
      border-radius: 20px;
      /* stylelint-enable */
      min-height: 36px;
      margin: 16px 0 4px;
      display: inline-flex;

      input {
        width: 100%;
        border: none;
        outline: none;
        caret-color: #ff5722;
        padding: 0 12px;
        line-height: 36px;
        background: transparent;
      }

      img {
        margin-top: 10px;
        margin-right: 12px;
      }
    }
  }

  .loading-wrap {
    padding: 0 16px;
    margin-top: 124px;
    position: relative;
    height: 300px;
  }

  .poi-list-wrap {
    padding: 0 16px;
    margin-top: 56px;
    position: relative;

    .no-result {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      margin-top: 92px;

      span {
        margin-top: 16px;
        font-size: $fontSize-body-m;
        line-height: 22px;
        color: $color-text-secondary;
        text-align: center;
      }
    }

    .no-result-in-local {
      font-size: $fontSize-caption-m;
      color: $color-text-secondary;
      line-height: 16px;
      padding-top: 16px;
    }

    .international-title {
      font-size: $fontSize-caption-m;
      line-height: 16px;
      color: $color-text-primary;
      margin: 32px 0 10px;
    }

    .poi-list {
      padding-top: 18px;

      &.no-pt {
        padding-top: 0;
      }

      &.sub-list {
        margin-left: 16px;
        padding-top: 0;
      }

      .poi-item {
        display: flex;
        flex-direction: row;
        padding: 10px 0;

        .poi-icon {
          flex: 40px 0 0;
          width: 40px;
          height: 40px;
          display: flex;
          justify-content: center;
          align-items: center;
          background-color: $color-bg-page;
          border-radius: $radius-s;
        }

        .poi-content {
          margin-left: 12px;
          display: flex;
          flex-direction: column;
          justify-content: center;

          .name {
            font-size: $fontSize-body-s;
            line-height: 20px;
            color: $color-text-primary;
            font-weight: $fontWeight-bold;
            word-break: break-all;
          }

          .info {
            font-size: $fontSize-caption-m;
            line-height: 16px;
            color: $color-text-disabled;
            margin-top: 2px;
          }
        }
      }
    }
  }
}
</style>
