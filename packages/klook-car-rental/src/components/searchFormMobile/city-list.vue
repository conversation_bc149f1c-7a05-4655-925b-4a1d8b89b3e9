<template>
  <div
    ref="cityListPage"
    :data-spm-page="pageModule"
    class="city-list-wrap"
  >
    <search-list
      :completePlaceListV3="completePlaceListV3"
      :loading="loading"
      :custom-module="customSpm"
      @focus-search-input="focusSearchInput"
      @get-complete-list="getCompleteList"
      @set-search-place="setSearchPlace"
      @go-poi-list="goPoiList"
      @close-search-list="showSearchList = false"
      @close="closePopup"
    ></search-list>
    <div v-if="loadingCityList" class="loading-wrap">
      <klk-loading></klk-loading>
    </div>
    <template v-if="!showSearchList && !loadingCityList">
      <!-- <div class="city-header">
        <h1 v-if="isDomestic" class="title">{{ $t('17802-domestic_cities') }}</h1>
        <h1 v-else class="title">{{ $t('17803-international_cities') }}</h1>
        <div
          :data-spm-module="`${isDomestic ? 'ChangeOverseaCity' : 'ChangeDomesticCity'}?trg=manual`"
          data-spm-virtual-item="__virtual?typ=entry"
          class="switch-btn"
          @click="switchCity"
        >
          <SvgIcon name="mobile-car-rental#icon-switch" width="16" height="16" />
          <span v-if="isDomestic">{{ $t('17803-international_cities') }}</span>
          <span v-else>{{ $t('17802-domestic_cities') }}</span>
        </div>
      </div> -->
      <div v-if="cityList && cityList.continent_tag_list && cityList.continent_tag_list.length > 0" class="continent-nav">
        <klk-card-swiper
          ref="cardSwiper"
          :item-gap="8"
          :scroll-mode="true"
          item-width="auto"
          data-spm-module="SelectArea"
          v-galileo-click-tracker="galileoClick1"
          data-spm-virtual-item="__virtual?typ=entry"
        >
          <klk-card-swiper-item @click="tagclick('all')">
            <div
              class="nav-item"
              :class="{'active': tagName === 'all'}"
            >
              {{ $t('16867-All') }}
            </div>
          </klk-card-swiper-item>
          <klk-card-swiper-item
            v-if="cityList && cityList.popular_city_list && cityList.popular_city_list.length > 0"
            @click="tagclick('popular')"
          >
            <div
              class="nav-item"
              :class="{'active': tagName === 'popular'}"
            >
              {{ cityList.popular_city_list_title }}
            </div>
          </klk-card-swiper-item>
          <klk-card-swiper-item
            v-for="(item, index) in cityList.continent_tag_list"
            :key="index + 2"
            ref="cards"
            @click="tagclick(item.name, index)"
          >
            <div class="nav-item" :class="{'active': tagName === item.name}">{{ item.name }}</div>
          </klk-card-swiper-item>
        </klk-card-swiper>
        <div class="alpha-block"></div>
      </div>
      <div v-if="tagName === 'all' && searchHistoryList.length > 0" class="category-item">
        <h3 class="caregory-title">
          <span>{{ $t('17805-search_history') }}</span>
          <i
            :data-spm-module="`ClearHistory?oid=city_${trackCityId}`"
            v-galileo-click-tracker="galileoClick2"
            data-spm-virtual-item="__virtual?typ=entry"
            @click="removeHistory"
          >
            <img src="https://res.klook.com/image/upload/web3.0/icon-remove_axnprh.svg" width="16" height="16">
          </i>
        </h3>
        <ul class="caregory-list">
          <li
            v-for="(item, index) in searchHistoryList"
            :key="index"
            class="poi-item"
            :data-spm-module="`SearchHistory_LIST?oid=city_${item.city_id}`"
            v-galileo-click-tracker="galileoClick3"
            data-spm-virtual-item="__virtual?typ=entry"
            @click="setSearchPlace(item)"
          >
            <i class="poi-icon">
              <img :src="item.image_url" width="20" height="20">
            </i>
            <div class="poi-content">
              <p class="name">{{ item.poi_name }}</p>
              <span class="info">{{ item.city_name }}</span>
            </div>
          </li>
        </ul>
      </div>
      <div v-if="cityList && cityList.popular_city_list && cityList.popular_city_list.length > 0 && (tagName === 'all' || tagName === 'popular')" class="category-item">
        <h3 class="caregory-title">
          <span>{{ cityList.popular_city_list_title }}</span>
        </h3>
        <ul class="caregory-list city">
          <li
            v-for="city in cityList.popular_city_list"
            :key="city.city_id"
            class="city-item"
            :data-spm-module="`CarRental_City_POI?oid=city_${city.city_id}`"
            v-galileo-click-tracker="galileoClick4"
            data-spm-virtual-item="__virtual?typ=entry"
            @click="goPoiList(city.city_id)"
          >
            {{ city.city_name }}
          </li>
        </ul>
      </div>
      <div v-if="cityList && cityList.continent_country_city_list && cityList.continent_country_city_list.length > 0" class="continent-list-body">
        <div
          v-for="(item, index) in cityList.continent_country_city_list"
          :key="index"
          class="continent-item"
        >
          <template v-if="tagName === item.continent_name || tagName === 'all'">
            <h2 class="continent-name">
              <i></i>
              {{ item.continent_name }}
            </h2>
            <template v-if="item.country_list && item.country_list.length > 0">
              <div
                v-for="country in item.country_list"
                :key="country.country_id"
                class="category-item"
              >
                <h3 class="caregory-title">
                  <span>{{ country.country_name }}</span>
                </h3>
                <ul v-if="country.city_list && country.city_list.length > 0" class="caregory-list city">
                  <li
                    v-for="(city, idx) in country.city_list"
                    :key="idx"
                    class="city-item"
                    :data-spm-module="`OverseaCities?trg=manual&oid=city_${city.city_id}`"
                    v-galileo-click-tracker="galileoClick5"
                    data-spm-virtual-item="__virtual?typ=entry"
                    @click="goPoiList(city.city_id)"
                  >
                    {{ city.city_name }}
                  </li>
                </ul>
              </div>
            </template>
          </template>
        </div>
      </div>
    </template>
  </div>
</template>

<script lang="ts">
import { Component, Prop, Ref, Watch } from 'vue-property-decorator'

import SearchList from './search-list.vue'
import { CarRental } from '../../types/car-rental'
import Base from "../../common/base";
import { WebClick_101, WebClick_102, WebClick_103, WebClick_104, WebClick_105 } from '../../../share/galileo/auto-click'

@Component({
  components: {
    SearchList
  }
})
export default class CityList extends Base {
  @Prop({ type: Boolean, default: true }) loading!: boolean
  @Prop({ type: Boolean, default: true }) loadingCityList!: boolean
  @Prop({ type: Boolean, default: true }) isLocalPoi!: boolean
  @Prop({ type: Number, default: null }) trackCityId!: number
  @Prop({ type: Object, default: () => null }) cityList!: any
  @Prop({ type: Object, default: null }) completePlaceListV3!: any
  @Prop({
    type: Object,
    default: () => {
      return {
        domestic: [],
        international: []
      }
    }
  }) searchHistory!: any

  @Ref() cardSwiper!: any
  @Ref() cards!: any

  showSearchList: boolean = false
  tagName: string = 'all'

  get searchHistoryList() {
    return this.searchHistory.international
  }

  get pageModule() {
    return `CarRental_Location_POI?trg=manual&oid=city_${this.trackCityId || null}`
  }

  get customSpm() {
    return 'CarRental_Location_POI.SearchBar.SearchInput'
  }

  @Watch('isDomestic')
  changeCity() {
    this.$nextTick(function () {
      this.$inhouse.track('pageview', this.$refs.cityListPage, { force: true })
    })
  }

  mounted() {
    // 给city list page单独设置pageview埋点
    this.$nextTick(function () {
      this.$nextTick(function () {
        this.$inhouse.track('pageview', this.$refs.cityListPage, { force: true })
      })
    })
  }

  closePopup() {
    this.$emit('close')
  }

  // 切换城市
  switchCity() {
    this.tagName = 'all'
    this.$emit('update:loading-city-list', true)
    this.$emit('update:is-local-poi', false)
    this.$emit('get-city-list', false)
  }

  tagclick(name: string, index?: number) {
    const ww = window.innerWidth - 32
    this.tagName = name
    if (index !== undefined) {
      const cardEl = this.cards[index].$el
      const offsetLeft = cardEl.offsetLeft
      if (offsetLeft >= ww - 100) {
        cardEl.parentNode.parentNode.scrollLeft = offsetLeft - 20
      } else {
        cardEl.parentNode.parentNode.scrollLeft = 0
      }
    }
    document.querySelector('.klk-modal-body')!.scrollTop = 0
  }

  goPoiList(id: number) {
    this.$emit('go-poi-list', id)
  }

  // 清空历史记录
  removeHistory() {
    this.$emit('remove-history', false)
  }

  focusSearchInput() {
    this.showSearchList = true
  }

  getCompleteList(val: string) {
    if (val) {
      this.$inhouse.track('custom', '.city-list-wrap', {
        spm: this.customSpm
      })
    }
    this.$emit('get-complete-list', { keyWord: val, isDomestic: false })
  }

  setSearchPlace(poi: CarRental.Poi) {
    this.$emit('set-search-place', poi)
  }

    get galileoClick1() {
        return { spm: WebClick_101, autoTrackSpm: true }
    }

    get galileoClick2() {
        return { spm: WebClick_102, autoTrackSpm: true }
    }

    get galileoClick3() {
        return { spm: WebClick_103, autoTrackSpm: true }
    }

    get galileoClick4() {
        return { spm: WebClick_104, autoTrackSpm: true }
    }

    get galileoClick5() {
        return { spm: WebClick_105, autoTrackSpm: true }
    }
}
</script>

<style lang="scss" scoped>
.city-list-wrap {
  .blank {
    height: 56px;
  }

  .loading-wrap {
    padding: 0 16px;
    margin-top: 124px;
    position: relative;
    height: 300px;
  }

  .city-header {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    padding: 20px 16px 0;

    .title {
      font-size: $fontSize-body-m;
      font-weight: $fontWeight-bold;
      color: $color-text-primary;
      line-height: 22px;
    }

    .switch-btn {
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;

      span {
        font-size: $fontSize-caption-m;
        margin-left: 4px;
        color: $color-text-secondary;
      }
    }
  }

  .continent-nav {
    width: 100%;
    margin-top: 12px;
    padding: 12px 16px 8px;
    position: sticky;
    top: 56px;
    left: 0;
    background-color: $color-bg-widget-normal;
    z-index: 10;

    .nav-item {
      padding: 6px 12px;
      font-size: $fontSize-caption-m;
      line-height: 16px;
      border: 0.5px solid $color-border-normal;
      /* stylelint-disable */
      border-radius: 14px;
      /* stylelint-enable */
      color: $color-text-primary;

      &.active {
        border: none;
        background-color: $color-brand-primary;
        color: $color-text-primary-onDark;
      }
    }

    .alpha-block {
      width: 12px;
      height: 100%;
      position: absolute;
      right: 0;
      top: 0;
      background: linear-gradient(90deg, rgba(255, 255, 255, 0.0001) 0%, #fff 100%);
    }

    ::v-deep .klk-card-swiper-items-wrapper {
      scroll-behavior: smooth;
    }
  }

  .category-item {
    margin-top: 28px;
    padding: 0 16px;

    .caregory-title {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      font-size: $fontSize-caption-m;
      font-weight: $fontWeight-bold;
      color: $color-text-primary;
      line-height: 16px;
    }

    .caregory-list {
      padding-top: 6px;

      &.city {
        padding-top: 8px;
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
      }

      .poi-item {
        display: flex;
        flex-direction: row;
        padding: 10px 0;

        &:last-child {
          padding-bottom: 0;
        }

        .poi-icon {
          flex: 40px 0 0;
          width: 40px;
          height: 40px;
          display: flex;
          justify-content: center;
          align-items: center;
          background-color: $color-bg-page;
          border-radius: $radius-s;
        }

        .poi-content {
          margin-left: 12px;
          display: flex;
          flex-direction: column;
          justify-content: center;

          .name {
            font-size: $fontSize-body-s;
            line-height: 20px;
            color: $color-text-primary;
            font-weight: $fontWeight-bold;
            word-break: break-all;
          }

          .info {
            font-size: $fontSize-caption-m;
            line-height: 16px;
            color: $color-text-disabled;
            margin-top: 2px;
          }
        }
      }

      .city-item {
        padding: 6px 10px;
        font-size: $fontSize-body-s;
        line-height: 20px;
        margin-top: 8px;
        margin-right: 8px;
        border-radius: $radius-s;
        color: $color-text-primary;
        border: 0.5px solid $color-border-normal;
      }
    }
  }

  .continent-list-body {
    padding: 0 16px 16px;

    .continent-item {
      margin-top: 28px;

      .continent-name {
        width: 100%;
        height: 28px;
        line-height: 28px;
        background-color: $color-bg-widget-darker-1;
        padding-left: 11px;
        font-size: $fontSize-body-s;
        font-weight: $fontWeight-bold;
        display: flex;
        flex-direction: row;
        align-items: center;

        > i {
          width: 3px;
          height: 14px;
          background-color: $color-brand-primary;
          border-radius: $radius-s;
          margin-right: 8px;
        }
      }

      .category-item {
        padding: 0;
      }
    }
  }

  ::v-deep .klk-input-inner {
    min-height: 36px;
    border: none;
    background-color: $color-bg-page;
    /* stylelint-disable */
    border-radius: 20px;
    /* stylelint-enable */
  }
}
</style>
