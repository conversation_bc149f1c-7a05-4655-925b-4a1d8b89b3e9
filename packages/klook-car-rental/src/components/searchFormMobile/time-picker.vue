<template>
  <div class="picker-panel" @click.stop>
    <div class="picker-content">
      <div class="mask-top border-bottom-1px"></div>
      <div class="mask-bottom border-top-1px"></div>
      <div ref="wheelWrapper" class="wheel-wrapper">
        <div v-for="(data, index) in pickerData" :key="index" class="wheel">
          <ul class="wheel-scroll">
            <li
              v-for="item in data"
              :key="item.value"
              class="wheel-item"
            >
              {{ item.label }}
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Prop, Ref, Watch } from 'vue-property-decorator'
import BScroll from '@better-scroll/core'
import Wheel from '@better-scroll/wheel'
import Base from "../../common/base";

BScroll.use(Wheel)

@Component
export default class TimePicker extends Base {
  @Ref() wheelWrapper!: any
  @Prop({ type: String, default: '' }) hour!: string
  @Prop({ type: String, default: '' }) minute!: string
  @Prop({ type: String, default: '' }) activeType!: string

  wheels: any[] = []
  selectHour: any = this.hour
  selectMinute: any = this.minute

  get selectedIndex() {
    return [
      parseInt(this.hour),
      this.minute === '00' ? 0 : 1
    ]
  }

  @Watch('hour')
  changeHour() {
    this.show()
  }

  get hourList() {
    const list: any[] = []
    for (let i = 0; i < 24; i++) {
      if (i < 10) {
        list.push({
          label: '0' + i,
          value: i
        })
      } else {
        list.push({
          label: String(i),
          value: i
        })
      }
    }
    return list
  }

  get minuteList() {
    return [
      {
        label: '00',
        value: 0
      }, {
        label: '30',
        value: 30
      }
    ]
  }

  get pickerData() {
    return [this.hourList, this.minuteList]
  }

  show() {
    if (!this.wheels.length) {
      this.$nextTick(() => {
        this.wheels = []
        const wheelWrapper = this.wheelWrapper
        for (let i = 0; i < this.pickerData.length; i++) {
          this._createWheel(wheelWrapper, i)
        }
      })
    } else {
      for (let i = 0; i < this.pickerData.length; i++) {
        this.wheels[i].enable()
        this.wheels[i].wheelTo(this.selectedIndex[i])
      }
    }
  }

  refresh() {
    this.$nextTick(() => {
      this.wheels.forEach((wheel) => {
        wheel.refresh()
      })
    })
  }

  _createWheel(wheelWrapper: any, i: number) {
    if (!this.wheels[i]) {
      this.wheels[i] = new BScroll(wheelWrapper.children[i], {
        wheel: {
          selectedIndex: this.selectedIndex[i],
          wheelWrapperClass: 'wheel-scroll',
          wheelItemClass: 'wheel-item',
          rotate: 10
        },
        probeType: 3
      })
      this.wheels[i].on('scrollEnd', () => {
        if (i === 0) {
          this.selectHour = this.wheels[i].getSelectedIndex() < 10 ? `0${this.wheels[i].getSelectedIndex()}` : this.wheels[i].getSelectedIndex()
        } else {
          this.selectMinute = this.wheels[i].getSelectedIndex() === 0 ? '00' : '30'
        }
        this.$emit('set-wheel-date-time', `${this.selectHour}:${this.selectMinute}`)
      })
    } else {
      this.wheels[i].refresh()
    }
    return this.wheels[i]
  }
}
</script>

<style scoped lang="scss">
.example-list {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  margin: 2rem;

  .example-item {
    background-color: $color-bg-widget-normal;
    padding: 0.8rem;
    border: 1px solid rgba(0, 0, 0, 0.1);
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.1);
    text-align: center;
    margin-bottom: 1rem;
    flex: 1;

    &.placeholder {
      visibility: hidden;
      height: 0;
      margin: 0;
      padding: 0;
    }
  }
}

.picker-panel {
  width: 100%;
  height: 100%;
  background: white;

  &.picker-move-enter,
  &.picker-move-leave-active {
    transform: translate3d(0, 273px, 0);
  }

  &.picker-move-enter-active,
  &.picker-move-leave-active {
    transition: all 0.3s ease-in-out;
  }

  .picker-choose {
    position: relative;
    height: 60px;
    color: $color-text-secondary;

    .picker-title {
      margin: 0;
      line-height: 60px;
      font-weight: $fontWeight-regular;
      text-align: center;
      font-size: $fontSize-body-l;
      color: $color-text-primary;
    }

    .confirm,
    .cancel {
      position: absolute;
      top: 6px;
      padding: 16px;
      font-size: $fontSize-body-s;
    }

    .confirm {
      right: 0;
      color: $color-info;

      &:active {
        color: $color-info;
      }
    }

    .cancel {
      left: 0;

      &:active {
        color: $color-text-disabled;
      }
    }
  }

  .picker-content {
    position: relative;
    top: 20px;

    .mask-top,
    .mask-bottom {
      z-index: 10;
      width: 100%;
      height: 120px;
      pointer-events: none;
      transform: translateZ(0);
    }

    .mask-top {
      position: absolute;
      top: -18px;
      border-bottom: 1px solid rgba(0, 0, 0, 0.12);
      background: linear-gradient(to top, rgba(255, 255, 255, 0.4), rgba(255, 255, 255, 0.8));
    }

    .mask-bottom {
      position: absolute;
      bottom: 0;
      border-top: 1px solid rgba(0, 0, 0, 0.12);
      background: linear-gradient(to bottom, rgba(255, 255, 255, 0.4), rgba(255, 255, 255, 0.8));
    }
  }

  .wheel-wrapper {
    display: flex;
    padding: 0 16px;
    position: relative;

    &::before {
      content: ":";
      position: absolute;
      top: 114px;
      left: 50%;
      transform: translateX(-50%);
    }

    .wheel {
      flex: 1;
      height: 260px;
      overflow: hidden;
      font-size: $fontSize-body-l;
      text-align: right;
      padding-right: 20px;

      &:last-child {
        text-align: left;
        padding-right: 0;
      }

      .wheel-scroll {
        padding: 0;
        margin-top: 105px;
        line-height: 36px;
        list-style: none;

        .wheel-item {
          list-style: none;
          height: 36px;
          overflow: hidden;
          white-space: nowrap;
          color: $color-text-primary;

          &.wheel-disabled-item {
            opacity: $opacity-pressed;
          }
        }
      }
    }
  }
}
</style>
