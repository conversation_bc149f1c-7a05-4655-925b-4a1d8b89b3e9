<template>
  <div class="date-time-picker">
    <div class="picker-header">
      <i class="close-btn" @click="closePicker">
        <!-- <SvgIcon name="mobile-common#icon-close" width="24" height="24" /> -->
        <img src="https://res.klook.com/image/upload/web3.0/icon-close_puzrvw.svg" width="24" height="24">
      </i>
      <div class="date-time-range">
        <!-- 取还车时间选择 -->
        <div class="pickup date-time">{{ pickUpDateTimeText }}</div>
        <span class="days-count">{{ $t('car_rental_home_days_desc', [diffDay]) }}</span>
        <div class="dropoff date-time">{{ dropOffDateTimeText }}</div>
      </div>
    </div>
    <div class="picker-wrap" :style="{height: pickerHeight}">
      <klk-date-picker
        v-if="pickerVisible"
        ref="datepicker"
        type="date-range"
        width="auto"
        :date.sync="dateArray"
        :min-date="minDate"
        :max-date="maxDate"
        :min-range-gap="0"
        :vertical-scroll="true"
        :vertical-month-offset="monthOffset"
        @select="selectDateTime"
        @change="changeDateTime"
      >
      </klk-date-picker>
      <klk-loading v-else></klk-loading>
    </div>
    <div class="picker-footer">
      <div class="time-picker">
        <div class="time-item" @click="openTimePicker('pick-up')">
          <span class="title">{{ $t('10213-pick_up_time') }}</span>
          <div class="content">
            <span>{{ pickupTimeText }}</span>
            <!-- <SvgIcon name="mobile-car-rental#navigation_down_24" size="24"></SvgIcon> -->
            <img src="https://res.klook.com/image/upload/web3.0/navigation_down_24_dp5ygc.svg" width="24" height="24">
          </div>
        </div>
        <div class="time-item" @click="openTimePicker('drop-off')">
          <span class="title">{{ $t('10214-drop_off_time') }}</span>
          <div class="content">
            <span>{{ dropoffTimeText }}</span>
            <!-- <SvgIcon name="mobile-car-rental#navigation_down_24" size="24"></SvgIcon> -->
            <img src="https://res.klook.com/image/upload/web3.0/navigation_down_24_dp5ygc.svg" width="24" height="24">
          </div>
        </div>
      </div>
      <div class="complate-btn">
        <klk-button v-if="btnActivated" type="primary" block @click="confirm">{{ $t('14052-confirm') }}</klk-button>
        <klk-button v-if="!btnActivated" type="primary" block disabled>{{ $t('14052-confirm') }}</klk-button>
      </div>
    </div>
    <klk-bottom-sheet :visible.sync="showTimePicker" :can-pull-close="false">
      <div class="time-picker-wrap">
        <div class="time-picker">
          <div class="time-item" @click="switchTimePicker('pick-up')">
            <span class="title">{{ $t('10213-pick_up_time') }}</span>
            <div class="content" :class="{active: activeType === 'pick-up'}">
              <span>{{ wheelPickupTimeText }}</span>
              <!-- <SvgIcon v-if="activeType !== 'pick-up'" name="mobile-car-rental#navigation_down_24" size="24"></SvgIcon> -->
              <img v-if="activeType !== 'pick-up'" src="https://res.klook.com/image/upload/web3.0/navigation_down_24_dp5ygc.svg" width="24" height="24">
              <!-- <SvgIcon v-if="activeType === 'pick-up'" name="mobile-car-rental#navigation_up_24" size="24"></SvgIcon> -->
              <img v-if="activeType === 'pick-up'" src="https://res.klook.com/image/upload/web3.0/navigation_up_24_hutcwf.svg" width="24" height="24">
            </div>
          </div>
          <div class="time-item" @click="switchTimePicker('drop-off')">
            <span class="title">{{ $t('10214-drop_off_time') }}</span>
            <div class="content" :class="{active: activeType === 'drop-off'}">
              <span>{{ wheelDropoffTimeText }}</span>
              <!-- <SvgIcon v-if="activeType !== 'drop-off'" name="mobile-car-rental#navigation_down_24" size="24"></SvgIcon> -->
              <img v-if="activeType !== 'drop-off'" src="https://res.klook.com/image/upload/web3.0/navigation_down_24_dp5ygc.svg" width="24" height="24">
              <!-- <SvgIcon v-if="activeType === 'drop-off'" name="mobile-car-rental#navigation_up_24" size="24"></SvgIcon> -->
              <img v-if="activeType === 'drop-off'" src="https://res.klook.com/image/upload/web3.0/navigation_up_24_hutcwf.svg" width="24" height="24">
            </div>
          </div>
        </div>
        <div class="picker-body">
          <time-picker
            ref="timepicker"
            :hour="dateTime.hour"
            :minute="dateTime.minute"
            :type="activeType"
            @set-wheel-date-time="setWheelDateTime($event)"
          ></time-picker>
        </div>
        <p class="time-tips">
          <!-- <SvgIcon name="mobile-car-rental#tips2" width="14" height="14" @click.native="showAgeTips = true"></SvgIcon> -->
          <img src="https://res.klook.com/image/upload/web3.0/tips2_fncwss.svg" width="14" height="14">
          <span>{{ $t('10217') }}，{{ $t('10218') }}</span>
        </p>
        <div class="time-complate-btn">
          <klk-button type="outlined" block @click="closeTimePicker">{{ $t('12577-cancel') }}</klk-button>
          <klk-button type="primary" block @click="confirmTime">{{ $t('14052-confirm') }}</klk-button>
        </div>
      </div>
    </klk-bottom-sheet>
    <div v-if="time24 && show24Tips" class="time24">
      <!-- <SvgIcon name="mobile-common#icon-info-yellow" size="20"></SvgIcon> -->
      <img src="https://res.klook.com/image/upload/web3.0/icon-info-yellow_gaxt2z.svg" width="20" height="20">
      <p v-if="showDateBeforeTips">{{ $t('10221-car_rental_home_select_date_before_tip') }}</p>
      <p v-else>{{ $t('car_rental_home_date_rule_mention') }}</p>
      <!-- <SvgIcon name="mobile-common#icon-close-yellow" size="16" @click.native="close24Time"></SvgIcon> -->
      <img src="https://res.klook.com/image/upload/web3.0/icon-close-yellow_da5sdn.svg" width="16" height="16" @click.native="close24Time">
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Prop, Ref, Watch } from 'vue-property-decorator'
import dayjs from 'dayjs'
import TimePicker from './time-picker.vue'
import { getStandardDateFormat } from '../../common/datetime'
import Base from "../../common/base";

const PICKUP = 'pick-up'
const DROPOFF = 'drop-off'
const ONE_DAY = 86400000

interface timeObject extends Object {
  hour: string,
  minute: string
}

@Component({
  components: {
    TimePicker
  }
})
export default class DateTimePicker extends Base {
  @Ref() readonly timepicker!: TimePicker
  @Ref() setViewDate!: any
  @Prop({ type: Object, default: () => {} }) pickUpDate!: any
  @Prop({ type: Object, default: () => {} }) dropOffDate!: any
  @Prop({ type: Number, default: 0 }) scrollTop!: number
  @Prop({ type: Boolean, default: false }) visible!: boolean
  pickerHeight: string = '449px'
  showTimePicker: boolean = false
  dateArray: Date[] = []
  confirmDateTime: any[] = []
  monthOffset: number[] = [-12, 12]
  minDate: Date = dayjs().subtract(1, 'day').toDate()
  maxDate: Date = dayjs().add(1, 'year').toDate()
  btnActivated: boolean = true
  activeType: string = PICKUP
  show24Tips: boolean = true
  showDateBeforeTips: boolean = true

  // 分段渲染弹窗内容，优化INP
  pickerVisible: boolean = false
  pickerVisibleTimer: any = null

  pickupTime: timeObject = {
    hour: this.pickUpDate.hour,
    minute: this.pickUpDate.minute
  }

  dropoffTime: timeObject = {
    hour: this.dropOffDate.hour,
    minute: this.dropOffDate.minute
  }

  switchPickupTime: timeObject = {
    hour: this.pickUpDate.hour,
    minute: this.pickUpDate.minute
  }

  switchDropoffTime: timeObject = {
    hour: this.dropOffDate.hour,
    minute: this.dropOffDate.minute
  }

  dateTime: timeObject = {
    hour: '10',
    minute: '00'
  }

  wheelPickupTimeText: string = `${this.pickupTime.hour}:${this.pickupTime.minute}`
  wheelDropoffTimeText: string = `${this.dropoffTime.hour}:${this.dropoffTime.minute}`

  get pickupTimeText() {
    const unit = this.pickupTime
    if (unit.hour && unit.minute) {
      return `${unit.hour}:${unit.minute}`
    }
  }

  get dropoffTimeText() {
    const unit = this.dropoffTime
    if (unit.hour && unit.minute) {
      return `${unit.hour}:${unit.minute}`
    }
  }

  @Watch('visible', { immediate: true })
  onDatePickerVisibleChange(val: boolean) {
    if (val) {
      clearTimeout(this.pickerVisibleTimer)
      this.pickerVisibleTimer = setTimeout(() => {
        this.pickerVisible = val
      }, 0)
    }
  }

  @Watch('scrollTop')
  pickScrollTop(val: number) {
    setTimeout(() => {
      const pickerPanels = document.getElementsByClassName('klk-date-picker-panels')[0]
      if (pickerPanels) {
        pickerPanels.scrollTop = val
      }
    }, 100)
  }

  created() {
    const h = window.innerHeight
    this.pickerHeight = h - 204 + 'px'
    this.dateArray = [
      dayjs(this.pickUpDate.date).toDate(),
      dayjs(this.dropOffDate.date).toDate()
    ]
    this.confirmDateTime = this.dateArray
  }

  beforeDestroy() {
    if (this.pickerVisibleTimer) {
      clearTimeout(this.pickerVisibleTimer)
    }
  }

  get pickUpDateTimeText() {
    const pick = this.formatDateTime(this.confirmDateTime)[0]
    return this.formatDate(pick.toDate())
  }

  get dropOffDateTimeText() {
    const drop = this.formatDateTime(this.confirmDateTime)[1]
    return this.formatDate(drop.toDate())
  }

  get diffDay() {
    const [pick, drop] = this.formatDateTime(this.confirmDateTime)
    if (pick && drop) {
      const diff = drop.diff(pick)
      const date = drop.diff(pick, 'day')
      const d = diff % ONE_DAY > 0 ? date + 1 : date
      return (diff > 0 && d > 0) ? `${d} ` : ''
    }
    return ''
  }

  get time24() {
    const [pick, drop] = this.formatDateTime(this.confirmDateTime)
    const hour = drop.diff(pick, 'hour')
    const minute = drop.diff(pick, 'minute')
    const diffHour = (hour === 0 && minute > 0) ? 1 : hour
    if (diffHour > 0) {
      this.showDateBeforeTips = false
      this.btnActivated = true
      if (hour < 24) {
        this.show24Tips = true
        return true
      } else {
        this.show24Tips = false
        return false
      }
    } else {
      this.show24Tips = true
      this.showDateBeforeTips = true
      this.btnActivated = false
      return true
    }
  }

  closePicker() {
    this.$emit('close')
  }

  closeTimePicker() {
    this.showTimePicker = false
  }

  openTimePicker(type: string) {
    const that = this
    that.showTimePicker = true
    that.setTime(type)

    // 激活bscroll-wheel
    that.$nextTick(() => {
      that.timepicker.show()
    })
  }

  close24Time() {
    this.show24Tips = false
  }

  closeDateBefore() {
    this.showDateBeforeTips = false
  }

  setTime(type: string) {
    if (type === PICKUP) {
      this.activeType = PICKUP
      this.dateTime.hour = this.pickupTime.hour
      this.dateTime.minute = this.pickupTime.minute
    }
    if (type === DROPOFF) {
      this.activeType = DROPOFF
      this.dateTime.hour = this.dropoffTime.hour
      this.dateTime.minute = this.dropoffTime.minute
    }
    this.wheelPickupTimeText = `${this.pickupTime.hour}:${this.pickupTime.minute}`
    this.wheelDropoffTimeText = `${this.dropoffTime.hour}:${this.dropoffTime.minute}`
  }

  setSwitchTime(type: string) {
    if (type === PICKUP) {
      const time = this.wheelPickupTimeText.split(':')
      const hour = time[0]
      const minute = time[1]
      this.dateTime.hour = hour
      this.dateTime.minute = minute
      this.activeType = PICKUP
    }
    if (type === DROPOFF) {
      const time = this.wheelDropoffTimeText.split(':')
      const hour = time[0]
      const minute = time[1]
      this.dateTime.hour = hour
      this.dateTime.minute = minute
      this.activeType = DROPOFF
    }
  }

  changeDateTime(date: Date[]) {
    if (Array.isArray(date)) {
      this.btnActivated = true
      this.confirmDateTime = date
    }
  }

  formatDateTime(date: Date[]) {
    const pHour = parseInt(this.pickupTime.hour)
    const pmiunte = parseInt(this.pickupTime.minute)
    const dHour = parseInt(this.dropoffTime.hour)
    const dMinute = parseInt(this.dropoffTime.minute)
    const pickDt = dayjs(date[0]).hour(pHour).minute(pmiunte)
    const dropDt = dayjs(date[1]).hour(dHour).minute(dMinute)
    return [
      pickDt,
      dropDt
    ]
  }

  selectDateTime() {
    this.btnActivated = false
  }

  switchTimePicker(type: string) {
    this.setSwitchTime(type)
  }

  setWheelDateTime(val: string) {
    if (this.activeType === PICKUP) {
      this.wheelPickupTimeText = val
    }
    if (this.activeType === DROPOFF) {
      this.wheelDropoffTimeText = val
    }
  }

  confirmTime() {
    const pickupTime = this.wheelPickupTimeText.split(':')
    const dropoffTime = this.wheelDropoffTimeText.split(':')
    this.pickupTime = {
      hour: pickupTime[0],
      minute: pickupTime[1]
    }
    this.dropoffTime = {
      hour: dropoffTime[0],
      minute: dropoffTime[1]
    }
    this.closeTimePicker()
  }

  confirm() {
    const confirmDateTime = this.formatDateTime(this.confirmDateTime)
    this.$emit('confirm-date-time', confirmDateTime)
    this.closePicker()
  }

  formatDate(date: any) {
    if (!date) {
      return ''
    }
    const { language } = this.$store.state.klook

    return getStandardDateFormat(date, this.$t.bind(this), language, 3) + ' ' + getStandardDateFormat(date, this.$t.bind(this), 'ja', 4)
  }
}
</script>

<style lang="scss" scoped>
.date-time-picker {
  max-height: 100%;
  overflow-y: scroll;
  display: flex;
  flex-direction: column;
  justify-content: space-between;

  .picker-header {
    width: 100%;
    height: 48px;
    border-bottom: 0.5px solid $color-border-dim;
    background-color: $color-bg-widget-normal;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 10;

    .close-btn {
      width: 48px;
      height: 48px;
      display: flex;
      justify-content: center;
      align-items: center;
      position: absolute;
      top: 0;
      left: 0;
      z-index: 1;
    }

    .date-time-range {
      height: 100%;
      position: relative;
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;

      .date-time {
        font-weight: $fontWeight-semibold;
        font-size: $fontSize-caption-m;
      }

      .days-count {
        min-width: 45px;
        height: 20px;
        padding: 3px 6px;
        border: 0.5px solid $color-info;
        border-radius: $radius-xl;
        line-height: 14px;
        font-size: $fontSize-caption-m;
        color: $color-info;
        text-align: center;
        margin: 0 10px;
        display: inline-block;
      }
    }
  }

  .picker-wrap {
    margin-top: 48px;
  }

  .picker-footer {
    background-color: $color-bg-widget-normal;
    box-shadow: 0 -2px 4px rgba(0, 0, 0, 0.08);
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    @at-root .time-picker {
      padding: 16px;
      display: flex;
      flex-direction: row;
      justify-content: space-between;

      .time-item {
        flex: 1;

        &:first-child {
          margin-right: 25px;
        }

        .title {
          font-size: $fontSize-caption-m;
          color: $color-text-secondary;
          line-height: 14px;
        }

        .content {
          height: 44px;
          display: flex;
          flex-direction: row;
          align-items: center;
          justify-content: space-between;
          box-shadow: inset 0 -0.5px 0 rgba(0, 0, 0, 0.87);

          &.active {
            box-shadow: inset 0 -0.5px 0 #ff5722;
          }

          span {
            font-size: $fontSize-body-m;
            font-weight: $fontWeight-semibold;
          }
        }
      }
    }

    .complate-btn {
      height: 60px;
      padding: 8px;
      box-shadow: inset 0 0.5px 0 rgba(0, 0, 0, 0.12);
    }
  }

  .time-picker-wrap {
    background-color: $color-bg-widget-normal;
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    .time-picker {
      padding: 0;
    }

    .picker-body {
      height: 310px;

      @media screen and (max-width: 320px) {
        height: 296px;
      }
    }

    .time-tips {
      margin-top: 16px;
      display: flex;
      flex-direction: row;

      span {
        font-size: $fontSize-caption-m;
        color: $color-text-secondary;
        line-height: 14px;
        margin-left: 5px;
      }
    }

    .time-complate-btn {
      height: 60px;
      padding: 8px 0;
      margin-top: 16px;
      box-shadow: inset 0 0.5px 0 rgba(0, 0, 0, 0.12);
      display: flex;
      flex-direction: row;

      button {
        &:first-child {
          margin-right: 9px;
        }
      }
    }
  }

  .time24 {
    width: 100%;
    padding: 12px 16px;
    font-size: $fontSize-caption-m;
    line-height: 20px;
    background-color: #fff7d9;
    color: $color-brand-secondary;
    position: fixed;
    top: 48px;
    left: 0;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: flex-start;

    p {
      flex: 1;
      padding: 0 10px;
      text-align: left;
    }
  }

  ::v-deep .klk-date-picker-vertical-scroll {
    min-height: auto;
  }
}
</style>
