<template>
  <div class="search-form-container">
    <div class="search-form-wrap">
      <!-- 是否异地还车钩选框 -->
      <div
        :data-spm-module="getModuleFormName('ReturnToDifferentLocation')"
        v-galileo-click-tracker.stop="galileoClick1"
        data-spm-virtual-item="__virtual?typ=entry"
        class="diff-local"
      >
        <klk-checkbox v-model="isDiffLocal" size="small">{{ $t('car_rental_home_diff_desc') }}</klk-checkbox>
      </div>
      <!-- poi搜索 -->
      <div class="search-input">
        <!-- 同一地点取还车 -->
        <div class="input-box" :class="{error: pickTip.error}">
          <div
            :data-spm-module="getModuleFormName('SearchPickup')"
            v-galileo-click-tracker="galileoClick2"
            data-spm-virtual-item="__virtual?typ=entry"
            class="location"
            @click="openCityList('pick-up')"
          >
            <span v-if="!searchListPick.poiName" class="no-value">{{ $t('car_rental_home_pick_up_loc') }}</span>
            <div v-else class="has-value">
              <i>{{ $t('car_rental_home_pick_up_loc') }}</i>
              <p>{{ searchListPick.poiName }}</p>
            </div>
          </div>
        </div>
        <!-- 不同地点取还车 -->
        <div v-show="isDiffLocal" class="input-box" :class="{error: dropTip.error}">
          <div
            :data-spm-module="getModuleFormName('DifferentSearchPickup')"
            v-galileo-click-tracker="galileoClick3"
            data-spm-virtual-item="__virtual?typ=entry"
            class="location"
            @click="openCityList('drop-off')"
          >
            <span v-if="!searchListDrop.poiName" class="no-value">{{ $t('car_rental_home_drop_off_loc') }}</span>
            <div v-else class="has-value">
              <i>{{ $t('car_rental_home_drop_off_loc') }}</i>
              <p>{{ searchListDrop.poiName }}</p>
            </div>
          </div>
        </div>
      </div>
      <!-- 日期选择 -->
      <div class="search-data-time">
        <div
          :data-spm-module="getModuleFormName('SearchDate')"
          v-galileo-click-tracker="galileoClick4"
          data-spm-virtual-item="__virtual?typ=entry"
          class="pickup date-time"
          @click="openDateTimePicker"
        >
          <b>{{ pickUpDateTimeText }}</b>
          <span v-if="pickUpDate.hour">{{ pickUpDate.hour }}:{{ pickUpDate.minute }}</span>
        </div>
        <div
          :data-spm-module="getModuleFormName('SearchDate')"
          v-galileo-click-tracker="galileoClick5"
          data-spm-virtual-item="__virtual?typ=entry"
          class="dropoff date-time"
          @click="openDateTimePicker"
        >
          <b>{{ dropOffDateTimeText }}</b>
          <span v-if="dropOffDate.hour">{{ dropOffDate.hour }}:{{ dropOffDate.minute }}</span>
        </div>
        <span class="days-count" @click="showDaysTips = true">{{ $t('car_rental_home_days_desc', [diffDay]) }}</span>
      </div>
      <!-- 年龄选择 -->
      <div class="driver-age">
        <div class="age">
          <span>{{ $t('car_rental_home_driver_age') }}{{ age.label }}</span>
          <!-- <SvgIcon class="tips-icon" name="mobile-car-rental#icon_tips_tips_xs" width="16" height="16" @click.native="showAgeTips = true"></SvgIcon> -->
          <img class="tips-icon" src="https://res.klook.com/image/upload/web3.0/icon_tips_tips_xs_gki5gl.svg" width="16" height="16" @click="showAgeTips = true">
        </div>
        <div class="change">
          {{ $t('change') }}
        </div>
        <select
          v-model="ageValue"
          :data-spm-module="getModuleFormName('ChangeDriverAge')"
          v-galileo-click-tracker="galileoClick6"
          data-spm-virtual-item="__virtual?typ=entry"
          class="age-select"
          @change="setAge(ageValue)"
        >
          <option v-for="(item, index) in ageList" :key="index" :value="item.value">{{ item.label }}</option>
        </select>
      </div>
      <div
        class="search-btn"
        :data-spm-module="getModuleFormName('SearchBtn')"
        v-galileo-click-tracker="galileoClick7"
        data-spm-virtual-item="__virtual?typ=entry"
      >
        <klk-button
          v-gtm:click="'Car Rental Vertical Page|Search_Search_Button_Click'"
          type="primary"
          block
          @click="search"
        >
          {{ $t('car_rental_home_search') }}
        </klk-button>
      </div>
      <!-- 取车天数浮层 -->
      <klk-bottom-sheet :visible.sync="showDaysTips" :show-close="true" :transfer="true">
        <div class="tips-box">
          <span>
            {{ $t('car_rental_home_date_rule_mention') }}
          </span>
        </div>
      </klk-bottom-sheet>
      <!-- 驾驶员年龄浮层 -->
      <klk-bottom-sheet :visible.sync="showAgeTips" :show-close="true" :transfer="true">
        <div class="tips-box">
          <div class="title">{{ $t('14618-car_rental_home_age_license_title') }}</div>
          <p>
            {{ $t('10202-car_rental_home_age_license_content') }}
          </p>
        </div>
      </klk-bottom-sheet>
      <!-- 时间日期选择 -->
      <klk-modal
        :open.sync="showDataTimePicker"
        fullscreen
        transition="slide-bottom"
        :show-default-footer="false"
        :delay-time="0"
      >
        <picker
          :pick-up-date="pickUpDate"
          :drop-off-date="dropOffDate"
          :scroll-top="pickerScrollTop"
          :visible="showDataTimePicker"
          @close="showDataTimePicker = false"
          @confirm-date-time="confirmDateTime($event)"
        ></picker>
      </klk-modal>
      <!-- 城市页 -->
      <klk-modal
        :open.sync="showCityPage"
        fullscreen
        scrollable
        :padding="0"
        transition="slide-bottom"
        :show-default-footer="false"
        :delay-time="0"
      >
        <div class="city-list-page">
          <city-list
            :completePlaceListV3="completePlaceListV3"
            :loading="searchList.loading"
            :loading-city-list.sync="loadingCityList"
            :is-local-poi.sync="isLocalPoi"
            :city-list="cityListObj"
            :search-history="searchHistory"
            :track-city-id="trackCityId"
            @get-city-list="getCityList"
            @go-poi-list="getPoiList"
            @close="closeListPage"
            @get-complete-list="getCompleteListByWord"
            @set-search-place="setSearchPlace($event)"
            @remove-history="removeHistory"
          ></city-list>
        </div>
      </klk-modal>
      <!-- POI页 -->
      <!-- 城市页 -->
      <klk-modal
        :open.sync="showPoiPage"
        fullscreen
        scrollable
        :padding="0"
        transition="slide-bottom"
        :show-default-footer="false"
      >
        <div class="city-list-page">
          <poi-list
            :completePlaceListV3="completePlaceListV3"
            :loading="searchList.loading"
            :loading-poi-list.sync="loadingPoiList"
            :is-local-poi.sync="isLocalPoi"
            :poi-list="cityPoiObj"
            :search-history="searchHistory"
            :track-city-id="trackCityId"
            @go-poi-list="getPoiList"
            @close="closeListPage"
            @change-city="changeCity"
            @get-complete-list="getCompleteListByWord"
            @set-search-place="setSearchPlace($event)"
            @remove-history="removeHistory"
          ></poi-list>
        </div>
      </klk-modal>
      <div
        v-if="showSearchInfoTip" 
        :class="{
          'warning-tips': true,
          'warning-tips-harmony': isOpenHarmony
        }"
      >
        {{ searchInfoTip }}
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Prop, Watch } from 'vue-property-decorator'
import dayjs from 'dayjs'
import SearchList from './search-list.vue'
import Picker from './data-time-picker.vue'
import CityList from './city-list.vue'
import PoiList from './poi-list.vue'
import { getStandardDateFormat } from '../../common/datetime'
import localStorageEx from '../../common/localstorage'
import Base from "../../common/base";
import { WebClick_81, WebClick_82, WebClick_83, WebClick_84, WebClick_85, WebClick_86, WebClick_87 } from '../../../share/galileo/auto-click'

const PICKUPLIST = 'pick-up'
const DROPOFFLIST = 'drop-off'
const CAR_RENTAL_COUNTRY = 'carRentalCountry'
const CAR_RENTAL_MWEB_SEARCH_HISTORY = 'carRentalMwebSearchHistory'
const ONE_DAY = 86400000
const l = localStorageEx()

interface ageObject extends Object {
  label: string,
  value: string
}
interface searchListObject extends Object {
  type: string,
  cityName: string,
  cityId: number,
  poiName: string,
  loading?: boolean
}

interface dateTimeObject extends Object {
  date: string,
  hour: string,
  minute: string
}

interface popTipObject extends Object {
  show: boolean,
  content: string
  error?: boolean
}

interface searchHistoryObject extends Object {
  domestic: any[]
  international: any[]
}

@Component({
  components: {
    SearchList,
    Picker,
    CityList,
    PoiList
  }
})
export default class SearchForm extends Base {
  @Prop({ type: Boolean, default: false }) updateUrl!: boolean
  @Prop({ type: Boolean, default: false }) useHomeConfig!: boolean
  @Prop({ type: String, default: '' }) currentPage!: string // home，dlp, result
  @Prop({ type: Boolean, default: false }) isAggregateTabs!: boolean

  isDiffLocal: boolean = false
  showDaysTips: boolean = false
  showAgeTips: boolean = false
  showCityPage: boolean = false
  showPoiPage: boolean = false
  showPoiSearch: boolean = false
  showDataTimePicker: boolean = false
  ageValue: string = '30'
  ageList: any[] = []
  pickerScrollTop: number = 0
  searchParams: any = {}
  searchLocalTitle: string = ''
  isLocal: boolean = false // 是否切换到本地tab
  countryCode: any = 'CN'
  cityListObj: any = null
  cityPoiObj: any = null
  loadingCityList: boolean = true
  loadingPoiList: boolean = true
  doUpdateUrl: boolean = true
  isInternationalPoi: boolean = true // 是否为境外数据
  isLocalPoi: boolean = this.isLocal
  trackCityId?: number
  isSearchPoi: boolean = false

  searchHistory: searchHistoryObject = {
    domestic: [],
    international: []
  }

  pickUpDate: dateTimeObject = {
    date: '',
    hour: '',
    minute: ''
  }

  dropOffDate: dateTimeObject = {
    date: '',
    hour: '',
    minute: ''
  }

  age: ageObject = {
    label: '30-65',
    value: '30'
  }

  searchList: searchListObject = {
    type: PICKUPLIST,
    cityName: '',
    cityId: 0,
    poiName: '',
    loading: false
  }

  searchListPick: searchListObject = {
    type: PICKUPLIST,
    cityName: '',
    cityId: 0,
    poiName: '',
    loading: false
  }

  searchListDrop: searchListObject = {
    type: DROPOFFLIST,
    cityName: '',
    cityId: 0,
    poiName: '',
    loading: false
  }

  pickTip: popTipObject = {
    show: false,
    content: 'Please select your pick-up location',
    error: false
  }

  dropTip: popTipObject = {
    show: false,
    content: 'Please select your pick-up location',
    error: false
  }

  place = {
    country_code: '',
    city_id: 0,
    city_name: '',
    poi_id: '',
    poi_name: '',
    image_url: '',
    address: '',
    longitude: '',
    latitude: '',
    poi_type: '',
    iata_code: ''
  }

  // 本地数据
  domesticData = {
    pickPlace: this.place,
    dropPlace: this.place,
    pickDate: this.pickUpDate,
    dropDate: this.dropOffDate,
    age: {
      label: '30-65',
      value: '30'
    },
    ageValue: this.ageValue,
    diffLoc: false,
    local: true
  }

  // 国际数据
  internationalData = {
    pickPlace: this.place,
    dropPlace: this.place,
    pickDate: this.pickUpDate,
    dropDate: this.dropOffDate,
    age: {
      label: '30-65',
      value: '30'
    },
    ageValue: this.ageValue,
    diffLoc: false,
    local: false
  }

  urlParams: any =  {
    pick: '',
    drop: '',
    diffLoc: '',
    pDate: '',
    dDate: '',
    age: '',
    lat: '',
    long: '',
    dLat: '',
    dLong: '',
    code: '',
    dCode: '',
    iata: '',
    dIata: '',
    pCityId: '',
    pCityName: '',
    pPoiId: '',
    dCityId: '',
    dCityName: '',
    dPoiId: ''
  }

  autocomplete_V3_city_id: any = null

  completePlaceListV3: any = null

  cityId: any = null

  get isOpenHarmony() {
    const ua = window.navigator.userAgent || ''
    const OHOS_SHELL_RE = /OpenHarmony[.\s\S]*klook.*ohos-shell.*/i
    const OHOS_RE = /OpenHarmony[.\s\S]*klook\/.*/i
    return OHOS_SHELL_RE.test(ua) || OHOS_RE.test(ua)
  }

  get pickUpDateTimeInput() {
    return `${this.pickUpDate.hour}:${this.pickUpDate.minute}`
  }

  get dropOffDateTimeInput() {
    return `${this.dropOffDate.hour}:${this.dropOffDate.minute}`
  }

  get showSearchInfoTip() {
    const { pickTip, dropTip } = this
    return pickTip.show || dropTip.show
  }

  get searchInfoTip() {
    const { pickTip, dropTip } = this
    if (pickTip.show) {
      return pickTip.content
    } else if (dropTip.show) {
      return dropTip.content
    }
  }

  // 用于计算日期 diff 的日期文本
  get pickUpDateTime() {
    const unit = this.pickUpDate
    if (unit.date && unit.hour && unit.minute) {
      return `${unit.date} ${unit.hour}:${unit.minute}`
    } else {
      return ''
    }
  }

  // 用于计算日期 diff 的日期文本
  get dropOffDateTime() {
    const unit = this.dropOffDate
    if (unit.date && unit.hour && unit.minute) {
      return `${unit.date} ${unit.hour}:${unit.minute}`
    } else {
      return ''
    }
  }

  // 多语言格式化后、仅供显示的日期文本
  get pickUpDateTimeText() {
    if (!this.pickUpDateTime) {
      return ''
    }
    return this.formatDate(this.pickUpDateTime)
  }

  // 多语言格式化后、仅供显示的日期文本
  get dropOffDateTimeText() {
    if (!this.dropOffDateTime) {
      return ''
    }
    return this.formatDate(this.dropOffDateTime)
  }

  get diffDay() {
    if (this.pickUpDateTime && this.dropOffDateTime) {
      const _p = this.pickUpDateTime.replace(/-/g, '/')
      const _d = this.dropOffDateTime.replace(/-/g, '/')
      const diff = dayjs(_d).diff(_p)
      const date = dayjs(_d).diff(_p, 'day')
      const d = diff % ONE_DAY > 0 ? date + 1 : date
      return (diff > 0 && d > 0) ? `${d} ` : ''
    }
    return ''
  }

  @Watch('isDiffLocal')
  checkboxValueChange(val: boolean) {
    this.setLocalInternationalData({ diffLoc: val }, false)
    this.dropTip.error = false
    if (!val) {
      this.searchListDrop.cityName = ''
      this.searchListDrop.poiName = ''
      // 清空还车地点
      this.setLocalInternationalData({
        dropPlace: this.place
      }, false)
    }
  }

  @Watch('isLocal')
  tabChange(val: boolean) {
    this.isLocalPoi = val
  }

  created() {
    this.pickTip.content = this.$t('car_rental_home_select_pick_tip')
    this.dropTip.content = this.$t('car_rental_home_select_drop_tip')
  }

  async mounted() {
    // 针对车麻吉滚动问题，进行fix
    const isAutopass = window.navigator.userAgent.includes('Autopass')
    isAutopass && window.autopass && window.autopass.toggleAutoRefresh(false)
    // 判断是否已经获取过客源国信息
    if (l.getItem(CAR_RENTAL_COUNTRY)) {
      this.countryCode = l.getItem(CAR_RENTAL_COUNTRY)
    } else {
      await this.getCountry().then((res:any) => {
        if (res && res.result) {
          this.countryCode = res.result
          l.setItem(CAR_RENTAL_COUNTRY, this.countryCode, ONE_DAY)
        }
      })
    }
    // 从localstorage中取出搜索历史
    if (l.getItem(CAR_RENTAL_MWEB_SEARCH_HISTORY)) {
      this.searchHistory = JSON.parse(l.getItem(CAR_RENTAL_MWEB_SEARCH_HISTORY))
    }
    this.ageList = this.createAgeList()
    this.setCityId()
    // if (!this.cityId && this.useHomeConfig) {
    //   await this.setHomeConfig()
    // }
    this.setQueryData()
  }

  getModuleFormName(moduleName: string) {
    // const query = `?evt=click&ext=${JSON.stringify({ LocalOrGlobal: value })}`
    const query = '?trg=manual'
    return `${moduleName}${query}`
  }

  // 解析URL city_id,默认选中poi
  setCityId() {
    const q: any = this.$route.query
    if (q.city_id) {
      this.cityId = q.city_id
      this.getPoiByUrlCityId(this.cityId)
        .then((res: any) => {
          this.setLocalInternationalData({
            pickPlace: res.result
          }, false)
        })
    }
  }

  // 根据city_id获取poi,接口返回最优poi
  getPoiByUrlCityId(city_id: any) {
    return this._axios
      .$get('/v1/transfercarrentalapisrv/poi/hot_city', { params: { city_id } })
      .then((res: Data.Res) => {
        return Promise.resolve(res)
      })
      .catch((error: any) => {
        return Promise.reject(error)
      })
  }

  // 获取客源国
  getCountry() {
    return this._axios
      .$get('/v1/transfercarrentalapisrv/source_country_code')
      .then((res: Data.Res) => {
        if (res.success && res.result) {
          const data = res.result
        }
        return Promise.resolve(res)
      })
  }

  setUrlParams(params: any) {
    const _urlParams = Object.assign(this.urlParams, params)
    return Promise.resolve(_urlParams)
  }

  // 点击获取google poi 数据
  getGooglePoiData({ placeId }: any) {
    return this._axios
      .$get('/v1/transfercarrentalapisrv/poi/detail_V3', { params: { placeId } })
      .then((res: Data.Res) => {
        return Promise.resolve(res)
      })
      .catch((error: any) => {
        return Promise.reject(error)
      })
  }

  // 选中city后获取poi list
  getCityPoiList({ city_id }: any) {
    return this._axios
      .$get('/v1/transfercarrentalapisrv/poi/get_city_poi', { params: { city_id } })
      .then((res: Data.Res) => {
        return Promise.resolve(res)
      })
      .catch((error: any) => {
        return Promise.reject(error)
      })
  }

  // 获取全量洲/国家/城市
  getInternationalCity({ source_country_code }: any) {
    return this._axios
      .$get('/v1/transfercarrentalapisrv/poi/overseas_city', { params: { source_country_code } })
      .then((res: Data.Res) => {
        return Promise.resolve(res)
      })
      .catch((error: any) => {
        return Promise.reject(error)
      })
  }

  // 获取默认配置poi
  getHomeConfig({ source_country_code }: any ) {
    return this._axios
      .$get('/v1/transfercarrentalapisrv/poi/home_config', { params: { source_country_code } })
      .then((res: Data.Res) => {
        return Promise.resolve(res)
      })
      .catch((error: any) => {
        return Promise.reject(error)
      })
  }

  // poi搜索
  getLocationCompleteV3({ is_domestic, search_word, source_country_code, city_id }: any) {
    return this._axios
      .$get('/v1/transfercarrentalapisrv/poi/autocomplete_V3', { params: { is_domestic, search_word, source_country_code, city_id } })
      .then((res: Data.Res) => {
        if (res.success && res.result) {
          const data = res.result
          this.completePlaceListV3 = data
        } else {
          this.completePlaceListV3 = {}
        }
      })
      .catch(() => {
        this.completePlaceListV3 = {}
      })
  }

  getDateObjByStorage() {
    const storage = localStorage.getItem('__car_rental__srp_search_data')
    if (!storage || this.currentPage !== 'home') {
      return {}
    }
    const obj = JSON.parse(storage)?.value || {}
    // 缓存日期过期
    if (dayjs(obj.pDate).isBefore(dayjs().add(1, 'hour'))) {
      return {}
    }
    const [pDate, pTime] = obj?.pDate.split(' ')
    const [pHour, pMinute] = pTime?.split(':')
    const [dDate, dTime] = obj?.dDate.split(' ')
    const [dHour, dMinute] = dTime?.split(':')
    const pDays = dayjs(pDate).diff(dayjs().format('YYYY-MM-DD'), 'day')
    const dDays = dayjs(dDate).diff(dayjs().format('YYYY-MM-DD'), 'day')
    return {
      pDays,
      pHour,
      pMinute,
      dDays,
      dHour,
      dMinute
    }
  }

  // URL上的取还车日期参数是否可用
  getDateParamCanUse(q?: any) {
    let canUse = true
    if (this.currentPage === 'home') {
      // 对于首页来说如果URL的取还车时间小于当前时间则不用
      if (q.pDate && dayjs(q.pDate).isBefore(dayjs().add(1, 'hour'))) {
        canUse = false
      } else if (q.dDate && dayjs(q.dDate).isBefore(dayjs().add(1, 'hour'))) {
        canUse = false
      }
    }
    return canUse
  }

  setQueryData() {
    this.doUpdateUrl = true
    if (this.$route && this.$route.query &&
      Object.getOwnPropertyNames(this.$route.query).length > 0) {
      const q: any = this.$route.query
      // 判断取车国家code和客源国是否相同，不相同就改为境外
      const isDiffLocal: boolean = q.diffLoc === 'true'
      // this.isLocal = isLocal
      this.setLocalInternationalData({ diffLoc: isDiffLocal }, false)
      if (q.age) {
        this.setAge(q.age)
      }
      // URL上的取还车参数是否可用
      const pdCanUse = this.getDateParamCanUse(q)
      if (q.pDate && pdCanUse) {
        // 判断affiliate链接的取车时间是否小于当前时间
        if ((q.aid || q.adid) && dayjs(q.pDate).isBefore(dayjs().add(1, 'hour'))) {
          this.initDateTime()
        } else {
          const d = q.pDate.split(' ')
          if (Array.isArray(d) && d.length > 1) {
            const c = d[1].split(':')
            // 更新取车时间
            const pickUpDate = {
              date: d[0],
              hour: c[0],
              minute: c[1]
            }
            // 更新url
            // this.setLocalInternationalData({
            //   pickDate: pickUpDate
            // }, isLocal)
            this.setLocalInternationalData({
              pickDate: pickUpDate
            }, false)
          }
        }
      } else {
        const dateObj = this.getDateObjByStorage()
        this.initDateTime(dateObj)
      }
      if (q.dypdate && !isNaN(Number(q.dypdate))) {
        const dyp = Number(q.dypdate)
        this.initDateTime( {pDays: dyp, dDays: dyp + 3} )
      }
      if (q.dDate && pdCanUse) {
        const d = q.dDate.split(' ')
        if (Array.isArray(d) && d.length > 1) {
          const c = d[1].split(':')
          // 更新还车时间
          const dropOffDate = {
            date: d[0],
            hour: c[0],
            minute: c[1]
          }
          // 更新url
          // this.setLocalInternationalData({
          //   dropDate: dropOffDate
          // }, isLocal)
          this.setLocalInternationalData({
            dropDate: dropOffDate
          }, false)
        }
      } else {
        const dateObj = this.getDateObjByStorage()
        this.initDateTime(dateObj)
      }
      if (q.drop) {
        this.dropTip.error = false
        const place = {
          latitude: q.dLat,
          longitude: q.dLong,
          country_code: q.dCode,
          iata_code: q.dIata,
          city_id: q.dCityId,
          city_name: q.dCityName,
          poi_id: q.dPoiId,
          poi_name: q.drop
        }
        this.setLocalInternationalData({
          dropPlace: place
        }, false)
      }
      if (q.pick) {
        this.pickTip.error = false
        const place = {
          latitude: q.lat,
          longitude: q.long,
          country_code: q.code,
          iata_code: q.iata,
          city_id: q.pCityId,
          city_name: q.pCityName,
          poi_id: q.pPoiId,
          poi_name: q.pick
        }
        this.setLocalInternationalData({
          pickPlace: place
        }, false)
      }
    } else {
      const dateObj = this.getDateObjByStorage()
      this.initDateTime(dateObj)
    }
  }

  // async setHomeConfig() {
  //   this.doUpdateUrl = false
  //   await this.getHomeConfig({ source_country_code: this.countryCode })
  //     .then((res:any) => {
  //       if (res && res.result && res.result.tabs) {
  //         const tabs = res.result.tabs
  //         if (tabs.length > 0) {
  //           tabs.forEach((item: any) => {
  //             this.setLocalInternationalData({
  //               pickPlace: item.poi_info
  //             }, item.is_overseas)
  //           })
  //         }
  //       }
  //     })
  // }

  // 切换tab
  toggleTab() {
    // this.isLocal = (type === 'domestic')
    this.pickTip.error = false
    this.dropTip.error = false
    this.updateLocalInternationalData()
  }

  setAge(age: any) {
    const ageItem = this.ageList.find((a: any) => a.value === age)
    if (!ageItem) {
      return
    }
    this.age.label = ageItem.label
    this.age.value = ageItem.value
    this.ageValue = ageItem.value
    this.setLocalInternationalData({ age: this.age, ageValue: this.ageValue }, false)
  }

  updateUrlParam(data: any) {
    if (this.updateUrl && this.doUpdateUrl) {
      this.setUrlParams(data).then((query) => {
        this.$router.push({ query, replace: true })
      })
    } else {
      this.searchParams = Object.assign(this.searchParams, data)
    }
    this.$emit('updateUrlParam', data)
  }

  initDateTime(dateObj?: any) {
    let addPickDate = 5, addDropDate = 8
    // 加个判断，避免把pDays=0或者dDays=0的情况过滤了
    if (dateObj?.pDays !== undefined && dateObj?.pDays !== null && typeof Number(dateObj.pDays) === 'number') {
      addPickDate = Number(dateObj.pDays)
    }
    if (dateObj?.dDays !== undefined && dateObj?.dDays !== null && typeof Number(dateObj.dDays) === 'number') {
      addDropDate = Number(dateObj.dDays)
    }
    const pickUpDate = {
      date: dayjs().add(addPickDate, 'day').format('YYYY-MM-DD'),
      hour: dateObj?.pHour || '10',
      minute: dateObj?.pMinute || '00'
    }
    const dropOffDate = {
      date: dayjs().add(addDropDate, 'day').format('YYYY-MM-DD'),
      hour: dateObj?.dHour || '10',
      minute: dateObj?.dMinute || '00'
    }
    // 设置境内默认时间
    // this.setLocalInternationalData({
    //   pickDate: pickUpDate,
    //   dropDate: dropOffDate
    // }, true)
    // 设置境外默认时间
    this.setLocalInternationalData({
      pickDate: pickUpDate,
      dropDate: dropOffDate
    }, false)
  }

  mwebDlpInitDateTime(dateObj?: any) {
    const pickUpDate = {
      date: dayjs().add(Number(dateObj?.pDays) || 5, 'day').format('YYYY-MM-DD'),
      hour: dateObj?.pHour || '10',
      minute: dateObj?.pMinute || '00'
    }
    const dropOffDate = {
      date: dayjs().add(Number(dateObj?.dDays) || 8, 'day').format('YYYY-MM-DD'),
      hour: dateObj?.dHour || '10',
      minute: dateObj?.dMinute || '00'
    }
    // 设置境内默认时间
    // this.setLocalInternationalData({
    //   pickDate: pickUpDate,
    //   dropDate: dropOffDate
    // }, true)
    // 设置境外默认时间
    this.setLocalInternationalData({
      pickDate: pickUpDate,
      dropDate: dropOffDate
    }, false)
  }

  initCarMoreTime() {
    const pickUpDate = {
      ...this.internationalData.pickDate,
      hour: '10',
      minute: '00'
    }
    const dropOffDate = {
      ...this.internationalData.dropDate,
      hour: '10',
      minute: '00'
    }
    // 设置境外默认时间
    this.setLocalInternationalData({
      pickDate: pickUpDate,
      dropDate: dropOffDate
    }, false)
  }

  createAgeList() {
    const ageList:any = []
    for (let i = 18; i <= 99; i++) {
      const o = {
        value: String(i),
        label: String(i)
      }
      if (i < 30) {
        ageList.push(o)
      } else if (i === 30) {
        ageList.push({
          value: '30',
          label: '30-65'
        })
      } else if (i > 65) {
        ageList.push(o)
      }
    }
    return ageList
  }

  getCompleteListByWord(data: any) {
    const that = this
    const isDomestic: number = data.isDomestic ? 1 : 0
    this.searchList.loading = true
    const city_id = this.isSearchPoi ? (this.trackCityId || null) : null
    if (data.keyWord) {
      this.getLocationCompleteV3({ is_domestic: isDomestic, search_word: data.keyWord, source_country_code: this.countryCode, city_id })
        .then(() => {
          that.searchList.loading = false
        })
        .catch(() => {
          that.searchList.loading = false
        })
    } else {
      this.searchList.loading = false
      this.completePlaceListV3 = {}
    }
  }

  async setSearchPlace(place: any, isSave: boolean = true) {
    this.showCityPage = false
    this.showPoiPage = false
    if (place.google_poi && place.place_id) {
      const {
        result: {
          latitude = '',
          longitude = '',
          city_name = '',
          country_code = '' // autocomplete_V3 接口 与 detail_V3 接口返回 city_name 不一致，以detail_V3为准
        }
      } = await this.getGooglePoiData({ placeId: place.place_id })
      place = Object.assign(place, { latitude, longitude, city_name, country_code })
    }
    if (this.searchList.type === PICKUPLIST) {
      // 如果是海外POI，切换到海外tab
      // this.isLocal = (place.country_code === this.countryCode)
      this.pickTip.error = false
      this.setLocalInternationalData({
        pickPlace: place
      }, false)
    } else {
      this.setLocalInternationalData({
        dropPlace: place
      }, false)
      this.dropTip.error = false
    }
    if (isSave) {
      this.saveSearchHistory(place)
    }
  }

  // 设置境内和境外的数据
  setLocalInternationalData(data: any, isLoc: boolean) {
    this.internationalData = Object.assign(this.internationalData, data)
    this.updateLocalInternationalData()
  }

  // 根据境内外填充页面数据
  updateLocalInternationalData() {
    // if (this.isLocal) {
    //   this.updateSearchData(this.domesticData)
    // } else {
    //   this.updateSearchData(this.internationalData)
    // }
    this.updateSearchData(this.internationalData)
  }

  // 更新搜索框内容
  updateSearchData(data: any) {
    const pick = data.pickPlace || {}
    const drop = data.dropPlace || {}
    const urlParam = {
      pick: pick.poi_name,
      drop: drop.poi_name,
      diffLoc: data.diffLoc ? 'true' : '',
      pDate: `${data.pickDate.date} ${data.pickDate.hour}:${data.pickDate.minute}`,
      dDate: `${data.dropDate.date} ${data.dropDate.hour}:${data.dropDate.minute}`,
      age: data.ageValue,
      lat: pick.latitude,
      long: pick.longitude,
      dLat: drop.latitude,
      dLong: drop.longitude,
      code: pick.country_code ? pick.country_code : '',
      dCode: drop.country_code ? drop.country_code : '',
      iata: pick.iata_code ? pick.iata_code : '',
      dIata: drop.iata_code ? drop.iata_code : '',
      pCityId: pick.city_id ? pick.city_id : '',
      pCityName: pick.city_name ? pick.city_name : '',
      pPoiId: pick.poi_id ? pick.poi_id : '',
      dCityId: drop.city_id ? drop.city_id : '',
      dCityName: drop.city_name ? drop.city_name : '',
      dPoiId: drop.poi_id ? drop.poi_id : '',
      isDomestic: data.local ? 'true' : ''
    }
    this.searchListPick = Object.assign(this.searchListPick, {
      cityName: pick.city_name,
      cityId: pick.city_id,
      poiName: pick.poi_name
    })
    this.searchListDrop = Object.assign(this.searchListDrop, {
      cityName: drop.city_name,
      cityId: drop.city_id,
      poiName: drop.poi_name
    })
    this.pickUpDate = data.pickDate
    this.dropOffDate = data.dropDate
    this.isDiffLocal = data.diffLoc
    this.age = data.age
    this.ageValue = data.ageValue

    this.updateUrlParam(urlParam)
  }

  openCityList(type: string) {
    this.isSearchPoi = false
    // this.isLocalPoi = this.isLocal
    this.getCityList()
    this.showCityPage = true
    if (type === PICKUPLIST) {
      this.searchList = this.searchListPick
      this.trackCityId = this.searchListPick.cityId
    } else {
      this.searchList = this.searchListDrop
      this.trackCityId = this.searchListDrop.cityId
    }
  }

  openPoiList(type: string) {
    this.isSearchPoi = true
    // this.isLocalPoi = this.isLocal
    if (type === PICKUPLIST) {
      this.searchList = this.searchListPick
      this.getPoiList(this.searchListPick.cityId)
    } else {
      this.searchList = this.searchListDrop
      this.getPoiList(this.searchListDrop.cityId)
    }
  }

  closeListPage() {
    this.showCityPage = false
    this.showPoiPage = false
  }

  changeCity() {
    this.showPoiPage = false
    if (!this.showCityPage) {
      this.openCityList(this.searchList.type)
    }
  }

  // 获取城市列表
  async getCityList() {
    const cityCode = {
      source_country_code: this.countryCode
    }
    this.loadingCityList = true
    await this.getInternationalCity(cityCode)
      .then((res:any) => {
        if (res && res.result) {
          this.loadingCityList = false
          this.cityListObj = res.result
        }
      })
  }

  // 获取POI列表
  async getPoiList(id: number) {
    this.trackCityId = id
    this.showPoiPage = true
    if (id) {
      const cityId = {
        city_id: id
      }
      this.loadingPoiList = true
      await this.getCityPoiList(cityId)
        .then((res:any) => {
          if (res && res.result) {
            this.loadingPoiList = false
            this.cityPoiObj = res.result
          }
        })
    } else {
      this.loadingPoiList = false
      this.cityPoiObj = null
    }
  }

  confirmDateTime(date: any) {
    const pick = dayjs(date[0])
    const drop = dayjs(date[1])
    if (Array.isArray(date)) {
      const pickUpDate = {
        date: pick.format('YYYY-MM-DD'),
        hour: pick.hour() < 10 ? `0${pick.hour()}` : pick.hour().toString(),
        minute: pick.minute() > 0 ? '30' : '00'
      }
      const dropOffDate = {
        date: drop.format('YYYY-MM-DD'),
        hour: drop.hour() < 10 ? `0${drop.hour()}` : drop.hour().toString(),
        minute: drop.minute() > 0 ? '30' : '00'
      }
      this.setLocalInternationalData({
        pickDate: pickUpDate,
        dropDate: dropOffDate
      }, false)
    }
  }

  openDateTimePicker() {
    this.showDataTimePicker = true

    // 解决datePicker第一次无法滚动到选中日期位置的方案
    setTimeout(() => {
      const pickerPanels = document.getElementsByClassName('klk-date-picker-panels')[0]
      if (pickerPanels) {
        this.pickerScrollTop = pickerPanels.scrollTop
      }
    }, 100)
  }

  // 时间多语言格式化方法
  formatDate(date: any) {
    const type = 1

    return getStandardDateFormat(date, this.$t.bind(this), this.curLocale, type)
  }

  // 保存搜索历史，只保存最近三条（不重复）
  saveSearchHistory(poi: any) {
    // let domestic: any
    const international = poi
    // if (this.isLocal) {
    //   domestic = poi
    //   if (this.searchHistory.domestic.length > 0) {
    //     this.searchHistory.domestic.forEach((item, index) => {
    //       // 同时判断id和name, 因为poi id可能为空
    //       if (item.poi_id === domestic.poi_id && item.poi_name === domestic.poi_name) {
    //         this.searchHistory.domestic.splice(index, 1)
    //       }
    //     })
    //   }
    //   this.searchHistory.domestic.unshift(domestic)
    //   this.searchHistory.domestic = this.searchHistory.domestic.slice(0, 3)
    // } else {
    //   international = poi
    //   if (this.searchHistory.international.length > 0) {
    //     this.searchHistory.international.forEach((item, index) => {
    //       // 同时判断id和name, 因为poi id可能为空
    //       if (item.poi_id === international.poi_id && item.poi_name === international.poi_name) {
    //         this.searchHistory.international.splice(index, 1)
    //       }
    //     })
    //   }
    //   this.searchHistory.international.unshift(international)
    //   this.searchHistory.international = this.searchHistory.international.slice(0, 3)
    // }
    // international = poi
    if (this.searchHistory.international.length > 0) {
      this.searchHistory.international.forEach((item, index) => {
        // 同时判断id和name, 因为poi id可能为空
        if (item.poi_id === international.poi_id && item.poi_name === international.poi_name) {
          this.searchHistory.international.splice(index, 1)
        }
      })
    }
    this.searchHistory.international.unshift(international)
    this.searchHistory.international = this.searchHistory.international.slice(0, 3)
    l.setItem(CAR_RENTAL_MWEB_SEARCH_HISTORY, JSON.stringify(this.searchHistory), ONE_DAY)
  }

  // 清空搜索历史
  removeHistory(isdomestic: boolean) {
    isdomestic ? this.searchHistory.domestic = [] : this.searchHistory.international = []
    l.setItem(CAR_RENTAL_MWEB_SEARCH_HISTORY, JSON.stringify(this.searchHistory), ONE_DAY)
  }

  search() {
    // 聚合页搜索历史记录，记录是哪个tab
    this.$emit('handle-add-history')
    // 重新搜索时先关闭carmore 小岛的提示
    this.$emit('closeCarmoreMention')
    const q = this.updateUrl ? this.$route.query : this.searchParams
    // check pick up location
    if (!this.searchListPick.poiName || (this.searchListPick.poiName !== q.pick)) {
      // 取车地点报错
      this.pickTip.show = true
      this.pickTip.error = true
      setTimeout(() => {
        this.pickTip.show = false
      }, 3000)
      return
    }
    // check drop off location
    if (this.isDiffLocal && (!this.searchListDrop.poiName ||
      (this.searchListDrop.poiName !== q.drop))) {
      // 还车地点报错
      this.dropTip.show = true
      this.dropTip.error = true
      setTimeout(() => {
        this.dropTip.show = false
      }, 3000)
      return
    }
    // check age
    if (q && (Number(q.age) <= 17 || Number(q.age) > 99)) {
      q.age = '30'
    }
    // 聚合页为组件内处理search事件，其他都是组件调用的地方处理
    if (this.currentPage === 'home') {
      this.handleUpdateUrl(q)
    } else {
      this.$emit('search', q)
    }
  }

  // 默认聚合页搜索
  handleUpdateUrl(data: any) {
    const paths = location.href.split('car-rentals')
    let search = '?'
    for (const key in data) {
      search += `${key}=${data[key]}&`
    }
    const url = `${paths[0]}car-rentals/results/${search.slice(0, -1)}`
    location.href = url
  }

    get galileoClick1() {
        return { spm: WebClick_81, enforce: 'post', autoTrackSpm: true }
    }

    get galileoClick2() {
        return { spm: WebClick_82, autoTrackSpm: true }
    }

    get galileoClick3() {
        return { spm: WebClick_83, autoTrackSpm: true }
    }

    get galileoClick4() {
        return { spm: WebClick_84, autoTrackSpm: true }
    }

    get galileoClick5() {
        return { spm: WebClick_85, autoTrackSpm: true }
    }

    get galileoClick6() {
        return { spm: WebClick_86, autoTrackSpm: true }
    }

    get galileoClick7() {
        return { spm: WebClick_87, autoTrackSpm: true }
    }
}
</script>

<style lang="scss" scoped>
@mixin text-ellipsis($line-clamp: 2) {
  display: -webkit-box;
  -webkit-line-clamp: $line-clamp;
  -webkit-box-orient: vertical;
  text-overflow: ellipsis;
  overflow: hidden;
}
.search-form-container {
  position: relative;
  top: 0;
  left: 0;
  border-radius: $radius-l;
  margin: 0 20px;
  // box-shadow: $shadow-normal-2;
}
.search-form-container_home-page{
  .carrental-transfer{
    width: 100%;
    height: 46px;
    background-image: url('https://res.klook.com/image/upload/car-rental/Home_Tab_ylupin.png');
    background-size: 100% 100%;
    display: flex;
    flex-direction: row;
    align-items: center;
    .tab {
      flex: 1 0 0;
      text-align: center;
      font-weight: $fontWeight-bold;
      color: $color-text-secondary;
      font-size: $fontSize-body-s;
      @include text-ellipsis(1);

      &.active {
        color: $color-brand-primary;
      }
    }
  }
  .domestic-foreign {
    background: #ffffff;
    padding-top: 14px;
    .search-form-tab_home-page {
      margin: 0 16px;
      background-size: 100% 100%;
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
      background: #F5F5F5;
      border-radius: 6px;
      padding: 4px;
      .tab {
        text-align: center;
        font-weight: $fontWeight-bold;
        color: $color-text-secondary;
        font-size: $fontSize-caption-m;
        border-radius: 6px;
        padding: 6px;
        width: 139.5px;
        @include text-ellipsis(2);
        &.active {
          color: $color-brand-primary;
          background: #ffffff;
          box-shadow: 0px 0px 12px rgba(0, 0, 0, 0.03);
        }
      }
    }
  }
}
.search-form-tab {
  width: 100%;
  height: 46px;
  background-image: url('https://res.klook.com/image/upload/search-from-tab-bg-left_zuzzf8.png');
  background-size: 100% 100%;
  display: flex;
  flex-direction: row;
  align-items: center;

  &.right {
    background-image: url('https://res.klook.com/image/upload/search-from-tab-bg-right_cdp1cm.png');
  }

  .tab {
    flex: 1 0 0;
    text-align: center;
    font-weight: $fontWeight-bold;
    color: $color-text-secondary;
    font-size: $fontSize-body-s;
    @include text-ellipsis(2);

    &.active {
      color: $color-brand-primary;
    }
  }
}

.search-form-wrap {
  background-color: $color-bg-widget-normal;
  padding: 16px 16px 20px;
  border-radius: $radius-l;

  .diff-local {
    line-height: 17px;
    display: flex;
    flex-direction: row;
    align-items: center;
    font-size: $fontSize-body-s;

    span {
      margin-left: 8px;
    }
  }

  .search-input {
    margin-top: 4px;
    display: flex;
    flex-direction: column;

    .input-box {
      min-height: 48px;
      margin-top: 8px;
      color: $color-text-primary;
      background-color: $color-bg-page;
      border-radius: $radius-m;
      border-style: none;
      padding: 7px 0;
      outline: none;
      display: flex;
      flex-direction: row;
      line-height: initial;

      .city {
        flex: 100px 0 1;
        padding-left: 12px;
        font-size: 0;
      }

      .location {
        flex: 242px 1 1;
        padding: 0 12px;
        font-size: 0;
      }

      .no-value {
        font-size: $fontSize-body-s;
        color: $color-text-secondary;
        line-height: 34px;

        &.ville {
          width: 88px;
          display: inline-block;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }

      .has-value {
        i {
          font-style: normal;
          font-size: $fontSize-caption-s;
          color: $color-text-disabled;
          line-height: 14px;
        }

        p {
          @include font-body-s-semibold;
          color: $color-text-primary;
          word-break: break-all;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -moz-box-orient: vertical;
          -webkit-line-clamp: 2;
          overflow: hidden;
          text-overflow: ellipsis;

          &.ellipsis {
            width: 88px;
            -webkit-line-clamp: 1;
          }
        }
      }

      &.error {
        background-color: $color-brand-primary-background;
        border: 1px solid $color-brand-primary;
      }
    }
  }

  .search-data-time {
    margin-top: 8px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    position: relative;

    .date-time {
      flex: 1;
      height: 48px;
      background-color: $color-bg-page;
      padding: 9px 12px;
      color: $color-text-primary;
      display: flex;
      flex-direction: column;

      &.pickup {
        padding-right: 30px;
        border-radius: 6px 0 0 6px;
      }

      &.dropoff {
        padding-left: 36px;
        border-radius: 0 6px 6px 0;

        @media screen and (max-width: 320px) {
          padding-left: 30px;
        }
      }

      b {
        @include font-body-s-semibold;

        @media screen and (max-width: 370px) {
          font-size: $fontSize-caption-m;
        }

        @media screen and (max-width: 320px) {
          font-size: $fontSize-caption-s;
        }
      }

      span {
        @include font-caption-m-regular;
      }
    }

    .days-count {
      position: absolute;
      left: 50%;
      top: 14px;
      transform: translateX(-50%);
      width: 53px;
      height: 20px;
      font-size: $fontSize-caption-s;
      line-height: 20px;
      color: $color-info;
      background-color: $color-bg-widget-normal;
      border-radius: $radius-xl;
      text-align: center;
    }
  }

  .driver-age {
    margin-top: 16px;
    color: $color-text-primary;
    line-height: 17px;
    font-size: $fontSize-body-s;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    position: relative;

    .age {
      display: flex;
      flex-direction: row;
      align-items: center;

      span {
        margin-right: 5px;
        line-height: 20px;
      }

      .tips-icon {
        color: #757575;
      }
    }

    .change {
      color: $color-info;
    }

    .age-select {
      position: absolute;
      right: 0;
      top: 0;
      width: 50px;
      height: 20px;
      opacity: $opacity-transparent;
    }
  }

  .search-btn {
    margin-top: 15px;
  }

  .warning-tips {
    width: 100%;
    padding: 16px;
    line-height: 20px;
    font-size: $fontSize-body-m;
    color: $color-text-primary-onDark;
    background-color: $color-brand-secondary;
    position: fixed;
    top: 48px;
    left: 0;
  }

  .warning-tips-harmony {
    top: 0px;
  }
}

::v-deep .klk-modal {
  padding: 0;
  margin: 0;
}

::v-deep .klk-bottom-sheet-inner {
  padding: 16px 0 !important;

  .klk-bottom-sheet-body {
    padding: 0 16px !important;
  }
}
</style>
<style lang="scss">
.tips-box {
  color: $color-text-primary;
  padding: 8px 4px 0;
  font-size: $fontSize-body-s;
  line-height: 20px;

  .title {
    font-size: $fontSize-body-m;
    line-height: 22px;
    font-weight: $fontWeight-bold;
  }

  p {
    margin-top: 12px;
  }
}
</style>
