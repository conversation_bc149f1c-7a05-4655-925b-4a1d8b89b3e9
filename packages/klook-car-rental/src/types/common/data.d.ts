/* eslint-disable camelcase */
/* eslint-disable no-unused-vars */
/* eslint-disable @typescript-eslint/no-unused-vars */
import { LocationRecord } from 'maxmind'
import { SiteConfigTypes } from '@klook/site-config'
import { IAffiliateConf } from './affiliateConf'

declare global {
  namespace Data {
    /** https://zh.wikipedia.org/wiki/%E5%9C%8B%E5%AE%B6%E5%9C%B0%E5%8D%80%E4%BB%A3%E7%A2%BC */
    type Country = SiteConfigTypes.Country

    type Language = SiteConfigTypes.Language

    type Currency = SiteConfigTypes.Currency

    type SupportWebsiteList = SiteConfigTypes.SupportWebsiteList

    type Platform = 'mobile' | 'desktop'

    interface Klook {
      language: Language
      backendLanguage: string
      supportLanguages: Language[]
      currency: Currency
      currencySymbol: string
      ip: string
      ipCountry: Data.Country
      platform: Platform
      isIOS: boolean
      isKlookApp: boolean
      webp: 0 | 1
      retina: 0 | 1
      utilConfig: any
      affiliateConf?: IAffiliateConf
      qsAffiliateConf: any,
      host: string,
      pageId: string,
      pageTemplate: string,
      websiteConfig: any
      currencyRate: any
      secondaryNav: any,
      destinationData: any,
      allCategoryList: any,
      isBot: boolean
      platformMp: string
    }

    interface User {
      id: number
      globalId: string
      username: string
      title: string
      email: string
      emailHash: string
      avatar: string
      countryCode: string
      createTime: string
      unreview: number
      giftCard: any[]
      registerSite: string
      preferSite: string
      confirmed_residence?: string
      user_residence?: string
      membership_level?: number | string
      membership_style?: any
    }

    interface Config {
      readonly server?: {
        host?: string
        port?: number
      }

      readonly whiteLabelServer?: {
        host?: string
        port?: number
      }

      readonly env?: {
        [key: string]: any
      }

      readonly baseBuild?: {
        loaders: object
        extractCSS?: boolean | object
        publicPath?: string
        memoryLimit?: number
        workers?: number
      }

      readonly build?: {
        extractCSS?: boolean
        publicPath?: string
        memoryLimit?: number
        workers?: number
      }

      readonly whiteLabelBuild?: {
        extractCSS?: boolean
        publicPath?: string
        memoryLimit?: number
        workers?: number
      }

      readonly redis?: {
        host: string,
        db: number,
        port: number
      }

      readonly includeRouterName?: string[] | RegExp

      readonly cacheHtml?: boolean

      readonly securityEnable?: boolean

    }

    interface Res {
      error: {
        code: string
        message: string
      }
      success: boolean
      result: any

      [field: string]: any
    }

    interface IpData {
      country: string | undefined
      continent: string | undefined
      postal: string | undefined
      city: string | undefined
      location: LocationRecord | undefined
      subdivision: string | undefined
    }

    type IKeplerExpsList = Array<{
      experiment_id: number
      experiment_name: string
      group_id: number
      group_name: string
      need_dye: boolean
      layer_ids: Array<number>
    }>

    type IKeplerExpsGroup = {
      [experimentName: string]: {
        id: number,
        group: {
          id: number,
          name: string
        },
        sendStatus: boolean
      }
    }

    type IKepler = {
      keplerId: string
      experimentsGroup: IKeplerExpsGroup
      tintedList: string
      expExtra: Record<string, string | number | null>
    }

    type HeaderSearch = {
      placeholder: string
      searchQuery: string
      suggestParams: {}
      searchHistoryArr: Array<string>
      slideWordList: Array<string>

    }
  }
}
