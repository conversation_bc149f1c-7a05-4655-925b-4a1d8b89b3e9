import { IncomingMessage } from 'http'

declare module 'http' {
  interface IncomingMessage {
    startTime: number
    requestId: string
    keplerId: string
    pageId: string
    isStaticOrApi: boolean
    oldPath: string
    ip: string,
    ips: string[],
    ipData: Data.IpData | null
    language: Data.Language
    country: Data.Country
    currency: Data.Currency
    currencySymbol: string
    webp: number
    retina: number
    isIOS: boolean
    isMobile: boolean
    isKlookApp: boolean
    platform: Data.Platform
    preCacheKey: string
    utilConfig: any
    memCache: any
    redis: any
    affiliateConf: any
    qsAffiliateConf: any
    websiteConfig: any,
    axios: any
    platformMp: string // '': 非小程序 mp-wechat: 微信小程序 mp-xiaohongshu: 百度/小红书小程序
    cacheHtmlKey?: string // HTML缓存key
    tintedList: string // kepler染色
    experimentsHitList: Data.IKeplerExpsGroup // 实验列表
    page: number
    trafficChannel?: string
  }
}
