import { Configuration } from '@nuxt/types'
import { Options as RouterOptions } from '~/modules/custom-router/types'
import { Options as AnalyticsOptions } from '~/modules/analytics/types'

interface ImageSpriteConfig {
  name: string
  input: string
  output: string
  image?: string
  retina?: boolean
  format?: string
}

interface ImageSpriteOptions {
  sprites: ImageSpriteConfig[]
  retina?: boolean
  format?: string
}

interface SvgSpriteConfig {
  name: string
  input: string
  output: string
  scoped?: boolean
}

interface SvgSpriteOptions {
  sprites: SvgSpriteConfig[]
  scoped?: boolean
}

declare module '@nuxt/types' {
  interface Configuration {
    customRouter: RouterOptions
    nuxtImageSprite?: ImageSpriteOptions
    nuxtSvgSprite?: SvgSpriteOptions
    analytics?: AnalyticsOptions,
    securityEnable: boolean
  }
}
