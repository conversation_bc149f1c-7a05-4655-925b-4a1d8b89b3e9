declare module 'nuxt'
declare module '@nuxt/utils'
declare module 'serialize-javascript'
declare module 'showdown'
declare module 'commonmark'
declare module 'md5.js'
declare module '@klook/klook-experience-other-info/lib/mobile/index.esm.js'
declare module '@klook/klook-experience-other-info/lib/web/index.esm.js'

// Image
declare module '*.png'

declare module '*.vue' {
  import Vue from 'vue'

  export default Vue
}

interface Window {
  __KLOOK__: any;
  // 引入buttion sdk提供的全局变量
  bttnio: any
  ButtonWebConfig: ButtonWebConfig
  ga: any
  hotelRangeCoordinates: any,
  seatsio: any,
  TcPlayer: any,

  // google one tap 实验添加
  google: any,
  gapi: any,
  googleSignInInit: any
  // google one tap 实验添加
  zChat: any,
  dsBridge: any,
  // 华为天际通sdk
  hbs: any,
  // 页面基础数据
  BaseData: any,
  // 车麻吉sdk
  autopass: any
  handleClientLoad: any
  fbAsyncInit: any
  FB: any
  AppleID: any
  Kakao: any
  /** 页面 GA dataLayer 数据埋点 */
  dataLayer: any[]

  Cookies: any
  $axios: any
  tracker: any
}

interface ButtonWebConfig {
  applicationId: string
}

declare module '@klook/klook-ui/lib/locale'
declare module 'hls.js'

// tsx css module 方式引入scss
declare module '*.scss?module' {
  const content: { [key: string]: any }
  export default content
}

declare interface Window {
  MP_JS_SDK: any
}
