import Vue from 'vue'
import { NuxtApp } from '@nuxt/types/app'

type ToastOptions = {
  message: string;
  icon?: string;
  iconColor?: string;
  duration?: number;
};

interface ModalOptions {
  // 详细配置 https://storybook.klook.io/?path=/story/%E7%BB%84%E4%BB%B6-klook-ui-%E7%BB%84%E4%BB%B6-42--modal-doc
  okLabel?: string // 默认确定
  cancelLabel?: string // 默认取消
  width?: string | number // 960px/420px
  showCancelButton?: boolean // 默认true 该字段控制是否有取消按钮
  buttonAlign?: string // 可选 'center equal block 默认right
  titleAlign?: string // 可选center默认left
  size?: string // 可选large(680px), 默认 normal(420px)
  closable?: true // 是否显示右上角的关闭按钮默认false
  overlay?: false // 是否有遮罩默认true
  overlayColor?: string // 默认#000
  [key: string]: any
}

type ModalContents = string | ((h: Vue['$createElement']) => Vue['$vnode'])

interface ModalFn {
  (content: ModalContents, title?: string, options?: ModalOptions): Promise<{ result: boolean, value: boolean }>
  (content: ModalContents, options?: ModalOptions): Promise<{ result: boolean, value: boolean }>
}

interface DialogFn {
  (content: ModalContents, title?: string, options?: ModalOptions): Promise<boolean>
  (content: ModalContents, options?: ModalOptions): Promise<boolean>
}

interface Dialog extends DialogFn {
  confirm: DialogFn
  alert: DialogFn
}

declare module 'vue/types/vue' {
  interface Vue {
    $cookies: any
    $klook: NuxtApp
    $showLoading(options?: {[property: string]: any}): void
    $hideLoading(): void
    $toast(options?: ToastOptions): Vue // 返回了toast组件
    $sendGTM: any // @TODO: 添加类型声明
    $sendGTMCustomEvent: any // @TODO: 添加类型声明
    $sendMixpanel: any // @TODO: 添加类型声明
    $alert: ModalFn
    $confirm: ModalFn
    $dialog: Dialog // klook-ui 支持了以后会删掉
    $tracker: Vue,
    $notification(any: {}): void
    $toast(message: string, icon?: string, iconColor?: string, duration?: number): Vue // 返回了toast组件
    $inhouse: any,
    $getAllCountryCode(): { value: string, countryCode: string, country: string }[]
  }
}
