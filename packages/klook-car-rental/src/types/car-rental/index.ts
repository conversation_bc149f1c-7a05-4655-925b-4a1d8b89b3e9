export namespace CarRental {
  export interface Place {
    iataCode: string // 机场三字码，当地点类型为机场为机场时，这个字段有值,
    latitude: string // 纬度坐标,
    locationDesc: string // 描述，一般包括国家名+城市名,
    locationIcon: string // 地标图标,
    locationId: number // 地点id，留着后面权重用到,
    locationName: string // 地点名称,
    locationType: number // 地点类型，1=机场，2=火车站，3=商圈,
    longitude: string // 经度坐标
    inedx?: number
  }
  export interface Poi {
    address: string // poi详细地址
    cityID: number // 城市id
    cityName: string // 城市名称
    countryCode: string // 客源国代码
    iataCode: string // 机场三字码，当地点类型为机场为机场时，这个字段有值,
    imageUrl: string // 地标图标
    latitude: string // 纬度坐标,
    longitude: string // 经度坐标
    poiID: number // 地点id
    poiName: string // 地点名称
    poiType: number // 地点类型，1=机场，2=火车站，3=商圈,
    inedx?: number
  }
}
