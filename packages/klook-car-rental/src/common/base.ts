import { Vue, Component } from 'vue-property-decorator'
import toast from '@klook/klook-ui/lib/toast'
import '@klook/klook-ui/lib/toast/style'

@Component
export default class Base extends Vue {
  $t!: Function
  $store!: any
  _axios!: any
  $toast!: toast
  $route!: any
  $router!: any
  $sendGTMCustomEvent!: Function
  $sendMixpanel!: Function
  $inhouse!: any

  get realWebp() {
    return this?.$store?.state?.klook?.webp || 0;
  }

  get curLocale() {
    return this?.$store?.$i18n?.options?.locale || 'en-US'
  }

  beforeMount() {
    this._axios = this.$attrs.axios || window.$axios
  }

  getKlkLanguage() {
    return window?.__KLOOK__?.state?.klook?.language || 'en_US'
  }
}
