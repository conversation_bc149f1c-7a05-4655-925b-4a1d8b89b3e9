import localStorageEx from './localstorage'

const CAR_RENTAL_COUNTRY = 'carRentalCountry'
const ONE_DAY = 86400 * 1000

// 租车首页head配置
export function homeHead(vm: any) {
  // const { link } = vm.$seoHeadTags(vm.$route.path)
  const d = String(vm.$t('car_rental_home_seo_desc'))
  const t = String(vm.$t('car_rental_home_seo_title'))
  const k = String(vm.$t('car_rental_home_seo_keywords'))
  const description = vm.$description(d)
  const headImage = 'https://res.klook.com/image/upload/car-rental/car-rental-banner.jpg'

  // let langPath = ''

  // if (vm.$store.state.klook.language !== 'en') {
  //   langPath = `/${vm.$store.state.klook.language}`
  // }

  let langPath = ''
  if (vm.$store.state.klook.language !== 'en') {
    langPath = `/${vm.$store.state.klook.language}`
  }

  const antiSpiderHead = (vm: any) => {
    if (vm.$store.state.klook.host === 'www.klook.cn') {
      return {}
    }
    return {
      script: []
    }
  }

  return {
    bodyAttrs: {
      'data-spm-page': `Mobility_Aggregate?oid=categorytree_38&ext=${encodeURIComponent(JSON.stringify({ object_id: 'category_15' }))}`
    },
    title: t,
    meta: [
      description,
      vm.$setMeta({ property: 'og:locale', content: `${vm.$store.state.klook.language}` }),
      vm.$setMeta({ property: 'og:type', content: 'product.group' }),
      vm.$setMeta({ property: 'og:title', content: t }),
      vm.$setMeta({ property: 'og:description', content: d }),
      vm.$setMeta({ property: 'og:url', content: `https://${vm.$store.state.klook.host}${langPath}/car-rentals/` }),
      vm.$setMeta({ property: 'og:image', content: `${headImage}` }),
      vm.$setMeta({ property: 'og:site_name', content: 'seo.site.name' }, true),
      vm.$setMeta({ property: 'keywords', content: k }, true),

      vm.$setMeta({ name: 'twitter:title', content: t }),
      vm.$setMeta({ name: 'twitter:description', content: d }),
      vm.$setMeta({ name: 'twitter:site', content: ' @KlookTravel' }),
      vm.$setMeta({ name: 'twitter:image', content: `${headImage}` })
    ],
    link: vm.$canonical(`/${langPath}car-rentals/`),
    ...antiSpiderHead
  }
}

// chat 配置
export function chatEntrance() {
  return {
    'Category Name': 'Car Rental - API',
    'Page': 'CarRental_Vertical'
  }
}


// 接口获取客源国
export function getSourceCountryCode(vm: any, callBack?: Function) {
  const l = localStorageEx()
  const auth = window.__KLOOK__.state.auth
  const platform = window.__KLOOK__.state.klook.platform
  let countryCode = ''
  if (l.getItem(CAR_RENTAL_COUNTRY)) {
    countryCode = l.getItem(CAR_RENTAL_COUNTRY)
    if (callBack) {
      callBack(countryCode)
    }
  } else {
    vm.$axios.$get('/v1/transfercarrentalapisrv/source_country_code')
      .then((res: any) => {
        if (res && res.result) {
          countryCode = res.result
          l.setItem(CAR_RENTAL_COUNTRY, countryCode, ONE_DAY)
          if (callBack) {
            callBack(countryCode)
          }
        }
      })
      .catch((error: any) => {
        vm.$logquery.service({
          timestamp: Date.now(),
          level: 'E',
          tag: 'client',
          uid: auth.user ? auth.user.globalId : '',
          message: '(carrental source country code error)'
        }, {
          headers: { 'X-Platform': platform }
        })
      })
  }
}
