<template>
  <div
    v-if="
      (termList && termList.length) || (displayTerms && displayTerms.length)
    "
    class="klk-login-term-list"
  >
    <div
      v-if="showRequired && !requiredIsChecked"
      class="klk-login--terms-warn"
      :class="{ actived: isWarning }"
    >
      {{ textData.requiredTerms }}
    </div>

    <div v-show="showCheckAll && termList.length > 1">
      <klk-checkbox v-model="checkAll" @change="checkAllFn">{{
        textData.agreeTip
      }}</klk-checkbox>
    </div>
    <div class="tnc-list" :class="{ 'padding-left-24': showCheckAll && termList.length > 1 }">
      <div v-for="(term, index) in termList" :key="index" :class="{'tnc-item': true, 'tnc-item-required_error': isErrorItem(term)}">
        <klk-checkbox v-model="term.isChecked" @change="changeBox">
          <template v-if="term.content_link">
            <a
              :href="term.content_link"
              target="_blank"
              class="checkbox-label checkbox-label--link"
              >{{ term.content }}</a
            >
          </template>
          <template v-else-if="term.content_md">
            <klk-markdown :content="term.content_md"></klk-markdown>
          </template>
          <template v-else>
            <span class="checkbox-label">{{ term.content }}</span>
          </template>
        </klk-checkbox>
      </div>
    </div>

    <div class="tnc-list klk-login-terms">
      <klk-markdown
        v-for="(term, index) in displayTerms"
        :key="index"
        :content="term.content_md"
      ></klk-markdown>
    </div>

    <klk-modal :open.sync="showConsentDialog" :title="textData.confirm">
      <div class="klk-modal-content">
        {{ preConsentHint }}
      </div>
      <div slot="footer" class="klk-login_pre-consent-footer">
        <klk-button
          @click="onCancel"
          type="outlined"
          :size="realPlatform === 'mobile' ? 'mini' : 'small'"
          >{{ textData.noThanks }}</klk-button
        >
        <klk-button
          @click="onConfirm"
          :size="realPlatform === 'mobile' ? 'mini' : 'small'"
          >{{ textData.agree }}</klk-button
        >
      </div>
    </klk-modal>
  </div>
</template>

<script>
import KlkModal from "@klook/klook-ui/lib/modal/index.js";
import { Checkbox as klkCheckbox } from "@klook/klook-ui/lib/checkbox/index.js";
import KlkMarkdown from "@klook/klook-ui/lib/markdown";
import KlkButton from "@klook/klook-ui/lib/button/index.js";
const TERM_LISTS_KEY = 'TERM_LISTS';

export default {
  name: "SpecialTerms",
  components: { klkCheckbox, KlkMarkdown, KlkModal, KlkButton },
  props: {
    textData: {
      type: Object,
      default: () => ({
        requiredTerms: "", // 14777
        agreeTip: "", // 15776
        confirm: "", // 79918
        noThanks: "", // 79921
        agree: "", // 79920
      }),
    },
    api: {
      type: String,
      default: "/v3/userserv/user/term_service/get_term_by_scene",
    },
    platform: {
      type: String,
      default: "",
    },
    scene: {
      type: String,
      default: "register",
    },
    edm: {
      // 0 - 所有条款，1 - 所有EDM条款，2 - 所有非EDM条款
      type: Number,
      default: 0,
    },
    showRequired: {
      type: Boolean,
      default: true,
    },
    showCheckAll: {
      type: Boolean,
      default: true,
    }
  },
  data() {
    return {
      termList: [],
      checkList: [],
      displayTerms: [],
      checkAll: false,
      checkRequired: false,
      isWarning: false,
      showConsentDialog: false,
      preConsent: null,
      preConsentHint: "",
      callback: () => {},
      showErrorItem: false,
    };
  },
  computed: {
    realPlatform() {
      return (
        this.platform || window.__KLOOK__.state.klook.platform || "desktop"
      );
    },
    // 找到是否存在必须勾选但未勾选的项
    requiredIsChecked() {
      const noCheckRequired = this.termList.find((term) => {
        return term.required && !term.isChecked;
      });

      return !noCheckRequired;
    }
  },
  async mounted() {
    await this.fetchTermList();
    this.initCheckList();
  },
  methods: {
    ajaxGet(url, params) {
      if (!window.$axios && !window.$axios.$get && !window.$axios.get) {
        return console.error("window.$axios is required!!!");
      }

      const get = (window.$axios.$get || window.$axios.get).bind(window.$axios);
      return get(url, { params });
    },
    cacheCheckedTermIds(termIds, termLists) {
      if (!termIds && !termLists) return;
      termIds && localStorage.setItem(CHECKED_TERMS_KEY, termIds);
      if (termLists) {
        try {
          localStorage.setItem(TERM_LISTS_KEY, JSON.stringify(termLists));
        } catch (e) {
          console.error(e);
        }
      }
    },
    fetchTermList() {
      return this.ajaxGet(this.api, {
        scene: this.scene,
      })
        .then((resp) => {
          if (!resp.success) return;
          let terms = resp.result.terms || [];
          if (this.edm === 1) {
            terms = terms.filter((term) => term.is_edm);
          } else if (this.edm === 2) {
            terms = terms.filter((term) => !term.is_edm);
          }
          this.cacheCheckedTermIds("", terms);
          this.handlePreConsent(terms);

          this.termList = terms
            .filter((term) => !term.display_only)
            .map((term) => {
              return Object.assign({}, term, {
                isChecked: term.default_check_status,
              });
            });
          this.displayTerms = terms.filter((term) => term.display_only);

          this.$nextTick(() => {
            this.handleLinkTarget();
          });
        })
        .finally(() => {
          this.$emit("loaded");
        });
    },
    initCheckList() {
      this.checkList = this.termList
        .filter((term) => term.default_check_status)
        .map((term) => term.term_id);
    },
    // 校验必选的是否勾选
    validator(callback) {
      const checkedIds = this.getCheckedTermIds();
      this.showErrorItem = false;

      if (!this.requiredIsChecked) {
        // if (this.$el.scrollIntoView) {
        //   this.$el.scrollIntoView({
        //     behavior: "smooth",
        //   });
        // }

        this.showError();
      } else if (
        this.preConsent &&
        checkedIds.includes(this.preConsent.termId)
      ) {
        // 有前置条款且前置条款未全部勾选
        if (
          !this.preConsent.preConsentDisplayTermIds.every((v) =>
            this.checkList.includes(v)
          )
        ) {
          this.showConsentDialog = true;
          if (typeof callback === "function") {
            this.callback = callback;
            return;
          } else {
            return false;
          }
        }
      }
      return typeof callback === "function"
        ? callback(this.requiredIsChecked, checkedIds)
        : this.requiredIsChecked;
    },
    isErrorItem(term) {
      return this.showErrorItem && term.required && !this.checkList.includes(term.term_id);
    },
    showError() {
      this.showErrorItem = true;
      if (this.isWarning) return;
      this.isWarning = true;
      setTimeout(() => {
        this.isWarning = false;
      }, 300);
    },
    checkAllFn(ischeck) {
      this.checkAll = ischeck;
      this.checkList = [];
      if (!ischeck) {
        this.batchCheck(ischeck);
      }
    },
    changeBox(ischeck) {
      this.checkList = this.termList
        .filter((term) => term.isChecked)
        .map((term) => term.term_id);
      this.checkAll = this.checkList.length >= this.termList.length;
      this.$emit("check-tnc", this.requiredIsChecked);
    },
    requiredNotCheck(term_id) {
      return !this.checkList.includes(term_id);
    },
    batchCheck(isCheck) {
      this.termList.forEach((term) => {
        term.isChecked = isCheck;
      });
      this.setCheckList();
    },
    setCheckList() {
      this.checkList = this.termList
        .filter((term) => term.isChecked)
        .map((term) => term.term_id);
    },
    getCheckedTermIds() {
      const displayList = (this.displayTerms || []).map((term) => term.term_id);
      return this.checkList.concat(displayList).join(",");
    },
    handlePreConsent(terms = []) {
      const preConsent = terms.find(
        (term) =>
          term.pre_consent_display_term_ids &&
          term.pre_consent_display_term_ids.length &&
          term.pre_consent_hint
      );
      if (preConsent) {
        let preConsentDisplayTermIds =
          preConsent.pre_consent_display_term_ids || [];
        const termIds = terms.map((item) => item.term_id);
        // 取前置条款和条款列表交集， 处理后端可能的异常数据，前置条款必须存在返回的条款列表中
        preConsentDisplayTermIds = termIds.filter((id) =>
          preConsentDisplayTermIds.includes(id)
        );

        this.preConsent = {
          termId: preConsent.term_id,
          preConsentDisplayTermIds,
        };
        this.preConsentHint = preConsent.pre_consent_hint;
      }
    },
    onCancel() {
      // 用户取消授权，系统取消勾选EDM
      this.termList.find(
        (term) => term.term_id === this.preConsent.termId
      ).isChecked = false;
      this.setCheckList();
      this.callback(true, this.getCheckedTermIds());
      this.showConsentDialog = false;
    },
    onConfirm() {
      // 用户同意授权，系统补勾选前置条款
      this.preConsent.preConsentDisplayTermIds.forEach((id) => {
        this.termList.find((term) => term.term_id === id).isChecked = true;
      });
      this.setCheckList();
      this.callback(true, this.getCheckedTermIds());
      this.showConsentDialog = false;
    },
    handleLinkTarget() {
      document.querySelectorAll(".klk-markdown a").forEach((node) => {
        node.target = "_blank";
      });
    },
  },
  watch: {
    checkAll: function (val) {
      if (val) {
        this.batchCheck(val);
      }
      this.$emit("check-tnc", this.requiredIsChecked);
    },
    termList: function (val) {
      this.setCheckList();
    },
  },
};
</script>

<style lang="scss">
.klk-login-term-list {
  margin-top: 24px;
  text-align: left;

  .required-not-check {
    .klk-checkbox-label {
      color: #e64340;
    }
  }
  .tnc-list {
    margin-bottom: 24px;
    &.padding-left-24 {
      padding-left: 24px;
    }
    .tnc-item {
      margin-top: 5px;

      &.tnc-item-required_error {
        .klk-markdown, .klk-checkbox .klk-markdown a {
          color: #f44622;
        }

        .klk-checkbox-base {
          border-color: #f44622;
        }
      }
    }

    .klk-markdown {
      font-size: 12px;
      color: #757575;

      * {
        margin: 0;
      }

      a {
        font-size: 12px;
        text-decoration: underline;
        color: #757575;
      }
    }

    &.klk-login-terms .klk-markdown {
      text-align: left;
      color: #b2b2b2;
      line-height: 1.4;

      a {
        text-decoration: underline;
        color: #b2b2b2;
        font-size: 12px;
      }
    }
  }

  .checkbox-label {
    font-size: 12px;
    color: #757575;
  }

  .checkbox-label-link {
    text-decoration: underline;
  }

  @keyframes warn {
    0% {
      transform: translateX(0);
    }

    20% {
      transform: translateX(-10px);
    }

    40% {
      transform: translateX(10px);
    }

    60% {
      transform: translateX(-8px);
    }

    80% {
      transform: translateX(8px);
    }
    100% {
      transform: translateX(-8px);
    }
  }
  .klk-login--terms-warn {
    display: inline-block;
    box-sizing: content-box;
    position: relative;
    height: 20px;
    line-height: 20px;
    left: 0px;
    top: 0px;
    border-radius: 4px;
    padding: 3px 6px 3px 6px;
    background-color: #ff9c00;
    color: #fff;
    font-size: 12px;
    margin-bottom: 6px;

    // 隐藏箭头
    &::after {
      // content: "";
      display: none;
      // position: absolute;
      // border-left: 6px solid transparent;
      // border-right: 6px solid transparent;
      // bottom: -6px;
      // border-top: 6px solid #ff9c00;
      // left: 10px;
      // margin-left: -6px;
    }

    &.actived {
      animation: 0.3s warn;
    }
  }

  .checkbox-label--link {
    text-decoration: underline;
  }

  // .klk-login-terms-checkbox ::v-deep .klk-checkbox-label {
  //   color: #757575;
  //   font-size: 12px;

  //   a {
  //     color: #757575;
  //   }
  // }
}

.klk-login_pre-consent-footer {
  text-align: right;
  margin-top: 20px;
}
</style>
