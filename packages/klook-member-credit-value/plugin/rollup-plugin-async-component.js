export default function resolveAsyncComponent (options = {}) {
  return {
    name: 'async-component',

    renderChunk (code, chunkInfo, options) {
      const { format } = options
      const { isDynamicEntry } = chunkInfo
      let codeString = `${code}`

      if (format !== 'cjs') {
        return {
          code: codeString,
          map: false,
        };
      }

      if (isDynamicEntry) {
        const esmoduleMarker = `Object.defineProperty(exports, '__esModule', { value: true });`

        if (codeString.includes('use strict')) {
          codeString = codeString.replace("'use strict';", "'use strict';\n\n" + esmoduleMarker)
        }

        if (!codeString.includes('__esModule')) {
          codeString = esmoduleMarker + '\n\n' + codeString
        }
      }

      return {
        code: codeString,
        map: false,
      };
    }
  };
}
