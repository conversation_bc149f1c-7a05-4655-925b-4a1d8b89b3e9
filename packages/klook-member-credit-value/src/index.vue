<template>
  <div class="klk-credit-member-value-box" @click.stop>
    <template v-if="inited && credit > 0">
      <LabelCredit
        v-if="type === 'label'"
        :webp="webp"
        :credit="credit"
        :member-credit-info="memberCreditInfo"
        :visible-reward-entry="visibleRewardEntry"
        :platform="_platform"
        :poptip-config="poptipConfig"
        :inhouse="inhouse"
        v-on="$listeners"
      />
      <card-credit
        v-else
        :webp="webp"
        :credit="credit"
        :member-credit-info="memberCreditInfo"
        :visible-reward-entry="visibleRewardEntry"
        :platform="_platform"
        :poptip-config="poptipConfig"
        :visible-min-credit="visibleMinCredit"
        :inhouse="inhouse"
        v-on="$listeners"
      />
    </template>
  </div>
</template>

<script lang="ts">
import { Vue, Component, Prop, Watch, Provide } from 'vue-property-decorator'
import { $colorTextPrimary } from '@klook/klook-ui/lib/utils/design-token-esm'
import LabelCredit from './components/label-credit.vue'
import CardCredit from './components/card-credit.vue'

@Component({
  components: {
    CardCredit,
    LabelCredit
  }
})
export default class KlkMemberCreditValue extends Vue {
  // @Prop({ default: 0 }) credit!: number
  @Prop({ default: () => {} }) creditsInfo!: any

  @Prop({ default: 'card' }) type!: string
  @Prop({ default: false }) visibleRewardEntry!: boolean
  @Prop({ default: '' }) platform!: string
  @Prop({ default: null }) userInfo!: any
  @Prop({ type: Number, default: undefined }) webp!: number
  @Prop() axios!: any
  @Prop() inhouse!: any
  @Prop({ default: () => ({}) }) poptipConfig!: any
  @Prop({ type: Number, default: 0 }) visibleMinCredit!: number

  @Provide() provideCreditsInfo = () => this.creditsInfo;

  get credit() {
    return this.creditsInfo?.original_credit || 0
  }

  get _axios() {
    return this.axios || this.$axios || null
  }

  get _state() {
    return this?.$store?.state || null
  }

  get _platform() {
    return this.platform || this._state?.klook?.platform || 'desktop'
  }

  get user() {
    return this._state?.auth?.user || null
  }

  get _userInfo() {
    return this.userInfo || this.user || null
  }

  memberCreditInfo: any = null
  inited: boolean = false

  get $colorTextPrimary() {
    return $colorTextPrimary
  }

  get userId() {
    return this._userInfo?.id || ''
  }

  @Watch('credit', {
    immediate: true
  })
  creditChangeRefresh(val: number | string, oldVal: number | string) {
    if (+val !== +oldVal) {
      this.getMemberCreditInfoData()
    }
  }

  @Watch('userId')
  onReloadMemberInfo(val: number | string, oldVal: number | string) {
    if (+val !== +oldVal) {
      this.getMemberCreditInfoData()
    }
  }

  async getMemberCreditInfoData() {
    const res = await this._axios.$get(
      '/v3/userserv/user/bff_service/get_membership_credit_component_info'
    )
    if (res?.success && res.result) {
      this.memberCreditInfo = res.result
    }
    this.inited = true
  }
}
</script>
<style lang="scss" scoped></style>
