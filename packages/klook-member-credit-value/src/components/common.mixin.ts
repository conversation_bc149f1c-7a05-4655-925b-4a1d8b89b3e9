import { Vue, Component, Prop, Inject } from 'vue-property-decorator'
import transformImageUrl from '@klook/klk-traveller-utils/lib/transformImageUrl'

@Component({})
export default class CommonMixin extends Vue {
  // @Prop({ default: 0 }) credit!: number
  @Prop({ default: null }) memberCreditInfo!: any
  @Prop({ default: false }) visibleRewardEntry!: boolean
  @Prop({ type: Number, default: undefined }) webp!: number
  @Prop({ default: 'desktop' }) platform!: string
  @Prop() inhouse!: any
  @Prop({ type: Number, default: 0 }) visibleMinCredit!: number

  @Inject() provideCreditsInfo!: any

  defaultPoptipOptions = {
    width: 400,
    'max-width': 400,
    'max-height': 550
  }

  get _inhouse() {
    return this?.inhouse || this?.$inhouse || (typeof window !== 'undefined' && window?.inhouse) || null
  }

  get creditInfo() {
    return this.memberCreditInfo || {}
  }

  get levelType() {
    const mapType: any = {
      '-1': 'Visitor',
      0: 'Lv1',
      1: 'Lv2'
    }
    return mapType[this.creditInfo?.level] || ''
  }

  get componentStyle() {
    return this.creditInfo?.component?.style || ''
  }

  get membershipResidence() {
    return this.creditInfo?.membership_residence || null
  }

  get confirmedResidence() {
    return this.creditInfo?.confirmed_residence || null
  }

  get rebateRate() {
    const { rebate_rate_multiplier = 0 } = this.creditInfo || {}
    return rebate_rate_multiplier
  }

  get is3X() {
    return this.rebateRate > 1
  }

  get creditsInfo() {
    return this.provideCreditsInfo()
  }

  // 积分总数
  get creditRate() {
    return this.creditsInfo?.credit || 0;
  }

  get componentData() {
    return this.creditInfo?.component[this.componentStyle] || {}
  }

  get countDesc() {
    return this.componentData?.count_desc || ''
  }

  get valueDesc() {
    return this.componentData?.value_desc || ''
  }

  get subDesc() {
    return this.componentData?.sub_desc || ''
  }

  get realWebp() {
    if (this.webp !== undefined) {
      return this.webp
    }
    return this?.$store?.state?.klook?.webp || 0
  }

  formatPicUrl(url: string, { width, height }) {
    return transformImageUrl(url, { width, height, webp: this.realWebp })
  }

  roundAndKeepDigits(num: number, digits: number) {
    return Math.round(num * Math.pow(10, digits)) / Math.pow(10, digits)
  }

  get currencyLabelMobile3XIcon() {
    return this.formatPicUrl(
      this.componentData.outer_ratio_image,
      { width: 96, height: 60 }
    )
  }

  get currencyLabelDesktop3XIcon() {
    return this.formatPicUrl(
      this.componentData.outer_ratio_image,
      { width: 102, height: 72 }
    )
  }

  get currencyCard3XIcon() {
    return this.formatPicUrl(
      this.componentData.outer_ratio_image,
      { width: 120, height: 96 }
    )
  }

  updateDataSpm(type: string, elementSelector: string, dataSpmString: string) {
    const selector = this.$el.querySelector(elementSelector)
    selector && selector.setAttribute(type, `${dataSpmString || ''}`)
  }

  sendDataSpm(type: string, elementSelector: Element | null, customExtra: any) {
    const inhouse = this._inhouse
    setTimeout(() => {
      inhouse && inhouse.track(type, elementSelector, customExtra)
    }, 0)
  }

  updated() {
    // console.log('componentStyle', this.componentData)
  }
}
