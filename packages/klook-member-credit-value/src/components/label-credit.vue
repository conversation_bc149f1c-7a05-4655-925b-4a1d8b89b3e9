<template>
  <div class="simple-credit-value-box">
    <component
      :is="platform === 'desktop' ? 'klk-poptip' : 'div'"
      defaultPoptipOptions="{"
      v-bind="
        platform === 'desktop'
          ? { placement: 'bottom', ...defaultPoptipOptions, ...poptipConfig }
          : {}
      "
    >
      <div
        class="simple_credit_value_btn"
        :class="`${is3X ? 'credit3x' : 'normal'} simple_credit_${platform}_layout`"
        :style="`color: ${componentData.label_text_color}; background-color: ${componentData.label_background_color};`"
        :data-spm-module="`CreditsEntry?ext=${JSON.stringify({
          LevelType: levelType,
          MemberResidence: membershipResidence,
          ConfirmedResidence: confirmedResidence,
          EntryType: 'Label'
        })}`"
        data-spm-virtual-item="__virtual"
        v-galileo-click-tracker="{ spm: 'CreditsEntry', componentName: 'klk-member-credit-value' }"
        @click="showCreditDetailDialog"
      >
        <div
          v-if="is3X"
          :style="`background-image:url(${
            platform == 'desktop' ? currencyLabelDesktop3XIcon : currencyLabelMobile3XIcon
          })`"
          class="credit_3x_currency_icon"
          :class="`credit_3x_currency_${platform}_icon`"
        ></div>
        <div class="credit_desc_box" :class="is3X ? 'gold_layout' : 'explorer_layout'">
          <span class="credit_desc_text">{{ creditDescText }}</span>
          <span class="credit_text">+{{ creditRate }}</span>
        </div>
        <IconNext
          v-if="platform === 'mobile'"
          theme="outline"
          size="16"
          :fill="componentData.label_text_color"
        />
        <IconChevronDown
          v-else
          theme="outline"
          size="16"
          :fill="componentData.label_text_color"
        />
      </div>
      <div
        v-if="platform === 'desktop'"
        slot="content"
        class="credit-dialog-content-box credit_label_content_poptip_layout"
        :data-spm-page="`CreditsDetailPopup?ext=${JSON.stringify({
          LevelType: levelType,
          MemberResidence: membershipResidence,
          ConfirmedResidence: confirmedResidence,
          EntryType: 'Label'
        })}`"
      >
        <template v-if="is3X">
          <GoldCreditValue
            :member-credit-info="memberCreditInfo"
            :webp="webp"
            type="credit"
          />
          <div class="credit_count_desc credit_count_desc_mweb_layout">
            {{ subDesc }}
          </div>
        </template>
        <template v-else>
          <div class="credit_value_title">{{ titleText }}</div>
          <div class="credit_value_desc">{{ countDesc }}</div>
          <div class="credit_currency_desc_poptip_layout normal">
            <div class="credit_currency_desc_text">
              <span class="currency_text">{{ creditsInfo.credit }}</span>
              <span class="credit_text">(≈ {{ currencyDesc }})</span>
            </div>
          </div>
          <div class="credit_count_desc">{{ subDesc }}</div>
          <RewardEntry
            class="reward-entry"
            :visible-reward-entry="visibleRewardEntry"
            :member-credit-info="memberCreditInfo"
            :webp="webp"
          />
        </template>
      </div>
    </component>

    <klk-bottom-sheet
      :visible.sync="visibleCreditDialog"
      :title="titleText"
      :show-close="platform === 'mobile'"
      :transfer="platform === 'mobile'"
      :closable="platform !== 'mobile'"
      :open.sync="visibleCreditDialog"
      :overlay-closable="platform !== 'mobile'"
      :show-default-footer="false"
      class="simple_credit-dialog"
      @close="closeHandle"
    >
      <div
        ref="creditDialogContent"
        class="credit-dialog-content-box"
        :data-spm-page="`CreditsDetailPopup?trg=manual&ext=${JSON.stringify({
          LevelType: levelType,
          MemberResidence: membershipResidence,
          ConfirmedResidence: confirmedResidence,
          EntryType: 'Label'
        })}`"
      >
        <template v-if="is3X">
          <GoldCreditValue
            :member-credit-info="memberCreditInfo"
            :webp="webp"
            type="credit"
          />
          <div class="credit_count_desc credit_count_desc_mweb_layout">
            {{ subDesc }}
          </div>
        </template>
        <template v-else>
          <div class="credit_value_desc">{{ countDesc }}</div>
          <div class="credit_currency_desc normal">
            <div class="credit_currency_desc_text">
              <span class="currency_text"> {{ creditsInfo.credit }} </span>
              <span class="credit_text"> (≈ {{ currencyDesc }}) </span>
            </div>
          </div>
          <div class="credit_count_desc">{{ subDesc }}</div>
          <RewardEntry
            class="reward-entry"
            :visible-reward-entry="visibleRewardEntry"
            :member-credit-info="memberCreditInfo"
            :webp="webp"
          />
        </template>
        <klk-button
          data-spm-module="GotIt"
          data-spm-virtual-item="__virtual"
          v-galileo-click-tracker="{ spm: 'GotIt', componentName: 'klk-member-credit-value' }"
          class="got_it_btn"
          type="primary"
          block
          @click="gotItHandle"
        >
          {{ buttonText }}
        </klk-button>
      </div>
    </klk-bottom-sheet>
  </div>
</template>
<script lang="ts">
import { Component, Prop, Inject } from 'vue-property-decorator'
import { $colorAccent10, $colorAccent8 } from '@klook/klook-ui/lib/utils/design-token-esm'
import IconChevronDown from '@klook/klook-icons/lib/IconChevronDown'
import IconNext from '@klook/klook-icons/lib/IconNext'

import CommonMixin from './common.mixin'
import RewardEntry from './reward-entry.vue'
import GoldCreditValue from './gold-credit-value.vue'

import KlkButton from '@klook/klook-ui/lib/button/index'
import KlkBottomSheet from '@klook/klook-ui/lib/bottom-sheet/index'
import KlkPoptip from '@klook/klook-ui/lib/poptip/index'

@Component({
  components: {
    RewardEntry,
    GoldCreditValue,
    IconChevronDown,
    IconNext,
    KlkButton,
    KlkBottomSheet,
    KlkPoptip
  }
})
export default class LabelCreditValue extends CommonMixin {
  @Prop({ default: {} }) poptipConfig!: any
  @Inject() readonly provideCreditsInfo!: any

  get creditsInfo() {
    return this.provideCreditsInfo() || {}
  }

  visibleCreditDialog = false

  get colorAccent10() {
    return $colorAccent10
  }

  get colorAccent8() {
    return $colorAccent8
  }

  get buttonText() {
    return this.creditInfo?.component?.button_desc || ''
  }

  get titleText() {
    return this.creditInfo?.component?.title || ''
  }

  get currencyDesc() {
    if (!this.creditsInfo) return '';
    const { price = '0', price_desc = '' } = this.creditsInfo.credit_price || {};
    return price_desc.replace('{price}', price);
  }

  // 积分总数描述
  get creditDescText() {
    const { credit_desc } = this.creditInfo || {}
    return credit_desc
  }

  // 积分总数描述
  get creditDesc() {
    const { credit_desc } = this.creditInfo || {}
    return `${this.creditRate} ${credit_desc}`
  }

  get rewardsEntryDesc() {
    return this.componentData?.rewards_entry_desc
  }

  showCreditDetailDialog() {
    if (this.platform !== 'desktop') {
      this.visibleCreditDialog = true
      if (window) {
        this.$nextTick(() => {
          const target: any = this.$refs.creditDialogContent
          this.sendDataSpm('pageview', target, {
            LevelType: this.levelType,
            MemberResidence: this.membershipResidence,
            ConfirmedResidence: this.confirmedResidence,
            EntryType: 'Label'
          })
        })
      }
      this.$emit('open-modal')
    }
  }

  closeHandle() {
    this.$emit('close-modal')
  }

  gotItHandle() {
    this.visibleCreditDialog = false
    if (this.platform !== 'mobile') {
      this.$emit('close-modal')
    }
  }

  created() {}
}
</script>
<style lang="scss" scoped>
.credit_count_desc {
  color: $color-text-secondary;
  @include font-body-s-regular();
}
.credit_desc_box {
  display: inline-flex;
  align-items: center;
}
.simple_credit_value_btn {
  display: inline-flex;
  align-items: center;
  background: $color-accent-14;
  border-radius: 6px;
  color: $color-accent-8;
  padding-right: 6px;
  height: 24px;
  cursor: pointer;
  overflow: hidden;
  @include font-body-s-semibold();
  .credit_desc_box {
    margin-left: 4px;
  }
  &.simple_credit_mobile_layout {
    height: 20px;
    padding-right: 4px;
    @include font-caption-m-semibold();
    .credit_3x_currency_icon {
      height: 20px;
      width: 32px;
    }
    .credit_desc_box {
      margin-left: 2px;
    }
  }
  &.normal {
    padding-left: 4px;
    background: $color-success-background;
  }
  // &.credit3x {
  //   color: $color-accent-10;
  //   background-color: $color-caution-background;
  // }
  .credit_text {
    margin-right: 4px;
    display: inline-block;
    // color: $color-accent-8;
    // @include font-body-s-bold();
  }
  .credit_desc_text {
    // color: $color-text-primary;
    margin-right: 3px;
  }
  .credit_3x_currency_icon {
    width: 34px;
    height: 24px;
    display: inline-block;
    background-size: contain;
    background-position: left;
    background-repeat: no-repeat;
  }
}
.credit_currency_desc_text {
  display: flex;
  align-items: center;
}
.credit_currency_desc {
  margin-top: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  vertical-align: bottom;

  padding: 8px 12px;

  background: $color-bg-3;
  border-radius: 12px;
  color: $color-accent-8;

  .currency_text {
    @include font-heading-s();
  }
  .credit_text {
    display: inline-block;
    margin-left: 4px;
    @include font-body-s-regular();
  }
}
.credit_currency_desc_poptip_layout {
  margin-top: 8px;
  display: inline-flex;
  align-items: center;
  vertical-align: bottom;
  background: $color-success-background;
  border-radius: 8px;
  color: $color-accent-8;
  padding: 4px 12px;
  .currency_text {
    @include font-body-m-bold();
  }
  .credit_text {
    display: inline-block;
    margin-left: 4px;
    @include font-body-s-regular();
  }
}
.credit_label_content_poptip_layout {
  .credit_count_desc {
    margin-top: 8px;
  }
  .credit_value_desc {
    margin-top: 8px;
  }
}
.credit_count_desc {
  margin-top: 12px;
}
.credit_value_title {
  @include font-body-m-bold();
}
.credit_value_desc {
  @include font-body-m-regular();
}
.simple_credit-dialog {
  color: $color-text-primary;
  .credit_3x_currency_icon {
    width: 44px;
    height: 32px;
    display: inline-block;
  }
}
.reward-entry {
  margin-top: 16px;
}
.credit_count_desc_mweb_layout {
  margin-top: 12px;
}
.got_it_btn {
  margin-top: 16px;
}
</style>
