<template>
  <div class="gold-credit-value-box">
    <div class="count_desc">
      {{ type === 'credit' ? countDesc : valueDesc }}
    </div>
    <div class="credit_calculate-content">
      <div class="credit_calculate_total">
        <div class="credit_calculate_total_left">
          <div class="gold_member_logo" :style="{backgroundImage: `url(${goldMemberBg})`}">
            <div
              class="gold_member_logo_icon"
            >
              <img class="gold_member_logo_icon" :src="goldMemberLogo" alt="">
            </div>
            <div class="gold_member_logo_text">{{ goldIconText }}</div>
          </div>
          <span class="earn_text">{{ getCreditDesc }}</span>
        </div>
        <div class="credit_calculate_total_right">
          <template v-if="type === 'credit'">
            <span class="credit_value_text">{{ creditRate }}</span>
            <span class="credit_text">(≈ {{ currencyDesc }})</span>
          </template>
          <template v-else>
            <span class="credit_value_text">≈ {{ currencyDesc }}</span>
            <span class="credit_text">({{ creditDesc }})</span>
          </template>
        </div>
      </div>
      <div class="rate_credit_icon">
        <img v-lazy="credit3XRateIcon" class="credit_3x_rate_icon" />
        <img v-lazy="credit3XRateUpIcon" class="credit_3x_up_icon" />
      </div>
      <div class="credit_calculate_base">
        <div class="credit_calculate_base_left">{{ supposeCreditDesc }}</div>
        <div class="credit_calculate_base_right">
          <template v-if="type === 'credit'">
            <span class="credit_value_text">{{ creditsInfo.original_credit }}</span>
            <div class="credit_text">(≈ {{ baseCurrencyDesc }})</div>
          </template>
          <template v-else>
            <span class="credit_value_text">≈ {{ baseCurrencyDesc }}</span>
            <div class="credit_text">({{ baseCreditDesc }})</div>
          </template>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Prop, Inject } from 'vue-property-decorator'

import CommonMixin from './common.mixin'

@Component({
  components: {}
})
export default class NormalCreditValue extends CommonMixin {
  // @Prop({ default: 0 }) credit!: number
  @Prop({ default: 'creditValue' }) type!: string
  @Prop({ default: null }) memberCreditInfo!: any
  @Prop({ type: Number, default: undefined }) webp!: number
  @Inject() readonly provideCreditsInfo!: any

  get creditsInfo() {
    return this.provideCreditsInfo() || {};
  }

  get currencyDesc() {
    if (!this.creditsInfo) return '';
    const { price = 0, price_desc = '' } = this.creditsInfo.credit_price || {}
    const defaultText = price_desc
    return defaultText.replace('{price}', price)
  }

  // 积分总数描述
  get creditDesc() {
    if (!this.creditsInfo) return '';
    const { credit_desc } = this.creditInfo
    return `${this.creditsInfo.credit} ${credit_desc}`
  }

  get baseCurrencyDesc() {
    if (!this.creditsInfo) return '';
    const { price = 0, price_desc = '' } = this.creditsInfo.original_credit_price || {}
    const defaultText = price_desc
    return defaultText.replace('{price}', price)
  }

  // 基础积分总数
  get baseCreditDesc() {
    if (!this.creditsInfo) return '';
    const { credit_desc } = this.creditInfo
    return `${this.creditsInfo.original_credit} ${credit_desc}`
  }

  get getCreditDesc() {
    return this.componentData?.get_credit_desc || ''
  }

  get supposeCreditDesc() {
    return this.componentData?.suppose_credit_desc || ''
  }

  get goldIconText() {
    return this.componentData?.icon_text || this.componentData?.gold_icon_text
  }

  get goldMemberLogo() {
    return this.formatPicUrl(
      this.componentData?.icon,
      { width: 55, height: 49 }
    )
  }

  get goldMemberBg() {
    return this.componentData?.background_image
  }

  get credit3XRateIcon() {
    return this.formatPicUrl(
      this.componentData?.inner_ratio_image,
      { width: 120, height: 96 }
    )
  }

  get credit3XRateUpIcon() {
    return this.formatPicUrl(
      'https://res.klook.com/image/upload/v1694601358/y8lvyjqqwectpt2v9gug.png',
      { width: 34, height: 48 }
    )
  }


  created() {}
}
</script>
<style lang="scss">
.markdown-body img {
  background-color: initial;
}
.gold-credit-value-box {
  .count_desc {
    color: $color-text-primary;
    @include font-body-m-regular();
  }
  .credit_calculate-content {
    margin-top: 12px;
    padding: 8px;
    border-radius: 16px;
    border: 1px solid $color-border-dim;
  }
  .credit_calculate_total {
    padding: 8px 12px;
    border-radius: 12px;
    background-color: $color-bg-3;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
  }
  .earn_text {
    display: block;
    margin-top: 4px;
    color: $color-text-secondary;
    @include font-body-m-regular();
  }
  .credit_calculate_total_right {
    color: $color-success;
    .credit_value_text {
      display: block;
      @include font-heading-s();
    }
    .credit_text {
      display: block;
      @include font-body-s-regular();
    }
  }
  .rate_credit_icon {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 12px;
    .credit_3x_rate_icon{
      height: 32px;
    }
    .credit_3x_up_icon {
      margin-left: 4px;
      height: 24px;
    }
  }
  .gold_member_logo {
    // background: linear-gradient(270.8deg, rgb(242, 130, 0) 21.5%, rgb(255, 221, 130) 104.48%);
    border-radius: 6px;
    display: inline-flex;
    align-items: center;
    height: 24px;
    width: auto;
    border-radius: 6px;
    overflow: hidden;
    padding-right: 6px;
    background-size: 100% 100%;
    .gold_member_logo_icon{
      width: 27px;
      height: 100%;
      background-size: contain;
      background-position: center;
      background-repeat: no-repeat;
    }
    .gold_member_logo_text{
      color: $color-text-reverse;
      @include font-body-s-bold();
    }
  }
  .credit_calculate_base {
    margin-top: 12px;
    display: flex;
    align-items: center;
    background-color: $color-bg-3;
    justify-content: space-between;
    padding: 8px 12px;
    border-radius: 12px;
    .credit_calculate_base_left {
      color: $color-text-secondary;
      @include font-body-m-regular();
    }
    .credit_calculate_base_right {
      color: $color-text-primary;
    }
    .credit_value_text {
      @include font-body-m-regular();
    }
    .credit_text {
      @include font-body-s-regular();
    }
  }
}
</style>
