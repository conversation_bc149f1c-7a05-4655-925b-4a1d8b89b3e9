<template>
  <a
    v-if="visibleRewardEntry && !is3X && isMemberArea"
    class="reward-entry-box"
    :href="rewardLink"
    target="_blank"
    data-spm-module="Get3XCredits"
    data-spm-virtual-item="__virtual"
    v-galileo-click-tracker="{ spm: 'Get3XCredits', componentName: 'klk-member-credit-value' }"
  >
    <img v-lazy="creditEntry3XIcon" class="credit_3x_entry_icon" />
    <span class="entry_text">{{ rewardsEntryDesc }}</span>
    <IconNext theme="outline" size="16" :fill="colorTextPlaceholder" />
  </a>
</template>

<script lang="ts">
import { Component, Prop } from 'vue-property-decorator'
import IconNext from '@klook/klook-icons/lib/IconNext'
import { $colorTextPlaceholder } from '@klook/klook-ui/lib/utils/design-token-esm'

import CommonMixin from './common.mixin'

@Component({
  components: { IconNext }
})
export default class NormalCreditValue extends CommonMixin {
  @Prop({ default: null }) memberCreditInfo!: any
  @Prop({ default: false }) visibleRewardEntry!: boolean
  @Prop({ type: Number, default: undefined }) webp!: number

  get isMemberArea() {
    return this.creditInfo?.level != -1
  }

  get colorTextPlaceholder() {
    return $colorTextPlaceholder
  }

  get rewardsEntryDesc() {
    return this.componentData?.rewards_entry_desc
  }

  get creditEntry3XIcon() {
    return this.formatPicUrl(
      'https://res.klook.com/image/upload/v1694506194/cvsbkp4rijfttoeo2fss.png',
      { width: 64, height: 52 }
    )
  }

  get rewardLink() {
    return this.componentData?.rewards_entry_deeplink || 'javascript:void(0)'
  }

  created() {}
}
</script>
<style lang="scss" scoped>
.reward-entry-box {
  padding: 8px 12px;
  border-radius: 16px;
  border: 1px solid $color-border-normal;
  display: flex;
  align-items: center;
  cursor: pointer;
  .credit_3x_entry_icon {
    width: 32px;
    height: 26px;
  }
  .entry_text {
    flex: 1;
    color: $color-text-primary;
    padding: 0 12px;
    @include font-body-s-regular();
    text-decoration: none;
  }
}
</style>
