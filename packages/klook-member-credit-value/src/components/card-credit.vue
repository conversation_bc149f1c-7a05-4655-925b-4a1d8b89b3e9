<template>
  <div
    v-if="creditRate >= visibleMinCredit"
    class="normal-credit-value-box"
    :data-spm-module="`CreditsDetail?ext=${JSON.stringify({
      LevelType: levelType,
      MemberResidence: membershipResidence,
      ConfirmedResidence: confirmedResidence,
      EntryType: 'Card'
    })}`"
  >
    <div class="credit_value_title">{{ titleText }}</div>
    <div class="credit_value_desc">{{ valueDesc }}</div>
    <component
      :is="platform === 'desktop' && is3X ? 'klk-poptip' : 'div'"
      v-bind="{ placement: 'bottom', ...(platform === 'desktop' && is3X ? {...defaultPoptipOptions,...poptipConfig} : { ...defaultPoptipOptions }) }"
    >
      <div
        class="credit_currency_desc"
        :class="is3X ? 'credit3x' : 'normal'"
        :style="`color: ${componentData.label_text_color}; background-color: ${componentData.label_background_color};`"
        v-bind="creditCurrencyDescSpmData"
        v-galileo-click-tracker="{ spm: 'CreditsEntry', componentName: 'klk-member-credit-value', enable: is3X }"
        @click="show3XCreditDesc"
      >
        <div
          v-if="is3X"
          :style="`background-image:url(${currencyCard3XIcon})`"
          class="credit_3x_currency_icon"
        ></div>
        <div class="credit_currency_desc_text">
          <span class="currency_text">≈ {{ currencyDesc }}</span>
          <span class="credit_text">({{ creditDesc }})</span>
        </div>
        <IconChevronDown
          v-if="is3X"
          class="down_icon"
          theme="outline"
          size="20"
          :fill="componentData.label_text_color"
        />
      </div>
      <div v-if="platform === 'desktop' && is3X" slot="content">
        <GoldCreditValue :member-credit-info="memberCreditInfo" :webp="webp" />
      </div>
    </component>
    <div class="credit_count_desc">{{ subDesc }}</div>
    <RewardEntry
      class="reward-entry"
      :visible-reward-entry="visibleRewardEntry"
      :member-credit-info="memberCreditInfo"
      :webp="webp"
    />

    <template v-if="platform === 'mobile'">
      <klk-bottom-sheet
        class="normal_credit-dialog"
        :visible.sync="visibleDescBottomSheet"
        :title="titleText"
        show-close
        :transfer="true"
        @close="closeHandle"
      >
        <div class="normal_credit-dialog_content_box">
          <GoldCreditValue :member-credit-info="memberCreditInfo" :webp="webp" />
          <div class="credit_count_desc credit_count_desc_mweb_layout">
            {{ subDesc }}
          </div>
          <klk-button class="got_it_btn" type="primary" block @click="getItHandle">
            {{ buttonText }}
          </klk-button>
        </div>
      </klk-bottom-sheet>
    </template>
  </div>
</template>

<script lang="ts">
import { Component, Prop, Inject } from 'vue-property-decorator'
import { $colorAccent10 } from '@klook/klook-ui/lib/utils/design-token-esm'
import IconChevronDown from '@klook/klook-icons/lib/IconChevronDown'

import KlkButton from "@klook/klook-ui/lib/button/index";
import KlkBottomSheet from "@klook/klook-ui/lib/bottom-sheet/index";
import KlkPoptip from "@klook/klook-ui/lib/poptip/index";

import CommonMixin from './common.mixin'
import RewardEntry from './reward-entry.vue'
import GoldCreditValue from './gold-credit-value.vue'



@Component({
  components: { RewardEntry, GoldCreditValue, IconChevronDown, KlkButton, KlkBottomSheet, KlkPoptip }
})
export default class NormalCreditValue extends CommonMixin {
  @Prop({ default: {} }) poptipConfig!: any

  @Inject() readonly provideCreditsInfo!: any

  get creditsInfo() {
    return this.provideCreditsInfo();
  }

  visibleDescBottomSheet = false

  get colorAccent10() {
    return $colorAccent10
  }

  get buttonText() {
    return this.creditInfo?.component?.button_desc || ''
  }

  get titleText() {
    return this.creditInfo?.component?.title || ''
  }

  get currencyDesc() {
    if (!this.creditsInfo) return '';
    const { price = '0', price_desc = '' } = this.creditsInfo.credit_price || {};
    return price_desc.replace('{price}', price);
  }

  get creditCurrencyDescSpmData() {
    return this.is3X ? {
      'data-spm-module':`CreditsEntry?ext=${JSON.stringify({
          LevelType: this.levelType,
          MemberResidence: this.membershipResidence,
          ConfirmedResidence: this.confirmedResidence,
          EntryType: 'Card'
        })}`,
        'data-spm-virtual-item':'__virtual'
    }: {}
  }

  // 积分总数描述
  get creditDesc() {
    if (!this.creditsInfo) return '';
    const { credit_desc } = this.creditInfo
    return `${this.creditsInfo.credit} ${credit_desc}`
    // const { credit_desc } = this.creditInfo
    // return `${this.creditRate} ${credit_desc}`
  }

  get rewardsEntryDesc() {
    return this.componentData?.rewards_entry_desc
  }


  show3XCreditDesc() {
    if (!this.is3X || this.platform !== 'mobile') {
      return
    }
    this.visibleDescBottomSheet = true
    this.$emit('open-modal')
  }

  closeHandle() {
    this.$emit('close-modal')
  }

  getItHandle() {
    this.visibleDescBottomSheet = false
  }

  created() {}
}
</script>
<style lang="scss" scoped>
.credit_count_desc {
  color: $color-text-secondary;
  @include font-body-s-regular();
}
.credit_currency_desc_text{
  display: flex;
  align-items: center;
}
.normal-credit-value-box {
  background: $color-bg-1;
}
.normal-credit-value-box,
.dialog_content_box {
  padding: 16px 20px;
  border-radius: 16px;
  color: $color-text-primary;

  .credit_value_title {
    @include font-body-m-bold();
  }
  .credit_value_desc {
    margin-top: 8px;
    @include font-body-m-regular();
  }
  .credit_currency_desc {
    margin-top: 8px;
    display: inline-flex;
    align-items: center;
    vertical-align: bottom;
    background: $color-success-background;
    border-radius: 8px;
    color: $color-accent-8;
    &.normal {
      padding: 4px 12px;
    }
    &.credit3x {
      cursor: pointer;
      color: $color-accent-10;
      background-color: $color-caution-background;
      padding-right: 8px;
      .down_icon {
        margin-left: 4px;
      }
    }
    .currency_text {
      @include font-body-m-bold();
    }
    .credit_text {
      display: inline-block;
      margin-left: 4px;
      @include font-body-s-regular();
    }
  }

  .credit_count_desc {
    margin-top: 8px;
  }
  .credit_3x_currency_icon {
    width: 40px;
    height: 32px;
    display: inline-block;
    background-size: contain;
    background-position: center;
    background-repeat: no-repeat;
    margin-right: 2px;
  }
}
.reward-entry {
  margin-top: 16px;
}
.credit_count_desc_mweb_layout {
  margin-top: 12px;
}
.got_it_btn {
  margin-top: 12px;
}
</style>
