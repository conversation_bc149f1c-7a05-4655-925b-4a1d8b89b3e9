const getMemberCreditInfoData: any = {
  membership_residence: 'HK',
  confirmed_residence: 'HK',
  level: 1, // -1, 0, 1
  rebate_rate_multiplier: 3, // explorer: 1, gold: 3
  credit_unit_price: 0.1, // 积分单价，例：当currency为HKD是，credit_unit_price是0.1
  currency_symbol: 'HK$',
  currency_frac: 0, // 计算价值保留小数位数
  currency_desc: '{price} HK$',
  credit_desc: 'credits',
  credit_threshold: 10, // 展示积分的阈值
  "component": {
    "title": "Klook credits",
    "button_desc": "Got it",
    "style": "explorer",
    "explorer": {
      "value_desc": "You’ll earn this much back in savings:",
      "count_desc": "You'll get this many credits after taking part",
      "display_rewards_entry": false,
      "rewards_entry_desc": "Want to triple your credits? Check out Klook Rewards.",
      "rewards_entry_deeplink": "https://www.klook.com/zh-CN/rewards",
      "sub_desc": "Use these credits to save on your next booking!"
    },
    "gold": {
      "value_desc": "You’ll earn this much back in savings:",
      "gold_icon_text": "Klook会员权益",
      "count_desc": "As a Gold member, you'll get up to 3 times the Klook credits!",
      "get_credit_desc": "You'll get",
      "suppose_credit_desc": "Regular amount",
      "sub_desc": "Use these credits to save on your next booking!"
    }
  }
}
export default getMemberCreditInfoData
