<template>
  <div class="member-credit-value">
    <klkMemberCreditValue :platform="'desktop'" :credit="30" type="card" :_$axios="mockaxios" :userInfo="{ id: 123123 }"/>
  </div>
</template>

<script lang="ts">
import { Vue, Component } from 'vue-property-decorator';

import klkMemberCreditValue from '../index.vue';
import mockData from './mock'

@Component({
  components: {
    klkMemberCreditValue
  }
})
export default class Demo extends Vue {
  mockaxios = {
    $get: async () => {
      return await {
        success: true,
        result: mockData
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.member-credit-value {
  padding: 440px 80px 100px;
  background: gainsboro;
}
</style>
