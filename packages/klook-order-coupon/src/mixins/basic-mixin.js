import { findComponentDownward } from '../utils/common-utils';
// import { genText } from '../i18n/index';
// import messages from '../../locales/index.js';

export default {
  // 测试环境多语言
  // async mounted() {
    // this.$t = genText(messages['en'])
  // },
  methods: {
    async setChosenCouponData(componentsName = 'mkt-basic-coupon-mobile', newCoupon) {
      const couponBasicComp = findComponentDownward(this, componentsName);
      this.$nextTick(()=>{
        couponBasicComp && (couponBasicComp.chosenCouponData = this.$lodash.cloneDeep(newCoupon))
      })
    },
    setShowAll(isShow = false){
      const BasicCouponList = findComponentDownward(this, 'MktBasicCouponList');
      this.$nextTick(()=>{
        BasicCouponList && (BasicCouponList.showAll = isShow)
      })
    },
    setCouponTypeTab(couponType = 'platformCoupon') {
      const CouponTypeTabs = findComponentDownward(this, 'MktCouponTypeTabs');
      this.$nextTick(() => {
        CouponTypeTabs && (CouponTypeTabs.curTab = (couponType))
      })
    },
    /**
     * @des 优惠券叠加使用公共逻辑
     * @param selectCoupon 选中优惠券
     * @param coupon 已选择优惠券
    */
    stacking_with_common_coupon(selectCoupon, coupon = {}){
      // common优惠券
      if (selectCoupon.finance_type == 'common_coupon' && selectCoupon.code) {
        let isHaveNoStackingPaymentCoupon = coupon?.payment_coupon?.code && !coupon?.payment_coupon?.stacking_with_others
        if ((selectCoupon.stacking_with_others && isHaveNoStackingPaymentCoupon) || !selectCoupon.stacking_with_others) {
          coupon = {common_coupon: selectCoupon}
        }
      }
      // 支付类优惠券
      if (selectCoupon.finance_type == 'payment_coupon' && selectCoupon.code) {
        let isHaveNoStackingCommonCoupon = coupon?.common_coupon?.code && !coupon?.common_coupon?.stacking_with_others
        if ((selectCoupon.stacking_with_others && isHaveNoStackingCommonCoupon) || !selectCoupon.stacking_with_others) {
          !selectCoupon.stacking_with_others && coupon?.common_coupon?.code ? this.$toast({
            message: this.$t("73233")
          }) : ''
          coupon = { payment_coupon: selectCoupon }
        }
      }
      return coupon
    },
    formmatTextId(textArr = []){
      let text = ''
      for (let index = 0; index < textArr.length; index++) {
        const element = textArr[index];
        if (this.$t(element) !== element) {
          text = this.$t(element)
          break
        }
      }
      return text ? text : textArr[0]
    }
  },
};

// 选择普通优惠券   
// 可叠加 -  1.有一个支付类不可叠加 只选中当前  2.其余情况正常选择 不处理
// 不可叠加 - 1.只选中当前优惠券没提示

// 选支付类优惠券   
// 可叠加 - 1.有一个普通不可叠加 只选中当前 2.其余情况不处理
// 不可叠加 - 1.只选中当前优惠券  有普通类优惠券才有提示
