/*
 * @Author: <PERSON>
 * @Date: 2023-04-24 11:20:23
 * @LastEditors: <PERSON>
 * @LastEditTime: 2023-04-24 11:23:10
 * @Description: file content
 */
export default {
  computed: {
    selectPlatformCoupon() {
      return this.couponData.chosenCoupon.common_coupon || null;
    },
    selectPaymentCoupon() {
      return this.couponData?.chosenCoupon?.payment_coupon || null;
    },
    enableSelectPlatformCoupon() {
      return this.usable.filter((item) => {
        if (this.selectPaymentCoupon) {
          // 支付优惠券与平台优惠券不可叠加
          return this.selectPaymentCoupon.stacking_with_others && item.stacking_with_others;
        }
        return true
      });
    },
    enableSelectPaymentCoupon() {
      return this.usablePaymentCoupon.filter((item) => {
        if (this.selectPlatformCoupon) {
          // 支付优惠券与平台优惠券不可叠加
          return this.selectPlatformCoupon.stacking_with_others && item.stacking_with_others;
        }
        return true
      });
    },
    combinedCouponResultStyle() {
      const paymentCouponInfo = this.couponData?.chosenCoupon?.payment_coupon?.code;
      const platformCouponInfo = this.couponData?.chosenCoupon?.common_coupon?.code;
      const paymentCouponLen = this.usablePaymentCoupon.length;
      const platformCouponLen = this.usable.length
      const totalCouponLen = paymentCouponLen + platformCouponLen;

      let text = "blank"
      if (!totalCouponLen) {
        text = 'disabled_select';
      }
      if (totalCouponLen && !platformCouponInfo && !paymentCouponInfo) {
        // 有平台优惠券，未选中平台优惠券
        text = 'has_select'
      }
      if (platformCouponLen && platformCouponInfo) {
        // 已选中平台优惠券
        text = 'selected_coupon'
      }
      return text;
    },
    combinedCouponResultText() {
      const platformCouponInfo = this.couponData?.chosenCoupon?.common_coupon;
      const useCouponLen = this.usable.length + this.usablePaymentCoupon.length;

      // 默认值文案是有可用但不可叠加的劵
      let text = "";
      // 不存在可用的优惠券
      if (!useCouponLen) {
        text = this.$t('173923');
      } else {
        let totalAmount = 0;
        let currencySymbol = ''
        if (platformCouponInfo && platformCouponInfo.coupon_use_info) {
          // 选择优惠劵，展示优惠金额
          const { coupon_use_info: { coupon_discount_dec, currency_symbol } } = platformCouponInfo; 
          totalAmount = coupon_discount_dec
          currencySymbol = currency_symbol
        }
    
        if (totalAmount > 0) {
          text = `- ${currencySymbol} ${totalAmount}`;
        } else {
          //  未选择可选的优惠券提示
          text = this.$t('91891', { amount: useCouponLen });
        }
      }
      return text;
    },
    platformCouponResultStyle() {
      const couponInfo = this.couponData?.chosenCoupon?.common_coupon;
      const useCouponLen = this.usable.length;
      let text = "disabled_select";
      if (!couponInfo && useCouponLen) {
        text = `has_select`;
      }
      if (couponInfo && useCouponLen) {
        text = `selected_coupon`;
      }
      return text;
    },
    platformCouponResultText() {
      const couponInfo = this.couponData?.chosenCoupon?.common_coupon;
      const useCouponLen = this.usable.length;
      const enableCouponLen = this.enableSelectPlatformCoupon.length;
      // 默认值文案是有可用但不可叠加的劵
      let text = "";
      // 不存在可用的优惠券
      if (!useCouponLen) {
        text = this.$t('91886');
      }

      if (enableCouponLen) {
        if (couponInfo && couponInfo.coupon_use_info) {
          // 选择优惠劵，展示优惠金额
          const {
            coupon_use_info: { coupon_discount_dec, currency_symbol },
          } = couponInfo;
          text = `- ${currency_symbol} ${coupon_discount_dec}`;
        } else {
          //  未选择可选的优惠券提示
          text = this.$t('91891', { amount: enableCouponLen });
        }
      }
      return text;
    },
    paymentCouponResultStyle() {
      const couponInfo = this.couponData?.chosenCoupon?.payment_coupon;
      const useCouponLen = this.usablePaymentCoupon.length;
      let text = "disabled_select";
      if (!couponInfo && useCouponLen) {
        text = `has_select`;
      }
      if (couponInfo && useCouponLen) {
        text = `selected_coupon`;
      }
      return text;
    },
    paymentCouponResultText() {
      const couponInfo = this.couponData?.chosenCoupon?.payment_coupon;
      const useCouponLen = this.usablePaymentCoupon.length;
      const enableCouponLen = this.enableSelectPaymentCoupon.length;
      // 默认值文案是有可用但不可叠加的劵
      let text = "";
      // 不存在可用的优惠券
      if (!useCouponLen) {
        text = this.$t('91886');
      }

      if (enableCouponLen) {
        // couponInfo.discount_category == 'percentage' && 
        if (couponInfo && couponInfo.discount_value_desc && couponInfo.coupon_use_info) {
          const {
            coupon_use_info: { currency_symbol },
          } = couponInfo;
          // 选择优惠劵，展示优惠金额
          text = this.$t('91892', {
            currency_symbol: couponInfo.discount_category == 'percentage' ? '' : currency_symbol || '',
            amount: couponInfo.discount_value_desc
          });
        } else {
          //  未选择可选的优惠券提示
          text = this.$t('91891', { amount: enableCouponLen });
        }
      }
      return text;
    },
  }
}
