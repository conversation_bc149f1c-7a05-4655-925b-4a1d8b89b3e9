import KlookOrderCoupon from './index.vue';
// import { genText } from './i18n/index';
// import messages from '../locales/index.js';
const lodash = require('lodash');

import {
  Button,
  Input,
  Checkbox,
  Collapse,
  Tabs,
  Icon,
  Modal,
  Poptip,
  Form,
  Loading,
  BottomSheet,
  Toast,
  Radio
} from '@klook/klook-ui';
const { TabPane } = require('@klook/klook-ui/lib/tabs')
const { CollapseItem } = require('@klook/klook-ui/lib/collapse')
const { FormItem } = require('@klook/klook-ui/lib/form')
const modal = require('@klook/klook-ui/lib/modal').default

import '@klook/klook-ui/lib/styles/components/button.scss'
import '@klook/klook-ui/lib/styles/components/input.scss'
import '@klook/klook-ui/lib/styles/components/checkbox.scss'
import '@klook/klook-ui/lib/styles/components/collapse.scss'
import '@klook/klook-ui/lib/styles/components/icon.scss'
import '@klook/klook-ui/lib/styles/components/modal.scss'
import '@klook/klook-ui/lib/styles/components/poptip.scss'
import '@klook/klook-ui/lib/styles/components/form.scss'
import '@klook/klook-ui/lib/styles/components/loading.scss'
import '@klook/klook-ui/lib/styles/components/bottom-sheet.scss'
import '@klook/klook-ui/lib/styles/components/tabs.scss'
import '@klook/klook-ui/lib/styles/components/toast.scss'
import '@klook/klook-ui/lib/styles/components/radio.scss'

// import iht
import inhouse from '@klook/inhouse-tracker';
// only use in demo and dev
process.env.NODE_ENV === 'development' ? inhouse.init({
  url: 'https://dev-frontsrv-new.dev.klook.io/v2/frontlogsrv/log/web',
  // url: 'https://t77.klook.io/v2/frontlogsrv/log/web',
  // url: window.KLK_PAGE_DATA._env === 'prd'
  //               ? 'https://log.klook.com/v2/frontlogsrv/log/web'
  //               : 'https://dev-frontsrv-new.dev.klook.io/v2/frontlogsrv/log/web',
  isDebugMode: true, //default false, 测试环境传 true
  timeout: 1500,
  autoPageView: true, //是否自动上报 pageview
  props: {
    userId: '8ee988ba1d5a62898a6d38d4a24d4043f0',
    pageId: '7da885c7-62d9-4119-aa9f-dd936b99c69b',
    deviceId: '2ea049f7-a81c-44cb-8d94-e0964c452a4a',
    keplerId: '2ea049f7-a81c-44cb-8d94-e0964c452a4a',
    experiments: [],
    siteName: 'klook',
    siteLanguage: 'zh-CN',
    siteCurrency: 'CNY'
  },
  transform(data) {
    // do something...
  }
}) : null

// apply klook-ui

KlookOrderCoupon.install = function (Vue, lang) {
  // apply i18n
  // const qs = new URLSearchParams(location.search);
  // let locale;
  // if (lang) {
  //   locale = lang;
  // } else if (Vue.prototype._i18n) {
  //   locale = this._i18n.options.locale;
  // } else if (qs.has('lang')) {
  //   locale = qs.get('lang');
  // } else {
  //   locale = 'en';
  // }

  Vue.component('klk-coupon-bottom-sheet', BottomSheet)
  Vue.component('klk-coupon-modal', modal)
  Vue.component('klk-coupon-button', Button)
  Vue.component('klk-coupon-input', Input)
  Vue.component('klk-coupon-icon', Icon)
  Vue.component('klk-coupon-tabs', Tabs)
  Vue.component('klk-coupon-tab-pane', TabPane)
  Vue.component('klk-coupon-collapse', Collapse)
  Vue.component('klk-coupon-collapse-item', CollapseItem)
  Vue.component('klk-coupon-form', Form)
  Vue.component('klk-coupon-form-item', FormItem)
  Vue.component('klk-coupon-checkbox', Checkbox)
  Vue.component('klk-coupon-poptip', Poptip)
  Vue.component('klk-coupon-radio', Radio)

  Vue.prototype.klkNewWebConfirm = Vue.prototype.$confirm
  if (!Vue.component('klk-modal')) {
    Vue.use(Modal);
  }

  Vue.prototype.$couponConfirm = Vue.prototype.$confirm
  Vue.prototype.$confirm = Vue.prototype.klkNewWebConfirm
  Vue.use(Loading);
  Vue.use(Toast);

  // Vue.prototype.$t = function (id){
  //   return id
  // }

  // 避免页面上出现多个弹窗，优化体验
  // todo，多个弹窗实际上表示了不同API的反馈错误，从功能上说是有意义的，后续可以讨论下这种场景更好的处理方式
  let dialogInstance = false
  if (Vue.prototype.$couponConfirm) {
    Vue.prototype.$singleConfirm = function (...args) {
      if (!dialogInstance) {
        dialogInstance = Vue.prototype.$couponConfirm.apply(this, args)
        dialogInstance.finally(() => {
          dialogInstance = false
        })
        return dialogInstance
      }
    }
  }

  // apply iht
  Vue.prototype.$lodash = lodash
  Vue.prototype.$inhouse = Vue.prototype.$inhouse || window.inhouse || inhouse;
  Vue.component(KlookOrderCoupon.name, KlookOrderCoupon);
};

export default KlookOrderCoupon;
