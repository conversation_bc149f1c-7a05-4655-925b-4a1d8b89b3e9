// pathIndex(ob, 'key_1.key_2')
function pathIndex(ob, str) {
  const keys_arr = str.split('.');
  return keys_arr.reduce((acc, cur) => {
    return acc[cur];
  }, ob);
}

// Find component downward
function findComponentDownward(context, componentName) {
  const childrens = context.$children;
  let children = null;

  if (childrens.length) {
    for (const child of childrens) {
      const name = child.$options.name;
      if (name === componentName) {
        children = child;
        break;
      } else {
        children = findComponentDownward(child, componentName);
        if (children) break;
      }
    }
  }
  return children;
}

//https://docs.google.com/spreadsheets/d/1v4OBZXF6if4XI7KudK49M9LlzPZVDrA-CuU_WvKiHM8/edit?pli=1#gid=0
const SUPPORT_CURRENCY_SYMBO = {
  HKD: 'HK$',
  USD: 'US$',
  TWD: 'NT$',
  SGD: 'S$',
  CNY: '¥',
  AUD: 'AUD',
  GBP: '£',
  LAK: '₭',
  EUR: '€',
  THB: '฿',
  NZD: 'NZ$',
  MYR: 'RM',
  JPY: '¥',
  AED: 'AED',
  PHP: '₱',
  KRW: '₩',
  VND: '₫',
  IDR: 'Rp',
  SEK: 'kr',
  NOK: 'kr',
  DKK: 'kr',
  CHF: 'CHF',
  RUB: '₽',
  TRY: 'YTL',
  ISK: 'kr',
  INR: '₹',
  KHR: 'KHR',
  MMK: 'BUK',
  MOP: 'MOP$',
  QAR: 'QR',
  OMR: 'OMR',
  JOD: 'JOD',
  LBP: 'LBP',
  FJD: '$',
  CAD: 'CAD',
  MUR: 'Rs',
  MXN: '$',
  MAD: 'DH',
  EGP: 'EGP',
  ZAR: 'R',
  MGA: 'MGA'
};

function getCurrencySymbol(currency) {
  return SUPPORT_CURRENCY_SYMBO[currency]
    ? SUPPORT_CURRENCY_SYMBO[currency]
    : '';
}

function formatTwoDecimal(amount) {
  if (isNaN(amount)) return '';
  return parseFloat(amount, 10).toFixed(2);
}

function formatPriceThousands(price) {
  price = (price || '0').toString();
  var tmp;

  if (price.indexOf('.') < 0) {
    tmp = price.replace(/(?=(?!(\b))(\d{3})+$)/g, ',');
  } else {
    price = price.split('.');
    tmp =
      price[0].toString().replace(/(?=(?!(\b))(\d{3})+$)/g, ',') +
      '.' +
      price[1];
  }

  return tmp;
}

function get_f2b_lang_map(frontLang) {
  var langs = {
    en: 'en_BS',
    'en-US': 'en_US',
    'en-AU': 'en_AU',
    'en-NZ': 'en_NZ',
    'en-GB': 'en_GB',
    'en-IN': 'en_IN',
    'en-SG': 'en_SG',
    'en-CA': 'en_CA',
    'en-PH': 'en_PH',
    'en-MY': 'en_MY',
    'en-HK': 'en_HK',
    'zh-CN': 'zh_CN',
    'zh-TW': 'zh_TW',
    'zh-HK': 'zh_HK',
    de: 'de_DE',
    it: 'it_IT',
    fr: 'fr_FR',
    ru: 'ru_RU',
    es: 'es_ES',
    ko: 'ko_KR',
    th: 'th_TH',
    vi: 'vi_VN',
    id: 'id_ID',
    ja: 'ja_JP'
  };
  return langs[frontLang];
}

export {
  pathIndex,
  findComponentDownward,
  getCurrencySymbol,
  formatPriceThousands,
  formatTwoDecimal,
  get_f2b_lang_map
};
