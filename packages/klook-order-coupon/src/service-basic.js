import Cookies from './utils/cookies.js';
import { get_f2b_lang_map } from './utils/common-utils';
// const axios = require("axios");
import axios from 'axios';
function genHeaders(vueInstance) {
  const token = Cookies.getItem('_pt');
  const {
    env: { language, currency, platform, host }
  } = vueInstance;
  return {
    'accept-language': get_f2b_lang_map(language),
    currency: currency,
    'x-platform': platform,
    'X-Klook-Host': host,
    version: 5.3,
    token
  };
}
// 获取业务线的信息
const internalRequestSettlement = async function(params, vueInstance) {
  console.log('internalRequestSettlement', vueInstance)
  const { isPageFirstRequest } = params;

  const {
    env: {
      currency,
      platform,
      host,
      settlementType,
      shoppingcartGuid,
      timeZoneOffSet
    },
    couponData: {
      chosenCoupon,
      chosenSrvCode,
      totalAmount,
      lastUseCouponDiscount
    }
  } = vueInstance;

  const url = `${host}/v2/usrcsrv/order/settlement`;
  const headers = genHeaders(vueInstance);
  const axiosParams = isPageFirstRequest
    ? {
        currency: currency,
        page_first_request: true,
        settlement_type: settlementType,
        shoppingcart_guid: shoppingcartGuid,
        time_zone_off_set: timeZoneOffSet
        // todo user info, time zone off 要不要
        // user_info: { mobile: "86-15200345369", region_code: "86" },
      }
    : {
        currency: currency,
        page_first_request: false,
        settlement_type: settlementType,
        shoppingcart_guid: shoppingcartGuid,
        time_zone_off_set: timeZoneOffSet,
        price_info: {
          settlement_discount: {
            coupon_code: `${[chosenCoupon, chosenSrvCode]
              .filter(item => item !== '')
              .join(',')}`,
            giftcard_guid: '',
            last_use_coupon_discount: lastUseCouponDiscount,
            last_use_credit: 0,
            last_use_giftcard_amount: '0',
            payment_gateway: [],
            use_credit: 0
          }
        }
      };

  try {
    const {
      data: { success, error, result }
    } = await axios.post(url, axiosParams, {
      headers
    });
    if (success) return Promise.resolve(result);
    if (error) {
      const errorObj = {
        ...error,
        errorComponent: 'order-coupon',
        errorRequest: url
      };
      return Promise.reject(errorObj);
    }
  } catch (error) {
    throw new Error(error);
  }
};

const getCouponList = async function(params, vueInstance) {
  const {
    env: {
      language,
      currency,
      platform,
      host,
      settlementType,
      shoppingcartGuid,
      timeZoneOffSet,
      tickets
    },
    couponData: {
      totalAmount,
    }
  } = vueInstance;
  const { select_coupon_code_list } = params;

  const url = `${host}/v2/couponapisrv/canonical/order/coupons`;
  const headers = genHeaders(vueInstance);
  const axiosParams = {
    coupon_key: '',
    currency: currency,
    language: get_f2b_lang_map(language),
    platform: platform,
    select_coupon_code_list,
    settlement_type: settlementType,
    shoppingcart_guid_list: [shoppingcartGuid],
    tickets: tickets,
    amount: totalAmount,
    supported_method_list: []
  };

  try {
    const {
      data: { success, error, result }
    } = await axios.post(url, axiosParams, {
      headers
    });
    if (success) return Promise.resolve(result);
    if (error) {
      const errorObj = {
        ...error,
        errorComponent: 'mkt-order-coupon',
        errorRequest: url
      };
      return Promise.reject(errorObj);
    }
  } catch (error) {
    throw new Error(error);
  }
};

const redeemCoupon = async function (params, vueInstance) {
  const {
    env: {
      language,
      currency,
      platform,
      host,
      settlementType,
      shoppingcartGuid,
      timeZoneOffSet,
      tickets
    }
  } = vueInstance;
  const { code, select_coupon_code_list } = params;

  const url = `${host}/v2/couponapisrv/canonical/order/redeem`;
  const headers = genHeaders(vueInstance);
  const axiosParams = {
    currency: currency,
    language: get_f2b_lang_map(language),
    platform: platform,
    settlement_type: settlementType,
    shoppingcart_guid_list: [shoppingcartGuid],
    tickets: tickets,
    // todo coupon_key?
    coupon_key: '',
    select_coupon_code_list,
    code,
    supported_method_list: []
  };

  try {
    const {
      data: { success, error, result }
    } = await axios.post(url, axiosParams, {
      headers: headers
    });
    if (success) return Promise.resolve(result);
    if (error) {
      const errorObj = {
        ...error,
        errorComponent: 'mkt-order-coupon',
        errorRequest: url
      };
      return Promise.reject(errorObj);
    }
  } catch (error) {
    throw new Error(error);
  }
};

export {
  internalRequestSettlement,
  getCouponList,
  redeemCoupon
};
