
const nswRedeem = {
  "error":{
      "code":"",
      "message":""
  },
  "result":{
      "coupon_code":"KLKNSWPSCQJPW4R"
  },
  "success":true
}
const nswSettlement = {
  "error":{
      "code":"",
      "message":""
  },
  "result":{
      "giftcard_info_list":null,
      "insurance_info":null,
      "price_info":{
          "is_hide_price_info":false,
          "coupon_info":{
              "coupon_code":"KLKNSWPSCQJPW4R",
              "coupon_discount":"290.1",
              "payment_restrict_desc":"",
              "payment_restrict_tips": " test text",
              "message":"",
              "message_code":"",
              "coupon_type":1,
              "is_srv_only":false
          },
          "credit_info":{
              "applicable_credits":1539,
              "applicable_credit_discount":"153.9",
              "credit_add_amount":15,
              "credit_discount":"0.0",
              "credit_use_amount":0,
              "dialog_desc":"- We'll update your usable credit balance automatically. The balance depends on total price and any promo codes used.\n- Promo codes and Klook credits can't be used for shipping and insurance fees.\n- Some payment methods require a minimum payment, which will be charged on top of your usable credits.",
              "hint_desc":"",
              "is_enable":1,
              "total_credits":1590
          },
          "currency":"HKD",
          "giftcard_info":{
              "amount":"0",
              "current_amount":"0",
              "dialog_desc":"Your usable balance will be adjusted automatically depending on total price, promo codes, or Klook credits applied. Some payment methods may require a minimum payment which will be charged on top of your usable balance",
              "giftcard_guid":"",
              "hint_desc":"",
              "is_enable":0,
              "usable_amount":"0"
          },
          "price_items":[
              {
                  "desc":"",
                  "name":"Subtotal",
                  "price_1":{
                      "currency":"HKD",
                      "desc":"",
                      "need_minus":0,
                      "need_strikethrough":0,
                      "value":"444.0"
                  },
                  "price_2":null
              }
          ],
          "total_payment_value":"153.9",
          "total_amount":"444.0",
          "discount":"290.1",
          "srv_coupon_code":"",
          "srv_discount":null,
          "minor_discount":null,
          "total_discount":null,
          "srv_special_desc":"",
          "promotion_amount":""
      },
      "shoppingcart_items":[
          {
              "addition_info":null,
              "commodity_info":{
                  "srv":null,
                  "activity_id":38939,
                  "activity_image_url":"https://res.klook.com/image/upload/fl_lossy.progressive,q_65,f_auto/c_fill,w_981,h_621/activities/b4kjji9psq927rj1irr3.jpg",
                  "activity_name":"测试造数据38939的英文标题-au",
                  "activity_template_id":1,
                  "category_id":101,
                  "sub_category_id":1,
                  "leaf_category_id":103,
                  "activity_type":"",
                  "city_id":23301,
                  "confirmation_type":1,
                  "country_id":20,
                  "merchant_id":2916,
                  "package_id":78540,
                  "package_name":"我是au package title",
                  "schedule_id":165378240078540,
                  "ticket_market_price":"333.0",
                  "ticket_sell_price":"444.0",
                  "is_hide_ticket_sell_price":false,
                  "upgrade_ticket_sell_price":"444.0",
                  "ins_state":0,
                  "schedule_time":"2022-05-29T00:00:00+08:00",
                  "seo_url":"www.klook.com",
                  "ins_total_price":"",
                  "ticket_type":0,
                  "activity_tags":[
                      {
                          "tag_name":"Dine & Discover / Parents Voucher",
                          "tag_type":1
                      }
                  ],
                  "participation_date_desc":"{schedule_time}",
                  "participation_date_map":{
                      "{schedule_time}":"2022-05-29T00:00:00Z"
                  },
                  "usage_validity_tips":"",
                  "usage_validity_render_obj":null,
                  "no_icon_tags":{
                      "template_tag_260":"",
                      "template_tag_35":"Popular Attractions",
                      "template_tag_38":"Museums & Galleries"
                  }
              },
              "insurance_info":null,
              "package_spec_list":[
                  {
                      "attr_id":59416,
                      "attr_name":"我是au package title",
                      "attr_tips_render_obj":[
                          {
                              "type":"paragraph",
                              "content":"null",
                              "props":{
                                  "margin_top":16
                              }
                          }
                      ],
                      "spec_id":31,
                      "spec_name":"Package Name"
                  }
              ],
              "price_items":[
                  {
                      "count":1,
                      "id":************,
                      "market_price":"333.0",
                      "name":"Adult",
                      "number_of_days":1,
                      "sell_price":"444.0",
                      "unit_type":0,
                      "srv_is_minor":false,
                      "promotion_info":null
                  }
              ],
              "shoppingcart_guid":"ea8bebc0-fb7f-4309-441e-321e4a5b815f",
              "first_promotion_info":null,
              "section_other_infos":[

              ],
              "preposition_other_info":null,
              "main_booking_item_guid":"",
              "total_price":"444.0",
              "addon_bookings":{
                  "ticket_sell_price":"0",
                  "items":null
              },
              "za_insurance_factor_map":null,
              "za_sdk_param":null
          }
      ],
      "sms_open":1,
      "mobile_verify_status":1,
      "contact_person_keys":[
          "family_name",
          "first_name",
          "traveller_email",
          "mobile"
      ],
      "contact_person_en_name":false,
      "section_infos":[
          {
              "section_type":4,
              "title":"Contact info",
              "tips":"",
              "grey_tips":"We'll only contact you about updates to your booking",
              "form_infos":[
                  {
                      "id":0,
                      "name":"",
                      "info_items":[
                          {
                              "id":11,
                              "field_key":"local_first_name",
                              "name":"First name",
                              "hint":"Please enter",
                              "hover":"",
                              "id_type":0,
                              "is_group":false,
                              "style":{
                                  "type":0,
                                  "required":1,
                                  "match_rule":{
                                      "type":3,
                                      "regex":"^(?!\\s)(.(?!\\s$)){1,128}$",
                                      "min_date":"",
                                      "max_date":"",
                                      "default_date":"",
                                      "date_option":0,
                                      "min_num":0,
                                      "max_num":0,
                                      "min_len":0,
                                      "max_len":0
                                  },
                                  "option_all_type":0,
                                  "keyboard_type":0
                              },
                              "default":0,
                              "options":[

                              ],
                              "version":0,
                              "content":"a",
                              "operation":[

                              ],
                              "other_info_snapshot_no":"local_first_name"
                          },
                          {
                              "id":10,
                              "field_key":"local_last_name",
                              "name":"Last name",
                              "hint":"Please enter",
                              "hover":"",
                              "id_type":0,
                              "is_group":false,
                              "style":{
                                  "type":0,
                                  "required":1,
                                  "match_rule":{
                                      "type":3,
                                      "regex":"^(?!\\s)(.(?!\\s$)){1,128}$",
                                      "min_date":"",
                                      "max_date":"",
                                      "default_date":"",
                                      "date_option":0,
                                      "min_num":0,
                                      "max_num":0,
                                      "min_len":0,
                                      "max_len":0
                                  },
                                  "option_all_type":0,
                                  "keyboard_type":0
                              },
                              "default":0,
                              "options":[

                              ],
                              "version":0,
                              "content":"a",
                              "operation":[

                              ],
                              "other_info_snapshot_no":"local_last_name"
                          },
                          {
                              "id":3,
                              "field_key":"mobile",
                              "name":"Phone number",
                              "hint":"Please enter",
                              "hover":"",
                              "id_type":0,
                              "is_group":false,
                              "style":{
                                  "type":8,
                                  "required":1,
                                  "verify_state":1,
                                  "match_rule":{
                                      "type":3,
                                      "regex":"^[0-9]{6,17}$",
                                      "min_date":"",
                                      "max_date":"",
                                      "default_date":"",
                                      "date_option":0,
                                      "min_num":0,
                                      "max_num":0,
                                      "min_len":0,
                                      "max_len":0
                                  },
                                  "option_all_type":1,
                                  "keyboard_type":2
                              },
                              "default":0,
                              "options":[

                              ],
                              "version":0,
                              "content":"***********",
                              "operation":[
                                  {
                                      "id":0,
                                      "field_key":"CN",
                                      "name":"",
                                      "hover":"",
                                      "id_type":0,
                                      "is_group":false,
                                      "style":null,
                                      "default":0,
                                      "options":[

                                      ],
                                      "version":0,
                                      "content":"",
                                      "operation":[

                                      ],
                                      "other_info_snapshot_no":"CN"
                                  }
                              ],
                              "other_info_snapshot_no":"mobile"
                          },
                          {
                              "id":5,
                              "field_key":"email",
                              "name":"Email (for updates on your booking)",
                              "hint":"Please enter",
                              "hover":"",
                              "id_type":0,
                              "is_group":false,
                              "style":{
                                  "type":0,
                                  "required":1,
                                  "match_rule":{
                                      "type":3,
                                      "regex":"^\\s*\\w+(?:\\.{0,1}[\\w-]+)*@[a-zA-Z0-9]+(?:[-.][a-zA-Z0-9]+)*\\.[a-zA-Z]+\\s*$",
                                      "min_date":"",
                                      "max_date":"",
                                      "default_date":"",
                                      "date_option":0,
                                      "min_num":0,
                                      "max_num":0,
                                      "min_len":0,
                                      "max_len":0
                                  },
                                  "option_all_type":0,
                                  "keyboard_type":3
                              },
                              "default":0,
                              "options":[

                              ],
                              "version":0,
                              "content":"<EMAIL>",
                              "operation":[

                              ],
                              "other_info_snapshot_no":"email"
                          }
                      ],
                      "custom_info":{
                          "traveler_is_show":true,
                          "traveler_id_use":0,
                          "address_id_use":0
                      },
                      "bottom_info":{
                          "is_checkbox_selected":true,
                          "save_content":"Save info for next time",
                          "update_content":"Update booking info in your account"
                      },
                      "extra_info":{

                      }
                  }
              ],
              "extra_info":null
          }
      ],
      "traveller_info":{
          "family_name":"a",
          "first_name":"a",
          "mobile":"86-***********",
          "mobile_country_code":"",
          "title":"MR",
          "travel_country":"CN",
          "traveller_email":"<EMAIL>",
          "traveller_id":0,
          "sync_traveler_info":false,
          "contact_person_en_name":false
      },
      "top_promotion_info":null,
      "tips":"Please use a Discover NSW voucher or a Parents voucher to complete this booking",
      "nsw_info":{
          "show_entry":true,
          "redeem_desc_url":"https://res.klook.com/image/upload/v1615947965/nsw_payment_how_to_use_wqz2ya.jpg"
      },
      "is_init_insurance_module":false
  },
  "success":true
}
const nswRedeemError = {
    "error":{
        "code":"50301",
        "message":"Voucher code could not be found for the specified voucher type"
    },
    "result":{

    },
    "success":false
}
const nswDialogRedeem = {
    "error":{
        "code":"",
        "message":""
    },
    "result":{
        "coupon_code":"KLKNSWPSCQJPW4R",
        "dialog": {
            content: '确定使用优惠券吗',
            cancel: 'cancel',
            continue: 'ok'
        }
    },
    "success":true
}

export { nswRedeem, nswSettlement, nswRedeemError, nswDialogRedeem};
