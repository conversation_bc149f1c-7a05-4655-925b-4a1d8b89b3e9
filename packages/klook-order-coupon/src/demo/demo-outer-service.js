import Cookies from "../utils/cookies.js"
import axios from "axios";
const defaultSettlementApi =
  "https://t64.fat.klook.io/v2/usrcsrv/order/settlement";

// gen dummy data
const qs = new URLSearchParams(location.search);
window.dummyBrowserInfo = {
  language: qs.has("lang") ? qs.get("lang") : "zh-CN",
  currency: qs.has("currency") ? qs.get("currency") : "CNY",
  platform: "desktop",
  shoppingcart_guid: qs.has("shoppingcart_guid")
    ? qs.get("shoppingcart_guid")
    : "",
  shoppingcart_id: qs.has("shoppingcart_id")
    ? qs.get("shoppingcart_id")
    : "",
  settlement_type: qs.has("settlement_type")
    ? parseInt(qs.get("settlement_type"))
    : 0,
  version: 5.3,
};
window.dummyFirstSettlementParams = {
  currency: window.dummyBrowserInfo.currency,
  page_first_request: true,
  settlement_type: window.dummyBrowserInfo.settlement_type,
  shoppingcart_guid: window.dummyBrowserInfo.shoppingcart_guid,
  time_zone_off_set: 28800,
  user_info: { mobile: "86-15200345369", region_code: "86" },
};

window.dummySettlementParams = {
  currency: window.dummyBrowserInfo.currency,
  page_first_request: false,
  price_info: {
    ins_upgraded_guid: [],
    settlement_discount: {
      giftcard_guid: "",
      last_use_coupon_discount: "0.00",
      last_use_credit: 0,
      last_use_giftcard_amount: "0",
      payment_gateway: [],
      use_credit: 0,
    },
  },
  settlement_type: window.dummyBrowserInfo.settlement_type,
  shoppingcart_guid: window.dummyBrowserInfo.shoppingcart_guid,
  time_zone_off_set: 28800,
  user_info: { mobile: "86-15200345369", region_code: "86" },
};
const dummySettlementParams = window.dummySettlementParams
const dummyFirstSettlementParams = window.dummyFirstSettlementParams
const token = Cookies.getItem("_pt");

const defaultHeaders = {
  "accept-language": window.dummyBrowserInfo.language,
  currency: window.dummyBrowserInfo.currency,
  "x-platform": window.dummyBrowserInfo.platform,
  "X-Klook-Host": location.host,
  version: window.dummyBrowserInfo.version,
  token,
}

async function outterFirstSettlementMethod() {
  const url = defaultSettlementApi;

  const params = JSON.parse(JSON.stringify(dummyFirstSettlementParams))

  try {
    const {
      data: { success, error, result },
    } = await axios.post(url, params, {
      headers: defaultHeaders
    });
    if (success) return Promise.resolve(result);
    // return Promise.resolve(defaultRes.result)
    if (error) {
      const errorObj = {
        ...error,
        errorComponent: "order-coupon",
        errorRequest: url,
      };
      return Promise.reject(errorObj);
    }
  } catch (error) {
    throw new Error(error);
  }
}

async function outterSettlementMethod(couponInfo) {
  const url = defaultSettlementApi;
  // 获取coupon
  const { chosenSrvCode, chosenCoupon } = couponInfo
  const combineCouponCode = [chosenSrvCode, chosenCoupon].filter(v => v).join(',')

  const params = JSON.parse(JSON.stringify(dummySettlementParams))
  if(typeof couponInfo !== undefined) {
    params.price_info.settlement_discount.coupon_code = combineCouponCode
  }

  try {
    const {
      data: { success, error, result },
    } = await axios.post(url, params, {
      headers: defaultHeaders
    });
    if (success) return Promise.resolve(result);
    // return Promise.resolve(defaultRes.result)
    if (error) {
      const errorObj = {
        ...error,
        errorComponent: "order-coupon",
        errorRequest: url,
      };
      return Promise.reject(errorObj);
    }
  } catch (error) {
    throw new Error(error);
  }
}

export { outterFirstSettlementMethod, outterSettlementMethod };
