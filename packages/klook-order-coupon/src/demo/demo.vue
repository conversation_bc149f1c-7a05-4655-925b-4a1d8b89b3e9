<template>
  <div>
    <div class="demo-wrapper" data-spm-page="orderPage">
      <div class="content mkt-wrapper">
        <!-- martech 内置服务配置方式 -->
        <!-- <klk-order-coupon
          ref="klookOrderCoupon"
          :use-customized-service="couponInfo.isMobile ? 'mobile' : 'desktop'"
          :platform="couponInfo.platform"
          :currency="couponInfo.currency"
          :language="couponInfo.language"
          :shoppingcart-guid="couponInfo.shoppingcartGuid"
          :shoppingcart-id="couponInfo.shoppingcartId"
          :settlement-type="couponInfo.settlementType"
          :tickets="couponInfo.tickets"
          :srv-info="srvInfo"
          @change-collapse="handleChangeCollapse"
        >
          <div slot="init-popover">this is custom from this is custom from this is custom
          from external this is custom from external this is custom from external</div>
        </klk-order-coupon> -->

        <!--common -->
        <!-- 
          :chosenCoupon="chosenCoupon"
          :discountAmount="discountAmount"
           -->
        <klk-order-coupon
          ref="klookOrderCoupon"
          :isNewStyle="true"
          :disabled="disabled"
          :platform="couponInfo.isMobile ? 'mobile' : 'desktop'"
          :couponType="isNsw ? 'nsw' : 'common'"
          :currency="currency"
          :pageType="'settlement'"
          :couponInfo="couponMsg"
          :bestCouponCode="bestCouponCode"
          :getBasicCouponList="getCouponList"
          :redeemBasicCoupon="redeemCoupon"
          :visiblePaymentCouponEntry="true"
          @confirmUse="confirmUse"
          @changeCoupon="changeCoupon"
          @change-collapse="handleChangeCollapse"
          @paymentCouponListChange="paymentCouponListChange"
        >
          <!-- <div slot="init-popover">
            this is custom from this is custom from this is custom from external
            this is custom from external this is custom from external
          </div> -->
        </klk-order-coupon>
        <!-- nsw -->
        <!-- <klk-order-coupon
          :couponType="'nsw'"
          :platform="couponInfo.isMobile ? 'mobile' : 'desktop'"
          :currency="'CNY'"
          :disabled="disabled"
          :priceInfo="priceInfo"
          :redeemNswCoupon="redeemNswCoupon"
          :nswValidateError="nswValidateError"
          @nswRedeemSuccess="nswRedeemSuccess"
        >
        </klk-order-coupon>
         -->
        <!-- cashRegister 
        <klk-order-coupon
          ref="klookOrderCoupon"
          :disabled="disabled"
          :platform="'desktop'"
          :pageType="'cashRegister'"
          :couponInfo="couponMsg"
          :currency="'CNY'"
          :getBasicCouponList="getCouponList"
          :redeemBasicCoupon="redeemCoupon"
          @changeCoupon="changeCoupon"
          @confirmUse="confirmUse"
          @tabsChange="tabsChange"
        >
          <div slot="init-popover">
            this is custom from this is custom from this is custom from external
            this is custom from external this is custom from external
          </div>
        </klk-order-coupon>-->
      </div>
      <klk-coupon-form class="demo-form">
        <klk-coupon-form-item>
          <klk-coupon-checkbox v-model="couponInfo.isMobile">
            mweb
          </klk-coupon-checkbox>
        </klk-coupon-form-item>
        <klk-coupon-form-item>
          <klk-coupon-checkbox v-model="isNsw">
           nsw
          </klk-coupon-checkbox>
        </klk-coupon-form-item>
        <p>备注</p>
        <p @click="chosenCoupons()">
          可以通过修改页面的参数，比如增加lang=zh-CN 来改变多语言
        </p>
        <p>事例页面参数</p>
        <p>
          settlement_type=1&shoppingcart_id=2297461&shoppingcart_guid=0d1b0bf9-aceb-4382-6a51-7d872cc206cd&lang=en-SG
        </p>
      </klk-coupon-form>
    </div>
  </div>
</template>

<script>
import Vue from "vue";
import CouponComponent from "../index.js";
import { outterSettlementMethod } from "../demo/demo-outer-service";
import { 
  getCouponList, 
  redeemCoupon ,
  settlementCoupon,
  redeemNswCoupon , 
  nswSettlementCoupon
} from "../demo/demo-basic-service";
Vue.use(CouponComponent);
export default {
  name: 'Demo',
  props: {},
  data() {
    return {
      couponInfo: {
        isMobile: false,
        platform: "desktop",
        currency: "SGD",
        language: "zh-CN",
        useCustomizedService: false,
        shoppingcartGuid: "0d1b0bf9-aceb-4382-6a51-7d872cc206cd",
        shoppingcartId: "2297461",
        settlementType: 1,
        tickets: [
          {
            activity_id: 128,
            package_id: 3823,
            participation_date: "2021-10-15T00:00:00+08:00",
            schedule_id: 16356384003823,
            shopping_cart_guid: "0d1b0bf9-aceb-4382-6a51-7d872cc206cd",
            template_id: 1,
            uen_id: "200804627C",
            venue_id: "MA200804627C",
            units: [
              {
                count: 1,
                days: 1,
                is_minor: false,
                market_price: "30.00",
                promotion_event_ids: null,
                selling_price: "20.00",
                sku_id: ************
              }
            ]
          }
        ]
      },
      srvInfo: {
        isSrv: true,
        // srv混合普通订单
        isSrvMixed: false,
        // 如你有资格享受儿童 / 青年票优惠，则使用 “重新探索新加坡”消费券时每张儿童 / 青年门票将自动扣除SGD10。请参考此额度选择消费券使用数量。
        hasChild: false,
        // srv订单详情,兑换srv需要的参数
        skuList: [
          {
            is_minor: false,
            participation_date: "2021-10-31T00:00:00+08:00",
            sku_id: ************,
            uen_id: "200804627C",
            venue_id: "MA200804627C"
          }
        ]
      },
      currency: 'CNY',
      // common
      chosenCoupon: {},
      // chosenCoupon: {common_coupon: '7C6WCC37'},
      // discountAmount: '100',
      bestCouponCode: 'SZNB3HU4',
      disabled: false,
      isNsw: false, // false: common, true: nsw
      pageType: 'settlement',
      couponMsg: null,
      // nsw
      priceInfo: {},
      nswValidateError: false
    }
  },
  mounted(){
    setTimeout(() => {
      this.couponMsg =  {
        "coupon_code": "9JWNTJ47",
        "coupon_discount": "34.1",
        "message": "",
        "message_code": "",
        payment_restrict_tips: 'test text',
        "payment_restrict_desc": "- 已选择1个优惠码并抵扣订单金额 \n - 已选择1项支付优惠（立减HK$238.9），请选择适用的付款方式，再次抵扣订单金额",
        "srv_only": null,
        "details": [
          {
          "code": "9JWNTJ47",
          "discount": "34.1",
          "finance_type": "payment_coupon",
          "batch_id": 452443,
          "is_srv_only": false
          }
        ]
      }
    }, 1000);
  },
  methods: {
    handleChangeCollapse(val) {
      console.log('handleChangeCollapse', val);
    },
    outterSettlementMethod,
    getCouponList,
    redeemCoupon,
    redeemNswCoupon,
    async changeCoupon(e){
      console.log('changCoupon-demo', e)
      // const obj = await settlementCoupon()
      if (e){
      this.couponMsg = {
        "coupon_code": "",
        "coupon_discount": "0.0",
        "payment_restrict_desc": "",
        payment_restrict_tips: 'test text',
        "message": "优惠券抵扣的金额小于当前货币的最小单元，无法使用。",
        "message_code": "0271B90003",
        "coupon_type": 0,
        "details": null
      }
      }
      // console.log('settlementCoupon', obj)

      // this.chosenCoupon = e
      // this.discountAmount = '111'
    },
    confirmUse(e){
      console.log('confirmUse-demo', e)
    },
    paymentCouponListChange(e){
      console.log('paymentCouponListChange', e)
    },
    chosenCoupons(){
      this.currency = 'HKD'
      this.couponMsg = null
      // this.$refs.klookOrderCoupon.fetchCouponList()
      // this.couponType = 'nsw'
      // this.nswValidateError = !this.nswValidateError
    },
    confirmUse(e){
      console.log(e, 'confirmUse')
    },
    // nsw
    async nswRedeemSuccess(e){
      console.log('nswRedeemSuccess', e)
      const obj = await nswSettlementCoupon()
      this.priceInfo = obj.price_info
    },
    tabsChange(e){
      console.log('tab chang:', typeof e)
    }
  },
  watch: {
  }
};
</script>

<style lang="scss" scoped>
// .vue-markdown .markdown-body {
// }
.demo-wrapper {
  display: flex;
  gap: 16px;
  .demo-form {
    flex: initial;
    width: 250px;
    overflow: hidden;
    .klk-form-item {
      flex-direction: column;
      align-items: start;
    }
    ::v-deep .klk-form-item-content {
      width: 250px;
      min-width: 250px;
    }
  }
  .mkt-wrapper {
    flex: 1;
  }
}
</style>
