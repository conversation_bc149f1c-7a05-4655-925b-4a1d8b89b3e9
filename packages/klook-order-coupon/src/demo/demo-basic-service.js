import Cookies from "../utils/cookies.js";
import { get_f2b_lang_map } from "../utils/common-utils";
import { dummyList,resRedeem,settlement,resRedeemAfter } from "./mock.js";
import { nswRedeem, nswSettlement, nswRedeemError,nswDialogRedeem} from "./nswMock.js";
// const axios = require("axios");
import axios from "axios";
// gen dummy data
const qs = new URLSearchParams(location.search);

window.dummyBrowserInfo = {
  language: qs.has("lang") ? qs.get("lang") : "zh-CN",
  currency: qs.has("currency") ? qs.get("currency") : "CNY",
  platform: "desktop",
  shoppingcart_guid: qs.has("shoppingcart_guid")
    ? qs.get("shoppingcart_guid")
    : "",
  shoppingcart_id: qs.has("shoppingcart_id")
    ? qs.get("shoppingcart_id")
    : "",
  settlement_type: qs.has("settlement_type")
    ? parseInt(qs.get("settlement_type"))
    : 0,
  version: 5.3,
};
window.couponParams = {
  language: window.dummyBrowserInfo.language,
  currency: window.dummyBrowserInfo.currency,
  platform: window.dummyBrowserInfo.platform,

  amount: "13.00",
  coupon_key: "",
  phone_number: "86-15200345369",
  select_coupon_code_list: [],
  settlement_type: 1,
  shoppingcart_guid_list: [window.dummyBrowserInfo.shoppingcart_guid],
  supported_method_list: [],
  tickets: [
    {
      activity_id: 128,
      package_id: 72859,
      participation_date: "2021-08-31T00:00:00+08:00",
      schedule_id: 163036800072859,
      shopping_cart_guid: "43bd58c2-73a4-4dda-6214-9d8ea0960284",
      template_id: 1,
      uen_id: "200804627C",
      units: [
        {
          count: 1,
          days: 1,
          is_minor: false,
          market_price: "42.45",
          promotion_event_ids: null,
          selling_price: "13.00",
          sku_id: ************,
        },
      ],
      venue_id: "MA200804627C",
    },
  ],
};
const token = Cookies.getItem("_pt");

const defaultHeaders = {
  "accept-language": window.dummyBrowserInfo.language,
  currency: window.dummyBrowserInfo.currency,
  "x-platform": window.dummyBrowserInfo.platform,
  "X-Klook-Host": location.host,
  version: window.dummyBrowserInfo.version,
  token,
}
function genHeaders(vueInstance) {
  return defaultHeaders
}

let count = 0
const getCouponList = async function(params, vueInstance) {
  // const {
  //   env: {
  //     language,
  //     currency,
  //     platform,
  //     host,
  //     settlementType,
  //     shoppingcartGuid,
  //     timeZoneOffSet,
  //     tickets
  //   }
  // } = vueInstance;
  const { select_coupon_code_list } = params;
  // resRedeemAfter.result
  return new Promise((resolve, reject)=> {
    // let msg = count < 1 ? dummyList.result : resRedeemAfter.result
    // count++
    setTimeout(() => {
      resolve(resRedeemAfter.result)
    }, 2000);
  })
  const url = `https://t64.fat.klook.io/v2/couponapisrv/canonical/order/coupons`;
  const headers = genHeaders();
  const axiosParams = {
    ...window.couponParams,
    select_coupon_code_list,
  };

  try {
    const {
      data: { success, error, result }
    } = await axios.post(url, axiosParams, {
      headers
    });
    if (success) return Promise.resolve(result);
    if (error) {
      const errorObj = {
        ...error,
        errorComponent: "mkt-order-coupon",
        errorRequest: url
      };
      return Promise.reject(errorObj);
    }
  } catch (error) {
    throw new Error(error);
  }
}

const settlementCoupon = async function() {
  return new Promise((resolve, reject)=> {
    setTimeout(() => {
      // resolve(nswRedeem)
      // resolve(nswRedeemError)
      resolve(settlement.result)
      
    }, 1000);
  })
}

const redeemCoupon = async function (params, vueInstance) {
  // const {
  //   env: {
  //     language,
  //     currency,
  //     platform,
  //     host,
  //     settlementType,
  //     shoppingcartGuid,
  //     timeZoneOffSet,
  //     tickets
  //   }
  // } = vueInstance;
  const { code, select_coupon_code_list } = params;
  return new Promise((resolve, reject)=> {
    setTimeout(() => {
      reject(resRedeem.result)
      // resolve(resRedeem.result)
    }, 1000);
  })

  const url = `${host}/v2/couponapisrv/canonical/order/redeem`;
  const headers = genHeaders();
  const axiosParams = window.couponParams

  try {
    const {
      data: { success, error, result }
    } = await axios.post(url, axiosParams, {
      headers: headers
    });
    if (success) return Promise.resolve(result);
    if (error) {
      const errorObj = {
        ...error,
        errorComponent: "mkt-order-coupon",
        errorRequest: url
      };
      return Promise.reject(errorObj);
    }
  } catch (error) {
    throw new Error(error);
  }
}
const redeemNswCoupon = async ()=>{
  return new Promise((resolve, reject)=> {
    setTimeout(() => {
      // resolve(nswRedeem)
      // resolve(nswRedeemError)
      resolve(nswDialogRedeem)
      
    }, 1000);
  })
}
const nswSettlementCoupon = async ()=>{
  return new Promise((resolve, reject)=> {
    setTimeout(() => {
      resolve(nswSettlement.result)
    }, 1000);
  })
}
export {
  getCouponList,
  redeemCoupon,
  redeemNswCoupon,
  nswSettlementCoupon,
  settlementCoupon
}
