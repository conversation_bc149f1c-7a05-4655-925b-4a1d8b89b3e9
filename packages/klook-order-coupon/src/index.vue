<template>
  <div>
    <basic-coupon
      ref="basicCoupon"
      v-bind="$attrs"
      :disabled="disabled"
      :is-mobile="isMobile"
      :couponType="couponType"
      :priceInfo="priceInfo"
      :nswValidateError="nswValidateError"
      :get-basic-coupon-list="getBasicCouponList"
      :redeem-basic-coupon="redeemBasicCoupon"
      :redeem-nsw-coupon="redeemNswCoupon"
      :spm-ext="spmExt"
      :is-merged-payment-page="isMergedPaymentPage"
      :is-new-style="isNewStyle"
      :visiblePaymentCouponEntry="visiblePaymentCouponEntry"
      :visibleBestCouponSuggestNotice="visibleBestCouponSuggestNotice"
      :bookingId="bookingId"
      @coupon-list-change="couponListChange"
      @common-coupon-list-change="commonCouponListChange"
      @click-use="clickApply"
      @best-coupon-change="bestCouponChange"
      @use-coupon="chooseCoupon"
      @change-collapse="event => $emit('change-collapse', event)"
      @nsw-redeem-success="nswRedeemSuccess"
      @tabs-change="tabsChange"
      @payment-coupon-list-change="paymentCouponListChange"
      @click-back="clickBack"
    >
      <div slot="init-popover">
        <slot name="init-popover"></slot>
      </div>
    </basic-coupon>
  </div>
</template>
<script>
import BasicCoupon from "./basic-coupon/basic-entry.vue";
import { getCurrencySymbol } from "./utils/common-utils";
import BasicMixin from "./mixins/basic-mixin";

export default {
  name: "klk-order-coupon",

  mixins: [BasicMixin],

  components: {
    BasicCoupon,
  },

  props: {
    spmExt: {
      type: Object,
      default: () => ({})
    },
    isMergedPaymentPage: {
      type: Boolean,
      default: false
    },
    isNewStyle: {
      type: Boolean,
      default: false
    },
    platform: {
      type: String,
      require: true,
      default: "desktop",
      validator: value => {
        return ["mobile", "desktop"].includes(value);
      }
    },
    disabled: {
      type: Boolean,
      default: false
    },
    currency:{
      type: String,
      require: true,
      default: ''
    },
    // 优惠券类型
    couponType: {
      type: String,
      require: true,
      default: 'common',
      validator: value => {
        return ["nsw", "common"].includes(value);
      }
    },
    bestCouponCode: {
      type: String,
      default: ''
    },
    // 返回基本优惠券列表函数
    getBasicCouponList: {
      type: Function,
      require: true
    },
    // common优惠券兑换接口
    redeemBasicCoupon: {
      require: true,
      type: Function
    },
    // nsw优惠券兑换接口
    redeemNswCoupon: {
      require: true,
      type: Function
    },
    // 页面类型 收银台｜结算页
    pageType: {
      type: String,
      require: true,
      default: 'settlement',
      validator: value => {
        return ["settlement", "cashRegister"].includes(value);
      }
    },
    // page type = cashRegister 收银台 booking id
    bookingId: {
      type: String,
      default: ''
    },
    couponInfo: {
      type: Object,
      default: ()=>{
        return null
      }
    },
    // nsw 
    priceInfo: {
      type: Object,
      default: ()=>{
        return {}
      }
    },
    nswValidateError: {
      type: Boolean,
      default: false
    },
    // 是否展示最佳优惠卷提示
    visibleBestCouponSuggestNotice:{
        type: Boolean,
        default: true
    },
    // 隐藏支付优惠券入口
    visiblePaymentCouponEntry: {
      type: Boolean,
      default: true
    }
  },

  data() {
    return {
      env: {
        platform: ''
      },
      couponData: {
        payment_restrict_desc: '', // pc 和 mwebrukou 选中优惠券的文案显示,
        payment_restrict_tips: '', //There are designated payment methods when using payment coupons, here we emphasize the payment methods supported by the payment coupons
        // 推荐优惠券
        bestCouponCode: '',
        // 当前选中的优惠券
        chosenCoupon: {
          common_coupon: {},
          payment_coupon: {}
        },
        // 折扣金额
        discountAmount: '',
        // 是否显示tip slot
        showPopTip: false,
        // 是否显示优惠券提示 all 展示两类优惠券提示 payment 展示支付类优惠券提示
        selectedCouponTip: '',
        // 货币标识
        currencySymbol: '',
        // 页面类型
        pageType: '',
      }
    };
  },

  computed: {
    isMobile() {
      return this.platform === "mobile"
        ? true
        : this.platform === "desktop"
        ? false
        : false;
    },
  },
  provide() {
    return {
      couponData: this.couponData,
      env: this.env
    };
  },

  async mounted() {
    await this.init();
  },

  methods: {
    async init() {
      // 是否定义了popover
      this._provided.couponData.showPopTip = !!this.$slots["init-popover"];
      // 获取优惠券列表 只有common才有列表
      this.couponType === 'common' ? this.fetchCouponList() : '';
    },
    /**
     * @desc 选择优惠券
     * @param Object e 优惠券信息
     * */ 
    chooseCoupon(e){
        this._provided.couponData.chosenCoupon = {...e}
        this.computedSelectedCouponTip()
        this.changeCoupon()
        if(e?.common_coupon?.code){
          this._provided.couponData.discountAmount = e.common_coupon?.coupon_use_info?.coupon_discount_dec || ''
        }
    },
    bestCouponChange(e){
      this._provided.couponData.bestCouponCode = e
    },
    // 优惠券提示
    computedSelectedCouponTip(){
      if (this._provided.couponData?.chosenCoupon?.payment_coupon?.code){
        this._provided.couponData.selectedCouponTip = 'payment'
        if (this._provided.couponData?.chosenCoupon?.common_coupon?.code) this._provided.couponData.selectedCouponTip = 'all'
      } else {
        this._provided.couponData.selectedCouponTip = null
      }
    },
    getChosenCouponList(){
      const chosenCoupon = this._provided?.couponData?.chosenCoupon || {}
      let couponList = []
      for (const key in chosenCoupon) {
        chosenCoupon[key]?.code ? couponList.push({finance_type: key, code: chosenCoupon[key]?.code}) : null
      }
      return couponList.length ? couponList : null
    },
    fetchCouponList(){
      this.$refs.basicCoupon && this.$refs.basicCoupon.fetchBasicCoupons()
    },
    couponListChange(basicCouponList){
      // 列表改变刷新
      let common_coupon = this.couponData.chosenCoupon?.common_coupon
      let payment_coupon = this.couponData.chosenCoupon?.payment_coupon
      basicCouponList?.usable.forEach(v=>{
        common_coupon?.code == v.code ? this.couponData.chosenCoupon.common_coupon = v : ''
      })
      basicCouponList?.usable_payment_coupon.forEach(v=>{
        payment_coupon?.code == v.code ? this.couponData.chosenCoupon.payment_coupon = v : ''
      })
    },
    commonCouponListChange(usable){
      this.$emit('commonCouponListChange', usable)
    },
    paymentCouponListChange(usable_payment_coupon){
      const isEmpty = !!usable_payment_coupon.length
      this.$emit('paymentCouponListChange', isEmpty)
    },
    // emit用户
    tabsChange(e){
      this.$emit('tabsChange', e)
    },
    clickApply(){
      this.$emit('confirmUse', this.getChosenCouponList())
    },
    changeCoupon(){
      // 收银台只有支付类优惠券
      if (this.pageType === 'cashRegister') {
        const payment_coupon = this._provided?.couponData?.chosenCoupon?.payment_coupon
        this.$emit(
          'changeCoupon',
          payment_coupon?.code
            ? payment_coupon
            : null
        )
      } else {
        this.$emit('changeCoupon', this.getChosenCouponList())
      }
    },
    nswRedeemSuccess(couponCode){
      this.$emit('nswRedeemSuccess', couponCode)
    },
    clickBack(){
      this.$emit('mobileClickBack')
    }
    /**
     * @description: 对外暴露使用的API
     * @return void
     */
    // refreshCouponComponent() {
    //   this.init();
    // },
  },
  watch: {
    currency: {
      handler(newVal, oldVal) {
        this.couponData.currencySymbol = getCurrencySymbol(newVal);
      },
      immediate: true
    },
    pageType: {
      handler(newVal, oldVal) {
        this.couponData.pageType = newVal;
      },
      immediate: true
    },
    couponInfo: {
      handler(newVal, oldVal) {
        let initChosenCoupon = {
          common_coupon: null,
          payment_coupon: null
        }
        if (newVal && newVal.details) {
          newVal.details?.forEach(v => {
            initChosenCoupon[v.finance_type] = v || null
          });
        }
        this.couponData.chosenCoupon = initChosenCoupon || {};
        // 每次传入数据 补全chosenCoupon信息 防止请求没返回情况
        let basicCoupon = this.$refs.basicCoupon 
        if (basicCoupon?.basicCoupons?.usable) {
          let { usable, usable_payment_coupon } = basicCoupon?.basicCoupons
          let common_coupon = this.couponData.chosenCoupon?.common_coupon || {}
          let payment_coupon = this.couponData.chosenCoupon?.payment_coupon || {}
          usable?.forEach(v=>{
            common_coupon?.code == v.code ? this.couponData.chosenCoupon.common_coupon = v : ''
          })
          usable_payment_coupon?.forEach(v=>{
            payment_coupon?.code == v.code ? this.couponData.chosenCoupon.payment_coupon = v : ''
          })
        }
        this.couponData.payment_restrict_desc = newVal && (newVal.payment_restrict_desc || '')
        this.couponData.payment_restrict_tips = newVal && (newVal.payment_restrict_tips || '')
      
        this.couponData.discountAmount = newVal && (newVal.coupon_discount || 0)
        let noRefreshList = ['4001', '4005', '4006', '5005'] // settlement 接口不需要刷新列表的报错
        if (newVal && newVal.message_code && !noRefreshList.includes(newVal.message_code)){
          this.fetchCouponList()
          this.platform === 'mobile' ? this.clickApply() : this.changeCoupon() // 优惠券报错时 需要选择优惠券的回调
          this.$toast({
            message: newVal.message,
            icon: "icon_feedback_failure_fill",
            iconColor: "red",
            duration: 3000
          });
        }
      },
      deep: true,
      immediate: true
    },
    bestCouponCode: {
      handler(newVal, oldVal) {
        this.couponData.bestCouponCode = newVal;
      },
      immediate: true
    }
  }
};
</script>
<style lang="scss">
.mkt-basic-container * {
  text-align: left;
}
</style>
