import Cookies from './utils/cookies.js';
import { get_f2b_lang_map } from './utils/common-utils';
import axios from 'axios';
function genHeaders(vueInstance) {
  const token = Cookies.getItem('_pt') || '';
  const {
    env: {
      language,
      currency,
      platform,
      host
    } 
  } = vueInstance;
  return {
    'accept-language': get_f2b_lang_map(language),
    currency: currency,
    'x-platform': platform,
    'X-Klook-Host': host,
    version: 5.3,
    token,
  };
}

export async function redeemSrv(params, vueInstance) {
  const {
    env: {
      host
    } 
  } = vueInstance;
  const {
    skuList,
    srvCode
  } = params;
  const url = `${host}/v1/couponapisrv/srv/redeem`;
  const axiosParams = {
    sku_list: skuList,
    srv_code: srvCode
  };

  const headers = genHeaders(vueInstance);
  try {
    const {
      data,
    } = await axios.post(url, axiosParams, {
      headers,
    });
    if (data.success) return Promise.resolve(data);
    // return Promise.resolve(defaultRes.result)
    if (data.error) {
      const errorObj = {
        ...data.error,
        errorComponent: 'mkt-coupon',
        errorRequest: url,
      };
      return Promise.reject(errorObj);
    }
  } catch (error) {
    throw new Error(error);
  }
}
