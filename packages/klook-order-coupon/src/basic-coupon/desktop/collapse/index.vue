<template>
  <div class="collapse-container-gray">
    <klk-coupon-collapse
      size="small"
      :value="active"
      @change="change"
    >
      <klk-coupon-collapse-item
        class="mkt-collapse-item"
        name="order-coupon"
      >
        <div slot="title">
          <slot name="title" />
        </div>
        <slot name="content" />
      </klk-coupon-collapse-item>
    </klk-coupon-collapse>
  </div>
</template>

<script>
export default {
  name: 'MktBasicCouponCollapse',

  components: {},

  props: {
    isMobile: {
      type: Boolean,
      default: false,
    },
    loading: {
      type: Boolean
    },
    active: {
      type: Array,
      default: ()=>[]
    }
  },

  data() {
    return {
    };
  },
  methods: {
    change(arr) {
      const res = !!arr.length && arr[0] === 'order-coupon';
      this.$emit('change-collapse', res);
    },
  },
};
</script>

<style lang="scss" scoped>
.klk-collapse-item.mkt-collapse-item {
  background-color: transparent;
  border: none;
  ::v-deep .klk-collapse-item-header-inner {
    margin-top: 12px;
    margin-bottom: 12px;
    margin-right: 0px;
    align-items: center;
  }
  ::v-deep .klk-collapse-item-content-inner {
    padding: 0;
  }
  .sale-amount {
    font-size: 16px;
    line-height: 22px;
    color: #ff5722;
  }
  .recommended {
    margin: 0px 12px;
    font-size: 14px;
    line-height: 20px;
    color: #ff5722;
  }
}
</style>
