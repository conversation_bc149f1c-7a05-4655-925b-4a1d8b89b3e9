<template>
  <div
    class="mkt-coupon-wrapper"
    :data-spm-module="panelModule"
    data-spm-virtual-item="__virtual?trg=manual"
  >
    <div v-if="!isMergedPaymentPage">
      <!-- 选择的优惠组合 -->
      <ul class="select-coupon-result-box">
        <li class="select-coupon-result-item">
          <span class="coupon_label">{{ $t('91903') }}</span>
          <span class="coupon_value_text" :class="platformCouponResultStyle">
            {{ platformCouponResultText }}
          </span>
        </li>
        <li v-show="visiblePaymentCouponEntry" class="select-coupon-result-item payment-item">
          <span class="coupon_label">{{ $t('91904') }}</span>
          <span class="coupon_value_text" :class="paymentCouponResultStyle">
            {{ paymentCouponResultText }}
          </span>
        </li>
      </ul>
      <!-- 选中的优惠券说明 同时选中两类优惠券显示 -->
      <!-- 弃用展示payment_restrict_desc字段，改展示payment_restrict_tips -->
      <div v-if="couponData.payment_restrict_tips" class="summary_error_msg_wrapper">
        <div v-html="couponData.payment_restrict_tips"></div>
      </div>
      <!-- tip -->
      <div class="mkt-poptip" v-show="popoverShow">
        <klk-coupon-icon
          class="mkt-poptip-close"
          type="icon_navigation_close"
          size="18"
          @click="closePopTip"
        />
        <slot name="init-popover"></slot>
      </div>
    </div>
    
    <div v-else class="paylater-entry" @click="mergedPageEntryClick">
      <div class="platform-coupon-info">
        <div class="platform-coupon-title">{{ $t('176630') }}</div>
        <div class="platform-coupon-action">
          <div v-if="loading" v-loading="loading" class="platform-coupon-loading" data-klk-loading-show-loading-bg="false" data-klk-loading-show-overlay="false"></div>
          <klk-atomic-basic-tag v-else-if="combinedCouponResultStyle === 'selected_coupon'" size="medium" :data="promoTagData" />
          <span v-else class="coupon_value_text" :class="combinedCouponResultStyle">
            {{ combinedCouponResultText }}
          </span>
          <IconNext class="arrow-btn" theme="outline" size="20" fill="#212121" />
        </div>
      </div>
      <div v-show="isBestCouponCombo" class="best-combo-applied">{{ $t('173922') }}</div>
    </div>

    <!-- 展开关闭 -->
    <component
      :is="comp"
      v-bind="compAttrs"
      v-on="compListener"
    >
      <div v-if="!isMergedPaymentPage" slot="title">
        <div v-if="!isNewStyle">
          <div v-show="hasUsableCoupons">
            <!-- 没有选择优惠券提示-->
            <span v-show="hasUsableCoupons && !isBestCouponCombo && !hasSelectAllEnableCoupon">
            {{
              $t("73173")
            }}
            </span>
          </div>
          <!-- 没有可用优惠券提示 -->
          <div v-show="!hasUsableCoupons">
            {{ $t("30122") }}
          </div>
          <div v-show="isBestCouponCombo" class="is_applied_best_combo">{{ $t('91894') }}</div>
        </div>
        <div v-else>
          <!-- 没有可用优惠券提示 -->
          <div v-show="!hasUsableCoupons">
            {{ $t("30122") }}
          </div>
          <!-- 没有选择优惠券提示-->
          <div
            class="no_coupon_tip"
            v-show="hasUsableCoupons && !isBestCouponCombo && !hasSelectAllEnableCoupon"
          >
            {{ $t("73173") }}
          </div>
          <!-- 选中的优惠券说明 同时选中两类优惠券显示 -->
          <div v-show="isBestCouponCombo" class="is_applied_best_combo">{{ $t('91894') }}</div>
        </div>
      </div>
      <div :slot="isMergedPaymentPage ? null : 'content'">
        <MaxDiscount
          v-if="isMergedPaymentPage && visibleBestCouponSuggestNotice"
          v-on="$listeners"
          v-bind="$attrs"
          :is-mobile="false"
          :max-discount="maxDiscount"
          :coupon-list="usable"
          :payment-coupon-list="usablePaymentCoupon"
          :chosen-coupon-data="chosenCouponData"
          @combo-change="comboChange"
        />
        <div class="mkt-coupon-content">
          <!-- 优惠券兑换 -->
          <redeem v-on="$listeners" v-bind="$attrs" />
          <!-- 最佳优惠推荐提示 -->
          <Sugguest
            v-show="!isMergedPaymentPage && visibleBestCouponSuggestNotice"
            v-on="$listeners"
            v-bind="$attrs"
            :bestcombo="bestcombo"
            :coupon-list="usable"
            :payment-coupon-list="usablePaymentCoupon"
            :chosen-coupon-data="chosenCouponData"
            @combo-change="comboChange"
          />
          <!-- 优惠券列表 -->
          <div class="mkt-basic-coupon-list-container" v-loading="loading">
            <tab
              v-bind="$attrs"
              v-on="$listeners"
              :available-length="usableLength"
              :unavailable-length="unusable.length"
            >
              <list
                slot="available"
                v-bind="$attrs"
                v-on="$listeners"
                :is-merged-payment-page="isMergedPaymentPage"
                :bestcombo="bestcombo"
                :coupon-list="usable"
                :payment-coupon-list="usablePaymentCoupon"
                :chosen-coupon-data="chosenCouponData"
                @choose-coupon="chooseCoupon"
              />
              <unusableList
                slot="unavailable"
                v-bind="$attrs"
                v-on="$listeners"
                :is-merged-payment-page="isMergedPaymentPage"
                :coupon-list="unusable"
              />
            </tab>
          </div>
        </div>
      </div>

      <div :slot="isMergedPaymentPage ? 'footer' : null" class="paylater-footer">
        <div class="paylater-footer-text">{{ $t('73173') }}</div>
        <klk-coupon-button class="paylater-footer-btn" @click="handleFooterBtnClick">
          <span>{{ $t("73205") }}</span>
        </klk-coupon-button>
      </div>
    </component>
  </div>
</template>

<script>
import Collapse from "./collapse/index.vue";
import List from "../common/new-list/index.vue";
import unusableList from "../common/new-list/unusableList.vue";
import Redeem from "../common/redeem/index.vue";
import Tab from "../common/tab/index.vue";
import Sugguest from "../common/suggest/index.vue";
import BasicMixin from "../../mixins/basic-mixin";
import SuggestMixin from '../../mixins/suggest-mixin'
import MaxDiscount from "../common/max-discount/index.vue";
import { IconNext } from "@klook/klook-icons";
import { KlkAtomicBasicTag } from "@klook/klook-card";
import "@klook/klook-card/dist/esm/index.css";
import {
  formatPriceThousands,
  formatTwoDecimal,
} from "../../utils/common-utils";

export default {
  name: "mkt-basic-coupon-desktop",
  mixins: [BasicMixin, SuggestMixin],

  filters: {
    formatPriceThousands,
    formatTwoDecimal,
  },

  components: {
    Collapse,
    List,
    unusableList,
    Redeem,
    Tab,
    Sugguest,
    MaxDiscount,
    IconNext,
    KlkAtomicBasicTag
  },

  inject: ["couponData"],

  props: {
    isMergedPaymentPage: {
      type: Boolean,
      default: false
    },
    isNewStyle: {
      type: Boolean,
      default: false,
    },
    usable: {
      type: Array,
      default: () => [],
    },
    bestcombo: {
      type: Array,
      default: () => [],
    },
    maxDiscount: {
      type: Object,
      default: () => ({})
    },
    usablePaymentCoupon: {
      type: Array,
      default: [],
    },
    unusable: {
      type: Array,
      default: () => [],
    },
    loading: {
      type: Boolean,
    },
    visibleBestCouponSuggestNotice: {
        type: Boolean,
        default: true
    },
    // 隐藏支付优惠券入口
    visiblePaymentCouponEntry: {
      type: Boolean,
      default: true
    }
  },
  computed: {
    comp() {
      return this.isMergedPaymentPage ? 'klk-coupon-modal' : 'collapse'
    },
    compClassName() {
      if (this.isMergedPaymentPage) {
        return 'mkt-paylater'
      }
      return this.isNewStyle ? 'mkt-collapse-new' : 'mkt-collapse'
    },
    compAttrs() {
      const def = {
        class: this.compClassName,
        loading: this.loading
      }

      if (this.isMergedPaymentPage) {
        return {
          ...def,
          title: this.$t('30125'),
          open: this.expanded,
          width: '55%',
          scrollable: true,
          showDefaultFooter: false,
          closable: true
        }
      }
      return def
    },
    compListener() {
      if (this.isMergedPaymentPage) {
        return {
          close: () => { this.changeCollapse(false) }
        }
      }
      return {
        changeCollapse: (val) => {
          this.changeCollapse(val)
        }
      }
    },
    promoTagData() {
      return {
        type: "platform_promo_code_tag",
        text: this.combinedCouponResultText
      }
    },
    usableLength() {
      return this.usablePaymentCoupon.length + this.usable.length;
    },
    panelModule() {
      const ext = { action: this.expanded ? "collapse" : "expand" };
      return `PromoCodePanel?ext=${JSON.stringify(ext)}`;
    },
    popoverShow() {
      return this.couponData.showPopTip && this.openPopTip;
    },
    hasUsableCoupons() {
      return this.usable.length || this.usablePaymentCoupon.length;
    },
    hasSelectedCoupons() {
      return this.chosenCouponData?.common_coupon?.code || this.chosenCouponData?.payment_coupon?.code
    },
    hasSelectAllEnableCoupon() {
      let platformSelectIfEnable = false;
      let paymentSelectIfEnable = false
      if (this.enableSelectPlatformCoupon.length) {
        if(this.couponData?.chosenCoupon?.common_coupon?.code){
          platformSelectIfEnable = true
        }
      } else {
        platformSelectIfEnable = true
      }

      if (this.enableSelectPaymentCoupon.length) {
        if(this.couponData?.chosenCoupon?.payment_coupon?.code){
          paymentSelectIfEnable = true
        }
      } else {
        paymentSelectIfEnable = true
      }
      return paymentSelectIfEnable && platformSelectIfEnable
    },
    common_coupon() {
      return this.couponData?.chosenCoupon?.common_coupon || {};
    },
    isBestCouponCombo() {
      const bestlist = this.isMergedPaymentPage ? [this.maxDiscount].filter(Boolean) : this.bestcombo;
      const { common_coupon = {}, payment_coupon = {} } = this.chosenCouponData;

      if (bestlist && bestlist.length) {
        const { combo_list = [] } = bestlist[0];
        return combo_list.every((couponItem) => {
          return [common_coupon, payment_coupon].some((selectCodeItem) => {
            return (
              selectCodeItem && couponItem.code == selectCodeItem.code
            );
          });
        });
      }

      return false;
    },
  },
  watch: {
    "couponData.chosenCoupon": {
      handler(newVal, oldVal) {
        this.chosenCouponData = newVal
          ? this.$lodash.cloneDeep(newVal)
          : {
              common_coupon: {},
              payment_coupon: {},
            };
      },
      immediate: true,
      deep: true,
    },
  },

  data() {
    return {
      expanded: false,
      couponData: this.couponData,
      openPopTip: true,
      chosenCouponData: {
        common_coupon: {},
        payment_coupon: {},
      },
    };
  },

  methods: {
    comboChange(comboData){
      const {common_coupon,payment_coupon} = comboData
      const currentCouponData = {
        common_coupon,
        payment_coupon
      }
      // 其实可直接emit，但是为了保险，还是做下处理
      this.chosenCouponData = this.stacking_with_common_coupon(
        common_coupon,
        currentCouponData
      );
      this.chosenCouponData = this.stacking_with_common_coupon(
        payment_coupon,
        currentCouponData
      );
      if (this.isMergedPaymentPage) { return };
      this.$emit("use-coupon", this.chosenCouponData);
    },
    chooseCoupon(e) {
      this.chosenCouponData = this.stacking_with_common_coupon(
        e,
        { ...this.chosenCouponData, [e.finance_type]: e }
      );
      if (this.isMergedPaymentPage) { return };
      this.$emit("use-coupon", this.chosenCouponData);
    },
    handleFooterBtnClick() {
      this.$emit("use-coupon", this.chosenCouponData);
      this.changeCollapse(false);
    },
    mergedPageEntryClick() {
      if (this.loading) { return };
      this.$emit("refresh-list");
      this.changeCollapse(true);
    },
    changeCollapse(value) {
      this.chosenCouponData = this.$lodash.cloneDeep(this.couponData.chosenCoupon);
      this.expanded = value;
      this.$emit("change-collapse", value);
      this.$inhouse.track("action", ".mkt-coupon-wrapper");
    },
    closePopTip() {
      this.openPopTip = false;
    },
  },

  async mounted() {},
};
</script>

<style lang="scss" scoped>
.mkt-coupon-wrapper {
  .select-coupon-result-box {
    list-style: none;
    padding: 0;
    margin: 0 0 10px 0;
    .select-coupon-result-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding-bottom: 12px;
      &:nth-last-of-type(1){
        padding-bottom: 0px;
      }  
    }
    .coupon_label {
      font-size: 16px;
      line-height: 150%;
    }
    .coupon_value_text {
      display: inline-block;
      line-height: 150%;
      font-size: 12px;
    }
    .disabled_select {
      color: #757575;
      font-size: 14px;
    }
    .has_select {
      font-size: 12px;
      font-weight: 500;
      background: #ff5b00;
      border-radius: 6px;
      padding: 0 6px;
      color: #ffffff;
    }
    .selected_coupon {
      font-size: 16px;
      font-weight: 500;
      color: #ff5b00;
    }
    .payment-item .selected_coupon {
      font-size: 12px;
      font-weight: 500;
      background: #F09B0A;
      border-radius: 6px;
      padding: 0 6px;
      color: #ffffff;
    }
  }
  .mkt-collapse {
    padding: 0 12px;
    background-color: #f5f5f5;
    border-radius: 4px;
    ::v-deep .klk-collapse-item-content {
      border-top: 1px solid #e0e0e0;
      padding-top: 16px;
      margin-left: -12px;
      margin-right: -12px;
      padding-left: 12px;
      padding-right: 12px;
    }
  }
  .sale-amount {
    font-size: 16px;
    line-height: 22px;
    color: #ff5722;
    font-weight: 600;
  }
  .recommended {
    margin: 0px 12px;
    font-size: 14px;
    line-height: 20px;
    color: #ff5722;
  }
  ::v-deep .mkt-poptip {
    max-width: 378px;
    min-width: 378px;
    margin-bottom: 18px;
    background: #fff4ed;
    position: relative;
    padding: 8px 36px 8px 12px;
    .mkt-poptip-close {
      cursor: pointer;
      position: absolute;
      top: 8px;
      right: 8px;
    }
    &::after {
      content: "";
      border: 9px solid transparent;
      position: absolute;
      border-top-color: #fff4ed;
      bottom: -18px;
      left: 18px;
    }
  }
  .summary_error_msg_wrapper {
    margin: 10px 0 8px 0;
    padding: 8px;
    background: #FCF3DE;
    font-size: 14px;
    line-height: 150%;
    border-radius: 8px;
  }
  .is_applied_best_combo {
    margin-top: 8px;
    color: #757575;
    font-size: 14px;
    line-height: 150%;
  }
  .summary_error_msg {
    white-space: pre-line;
  }
  // 新版优惠券组件样式
  .mkt-collapse-new {
    padding: 0;
    background-color: #fff;
    ::v-deep .klk-collapse-item-header-inner {
      margin-top: 0px !important;
      margin-bottom: 0px !important;
      line-height: 24px;
      align-items: flex-start !important;
    }
    ::v-deep .klk-collapse-item-content {
      background-color: #f5f5f5;
      padding-top: 16px;
      border-radius: 16px;
      margin-top: 12px;
      margin-bottom: 16px;
      padding-bottom: 4px;
      padding-left: 20px;
      padding-right: 20px;
    }
    ::v-deep .klk-collapse-item-ctrl-btn {
      margin-top: 3px;
      color: #222222;
    }
    .label-box {
      display: flex;
      justify-content: space-between;
      margin-right: -12px;
      .label {
        font-weight: 600;
        font-size: 16px;
        line-height: 150%;
        color: #212121;
      }
      .sale-amount {
        display: flex;
        align-items: flex-start;
        flex-wrap: nowrap;
        color: #ff5b00;
        .currency {
          word-break: break-word;
          min-width: 80px;
          text-align: right;
          padding-right: 6px;
          // display: inline-block;
        }
        span {
          display: inline-block;
        }
      }
    }
    .recommended {
      margin: 0;
      margin-top: 4px;
      font-size: 14px;
      line-height: 20px;
      color: #ff5b00;
    }
    .summary_error_msg_wrapper {
      width: calc(100% + 36px);
      margin: 8px 0 0 0;
      padding: 12px 16px;
      background: #FCF3DE;
      border-radius: 12px;
    }
    .summary_error_msg {
      white-space: pre-line;
    }
    .no_coupon_tip {
      font-weight: 400;
      font-size: 14px;
      line-height: 150%;
      color: #757575;
    }
  }
}

.platform-coupon-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.platform-coupon-title {
  @include font-heading-xs-v2();
}

.platform-coupon-action {
  cursor: pointer;
  display: flex;
  align-items: center;

  .platform-coupon-loading {
    position: relative;
    margin-right: 20px;
  }

  .arrow-btn {
    margin-left: 4px;
  }

  .has_select {
    @include font-caption-m-semibold();
    color: $color-text-reverse;
    background: $color-brand-primary;
    border-radius: 6px;
    padding: 0 6px; 
  }

  .disabled_select {
    @include font-body-s-regular();
    color: $color-text-secondary;
  }

  .blank {
    display: none;
  }
}

.best-combo-applied {
  margin-top: 4px;
  @include font-body-m-regular();
  color: $color-text-secondary;
}

.mkt-paylater {
  .mkt-coupon-content {
    border-radius: $radius-m;
    padding: 16px;
    background-color: $color-bg-3;
    max-height: calc(100vh - 360px);
    overflow-y: scroll;
  }
}

.paylater-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 16px;

  &-text {
    @include font-body-m-regular;
    color: $color-text-primary;
  }
}
</style>
