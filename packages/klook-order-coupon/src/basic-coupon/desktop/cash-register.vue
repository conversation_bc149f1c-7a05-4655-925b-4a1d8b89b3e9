<template>
  <div class="mkt-coupon-wrapper">
    <div :class="[isMergedPaymentPage ? 'mkt-collapse-new' : 'mkt-collapse']">
      <div slot="content">
        <MaxDiscount
          v-if="isMergedPaymentPage && visibleBestCouponSuggestNotice"
          v-on="$listeners"
          v-bind="$attrs"
          :is-mobile="false"
          :max-discount="maxDiscount"
          :coupon-list="usable"
          :payment-coupon-list="usablePaymentCoupon"
          :chosen-coupon-data="chosenCouponData"
          @combo-change="comboChange"
        />
        <div class="mkt-coupon-content">
          <!-- 优惠券兑换 -->
          <redeem v-on="$listeners" v-bind="$attrs" />
          <!-- 最佳优惠说明 -->
          <!-- 最佳优惠推荐提示 -->
          <Sugguest
            v-show="!isMergedPaymentPage && visibleBestCouponSuggestNotice"
            v-on="$listeners"
            v-bind="$attrs"
            :coupon-list="usable"
            :payment-coupon-list="usablePaymentCoupon"
            :chosen-coupon-data="chosenCouponData"
            @combo-change="comboChange"
          />
          <!-- 优惠券列表 -->
          <div class="mkt-basic-coupon-list-container" v-loading="loading">
            <tab
              v-on="$listeners"
              v-bind="$attrs"
              :available-length="usablePaymentCoupon.length"
              :unavailable-length="unusable.length"
            >
              <list
                slot="available"
                v-bind="$attrs"
                v-on="$listeners"
                :is-merged-payment-page="isMergedPaymentPage"
                :payment-coupon-list="usablePaymentCoupon"
                :chosen-coupon-data="chosenCouponData"
                @choose-coupon="chooseCoupon"
              />
              <unusableList
                slot="unavailable"
                v-bind="$attrs"
                v-on="$listeners"
                :coupon-list="unusable"
              />
            </tab>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import Collapse from "./collapse/index.vue";
import List from "../common/new-list/index.vue";
import unusableList from "../common/new-list/unusableList.vue";
import Redeem from "../common/redeem/index.vue";
import Tab from "../common/tab/index.vue";
import MaxDiscount from "../common/max-discount/index.vue";
import Sugguest from "../common/suggest/index.vue";

import {
  formatPriceThousands,
  formatTwoDecimal,
} from "../../utils/common-utils";

export default {
  name: "cash-register-coupon-desktop",

  filters: {
    formatPriceThousands,
    formatTwoDecimal,
  },

  components: {
    Collapse,
    List,
    unusableList,
    Redeem,
    Tab,
    MaxDiscount,
    Sugguest,
  },

  inject: ["couponData"],

  props: {
    isMergedPaymentPage: {
      type: Boolean,
      default: false
    },
    maxDiscount: {
      type: Object,
      default: () => ({})
    },
    visibleBestCouponSuggestNotice: {
      type: Boolean,
      default: true
    },
    usable: {
      type: Array,
      default: [],
    },
    usablePaymentCoupon: {
      type: Array,
      default: [],
    },
    unusable: {
      type: Array,
      default: [],
    },
    loading: {
      type: Boolean,
    },
  },

  watch: {
    "couponData.chosenCoupon": {
      handler(newVal, oldVal) {
        this.chosenCouponData = newVal
          ? this.$lodash.cloneDeep(newVal)
          : {
              common_coupon: {},
              payment_coupon: {},
            };
      },
      immediate: true,
      deep: true,
    },
  },
  computed: {
    usableLength() {
      return this.usablePaymentCoupon.length + this.usable.length;
    },
  },

  data() {
    return {
      expanded: false,
      chosenCouponData: {
        common_coupon: {},
        payment_coupon: {},
      },
    };
  },
  methods: {
    comboChange(comboData){
      const {common_coupon,payment_coupon} = comboData
      this.chosenCouponData = {
        common_coupon,
        payment_coupon
      }
      this.$emit("use-coupon", this.chosenCouponData);
    },
    chooseCoupon(e) {
      this.chosenCouponData[e.finance_type] = e;
      this.$emit("use-coupon", this.chosenCouponData);
    },
  }
};
</script>

<style lang="scss" scoped>
.mkt-coupon-wrapper {
  .mkt-collapse {
    padding: 0 12px;
    padding-top: 12px;
    background-color: #f5f5f5;
    border-radius: 4px;
    ::v-deep .klk-collapse-item-content {
      border-top: 1px solid #e0e0e0;
      padding-top: 16px;
      margin-left: -12px;
      margin-right: -12px;
      padding-left: 12px;
      padding-right: 12px;
    }
  }

  .mkt-collapse-new {
    .mkt-coupon-content {
      border-radius: $radius-m;
      padding: 16px;
      background-color: $color-bg-3;
      max-height: calc(100vh - 360px);
      overflow-y: scroll;
    }
  }
  
  .sale-amount {
    font-size: 16px;
    line-height: 22px;
    color: #ff5722;
    font-weight: 600;
  }
  .recommended {
    margin: 0px 12px;
    font-size: 14px;
    line-height: 20px;
    color: #ff5722;
  }
  ::v-deep .mkt-poptip {
    max-width: 378px;
    min-width: 378px;
    margin-bottom: 18px;
    background: #fff4ed;
    position: relative;
    padding: 8px 36px 8px 12px;
    .mkt-poptip-close {
      cursor: pointer;
      position: absolute;
      top: 8px;
      right: 8px;
    }
    &::after {
      content: "";
      border: 9px solid transparent;
      position: absolute;
      border-top-color: #fff4ed;
      bottom: -18px;
      left: 18px;
    }
  }
  .summary_error_msg_wrapper {
    margin: 8px 0 0 0;
  }
  .summary_error_msg {
    padding: 4px 8px;
    background-color: #ebf0ff;
  }
}
</style>
