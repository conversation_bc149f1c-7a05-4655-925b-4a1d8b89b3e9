<template>
  <div class="mkt-basic-container"
    ref="mktBasicContainer"
    :data-spm-page="pageSpmData"
  >
    <component
      :is="comp"
      v-bind="$attrs"
      v-on="$listeners"
      :is-mobile="isMobile"
      :usable="getUsableBasicCoupons"
      :bestcombo="getBestCombo"
      :max-discount="getMaxDiscount"
      :usablePaymentCoupon="getUsablePaymentCoupon"
      :unusable="getUnusableBasicCoupons"
      :tabsStatus="tabsStatus"
      :loading="loading"
      @refresh-list="handleRefreshList"
      @confirm-redeem="handleConfirmRedeem"
      @tabs-changes="tabsChange"
      @open-modal="openModal"
    >
      <div slot="init-popover">
        <slot name="init-popover"></slot>
      </div>
    </component>
    <!-- 埋点 -->
    <span
      class="redeem-status"
      data-spm-module="redeemStatus"
      style="display: none"
    ></span>
    <span
      class="fetch-coupon-list"
      data-spm-module="CheckOut_PromoCode_Load"
      style="display: none"
    ></span>
  </div>
</template>

<script>
import { findComponentDownward } from "../utils/common-utils";
import Desktop from "./desktop/index.vue";
import Mobile from "./mobile/index.vue";
import cashRegisterDesktop from "./desktop/cash-register.vue";
import cashRegisterMobile from "./mobile/cash-register.vue";
import nswDesktop from "./nsw/desktop/index.vue";
import nswMobile from "./nsw/mobile/index.vue";
import BasicMixin from "../mixins/basic-mixin";

export default {
  name: "mkt-basic-coupon",

  inject: ["env", "couponData"],

  components: {},

  mixins: [BasicMixin],

  props: {
    // page type = cashRegister 收银台 booking id
    bookingId: {
      type: String,
      default: ''
    },
    // 优惠券类型 nsw|common
    couponType: {
      type: String,
      default: 'common'
    },
    // 返回基本优惠券列表函数
    getBasicCouponList: {
      type: Function
    },
    // 兑换基本优惠券函数
    redeemBasicCoupon: {
      type: Function
    },
    isMobile: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      basicCoupons: {},
      loading: false,
      env: this.env,
      hasUsableCoupons: true,
      tabsStatus: 0,

    };
  },

  computed: {
    pageSpmData() {
      let extStr = ''
      if (this.bookingId) {
        const ext = JSON.stringify({ 
          BookingID: this.bookingId
        })
        extStr = `&ext=${ext}`
      }
      return `PromoCode?trg=manual${extStr}`
    },
    comp() {
      if (this.couponData.pageType === 'cashRegister') {
        return this.isMobile ? cashRegisterMobile : cashRegisterDesktop;
      } else {
        if (this.couponType === 'nsw'){
          return this.isMobile ? nswMobile : nswDesktop; // nsw component
        } else {
          return this.isMobile ? Mobile : Desktop;
        }
      }
    },

    getUsableBasicCoupons() {
      this.$emit('common-coupon-list-change', this.basicCoupons.usable || [])
      return this.basicCoupons.usable || [];
    },
    getUsablePaymentCoupon(){
      this.$emit('payment-coupon-list-change', this.basicCoupons.usable_payment_coupon || [])
      return this.basicCoupons.usable_payment_coupon || [];
    },
    getUnusableBasicCoupons() {
      return this.basicCoupons.unusable || []
    },
    getBestCombo() {
      return this.basicCoupons.bestcombo || []
    },
    getMaxDiscount() {
      return this.basicCoupons.max_discount || null
    }
  },
  mounted() {
    this.$inhouse.track('pageview', this.$refs.mktBasicContainer, {
      force: true
    })
  },
  methods: {
    /**
     * @desc 优惠券列表切换
     * @param e 0-available 1-unavailable
    */
    tabsChange(e){
      this.tabsStatus = e
      this.$emit('tabs-change', e)
    },
    // 获取优惠券列表
    async fetchBasicCoupons(isRedeem = false, redeemedCoupon,
      params = {
        component_data: {
          "client_sdk_version": "1.0"
        }
      }
    ) {
      this.loading = true;
      if (
        this.getBasicCouponList &&
        typeof this.getBasicCouponList === "function"
      ) {
        this.basicCoupons = await this.getBasicCouponList(params).catch(
          this.handleGetCouponListError
        );
      } else {
        console.error('getBasicCouponList is not a function')
      }
      if (!this.basicCoupons) {
        this.basicCoupons = { usable: [], unusable: [], usable_payment_coupon: [], bestcombo: []};
      }
      if (!this.basicCoupons.usable) {
        this.basicCoupons.usable = [];
      }
      if (!this.basicCoupons.bestcombo) {
        this.basicCoupons.bestcombo = [];
      }
      if (!this.basicCoupons.unusable) {
        this.basicCoupons.unusable = [];
      }
      if (!this.basicCoupons.usable_payment_coupon) {
        this.basicCoupons.usable_payment_coupon = [];
      }

      if (this.basicCoupons.bestcombo && this.basicCoupons.bestcombo.length) {
        const { combo_list = [] } = this.basicCoupons.bestcombo[0]
        if(combo_list && combo_list.length){
          const hasPlatformCoupon = combo_list.find(item=>item.finance_type == 'common_coupon')
          if (hasPlatformCoupon) {
            // 在两个之后的都可以执行展示所有优惠券的函数
            if((this.basicCoupons.usable || []).findIndex((couponItem) => couponItem.code == hasPlatformCoupon.code) > 1){
              this.setShowAll(true)
            }
          }
        }
        
      }
      this.basicCoupons && this.$emit('coupon-list-change', this.basicCoupons)
      if (this.basicCoupons) {
        // 判断有没有可用common优惠券 
        this.hasUsableCoupons =
          this.basicCoupons.usable && !!this.basicCoupons.usable.length;
      }
      // 2023-05-17,最佳优惠券需求中，去掉该功能，后端排序中已经做了该功能
      // if (this.hasUsableCoupons) {
      //   this.sortBasicCoupons(this.basicCoupons.usable);
      // }
      this.sendCouponListIht();
      // 兑换完刷列表之后的处理
      if (isRedeem){
        // 找到优惠券列表中对应的优惠券
        let type = this.findCouponListType(redeemedCoupon)
        let redeemMsg = null
        // 所属的列表查找
        this.basicCoupons[type]?.forEach(v =>{
          if (redeemedCoupon.code == v.code) {
            redeemMsg = v
          }
        })
        // 没查到去不可用列表查
        if (!redeemMsg) {
          this.basicCoupons.unusable?.forEach(v => {
            if (redeemedCoupon.code == v.code) {
              redeemMsg = v
            }
          })
        }
        redeemedCoupon = redeemMsg 
        // 优惠券存在 插入到第一项，2023-05-17,最佳优惠券需求中，去掉该功能，后端做排序，新交互与就交互冲突
        // if (redeemedCoupon){
        //   this.insertBasicCoupons(redeemedCoupon);
        // }
        // pc使用兑换的券 mweb选择
        if (redeemedCoupon && redeemedCoupon.usable) {
          // 2023-05-17,最佳优惠券需求中，去掉该功能,与改动的逻辑发生冲突
          // this.updateBestCoupon()
          // let coupon = this.$lodash.cloneDeep(this.couponData.chosenCoupon)
          // coupon[redeemedCoupon.finance_type] = redeemedCoupon
          // coupon = {...this.stacking_with_common_coupon(redeemedCoupon, coupon)}
          // // cloneDeep
          // // cash-register-coupon-mobile 
          // let refName = this.couponData.pageType == 'cashRegister' ? 'cash-register-coupon-mobile' : 'mkt-basic-coupon-mobile'
          // this.isMobile ? this.setChosenCouponData(refName, coupon) : this.$emit('use-coupon', coupon)
        }
      }
      
      this.loading = false;
    },
    // 埋点
    sendCouponListIht() {
      // iht 需要等list和settlement都调用完才能发送
      const couponListStatus = {
        1: "HasApplicable",
        2: "NoApplicable",
        3: "NoPromo"
      };

      let status;
      // 获取优惠状态 适用 不适用 无促销
      if (!this.getUsableBasicCoupons.length) {
        status = couponListStatus[2];
      } else if (
        this.getUsableBasicCoupons.length &&
        this.couponData.bestCouponCode
      ) {
        status = couponListStatus[1];
      } else if (
        this.getUsableBasicCoupons.length &&
        !this.couponData.bestCouponCode
      ) {
        status = couponListStatus[3];
      }
      // 是否自动使用
      const autoApply = this.couponData.bestCouponCode ? "true" : "false";
      // 自动使用优惠券code
      const autoApplyBatchCode = this.couponData.bestCouponCode
        ? this.couponData.bestCouponCode
        : "NA";
      // 埋点
      this.$inhouse.track("custom", ".fetch-coupon-list", {
        status,
        autoApply,
        autoApplyBatchCode
      });
    },
    // 把最优优惠券放在优惠券列表最前面
    sortBasicCoupons(usableList) {
      if (usableList && this.couponData.bestCouponCode) {
        usableList.some((item, index) => {
          if (this.couponData.bestCouponCode == item.code) {
            usableList.unshift(usableList.splice(index, 1)[0]);
            return usableList;
          }
        });
      }
    },
    openModal(coupon, couponType){
      this.tabsStatus = 0
      // 平台支付优惠劵不需要改变
      if (couponType == 'platformCoupon') {
        if ((coupon && coupon?.code)) {
          this.insertBasicCoupons(coupon, 0)
        }
        this.$nextTick(()=>{
          const boxNode = document.getElementById('mkt-coupon-list-container')
          if (boxNode) {
            boxNode.scrollTop = 0
          }
        })
      } else if(couponType == 'paymentCoupon' &&
        this.basicCoupons.usable_payment_coupon &&
        this.basicCoupons.usable_payment_coupon.length
      ){
        this.$nextTick(()=>{
          const boxNode = document.getElementById('mkt-coupon-list-container')
          const payCouponListNodes = document.getElementsByClassName('mkt-paymentCouponListBox')
          if (boxNode && payCouponListNodes && payCouponListNodes.length) {
            // 存在平台支付优惠券才需要做定位
            if(this.basicCoupons.usable && this.basicCoupons.usable.length){
              boxNode.scrollTop = payCouponListNodes[0].offsetTop - 80
            } else {
              boxNode.scrollTop = 0
            }
            
          }
        })
      }
      this.setCouponTypeTab(couponType)
    },
    // 找到优惠券数组类型
    findCouponListType(coupon){
      let type = ''
      if (coupon.usable) {
        if (coupon.finance_type === 'payment_coupon'){
          type = 'usable_payment_coupon'
        }else {
          type = 'usable';
        }
      } else {
          type = 'unusable'
      }
      return type
    },
    // 优惠券插入优惠券列表第一项
    insertBasicCoupons(coupon, index = 0) {
      let type = this.findCouponListType(coupon)
      const redeemedIndex = this.basicCoupons[type].findIndex(v=>v.code == coupon.code)
      let item = this.basicCoupons[type].splice(redeemedIndex, 1);
      this.basicCoupons[type].splice(index, 0, item[0]);
    },
    // 替换最优优惠券
    updateBestCoupon(){
        let bestCouponMoney = -Infinity
        let bestCouponCode = 0
        this.basicCoupons.usable?.forEach(v=>{
          if (v?.coupon_use_info?.coupon_discount_dec > Number(bestCouponMoney)) {
            bestCouponMoney = v.coupon_use_info.coupon_discount_dec
            bestCouponCode = v.code
          }
        })
        this.$emit('best-coupon-change', bestCouponCode)
    },
    getIsBestCouponCombo() {
      const bestlist = this.basicCoupons.bestcombo;
      const { chosenCoupon } = this.couponData || {}
      if (chosenCoupon) {
        const { common_coupon = {}, payment_coupon = {} } = chosenCoupon;
        if (bestlist && bestlist.length) {
          const { combo_list = [] } = bestlist[0];
          return combo_list.every((couponItem) => {
            return [common_coupon, payment_coupon].some((selectCodeItem) => {
              return (
                selectCodeItem && couponItem.code == selectCodeItem.code
              );
            });
          });
        }
      }
      return false;
    },
    // 兑换优惠券   stacking_with_common_coupon true可堆叠
    async handleConfirmRedeem(code) {
      this.loading = true;
      let redeemedCoupon
      let visibleToastType = ''
      try {
        redeemedCoupon = await this.redeemBasicCoupon({
          code,
          component_data: {
            "client_sdk_version": "1.0"
          }
        }) || {};
        this.clearCode();
        visibleToastType = 'redeem-success'
        // 判断是否是已选中最佳优惠劵组合，是则选择重新请求最佳优惠组合之后的最大的优惠券组合
        const isSelectedBest = this.getIsBestCouponCombo()
        const oldChosenData = this.$lodash.cloneDeep(this.couponData.chosenCoupon)
        redeemedCoupon?.code && await this.fetchBasicCoupons(true, redeemedCoupon)
        
        if(isSelectedBest) {
          const { common_coupon = {}, payment_coupon = {} } = oldChosenData;
          const {bestcombo = [], usable= [], usable_payment_coupon = []} = this.basicCoupons;
          if (bestcombo && bestcombo.length) {
            const { combo_list = [] } = bestcombo[0];
            const newBestList = combo_list.map(couponItem=>couponItem.code)
            const oldBestList = [common_coupon, payment_coupon].filter(couponItem=>couponItem && couponItem.code).map(couponItem=>couponItem.code)
            if(newBestList.sort().toString() !== oldBestList.sort().toString()) {
              // 重新选择最大优惠劵组合
              let newComboData = {}
              combo_list.forEach(couponItem=>{
                if (couponItem.finance_type == 'common_coupon') {
                    const couponData = usable.find(item=>item.code == couponItem.code)
                  if (couponData) {
                    newComboData.common_coupon = couponData
                  }
                }
                // 支付类优惠券
                if (couponItem.finance_type == 'payment_coupon') {
                  const couponData = usable_payment_coupon.find(item=>item.code == couponItem.code)
                  if (couponData) {
                    newComboData.payment_coupon = couponData
                  }
                  
                }
              })
              // cloneDeep
              // cash-register-coupon-mobile 
              let refName = this.couponData.pageType == 'cashRegister' ? 'cash-register-coupon-mobile' : 'mkt-basic-coupon-mobile'
              this.isMobile ? this.setChosenCouponData(refName, newComboData) : this.$emit('use-coupon', newComboData)
              this.$toast({
                message: this.$t('95673')
              });
              visibleToastType = 'select-best'
            }
          }
        } else {
        }
        
        // 埋点
        this.$inhouse.track("custom", ".redeem-status", {
          ifRedeem: true,
          couponbatch: redeemedCoupon?.batch_id || '',
          coupon_type: redeemedCoupon?.finance_type,
          spm: 'PromoCode.RedeemStatus',
          Input: code,
        });
      } catch (err) {
        this.handleBasicRedeemError(err, code)
        console.error(err)
      } finally {
        if (visibleToastType == 'redeem-success') {
          redeemedCoupon?.code && this.redeemedToast(redeemedCoupon)
        }
        const {usable, usable_payment_coupon, unusable} = this.basicCoupons
        const coupon = [...(usable|| []), ...(usable_payment_coupon || []), ...(unusable || [])].find(couponItem=>{
          return couponItem.code == code
        })
        if(coupon){
          // 在两个之后的都可以执行展示所有优惠券的函数
          if((usable|| []).findIndex((couponItem) => couponItem.code == code) > 1){
            this.setShowAll(true)
          }
          if((unusable || []).find((couponItem) => couponItem.code == code)) {
            if (this.tabsStatus != 1) {
              this.tabsStatus = 1
            }
          } else {
            if (this.tabsStatus != 0) {
              this.tabsStatus = 0
            }
          }
          this.$nextTick(()=>{
            const couponNode =document.getElementById(`mkt-coupon-card-${code}`)
            if(couponNode){
              couponNode.scrollIntoView({ behavior: "smooth", block: "center", inline: "nearest" })
            }
          })
        }
      }
      this.loading = false;
    },
    // 兑换提示
    redeemedToast(redeemedCoupon){
      if (this.tabsStatus == 0 && !redeemedCoupon.usable) {
        this.$toast({
          message: this.$t("74020")
        });
      } else if (this.tabsStatus == 1 && redeemedCoupon.usable){
        this.$toast({
          message: this.$t("74021")
        });
      } else {
        this.$toast({
          message: this.formmatTextId(['5347', 'campaign_success_redeem']),
          icon: "icon_feedback_success_fill",
          iconColor: "green",
          duration: 3000
        });
      }
    },
    // 删除code
    clearCode() {
      const redeemComp = findComponentDownward(this, "mkt-basic-coupon-redeem");
      redeemComp && redeemComp.clearInputCouponCode();
    },
    // 兑换接口错误统一处理
    handleBasicRedeemError(err, code) {
      this.$inhouse.track("custom", ".redeem-status", {
        Input: code,
        ifRedeem: false,
        reason: err.code,
        message: err?.message,
        spm: 'PromoCode.RedeemStatus'
      });
      if (err.code == 'feedback_failure_repeat') {
        // 重复兑换页面中间出现toast 提示
        this.$toast({
          message: this.$t('91899')
        });
      } else {
        this.$toast({
          message: err.message,
          icon: "icon_feedback_failure_fill",
          iconColor: "red",
          duration: 3000
        });
      }
      
    },
    // 列表接口错误统一处理
    handleGetCouponListError(err) {
      if (err.code === "4001") {
        this.showSimpleDialog(err.message, () => {
          this.offbeforeunload(); // ？
          window.location.reload();
        });
      } else {
        this.showSimpleDialog(err.message);
      }
    },

    // 弹窗提示
    async showSimpleDialog(content, cb) {
      const { result, value } = await this.$singleConfirm(content, {
        content,
        okLabel: this.formmatTextId(['15788',
        'invite_activity_dialog_sure',
        'order_submit_yes',
        'specificevent_b_client_unavaliable_ok',
        'undo_fail_dialog_ok_5_18',
        'activity.v2.mobile.guarantee_close',
        'applecny.ok',
        'city_page_ok',
        'global.tips.okTxt',
        'search_web_ok'
        ])
      });
      if (result === true) {
        typeof cb === "function" && cb().apply(this);
      }
    },
    async handleRefreshList() {
      await this.fetchBasicCoupons();
    }
  }
};
</script>
<style lang="scss" scoped>
.mkt-basic-container {
  position: relative;
}
</style>
