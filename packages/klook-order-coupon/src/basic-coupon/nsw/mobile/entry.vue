<template>
  <!-- nsw优惠 -->
  <div class="summary_row promo_code nsw_promo_code">
    <div class="promo_code-entrance">
      <p class="nsw_promo_code_left" @click="openBottomSheet">
        <b class="red">*</b> {{ $t('19658') }}
      </p>
      <span class="promo_code_right" @click="openBottomSheet">
        <span class="nsw_best_text"> {{ $t('19806') }}</span>
        <img src="../img/arrow-right.svg" class="arrow-right" alt="">
      </span>
    </div>

    <div v-if="nswValidateError" class="nws_validate_error">
      {{ $t('19807') }}
    </div>

    <div v-if="couponCode" :class="['nsw_redeem_box']">
      <div
        v-if="haveDiscount"
        class="nsw_redeem_item"
      >
        <div class="nsw_name">{{ $t('19657') }}</div>
        <div class="nsw_discount">
          - {{ currency }}
          {{
            Number(coupon_info.coupon_discount)
            | formatTwoDecimal
            | formatPriceThousands
          }}
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { 
  getCurrencySymbol,
  formatPriceThousands,
  formatTwoDecimal 
} from "../../../utils/common-utils";
export default {
  name: 'nsw-mobile-entry',
  components: {},
  filters: {
    formatPriceThousands,
    formatTwoDecimal
  },
  props: {
    priceInfo: {
      type: Object,
      default() {
        return {
          coupon_info: {}
        }
      }
    },
    nswValidateError: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
    };
  },
  mounted() {
  },
  computed: {
    haveDiscount(){
      let discount = this.priceInfo?.coupon_info?.coupon_discount || 0
      return discount > 0
    },
    currency(){
      return getCurrencySymbol(this.priceInfo.currency)
    },
    coupon_info(){
      return this.priceInfo?.coupon_info || {}
    },
    couponCode(){
      return this.priceInfo?.coupon_info?.coupon_code || ''
    }
  },
  methods: {
    openBottomSheet() {
      this.$emit('enter-nsw-promo-code');
    },
  },
};
</script>

<style lang="scss" scoped>

    .summary_row {
      display: flex;
      justify-content: space-between;
      flex-direction: row;
      padding: 16px 0;
    }


    .nsw_promo_code {
      .promo_code-entrance {
        display: flex;
        justify-content: space-between;
        width: 100%;
        align-items: center;
      }

      border-bottom: 0.5px solid rgba(0, 0, 0, 0.12);

      .nsw_promo_code_left {
        word-break: break-word;

        .red {
          color: #ff5722;
        }
      }

      .nsw_best_text {
        border-radius: 99rem;
        background-color: #ff5722;
        color: #ffffff;
        padding: 4px 10px;
        font-size: 10px;
        font-weight: 500;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        width: calc(100% - 24px);

        .best_icon {
          width: 10px;
          height: 10px;
          margin-right: 4px;
          color: #ffffff;
        }
      }

      .nws_validate_error {
        font-size: 12px;
        line-height: 14px;
        margin-top: 8px;
        color: #EB4221;
      }

      .nsw_redeem_box {
        display: flex;
        justify-content: flex-end;
        flex-direction: column;
        width: 100%;
        margin-top: 18px;

        .nsw_redeem_item {
          width: 100%;
          display: flex;
          justify-content: space-between;
          margin-bottom: 12px;

          .nsw_name {
            color: #212121;
          }

          .nsw_discount {
            color: #ff5722;
            font-size: 14px;
            font-weight: 700;
          }
        }
      }
    }

    .promo_code {
      margin-top: 8px;
      flex-wrap: wrap;
      align-items: center;

      .promo_code_right {
        display: flex;
        align-items: center;
        max-width: calc(30% + 24px);

        b {
          color: #00b371;
          font-weight: 500;
        }

        svg {
          color: #757575;
          font-size: 24px;
          flex: none;
        }
      }
    }
    .arrow-right {
      color: #757575;
      width: 24px;
      height: 24px;
    }
</style>
