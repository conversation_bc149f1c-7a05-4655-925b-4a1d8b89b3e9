<template>
  <div>
    <klk-coupon-bottom-sheet
      show-close
      :visible="dialogShow"
      :title="$t('19658')"
      @close="close"
      :mask-closable="false"
    >
      <div class="klk-bottom-sheet-content">
        <div class="nsw-tip">{{ $t('49446') }}</div>
        <div class="logo-box">
            <img class="nsw-logo" src="../img/dine_nsw_logo.svg" alt="">
        </div>
        <div :class="['redeem-detail', { 'redeem-detail-error': errorTip }]">
          <div style="display: flex;">
            <klk-coupon-input
              v-model="inputCouponCode"
              class="redeem-input"
              :placeholder="$t('19930')"
            ></klk-coupon-input>
            <klk-coupon-button
              @click.native="redeemNewCoupon"
              class="redeem-btm"
              type="primary"
              >{{ redeemText }}</klk-coupon-button>
          </div>
          <div v-if="errorTip" class="nsw-error-tip">{{ errorTip }}</div>
        </div>
        <div class="nsw-detail-h">
          <img v-lazy="'https://res.klook.com/image/upload/v1615947965/nsw_payment_how_to_use_wqz2ya.jpg'" />
        </div>
      </div>
    </klk-coupon-bottom-sheet>

    <klk-coupon-modal
      button-align="center"
      :z-index="9999"
      :open.sync="moduleShow"
      @on-cancel="moduleCancel"
      @on-confirm="moduleConfirm"
      :ok-label="moduleMsg.continue"
      :cancel-label="moduleMsg.cancel"
    >
      {{moduleMsg.content}}
    </klk-coupon-modal>
  </div>
</template>

<script>
import Vue from 'vue';
import VueLazyLoad from 'vue-lazyload';
const debounce = require("lodash/debounce");
import BasicMixin from "../../../mixins/basic-mixin";
Vue.use(VueLazyLoad);

export default {
  name: 'nsw-coupon-promo-code',
  mixins: [BasicMixin],
  props: {
    redeemNswCoupon: {
      type: Function
    },
    dialogShow: Boolean
  },
  data() {
    return {
      inputCouponCode: '',
      errorTip: '',
      couponCode: '',
      conformDialog: {},
      dialogProp: null,
      moduleShow: false,
      moduleContent: '',
      moduleMsg: {
        content: '',
        continue: '',
        cancel: ''
      }
    };
  },
  computed:{
    redeemText(){
      return this.formmatTextId(['12208', 'srv_redeem'])
    }
  },
  watch: {
    inputCouponCode() {
      this.errorTip = false;
    }
  },
  methods: {
    clearInputCouponCode(){
      this.inputCouponCode = ''
    },
    close() {
      this.$emit('close-promo-code');
    },

    redeemNewCoupon: debounce(async function() {
      if (!this.inputCouponCode.length) {
        return;
      }

      this.redeem();
    }, 500),

    async redeem() {
      this.$emit('show-loading', true);
      let res = await this.redeemNswCoupon({
          nsw_code: this.inputCouponCode,
          component_data: {
            "client_sdk_version": "1.0"
          }
        })
      if (res.success && res.result) {
        this.$emit('show-loading', false);
        const { coupon_code } = res.result;
        this.couponCode = coupon_code;
        if (res.result.dialog) {
          this.moduleShow = true
          this.moduleMsg = res.result.dialog
          return;
        }
        this.$emit('nsw-redeem-success', coupon_code);
      } else {
        this.errorTip = res.error && res.error.message;
        this.$emit('show-loading', false);
      }
    },
    moduleCancel(){
      this.moduleShow = false;
      this.couponCode = '';
    },
    moduleConfirm(){
      this.moduleShow = false;
      this.$emit('nsw-redeem-success', this.couponCode);
    }
  },
};
</script>

<style lang="scss" scoped>
.nsw-error-tip {
  padding-top: 4px;
  line-height: 14px;
  font-size: 12px;
  margin-top: 20px;
  color: #eb4221;
  display: none;
}

.fade-enter,
.fade-leave-active {
  opacity: 0;
}

.klk-bottom-sheet-content {
  .nsw-tip {
    font-size: 14px;
    line-height: 1.44;
    font-weight: 400;
    color: #757575;
    margin-bottom: 24px;
    text-align: left;
  }
  .logo-box {
    display: flex;
    justify-content: center;

    .nsw-logo {
      width: 60px;
      height: 60px;
    }
  }

  .redeem-detail {
    display: flex;
    margin-top: 12px;
    flex-direction: column;
    margin-bottom: 48px;

    &.redeem-detail-error {

      .klk-input-inner {
        border-color: #eb4221;
      }
      .redeem-input {
        border-color: #eb4221;
      }
      
      .nsw-error-tip {
        display: block;
        margin-top: 0;
      }
    }

    .redeem-input {
      border-radius: 4px;
      flex-grow: 1;
      width: auto;
      border: 1px solid #e0e0e0;
      padding: 1px;
      height: 48px;

      input {
        height: 100%;
        border: none;
      }
    }

    .redeem-btm {
      margin-left: -3px;
      z-index: 3;
      border-radius: 0 4px 4px 0;
      height: 48px;
    }
  }

  .nsw-detail-h {
    img {
      width: 100%;
    }
  }
}
</style>

<style lang="scss">
.redeem-input {
  .klk_c_input {
    height: 100%;
    border: none;

    &.klk_c_input.klk_c_input--disabled,
    &.klk_c_input.klk_c_input--disabled:hover,
    &.klk_c_input.klk_c_input--disabled:focus {
      border: none;
    }
  }
}

.guide-tip {
  a {
    color: #437DFF;
    text-decoration: underline;
  }
}
</style>
