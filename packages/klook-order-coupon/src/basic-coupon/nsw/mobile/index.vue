<template>
  <!-- nsw优惠 -->
  <div>
    <nsw-entry
      ref="nswEntry"
      v-bind="$attrs"
      @enter-nsw-promo-code="enterNswPromoCode"
    >
    </nsw-entry>
    <nsw-promo-code
      ref="promoCode"
      v-bind="$attrs"
      :dialogShow="dialogShow"
      @close-promo-code="closePromoCode"
      @nsw-redeem-success="nswRedeemSuccess"
      @show-loading="showLoading"
    ></nsw-promo-code>
  </div>
</template>

<script>

import nswEntry from './entry.vue';
import nswPromoCode from './promo-code.vue';
export default {
  name: 'nsw-coupon-mobile',
  components: {
    nswEntry,
    nswPromoCode
  },
  data() {
    return {
      dialogShow: false,
    };
  },
  methods: {
    showLoading(isShow){
      isShow ? this.$showLoading() : this.$hideLoading()
    },
    enterNswPromoCode(){
      this.dialogShow = true
    },
    nswRedeemSuccess(code){
      this.$emit('nsw-redeem-success', code);
      this.closePromoCode()
    },
    closePromoCode(){
      this.dialogShow = false
      this.$refs.promoCode.clearInputCouponCode()
    }
  }
};
</script>