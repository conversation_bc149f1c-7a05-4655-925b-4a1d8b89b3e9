<template>
  <div class="os-nsw-promo">
    <div class="os-nsw-promo-box">
      <div class="os-nsw-promo-content">
        <div class="left">
          <div class="title-box">
            <div class="title"><b class="red">*</b> {{ $t('19658') }}</div>
            <div class="how-to-use" @click="openHowToUse">
              {{ $t('15470') }}
            </div>
          </div>
          <div class="nsw-tip"> {{ $t('49446') }} </div>
          <!-- 输入框 -->
          <div
            v-if="!haveDiscount"
            :class="['redeem-box', { 'redeem-detail-error': nswValidateError }]"
          >
            <div style="display: flex;">
              <klk-coupon-input
                class="redeem-input"
                v-model="inputCouponCode"
                :placeholder="$t('19930')"
              ></klk-coupon-input>
              <klk-coupon-button
                class="redeem-btm"
                type="primary"
                size="normal"
                :disabled="disabled"
                :loading="status === 'loading'"
                @click.native="handlerRedeem"
              >
                <template v-if="status !== 'loading'">
                  {{ redeemText }}
                </template>
              </klk-coupon-button>
            </div>
            <div v-show="nswValidateError" class="nsw-error-tip">
              {{ errorTip }}
            </div>
          </div>

          <!-- redeem-success -->
          <div
            v-else
            class="redeem-success"
          >
            <span class="nsw_name">{{ $t('19657') }}</span>
            <span class="nsw_discount">
              - {{ currency }}
              {{
                Number(priceInfo.coupon_info.coupon_discount)
                | formatTwoDecimal
                | formatPriceThousands
              }}
              </span>
          </div>
        </div>
        <div class="right">
          <img class="nsw-logo" src="../img/icon_nsw_badge.svg" alt="">
        </div>
      </div>
    </div>

    <!-- how to use -->
    <klk-coupon-modal
      title=""
      size="large"
      width="1160px"
      modal-class="how-to-usw-modal"
      :open.sync="showHowToUse"
      :closable="true"
      :show-default-footer="false"
      :overlay-closable="true"
      @on-close="showHowToUse = false"
    >
      <div class="klk-modal-content">
        <img
          v-lazy="
            'https://res.klook.com/image/upload/v1615949559/web3.0/nsw_promo_guide.png'
          "
        />
      </div>
    </klk-coupon-modal>

    <klk-coupon-modal
      modal-class="confirm-to-pay-modal"
      :open.sync="showConfirmToPay"
      :ok-label="conformDialog.continue"
      :cancel-label="conformDialog.cancel"
      @on-confirm="continuePay"
      @on-cancel="cancelPay"
    >
      <div class="klk-modal-content">
        {{ conformDialog.content }}
      </div>
    </klk-coupon-modal>
  </div>
</template>

<script>
const debounce = require("lodash/debounce");
import { 
  getCurrencySymbol,
  formatPriceThousands,
  formatTwoDecimal 
} from "../../../utils/common-utils";

import Vue from 'vue';
import VueLazyLoad from 'vue-lazyload';
import BasicMixin from "../../../mixins/basic-mixin";

Vue.use(VueLazyLoad);

export default {
  name: 'nsw-coupon-desktop',
  mixins: [BasicMixin],
  filters: {
    formatPriceThousands,
    formatTwoDecimal
  },
  props: {
    redeemNswCoupon: {
      type: Function
    },
    priceInfo: {
      type: Object,
      default() {
        return {
          coupon_info: {}
        };
      }
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      nswValidateError: false,
      inputCouponCode: '',
      showHowToUse: false,
      status: '', //used nouse error loading
      errorTip: '',
      couponCode: '',
      conformDialog: {},
      showConfirmToPay: false
    };
  },
  computed: {
    haveDiscount(){
      let discount = this.priceInfo?.coupon_info?.coupon_discount || 0
      return discount > 0
    },
    currency(){
      return getCurrencySymbol(this.priceInfo?.currency || '')
    },
    redeemText(){
      return this.formmatTextId(['12208', 'srv_redeem'])
    }
  },
  methods: {
    openHowToUse() {
      this.showHowToUse = true;
    },
    handlerRedeem: debounce(async function() {
      if (!this.inputCouponCode.length) {
        return;
      }
      if (this.status === 'loading') {
        return;
      }
      this.redeem();
    }, 500),
    async redeem() {
      this.status = 'loading';
      try{
        let res = await this.redeemNswCoupon({
            nsw_code: this.inputCouponCode,
            component_data: {
              "client_sdk_version": "1.0"
            }
          })
        if (res.success && res.result) {
          this.status = 'used';
          const { coupon_code } = res.result;
          this.couponCode = coupon_code;

          if (res.result.dialog) {
            this.showConfirmToPay = true;
            this.conformDialog = res.result.dialog;
            return;
          }
          this.$emit('nsw-redeem-success', coupon_code);
        } else {
          this.errorTip = res.error && res.error.message;
          this.nswValidateError = true;
          this.status = 'error';
        }
      }catch(err){
        console.error(err)
      }
    },
    continuePay() {
      this.showConfirmToPay = false;
      this.$emit('nsw-redeem-success', this.couponCode);
    },
    cancelPay() {
      this.showConfirmToPay = false;
      this.couponCode = '';
    },
  },
  mounted() {
  }
};
</script>

<style lang="scss">
.os-nsw-promo {
  margin-bottom: 32px;
  .os-nsw-promo-box {
    background: #F5F5F5;
    padding: 32px 24px;

    .same-time-tip {
      margin-bottom: 24px;
    }

    .os-nsw-promo-content {
      display: flex;
      justify-content: space-between;

      .left {
        .title-box {
          display: flex;
          font-size: 16px;
          line-height: 22px;
          margin-bottom: 2px;

          .title {
            margin-right: 8px;
            font-weight: 700;
          }

          .red {
            color: #ff5722;
          }

          .how-to-use {
            display: flex;
            align-items: center;
            color: #888888;
            text-decoration: underline;
            font-size: 12px;

            &:hover {
              color: #700fff;
              cursor: pointer;
            }

            svg {
              margin-right: 6px;
            }
          }
        }
        .nsw-tip {
          font-size: 14px;
          line-height: 1.44;
          font-weight: 400;
          color: #888888;
          text-align: left;
          margin-bottom: 8px;
          width: 520px;
        }

        .redeem-box {
          &.redeem-detail-error {
            .klk-input-inner {
              border-color: #EB4221;
            }
          }

          .redeem-input {
            width: 420px;
          }

          .redeem-btm {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-left: -4px;
            z-index: 3;
            border-top-left-radius: 0px;
            border-bottom-left-radius: 0px;
            line-height: 1.3;
            // width: 100px;
          }

          .nsw-error-tip {
            line-height: 14px;
            font-size: 12px;
            color: #EB4221;
            padding-top: 8px;
          }
        }

        .redeem-success {
          font-size: 16px;
          padding-top: 5px;

          .nsw_name {
            color: #212121;
          }

          .nsw_discount {
            color: #ff5722;
            margin-left: 16px;
            font-weight: 700;
          }
        }
      }

      .right {
        padding: 0 8px 0 32px;
        border-left: 1px solid #e0e0e0;

        .nsw-logo {
          width: 60px;
          height: 60px;
        }
      }
    }
  }
}

.how-to-usw-modal {
  padding: 0 !important;
  background: transparent !important;

  .klk-modal-body {
    padding: 0 !important;

    .klk-modal-content {
      img {
        width: 1160px;
      }
    }
  }
}

.confirm-to-pay-modal {
  .klk-modal-footer {
    box-shadow: 0;
  }
}
</style>
