<!--
 * @Author: <PERSON>
 * @Date: 2023-04-20 16:00:02
 * @LastEditors: <PERSON>
 * @LastEditTime: 2023-04-27 11:41:59
 * @Description: file content
-->
<template>
  <div v-if="visibleSuggestCoupon">
    <div
      class="sugguest-coupon-notice-wrapper"
      data-spm-module="Suggestedoptions"
    >
      <i class="coupon_icon" />
      <p class="notice_text">
        <span >{{ $t("91895") }}</span>
        <span
          data-spm-module="ViewallsuggestedoptionS"
          data-spm-virtual-item="__virtual"
          class="view_suggest_options_btn"
          @click.stop="showSuggestModal"
        >
          {{ $t("91896") }}
        </span>
      </p>
    </div>
    <klk-coupon-bottom-sheet
      v-if="isMobile"
      show-close
      :visible="visibleSuggestCouponModal"
      :title="$t('91897')"
      @close="closeModal"
      :mask-closable="false"
    >
      <div class="klk-modal-content" v-if="visibleSuggestCouponModal">
        <p class="tip_text">{{ $t("91898") }}</p>

        <ComboOptions
          v-bind="{ ...$props }"
          :visible="visibleSuggestCouponModal"
          @select-combo="selectComboChange"
        />
      </div>
    </klk-coupon-bottom-sheet>
    <klk-coupon-modal
      v-else
      :title="$t('91897')"
      :open.sync="visibleSuggestCouponModal"
      :closable="true"
      :show-default-footer="false"
      :overlay-closable="true"
      @on-close="closeModal"
      width="736px"
      modal-class="suggest-combo-list-modal"
    >
      <div class="klk-modal-content" v-if="visibleSuggestCouponModal">
        <p class="tip_text">{{ $t("91898") }}</p>

        <ComboOptions
          v-bind="{ ...$props }"
          :visible="visibleSuggestCouponModal"
          @select-combo="selectComboChange"
        />
        <div class="modal-footer">
          <klk-coupon-button
            class="close_modal_btn"
            size="small"
            type="outlined"
            @click="closeModal"
            >Cancel</klk-coupon-button
          >
          <klk-coupon-button
            class="confirm_combo_btn"
            size="small"
            type="primary"
            :data-spm-module="`Option_click`"
            data-spm-virtual-item="__virtual"
            @click="confirmComboHandle"
          >
            Confirm
          </klk-coupon-button>
        </div>
      </div>
    </klk-coupon-modal>
  </div>
</template>

<script>
import ComboOptions from "./options.vue";

export default {
  name: "sugguest-coupon-notice",
  components: { ComboOptions },

  props: {
    isMobile: {
      type: Boolean,
      default: false,
    },
    couponList: {
      type: Array,
      default: () => [],
    },
    paymentCouponList: {
      type: Array,
      default: () => [],
    },
    bestcombo: {
      type: Array,
      default: () => [],
    },
    chosenCouponData: {
      type: Object,
      default: () => {
        return {
          common_coupon: {},
          payment_coupon: {},
        };
      },
    },
  },

  data() {
    return {
      visibleSuggestCouponModal: false,
      selectComboCoupons: {
        common_coupon: {},
        payment_coupon: {},
      },
    };
  },
  computed: {
    visibleSuggestCoupon() {
      return this.bestcombo && this.bestcombo.length;
    },
    isBestCouponCombo() {
      const bestlist = this.bestcombo;
      const { common_coupon = {}, payment_coupon = {} } = this.chosenCouponData;

      if (bestlist && bestlist.length) {
        const { combo_list = [] } = bestlist[0];
        return combo_list.every((couponItem) => {
          return [common_coupon, payment_coupon].some((selectCodeItem) => {
            return (
              selectCodeItem && couponItem.code == selectCodeItem.code
            );
          });
        });
      }

      return false;
    },
  },
  watch: {},
  methods: {
    showSuggestModal() {
      this.visibleSuggestCouponModal = true;
    },

    selectComboChange(val) {
      this.selectComboCoupons = val;
      // 手机端数据要上传
      if (this.isMobile) {
        this.$emit("combo-change", this.selectComboCoupons);
        this.closeModal();
      }
    },
    confirmComboHandle() {
      this.$emit("combo-change", this.selectComboCoupons);
      this.closeModal();
    },
    closeModal() {
      this.visibleSuggestCouponModal = false;
    },
  },
};
</script>

<style lang="scss" scoped>
.sugguest-coupon-notice-wrapper {
  padding: 16px 20px;
  border: 1px solid #e6e6e6;
  border-radius: 8px;
  background-color: #faf5f2;
  display: flex;
  align-items: center;
}
.coupon_icon {
  width: 17px;
  height: 13px;
  display: inline-block;
  background: url(https://res.klook.com/image/upload/v1683889701/bwcozuzrg87rowmokyqs.png);
  background-size: cover;
}
.notice_text {
  padding-left: 10px;
  margin: 0;
  font-size: 16px;
  color: #212121;
  flex: 1;
}
.view_suggest_options_btn {
  color: rgba(32, 115, 249, 1);
  cursor: pointer;
}
.klk-modal-content {
  background-clip: #ffffff;
  color: #212121;
  font-size: 16px;
  .tip_text {
    color: #757575;
    margin: 22px 0 10px 0;
  }
}
.modal-footer {
  margin-top: 24px;
  display: flex;
  justify-content: flex-end;
}
.confirm_combo_btn {
  margin-left: 10px;
}
</style>
