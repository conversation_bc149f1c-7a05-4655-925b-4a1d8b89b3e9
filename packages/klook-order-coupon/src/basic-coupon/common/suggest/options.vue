<!--
 * @Author: <PERSON>
 * @Date: 2023-04-20 16:00:02
 * @LastEditors: <PERSON>
 * @LastEditTime: 2023-04-27 11:37:04
 * @Description: file content
-->
<template>
  <klk-radio-group data-spm-module="PromocodeSuggestedoptions" v-model="selectCoupon" @change="selectComboChange">
    <div
      class="combo_opt_item_box"
      v-for="(comboItem, ind) in suggestOptions"
      :key="comboItem.suggest_comno_opt_value + '_suggest_comno_opt_' + ind"
      @click.stop="selectComboChange('')"
    >
      <div class="combo_opt_item" :data-spm-item="`Option_click?ext=${JSON.stringify({'batch id': getExtrasData(comboItem.combo_list)})}`" @click.stop="">
        <klk-coupon-radio :group-value="comboItem.suggest_comno_opt_value" />
        <div class="opt_label">
          <span class="label_text">{{ comboItem.discount_title }}</span>
          <span class="total_text">{{ comboItem.discount_desc }}</span>
        </div>
      </div>
    </div>
  </klk-radio-group>
</template>

<script>
export default {
  name: "ComboOptions",
  components: {},

  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    couponList: {
      type: Array,
      default: () => [],
    },
    paymentCouponList: {
      type: Array,
      default: () => [],
    },
    bestcombo: {
      type: Array,
      default: () => [],
    },
    chosenCouponData: {
      type: Object,
      default: () => {
        return {
          common_coupon: {},
          payment_coupon: {},
        };
      },
    },
  },

  data() {
    return {
      selectCoupon: "",
    };
  },
  computed: {
    suggestOptions() {
      return (
        this.bestcombo.map((comboItem) => {
          return {
            ...comboItem,
            suggest_comno_opt_value: this.getOptionValue(comboItem.combo_list),
          };
        }) || []
      );
    },
  },
  watch: {
    visible: {
      immediate: true,
      handler: function (val) {
        if (val) {
          const { common_coupon, payment_coupon } = this.chosenCouponData;
          this.selectCoupon = this.getOptionValue(
            [common_coupon, payment_coupon]
              .filter((couponItem) => couponItem && couponItem.code)
          );
        }
      },
    },
  },
  methods: {
    getExtrasData(combo_list){
      const {common_coupon, payment_coupon } = this.getComboCouponInfo(combo_list);
      return {payment_batch_id: payment_coupon.batch_id, platform_batch_id: common_coupon.batch_id}
    },
    getComboCouponInfo(combo_list) {
      const common_coupon =
        this.couponList.find((item) => {
          return combo_list.find(
            (cmboCouponItem) => cmboCouponItem.code == item.code
          );
        }) || {};
      const payment_coupon =
        this.paymentCouponList.find((item) => {
          return combo_list.find(
            (cmboCouponItem) => cmboCouponItem.code == item.code
          );
        }) || {};

      return {common_coupon,payment_coupon };
    },
    getOptionValue(combo_list = []) {
      const {common_coupon,payment_coupon } =
        this.getComboCouponInfo(combo_list);
      const paltformCode = common_coupon.code;
      const paymentCode = payment_coupon.code;
      const codeList = [];
      if (paltformCode) {
        codeList.push(paltformCode);
      }
      if (paymentCode) {
        codeList.push(paymentCode);
      }
      return codeList.join(",");
    },
    selectComboChange(val) {
      this.selectCoupon = val;
      const { common_coupon,payment_coupon } = this.getComboCouponInfo(
        val.split(",").map((code) => {
          return {code};
        })
      );
      common_coupon,payment_coupon
      this.$emit("select-combo", { common_coupon,payment_coupon });
    },
  },
};
</script>

<style lang="scss" scoped>
.combo_opt_item_box {
  padding: 6px 0;
}
.combo_opt_item {
  width: 100%;
  display: flex;
  align-items: center;
}
.opt_label {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
  .label_text{
    flex: 1;
  }
  .total_text {
    font-weight: 600;
    padding-left: 8px;
    /* Brand 品牌/$color-brand-primary */
    color: #ff5b00;
    white-space: nowrap;
  }
}
::v-deep .combo_opt_item {
  .klk-radio:not(:last-child) {
    margin-right: 0;
  }
}
</style>
