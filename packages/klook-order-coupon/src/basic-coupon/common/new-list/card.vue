<template>
  <div
    :class="[
      {
        'mkt-disabled': internalDisabled,
        'mkt-mobile': isMobile,
        'mkt-desktop': !isMobile,
        'is-chosen': isChosen
      },
      'mkt-coupon-card'
    ]"
    :id="`mkt-coupon-card-${couponItem.code}`"
    :data-spm-module="cardSpmModuleData"
    data-spm-virtual-item="__virtual?trg=manual"
    @click.prevent="changeChosen"
  >
    <!-- radio -->
    <input
      :id="couponItem.code"
      ref="checkbox"
      type="radio"
      tabindex="-1"
      class="mkt-radio"
      name="same"
      :checked="isChosen"
      :value="couponItem.code"
      :disabled="internalDisabled"
    />
    <div
      class="mkt-radio-icon"
      :class="{ 'mkt-radio-icon-disabled': internalDisabled }"
    >
      <klk-coupon-icon
        class="radio-check"
        type="icon_other_check_xs"
        :size="12"
        color="#fff"
      />
    </div>
    <!-- 最优优惠券标识图 2023/04/23 有最佳优惠券组合不需要最优优惠券标识-->
    <!-- <div class="card-top">
      <span v-if="isRecommended && !internalDisabled" class="mkt-recommended">
        {{ $t("73208") }}
      </span>
    </div> -->
    <!-- 卡片内容 -->
    <div class="card-content">
      <div class="card-title">
        <img class="payment-logo" v-if="couponItem.finance_type == 'payment_coupon' && method_icon.length" :src="method_icon[0]" alt="">
        {{ couponItem.discount_desc }}
      </div>
      <div class="card-description">
        {{ couponItem.special_desc }}
      </div>
    </div>
    <div class="card-middle">
      <div class="decorator" />
    </div>
    <div class="card-bottom">
      <div v-show="couponItem.reason" class="mkt-coupon-reason">
        {{ couponItem.reason }}
      </div>
      <!-- desktop -->
      <div v-if="!isMobile" class="card-bottom-inner">
        <div>
          <div class="card-end-time">
            {{ displayTime }}
          </div>
          <div v-if="couponItem.finance_type == 'payment_coupon' &&
            couponItem.stacking_with_others" class="coupon_enbale_stacking_others">
            {{ $t('91900') }}
          </div>
          <div v-if="couponItem.finance_type == 'payment_coupon' &&
            !couponItem.stacking_with_others" class="coupon_disable_stacking_others">
            {{ $t('173937') }}
          </div>
          <div class="card-code">
            {{ couponItem.code }}
          </div>
          <div class="bottom-link" @click="jumpUrl(bottomLink.url)" :style="{color: bottomLink.text_color}">
            {{ bottomLink.text }}
          </div>
        </div>
        <info :content="tnc" :coupon-item="couponItem" />
      </div>
      <!-- mobile -->
      <klk-coupon-collapse class="mkt-collapse" v-else @click.native="handleChange">
        <klk-coupon-collapse-item>
          <div slot="title">
            <div class="card-end-time">
            {{ displayTime }}
            </div>
            <div v-if="couponItem.finance_type == 'payment_coupon' &&
              couponItem.stacking_with_others" class="coupon_enbale_stacking_others">
              {{ $t('91900') }}
            </div>
            <div v-if="couponItem.finance_type == 'payment_coupon' &&
              !couponItem.stacking_with_others" class="coupon_disable_stacking_others">
              {{ $t('173937') }}
            </div>
            <div class="card-code">
              {{ couponItem.code }}
            </div>
          </div>
          <div
            class="tnc"
            :data-spm-module="
              `PromoCodeDetail?oid=couponcode_${encodeURIComponent(couponItem.code)}`
            "
          >
            <p class="tnc-title">
              {{ $t("44205") }}
            </p>
            <p class="tnc-desc">
              {{ tnc }}
            </p>
            <span class="tnc-bottom-link" @click="jumpUrl(bottomLink.url)">
              {{ bottomLink.text }}
            </span>
          </div>
        </klk-coupon-collapse-item>
      </klk-coupon-collapse>
    </div>
  </div>
</template>

<script>
import Info from "./info.vue";
var dayjs = require('dayjs');
const updateLocale = require('dayjs/plugin/updateLocale');
dayjs.extend(updateLocale);

import BasicMixin from "../../../mixins/basic-mixin";

export default {
  name: "MktBasicCouponCard",

  mixins: [BasicMixin],

  components: {
    Info
  },

  props: {
    imgurl: {
      type: String,
      default: ''
    },
    isMobile: {
      type: Boolean,
      default: false
    },
    disabled: {
      type: Boolean,
      default: false
    },
    // 被选中
    isChosen: {
      type: Boolean,
      default: false
    },
    // 最优优惠券
    isRecommended: {
      type: Boolean,
      default: false
    },
    couponItem: {
      type: Object,
      required: true,
      default: () => {
        return {
          usable: true,
          discount_desc: "koin v2 send coupon",
          end_time: "2022-07-22T15:01:59+08:00",
          end_date: "2022-07-22",
          code: "F3CLL8WU",
          special_desc: "", // for tooltip
          desc: "", // for tooltip
          reason: "", // for tooltip, optional
          // optional
          bottom_link: {
            url: "",
            text: ""
          }
        };
      }
    }
  },

  computed: {
    unusableCode() {
      return this.couponItem.unusable_code || ''
    },
    cardSpmModuleData() {
      const ext = encodeURIComponent(JSON.stringify({
        IfAvailable: this.couponItem.usable,
        UnavailableReasonID: this.unusableCode,
        UnavailableReasonText: this.couponItem.reason || '',
        coupon_type: this.couponItem.finance_type,
        coupon_batch_id: this.couponItem.batch_id
      }))
      const couponCode = encodeURIComponent(this.couponItem.code)
      return `PromoCode_Select_LIST?oid=couponcode_${couponCode}&ext=${ext}`
    },
    displayTime() {
      const formater = ' ' + this.formmatTextId(['1339','global.standard.date.format'])
      const { end_time, end_date } = this.couponItem || {}
      let timeValue = ''

      if (end_time) {
        timeValue +=  dayjs(end_time).format(formater + ' HH:mm')
      } else if(end_date) {
        timeValue +=  dayjs(end_date).format(formater)
      }

      return this.formmatTextId(['1905', 'promo_code.expires_label']) + timeValue
    },
    // 优惠券是否可用 true 不可用 false可用
    internalDisabled() {
      return !this.couponItem.usable;
    },

    tnc() {
      return this.couponItem.desc;
    },
    bottomLink(){
      return this.couponItem?.bottom_link || {}
    },
    method_icon(){
      return this.couponItem?.payment_restrict?.method_icon || []
    }
  },
  methods: {
    changeChosen() {
      // 有效优惠券
      if (this.couponItem.usable && !this.disabled) {
        // 优惠券没有code 代表取消选中
        const selectCoupon = this.isChosen ? { finance_type: this.couponItem.finance_type } : this.couponItem
        this.$emit("click-coupon", selectCoupon);
        this.$inhouse.track('action', '.mkt-coupon-card');
      }
    },
    handleChange(event) {
      event.stopPropagation();
    },
    jumpUrl(url){
      window.open(url, '_blank')
    }
  }
};
</script>

<style lang="scss" scoped>
$orange: #ff5722;
$backgroundGray: rgb(245, 245, 245);
.mkt-coupon-card {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;

  border-radius: 12px;
  border: 1px solid transparent;
  background-color: #fff;
  position: relative;

  .mkt-radio {
    display: none;
  }

  .mkt-radio-icon {
    border: 1px solid #dcdfe6;
    border-radius: 100%;
    width: 20px;
    height: 20px;
    background-color: #fff;
    position: absolute;
    right: 12px;
    top: 14px;
    cursor: pointer;
    display: inline-block;
    box-sizing: border-box;
    .radio-check {
      display: none;
    }
  }
  .mkt-radio-icon-disabled {
    background-color: #eeeeee;
  }

  .card-top {
    margin-bottom: 8px;
    .mkt-recommended {
      display: inline-block;
      background-color: $orange;
      border-radius: 10px 0 10px 0;
      padding: 4px 8px;

      font-size: 14px;
      line-height: 20px;
      color: #fff;
      margin-top: -1px;
      margin-left: -1px;
    }
  }

  .card-content {
    flex: 1;
    padding: 0 12px 8px;
    .mkt-tag {
      display: inline-block;
      padding: 6px 8px;
      margin: 8px 0 0;
      color: "#212121";
    }
  }

  .card-middle {
    height: 16px;
    position: relative;
    &::before {
      content: "";
      display: block;
      width: 10px;
      position: absolute;
      top: 0;
      bottom: 0;
      left: -1px;
      background: radial-gradient(#f5f5f5 0, #f5f5f5 8px, transparent 9px);
      background-size: 16px 16px;
      background-position: right 0 top 0;
    }

    &::after {
      content: "";
      display: block;
      width: 10px;
      position: absolute;
      top: 0;
      bottom: 0;
      right: -1px;
      background: radial-gradient(#f5f5f5 0, #f5f5f5 8px, transparent 9px);
      background-size: 16px 16px;
      background-position: left 0 top 0;
    }

    .decorator {
      height: 1px;
      border: 1px dashed #e0e0e0;
      position: absolute;
      top: 7px;
      left: 12px;
      right: 12px;
    }
  }

  .card-bottom {
    padding: 8px 12px 8px;
    .mkt-coupon-reason {
      font-size: 14px;
      border-radius: 4px;
      padding: 6px 8px;
      margin: 0 0 8px;
      background-color: #ffeeee;
      color: #212121;
    }
  }

  .card-bottom-inner {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .klk-collapse-item {
    border: none;
  }
  ::v-deep .klk-collapse-item-header-inner {
    align-items: center;
  }

  ::v-deep .klk-collapse-item-header {
    margin-bottom: 12px;
  }

  .tnc {
    .tnc-title {
      margin: 0;
      font-size: 16px;
      line-height: 22px;
      color: #999999;
    }
    .tnc-desc {
      margin: 0;
      font-size: 16px;
      line-height: 22px;
      color: #757575;
    }
    .tnc-bottom-link {
      margin-top: 8px;

      font-style: normal;
      font-weight: 400;
      font-size: 16px;
      line-height: 144%;
      display: flex;
      align-items: center;
      text-decoration-line: underline;
      color: #999999;
      cursor: pointer;
    }
  }

  .card-title {
    color: $orange;
    display: -webkit-box;
    max-height: 64px;
    margin: 0 auto;
    font-size: 18px;
    line-height: 32px;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    padding-right: 22px;
    word-break: break-all;
    font-weight: 700;
  }
  .payment-logo {
    height: 22px;
    vertical-align: middle;
    margin-right: 5px;
  }

  .card-description {
    display: -webkit-box;
    max-height: 60px;
    margin: 0 auto;
    font-size: 14px;
    line-height: 20px;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    word-break: break-all;
    color: #212121;
  }
  .precent-exchange-discount-notice-text{
    font-size: 14px;
    line-height: 150%;
    font-weight: bold;
    color: #212121;
  }
  .card-end-time {
    font-size: 14px;
    line-height: 20px;
    color: #757575;
  }

  .coupon_enbale_stacking_others {
    color: #212121;
    font-size: 14px;
  }

  .coupon_disable_stacking_others {
    @include font-body-s-regular();
    color: $color-error;
  }

  .card-code {
    font-size: 14px;
    line-height: 20px;
    color: #757575;
  }
  .bottom-link {
    margin-top: 10px;
    font-style: normal;
    font-weight: 500;
    font-size: 14px;
    line-height: 144%;
    text-decoration-line: underline;
    color: #212121;
    cursor: pointer;
  }

  &.mkt-disabled {
    * {
      color: #b2b2b2;
    }
  }
}

.mkt-coupon-card.is-chosen {
  border: 1px solid $orange;

  .mkt-radio-icon {
    border-color: $orange;
    background: $orange;
    .radio-check {
      display: inline-block;
      position: absolute;
      z-index: 1;
      top: 50%;
      left: 50%;
      margin-left: -6px;
      margin-top: -7px;
    }
  }
  .card-middle::before {
    border-radius: 0px 8px 8px 0px;
    border-right: 1px solid $orange;
    border-top: 1px solid $orange;
    border-bottom: 1px solid $orange;
    border-left: 1px solid $backgroundGray;
  }
  .card-middle::after {
    border-radius: 8px 0px 0px 8px;
    border-left: 1px solid $orange;
    border-top: 1px solid $orange;
    border-bottom: 1px solid $orange;
    border-right: 1px solid $backgroundGray;
  }
}
// desktop
.mkt-coupon-card.mkt-desktop {
  max-width: 49%;
  min-width: 49%;
  width: 49%;

  .mkt-coupon-reason {
    min-height: 54px;
    // max-height: 54px;
  }
}
// mobile
.mkt-coupon-card.mkt-mobile {
  max-width: initial;
  min-width: initial;
  width: 100%;
  margin-bottom: 12px;

  .card-description {
    display: -webkit-box;
    margin: 0 auto;
    font-size: 14px;
    line-height: 20px;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    word-break: break-all;
  }

  .card-bottom {
    ::v-deep .klk-collapse-item-header-inner {
      margin: 0;
    }
    ::v-deep .klk-collapse-item-header-inner svg {
      width: 16px;
      height: 16px;
    }
  }
  ::v-deep .klk-collapse-item-main .klk-collapse-item-content-inner {
    padding: 0;
  }
  .card-middle {
    &::before {
      background: radial-gradient(#F5F5F5 0, #F5F5F5 8px, transparent 8px);
      background-size: 16px 16px;
      background-position: right 0 top 0;
    }
    &::after {
      background: radial-gradient(#F5F5F5 0, #F5F5F5 8px, transparent 8px);
      background-size: 16px 16px;
      background-position: left 0 top 0;
    }
  }
}
</style>
