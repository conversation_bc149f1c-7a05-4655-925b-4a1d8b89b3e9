<template>
  <div>
    <klk-coupon-poptip
      placement="bottom"
      append-to-body
      :z-index="9999"
    >
      <klk-coupon-icon
        type="icon_tips_tips"
        size="18"
        @click.stop="handleClick"
      />
      <div
        slot="content"
        class="tnc"
        :data-spm-module="`PromoCodeDetail?oid=couponcode_${encodeURIComponent(couponItem.code)}`"
      >
        <p class="tnc-title">
          {{ $t("44205") }}
        </p>
        <p class="tnc-desc">
          {{ content }}
        </p>
      </div>
    </klk-coupon-poptip>
  </div>
</template>

<script>
export default {
  props: {
    content: {
      type: String,
    },

    couponItem: {
      type: Object
    }
  },
  methods: {
    handleClick() {}
  }
};
</script>

<style lang="scss" scoped>
.klk-poptip-popper-inner {
  min-width: 343px;
  max-width: 343px;
  margin: 0 !important;
  padding: 12px 16px !important;
  .tnc {
    .tnc-title {
      margin: 0;
      font-size: 16px;
      line-height: 22px;
      color: #999999;
    }
    .tnc-desc {
      margin: 0;
      font-size: 16px;
      line-height: 22px;
      color: #212121;
    }
  }
}
</style>
