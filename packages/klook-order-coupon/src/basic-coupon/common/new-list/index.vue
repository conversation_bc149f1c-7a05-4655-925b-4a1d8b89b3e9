<template>
  <div
    role="radiogroup"
    :class="[
      {
        'mkt-mobile': isMobile,
        'mkt-desktop': !isMobile,
        'mkt-list-container-new': isMergedPaymentPage
      },
      'mkt-list-container',
    ]"
  >
    <!-- common -->
    <div
      class="list-contain mkt-platformCouponListBox"
      v-if="couponList.length && couponData.pageType == 'settlement'"
    >
      <div :class="[{ 'mkt-mobile-title': isMobile }, 'title']">
        <div class="title-left">
          <div class="icon-line"></div>
          <span>{{ $t("73198") }}</span>
        </div>
        <span v-if="!isMergedPaymentPage" class="select"
          >{{ $t("73199") }}: {{ selectedNum("common_coupon") }}/1</span
        >
      </div>
      <card
        v-for="coupon in couponView"
        :key="coupon.coupon_id"
        :coupon-item="coupon"
        :is-chosen="chosen(coupon.code, coupon.finance_type)"
        :is-recommended="couponData.bestCouponCode === coupon.code"
        :is-mobile="isMobile"
        v-bind="$attrs"
        v-on="$listeners"
        @click-coupon="(e) => handleClickCoupon(e, coupon.finance_type)"
      />
      <div class="view" v-if="viewRule" @click="showAllCoupon">
        {{ $t("73201") }}
        <klk-coupon-icon
          class="down-icon"
          type="icon_navigation_chevron_down"
          color="#437DFF"
          :size="14"
        ></klk-coupon-icon>
      </div>
    </div>
    <!-- payment -->
    <div
      class="list-contain mkt-paymentCouponListBox"
      v-if="paymentCouponList.length"
    >
      <div
        :class="[
          { 'mkt-mobile-title': isMobile },
          'mkt-title',
          'paymentCouponListTitle',
        ]"
      >
        <div class="title">
          <div class="title-left">
            <template v-if="couponData.pageType == 'settlement'">
              <div class="icon-line"></div>
              <span>{{ $t("73202") }}</span>
            </template>
          </div>
          <span v-if="!isMergedPaymentPage" class="select"
            >{{
              couponData.pageType == "settlement" ? $t("73200") : $t("73199")
            }}: {{ selectedNum("payment_coupon") }}/1</span
          >
        </div>
        <div class="tip" v-if="!isMergedPaymentPage && couponData.pageType == 'settlement'">
          {{ $t("73203") }}
        </div>
      </div>
      <card
        v-for="coupon in paymentCouponList"
        :key="coupon.coupon_id"
        :coupon-item="coupon"
        :is-mobile="isMobile"
        :is-chosen="chosen(coupon.code, coupon.finance_type)"
        v-bind="$attrs"
        v-on="$listeners"
        @click-coupon="(e) => handleClickCoupon(e, coupon.finance_type)"
      />
    </div>
    <div v-show="!hasUsableCoupons" class="empty-class">
      <img
        class="empty-img"
        src="https://res.klook.com/image/upload/v1652664870/paymentdiscounts/ill_spot_hero_promo_code.png"
        alt=""
        v-if="emptyImgShow"
      />
      {{ $t("30123") }}
    </div>
  </div>
</template>

<script>
import Card from "./card.vue";

export default {
  name: "MktBasicCouponList",

  components: {
    Card,
  },

  inject: ["couponData"],

  props: {
    isMergedPaymentPage: {
      type: Boolean,
      default: false
    },
    chosenCouponData: {
      type: Object,
      default: () => {
        return {
          common_coupon: {},
          payment_coupon: {},
        };
      },
    },
    isMobile: {
      type: Boolean,
      default: false,
    },
    couponList: {
      type: Array,
      default: () => [],
    },
    paymentCouponList: {
      type: Array,
      default: () => [],
    },
  },
  computed: {
    common_coupon() {
      return this.chosenCouponData?.common_coupon || {}
    },
    couponView() {
      // 选中的平台优惠券在两个之后的展示所有platform优惠券的函数
      // 没有支付类优惠券直接展示
      if (!this.paymentCouponList.length || this.showAll || (this.couponList || []).findIndex((couponItem) => couponItem.code == this.common_coupon.code) > 1) {
        return this.couponList;
      }
      return this.couponList.filter((v, index) => index < this.couponViewNum);
    },
    viewRule() {
      return (
        this.couponList.length > this.couponView.length &&
        this.paymentCouponList.length &&
        !this.showAll
      );
    },
    hasUsableCoupons() {
      return this.couponList.length || this.paymentCouponList.length;
    },
    emptyImgShow() {
      // 收银台 ｜｜ 非收银台mobileWeb/合并 显示
      return (
        this.couponData.pageType === "cashRegister" ||
        (this.couponData.pageType !== "cashRegister" && (this.isMobile || this.isMergedPaymentPage))
      );
    },
  },
  data() {
    return {
      couponViewNum: 2,
      couponData: this.couponData,
      showAll: false,
    };
  },
  methods: {
    showAllCoupon() {
      this.showAll = true;
    },
    // 当前优惠券有没有选中
    chosen(code, type) {
      return this.chosenCouponData[type]?.code === code;
    },
    selectedNum(type) {
      return Number(!!this.chosenCouponData[type]?.code);
    },
    /**
     * @description: 选择优惠券
     * @params e 优惠券信息
     * @params type common_coupon ｜ payment_coupon
     */
    handleClickCoupon(e, type) {
      const newChosenCode = this.chosen(e.code, type) ? "" : e;
      if (newChosenCode) {
        this.$emit("choose-coupon", newChosenCode);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.title {
  width: 100%;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;

  .title-left {
    display: flex;
    flex-direction: row;
    align-items: center;
    font-size: 14px;
    font-weight: 600;
    line-height: 20px;
    letter-spacing: 0em;
    text-align: left;
    color: #212121;

    .icon-line {
      display: inline-block;
      height: 14px;
      width: 4px;
      left: 0px;
      top: 3px;
      border-radius: 3px;
      background: #ff5b00;
      margin-right: 12px;
    }
  }
  .select {
    font-style: normal;
    font-weight: 400;
    font-size: 12px;
    line-height: 20px;
    color: #757575;
  }
}

.mkt-list-container {
  padding-bottom: 12px;
  .view {
    cursor: pointer;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;

    font-style: normal;
    font-weight: bold;
    font-size: 12px;
    line-height: 17px;
    color: #437dff;
    .down-icon {
      margin-left: 8px;
    }
  }
}
.tip {
  font-style: normal;
  font-weight: 400;
  font-size: 12px;
  line-height: 17px;
  color: #757575;
}
.mkt-list-container.mkt-desktop {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  gap: 12px;
  max-height: 392px;
  overflow-y: scroll;
  .list-contain {
    width: 100%;
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    gap: 12px;
  }
}

.mkt-list-container-new.mkt-desktop {
  max-height: none;
  overflow-y: visible;
}

.mkt-list-container.mkt-mobile {
  display: flex;
  flex-direction: column;
  flex-wrap: wrap;
}
.mkt-title {
  width: 100%;
}
.mkt-mobile-title {
  margin-bottom: 12px;
}
.empty-class {
  display: flex;
  flex-direction: column;
  width: 100%;
  align-items: center;
  justify-content: center;
  .empty-img {
    width: 150px;
    height: 150px;
    background-color: #f5f5f5;
    margin-bottom: 24px;
    font-size: 16px;
  }
}
.mkt-mobile {
  .empty-class {
    height: 500px;
  }
}
</style>
