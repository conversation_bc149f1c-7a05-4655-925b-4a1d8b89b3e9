<template>
  <div
    role="radiogroup"
    :class="[
      {
        'mkt-mobile': isMobile,
        'mkt-desktop': !isMobile,
        'mkt-list-container-new': isMergedPaymentPage
      },
      'mkt-list-container',
    ]"
  >
    <!-- common -->
    <card
      v-for="coupon in couponListArr"
      :key="coupon.coupon_id"
      :coupon-item="coupon"
      :is-mobile="isMobile"
      :disabled="true"
      v-on="$listeners"
    />
    <div
      v-show="!couponListArr.length"
      class="empty-class"
    >
    <img class="empty-img" src="https://res.klook.com/image/upload/v1652664870/paymentdiscounts/ill_spot_hero_promo_code.png" alt="" v-if="emptyImgShow">
    {{ $t("30123") }}
    </div>
  </div>
</template>

<script>
import Card from './card.vue';

export default {
  name: 'MktBasicCouponList',

  components: {
    Card,
  },

  inject: ['couponData'],

  props: {
    isMergedPaymentPage: {
      type: Boolean,
      default: false
    },
    isMobile: {
      type: Boolean,
      default: false,
    },
    couponList: {
      type: Array,
      default: () => [],
    }
  },
  computed:{
    couponListArr(){
      return this.couponList?.filter(item => item.coupon_id) || []
    },
    emptyImgShow(){
      // 收银台 ｜｜ 非收银台mobileWeb/合并 显示
      return this.couponData.pageType === 'cashRegister' || (this.couponData.pageType !== 'cashRegister' && (this.isMobile || this.isMergedPaymentPage))
    }
  },
  data() {
    return {
      couponData: this.couponData
    };
  },

  methods: {},
};
</script>

<style lang="scss" scoped>
.mkt-list-container {
  padding-bottom: 12px;
}
.mkt-list-container.mkt-desktop {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  gap: 12px;
  max-height: 392px;
  overflow-y: scroll;
}
.mkt-list-container-new.mkt-desktop {
  max-height: none;
  overflow-y: visible;
}
.mkt-list-container.mkt-mobile {
  display: flex;
  flex-direction: column;
  flex-wrap: wrap;
}
.empty-class {
  display: flex;
  flex-direction: column;
  width: 100%;
  align-items: center;
  justify-content: center;
  .empty-img {
    width: 150px;
    height: 150px;
    background-color: #f5f5f5;
    margin-bottom: 24px;
    font-size: 16px;
  }
}
</style>
