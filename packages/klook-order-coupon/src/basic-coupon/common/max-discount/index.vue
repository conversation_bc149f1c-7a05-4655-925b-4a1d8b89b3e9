<template>
  <div v-if="maxDiscount" class="max-discount-wrapper">
    <div class="max-discount-info">
      <IconSmilingFace
        v-if="isBestCouponCombo"
        theme="outline"
        size="16"
        fill="#212121"
      />
      <IconWarnCircle v-else theme="outline" size="16" fill="#212121" />
      <div :class="`max-discount-text max-discount-text--${isMobile ? 'mobile' : 'desktop'}`">
        {{ isBestCouponCombo ? $t("173934") : $t("173935") }}
      </div>
    </div>

    <klk-coupon-button
      v-if="!isBestCouponCombo"
      class="max-discount-btn"
      size="mini"
      type="white-filled"
      :data-spm-module="`Use_Max_Discount?ext=${JSON.stringify(spmExt)}`"
      data-spm-virtual-item="__virtual"
      @click="handleMaxDiscClick"
    >
      {{ $t("173936") }}
    </klk-coupon-button>
  </div>
</template>

<script>
import { IconSmilingFace, IconWarnCircle } from "@klook/klook-icons";

export default {
  name: "max-discount-info",
  components: {
    IconSmilingFace,
    IconWarnCircle,
  },
  props: {
    spmExt: {
      type: Object,
      default: () => ({})
    },
    isMobile: {
      type: Boolean,
      default: true,
    },
    couponList: {
      type: Array,
      default: () => [],
    },
    paymentCouponList: {
      type: Array,
      default: () => [],
    },
    maxDiscount: {
      type: Object,
      default: () => ({}),
    },
    chosenCouponData: {
      type: Object,
      default: () => {
        return {
          common_coupon: {},
          payment_coupon: {},
        };
      },
    },
  },
  computed: {
    isBestCouponCombo() {
      const { common_coupon = {}, payment_coupon = {} } = this.chosenCouponData;

      if (this.maxDiscount) {
        return this.maxDiscount.combo_list.every((couponItem) => {
          return [common_coupon, payment_coupon].some((selectCodeItem) => {
            return selectCodeItem && couponItem.code == selectCodeItem.code;
          });
        });
      }
      return false
    },
  },
  methods: {
    getComboCouponInfo(combo_list) {
      const common_coupon =
        this.couponList.find((item) => {
          return combo_list.find(
            (cmboCouponItem) => cmboCouponItem.code == item.code
          );
        }) || {};
      const payment_coupon =
        this.paymentCouponList.find((item) => {
          return combo_list.find(
            (cmboCouponItem) => cmboCouponItem.code == item.code
          );
        }) || {};

      return { common_coupon, payment_coupon };
    },
    handleMaxDiscClick() {
      const { common_coupon, payment_coupon } = this.getComboCouponInfo(this.maxDiscount.combo_list);
      this.$emit("combo-change", { common_coupon, payment_coupon });
    },
  }
};
</script>

<style lang="scss" scoped>
.max-discount-wrapper {
  margin-bottom: 16px;
  padding: 10px;
  border-radius: $radius-m;
  background-color: $color-brand-primary-light;
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 10px;
}

.max-discount-info {
  display: flex;
  align-items: center;

  .max-discount-text {
    color: $color-text-primary;
    margin-left: 4px;

    &--mobile {
      @include font-body-s-regular();
    }

    &--desktop {
      @include font-body-m-regular();
    }
  }
}

.max-discount-btn {
  height: 28px;
  border: 1px solid $color-border-active;
  text-align: center;
}
</style>