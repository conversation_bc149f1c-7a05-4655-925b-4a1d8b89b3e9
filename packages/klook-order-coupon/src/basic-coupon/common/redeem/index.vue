<template>
  <div class="redeem-wrapper">
    <klk-coupon-form :model="model" ref="form">
      <klk-coupon-form-item
        :class="[
          {
            'mkt-redeem-form-item-mobile': isMobile,
            'mkt-redeem-form-item-desktop': !isMobile
          }
        ]"
        prop="inputCouponCode"
        :rules="couponCodeRule"
      >
        <div
          :class="[
            { 'mkt-mobile': isMobile, 'mkt-desktop': !isMobile },
            'mkt-input-button'
          ]"
        >
          <klk-coupon-input
            class="redeem-input"
            size="small"
            v-model.trim="model.inputCouponCode"
            :placeholder="$t('30120')"
          >
            <klk-coupon-button
              slot="append"
              id="redeem"
              class="redeem-btn"
              size="small"
              :disabled="disabled"
              type="primary"
              @click="handleConfirmRedeem"
              :data-spm-module="'Redeem'"
              data-spm-virtual-item="__virtual"
            >
              {{ $t("30121") }}
            </klk-coupon-button>
          </klk-coupon-input>
        </div>
      </klk-coupon-form-item>
    </klk-coupon-form>
  </div>
</template>

<script>
import BasicMixin from "../../../mixins/basic-mixin";
export default {
  name: "mkt-basic-coupon-redeem",
  mixins: [BasicMixin],

  components: {},

  props: {
    isMobile: {
      type: Boolean,
      default: false
    },
    disabled: {
      type: Boolean,
      default: false
    },
    inputCouponCode: {
      type: String,
      default: ""
    }
  },

  data() {
    return {
      model: {
        inputCouponCode: this.inputCouponCode
      },
      couponCodeRule: {
        required: false,
        message: this.formmatTextId(['13274', 'pay.coupon_code.required', 'promo_code.redeem.placeholder']),
        trigger: "change"
      }
    };
  },

  methods: {
    clearInputCouponCode() {
      this.model.inputCouponCode = "";
    },
    validate() {
      return this.$refs["form"].validate();
    },
    resetValidate() {
      const form = this.$refs["form"];
      form && form.resetFields();
    },
    async handleConfirmRedeem() {
      let code = this.model.inputCouponCode;
      if (!code.length) {
        this.couponCodeRule.required = true;
        const isInputCouponCodeValid = await this.validate().catch(() => {});

        setTimeout(() => {
          this.resetValidate();
          this.couponCodeRule.required = false;
        }, 2000);

        if (!isInputCouponCodeValid) {
          return;
        }
      }
      this.$emit("confirm-redeem", this.model.inputCouponCode);
    }
  }
};
</script>

<style lang="scss" scoped>
.redeem-wrapper {
  padding: 0 0 0;
  ::v-deep .mkt-redeem-form-item-mobile {
    margin-bottom: 12px;
  }
  ::v-deep .mkt-redeem-form-item-desktop {
    margin-bottom: 8px;
  }
  ::v-deep .klk-input-inner {
    border-right: none;
  }
  .redeem-btn {
    text-align: center;
    border: 1px solid #ff5722;
    border-left: none;
  }
}
.mkt-input-button {
  display: flex;
  ::v-deep .klk-input {
    flex: 1;
  }
}
// desktop
.mkt-desktop.mkt-input-button {
  max-width: 353px;
}
// mobile
.mkt-mobile.mkt-input-button {
  max-width: initial;
  width: 100%;
}
</style>
