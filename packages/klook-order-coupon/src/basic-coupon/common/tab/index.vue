<template>
  <klk-coupon-tabs :class="[{ 'mkt-mobile': isMobile, 'mkt-desktop': !isMobile }, 'mkt-tabs']" v-model="curTab" @change="handleChange">
    <klk-coupon-tab-pane
      :label="availableTitle"
      v-bind="halfProps"
      :name="0"
    >
      <slot name="available" />
    </klk-coupon-tab-pane>
    <klk-coupon-tab-pane
      :label="unavailableTitle"
      v-bind="halfProps"
      :name="1"
    >
      <slot name="unavailable" />
    </klk-coupon-tab-pane>
  </klk-coupon-tabs>
</template>

<script>
export default {
  name: 'MktBasicCouponTab',
  props: {
    isMobile: {
      type: Boolean,
      default: false
    },
    availableLength: {
      type: Number
    },
    unavailableLength: {
      type: Number
    },
    tabsStatus: {
      type: Number,
      default: 0
    }
  },
  data(){
    return {
      curTab: 0
    }
  },
  computed: {
    halfProps() {
      if (this.isMobile) {
        return {
          width: '50%',
          'max-width': '50%'
        };
      } else {
        return {};
      }
    },
    availableTitle() {
      return this.$t('73196', {
        x: this.availableLength
      });
    },

    unavailableTitle() {
      return this.$t('73197', {
        x: this.unavailableLength
      });
    }
  },
  watch: {
    tabsStatus(val){
      if (val!=this.curTab) {
        this.curTab = val
      }
    },
  },
  methods: {
    handleChange(e){
      this.$emit('tabs-changes', e)
    }
  }
};
</script>

<style lang="scss" scoped>
.mkt-tabs ::v-deep  .klk-tabs-items{
  border-bottom: 1px solid #e0e0e0;
  .klk-tabs-item {
    position: relative;
    top: 1px;
    border-bottom-width: 1px;
  }
  .klk-tabs-item-active {
    border-bottom-width: 2px;
    color: $color-brand-primary;
  }
  .klk-tabs-item-disabled {
    border-bottom-width: 1px;
  }
}
.mkt-tabs.mkt-mobile ::v-deep  .klk-tabs-items {
  .klk-tabs-item {
    margin-right: 0;
  }
}
</style>
