<template>
  <klk-coupon-tabs :class="['mkt-desktop', 'mkt-tabs', 'mkt-coupon-type-tab-box']" v-model="curTab" @change="handleChange">
    <klk-coupon-tab-pane
      v-if="couponList.length"
      :label="platformCouponTitle"
      name="platformCoupon"
    >
    </klk-coupon-tab-pane>
    <klk-coupon-tab-pane
      v-if="paymentCouponList.length"
      :label="paymentCouponTitle"
      name="paymentCoupon"
    >
    </klk-coupon-tab-pane>
  </klk-coupon-tabs>
</template>

<script>
export default {
  name: 'MktCouponTypeTabs',
  props: {
    couponTypeTab: {
      type: String,
      default: 'platformCoupon'
    },
    couponList: {
      type: Array,
      default: () => [],
    },
    paymentCouponList: {
      type: Array,
      default: () => [],
    },
    bestcombo: {
      type: Array,
      default: () => [],
    },
    chosenCouponData: {
      type: Object,
      default: () => {
        return {
          common_coupon: {},
          payment_coupon: {},
        };
      },
    },
  },
  data(){
    return {
      curTab: 'platformCoupon'
    }
  },
  computed: {
    platformCouponTitle() {
      const {common_coupon} = this.chosenCouponData
      const selectLen = common_coupon && common_coupon.code?1:0
      const len = this.couponList.length
      return `Platform(${selectLen}/${len})`;
    },
    paymentCouponTitle() {
      const {payment_coupon} = this.chosenCouponData
      const selectLen = payment_coupon && payment_coupon.code?1:0
      const len = this.paymentCouponList.length
      return `Payment(${selectLen}/${len})`;
    }
  },
  watch: {
    couponTypeTab(val){
      if (val!=this.curTab) {
        this.curTab = val
      }
    },
  },
  methods: {
    handleChange(e){
      this.$emit('coupon-type-tabs-changes', e)
    }
  }
};
</script>

<style lang="scss" scoped>
.mkt-coupon-type-tab-box{
  position: fixed;
  top: 48px;
  left: 0;
  width: 100%;
  z-index: 1;
  ::v-deep .klk-tabs-header{
  background-color: white;
}
}
.mkt-tabs ::v-deep  .klk-tabs-items{
  border-bottom: 1px solid #e0e0e0;
  .klk-tabs-item {
    position: relative;
    top: 1px;
    border-bottom-width: 1px;
  }
  .klk-tabs-item-active {
    border-bottom-width: 1px;
    color: #212121;
  }
  .klk-tabs-item-disabled {
    border-bottom-width: 1px;
  }
}


</style>
