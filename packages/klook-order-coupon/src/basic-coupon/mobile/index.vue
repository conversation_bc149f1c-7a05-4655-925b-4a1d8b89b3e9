<template>
  <div
    :class="[isNewStyle ? 'mkt-coupon-wrapper-new' : 'mkt-coupon-wrapper', 'summary_row', 'prome_code']"
    :data-spm-module="panelModule"
  >
    <trigger
      @open-modal="open"
      :is-merged-payment-page="isMergedPaymentPage"
      :isNewStyle="isNewStyle"
      :is-best-coupon-combo="isBestCouponCombo"
      :has-usable-coupons="hasUsableCoupons"
      :usable="usable"
      :bestcombo="bestcombo"
      :usablePaymentCoupon="usablePaymentCoupon"
      :unusable="unusable"
      :chosen-coupon-data="chosenCouponData"
      v-bind="$attrs"
      v-on="$listeners"
    >
      <div slot="init-popover">
        <slot name="init-popover"></slot>
      </div>
    </trigger>

    <div v-if="!isMergedPaymentPage">
      <!-- 自定义提示 -->
      <div class="mkt-poptip" v-show="popoverShow">
        <klk-coupon-icon
          class="mkt-poptip-close"
          type="icon_navigation_close"
          size="18"
          @click="closePoptip"
        />
        <slot name="init-popover"></slot>
      </div>
      <!-- 弃用展示payment_restrict_desc字段，改展示payment_restrict_tips -->
      <div v-if="couponData.payment_restrict_tips" class="payment_restrict_tips_box">
        <div v-html="couponData.payment_restrict_tips"></div>
      </div>
      <div v-if="isBestCouponCombo" class="is_applied_best_combo">{{ $t('91894') }}</div>
    </div>
    <modal
      :visible="openDialog"
      v-bind="$attrs"
      v-on="$listeners"
      v-loading="loading"
      :is-merged-payment-page="isMergedPaymentPage"
      :usable="usable"
      :bestcombo="bestcombo"
      :max-discount="maxDiscount"
      :usablePaymentCoupon="usablePaymentCoupon"
      :unusable="unusable"
      :chosen-coupon-data="chosenCouponData"
      :has-usable-coupons="hasUsableCoupons"
      @close-modal="close"
      @choose-coupon="chooseCoupon"
      @combo-change="comboChange"
    />
  </div>
</template>

<script>
import Trigger from "./trigger.vue";
import Modal from "./modal.vue";
import BasicMixin from "../../mixins/basic-mixin";

export default {
  name: "mkt-basic-coupon-mobile",
  components: {
    Trigger,
    Modal
  },

  inject: ["couponData"],
  mixins: [BasicMixin],

  props: {
    isMergedPaymentPage: {
      type: Boolean,
      default: false
    },
    isNewStyle: {
      type: Boolean,
      default: false,
    },
    usable: {
      type: Array,
      default: () => []
    },
    bestcombo: {
      type: Array,
      default: () => []
    },
    maxDiscount: {
      type: Object,
      default: () => ({})
    },
    usablePaymentCoupon: {
      type: Array,
      default: []
    },
    unusable: {
      type: Array,
      default: () => []
    },
    loading: {
      type: Boolean
    }
  },
  data() {
    return {
      openDialog: false,
      openPopTip: true,
      chosenCouponData: {
        common_coupon: {},
        payment_coupon: {}
      }
    };
  },
  mounted(){
  },

  computed: {
    panelModule() {
      const ext = { status: this.hasUsableCoupons ? 'available' : 'no available'};
      return `PromoCodePanel?ext=${JSON.stringify(ext)}`;
    },
    hasUsableCoupons() {
      return Boolean(this.usable.length || this.usablePaymentCoupon.length);
    },
    popoverShow() {
      return this.couponData.showPopTip && this.openPopTip;
    },
    isBestCouponCombo() {
      const bestlist = this.isMergedPaymentPage ? [this.maxDiscount].filter(Boolean) : this.bestcombo;
      const { common_coupon = {}, payment_coupon = {} } = this.chosenCouponData;

      if (bestlist && bestlist.length) {
        const { combo_list = [] } = bestlist[0];
        return combo_list.every((couponItem) => {
          return [common_coupon, payment_coupon].some((selectCodeItem) => {
            return (
              selectCodeItem && couponItem.code == selectCodeItem.code
            );
          });
        });
      }

      return false;
    },
  },
  watch:{
    'couponData.chosenCoupon': {
      handler(newVal, oldVal) {
        this.chosenCouponData = newVal ? this.$lodash.cloneDeep(newVal) : {
          common_coupon: {},
          payment_coupon: {}
        }
      },
      immediate: true,
      deep: true
    },
  },


  methods: {
    comboChange(comboData){
      const {common_coupon,payment_coupon} = comboData
      this.chosenCouponData = {
        common_coupon,
        payment_coupon
      }
      this.chosenCouponData = this.$lodash.cloneDeep(this.stacking_with_common_coupon(
        common_coupon,
        this.chosenCouponData
      ));
      this.chosenCouponData = this.$lodash.cloneDeep(this.stacking_with_common_coupon(
        payment_coupon,
        this.chosenCouponData
      ));
    },
    chooseCoupon(e){
      this.chosenCouponData[e.finance_type] = e
      this.chosenCouponData = this.$lodash.cloneDeep(this.stacking_with_common_coupon(e, this.chosenCouponData))
    },
    async open(couponType) {
      this.fixScrollY = document.scrollingElement.scrollTop;
      document.body.style.top = -this.fixScrollY + "px";
      document.querySelector("body").classList.add("noscroll");
      this.chosenCouponData = this.$lodash.cloneDeep(this.couponData.chosenCoupon)
      this.openDialog = true;
      let coupon = this.chosenCouponData?.common_coupon
      if (couponType == 'paymentCoupon') {
        coupon = this.chosenCouponData?.payment_coupon
      }
      this.$emit("open-modal", coupon, couponType);
      this.setShowAll(false)
    },
    close() {
      document.querySelector("body").classList.remove("noscroll");
      document.body.scrollTop = this.fixScrollY;
      document.scrollingElement.scrollTop = this.fixScrollY;
      this.openDialog = false;
    },
    closePoptip() {
      this.openPopTip = false;
    }
  }
};
</script>

<style lang="scss" scoped>
.mkt-coupon-wrapper {
  padding: 12px 0;
  ::v-deep .mkt-poptip {
    width: 100%;
    margin-top: 18px;
    background: #fff4ed;
    position: relative;
    padding: 8px 36px 8px 12px;
    .mkt-poptip-close {
      cursor: pointer;
      position: absolute;
      top: 8px;
      right: 8px;
    }
    &::after {
      content: "";
      border: 9px solid transparent;
      position: absolute;
      border-bottom-color: #fff4ed;
      top: -18px;
      left: 18px;
    }
  }
  .summary_error_msg {
    margin: 8px 0 0;
    padding: 8px;
    background: #FCF3DE;
    white-space: pre-line;
    font-size: 14px;
    line-height: 150%;
    border-radius: 8px;
  }
}
.mkt-coupon-wrapper-new {
  padding: 0;
  ::v-deep .mkt-poptip {
    width: 100%;
    margin-top: 18px;
    background: #fff4ed;
    position: relative;
    padding: 8px 36px 8px 12px;
    .mkt-poptip-close {
      cursor: pointer;
      position: absolute;
      top: 8px;
      right: 8px;
    }
    &::after {
      content: "";
      border: 9px solid transparent;
      position: absolute;
      border-bottom-color: #fff4ed;
      top: -18px;
      left: 18px;
    }
  }
  .summary_error_msg_new {
    margin: 8px 0 0;
    padding: 12px 16px;
    background-color: #f5f5f5;
    white-space: pre-line;
    border-radius: 12px;
  }

}
.payment_restrict_tips_box{
  margin: 8px 0 0;
  background: #FCF3DE;
  border-radius: 8px;
  padding: 8px;
  font-size: 14px;
  line-height: 150%;
}
.is_applied_best_combo {
    margin-top: 8px;
    color: #757575;
    font-size: 14px;
    line-height: 150%;
  }
</style>
<style>
body.noscroll {
  position: fixed;
  overflow-y: hidden;
}
</style>
