<template>
  <div
    v-show="visible"
    class="coupons_page"
    :class="{ 'coupons_page__new': isMergedPaymentPage && visibleBestCouponSuggestNotice && maxDiscount }"
    id="mkt-coupon-list-container"
    ref="CouponPage"
    >
    <modal-header
      v-bind="{ title: titleText() }"
      v-on="$listeners"
      @click-back="handleBack"
    />
    <div class="coupons_page__main j-coupons_main">
      <redeem
        v-bind="$attrs"
        v-on="$listeners"
        :is-mobile="isMobile"
      />
      <!-- 最佳优惠推荐提示 -->
      <Sugguest
        v-show="!isMergedPaymentPage && tabsStatus == 0 && visibleBestCouponSuggestNotice"
        v-on="$listeners"
        v-bind="$attrs"
        :is-mobile="isMobile"
        :bestcombo="bestcombo"
        :coupon-list="usable"
        :payment-coupon-list="usablePaymentCoupon"
        :chosen-coupon-data="chosenCouponData"
      />
      <tab
        ref="basicTabs"
        v-bind="$attrs"
        v-on="$listeners"
        :is-mobile="isMobile"
        :tabsStatus="tabsStatus"
        :available-length="usableLength"
        :unavailable-length="unusable.length"
      >
        <list
          slot="available"
          v-bind="$attrs"
          v-on="$listeners"
          :is-merged-payment-page="isMergedPaymentPage"
          :is-mobile="isMobile"
          :coupon-list="usable"
          :payment-coupon-list="usablePaymentCoupon"
          :chosen-coupon-data="chosenCouponData"
        />
        <unusableList
          slot="unavailable"
          v-bind="$attrs"
          v-on="$listeners"
          :is-merged-payment-page="isMergedPaymentPage"
          :is-mobile="isMobile"
          :coupon-list="unusable"
        />
      </tab>
    </div>
    <modal-footer
      v-on="$listeners"
      v-bind="$attrs"
      :is-merged-payment-page="isMergedPaymentPage"
      :max-discount="maxDiscount"
      :visible-best-coupon-suggest-notice="visibleBestCouponSuggestNotice"
      :usable="usable"
      :usable-payment-coupon="usablePaymentCoupon"
      :has-usable-coupons="hasUsableCoupons"
      :chosen-coupon-data="chosenCouponData"
      @click-close="handleCloseModal"
      @click-apply="handleApply"
    />
  </div>
</template>

<script>
import ModalHeader from './modal-header.vue';
import ModalFooter from './modal-footer.vue';
import List from '../common/new-list/index.vue';
import unusableList from "../common/new-list/unusableList.vue";
import Redeem from '../common/redeem/index.vue';
import Tab from '../common/tab/index.vue';
import Sugguest from "../common/suggest/index.vue";
import BasicMixin from "../../mixins/basic-mixin"; 

export default {
  name: 'mkt-basic-coupon-modal',
  inject: ["couponData"],
  mixins: [BasicMixin],
  components: {
    ModalHeader,
    ModalFooter,
    List,
    unusableList,
    Redeem,
    Tab,
    Sugguest
  },
  props: {
    isMergedPaymentPage: {
      type: Boolean,
      default: false
    },
    chosenCouponData: {
      type: Object,
      default: () => {
        return {
          common_coupon: {},
          payment_coupon: {},
        }
      },
    },
    visible: {
      type: Boolean,
      default: false,
    },
    bestcombo: {
      type: Array,
      default: () => []
    },
    maxDiscount: {
      type: Object,
      default: () => ({})
    },
    usable: {
      type: Array,
      default: () => []
    },
    usablePaymentCoupon: {
      type: Array,
      default: []
    },
    unusable: {
      type: Array,
      default: () => []
    },
    hasUsableCoupons: {
      type: Boolean,
      default: false
    },
    tabsStatus: {
      type: Number,
      default: 0
    },
    isMobile: {
      type: Boolean,
      default: true
    },
    visibleBestCouponSuggestNotice:{
        type: Boolean,
        default: true
    },
  },
  computed: {
    usableLength() {
      return this.usablePaymentCoupon.length + this.usable.length
    },
  },
  methods: {
    titleText(){
      return this.formmatTextId(['2103', 'promo_code.coupon.label'])
    },
    handleApply(){
      this.$emit('close-modal');
      this.$emit('use-coupon', this.chosenCouponData)
      this.$emit('click-use');
    },
    handleCloseModal() {
      this.$emit('close-modal');
      this.$emit('use-coupon', this.chosenCouponData)
      this.$emit('click-use');
    },
    handleBack(){
      this.$emit('close-modal');
    }
  },
};
</script>

<style lang="scss" scoped>
.coupons_page {
  position: fixed;
  overflow-y: scroll;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  top: constant(safe-area-inset-top);
  top: env(safe-area-inset-top);
  bottom: constant(safe-area-inset-bottom);
  bottom: env(safe-area-inset-bottom);
  width: 100%;
  height: 100%;
  background-color: #F5F5F5;
  padding-top: 60px;
  z-index: 2000;
  padding-left: 16px;
  padding-right: 16px;
  padding-bottom: 90px;
}

.coupons_page__new {
  padding-bottom: 140px;
}
</style>
