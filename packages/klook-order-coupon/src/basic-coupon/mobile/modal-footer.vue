<template>
  <div
    v-show="hasUsableCoupons"
    class="mkt-modal-bottom"
  >
    <div v-if="!isMergedPaymentPage" class="bottom-container">
      <div class="bottom-left">
        <p
          v-show="
            (common_coupon.code || payment_coupon.code) && discountAmount
          "
          class="coupon_discount"
        >
          - {{ couponData.currencySymbol }}
          {{
            discountAmount
          }}
        </p>
        <div v-if="isSettlementPage && payment_coupon.code" class="coupon_discount">
          {{ $t('91901') }}
        </div>
        <div v-show="hasUsableCoupons && !hasSelectedCoupons">
          {{ $t("44657") }}
        </div>
        <div v-show="payment_coupon.code && !common_coupon.code && couponData.pageType == 'settlement'">
          {{ $t("73206") }}
        </div>
      </div>
      <div class="bottom-right">
        <klk-coupon-button
          :data-spm-module="applySpmModuleData"
          data-spm-virtual-item="__virtual"
          type="primary"
          size="small"
        >
          <span @click="hasSelectedCoupons ? $emit('click-apply') : $emit('click-close')">{{ $t("73205") }}</span>
        </klk-coupon-button>
      </div>
    </div>
    <div v-else class="bottom-container-new">
      <MaxDiscount
        v-if="visibleBestCouponSuggestNotice"
        v-on="$listeners"
        v-bind="$attrs"
        :is-mobile="true"
        :max-discount="maxDiscount"
        :coupon-list="usable"
        :payment-coupon-list="usablePaymentCoupon"
        :chosen-coupon-data="chosenCouponData"
      />
      <klk-coupon-button
        class="action-btn"
        :data-spm-module="applySpmModuleData"
        data-spm-virtual-item="__virtual"
        type="primary"
        block
        @click="hasSelectedCoupons ? $emit('click-apply') : $emit('click-close')"
      >
        <span>{{ $t("73205") }}</span>
      </klk-coupon-button>
    </div>
  </div>
</template>

<script>
import { formatPriceThousands, formatTwoDecimal } from '../../utils/common-utils';
import MaxDiscount from "../common/max-discount/index.vue";

export default {
  inject: ['couponData'],
  components: {
    MaxDiscount
  },
  filters: {
    formatPriceThousands,
    formatTwoDecimal,
  },
  props: {
    isMergedPaymentPage: {
      type: Boolean,
      default: false
    },
    visibleBestCouponSuggestNotice:{
      type: Boolean,
      default: true
    },
    maxDiscount: {
      type: Object,
      default: () => ({})
    },
    usable: {
      type: Array,
      default: () => []
    },
    usablePaymentCoupon: {
      type: Array,
      default: []
    },
    hasUsableCoupons: {
      type: Boolean,
    },
    chosenCouponData: {
      type: Object,
      default: () => {},
    }
  },
  computed:{
    applySpmModuleData() {
      const ext = JSON.stringify({
        ClickType: this.hasSelectedCoupons? 'Comfirm': 'Close',
        PaymentCouponBatchID: this.payment_coupon?.batch_id || '',
        PlatformCouponBatchID: this.common_coupon?.batch_id || '',
      })
      return `CouponApplyBtn?ext=${ext}`
    },
    hasSelectedCoupons(){
      return this.chosenCouponData?.common_coupon?.code || this.chosenCouponData?.payment_coupon?.code
    },
    common_coupon(){
      return this.chosenCouponData?.common_coupon || {}
    },
    payment_coupon(){
      return this.chosenCouponData?.payment_coupon || {}
    },
    isSettlementPage(){
      return this.couponData.pageType == 'settlement'
    },
    discountAmount(){
      if(this.couponData.pageType == 'settlement'){
        return this.chosenCouponData?.common_coupon?.coupon_use_info?.coupon_discount_dec || ''
      }
      return this.chosenCouponData?.payment_coupon?.coupon_use_info?.coupon_discount_dec || ''
    }
  },
  data() {
    return {
      couponData: this.couponData,
    };
  },
};
</script>

<style lang="scss" scoped>
.mkt-modal-bottom {
  position: fixed;
  z-index: 10;
  bottom: 0;
  left: 0;
  right: 0;
  box-shadow: 0px -1px 8px rgba(0, 0, 0, 0.11);
  p {
    margin: 0;
  }
  .bottom-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    padding: 8px;
    background-color: #fff;
    min-height: 62px;
  }
  .coupon_discount {
    color: #ff5722;
    font-size: 16px;
    line-height: 22px;
    font-weight: 600;
  }
  .coupon_discount-best {
    color: #ff5722;
    font-size: 14px;
    line-height: 20px;
  }
  .bottom-right {
    button {
      text-align: center;
    }
  }
}

.bottom-container-new {
  width: 100%;
  background-color: #fff;
  min-height: 62px;
  padding: 16px 20px;

  .max-discount-wrapper {
    margin: -16px -20px 16px;
  }

  .action-btn {
    text-align: center;
  }
}
</style>
