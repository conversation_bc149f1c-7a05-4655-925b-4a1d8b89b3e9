<template>
  <header
    class="klk_func_header"
    :class="classes"
  >
    <klk-coupon-icon
      class="klk_func_header__icon"
      type="icon_navigation_chevron_left"
      size="18"
      @click.native="clickBack"
    />
    <span
      v-if="title"
      class="klk_func_header__title"
    >{{ title }}</span>
  </header>
</template>

<script>
const prefixCls = 'klk_func_header';

export default {
  components: {
  },
  props: {
    position: {
      type: String,
      default: 'absolute',
    },
    title: {
      type: String,
      default: '',
    },
    bottomBorder: {
      type: Boolean,
      default: true,
    },
  },
  computed: {
    classes() {
      return [
        `${prefixCls}`,
        {
          [`${prefixCls}--is_fixed}`]: this.position === 'fixed',
          [`${prefixCls}--bottom_border`]: this.bottomBorder === true,
        },
      ];
    },
  },
  methods: {
    clickBack() {
      this.$emit('click-back');
    },
  },
};
</script>

<style lang="scss" scoped>
.klk_func_header {
  position: fixed;
  z-index: 10;
  top: 0;
  left: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 12px;
  width: 100%;
  height: 48px;
  background-color: #fff;

  &--bottom_border {
    box-shadow: inset 0 -1px 1px -1px rgba(0, 0, 0, 0.12);
  }

  &--is_fixed {
    position: fixed;
  }

  &__icon {
    flex: none;
    font-size: 24px;
    color: rgba(0, 0, 0, 0.54);
  }

  &__title {
    position: absolute;
    top: 50%;
    left: 50%;
    line-height: 19px;
    font-size: 16px;
    font-weight: bold;
    transform: translate(-50%, -50%);
  }

  &__opt {
    flex: none;
    line-height: 1.25;
    font-size: 16px;
    color: rgba(0, 0, 0, 0.87);
    font-weight: bold;

    &--is_disabled {
      color: rgba(0, 0, 0, 0.24);
    }
  }
}
</style>
