<template>
  <div
    class="mkt-order-coupon-mobile-trigger"
    data-spm-item="PromocodeSection"
  >
    <!-- 选择的优惠组合 -->
    <ul v-if="!isMergedPaymentPage" class="select-coupon-result-box">
      <li 
        class="select-coupon-result-item" 
        :data-spm-module="`PlatformPromo?ext=${ JSON.stringify({ status: usable.length? 'available' : 'no available' }) }`" 
        data-spm-virtual-item="__virtual" 
        @click="openModal('platformCoupon')">
        <span class="coupon_label">{{ $t('91903') }}</span>
        <div class="promo_code_right">
          <span class="coupon_value_text" :class="platformCouponResultStyle">
            {{ platformCouponResultText }}
          </span>
          <klk-coupon-icon
            class="klk_func_header__icon"
            type="icon_navigation_chevron_right"
            size="18"
          />
        </div>
      </li>
      <li 
        v-if="visiblePaymentCouponEntry" 
        class="select-coupon-result-item payment-item" 
        :data-spm-module="`PaymentPromo?ext=${ JSON.stringify({ status: usablePaymentCoupon.length? 'available' : 'no available' }) }`" 
        data-spm-virtual-item="__virtual" 
        @click="openModal('paymentCoupon')">
        <span class="coupon_label">{{ $t('91904') }}</span>
        <div class="promo_code_right">
          <span class="coupon_value_text" :class="paymentCouponResultStyle">
            {{ paymentCouponResultText }}
          </span>
          <klk-coupon-icon
            class="klk_func_header__icon"
            type="icon_navigation_chevron_right"
            size="18"
          />
        </div>
      </li>
    </ul>
    <div v-else class="paylater-entry" @click="openMergedPayModal">
      <div class="platform-coupon-info">
        <div class="platform-coupon-title">{{ $t('176630') }}</div>
        <div class="platform-coupon-action">
          <klk-atomic-basic-tag v-if="combinedCouponResultStyle === 'selected_coupon'" size="medium" :data="promoTagData" />
          <span v-else class="coupon_value_text" :class="combinedCouponResultStyle">
            {{ combinedCouponResultText }}
          </span>
          <IconNext class="arrow-btn" theme="outline" size="20" fill="#212121" />
        </div>
      </div>
      <div v-show="isBestCouponCombo" class="best-combo-applied">{{ $t('173922') }}</div>
    </div>
    <div v-if="false" class="trigger-content">
      <div v-if="!isNewStyle" class="prome_code_left">
        {{ $t("30125") }}
        <!-- 最大减免提示 -->
        <span
          v-show="
            couponData.bestCouponCode &&
            common_coupon.code == couponData.bestCouponCode
          "
          class="t12"
        >
          {{ $t("30126") }}</span
        >
        <!-- 没有可用优惠券提示 -->
        <div v-show="!hasUsableCoupons">
          {{ $t("30122") }}
        </div>
        <!-- 没有选择优惠券提示-->
        <div v-show="hasUsableCoupons && !hasSelectedCoupons">
          {{ $t("73173") }}
        </div>
      </div>
      <div class="prome_code_left_new" v-else>
        <div class="promo-label">{{ $t("30125") }}</div>
        <!-- 最大减免提示 -->
        <div
          v-show="
            couponData.bestCouponCode &&
            common_coupon.code == couponData.bestCouponCode
          "
          class="best"
        >
          {{ $t("30126") }}
        </div>
        <!-- 没有可用优惠券提示 -->
        <div v-show="!hasUsableCoupons">
          {{ $t("30122") }}
        </div>
        <!-- 没有选择优惠券提示-->
        <div v-show="hasUsableCoupons && !hasSelectedCoupons">
          {{ $t("73173") }}
        </div>
      </div>
      <span v-if="!isNewStyle" class="prome_code_right">
        <span
          v-show="
            hasUsableCoupons && couponData.discountAmount && common_coupon.code
          "
          class="coupon_discount"
          >- {{ couponData.currencySymbol }}
          {{ couponData.discountAmount }}</span
        >
        <klk-coupon-icon
          class="klk_func_header__icon"
          type="icon_navigation_chevron_right"
          size="18"
        />
      </span>
      <span v-else class="prome_code_right_new">
        <span
          v-show="
            hasUsableCoupons &&
            !isNaN(couponData.discountAmount) &&
            common_coupon.code
          "
          class="currency"
          >- {{ couponData.currencySymbol }}</span
        >
        <span
          v-show="
            hasUsableCoupons &&
            !isNaN(couponData.discountAmount) &&
            common_coupon.code
          "
          class="coupon_discount"
        >
          {{ couponData.discountAmount }}</span
        >
        <klk-coupon-icon
          class="klk_func_header__icon"
          type="icon_navigation_chevron_right"
          size="18"
        />
      </span>
    </div>
  </div>
</template>

<script>
import {
  formatPriceThousands,
  formatTwoDecimal,
} from "../../utils/common-utils";
import SuggestMixin from "../../mixins/suggest-mixin";
import { KlkAtomicBasicTag } from "@klook/klook-card";
import "@klook/klook-card/dist/esm/index.css";
import { IconNext } from "@klook/klook-icons";

export default {
  name: "MktBasicCouponTrigger",
  mixins: [SuggestMixin],
  filters: {
    formatPriceThousands,
    formatTwoDecimal,
  },

  components: {
    KlkAtomicBasicTag,
    IconNext
  },
  inject: ["couponData"],
  props: {
    isMergedPaymentPage: {
      type: Boolean,
      default: false
    },
    isNewStyle: {
      type: Boolean,
      default: false,
    },
    isBestCouponCombo: {
      type: Boolean,
      default: true
    },
    hasUsableCoupons: {
      type: Boolean,
    },
    usable: {
      type: Array,
      default: () => [],
    },
    usablePaymentCoupon: {
      type: Array,
      default: [],
    },
    unusable: {
      type: Array,
      default: () => [],
    },
    // 隐藏支付优惠券入口
    visiblePaymentCouponEntry: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {};
  },
  computed: {
    hasSelectedCoupons() {
      return (
        this.couponData?.chosenCoupon?.common_coupon?.code ||
        this.couponData?.chosenCoupon?.payment_coupon?.code
      );
    },
    common_coupon() {
      return this.couponData.chosenCoupon.common_coupon || {};
    },
    promoTagData() {
      return {
        type: "platform_promo_code_tag",
        text: this.combinedCouponResultText
      }
    }
  },
  watch: {},
  mounted() {},
  methods: {
    openMergedPayModal() {
      this.$emit('refresh-list')
      this.openModal('platformCoupon')
    },
    openModal(type){
      this.$emit('open-modal', type)
    }
  },
};
</script>

<style lang="scss" scoped>
.platform-coupon-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.platform-coupon-title {
  @include font-body-m-bold();
}

.platform-coupon-action {
  display: flex;
  align-items: center;

  .arrow-btn {
    margin-left: 4px;
  }

  .has_select {
    @include font-caption-m-semibold();
    color: $color-text-reverse;
    background: $color-brand-primary;
    border-radius: 6px;
    padding: 0 6px; 
  }

  .disabled_select {
    @include font-body-s-regular();
    color: $color-text-secondary;
  }

  .blank {
    display: none;
  }
}

.best-combo-applied {
  margin-top: 4px;
  @include font-body-s-regular();
  color: $color-text-secondary;
}

.discount_box {
  border-bottom: 0.5px solid rgba(0, 0, 0, 0.12);
}

.mkt-order-coupon-mobile-trigger {
  // display: flex;
  // justify-content: space-between;
  // flex-direction: row;
}
.trigger-content {
  display: flex;
  justify-content: space-between;
  flex-direction: row;
}
.price_item {
  border-bottom: 0.5px solid rgba(0, 0, 0, 0.12);
  .price_item_label_cantainer {
    .label_text {
      margin-right: 8px;
    }
    .label_icon {
    }
  }
  .price_item_value_cantainer {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    .price_cantainer {
      display: flex;
      flex-direction: row;
      align-items: center;
      .value_text {
        margin-right: 8px;
        color: rgba(0, 0, 0, 0.38);
        &.through {
          text-decoration: line-through;
        }
      }
      .value_icon {
      }
      &:last-child {
        .value_text {
          color: rgba(0, 0, 0, 0.87);
          font-weight: bold;
        }
      }
    }
  }
}

.srv_prome_code {
  .prome_code-entrance {
    display: flex;
    justify-content: space-between;
    width: 100%;
    align-items: center;
  }

  .srv_prome_code_left {
    word-break: break-word;
  }

  .srv_best_text {
    border-radius: 17px;
    background-color: #ff5722;
    color: #fff;
    padding: 4px 10px;
    font-size: 10px;
    font-weight: 600;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    width: calc(100% - 24px);

    .best_icon {
      width: 10px;
      height: 10px;
      margin-right: 4px;
      color: #fff;
    }
  }

  .srv_redeem_box {
    display: flex;
    justify-content: flex-end;
    flex-direction: column;
    width: 100%;
    margin-top: 18px;

    &.redeem-tnc-error {
      .redeem-tnc {
        border-color: #e64340;
      }

      .srv-error-tip {
        display: block;
        margin-top: 8px;
        color: #e64340;
      }
    }

    .srv_redeem_item {
      width: 100%;
      display: flex;
      justify-content: space-between;
      margin-bottom: 12px;
      .srv_discount {
        color: #ff5722;
        font-size: 14px;
        font-weight: bold;
      }
    }

    .cancel-redeem {
      font-size: 14px;
      line-height: 20px;
      text-decoration: underline;
      color: #757575;
      cursor: pointer;
      margin-top: 16px;
    }

    .srv-error-tip {
      margin-top: 0;
      padding-top: 0;
    }

    .redeem-tnc {
      border: 1px solid #eeeeee;
      padding: 16px 16px 8px 16px;
      margin-top: 12px;
      background-color: #fafafa;
      display: flex;
      justify-content: space-between;

      .redeem-tnc-content {
        p {
          margin-bottom: 8px;
          font-size: 14px;
          line-height: 21px;
          color: #333333;
        }

        a {
          color: #4985e6;
        }
      }

      .redeem-tnc-tip {
        flex: none;

        .redeem-tnc-tip-star {
          color: #ff5722;
          margin-left: 4px;
        }
        a {
          color: #4985e6;
          text-decoration: underline;
        }
      }
    }
  }
}

.prome_code {
  margin-top: 8px;
  flex-wrap: wrap;
  align-items: center;
  .prome_code_left {
    // max-width: 48vw;
    span {
      font-weight: normal;
      color: #ff5722;
      display: block;
      padding-top: 6px;
    }
  }
  .prome_code_right {
    display: flex;
    align-items: center;
    max-width: calc(40% + 24px);
    b {
      color: #009685;
      font-weight: 600;
    }
    .coupon_discount {
      margin-right: 8px;
      font-weight: bold;
      color: #ff5722;
    }
    svg {
      color: rgba(0, 0, 0, 0.54);
      font-size: 24px;
      flex: none;
    }
  }
}
// 新版优惠券组件样式
.prome_code_left_new {
  font-size: 14px;
  line-height: 160%;
  color: #757575;
  .promo-label {
    font-weight: 600;
    font-size: 16px;
    line-height: 150%;
    color: #212121;
  }
  .best {
    color: #ff5b00;
    margin-top: 4px;
  }
}
// 新版优惠券组件样式
.prome_code_right_new {
  display: flex;
  align-items: flex-start;
  flex-wrap: nowrap;
  // max-width: calc(40% + 24px);
  font-size: 16px;
  color: #ff5b00;
  .currency {
    word-break: break-word;
    min-width: 80px;
    text-align: right;
    padding-right: 6px;
    line-height: 150%;
  }
  .coupon_discount {
    display: inline-block;
    margin-right: 8px;
    font-size: 16px;
    color: #ff5b00;
    line-height: 150%;
  }
  .klk_func_header__icon {
    color: #222222;
    line-height: 2rem;
  }
  svg {
    color: rgba(0, 0, 0, 0.54);
    font-size: 24px;
    flex: none;
  }
}
.select-coupon-result-box {
  list-style: none;
  padding: 0;
  margin: 0;
  .select-coupon-result-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 12px;
    &:nth-last-of-type(1){
      padding-bottom: 0px;
    } 
  }
  .coupon_label {
    font-size: 16px;
    line-height: 150%;
  }
  .coupon_value_text {
    display: inline-block;
    line-height: 150%;
  }
  .promo_code_right {
    display: flex;
    align-items: center;
  }
  .disabled_select {
    color: #757575;
    font-size: 14px;
  }
  .has_select {
    font-size: 12px;
    font-weight: 500;
    background: #ff5b00;
    border-radius: 6px;
    padding: 0 6px;
    color: #ffffff;
  }
  .selected_coupon {
    font-size: 16px;
    font-weight: 500;
    color: #ff5b00;
  }
  .payment-item .selected_coupon {
    font-size: 12px;
    font-weight: 500;
    background: #f09b0a;
    border-radius: 6px;
    padding: 0 6px;
    color: #ffffff;
  }
}
</style>
