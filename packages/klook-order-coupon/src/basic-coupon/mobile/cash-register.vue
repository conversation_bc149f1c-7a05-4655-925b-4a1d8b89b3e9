<template>
  <div
    class="mkt-coupon-wrapper summary_row prome_code"
    :data-spm-module="panelModule"
  >
    <modal
      :visible="true"
      v-bind="$attrs"
      v-on="$listeners"
      v-loading="loading"
      :usable="usable"
      :usablePaymentCoupon="usablePaymentCoupon"
      :unusable="unusable"
      :has-usable-coupons="hasUsableCoupons"
      :chosen-coupon-data="chosenCouponData"
      @choose-coupon="chooseCoupon"
    />
  </div>
</template>

<script>
import Trigger from "./trigger.vue";
import Modal from "./modal.vue";

export default {
  name: "cash-register-coupon-mobile",
  components: {
    Trigger,
    Modal
  },
  inject: ["couponData"],

  props: {
    usable: {
      type: Array,
      default: []
    },
    usablePaymentCoupon: {
      type: Array,
      default: []
    },
    unusable: {
      type: Array,
      default: []
    },
    loading: {
      type: Boolean
    }
  },
  data(){
    return {
      chosenCouponData: {
        common_coupon: {},
        payment_coupon: {}
      }
    }
  },
  computed: {
    panelModule() {
      const ext = { status: this.hasUsableCoupons ? 'available' : 'no available'};
      return `PromoCodePanel?ext=${JSON.stringify(ext)}`;
    },
    hasUsableCoupons() {
      return Boolean(this.usable.length || this.usablePaymentCoupon.length);
    },
  },
  watch:{
    'couponData.chosenCoupon': {
      handler(newVal, oldVal) {
        this.chosenCouponData = newVal ? this.$lodash.cloneDeep(newVal) : {
          common_coupon: {},
          payment_coupon: {}
        }
      },
      immediate: true,
      deep: true
    },
  },
  methods: {
    chooseCoupon(e){
      this.chosenCouponData[e.finance_type] = e
    },
  }
};
</script>

<style lang="scss" scoped>
.mkt-coupon-wrapper {
  padding: 12px 0;
  ::v-deep .mkt-poptip {
    width: 100%;
    margin-top: 18px;
    background: #fff4ed;
    position: relative;
    padding: 8px 36px 8px 12px;
    .mkt-poptip-close {
      cursor: pointer;
      position: absolute;
      top: 8px;
      right: 8px;
    }
    &::after {
      content: "";
      border: 9px solid transparent;
      position: absolute;
      border-bottom-color: #fff4ed;
      top: -18px;
      left: 18px;
    }
  }
  .summary_error_msg {
    margin: 8px 0 0;
    padding: 8px;
    background: #FCF3DE;
    font-size: 14px;
    line-height: 150%;
    border-radius: 8px;
  }
}
</style>
<style>
body.noscroll {
  position: fixed;
  overflow-y: hidden;
}
</style>
