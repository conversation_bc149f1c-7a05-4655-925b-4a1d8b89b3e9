{"name": "@klook/klk-order-coupon", "version": "1.5.5", "description": "A Common Component based on Vue of Klook", "author": "<EMAIL>", "homepage": "https://design.klook.io", "keywords": ["vue", "component", "ui", "framework"], "main": "dist/cjs/index.js", "module": "dist/esm/index.min.js", "files": ["dist", "locales", "locale"], "license": "UNLICENSED", "publishConfig": {"registry": "https://knpm.klook.io", "access": "public"}, "scripts": {"build": "NODE_ENV=production rollup --config ./rollup.config.js && npm run build:locale", "build:dev": "NODE_ENV=production rollup --config ./rollup.dev.config.js", "build:locale": "NODE_ENV=production rollup --config ./rollup.locale.config.js", "lint": "NODE_ENV=production eslint --ext .js,.vue src", "lint-fix": "NODE_ENV=production eslint --fix --ext .js,.vue src", "test": "NODE_ENV=test jest -i --updateSnapshot", "test:coverage": "NODE_ENV=test jest -i --coverage --updateSnapshot", "prepush": "yarn run lint", "prepublishOnly": "bash prepublishOnly.sh", "commit": "npx git-cz", "commitmsg": "commitlint -E GIT_PARAMS"}, "devDependencies": {"@babel/core": "^7.17.9", "@babel/eslint-parser": "^7.15.8", "@babel/plugin-transform-runtime": "^7.17.0", "@babel/preset-env": "^7.16.11", "@klook/inhouse-tracker": "^1.11.9", "@klook/klk-traveller-utils": "1.1.3", "@klook/klook-icons": "^0.20.0", "@klook/klook-card": "^0.4.10", "@klook/klook-ui": "^1.23.2", "@rollup/plugin-babel": "^5.3.1", "@rollup/plugin-image": "^2.1.1", "@rollup/plugin-json": "^4.1.0", "@types/jest": "^26.0.0", "@types/webpack-env": "^1.14.0", "@vue/test-utils": "^1.0.0-beta.32", "axios": "^0.22.0", "babel-eslint": "^10.1.0", "jest": "^25.5.4", "rimraf": "^3.0.0", "rollup": "^2.74.1", "rollup-plugin-alias": "^2.2.0", "rollup-plugin-analyzer": "^4.0.0", "rollup-plugin-babel": "^4.4.0", "rollup-plugin-buble": "^0.19.8", "rollup-plugin-copy": "^3.4.0", "rollup-plugin-clear": "^2.0.7", "rollup-plugin-commonjs": "^10.0.2", "rollup-plugin-json": "^4.0.0", "rollup-plugin-node-resolve": "^5.2.0", "rollup-plugin-postcss": "^4.0.2", "rollup-plugin-replace": "^2.2.0", "rollup-plugin-svg": "^2.0.0", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-typescript2": "^0.31.2", "rollup-plugin-vue": "^5.1.9", "terser": "^4.1.3", "ts-jest": "^26.1.0", "ts-node": "^8.10.2", "typescript": "^4.6.3", "vue": "2.6.11", "vue-jest": "^3.0.4", "vue-property-decorator": "^8.3.0", "vue-template-compiler": "2.6.11"}, "dependencies": {"dayjs": "^1.10.7", "js-cookie": "^3.0.1", "lodash": "^4.17.21", "postcss": "^8.4.14", "vue-lazyload": "^1.3.4"}, "peerDependencies": {"vue": "2.6.11", "vue-template-compiler": "2.6.11", "@klook/klook-ui": "^1.23.2", "@klook/klk-traveller-utils": "^1.1.3", "@klook/inhouse-tracker": "^1.11.9", "axios": "^0.22.0", "@klook/klook-icons": "^0.20.0", "@klook/klook-card": "^0.4.10"}}