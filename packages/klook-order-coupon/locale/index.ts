import KlookLocalesMerge from '@klook/klk-traveller-utils/lib/klookLocalesMerge'
// const LocalesData = require('./locales')
export default function (i18n: any, language?: string) {
  const lang = i18n.locale || language
  return KlookLocalesMerge({
    i18n,
    language: lang,
    // getLocal: async() => {return await LocalesData[lang] }
    getLocal: () => import(`../locales/${lang}.json` /* webpackChunkName: "order-coupon-lang-[request]" */),
  })
}
