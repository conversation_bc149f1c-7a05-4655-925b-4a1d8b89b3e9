"use strict";function e(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(r){if("default"!==r){var n=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,n.get?n:{enumerable:!0,get:function(){return e[r]}})}})),t.default=e,Object.freeze(t)}function t(e,t){return e(t={exports:{}},t.exports),t.exports}Object.defineProperty(exports,"__esModule",{value:!0});var r=t((function(e,t){var r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])},r(e,t)};t.__assign=function(){return t.__assign=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},t.__assign.apply(this,arguments)},t.__extends=function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)},t.__spreadArray=function(e,t,r){if(r||2===arguments.length)for(var n,o=0,a=t.length;o<a;o++)!n&&o in t||(n||(n=Array.prototype.slice.call(t,0,o)),n[o]=t[o]);return e.concat(n||Array.prototype.slice.call(t))}}));r.__assign,r.__extends,r.__spreadArray;var n,o=t((function(e,t){Object.defineProperty(t,"__esModule",{value:!0});var n=function(e){return"[object Object]"===Object.prototype.toString.call(e)},o=function(){var e=Object.getPrototypeOf(this).$t;return e?e.apply(this,arguments):arguments[0]};function a(e){for(var t=[],r=1;r<arguments.length;r++)t[r-1]=arguments[r];var o=new RegExp("{(\\w+)}","g");return 1===t.length&&(n(t[0])||Array.isArray(t[0]))&&(t=t[0]),e.replace(o,(function(e,r){var n=t[r];return void 0===n?e:n}))}t.default=function(e){var t=e.i18n,r=e.language,n=e.getLocal,o=t.locale||r;return n().then((function(e){!function(e,t,r){if(e.mergeLocaleMessage)return e.mergeLocaleMessage(t,r);e.options.messages[t]=Object.assign({},e.options.messages[t]||{},r)}(t,o,e)}))},t.t=o,t.translate=function(e){return n(e)?function(t){for(var n=[],o=1;o<arguments.length;o++)n[o-1]=arguments[o];return a.apply(void 0,r.__spreadArray([e[t]||""],n,!1))||t}:o}})),a=(n=o)&&n.__esModule&&Object.prototype.hasOwnProperty.call(n,"default")?n.default:n;o.t,o.translate,exports.default=function(t,r){var n=t.locale||r;return a({i18n:t,language:n,getLocal:function(){return t="../locales/".concat(n,".json"),Promise.resolve().then((function(){return e(require(t))}));var t}})};
