import de from '../locales/de.json';
import enAU from '../locales/en-AU.json';
import enCA from '../locales/en-CA.json';
import enGB from '../locales/en-GB.json';
import enHK from '../locales/en-HK.json';
import enIN from '../locales/en-IN.json';
import enMY from '../locales/en-MY.json';
import enNZ from '../locales/en-NZ.json';
import enPH from '../locales/en-PH.json';
import enSG from '../locales/en-SG.json';
import enUS from '../locales/en-US.json';
import en from '../locales/en.json';
import es from '../locales/es.json';
import fr from '../locales/fr.json';
import id from '../locales/id.json';
import it from '../locales/it.json';
import ja from '../locales/ja.json';
import ko from '../locales/ko.json';
import ru from '../locales/ru.json';
import th from '../locales/th.json';
import vi from '../locales/vi.json';
import zhCN from '../locales/zh-CN.json';
import zhHK from '../locales/zh-HK.json';
import zhTW from '../locales/zh-TW.json';
import msMY from '../locales/ms-MY.json';
export default {
  de,
  'en-AU': enAU,
  'en-CA': enCA,
  'en-GB': enGB,
  'en-HK': enHK,
  'en-IN': enIN,
  'en-MY': enMY,
  'en-NZ': enNZ,
  'en-PH': enPH,
  'en-SG': enSG,
  'en-US': enUS,
  en,
  es,
  fr,
  id,
  it,
  ja,
  ko,
  ru,
  th,
  vi,
  'zh-CN': zhCN,
  'zh-HK': zhHK,
  'zh-TW': zhTW,
  'ms-MY': msMY
};
