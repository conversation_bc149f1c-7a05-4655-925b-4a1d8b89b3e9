# klook-order-coupon
结算页优惠券组件：可轻松在结算页面中使用优惠券组件
# release version
0.0.1: 包含了通用优惠券，srv优惠券模块
0.0.2, 0.0.3: 修复了一些bug
https://orion.myklook.com/1000026/145/orion/features/1074040/detail
https://klook.larksuite.com/docs/docusw6EwtfDDjjUifxPh3fHmsd
https://klook.larksuite.com/base/basusxvkjtGD4206VRTmU2tJIXg?table=tblvKqcwotOQ0Cc3&view=vewIyyKxvE

# dev doc
https://storybook.klook.io/?path=/story/%E7%BB%84%E4%BB%B6%E5%BA%93-klook-martech--intro-doc

# 开发者文档
多语言文案：
 获取多语言文档步骤：
 1. 获取仓库klk-f2e-cli权限，拉取代码运行（项目运行常见报错解答：https://klook.larksuite.com/wiki/wikusyaJFq30SZCFZtfFx1gNdHf）
 2. 通过多语言平台（https://admin.klook.com/mspa/platform/loc_cms/text_list?collection_id=1）获取多语言id（注意：多语言需要添加当前项目权限才能正常拉取） 
 3. 如果增加或者修改多语言请手动导入text id 运行命令：f2e tetris locale add [key]

# 设计稿
https://www.figma.com/file/oNhFaayiYPBeK2PgbVJnZh/%E4%BC%98%E6%83%A0%E5%88%B8%E7%BB%84%E4%BB%B6-%26-%E7%BB%93%E7%AE%97%E9%A1%B5%E4%BC%98%E6%83%A0%E6%A8%A1%E5%9D%97?node-id=370%3A14980

# srv生成地址
https://samliew.com/nric-generator

# prd + orion
https://klook.larksuite.com/docs/docus4qtm7xqcspe51cBn7kjMQf
https://orion.myklook.com/1000026/145/orion/features/1069582/detail?query=U2FsdGVkX1%2FGOmEAResCUkL2wmAZycPOsayeXVEG4oGQ0L%2FH3UZ%2B%2B6181%2BrTo7zupfZ3D%2FnQcsRQFnCaEKOe5pA53aPdLSN6hhgcdlv86G8WlsIn28%2FsAdJe0rfCvVZg

# 埋点文档
https://klook.larksuite.com/docs/docusWYAnmNMD5CtgE2HfiAuzZe#LqfiuD