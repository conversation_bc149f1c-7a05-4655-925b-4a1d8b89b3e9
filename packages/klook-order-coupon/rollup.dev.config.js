import replace from "rollup-plugin-replace";
import typescript from "rollup-plugin-typescript2";
import vue from "rollup-plugin-vue";
import alias from "rollup-plugin-alias";
import json from "rollup-plugin-json";
import babel from "@rollup/plugin-babel";
import { terser } from "rollup-plugin-terser";
import analyze from "rollup-plugin-analyzer";
import image from '@rollup/plugin-image';
import resolve from 'rollup-plugin-node-resolve'
import postcss from "rollup-plugin-postcss";
import copy from 'rollup-plugin-copy';
import clear from 'rollup-plugin-clear';

const { name, version, author } = require("./package.json");

const target = '/Users/<USER>/code/experiences/node_modules/@klook/klk-order-coupon'

// 删除dist
const targetDir = ['esm', 'cjs'].map(v => `${target}/dist/${v}`)
clear({ targets: targetDir });

const banner = `/**
  * v${version}
  * (c) ${new Date().getFullYear()} ${author}
  */`;
const configs = {
  // umd: {
  //   output: 'dist/umd/index.js',
  //   format: 'umd',
  //   target: 'es5',
  //   env: 'production'
  // },
  // umdMin: {
  //   output: 'dist/umd/index.min.js',
  //   format: 'umd',
  //   target: 'es5',
  //   plugins: {
  //     post: [terser()]
  //   },
  //   env: 'production'
  // },
  esm: {
    output: `${target}/dist/esm/index.js`,
    format: "esm",
    target: "es2015",
    env: "production",
    genDts: true
  },
  esmMin: {
    output: `${target}/dist/esm/index.min.js`,
    format: "esm",
    target: "es2015",
    plugins: {
      post: [terser()]
    },
    env: "production",
    genDts: true
  },
  cjs: {
    output: `${target}/dist/cjs/index.js`,
    format: "cjs",
    target: "es2015"
  }
};

const externals = [
  "vue",
  "@klook/klook-ui",
  "axios",
  "@klook/inhouse-tracker",
  '@klook/klook-ui/lib/styles/components/loading.scss',
  '@klook/klook-ui/lib/styles/components/button.scss',
  '@klook/klook-ui/lib/styles/components/input.scss',
  '@klook/klook-ui/lib/styles/components/checkbox.scss',
  '@klook/klook-ui/lib/styles/components/collapse.scss',
  '@klook/klook-ui/lib/styles/components/icon.scss',
  '@klook/klook-ui/lib/styles/components/modal.scss',
  '@klook/klook-ui/lib/styles/components/poptip.scss',
  '@klook/klook-ui/lib/styles/components/form.scss',
  '@klook/klook-ui/lib/styles/components/loading.scss',
  '@klook/klook-ui/lib/styles/components/bottom-sheet.scss',
  '@klook/klook-ui/lib/styles/components/tabs.scss',
  '@klook/klook-ui/lib/styles/components/toast.scss'
];

const genTsPlugin = configOpts =>
  typescript({
    useTsconfigDeclarationDir: true,
    tsconfigOverride: {
      compilerOptions: {
        target: configOpts.target,
        declaration: configOpts.genDts
      }
    },
    abortOnError: false
  });

const genPlugins = configOpts => {
  const plugins = [];
  if (configOpts.env) {
    plugins.push(
      replace({
        "process.env.NODE_ENV": JSON.stringify(configOpts.env)
      })
    );
  }
  plugins.push(
    replace({
      "process.env.MODULE_FORMAT": JSON.stringify(configOpts.format)
    })
  );
  if (configOpts.plugins && configOpts.plugins.pre) {
    plugins.push(...configOpts.plugins.pre);
  }
  plugins.push(genTsPlugin(configOpts));

  plugins.push(
    vue({
      template: {
        isProduction: true
      }
    })
  );

  plugins.push(
    babel({
      include: ["src/**", "node_modules/**"],
      extensions: [".js", ".vue"]
    })
  );

  if (configOpts.plugins && configOpts.plugins.post) {
    plugins.push(...configOpts.plugins.post);
  }
  return plugins;
};

const genConfig = configOpts => ({
  input: "src/index.js",
  output: {
    banner,
    file: configOpts.output,
    format: configOpts.format,
    name: name,
    sourcemap: false,
    exports: "named",
    globals: configOpts.globals
  },
  external: externals,
  plugins: [
    postcss(),
    resolve(),
    ...genPlugins(configOpts),
    json(),
    alias({
      resolve: [".jsx", ".js"],
      entries: {
        root: "./",
        src: "./src",
        utils: "./src/utils"
      }
    }),
    image(),
    analyze()
  ]
});

const genAllConfigs = configs =>
  Object.keys(configs).map(key => genConfig(configs[key]));

export default genAllConfigs(configs);
