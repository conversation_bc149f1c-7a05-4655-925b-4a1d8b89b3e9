{"410": "卡号", "411": "有效日期", "412": "安全码", "414": "保存卡信息到我的账户，便于下次预订", "453": "提交", "497": "信用卡不能为空", "498": "安全码不能为空", "499": "请选择信用卡有效期", "517": "已验证", "521": "您的付款信息将由信誉良好的支付系统公司进行安全加密和保护。", "534": "信用卡安全码(CVV)有误，请确认您的安全码输入正确", "1974": "您输入的信用卡信息有误，请核对您的输入是否正确。", "1975": "发生网络错误。请稍后重试。", "1976": "您的发卡银行无法核准本次交易。如需了解详情，请联络您的银行。", "2792": "信用卡安全", "10631": "MM", "10632": "YYYY", "12917": "此优惠码仅适用于指定信用卡，请使用符合要求的信用卡以享受优惠", "13726": "请输入有效日期", "13983": "卡号错误", "14474": "国家 / 地区代码", "15235": "请输入卡号", "15283": "卡号格式错误，请检查并重新输入", "17604": "刷新", "27068": "卡号", "27069": "请填写有效卡号", "27075": "手机号码", "27076": "请输入有效手机号码", "27078": "持卡人姓名", "27079": "请填写有效持卡人姓名", "27080": "身份证号码", "27081": "请填写有效身份证号码", "48181": "请输入指定卡的有效卡号", "48182": "请输入指定卡的卡号", "48183": "抱歉，订单无法使用此卡付款，请使用其他卡再试。", "111119": "卡片信息", "122710": "哎呀~ 出了点问题", "172836": "信用卡", "172837": "仅限使用信用卡付款，恕不接受借记卡付款", "172838": "为确保预订成功，付款信息将加密并发送给供应商由供应商收款，Klook将不扣款", "172839": "出错了~ 请重试", "173572": "取车时需出示付款时使用的卡", "173812": "新增信用卡", "global.select.empty_error": "请选择", "global.error.cant_be_empty": "请填写内容", "global.select.palceholder": "请选择"}