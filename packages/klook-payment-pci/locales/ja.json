{"410": "カード番号", "411": "有効期限", "412": "セキュリティコード（CVV）", "414": "カード情報を保存する", "453": "送信", "497": "※クレジットカード番号を入力してください", "498": "※セキュリティコードを入力してください", "499": "※クレジットカードの有効期限を選択してください", "517": "認証完了", "521": "お支払い情報はすべて暗号化され、サーバーに安全に送信されます。", "534": "※セキュリティコード（CVV）が無効です。もう一度入力してください。", "1974": "一部の入力内容に誤りがあります。カード情報をもう一度確認してください。", "1975": "ネットワークの問題が発生しました。あとでもう一度お試しください。", "1976": "ご利用のカード会社が決済を拒否しました。詳細については、ご利用の銀行にお問い合わせください。", "2792": "カード情報の安全性について", "10631": "MM（月）", "10632": "YYYY（年）", "12917": "クーポンコードは一部のクレジットカードが対象です。対象のクレジットカードのみクーポンコードが適用されます。", "13726": "有効期限を正しく入力してください", "13983": "カード番号エラー", "14474": "国番号", "15235": "カード番号を入力してください。", "15283": "フォーマットが正しくありません。ご確認のうえ、もう一度お試しください。", "17604": "再読み込み", "27068": "カード番号", "27069": "有効なカード番号を入力してください", "27075": "電話番号（携帯）", "27076": "有効な電話番号を入力してください", "27078": "カード名義人氏名", "27079": "有効なカード名義人氏名を入力してください", "27080": "身分証明書番号", "27081": "有効な身分証明書番号を入力してください", "48181": "指定カードの有効なカード番号を入力してください", "48182": "指定カードのカード番号を入力してください", "48183": "この種類のカードは今回の注文に使用できません。別のカードをご使用ください。", "111119": "カード情報", "122710": "エラー発生", "172836": "クレジットカード", "172837": "お支払いはクレジットカードのみ利用できます。デビットカードは利用できません", "172838": "To secure your booking, your payment information will be encrypted and sent to the supplier who will charge your card directly. Klook will not charge your card.", "172839": "エラーが発生しました。もう一度お試しください", "173572": "車の受け取り時に支払いに使用したカードを提示する必要があります", "173812": "クレジットカードを追加", "global.select.empty_error": "選択してください", "global.error.cant_be_empty": "入力必須です", "global.select.palceholder": "選択してください"}