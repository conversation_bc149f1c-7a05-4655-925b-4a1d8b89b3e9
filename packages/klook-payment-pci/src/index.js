
import paymentPci from './index.vue'
import { createLanguageAsyncComponent } from '@klook/klk-traveller-utils/lib/createLanguageAsyncComponent'

function createLanguageComponent(comp) {
  return createLanguageAsyncComponent({
    comp,
    importLocale: (language) => import(`../../locales/${language}.json`)
  })
}

const PaymentPci = createLanguageComponent(paymentPci)

export { PaymentPci }
export default PaymentPci
