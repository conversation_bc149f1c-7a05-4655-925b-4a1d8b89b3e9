<template>
  <observeDom @init="init" class="credit-card-pci" :class="realPlatform">
    <!-- loading -->
    <div v-if="isLoading" class="loading" :class="realPlatform">
      <klk-loading :show-overlay="true" overlay-color="#fff" />
    </div>

    <!-- error -->
    <div v-else-if="isError" class="refresh" :class="realPlatform">
      <div class="wrong">{{ __t("122710") }}</div>
      <div class="refresh-btn" @click="handleInitPCI">
        <IconRefresh theme="outline" size="20" :fill="colorTextPrimary" />
        <span>{{ __t("17604") }}</span>
      </div>
    </div>

    <div class="top-tip">
      <IconInformation theme="filled" size="20" :fill="colorTextPrimary" />
      {{ topText ? topText : __t("173572") }}
    </div>

    <div v-if="!isError" class="supported-cards">
      <div class="title">
        <span>{{ __t("172836") }}</span>
        <div>{{ __t("172837") }}</div>
      </div>
      <ul v-if="isDesktop && iconList.length">
        <li v-for="(item, index) in iconList" :key="index">
          <img :src="item.icon" />
        </li>
      </ul>
    </div>

    <IframeComp v-bind="iframeCompAttr" />

    <div class="bottom-text">
      {{ bottomText ? bottomText : __t("172838") }}
    </div>
  </observeDom>
</template>

<script lang="ts">
import { Component, Vue, Prop, Inject } from "vue-property-decorator";
import { getIframeElement, PostChannel } from "./utils";
import {
  $colorBrandPrimary,
  $colorTextPrimary,
} from "@klook/klook-ui/lib/utils/design-token-esm";
import KlkLoading from "@klook/klook-ui/lib/loading";
// @ts-ignore
import IconRefresh from "@klook/klook-icons/lib/IconRefresh";
// @ts-ignore
import IconInformation from "@klook/klook-icons/lib/IconInformation";
// @ts-ignore
import { Message } from "@klook/klook-ui";
import IframeComp from "./iframeComp.vue";
import observeDom from "./observeDom.vue";
import uuidv4 from 'uuid/v4'

@Component({
  components: {
    observeDom,
    KlkLoading,
    IconRefresh,
    IframeComp,
    IconInformation,
  },
})
export default class PaymentPci extends Vue {
  @Inject({ default: () => "" }) __t!: Function;
  @Prop() platform!: string;
  @Prop() language!: string;
  @Prop() axios!: any;
  @Prop({ default: () => "1.4.0" }) pciVersion!: string;
  @Prop({ default: () => "" }) topText!: any;
  @Prop({ default: () => "" }) bottomText!: any;
  @Prop({ default: () => "" }) env!: any;
  @Prop({ default: () => false }) isScop!: boolean; // fase: paynow_hertz; true: scop paynow_hertz

  _axios!: any;
  $iframeChannel: any = null;
  iframeHeight: number = 10;
  colorBrandPrimary = $colorBrandPrimary;
  colorTextPrimary = $colorTextPrimary;
  iframeMounted: boolean = false;
  isLoading: boolean = false;
  isError: boolean = false;
  creditCardInfo: any = null;
  cardTypeRules: any = [];
  supportedCards: any = {};

  get iframeCompAttr() {
    return {
      target: this.target,
      iframeHeight: this.iframeHeight,
      platform: this.realPlatform,
    };
  }

  get iframeUrl() {
    const pciVersion = this.pciVersion;
    return {
      test: `https://pay.test.klooktest.com/dist/${pciVersion}/index.html`,
      prd: `https://pay.klook.com/${pciVersion}/index.html`,
    };
  }

  get target() {
    return this.iframeUrl[
      this.env === "dev" || (window as any)?.KLK_ENV === "dev" || (window as any)?.KLK_ENV === "test" ? "test" : "prd"
    ];
  }

  get realPlatform() {
    return (
      this.platform ||
      (window as any)?.__KLOOK__?.state?.klook?.platform ||
      "desktop"
    );
  }

  get localization() {
    return {
      "pay.number.card_error": this.__t("48181"),
      "pay.number.card_bin_not_support": this.__t("48182"),
      "pay.number.order_not_support": this.__t("48183"),
      "pay.id.number": this.__t("27080"),
      "pay.id.number.tips": this.__t("27081"),
      "pay.hold.name": this.__t("27078"),
      "pay.hold.name.tips": this.__t("27079"),
      "pay.phone.number": this.__t("27075"),
      "pay.phone.number.tips": this.__t("27076"),
      "pay.card.number.tips": this.__t("27069"),
      "pay.card.number.fourKey": this.__t("27068"),
      "pay.card.number": this.__t("410"), // 410
      "pay.credicard.tooltips": this.__t("521"), // 521
      "pay.card.expiryDate": this.__t("411"), // 411
      "pay.select.palceholder.month": this.__t("10631"), // 10631
      "pay.select.palceholder.year": this.__t("10632"), // 10632
      "pay.card.SecurityCode": this.__t("412"), // 412
      "pay.contact.information": this.__t("14474"), // 14474
      "pay.card.save.credit": this.__t("414"), // 414
      "pay.card.error.tip1": this.__t("1974"), // 1974
      "payment.certain.credit.card": this.__t("12917"), // 12917
      "global.payment.cvv.need_be_number": this.__t("534"), // 534
      "global.select.empty_error": this.__t("global.select.empty_error"),
      "global.error.cant_be_empty": this.__t("global.error.cant_be_empty"),
      "global.select.palceholder": this.__t("global.select.palceholder"),
      "pay.validateTxt": this.__t("517"), // 517
      "payment.mobile.credit_card_tip_title": this.__t("2792"), // 2792
      "global.payment.cardnumber.empty": this.__t("497"), // 497
      "global.payment.expiryDate.empty": this.__t("499"), // 499
      "global.payment.cvv.empty": this.__t("498"), // 498
      "global.payment1.cardnumber.empty": this.__t("13983"), // 13983
      "pay.card.error.ntt.tip2": this.__t("15283"), // 15283
      "pay.card.error.ntt.tip3": this.__t("15235"), // 15235
      "global.payment.month.need_be_number": this.__t("13726"),
      "global.payment.year.need_be_number": this.__t("13726"), // 13726
    };
  }

  get realLanguage() {
    return (
      this.language ||
      (window as any)?.__KLOOK__?.state?.klook?.language ||
      "en"
    );
  }

  get initData() {
    return {
      themeColor: "#ff5722",
      paymentType: "credit",
      resEnLang: this.realLanguage,
      localization: this.localization,
      supportedCards: this.supportedCards,
      cardTypeRules: this.cardTypeRules.filter((rule: { type: any; }) => (this.supportedCards.types || []).some((x: { type: any; }) => x.type === rule.type)),
      parentsPlatform: this.realPlatform,
    };
  }

  get iconList() {
    return this.initData.supportedCards.types || [];
  }

  get isDesktop() {
    return this.realPlatform === "desktop";
  }

  get stripeCardErrorTips() {
    return {
      "pay.card.error.tip1": this.__t("1974"),
      "pay.card.error.tip2": this.__t("1975"),
      "pay.card.error.tip3": this.__t("1976"),
    };
  }

  init() {
    if (this.$attrs.isAsyncComponentLoading) {
      return;
    }
    this.handleInitPCI();
    this.handleBindEventListener();
  }

  async getIframeData() {
    this.isLoading = true;
    this.isError = false;
    try {
      await Promise.all([this.getConfig(), this.getPaymentMethod()]);
      this.isError = false;
    } catch (error) {
      this.isError = true;
    } finally {
      this.isLoading = false;
    }
  }

  async getConfig() {
    try {
      /** @channel number
       * 1（paynow_hertz 透传渠道，前端固定）
        2（scop paynow_hertz 透传渠道，前端固定）
       */
      const { success, result } = await this._axios.$get(
        `/v1/cashier/payment/forward/config?channel=${(Number(this.isScop) + 1) || 1}`
      );
      if (success) {
        this.cardTypeRules = result.card_type_rules;
      } else {
        throw new Error("getConfig request failed");
      }
    } catch (error) {
      throw error;
    }
  }

  async getPaymentMethod() {
    try {
      const { success, result } = await this._axios.$post(
        "/v1/cashier/payment/forward/get_payment_method",
        {
          method_key: "creditcard_braintree",
          scop: Number(this.isScop)
        }
      );
      if (success) {
        this.supportedCards = result.supported_cards;
      } else {
        throw new Error("getPaymentMethod request failed");
      }
    } catch (error) {
      throw error;
    }
  }

  async handleInitPCI() {
    !this.iframeMounted && (await this.getIframeData());
    if (this.isError) {
      return false;
    }
    const iframeElement = getIframeElement();
    if (iframeElement) {
      this.handleCreateIframeChannel();
      this.$iframeChannel?.post?.("init", this.initData, { reply: false });
      iframeElement.onload = () => {
        if (!this.iframeMounted) {
          this.$iframeChannel?.post?.("init", this.initData, { reply: false });
        }
      };
    }
  }

  verified() {
    return new Promise(async (resolve, reject) => {
      try {
        const res = await this.$iframeChannel?.post?.(
          "validateNewCreditCardInfo",
          {},
          { typeList: ["generateNewCardValidateResult"] }
        );
        if (!res?.props?.newCardValidateResp?.success) {
          reject(res);
          return res;
        }
        resolve(res);
      } catch (error) {
        reject(error);
      }
    });
  }

  getCardToken() {
    return new Promise(async (resolve, reject) => {
      let request_id = ''
      try {
        const verifiedResult = (await this.verified()) as any;
        const cardValidateResp = verifiedResult?.props?.newCardValidateResp || {
          success: false, result: { code: '', errorMessage: 'Failed to verify the bank card' }
        }

        if (!cardValidateResp.success) {
          const { errorMessage: message, code } = cardValidateResp.result
          reject({
            request_id,
            message,
            code
          });
          return;
        }

        const { bin, last4, issuer_bin } = this.creditCardInfo || {};
        const params = {
          method_key: "creditcard_braintree",
          transaction_type: "3",
          credit_card_info: { bin, last4, issuer_bin },
        };

        request_id = `checkout-${uuidv4().slice(0, 8)}`
        const res = await this._axios.$post(
          "/v1/cashier/payment/forward/submit",
          params,
          {
            headers: { 'X-Klook-Request-Id': request_id }
          }
        );

        const { native } = res?.result?.action?.action_details || {};
        if (res.success && native.client_token) {
          const {
            props: { tokenResp },
          } = await this.$iframeChannel?.post?.(
            "requestNewCardToken",
            {
              sdkType: native?.sdk_type,
              clientToken: native?.client_token,
              native: native,
              generationTime: new Date().toISOString(),
              stripeCardErrorTips: this.stripeCardErrorTips,
            },
            { typeList: ["generateNewCardToken"] }
          );
          if (tokenResp && tokenResp.success) {
            resolve({
              type: "generateNewCardToken",
              props: tokenResp,
            });
          } else {
            const { message = '', code = '' } = tokenResp?.result || {};
            reject({
              request_id,
              code,
              message: typeof message === 'string'
                ? message
                : JSON.stringify(message)
            });
          }
          return;
        }
        
        ;(Message as any).error(this.__t("172839"));
      
        reject({
          code: res?.error?.code || '',
          request_id,
          message: res?.error?.message || 'request client_token fail'
        });
      } catch (error: any) {
        reject({
          code: error?.code || '',
          request_id,
          message: error?.message
        });
      }
    });
  }

  handleBindEventListener() {
    const handleIframeEvent = (event: {
      data: { type: any; props: any };
      origin: any;
    }) => {
      this.$emit("handle-iframe-event", event);
      const { type, props } = event.data;
      switch (type) {
        case "mountIframe": {
          this.iframeMounted = true;
          this.$nextTick(function () {
            this.$iframeChannel?.post?.("reselect", {}, { reply: false });
          });
          break;
        }
        case "generateNewCardValidateResult": {
          if (!props?.newCardValidateResp?.success) {
            (Message as any).error(
              props?.newCardValidateResp?.result?.errorMessage
            );
          }
          break;
        }
        case "autoHeight": {
          if (!props.height) {
            this.isError = true;
          }
          this.iframeHeight = props.height + 2;
          break;
        }
        case "updatePhoneNumberVisible": {
          this.creditCardInfo = null;
          break;
        }
        case "updateCheckout": {
          this.creditCardInfo = props;
          break;
        }
        default:
          break;
      }
    };

    (window as any)?.addEventListener("message", handleIframeEvent);

    this.$once("hook:beforeDestroy", () => {
      (window as any)?.removeEventListener("message", handleIframeEvent);
    });
  }

  handleCreateIframeChannel() {
    if (!this.$iframeChannel) {
      this.$iframeChannel = new PostChannel(
        getIframeElement()?.contentWindow,
        this.target
      );
      this.$once("hook:beforeDestroy", () => {
        this.$iframeChannel.clearAllListeners();
      });
    } else {
      this.$iframeChannel.clearAllListeners();
    }
  }

  beforeMount() {
    this._axios = this.axios || (window as any)?.$axios;
  }
}
</script>

<style lang="scss" scoped>
.refresh {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
  cursor: pointer;

  .wrong {
    width: 100%;
    margin-top: auto;
    margin-bottom: 12px;
  }

  .refresh-btn {
    margin-bottom: auto;
  }

  .refresh-btn,
  .wrong {
    @include font-body-m-regular;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
  }
}

.refresh,
.loading {
  position: absolute;
  background-color: $color-bg-1;
  width: 100%;
  height: 100%;
  z-index: 9;
  left: 0;
  top: 0;
}

.credit-card-pci {
  background-color: $color-bg-1;
  position: relative;

  &.desktop {
    .bottom-text {
      @include font-body-s-regular;
      color: $color-text-secondary;
    }
  }

  &.mobile {
    @include font-caption-m-regular;
    color: $color-text-placeholder;
  }

  .top-tip {
    @include font-body-s-regular;
    color: $color-text-primary;
    background: $color-info-background;
    display: flex;
    gap: 10px;
    margin: 0 0 16px 0;
    border-radius: $radius-xl;
    padding: 12px;

    ::v-deep .klk-icon {
      color: $color-text-primary;
    }
  }
}

.supported-cards {
  display: flex;
  justify-content: space-between;

  .title {
    span {
      @include font-body-m-semibold;
      color: $color-text-primary;
    }

    div {
      @include font-body-s-regular;
      color: $color-text-placeholder;
    }
  }

  ul {
    display: flex;
    gap: 8px;
    align-items: center;

    li {
      width: 40px;
      border: solid 1px $color-border-normal;
      border-radius: $radius-s;
      overflow: hidden;
      display: flex;
      justify-content: center;
      align-items: center;
      height: 28px;

      img {
        width: 100%;
      }
    }
  }
}
</style>
