<template>
  <div>
    <slot></slot>
  </div>
</template>

<script lang="ts">
import { Component, Vue } from "vue-property-decorator";

@Component
export default class ReviewsLoading extends Vue {
  elementObserver: any = null

  mounted() {
    this.createObserver()
  }

  beforeDestroy() {
    this.destroyObserver()
  }

  createObserver() {
    if ('IntersectionObserver' in window) {
      this.elementObserver = new IntersectionObserver((entries) => {
        entries.forEach((entry) => {
          if (entry.intersectionRatio > 0) {
            this.$emit('init')
          }
        })
      })

      this.elementObserver.observe(this.$el)
    }
  }

  destroyObserver() {
    this.elementObserver && this.$el && this.elementObserver.unobserve(this.$el)
  }
}
</script>

<style lang="scss" scoped>
</style>
