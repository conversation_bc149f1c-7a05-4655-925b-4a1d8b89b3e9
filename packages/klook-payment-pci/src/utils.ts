const iframeOriginWhiteList = [
  'http://127.0.0.1:3000',
  'https://klook-pci-staticfile-dev.s3-ap-southeast-1.amazonaws.com',
  'https://pay.klook.com',
  'https://pay.test.klooktest.com'
]


export const checkOrigin = (origin: string) => {
  return [...iframeOriginWhiteList, location.origin].includes(origin)
}

export const getIframeElement = () => {
  const iframeElement = document.getElementById('pciFrame') as any
  return iframeElement
}

export class PostChannel {
  private dom: Window
  private target: string
  private eventListeners: any[]
  constructor(dom: Window, target: string) {
    this.dom = dom
    this.target = target
    this.eventListeners = []
  }

  addEventListeners(listener: { (e: any): void; (e: any): void; (this: Window, ev: MessageEvent<any>): any }) {
    window.addEventListener('message', listener)
    this.eventListeners.push(listener)
  }

  removeEventListeners(listener: { (e: any): void; (e: any): void; (this: Window, ev: MessageEvent<any>): any }) {
    const index = this.eventListeners.indexOf(listener)
    // 如果存在则在数组中删除
    if (index !== -1) {
      this.eventListeners.splice(index, 1)
    }
    window.removeEventListener('message', listener)
  }

  post(type: any, payload = {}, customConfig: any) {
    return new Promise((resolve, reject) => {
      const config = Object.assign({ reply: true, typeList: [] }, customConfig)
      if (!type) {
        reject(new Error('type or target is empty'))
      }
      this.dom?.postMessage?.({
        type,
        props: payload
      }, this.target)
      if (config.reply) {
        const handle = (e: { origin: any; data: any }) => {
          const ReplyTypeList = config.typeList
          ReplyTypeList.push(type)
          if (checkOrigin(e?.origin) && ReplyTypeList.includes(e?.data?.type)) {
            resolve(e?.data)
            this.removeEventListeners(handle)
          }
        }
        this.addEventListeners(handle)
      } else {
        resolve('')
      }
    })
  }

  clearAllListeners() {
    this.eventListeners.forEach((listener) => {
      window.removeEventListener('message', listener)
    })
  }
}
