<template>
  <div class="pci-frame" :class="platform">
    <iframe
      id="pciFrame"
      ref="pciFrame"
      :src="target"
      frameborder="0"
      :style="{ height: iframeHeight + 'px' }"
    />
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop, Inject } from "vue-property-decorator";

@Component
export default class IframeComp extends Vue {
  @Inject({ default: () => "" }) __t!: Function;
  @Prop() iframeHeight!: string;
  @Prop() target!: string;
  @Prop() platform!: string;
}
</script>

<style lang="scss" scoped>
.pci-frame iframe {
  width: 100%;
  margin-bottom: 16px;
}

.pci-frame.mobile iframe {
  margin-top: 12px;
  margin-bottom: 8px;
}
</style>
