import replace from 'rollup-plugin-replace'
import typescript from 'rollup-plugin-typescript2'
import nodeResolve from 'rollup-plugin-node-resolve'
import commonjs from 'rollup-plugin-commonjs';
import clear from 'rollup-plugin-clear'
import vue from 'rollup-plugin-vue';
import postcss from 'rollup-plugin-postcss'
import babel from 'rollup-plugin-babel'

clear({ targets: ['dist'] })

const {
  name,
  version,
  author
} = require('./package.json')

const banner =
  `/**
  * v${version}
  * (c) ${new Date().getFullYear()} ${author}
  */`
const configs = {
  esm: {
    output: 'dist/esm/index.js',
    format: 'esm',
    target: 'es5',
    env: 'production',
    genDts: true,
    isSSR: false
  },
  cjs: {
    output: 'dist/cjs/index.js',
    format: 'cjs',
    target: 'es5',
    isSSR: true
  }
}

const externals = [
  'vue',
  'vue-lazyload',
  'vue-property-decorator'
]

const genTsPlugin = (configOpts) => typescript({
  useTsconfigDeclarationDir: true,
  tsconfigOverride: {
    compilerOptions: {
      target: configOpts.target,
      declaration: configOpts.genDts
    }
  },
  abortOnError: false
})

const genPlugins = (configOpts) => {
  const plugins = []
  if (configOpts.env) {
    plugins.push(replace({
      'process.env.NODE_ENV': JSON.stringify(configOpts.env)
    }))
  }
  plugins.push(replace({
    'process.env.MODULE_FORMAT': JSON.stringify(configOpts.format)
  }))
  if (configOpts.plugins && configOpts.plugins.pre) {
    plugins.push(...configOpts.plugins.pre)
  }
  plugins.push(genTsPlugin(configOpts))

  plugins.push(nodeResolve({
    extensions: ['.mjs', '.js', '.jsx', '.vue']
  }))

  plugins.push(commonjs())

  plugins.push(vue({
    css: false,
    normalizer: '~vue-runtime-helpers/dist/normalize-component.js',
    template: {
      isProduction: true,
      optimizeSSR: configOpts.isSSR
    },
    style:{
      postcssPlugins:[
        require('autoprefixer')({
          overrideBrowserslist: [
            '> 1%',
            'last 5 versions',
            'ios >= 7',
            'android > 4.4',
            'not ie < 10'
          ]
        }),
        require('cssnano')({
          safe: true
        })
      ]
    }
  }))

  plugins.push(babel({
    include: ['src/**', 'node_modules/**'],
    extensions: ['.js', '.vue','.ts','.jsx','.tsx']
  }))

  // 不必提取css
  plugins.push(postcss({
    extract: false,
    plugins: [
      require('autoprefixer')()
    ]
  }))

  if (configOpts.plugins && configOpts.plugins.post) {
    plugins.push(...configOpts.plugins.post)
  }
  return plugins
}

const genConfig = (configOpts) => ({
  input: 'src/index.js',
  output: {
    banner,
    file: configOpts.output,
    format: configOpts.format,
    name: name,
    sourcemap: false,
    exports: 'named',
    globals: configOpts.globals,
  },
  external: externals,
  plugins: genPlugins(configOpts)
})

const genAllConfigs = (configs) => (Object.keys(configs).map(key => genConfig(configs[key])))

export default genAllConfigs(configs)
