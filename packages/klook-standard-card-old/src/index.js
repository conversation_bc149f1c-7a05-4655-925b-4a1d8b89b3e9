import KlookStandardCard from './standard-activity-card/index.vue';
import VueLazyload from 'vue-lazyload'
const lazyLoadingImage = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABAQMAAAAl21bKAAAABGdBTUEAALGPC/xhBQAAAAFzUkdCAK7OHOkAAAADUExURenp6VcjxIwAAAAKSURBVAjXY2AAAAACAAHiIbwzAAAAAElFTkSuQmCC'

KlookStandardCard.install = function (vue) {
  vue.use(VueLazyload,{
    observer: true,
    loading: lazyLoadingImage,
    error: lazyLoadingImage,
    filter: {
      webp(listener, options) {
        const src = listener.src
        if (!options.supportWebp) {
          return
        }
        const isCDN = /res\.klook\.com/
        if (isCDN.test(src)) {
          listener.src = src.replace(/\.\w+$/, '.webp')
        }
      }
    }
  })
  vue.component('KlkStandardCard', KlookStandardCard);
};

export default KlookStandardCard;
