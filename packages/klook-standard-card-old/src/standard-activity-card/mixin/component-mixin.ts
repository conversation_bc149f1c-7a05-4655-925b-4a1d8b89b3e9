import { Vue, Component, Prop } from 'vue-property-decorator'
import CardIcon from '../component/card-icon.vue'
import PromoTag from "../component/promo-tag.vue";
import CardText from "../component/card-text.vue";
import CardParticipation from "../component/card-participation.vue";
import CardSubTitle from "../component/card-sub-title.vue";
import CardDistance from "../component/card-distance.vue";
import CardVisitor from "../component/card-visitor.vue";
import CardPriceDot from "../component/card-price-dot.vue";
import CardGeneralTag from "../component/card-general-tag.vue";
import CardTitle from "../component/card-title.vue";
import CardPriceDesc from "../component/card-price-desc.vue";
import CardPriceDescSmall from '../component/card-price-desc-small.vue'
import SeoTitle from '../component/seo-title.vue'

@Component({
  components: {
    CardIcon,
    PromoTag,
    CardText,
    CardParticipation,
    CardSubTitle,
    CardDistance,
    CardVisitor,
    CardPriceDot,
    CardGeneralTag,
    CardTitle,
    CardPriceDesc,
    CardPriceDescSmall,
    SeoTitle
  }
})
export default class ComponentMixin extends Vue {
  @Prop({ type: Object, default: () => {} }) cardData!: any
  @Prop({ type: Object, default: () => {} }) jsonSchema!: any

  get isSoldOut() {
    const dataKey = this.jsonSchema && this.jsonSchema.slot.image.dataKey
    const image: any = this.cardData[dataKey]
    return image && image.is_gray || false
  }

  getComponentName(type: string) {
    switch (type) {
      case 'icon':
        return 'CardIcon'
      case 'title':
        return 'CardTitle'
      case 'sub_title':
        return 'CardSubTitle'
      case 'participation':
        return 'CardParticipation'
      case 'general_tag':
        return 'CardGeneralTag'
      case 'promo_tag':
        return 'PromoTag'
      case 'price_desc':
        return 'CardPriceDesc'
      case 'sold_out_price_desc':
        return 'CardSoldOutPriceDesc'
      case 'description_text':
        return 'CardText'
      case 'price_small':
        return 'CardPriceDescSmall'
      case 'seo_title':
        return 'SeoTitle'
      case 'distance':
        return 'CardDistance'
      case 'card_visitor':
        return 'CardVisitor'
      case 'card_price_dot':
        return 'CardPriceDot'
    }
  }
}
