export const getPrice = function (priceNumber: string, priceText: string, idBold: Boolean = false) {
  if (!(priceNumber && priceText)) { return '' }
  let price = ''
  const priceMatch = priceText.match(/\{[^}]+\}/)

  if (priceMatch && priceMatch[0]) {
    if (idBold) {
      price = `<strong>${priceNumber}</strong>`
    } else {
      price = `${priceNumber}`
    }
    price = priceText.replace(priceMatch[0], price)
  }

  return price
}
