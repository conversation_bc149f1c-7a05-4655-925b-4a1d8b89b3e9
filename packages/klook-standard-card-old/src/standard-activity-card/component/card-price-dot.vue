<template>
  <div
    v-if="data"
    class="card-price-dot"
    :style="paddingStyle"
  >
    <div class="card-price-dot_content">
      <template v-if="data.desc">
        <p
          v-for="(descInfo,index) in data.desc "
          :key="index"
          class="card-price-dot_desc"
          :class="{'card-price-dot_desc--dot':descInfo.show_index_symbol}"
          :style="{color:descInfo.text_color,webkitLineClamp:line.toString()}"
        >
          {{ descInfo.text }}
        </p>
      </template>
    </div>
    <div
      v-if="data.discount_tag && data.discount_tag.text"
      :style="getStyle(data.discount_tag)"
      class="card-price-dot_tag"
    >
      {{ data.discount_tag.text }}
    </div>
  </div>
</template>

<script lang="ts">
import { Vue, Component, Prop } from 'vue-property-decorator'

@Component
export default class CardPriceDot extends Vue {
    @Prop({ type: Object, default: () => {} }) data!: any
    @Prop({ type: Number, default: 0 }) placeHolder!: number
    @Prop({ type: Number, default: 1 }) line!: number

    getStyle(tag: any) {
      const style: CSSStyleDeclaration = Object.create(null)
      if (tag.background_color_left && tag.background_color_left !== '' &&
        tag.background_color_right && tag.background_color_right !== '') {
        style.background = `linear-gradient(95.08deg, ${tag.background_color_left} 0%,${tag.background_color_right} 100%)`
      }

      if (tag.text_color && tag.text_color !== '') {
        style.color = tag.text_color
      }

      return style
    }

    get paddingStyle() {
      const paddingStyle = Object.create(null)
      paddingStyle.paddingTop = `${this.placeHolder}px`
      return paddingStyle
    }
}
</script>

<style lang="scss">
  .card-price-dot{
    display: flex;
    flex-wrap: nowrap;
    justify-content: space-between;
    align-items: flex-end;

    &_content{
      flex: 1;
    }

    &_desc{
      position: relative;
      padding-left: 11px;
      width: 100%;
      font-size: 12px;
      line-height: 16px;
      color: #212121;
      word-break: break-all;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 1;

      &--dot{
        &::before{
          content: '';
          display: block;
          position: absolute;
          top: 5px;
          left: 0;
          width: 5px;
          height: 5px;
          border-radius: 50%;
          background-color: #FF9D26;;
        }
      }
    }

    &_tag{
      margin-left:10px ;
      // margin-top: 2px;
      color: #ffffff;
      font-size: 12px;
      font-weight: 600;
      padding: 2px 4px;
      background: #FF9557;
      border-radius: 2px;
    }
  }

</style>
