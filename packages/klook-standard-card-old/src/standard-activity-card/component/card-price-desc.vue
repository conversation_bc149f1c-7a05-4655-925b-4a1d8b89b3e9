<template>
  <div
    v-if="data"
    class="card-price-desc"
    :style="paddingStyle"
  >
    <div class="card-price-desc_content">
      <p
        class="card-price-desc_content-market"
        :style="{ 'textDecoration': data.market_price_strike ? 'line-through' : 'none' }"
      >
        {{ data.market_price }}
      </p>
      <p
        class="card-price-desc_content-sell"
        v-html="getPrice(data.selling_price,data.selling_price_format,true)"
      >
      </p>
    </div>
    <div
      v-if="data.discount_tag && data.discount_tag.text"
      :style="getStyle(data.discount_tag)"
      class="card-price-desc_tag"
    >
      {{ data.discount_tag.text }}
    </div>
  </div>
</template>

<script lang="ts">
import { Vue, Component, Prop } from 'vue-property-decorator'
import { getPrice } from '../util'

type PriceFunction = (priceObj: any, priceText: string, idBold?: Boolean) => String

@Component
export default class CardPriceDesc extends Vue {
  @Prop({ type: Object, default: () => {} }) data!: any
  @Prop({ type: Number, default: 0 }) placeHolder!: number

  getPrice: PriceFunction = getPrice

  getStyle(tag: any) {
    const left = tag.background_color_left
    const right = tag.background_color_right
    const textColor = tag.text_color
    const style = `
          background: ${left};
          background: -moz-linear-gradient(left,  ${left} 0%, ${right} 100%);
          background: -webkit-linear-gradient(left,  ${left} 0%,${right} 100%);
          background: linear-gradient(to right,  ${left} 0%,${right} 100%);
          filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='${left}', endColorstr='${right}',GradientType=1 );
          color: ${textColor}
        `
    return style
  }

  get paddingStyle() {
    const paddingStyle = Object.create(null)
    paddingStyle.paddingTop = `${this.placeHolder}px`
    return paddingStyle
  }
}
</script>

<style lang="scss">
.card-price-desc{
  display: flex;
  flex-wrap: nowrap;
  justify-content: space-between;
  align-items: flex-end;

  &_content{
    flex: 1;
  }

  &_content-sell{
    width: 100%;
    font-size: 12px;
    line-height: 16px;
    color: #212121;
    word-break: break-all;
    // overflow: hidden;
    // text-overflow: ellipsis;
    // display: -webkit-box;
    // -webkit-box-orient: vertical;
    // -webkit-line-clamp: 1;
    // word-break: break-all;

    strong{
      // margin: 0 2px;
      font-size: 14px;
      font-weight: bold;
    }
  }

  &_content-market{
    width: 100%;
    font-size: 12px;
    line-height: 16px;
    color: #999999;
    text-decoration: line-through;
    word-break: break-all;
    // overflow: hidden;
    // text-overflow: ellipsis;
    // display: -webkit-box;
    // -webkit-box-orient: vertical;
    // -webkit-line-clamp: 1;
    // word-break: break-all;
  }

  &_tag{
    margin-left:10px ;
    // margin-top: 2px;
    color: #ffffff;
    font-size: 12px;
    font-weight: 600;
    padding: 2px 4px;
    background: #FF9557;
    border-radius: 2px;
  }
}

</style>
