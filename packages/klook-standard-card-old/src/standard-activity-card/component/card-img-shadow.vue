<template>
  <div class="card-img-shadow" :style="shadowStyle"></div>
</template>

<script lang="ts">
import { Vue, Component, Prop } from 'vue-property-decorator'

  enum Position{
    Top = 1,
    Bottom = 2
  }

@Component
export default class CardImgShadow extends Vue {
    @Prop({ type: Number }) height!: number
    @Prop({ type: Number }) position!: Position

    get shadowStyle() {
      const shadowStyle: CSSStyleDeclaration = Object.create(null)
      shadowStyle.height = `${this.height}px`
      if (this.position === Position.Top) {
        shadowStyle.top = '0px'
      } else if (this.position === Position.Bottom) {
        shadowStyle.bottom = '0px'
      }
      return shadowStyle
    }
}
</script>

<style lang="scss">
.card-img-shadow{
  position: absolute;
  width: 100%;
  background: linear-gradient(180deg, rgba(0, 0, 0, 0.5) 0%, rgba(0, 0, 0, 0) 100%);
}

</style>
