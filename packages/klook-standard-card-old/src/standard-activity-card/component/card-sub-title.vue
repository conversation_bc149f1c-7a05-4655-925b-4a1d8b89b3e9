<template>
  <p
    v-if="data"
    :style="paddingStyle"
    class="new-card-subTitle"
  >
    <img
      v-if="data.icon"
      class="icon"
      width="14"
      height="14"
      :src="data.icon"
      alt="icon"
    >{{ data.text }}
  </p>
</template>

<script lang="ts">
import { Vue, Component, Prop } from 'vue-property-decorator'

@Component
export default class CardSubTitle extends Vue {
  @Prop({ type: Object, default: null }) data!: any

  @Prop({ type: Number, default: 0 }) placeHolder!: number
  @Prop({ type: Number, default: 1 }) line!: number

  get paddingStyle() {
    const paddingStyle: CSSStyleDeclaration = Object.create(null)
    paddingStyle.paddingTop = `${this.placeHolder}px`
    paddingStyle.webkitLineClamp = this.line.toString()
    paddingStyle.color = this.data ? this.data.text_color : '#757575'
    if (this.data && this.data.bold) {
      paddingStyle.fontWeight = 'bold'
    }
    return paddingStyle
  }
}
</script>

<style lang="scss">
.new-card-subTitle{
  display: flex;
  align-items: center;
  max-width: 100%;
  font-size: 12px;
  line-height: 16px;
  color: #757575;
  overflow:hidden;
  text-overflow:ellipsis;
  white-space: normal;
  display:-webkit-box;
  -webkit-box-orient:vertical;
  -webkit-box-align:start;
  -webkit-line-clamp:1;
  z-index: 1;

  .icon{
    vertical-align: middle;
    margin-right: 4px;
  }
}
</style>
