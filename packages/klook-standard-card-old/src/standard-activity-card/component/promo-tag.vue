<template>
  <div class="promo-tag-large-wrapper">
    <template
      v-if="data && data.length > 0"
    >
      <div
        v-for="(promoTagData,index) in data"
        :key="index"
        class="promo-tag-large"
        :class="{ 'promo-tag-large--have-icon': promoTagData.icon_src }"
        :style="getStyle(promoTagData)"
      >
        <img
          v-if="promoTagData.icon_src"
          class="promo-tag-large_icon"
          :src="promoTagData.icon_src"
        />
        <span
          v-if="promoTagData.text"
          class="promo-tag-large_text"
        >{{ promoTagData.text }}</span>
      </div>
    </template>
  </div>
</template>

<script lang="ts">
import { Vue, Component, Prop } from 'vue-property-decorator'

  @Component
export default class PromoTagLarge extends Vue {
    @Prop({ type: Array, default: () => [] }) data!: any

    getStyle(tag: any) {
      const left = tag.background_color_left
      const right = tag.background_color_right
      const textColor = tag.text_color
      const style = `
          background: ${left};
          background: -moz-linear-gradient(left,  ${left} 0%, ${right} 100%);
          background: -webkit-linear-gradient(left,  ${left} 0%,${right} 100%);
          background: linear-gradient(to right,  ${left} 0%,${right} 100%);
          filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='${left}', endColorstr='${right}',GradientType=1 );
          color: ${textColor}
        `
      return style
    }
}
</script>

<style lang="scss">
  .promo-tag-large-wrapper{
    display: flex;
    flex-wrap: wrap;
    max-height: 24px;
    overflow-y: hidden;
    margin-right: 16px;
  }

  .promo-tag-large{
    display:flex;
    align-items: center;
    max-width: 100%;
    margin-right: 10px;
    padding: 2px 4px;
    color: #ffffff;
    font-size: 12px;
    line-height: 16px;
    border-radius: 4px;
    background: #FF5722;

    &_icon{
      width: 20px;
      height: 20px;
    }

    &_text{
      padding: 2px 0;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }

    &:last-of-type{
      margin-right: 0;
    }
  }

</style>
