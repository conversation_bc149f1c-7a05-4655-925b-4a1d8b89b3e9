<template>
  <a
    v-if="data"
    style="display:none;"
    :href="data.seo_link || ''"
  >
    {{ data.seo_title || '' }}
  </a>
</template>
<script lang="ts">
import { Vue, Component, Prop } from 'vue-property-decorator'

@Component
export default class CardTitle extends Vue {
  @Prop({
    type: Object,
    default: () => {
      return { seo_title: '', seo_link: '' }
    }
  }) data!: any
}

</script>
