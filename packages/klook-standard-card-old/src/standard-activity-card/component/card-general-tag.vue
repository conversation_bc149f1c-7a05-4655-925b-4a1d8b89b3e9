<template>
  <div
    v-if="data && data.length > 0"
    class="card-general-tag"
    :style="paddingStyle"
  >
    <span
      v-for="(tag,index) in data"
      :key="index"
      class="card-general-tag_item"
      :style="getStyle(tag)"
    >{{ tag.text }}
    </span>
  </div>
</template>

<script lang="ts">
import { Vue, Component, Prop } from 'vue-property-decorator'

@Component
export default class CardGeneralTag extends Vue {
  @Prop({ type: Array, default: () => [] }) data!: any
  @Prop({ type: Number, default: 0 }) placeHolder!: number

  getStyle(tag: any) {
    const style: CSSStyleDeclaration = Object.create(null)
    if (tag.bg_color && tag.bg_color !== '') {
      style.backgroundColor = tag.bg_color
    }

    if (tag.text_color && tag.text_color !== '') {
      style.color = tag.text_color
    }

    return style
  }

  get paddingStyle() {
    const paddingStyle = Object.create(null)
    paddingStyle.paddingTop = `${this.placeHolder}px`
    return paddingStyle
  }
}
</script>

<style lang="scss">
  .card-general-tag{
    width: 100%;
    overflow: hidden;
    display: flex;
    flex-wrap: wrap;
    max-height: 18px;
    box-sizing: content-box;

    &_item{
      font-size: 12px;
      line-height: 16px;
      color: #757575;
      background: #f5f5f5;
      padding: 1px 6px;
      margin-right: 6px;
      max-height: 18px;
      border-radius: 2px;
    }

    &_item:last-of-type{
      margin-right: 0;
    }
    &_item:first-of-type{
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 1;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }
  }
</style>
