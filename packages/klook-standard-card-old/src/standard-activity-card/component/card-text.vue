<template>
  <div
    v-if="data && data.text"
    class="card-text"
    :style="paddingStyle"
  >
    <div :style="style" class="card-text_wrap">
      <p class="card-text_content">{{ data.text }}</p>
    </div>
  </div>
</template>

<script lang="ts">
import { Vue, Component, Prop } from 'vue-property-decorator'

@Component
export default class CardText extends Vue {
  @Prop({ type: Object, default: () => ({ text: '' }) }) data!: any
  @Prop({ type: Number, default: 6 }) placeHolder!: number

  get style() {
    const style = Object.create(null)
    style.color = this.data? this.data.text_color : '#999999'
    if (this.data && this.data.bg_color) {
      style.background = this.data.bg_color
    }
    return style
  }

  get paddingStyle() {
    const paddingStyle = Object.create(null)
    paddingStyle.paddingTop = `${this.placeHolder}px`
    return paddingStyle
  }
}

</script>

<style lang="scss">
.card-text{

  &_wrap{
    border-radius: 2px;
    padding: 2px 6px;
    color: #999999;
  }

  &_content{
      max-width: 100%;
      line-height: 16px;
      font-size: 12px;
      overflow:hidden;
      text-overflow:ellipsis;
      white-space: normal;
      display:-webkit-box;
      -webkit-box-orient:vertical;
      -webkit-line-clamp:2
  }
}

</style>
