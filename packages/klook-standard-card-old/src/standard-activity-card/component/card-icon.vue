<template>
  <div
    v-if="data && data.icon_src"
    class="card-icon"
  >
    <klk-poptip
      v-if="data.hover_text"
      :content="data.hover_text"
      append-to-body
    >
      <div
        v-lazy:background-image.container="data.icon_src"
        :style="imageStyle"
        class="card-icon_image"
      ></div>
    </klk-poptip>
    <div
      v-else
      v-lazy:background-image.container="data.icon_src"
      :style="imageStyle"
      class="card-icon_image"
    ></div>
  </div>
</template>

<script lang="ts">
import { Vue, Component, Prop } from 'vue-property-decorator'

@Component
export default class CardIcon extends Vue {
    @Prop({ type: Object, default: () => null }) data!: any
    @Prop({ type: Number, default: 20 }) width!: number
    @Prop({ type: Number, default: 20 }) height!: number

    get imageStyle() {
      const width = this.width || 20
      const height = this.height || 20
      return {
        width: `${width}px`,
        height: `${height}px`
      }
    }
}
</script>

<style lang="scss">
  .card-icon{

    &_image{
      width: 20px;
      height: 20px;
      background-size: cover;
      background-repeat: no-repeat;
      background-position: center;
    }
  }

</style>
