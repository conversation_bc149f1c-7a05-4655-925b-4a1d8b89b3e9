<template>
  <div
    v-if="data && data.text"
    class="card-distance"
  >
    {{ data.text }}
  </div>
</template>

<script lang="ts">
import { Vue, Component, Prop } from 'vue-property-decorator'

@Component
export default class CardDistance extends Vue {
  @Prop({ type: Object, default: () => {} }) data!: any
}
</script>

<style lang="scss">
.card-distance{
  padding: 2px 4px 2px 6px;
  background: #FFFFFF;
  box-shadow: 0 0 12px rgba(0, 0, 0, 0.03);
  border-radius: 2px;
}
</style>
