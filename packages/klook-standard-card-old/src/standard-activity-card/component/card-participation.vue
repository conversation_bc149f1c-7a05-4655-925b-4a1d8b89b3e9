<template>
  <p
    v-if="data"
    class="card-participation"
    :style="paddingStyle"
  >
    <template v-if="data.star || data.star === 0">
      <span>
        <svg width="10" height="10" viewBox="0 0 12 12" xmlns="http://www.w3.org/2000/svg" fill="#F58B1B">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M7.42988 4.5H10.4899C10.9835 4.5 11.188 5.15999 10.789 5.46362L8.32333 7.25625L9.26878 10.3007C9.42151 10.7907 8.88424 11.1989 8.48515 10.8962L6.0097 9.0144L3.53425 10.8962C3.13516 11.1989 2.5988 10.7907 2.75062 10.3007L3.69607 7.25625L1.21081 5.46362C0.811721 5.15999 1.01717 4.5 1.5099 4.5H4.56989L5.52607 1.36817C5.67788 0.877275 6.34152 0.877275 6.49424 1.36817L7.42988 4.5Z"></path>
        </svg>
      </span>
      <span
        class="card-participation_number"
      >{{ data.star }}</span>
    </template>
    <span class="card-participation_desc">{{ data.text }}</span>
  </p>
</template>

<script lang="ts">
import { Vue, Component, Prop } from 'vue-property-decorator'

@Component
export default class CardParticipation extends Vue {
  @Prop({
    type: Object,
    default: () => {
      return {
        star: '',
        text: ''
      }
    }
  }) data!: any

  @Prop({ type: Number, default: 0 }) placeHolder!: number

  get paddingStyle() {
    const paddingStyle: CSSStyleDeclaration = Object.create(null)
    paddingStyle.paddingTop = `${this.placeHolder}px`
    return paddingStyle
  }
}
</script>

<style lang="scss">
  .card-participation {
    max-width: 100%;
    line-height: 16px;
    color: #999999;
    align-items: center;
    display: flex;

    &_number{
      margin:0 2px;
      font-weight: bold;
      font-size: 12px;
      line-height: 16px;
      color: #F58B1B;
      align-self: center;
    }

    &_desc{
      font-size: 12px;
      line-height: 16px;
      align-self: center;
      flex-grow: 0;
      word-break: break-all;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: normal;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 1;
    }
  }
</style>
