<template>
  <p
    v-if="data && data.text"
    class="new-card-title"
    :style="style"
  >
    {{ data.text }}
  </p>
</template>

<script lang="ts">
import { Vue, Component, Prop } from 'vue-property-decorator'

@Component
export default class Card<PERSON>itle extends Vue {
  @Prop({
    type: Object,
    default: () => {
      return { text: '' }
    }
  }) data!: any

  @Prop({ type: Number, default: 0 }) placeHolder!: number
  @Prop({ type: Number, default: 1 }) line!: number

  get style() {
    const style: CSSStyleDeclaration = Object.create(null)
    style.paddingTop = `${this.placeHolder}px`
    style.webkitLineClamp = this.line.toString()
    style.color = this.data? this.data.text_color : '#212121'
    return style
  }
}

</script>

<style lang="scss">
.new-card-title{
  max-width: 100%;
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
  color: #212121;
  overflow:hidden;
  text-overflow:ellipsis;
  white-space: normal;
  display:-webkit-box;
  -webkit-box-orient:vertical;
  -webkit-line-clamp:1;
  pointer-events: none;
}
</style>
