<template>
  <div
    class="horizontal-card-container"
    :class="backgroundClass"
    :style="backgroundStyle"
  >
    <div
      v-lazy:background-image.container="backgroundImage"
      class="horizontal-card-container_image"
      :class="{'horizontal-card-container_image--sold': isSoldOut }"
    >
      <div
        v-if="jsonSchema.slot.image.topLeftTag && cardData[jsonSchema.slot.image.topLeftTag.dataKey]"
        class="horizontal-card-container_image-top"
      >
        <component
          :is="getComponentName(jsonSchema.slot.image.topLeftTag.type)"
          v-if="jsonSchema.slot.image && jsonSchema.slot.image.topLeftTag"
          :data="cardData[jsonSchema.slot.image.topLeftTag.dataKey]"
          v-bind="jsonSchema.slot.image.topLeftTag"
        ></component>
      </div>
      <div
        v-if="jsonSchema.slot.image.bottomLeftTag && cardData[jsonSchema.slot.image.bottomLeftTag.dataKey]"
        class="horizontal-card-container_image-bottom"
      >
        <component
          :is="getComponentName(jsonSchema.slot.image.bottomLeftTag.type)"
          v-if="jsonSchema.slot.image && jsonSchema.slot.image.bottomLeftTag"
          :data="cardData[jsonSchema.slot.image.bottomLeftTag.dataKey]"
          v-bind="jsonSchema.slot.image.bottomLeftTag"
        ></component>
      </div>
    </div>
    <div class="horizontal-card-container_content">
      <div class="horizontal-card-container_content-top">
        <div class="horizontal-card-container_content-top-wrapper">
            <div
              v-if="jsonSchema.slot.topItemList && jsonSchema.slot.topItemList.length > 0 "
            >
              <component
                :is="getComponentName(topItem.type)"
                v-for="(topItem,index) in jsonSchema.slot.topItemList"
                :key="index"
                :data="cardData[topItem.dataKey]"
                v-bind="topItem"
              ></component>
            </div>
          <slot name="topList"></slot>
        </div>
        <div
          v-if="jsonSchema.slot.brandingTag && jsonSchema.slot.brandingTag.dataKey && cardData[jsonSchema.slot.brandingTag.dataKey]"
          class="horizontal-card-container_content-top-tag"
        >
            <component
              :is="getComponentName(jsonSchema.slot.brandingTag.type)"
              :data="cardData[jsonSchema.slot.brandingTag.dataKey]"
              v-bind="jsonSchema.slot.brandingTag"
            ></component>
          <slot name="brandingTag"></slot>
        </div>
      </div>
      <div class="horizontal-card-container_content-bottom">

          <div
            v-if="jsonSchema.slot.bottomItemList && jsonSchema.slot.bottomItemList.length > 0 "
            slot="top-list"
          >
            <component
              :is="getComponentName(bottomItem.type)"
              v-for="(bottomItem,index) in jsonSchema.slot.bottomItemList"
              :key="index"
              :data="cardData[bottomItem.dataKey]"
              v-bind="bottomItem"
            ></component>
          </div>
        <slot name="bottomList"></slot>
      </div>
    </div>
    <slot name="bottomContent"></slot>
  </div>
</template>

<script lang="ts">
import { Component, Mixins } from 'vue-property-decorator'
import ComponentMixin from '../mixin/component-mixin'

@Component
export default class HorizontalCardContainer extends Mixins(ComponentMixin) {
  get backgroundImage() {
    const dataKey = this.jsonSchema.slot && this.jsonSchema.slot.image.dataKey
    const image: any = this.cardData[dataKey]
    return (image && image.url) || ''
  }

  get backgroundClass() {
    return this.jsonSchema.cardStyle && this.jsonSchema.cardStyle.hoverClass
      ? `horizontal-card-container--${this.jsonSchema.cardStyle.hoverClass}` : ''
  }

  get backgroundStyle() {
    const style: any = Object.create(null)
    if (this.jsonSchema.cardStyle && this.jsonSchema.cardStyle.borderRadius) {
      style.borderRadius = this.jsonSchema.cardStyle.borderRadius
    }
    if (this.jsonSchema.cardStyle && this.jsonSchema.cardStyle.padding) {
      style.padding = `${this.jsonSchema.cardStyle.padding}px`
    }

    return style
  }

  get paddingValue() {
    let value = '33.3%'
    if (this.cardData.image_info && this.cardData.image_info.height && this.cardData.image_info.width) {
      value = `${this.cardData.image_info.height / this.cardData.image_info.width * 100}%`
    }

    return value
  }
}
</script>

<style lang="scss">
  .horizontal-card-container{
    display: flex;
    padding: 16px 12px;
    width: 100%;
    height: 100%;
    background-color: #ffffff;
    border-radius:6px;
    box-shadow: 0 1px 6px rgba(0, 0, 0, 0.1);

    &--white {
      -webkit-transition: all .2s ease;
      transition: all .2s ease;
      box-shadow: 0px 1px 6px rgba(0, 0, 0, 0.1);
      transform-style:preserve-3d;
      @media (min-width: 600px) {
        &:hover {
          -webkit-transform: translateY(-4px);
          transform: translateY(-4px);
          box-shadow: 0px 4px 20px rgba(0, 0, 0, 0.1);
          z-index: 2;
        }
      }
    }

    &--gray {
      -webkit-transition: all .2s ease;
      transition: all .2s ease;
      box-shadow: 0 0 12px rgba(0, 0, 0, 0.03);

      @media (min-width: 600px) {
        &:hover {
          -webkit-transform: translateY(-4px);
          transform: translateY(-4px);
          box-shadow: 0px 4px 20px rgba(0, 0, 0, 0.1);
          z-index: 2;
        }
      }
    }

    &_image{
      position: relative;
      padding-bottom: 25%;
      width: 25%;
      height: 0;
      flex: 0 0 25%;
      border-radius: 4px;
      background-size: cover;
      background-repeat: no-repeat;
      background-position: center;
      margin-right: 12px;

      @media(min-width: 600px) {
        padding-bottom: 33.3%;
        width: 33.3%;
        height: 0;
        flex: 0 0 33.3%;
      }

      &--sold{
        filter: opacity(.4);
      }
    }

    &_image-top{
      display: flex;
      padding: 2px 2px 0 2px;
    }

    &_image-bottom{
      position: absolute;
      left: 2px;
      bottom: 2px;
    }

    &_content{
      display: flex;
      flex: 1;
      flex-direction: column;
      justify-content: space-between;
    }

    &_content-top{
      display: flex;
    }

    &_content-top-wrapper{
      flex: 1;
    }

    &_content-top-tag{
      margin-left: 6px;
      width: 34px;
    }
  }

</style>
