<template>
  <div
    class="vertical-card-container"
    :class="backgroundClass"
    :style="backgroundStyle"
  >
    <div
      v-lazy:background-image.container="backgroundImage"
      :style="{ paddingBottom: paddingValue }"
      class="vertical-card-container_image"
      :class="{'vertical-card-container_image--sold': isSoldOut }"
    >
      <div class="vertical-card-container_top">
        <component
          :is="getComponentName(jsonSchema.slot.image.topLeftTag.type)"
          v-if="jsonSchema.slot.image.topLeftTag"
          :data="cardData[jsonSchema.slot.image.topLeftTag.dataKey]"
          v-bind="jsonSchema.slot.image.topLeftTag"
        ></component>
        <span v-else></span>
        <component
          :is="getComponentName(jsonSchema.slot.image.topRightTag.type)"
          v-if="jsonSchema.slot.image.topRightTag"
          class="vertical-card-container_top-right-tag"
          :data="cardData[jsonSchema.slot.image.topRightTag.dataKey]"
          v-bind="jsonSchema.slot.image.topRightTag"
        ></component>
      </div>
    </div>
    <div
      class="vertical-card-container_content"
      :style="paddingStyle"
    >
      <div class="vertical-card-container_content-top">
        <div
          v-if="jsonSchema.slot.topItemList && jsonSchema.slot.topItemList.length > 0 "
          class="vertical-card-container_top-list"
        >
          <component
            :is="getComponentName(topItem.type)"
            v-for="(topItem,index) in jsonSchema.slot.topItemList"
            :key="index"
            :data="cardData[topItem.dataKey]"
            v-bind="topItem"
          ></component>
        </div>
        <component
          :is="getComponentName(jsonSchema.slot.brandingTag.type)"
          v-if="jsonSchema.slot.brandingTag"
          class="vertical-card-container_branding-tag"
          :data="cardData[jsonSchema.slot.brandingTag.dataKey]"
          v-bind="jsonSchema.slot.brandingTag"
        ></component>
      </div>
      <div class="vertical-card-container_content-bottom">
        <div
          v-if="jsonSchema.slot.bottomItemList && jsonSchema.slot.bottomItemList.length > 0 "
          slot="top-list"
        >
          <component
            :is="getComponentName(bottomItem.type)"
            v-for="(bottomItem,index) in jsonSchema.slot.bottomItemList"
            :key="index"
            :data="cardData[bottomItem.dataKey]"
            v-bind="bottomItem"
          ></component>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Mixins } from 'vue-property-decorator'
import ComponentMixin from '../mixin/component-mixin'

@Component
export default class VerticalCardContainer extends Mixins(ComponentMixin) {
  get paddingValue() {
    let value = '66.6%'
    if (this.jsonSchema.slot.image && this.jsonSchema.slot.image.height && this.jsonSchema.slot.image.width) {
      value = `${this.jsonSchema.slot.image.height / this.jsonSchema.slot.image.width * 100}%`
    }

    return value
  }

  get backgroundImage() {
    const dataKey = this.jsonSchema.slot && this.jsonSchema.slot.image.dataKey
    const image: any = this.cardData[dataKey]
    return (image && image.url) || ''
  }

  get backgroundClass() {
    return this.jsonSchema.cardStyle && this.jsonSchema.cardStyle.hoverClass
      ? `vertical-card-container--${this.jsonSchema.cardStyle.hoverClass}` : ''
  }

  get backgroundStyle() {
    const style: any = Object.create(null)
    if (this.jsonSchema.cardStyle && this.jsonSchema.cardStyle.borderRadius) {
      style.borderRadius = this.jsonSchema.cardStyle.borderRadius
    }

    return style
  }

  get paddingStyle() {
    const style: any = Object.create(null)
    if (this.jsonSchema.cardStyle && this.jsonSchema.cardStyle.padding) {
      const padding = this.jsonSchema.cardStyle.padding
      style.paddingBottom = `${padding}px`
      style.paddingLeft = `${padding}px`
      style.paddingRight = `${padding}px`
    }

    return style
  }
}
</script>

<style lang="scss">
  .vertical-card-container{
    display: flex;
    flex-direction: column;
    justify-content: stretch;
    width: 100%;
    height: 100%;
    background-color: #ffffff;
    border-radius:6px;
    box-shadow: 0 1px 6px rgba(0, 0, 0, 0.1);

    &--white {
      -webkit-transition: all .2s ease;
      transition: all .2s ease;
      box-shadow: 0px 1px 6px rgba(0, 0, 0, 0.1);
      transform-style:preserve-3d;
      @media (min-width: 600px) {
        &:hover {
          -webkit-transform: translateY(-4px);
          transform: translateY(-4px);
          box-shadow: 0px 4px 20px rgba(0, 0, 0, 0.1);
          z-index: 2;
        }
      }
    }

    &--gray {
      -webkit-transition: all .2s ease;
      transition: all .2s ease;
      box-shadow: 0px 0px 6px rgba(0, 0, 0, 0.03);
      @media (min-width: 600px) {
        &:hover {
          -webkit-transform: translateY(-4px);
          transform: translateY(-4px);
          box-shadow: 0px 4px 20px rgba(0, 0, 0, 0.1);
          z-index: 2;
        }
      }
    }

    &_image{
      border-radius: 6px 6px 0 0;
      padding-bottom: 66.6%;
      height: 0;
      background-size: cover;
      background-repeat: no-repeat;
      background-position: center;

      &--sold{
        filter: opacity(.4);
      }
    }

    &_top{
      display: flex;
      padding: 10px 16px 0 16px;
      align-items: center;
      justify-content: space-between;
    }

    &_content{
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      padding: 0 16px 16px 16px;
    }

    &_content-top{
      display: flex;
      justify-content: space-between;
    }

    &_top-list {
      flex: 1;
    }

    &_branding-tag{
      margin-top: -13px;
    }

    &_top-brand{
      flex: 0 0 44px;
      margin-top: -16px;
      margin-left: 6px;
      width: 44px;
      height: 44px;
      background-size: cover;
      background-position: center;
      background-repeat: no-repeat;
    }
  }

</style>
