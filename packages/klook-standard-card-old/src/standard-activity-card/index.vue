<template>
  <component
    :is="getLayout(jsonSchema.cardStyle && jsonSchema.cardStyle.layout)"
    class="klook-standard-card"
    :card-data="cardData"
    :json-schema="jsonSchema"
    :data-spm-virtual-item="`__virtual?typ=entry`"
    @click.native="handleClick"
  >
    <template slot="bottomList">
      <slot name="bottomList"></slot>
    </template>
    <template slot="bottomContent">
      <slot name="bottomContent"></slot>
    </template>
  </component>
</template>

<script lang="ts">
import { Vue, Component, Prop } from 'vue-property-decorator'
import HorizontalCardContainer from './card-container/horizontal-card-container.vue'
import VerticalCardContainer from "./card-container/vertical-card-container.vue";
import HorizontalPoiCardContainer from './card-container/h-poi-card-container.vue';
import VerticalPoiCardContainer from './card-container/v-poi-card-container.vue'

@Component({
  components: {
    HorizontalCardContainer,
    HorizontalPoiCardContainer,
    VerticalCardContainer,
    VerticalPoiCardContainer
  }
})
export default class StandardActivityCard extends Vue {
  @Prop({ type: Object, default: () => {} }) cardData!: any
  @Prop({ type: Object, default: () => {} }) action!: any
  @Prop({ type: Object, default: () => {} }) jsonSchema!: any

  getLayout(type: string) {
    switch (type) {
      case 'vertical_card_layout':
        return 'VerticalCardContainer'
      case 'horizontal_card_layout':
        return 'HorizontalCardContainer'
      case 'vertical_poi_card_layout':
        return 'VerticalPoiCardContainer'
      case 'horizontal_poi_card_layout':
        return 'HorizontalPoiCardContainer'
    }
  }

  handleClick() {
    this.$emit('click')
    if (this.action && this.action.deep_link) {
      if (this.jsonSchema && this.jsonSchema.cardAction && this.jsonSchema.cardAction.targetBlank) {
        window.location.href = this.action.deep_link
      } else {
        window.open(this.action.deep_link)
      }
    }
  }
}
</script>

<style lang="scss">
.klook-standard-card{
  p{margin: 0;}
}
</style>
