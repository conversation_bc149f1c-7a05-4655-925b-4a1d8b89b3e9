/**
 * Created by <PERSON><PERSON> on 2021/4/2 12:13.
 */

import { STORAGE_KEY, permissionOptions } from '../conf'
import getPermission from '../utils/getPermission'
import permissionChange from '../utils/permissionChange'

function pushDataLayer(data: any) {
  if (!((window as any).dataLayer)) {
    (window as any).dataLayer = []
  }

  (window as any).dataLayer.push(data)
}

function sendGA(data: string) {
  const curPermission = getPermission()

  // 没权限数据
  // 或者由没 analytical 权限变更为有权限 触发上报
  if ((!curPermission || !curPermission.includes(permissionOptions.ANALYTICAL)) && data.includes(permissionOptions.ANALYTICAL)) {
    pushDataLayer({ event: 'postGDPRAnalyticsPageView' })
  }

  if ((!curPermission || !curPermission.includes(permissionOptions.MARKETING)) && data.includes(permissionOptions.MARKETING)) {
    pushDataLayer({ event: 'postGDPRMarketingPageView' })
  }
}

// 设置cookie 权限
export default function setPermission(data: string) {
  // 先发送
  sendGA(data)

  localStorage.setItem(STORAGE_KEY, data)

  // trigger permission changed
  permissionChange()
}

export function setAll() {
  setPermission(JSON.stringify(Object.values(permissionOptions)))
}
