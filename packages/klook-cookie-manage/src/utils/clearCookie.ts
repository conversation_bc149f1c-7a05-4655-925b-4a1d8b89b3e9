/**
 * Created by <PERSON><PERSON>Jun on 2021/4/12 11:50.
 */

import getPermission from './getPermission'
import { cookieDetails, permissionOptions } from '../conf'


// 移除cookie
function removeCookie(name: string, domain: string) {
  const exp = new Date()
  exp.setTime(exp.getTime() - 1)
  document.cookie= `${name}=0; Expires=${exp.toUTCString()}; Path=/; Domain=${domain}`
}

export default function() {
  const curPermission = getPermission() || []
  const pList: permissionOptions[] = [permissionOptions.MARKETING, permissionOptions.ANALYTICAL]

  // 删除没有权限的cookie
  pList.forEach((p) => {
    if (!curPermission.includes(p)) {
      cookieDetails[p].forEach(({ cookies, domain, isOwn }) => {
        // 三方cookie跳过处理
        if (isOwn) {
          cookies.forEach((cookie) => {
            removeCookie(cookie, domain)
          })
        }
      })
    }
  })
}
