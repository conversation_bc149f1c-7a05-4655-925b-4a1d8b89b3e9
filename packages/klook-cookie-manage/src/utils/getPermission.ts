/**
 * Created by <PERSON>.Jun on 2021/4/8 18:58.
 */

import { STORAGE_KEY, HASVISIT_KEY } from '../const'

// 获取cookie 权限
export default function getPermission(): any {
  const storage = localStorage.getItem(STORAGE_KEY)
  try {
    return storage ? JSON.parse(storage) : null
  } catch (e) {
    return null
  }
}

export function hasVisited(): any {
  try {
    const storage = localStorage.getItem(HASVISIT_KEY)
    return storage
  } catch (e) {
    return null
  }
}
