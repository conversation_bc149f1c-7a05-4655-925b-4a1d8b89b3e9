export function transferLinkToButton(linkText: string) {
  try {
    if (typeof linkText !== "string") return "";

    const buttonClassName = "t_href";
    const reg = /<a.*?href=['"](.*?)['"].*?>(.*?)<\/a>/g;

    const hasLink = reg.test(linkText);
    if (!hasLink) return linkText;

    const result = linkText.replace(reg, (match, p1, p2) => {
      return `<button class="${buttonClassName}">${p2}</button>`;
    });
    return result;
  } catch (error) {
    return linkText;
  }
}

export function getLinkAddressInText(text: string) {
  try {
    if (typeof text !== "string") return "";
    const reg = /<a.*?href=['"](.*?)['"].*?>(.*?)<\/a>/;
    if (!reg.test(text)) return "";
    const result = text.match(reg);
    if (!result) return "";
    if (result.length < 2) return "";
    return result[1];
  } catch (error) {
    return "";
  }
}
