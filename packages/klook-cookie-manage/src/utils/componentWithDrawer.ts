import Vue, { CreateElement } from 'vue'
import KlkDrawer from '@klook/klook-ui/lib/drawer/index.js'
import '@klook/klook-ui/lib/drawer/style'
// import '@klook/klook-ui/lib/styles/transitions.scss'

type args = {
  injectionOptions?: object,
  dialogAttrs?: object,
  dialogClass?: object,
  dialogListeners?: any,
  componentAttrs?: object,
  componentListeners?: object,
  lockScroll?: boolean
}

export default function componentWithDrawer(VueComponent: any = null, {
  injectionOptions = {},
  dialogAttrs = {},
  dialogClass = {},
  dialogListeners = {},
  componentAttrs = {},
  componentListeners = {},
  lockScroll = true // 需要支持不锁定滚动条
}: args = {}) {
  if (!VueComponent) {
    throw new Error('必须的参数：VueComponent')
  }

  const DrawerCtor = Vue.extend({
    name: 'Drawer',
    data() {
      return {
        visible: false
      }
    },
    methods: {
      show() {
        (this as any).visible = true;

        (this as any).$nextTick(() => {
          if (!lockScroll) {
            document.documentElement.classList.remove('klk-lock-body-scroll')
            document.body.style.paddingRight = ''
            document.body.style.top = ''
          }
        })
      },
      close() {
        (this as any).visible = false
      }
    },
    render(h: CreateElement) {
      return h(KlkDrawer, {
        on: {
          ...dialogListeners,
          closed: (...args: any[]) => {
            // 传入的方法先执行
            if (dialogListeners.closed) {
              dialogListeners.close.apply(null, args)
            }

            this.$destroy()
            if (this.$el && this.$el.parentElement) {
              this.$el.parentElement.removeChild(this.$el)
            }
          },
          'update:visible': (val: boolean) => {
            (this as any).visible = val
          }
        },
        class: dialogClass,
        attrs: {
          visible: (this as any).visible,
          maskClosable: true,
          direction: 'bottom',
          ...dialogAttrs
        }
      }, [
        h(VueComponent, {
          attrs: {
            ...componentAttrs
          },
          on: {
            // 方便组件内关闭弹窗
            close: () => {
              (this as any).close()
            },
            ...componentListeners
          }
        })
      ])
    }
  })

  const componentDialog = (new DrawerCtor({
    ...injectionOptions
  })).$mount()
  document.body.appendChild(componentDialog.$el)

  componentDialog.$nextTick(() => {
    (componentDialog as any).show()
  })

  return componentDialog
}
