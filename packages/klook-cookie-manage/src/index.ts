/**
 * Created by <PERSON><PERSON>Jun on 2021/4/2 10:56.
 */
import getPermission, { hasVisited } from './utils/getPermission'
import { permissionOptions } from './conf'
import { onPermissionChange } from './utils/permissionChange'

// 属于欧盟地区 且没设置过
function showCookiePop(isEUArea: boolean) {
  return isEUArea && !getPermission()
}

// 不属于欧盟地区 且没设置过
function showNormalCoolie(isEUArea: boolean) {
  const hasVst = hasVisited()
  return !isEUArea && !hasVst
}

type TextObj = Record<string, string>

type CookieManageArgs = {
  platform?: string,
  isEUArea?: boolean
}

/**
 * 自动判断并显示cookie弹窗
 * @param platform pc | m
 * @param isEUArea
 * @param textObj
 */
export default function cookieManage(
  textObj: TextObj = {},
  {
    platform = 'desktop',
    isEUArea = true
  }: CookieManageArgs = {}
) {
  // 清空没有权限的 cookie
  if (isEUArea) {
    import('./utils/clearCookie').then((res) => {
      const clearCookie = res.default
      clearCookie()
    })
  }

  let curFn = null

  // 显示弹窗
  if (showCookiePop(!!isEUArea)) {
    // 拆分代码, 避免文件整体导入
    curFn = platform === 'desktop' ? import('./platform/pc/show') : import('./platform/m/show')
  }

  if (showNormalCoolie(!!isEUArea)) {
    curFn = platform === 'desktop' ? import('./platform/pc/showNormal') : import('./platform/m/showNormal')
  }

  if (curFn) {
    return curFn.then((res: { default: any }) => {
      const showFn = res.default || res
      return showFn(textObj)
    })
  }

  return Promise.resolve()
}

// 点击cookie管理按钮直接显示配置页
export function showSetting(
  textObj: TextObj = {},
  {
    platform = 'desktop'
  }: CookieManageArgs = {}) {
  // 拆分代码, 避免文件整体导入
  const curFn = platform === 'desktop' ? import('./platform/pc/showSetting') : import('./platform/m/showSetting')
  curFn.then((res: { default: any }) => {
    const showFn = res.default || res
    showFn(textObj)
  })
}

// get user permission
export function getUserPermission(isEUArea: boolean) {
  const storePermission = getPermission()
  let marketing = false
  let analytical = false

  // default 用户没设置时
  if (!storePermission || storePermission.length === 0) {
    marketing = analytical = !isEUArea
  } else {
    marketing = storePermission.includes(permissionOptions.MARKETING)
    analytical = storePermission.includes(permissionOptions.ANALYTICAL)
  }

  return {
    marketing,
    analytical
  }
}

export {
  onPermissionChange
}
