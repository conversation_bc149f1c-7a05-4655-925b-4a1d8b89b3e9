<template>
  <button class="cookie_btn" :class="`cookie_btn-${$attrs.btntype || 'default'}`">
    <slot></slot>
  </button>
</template>

<style scoped>
.cookie_btn {
  display: inline-block;
  font-size: 16px;
  font-stretch: normal;
  font-style: normal;
  font-weight: 600;
  border-radius: 4px;
  padding: 10px 20px;
  margin: 0;
  text-align: center;
  line-height: 22px;
  outline: none;
  user-select: none;
  appearance: none;
  text-transform: none;
  text-decoration: none;
  overflow: hidden;
  cursor: pointer;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.cookie_btn +.cookie_btn {
  margin-left: 10px;
}
.cookie_btn-default {
  color: #4a4a4a;
  background-color: rgba(0,0,0,0);
  border: 1px solid #757575;
}
.cookie_btn-default:hover {
  background-color: rgba(0,0,0,0.04);
}
.cookie_btn-default:active {
  background-color: rgba(0,0,0,0.08);
}
.cookie_btn-primary {
  color: #fff;
  background-color: #FF5722;
  border: 1px solid #FF5722;
}
.cookie_btn-primary:hover {
  background-color: #f55421;
  border-color: #f55421;
}
.cookie_btn-primary:active {
  background-color: #eb501f;
  border-color: #eb501f;
}
</style>
