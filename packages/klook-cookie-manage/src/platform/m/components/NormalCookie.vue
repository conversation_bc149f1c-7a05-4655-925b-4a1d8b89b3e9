<template>
  <transition name="fade">
    <div v-if="visible" class="cookie-agreement cookie-manager_normal">
      <div 
        class="cookie-agreement-content" 
        v-html="desc" 
        @click="openCookiePolicy"
      />

      <svg
        class="cookie-manager_normal-close"
        width="16"
        height="16"
        viewBox="0 0 16 16"
        fill="currentColor"
        xmlns="http://www.w3.org/2000/svg"
        @click="close"
      >
        <path fill-rule="evenodd" clip-rule="evenodd" d="M13.7803 2.21967C13.4874 1.92678 13.0126 1.92678 12.7197 2.21967L8 6.93934L3.28033 2.21967L3.19621 2.14705C2.9026 1.9292 2.48594 1.9534 2.21967 2.21967C1.92678 2.51256 1.92678 2.98744 2.21967 3.28033L6.93934 8L2.21967 12.7197L2.14705 12.8038C1.9292 13.0974 1.9534 13.5141 2.21967 13.7803C2.51256 14.0732 2.98744 14.0732 3.28033 13.7803L8 9.06066L12.7197 13.7803L12.8038 13.8529C13.0974 14.0708 13.5141 14.0466 13.7803 13.7803C14.0732 13.4874 14.0732 13.0126 13.7803 12.7197L9.06066 8L13.7803 3.28033L13.8529 3.19621C14.0708 2.9026 14.0466 2.48594 13.7803 2.21967Z"></path>
      </svg>
    </div>
  </transition>
</template>

<script lang="ts">
import { Component } from 'vue-property-decorator'
import { HASVISIT_KEY } from '../../../const'
import Translator from '../../../mixins/Translator'
import {
  transferLinkToButton,
  getLinkAddressInText,
} from "../../../utils/transferLinkToButton";

@Component
export default class CookieAgreement extends Translator {
  scrollY = 0
  visible = false
  timer: any = 0

  get desc() {
    // 这里由于SEO的需求，policy的链接需要用button来实现
    // 这里的4132多语言包含了A标签，需要转换成button
    // 如果多语言替换，这里可以考虑去除 utils/transferLinkToButton.ts 中的转换逻辑 
    const translated = this.trans_("4132") || "";
    const policyText = transferLinkToButton(translated);
    return policyText;
  }

  handleVisible() {
    const scrollTop = document.documentElement.scrollTop || document.body.scrollTop
    this.visible = scrollTop < 5
  }

  debounce(fn: Function, gap: number) {
    return (...params: any) => {
      clearTimeout(this.timer)
      this.timer = setTimeout(() => {
        fn(...params)
      }, gap)
    }
  }

  handleScroll = this.debounce(this.handleVisible, 300)

  mounted() {
    localStorage.setItem(HASVISIT_KEY, 'true')
    this.handleVisible()
    document.addEventListener('scroll', this.handleScroll)
  }

  beforeDestroy() {
    document.removeEventListener('scroll', this.handleScroll)
  }

  close() {
    this.visible = false
    document.removeEventListener('scroll', this.handleScroll)
  }

  openCookiePolicy(event: Event) {
    // 这里由于SEO的需求，policy的链接需要用button来实现
    // 点击多语言中的按钮，跳转到对应的链接
    // 这个方法依赖多语言包中的4132，如果多语言包替换，这里需要修改
    const translated = this.trans_("4132") || "";
    const linkAddress = getLinkAddressInText(translated);
    if (
      event.target &&
      event.target instanceof HTMLElement &&
      event.target.tagName === "BUTTON"
    ) {
      event.preventDefault();
      linkAddress && (location.href = linkAddress);
    }
  }
}
</script>

<style lang="scss" scoped>
  .fade-enter-active, .fade-leave-active {
    transition: opacity .2s;
  }
  .fade-enter, .fade-leave-to {
    opacity: 0;
  }

  .cookie-agreement {
    position: fixed;
    bottom: 0;
    left: 0;
    z-index: 1000;
    background: rgba(0,0,0,0.66);
    color: #ffffff;
    padding: 18px 2px 18px 18px;
    width: 100vw;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
  }

  .cookie-agreement-content {
    font-size: 12px;
    line-height: 14px;
    color: white;
  }

  .cookie-manager_normal-close {
    flex: none;
    margin: 0 16px;
    color: white;
  }

  .cookie-agreement-content {
    & ::v-deep button {
      color: inherit;
      background: none;
      border: none;
      padding: 0;
      cursor: pointer;
    }
  }
</style>
