<template>
  <div
    class="pop_wrap"
    data-spm-module="CookieNotification"
  >
    <div class="pop_scrollBox">
      <h3 class="pop_title">{{ trans_('22904') }}</h3>
      <div class="pop_content" v-html="trans_('22905')"></div>
    </div>
    <div class="pop_tool">
      <CookieButton
        class="pop_toolBtn"
        :data-spm-item="`Tab?ext=${JSON.stringify({TabName: 'Manage'})}`"
        @click.native="handleManage"
        v-galileo-click-tracker="{ spm: 'CookieNotification.Tab', componentName: 'cookie-manage' }"
      >
        {{ trans_('22908') }}
      </CookieButton>
      <CookieButton
        class="pop_toolBtn"
        btntype="primary"
        :data-spm-item="`Tab?ext=${JSON.stringify({TabName: 'Confirm'})}`"
        v-galileo-click-tracker="{ spm: 'CookieNotification.Tab', componentName: 'cookie-manage' }"
        @click.native="handleOk"
      >
        {{ trans_('22906') }}
      </CookieButton>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Mixins } from 'vue-property-decorator'
import Translator from '../../../mixins/Translator'
import componentWithDrawer from '../../../utils/componentWithDrawer'
import { setAll } from '../../../utils/setPermission'

import CookieButton from '../../../components/Button.vue'
import ManagePanel from './ManagePanel.vue'

import '../style.css'

@Component({
  components: {
    CookieButton
  }
})
export default class AskPop extends Mixins(Translator) {
  manageInstance: any = undefined
  handleManage() {
    this.manageInstance = componentWithDrawer(ManagePanel, {
      componentListeners: {
        onSaveSuccess: () => {
          // 关闭设置面板
          (this.manageInstance as any).close()

          // 关闭自己
          this.$emit('close')
        }
      },
      dialogClass: {
        // [(style && style.klkCookiePop) || 'klkCookiePop']: true,
        klkCookiePop: true
      }
    })
  }

  beforeDestroy() {
    if (this.manageInstance && this.manageInstance.close) {
      this.manageInstance.close()
    }
  }

  handleOk() {
    setAll()
    this.$emit('close')
  }
}
</script>

<style scoped>
.pop_scrollBox {
  margin: 20px 0;
  padding: 0 16px;
  max-height: 160px;
  background-color: #FFF;
  overflow: auto;
  overscroll-behavior: contain;
}
.pop_title {
  line-height: 22px;
  font-weight: 600;
  font-size: 16px;
}
.pop_content {
  margin-top: 8px;
  font-weight: normal;
  font-size: 14px;
  line-height: 20px;
}
.pop_tool {
  background-color: #FFF;
  display: flex;
  justify-content: center;
  padding: 8px 16px;
  box-shadow: 0 -0.5px 0 0 rgba(0, 0, 0, 0.12);
}
.pop_toolBtn {
 flex: 1;
}
.pop_content /deep/ a {
  text-decoration: underline;
}

</style>
