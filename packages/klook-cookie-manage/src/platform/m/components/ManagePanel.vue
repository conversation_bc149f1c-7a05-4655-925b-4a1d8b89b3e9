<template>
  <div class="managePanel_wrap">
    <div v-if="!showDetail" class="managePanel_box">
      <div class="managePanel_header">
        <Header @onBack="$emit('close')"></Header>
        <ul ref="navScrollDom" class="managePanelNav_list">
          <li
            v-for="item in navList"
            :key="item.key"
            :class="{
              'managePanelNav_item-active': activeNav === item.key
            }"
            class="managePanelNav_item"
          >
            <a
              :href="`#${item.key}`"
              class="managePanelNav_link"
              @click="handleNavAnchor($event)"
            >{{ item.text }}</a>
          </li>
        </ul>
      </div>
      <div ref="scrollDom" class="managePanel_middle" data-spm-module="CookieManagement">
        <div v-if="navList[0]" :id="`id_${navList[0].key}`" class="cookie_privacy">
          <h3 class="privacy_title">{{ navList[0].text }}</h3>
          <div class="privacy_content">{{ navList[0].des }}</div>
        </div>
        <div class="setting_list">
          <template v-for="item in navList">
            <div
              v-if="item.key !== 'privacy'"
              :id="`id_${item.key}`"
              :key="item.key"
              :class="`setting_item-${item.key}`"
              class="setting_item"
            >
              <div class="setting_titleWrap">
                <h3 class="setting_title">
                  {{ item.text }}
                  <span
                    v-if="item.tag"
                    class="setting_titleTag"
                  >
                    {{ item.tag }}
                  </span>
                </h3>
                <KlkSwitch
                  v-if="!item.tag"
                  v-model="item.openStatus"
                  class="setting_btn setting_btn-switch"
                  :data-spm-item="`Switch?ext=${JSON.stringify({SwitchType: !item.openStatus ? 'On' : 'Off',CookieType: item.cookieType })}`"
                  v-galileo-click-tracker="{ spm: 'CookieManagement.Switch', componentName: 'cookie-manage' }"
                ></KlkSwitch>
              </div>
              <div class="setting_des">
                {{ item.des }}
              </div>
              <a
                href="javascript:;"
                class="setting_viewDetail"
                data-spm-item="Detail"
                v-galileo-click-tracker="{ spm: 'CookieManagement.Detail', componentName: 'cookie-manage' }"
                @click="viewDetail(item)"
              >
                {{ item.detail }}
              </a>
            </div>
          </template>
        </div>
      </div>
      <div class="managePanel_footer" data-spm-module="CookieManagement?trg=manual">
        <CookieButton
          class="saveBtn"
          btntype="primary"
          data-spm-item="Save"
          v-galileo-click-tracker="{ spm: 'CookieManagement.Save', componentName: 'cookie-manage' }"
          @click.native="handleSave"
        >
          {{ trans_('22922') }}
        </CookieButton>
      </div>
    </div>

    <div v-else class="detail_box">
      <ManageDetail
        :title="detailData.title"
        :description="detailData.description"
        :domain-list="detailData.domainList"
        @close="showDetail = false"
      ></ManageDetail>
    </div>
  </div>
</template>

<script lang="ts">
import { Mixins, Component } from 'vue-property-decorator'
import KlkSwitch from '@klook/klook-ui/lib/switch/index.js'
import '@klook/klook-ui/lib/switch/style'
import CookieButton from '../../../components/Button.vue'
import Translator from '../../../mixins/Translator'
import CookieSettingManager from '../../../mixins/CookieSettingManager'
import Header from './Header.vue'
import ManageDetail from './ManageDetail.vue'

// import componentWithDrawer from '../../../utils/componentWithDrawer'
// import style from '../style.module.css'

interface DetailItem {
  title: string,
  description: string,
  domainList: string[]
}

@Component({
  components: {
    Header,
    ManageDetail,
    KlkSwitch,
    CookieButton
  }
})
export default class ManagePanel extends Mixins(Translator, CookieSettingManager) {
  activeNav = 'privacy'
  showDetail = false
  // 默认偏移量
  defaultOffset = 16
  detailData: DetailItem = {
    title: '',
    description: '',
    domainList: []
  }

  setNav(target: HTMLElement, curKey: string, scrollTo = true) {
    this.activeNav = curKey
    const curDom = this.$el.querySelector(`#id_${curKey}`) as HTMLElement

    if (scrollTo) {
      // 保证offsetParent为 managePanel_middle 元素
      (this.$refs.scrollDom as Element).scrollTop = curDom.offsetTop - this.defaultOffset
    }

    const curItem = target.parentElement as HTMLElement
    const itemWidth = curItem.offsetWidth || 0
    const left = curItem.offsetLeft

    const containerWidth = (this.$refs.navScrollDom as HTMLElement).offsetWidth || 0
    ;(this.$refs.navScrollDom as HTMLElement).scrollLeft = left + (itemWidth / 2 - containerWidth / 2)

    // 显示不全 需要移动滚动条
    // if (left + itemWidth > containerWidth) {
    //   (this.$refs.navScrollDom as HTMLElement).scrollLeft = left + (itemWidth / 2 - containerWidth / 2)
    // }
  }

  handleNavAnchor(e: Event) {
    const target = e.target as any
    const curKey = target.getAttribute('href').replace('#', '')

    e.preventDefault()

    this.setNav(target, curKey)
  }

  viewDetail(item: any) {
    // 延迟切换视图，避免track无法分析到spm
    setTimeout(() => {
      this.showDetail = true
    }, 10)

    this.detailData = {
      title: item.text,
      description: item.des,
      domainList: item.domainList
    }
  }

  navItemsPosition: any = undefined
  getNavItemsPosition(): any[] {
    if (!this.navItemsPosition) {
      const scrollElement = this.$refs.scrollDom as HTMLElement
      this.navItemsPosition = Array.from((this.$refs.navScrollDom as HTMLElement).querySelectorAll('.managePanelNav_link')).map((navItemDom: any) => {
        const curKey = navItemDom.getAttribute('href').replace('#', '')
        const target = scrollElement.querySelector(`#id_${curKey}`) as HTMLElement
        return {
          target: navItemDom,
          curKey,
          offsetTop: target.offsetTop
        }
      })
    }

    return this.navItemsPosition
  }

  scrollEvent() {
    const scrollElement = this.$refs.scrollDom as HTMLElement

    const onScrollEvent = () => {
      const navItemsPosition = this.getNavItemsPosition()

      // 获取到第一个匹配的元素
      let curItem: any = navItemsPosition[0]
      navItemsPosition.some((item) => {
        if (item.offsetTop > scrollElement.scrollTop - 90) {
          curItem = item
          return true
        } else {
          return false
        }
      })

      // 滚动的时候先禁止事件
      this.setNav(curItem.target, curItem.curKey, false)
    }

    scrollElement.addEventListener('scroll', onScrollEvent)
    this.$once('hook:beforeDestroy', () => {
      window.removeEventListener('resize', onScrollEvent)
    })
  }

  mounted() {
    this.scrollEvent()
  }
}

</script>

<style scoped>
.managePanel_wrap {
  height: 85vh;
  background-color: #FFFFFF;
}
.managePanel_box {
  display: flex;
  height: 100%;
  flex-direction: column;
}
.managePanel_header {
  padding: 0 16px;
  box-shadow: 0 0.5px 0 0 rgba(0, 0, 0, 0.12);
}
.managePanel_middle {
  position: relative;
  flex: 1;
  overflow: auto;
  overscroll-behavior: contain;
  padding: 20px 16px 0;
}

.managePanelNav_list {
  position: relative;
  overflow: auto;
  display: flex;
  align-items: center;
  flex-wrap: nowrap;
}

.managePanelNav_list::-webkit-scrollbar {
  display: none;
}

.managePanelNav_item {
  flex-shrink: 0;
  flex-grow: 0;
  font-size: 0;
  margin-right: 24px;
  position: relative;
}

.managePanelNav_item-active .managePanelNav_link {
  color: #FF5722;
}

.managePanelNav_item-active:after {
  content: '';
  position: absolute;
  width: 100%;
  height: 3px;
  bottom: 0;
  left: 0;
  background: #FF5722;
  border-radius: 100px 100px 0px 0px;
}

.managePanelNav_link {
  display: inline-block;
  padding: 12px 0;
  font-size: 14px;
  line-height: 18px;
  color: rgba(0, 0, 0, 0.54);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.privacy_title {
  font-weight: bold;
  font-size: 18px;
  line-height: 24px;
  color: #212121;
}

.privacy_content {
  margin-top: 8px;
  font-size: 14px;
  line-height: 20px;
  color: #212121;
}

.setting_list {
  padding: 16px 0;
}

.setting_item {
  padding: 14px;
  background: rgba(73, 133, 230, 0.06);
  border-radius: 6px;
}

.setting_item + .setting_item {
  margin-top: 16px;
}

.setting_titleWrap {
  display: flex;
}

.setting_title {
  flex: 1;
  font-weight: 600;
  font-size: 16px;
  line-height: 22px;
  color: #212121;
}
.setting_btn {
  margin-left: 16px;
}

.setting_titleTag {
  vertical-align: middle;
  display: inline-block;
  padding: 2px 4px;
  font-size: 12px;
  line-height: 16px;
  font-weight: normal;
  color: #16AA77;
  background: #E9F8F1;
  border-radius: 4px;
}

.setting_des {
  margin-top: 12px;
  font-size: 14px;
  line-height: 20px;
  color: #212121;
}

.setting_viewDetail {
  display: inline-block;
  vertical-align: top;
  margin-top: 12px;
  font-size: 14px;
  line-height: 20px;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: #4985E6;
}

.managePanel_footer {
  background-color: #FFF;
  display: flex;
  justify-content: center;
  padding: 8px 16px;
  box-shadow: 0 -0.5px 0 0 rgba(0, 0, 0, 0.12);
}

.saveBtn {
  width: 100%;
}
</style>
