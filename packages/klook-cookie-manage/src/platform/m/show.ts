/**
 * Created by Liu.Jun on 2021/4/2 17:12.
 */

import componentWithDrawer from '../../utils/componentWithDrawer'
import { setTranslate } from '../../utils/translate'
import AskPop from './components/AskPop.vue'

// @ts-ignore
import './style.css'

export default function show(textObj: any) {
  setTranslate(textObj)
  return componentWithDrawer(AskPop, {
    lockScroll: false,
    dialogClass: {
      klkCookiePop: true,
      klkCookieNoMask: true
    }
  })
}
