/**
 * Created by Liu.Jun on 2021/4/12 10:45.
 */

import componentWithDrawer from '../../utils/componentWithDrawer'
import { setTranslate } from '../../utils/translate'
import ManagePanel from './components/ManagePanel.vue'


export default function showSetting($t: any) {
  setTranslate($t)
  const instance: any = componentWithDrawer(ManagePanel, {
    componentListeners: {
      onSaveSuccess: () => {
        // 关闭设置面板
        instance.close()
      },
      dialogClass: {
        // [(style && style.klkCookiePop) || 'klkCookiePop']: true,
        klkCookiePop: true
      }
    }
  })
}

