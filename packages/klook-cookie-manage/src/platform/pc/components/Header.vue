<template>
  <div class="backHead_wrap">
    <span class="backBtn" @click="$emit('onBack')">
      <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M12.5 3.5L5.5 10L12.5 16.5" stroke="#4A4A4A" stroke-width="1.2" stroke-linecap="round" stroke-linejoin="round" />
      </svg>
      <span class="backText">{{ trans_('22923') }}</span>
    </span>
  </div>
</template>

<script lang="ts">
import { Mixins, Component } from 'vue-property-decorator'
import Translator from '../../../mixins/Translator'

@Component({})
export default class Header extends Mixins(Translator) {}

</script>

<style scoped>
.backHead_wrap {
  height: 36px;
  line-height: 36px;
}
.backBtn {
  cursor: pointer;
  display: inline-flex;
  align-items: center;
}
.backText {
  color: #212121;
  font-size: 14px;
}
</style>
