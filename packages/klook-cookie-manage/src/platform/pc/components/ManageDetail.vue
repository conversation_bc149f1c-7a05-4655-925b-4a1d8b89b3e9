<template>
  <div class="manageDetail_wrap">
    <Header @onBack="$emit('close')"></Header>
    <div class="manageDetail_box">
      <h3 class="manageDetail_title">
        {{ title }}
      </h3>
      <div class="manageDetail_des">
        {{ description }}
      </div>
      <ul class="domainList">
        <li v-for="(item, index) in domainList" :key="index" class="domainList_item">
          <span>{{ item }}</span>
        </li>
      </ul>
    </div>
  </div>
</template>

<script lang="ts">
import { Prop, Vue, Component } from 'vue-property-decorator'
import Header from './Header.vue'

@Component({
  components: {
    Header
  }
})
export default class ManageDetail extends Vue {
  @Prop({ type: String, default: '' }) title!: string
  @Prop({ type: String, default: '' }) description!: string
  @Prop({ type: Array, default: () => [] }) domainList!: string[]
}
</script>

<style scoped>
.manageDetail_wrap {
  position: relative;
  flex: 1;
  overflow: auto;
  overscroll-behavior: contain;
}
.manageDetail_box {
  margin-top: 12px;
}
.manageDetail_title {
  font-weight: 600;
  font-size: 16px;
  line-height: 22px;
  color: #212121;
}
.manageDetail_des {
  margin-top: 12px;
  font-size: 14px;
  line-height: 20px;
  color: #212121;
}
.domainList {
  padding-left: 16px;
  list-style: disc;
}
.domainList_item {
  margin-top: 8px;
  font-size: 14px;
  line-height: 20px;
  color: #212121;
}
</style>
