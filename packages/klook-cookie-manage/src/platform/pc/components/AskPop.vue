<template>
  <div class="pop_wrap" data-spm-module="CookieNotification">
    <div class="pop_scrollBox">
      <h3 class="pop_title">{{ trans_('22904') }}</h3>
      <div class="pop_content" v-html="trans_('22905')"></div>
    </div>
    <div class="pop_tool">
      <CookieButton class="pop_toolBtn" btntype="primary" :data-spm-item="`Tab?ext=${JSON.stringify({TabName: 'Confirm'})}`" v-galileo-click-tracker="{ spm: 'CookieNotification.Tab', componentName: 'cookie-manage' }" @click.native="handleOk">
        {{ trans_('22906') }}
      </CookieButton>
      <CookieButton class="pop_toolBtn" :data-spm-item="`Tab?ext=${JSON.stringify({TabName: 'Manage'})}`" v-galileo-click-tracker="{ spm: 'CookieNotification.Tab', componentName: 'cookie-manage' }" @click.native="handleManage">
        {{ trans_('22908') }}
      </CookieButton>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Mixins } from 'vue-property-decorator'
import componentWithDialog from '../../../utils/componentWithDialog'
import CookieButton from '../../../components/Button.vue'
import { setAll } from '../../../utils/setPermission'
import Translator from '../../../mixins/Translator'
import ManagePanel from './ManagePanel.vue'

@Component({
  components: {
    CookieButton
  }
})
export default class AskPop extends Mixins(Translator) {
  manageInstance: any = undefined
  handleManage() {
    this.manageInstance = componentWithDialog(ManagePanel, {
      dialogAttrs: {
        size: 'large'
      },
      componentListeners: {
        onSaveSuccess: () => {
          // 关闭设置面板
          this.manageInstance.close()

          // 关闭自己
          this.$emit('close')
        }
      }
    })
  }

  beforeDestroy() {
    if (this.manageInstance && this.manageInstance.close) {
      this.manageInstance.close()
    }
  }

  handleOk() {
    setAll()
    this.$emit('close')
  }
}
</script>

<style>
.klkCookieNoMask {
  pointer-events: none;
}
.klkCookieNoMask .klk-drawer-mask {
  visibility: hidden;
  pointer-events: none;
  /*display: none;*/
}
.klkCookiePop .klk-drawer-content {
  pointer-events: auto;
  border-radius: 8px 8px 0 0;
  max-height: 100%;
  overflow-y: auto;
}
</style>

<style scoped>
.pop_wrap {
}
.pop_scrollBox {
  background-color: #FFF;
  overflow: auto;
  overscroll-behavior: contain;
  text-align: center;
}
.pop_scrollBox::-webkit-scrollbar {
  display: none;
}
.pop_title {
  font-weight: 600;
  font-size: 14px;
  line-height: 20px;
}
.pop_content {
  margin-top: 8px;
  font-weight: normal;
  font-size: 14px;
  line-height: 20px;
}
.pop_tool {
  margin-top: 20px;
}
.pop_toolBtn {
  line-height: 20px;
  font-size: 14px;
  font-weight: normal;
  padding: 6px;
  width: 100%;
  border-radius: 100px;
}
.pop_toolBtn+.pop_toolBtn {
  margin-left: 0 !important;
  margin-top: 10px;
}
.pop_content /deep/ a {
  text-decoration: underline;
}
</style>
