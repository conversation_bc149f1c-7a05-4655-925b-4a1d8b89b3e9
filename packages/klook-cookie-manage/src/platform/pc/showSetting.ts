/**
 * Created by Liu.Jun on 2021/4/12 10:44.
 */

import componentWithDialog from '../../utils/componentWithDialog'
import { setTranslate } from '../../utils/translate'
import ManagePanel from './components/ManagePanel.vue'


export default function showSetting($t: any) {
  setTranslate($t)
  const instance: any = componentWithDialog(ManagePanel, {
    dialogAttrs: {
      size: 'large'
    },
    componentListeners: {
      onSaveSuccess: () => {
        // 关闭设置面板
        instance.close()
      }
    }
  })
}
