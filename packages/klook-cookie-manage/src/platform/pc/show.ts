/**
 * Created by Liu.Jun on 2021/4/2 17:12.
 */

import Notification from '@klook/klook-ui/lib/notification/index.js'
import '@klook/klook-ui/lib/notification/style'

import { setTranslate } from '../../utils/translate'
import AskPop from './components/AskPop.vue'

export default function show(textObj: any) {
  setTranslate(textObj)

  const instance = Notification({
    position: 'bottom-left',
    showClose: false,
    render (h: any) {
      return h(AskPop, {
        on: {
          close: () => {
            instance.close()
          }
        }
      });
    }
  })

  return instance
}
