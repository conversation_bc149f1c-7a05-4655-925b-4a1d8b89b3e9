/**
 * Created by <PERSON><PERSON> on 2021/4/2 11:00.
 */

import { STORAGE_KEY } from './const'

enum permissionOptions {
  STRICTLY = 'p0',
  MARKETING = 'p1',
  ANALYTICAL = 'p2'
}

type CookieDetailItem = {
  cookies: string[],
  domain: string,
  isOwn?: boolean
}

type CookieDetails = {
  [index: string]: Array<CookieDetailItem>
}

export const isNode = typeof window === 'undefined'

const ownDomain = !isNode ? window.location.hostname.replace(/^.*(\.klook.*$)/,'$1') : '.klook.com'
const ownSubDomain = !isNode ? window.location.hostname : 'www.klook.com'

const cookieDetails: CookieDetails = {
  // 数据不会被使用，暂不配置
  // 必须
  [permissionOptions.STRICTLY]: [{
    cookies: [],
    domain: ownDomain
  }, {
    cookies: [],
    domain: ownSubDomain
  }],

  // 外部记录
  [permissionOptions.MARKETING]: [{
    domain: ownSubDomain,
    cookies: ['__utmz', 'wcs_bt', 'sa-user-id'],
    isOwn: true
  }, {
    domain: ownDomain,
    cookies: ['_gcl_au', '_uetsid', 'cto_bundle', '_uetvid'],
    isOwn: true
  }, {
    domain: '.yandex.ru',
    cookies: ['yandexuid', 'ymex', 'yuidss', 'i']
  }, {
    domain: 'mc.yandex.ru',
    cookies: ['yabs-sid']
  }, {
    domain: '.yahoo.com',
    cookies: ['APID', 'A1', 'GUC', 'A3', 'A1S', 'B']
  }, {
    domain: '.doubleclick.net',
    cookies: ['IDE']
  }, {
    domain: '.ipinyou.com',
    cookies: ['IDE']
  }, {
    domain: '.facebook.com',
    cookies: ['_fbp']
  }, {
    domain: '.bing.com',
    cookies: ['ANON', 'MSPTC', 'MUID', 'NAP']
  }, {
    domain: '.dable.io',
    cookies: ['uid',]
  }, {
    domain: '.google.com',
    cookies: ['SID', 'SSID', 'APISID', 'SAPISID', 'HSID', 'NID', '__Secure-3PSID', '__Secure-3PSIDCC']
  }, {
    domain: '.twitter.com',
    cookies: ['guest_id_ads', 'personalization_id']
  }, {
    domain: '.line.me',
    cookies: ['_ldbrbid']
  }, {
    domain: '.tiktok.com',
    cookies: ['_ttp']
  }, {
    domain: '.stackadapt.com',
    cookies: ['_uetsid', '_uetvid', 'sa-user-id']
  }, {
    domain: '.srv.stackadapt.com',
    cookies: ['sa-user-id']
  }, {
    domain: '.amazon-adsystem.com',
    cookies: ['ad-id', 'ad-privacy']
  }, {
    domain: '.adsrvr.org',
    cookies: ['TDID', 'TDCPM']
  }],

  // 提高
  [permissionOptions.ANALYTICAL]: [{
    domain: ownDomain,
    isOwn: true,
    cookies: ['_ga', '_gat_UA-XXX', '_gid', '_dc_gtm_UA-XXX', '_ga_FW3CMDM313']
  }]
}

export {
  STORAGE_KEY,
  permissionOptions,
  cookieDetails
}
