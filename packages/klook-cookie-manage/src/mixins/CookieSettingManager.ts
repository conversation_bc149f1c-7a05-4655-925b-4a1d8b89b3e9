/**
 * Created by <PERSON><PERSON> on 2021/4/8 09:36.
 */

import { Mixins, Component } from 'vue-property-decorator'
import getPermission from '../utils/getPermission'
import setPermission from '../utils/setPermission'
import { permissionOptions, cookieDetails } from '../conf'
import Translator from './Translator'

interface navObj {
  key: string,
  text: string,
  des: string,
  tag?: string,
  detail?: string,
  permissionKey?: string,
  domainList?: string[],
  openStatus?: boolean,
  cookieType?: string
}

@Component({})
export default class CookieSettingManager extends Mixins(Translator) {
  curPermission: any = []
  navList: any = []

  mounted() {
    // 没配置数据，默认勾选所有权限
    this.curPermission = getPermission() || Object.values(permissionOptions)

    const navList: Array<navObj> = [{
      key: 'privacy',
      text: this.trans_('22909'),
      des: this.trans_('22910')
    }, {
      key: 'strictlyType',
      text: this.trans_('22911'),
      tag: this.trans_('22912'),
      des: this.trans_('22914'),
      detail: this.trans_('22915'),
      permissionKey: permissionOptions.STRICTLY,
      domainList: cookieDetails[permissionOptions.STRICTLY].map(item => item.domain),
      openStatus: true,
      cookieType: 'Necessary'
    }, {
      key: 'marketingType',
      text: this.trans_('22916'),
      des: this.trans_('22917'),
      detail: this.trans_('22915'),
      permissionKey: permissionOptions.MARKETING,
      domainList: cookieDetails[permissionOptions.MARKETING].map(item => item.domain),
      openStatus: this.curPermission.includes(permissionOptions.MARKETING),
      cookieType: 'External'
    }, {
      key: 'analyticalType',
      text: this.trans_('22918'),
      des: this.trans_('22919'),
      detail: this.trans_('22915'),
      permissionKey: permissionOptions.ANALYTICAL,
      domainList: cookieDetails[permissionOptions.ANALYTICAL].map(item => item.domain),
      openStatus: this.curPermission.includes(permissionOptions.ANALYTICAL),
      cookieType: 'Experience'
    }]

    this.navList = navList
  }

  handleSave() {
    const saveData = this.navList.reduce((preVal: string[], curVal: navObj) => {
      if (curVal.openStatus && curVal.permissionKey) {
        preVal.push(curVal.permissionKey)
      }

      return preVal
    }, [])
    setPermission(JSON.stringify(saveData))
    this.$emit('onSaveSuccess')
  }
}
