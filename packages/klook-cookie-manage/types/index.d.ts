/**
 * 自动判断并显示cookie弹窗
 * @param platform pc | m
 * @param isEUArea
 * @param $t
 */

type TextObj = Record<string, string>

export default function cookieManage(textObj: TextObj, { platform, isEUArea }?: {
    platform?: string;
    isEUArea?: boolean;
}): Promise<any>

export function showSetting(textObj: TextObj, { platform }?: {
    platform?: string;
}): void;

export function getUserPermission(isEUArea?: boolean): {
  marketing: boolean,
  Analytical: boolean
}

export function onPermissionChange(callFn: () => void): void
