// 将hex颜色转成rgb
export const hexToRgba = (hex: string, opacity: string) => {
  const red = parseInt('0x' + hex.slice(1, 3))
  const green = parseInt('0x' + hex.slice(3, 5))
  const blue = parseInt('0x' + hex.slice(5, 7))
  const RGBA = `rgba(${red}, ${green}, ${blue}, ${opacity})`
  return RGBA
}
/**
 * Generate a href
 */
export function href(
  pathname: string,
  language: string,
  baseLink: string = ''
) {
  let lang = ''
  if (
    language &&
    language !== 'en' &&
    pathname &&
    !pathname.startsWith('/' + language)
  ) {
    lang = `/${language}`
  }

  return baseLink + lang + pathname
}