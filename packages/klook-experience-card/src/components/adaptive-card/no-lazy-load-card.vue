<template>
  <NoLazyLoadComp
    v-bind="{ ...$props, ...$attrs }"
    :img-url="replacedImgUrl"
  >
    <slot />
  </NoLazyLoadComp>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator'
import { FunctionalComponentOptions, CreateElement, RenderContext } from 'vue'

const NoLazyLoadComp: FunctionalComponentOptions = {
  functional: true,
  props: {
    imgUrl: { type: String },
    coverUseImgTag: { type: Boolean },
    imageAlt: { type: String, default: '' }
  },
  render(h: CreateElement, ctx: RenderContext) {
    const { imgUrl, coverUseImgTag, imageAlt } = ctx.props
    const defaultSlots = (ctx.slots().default || [])

    if (coverUseImgTag) {
      const img = h('img', {
        class: {
          'no-lazy-load-card__img': true
        },
        attrs: {
          ...ctx.data.attrs,
          alt: imageAlt,
          src: imgUrl
        }
      })

      return h(
        'div',
        { slot: 'NoLazyLoad2ImgTag' },
        [img, ...defaultSlots]
      )
    }

    return h(
      'div',
      {
        slot: 'NoLazyLoad2BackgroundImage',
        attrs: ctx.data.attrs,
        style: {
          backgroundImage: `url(${imgUrl})`
        }
      },
      defaultSlots
    )
  }
}

@Component({
  components: {
    NoLazyLoadComp
  }
})
export default class NoLazyLoad extends Vue {
  @Prop() platform!: string
  @Prop() imgUrl!: string
  @Prop({ type: Boolean, default: false }) coverUseImgTag!: boolean
  @Prop({ type: String, default: '' }) imageAlt!: string

  get replacedImgUrl() {
    const w = this.platform === 'desktop' ? 413 : 563 // desktop = 275 * 1.5 mobile = 375 * 1.5
    return this.imgUrl
      .replace('image/upload/', `image/upload/q_85/c_fill,w_${w}/`)
  }
}
</script>

<style lang="scss" scoped>
.no-lazy-load-card__img {
  position: absolute;
  width: 100%;
  height: 100%;
  object-fit: cover;
  z-index: 1;
}
</style>
