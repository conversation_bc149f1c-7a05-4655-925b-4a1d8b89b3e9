<template>
  <img
    src="https://res.klook.com/image/upload/image_logo_mx7wgd.png"
    alt="logoImg"
    :style="{ 'width': width + 'px'}"
    class="v-lazy-logo"
  />
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator'

@Component
export default class Logo extends Vue {
  @Prop({ type: Number }) width!: number
  @Prop({ type: String }) ratio!: string
}
</script>

<style scoped lang="scss">
.v-lazy-logo {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 0;
  height: auto;
}
</style>
