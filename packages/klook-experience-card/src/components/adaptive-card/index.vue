<template>
  <div class="adaptive-card_wrap">
    <component
      :is="component"
      v-bind="$attrs"
      :ratio="ratio"
      :img-url="image"
      :style="linkStyle"
      class="adaptive-card_link"
      :image-type="newImageType"
      :platform="platform"
      @click="handleClick"
    >
      <div class="adaptive-card_content">
        <Logo v-if="!noLazyLoad" :ratio="ratio" :width="logoWidth" />
        <slot></slot>
      </div>
    </component>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator'
import { get } from 'lodash'
import NoLazyLoadCard from './no-lazy-load-card.vue'
import LazyLoadCard from './lazy-load-card.vue'
import Logo from './logo.vue'

@Component({
  components: {
    NoLazyLoadCard,
    LazyLoadCard,
    Logo
  }
})
export default class AdaptiveCard extends Vue {
  @Prop() platform!: string
  @Prop({ type: String }) ratio!: string
  @Prop() image!: string
  @Prop() href!: string
  @Prop() imageType!: string
  @Prop() logoWidth!: number

  get linkStyle() {
    const [width, height] = this.ratio.split(':')
    const percent = Number(width) / Number(height)

    return {
      paddingBottom: `${(1 / percent) * 100}%`
    }
  }

  // 防止a标签嵌套闭合导致的渲染不一致
  handleClick() {
    if (this.href) {
      window.location.href = this.href
    }
  }

  get isDesktop() {
    return this.platform === 'desktop'
  }

  get newImageType() {
    let imgType = this.imageType || ''
    if (!this.isDesktop) {
      const isBigCard = get(this.$attrs, 'isBigCard', false)
      if (isBigCard) {
        imgType = 'experienceMobileActivityCardLarge'
      } else {
        imgType = 'mobileActivityCard'
      }
    }
    return imgType
  }

  get noLazyLoad() {
    return get(this.$attrs, 'noLazyLoad', false)
  }

  get component() {
    return this.noLazyLoad ? 'NoLazyLoadCard' : 'LazyLoadCard'
  }
}
</script>

<style lang="scss">
.adaptive-card_wrap {
  width: 100%;

  .adaptive-card_link {
    display: block;
    background-position: center;
    background-size: cover;
    position: relative;
    border-radius: $radius-s;
    background-color: $color-bg-widget-darker-3;

    .adaptive-card_content {
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
    }
  }
}
</style>
