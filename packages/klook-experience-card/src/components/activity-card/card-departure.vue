<template>
  <div v-if="calcCity" class="exp-departure">
    <span class="exp-departure__content" v-html="calcCity"></span>
  </div>
</template>

<script lang="ts">
import { Vue, Component, Prop } from 'vue-property-decorator'

@Component({
  components: {
    //
  }
})
export default class Index extends Vue {
  @Prop() list!: any[]
  @Prop({ type: Function, default: () => {} }) localesTranslate!: Function

  get calcCity() {
    const arr = this.list || []
    if (!arr.length) {
      return ''
    }
    const city = arr.map((city: string) => {
      return `<span class="exp-departure__city">${city}</span>`
    }).join(', ')
    const cmsid = '101040'
    const str = this.localesTranslate(cmsid, { city })
    return str
  }
}
</script>

<style lang="scss" scoped>
@import '../../styles/mixins.scss';
.exp-departure{
  @include font-body-s-regular;
  color: $color-text-primary;
  &__content{
    @include text-ellipsis(1);
  }
}
</style>
