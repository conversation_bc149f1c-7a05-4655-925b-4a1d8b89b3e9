<template>
  <div v-swiper:$bannerSwiper="bannerSwiperOption" class="swiper-container">
    <div class="swiper-wrapper">
      <div v-for="(slideItem, slideIndex) in bannerList" :key="slideIndex" class="swiper-slide">
        <AdaptiveCard
          :key="slideIndex"
          :image="slideItem"
          class="activity-card-background"
          v-bind="{ ...$attrs, ...getSwiperIndexData(slideIndex, $attrs.noLazyLoad) }"
          :platform="platform"
        />
      </div>
    </div>
    <div class="swiper-pagination">
      <div
        v-for="(slideItem, slideIndex) in bannerList"
        :key="slideIndex"
        class="swiper-pagination-item"
        :class="{ active: currentIndex === slideIndex }"
        @click.stop="changeSlide(slideIndex)"
      ></div>
    </div>
    <slot></slot>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator'
import AdaptiveCard from '../adaptive-card/index.vue'

@Component({
  components: {
    AdaptiveCard
  }
})
export default class CardBanner extends Vue {
  @Prop() isBot!: boolean
  @Prop() platform!: string
  @Prop({ type: Array, default: [] }) bannerList!: any[]
  @Prop() handlerSwiperIndexFn!: Function

  $bannerSwiper: any = null

  getSwiperIndexData(slideIndex: number, noLazyLoad?: undefined | boolean) {
    const { handlerSwiperIndexFn: fn, isBot } = this
    if (typeof fn === 'function') {
      return fn({ slideIndex, isBot, noLazyLoad }) as { noLazyLoad: boolean, fetchpriority?: string }
    }
    return { noLazyLoad }
  }

  bannerSwiperOption = {
    resistanceRatio: 0,
    on: {
      slideChange: () => {
        this.swiperSlideChange()
      }
    }
  }

  currentIndex: number = 0

  swiperSlideChange() {
    const idx = this.$bannerSwiper.realIndex
    this.currentIndex = +idx
  }

  changeSlide(index: number) {
    this.$bannerSwiper.slideTo(index)
  }
}

</script>

<style lang="scss" scoped>
  .swiper-container {
    width: 100%;
    border-radius: $radius-xl;

    .swiper-pagination {
      position: absolute;
      z-index: 1;
      bottom: 0;
      left: 0;
      right: 0;
      height: 36px;
      background: $color-overlay-gradient-2;
      display: flex;
      justify-content: center;
      align-items: center;
      .swiper-pagination-item {
        border-radius: 50%;
        width: 6px;
        height: 6px;
        margin: 0 3px;
        background: $color-divider-dashed-darker;

        &.active {
          background: $color-white;
          width: 8px;
          height: 8px;
        }
      }
    }

    ::v-deep {
      .adaptive-card_wrap .adaptive-card_link,
      img.lazy-load-card__img,
      img.no-lazy-load-card__img {
        border-radius: 0 !important;
      }
    }
  }
</style>
