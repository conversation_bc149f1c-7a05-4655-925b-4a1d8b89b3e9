<template>
  <div v-if="priceData.free_text" class="activity-card-free-text" :class="{ 'small-card': !isLargeCard }">
    {{ priceData.free_text }}
  </div>

  <div v-else class="activity-card-price-box" :class="[isLargeCard ? 'big-card' : 'small-card']">
    <span v-if="fromPrice" class="activity-card-price-box__from-price dynamic-width-price" v-html="fromPrice" />
    <span v-if="!isLargeCard && priceData.underline_price" class="activity-card-price-box__underline-price dynamic-width-price">
      {{ priceData.underline_price }}
    </span>
    <span
      v-if="priceData.sale_price"
      class="activity-card-price-box__sell-price dynamic-width-price"
      :class="{ 'is-sold-out': isSoldOut }"
    >
      {{ priceData.sale_price }}
    </span>
    <span v-if="isLargeCard && priceData.underline_price" class="activity-card-price-box__underline-price dynamic-width-price">
      {{ priceData.underline_price }}
    </span>
    <span v-if="priceData.save_price" class="activity-card-price-box__save-price">
      {{ priceData.save_price }}
    </span>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator'
import { get } from 'lodash'

interface PriceData {
  underline_price: string // 划线价
  from_price: string // 起始价
  sale_price: string // 售卖价（有可能不是价格，也可能是暂停销售）
  save_price: string // 节省的价格
  free_text: string
}

@Component
export default class CardPrice extends Vue {
  @Prop({ type: Boolean, default: false }) isServer!: boolean
  @Prop({ type: Function, default: () => {} }) localesTranslate!: Function
  @Prop({ type: Object, default: () => ({}) }) priceData!: PriceData
  @Prop({ type: Number, default: 14 }) priceFromFontSize!: number
  @Prop({ type: Boolean, default: false }) isSoldOut!: boolean

  get isLargeCard() {
    return get(this.$attrs, 'is-big-card', false)
  }

  get fromPrice() {
    return this.priceData?.from_price ? this.localesTranslate('activity_detail_from_price', [this.priceFromFontSize, this.priceData.from_price]) : ''
  }

  calcWidth(doms: any = null, fontSize: string = '14px') {
    if (this.isServer) { return }
    const targets = doms || this.$el.querySelectorAll('.dynamic-width-price')

    if (targets) {
      const list: any = []
      ;(Array.from(targets) || []).forEach((item: any) => {
        const { clientWidth, scrollWidth } = item
        if (clientWidth < scrollWidth) {
          item.style.fontSize = fontSize
          list.push(item)
        }
      })

      if (list.length) {
        this.$nextTick(() => {
          this.calcWidth(list, '12px')
        })
      }
    }
  }

  mounted() {
    this.calcWidth()
  }
}
</script>

<style lang="scss" scoped>
.activity-card-free-text {
  @include font-body-m-bold;
  color: #212121;

  &.small-card {
    @include font-body-s-bold;
  }
}

.activity-card-price-box {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  align-items: baseline;

  > span {
    flex: none;
    white-space: nowrap;
    max-width: 100%;
    overflow: hidden;
    margin-right: 4px;

    &:last-child {
      margin-right: 0;
    }
  }

  &__from-price {
    ::v-deep {
      b {
        @include font-body-m-bold;
        color: #212121;
      }
      span {
        @include font-body-m-bold;
        color: #212121 !important;
      }
    }
  }

  &__sell-price {
    @include font-body-m-bold;

    &.is-sold-out {
      color: $color-text-secondary;
    }
  }

  &__underline-price {
    color: $color-text-secondary;
    text-decoration: line-through;
    @include font-body-m-regular;
  }

  &__save-price {
    color: #FF5B00;
    @include font-caption-m-semibold
  }

  &.small-card {
    .activity-card-price-box__from-price {
      ::v-deep {
        b {
          @include font-body-s-bold;
          color: #212121;
        }
        span {
          @include font-body-s-bold;
          color: #212121 !important;
        }
      }
    }
    
    .activity-card-price-box__sell-price {
      @include font-body-s-bold;
    }

    .activity-card-price-box__underline-price {
      @include font-body-s-regular;
    }
  }

  &.small-card{
    flex-direction: column;
    flex-wrap: nowrap;
    align-items: flex-start;

    > span {
      margin-right: 0;
    }
  }
}

</style>
