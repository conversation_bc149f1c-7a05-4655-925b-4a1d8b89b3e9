<template>
  <h3 class="activity-card-title" :class="[platform, { 'large-card': isLargeCard }]">
    <a :href="href" :target="target" @click.stop>{{ title }}</a>
  </h3>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator'

@Component
export default class CardTitle extends Vue {
  @Prop() platform!: string
  @Prop() title!: string
  @Prop() href!: string
  @Prop({ type: Boolean, default: true }) isLargeCard!: boolean

  get target() {
    return this.platform === 'desktop' ? '_blank' : '_self'
  }
}
</script>

<style lang="scss" scoped>
.activity-card-title {
  width: 100%;
  font-weight: $fontWeight-bold;
  font-size: $fontSize-body-s;
  line-height: 150%;
  color: $color-text-primary;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;

  &.large-card {
    font-size: $fontSize-body-m;
  }

  a {
    pointer-events: none;
  }
}
</style>
