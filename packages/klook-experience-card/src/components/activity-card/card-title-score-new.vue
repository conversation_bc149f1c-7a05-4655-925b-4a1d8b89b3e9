<template>
  <div v-if="showScore || isNewActivity" class="page-activity-recommend-info-title-review" :class="{ 'is-big-card': isBigCard }" v-bind="$attrs">
    <div v-if="showScore" class="page-activity-recommend-score">
      <span
        v-if="reviewScore > 0"
        class="page-activity-recommend-score-number"
      >
        <IconStar theme="filled" size="12" :fill="'#f09b0a'" />
        <span class="score-rate">{{ star }}</span>
      </span>
      <span v-if="reviewNumber" class="page-activity-recommend-review-number">
        (<span class="review-number">{{ reviewNumber }}</span>)
      </span>
      <i v-if="(reviewScore > 0 || reviewNumber) && showBookedNumber" class="sep" />
      <span v-if="showBookedNumber" class="page-activity-recommend-booked-number">
        {{ bookedNumber }}
      </span>
    </div>
    <div v-if="isNewActivity" class="page-activity-recommend-new-activity">
      <span>{{ reviewData.description }}</span>
    </div>
  </div>
</template>

<script lang="ts">
import { Vue, Component, Prop } from 'vue-property-decorator'
import { IconStar } from '@klook/klook-icons'
import { toNumber } from 'lodash'

interface ReviewData {
  description: string
  score: string // 评分
  number: string // 评论数量
  booked: string // 预定数量
}

@Component({
  components: {
    IconStar
  }
})
export default class CardTitleScore extends Vue {
  @Prop() language!: string
  @Prop() reviewData!: ReviewData
  @Prop({ default: false }) isBigCard!: Boolean
  showBookedNumber = true

  get starIconSize() {
    return this.isBigCard ? 16 : 12
  }

  get reviewScore() {
    return toNumber(this.reviewData?.score)
  }

  get isNewActivity() {
    return !!this.reviewData?.description
  }

  get reviewNumber() {
    return this.reviewData?.number || ''
  }

  get star() {
    if (Number.isInteger(this.reviewScore)) {
      return `${this.reviewScore}.0`
    }

    return this.reviewScore
  }

  get bookedNumber() {
    return this.reviewData?.booked || ''
  }

  get showScore() {
    return !this.isNewActivity && (this.reviewScore > 0 || this.reviewNumber || this.showBookedNumber)
  }

  get calcWidthByLanguage() {
    const arr = ['zh-CN', 'ko', 'zh-HK', 'zh-TW', 'ja']
    return arr.includes(this.language)
  }

  setShowBookedNumber() {
    if (!this.bookedNumber || !this.isBigCard) {
      this.showBookedNumber = false
    }
  }

  calcWidth() {
    if (this.isNewActivity) { return }
    if (this.$el && this.$el.querySelector) {
      const target = this.$el.querySelector('.page-activity-recommend-score')
      const star = this.$el.querySelector('.page-activity-recommend-score-number')
      const reviews = this.$el.querySelector('.page-activity-recommend-review-number') as HTMLElement
      const booked = this.$el.querySelector('.page-activity-recommend-booked-number') as HTMLElement

      if (target) {
        const { clientWidth, scrollWidth } = target

        if (scrollWidth > clientWidth) {
          const calcBookedWidth = this.calcWidthByLanguage ? '0px' : '17px'

          if (star && reviews) {
            if (star.scrollWidth + reviews.scrollWidth > clientWidth) {
              this.showBookedNumber = false
              const width = `calc(100% - ${star.scrollWidth}px - 2px)`
              this.updateElementAttr(reviews, 'style', `width:${width}`)
            } else if (booked) {
              const width = `calc(100% - ${star.scrollWidth}px - ${reviews.scrollWidth}px  - 16px - ${calcBookedWidth})`
              this.updateElementAttr(booked, 'style', `width:${width}`)
            }
          } else if (booked && reviews) {
            if (reviews.scrollWidth > clientWidth) {
              this.showBookedNumber = false
              const width = 'calc(100% - 2px)'
              this.updateElementAttr(reviews, 'style', `width:${width}`)
            } else {
              const width = `calc(100%  - ${reviews.scrollWidth}px - 16px - ${calcBookedWidth})`
              this.updateElementAttr(booked, 'style', `width:${width}`)
            }
          } else if (booked && star) {
            const width = `calc(100% - ${star.scrollWidth}px - 16px - ${calcBookedWidth})`
            this.updateElementAttr(booked, 'style', `width:${width}`)
          }
        }
      }
    }
  }

  updateElementAttr(dom: HTMLElement, attr: any, value: any) {
    dom && dom.setAttribute(attr, value)
  }

  created() {
    this.setShowBookedNumber()
  }

  mounted() {
    this.$nextTick(() => {
      this.calcWidth()
    })
  }
}
</script>

<style scoped lang="scss">
.page-activity-recommend-info-title-review {
  .page-activity-recommend-new-activity {
    @include font-caption-1;
    color: $color-brand-secondary;
  }

  .page-activity-recommend-score {
    display: flex;
    align-items: center;
    height: 17px;
    line-height: 17px;
    font-size: $fontSize-caption-m;

    .page-activity-recommend-score-number {
      border-radius: $radius-l;
      color: $color-brand-secondary;
      font-weight: $fontWeight-semibold;
      flex-shrink: 0;
      display: flex;
      align-items: center;
      margin-right: 2px;
      .score-rate {
        margin: 0 2px 0 3px;
      }
      svg {
        margin-top: -1px;
      }
    }

    .page-activity-recommend-review-number, .page-activity-recommend-booked-number {
      color: $color-text-secondary;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      word-break: break-word;
      flex: none;
    }

    .page-activity-recommend-review-number {
      display: flex;
      align-items: center;

      .review-number {
        display: inline-block;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }

    .sep {
      display: inline-block;
      width: 3px;
      height: 3px;
      border-radius: 50%;
      background-color: $color-neutral-700;
      margin:0 4px;
    }
  }

  &.is-big-card {
    .page-activity-recommend-score {
      height: initial;
      @include font-body-s-regular;
    }

    .page-activity-recommend-new-activity {
      @include font-body-s-bold;
      color: $color-accent-9;
    }
  }
}
</style>
