<template>
  <div class="tagging-tag">
    <klk-poptip v-bind="calcPoptip" :content="platform === 'desktop' && showTips ? $attrs.text : ''">
      <div class="tagging-tag_custom js-tag-body-node" :style="customStyle" @click="showTipsHandler($attrs.text, $event)">
        <span v-if="leftIcon && leftIcon.src" class="tagging-tag_tips">
          <img class="tagging-tag_icon left-icon" v-bind="leftIcon" />
        </span>
        <span class="tagging-tag_text">
          <span class="js-tag-content-node">{{ $attrs.text }}</span>
        </span>
        <template v-if="rightIcon && rightIcon.src">
          <klk-poptip v-if="platform === 'desktop'" class="tagging-tag_tips" :title="rightIcon.desc" dark>
            <img class="tagging-tag_icon right-icon" v-bind="rightIcon" />
          </klk-poptip>
          <span v-else class="tagging-tag_tips">
            <img class="tagging-tag_icon right-icon" v-bind="rightIcon" @click="clickRightIcon(rightIcon.desc)" />
          </span>
        </template>
      </div>
    </klk-poptip>
  </div>
</template>

<script lang="ts">
import { Vue, Component, Prop } from 'vue-property-decorator'
import { merge } from 'lodash'
import Toast from '@klook/klook-ui/lib/toast'
import KlkModal from '@klook/klook-ui/lib/modal'

Vue.use(Toast)
Vue.use(KlkModal)

@Component
export default class ExpTag extends Vue {
  @Prop() platform!: string

  @Prop() autoTips?: boolean
  @Prop({ default: () => ({}) }) poptipData?: Object

  showTips = false

  defObj = {
    dark: true,
    flip: true,
    preventOverflow: true
  }

  showTipsHandler(message: string, _: any) {
    const { platform, showTips } = this
    if (platform === 'mobile' && showTips) {
      (this as any).$toast({
        duration: 2000,
        message
      })
      _.preventDefault()
      _.stopPropagation()
      return false
    }
  }

  get calcPoptip() {
    return merge(this.defObj, this.poptipData)
  }

  get customStyle() {
    const { class: myClass, maxWidth, color, borderColor, backgroundColor, borderRadius } = this.$attrs
    return { class: myClass, maxWidth, color, borderColor, backgroundColor, borderRadius }
  }

  get leftIcon() {
    const obj: any = this.$attrs?.leftIcon
    return obj
  }

  get rightIcon() {
    const obj: any = this.$attrs?.right_icon || {}
    return obj
  }

  clickRightIcon(title: string) {
    (this as any).$alert(title)
  }

  initTips() {
    const { autoTips } = this
    if (autoTips) {
      const dom: any = this.$el.querySelector('.js-tag-content-node')
      const parentNode: any = this.$el.querySelector('.js-tag-body-node')
      if (dom && dom.offsetHeight > parentNode.offsetHeight) {
        this.showTips = true
      }
    } else {
      this.showTips = false
    }
  }

  updated() {
    this.initTips()
  }

  mounted() {
    this.initTips()
  }
}
</script>

<style lang="scss" scoped>
@import '../../styles/mixins.scss';
.tagging-tag{
  &.middle-style.credits-style,
  &.credits-style{
    .tagging-tag_custom{
      position: relative;
      padding-left: 32px;
    }
    img.left-icon{
      position: absolute;
      top: 0;
      left: -1px;
      width: 32px;
      height: 100%;
      margin: 0;
    }
  }
}
.tagging-tag {
  display: inline-block;

  &_custom{
    box-sizing: border-box;
    display: flex;
    align-items: center;
    height: 20px;
    line-height: 18px;
    border: 1px solid #FF5B00;
    color: $color-text-primary-onDark;
    background-color: #FF5B00;
    border-radius: $radius-s;
    overflow: hidden;
    padding: 0 4px;
    @include font-caption-m-semibold;
  }
  &_icon{
    width: 16px;
    height: 16px;
    cursor: pointer;
    &.left-icon{
      margin-right: 4px;
    }
    &.right-icon{
      margin-left: 4px;
    }
  }
  &_text{
    box-sizing: border-box;
    // flex: 1 1 auto;
    @include text-ellipsis(1);
    @include font-caption-m-semibold;
    white-space: pre-wrap;
    word-break: break-all;
  }
  &_tips{
    display: flex;
    align-items: center;
    ::v-deep .klk-poptip-reference{
      height: 16px;
    }
  }
}

.middle-style.tagging-tag{
  .tagging-tag{
    &_custom{
      height: 20px;
      padding: 0 4px;
      @include font-caption-m-semibold;
    }
    &_text {
      @include font-caption-m-semibold;
    }
  }
}
</style>
