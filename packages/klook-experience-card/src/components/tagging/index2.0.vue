<template>
  <div v-if="calcList && calcList.length" class="tagging-wrap" v-bind="bindIHTrack(getTrackObj({ track, pkg, list }))">
    <div class="tagging-box">
      <klk-promo-tag
        v-for="(item, i) in calcList"
        :key="i"
        class="tagging-tag"
        size="small"
        :data="item"
      />
    </div>
  </div>
</template>

<script lang="ts">
import { Vue, Component, Prop } from 'vue-property-decorator'
import { merge } from 'lodash'
import { KlkAtomicBasicTag as KlkPromoTag } from '@klook/klook-card'
import '@klook/klook-card/dist/esm/index.css'
import { handleIHTrack } from '../../utils/inHouseTrack'

@Component({
  components: {
    KlkPromoTag
  }
})
export default class NewExpTagging extends Vue {
  @Prop({
    type: Object,
    default: null
  }) track!: any

  @Prop({
    type: Object,
    default: null
  }) pkg!: any

  @Prop({
    type: Array,
    default() {
      return []
    }
  }) list!: any[]

  get calcList() {
    return Array.isArray(this.list) ? this.list : []
  }

  getTrackObj(obj: any) {
    return getTaggingTrackObj(obj)
  }

  bindIHTrack(...args: any) {
    return handleIHTrack(...args)
  }
}

export const getTaggingExtraTrackObj = (list: any[]) => {
  const arr = list || []
  const extra: any = arr.reduce((o: any, o2: any) => {
    o.TagIDList.push(o2?.track?.tag_id || '')
    o.TagKeyList.push(o2?.track?.tag_key || '')
    return o
  }, { TagIDList: [], TagKeyList: [] })
  return extra
}

export const getTaggingTrackObj = ({ track, pkg, list }: any) => {
  const mergeTrack = track || {}
  if (!mergeTrack?.spm) { return }
  const { package_id, packageId } = pkg || {}
  const pid = package_id || packageId
  // console.log(999999999, pid, pkg, list)
  const extra: any = getTaggingExtraTrackObj(list)

  const def = {
    type: 'module',
    spm: '', // 必需外部传入 Package_Product_Tag
    exposure: true,
    action: false,
    query: {
      oid: pid ? `package_${pid}` : undefined,
      ext: encodeURIComponent(JSON.stringify(extra))
      // ext: JSON.stringify(extra) // test code
    }
  }
  return merge(def, mergeTrack)
}
</script>

<style lang="scss" scoped>
.tagging-wrap{
  overflow: hidden;

  .tagging-box {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: -8px;

    .tagging-tag{
      margin: 0 0 4px 4px;
      &:first-child{
        margin-left: 0;
      }
    }
  }
}
</style>
