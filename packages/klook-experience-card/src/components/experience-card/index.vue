<template>
  <div
    v-if="cardData"
    ref="rwdCard"
    v-bind="$attrs"
    :class="['rwd-activity-card', platform, { 'large-card':isLargeCard, 'has-border': $attrs.hasBorder, 'theme-modern-card': isModernCard }]"
    data-spm-virtual-item="__virtual?typ=entry"
    @click="cardClick"
  >
    <div
      v-if="showWish"
      class="wish-wrap"
    >
      <KlkAtomicFavorite
        v-bind="{
          ...$attrs
        }"
      />
    </div>
    <AdaptiveCard
      v-if="isSingleBanner"
      :ratio="ratio"
      :image="image"
      :image-alt="cardImgAlt"
      :logo-width="logoWidth"
      class="activity-card-background"
      :image-type="imageType"
      :platform="platform"
      v-bind="$attrs"
    >
      <div v-if="bannerInfoTages && bannerInfoTages.length > 0">
        <div class="activity-card-wrap" />
        <div class="activity-card-top" :class="[{'activity-card-top_bigCard' : isLargeCard}]">
          <ExpTagging :platform="platform" class="banner-info-tag" :list="bannerInfoTages" />
        </div>
      </div>
    </AdaptiveCard>
    <CardBanner
      v-else
      :banner-list="bannerList"
      :ratio="ratio"
      :logo-width="logoWidth"
      :image-type="imageType"
      v-bind="$attrs"
      :is-bot="isBot"
      :platform="platform"
    >
      <div v-if="bannerInfoTages && bannerInfoTages.length > 0">
        <div class="activity-card-wrap" />
        <div class="activity-card-top" :class="[{'activity-card-top_bigCard' : isLargeCard}]">
          <ExpTagging :platform="platform" class="banner-info-tag" :list="bannerInfoTages" />
        </div>
      </div>
    </CardBanner>

    <div :class="['activity-card_content', {'large-card_content': isLargeCard, 'theme-modern-card_content': isModernCard}]">
      <div class="activity-card_content-top">
        <div class="activity-card_content-title-section">
          <div class="activity-card_content-top_title">
            <div
              v-if="isLargeCard && summaries.length > 0"
              class="activity-card_content-top_baseinfo"
              :class="{
                '--is-large': isLargeCard
              }"
            >
              <span v-for="(item, index) in summaries" :key="index" class="summary-item">
                <span>{{ item }}</span>
                <i />
              </span>
            </div>

            <CardTitle :platform="platform" :title="cardData.title" :href="cardData.deeplink" :is-large-card="!!isLargeCard"></CardTitle>
            <CardDeparture
              v-if="isLargeCard && calcDepartureList.length"
              :locales-translate="localesTranslate"
              :list="calcDepartureList"
              class="card-departure-wrap"
            ></CardDeparture>

            <div v-if="whatWeLove && isLargeCard" class="seo-content">{{ whatWeLove }}</div>

            <ExpTagging v-if="isLargeCard && showTag('attribute_tags')" :platform="platform" class="activity-card_property" :list="cardData.product_tags.attribute_tags || []" />

            <CardTitleScore
              :language="language"
              class="activity-card_score"
              :review-data="cardData.review"
              :is-big-card="isLargeCard"
            ></CardTitleScore>
          </div>
        </div>
      </div>

      <div class="activity-card-bottom">
        <div>
          <CardPrice
            :locales-translate="localesTranslate"
            :is-server="isServer"
            class="home-activity-card_price"
            :price-data="cardData.price"
            :is-big-card="isLargeCard"
            :is-sold-out="isSoldOut"
            :price-from-font-size="isLargeCard ? 16 : 14"
          ></CardPrice>

          <NewExpTagging v-if="!cardData.price.free_text && showTag('discount_tags')" class="bottom-tag" :list="cardData.new_promo_tags || []" />
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop, Ref } from 'vue-property-decorator'
import { get } from 'lodash'
import { href } from '../../utils/index'
import NewExpTagging from '../tagging/index2.0.vue'
import ExpTagging from '../tagging/index.vue'
import AdaptiveCard from '../adaptive-card/index.vue'
import CardTitle from '../activity-card/card-title.vue'
import CardTitleScore from '../activity-card/card-title-score-new.vue'
import CardPrice from '../activity-card/card-price-new.vue'
import CardDeparture from '../activity-card/card-departure.vue'
import CardBanner from '../activity-card/card-banner.vue'

import { KlkAtomicFavorite } from '@klook/klook-card'
import '@klook/klook-card/dist/esm/index.css'

enum ThemeEnum {
  MODERN = 'modern',
  NORMAL = 'normal'
}

@Component({
  components: {
    NewExpTagging,
    ExpTagging,
    CardDeparture,
    AdaptiveCard,
    CardTitle,
    CardTitleScore,
    CardPrice,
    CardBanner,
    KlkAtomicFavorite
  }
})
export default class ExperienceCard extends Vue {
  @Prop({ type: Boolean, default: false }) isBot!: boolean
  @Prop({ type: Boolean, default: false }) isServer!: boolean
  @Prop({ type: Function, default: () => {} }) localesTranslate!: Function
  @Prop() language!: string
  @Prop() platform!: string
  @Prop() cardData!: any
  // 卡片裁剪类型
  @Prop() imageType!: string
  // 卡片类型
  @Prop({ default: 'large' }) type!: 'large' | 'small' | 'no-limit'
  @Prop({ default: ThemeEnum.NORMAL }) theme!: ThemeEnum
  @Prop() cardType!: number
  @Prop() logoWidth!: number
  @Prop({ type: Boolean, default: false }) needBanner!: boolean
  @Prop({ type: Boolean, default: false }) showWish!: boolean
  @Prop({ type: Boolean, default: false }) isWished!: boolean

  @Ref() rwdCard!: any

  get bannerList() {
    // const image = this.image
    // return [image, image, image, image]
    return this.cardData?.brand_info?.imgs ?? []
  }

  get cardImgAlt() {
    return this.cardData?.brand_info?.image_alt ?? ''
  }

  get isSingleBanner() {
    const { isDesktop, needBanner, bannerList } = this
    return isDesktop || bannerList.length < 2 || !needBanner
  }

  get isDesktop() {
    return this.platform === 'desktop'
  }

  get isLargeCard() {
    return get(this.$attrs, 'isBigCard', '')
  }

  get image() {
    return this.cardData?.brand_info?.image_url || ''
  }

  get bannerInfoTages() {
    return this.cardData?.brand_info?.tags || []
    // return [{ text: 'aaa' }, { text: 'bbb' }, { text: 'ccc' }]
  }

  get isLarge() {
    return this.type === 'large'
  }

  get isTagsHidden() {
    return !this.isDesktop && !this.isLarge
  }

  get svgStyle() {
    const num = this.isDesktop ? '24px' : '20px'
    return {
      width: num,
      height: num
    }
  }

  // 卡片图片的宽高比
  get ratio() {
    const ratio = this.$attrs.ratio || ''
    if (ratio) {
      return ratio
    }

    return this.isLargeCard ? '16:9' : '4:3'
  }

  get whatWeLove() {
    return this.cardData?.seo?.trim() || ''
  }

  cardClick() {
    setTimeout(() => {
      if (this.platform === 'desktop') {
        window.open(this.getHref(this.cardData.deeplink))
      } else {
        window.location.href = (this.getHref(this.cardData.deeplink))
      }
    })
  }

  getHref(path: string) {
    return href(path, this.language)
  }

  get isSoldOut() {
    return this.cardData?.sold_out || false
  }

  get calcDepartureList() {
    return this.cardData?.departures || []
  }

  get summaries() {
    const { distance = '', summaries = [] } = this.cardData || {}
    distance && Array.isArray(summaries) && summaries.push(distance)
    return summaries ?? []
  }

  showTag(attr: string) {
    return !!this.cardData?.product_tags?.[attr]?.length
  }

  get isModernCard() {
    return this.isDesktop ? this.theme === ThemeEnum.MODERN : true
  }
}
</script>

<style lang="scss" scoped>
@import '../../styles/mixins.scss';
.card-departure-wrap{
  margin: 4px 0 0 0;
}
  .rwd-activity-card {
    display: flex;
    flex-direction: column;
    background: $color-bg-widget-normal;
    transition: all 0.2s ease;
    cursor: pointer;
    border-radius: $radius-l;
    overflow: hidden;
    min-height: 100%;
    width: 100%;
    position: relative;

    .wish-wrap {
      position: absolute;
      top: 12px;
      right: 12px;
      z-index: 2;
      cursor: pointer;
    }

    &.large-card {
      border-radius: $radius-xl;
    }

    &:not(.large-card) {
      ::v-deep .tagging-tag_custom {
        @include font-caption-m-semibold;
      }
    }

    &.has-border {
      border: 1px solid $color-border-normal;
    }

    &.desktop:hover {
      transform: translateY(-4px);
      box-shadow: $shadow-hover-1;
    }

    &.desktop.large-card {
      // (1160 - 270 - 40) / 4
      width: 275px;
    }

    &.desktop {
      width: 216px;
      // border-radius: $radius-xl;

      .activity-card-tags {
        left: 0;
        bottom: 14px;
      }
    }

    ::v-deep .adaptive-card_wrap {
      flex: none;

      .adaptive-card_link {
        border-radius: $radius-none;
      }
    }

    &.theme-modern-card {
      height: initial;
      min-height: initial;
      border: none;
      border-radius: 0;
      background-color: $color-transparent;

      ::v-deep {
        .adaptive-card_wrap .adaptive-card_link,
        img.lazy-load-card__img,
        img.no-lazy-load-card__img {
          border-radius: $radius-xl;
        }

        .activity-card-wrap {
          border-radius: $radius-xl $radius-xl 0 0;
        }

        .swiper-pagination {
          border-radius: 0 0 $radius-xl $radius-xl;
        }
      }
    }

    .activity-card-wrap {
      position: absolute;
      height: 44px;
      left: 0;
      right: 0;
      top: 0;
      z-index: 1;
      /* stylelint-disable */
      background: linear-gradient(180deg, rgba(0, 0, 0, 0.3) 0%, rgba(0, 0, 0, 0) 100%);
      /* stylelint-enable */
    }

    &.large-card .activity-card-wrap {
      height: 48px;
    }

    .activity-card-top {
      position: absolute;
      left:0;
      right:0;
      top: 0;
      z-index: 1;
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 44px;
      padding:0 12px;

      &.activity-card-top_bigCard {
        padding:0 16px;
        height: 48px;
      }

      &.flex-end {
        justify-content: flex-end;
      }
    }

    .banner-info-tag {
      height: 20px;
      line-height: 20px;
    }

    // 内容区域
    .activity-card_content {
      padding: 8px 12px 12px;
      background-color: $color-bg-widget-normal;
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      border-radius: $radius-none $radius-none $radius-l $radius-l;

      .activity-card_content-top {
        .activity-card_content-title-section {
          display: flex;
          justify-content: space-between;

          .activity-card_content-top_title {
            width: 100%;
            overflow: hidden;

            .activity-card_content-top_baseinfo {
              font-size: $fontSize-caption-m;
              line-height: 17px;
              overflow: hidden;
              padding-bottom: 4px;
              text-overflow: ellipsis;
              white-space: nowrap;
              color: $color-neutral-700;

              &.--is-large {
                @include font-body-s-regular;
              }

              i {
                display: inline-block;
                width: 3px;
                height: 3px;
                border-radius: 50%;
                background: $color-neutral-700;;
                vertical-align: middle;
                margin-right: 4px;
              }

              .summary-item:last-child {
                i {
                  display: none;
                }
              }
            }
          }
        }
      }

      .seo-content {
        font-style: normal;
        line-height: 17px;
        color: $color-neutral-700;
        font-size: $fontSize-caption-m;
        font-weight: $fontWeight-regular;

        @include text-ellipsis(1);
        padding-top: 2px;
      }

      &.large-card_content {
        padding: 12px 16px 16px;

        .seo-content {
          @include font-body-s-regular;
        }

        .seo-content {
          padding-top: 4px;

        }
      }

      &.theme-modern-card_content {
        padding: 8px 0 0;
        background-color: $color-transparent;

        &.large-card_content {
          padding: 12px 0 0;
        }

        .activity-card-bottom {
          padding-top: 8px;
        }
      }
    }

    .activity-card_score {
      padding-top: 4px;
    }

    .activity-card_property, .bottom-tag {
      height: 20px;

      &.small-style {
        height: 20px;
      }
    }

    .activity-card_property {
      margin-top: 4px;

      ::v-deep {
        .middle-style.tagging-tag .tagging-tag_custom, 
        .middle-style.tagging-tag .tagging-tag_text {
          @include font-caption-m-regular;
        }
      }
    }

    .bottom-tag {
      margin-top: 2px;
    }


    .activity-card-bottom {
      padding-top: 12px;
    }

    &.mobile {

      &.small {
        .activity-card_content {
          padding: 8px 12px 12px;
        }
      }

    }
  }
</style>
