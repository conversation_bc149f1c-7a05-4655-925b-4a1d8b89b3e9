import replace from "rollup-plugin-replace";
import typescript from "rollup-plugin-typescript2";
import vue from "rollup-plugin-vue";
import postcss from "rollup-plugin-postcss";
import nodeResolve from "rollup-plugin-node-resolve";
import svgPlugin from "rollup-plugin-vue-inline-svg";
import clear from "rollup-plugin-clear";
import pkg from "./package.json";

const commonjs = require("rollup-plugin-commonjs");
const babel = require("rollup-plugin-babel");

// 删除dist
clear({ targets: ["dist"] });

const { name, version, author } = require("./package.json");

const banner = `/**
  * v${version}
  * (c) ${new Date().getFullYear()} ${author}
  */`;
const configs = {
  esm: {
    widthKlookUI: false,
    dir: "dist/esm/",
    output: "dist/esm/index.js",
    format: "esm",
    target: "es5",
    env: "production",
    genDts: true,
  },
  cjs: {
    widthKlookUI: false,
    dir: "dist/cjs/",
    output: "dist/cjs/index.js",
    inlineDynamicImports: true,
    format: "cjs",
    target: "es5",
  },
  // esm_klook_ui: {
  //   widthKlookUI: true,
  //   dir: "dist/esm-klook-ui/",
  //   format: "esm",
  //   target: "es5",
  //   env: "production",
  //   genDts: true,
  //   output: "dist/esm-klook-ui/index.mjs",
  //   outputOptions: {
  //     entryFileNames: "index.mjs",
  //     chunkFileNames: "[name]-[hash].mjs",
  //   },
  // },
  // cjs_klook_ui: {
  //   widthKlookUI: true,
  //   dir: "dist/cjs-klook-ui/",
  //   output: "dist/cjs-klook-ui/index.js",
  //   inlineDynamicImports: true,
  //   format: "cjs",
  //   target: "es5",
  // },
};

const genTsPlugin = (configOpts) =>
  typescript({
    useTsconfigDeclarationDir: true,
    tsconfigOverride: {
      compilerOptions: {
        target: configOpts.target,
        declaration: configOpts.genDts,
      },
      exclude: ["**/__tests__", "test-dts"],
    },
    abortOnError: false,
  });

const genPlugins = (configOpts) => {
  const plugins = [];
  if (configOpts.env) {
    plugins.push(
      replace({
        "process.env.NODE_ENV": JSON.stringify(configOpts.env),
      })
    );
  }
  plugins.push(
    svgPlugin({
      svgoConfig: {
        plugins: [
          { removeXMLNS: true },
          { removeViewBox: false },
          { removeDimensions: true },
          {
            removeAttrs: {
              elemSeparator: "#",
              attrs: "svg#xmlns:xlink",
            },
          },
        ],
      },
    })
  );
  plugins.push(
    nodeResolve({
      extensions: [".mjs", ".js", ".jsx", ".vue"],
    })
  );
  plugins.push(
    commonjs({
      include: /node_modules/,
    })
  );
  plugins.push(
    replace({
      "process.env.MODULE_FORMAT": JSON.stringify(configOpts.format),
    })
  );
  if (configOpts.plugins && configOpts.plugins.pre) {
    plugins.push(...configOpts.plugins.pre);
  }
  plugins.push(genTsPlugin(configOpts));

  plugins.push(
    vue({
      css: false,
      normalizer: "~vue-runtime-helpers/dist/normalize-component.js",
      template: {
        isProduction: true,
      },
      style: {
        postcssPlugins: [require("autoprefixer")()],
        preprocessStyles: true,
        preprocessOptions: {
          scss: {
            data: '@import "../klook-ui/src/styles/token/index.scss";',
          },
        },
      },
    })
  );

  plugins.push(
    babel({
      include: ["src/**", "node_modules/**"],
      extensions: [".js", ".vue"],
    })
  );

  // 不必提取css
  plugins.push(
    postcss({
      extract: true,
      plugins: [require("autoprefixer")()],
    })
  );

  if (configOpts.plugins && configOpts.plugins.post) {
    plugins.push(...configOpts.plugins.post);
  }
  return plugins;
};

const genConfig = (configOpts) => ({
  input: "src/index.ts",
  inlineDynamicImports: configOpts.inlineDynamicImports,
  output: {
    banner,
    dir: configOpts.dir,
    format: configOpts.format,
    name,
    sourcemap: false,
    exports: "named",
    ...(configOpts.outputOptions || {})
  },
  external(id) {
    // cjs: 不外置scss/css，其它外置
    // esm: 所有的都外置
    // cjs-klook-ui: 都不外置
    // esm-klook-ui: 都不外置
    if (
      configOpts.widthKlookUI &&
      (id.includes("@klook/") || id.includes("vue-") || id.includes("swiper"))
    ) {
      return false;
    }

    if (
      configOpts.format === "cjs" && id.includes("@klook/") &&
      (id.endsWith(".scss") || id.endsWith(".css"))
    ) {
      return false
    }

    return (
      Object.keys(pkg.peerDependencies).includes(id.split("/")[0]) ||
      id.includes("@klook/")
    );
  },
  plugins: genPlugins(configOpts),
});

const genAllConfigs = (configs) =>
  Object.keys(configs).map((key) => genConfig(configs[key]));

export default genAllConfigs(configs);
