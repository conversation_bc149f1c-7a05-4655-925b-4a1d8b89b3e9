import KlookStandardCard from './main/index.vue';
import VueLazyload from 'vue-lazyload'
const lazyLoadingImage = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABAQMAAAAl21bKAAAABGdBTUEAALGPC/xhBQAAAAFzUkdCAK7OHOkAAAADUExURenp6VcjxIwAAAAKSURBVAjXY2AAAAACAAHiIbwzAAAAAElFTkSuQmCC'

KlookStandardCard.install = function (vue, opt = {}) {
  vue.use(VueLazyload,{
    observer: true,
    loading: lazyLoadingImage,
    error: lazyLoadingImage,
    filter: {
      webp(listener, options) {
        const src = listener.src
        if (!options.supportWebp) {
          return
        }
        const isCDN = /res\.klook\.com/
        if (isCDN.test(src)) {
          listener.src = src.replace(/\.\w+$/, '.webp')
        }
      }
    }
  })
  vue.component(opt.name || 'KlkStandardCard', KlookStandardCard);
};

export default KlookStandardCard;
