// @ts-ignore
import Vue, {VNodeData} from "vue"
import {prefix} from 'inline-style-prefixer'
// @ts-ignore
import objectPath from 'object-path'
import {getColor} from "./design-token/token-utils";

export interface ModuleExt{
  [propName: string]: string | number
}

export interface InHouseModule {
  moduleName?: string
  moduleExt?: ModuleExt
  index?: number
  length?: number
  objectId?: string
}

export type TagSize = 'small' | 'big'

export interface TagViewStyle { sizeStyle?: TagSize, lines?: number }

export interface CardSchemaModel {
  style_config: any
  style_name: string
  style_id: number
  card_name: string
  card_id: number
}


const BoxShadow = ['klk-standard-card','klk-standard-card--white','klk-standard-card--gray']

const ComponentReg = /^(TextView|TextViewCustomTag|ImageView|ReviewView|GeneralTagView|PromotionTagView|VisitorView|GeneralPriceView|SellingPriceView|DescPriceView|SmallPriceView|Seo|Favorite|CouponTagView|DiscountView|CardDiscountTag)$/

/**
 * 处理图片URL：宽高参数等
 * @param url
 * @param options
 * @returns
 */
export function formatImgUrl(url: string, options: any = {}) {
  if (!url.includes('res.klook.com')) {
    return url
  }

  const params = []
  if (options.width) {
    params.push(`w_${options.width}`)
  }

  if (options.height) {
    params.push(`h_${options.height}`)
  }

  if (options.isFill) {
    params.push('c_fill')
  }

  const paramStr = params.length ? `/${params.join(',')}` : ''
  const sectionStr = options.sectionStr || 'image/upload'

  return url.replace(sectionStr, `${sectionStr}/fl_lossy.progressive,q_auto${paramStr}`)
}

export function formatSrc(node:any, src:string, cropWidth?:number){
  if(!src) { return src }

  // 不裁剪
  if (node.skipCrop) return src

  let CropImageWith;
  let CropImageHeight;

  // 定宽
  if(node.viewSize && !isNaN(node.viewSize.layoutWidth) && node.viewSize.layoutWidth >= 1) {
    CropImageWith = node.viewSize.layoutWidth * 2
  } else if(node.viewSize && node.viewStyle && node.viewStyle.imgFac && cropWidth) {
    // 比例宽度 eg：100%
    // cropWidth 组件props配置
    // imgFac schema节点上配置和基准值的比例
    CropImageWith = Math.floor(node.viewStyle.imgFac * cropWidth * 2)
  }

  if (CropImageWith) {
    if (!isNaN(node.viewSize.layoutHeight) && node.viewSize.layoutHeight >= 1) {
      CropImageHeight = node.viewSize.layoutHeight * 2
    } else if (node.viewSize.aspectRatio) {
      CropImageHeight = Math.floor(CropImageWith * (1 / node.viewSize.aspectRatio))
    }
  }

  if(CropImageWith && CropImageHeight && CropImageWith > 50 ){
    src = formatImgUrl(src,{ width:CropImageWith, height: CropImageHeight , isFill: true})
  }

  return src;
}

// 获取价格
export const getPrice = function (priceNumber: string, priceText: string, idBold: Boolean = false) {
  if (!(priceNumber && priceText)) { return '' }
  let price = ''
  const priceMatch = priceText.match(/\{[^}]+\}/)

  if (priceMatch && priceMatch[0]) {
    if (idBold) {
      price = `<strong>${priceNumber}</strong>`
    } else {
      price = `${priceNumber}`
    }
    price = priceText.replace(priceMatch[0], price)
  }

  return price
}
// 判断是否是一个容器类型
export const isContainer = (t:string) => /^(Flex|Stack)$/.test(t)

// 判断是否是一个组件类型
export const isComponent = (t:string) => ComponentReg.test(t)

// 生成style的map方法
const generateStyleMap = {
  flexStyle:function (flexStyle:any){
    const style: CSSStyleDeclaration = Object.create(null)
    //direction和（horizontalDirection|verticalDirection）两个属性组合生成如下属性
    //flex-direction: row | row-reverse | column | column-reverse;

    //mainAxisAlignment属性
    //justify-content: flex-start | flex-end | center | space-between | space-around;

    //crossAxisAlignment属性
    //align-items: flex-start | flex-end | center | baseline | stretch;
    if(flexStyle.direction && ['horizontal',"vertical"].includes(flexStyle.direction)){
      const directionMap:any = {
        "horizontal": "row",
        "vertical": "column"
      }
      style.flexDirection = directionMap[flexStyle.direction]
    }

    if(flexStyle.direction === 'horizontal'
    && flexStyle.horizontalDirection && ['ltr',"rtl"].includes(flexStyle.horizontalDirection)){
      const horizontalDirectionMap:any = {
        "ltr": "row",
        "rtl": "row-reverse"
      }
      style.flexDirection = horizontalDirectionMap[flexStyle.flexDirection]
    }

    if(flexStyle.direction === 'vertical'
      && flexStyle.verticalDirection && ['up',"down"].includes(flexStyle.verticalDirection)){
      const verticalDirectionMap:any = {
        "down": "column",
        "up": "column-reverse"
      }
      style.flexDirection = verticalDirectionMap[flexStyle.verticalDirection]
    }

    // wrap

    if(flexStyle.wrap && ['wrap','nowrap'].includes(flexStyle.wrap)){
      const flexWrapMap:any = {
        "wrap": "wrap",
        "nowrap": "nowrap"
      }
      style.flexWrap = flexWrapMap[flexStyle.wrap]
    }

    //主轴对齐
    if(flexStyle.mainAxisAlignment
        && ['start','end','center' , 'spaceBetween' , 'spaceAround' , 'spaceEvenly'].includes(flexStyle.mainAxisAlignment))
    {
      const mainAxisAlignmentMap:any = {
        "start": "flex-start",
        "end": "flex-end",
        "center" :"center",
        "spaceBetween" :"space-between",
        "spaceAround": "space-around"
      }
      style.justifyContent = mainAxisAlignmentMap[flexStyle.mainAxisAlignment]
    }

    //交叉轴对齐
    if(flexStyle.crossAxisAlignment
      &&['start','end','center','stretch'].includes(flexStyle.crossAxisAlignment) )
    {
      const crossAxisAlignmentMap:any = {
        "start": "flex-start",
        "end": "flex-end",
        "center" :"center",
        "stretch": "stretch"
      }
      style.alignItems = crossAxisAlignmentMap[flexStyle.crossAxisAlignment]
    }

    return style
  },

  stackStyle:function(stackStyle:any){
    const style: CSSStyleDeclaration = Object.create(null)
    if(stackStyle.overflow && ['clip','visible'].includes(stackStyle.overflow)){
      const overflowMap:any = {
        "clip": "hidden",
        "visible": "visible"
      }
      style.overflow = overflowMap[stackStyle.overflow]
    }
    style.position = 'relative'

    return style
  },

  positionedStyle:function(positionedStyle:any){
    const style: CSSStyleDeclaration = Object.create(null)
    style.position = 'absolute'
    // 定位属性的处理
    if(positionedStyle.left || positionedStyle.left === 0){
      style.left = `${positionedStyle.left}px`
    }
    if(positionedStyle.top || positionedStyle.top === 0){
      style.top = `${positionedStyle.top}px`
    }
    if(positionedStyle.right || positionedStyle.right === 0){
      style.right = `${positionedStyle.right}px`
    }
    if(positionedStyle.bottom || positionedStyle.bottom === 0){
      style.bottom = `${positionedStyle.bottom}px`
    }
    if(positionedStyle.horizontalCenter){
      style.top = "50%"
      style.transform = "translateX(-50%)"
    }
    if(positionedStyle.verticalCenter){
      style.left = "50%"
      style.transform = "translateY(-50%)"
    }
    return style
  },

  viewStyle:function(viewStyle:any, data:any){
    const style: any = Object.create(null)
    // 当存在背景图时设置默认的背景图片设置
    if(viewStyle.bgImgUrl){
      style.backgroundSize = 'cover'
      style.backgroundPosition = 'center'
      style.backgroundRepeat = 'no-repeat'
      style.backgroundColor = '#E9E9E9'
    }
    // 颜色的处理
    if(viewStyle.bgColor){
      let background = viewStyle.bgColor
      if(isObjectPath(viewStyle.bgColor)){
        background = generateData(data,viewStyle.bgColor)
        background = getColor(background)
      }
      if(background){
        if(background.startsWith('#')){
          style.backgroundColor = getColor(background)
        }else {
          const [type, left, right] = background.split(',')
          // - 1：左到右
          // - 2：上到下
          // - 3：左上到右下
          // - 4：左下到右上
          const gradientTypeMap:any = {
            1:'to right',
            2:'to bottom',
            3:'bottom right',
            4:'top right'
          }
          const gradientType = gradientTypeMap[type]
          if(gradientType && left && right){
            style.backgroundImage = `linear-gradient(${gradientType},${left} 0%,${right} 100%)`
          }
        }
      }
    }
    if(viewStyle.bgImgScaleType){
        switch (viewStyle.bgImgScaleType) {
          case "cover":
            style.backgroundSize = 'cover'
            break
          case "contain":
            style.backgroundSize = "contain"
            break
          case "fill":
            style.backgroundSize = "auto"
            break
          default:
            style.backgroundSize ="cover"
        }
    }
    if(viewStyle.bgBlur){
        style.backdropFilter = `blur(${viewStyle.bgBlur}px)`
    }
    // border-radius处理
    style.borderTopLeftRadius = `${viewStyle.conerLeftTop || viewStyle.coner || 0}px`
    style.borderTopRightRadius = `${viewStyle.conerRightTop || viewStyle.coner || 0}px`
    style.borderBottomLeftRadius = `${viewStyle.conerLeftBottom || viewStyle.coner || 0}px`
    style.borderBottomRightRadius = `${viewStyle.conerRightBottom || viewStyle.coner || 0}px`

    // border 边框处理
    if(viewStyle.borderStyle){
      style.borderStyle = viewStyle.borderStyle
      style.borderWidth = `${viewStyle.borderWidth || 1}px`
      style.borderColor = getColor(viewStyle.borderColor || '#ff5722')
    }

    return style
  },

  viewSize:function(viewSize:any){
    // layoutHeight: -1:height:100%,-2:不设置，0-1设置为百分比 具体数值px
    // layoutWidth: -1:wight: 100%, -2 display:inline-block这个会跟容器的flex布局冲突 0-1设置为百分比 具体数值
    const style: CSSStyleDeclaration = Object.create(null)
    const height = viewSize.layoutHeight
    const width = viewSize.layoutWidth
    if(height){
      if(height === -1){
        style.height = '100%'
      }else if(height>0 && height<1){
        style.height = `${height * 100}%`
      }else if(!isNaN(height) && height >= 1){
        style.height = `${height}px`
      }
    }

    if(width){
      if(width === -1){
        style.width = '100%'
      }else if(width === -2){
        style.width = 'fit-content'
      }else if(width>0 && width<1){
        style.width = `${width * 100}%`
      }else if(!isNaN(width) && width >= 1){
        style.width = `${width}px`
        style.flexShrink = '0'
      }
    }


    // padding处理
    style.paddingLeft = `${viewSize.paddingLeft || viewSize.padding || 0}px`
    style.paddingTop = `${viewSize.paddingTop || viewSize.padding || 0}px`
    style.paddingRight = `${viewSize.paddingRight || viewSize.padding || 0}px`
    style.paddingBottom =  `${viewSize.paddingBottom || viewSize.padding || 0}px`

    if(viewSize.aspectRatio){
      style.paddingBottom = `${(1 / viewSize.aspectRatio)*100}%`
    }

    // margin处理
    style.marginLeft = `${viewSize.marginLeft || viewSize.margin || 0}px`
    style.marginTop = `${viewSize.marginTop || viewSize.margin || 0}px`
    style.marginRight = `${viewSize.marginRight || viewSize.margin || 0}px`
    style.marginBottom = `${viewSize.marginBottom || viewSize.margin || 0}px`

    return style
  }
}

const operationReg = /^(and|or)$/i;
const isOperation = (t:string) => operationReg.test(t);

export const isVisibility = (ex:any,data:any):boolean => {
  // 如果表达式不存在则不需要判断直接返回true
  const singleOperation = ['notEmpty','empty','isTrue']
  if(!ex){ return true }

  if(isOperation(ex.operation)) {
    switch (ex.operation) {
      case 'and':
        return isVisibility(ex.left,data) && isVisibility(ex.right,data)
      case 'or':
        return  isVisibility(ex.left,data ) || isVisibility(ex.right,data)
    }
  } else {
    // - empty 空
    // - notEmpty 非空
    // - isTrue 如果是boolean类型的true 或者 string类型的“true”返回true
    // - isFalse 如果是boolean类型的false 或者 string类型的"false"返回true
    const rules:any = {
      notEmpty(value:any):boolean {
        return !!value
      },
      empty(value:any):boolean {
        return !value
      },
      isTrue(value:boolean|string):boolean {
        return (value === true || value === 'true')
      },
      isFalse(value:boolean|string):boolean {
        return (value === false || value === 'false')
      }
    }
    const value = generateData(data,ex.data)
    return rules[ex.operation](value)
  }

  return false
}

// 生成style
export const generateStyle = function (node:any, data:any):CSSStyleDeclaration|null {
  const nodeStyle: CSSStyleDeclaration = Object.create(null)
  if(node.type === 'Stack'){
    nodeStyle.position = 'relative'
    nodeStyle.overflow = 'hidden'
  }
  if(node.type === 'Flex'){
    nodeStyle.display = 'flex'
    nodeStyle.overflow = 'hidden'
  }
  if(node.flexGrow){
    nodeStyle.flexGrow = ''+node.flexGrow
  }
  if(node.viewAction){
    nodeStyle.cursor = 'pointer'
  }
  const flexStyle = node.flexStyle ?  generateStyleMap.flexStyle(node.flexStyle) : null
  const stackStyle = node.stackStyle ?  generateStyleMap.stackStyle(node.stackStyle) : null
  const positionedStyle = node.positionedStyle ?  generateStyleMap.positionedStyle(node.positionedStyle) : null
  const viewStyle = node.viewStyle ?  generateStyleMap.viewStyle(node.viewStyle,data) : null
  const viewSize = node.viewSize ?  generateStyleMap.viewSize(node.viewSize) : null
  //计算出最终的合并样式
  Object.assign(nodeStyle,flexStyle,stackStyle,positionedStyle,viewStyle,viewSize)
  // 进行prefix之前先判断对象里有没有值
  if(Object.keys(nodeStyle).length > 0){
    return prefix(nodeStyle)
  }else {
    return null
  }
}


// 生成class
const generateStaticClass = function (node:any):string|null {
  if(node.viewStyle && node.viewStyle.boxShadow !==void 0){
    return BoxShadow[node.viewStyle.boxShadow]
  }
  if(node.viewStyle && node.viewStyle.bgImgUrl){
    return 'klk-standard-card-lazy'
  }
   return ''
}


// 组件名称map映射
export const generateName = function (type:string):string{
  switch (type) {
    case 'TextView':
      return 'CardTextView';
    case 'TextViewCustomTag':
      return 'CardTextViewCustomTag';
    case 'ImageView':
      return 'CardImageView';
    case 'ReviewView':
      return 'CardParticipation';
    case 'GeneralTagView':
      return 'CardGeneralTag';
    case 'PromotionTagView':
      return 'CardPromoTag';
    case 'VisitorView':
      return 'CardVisitor';
    // 大price描述
    case 'GeneralPriceView':
      return 'CardPriceDesc';
    // 换行price描述
    case 'SmallPriceView':
      return  'CardPriceDescSmall';
    // 带点price描述
    case 'DescPriceView':
      return 'CardPriceDot';
    // 简易价格描述
    case 'SellingPriceView':
      return  'CardSellPrice';
    case 'Seo':
      return 'CardSeoTitle';
    case 'Favorite':
      return 'CardFavorite';
    case 'CouponTagView':
      return 'CouponTagView';
    case 'DiscountView':
      return 'CardDiscountView'
    case 'CardDiscountTag':
      return 'CardDiscountTag'
    default:
      return ''
  }
}

const objectPathReg:RegExp = /<\?(.+?)\?>/g
const realDataReg:RegExp = /<\!(.+?)\!>/g
export const isObjectPath = (path:string):boolean => objectPathReg.test(path);
export const isRealData = (path: string): boolean => realDataReg.test(path)
export const pathUtil = function (reg:RegExp, path:string):string {
  const objectPathStr = path.replace(reg,"$1")
  if(objectPathStr) { return objectPathStr }
  else { return '' }
}



// 根据object-path获取内容
export function generateData(object:any,path:string):any{
  let result;
  if(isRealData(path)){
    result = pathUtil(realDataReg,path)
  }else {
    const objectPathStr = pathUtil(objectPathReg,path)
    if(objectPathStr){
      result = objectPath.get(object,objectPathStr)
    }
  }
  return result
}

export function isNotEmpty(data:any){
  return typeof data !== "undefined" && data !== null
}

// 生成VnodeData
export function generateVNodeData(this:Vue, data:any,node:any,inHouseModule:InHouseModule, inHouseDefaultExt: any,cropWidth:number|undefined, trackInfo:any):VNodeData{
  const vNodeData: VNodeData = Object.create(null)
  vNodeData.props = {}
  vNodeData.attrs = {
    type: node.type
  }
  // module层埋点
  if(inHouseModule && inHouseModule.moduleName && node.spm && node.spm.type === 'module'){
    let oid = isNotEmpty(inHouseModule.objectId) ? 'oid=' + inHouseModule.objectId:''

    let idx = isNotEmpty(inHouseModule.index) ? '&idx=' + inHouseModule.index : ''

    let len = isNotEmpty(inHouseModule.length) ? '&len=' + inHouseModule.length : ''

    let moduleName = inHouseModule.moduleName || ''

    let extendParams  = inHouseDefaultExt

    // 证明当前是一个多module埋点结构
    if(node.spm.tag){
      moduleName = `${ inHouseModule.moduleName }_${ node.spm.tag }`
      // 多module状态下不需要List类型的inhouse埋点，需要将索引和长度添加到扩展参数中
      idx = ''
      len = ''
      Object.assign(extendParams,{
        idx:inHouseModule.index,
        len:inHouseModule.length
      })
      if(trackInfo && trackInfo[node.spm.tag]){
        const CurrentNodeTrackInfo = trackInfo[node.spm.tag]
        // 后端可能会下发对应的object_id和扩展参数
        if(CurrentNodeTrackInfo['object_id']){
          oid = `oid=${ CurrentNodeTrackInfo['object_id'] }`
        }
        if(CurrentNodeTrackInfo['extra']){
          Object.assign(extendParams,CurrentNodeTrackInfo['extra'])
        }else if(inHouseModule.moduleExt){
          Object.assign(extendParams,inHouseModule.moduleExt)
        }
      }
    }else {
      // 合并埋点的扩展参数
      inHouseModule.moduleExt && Object.assign(extendParams,inHouseModule.moduleExt)
    }

    const ext = extendParams ? '&ext='+encodeURIComponent(JSON.stringify(extendParams)) : ''



    vNodeData.attrs['data-spm-module'] = `${moduleName}?${oid}${idx}${len}${ext}`

    vNodeData.attrs['data-spm-virtual-item'] = "__virtual?typ=entry"

  }
  // item层埋点
  if(node.spm && node.spm.type === 'item' && node.spm.tag){
    const itemName = node.spm.tag || ''
    let oid = ''
    let ext = ''

    // 如果trackInfo内有对应的扩展参数
   if (trackInfo && trackInfo.items && trackInfo.items[node.spm.tag]) {

     const CurrentNodeTrackInfo = trackInfo.items[node.spm.tag]

     oid = isNotEmpty(CurrentNodeTrackInfo['object_id']) ? '&oid=' + CurrentNodeTrackInfo['object_id']:''
     ext = CurrentNodeTrackInfo.extra ? '&ext='+encodeURIComponent(JSON.stringify(CurrentNodeTrackInfo.extra)) : ''
   }


    // 这里所有的都组织点击事件的穿透上报，因为考虑到可能存在module套item的情况module也需要点击
    vNodeData.attrs['data-spm-item'] = `${itemName}?mod=stop${oid}${ext}`
  }
  // 如果有data props到下一层
  if(node.data){
    let realData = generateData(data,node.data)

    if(node.type === 'ImageView'){
      realData = formatSrc(node, realData, cropWidth)
    }

    vNodeData!.props!.data = realData
  }
  // 如果有textViewStyle props到下一层
  if(node.textViewStyle){
    const textViewStyle: any = node.textViewStyle
    if(node.textViewStyle.textColor && (isObjectPath(node.textViewStyle.textColor) || isRealData(node.textViewStyle.textColor))) {
      textViewStyle.textColor = generateData(data,node.textViewStyle.textColor)
    }
    vNodeData!.props!.textViewStyle = textViewStyle
  }

  // 如果有tag props到下一层
  if(node.elementTag){
    vNodeData!.props!.tag = node.elementTag
  }

  // 如果有hrefLink props到下一层
  if(node.hrefLink){
    const hrefLink = generateData(data,node.hrefLink)
    vNodeData!.props!.hrefLink = hrefLink
  }

  // 如果有sellingPriceViewStyle props 到下一层
  if(node.sellingPriceViewStyle){
    vNodeData!.props!.sellingPriceViewStyle = node.sellingPriceViewStyle
  }

  // 如果有 favoriteParams props 到下一层
  if(node.favoriteParams){
    const favoriteParams:any = {}
    Object.keys(node.favoriteParams).forEach((param)=>{
      if(node.favoriteParams[param]){
        favoriteParams[param] = generateData(data,node.favoriteParams[param])
      }
    })
    vNodeData!.props!.favoriteParams = favoriteParams
  }

  // 如果有图片样式属性imageViewStyle props到下一层
  if(node.imageViewStyle){
    vNodeData!.props!.imageViewStyle = node.imageViewStyle
  }

  // 如果有discountViewStyle props 到下一层
  if(node.discountViewStyle){
    vNodeData!.props!.discountViewStyle = node.discountViewStyle
  }

  // 这里是schema需要透传给元素组件的prop

  node.generalPriceViewStyle && ( vNodeData!.props!.generalPriceViewStyle = node.generalPriceViewStyle )

  node.generalTagViewStyle &&  ( vNodeData!.props!.generalTagViewStyle = node.generalTagViewStyle )

  node.promotionTagViewStyle && ( vNodeData!.props!.promotionTagViewStyle = node.promotionTagViewStyle )

  node.couponTagViewStyle && ( vNodeData!.props!.couponTagViewStyle = node.couponTagViewStyle )

  // 如果有style生成style
  const style = generateStyle(node,data)
  if(style){ vNodeData.style = style }

  // 如果有staticClass生成staticClass
  const staticClass = generateStaticClass(node)
  if(staticClass){  vNodeData.staticClass = staticClass }

  // 如果有bgImgUrl需要lazyLoad去加载
  if(node.viewStyle && node.viewStyle.bgImgUrl){
    let src = generateData(data,node.viewStyle.bgImgUrl)
    if(src){
      src = formatSrc(node, src, cropWidth)
      vNodeData.directives = [
        {
          name: 'lazy',
          value: src,
          arg: 'background-image',
          modifiers:{
            "container":true
          }
        }
      ]
    }
  }

  if(node.viewAction){
    const handleOn = isComponent(node.type) ? 'nativeOn': 'on'
    vNodeData[handleOn] = {
      click:(event:Event)=>{
        event.stopPropagation()

        if(node.viewAction.action === 'DEEP_LINK'){
          const target = node.viewAction.target ? (generateData(data,node.viewAction.target) || 'blank') : 'blank'
          const deepLink = generateData(data,node.viewAction.deepLink);
          if(deepLink){
            if(target === 'self'){
              window.location.href = deepLink
            }else {
              window.open(deepLink)
            }
          }
        }else if(node.viewAction.action === 'EVENT'){
          const event = generateData(data,node.viewAction.event);
          this.$emit('card-event',event)
        }
      }
    }
  }
  return vNodeData
}
