// #8F00FF
export const DESIGNTOKEN:any = {
  "default": {
    "color_config": {
      "$black": "#000000",
      "$white": "#FFFFFF",
      "$neutral-50": "#FAFAFA",
      "$neutral-100": "#F5F5F5",
      "$neutral-200": "#EEEEEE",
      "$neutral-300": "#E0E0E0",
      "$neutral-400": "#CCCCCC",
      "$neutral-500": "#B2B2B2",
      "$neutral-600": "#999999",
      "$neutral-700": "#757575",
      "$neutral-800": "#4A4A4A",
      "$neutral-900": "#212121",
      "$orange-50": "#FFEFEB",
      "$orange-100": "#FFD6C9",
      "$orange-200": "#FFAC91",
      "$orange-300": "#FF855E",
      "$orange-400": "#FF6C3D",
      "$orange-500": "#FF5722",
      "$orange-600": "#E54C1C",
      "$orange-700": "#CC4216",
      "$orange-800": "#D84315",
      "$orange-900": "#B23812",
      "$yellow-50": "#FFF3E8",
      "$yellow-100": "#FFE6CF",
      "$yellow-200": "#FFCC9C",
      "$yellow-300": "#FFB46E",
      "$yellow-400": "#FFA44F",
      "$yellow-500": "#FF9735",
      "$yellow-600": "#F08520",
      "$yellow-700": "#DB6D05",
      "$yellow-800": "#C66001",
      "$yellow-900": "#B15600",
      "$red-50": "#FFEBEB",
      "$red-100": "#FFD1D2",
      "$red-200": "#FFA3A5",
      "$red-300": "#FA7D7F",
      "$red-400": "#F75C5E",
      "$red-500": "#F44447",
      "$red-600": "#DE373A",
      "$red-700": "#C73234",
      "$red-800": "#B02124",
      "$red-900": "#99181B",
      "$green-50": "#E6F7EE",
      "$green-100": "#C5F0D9",
      "$green-200": "#87E0B2",
      "$green-300": "#58D193",
      "$green-400": "#36C97E",
      "$green-500": "#20C36F",
      "$green-600": "#1AB063",
      "$green-700": "#169E58",
      "$green-800": "#128C4D",
      "$green-900": "#0F7A43",
      "$blue-50": "#EBF0FF",
      "$blue-100": "#CCDAFF",
      "$blue-200": "#9EB8FF",
      "$blue-300": "#7397FA",
      "$blue-400": "#5781F7",
      "$blue-500": "#3F70F5",
      "$blue-600": "#3562DE",
      "$blue-700": "#2C55C7",
      "$blue-800": "#2349B0",
      "$blue-900": "#1C3D99",
      "$skyBlue-50": "#E3F7FC",
      "$skyBlue-100": "#B9EBFA",
      "$skyBlue-200": "#78D8F5",
      "$skyBlue-300": "#4DCAF0",
      "$skyBlue-400": "#26BFED",
      "$skyBlue-500": "#00B3EA",
      "$skyBlue-600": "#009FD7",
      "$skyBlue-700": "#0090C2",
      "$skyBlue-800": "#007FAC",
      "$skyBlue-900": "#006D94",
      "$purple-50": "#F1EDFF",
      "$purple-100": "#E2D9FF",
      "$purple-200": "#BFABFF",
      "$purple-300": "#A285FF",
      "$purple-400": "#8965FC",
      "$purple-500": "#7950FA",
      "$purple-600": "#6741E0",
      "$purple-700": "#5734C7",
      "$purple-800": "#4828AD",
      "$purple-900": "#3A1E94",
      "$roseRed-50": "#FFEBF5",
      "$roseRed-100": "#FFD1E9",
      "$roseRed-200": "#FFA3D2",
      "$roseRed-300": "#FA7DBD",
      "$roseRed-400": "#F760AE",
      "$roseRed-500": "#F5459F",
      "$roseRed-600": "#DB378B",
      "$roseRed-700": "#C22B78",
      "$roseRed-800": "#A82066",
      "$roseRed-900": "#8F1754"
  }
  }
}
const reverse: any = {
    "#757575": "$neutral-700",
    "#212121": "$neutral-900",
    "#999999": "$neutral-600",
    "#f5f5f5": "$neutral-100",
    "#ff5722": "$orange-500",
    "#ffffff": "$white",
    "#f58b1b": "$yellow-500",
    "#ff8c00": "$yellow-500",
    "#ff5e00": "$orange-500",
    "#16aa77": "$green-500",
    "#fff4ed": "$orange-50",
    "#ff9d26": "$yellow-500",
    "#fafafa": "$neutral-50",
    "#99181b": "$red-900",
    "#ff9735": "$yellow-500",
    "#ff6b3d":"$orange-500",
    "#33333":"$neutral-900",
    "#e9e9e9":"$neutral-200",
    "#e9f8f1":"$green-50",
    "#ffffffe3":"$white"
}

const alphaReg: RegExp = /^\$alpha-(.+)_(.+)$/


export const getColorToken = function (name: string): string  {
  return DESIGNTOKEN.default.color_config[name]
};


export const getReverseToken = (name: string): string  => reverse[name];

export function getColor(color:string){
  if (!color) { return color }
  let realColor = color.toLowerCase()
  if(realColor.startsWith('#')){  // 如果设置的是色值那么先转换成对应的designToken
    const ReverseToken = getReverseToken(realColor)
    if(ReverseToken){
      realColor = ReverseToken
    }else {
      return realColor
    }
  }
  if(realColor.startsWith('$')){ // 如果是一个token形式的值
    let ColorDesignToken = realColor
    let AlphaString = ''
    if(realColor.match(alphaReg)){
      const AlphaMatch = realColor.match(alphaReg)
      AlphaString = AlphaMatch && AlphaMatch[1]? AlphaMatch[1] : ''
      ColorDesignToken = AlphaMatch && AlphaMatch[2] ? '$'+AlphaMatch[2] : ''
    }
    if(ColorDesignToken && getColorToken(ColorDesignToken)){
      realColor = getColorToken(ColorDesignToken)
    }
    return  realColor+AlphaString
  }
  return realColor
}
