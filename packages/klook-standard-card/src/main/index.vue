

<script lang="ts">
  import { Vue, Component, Prop, Provide } from 'vue-property-decorator'
  import { CreateElement } from "vue/types/vue";
  import { VNode } from "vue/types/vnode";
  import {
    isContainer,
    isComponent,
    generateName,
    generateVNodeData,
    isVisibility,
    InHouseModule,
    CardSchemaModel
  } from './util'

  import CardImageView from './component/card-image-view.vue'
  import CardPromoTag from "./component/card-promo-tag.vue";
  import CardParticipation from "./component/card-participation.vue";
  import CardVisitor from "./component/card-visitor.vue";
  import CardPriceDot from "./component/card-price-dot.vue";
  import CardGeneralTag from "./component/card-general-tag.vue";
  import CardPriceDesc from "./component/card-price-desc.vue";
  import CardPriceDescSmall from './component/card-price-desc-small.vue'
  import CardSeoTitle from './component/card-seo-title.vue'
  import CardTextViewCustomTag from './component/card-text-view-custom-tag.vue'
  import CardTextView from './component/card-text-view.vue'
  import CardSellPrice from './component/card-sell-price.vue'
  import CardFavorite from "./component/card-favorite.vue";
  import CouponTagView from './component/card-coupon-tag.vue'
  import CardDiscountView from './component/card-discount-view.vue'
  import CardDiscountTag from "./component/card-discount-tag.vue";

  @Component({
    components: {
      CardImageView,
      CardPromoTag,
      CardParticipation,
      CardVisitor,
      CardPriceDot,
      CardGeneralTag,
      CardPriceDesc,
      CardPriceDescSmall,
      CardSeoTitle,
      CardTextViewCustomTag,
      CardTextView,
      CardSellPrice,
      CardFavorite,
      CouponTagView,
      CardDiscountView,
      CardDiscountTag
    }
  })
  export default class KlkStandardCard extends Vue{
    @Prop({ type:Object,default:()=>{} }) data!:any
    @Prop({ type:Number }) cropWidth!:number
    @Prop({ type:Object,default:()=>{} }) schema!:CardSchemaModel
    @Prop({ type:Object,default:()=>{} }) inHouseModule!:InHouseModule
    @Prop({ type:Object }) trackInfo!:any
    @Prop({type: Function, default: null}) handleFavorite!: Function

    @Provide() handleFavoriteFunction = this.handleFavorite

    get inHouseDefaultExt(){
      return {
        CardID: this.schema.card_id || 0,
        CardName:this.schema.card_name || '',
        StyleID:this.schema.style_id || 0,
        StyleName:this.schema.style_name || ''
      }
    }

    renderFunc(h:CreateElement,node:any):any{
      if(isContainer(node.type) && isVisibility(node.visibility,this.data)){
        if(node.children && node.children.length >0 ){
          return h('div', generateVNodeData.call(this, this.data,node,this.inHouseModule,this.inHouseDefaultExt, this.cropWidth, this.trackInfo),
            node.children.map((nodeChildren:any)=>{
              return this.renderFunc(h,nodeChildren)
            }))
        }else {
          return h('div',generateVNodeData.call(this,this.data,node,this.inHouseModule, this.inHouseDefaultExt, this.cropWidth, this.trackInfo))
        }
      }else if(isComponent(node.type) && isVisibility(node.visibility,this.data)){
        return h(generateName(node.type),generateVNodeData.call(this,this.data,node, this.inHouseModule, this.inHouseDefaultExt, this.cropWidth,this.trackInfo))
      }
    }

    render(h:CreateElement): VNode{
      if(this.schema && this.schema.style_config){
        return this.renderFunc(h,this.schema.style_config)
      }else {
        console.warn("schema is undefined, plz check it")
        return h('div')
      }
    }
  }
</script>
<style lang="scss">
@import "share.scss";
  .klk-standard-card--white, .klk-standard-card--gray {
    transition: box-shadow .2s ease, transform .2s ease;
    border: 1px solid $color-border-normal;
    transform-style:preserve-3d;
  }

  @media (min-width: 600px) {
    .klk-standard-card--gray:hover,.klk-standard-card--white:hover {
      transform: translateY(-4px);
      box-shadow: $shadow-normal-5;
      z-index: 2;
    }
  }

  .klk-standard-card-lazy {
    transition: background-color .2s ease;
  }

  .klk-standard-card-lazy[lazy="loaded"] {
      background-color: transparent!important;
  }

</style>
