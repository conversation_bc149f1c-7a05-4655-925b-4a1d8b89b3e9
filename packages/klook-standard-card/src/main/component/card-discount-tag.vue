<template>
  <span
    v-if="data"
    :style="getStyle(data)"
    class="klk-card-discount-tag"
  >
    {{ data.text }}
  </span>
</template>

<script lang="ts">
import {Vue, Component, Prop} from 'vue-property-decorator'
import { getColor } from '../design-token/token-utils'

@Component
export default class CardDiscountTag extends Vue {
  @Prop({ type: Object, default: () => {} }) data!: any

  getStyle(tag: any) {
    const left = getColor(tag.background_color_left)
    const right = getColor(tag.background_color_right)
    const textColor = getColor(tag.text_color)
    const style = `
          background: ${left};
          background: -moz-linear-gradient(left,  ${left} 0%, ${right} 100%);
          background: -webkit-linear-gradient(left,  ${left} 0%,${right} 100%);
          background: linear-gradient(to right,  ${left} 0%,${right} 100%);
          filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='${left}', endColorstr='${right}',GradientType=1 );
          color: ${textColor}
        `
    return style
  }
}
</script>

<style lang="scss">
@import '../share';
.klk-card-discount-tag{
    color: $white;
    font-size: 12px;
    font-weight: 500;
    padding: 2px 4px!important;
    background: $orange-300;
    border-radius: 4px;
    width: fit-content;
}
</style>
