<template>
  <div v-if="data" class="klk-card-text-custom" :class="textContentClass">
    <component
      :is="tag"
      class="klk-card-text-custom--content"
      :style="textStyle"
    >
      <a :href="hrefLink" @click.prevent>{{ data }}</a>
    </component
    >
  </div>
</template>

<script lang="ts">
import { Vue, Component, Prop } from "vue-property-decorator";
import { getColor } from "../design-token/token-utils";
const TextDecorationMap: any = {
  underLine: "underline",
  lineThrough: "line-through",
};
const LineHeightMap: any = {
  60: 66,
  44: 52,
  32: 42,
  28: 36,
  24: 32,
  20: 26,
  18: 24,
  16: 22,
  14: 20,
  12: 16,
};

@Component
export default class CardTextCustomTag extends Vue {
  @Prop({ type: String, default: () => "span" }) tag!: string;
  @Prop({ type: String, default: "" }) data!: string;
  @Prop({ type: String, default: "" }) hrefLink!: string;
  @Prop({ type: Object, default: () => {} }) textViewStyle!: any;

  get textStyle() {
    const style: CSSStyleDeclaration = Object.create(null);
    let LineHeight = 14;
    style.display = "inline-block";
    if (!this.textViewStyle) {
      return null;
    }
    if (this.textViewStyle.textSize) {
      style.fontSize = `${this.textViewStyle.textSize}px`;
      LineHeight =
        LineHeightMap[this.textViewStyle.textSize] ||
        this.textViewStyle.textSize * 1.44;
      style.lineHeight = `${LineHeight}px`;
    }
    if (this.textViewStyle.textColor) {
      style.color = getColor(this.textViewStyle.textColor);
    }
    if (this.textViewStyle.bold) {
      style.fontWeight = "500";
    }
    if (this.textViewStyle.bgColor) {
      style.backgroundColor = getColor(this.textViewStyle.bgColor);
    }
    if (this.textViewStyle.maxLine) {
      style.maxHeight = `${LineHeight * this.textViewStyle.maxLine}px`;
      style.overflow = "hidden";
      style.textOverflow = "ellipsis";
      style.display = "-webkit-box";
      style.webkitBoxOrient = "vertical";
      style.webkitBoxAlign = "start";
      style.webkitLineClamp = this.textViewStyle.maxLine.toString();
      if (this.textViewStyle.maxLine === 1) {
        style.wordBreak = "break-all";
      } else {
        style.wordBreak = "break-word";
      }
    }
    if (
      this.textViewStyle.lineStyle &&
      ["underLine", "lineThrough"].includes(this.textViewStyle.lineStyle)
    ) {
      style.textDecoration = TextDecorationMap[this.textViewStyle.lineStyle];
    }
    if (this.textViewStyle.textAlign) {
      style.textAlign = this.textViewStyle.textAlign;
    }

    return style;
  }

  get textContentClass() {
    if (this.textViewStyle.textAlign) {
      switch (this.textViewStyle.textAlign) {
        case "center":
          return "klk-card-text_content--center";
        case "left":
          return "klk-card-text_content--left";
        case "right":
          return "klk-card-text_content--right";
        default:
          return "klk-card-text_content--left";
      }
    }
    return "klk-card-text_content--left";
  }
}
</script>

<style lang="scss">
@import "../share";
.klk-card-text-custom {
  display: flex;
  align-items: center;

  &--center {
    justify-content: center;
  }

  &--left {
    justify-content: flex-start;
  }

  &--right {
    justify-content: flex-end;
  }

  &--content {
    max-width: 100%;
    line-height: 1.2;
    font-size: 12px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: pre-wrap;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }
}
</style>
