<template>
  <div class="klk-promo-tag-wrapper"
      :style="WrapperStyle"
  >
    <template
      v-if="data && data.length > 0"
    >
      <div
        v-for="(promoTagData,index) in data"
        :key="index"
        class="klk-promo-tag"
        :class="{
            'klk-promo-tag--have-icon': promoTagData.icon_src,
            'klk-promo-tag--small': size === 'small',
            'klk-promo-tag--margin': ( line > 1 || line === -1 ) && data.length > 1
         }"
        :style="getStyle(promoTagData)"
      >
        <img
          v-if="promoTagData.icon_src"
          class="klk-promo-tag_icon"
          :class="{ 'klk-promo-tag_icon--small': size === 'small'}"
          :src="promoTagData.icon_src"
        />
        <span
          v-if="promoTagData.text"
          class="klk-promo-tag_text"
        >{{ promoTagData.text }}</span>
      </div>
    </template>
  </div>
</template>

<script lang="ts">
import { Vue, Component, Prop } from 'vue-property-decorator'
import { TagViewStyle } from "../util";
import { getColor } from '../design-token/token-utils'

const TagHeightMap = {
  'big': 24,
  'small': 20
}

@Component
export default class CardPromoTag extends Vue {
  @Prop({type: Array, default: () => []}) data!: any
  @Prop({ type: Object, default: () => ({ sizeStyle: 'big', lines: 1 }) }) promotionTagViewStyle!: TagViewStyle

  get size(){
    if(this.promotionTagViewStyle && this.promotionTagViewStyle.sizeStyle ){
      return this.promotionTagViewStyle.sizeStyle
    }else {
      return 'big'
    }
  }

  get line(){
    if(this.promotionTagViewStyle && this.promotionTagViewStyle.lines){
        return this.promotionTagViewStyle.lines
    }else {
        return 1
    }
  }

  get maxHeight(){
      const TAG_HEIGHT = TagHeightMap[this.size]
      if(this.line === 1){
        return TAG_HEIGHT
      }else if(this.line > 1){
        return ( TAG_HEIGHT * this.line) + (6 * ( this.line - 1))
      }
      return null
  }

  get WrapperStyle(){
      if(this.maxHeight){
        return {
          maxHeight: `${ this.maxHeight }px`,
          overflow: 'hidden'
        }
      }else {
        return ''
      }
  }

  getStyle(tag: any) {
    const left = getColor(tag.background_color_left)
    const right = getColor(tag.background_color_right)
    const textColor = getColor(tag.text_color)
    const marginBottom = this.line > 1 ? '6px' : 0
    const style = `
          background: ${left};
          background: -moz-linear-gradient(left,  ${left} 0%, ${right} 100%);
          background: -webkit-linear-gradient(left,  ${left} 0%,${right} 100%);
          background: linear-gradient(to right,  ${left} 0%,${right} 100%);
          filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='${left}', endColorstr='${right}',GradientType=1 );
          color: ${textColor};
          margin-bottom: ${ marginBottom }
        `
    return style
  }
}
</script>

<style lang="scss">
@import '../share';
.klk-promo-tag-wrapper {
  display: flex;
  flex-wrap: wrap;
}

.klk-promo-tag {
  display: flex;
  align-items: center;
  max-width: 100%;
  margin-right: 10px;
  padding: 2px 4px;
  color: $white;
  font-size: 12px;
  line-height: 16px;
  border-radius: 4px;
  background: $orange-500;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;

  &--small{
    padding: 0 4px;
  }

  &--margin{
    margin-bottom: 6px;
  }

  &_icon {
    width: 20px;
    height: 20px;

    &--small{
      width: 16px;
      height: 16px;
    }
  }

  &_text {
    padding: 2px 0;
    max-width: 100%;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  &:last-of-type {
    margin-right: 0;
  }
}

</style>
