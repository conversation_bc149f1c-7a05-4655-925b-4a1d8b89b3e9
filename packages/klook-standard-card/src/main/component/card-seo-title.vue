<template>
  <a
    v-if="data"
    class="seo-title"
    :href="data.url || ''"
  >
    {{ data.title || '' }}
  </a>
</template>
<script lang="ts">
import { Vue, Component, Prop } from 'vue-property-decorator'

@Component
export default class CardSeoTitle extends Vue {
  @Prop({
    type: Object,
    default: () => {
      return { title: '', url: '' }
    }
  }) data!: any
}

</script>
<style>
  .seo-title{
    display: block;
    height: 0;
    width: 0;
    overflow: hidden;
  }
</style>
