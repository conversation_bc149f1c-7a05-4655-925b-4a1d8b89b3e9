<template>
  <div
    v-if="data && data.length > 0"
    class="klk-card-general-tag"
    :style="paddingStyle"
  >
    <span
      v-for="(tag,index) in data"
      :key="index"
      class="klk-card-general-tag_item"
      :class="{'klk-card-general-tag_item--margin':( line > 1 || line === -1 ) && data.length > 1}"
      :style="getStyle(tag)"
    >{{ tag.text }}
    </span>
  </div>
</template>

<script lang="ts">
const TagHeightMap = {
  'big': 20,
  'small': 18
}
import { Vue, Component, Prop } from 'vue-property-decorator'
import { TagViewStyle } from "../util";
import { getColor } from '../design-token/token-utils'

@Component
export default class CardGeneralTag extends Vue {
  @Prop({ type: Array, default: () => [] }) data!: any
  @Prop({ type: Number, default: 0 }) placeHolder!: number
  @Prop({ type: Object, default: () => ({ sizeStyle: 'small', lines: 1 }) }) generalTagViewStyle!: TagViewStyle

  get size(){
    if(this.generalTagViewStyle && this.generalTagViewStyle.sizeStyle ){
      return this.generalTagViewStyle.sizeStyle
    }else {
      return 'small'
    }
  }

  get line(){
    if(this.generalTagViewStyle && this.generalTagViewStyle.lines){
      return this.generalTagViewStyle.lines
    }else {
      return 1
    }
  }

  get maxHeight(){
    const TAG_HEIGHT = TagHeightMap[this.size]
    if(this.line === 1){
      return TAG_HEIGHT
    }else if(this.line > 1){
      return ( TAG_HEIGHT * this.line) + (4 * ( this.line - 1))
    }
    return null
  }

  getStyle(tag: any) {
    const style: CSSStyleDeclaration = Object.create(null)
    if (tag.bg_color && tag.bg_color !== '') {
      style.backgroundColor = getColor(tag.bg_color)
    }

    if (tag.text_color && tag.text_color !== '') {
      style.color = getColor(tag.text_color)
    }

    return style
  }

  get paddingStyle() {
    const paddingStyle = Object.create(null)
    if(this.maxHeight){
        paddingStyle.maxHeight = `${ this.maxHeight }px`,
        paddingStyle.overflow = 'hidden'
    }
    paddingStyle.paddingTop = `${this.placeHolder}px`
    return paddingStyle
  }
}
</script>

<style lang="scss">
@import '../share';
  .klk-card-general-tag{
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    box-sizing: content-box;

    &_item{
      font-size: 12px;
      line-height: 16px;
      color: $neutral-700;
      background: $neutral-100;
      padding: 1px 6px;
      margin-right: 6px;
      max-height: 18px;
      max-width: 100%;
      border-radius: 2px;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 1;
      -webkit-box-orient: vertical;
      overflow: hidden;

      &--margin{
        margin-bottom: 4px;
      }
    }

    &_item:last-of-type{
      margin-right: 0;
    }
  }
</style>
