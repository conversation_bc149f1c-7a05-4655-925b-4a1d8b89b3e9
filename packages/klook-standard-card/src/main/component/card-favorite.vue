<template>
  <div
    class="klk-card-favorite"
    @click.stop="favoriteChange"
  >
    <img :src="favoriteImage" class="klk-card-favorite_image" />
  </div>
</template>

<script lang="ts">
import {Vue, Component, Prop, Inject} from 'vue-property-decorator'

enum FavoriteStatus {
  Favorite = 0, // 收藏
  UnFavorite = 1, // 未收藏
  UnLogin = 2, // 未登录
}

@Component
export default class CardFavorite extends Vue {
  @Prop({ type: Number, default: () => [] }) data!: FavoriteStatus
  @Prop({ type: Object, default: () => {} }) favoriteParams!: any
  @Inject({ from: 'handleFavoriteFunction', default: null }) handleFavorite!: Function

  status: FavoriteStatus = this.data

  changeFavorite() {
    if (this.status === FavoriteStatus.Favorite) {
      this.status = FavoriteStatus.UnFavorite
    } else {
      this.status = FavoriteStatus.Favorite
    }
  }

  favoriteChange() {
    if (!this.handleFavorite || this.status === FavoriteStatus.UnLogin) return;

    const res = this.handleFavorite(this.status, this.favoriteParams)

    if(!res)  return;

    if (res.then) {
        res.then(() => {
          this.changeFavorite()
        })
    } else {
      this.changeFavorite()
    }
  }

  get favoriteImage() {
    if (this.status === FavoriteStatus.Favorite) {
      return 'https://res.klook.com/image/upload/v1625474634/tloihxiv6womaet1qkbl.png'
    } else {
      return 'https://res.klook.com/image/upload/v1625474788/tfx7bujrjf0yv8taqpd8.png'
    }
  }
}
</script>

<style lang="scss">
.klk-card-favorite {
  display:inline-block;
  &_image{
    width: 20px;
    height: 18px;
  }
}
</style>
