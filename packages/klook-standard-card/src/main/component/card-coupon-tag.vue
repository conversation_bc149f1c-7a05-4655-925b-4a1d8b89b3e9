<template>
  <div
    v-if="data && data.length > 0"
    class="klk-card-coupon-tag"
    :style="paddingStyle"
  >
  <div v-for="(tag,index) in data"
       :key="index"
       class="klk-card-coupon-tag_item"
       :class="{'klk-card-coupon-tag_item--margin':( line >1 || line===-1 ) && data.length > 1}"
       :style="getStyle(tag)">
    <span class="klk-card-coupon-tag_text">{{ tag.text }}</span>
  </div>
  </div>
</template>

<script lang="ts">
const TagHeightMap = {
  'big': 20,
  'small': 18
}
import { Vue, Component, Prop } from 'vue-property-decorator'
import { TagViewStyle } from "../util";
import { getColor } from '../design-token/token-utils'

@Component
export default class CardGeneralTag extends Vue {
  @Prop({ type: Array, default: () => [] }) data!: any
  @Prop({ type: Number, default: 0 }) placeHolder!: number
  @Prop({ type: Object, default: () => ({ lines: 1 }) }) couponTagViewStyle!: TagViewStyle



  get maxHeight(){
    const TAG_HEIGHT = 20
    if(this.line === 1){
      return TAG_HEIGHT
    }else if(this.line > 1){
      return ( TAG_HEIGHT * this.line) + (4 * ( this.line - 1))
    }
    return null
  }

  get line(){
    if(this.couponTagViewStyle && this.couponTagViewStyle.lines){
      return this.couponTagViewStyle.lines
    }else {
      return 1
    }
  }

  getStyle(tag: any) {
    const style: CSSStyleDeclaration = Object.create(null)

    style.backgroundColor = getColor(tag.bg_color) || getColor('$neutral-400')

    style.color = getColor(tag.text_color)|| getColor('$orange-500')

    style['--lineColor'] = getColor(tag.lineColor) || getColor('$orange-500')

    return style
  }

  get paddingStyle() {
    const paddingStyle = Object.create(null)
    if(this.maxHeight){
        paddingStyle.maxHeight = `${ this.maxHeight }px`,
        paddingStyle.overflow = 'hidden'
    }
    paddingStyle.paddingTop = `${this.placeHolder}px`
    return paddingStyle
  }
}
</script>

<style lang="scss">
@import '../share';
  .klk-card-coupon-tag{
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    box-sizing: content-box;

    &_item{
      display:flex;
      align-items: center;
      position:relative;
      max-width: calc(100% - 14px);
      font-size: 12px;
      line-height: 16px;
      color: $neutral-700;
      background: $neutral-100;
      padding: 1px 6px;
      margin-right: 6px;
      max-height: 18px;
      border-radius: 2px;
      border: 1px solid var(--lineColor);


      &:before{
        position: absolute;
        display: block;
        content: "";
        width: 3px;
        height: 6px;
        top: 6px;
        left:-1px;
        background-color: $white;
        border-radius: 0 6px 6px 0;
        border: 1px solid var(--lineColor);
        border-left: 0;
      }
      &:after{
        position: absolute;
        display: block;
        content: "";
        top: 6px;
        width: 3px;
        height: 6px;
        background-color: $white;
        border-radius: 0 6px 6px 0;
        border: 1px solid var(--lineColor);
        border-left: 0;
        right: -1px;
        transform: rotateY(180deg);
      }

      &--margin{
        margin-bottom:4px
      }
    }

    &_item:last-of-type{
      margin-right: 0;
    }
    &_text{
      color: currentColor;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      max-width: 100%;
    }
  }
</style>
