<template>
  <p
    v-if="data && data.selling_price && data.selling_price_format"
    class="card-price-desc-small_content-sell"
    :style="formatStyle"
    v-html="formatPrice(data.selling_price,data.selling_price_format,sellingPriceViewStyle)"
  >
  </p>
</template>

<script lang="ts">
import {Vue, Component, Prop} from 'vue-property-decorator'
import { getColor } from '../design-token/token-utils'

@Component
export default class CardSellPrice extends Vue {
  @Prop({ type: Object, default: () => {} }) data!: any
  @Prop({ type: Object, default: () => {} }) sellingPriceViewStyle!:any

  get formatStyle(){
    const style: CSSStyleDeclaration = Object.create(null)
    if(!this.sellingPriceViewStyle){
      return null
    }

    style.fontSize = (this.sellingPriceViewStyle.formatTextSize || '16px') + 'px'
    style.fontWeight = !!this.sellingPriceViewStyle.formatTextBold ? '500' : 'normal'
    style.color = getColor(this.sellingPriceViewStyle.formatColor) || getColor('$neutral-900')

    return style
  }

  formatPrice (priceNumber: string, priceText: string, style:any) {
    const color = getColor(style.priceColor) || getColor('$neutral-900')
    const fontSize = (style.priceTextSize || '12px') + 'px'
    const bold = !!style.priceTextBold ? 500 : 'normal'

    if (!(priceNumber && priceText)) { return '' }
    let price = ''
    const priceMatch = priceText.match(/\{[^}]+\}/)

    if (priceMatch && priceMatch[0]) {
      price = `<span style="color: ${color};font-size: ${fontSize};font-weight: ${bold}">${priceNumber}</span>`
      price = priceText.replace(priceMatch[0], price)
    }

    return price
  }


}
</script>

<style lang="scss">
@import '../share';
  .card-price-desc-small_content-sell{
    width: 100%;
    font-size: 12px;
    line-height: 1.2;
    color: $neutral-900;
    word-break: break-all;

    strong{
      font-size: 14px;
      font-weight: 500;
    }
  }

</style>
