<template>
  <div
    v-if="data"
    class="card-price-desc-small"
  >
    <div class="card-price-desc-small_content">
      <p
        class="card-price-desc-small_content-market"
        :style="{ 'textDecoration': data.market_price_no_strike ? 'none' : 'line-through' }"
      >
        {{ data.market_price }}
      </p>
      <p
        class="card-price-desc-small_content-sell"
        v-html="getPrice(data.selling_price,data.selling_price_format,true)"
      >
      </p>
    </div>
    <div
      v-if="data.discount_tag && data.discount_tag.text"
      :style="getStyle(data.discount_tag)"
      class="card-price-desc-small_tag"
    >
      {{ data.discount_tag.text }}
    </div>
  </div>
</template>

<script lang="ts">
import { Vue, Component, Prop } from 'vue-property-decorator'
import { getPrice } from '../util'
import { getColor } from '../design-token/token-utils'

type PriceFunction = (priceObj: any, priceText: string, idBold?: Boolean) => String

@Component
export default class CardPriceDesc extends Vue {
  @Prop({ type: Object, default: () => {} }) data!: any

  getPrice: PriceFunction = getPrice

  getStyle(tag: any) {
    const left = getColor(tag.background_color_left)
    const right = getColor(tag.background_color_right)
    const textColor = getColor(tag.text_color)
    const style = `
          background: ${left};
          background: -moz-linear-gradient(left,  ${left} 0%, ${right} 100%);
          background: -webkit-linear-gradient(left,  ${left} 0%,${right} 100%);
          background: linear-gradient(to right,  ${left} 0%,${right} 100%);
          filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='${left}', endColorstr='${right}',GradientType=1 );
          color: ${textColor}
        `
    return style
  }
}
</script>

<style lang="scss">
@import '../share';
.card-price-desc-small{
  p{
    margin: 0;
    padding: 0;
  }

  &_content-sell{
    width: 100%;
    font-size: 12px;
    line-height: 16px;
    color: $neutral-900;
    word-break: break-all;

    strong{
      font-size: 14px;
      font-weight: 500;
    }
  }

  &_content-market{
    width: 100%;
    font-size: 12px;
    line-height: 16px;
    color: $neutral-600;
    text-decoration: line-through;
    word-break: break-all;
  }

  &_tag{
    display: inline-block;
    margin-top: 2px;
    color: $white;
    font-size: 12px;
    font-weight: 500;
    padding: 2px 4px;
    background: $orange-300;
    border-radius: 2px;
  }
}

</style>
