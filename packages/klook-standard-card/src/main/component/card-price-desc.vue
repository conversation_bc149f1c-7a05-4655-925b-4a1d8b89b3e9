<template>
  <div
    v-if="data"
    class="klk-card-price-desc"
    :class="{
      'klk-card-price-desc--right':generalPriceViewStyle.pricePosition && generalPriceViewStyle.pricePosition === 'right'
    }"
    :style="paddingStyle"
  >
    <div class="klk-card-price-desc_content">
      <p
        class="klk-card-price-desc_content-market"
        :style="{ 'textDecoration': data.market_price_no_strike ? 'none' : 'line-through' }"
      >
        {{ data.market_price }}
      </p>
      <p
        class="klk-card-price-desc_content-sell"
        v-html="getPrice(data.selling_price,data.selling_price_format,true)"
      >
      </p>
    </div>
    <div
      v-if="data.discount_tag && data.discount_tag.text"
      :style="getStyle(data.discount_tag)"
      class="klk-card-price-desc_tag"
    >
      {{ data.discount_tag.text }}
    </div>
  </div>
</template>

<script lang="ts">
import {Vue, Component, Prop} from 'vue-property-decorator'
import { getPrice } from '../util'
import { getColor } from '../design-token/token-utils'

type PriceFunction = (priceObj: any, priceText: string, idBold?: Boolean) => String

@Component
export default class CardPriceDesc extends Vue {
  @Prop({ type: Object, default: () => {} }) data!: any
  @Prop({ type: Number, default: 0 }) placeHolder!: number
  @Prop({ type: Object, default: () => ({ pricePosition: 'left' }) }) generalPriceViewStyle!: any

  getPrice: PriceFunction = getPrice

  getStyle(tag: any) {
    const left = getColor(tag.background_color_left)
    const right = getColor(tag.background_color_right)
    const textColor = getColor(tag.text_color)
    const style = `
          background: ${left};
          background: -moz-linear-gradient(left,  ${left} 0%, ${right} 100%);
          background: -webkit-linear-gradient(left,  ${left} 0%,${right} 100%);
          background: linear-gradient(to right,  ${left} 0%,${right} 100%);
          filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='${left}', endColorstr='${right}',GradientType=1 );
          color: ${textColor}
        `
    return style
  }

  get paddingStyle() {
    const paddingStyle = Object.create(null)
    paddingStyle.paddingTop = `${this.placeHolder}px`
    return paddingStyle
  }
}
</script>

<style lang="scss">
@import '../share';
.klk-card-price-desc {
  display: flex;
  flex-wrap: nowrap;
  justify-content: space-between;
  align-items: flex-end;

  &_content {
    flex: 1;
  }

  &_content-sell {
    width: 100%;
    font-size: 12px;
    line-height: 16px;
    color: $neutral-900;
    word-break: break-all;
    // overflow: hidden;
    // text-overflow: ellipsis;
    // display: -webkit-box;
    // -webkit-box-orient: vertical;
    // -webkit-line-clamp: 1;
    // word-break: break-all;

    strong {
      // margin: 0 2px;
      font-size: 14px;
      font-weight: 500;
    }
  }

  &_content-market {
    width: 100%;
    font-size: 12px;
    line-height: 16px;
    color: $neutral-600;
    text-decoration: line-through;
    word-break: break-all;
    // overflow: hidden;
    // text-overflow: ellipsis;
    // display: -webkit-box;
    // -webkit-box-orient: vertical;
    // -webkit-line-clamp: 1;
    // word-break: break-all;
  }

  &_tag {
    margin-left: 10px;
    // margin-top: 2px;
    color: $white;
    font-size: 12px;
    font-weight: 500;
    padding: 2px 4px;
    background: $orange-300;
    border-radius: 2px;
  }

  &--right{
    flex-direction: row-reverse;

    .klk-card-price-desc_content-market{
      text-align: end;
    }

    .klk-card-price-desc_content-sell{
      text-align: end;
    }
    .klk-card-price-desc_tag{
      margin-left:0;
      margin-right: 10px;
    }
  }
}

</style>
