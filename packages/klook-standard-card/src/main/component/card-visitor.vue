<template>
  <div
    v-if="data"
    class="klk-card-visitor"
    :style="paddingStyle"
  >
    <span class="klk-card-visitor_avatar-wrapper">
      <div
        v-lazy:background-image.container="formatPicUrl(data.avatar1,'m_activity_card_xs_160')"
        class="klk-card-visitor_avatar-one"
      >
      </div>
      <div
        v-lazy:background-image.container="formatPicUrl(data.avatar2,'m_activity_card_xs_160')"
        class="klk-card-visitor_avatar-two"
      >
      </div>
      <div
        v-lazy:background-image.container="formatPicUrl(data.avatar3,'m_activity_card_xs_160')"
        class="klk-card-visitor_avatar-three"
      >
      </div>
    </span>
    <span class="klk-card-visitor_fans">{{ data.text }}</span>
  </div>
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator'
@Component
export default class CardVisitor extends Vue {
  @Prop({ type: Object, default: null }) data!: any
  @Prop({ type: Number, default: 0 }) placeHolder!: number

  formatPicUrl(url: string) {
    return url || 'https://res.klook.com/image/upload/v1611304744/ued/Other/Group_871.jpg'
  }

  get paddingStyle() {
    const paddingStyle = Object.create(null)
    paddingStyle.paddingTop = `${this.placeHolder}px`
    return paddingStyle
  }
}
</script>

<style lang="scss">
@import '../share';
.klk-card-visitor{
  display: flex;
  align-items: center;

  &_avatar-wrapper{
    position: relative;
    flex: 0 0 40px;
    width: 40px;
    height: 20px;

    >div{
      position: absolute;
      width: 20px;
      height: 20px;
      border-radius: 50%;
      border: 1px solid $white;
      background-color: $neutral-200;
      background-repeat: no-repeat;
      background-size: contain;
      background-position: center;
    }
  }

  &_avatar-one{
    left: 0;
    z-index:2 ;
  }

  &_avatar-two{
    left: 10px;
    z-index: 1;
  }

  &_avatar-three{
    left: 20px;
  }

  &_fans{
    flex: 1;
    font-size: 12px;
    line-height: 16px;
    color: #212121;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: normal;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
  }
}
</style>
