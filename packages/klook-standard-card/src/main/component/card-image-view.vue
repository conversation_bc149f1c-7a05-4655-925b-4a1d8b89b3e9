<template>
  <div
    v-if="data"
    v-lazy:background-image.container="data"
    :style="imageStyle"
    class="klk-card-image-view"
  ></div>
</template>

<script lang="ts">
import { Vue, Component, Prop } from 'vue-property-decorator'

@Component
export default class CardImageView extends Vue {
  @Prop({ type: String, default: '' }) data!: string
  @Prop({ type: Object, default: () => {} }) imageViewStyle!:any

  get imageStyle() {
    const style:CSSStyleDeclaration = Object.create(null)
    if(this.imageViewStyle){
      switch (this.imageViewStyle.scaleType) {
        case "cover":
          style.backgroundSize = 'cover'
          break
        case "contain":
          style.backgroundSize = "contain"
          break
        case "fill":
          style.backgroundSize = "auto"
          break
        default:
          style.backgroundSize ="cover"
      }
      if(this.imageViewStyle.placeHolder){
        style.backgroundColor = '#E9E9E9'
      }
    }

    return style
  }

}
</script>

<style lang="scss">
  .klk-card-image-view{
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center;
  }

</style>
