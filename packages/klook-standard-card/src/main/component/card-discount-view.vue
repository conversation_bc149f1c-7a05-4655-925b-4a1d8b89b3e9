<template>
  <div class="klk-card-discount-view">
    <div class="klk-card-discount-view_wrapper">
      <template v-if="direction === 'vertical'">
        <p v-if="discount.discountTitle" class="klk-card-discount-view_title" :style="discountStyle">
          {{ discount.discountTitle }}
        </p>
        <p class="klk-card-discount-view_discount" :style="formatStyle">{{ data.discount}}</p>
        <p v-if="discount.discountDesc" class="klk-card-discount-view_desc" :style="discountStyle">
          {{ discount.discountDesc }}
        </p>
      </template>
      <template v-else>
        <p class="klk-card-discount-view_content" :style="discountStyle">{{ discount.discountDesc }}<span :style="formatStyle">{{ data.discount}}</span>{{ discount.discountDesc }}</p>
      </template>
    </div>
  </div>
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator'
import {getColor} from "../design-token/token-utils";
const LineHeightMap:any = {
  60:66,
  44:52,
  32:42,
  28:36,
  24:32,
  20:26,
  18:24,
  16:22,
  14:20,
  12:16
}
@Component
export default class CardDiscountView extends Vue {
  @Prop({ type: Object, default: ()=>{} }) discountViewStyle!: any
  @Prop({ type: Object, default: () => {} }) data!: any

  get direction(){
    return (this.discountViewStyle && this.discountViewStyle.direction) || 'vertical'
  }

  get discountStyle(){
    const Style:CSSStyleDeclaration = Object.create(null)
    if(!this.discountViewStyle){ return Style }

    const LineHeight = LineHeightMap[this.discountViewStyle.discountTextSize] || (this.discountViewStyle.discountTextSize * 1.44)
    Style.color = getColor(this.discountViewStyle.discountColor)
    Style.fontSize = `${this.discountViewStyle.discountTextSize}px`
    Style.lineHeight = `${LineHeight}px`
    if(this.discountViewStyle.formatTextBold){ Style.fontWeight = '500' }

    return Style
  }

  get formatStyle(){
    const Style:CSSStyleDeclaration = Object.create(null)
    if(!this.discountViewStyle){ return Style }

    const LineHeight = LineHeightMap[this.discountViewStyle.formatTextSize] || (this.discountViewStyle.formatTextSize * 1.44)
    Style.color = getColor(this.discountViewStyle.formatColor)
    Style.fontSize = `${this.discountViewStyle.formatTextSize}px`
    Style.lineHeight = `${LineHeight}px`
    if( this.discountViewStyle.discountTextBold){ Style.fontWeight = '500' }

    return Style
  }

  get discount(){
    if(this.data && this.data.discount_format){
      const tpl = this.data.discount_format.split('{discount}')
      if(tpl){
        return {
          discountTitle: tpl[0],
          discountDesc: tpl[1]
        }
      }
    }else {
      return {
        discountTitle: '',
        discountDesc: ''
      }
    }
  }
}
</script>

<style lang="scss">
.klk-card-discount-view{
  display: flex;
  align-items: center;
  justify-content: center;
  &_title,&_discount,&_desc{
    width: 100%;
    word-break: break-word;
    text-align: center;
  }
  &_content {
    text-align: center;
    word-break: break-word;
  }
}

</style>
