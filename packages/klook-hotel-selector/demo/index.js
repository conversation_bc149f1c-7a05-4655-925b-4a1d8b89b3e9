import HotelSelectorMobile from '../src/mobile/selector-mobile.vue'
import HotelLayerGuest from '../src/mobile/layer-guest.vue'
import HotelLayerDestination from '../src/mobile/layer-destination.vue'
import HotelLayerCalendar from '../src/mobile/layer-calendar.vue'

import HotelSelectorDesktop from '../src/desktop/selector-desktop.vue'
import HotelPoptipGuest from '../src/desktop/poptip-guest.vue'
import HotelPoptipDestination from '../src/desktop/poptip-destination.vue'
import HotelPoptipCalendar from '../src/desktop/poptip-calendar.vue'

import Calendar from '../src/components/calendar.vue'
import Guest from '../src/components/guest.vue'
import PageLayer from '../src/components/page-layer.vue'

import locale from "./locale.json";
import Format from '../../klook-ui/src/locale/format';

export { default as dayjs } from 'dayjs'

export default (Vue) => {
  const format = Format()
  const transform = (key, options) => format(locale[key.replace(/-\w*/, '')], options)
  Vue.directive('no-scroll', () => {})
  ;[
    Calendar,
    Guest,
    PageLayer,
    HotelSelectorMobile,
    HotelLayerGuest,
    HotelLayerDestination,
    HotelLayerCalendar,
    HotelSelectorDesktop,
    HotelPoptipGuest,
    HotelPoptipDestination,
    HotelPoptipCalendar
  ].forEach(component => {
    component.prototype.$t = transform
    Vue.component(component.options.name, component)
  })

  Vue.component('svg-icon',  {
    name: 'SvgIcon',
    props: ['name', 'size', 'width', 'height', 'color'],
    render(h) {
      const { name, size, width, height, color } = this

      const [, iconType] = name.split('#')
      return h('klk-icon', {
        props: {
          size: [size, width, height].find(a => +a > 0) || undefined,
          color,
          type: {
          'icon-close': 'icon_navigation_close_m',
          'icon-location': 'icon_travel_location_fill',
          'icon_edit_delete_s': 'icon_edit_delete',
          'icon_navigation_chevron_up_m': 'icon_navigation_chevron_up',
          'icon_navigation_chevron_down_m': 'icon_navigation_chevron_down'
          }[iconType]
        }
      })
    }
  })

  Vue.component('client-only',  {
    name: 'ClientOnly',
    functional: true,
    props: {
      placeholder: String,
      placeholderTag: {
        type: String,
        default: 'div'
      }
    },
    render(h, { parent, slots, props }) {
      const { default: defaultSlot = [], placeholder: placeholderSlot } = slots()

      if (parent._isMounted) {
        return defaultSlot
      }

      parent.$once('hook:mounted', () => {
        parent.$forceUpdate()
      })

      if (props.placeholderTag && (props.placeholder || placeholderSlot)) {
        return h(
          props.placeholderTag,
          {
            class: ['client-only-placeholder']
          },
          props.placeholder || placeholderSlot
        )
      }

      // Return a placeholder element for each child in the default slot
      // Or if no children return a single placeholder
      return defaultSlot.length > 0 ? defaultSlot.map(() => h(false)) : h(false)
    }
  })
}
