<template>
  <transition name="bottom-in">
    <div v-show="visible" class="page-layer" :style="{ zIndex: zIndex }">
      <div v-if="needHeader" class="page-layer-header" :class="headerBottom">
        <slot name="header">
          <div class="flex-header">
            <klk-icon
              v-galileo-click-tracker="{spm: 'BackBtnModule'}"
              :size="24"
              type="icon_navigation_close_m"
              data-spm-virtual-item="__virtual"
              :data-spm-module="`${BackBtnModule}?trg=manual`"
              @click.native="close"
            />
            <span class="flex-auto">{{ title }}</span>
            <slot name="header-right">
              <span v-if="navBtnText" class="btn-secondary" @click="$emit('rightbtn-click')">{{ navBtnText }}</span>
            </slot>
          </div>
          <slot name="header-plus"></slot>
        </slot>
      </div>
      <div class="page-layer-content">
        <slot></slot>
      </div>
      <div v-if="$slots.footer" class="page-layer-footer" @click="$emit('footer-click')">
        <slot name="footer"></slot>
      </div>
    </div>
  </transition>
</template>

<script lang="ts">
import { Vue, Component, Watch, Model, Prop } from 'vue-property-decorator'
// @ts-ignore
import { lockOuterScroll, unlockOuterScroll } from '@klook/klook-ui/lib/utils/dom'

@Component({
  name: 'page-layer'
})
export default class PageLayer extends Vue {
  @Model('close', { type: Boolean, default: false }) visible!: boolean
  @Prop({ type: String, default: '' }) title!: string
  @Prop({ type: Boolean, default: true }) releaseBody!: boolean // 单例弹窗
  @Prop({ type: String, default: '' }) navBtnText!: string
  @Prop({ type: String, default: 'border' }) headerBottom!: string // border || shadow
  @Prop({ type: Boolean, default: true }) needHeader!: boolean
  @Prop({ type: String, default: 'BackBtn' }) BackBtnModule!: string
  @Prop({ type: Number, default: 2000 }) zIndex!: string

  close() {
    this.$emit('close', false)
    this.$emit('update:visible', false)
  }

  mounted() {
    document.body.appendChild(this.$el)
  }

  @Watch('visible')
  visibleChange(val?: boolean) {
    val ? lockOuterScroll(this.$el, false) : unlockOuterScroll(this.$el, false)
  }

  beforeDestroy () {
    this.visible && this.visibleChange()
    document.body.removeChild(this.$el)
  }
}

export const showLayer = (
  slots = {} as {
    default?: Vue['$vnode'],
    header?: Vue['$vnode'],
    footer?: Vue['$vnode']
  },
  option: { [key: string]: string } = {}
): PageLayer => {
  let layer: any = new PageLayer({
    propsData: {
      visible: true,
      headerBottom: '',
      ...option
    }
  })

  slots.default = slots.default || layer.$createElement('klk-loading')

  layer.$slots = slots

  layer.$mount()

  document.body.appendChild(layer.$el)
  layer.$on('close', () => {
    document.body.removeChild(layer.$el)
    layer.$destroy()
    layer = null
  })
  return layer
}
</script>

<style lang="scss" scoped>

.bottom-in-enter,
.bottom-in-leave-to {
  transform: translateY(100%);
}

.bottom-in-leave-active,
.bottom-in-enter-active {
  transition: all 0.3s ease-in;
}

.page-layer {
  color: $color-text-primary;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: $color-bg-widget-normal;
  flex-direction: column;
  display: flex;

  .flex-header {
    display: flex;
    position: relative;
    align-items: center;
    min-height: 48px;

    & > svg {
      position: absolute;
      left: 0;
      color: $color-icon-normal;
    }
  }

  .btn-secondary {
    position: absolute;
    right: 0;
    height: 24px;
    flex: 0 0 auto;
    border-radius: $radius-s;
    color: $color-info;
    line-height: 24px;
    font-weight: $fontWeight-regular;
    font-size: $fontSize-body-s;
  }

  .flex-auto {
    flex: 1 1 auto;
  }

  &-header {
    padding: 0 16px;
    overflow: hidden;
    flex: 0 0 auto;
    background-color: $color-bg-widget-normal;
    &.border {
      box-shadow: inset 0 -1px 0 0 rgba(0, 0, 0, 0.12);
    }

    &.shadow {
      box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.12);
    }

    .flex-auto {
      text-align: center;
      margin: 0 16px;
      overflow: hidden;
      text-overflow: ellipsis;
      @include font-body-m-semibold;
    }

    svg.placeholder {
      opacity: $opacity-transparent;
    }
  }

  &-content {
    flex: 1 1 auto;
    min-height: 0;
    overflow-x: hidden;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
    transform: translate3d(0, 0, 0);
  }

  &-footer {
    flex: 0 0 auto;
    padding: 8px 20px;
    background: $color-bg-widget-normal;
    display: flex;
    border-top: solid 0.5px $color-border-dim;

    button {
      height: 44px;
    }
  }
}
</style>
