<script lang="tsx">
import { Vue, Component, Model, Prop, Watch } from 'vue-property-decorator'
import { GuestInfo, GuestConfig } from '../../type'
import type { RenderContext } from "vue/types/options";

type GuestConfigWithValue = GuestConfig & { value: number }
type GuestConfigWithOption = 'hide' | 'disabled' | GuestConfig
type GuestInfoKey = keyof GuestInfo

type CollapseScopedProps = { expand: boolean, toggle: Function }

const guestKey: GuestInfoKey[] = ['adult_num', 'room_num', 'child_num', 'age']

type LocalItem = {
  label: string
  increase?: (num: number) => string
  decrease?: (num: number) => string
}

@Component({
  name: 'hotel-guest',
  inheritAttrs: false,
  components: {
    Num: {
      functional: true,
      props: { guestKey: String },
      render(
        _h: GuestSelect['$createElement'],
        {
          parent,
          props: { guestKey },
        }: RenderContext<{
          guestKey: GuestInfoKey
        }>
      ) {
        const attrs = parent.guestInfo[guestKey]
        const locale = parent.getLocale(guestKey)
        return <section class={['hotel-guest-item', guestKey === 'child_num' && !attrs.value && 'no-child']}>
          <b>{ locale.label }</b>
          <div class="counter-wraper">
            {
              attrs.max <= attrs.min
                ? attrs.value
                : <klk-counter
                  size="small"
                  max={ attrs.max }
                  min={ (guestKey === 'adult_num' && parent.guestInfo.room_num?.value) || attrs.min }
                  value={ attrs.value }
                  onDisabled-click={ (type: 'increase' | 'decrease') => parent.remindToast(type, locale, attrs.value) }
                  onChange={ parent.setGuestInfo.bind(parent, attrs, 'value') }
                />
            }
          </div>
        </section>
      }
    },
    Age: {
      functional: true,
      render(
        _h: GuestSelect['$createElement'],
        { parent }: RenderContext
      ) {
        const { age } = parent.guestInfo
        const { max, min } = age
        const disabled = max <= min

        return !parent.guestInfo.child_num?.value
        // @ts-ignore
          ? undefined as Vue['$vnode']
          : <div class="hotel-child-age">
            <p><b>{ parent.$t('168456') }</b></p>
            <div class="age-tip">{ parent.$t('168454') }</div>
            <div class="age-select">
              {
                <klk-collapse size="small" accordion>
                  {
                    age.map((value: number, index: number) => <klk-collapse-item
                      key={index}
                      name={index}
                      scopedSlots={{
                        header: ({ expand, toggle}: CollapseScopedProps) => <p
                          class="collapse-age-header"
                          onClick={ (evt: Event) => {
                            !disabled && toggle()
                            setTimeout(() => {
                              const top = (evt.target as HTMLElement)?.offsetTop
                              top && parent.$emit('collapse-change', top)
                            }, 400)
                          }}
                        >
                          <span>{ parent.$t('168453', { num: index + 1 }) }</span>
                          <span>
                            <span class={ disabled && 'disabled' }>
                              { value > 0 ? parent.$t('168452', { num: value }) : parent.$t('168451') }
                            </span>
                              {
                                !disabled && <klk-icon
                                  class="svg-icon"
                                  type={expand ? 'icon_navigation_chevron_up' : 'icon_navigation_chevron_down'}
                                  size={20}
                                />
                              }
                        </span>
                      </p>,
                      default: disabled
                        ? undefined
                        : ({ toggle }: CollapseScopedProps) => <klk-tag-group
                          checkable
                          value={ value }
                          onInput={(val: number) => {
                            parent.setGuestInfo(age, index, val)
                            toggle()
                          }}
                          >
                            {
                              Array.from({ length: max + 1 - min }, (_v, idx) => {
                              const val = min + idx
                              return <klk-tag class="age-tag" value={ val } key={ idx } >
                                  { val || '<1' }
                                </klk-tag>
                            })
                          }
                          </klk-tag-group>
                        }}
                    />)
                  }
                </klk-collapse>
              }
            </div>
          </div>
      }
    }
  }
})
export default class GuestSelect extends Vue {
  @Model('change', { type: Object, default: () => ({}) }) options!: GuestInfo
  @Prop({ type: String }) platform!: 'mobile' | 'desktop'
  @Prop({ type: Boolean, default: true }) syncChange!: boolean
  @Prop({ type: Number, default: 8 }) defaultChildAge!: number
  @Prop({ type: [String, Object] }) room!: GuestConfigWithOption
  @Prop({ type: [String, Object] }) age!: GuestConfigWithOption
  @Prop({ type: [String, Object] }) adult!: GuestConfigWithOption
  @Prop({ type: [String, Object] }) child!: GuestConfigWithOption

  get isMobile() {
    // @ts-ignore
    return this.platform === 'mobile'
  }

  guestInfo: {
    room_num?: GuestConfigWithValue
    adult_num?: GuestConfigWithValue
    child_num?: GuestConfigWithValue
    age?: number[] & GuestConfigWithValue
  } = {}

  isChange = false
  componentHash = ''
  unwatchMap: { child?: Function , room?: Function } = {}

  setGuestInfo(obj: GuestConfigWithValue, key: string, value: number) {
    this.$set(obj, key, +value)
    this.isChange = true
    this.syncChange && this.$nextTick(this.commitGuestInfo)
  }

  // commitChange 仅mobile 端 点击了确认按钮后传的是 true
  commitGuestInfo() {
    const result = Object.assign({}, this.options, Object.keys(this.guestInfo).reduce((res, key) => {
      res[key] = this.guestInfo[key as GuestInfoKey]!.value
      return res
    }, {} as Record<string, any>))

    // 这个场景用在 package页 去除配置项为 hide 的选项
    guestKey.forEach(key => {
      if(!this.guestInfo.hasOwnProperty(key)) {
        delete result[key]
      }
    })

    this.$emit('change', result)
    return result
  }


  // 设置项变化也得重新初始化
  @Watch('$props', { immediate: true, deep: true })
  initGuestConfig() {
    if (this.isChange) {
      return (this.isChange = false)
    }

    const { room_num, adult_num, child_num, age } = this.options

    const defaultGuestInfo = {
      room_num: {
        max: 8,
        min: 1,
        value: +room_num || 1
      },
      adult_num: {
        max: 10,
        min: 1,
        value: +adult_num || 2
      },
      child_num: {
        max: 8,
        min: 0,
        value: +child_num || 0
      },
      age: Object.defineProperty(
        Object.assign(
          child_num > 0 && this.options.age
            ? age!.split(',').slice(0, child_num).map(Number)
            : []
          , { max: 17, min: 0 }
        )
        , 'value'
        , { get() { return this.toString() } }
      ) as this['guestInfo']['age']
    } as this['guestInfo']

    for (const key in defaultGuestInfo) {
      let config = this[key.replace('_num', '') as 'room'] as GuestConfig
      if (!config) {
        continue
      }

      const configType = typeof config

      if(configType === 'string') {
        if(config === 'hide') {
          delete defaultGuestInfo[key as GuestInfoKey]
          continue
        } else if (config === 'disabled') {
          config = { max: 0, min: 0 }
        }
      } else if(configType !== 'object' || !['max', 'min'].every(key => !config.hasOwnProperty(key) || (config as any)[key] > -1)) {
        continue
      }
      Object.assign(defaultGuestInfo[key as 'room_num']!, config)
    }

    // klk-counter 组件 会在prop的值发生变化时触发事件， 又导致了 isChange 变为 true
    // 所以这里处理为强制组件重新 render
    this.componentHash = String(new Date().getTime()).slice(-5)

    this.guestInfo = defaultGuestInfo

    // 可能会存在联动组合配置项动态变化的场景
    if(this.guestInfo.age && this.guestInfo.child_num && this.guestInfo.child_num.max !== this.guestInfo.child_num.min) {
      this.unwatchMap.child = this.unwatchMap.child  ||
        this.$watch('guestInfo.child_num.value', (val: number) => {
          const { age } = this.guestInfo
          const len = age!.length
          len < val
        ? age!.push(...new Array(val - len).fill(this.defaultChildAge))
        : age!.splice(val)
        })
    } else if(this.unwatchMap.child) {
      this.unwatchMap.child()
      delete this.unwatchMap.child
    }

    if([this.guestInfo.room_num, this.guestInfo.adult_num].every(config => config && config.max !== config.min)) {
      this.unwatchMap.room = this.unwatchMap.room ||
        this.$watch('guestInfo.room_num.value', (val: number) => {
          if (val > Number(this.guestInfo.adult_num?.value)) {
            this.guestInfo.adult_num!.value = val
          }
        })
    } else if(this.unwatchMap.room) {
      this.unwatchMap.room()
      delete this.unwatchMap.room
    }
  }

  beforeDestroy() {
    const unwatchArr = Object.values(this.unwatchMap)
    if(unwatchArr.length) {
      unwatchArr.forEach(unwatch => unwatch())
      this.unwatchMap = {}
    }
  }

  getLocale(field: GuestInfoKey): LocalItem {
    switch (field) {
      case 'adult_num':
        return  {
          label: this.$t('17642'),
          increase: num => this.$t('168437', { num }),
          decrease: num => this.$t('168436', { num })
        }
      case 'child_num':
        return {
          label: this.$t('168457'),
          increase: num => this.$t('168435', { num }),
        }
      case 'room_num':
        return {
          label: this.$t('17641'),
          increase: num => this.$t('168439', { num }),
          decrease: num => this.$t('168438', { num })
        }
      case 'age':
        return {
          label: this.$t('168456'),
        }
    }
    return {
      label: ''
    }
  }

  remindToast(type: 'increase' | 'decrease', locale: LocalItem, val: number) {
    let container = this.$el
    const parentName = this.$parent?.$options.name

    if(parentName === 'klk-bottom-sheet') {
      container = this.$parent.$el.querySelector('.klk-bottom-sheet-inner')!
    } else if(parentName === 'klk-poptip') {
      container = this.$parent.$el.querySelector('.klk-poptip-popper')!
    }

    if (val) {
      const tip = locale[type]?.(val)
      tip && this.$toast(tip, container)
    }
  }

  render() {
    return <div
      class={ ['hootel-guest-content', `platform-${this.isMobile ? 'mobile' : 'desktop'}`] }
      key={ this.componentHash }
    >
      {
        Object.keys(this.guestInfo).map((key) => {
          const componentName = key.slice(-3)
          return <componentName
            key={key}
            guestKey={key}
          />
        })
      }
    </div>
  }
}
</script>

<style lang="scss" scoped>

.hootel-guest-content {
  width: 100%;
  position: relative;

  .hotel-guest-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid $color-border-dim;
    padding: 20px 0;

    > b {
      @include font-paragraph-m-bold;
    }

    .counter-wraper {
      width: 108px;
      text-align: center;
    }
  }

  .hotel-child-age {
    margin-top: 20px;
    .age-tip {
      margin-bottom: 4px;
      color: $color-text-secondary;

      @include font-body-s-regular;
    }
  }

  .age-select {
    @include font-body-s-regular;

    ::v-deep .klk-collapse-item {
      .klk-collapse-item-content-inner {
        padding: 0;
      }

      &:last-child {
        border: none;
      }

    }


    .collapse-age-header {
      display: flex;
      justify-content: space-between;
      padding: 16px 0;
      cursor: pointer;

      .svg-icon {
        margin-left: 4px;
        vertical-align: middle
      }
    }

    .age-tag {
      margin-right: 10px;
      margin-bottom: 10px;
      padding-left: 0;
      padding-right: 0;
      text-align: center;
      text-align: center;
      white-space: nowrap;
      border-radius: $radius-xxl;
      width: calc((100% - 50px) / 6);
      &:nth-child(6n) {
        margin-right: 0;
      }
      @include font-body-s-regular;
    }
  }

  &.platform-desktop .no-child {
    border: none;
  }
}

</style>
