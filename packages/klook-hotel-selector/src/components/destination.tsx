import Vue, { RenderContext, CreateElement } from 'vue'
import { DestinationListItem, HotelQueryHistory, DestinationInfo } from '../../type'
import { formatGuestDesc, formatDate, getTagIHTModule, getTagSpm, getHighlightFormater, setNewImageSize } from '../utils'
import { IconDelete } from '@klook/klook-icons'

type Ctx = {
  destinationList: DestinationListItem[],
  historyList: HotelQueryHistory[],
  chooseLocation: Function
  keyword: string
  searchList: DestinationInfo[] & { keyword?: string }
} & Vue

const renderHistory = (h: CreateElement, title: string, ctx: Ctx) => {
  // 不展示探索附近酒店以及无日期的历史记录
  const historyList = ctx.historyList.filter((item: HotelQueryHistory) => item.location.stype !== 'location' && item.time?.[0] && item.time?.[1]).slice(0, 2)
  const single = historyList.length === 1
  return historyList.length > 0 && [
    <p class="destination-title">
      { title }
      <IconDelete
        class="history-clear"
        size={20}
        nativeOnClick={() => {
          ctx.$emit('clear-history')
        }}
      />
    </p>,
    <div class="destination-group">
      {
        historyList.map((history, index) => <div
            class = { ['destination-group-item', 'search-history', { single }] }
            {...{ directives: [{ name: 'galileo-click-tracker', value: { spm: getTagSpm('search_history') } }] }}
            data-spm-virtual-item="__virtual"
            data-spm-module={ getTagIHTModule(history.location, 'search_history', index, historyList.length) }
            onClick={ (e: Event) => { ctx.$emit('select-history', history) } }
          >
            <p class="history-name text-ellipsis">{ history.location.title }</p>
            <p class="history-params text-ellipsis">{ formatGuestDesc.call(ctx, history.params) }</p>,
              {
                history.time?.length === 2 && <p class="history-params text-ellipsis">
                  { history.time.map(date => formatDate.call(ctx, date)).join(' - ') }
                </p>
              }
          </div>
        )
      }
    </div>
  ]

}

const renderDestination = (h: CreateElement, section: DestinationListItem, ctx: Ctx) => {
  const isLocation = section.type === 'location'
  const vNodes = section.content.map((item, index) => {
    const image = item.image ? setNewImageSize(item.image, 'image/upload/', 375, 212, false) : ''
    return <span
      key={ `${section.type}-${ index}` }
      class={`destination-group-item destination-location ${isLocation ? 'text-ellipsis' : ''}`}
      {...{ directives: [{ name: 'galileo-click-tracker', value: { spm: getTagSpm(section.type), enable: 'getTagSpm(section.type) ' } }] }}
      data-spm-virtual-item="__virtual"
      data-spm-module={ getTagIHTModule(item, section.type, index, section.content.length) }
      onClick={ () => ctx.chooseLocation(item) }
      style={{ backgroundImage: item.image ? `url(${image})` : 'unset'}}
    >
      {item.icon_url && <img src={item.icon_url} />}
      <span>{ item.title }</span>
    </span>
  })

  return [
    <p class="destination-title">
      { section.section_title }
    </p>,
    <div class={`destination-group ${section.type}`}>
      { vNodes }
    </div>
  ]
}

export default {
  Recommend: {
    functional: true,
    render(
      h: CreateElement,
      { parent: ctx }: { parent: Ctx } & RenderContext
    ) {
      return ctx.destinationList.map((section, sectionIndex) => {
        const contentVnode = section.type === 'search_history'
          ? ctx.historyList.length > 0 && renderHistory(h, section.section_title, ctx)
          : section.content.length > 0 && renderDestination(h, section, ctx)

          return contentVnode && <section
            key={ `${section.type}-${sectionIndex}`}
            class="destination-wrapper"
          >
            { contentVnode }
          </section>
      })
    }
  },
  Suggest: {
    functional: true,
    render(
      h: CreateElement,
      { parent: ctx }: { parent: Ctx } & RenderContext
    ) {
      const formatHighlight = getHighlightFormater(ctx.searchList.keyword!)
      return ctx.searchList.map((item, index) => {
        const isGroup = Boolean(item.child_list?.length)
        return <div
          class="search-suggest flex-container"
          key={index}
          {
            ...(!isGroup && {
              attrs: {
                'data-spm-virtual-item': '__virtual',
                'data-spm-module': getTagIHTModule(item, 'suggestion', index, ctx.searchList.length, ctx.searchList.keyword)
              },
              directives: [
                {
                  name: 'galileo-click-tracker',
                  value: {
                    spm: getTagSpm('suggestion'),
                  }
                }
              ],
              on: {
                click: () => { ctx.chooseLocation(item) }
              }
            })
          }
        >
          <img class="flex-none" src={ item.icon_url } width="20" height="20" />
          <div class="flex-grow">
            <div
              class="flex-container"
              {...{ directives: [{ name: 'galileo-click-tracker', value: { spm: getTagSpm('suggestion') } }] }}
              data-spm-module={ isGroup && getTagIHTModule(item, 'suggestion', index, ctx.searchList.length, ctx.searchList.keyword, true) }
            >
              <div class="flex-grow flex-container">
                <p class="search-suggest-title text-ellipsis" domProps={ { innerHTML: formatHighlight(item.title!) }} />
                {
                  item.tag_info && <klk-label
                  class="flex-none"
                    size="small"
                    style={ {
                      marginLeft: '8px',
                      color: item.tag_info.text_color,
                      backgroundColor: item.tag_info.bg_color,
                      border: item.tag_info.border_color && `1px solid ${item.tag_info.border_color}`,
                    } }
                  >{ item.tag_info.name }</klk-label>
                }
              </div>
              <span class="search-suggest-desc c-gray flex-none text-ellipsis">{ item.type_desc }</span>
            </div>
            { item.secondary_title && <p class="search-suggest-desc-second flex-none text-ellipsis" domProps={ { innerHTML: formatHighlight(item.secondary_title!) }}></p> }
            {
              isGroup
              ? item.child_list!.map((child, childIndex) => <div
                ket={childIndex}
                class="search-suggest-desc text-ellipsis"
                {...{ directives: [{ name: 'galileo-click-tracker', value: { spm: getTagSpm('suggest_inner') } }] }}
                data-spm-virtual-item="__virtual"
                data-spm-module={ getTagIHTModule(child, 'suggest_inner', childIndex, item.child_list!.length, ctx.searchList.keyword) }
                onClick={ () =>ctx.chooseLocation(child) }
              >{ child.title }</div>)
              : <p class="search-suggest-desc c-gray text-ellipsis">{ item.sub_title }</p>
            }
          </div>
        </div>
      })
    }
  }
}
