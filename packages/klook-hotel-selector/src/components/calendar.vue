<script lang="ts">
import { Vue, Component, Prop, Model } from 'vue-property-decorator'
import dayjs from 'dayjs'
import { diffDay } from '../utils'

const stringDateReg = /^2\d{3}-(0[1-9]|1[0-2])-([0-2]\d|3[0-1])$/

@Component({
  name: 'hotel-calendar',
  inheritAttrs: false
})
export default class SimpleCalendar extends Vue {
  @Model('change', { type: [Object, Array], default: () => [] }) calendarInfo!: { check_in?: string; check_out?: string } & string[]
  @Prop({ type: String }) platform!: 'mobile' | 'desktop'
  @Prop({ type: Object, default: null }) dailyPriceInfo!: { [key: string]: string }
  @Prop({ type: Function }) isSoldOut!: Function
  @Prop({ type: Function }) customRenderPrice!: Function
  @Prop({ type: [Date, String, Number], default: () => new Date() }) minDate!: number | Date | string
  @Prop({ type: [Number, Date, String] }) maxDate!: number | Date | string
  @Prop({ type: [Date, null]}) singleCheckIn: !Date | null
  @Prop({ type: Number, default: 1 }) minLos!: number
  @Prop({ type: Number, default: 28 }) maxLos!: number

  // 首次自动回填触发everyChange的时候修改为ture, 用于后续日期无变更的场景交互
  isAutoSelected: boolean = false
  isSupportEveryChange = false

  get isArrayDates() {
    return Array.isArray(this.calendarInfo)
  }

  get dateRange() {
    let calendarInfo = this.calendarInfo || []
    if (!this.isArrayDates) {
      const { check_in, check_out } = this.calendarInfo || {}
      calendarInfo = [check_in, check_out] as string[]
    }
    const range = calendarInfo
      .filter(stringDate => stringDate && stringDateReg.test(stringDate))
      .map(date => new Date(date))
    return range.length === 2 ? range : []
  }

  set dateRange(dates: Date[]) {
    dates = Array.isArray(dates) ? dates : []
    this.$emit('single-change', dates.length === 1 ? dates[0] : null)

    if (dates.length !== 2) {
      return
    }

    const [oldDates, newDates] = [this.dateRange, dates].map(dates => dates.map(date => dayjs(date).format('YYYY-MM-DD')))
    // 相同日期 不触发事件  否则切换历史记录有问题
    if (oldDates.toString() !== newDates.toString()) {
      this.$emit('change', this.isArrayDates
        ? newDates
        : Object.assign({}, this.calendarInfo, { check_in: newDates[0], check_out: newDates[1] })
      )
    }
  }

  get finalMinDate() {
    if (this.minDate) {
      return dayjs(this.minDate).toDate()
    }
    return new Date()
  }

  get finalMaxDate() {
    let limit = this.$store?.state?.hotel?.dateRange || 365
    if (this.maxDate) {
      if (typeof this.maxDate === 'number' && this.maxDate < 9999) {
        limit = this.maxDate
      } else {
        return dayjs(this.maxDate).toDate()
      }
    }
    return dayjs(this.finalMinDate).add(limit, 'd').toDate()
  }

  get isMobile() {
    return this.platform === 'mobile'
  }

  get datePickerProps() {
    return {
      type: 'date-range',
      isSoldOut: this.isSoldOut,
      width: this.isMobile ? 'auto' : undefined,
      syncAllDate: true,
      [this.isMobile ? 'verticalScroll' : 'doublePanel']: true,
      minDate: this.finalMinDate,
      maxDate: this.finalMaxDate,
      defaultViewDate: this.finalMinDate,
      isSelectable: (date: Date, selectedDates: Date[] = []) => {
        if (selectedDates.length === 1) {
          const [selectedDate] = selectedDates
          const diff = diffDay(date, selectedDate, false)

          if (diff === 0) {
            return false
          }
          if (diff > this.maxLos) {
            this.$toast(this.$t('168458', { num: this.maxLos }), this.$el)
            return false
          }
        }

        return true
      }
    }
  }

  // klook-ui 1.30.0 以上才支持，有些运行环境的klook-ui版本较低, 所以这里只处理 single 的情况
  // 这里是为了满足用户选择了其中一个日期后，清除按钮能够正常操作、确定按钮能禁用并友好提示
  onEveryChange(dates: Date[]) {
    this.isSupportEveryChange = true
    this.dateRange = dates
    if (dates.length === 2) {
      this.isAutoSelected && this.$emit('update', dates.map(date => dayjs(date).format('YYYY-MM-DD')))
      this.isAutoSelected = true
    }
  }
  onChange(dates: Date[]) {
    if (this.isSupportEveryChange) return
    this.dateRange = dates
  }

  render(h: this['$createElement']) {
    return h('klk-date-picker', {
      ref: 'datePicker',
      class: ['hotel-calendar', 'hotel-calendar-' + (this.isMobile ? 'mobile' : 'desktop')],
      props: {
        date: this.dateRange,
        ...this.datePickerProps
      },
      on: {
        ...this.$listeners,
        everyChange: this.onEveryChange,
        change: this.onChange
      },
      scopedSlots: {
        ...this.$scopedSlots,
        'date-append': this.dailyPriceInfo
          ? ({ date, selected, disabled }: { date: Date; selected: boolean; disabled: boolean }) => {
            const value = this.dailyPriceInfo[dayjs(date).format('YYYY-MM-DD')]
            return this.customRenderPrice ? this.customRenderPrice(value, h, date, selected, disabled) : value
          }
          : undefined,
        // footer: this.$scopedSlots.footer
      }
    })
  }
}
</script>

<style lang="scss">
.hotel-calendar {
  position: relative;
  &:has(.klk-date-picker-footer) {
    .klk-date-picker-panels {
      padding-bottom: 20px;
    }
  }
}
</style>
