import dayjs from 'dayjs'
import { GuestInfo, CalendarInfo, DestinationInfo, HotelQueryHistory } from '../type'
import isEqual from 'lodash/isEqual'

export const poptipDefaultOptions = {
  trigger: 'click',
  placement: 'bottom-start',
  arrow: false,
  zIndex: 99,
  showDelay: 0,
  tabindex: -1,
  'max-width': 1000,
  'max-height': 1000,
  offset: [0, 5]
}

interface DiffDay {
  (...dates: Date[]): number
  (date1: Date, date2: Date, isAbsVale: any): number
}

export const diffDay: DiffDay = (date1: Date, date2: Date, isAbsVale: any = true) => {
  const dates = [date1, date2]
  if (dates.length === 2 && dates.every(d => Object.prototype.toString.call(d).slice(8, -1) === 'Date')) {
    const [d1, d2] = dates.map(d => Date.UTC(d.getFullYear(), d.getMonth(), d.getDate()))
    let res = d1 - d2
    if (isAbsVale) {
      res = Math.abs(res)
    }
    return Math.floor(res / 86400000)
  }
  return 0
}

export function formatGuestDesc(
  this: { $t: Function },
  params: GuestInfo,
) {
  return [
    {
      field: 'adult_num',
      getLocale: (num: number) => num === 1
        ? this.$t('168447')
        : this.$t('168442', { num }),
    },
    {
      field: 'child_num',
      getLocale: (num: number) => num === 1
        ? this.$t('168446')
        : this.$t('168441', { num }),
    },
    {
      field: 'room_num',
      getLocale: (num: number) =>  num === 1
        ? this.$t('168444')
        : this.$t('168440', { num }),
    }
  ].reduce<string[]>((res, { field, getLocale }) => {
    const val = params[field as keyof GuestInfo]

    if (!val) {
      return res
    }

    return res.concat(getLocale(val as number))
  }, []).join(', ') || this.$t('15753')
}

// 获取可选的月份
export const getMonthOption = () => {
  const mothArr = [1, 2]
  const today = dayjs()
  if (today.daysInMonth() - today.get('date') >= 7) {
    mothArr.unshift(0)
  } else {
    mothArr.push(3)
  }
  return mothArr.map(m => today.add(m, 'month').format('YYYY-MM'))
}

// date: 2022-05
export function getMonthText(this: { $t: Function }, date: string){
  return [
    this.$t('47269'),
    this.$t('47270'),
    this.$t('47271'),
    this.$t('47272'),
    this.$t('47273'),
    this.$t('47274'),
    this.$t('47275'),
    this.$t('47276'),
    this.$t('47277'),
    this.$t('47278'),
    this.$t('47279'),
    this.$t('47280'),
  ][+date.slice(5) - 1]
}
export const getMonthTextId = (month: string) => String(47268 + Number(month.slice(5)))

// 日历描述文案
export function calendarValueDesc(
  this: { $t: Function, customFormatDate?: Function },
  travelDate: CalendarInfo,
  hasDiff = true
) {
  const { flexible_date_list, check_in, check_out  } = travelDate
  if (flexible_date_list) {
    const { flexible_date_list, flexible_type, flexible_day } = travelDate
    const no = flexible_day
    const month = flexible_date_list!.split(',').map(month => getMonthText.call(this, month)).join(', ')
    if (flexible_type === 1) {
      return this.$t('47343', { month })
    }
    if ((flexible_day as number) > 1) {
      return this.$t('47349', { month, no })
    }
    return this.$t('47346', { month, no })
  } else if (check_in && check_out) {
    const dayjsDates = [check_in, check_out].map(date => dayjs(date))
    const diff = dayjs(dayjsDates[1]).diff(dayjsDates[0], 'day')
    const daysFormatValue = dayjsDates.map(date => formatDate.call(this, date))
    const daysDesc = this.customFormatDate ? this.customFormatDate(daysFormatValue, dayjsDates) : daysFormatValue.join(' - ')
    return daysDesc + (
      hasDiff
        ? `<span class="travel-date-diff">
            ${ diff > 1 ? this.$t('168434', { num: diff }) : this.$t('101491') }
          </span>`
        : ''
    )
  }
  return this.$t('47308')
}

export function formatDateRange(this: { $t: Function }, dates: [Date, null | Date] | Date[]) {
  let desc = formatDate.call(this, dates[0]) + ' - '

  if (dates[1]) {
    const diff = dayjs(dates[1]).diff(dates[0], 'day')
    if (diff > 28) {
      return this.$t('168458', { num: 28 })
    } else if (diff > 0) {
      return desc + formatDate.call(this, dates[1] as Date)  + ` (${diff > 1 ? this.$t('168434', { num: diff }) : this.$t('101491')})`
    }
  }

  return desc + this.$t('79979')
}

export function formatDate(this: { $t: Function }, date: string | Date | dayjs.Dayjs, hideYear = true) {
  if (!dayjs.isDayjs(date)) {
    date = dayjs(date)
  }
  const template = hideYear
    ? this.$t('168909')
    : this.$t('168908')

  return date.format(template)
}


const moduleIHT: Record<string, string> = {
  location: 'YourLocation',
  search_history: 'SearchHistory_LIST',
  popular: 'PopularDestination_LIST',
  suggestion: 'Hotel_SearchSuggest_LIST',
  'suggest_inner': 'Hotel_Popular_Destination_LIST'
}

export const formatIHTvalue = (value: any) => value || 'NA'

export const getTagSpm = (type: string) => {
  return moduleIHT[type] || ''
}

export const getTagIHTModule = (
  location: DestinationInfo,
  type: string,
  idx = 0,
  len = 1,
  SearchKeyword?: string,
  IsisExclude = false
) => {
  const commonExtra = {
    SearchCardName: formatIHTvalue(location.title),
    ISDestinationPOI: type.includes('inner'),
    IsisExclude,
    DestinationType: formatIHTvalue(location.type_desc),
    TagType: formatIHTvalue(location.tag_info?.id),
    SearchDestinationCityID: formatIHTvalue(location.city_id),
    SearchDestinationType: formatIHTvalue(location.stype),
    SearchDestinationTypeID: formatIHTvalue(location.svalue)
  }
  const otherExtra: any = {}
  if (type.includes('suggest')) {
    otherExtra.SearchKeyword = SearchKeyword
  }
  return typeof location === 'object' && `${moduleIHT[type]}?oid=city_${formatIHTvalue(location.city_id )}&idx=${idx}&len=${len}&ext=${
    encodeURIComponent(JSON.stringify(Object.assign(commonExtra, otherExtra)))}`
}

export function getHighlightFormater(keyword: string) {
  const keySet = new Set<string>()
  keySet.add(keyword)

  if (keyword.length > 1) {
    const keyMap = Array.from(new Set(keyword.split(/[，。,\.<>\\\s\(\)&'"\[\]\{\}\^\|*@\-\+%\$!=;\?:`~#%]+/g))).filter(Boolean)
    keySet.add(keyMap.join('|'))
  }

  const regArr = Array.from(keySet).map(key => {
    try {
      return new RegExp(`(${key})`, 'gi')
    } catch (err) {
      // empty
    }
  }).filter(v => v)

  return (str: string) => {
    for (const reg of regArr) {
      if (str.match(reg)) {
        return str.replace(reg, '<span class="hightlight-keyword">$1</span>')
      }
    }

    return str
  }
}

export const sleep = (delay: number) => new Promise(resolve => setTimeout(resolve, delay))

export class SearchHistory {
  public list: any
  readonly locationKeys = ['title', 'svalue', 'stype', 'override', 'city_id']

  constructor(history?: HotelQueryHistory[]) {
    this.list = history || []
    this.init()
  }

  init() {
    // 更新历史记录
    const TODAY = dayjs()
    let whithoutUpdateHistory = true
    this.list = (
      JSON.parse(window.localStorage.getItem('HOTEL_QUERY_LIST') || '[]') as HotelQueryHistory[]
    ).filter(({
      time: [startDate],
      location: { svalue } // TODO 过滤掉旧数据
    }) => (svalue && !TODAY.isAfter(dayjs(startDate), 'day')) || (whithoutUpdateHistory = false))

    if (!whithoutUpdateHistory) {
      window.localStorage.setItem('HOTEL_QUERY_LIST', JSON.stringify(this.list))
    }
    return this.list
  }

  update(history?: HotelQueryHistory) {
    if (!history) {
      localStorage.removeItem('HOTEL_QUERY_LIST')
      this.list = []
      return []
    }
    // 判断 是不是已有的历史记录, 只需要比对部分数据（locationKeys）完全相同则认为已存在
    this.list.forEach((item: HotelQueryHistory, index: number) => {
      if (isEqual(this.getLocationData(item), this.getLocationData(history))) {
        this.list.splice(index, 1)
      }
    })

    this.list.unshift(JSON.parse(JSON.stringify(history))) > 5 && this.list.pop()
    localStorage.setItem('HOTEL_QUERY_LIST', JSON.stringify(this.list))
    return this.list
  }

  getLocationData(data: HotelQueryHistory) {
    const location: Omit<Pick<DestinationInfo, 'title' | 'stype' | 'svalue' | 'override' | 'city_id'> & { [key: string]: string | number }, 'city_id' | 'svalue'> = {}
    this.locationKeys.forEach((key: string) => {
      if (!location[key]) {
        location[key] = (data.location as any)[key]
      }
    })
    return { location, time: data.time, params: data.params }
  }
}

/**
 * @param originUrl 图片url
 * @param targetUrl 替换url
 * @param width 图片宽度
 * @param height 高度
 * @param webp 是否支持 webp
 */
export function setNewImageSize(originUrl: string, targetUrl: string, width: number, height: number, webp: boolean = true, isFill: boolean = true) {
  let newImgUrl = originUrl || ''

  if (targetUrl && originUrl) {
    // 设置清晰度
    const temp = targetUrl + 'fl_lossy.progressive,q_85/'
    const newHeight = height > 0 ? ',h_' + height + '/' : '/'

    newImgUrl = newImgUrl.replace(targetUrl, temp)
    newImgUrl = newImgUrl.replace(temp, temp + (isFill ? 'c_fill,' : '') + 'w_' + width + newHeight)

    // Apply .webp
    if (webp && newImgUrl.indexOf('res.klook.com') > 0) {
      newImgUrl = newImgUrl.replace(/.(jpg|png|jpeg|gif)$/, '.webp')
    }
  }

  return newImgUrl
}
