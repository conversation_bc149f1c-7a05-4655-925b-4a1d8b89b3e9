
@mixin ellipsis() {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  word-wrap: break-word;
}

.hotel-selector-item {
  color: $color-text-primary;
  display: block;
  flex: 1;
  margin-right: 8px;
  position: relative;
  cursor: pointer;
  margin-right: 10px;
  background-color: $color-bg-page;
  box-sizing: border-box;
  border-radius: $radius-l;
  padding: 0;
  outline: 0;

  &.poptip-destination,
  ::v-deep > .klk-poptip-reference {
    width: 100%;
    padding: 8px 16px;
  }

  .klk-poptip-popper-inner {
    overflow: visible;
  }

  ::v-deep &_name {
    color: $color-text-secondary;
    @include font-caption-m-semibold;
    @include ellipsis;
  }

  ::v-deep &_value {
    @include ellipsis;
    @include font-body-m-bold;

    &.placeholder {
      font-weight: $fontWeight-regular;
      color: $color-text-placeholder;
    }
  }

  .poptip-destination-modal,
  ::v-deep .klk-poptip-popper {
    cursor: default;
      // 不可复制  防止焦点丢失
    user-select: none;
    -ms-user-select: none;
    -moz-user-select: none;
    -webkit-user-select: none;
  }

  &:hover,
  &:focus,
  &:focus-within {
    background-color: $color-accent-13 !important;
  }
}
