@mixin ellipsis() {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  word-wrap: break-word;
}

.hotel-selector-item {
  background: $color-bg-page;
  border-radius: $radius-l;
  padding: 8px 12px;
  margin-bottom: 8px;
  display: block;
  > .i-icon {
    font-size: 16px;
  }
  ::v-deep .selector_item_label {
    color: $color-text-secondary;
    margin-bottom: 2px;

    @include font-caption-m-regular;
    @include ellipsis;
  }

  .selector_item_value {
    @include font-body-m-semibold;
    @include ellipsis;

    &.placeholder {
      font-weight: $fontWeight-regular;
      color: $color-text-placeholder;
    }
  }
}
