.hotel-selector-item ::v-deep .travel-date-diff {
  font-size: $fontSize-caption-m;
  font-weight: $fontWeight-bold;

  padding: 3px 8px;
  border-radius: $radius-l;
  margin-left: 8px;
  border: 1px solid $color-text-primary;
  display: inline-block;
  line-height: 1;
  vertical-align: middle;
  margin-top: -1px;
}

.klk-date-picker {
  padding-bottom: 0;
}

.calendar-type-tab {
  display: inline-flex;
  text-align: center;
  margin-left: 50%;
  transform: translateX(-50%);
  background-color: $color-bg-page;
  border-radius: 24px;
  height: 44px;
  padding: 4px;
  @include font-body-m-regular;

  .calendar-type-btn {
    height: 100%;
    border-radius: $radius-xl;
    padding: 8px 16px;
    width: auto;
    white-space: nowrap;
    cursor: pointer;

    &:last-child {
      margin-left: 8px;
    }
    &:hover {
      color: $color-brand-primary;
    }
    &.active {
      background-color: $color-bg-widget-normal;
      font-weight: $fontWeight-bold;
      color: $color-brand-primary;
    }
  }
}

.c-gray {
  font-size: $fontSize-body-s;
}

.flex-tag-list {
  margin-top: 20px;
  width: 100%;
  display: flex;
  text-align: center;

  .ellipsis {
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    word-wrap: break-word;
  }

  .flex-tag-item {
    border: 1px solid $color-border-normal;
    border-radius: $radius-l;
    padding: 12px;
    cursor: pointer;

    @each $i in 2, 3, 4, 7 {
      &.row-column#{$i} {
        width: calc((100% - (#{$i} - 1) * 8px ) / #{$i});
      }
    }

    &:not(:last-child) {
      margin-right: 8px;
    }

    &.active {
      border: 2px solid $color-border-active;
      font-weight: $fontWeight-bold;
    }
  }
}

.flexible-container {
  overflow: hidden;
  line-height: $lineHeight-relaxed;

  .flexible-tip {
    margin-top: 32px;
  }

  .c-gray {
    color: $color-text-secondary;
    font-weight: 400;
  }

  .c-primary {
    color: $color-brand-primary;
  }

  .gray-tip {
    font-size: 12px;
    margin-top: 2px;
  }
}
