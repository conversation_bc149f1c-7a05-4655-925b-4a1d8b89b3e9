.text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

@mixin text-ellipsis($line-clamp: 2) {
  display: -webkit-box;
  -webkit-line-clamp: $line-clamp;
  -webkit-box-orient: vertical;
  text-overflow: ellipsis;
  overflow: hidden;
}

.destination-wrapper {
  margin-bottom: 20px;
  .destination-title {
    @include  font-body-s-bold;
    .history-clear {
      cursor: pointer;
      float: right;
      margin-left: 12px;
    }
  }

  .destination-group {
    margin-left: -10px;
    font-size: 0;
    transition: max-height .2s linear;
    overflow: hidden;

    &-item {
      display: inline-block;
      cursor: pointer;
      margin-left: 10px;
      margin-top: 12px;

      &.destination-location {
        @include font-body-s-regular;
        padding: 8px 10px;
        max-width: 100%;
        background: $color-bg-page;
        border-radius: 20px;
        display: inline-flex;
        align-items: center;

        > img {
          width: 16px;
          height: 16px;
          margin-right: 8px;
        }

        &:hover {
          border-color: $color-border-active;
        }
      }

      &.search-history {
        border-radius: $radius-s;
        background-color: $color-bg-3;
        width: calc(50% - 10px);
        border-radius: $radius-m;
        padding: 12px;

        .history-name {
          @include font-body-s-bold;
        }

        .history-params {
          @include font-caption-m-regular;
          margin-top: 4px;
        }

        &.single {
          width: calc(100% - 10px);
          .history-params {
            display: inline-block;
            max-width: 100%;
            &:nth-of-type(2) {
              margin-right: 8px;
            }
          }
        }
      }
    }
  }
}

.flex-container {
  display: flex;
  align-items: center;

  .flex-none {
    flex: none;
  }

  .flex-grow {
    flex: 1;
    min-width: 0;
  }
}

.search-suggest {
  margin-top: 20px;
  cursor: pointer;
  > img {
    margin-right: 12px;
    margin-top: 2px;
    align-self: flex-start;
  }

  .c-gray {
    color: $color-text-secondary;
  }

  ::v-deep .hightlight-keyword {
    color: $color-brand-primary;
  }

  &-title {
    @include font-body-m-regular;
  }

  &-desc {
    @include font-caption-m-regular;
  }

  &-desc-second {
    margin-top: 4px;
    @include font-body-s-regular-v2;
  }

  p.search-suggest-desc {
    margin-top: 4px;
  }

  span.search-suggest-desc {
    margin-left: 12px;
    max-width: 100px;
  }

  div.search-suggest-desc {
    @include font-caption-m-regular;
    border-radius: $radius-m;
    padding: 8px;
    margin-top: 12px;
    background-color: $color-bg-3;

    &:nth-of-type(2) {
      margin-top: 8px;
    }
  }
}

.layer-destination-container {
  .destination-group.popular {
    padding: 12px 0 56px 10px;
    display: flex;
    flex-wrap: wrap;
    .destination-group-item {
      width: calc(50% - 5px);
      height: calc((100vw - 50px) / 3.54);
      background-repeat: no-repeat;
      background-size: cover;
      background-position: center;
      background-color: unset;
      border-radius: $radius-m;
      padding: 8px;
      display: flex;
      justify-content: center;
      align-items: center;
      margin: 0 8px 8px 0;
      @include font-body-s-semibold;
      color: $color-text-reverse;
      > span {
        @include text-ellipsis(2);
      }

      &:nth-child(2n) {
        margin-right: 0;
      }
    }
  }
}

.poptip-destination-modal {
  .destination-group.popular .destination-group-item {
    background-image: unset !important;
  }
}
