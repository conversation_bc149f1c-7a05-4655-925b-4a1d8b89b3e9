<script lang="tsx">
import { Component, Prop } from 'vue-property-decorator'
import CalendarMixin from '../mixins/calendar'
import {calendarValueDesc, formatDateRange} from '../utils'
import dayjs from 'dayjs'
import { IconCalendar } from '@klook/klook-icons'

let showTipTimer: any = null
@Component({
  name: 'layer-calendar',
  components: {
    IconCalendar
  }
})
export default class LayerCalendar extends CalendarMixin {
  @Prop({ type: Boolean, default: true }) hasConfirm!: boolean

  isMobile = true
  showCalendar = false
  showSelectTip = false

  onClose() {
    this.showCalendar = false
    this.singleCheckIn = null
    this.$emit('hide')
    // 如果用户操作了日历组件 直接点击了关闭  那么执行还原操作
    if (this.confirmIsChange()) {
      this.isChange = false
      this.initDateOption()
    }
  }

  onConfirm() {
    this.commitTravelDate()
    this.showCalendar = false
    this.$emit('confirm', this.getPostData())
  }

  get valueDesc() {
    return calendarValueDesc.call(this, this.options)
  }

  get galileoSpm() {
    const spm = (this.ihtAttrs as any)?.['data-spm-module']?.split('?')[0]
    return {
      spm,
      enable: !!spm
    }
  }

  get selectedDateTip() {
    if (this.singleCheckIn) {
      return this.$t('79979')
    } else if(this.isValid) {
      return formatDateRange.call(this, [this.calendarOptions.check_in, this.calendarOptions.check_out])
    }
    return this.$t('168459')
  }

  isShowSelectedTip(date: Date) {
    if (this.singleCheckIn && this.singleCheckIn === date) {
      return true
    }
    if (
      this.isChange &&
      this.calendarOptions.check_out &&
      new Date(this.calendarOptions.check_out).setHours(0) === date.valueOf()
    ) {
      return true
    }
    return false
  }

  showSelectedTip() {
    clearTimeout(showTipTimer)
    this.showSelectTip = true
    showTipTimer = setTimeout(() => {
      this.showSelectTip = false
    },3000)
    this.$nextTick(() => {
      const tipEl = document.querySelector('.hotel-calendar-mobile .hotel-calendar-date-selected-tip') as any
      if (tipEl && !tipEl?.style.transform) {
        tipEl.__vue__.updatePopper()
      }
    })
  }

  show() {
    this.showCalendar = true
    this.$emit('show', this.calendarOptions)
    this.getDailyPriceInfo()
  }

  render() {
    const hasDefaultSlot = typeof this.$scopedSlots.default === 'function'

    return <div
      class={ ['layer-calendar', !hasDefaultSlot && 'hotel-selector-item'] }
      {...{ directives: [{ name: 'galileo-click-tracker', value: this.galileoSpm }] }}
      attrs={ this.ihtAttrs }
      onClick={ this.show }
    >
      {
        hasDefaultSlot
          ? this.$scopedSlots.default!({ valueDesc: this.valueDesc })
          : [
            <IconCalendar />,
            <p class="selector_item_value" domPropsInnerHTML={ this.valueDesc } />
          ]
      }
      <client-only>
        <klk-bottom-sheet
          transfer
          height="90%"
          visible={this.showCalendar}
          onClose={this.onClose}
          delayTime={0}
        >
          <div slot="header">{ this.renderTab() }</div>
          { this.loading
            ? <klk-loading />
            : (
              this.showCalendar &&
                [
                  this.renderCalendar({
                    scopedSlots: {
                      day: ({date, selected}) => {
                        if (this.showSelectTip) {
                          if (selected && this.isShowSelectedTip(date)) {
                            const dateDay = dayjs(date)
                            const vNodes = [
                              h('span', { class: 'klk-date-picker-date-main' }, [
                                h('span', {class: 'klk-date-picker-date-inner'}, [
                                  h('klk-poptip', {
                                    class: 'hotel-calendar-date-selected-tip',
                                    props: {
                                      dark: true,
                                      placement: 'top',
                                      value: true,
                                      trigger: 'none',
                                      content: this.selectedDateTip,
                                      offset: [0, 12],
                                      flip: true,
                                      preventOverflow: true,
                                      arrow: false
                                    },
                                  }, dateDay.format('D'))
                                ])
                              ])
                            ]
                            // 判断有无append 一般是价格，如果有其他情况，也需要兼顾处理
                            if (this.finalDailyPriceInfo && this.finalDailyPriceInfo[dateDay.format('YYYY-MM-DD')]) {
                              vNodes.push(h('span', { class: 'klk-date-picker-date-append' }, this.finalDailyPriceInfo[dateDay.format('YYYY-MM-DD')]))
                            }
                            return vNodes
                          }
                        }
                        return undefined
                      },
                      footer: this.$scopedSlots.tip ? () => this.$scopedSlots.tip?.() : undefined
                    },
                    on: {}
                  }),
                  this.renderFlexible()
                ]
            )
          }
          { !this.loading &&
            <div slot="footer" {...{ directives: [{ name: 'galileo-click-tracker', value: 'SearchDate' }] }} class="calendar-footer" data-spm-module="SearchDate?trg=manual">
              {
                this.clearable && <klk-button
                  type="text"
                  disabled={this.clearableBtnDisabled}
                  onClick={this.clearDates}
                >
                  {this.$t('47258')}
                </klk-button>
              }
              <klk-button
                type="primary"
                block={!this.clearable}
                nativeOnClick={this.onConfirm}
                disabled={this.confirmBtnDisabled}
                style="min-width: 240px"
              >
                {this.$t('28850')}
                {this.tabName !== 'flexibleOptions' && this.diffDayText}
              </klk-button>
            </div>
          }
        </klk-bottom-sheet>
      </client-only>
    </div>
  }

  mounted() {
    let unWatch: any = this.$watch('showCalendar', (value: boolean) => {
      value && this.$nextTick(() => {
        const datePicker = (this.$refs.simpleCalendar as Vue)?.$refs?.datePicker as any
        // 第一次弹出滚动到选中日期
        datePicker && setTimeout(() => {
          datePicker.scrollToCurrentMonth()
        }, 200)
        unWatch()
        unWatch = null
      })
    })
  }
}

</script>
<style lang="scss" scoped>
@import "../styles/layer-common.scss";
@import '../styles/calendar.scss';

.calendar-footer {
  display: flex;
  justify-content: space-between;
}

.layer-calendar {
  .i-icon-icon-calendar {
    margin-right: 8px;
  }
  .selector_item_value {
    flex: 1;
    display: flex;
    justify-content: space-between;
    color: $color-text-primary;
    ::v-deep .travel-date-diff {
      flex: 0 0 auto;
      margin-left: 8px;
      padding: 2px 8px;
      background: $color-bg-3;
      border: none;
      color: $color-text-secondary;
      @include font-caption-m-semibold;
      margin-top: 0;
    }
  }
}

.hotel-calendar-date-selected-tip {
  ::v-deep .klk-poptip-popper {
    border-radius: $radius-s;
    user-select: none;
    pointer-events: none;
    .klk-poptip-popper-content {
      @include font-body-s-regular;
    }
  }
}

.flexible-container {
  font-size: $fontSize-body-s;

  .flexible-dayly-list {
    flex-wrap: wrap;
    .row-column4 {
      margin-bottom: 8px;
      &:nth-child(4) {
        margin-right: 0;
      }
    }
  }

  .row-column2 {
    display: flex;

    > span {
      margin: auto;
    }

    &:first-child {
      width: 42%;
    }
    &:last-child {
      flex: 1;
      width: auto !important;
    }
  }
}

::v-deep .klk-bottom-sheet-body {
  padding-top: 0 !important;
  padding-bottom: 0 !important;
  position: relative;
}

::v-deep .klk-date-picker-weeks {
  border-bottom: 1px solid $color-border-dim;
}


</style>
