<script lang="tsx">
import { Component, Prop } from 'vue-property-decorator'
import HotelSelectorMixin from '../mixins'
import Destination from './layer-destination.vue'
import Calendar from './layer-calendar.vue'
import Guest from './layer-guest.vue'
import { calendarValueDesc } from '../utils'

@Component({
  name: 'selector-mobile',
  components: { Calendar, Guest, Destination }
})
export default class HotelSelectorMobile extends HotelSelectorMixin {
  @Prop({ type: Number, default: 0 }) fixedTop!: number

  drawerVisible = false

  searchCancel() {
    this.drawerVisible = false
    // 当点击了搜索按钮 isChange 为 true 其他情况都是取消操作
    if (this.isChange) {
      return
    }
    // 执行初始化
    this.initParams()
    this.$emit('cancel')
  }

  renderCalendarDesc() {
    const valueDesc: string = calendarValueDesc.call(this, this.paramsInfo, false)

    return valueDesc.includes(' - ')
      ? valueDesc.split(' - ').map(desc => <div class="date-item">{ desc }</div>)
      : valueDesc
  }

  render() {
    let conentVnode = this.conentVnode
    let destination!: typeof this.conentVnode[0]

    if (this.fixedTop) {
      // @ts-ignore
      destination = this.conentVnode[0].componentOptions.children[0]
      conentVnode = this.conentVnode.slice(1)
      // @ts-ignore
      destination.data.scopedSlots.default = ({ valueDesc }: { valueDesc: string }) => <div class="entrance-desc destination-entrance">
        <klk-icon type="icon_edit_search_s" size="16" color="#8A8A8A"></klk-icon>
        { valueDesc }
      </div>
    }

    const content = <div class="hotel-selector hotel-selector-mobile">
      { conentVnode }
    </div>


    return !this.fixedTop
      ? content
      : <div class="hotel-drawer-selector-mobile">
        <div class="drawer-selector-container">
          <div class="drawer-selector-wraper">
            <div
              class="entrance-desc selector-entrance"
              onClick={ () => this.drawerVisible = !this.drawerVisible }
            >
              { this.renderCalendarDesc() }
            </div>
            <span class="split-line">|</span>
            { destination }
          </div>
        </div>
        <client-only>
          <klk-drawer
            class="selector-drawer"
            style={`z-index: 15; top: ${ this.fixedTop }px`}
            visible={ this.drawerVisible }
            direction="top"
            on={{ close: this.searchCancel }}
          >
          { content }
          </klk-drawer>
        </client-only>
      </div>
  }
}
</script>

<style lang="scss" scoped>

.hotel-drawer-selector-mobile {
  ::v-deep .selector-drawer .klk-drawer-content {
    border-bottom-left-radius: 20px;
    border-bottom-right-radius: 20px;
  }
  .drawer-selector-container {
    position: relative;
    z-index: 20;
    background-color: $color-bg-widget-normal;
    padding: 12px 20px;

    .drawer-selector-wraper {
      display: flex;
      align-items: center;
      background-color: $color-bg-3;
      border-radius: $radius-xxl;
      padding: 0 20px;

      .split-line {
        margin: 0 16px;
        flex: 0 0 4px;

        @include font-body-s-regular;
        color: $color-text-disabled;
      }

      .layer-destination {
        flex: 1;
        min-width: 0;
        .entrance-desc {
          width: 100%;
        }
      }
      .entrance-desc {
        @include font-body-s-semibold;

        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
        word-wrap: break-word;

        &.selector-entrance {
          flex: 0 0 auto;
          max-width: 50%;
          .date-item {
            font-size: 12px;
            line-height: 16px;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
            word-wrap: break-word;
          }
        }

        &.destination-entrance {
          padding: 9.5px 0;
          text-align: center;
          ::v-deep .klk-icon {
            vertical-align: middle;
            margin: -2px 4px 0 0;
          }
        }
      }
    }
  }
}

.hotel-selector-mobile {
  box-shadow: 0 1px 6px 0 rgba(0, 0, 0, .1);
  background-color: #fff;
  padding: 0 16px 16px;
  border-radius: $radius-xl;

  .hotel-selector-item {
    background: $color-bg-1;
    padding: 20px 0;
    border-bottom: 1px solid $color-border-dim;
    margin-bottom: 0;
    border-radius: unset;
    display: flex;
    align-items: center;
  }

  .poptip-destination {
    display: block;
    ::v-deep .klk-poptip-popper-inner {
      margin: 16px 0;
      padding: 0 16px;
    }
  }

  .hotel-selector-button {
    width: 100%;
    border-radius: $radius-l;
    margin-top: 16px;
    ::v-deep .i-icon {
      margin-right: 8px;
    }
  }
}
</style>
