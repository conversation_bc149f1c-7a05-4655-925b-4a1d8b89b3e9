<template>
  <div
    v-galileo-click-tracker="galileoSpm"
    class="layer-destination"
    :class="{'hotel-selector-item': !$scopedSlots.default, 'no-label': !label }"
    v-bind="ihtAttrs"
    @click="show"
  >
    <slot :value-desc="destinationValue">
      <IconSearch />
      <p class="selector_item_value" :class="{ placeholder: !destinationValue }">
        {{ destinationValue || inputPlaceholder }}
      </p>
    </slot>
    <client-only>
      <component
        :is="bottomSheet ? 'klk-bottom-sheet' : 'page-layer'"
        ref="pageLayer"
        :visible.sync="showLayer"
        :title="$t('168448')"
        :delay-time="0"
        height="90%"
        transfer
        header-bottom=""
        :data-spm-page="spmStr"
        @close="hide"
      >
        <slot slot="header-right" :chooseLocation="chooseLocation" name="header-right" />
        <div class="destination_container" :class="{'layer-destination-container': !bottomSheet}">
          <div v-if="suggest" class="destination_search">
            <input
              ref="searchInput"
              :placeholder="inputPlaceholder"
              class="destination_search_input"
              autocomplete="off"
              v-model="inputValue"
              @input="keyword = $event.target.value"
              type="text"
            />
            <svg
              v-show="inputValue"
              class="destination_search_clear"
              xmlns="http://www.w3.org/2000/svg"
              :size="20"
              viewBox="0 0 24 24"
              @click="clearKeywords"
            >
              <path fill="#D1D1D1" d="M19 12c0 3.866-3.134 7-7 7s-7-3.134-7-7 3.134-7 7-7 7 3.134 7 7zm-8.97-3.03c-.293-.293-.767-.293-1.06 0-.293.292-.293.768 0 1.06L10.94 12l-1.97 1.97c-.293.292-.293.768 0 1.06.293.293.767.293 1.06 0L12 13.06l1.97 1.97c.293.293.767.293 1.06 0 .293-.292.293-.768 0-1.06L13.06 12l1.97-1.97c.293-.292.293-.768 0-1.06-.293-.293-.767-.293-1.06 0L12 10.94l-1.97-1.97z" />
            </svg>
          </div>
          <div ref="destinationRecommend" v-show="!keyword">
            <slot name="content" v-bind="{ chooseLocation, destinationList }">
              <recommend v-if="destinationList.length" />
            </slot>
          </div>
          <template v-if="keyword">
            <suggest v-if="searchList && searchList.length" />
            <div v-else class="suggest-empty">
              <klk-loading v-if="timmer" :showOverlay="false" />
              <template v-else-if="searchError">
                <img src="https://res.klook.com/image/upload/Group_3880_b3obts.png" width="170" alt="error image">
                <p>{{ $t('168460') }}</p>
                <klk-button @click="searchSuggest(keyword)">{{ $t('16582') }}</klk-button>
              </template>
              <template v-else>
                <img src="https://res.klook.com/image/upload/v1657158870/pcdpb2gqttqatpagckq5.png" width="130" alt="empty image">
                <p>{{ $t('168462', { search_term: keyword }) }}</p>
              </template>
            </div>
          </template>
        </div>
      </component>
    </client-only>
  </div>
</template>

<script lang="ts">
import { Component, Prop, Watch } from 'vue-property-decorator'
import DestinationMixin from '../mixins/destination'
import { IconSearch, IconPosition } from '@klook/klook-icons'

@Component({
  name: 'layer-destination',
  components: { 
    PageLayer: () => import('../components/page-layer.vue'),
    IconSearch,
    IconPosition
  }
})
export default class LayerDestination extends DestinationMixin {
  @Prop({ type: Boolean, default: false }) bottomSheet!: boolean

  // 用来同步输入框内容，判断clear 按钮的显示隐藏
  inputValue = ''

  get galileoSpm() {
    const spm = (this.ihtAttrs as any)?.['data-spm-module']?.split('?')[0]
    return {
      spm,
      enable: !!spm
    }
  }

  hide() {
    this.showLayer = false
    this.$emit('hide')
  }

  clearKeywords() {
    this.keyword = this.inputValue = ''
    this.searchInput!.focus()
  }

  @Watch('options', { immediate: true })
  optionsChange({ stype }: this['options']) {
    this.showLayer && this.hide()
    this.inputValue = stype === 'location'
      ? ''
      : this.destinationValue
    this.keyword = ''
  }

  show() {
    this.showLayer = true
    this.$emit('show')
    this.$inhouse?.track('action', this.$el)

    setTimeout(() => {
      !this.bottomSheet && window.scrollTo(0, 0)
      this.spmStr && this.$inhouse?.track('pageview', (this.$refs.pageLayer as Vue).$el as HTMLElement, {
        force: true
      })
      this.searchInput?.focus()
    }, 350)
  }
}
</script>

<style lang="scss" scoped>
@import "../styles/layer-common.scss";
@import '../styles/destination.scss';

.layer-destination.no-label {
  padding: 12px;
}

.layer-destination {
  .i-icon {
    flex: 0 0 auto;
    font-size: 16px;
    &.i-icon-icon-search {
      color: $color-text-primary;
      margin-right: 8px;
    }
    &.i-icon-icon-position {
      margin-left: 20px;
      color: $color-text-link;
    }
  }
  .selector_item_value {
    flex: 1;
  }
}

.destination_container {
  &.layer-destination-container {
    padding: 16px 20px;
  }

  .destination_search {
    position: relative;
    line-height: 24px;
    margin-bottom: 20px;

    &_input {
      @include font-body-s-regular;

      width: 100%;
      height: 36px;
      border: 1px solid $color-brand-primary;
      border-radius: $radius-pill;
      color: $color-text-primary;
      padding: 0 12px;
      outline: none;
      caret-color: $color-brand-primary;
      appearance: none;

      &:hover,
      &:active,
      &:visited {
        border: 1px solid $color-brand-primary;
        outline: none;
      }
    }

    &_clear {
      position: absolute;
      right: 10px;
      top: 6px;
      width: 24px;
      height: 24px;
    }
  }
  .suggest-empty {
    position: relative;
    text-align: center;
    padding-top: 100px;
    min-height: 300px;
    > p {
      @include font-body-m-regular;
      margin: 24px 0;
      color: $color-text-secondary;
    }
  }

  ::v-deep .search-suggest {
    .search-suggest-desc-second {
      margin-top: 2px;
    }

    p.search-suggest-desc {
      margin-top: 2px;
    }
  }
}

</style>
