<template>
  <div
    v-galileo-click-tracker="galileoSpm"
    :class="{'hotel-selector-item': !$scopedSlots.default}"
    class="layer-guest"
    v-bind="ihtAttrs"
    @click="show"
  >
    <slot :value-desc="valueDesc">
      <IconUser />
      <p class="selector_item_value">{{ valueDesc }}</p>
    </slot>
    <client-only>
      <klk-bottom-sheet
        :title="guestLabel"
        header-divider
        transfer
        height="70%"
        :visible="showLayer"
        @close="cancel"
        :delay-time="0"
      >
        <guest
          ref="guest"
          :sync-change="false"
          platform="mobile"
          :options="options"
          v-bind="$attrs"
          @change="$emit('change', $event)"
          @collapse-change="scroll2view"
        />
        <klk-button
          v-galileo-click-tracker="{spm: 'SearchGuestAndRoomConfirm'}"
          slot="footer"
          block
          class="conmirm-button"
          data-spm-virtual-item="__virtual"
          data-spm-module="SearchGuestAndRoomConfirm?trg=manual"
          @click="confirm"
        >
          {{ $t('11976') }}
        </klk-button>
      </klk-bottom-sheet>
    </client-only>
  </div>
</template>

<script lang="ts">
import { Component } from 'vue-property-decorator'
import GuestMixin from '../mixins/guest'
import { IconUser } from '@klook/klook-icons'

@Component({
  inheritAttrs: false,
  name: 'layer-guest',
  components: {
    IconUser
  }
})
export default class LayerGuest extends GuestMixin {
  showLayer = false

  get galileoSpm() {
    const spm = (this.ihtAttrs as any)?.['data-spm-module']?.split('?')[0]
    return {
      spm,
      enable: !!spm
    }
  }

  show() {
    this.showLayer = true
    this.$emit('show')
  }

  scroll2view(top: number) {
    const scrollEl = (this.$refs.guest as Vue).$el.parentElement as HTMLElement
    scrollEl.scroll({ top, behavior: 'smooth' })
  }

  cancel() {
    // 还原原来选择的结果
    this.showLayer = false
    this.$emit('hide')
    const guest = this.$refs.guest as any
    this.$emit('hide')
    if (guest && guest.isChange) {
      guest.isChange = false
      guest.initGuestConfig()
    }
  }

  confirm() {
    this.showLayer = false
    this.$emit('hide')
    const Guest = this.$refs.guest as any
    Guest.commitGuestInfo()
  }
}
</script>

<style lang="scss" scoped>
@import "../styles/layer-common.scss";
.layer-guest {
  .i-icon-icon-user {
    margin-right: 8px;
  }
  .selector_item_value {
    color: $color-text-primary;
  }
}
</style>
