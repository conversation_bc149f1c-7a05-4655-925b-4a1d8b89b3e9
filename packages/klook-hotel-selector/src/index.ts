import { VueConstructor } from 'vue'

import SelectorMobile from './mobile/selector-mobile.vue'
import LayerGuest from './mobile/layer-guest.vue'
import LayerDestination from './mobile/layer-destination.vue'
import LayerCalendar from './mobile/layer-calendar.vue'

import SelectorDesktop from './desktop/selector-desktop.vue'
import PoptipGuest from './desktop/poptip-guest.vue'
import PoptipDestination from './desktop/poptip-destination.vue'
import PoptipCalendar from './desktop/poptip-calendar.vue'

import Calendar from './components/calendar.vue'
import Guest from './components/guest.vue'
import PageLayer from './components/page-layer.vue'
import { SearchHistory } from './utils'

// @ts-ignore
const comonComponents = [Calendar, Guest, PageLayer] as Array<Vue & { options: { name: string } }>

const installComponents = (Vue: VueConstructor, ...components: typeof comonComponents) => {
  comonComponents.concat(components).forEach(component => Vue.component(component.options.name, component))
}

export default {
  // 安装mobile + desktop端所需的组件
  install(Vue: VueConstructor) {
    this.mobile(Vue)
    this.desktop(Vue)
  },
  // 安装mobile 端所需的组件
  mobile(Vue: VueConstructor) {
    // @ts-ignore
    installComponents(Vue, SelectorMobile, LayerGuest, LayerDestination, LayerCalendar)
  },
  // 安装desktop 端所需的组件
  desktop(Vue: VueConstructor) {
    // @ts-ignore
    installComponents(Vue, SelectorDesktop, PoptipGuest, PoptipDestination, PoptipCalendar)
  },
  // 仅仅安装 Calendar, Guest 组件
  common(Vue: VueConstructor) {
    // @ts-ignore
    installComponents(Vue)
  }
}

export {
  Calendar,
  Guest,
  SelectorMobile,
  LayerGuest,
  LayerDestination,
  LayerCalendar,
  SelectorDesktop,
  PoptipGuest,
  PoptipDestination,
  PoptipCalendar,
  SearchHistory
}
