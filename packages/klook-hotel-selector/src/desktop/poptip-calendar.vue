<script lang="tsx">
import { Component, Prop } from 'vue-property-decorator'
import CalendarMixin from '../mixins/calendar'
import {poptipDefaultOptions, calendarValueDesc, formatDateRange} from '../utils'

@Component({
  name: 'poptip-calendar'
})
export default class PoptipCalendar extends CalendarMixin {
  @Prop({ type: Object, default: () => ({}) }) poptipProps!: typeof poptipDefaultOptions

  showCalendar = false
  hoveringDate: Date | null = null

  get mergedPoptipProps() {
    return Object.assign({}, poptipDefaultOptions, this.poptipProps, this.ihtAttrs)
  }

  get valueDesc() {
    const options = this.hasConfirm
      ? this.options
      : this.monthOption.length ? this[this.tabName] : this.options
    // 服务端渲染的文案 应是根据options计算得出的结果, 而monthOption 只有在 mounted 后才会有值
    return calendarValueDesc.call(this, options)
  }

  get selectedDateTip() {
    if (this.singleCheckIn) {
      return formatDateRange.call(this, [this.singleCheckIn, this.hoveringDate])
    } else if(this.isValid) {
      return formatDateRange.call(this, [this.calendarOptions.check_in, this.calendarOptions.check_out])
    }
    return this.$t('168459')
  }

  show() {
    (this.$el.firstChild  as HTMLElement).click()
  }

  onConfirm() {
    this.commitTravelDate()
    this.$refs.poptipCalendar?.hide?.()
    this.$emit('confirm', this.getPostData())
  }

  onPoptipShow() {
    this.showCalendar = true
    this.$emit('show', this.calendarOptions)
    this.getDailyPriceInfo()
  }

  onPoptipHide() {
    this.singleCheckIn = null
    setTimeout(() => {
      this.showCalendar = false
    }, 200)
    if (!this.hasConfirm) {
      this.commitTravelDate()
    }
    this.$emit('hide')
  }

  renderSelectedDateTip() {
    return <div class="hotel-calendar-desktop-extra-tip">
      {this.selectedDateTip}
    </div>
  }

  get galileoSpm() {
    const spm = (this.mergedPoptipProps as any)?.['data-spm-module']?.split('?')[0]
    return {
      spm,
      enable: !!spm,
      enforce: 'post'
    }
  }

  render() {
    const hasDefaultSlot = typeof this.$scopedSlots.default === 'function'
    return <klk-poptip
      ref="poptipCalendar"
      {...{ directives: [{ name: 'galileo-click-tracker', value: this.galileoSpm }] }}
      attrs={ { ...this.mergedPoptipProps } }
      width={ 680 }
      class={['poptip-calendar', !hasDefaultSlot && 'hotel-selector-item']}
      onShow={ this.onPoptipShow }
      onHide={ this.onPoptipHide }
    >
      {
        hasDefaultSlot
          ? this.$scopedSlots.default!({ valueDesc: this.valueDesc })
          : [
            <p class="hotel-selector-item_name">{ this.$t('168433') }</p>,
            <div class="hotel-selector-item_value" domPropsInnerHTML={ this.valueDesc } />
          ]
      }
        <client-only slot="content">
          {
            this.loading
              ? <div style="width: 680px; height: 440px; position: relative"><klk-loading /></div>
              : this.showCalendar &&
                [
                  this.renderTab(),
                  this.renderCalendar({
                    scopedSlots: {
                      footer: this.$scopedSlots.tip ? () => this.$scopedSlots.tip?.() : undefined
                    },
                    on: {
                      hover: (date: Date | null) => {
                        this.hoveringDate = date
                      }
                    }
                  }),
                  this.renderFlexible()
                ]
          }
          { !this.loading && !this.hasConfirm && !this.isFlexibleTab && this.renderSelectedDateTip() }
          {
            !this.loading && (this.hasConfirm || this.clearable) &&
            <div class="calendar-footer">
              {
                this.hasConfirm &&
                <klk-button
                  type="primary"
                  nativeOnClick={this.onConfirm}
                  disabled={this.confirmBtnDisabled}
                  style="min-width: 240px"
                >
                  {this.$t('28850')}
                  {!this.isFlexibleTab && this.diffDayText}
                </klk-button>
              }
              { this.hasConfirm && !this.isFlexibleTab && <span class="extra-tip">{this.selectedDateTip}</span> }
              {
                this.clearable &&
                <klk-button
                  type="text"
                  disabled={this.clearableBtnDisabled}
                  onClick={this.clearDates}
                >
                  {this.$t('47258')}
                </klk-button>
              }
            </div>
          }
        </client-only>
    </klk-poptip>
  }
}

</script>

<style lang="scss" scoped>
@import '../styles/poptip-common.scss';
@import '../styles/calendar.scss';

.hotel-calendar-desktop-extra-tip {
  @include font-body-m-bold;
  padding: 16px 20px 0;
  border-top: 1px solid $color-border-normal;
  text-align: center;
  margin: 12px -16px;
}

.flexible-container {
  @include font-body-m-regular;
  font-size: $fontSize-body-m;
  margin-bottom: 24px;
}

.calendar-footer {
  border-top: 1px solid $color-border-normal;
  padding: 16px 16px 0;
  margin: 0 -16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-direction: row-reverse;
  .extra-tip {
    font-size: $fontSize-body-m;
    line-height: $lineHeight-compact;
    font-weight: $fontWeight-semibold;
  }
}

</style>
