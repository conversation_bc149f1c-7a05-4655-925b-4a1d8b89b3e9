<template>
  <div
    v-galileo-click-tracker="galileoSpm"
    :class="{ 'no-label': !label }"
    tabindex="-1"
    class="poptip-destination hotel-selector-item"
    v-bind="ihtAttrs"
    @mouseup="focusIn"
  >
    <slot :value-desc="destinationValue">
      <p v-if="label" class="hotel-selector-item_name">{{ inputLabel }}</p>
      <klk-icon v-else :size="20" class="destination-icon" type="icon_travel_location_fill" />
      <input
        v-if="suggest"
        ref="searchInput"
        type="search"
        class="hotel-selector-item_value poptip-destination-input"
        autocomplete="off"
        :placeholder="inputPlaceholder"
        @input="keyword = $event.target.value"
      />
      <p v-else class="hotel-selector-item_value" :class="{ placeholder: !destinationValue }">
        {{ destinationValue || inputPlaceholder }}
      </p>
    </slot>
    <destination-content />
  </div>
</template>

<script lang="ts">
import { Component, Ref } from 'vue-property-decorator'
import { RenderContext, CreateElement, VNode } from 'vue'
import DestinationMixin from '../mixins/destination'

@Component({
  name: 'poptip-destination',
  components: {
    DestinationContent: {
      functional: true,
      render(
        h: CreateElement,
        { parent: ctx }: { parent: PoptipDestination } & RenderContext
      ) {
        const contentVnode = []
        const { keyword } = ctx

        const recommend = (ctx.$scopedSlots.content?.({
          destinationList: ctx.destinationList,
          chooseLocation: ctx.chooseLocation.bind(ctx)
        }) || ctx.destinationList.length > 0 && h('recommend')) as VNode[]


        recommend?.length && contentVnode.push(
          h('div', { ref: 'destinationRecommend', style: keyword && 'display: none;' }, recommend)
        )

        keyword && contentVnode.push(
          ctx.searchList?.length > 0
            ? h('suggest')
            : h('p',  ctx.timmer
                ? ctx.$t('80356')
                : ctx.searchError
                  ? [
                      ctx.$t('168460'),
                      h('klk-link', {
                        style: 'margin-left: 8px',
                        props: { underline: false },
                        on: { click: () => ctx.searchSuggest(keyword) }
                      }, ctx.$t('16582'))
                    ]
                  :  ctx.$t('168462', { search_term: keyword })
            )
        )

        // 根据 children Vnode render 结果反向引导计算 parent vnode状态
        return  contentVnode.length > 0 && h('transition', { props: { name: 'fade-in' }}, [
          h('div', {
            class: 'poptip-destination-modal',
            ref: 'modalContent',
            attrs: { 'data-spm-page': ctx.spmStr },
            directives: [{ name: 'show', value: ctx.showLayer }]
          }, contentVnode)
        ]) as any
      }
    }
  }
})
export default class PoptipDestination extends DestinationMixin {
  @Ref() modalContent!: HTMLElement

  get galileoSpm() {
    const spm = (this.ihtAttrs as any)?.['data-spm-module']?.split('?')[0]
    return {
      spm,
      enforce: 'post',
      enable: !!spm
    }
  }

  show() {
    (this.$el.firstChild  as HTMLElement).click()
  }

  hide() {
    const activeElement = document.activeElement
    activeElement && (activeElement as HTMLInputElement).blur()
  }


  focusIn(e: Event & { target: HTMLElement}) {
    if(!this.showLayer) {
      this.showLayer = true
      this.$emit('show')
      this.$inhouse?.track('action', this.$el)

      this.spmStr && this.modalContent &&  setTimeout(() => {
        this.$inhouse?.track('pageview', this.modalContent, { force: true })
      }, 200)

      if(this.searchInput) {
        if(this.options.stype === 'location') {
          this.searchInput.value = ''
        }
        this.searchInput.select()
      }
    } else {
      this.searchInput?.focus()
      if(this.modalContent?.contains(e.target)) {
          e.stopPropagation()
      } else {
        this.$inhouse?.track('action', this.$el)
        !this.searchInput && this.hide()
      }
    }
  }

  focusOut(e: Event) {
    // FocusOut 会多次触发，点击目的地浮层也会触发 input 元素的 blur 然后几毫秒后又让 input 获得焦点
    // 获得焦点的元素还继续是输入框说明点击的是popper内部的空白区域 不需要做任何处理 是期望的
    // 否则可认为点击的是其他区域 需要关闭poptip  此时如果有搜索记录 则使用第一个 否则为还原操作
    if (this.$el.contains(document.activeElement)) {
      return
    }
      this.showLayer = false
      this.$emit('hide')

      if(!this.searchInput) {
        return
      }

      if (this.searchInput.value !== this.destinationValue) {
        // @ts-ignore
        if(this.searchList.length && ['stype', 'svalue'].some((type) => this.searchList[0][type] !== this.options[type])) {
          this.chooseLocation(this.searchList[0], false)
        } else {
          this.searchInput.value = this.destinationValue
        }
      }
      this.keyword = ''
  }


  // @ts-ignore
  mounted () {
    setTimeout(() => {
      let blurTimmer: any
      let focusTarget = this.searchInput || this.$el as HTMLInputElement

      let handler = () => {
        clearTimeout(blurTimmer)
        blurTimmer = setTimeout(this.focusOut, 130)
      }

      let unwatch = this.$watch('options', () => {
        this.showLayer && this.hide()
        if (this.searchInput) {
          this.searchInput.value = this.destinationValue
        }
      }, {
        immediate: true
      })

      focusTarget.addEventListener('blur', handler)

      this.$once('hook:beforeDestroy', () => {
        unwatch()
        focusTarget.removeEventListener('blur', handler)
        // @ts-ignore
        blurTimmer = unwatch = focusTarget = handler = null
      })
    }, 300)
  }
}
</script>
<style lang="scss" scoped>
@import '../styles/poptip-common.scss';
@import '../styles/destination.scss';

.poptip-destination {
  display: block;

  &.no-label {
    padding: 12px;
    display: flex;
    align-items: center;
    .hotel-selector-item_value {
      flex: 1;
      margin-left: 4px;
    }
  }

  &-input {
    display: block;
    border: none;
    background: none;
    outline: none;
    padding: 0;
    width: 100%;
    &::-webkit-input-placeholder {
      font-weight: $fontWeight-regular;
      color: $color-text-placeholder;
      @include ellipsis
    }

    &:focus {
      text-overflow: initial;
    }
  }

  &-modal {
    word-break: break-word;
    position: absolute;
    top: calc(100% + 4px);
    left: -1px;
    z-index: 2;
    width: 544px;
    border-radius: $radius-l;
    box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.08);
    background: $color-bg-widget-normal;
    padding: 0 16px;
    max-height: 500px;
    overflow-y: auto;
    overflow-x: hidden;

    &::before {
      z-index: 2;
      content: '';
      display: block;
      position: sticky;
      top: 0;
      width: 100%;
      height: 20px;
      background-color: $color-bg-widget-normal;
    }

    &::after {
      z-index: 2;
      display: block;
      position: sticky;
      bottom: 0;
      width: 100%;
      height: 24px;
      content: '';
      margin-top: -4px;
      background-image:
        linear-gradient(
          to bottom,
          rgba(255, 255, 255, 0),
          rgba(255, 255, 255, 1)
        );
    }

    // for firefox
    scrollbar-width: thin;
    scrollbar-color: transparent transparent;

    /*for others 定义滚动条高宽及背景
    高宽分别对应横竖滚动条的尺寸*/
    &::-webkit-scrollbar {
      width: 5px;
      height: 0;
    }
    /*定义滚动条轨道
    内阴影+圆角*/
    &::-webkit-scrollbar-track {
      border-radius: 2px;
      background-color: transparent;
    }
    /*定义滑块
    内阴影+圆角*/
    &::-webkit-scrollbar-thumb {
      border-radius: 4px;
      border-right: 1px solid transparent;
      box-shadow: 4px 0 0 $color-neutral-600 inset;
      visibility: hidden;
    }
    &:hover {
    // for firefox
      scrollbar-color: $color-neutral-600 transparent;
    // for others
      &::-webkit-scrollbar-thumb {
        visibility: initial;
      }
    }

    &.fade-in-enter,
    &.fade-in-leave-to {
      opacity: 0;
    }

    &.fade-in-leave-active,
    &.fade-in-enter-active {
      transition: opacity $motion-duration-m $motion-timing-ease;
    }
  }
}
</style>
