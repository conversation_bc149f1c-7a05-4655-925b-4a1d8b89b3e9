<template>
  <klk-poptip
    ref="poptip"
    v-galileo-click-tracker="galileoSpm"
    v-bind="{ ...mergedPoptipProps }"
    :class="{'hotel-selector-item': !$scopedSlots.default}"
    :width="400"
    :max-height="592"
    class="poptip-guests"
    @show="$emit('show')"
    @hide="$emit('hide')"
  >
    <slot :value-desc="valueDesc">
      <p class="hotel-selector-item_name">{{ guestLabel }}</p>
      <p class="hotel-selector-item_value">{{ valueDesc }}</p>
    </slot>
    <template #content>
      <client-only>
        <guest :options="options" ref="guest" v-bind="$attrs" @change="$emit('change', $event)" @collapse-change="scroll2view" />
        <div  v-if="showConfirmBtn" class="guest-footer">
          <klk-button size="small" @click="confirm">{{ $t('28850') }}</klk-button>
        </div>
      </client-only>
      </template>
  </klk-poptip>
</template>

<script lang="ts">
import { Component, Prop } from 'vue-property-decorator'
import { poptipDefaultOptions } from '../utils'
import GuestMixin from '../mixins/guest'

@Component({
  inheritAttrs: false,
  name: 'poptip-guest'
})
export default class PoptipGuest extends GuestMixin {
  @Prop({ type: Object, default: () => ({}) }) poptipProps!: typeof poptipDefaultOptions

  get galileoSpm() {
    const spm = (this.mergedPoptipProps as any)?.['data-spm-module']?.split('?')[0]
    return {
      spm,
      enforce: 'post',
      enable: !!spm
    }
  }

  confirm() {
    ;(this.$refs.poptip as any).hide()
    const Guest = this.$refs.guest as any
    Guest.commitGuestInfo()
  }

  get showConfirmBtn() {
    // @ts-ignore
    return [this.$attrs.syncChange, this.$attrs['sync-change']].includes(false)
  }

  scroll2view(top: number) {
    const scrollEl = this.$el.querySelector('.klk-poptip-popper-inner') as HTMLElement
    scrollEl.scroll({ top, behavior: 'smooth' })
  }

  get mergedPoptipProps() {
    return Object.assign({}, poptipDefaultOptions, this.poptipProps, this.ihtAttrs)
  }

  show() {
    (this.$el as any).firstChild.click()
  }
}
</script>

<style lang="scss" scoped>
@import "../styles/poptip-common.scss";

.guest-footer {
  margin: 8px -20px -16px;
  padding: 8px 20px;
  position: sticky;
  bottom: -16px;
  border-top: 1px solid $color-border-dim;
  background-color: #fff;
  text-align: right;
}
</style>
