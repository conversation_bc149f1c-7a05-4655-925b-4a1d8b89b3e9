<script lang="tsx">
import { Component, Prop } from 'vue-property-decorator'
import HotelSelector from '../mixins'
import Destination from './poptip-destination.vue'
import Calendar from './poptip-calendar.vue'
import Guest from './poptip-guest.vue'

@Component({
  name: 'selector-desktop',
  components: { Calendar, Guest, Destination }
})
export default class HotelSelectorDesktop extends HotelSelector {
  @Prop({ type: Boolean, default: true }) autoNext!: boolean
  @Prop({ type: Boolean, default: true }) needUpdate!: boolean
  @Prop({ type: Boolean, default: true }) showIcon!: boolean

  render() {
    return <div class="hotel-selector hotel-selector-desktop">
      { this.conentVnode }
    </div>
  }
}
</script>

<style lang="scss" scoped>

.hotel-selector-desktop {
  width: 1160px;
  padding: 16px;
  background-color: #fff;
  display: flex;
  .poptip-destination {
    flex: 1;
    margin-right: 8px;
    ::v-deep .klk-poptip-popper-inner {
      margin: 16px 0;
      padding: 0 16px;
    }
  }

  .hotel-selector-button {
    border-radius: $radius-l;
    min-width: 140px;
    padding: 8px 12px;
    ::v-deep .i-icon {
      margin-right: 8px;
    }
  }
}
</style>
