import { Vue, Component, Prop, Model, Watch } from 'vue-property-decorator'
import dayjs from 'dayjs'
import { DestinationInfo, CalendarInfo, GuestInfo, DestinationConfig, GuestConfig, CalendarConfig, HotelQueryHistory, GeoPosition } from '../../type'
import { sleep, SearchHistory, formatIHTvalue } from '../utils'
import { IconRefresh, IconSearch } from '@klook/klook-icons'

const calendarInfoKeys: Array<keyof CalendarInfo> = ['check_in', 'check_out', 'calendar_type', 'flexible_date_list', 'flexible_day', 'flexible_type']

const childComponents = ['destination', 'calendar', 'guest']

@Component({
  inheritAttrs: false,
  data(this: HotelSelectorMixin) {
    return this.getDefaultParams()
  },
  components: {
    IconRefresh,
    IconSearch
  }
})
export default class HotelSelectorMixin extends Vue {
  @Prop({ type: Boolean, default: false }) needHistory!: boolean // 列表页和垂直页传true
  @Prop({ type: Boolean, default: false }) needUpdate!: boolean // 列表页和详情页传true
  @Prop({ type: Function }) shouldSaveHistory!: Function
  @Prop({ type: Object, default: () => null }) destinationConfig!: DestinationConfig
  @Prop({ type: Object, default: () => ({}) }) guestConfig!: { [key: string]: 'hide' | 'disabled' | GuestConfig }
  @Prop({ type: Object, default: () => ({}) }) calendarConfig!: CalendarConfig
  @Model('search', { type: Object, default: () => ({}) }) // 询接口所需的参数集合

  paramsInfo!: DestinationInfo & CalendarInfo & GuestInfo & {
    longitude?: string | number
    latitude?: string | number
  }


  destinationEmptyRemind = false // 空目的地的提醒

  calendarInfo!: CalendarInfo
  destinationInfo!: DestinationInfo
  guestInfo!: GuestInfo

  geoPosition: GeoPosition = {}
  historyQueryList: HotelQueryHistory[] = []
  isChange = false

  // mobile 独有变量
  fixedTop!: number
  drawerVisible!: boolean
  // desktop 独有变量
  autoNext!: boolean
  
  // Search按钮文案是否展示为Update
  showUpdate: boolean = false

  selector!: any

  // 用户手动修改目的地/日期/人数等信息 按钮变成更新，点更新后重置为Search
  updateSearchBotton(val: boolean) {
    if (this.needUpdate) {
      this.showUpdate = val
    }
  }

  autoShowNext(childComponentName: string) {
    if(this.isChange) {
      return
    }
    let index = childComponents.indexOf(childComponentName)
    if (~index) {
      while (++index < childComponents.length) {
        if (this.show(childComponents[index] as 'guest')) {
          return
        }
      }
    }
  }


  show(childComponentName?: 'destination' | 'calendar' | 'guest') {
    if (!childComponentName) {
      this.drawerVisible = Boolean(this.fixedTop)
    } else if (typeof childComponentName === 'string' && childComponents.includes(childComponentName)) {
      const childComponent = this.$refs[childComponentName] as Vue & { show: Function }
      return childComponent && setTimeout(childComponent.show, 50)
    }
  }

  updateSearchHistory(history?: HotelQueryHistory | void) {
    this.historyQueryList = this.selector?.update(history)
  }

  async onSearch(evt?: Event) {
    // web端目的地浮层还没收起来时候，等待收起来选中
    if (this.$el.querySelector('.poptip-destination-modal')) {
      await sleep(150)
    }

    if (this.destinationConfig) {
      const { svalue, stype } = this.destinationInfo

      if (!this.fixedTop) {
        this.destinationEmptyRemind = !this.$scopedSlots['destination-content'] && !svalue
        if(this.destinationEmptyRemind) {
          return setTimeout(this.show, 50, 'destination')
        }
      } else {
        this.drawerVisible = false
      }

      this.isChange = true

      if (this.needHistory && this.calendarInfo.calendar_type !== 2 && (!this.shouldSaveHistory || this.shouldSaveHistory(this.destinationInfo))) {
        const { check_in, check_out } = this.calendarInfo
        check_in && check_out && this.updateSearchHistory({
          time: [check_in, check_out] as string[],
          params: this.guestInfo,
          location: this.destinationInfo
        })
      }
    }
    const params = { ...this.paramsInfo }

      if(this.calendarConfig && this.calendarConfig.flexible !== false) {
        /*
        由于弹性搜索和日期搜索存在字段的不同 所以每次搜索以this.calendarInfo 的字段为准
        paramsInfo的其他字段是因为可能有除了paramsInfo声明以外的字段， 比如 hotel_id。如果依靠组件的输出来维持 query 的变动 可能会衔接不上
      */
        calendarInfoKeys.forEach((key) => {
          delete params[key]
        })
      }

    const query = Object.assign(params, this.calendarInfo, this.guestInfo, this.geoPosition, this.destinationInfo)

    this.$emit('search', query)

    evt && evt.stopPropagation()
    const btnEl = evt?.currentTarget || (this.$refs?.searchButton as Vue)?.$el
    const hasDates = query.check_in && query.check_out || undefined

    if(this.$inhouse) {
      const { type_desc, tag_info, title } = this.destinationInfo
      this.$inhouse.updateBinding(btnEl, {
        ext: JSON.stringify({
          SearchDestinationCityID: query.city_id,
          SearchDestinationType: query.stype,
          SearchDestinationTypeID: query.svalue,
          SearchLeadTime: hasDates && dayjs(query.check_in).diff(dayjs().format('YYYY-MM-DD'), 'day'),
          SearchNights: hasDates && dayjs(query.check_out).diff(dayjs(query.check_in), 'day'),
          SearchRoom: query.room_num,
          SearchGuestAdult: query.adult_num,
          SearchGuestChild: query.child_num,
          ISDestinationPOI: false,
          IsisExclude: false,
          DestinationType: formatIHTvalue(type_desc),
          TagType: formatIHTvalue(tag_info?.id),
          SearchCardName: formatIHTvalue(title)
        })
      })
      setTimeout(() => {
        this.$inhouse.track('action', btnEl)
      }, 10)
    }

    this.updateSearchBotton(false)
  }

// 非 user 操作不需要 auto next
  selectDestination(destinationInfo: DestinationInfo, isUserChoose = true) {
    this.destinationEmptyRemind = false
    this.destinationInfo = destinationInfo
    isUserChoose && this.updateSearchBotton(true)
    if (this.fixedTop) {
      this.onSearch()
    } else if (this.autoNext && isUserChoose) {
      this.autoShowNext('destination')
    }
  }

  selectHistory(history: HotelQueryHistory, isUserChoose = true) {
    const { params, location, time: [check_in, check_out] } = JSON.parse(JSON.stringify(history))
    this.selectDestination(location, false)
    this.guestInfo = params
    this.calendarInfo = {
      check_in,
      check_out
    }
    isUserChoose && this.updateSearchBotton(true)
    this.updateSearchHistory(history)
  }

  @Watch('paramsInfo', { immediate: true })
  initParams() {
    // 阻止双向绑定变化不必要的计算
    if (this.isChange) {
      return (this.isChange = false)
    }

    const dirtyData = undefined

    Object.entries(this.getDefaultParams()).forEach(([prop, info]) => {
      Object.keys(info).forEach((key) => {
          // @ts-ignore
        let val = this.paramsInfo[key]
        if (val !== dirtyData) {
          if (key.includes('_num')) {
            val = Number(val)
          }
          // @ts-ignore
          info[key] = val
          // @ts-ignore
        } else if (info[key] === dirtyData) {
          // @ts-ignore
          delete info[key]
        }
      })
      // @ts-ignore
      this[prop] = info
    })
  }

  mounted() {
    if (this.destinationConfig && this.needHistory) {
      this.selector =  new SearchHistory()
      this.historyQueryList = this.selector.list
      if (!this.fixedTop &&  !this.destinationInfo.svalue && this.historyQueryList.length) {
        this.selectHistory(this.historyQueryList[0], false)
      }
      this.$emit('mounted', this.$refs.destination)
    }
  }

  getDefaultParams() {
    const res: Partial<Pick<this, 'calendarInfo' | 'guestInfo' | 'geoPosition' | 'destinationInfo'>> = {}
    // 初始化值为 undefined 的key 在paramsInfo 没有 值的情况下会被删掉
    if (this.calendarConfig) {
      res.calendarInfo = {
        check_in: undefined,
        check_out: undefined,
        calendar_type: undefined,
        flexible_type: undefined,
        flexible_date_list: undefined,
        flexible_day: undefined
      }
    }

    if (this.guestConfig) {
      const guestInfo = res.guestInfo = {
        room_num: 1,
        adult_num: 2,
        child_num: 0,
        age: ''
      }

      for (const key in guestInfo) {
        const val = this.guestConfig[key.replace('_num', '')];

        if(val === 'hide') {
          delete guestInfo[key as keyof GuestInfo]
        }
      }
    }

    if (this.destinationConfig) {
      res.destinationInfo = {
        stype: '', // city/country/brand/hotel/area 类型，用于区分  旧数据为city
        svalue: '', // 数据id或其他值 对应旧数据cityId
        override: '', // 用于展示的值：深圳，中国 对应旧数据destination
        title: '',
        city_id: '',
        latlng: ''
      }

      if (this.destinationConfig.needLocation) {
        res.geoPosition = {
          longitude: undefined,
          latitude: undefined
        }
      }
    }

    return res
  }

  getListeners(keyword: string, listeners: Record<string, Function | Function[]> = {}) {
    Object.keys(this.$listeners).forEach((key) => {
      const prefix = keyword + '-'
      if (key.indexOf(prefix) === 0) {
        listeners[key.replace(prefix, '')] = this.$listeners[key];
      }
    })

    return listeners
  }

  get conentVnode () {
    return [
        this.destinationConfig && <klk-poptip
          dark
          content={ this.$t('168450') }
          value={ this.destinationEmptyRemind }
          placement="top-start"
          trigger="none"
          class="poptip-destination"
        >
          <destination
            ref="destination"
            options={ this.destinationInfo }
            geoPosition={ this.geoPosition }
            historyList={ this.historyQueryList }
            attrs={ Object.assign({}, this.destinationConfig, this.$attrs) }
            on={ this.getListeners('destination', {
                change: this.selectDestination,
                'clear-history': this.updateSearchHistory,
                'select-history': ($event: HotelQueryHistory) => this.selectHistory($event, true),
                'geo-position': (geoPosition: this['geoPosition']) => this.$emit('created', this.geoPosition = geoPosition)
              })
            }
            scopedSlots={{
              default: this.$scopedSlots.destination,
              content: this.$scopedSlots['destination-content']
            }}
          />
        </klk-poptip>,

        ['calendar', 'guest'].map((tagName) => {
          const attrs = this[tagName + 'Config' as 'guestConfig']
          if (attrs) {
            const optionsName = tagName + 'Info' as 'calendarInfo'
            return <tagName
              attrs={ Object.assign({}, attrs, this.$attrs) }
              ref={ tagName }
              options={ this[optionsName] }
              key={ tagName }
              on={ this.getListeners(tagName, {
                change: (info: this[typeof optionsName]) => {
                  this[optionsName] = info
                  let status = tagName === 'guest'
                  if (this.autoNext && tagName === 'calendar' && (info.check_in || info.flexible_date_list)) {
                    status = true
                    info.check_in && this.autoShowNext('calendar')
                  }
                  this.updateSearchBotton(status)
                },
                update: (dates: Date[]) => {
                  this.updateSearchBotton(true)
                  if (this.autoNext && tagName === 'calendar' && Array.isArray(dates)) {
                    this.autoShowNext('guest')
                  }
                }
              }) }
              scopedSlots={
                typeof this.$scopedSlots[tagName] === 'function'
                  ? { default:  this.$scopedSlots[tagName] }
                  : {}
              }
            />
          }
        }),
        <klk-button
          class="hotel-selector-button"
          ref="searchButton"
          {...{ directives: [{ name: 'galileo-click-tracker', value: { spm: 'SearchButton' } }] }}
          data-spm-module="SearchButton?trg=manual"
          data-spm-virtual-item="__virtual?trg=manual&typ=entry"
          type="primary"
          size="large"
          nativeOnClick={ this.onSearch }
        > 
          { this.needUpdate ? (this.showUpdate ? <IconRefresh /> : <IconSearch />) : this.showIcon && <IconSearch /> }
          { this.needUpdate && this.showUpdate ? this.$t('195094') : this.$t('168432') }
        </klk-button>
    ]
  }
}
