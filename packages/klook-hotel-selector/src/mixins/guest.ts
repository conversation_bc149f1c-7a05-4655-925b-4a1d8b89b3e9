import { Vue, Component, Model, Prop } from 'vue-property-decorator'
import { formatGuestDesc } from '../utils'
import Guest from '../components/guest.vue'
import { GuestInfo, GuestConfig } from '../../type'

@Component({
  inheritAttrs: false,
  components: { Guest }
})
export default class HotelGuest extends Vue {
  @Prop({
    type: Object,
    default: () => ({
      'data-spm-module': 'SearchGuestAndRoom?trg=manual',
      'data-spm-virtual-item': '__virtual'
    })
  }) ihtAttrs!: object
  @Model('change', {
    type: Object,
    default: () => ({
      room_num: 1,
      adult_num: 2,
      child_num: 0,
      age: ''
    })
  }) options!: GuestInfo

  get guestLabel() {
    // @ts-ignore
    const { room } = this.$attrs as GuestConfig
    return room && (room === 'hide' || room?.display === 'hide')
        ? this.$t('15753')
        : this.$t('196535')
  }

  get valueDesc() {
    return formatGuestDesc.call(this, this.options)
  }
}
