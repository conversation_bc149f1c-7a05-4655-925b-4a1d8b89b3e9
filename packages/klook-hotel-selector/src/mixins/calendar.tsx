import { Vue, Component, Prop, Model } from 'vue-property-decorator'
import dayjs from 'dayjs'
import { getMonthText, getMonthOption } from '../utils'
import SimpleCalendar from '../components/calendar.vue'

import { CalendarInfo } from '../../type'

@Component({ inheritAttrs: false })
export default class HotelCalendar extends Vue {
  @Model('change', { type: Object, default: () => ({}) }) options!: CalendarInfo
  @Prop({ type: Boolean, default: true }) flexible!: boolean // 是否允许弹性搜索
  @Prop({ type: Boolean, default: true }) clearable!: boolean // 是否允许清空
  @Prop({ type: Boolean, default: true }) allowNotSelect!: boolean // 允许可以不选日期，场景是可以无日期操作
  @Prop({ type: Function, default: null }) fetchDailyPrice!: () => Promise<{ date: string, price_desc: string }[]>
  @Prop({ type: Object, default: null }) dailyPriceInfo!: Record<string, string>
  @Prop({
    type: Object,
    default: () => ({
      'data-spm-module': 'SearchDate?trg=manual',
      'data-spm-virtual-item': '__virtual'
    })
  }) ihtAttrs!: object
  @Prop({ type: Boolean, default: false }) hasConfirm!: boolean
  @Prop({ type: Number, default: 1 }) minLos!: number
  @Prop({ type: Number, default: 28 }) maxLos!: number
  @Prop({ type: [Date, String, Number], default: () => new Date() }) minDate!: number | Date | string
  @Prop({ type: [Number, Date, String] }) maxDate!: number | Date | string
  @Prop({ type: Boolean, default: false }) loading!: boolean
  @Prop({ type: Function, default: null }) customFormatDate!: any // (dates: string[], days: Date[]) => string

  isMobile = false

  tabName: 'flexibleOptions' | 'calendarOptions' = 'calendarOptions'

  calendarOptions = {
    check_in: '',
    check_out: '',
  }

  flexibleOptions: {
    flexible_type: number, // 灵活日期类型， 1：weekend, 2:day
    flexible_date_list?: string // 灵活日期的月份yyyy-MM 多个以逗号分割
    flexible_day?: number, // 灵活日期的天数
  } = {
    flexible_type: 1,
    flexible_date_list: '',
    flexible_day: 2
  }

  isChange = false

  otherOptions = {}

  monthOption: string[] = []

  innerDailyPriceInfo: { [key: string]: string } | null = null

  get finalDailyPriceInfo() {
    return this.dailyPriceInfo || this.innerDailyPriceInfo
  }

  singleCheckIn: Date | null = null

  getDailyPriceInfo() {
    if (typeof this.fetchDailyPrice === 'function') {
      const promiseRes = this.fetchDailyPrice()

      if (typeof promiseRes === 'object' && typeof promiseRes.then === 'function') {
        promiseRes.then((dailyPriceList) => {
          if (dailyPriceList.length && typeof dailyPriceList[0] === 'object' && dailyPriceList[0].hasOwnProperty('date')) {
            this.innerDailyPriceInfo = dailyPriceList.reduce((res: { [key: string]: string }, { date, price_desc }) => {
              res[date] = price_desc
              return res
            }, {})
          }
        })
      }
    }
  }

  get isFlexibleTab() {
    return this.tabName === 'flexibleOptions'
  }

  get diffDay() {
    if (this.isValid) {
      const {
        check_in,
        check_out
      } = this.calendarOptions
      if (check_in && check_out) {
        return dayjs(check_out).diff(check_in, 'day')
      }
    }
    return 0
  }

  get diffDayText() {
    if (this.singleCheckIn || !this.diffDay) {
      return
    }
    const diff = this.diffDay
    const text = diff > 1 ? this.$t('168434', { num: diff }) : this.$t('101491')
    return ` ( ${text} )`
  }

  get confirmBtnDisabled() {
    if (this.tabName === 'flexibleOptions') {
      return false
    }
    if (this.singleCheckIn) {
      return true
    }
    if (this.allowNotSelect && !this.diffDay) {
      return false
    }
    // 所选中的日期在可选的日期范围外
    if (!this.diffDay || !this.isValid) {
      return true
    }
    return this.diffDay < this.minLos || this.diffDay > this.maxLos;
  }

  get clearableBtnDisabled() {
    return this.tabName === 'flexibleOptions'
      ? !this.flexibleOptions.flexible_date_list?.length
      : !(this.singleCheckIn || this.isValid)
  }

  get finalMinDate() {
    if (this.minDate) {
      return dayjs(this.minDate).startOf('d').toDate()
    }
    return dayjs().startOf('d').toDate()
  }

  get finalMaxDate() {
    let limit = this.$store?.state?.hotel?.dateRange || 365
    if (this.maxDate) {
      if (typeof this.maxDate === 'number' && this.maxDate < 9999) {
        limit = this.maxDate
      } else {
        return dayjs(this.maxDate).endOf('d').toDate()
      }
    }
    return dayjs(this.finalMinDate).add(limit, 'd').endOf('d').toDate()
  }

  get isValid() {
    return new Date(this.calendarOptions.check_in) >= this.finalMinDate && new Date(this.calendarOptions.check_out) <= this.finalMaxDate
  }

  get finalCalendarOptions() {
    return this.isValid ? this.calendarOptions : { check_in: '', check_out: '' }
  }

  mounted() {
    // 延时执行  是因为 可能在垂直页 有需要应用历史记录的情况， 避免多次执行
    setTimeout(() => {
      this.monthOption = getMonthOption()
      // 在mounted 中 watch 是因为 服务端和本地在跨时区情况下 极端情况下可能存在跨月份 继而导致node端和客户端的渲染差异 出现appenchild 报错
      this.$once('hook:beforeDestroy', this.$watch('options', this.initDateOption, { immediate: true }))
    }, 500)
  }

  /**
   * 数据更新流程
   * watch 的目的是监控被组件外部修改的情况
   * 若是组件内部的改动 则会isChange为true, 在initDateOption return 掉
   */
  initDateOption(options?: this['options'], oldOptions?: this['options']) {
    if (this.isChange) {
      return (this.isChange = false)
    }

    options = options || this.options
    // eslint-disable-next-line prefer-const
    let { check_in = '', check_out = '', calendar_type, flexible_day = '', flexible_date_list, flexible_type = 1, ...others } = this.options
    // 保存多于的参数  提交的时候一并返回
    this.otherOptions = others
    // @ts-ignore 阻止不必要的初始化
    if(oldOptions && !Object.entries(this[this['tabName']]).some(([key, value]) => value !== options[key as keyof CalendarInfo])) {
      return
    }

    this.calendarOptions = { check_in, check_out }

    if (this.flexible) {
      const isFlexibleTab = calendar_type === 2
      this.tabName = isFlexibleTab ? 'flexibleOptions' : 'calendarOptions'

      if (flexible_date_list && flexible_date_list.length) {
        const today = dayjs()
        flexible_date_list = flexible_date_list.split(',')
          .filter(month => !dayjs(month).isBefore(today, 'month'))
          .toString()
      }

      this.flexibleOptions = {
        flexible_type,
        flexible_date_list: flexible_date_list || this.monthOption.slice(0, 2).toString(),
        flexible_day: +flexible_day || 2
      }
    }
  }

  clearDates() {
    this[
      this.tabName !== 'calendarOptions'
      ? 'setFlexible'
      : 'setCalendar'
    ]()
  }

  setFlexible(data?: CalendarInfo) {
    this.isChange = true
    Object.assign(this.flexibleOptions, data || {
      flexible_type: 1, // 灵活日期类型， 1：weekend, 2:day
      flexible_date_list: '', // 灵活日期的月份yyyy-MM 多个以逗号分割
      flexible_day: 2 // 灵活日期的天数
    })
    data && this.$emit('update')
  }

  setCalendar(data?: this['calendarOptions']) {
    this.isChange = true

    this.calendarOptions = data || { check_in: '', check_out: '' }

    this.showSelectedTip?.()

    if (this.hasConfirm) {
      return
    }

    if (this.calendarOptions.check_out) {
      // desktop 选完日期后关闭 poptip
      const poptipCalendar = this.$refs.poptipCalendar as any

      poptipCalendar?.hide()
    }
  }

  onSingleChange(checkIn: null | Date) {
    this.singleCheckIn = checkIn
    this.showSelectedTip?.()
    this.$emit('single-change', checkIn)
  }

  confirmIsChange() {
    // 由于切换 TAB 也可能是 A -> B  -> A 最终复原。所以不能以点击 tab确认用户的操作
    if (!this.isChange) {
      const oldTabValue = this.options.calendar_type || 1
      const newTabValue = this.tabName === 'flexibleOptions' ? 2 : 1
      if (oldTabValue !== newTabValue) {
        this.isChange = true
      }
    }
    return this.isChange
  }

  getPostData() {
    // 清除操作后/无日期 无需提交多余的参数(后端接口不支持)
    let postData: CalendarInfo
    if (this.tabName !== 'flexibleOptions') {
      postData = { ...this.calendarOptions }
    } else if(!this.flexibleOptions.flexible_date_list) {
      postData ={}
    } else {
      postData = { ...this.flexibleOptions, calendar_type: 2 }
      if (postData.flexible_type !== 2) {
        delete postData.flexible_day
      }
    }

    return Object.assign(postData, this.otherOptions)
  }

  commitTravelDate() {
    if(!this.confirmIsChange()) {
      return
    }

    this.$emit('change',this.getPostData())
  }

  selectFlexibleMonth(month: string) {
    let { flexible_date_list } = this.flexibleOptions
    if (flexible_date_list) {
      const flexibleList = flexible_date_list.split(',')
      const index = flexibleList.indexOf(month)
      if (index > -1) {
        if (flexibleList.length === 1) {
          return
        }
        flexibleList.splice(index, 1)
      } else {
        flexibleList.push(month)
      }
      flexible_date_list = flexibleList.sort((...months) => {
        const [a, b] = months.map(month => +month.replace('-', ''))
        return a - b
      }).toString()
    } else {
      flexible_date_list = month
    }
    this.setFlexible({ flexible_date_list })
  }

  renderTab() {
    return this.flexible && <div
      class="calendar-type-tab"
      data-spm-module="SearchDate?trg=manual"
    >
      <div
        {...{ directives: [{ name: 'galileo-click-tracker', value: { spm: 'SearchDate.SearchMode' } }] }}
        data-spm-item="SearchMode"
        class={['calendar-type-btn', {'active': !this.isFlexibleTab}]}
        onClick={(e: any) => {
          this.tabName = 'calendarOptions'
          e.stopPropagation()
        }}
      >
        {this.$t('47257')}
      </div>
      <div
        {...{ directives: [{ name: 'galileo-click-tracker', value: { spm: 'SearchDate.SearchMode' } }] }}
        data-spm-item="SearchMode"
        class={['calendar-type-btn', {'active': this.isFlexibleTab}]}
        onClick={(e: any) => {
          this.tabName = 'flexibleOptions'
          e.stopPropagation()
        }}
      >
        {this.$t('47265')}
      </div>
    </div>
  }

  renderFlexible() {
    return this.flexible && <div
      style={`display: ${this.isFlexibleTab ? '' : 'none'}`}
      class="flexible-container"
      data-spm-module="SearchDate?trg=manual"
    >
      <p class="flexible-tip">
        {
          !this.flexibleOptions.flexible_date_list
            ? this.$t('12022')
            : [
              this.$t('47266') + ' ',
              <b>
                {
                  this.flexibleOptions.flexible_date_list.split(',')
                    .map(date => getMonthText.call(this, date)).join(', ')
                }
              </b>
            ]
        }
      </p>
      <div class="flex-tag-list">
        {
          this.monthOption.map(month => <div
            key={month}
            {...{ directives: [{ name: 'galileo-click-tracker', value: { spm: 'SearchDate.FlexibleMonth' } }] }}
            data-spm-item="FlexibleMonth"
            class={['flex-tag-item row-column3', { active: this.flexibleOptions.flexible_date_list && this.flexibleOptions.flexible_date_list.includes(month) }]}
            onClick={(e: Event) => {e.stopPropagation(); this.selectFlexibleMonth(month)}}
          >
            <p class="ellipsis">{getMonthText.call(this, month)}</p>
            <p class="c-gray ellipsis">{month.slice(0, 4)}</p>
          </div>)
        }
      </div>
      {this.flexibleOptions.flexible_date_list && [
        <p class="flexible-tip">
          { [this.$t('47268'), this.$t('47267')][this.flexibleOptions.flexible_type - 1] }
          &nbsp;
          <b>{ [this.$t('47281'), this.$t('47283')][this.flexibleOptions.flexible_type - 1 as number] }</b>
        </p>,
        this.flexibleOptions.flexible_type === 1 && <p  class="c-gray gray-tip">
          {this.$t('47307')}
        </p>,
        <div class="flex-tag-list">
          {
            [this.$t('47281'), this.$t('47283')].map((name, index) => <div
              key={index}
              {...{ directives: [{ name: 'galileo-click-tracker', value: { spm: 'SearchDate.FlexiblePeriod' } }] }}
              data-spm-item="FlexiblePeriod"
              class={['flex-tag-item row-column2', { active: (this.flexibleOptions.flexible_type as number) - 1 === index }]}
              onClick={(e: Event) => {e.stopPropagation();this.setFlexible({ flexible_type: index + 1 })}}
            >
              <span>{name}</span>
            </div>)
          }
        </div>,
        this.flexibleOptions.flexible_type === 2 && [
          <p class="flexible-tip" style="margin-top: 40px">
            {this.$t('47268')}
            <b>
              {' ' + (this.flexibleOptions.flexible_day > 1 ? this.$t('168434', { num: this.flexibleOptions.flexible_day }) : this.$t('101491'))}
            </b>
          </p>,
          <div class="flex-tag-list flexible-dayly-list">
            {
              Array.from({ length: 7 }, (_, index) => <div
                key={index}
                {...{ directives: [{ name: 'galileo-click-tracker', value: { spm: 'SearchDate.FlexibleStayNight'} }] }}
                data-spm-item="FlexibleStayNight"
                class={[
                  `flex-tag-item ellipsis row-column${this.hasConfirm ? 4 : 7}`,
                  { active: this.flexibleOptions.flexible_day === index + 1 }
                ]}
                onClick={(e: Event) => {e.stopPropagation();this.setFlexible({ flexible_day: index + 1 })}}
              >
                { index > 0 ? this.$t('168434', { num: index + 1 }) : this.$t('101491')}
              </div>)
            }
          </div>
        ]]}
    </div>
  }

  renderCalendar({scopedSlots = {}, on = {}}) {
    return <SimpleCalendar
      // @ts-ignore
      ref="simpleCalendar"
      attrs={{
        ...this.$attrs,
        dailyPriceInfo: this.finalDailyPriceInfo,
        platform: this.isMobile ? 'mobile' : 'desktop',
        calendarInfo: this.finalCalendarOptions,
        minLos: this.minLos,
        maxLos: this.maxLos,
        minDate: this.finalMinDate,
        maxDate: this.finalMaxDate
      }}
      style={{
        display: this.isFlexibleTab ? 'none' : undefined,
        margin: '0 -16px'
      }}
      singleCheckIn={this.singleCheckIn}
      on={{
        ...on,
        change: this.setCalendar,
        update: (dates: string[]) => {
          this.setCalendar({ check_in: dates[0], check_out: dates[1] })
          this.$emit('update', dates)
        },
        'single-change': this.onSingleChange,
        'view-change': (...args) => this.$emit('view-change', ...args)
      }}
      scopedSlots={{
        ...scopedSlots
      }}
    />
  }
}
