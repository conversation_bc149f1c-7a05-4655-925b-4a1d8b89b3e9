import { Vue, Component, Prop, Watch, Model, Ref } from 'vue-property-decorator'
import { DestinationInfo, DestinationListItem, GeoPosition, HotelQueryHistory, DestinationConfig } from '../../type'
import components from '../components/destination'

@Component({
  inheritAttrs: false,
  components
  })
export default class HotelDestination extends Vue {
  @Model('change', { type: Object, default: () => ({
    stype: '', // city/country/brand/hotel/area 类型，用于区分  旧数据为city
    svalue: '', // 数据id或其他值 对应旧数据cityId
    override: '', // 用于展示的值：深圳，中国 对应旧数据destination
    city_id: '',
    title: '',
    latlng: ''
  }) }) options!: DestinationInfo
  @Prop({ type: Object, default: () => ({}) }) geoPosition!: GeoPosition
  @Prop({ type: Boolean }) needLocation!: DestinationConfig['needLocation']
  @Prop({ type: Function }) getCityList?: DestinationConfig['getCityList']
  @Prop({ type: Function }) suggest?: DestinationConfig['suggest']
  @Prop({ type: [String, Boolean], default: true }) label!: string | boolean
  @Prop({ type: String }) placeholder?: string
  @Prop({ type: Array, default: () => [] }) historyList!: HotelQueryHistory[]
  @Prop({
    type: Object,
    default: () => ({
      'data-spm-module': 'SearchDestination?trg=manual',
      'data-spm-virtual-item': '__virtual?trg=manual'
    })
  }) ihtAttrs!: object
  @Prop({ type: [Object, String], default: 'HotelAPI_SearchStart' }) spmPage!: string | {
    name: string
    oid?: string
    ext?: object
  }
  @Ref() searchInput?: HTMLInputElement

  keyword: string = ''

  showLayer = false

  searchList: DestinationInfo[] & { keyword?: string} = []

  destinationList: DestinationListItem[] = []

  searchError = false
  timmer: any = null

  get spmStr() {
    const { name, oid, ext } = typeof this.spmPage === 'object'
      ? this.spmPage
      : { name: this.spmPage } as Record<string, string>

    let str = ''

    if(name) {
      str += `${name}?trg=manual`

      if (oid) {
        str += `&oid=${oid}`
      }

      if (typeof ext === 'object') {
        str += `&ext=${decodeURIComponent(JSON.stringify(ext))}`
      }
    }

    return str
  }

  get inputLabel() {
    return typeof this.label === 'string' ? this.label : this.$t('88925')
  }

  get destinationValue() {
    return this.options.override || this.options.title || ''
  }

  get inputPlaceholder() {
    return typeof this.placeholder === 'string' ?  this.placeholder : this.$t('88914')
  }

  async mounted(this: this & { _uid: number }) {
    const { needLocation, getCityList } = this
    // staycation 特殊逻辑  单独处理
    if (!getCityList) {
      return
    }
    let position = this.geoPosition
    if (!this.options.svalue && needLocation && (!position.longitude || !position.latitude) && navigator.geolocation) {
      position = await new Promise((resolve) => {
        navigator.geolocation.getCurrentPosition(({ coords: { longitude, latitude } }) => {
          resolve({ longitude, latitude })
        }, () => { resolve({}) }, {
          timeout: 3000,
          maximumAge: 60000
        })
        // 兼容whitelabel app中Promise一直pending状态，导致后续逻辑未执行
        setTimeout(() => {
          resolve({})
        }, 3000)
      })
    }
    this.$emit('geo-position', position)
    this.destinationList = await getCityList(position) || []
    
    if(this.destinationList?.length) {
      if (!this.options.svalue && position.longitude && position.latitude) {
        const locationType = 'location'
        const userLocation = this.destinationList.find(({ type }) => type === locationType)
        if (userLocation?.content?.length) {
          this.chooseLocation(userLocation.content[0], false)
        }
      }

      let unWatch: any = this.$watch('showLayer', (isShow: boolean) => {
        isShow && setTimeout(() => {
          const iconsList = (this.$refs.destinationRecommend as Element)?.querySelectorAll('.destination-group .show_all_icon')
          if(iconsList?.length) {
            iconsList.forEach(icon => {
              const destinationGrop = icon.parentElement as HTMLElement
              const scrollHeight = destinationGrop.scrollHeight
              destinationGrop.style.maxHeight = scrollHeight + 'px'
              const disHeight = 98
              if (scrollHeight > disHeight) {
                icon.classList.remove('f_hidden')
                const children = destinationGrop.children || []
                let index = children.length - 2
                while (index > 1 && (icon as HTMLElement).offsetTop >= disHeight) {
                  children[index].classList.add('f_hidden')
                  index--
                }
              }
            })
          }
          this.$off('hook:beforeDestroy', destroy)

          destroy()
        }, 350)
      })

      let destroy: any = () => {
        unWatch()
        destroy = unWatch = null
      }

      this.$once('hook:beforeDestroy', destroy)
    }
  }

  chooseLocation({
    svalue,
    stype = 'city',
    title,
    city_id = '',
    latlng,
    override
  }: DestinationInfo, isUserChoose = true) { // 非 user 操作不需要 auto next
    this.$emit('change', {
      ...this.options,
      svalue,
      stype,
      latlng, // 地图需求新增， 用于地图初始化选中目的地的中心点
      title,
      override,
      city_id,
    }, isUserChoose)
  }

  @Watch('keyword')
  searchSuggest(val: string) {
    this.timmer && clearTimeout(this.timmer)
    if(this.searchList.length) {
    this.searchList = []
    }
    if (val) {
      this.timmer = setTimeout(() => {
        this.suggest!(val.trim()).then((result) => {
          if(this.keyword && result?.suggests?.length) {
          this.searchList = Object.assign(result.suggests, { keyword: result.keyword })
          }
          this.searchError = false
          this.timmer = null
        }, (err) => {
          if(err.message !== 'cancel') {
            this.searchError = true
            this.timmer = null
          }
        })
      }, 300)
    } else {
      this.timmer = null
    }
  }
}
