import Vue from 'vue';

type ShowAbleComponent = Vue.Component & { show: Function, hide: Function}
type ShowAbleWithParamsComponent = Vue.Component & { show: (childComponentName?: 'destination' | 'calendar' | 'guest') => void, hide: Function }

export const Calendar: Vue.Component;
export const Guest: Vue.Component;
export const PageLayer: ShowAbleComponent;
export const SelectorMobile: ShowAbleWithParamsComponent;
export const LayerGuest: ShowAbleComponent;
export const LayerDestination: ShowAbleComponent;
export const LayerCalendar: ShowAbleComponent;
export const SelectorDesktop: ShowAbleWithParamsComponent;
export const PoptipGuest: ShowAbleComponent;
export const PoptipDestination: ShowAbleComponent;
export const PoptipCalendar: ShowAbleComponent;


type install = (vue: typeof Vue) => void

export const HotelSelector: {
  install: install
  desktop: install
  mobile: install
  common: install
}

export default HotelSelector

export interface HotelQueryHistory {
  time: string[],
  location: DestinationInfo,
  params: GuestInfo
}

export interface GeoPosition {
  longitude?: string | number
  latitude?: string | number
}

export interface DestinationInfo {
  title?: string
  icon_url?: string
  sub_title?: string
  type_desc?: string
  type_desc_en?: string
  stype?: string // city/country/brand/hotel/area 类型，用于区分  旧数据为city
  svalue: number | string // 数据id或其他值 对应旧数据cityId
  secondary_title?: string // 用户页面语言和输入语言不一致返回
  latitude?: string
  longitude?: string
  override?: string // 用于展示的值：深圳，中国 对应旧数据destination
  city_id: string | number
  latlng?: string // 自定义字段
  child_list?: DestinationInfo[]
  tag_info?: {
    desc?: string
    name: string
    id: string
    bg_color_left: string
    bg_color_right: string
    text_color: string
    bg_color: string
    border_color?: string
  }
  image?: string
}

export type SuggestResult = {
  keyword: string
  version?: string
  suggests: DestinationListItem[]
}

export type DestinationListItem = {
  section_title: string
  type: string
  ihc_type: string
  ihc_object_name?: string // '?oid=city_121212'
  content: DestinationInfo[]
}

export interface DestinationConfig {
  suggest?: (keyword: string) => Promise<{keyword: string; suggests: DestinationInfo[]}> // 没有传代表不能搜索 -- readonly
  getCityList?: (params: GeoPosition) => Promise<DestinationListItem[]>
  label?: string | boolean
  placeholder?: string // 没有传 会走默认
  needLocation?: boolean
}

export interface GuestInfo {
  adult_num: number
  room_num: number
  child_num: number
  age?: string
}

export interface GuestConfig {
  max?: number
  min?: number
}

export interface CalendarInfo {
  check_in?: string,
  check_out?: string,
  calendar_type?: number, // 1：calendar，2：flexible 0: 没有开启flexible
  flexible_date_list?: string // 灵活日期的月份yyyy-MM 多个以逗号分割
  flexible_day?: number | string, // 灵活日期的天数
  flexible_type?: number, // 灵活日期类型， 1：weekend, 2:day
}

export interface CalendarConfig {
  flexible?: boolean
  dailyPriceInfo?: { [key: string]: string}
  minDate?: number | Date | string
  maxDate?: number | Date | string
  minLos?: number
  maxLos?: number
  isSoldOut?: Function
  customRenderPrice?: Function
}


