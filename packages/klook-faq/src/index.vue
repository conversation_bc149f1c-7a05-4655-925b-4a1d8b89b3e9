<template>
    <div :class="`klk-faq klk-faq-${type}`">
        <div class="klk-faq-container">
            <div class="klk-faq-container-left">
                <div class="klk-faq-title">Common Questions</div>
                <div class="klk-faq-container-left--wrapper">
                    <klk-collapse :size="collapseSize" :accordion="!multiple">
                        <klk-collapse-item v-for="(item, index) in questions" :key="index" :title="item.question" :name="`${index}`">
                            <div class="klk-faq-answer">{{item.answer}}</div>
                        </klk-collapse-item>
                    </klk-collapse>
                    <a class="klk-faq-link" :href="href">Klook Help Center</a>
                </div>
            </div>
            <div class="klk-faq-container-right" v-if="image">
                <img :data-src="image" />
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: 'klk-faq',
    props: {
        multiple: { type: Boolean, defualt: false },
        questions: { type: Array, default:()=> [{ question: 'This is a problem', answer: 'This is answer' }] },
        href: { type: String, default: '' },
        type: { type: String, defualt: 'desktop', validator: value => ['mobile', 'desktop', 'image'].includes(value) },
        image: { type: String, default: '' }
    },
    computed: {
        collapseSize() {
            return this.type === 'mobile' ? 'small' : 'normal';
        }
    },
    mounted() {
        setTimeout(() => {
            const imgEl  = this.$el && this.$el.querySelector('img');
            if (!imgEl) {
                return ;
            }
            imgEl.src = imgEl.dataset.src;
        }, 500);
    }
}
</script>

<style lang="scss">
.klk-faq {
    position: relative;
    color: #333;

    .klk-faq-answer {
        background: #f5f5f5;
        line-height: 1.5;
    }

    .klk-faq-title {
        font-size: 24px;
        line-height: 30px;
        font-weight: bold;
    }

    &-desktop, &-image {

        .klk-faq-title {
            margin-bottom: 32px;
        }

        .klk-faq-container {
            display: flex;
            align-items: flex-start;

            &-left {
                flex: 1 1 0;
            }

            &-right {
                flex: 0 0 auto;
                margin-left: 40px;
                width: 380px;
                align-self: stretch;

                img {
                    width: 100%;
                    object-fit: cover;
                    border-radius: 4px;
                }
            }
        }

        .klk-faq-link {
            margin-top: 30px;
            padding: 0 40px;
            height: 40px;
            display: inline-flex;
            align-items: center;
            font-size: 16px;
            font-weight: bold;
            color: #666;
            border: 1px solid #999;
            border-radius: 2px;

            &:hover, &:visited, &:link, &:active {
                text-decoration: none;
            }

            &:hover {
                color: #333;
            }
        }

        .klk-faq-answer {
            padding: 24px;
            font-size: 14px;
        }
    }

    &-img {
        padding: 16px 16px 16px 40px;

        .klk-faq-title {
            margin-top: 24px;
        }
    }

    &-mobile {
        font-size: 14px;
        line-height: 1.3;

        .klk-faq-title {
            margin-bottom: 24px;
        }

        .klk-faq-container {

            &-left--wrapper {
                background: #fff;
                border-radius: 4px;
                padding: 0 20px;
            }
        }

        .klk-faq-answer {
            padding: 16px;
            font-size: 12px;
        }

        .klk-faq-link {
            padding: 24px 0;
            font-size: 14px;
            color: #4985e6;
            display: block;
            text-align: center;
        }

        .klk-collapse-item:first-child {
            border-top: none;
        }
    }
}
</style>

