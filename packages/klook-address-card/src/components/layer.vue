<template>
  <transition name="bottom-in">
    <div
      v-show="visible"
      class="page-layer">
      <div class="page-layer-content">
        <slot></slot>
      </div>
      <div
        v-if="$slots.footer"
        class="page-layer-footer"
        @click="$emit('footer-click')">
        <slot name="footer"></slot>
      </div>
    </div>
  </transition>
</template>

<script lang="ts">
import {
  Vue,
  Component,
  Prop,
  Model
} from 'vue-property-decorator';

@Component({
  name: "Layer",
  components: {},
})
export default class Layer extends Vue {
  @Model('visible', {
    type: Boolean,
    default: false
  }) visible!: boolean
  @Prop({
    type: String,
    default: ''
  }) title!: string
  @Prop({
    type: Boolean,
    default: true
  }) releaseBody!: boolean // 单例弹窗
  @Prop({
    type: String,
    default: ''
  }) navBtnText!: string
  @Prop({
    type: String,
    default: 'common#icon-close'
  }) iconName!: string
  @Prop({
    type: String,
    default: 'border'
  }) headerBottom!: string // border || shadow
  @Prop({
    type: Boolean,
    default: true
  }) needHeader!: boolean
  @Prop({
    type: String,
    default: 'BackBtn'
  }) BackBtnModule!: string

  close() {
    this.$emit('close', false)
  }
}
</script>

<style lang="scss" scoped>
// 向上淡入显示
.bottom-in-enter,
.bottom-in-leave-to {
  transform: translateY(100%);
}

.bottom-in-leave-active,
.bottom-in-enter-active {
  transition: all 0.3s ease-in;
}

.page-layer {
  color: #212121;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: #fff;
  z-index: 999999;
  flex-direction: column;
  display: flex;

  .flex-header {
    display: flex;
    position: relative;
    align-items: center;
    min-height: 48px;
  }
  .flex-auto {
    flex: 1 1 auto;
  }

  &-content {
    flex: 1 1 auto;
    min-height: 0;
    overflow-x: hidden;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
    transform: translate3d(0, 0, 0);
  }
}
</style>
