<template>
  <div class="klook-address-card-container" :data-spm-page="`CmptAddressCard?oid=booking_${bookingRefNo}`">
    <Layer :visible="visible" id="klook-address-card-modal">
      <div class="klook-address-card-main">
        <div class="klook-address-card-content">
          <div class="address-name" v-if="addressNameOrder">{{ addressNameOrder }}
            <IconCopy v-if="showCopy" class="icon-copy" @click.native="copy(addressNameOrder)" theme="outline" size="20" fill="#757575" />
          </div>
          <div class="address-detail"><span v-if="addressDetailOrder">{{ addressDetailOrder }}</span></div>
          <div class="address-destination" v-if="addressNameDestination">{{ addressNameDestination }}
            <IconCopy v-if="showCopy" class="icon-copy" @click.native="copy(addressNameDestination)" theme="outline" size="20"
              fill="#757575" />
          </div>
          <div class="address-detail-destination">
            <span v-if="addressDetailDestination">{{ addressDetailDestination }}</span>
          </div>

          <klk-icon @click="close" class="close-btn" type="icon_navigation_close_m" size="24"></klk-icon>
          <klk-button v-if="showSaveButton" @click.native.stop.prevent="downloadElementAsPic" :data-spm-module="`Save`"
            data-spm-virtual-item="__virtual" class="save-btn" type="outlined" data-html2canvas-ignore>{{ __t('109038')
            }}</klk-button>
        </div>
      </div>
    </Layer>
  </div>
</template>

<script lang="ts">
import {
  Vue,
  Component,
  Prop,
  Provide
} from 'vue-property-decorator'
import Layer from './components/layer.vue';
import KlkButton from '@klook/klook-ui/lib/button/index.js'
import KlkIcon from '@klook/klook-ui/lib/icon/index.js'
import { IconCopy } from '@klook/klook-icons';
import { genText } from "./locale/index";
import messages from "../locales/index.js";

@Component({
  name: "AddressCard",
  components: {
    KlkButton,
    KlkIcon,
    Layer,
    IconCopy
  }
})
export default class AddressCard extends Vue {
  // start from here
  @Prop({
    default: false
  }) visible!: boolean;

  @Prop({
    default: true
  }) showCopy!: boolean;

  @Prop({
    default: true
  }) showSaveButton!: boolean;

  @Prop()
  bookingRefNo!: string;

  @Prop({
    default: ''
  }) addressNameOrder!: string;

  @Prop({
    default: ''
  }) addressDetailOrder!: string;

  @Prop({
    default: ''
  }) addressNameDestination!: string;

  @Prop({
    default: ''
  }) addressDetailDestination!: string;

  @Provide() __t: any = this.getTranslate()

  getTranslate() {
    return this.__t;
  }

  beforeCreate(this: any) {
    const locales = messages as any;
    const lang = this.$attrs.language || 'en';
    this.__t = locales[lang]
      ? genText(locales[lang])
      : genText(locales["en"]);
  }

  copy(text: string) {
    if (navigator.clipboard) {
      // clipboard api 复制
      navigator.clipboard.writeText(text);
    } else {
      var textarea = document.createElement('textarea');
      document.body.appendChild(textarea);
      // 隐藏此输入框
      textarea.style.position = 'fixed';
      textarea.style.clip = 'rect(0 0 0 0)';
      textarea.style.top = '10px';
      // 赋值
      textarea.value = text;
      // 选中
      textarea.select();
      // 复制
      document.execCommand('copy', true);
      // 移除输入框
      document.body.removeChild(textarea);
    }

    this?.$toast(this.__t('109149'))
  }

  close() {
    this.$emit('update:visible', false);
    this.$emit('close');
  }

  downloadElementAsPic() {
    const html2canvas = require('html2canvas');
    const htmlElement = document.querySelector('#klook-address-card-modal') as HTMLElement;
    const imgWidth = htmlElement.offsetWidth
    const imgHeight = htmlElement.offsetHeight
    const scale = window.devicePixelRatio || 1 // 获取设备像素比

    const opt = Object.assign({
      useCORS: true,
      scale,
      width: imgWidth,
      height: imgHeight
    })

    html2canvas(htmlElement, opt).then((canvas: any) => {
      const dataurl = canvas.toDataURL('image/png')
      const link = document.createElement('a')
      link.setAttribute('href', dataurl)
      link.setAttribute('download', 'address card' + '.png')
      link.style.visibility = 'hidden'
      document.body.appendChild(link)
      link.click();
      (link.parentNode as HTMLElement).removeChild(link)

    });
  }
}
</script>

<style lang="scss">
@mixin text-ellipsis($line-clamp: 2) {
  display: -webkit-box;
  -webkit-line-clamp: $line-clamp;
  -webkit-box-orient: vertical;
  text-overflow: ellipsis;
  overflow: hidden;
}

@mixin name() {
  font-size: 16px;
  color: #757575;

  svg,
  img {
    cursor: pointer;
    vertical-align: middle;
  }
}

@mixin detail() {
  @include text-ellipsis(2);
  color: #212121;
  font-size: 32px;
  font-weight: 600;
  line-height: 1.3;
  max-height: 90px;
  overflow: hidden;
}

#klook-address-card-modal {
  .klk-modal {
    margin: 0 !important;
    padding: 0 !important;

    &-body {
      width: 100%;
      height: 100%;
    }
  }

  .klook-address-card-main {
    background: url('https://res.klook.com/image/upload/v1697712760/bg_c8ucbx.jpg') no-repeat;
    background-size: 100% 100%;
    background-color: #fff;
    padding: 32px;
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    top: 0;
    width: 100vh;
    height: 100vw;
    transform: rotate(90deg) translate3d(0, -100%, 0);
    transform-origin: top left;
  }

  .klook-address-card-content {
    height: 100%;
    background-color: #fff;
    border-radius: 20px;
    padding: 24px;

    .address-name {
      @include name;
    }

    .icon-copy {
      vertical-align: middle;
      margin-left: 4px;
    }

    .address-detail {
      @include detail;
      padding-right: 60px;
      margin-bottom: 54px;
    }

    .address-destination {
      @include name;
    }

    .address-detail-destination {
      @include detail;
      padding-right: 100px;
    }

    .close-btn {
      cursor: pointer;
      position: absolute;
      right: 56px;
      top: 56px;
    }

    .save-btn {
      position: absolute;
      right: 56px;
      bottom: 56px;
    }
  }
}
</style>
