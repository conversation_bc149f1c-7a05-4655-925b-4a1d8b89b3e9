export type CommonTag = {
  [index: string]: any,
  renderer?: Function, // (h, CommonTag) => {}
  desc?: string
}
export type CommonTagV2 = {
  [index: string]: any,
  renderer?: Function, // (h, CommonTag) => {}
  desc?: string
  name?: string
  type?: string
  text_color: string
  bg_color: string
  border_color?: string
  icon?: string
  time_left?: string
}
export interface CardInfo {
  titleTag: CommonTag | string,
  imgList: string[], // 卡片图集
  ratingTag?: CommonTag,
  basicTag?: CommonTag | string, // 卡片基本信息tag, 最多一行，多余内容...隐藏
  descTag?: CommonTag | string, // 一些描述信息tag, 位于基本信息tag下方，标题上方
  additionTag?: CommonTag | string, // 位于标题下方的 辅助信息tag, 最多一行
  purchaseTags?: CommonTag[], // 位于辅助信息下方的 购买信息tag列表，最多一行
  marketPrice?: CommonTag, // 市场价
  sellingPrice: CommonTag, // 售价
  priceTag?: CommonTag, // 价格相关tag, 和售价在同一行
  srvTag? : CommonTag, // 默认slot
  topTags?: CommonTag[], // 位于图片左上角的tag列表
  status: number // 卡片状态； 正常 = 1 售罄 = 0
  checkInInfo?: CommonTag,  // 日期提示信息
  vipLoginTip?: CommonTag // vip 登陆提示，1.2.16新增
}

export type HotelInfo = {
  hotel_id: string
  name: string
  name_en?: string
  image_list: string[]
  name_icon: string
  city_id: string
  city_name: string
  country_id: string
  longitude: string
  latitude: string
  star: number
  country_name: string
  stay_desc: string  // 住宿星级描述
  location_desc: string // 地址位置描述
  rating: {              // 评分信息
    avg_score: string    // 平均分
    max_score: string    // 最大分
    score_desc: string   // 评分描述
  },
}

export type PromotionInfo = {
  icon: string
  name: string
  text_color: string
  bg_color: string
  border_color: string
  promotion: {
    title: string
    desc: string
    original_per_night_price: string  // 单晚划线价
    original_price_desc: string // 划线价描述
    per_night_price: string // 单晚售卖价
    price_desc: string // 售卖价价描述
    currency_symbol: string // 币种符号
    discount: {
      price: string // 折扣总金额
      currency_symbol: string
      sign: string // 标识符， -
      discount_list: Array<{ tag: CommonTagV2, price_desc: string }>
    }, // 折扣信息
    gift: {
      gift_list: CommonTagV2[]
    }, // 赠品信息
    tip: CommonTagV2 // 价格库存提醒
  }
}

export type Pricing = {
  price: string  // 售卖价
  currency_symbol: string // 币种符号
  format_price: string // 格式化价格
  price_desc: string // 价格描述，From {format_price}
  original_price_desc: string // 划线价描述
  price_tag_list: CommonTagV2[] // 价格标签列表
  price_tags?: CommonTagV2[]
  promotion_info: PromotionInfo // 促销信息
  check_in_info: {
    check_in_tip: string
    text_color: string
  } // 特殊los 入住信息
  tax_desc?: string
  discount_type?: 'vip' | 'login' | 'promotion'
  has_stock: boolean
}
export interface CardInfoV2 {
  campaign_tag?: CommonTagV2, // campaign tag, 只有一个
  operation_tags?: CommonTagV2[], // 运营标签位
  top_tags?: CommonTagV2[], // 运营标签位
  hotel_info: HotelInfo     // 酒店信息
  promotion_tag_list?: CommonTagV2[], // 促销标签列表
  feature_tag_list?: CommonTagV2[], // 特色标签列表
  feature_tags?: CommonTagV2[], // 特色标签列表
  promotion_tags?: CommonTagV2[]
  pricing: Pricing, // 位于辅助信息下方的 购买信息tag列表，最多一行
  status?: number // 卡片状态； 正常 = 1 售罄 = 0
  deep_link: string
  report?: any
}
