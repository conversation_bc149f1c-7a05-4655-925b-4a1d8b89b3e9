import { CommonTagV2 } from "./types"

export type CommonTag = {
  [index: string]: any,
  renderer?: Function, // (h, CommonTag) => {}
  text: string
  type?: string
  text_color: string
  bg_color: string
  border_color?: string
  icon?: string
  time_left?: string
}

export type HotelInfo = {
  desc?: string
  hotel_id: string
  name: string
  local_name?:string
  image_list: string[]
  city_id?: string
  city_name?: string
  country_id?: string
  country_name?: string
  star: number // 星级
  avg_score: string    // 平均分
  max_score: string    // 最大分
  score_desc: string   // 评分描述
  review_num_desc?: string // 评论数
  latitude: string
  longitude: string
  location?: {
    icon?: string
    desc: string
  }
}

export type Pricing = {
  price?: string  // 售卖价
  post_format_tax_price?: string  // 税后价
  currency_symbol: string // 币种符号
  format_price: string // 格式化价格
  original_price_desc?: string // 划线价描述
  tax_desc?: string // 税费描述
  reward_desc?: string // 积分描述
  price_tag_list?: CommonTagV2[] // 价格标签列表
  stock_tag?: CommonTagV2
  price_tags?: CommonTagV2[]
  has_stock: boolean
  check_in_desc?: string
  discount_type?: 'vip' | 'login' | 'promotion' // VIP:vip, 限时优惠:promotion, 引导登录:login
}
export interface CardInfoV2 {
  campaign_tag?: CommonTagV2
  wish?: boolean
  deep_link: string
  book_tag?: CommonTagV2, // 预定信息    
  hotel_info: HotelInfo     // 酒店信息
  feature_tag_list?: CommonTagV2[], // 特色、运营标签列表
  feature_tags?: CommonTagV2[]
  stock_tag?: CommonTagV2[] // 剩余几间房
  promotion_tag_list?: CommonTagV2[], // 促销标签列表
  promotion_tags?: CommonTagV2[]
  pricing: Pricing, // 位于辅助信息下方的 购买信息tag列表，最多一行
  stay_plus_list?: {
    icon?: string
    name: string
    desc: string
  }[],
  promo_tag_list?: CommonTagV2[], // v3的促销标签列表
  report?: any
}


export type CardSection = 'viewMap' | 'favorite' | 'poi'

export type CardScene = 'mapNearby' | 'mapList' | 'voucher' | 'listHorizontal' | 'listVertical' | ''

export type CardType = 'small' | 'normal' | 'long'

export type CardLayout = 'horizontal' | 'vertical' | ''



