{"name": "@klook/hotel-card", "version": "2.2.20", "description": "A Common Component based on Vue of Klook", "author": "xiaohai.liu", "homepage": "https://design.klook.io", "keywords": ["vue", "component", "ui", "framework"], "main": "dist/cjs/index.js", "module": "dist/esm/index.js", "files": ["dist", "types"], "typings": "types/index.d.ts", "license": "UNLICENSED", "publishConfig": {"registry": "https://knpm.klook.io", "access": "public"}, "scripts": {"build": "NODE_ENV=production rollup --config ./rollup.config.js", "lint": "NODE_ENV=production eslint --ext .js,.vue src", "prepush": "yarn run lint", "prepublishOnly": "bash prepublishOnly.sh", "commit": "npx git-cz", "commitmsg": "commitlint -E GIT_PARAMS"}, "devDependencies": {"@babel/core": "^7.17.9", "@babel/plugin-transform-runtime": "^7.17.0", "@babel/preset-env": "^7.16.11", "@klook/image": "^0.0.14", "@klook/klook-ui": "^1.38.7", "@types/jest": "^26.0.0", "@types/webpack-env": "^1.14.0", "@vue/test-utils": "^1.0.0-beta.32", "autoprefixer": "^9.8.6", "jest": "^25.5.4", "rimraf": "^3.0.0", "rollup": "^2.70.1", "rollup-plugin-babel": "^4.4.0", "rollup-plugin-buble": "^0.19.8", "rollup-plugin-clear": "^2.0.7", "rollup-plugin-commonjs": "^10.0.2", "rollup-plugin-css-only": "^3.1.0", "rollup-plugin-node-resolve": "^5.2.0", "rollup-plugin-replace": "^2.2.0", "rollup-plugin-scss": "^3.0.0", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-typescript2": "^0.31.2", "rollup-plugin-vue": "^5.1.9", "terser": "^4.1.3", "ts-jest": "^26.1.0", "ts-node": "^8.10.2", "typescript": "^4.6.3", "vue": "2.6.11", "vue-jest": "^3.0.4", "vue-property-decorator": "^8.3.0", "@klook/galileo-vue": "^1.2.14", "@klook/hotel-promotion-tag": "^0.2.0-beta.4", "@klook/klook-scroll-snap": "0.0.3", "vue-template-compiler": "2.6.11", "@klook/klook-card": "^0.4.18", "@klook/klook-traveller-login": "^2.0.0", "postcss": "^8.4.13", "postcss-loader": "^6.2.1", "rollup-plugin-postcss": "^4.0.1"}, "peerDependencies": {"@klook/galileo-vue": "^1.2.14", "@klook/hotel-promotion-tag": "0.2.0-beta.4", "@klook/klook-scroll-snap": "0.0.3", "@klook/image": "^0.0.14", "@klook/klook-ui": "^1.38.7", "vue": "2.6.11", "@klook/klook-card": "^0.4.18", "@klook/klook-traveller-login": "^2.0.0"}}