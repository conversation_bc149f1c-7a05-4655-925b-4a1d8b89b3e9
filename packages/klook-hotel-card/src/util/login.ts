/**
 * @function Vue.$login
 * @param onSuccess: onSuccess?: boolean | Function
     - true: 刷新当前页面
     - false | undefined: 什么也不做
    - Function: 自主登录成功的回调函数
 * @param onFail?: boolean | Function
    - true: 跳转到登录页
    - false | undefined: 什么也不做
    - Function: 自主登录失败的回调函数
 * @returns boolean
 */

export default async function loginWithSDK(
  vm: any,
  onSuccess?: boolean | Function,
  onFail?: boolean | Function
): Promise<boolean> {
  if(!vm.$store?.state) {
    return false
  }
  const { auth: { isLoggedIn }, klook } = vm.$store?.state

  if (!isLoggedIn) {
    try {
      const {
        platform,
        platformMp,
        language,
        currency
      } = klook

      if (platformMp) {
        onFail = true
        throw new Error('unable login')
      }

      const { loginWithSDK: login } = await import('@klook/klook-traveller-login')

      await new Promise((resolve, reject) => {
        login({
          aid: vm.$cookies?.get('aid'),
          isMP: false,
          isKlookApp: false,
          platform,
          language,
          currency,
          bizName: 'HOTEL',
          purpose: 'Booking',

          // @ts-ignore
          market: klook.market,
          cancel: reject,
          // fail: reject, // @baoyi 2024-01-24  暂时不关注fail 的情况
          success: resolve
        }).then((res: Boolean) => {
          if (!res) {
            return reject(new Error('no support login with SDK'))
          }
        })
      })

      const isFn = typeof onSuccess === 'function'
      if (!isFn && onSuccess) {
        // 已经要刷新了 不应该再往下继续操作了
        setTimeout(vm.$router.go, 100)
        return false
      }

      await vm.$store?.dispatch('auth/getProfile')
      // @ts-ignore
      isFn && onSuccess()
    } catch (err) {
      typeof onFail === 'function'
        ? onFail()
        : window.location.href = vm.$href('/signin')
      return false
    }
  }

  return true
}
