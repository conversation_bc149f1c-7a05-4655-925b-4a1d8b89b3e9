import { CardInfo, CardInfoV2 } from '../types/types'
import { CardSection, CardScene, CardType, CardInfoV2 as CardInfoV3  } from '../types/types-v2'

type Vector = {
  x: number,
  y: number
}

type Handlers = {
  [index: string]: any[]
}

type SwiperOptions = {
  containerEl: HTMLElement,
  wrapperEl: HTMLElement,
  platform: string,
  leftArrowEl?: HTMLElement,
  rightArrowEl?: HTMLElement,
  onChange?: Function
}

/**
 * Touch手势操作
 */
class SwiperTouch {
  _el!: HTMLElement

  v1!: Vector
  v2!: Vector
  _handlers: Handlers = {}
  _swiperTimeout!: any
  deltaV!: Vector
  last!: number

  _startHandler!: any
  _moveHandler!: any
  _endHandler!: any
  _cancelHandler!: any

  constructor(el: HTMLElement | string) {
    const _el = typeof el === 'string' ? document.querySelector(el) : el
    if (!_el) {
      return
    }
    this._el = _el as HTMLElement

    this._startHandler = this.onTouchStart.bind(this)
    this._moveHandler = this.onTouchMove.bind(this)
    this._endHandler = this.onTouchEnd.bind(this)
    this._cancelHandler = this.onTouchCancel.bind(this)

    this._el.addEventListener('touchstart', this._startHandler, true)
    this._el.addEventListener('touchmove', this._moveHandler, false)
    this._el.addEventListener('touchend', this._endHandler, true)
    this._el.addEventListener('touchcancel', this._cancelHandler, true)
  }

  onTouchStart(e: TouchEvent) {
    if (!e.touches) {
      return
    }
    this.v1 = { x: e.touches[0].pageX, y: e.touches[0].pageY }
    this.last = Date.now()
  }

  onTouchMove(e: TouchEvent) {
    if (!e.touches) {
      return
    }
    this.v2 = { x: e.touches[0].pageX, y: e.touches[0].pageY }
    this.deltaV = { x: this.v2.x - this.v1.x, y: this.v2.y - this.v1.y }

    if (Math.abs(this.deltaV.x) > Math.abs(this.deltaV.y)) {
      this.$emit('move', { delta: this.deltaV })
      e.stopPropagation()
      e.preventDefault()
    }
  }

  onTouchEnd(e: TouchEvent) {
    if (!e.changedTouches || !this.v2) {
      return
    }

    const xDiff = Math.abs(this.v1.x - this.v2.x)
    const yDiff = Math.abs(this.v1.y - this.v2.y)

    if (xDiff > 0 && xDiff > yDiff) {
      const direction = this.v1.x - this.v2.x > 0 ? 'Left' : 'Right'
      this._swiperTimeout = setTimeout(() => {
        this.$emit('swipe', { direction, delta: this.deltaV, timeDiff: Date.now() - this.last })
      }, 0)
    }
    this.v1 = this.v2 = { x: 0, y: 0 }
  }

  onTouchCancel() {
    clearTimeout(this._swiperTimeout)
  }

  $on(type: string, handler: Function) {
    if (!this._handlers[type]) {
      this._handlers[type] = [handler]
    }
  }

  $emit(type: string, params: any) {
    (this._handlers[type] || []).forEach((_fn: Function) => _fn(params))
  }

  destroy() {
    if (!this._el) {
      return
    }
    // @ts-ignore
    ['start', 'move', 'end', 'cancel'].forEach((name: string) => this._el.removeEventListener(`touch${name}`, this[`_${name}Handler`]))

    // @ts-ignore
    this._el = null

    this._handlers = {}
  }
}

/**
 * 前提 warpperEl: transfrom: translate3d(-100%, 0px, 0px)
 * @param options
 */
export const initSwiper = (options: SwiperOptions) => {
  if (options.wrapperEl.children.length < 2) {
    return () => {}
  }

  let width = 0
  const onResize = () => {
    const el = options.containerEl
    // hack在poptip等开始不可见情况下的处理
    if (!el.offsetWidth) {
      width = 0
      return
    }
    width = parseFloat(window.getComputedStyle(options.containerEl).getPropertyValue('width'))
  }

  onResize()

  window.addEventListener('resize', onResize)
  window.addEventListener('orientationchange', onResize)

  let touchIntance: SwiperTouch
  let translateX = -width
  let slideLen = options.wrapperEl.children.length

  const transformTo = (diff: number, delta: Vector, isTransition: boolean = false) => {
    if(!width) {
      onResize()
      translateX = -width
    }
    
    let xStartValue = translateX

    if (Math.abs(translateX) === 0 && delta.x > 0) {
      xStartValue = -(slideLen - 2) * width
    } else if (Math.abs(translateX) === (slideLen - 1) * width && delta.x < 0) {
      xStartValue = -width
    }

    Object.assign(options.wrapperEl.style, {
      transitionDuration: isTransition ? '300ms' : '0ms',
      transform: `translate3d(${xStartValue + diff}px, 0px, 0px)`
    })
    return xStartValue + diff
  }

  const updatePagination = () => {
    const curIndex = Math.floor(Math.abs(translateX) / width)
    const realIndex = curIndex === 0 ? slideLen - 2 : (curIndex === slideLen - 1 ? 1 : curIndex)

    typeof options.onChange === 'function' && options.onChange(realIndex)

    const imgVueInstances = Array.from(options.wrapperEl.querySelectorAll('img')).map((el: any) => el.__vue__)

    ;[imgVueInstances[curIndex - 1], imgVueInstances[curIndex + 1]].forEach((instance: any) => {
      instance && typeof instance.preload === 'function' && instance.preload()
    })
  }

  if (options.platform === 'mobile') {
    touchIntance = new SwiperTouch(options.containerEl)
    touchIntance.$on('move', (params: any) => {
      transformTo(params.delta.x, params.delta)
    })
    touchIntance.$on('swipe', (params: any) => {
      const { direction, delta } = params

      if (Math.abs(delta.x * 3) < width && params.timeDiff > 250) {
        transformTo(0, params.delta, true)
        return
      }
      translateX = transformTo((direction === 'Left' ? -1 : 1) * width, delta, true)

      updatePagination()
    })
  } else if (options.leftArrowEl && options.rightArrowEl) {
    const outContainer = options.containerEl.parentElement as HTMLElement

    outContainer.addEventListener('mouseenter', () => {
      // @ts-ignore
      options.leftArrowEl.style.display = options.rightArrowEl.style.display = 'flex'
    }, true)
    outContainer.addEventListener('mouseleave', () => {
      // @ts-ignore
      options.leftArrowEl.style.display = options.rightArrowEl.style.display = 'none'
    })
    options.leftArrowEl.addEventListener('click', (e: MouseEvent) => {
      transformTo(0, { x: 1, y: 0 })
      setTimeout(() => {
        translateX = transformTo(width, { x: 1, y: 0 }, true)
        updatePagination()
      }, 0)
      e.stopPropagation()
      e.preventDefault()
    }, true)
    options.rightArrowEl.addEventListener('click', (e: MouseEvent) => {
      transformTo(0, { x: -1, y: 0 })
      setTimeout(() => {
        translateX = transformTo(-width, { x: -1, y: 0 }, true)
        updatePagination()
      }, 0)
      e.stopPropagation()
      e.preventDefault()
    }, true)
  }

  return () => {
    touchIntance && touchIntance.destroy()
    window.removeEventListener('resize', onResize)
    window.removeEventListener('orientationchange', onResize)
  }
}

export const adjustEl = (container: HTMLElement, cardType: string) => {
  const purchaseTag = container.querySelector('.content-purchase') as HTMLElement
  const isNormalCard = ['normal', 'small'].includes(cardType) // cardType: normal, small, long

  if (isNormalCard && purchaseTag && purchaseTag.children.length) {
    const containerWidth = purchaseTag.offsetWidth
    const marginRight = parseInt(window.getComputedStyle(purchaseTag.children[0]).marginRight || '0')
    const elWidths = Array.from(purchaseTag.children).map(el => el.scrollWidth)
    let _index = purchaseTag.children.length - 1
    elWidths.reduce((acc: number, cur: number, index:number) => {
      if (acc + cur + marginRight + 20 > containerWidth) {
        _index = index
        return -99999
      }
      return acc + cur + marginRight
    })
    purchaseTag.children[_index].classList.add('card-overflow')
    Array.from({ length: purchaseTag.children.length - _index - 1 }, (_i, index) => _index + index + 1).forEach(i => {
      (purchaseTag.children[i] as HTMLElement).style.display = 'none'
    })
  }

  const sellingPrice = container.querySelector('.content-selling-price') as HTMLElement
  const priceTag = container.querySelector('.content-price-tag') as HTMLElement
  const marketPrice = container.querySelector('.content-market-price') as HTMLElement
  if (isNormalCard || !cardType) {
    if (sellingPrice && priceTag && sellingPrice.scrollWidth + priceTag.scrollWidth + 6 > (sellingPrice.parentElement as HTMLElement).offsetWidth) {
      if (marketPrice) {
        marketPrice.appendChild(priceTag)
        sellingPrice.style.overflow = 'hidden'
      } else {
        // @ts-ignore
        priceTag.parentElement.parentElement.insertBefore(priceTag, priceTag.parentElement)
        Object.assign(priceTag.style, { marginLeft: 0 })
        sellingPrice.style.overflow = 'initial'
      }
    }
  } else {
    if (priceTag && marketPrice && marketPrice.scrollWidth + priceTag.scrollWidth + 6 > (marketPrice.parentElement as HTMLElement).offsetWidth) {
      // @ts-ignore
      priceTag.parentElement.parentElement.insertBefore(priceTag, priceTag.parentElement)
      // @ts-ignore
      marketPrice.parentElement.classList.remove('content-price')
      // @ts-ignore
      marketPrice.parentElement.style.marginTop = '2px'
    }
  }
}

// https://www.notion.so/b88a91f9e8e443f7814ff4c1d12e75f2
export const transformData = (cardInfo: any, cardType?: string) => {
  const { image_list, card_info, card_tags, stock_info, sell_price, market_price, voucher_info, rating_info } = cardInfo || {}
  if (!card_info) {
    return undefined
  }

  const ret: CardInfo = {} as CardInfo
  const cardTags = card_tags || {}

  ret.imgList = typeof image_list === 'string' ? [image_list] : image_list || []
  ret.titleTag = voucher_info ? { desc: voucher_info.name } : { desc: card_info.name, icon: card_info.name_icon }

  // 1.2.1 调整 small卡片
  ret.ratingTag = {...(rating_info || {}), ...(cardType === 'small' ? { score_desc: '' } : {})}
  ret.basicTag = card_info.stay_desc
  ret.descTag = card_info.sub_name
  ret.purchaseTags = cardTags.tag_middle || []
  ret.additionTag = card_info.desc
  ret.status = stock_info && stock_info.has_stock ? 1 : 0
  ret.marketPrice = market_price
  ret.sellingPrice = sell_price

  const [priceTag] = cardTags.tag_bottom || []
  if(priceTag?.type && [1, 3].includes(+priceTag.type)) {
    ret.priceTag  = {
      id: card_info.id,
      ...priceTag
    }
  }

  const topTags = Array.isArray(cardTags.tag_top) ? cardTags.tag_top : []

  // flexible date 提示信息
  if (cardType !==  'small') {
    const checkInInfo = cardInfo.check_in_info
    ret.checkInInfo = checkInInfo ? {
      desc: checkInInfo.check_in_tip,
      text_color: checkInInfo.text_color
    } : undefined

    // 1.2.16 新增会员价登陆提示字段
    ret.vipLoginTip = sell_price?.vip_login_tips ? { desc: sell_price.vip_login_tips, text_color: '#999999' } : undefined
  }

  if (cardType === 'long') {
    return ret
  }

  // 1.2.0 新增
  ret.topTags = topTags

  // 小卡片，time tag优先展示
  if (cardType ===  'small') {
    const timeTag = topTags.find((i: any) => i.type === 'promotion_timer')
    ret.topTags = timeTag ? [timeTag] : topTags

    ret.priceTag = undefined
    ret.imgList = ret.imgList.length ? [ret.imgList[0]] : []
  }

  return ret
}

/**
 * 旧版  =》 新版
  top_tags 倒计时 => promotion_tags 
  promotion_tags（预定人数等） => promotion_tags 
  feature_tags => feature_tags
  price_tags + promotionInfo => price_tags
  stay plus => 不要
 */
export const formatCardData = (cardInfo: CardInfoV2): CardInfoV3 => {
  const { top_tags, operation_tags, hotel_info, feature_tags, promotion_tags, pricing, status, deep_link, report } =  cardInfo
  let ret = {} as CardInfoV3

  const { hotel_id, name, image_list, location_desc, rating, longitude, latitude, star, name_en: local_name } = hotel_info

  const { format_price, currency_symbol, tax_desc, price_tags, discount_type, check_in_info, has_stock, promotion_info} = pricing
  ret = {
    hotel_info: {
      hotel_id, name, image_list, longitude, latitude, star, local_name, location: {
        desc: location_desc
      },...(rating || {})},
    feature_tags,
    promotion_tags: (top_tags || []).concat(promotion_tags || []),
    pricing: {
      format_price,
      currency_symbol,
      tax_desc,
      price_tags: (price_tags || []).concat(promotion_info ? {
        bg_color: '#FFF0E5',
        text_color: '#FF5B00',
        name :promotion_info.name
      }: []),
      has_stock,
      check_in_desc: check_in_info?.check_in_tip,
      discount_type
    },
    deep_link,
    report
  }
  return ret
}

const removeUrlHost = (url: string) => {
  return url.replace(/^https?:\/\/[^\/]+(.*)$/, '$1')
}

/**
 * 返回不带search参数的url
 * @param url 
 * @returns 
 */
export const cleanUrl = (url: string) => {
  if (typeof url !== 'string') {
    return url
  }

  return removeUrlHost(url).replace(/\?.*$/s, '')
}

export const goUrl = (url: string, target: string) => {
  const a = document.createElement('a')
  a.href = removeUrlHost(url)
  a.target = target
  document.body.appendChild(a)
  a.click()

  setTimeout(() => document.body.removeChild(a), 500)
}

let isDlpPage: undefined | boolean = undefined
export const isDlp = () => {
  if (isDlpPage !== undefined) {
    return isDlpPage
  }
  isDlpPage = /\/hotels\/city\//.test(location.pathname) || /\/destination\//.test(location.pathname)
  return isDlpPage
}

export const adjustElV2 = (container: HTMLElement, isMobile: boolean, cardType: CardType, scene: CardScene) => {
  const priceInfo = container.querySelector('.price-info') as HTMLElement
  const priceSale = container.querySelector('.price-sale') as HTMLElement
  const taxDesc = container.querySelector('.tax-desc') as HTMLElement
  const priceOrigin = container.querySelector('.price-origin') as HTMLElement
  // 价格部分的是有如果税费不够就放在卖价下面, （只有上图下文scene === 'listVertical'）其他情况都是独立一行，
  // 然后原价不够就放在卖价上面
  if (priceInfo) {
    const priceInfoWidth = priceInfo.offsetWidth
    const priceSaleWidth = priceSale?.offsetWidth || 0
    const taxDescWidth = taxDesc?.offsetWidth || 0
    const priceOriginWidth = priceOrigin?.offsetWidth || 0
    if (scene === 'listVertical' && taxDescWidth && taxDescWidth + priceOriginWidth + 8 + 2 + priceSaleWidth > priceInfoWidth) {
      taxDesc.style.marginRight = '0'
    }
    if (priceOriginWidth && priceOriginWidth + 2 + priceSaleWidth > priceInfoWidth) {
      priceOrigin.style.marginRight = '0'
    }
  }

  // 对于已经一行的（但不是最后一个）的进行左右margin隐藏
}


export const hideInfoSet = (card: CardInfoV3) => {
  return {
    imageList: () => {
      const imageList = card.hotel_info.image_list
      card.hotel_info.image_list = imageList.slice(0, 1)
    },
    reviewNumDesc: () => {
      card.hotel_info.review_num_desc = undefined
    },
    nameEn: () => {
      card.hotel_info.local_name = undefined
    },
    location: () => {
      card.hotel_info.location = undefined
    },
    stayPlusList: () => {
      card.stay_plus_list = undefined
    },
    featureTags: () => {
      card.feature_tag_list = undefined
    },
    promotionTags: () => {
      card.promotion_tag_list = undefined
      card.promo_tag_list = undefined
    },
    bookingTag: () => {
      card.book_tag = undefined
    },
    priceTags: () => {
      card.pricing.price_tag_list = undefined
    },
    taxDesc: () => {
      card.pricing.tax_desc = ''
    },
    rewardDesc: () => {
      card.pricing.reward_desc = ''
    },
    originalPriceDesc: () => {
      card.pricing.original_price_desc = ''
    },
    checkInDesc: () => {
      card.pricing.check_in_desc = ''
    },
    stockDesc: () => {
      card.stock_tag = undefined
    },
    discountType: () => {
      card.pricing.discount_type = undefined
    },
    campaignTag: () => {
      card.campaign_tag = undefined
    }
  }
}
