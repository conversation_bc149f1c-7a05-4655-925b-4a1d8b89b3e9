<script lang="tsx">
import { Component, Prop, Watch } from "vue-property-decorator";
import render from "./render";

@Component({})
export default class CardV3 extends render {}
</script>

<style lang="scss" scoped>
// 文本溢出隐藏
@mixin text-ellipsis($lines: 1) {
  @if $lines > 1 {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: $lines;
    -webkit-box-orient: vertical;
  } @else {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}
.hotel-card {
  border-radius: $radius-xl;
  background-color: #fff;
  position: relative;
  line-height: 1.32;
  font-size: 14px;
  display: flex;
  flex-direction: column;
  color: #212121;
  cursor: pointer;

  &:hover,
  &:link,
  &:active,
  &:visited {
    text-decoration: none;
  }

  .hotel-card-header {
    position: relative;

    flex-shrink: 0;
    .hotel-image-swipe {
      z-index: 0;
      overflow: hidden;
      width: 100%;
      padding-bottom: 50%;
      height: 0;
      border-radius: $radius-l $radius-l 0 0;
      height: 0;
      position: relative;
    }

    .header-img-container {
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      .header-img-wrapper {
        font-size: 0;
        display: flex;
        align-items: center;
        height: 100%;
        transition: transform 0ms ease;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
          flex-shrink: 0;
        }
      }
    }

    .image-pagination {
      position: absolute;
      right: 12px;
      bottom: 12px;
      z-index: 999;
      border-radius: $radius-l;
      padding: 1px 8px;
      background-color: rgba(0, 0, 0, 0.6);
      @include font-caption-m-semibold;
      color: $color-text-reverse;
      z-index: 1;
    }

    .header-tag {
      position: absolute;
      top: 12px;
      left: -5px;
      z-index: 1;
      max-width: 50%;
      & > span {
        @include text-ellipsis;
      }
    }

    .hotel-add2favor {
      position: absolute;
      right: 8px;
      top: 8px;
      z-index: 1;
      width: 24px;
      height: 24px;
    }
    .hotel-stayplus-tag {
        position: absolute;
        left: 8px;
        top: 10px;
        z-index: 1;
        border-radius: 2px;
        padding: 0 4px;
        background-color: #F1EEFA;
        display: flex;
        align-items: center;
        img {
          width: 48px;
        }
      }
  }

  .hotel-card-main {
    padding: 12px;
    min-height: 180px;
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }

  .hotel-info {
    .hotel-info-name {
      margin-bottom: 4px;
      .hotel-name-section {
        display: flex;
        justify-content: space-between;
        .hotel-right-icon {
          margin-left: 8px;
          flex-shrink: 0;
          width: 32px;
          height: 32px;
          background-color: #F5F5F5;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          svg {
            width: 20px;
            height: 20px;
          }
        }
      }
      .hotel-name {
        color: $color-text-primary;
        ::v-deep h3, a {
            @include font-heading-xxxs;
            color: $color-text-primary;
            display: inline;
            cursor: pointer;
        }
        .prefix {
          &::after {
            width: 6px;
            height: 1px;
            content: "";
            display: inline-block;
          }
        }

        ::v-deep .text {
          &::after {
            width: 6px;
            height: 1px;
            content: "";
            display: inline-block;
          }
        }

        ::v-deep .ellipsis {
          &::after {
            width: 6px;
            height: 1px;
            content: "";
            display: inline-block;
          }
        }
        &.has-ellipsis {
          ::v-deep .text {
            &::after {
              width: 0 !important;
            }
          }
        }
        &.has-english {
          // line-height: 1.2;
          ::v-deep .prefix {
            @include font-heading-xxxs;
            color: $color-text-primary;
          }
          ::v-deep .text {
            @include font-body-xs-regular;
            color: $color-text-secondary;
            vertical-align: middle;
          }
        }
      }
      .hotel-name-english {
        @include font-body-xs-regular;
        color: $color-text-secondary;
        @include text-ellipsis(1);
      }
    }

    .hotel-name-tag-star {
      .hotel-name-tags {
        display: inline-flex;
        padding-right: 6px;
        line-height: 0;
        vertical-align:  middle;
      }
      ::v-deep .sustainable-tag {
        position: relative;
        width: 20px;
        height: 12px;
        img {
          position: absolute;
          top: 50%;
          width: 20px;
          height: 20px;
          transform: translateY(-50%);
        }
      }
      .hotel-name-desc {
        vertical-align:  bottom;
        span {
          display: inline-block;
          padding: 1px 4px;
          background-color: $color-bg-3;
          border-radius: 2px;
          color: $color-accent-4;
          @include font-body-xs-regular;
          transform: translateY(-1px);
        }
      }
      & > span:first-child {
        &::after {
            width: 6px;
            height: 1px;
            content: "";
            display: inline-block;
          }
      }
      & > span:last-child {
        &::after {
            display: none;
          }
      }
    }

    .hotel-stars {
      display: inline-flex;
      vertical-align:  middle;
      img {
        width: 12px;
        height: 12px;
      }
    }

    .hotel-review {
      display: flex;
      align-items: center;
      margin-bottom: 4px;
      .hotel-review-score {
        flex-shrink: 0;
        border-radius: 8px 3px;
        background-color: $color-accent-1;
        display: flex;
        padding: 0px 4px 0px 4px;
        justify-content: center;
        align-items: center;
        height: 20px;
        & > span {
          color: $color-text-reverse;
          &:first-child {
            @include font-caption-1;
          }
          &.slash {
            transform: scaleY(0.7);
            @include font-caption-m-regular;
          }

          &:last-child {
            @include font-caption-m-regular;
          }
        }
      }
      .hotel-review-desc {
        color: $color-accent-1;
        @include font-body-s-regular;
        margin-left: 4px;
        flex: 0 2 auto;
        @include text-ellipsis(1);
      }
      .hotel-review-count {
        min-width: 50px;
        color: $color-text-secondary;
        margin-left: 4px;
        @include font-caption-m-regular;
        flex: 0 3 auto;
        @include text-ellipsis(1);
      }
    }

    .hotel-location {
      @include font-body-xs-regular;
      color: $color-text-secondary;

      & > img {
        width: 12px;
        height: 12px;
        vertical-align: sub;
        transform: translateY(-1px);
        margin-right: 2px;
      }
    }

    & > div:last-child {
      margin-bottom: 0;
    }
  }

  .hotel-tag-section {
    width: 100%;
    & > div {
      padding-bottom: 6px;
      &:last-child {
        padding-bottom: 0;
      }
      &.hotel-tag-price-tip, &.hotel-tag-booking, &.hotel-tag-promo {
        ::v-deep .hotel-no-bg {
          height: 16px !important;
          line-height: 16px !important;
        }
      }

      &.hotel-tag-promo {
        display: flex;
        flex-wrap: wrap;
        ::v-deep .hotel-no-bg {
          width: 100%;
          margin-right: 0;
        }
      }
    }
  }

  .hotel-bottom-section {
    padding-top: 6px;

    .hotel-tag-section {
      margin-bottom: 12px;
    }

    .hotel-price-info {
      .price-info {
        display: flex;
        flex-wrap: wrap;
        justify-content: flex-end;
        align-items: baseline;
        margin-bottom: 2px;

        .tax-desc {
          color: $color-text-secondary;
          @include font-body-xs-regular;
          // @include text-ellipsis();
          width: 100%; // 都不换行了
          max-width: 100%;
          text-align: right;
          .tax-desc-price {
            display: inline-block;
          }
        }
        .price-info-value {
          display: flex;
          flex-wrap: wrap;
          align-items: baseline;
          justify-content: flex-end;
          max-width: 100%;
          .price-origin {
            color: $color-text-secondary;
            @include font-caption-m-regular;
            text-decoration-line: line-through;
            margin-right: 2px;
          }
          .price-sale {
            line-height: 0;
            @include text-ellipsis();
            i {
              @include font-heading-xs-v2;
              font-style: normal;
            }
            .price-amount {
              @include font-heading-xs-v2;
            }
          }
        }
      }
      .reward-desc {
        color: $color-accent-19;
        @include font-body-xs-regular;
        max-width: 100%;
        margin-bottom: 6px;
        text-align: right;
      }

      .date-tip {
        @include font-body-xs-regular;
        color: $color-text-secondary;
        text-align: right;
      }

      .login-tip {
        @include font-body-xs-regular;
        color: $color-text-secondary;
        text-align: right;
      }

      & > div {
        &:last-child {
          margin-bottom: 0;
        }
      }

      &-sold-out {
        @include font-heading-xs;
        color: $color-text-disabled;
        text-align: right;
      }
    }

    .price-tag {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      padding-bottom: 2px;
      flex-wrap: wrap;

      ::v-deep .hotel-card-tag {
        margin-left: 6px;
        margin-bottom: 4px;
        @include font-caption-m-semibold;
        img {
          width: 14px;
          height: 14px;
        }
        margin-right: 0;
        &:first-child {
          margin-left: 0;
        }
      }

      & > .member-price-tag:first-child {
        width: 100%;
        margin-right: 0 !important;
        display: flex;
        justify-content: flex-end;
      }

      ::v-deep .hotel-klk-atomic-basic-tag {
        margin-left: 6px;
        margin-right: 0;
        &:last-child {
          margin-right: 0;
        }
        &:first-child {
          margin-left: 0
        }
      }
    }
  }

  .hotel-highlight-tag {
    $corner-color: #ff5b00;
    padding: 1px 8px 2px;
    border-radius: 6px;
    @include font-caption-1;
    border-bottom-left-radius: 0;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    cursor: default;
    color: #fff;
    background-color: $corner-color;
    position: relative;
    &:before {
      position: absolute;
      bottom: -6px;
      left: 0;
      content: "";
      display: block;
      width: 0;
      height: 0;
      border-top: 3px solid $corner-color;
      border-right: 3px solid $corner-color;
      border-left: 3px solid transparent;
      border-bottom: 3px solid transparent;
    }
    &::after {
      position: absolute;
      bottom: -6px;
      left: 0;
      content: "";
      display: block;
      width: 0;
      height: 0;
      border-color: rgba(0, 0, 0, 0.32) rgba(0, 0, 0, 0.32) transparent
        transparent;
      border-style: solid;
      border-width: 3px;
    }
  }

  .card-flex {
    display: flex;
    align-items: center;
    overflow: hidden;
  }

  .hotel-tag-wrap {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: -4px;
    ::v-deep .hotel-card-tag {
      margin-bottom: 4px;
      margin-right: 6px;
      &:last-child {
        margin-right: 0;
      }
    }

    ::v-deep .klk-poptip {
      margin-right: 6px;

      &:last-child {
        margin-right: 0;
      }
    }
  }

  ::v-deep .hotel-klk-atomic-basic-tag {
    margin-bottom: 4px;
    margin-right: 6px;
    &:last-child {
      margin-right: 0;
    }
    &.hotel-no-bg {
      padding-left: 0 !important;
      padding-right: 0 !important;
    }
  }

  ::v-deep .hotel-card-tag {
    max-width: 100%;
    display: flex;
    align-items: center;
    color: #212121;
    @include font-caption-m-regular;

    svg,
    img {
      width: 16px;
      height: 16px;
      margin-right: 4px;
      flex-shrink: 0;
      color: inherit;
    }

    span {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    &.no-padding {
      padding: 1px 0 1px 4px;
      svg,
      img {
        width: 12px;
        height: 12px;
        margin-top: -1px;
      }
    }
    &.type-countdown_manual_tag {
      & > span {
        display: flex;
      }
      .time-count {
        display: flex;
        margin-left: 2px;
        & > span {
          min-width: 8px;
          text-align: center;
        }
      }
    }
    &.type-reward {
      border-radius: 2px;
      img {
        width: auto !important;
        height: 20px !important;
        border-top-left-radius: 2px;
        border-bottom-left-radius: 2px;
        margin-top: 0 !important;
      }
      span {
        padding: 1px 6px 1px 1px;
        @include font-caption-1;
      }
    }
  }

  &.hotel-card-desktop {
    transition: transform 0.2s ease;

    .header-left-arrow,
    .header-right-arrow {
      position: absolute;
      width: 32px;
      height: 32px;
      border-radius: 100%;
      background: rgba(0, 0, 0, 0.3);
      top: 50%;
      transform: translateY(-50%);
      justify-content: center;
      cursor: pointer;
      display: none;

      &:hover {
        background: rgba(0, 0, 0, 0.5);
      }

      svg {
        color: #ffffff;
      }
    }

    .header-left-arrow {
      left: 7px;

      svg {
        transform: rotate(180deg);
      }
    }

    .header-right-arrow {
      right: 7px;
    }

    &.hotel-card-normal {
      box-shadow: inset 0 0px 1px 0.5px #e0e0e0;
      &:hover {
        transform: translateY(-4px);
      }
    }
  }

  &.hotel-card-horizontal {
    flex-direction: row;
    border-radius: $radius-l 0 0 $radius-l;

    .hotel-card-header {
      width: 120px;
      flex-shrink: 0;
      .image-pagination {
        display: none;
      }
      .hotel-image-single {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        overflow: hidden;
        border-radius: $radius-m;
        &.has-new-tag::before {
          content: "";
          display: block;
          position: absolute;
          bottom: 0;
          left: 0;
          width: 100%;
          height: 27px;
          background-color: #6056d6;
          z-index: 1;
          border-radius: 0 0 $radius-m $radius-m;
        }
      }
      .header-tag {
        color: $color-text-reverse;
        @include font-caption-m-semibold;
        @include text-ellipsis();
        text-align: center;
        z-index: 1;
        position: absolute;
        padding: 4px 8px;
        left: 0;
        top: auto;
        bottom: 0;
        max-width: 100%;
        width: 100%;
        background: transparent;
        &::after,
        &::before {
          content: none;
        }
      }
    }

    .hotel-card-main {
      width: calc(100% - 120px);
      padding: 0 0 0 8px;
    }
    .hotel-bottom-section {
      padding-top: 6px;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      height: 100%;
    }
  }

  &.hotel-card-vertical {
    .hotel-card-header {
      .image-pagination {
        right: 12px;
        bottom: 8px;
      }
    }

    .hotel-card-main {
      min-height: 180px;
    }
  }

  &.hotel-card-listHorizontal {
    .hotel-info {
      .hotel-info-name {
        margin-bottom: 6px;
      }
    }

    .hotel-review {
      margin-top: 8px;
      margin-bottom: 8px;
    }
  }

  &.hotel-card-mapList, &.hotel-card-mapNearby {
    background: #FFF;
    border-radius: $radius-l;

    &:hover {
      transform: none;
    }

    .hotel-card-header {
      width: 112px;
      .hotel-image-single {
        border-radius: $radius-l 0 0 $radius-l;
      }
    }
    .hotel-card-main {
      min-height: 156px;
      padding: 8px;
      width: calc(100% - 112px);

      .hotel-info {
        .hotel-info-name {
          .hotel-name {
            font-size: 14px; // 这里防止2行高度里面tag等有高度导致只能一行的问题
          }
        }
      }
    }

    .hotel-bottom-section {
      padding-top: 4px;
      .hotel-price-info {
        .price-info {
          .price-info-value {
            .price-sale {
              .price-amount {
                line-height: 1;
              }
            }
          }
        }
      }
    }

    &.hotel-card-normal {
      outline: 1px solid $color-border-dim;
    }

    &.hotel-card-desktop {
      box-shadow: none;
      &:hover {
        transform: none;
      }
    }
  }
  &.hotel-card-mapNearby {
    .hotel-card-header {
      width: 100px;
    }
    .hotel-card-main {
      width: calc(100% - 100px);
    }
  }

  &.hotel-card-long {
    border-radius: $radius-l;
    padding: 0;

    &:hover {
      box-shadow: $shadow-normal-5;
    }
    .hotel-card-header {
      width: 200px;

      .header-tag {
        @include font-body-s-semibold;
        padding-bottom: 6px;
      }

      .hotel-image-single {
        border-radius: $radius-l 0 0 $radius-l;
        &.has-new-tag::before {
          height: 32px;
        }
      }

      .hotel-add2favor {
        right: 8px;
        top: 12px;
      }
      .hotel-stayplus-tag {
        left: 8px;
        top: 14px;
      }
    }

    .hotel-info {
      .hotel-info-name {
        .hotel-name {
          font-size: 14px; // 整体行高问题
          ::v-deep h3, a {
            @include font-heading-xs-v2;
            color: $color-text-primary;
            display: inline;
          }
          .prefix {
            &::after {
              width: 6px;
            }
          }
          ::v-deep .text {
            &::after {
              width: 6px;
            }
          }
          ::v-deep .ellipsis {
            &::after {
              width: 6px;
            }
          }
          &.has-english {
            // line-height: 1.2;
            ::v-deep .prefix {
              @include font-heading-xs-v2;
              &::after {
                width: 6px;
              }
            }
            ::v-deep .text {
              @include font-body-s-regular-v2;
              vertical-align: middle;
            }
          }
        }
      }
      .hotel-stars {
        display: inline-flex;
        img {
          width: 16px;
          height: 16px;
        }
      }

      .hotel-review {
        margin-top: 6px;
        margin-bottom: 6px;
        .hotel-review-score {
          align-items: center;
          height: 21px;
          padding: 0px 4px 0px 4px;
          & > span {
            &:first-child {
              @include font-body-s-bold;
            }
          }
        }
        .hotel-review-desc {
          margin-left: 8px;
          @include font-body-s-semibold;
        }
        .hotel-review-count {
          margin-left: 8px;
          @include font-body-s-regular;
        }
      }

      .hotel-location {
        @include font-body-s-regular-v2;

        & > img {
          width: 16px;
          height: 16px;
          margin-right: 2px;
          vertical-align: sub;
          transform: translateY(0);
        }

        .view-map {
          color: $color-text-link;
          margin-left: 6px;
          display: inline-block;
          @include font-body-s-regular-v2;
        }
      }
    }

    .hotel-card-main {
      width: calc(100% - 200px);
      padding: 16px;
    }
    .hotel-bottom-section {
      display: flex;
      padding-top: 12px;
      justify-content: space-between;
      align-items: flex-start;
      flex-direction: row;

      .hotel-tag-section {
        margin-bottom: 0;
        & > div {
          padding-bottom: 4px;
          &.hotel-tag-feature {
            padding-bottom: 6px;
            &:last-child {
            padding-bottom: 0;
          }
          }
          &:last-child {
            padding-bottom: 0;
          }
          &.hotel-tag-price-tip, &.hotel-tag-booking, &.hotel-tag-promo {
            ::v-deep .hotel-no-bg {
              font-size: $fontSize-body-xs !important;
              height: 16px !important;
              line-height: 16px !important;
            }
          }
        }
      }

      &:has(.hotel-tag-section) {
        .hotel-tag-section {
          width: calc(65% - 12px);
          flex-shrink: 0;
          position: relative;
          height: 100%;
          padding-right: 12px;
          &::after {
            position: absolute;
            right: 0;
            top: 0;
            content: "";
            display: block;
            width: 1px;
            height: 100%;
            background: radial-gradient(1834.85% 49.77% at 49.77% 50%, #EEE 0%, rgba(238, 238, 238, 0.50) 70%, rgba(238, 238, 238, 0.00) 100%);
          }
        }
        .hotel-price-info {
          flex-shrink: 0;
          flex: 1;
          width: calc(35% - 12px);
          display: flex;
          flex-direction: column;
          justify-content: flex-end;
          height: 100%;
        }
      }

      .hotel-price-info {
        flex-shrink: 0;
        padding-left: 12px;
        .price-info {
          margin-bottom: 8px;
          display: block;
          .tax-desc {
            margin-right: 0;
            display: block;
            width: auto;
          }
          .price-info-value {
            .price-origin {
              color: $color-text-secondary;
              @include font-body-s-regular-v2;
            }
            .price-sale {
              i {
                @include font-heading-s;
              }
              .price-amount {
                @include font-heading-s;
              }
            }
          }
        }

        .reward-desc {
          margin: -4px 0 8px;
        }

        .price-tag {
          ::v-deep .hotel-card-tag {
            img {
              margin-top: -2px;
            }
            @include font-caption-m-semibold;
            &.hotel-card-tag--reward {
              padding: 1.5px 8px 1.5px 30px!important;
            }
          }
        }

        .price-button {
          display: flex;
          justify-content: flex-end;
          margin-bottom: 0;
          margin-top: 0;
          .klk-button {
            white-space: nowrap;
          }
          & + .date-tip,  & + .login-tip {
            margin-top: 8px;
          }
        }

        .date-tip {
          margin-top: 8px;
        }

        &-sold-out {
          @include font-heading-s;
          flex-shrink: 0;
        }
      }
    }

    ::v-deep .hotel-card-tag {
      @include font-body-s-regular;
      svg,
      img {
        width: 20px;
        height: 20px;
        margin-top: -1px
      }
      &.no-padding {
        svg,
        img {
          width: 16px;
          height: 16px;
        }
        height: auto;
        padding: 0 0 0 4px !important;
      }
      &.type-countdown_manual_tag {
        .time-count {
          & > span {
            min-width: 9px;
          }
        }
      }
    }
  }

  &.hotel-card-small {
    .hotel-card-header {
      .header-tag {
        max-width: calc(100% - 8px);
      }
    }
    .hotel-card-main {
      min-height: auto;
      padding: 8px;
      .hotel-info {
        padding-bottom: 0;
        .hotel-smallcard-name-tag {
          & > div {
            display: inline-flex;
            @include text-ellipsis();
            padding: 2px 4px;
            background-color: $color-bg-3;
            border-radius: 2px;
            color: $color-accent-4;
            @include font-body-xs-regular;
          }
        }
      }
    }
  }
}
</style>
