<template>
  <div class="favorite-wrap">
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      @click="action"
    >
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M1 8.83152C1.01005 5.32375 3.72586 2.40002 7.1749 2.40002C9.14088 2.40002 10.876 3.35709 12 4.82508C13.124 3.35709 14.8591 2.40002 16.8251 2.40002C20.2806 2.40002 23 5.3346 23 8.85119C23 8.87112 22.9989 8.93926 22.998 8.99535L22.9971 9.05216C22.9968 9.07005 22.9966 9.08644 22.9964 9.1C22.9962 9.1109 22.9961 9.11908 22.996 9.1244C22.996 9.12518 22.9961 9.12596 22.9961 9.12675C22.9961 12.0246 21.6774 14.5235 19.7977 16.6271C17.9223 18.7259 15.4432 20.4845 12.9968 21.9294C12.3778 22.295 11.6073 22.2898 10.9934 21.916C8.56088 20.4349 6.09711 18.6407 4.22674 16.5103C2.35263 14.3756 1.03161 11.8525 1.00056 8.93868C1.00009 8.90205 1 8.86662 1 8.83401L1 8.83152ZM22.9961 9.12675C22.996 9.12854 22.996 9.12801 22.9961 9.12675C22.9961 9.12674 22.9961 9.12699 22.9961 9.12675ZM2.75124 8.83529C2.75125 8.86487 2.75134 8.89168 2.75167 8.91731L2.75169 8.91935C2.77669 11.2873 3.8451 13.4259 5.54512 15.3623C7.24907 17.3032 9.54444 18.9896 11.9069 20.428C11.9674 20.4648 12.0427 20.4653 12.1035 20.4293C14.4791 19.0262 16.7852 17.375 18.4894 15.4677C20.1878 13.567 21.243 11.4655 21.2448 9.13288C21.2448 9.12702 21.2448 9.12235 21.2448 9.12074C21.2448 9.11557 21.2449 9.11025 21.2449 9.10635C21.245 9.09784 21.2451 9.0867 21.2453 9.07461C21.2457 9.05011 21.2462 9.01746 21.2467 8.98479C21.2472 8.95178 21.2478 8.9186 21.2482 8.89204C21.2486 8.86091 21.2488 8.84927 21.2488 8.85119C21.2488 6.20524 19.223 4.14409 16.8251 4.14409C15.2055 4.14409 13.7664 5.07431 12.9917 6.5007C12.5693 7.27848 11.4307 7.27848 11.0083 6.5007C10.2336 5.07431 8.79453 4.14409 7.1749 4.14409C4.78177 4.14409 2.75942 6.1969 2.75124 8.83529Z"
        fill="white"
      />
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M12.596 20.4029C12.2885 20.582 11.9083 20.5794 11.6032 20.3962C7.09725 17.6904 2.65247 13.9419 2.60008 9.0408C2.59977 9.01696 2.59961 8.99308 2.59961 8.96916L2.59961 8.96266L2.59961 8.95324C2.60787 6.10574 4.83097 3.80005 7.57208 3.80005C9.58395 3.80005 11.3168 5.04212 12.0996 6.82885C12.8825 5.04212 14.6153 3.80005 16.6271 3.80005C19.3734 3.80005 21.5996 6.11434 21.5996 8.96916C21.5996 9.05476 21.5976 9.13988 21.5937 9.22446H21.5959C21.5959 14.0799 17.1275 17.7634 12.596 20.4029Z"
        v-bind="attrs"
      />
    </svg>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop, Inject } from "vue-property-decorator";

import loginWithSDK from '../../util/login'
@Component({
  components: {},
})
export default class Favorite extends Vue {
  @Prop({ type: Boolean, default: false }) isFavorite!: boolean;
  @Prop({ type: Object, default: () => ({}) }) cardInfo!: any;

  currentFavorite = this.isFavorite;
  loading = false;

  get attrs() {
    return this.currentFavorite
      ? {
          fill: "#FF5B00",
        }
      : {
          fill: "black",
          "fill-opacity": "0.38",
        };
  }

  async action(e: MouseEvent) {
    e.preventDefault();
    e.stopPropagation();
    if (!(await loginWithSDK(this, true))) {
      return;
    }

    if (this.loading) return;
    this.loading = true;

    const hotelId = this.cardInfo?.hotel_info?.hotel_id
    let deeplink = this.cardInfo?.deep_link || ''

    try {
      deeplink = new URL(deeplink, location.origin).toString()
    } catch (e) {
      // empty block
    }

    (this as any).$parent._axios
      ?.$post(
        this.currentFavorite ? '/v1/usrcsrv/wishlist/favorites/joint/remove' : '/v1/usrcsrv/wishlist/favorites/joint/put',
        {
          object_id: `11${Array.from({ length: 8 - hotelId.toString().length }, () => 0).join('')}${hotelId}`,
          object_type: 'act-102',
          deeplink
        }
      )
      .then((res: any) => {
        let message = ''
        if (res.success) {
          this.currentFavorite = !this.currentFavorite;
        } else {
          message = res?.error.message || ''
        }
        this.$emit('saved', { hotel_id: hotelId, status: this.currentFavorite, message }) })
      .finally(() => {
        this.loading = false;
      });
  }
}
</script>
