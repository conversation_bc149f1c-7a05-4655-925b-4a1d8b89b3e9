
import Vue, { CreateElement } from 'vue'
import { KlkAtomicBasicTag } from '@klook/klook-card'
import '@klook/klook-card/dist/esm/index.css'
import { Poptip } from '@klook/klook-ui'

const hexToRGBA = (hex: string, a: number = 1) => {
  hex = hex.replace('#', '')
  const r = parseInt(hex.substring(0, 2), 16)
  const g = parseInt(hex.substring(2, 4), 16)
  const b = parseInt(hex.substring(4, 6), 16)
  const rgba = 'rgba(' + r + ', ' + g + ', ' + b + ', ' + a + ')'
  return rgba
}

const renderMap: any = {
  // tag.type === 1 多种类型
  common(this: any) {
    const { $createElement: h, tag } = this
    if (!tag.text && !tag.name) {
      return null
    }
    const isTimeTag = tag.type === 'countdown_manual_tag'
    if (isTimeTag && !this.timeLeftDesc) {
      return null
    }
    const contentStyle = tag.style || {
      color: tag.text_color || '#212121',
      background: tag.bg_color || undefined,
      border: tag.border_color ? `1px solid ${tag.border_color}` : undefined,
      padding: '0px 4px',
      borderRadius: '2px'
    }

    if (tag.type === 'countdown_manual_tag') {
      contentStyle.color = tag.tag_color
      contentStyle.border = `1px solid ${hexToRGBA(tag.tag_color, 0.4)}`
      contentStyle.fontSize = '12px'
      contentStyle.height = '20px'
      contentStyle.lineHeight = '20px'
    }

    // 没有背景色的tag且没有style属性 不需要padding
    const noPadding = !tag.bg_color && !tag.border_color && !tag.style && !tag.tag_color
    if (noPadding) delete contentStyle.padding
    return h('div',
      {
        'class': `hotel-card-tag ${tag.type ? `type-${tag.type}` : ''} ${noPadding ? 'no-padding' : ''}`,
        style: contentStyle
      },
      [
        tag.icon && h('img', { domProps: { src: tag.icon } }),
        h('span', {
          domProps: {
            innerHTML: `${tag.text || tag.name}${isTimeTag ? `: ${this.timeLeftDesc}` : ''}`
          }
        }),
      ]
    )
  },
  reward(this) {
    const { $createElement: h, tag, oid } = this
    const contentStyle: Partial<CSSStyleDeclaration> = {}
    const innerStyle: Partial<CSSStyleDeclaration> = {}

    if (tag.bg_url) {
      contentStyle.background = `url(${tag.bg_url}) 0% 0% / cover no-repeat`
    }
    if (tag.text_color) {
      innerStyle.color = tag.text_color
    }
    const spm = `MemberRateTag${tag.id === 'Gold' ? 'Gold' : 'Member'}`

    return h('div',
      {
        class: `hotel-card-tag type-reward type-reward-${tag.id}`,
        style: contentStyle,
        attrs: {
          'data-spm-module': `${spm}?oid=hotel_${encodeURIComponent(oid)}&ext=${encodeURIComponent(JSON.stringify({ ResourceTag: tag.id }))}`
        },
        directives: [{
          name: 'galileo-click-tracker',
          value: {
            spm
          }
        }]
      },
      [
        tag.icon_src && h('img', { domProps: { src: tag.icon_src } }),
        h('span', {
          style: innerStyle,
          domProps: {
            innerHTML: tag.text
          }
        }),
      ]
    )
  },
  hotel_sustainable_tag(this: any) {
    const { $createElement: h, tag, platform } = this
    if (!tag.text && !tag.name) {
      return null
    }
    const content = h('div', { class: 'sustainable-tag' }, [h('img', { attrs: {src: tag.icon_src} })])

    if (platform === 'desktop') {
      return h(Poptip, {
        props: {
          content: tag.text,
          placement: 'top',
          maxWidth: 430,
          offset: 15,
          flip: true,
          flipConfig: {
            fallbackPlacements: ['right', 'left', 'top', 'bottom'],
            rootBoundary: 'viewport',
            padding: 80
          }
        }
      }, [content])
    }
    return content
  }
}

export const CardTag = Vue.extend({
  name: 'HotelCardTag',
  props: ['tag', 'type', 'oid', 'platform', 'size'],
  components: {
    KlkAtomicBasicTag
  },
  data() {
    return { timeLeftDesc: '' }
  },
  render(h: CreateElement) {
    if (!this.tag) {
      return null
    }
    const type = this.tag.type
    const tagClass = ['hotel-klk-atomic-basic-tag']

    // 使用平台tag
    if (['manual_tag', 'hotel_manual_tag', 'vertical_promo_tag', 'platform_promo_code_tag'].includes(type)) {
      let contentStyle = {}
      // 边框和背景色都无的情况，清除左右padding
      if (!this.tag.bg_color && !this.tag.line_color) {
        tagClass.push('hotel-no-bg')
      }
      return h('klk-atomic-basic-tag', {
        props: { data: this.tag, size: this.size || 'small' },
        class: tagClass.join(' '),
        style: contentStyle
      })
    }

    if (type === 'hotel_promo_code_tag') {
      const hoverItem = Array.isArray(this.tag.hover_list) ? this.tag.hover_list[0] : null
      const hoverContent = hoverItem ? `${hoverItem.title || ''}${hoverItem.desc || ''}` : ''
      return h('klk-poptip', {
        props: {
          content: hoverContent,
          dark: true
        }
      }, [h('klk-atomic-basic-tag', {
        props: { data: { ...this.tag, type: 'platform_promo_code_tag' }, size: 'small' },
        class: tagClass.join(' ')
      })])
    }

    const renderer = this.tag.renderer || (this.tag.type && renderMap[this.tag.type]) || renderMap[this.type]

    return typeof renderer === 'function'
      ? renderer.call(this)
      : this.tag.name && h('div', { 'class': 'hotel-card-tag' }, [h('span', this.tag.name)])
  },
  mounted() {
    let { type, time_left } = this.tag || {}
    if (type === 'countdown_manual_tag' && time_left > 0) {
      const getTimeLeftDesc = (seconds: number) => {
        if (!seconds || +seconds < 1) {
          return (this as any).$t('74910-Event is over')
        }
        const hour = Math.floor(seconds / 3600)
        const minute = Math.floor((seconds - 3600 * hour) / 60)
        const second = seconds - hour * 3600 - minute * 60
        const hh_mm_ss = [hour, minute, second].map((i: number) => `${i > 9 ? i : `0${i}`}`).join(':')

        return (this as any).$t(`74909-Ends in ${hh_mm_ss}`, { hh_mm_ss: `<span class="time-count">${hh_mm_ss.replace(/(\d)/g, '<span>$1</span>')}</span>` })
      }
      const _clearInterval = clearInterval.bind(null, setInterval(() => {
        this.timeLeftDesc = getTimeLeftDesc(time_left--)
        !time_left && _clearInterval()
      }, 1000))
      this.$once('hook:beforeDestroy', _clearInterval)
    }
  }
})
