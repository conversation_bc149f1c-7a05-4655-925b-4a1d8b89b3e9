<script lang="tsx">
import { Component } from "vue-property-decorator";
import render from "./render";

@Component({})
export default class CardSkeleton extends render {}
</script>

<style lang="scss" scoped>
.card-skeleton {
  .header {
    height: 0;
    padding-bottom: 50% !important;
    border-radius: $radius-m !important;
  }
  .content {
    & > div {
      border-radius: $radius-s;
      &:last-child {
        margin-bottom: 0;
        width: 57%
      }
    }
  }

  &.card-skeleton-horizontal {
    display: flex;
    .header {
      width: 123px;
      height: 180px !important;;
      padding-bottom: 0 !important;;
    }
    .content {
      flex: 1;
      padding-left: 12px;
      padding-top: 0;
    }
  }
  &.card-skeleton-mapNearby, &.card-skeleton-mapList {
    display: flex;
    padding: 12px;
    background: #FFF;
    border-radius: $radius-xl;
    .header {
      width: 100px;
      height: 140px;
      padding-bottom: 0;
    }
    .content {
      flex: 1;
      padding-left: 16px;
      padding-top: 0;
    }
  }
  &.card-skeleton-long {
    display: flex;
    .header {
      width: 192px;
      height: 188px;
      padding-bottom: 0;
      margin-bottom: 0;
    }
    .content {
      flex: 1;
      padding-left: 16px;
      padding-top: 0;
      & > div {
        &:nth-child(2) {
          margin-bottom: 24px;
        }
        &:nth-child(2), &:nth-child(4) {
            width: 232px;
        }
      }
    }
  }
}
</style>