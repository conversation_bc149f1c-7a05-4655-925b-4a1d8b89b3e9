import { Vue, Component, Prop } from 'vue-property-decorator';
import { CardLayout, CardScene, CardType } from '../../../../types/types-v2'

@Component({
  components: {
  }
})
export default class CardSkeleton extends Vue {
  @Prop({ type: [Number, String], default: '' }) readonly width!: number | string
  @Prop({ type: [Number, String], default: '' }) readonly height!: number | string
  /**
   * small 只用于mWeb酒店垂直的小卡片和酒店详情页的附近酒店
   * normal用途 mWeb搜索列表页， web酒店垂直页和酒店详情页的方形卡片
   * long 只用于mWeb的搜索列表页
   */
  @Prop({ type: String, default: 'normal' }) readonly type!: CardType
  @Prop({ type: String, default: 'desktop' }) readonly platform!: 'mobile' | 'desktop'
  @Prop({ type: String, default: '' }) readonly layout!: CardLayout
  @Prop({ type: String, default: '' }) readonly scene!: CardScene  //标识card使用场景
  @Prop({ type: String, default: 'light' }) readonly skeletonMode!: 'light' | 'dark'

  get cardLayout() {
    return this.layout || ((['listHorizontal', 'mapNearby', 'mapList'].includes(this.scene) || this.type === 'long') ? 'horizontal' : 'vertical')
  }

  get style() {
    return {
      width: +this.width ? `${parseFloat(this.width as string)}px` : undefined,
      height: +this.height ? `${parseFloat(this.height as string)}px` : undefined,
    }
  }

  render() {
    return <klk-skeleton animate mode={this.skeletonMode} class={`card-skeleton card-skeleton-${this.cardLayout} card-skeleton-${this.type} card-skeleton-${this.scene}`} style={this.style}>
      <klk-skeleton-block class="header" />
      <div class="content">
        <klk-skeleton-block />
        <klk-skeleton-block />
        {this.cardLayout === 'horizontal' && <klk-skeleton-block />}
        {this.type === 'long' && <klk-skeleton-block />}
      </div>
    </klk-skeleton>
  }

}