import { Vue, Component, Prop, Provide } from 'vue-property-decorator';
import { CardTag } from './components/component'
import { initSwiper, adjustElV2, cleanUrl, goUrl, isDlp, hideInfoSet } from '../util'
import { CardLayout, CardScene, CardType, CardInfoV2 } from '../../types/types-v2'

@Component({
    components: {
        CardTag
    }
})
export default class Base extends Vue {
    @Prop({ type: Object, default: () => ({}) }) readonly cardInfo!: CardInfoV2
    @Prop({ type: [Number, String], default: '' }) readonly width!: number | string
    @Prop({ type: [Number, String], default: '' }) readonly height!: number | string
    /**
     * small 只用于mWeb酒店垂直的小卡片和酒店详情页的附近酒店
     * normal用途 mWeb搜索列表页， web酒店垂直页和酒店详情页的方形卡片
     * long 只用于mWeb的搜索列表页
     */
    @Prop({ type: String, default: 'normal' }) readonly type!: CardType
    @Prop({ type: String, default: 'desktop' }) readonly platform!: 'mobile' | 'desktop'
    @Prop({ type: String, default: '' }) readonly layout!: CardLayout
    @Prop({ type: String, default: '' }) readonly href!: string
    @Prop({ type: String, default: '' }) readonly target!: string // 本页还是新开窗口打开， 如果传空则mobile用_self，desktop用——blank设置为默认值
    @Prop({ type: Boolean, default: false }) readonly noLazy!: boolean
    @Prop({ type: String, default: '' }) readonly scene!: CardScene  //标识card使用场景
    @Prop({ type: Boolean, default: false }) readonly showRightIcon!: boolean  //mapNearby 右侧的点击icon
    @Prop({ type: Boolean, default: false }) readonly showFavorite!: boolean
    @Prop({ type: Boolean, default: false }) readonly showMap!: boolean
    @Prop({ type: String, default: '' }) readonly favoriteSpm!: string // 收藏按钮埋点spm
    @Prop({ type: Object, default: null }) readonly t!: Function // $t函数默认this有时候异常，可以支持显示传入

    _destroySwiper!: any
    imageIndex = 0

    _axios = null

    get isMobile() {
        return this.platform === 'mobile'
    }

    get cleanHref() {
        return cleanUrl(this.href)
    }

    get cardStyle() {
        return {
            width: +this.width ? `${parseFloat(this.width as string)}px` : undefined,
            height: +this.height ? `${parseFloat(this.height as string)}px` : undefined,
        }
    }

    get formatTarget() {
        return this.target || (this.isMobile ? '_self' : '_blank')
    }

    get cardLayout() {
        return this.layout || ((['listHorizontal', 'mapNearby', 'mapList'].includes(this.scene) || this.type === 'long') ? 'horizontal' : 'vertical')
    }


    get formatCardInfo() {
        if(this.isAllInfoCard) return this.cardInfo

        const ret = JSON.parse(JSON.stringify(this.cardInfo)) as CardInfoV2
        const hideInfo = hideInfoSet(ret)
        // 不展示campaign_tag
        hideInfo.campaignTag()
        // 只有搜索列表页部分才展示reward积分信息
        if (!(this.type === 'long')) {
            hideInfo.rewardDesc()
        }
        // 不展示评论人数、poi信息、运营tag、stayplustag、预定人数、promotion tag、 价格tag、 含税信息、 参考日期、登录价提示
        if (this.type === 'small') {
            hideInfo.imageList()
            hideInfo.reviewNumDesc()
            hideInfo.stayPlusList()
            hideInfo.location()
            hideInfo.featureTags()
            hideInfo.promotionTags()
            hideInfo.bookingTag()
            hideInfo.priceTags()
            hideInfo.taxDesc()
            hideInfo.nameEn()
            hideInfo.checkInDesc()
            hideInfo.stockDesc()
            hideInfo.discountType()
            return ret
        }

        const isMap = ['mapNearby', 'mapList'].includes(this.scene)

        if (this.type === 'normal') {
            hideInfo.promotionTags()
            hideInfo.stayPlusList()
            !isMap && hideInfo.bookingTag()
            hideInfo.checkInDesc()
            hideInfo.checkInDesc()
            hideInfo.stockDesc()
            hideInfo.discountType()
            if (isMap) {
                hideInfo.nameEn()
                hideInfo.featureTags()
                hideInfo.priceTags()
                hideInfo.originalPriceDesc()

                if (this.scene === 'mapNearby') {
                  hideInfo.bookingTag()
                }

                if (this.scene === 'mapList') {
                    hideInfo.location()
                }
            }

            return ret
        }
        return ret
    }

    get isAllInfoCard() {
        return ['listHorizontal'].includes(this.scene) || this.type === 'long'
    }

    @Provide('_t')
    get _t() {
        // @ts-ignore
        return this.t || this?.$t || (typeof $klook !== 'undefined' && $klook?.$t) || null
    }

    get _inhouse() {
        // @ts-ignore
        return this?.$inhouse || (typeof $klook !== 'undefined' && $klook?.$inhouse) || null
    }

    get imageList() {
        return this.formatCardInfo.hotel_info.image_list
    }

    get pricing() {
        return this.formatCardInfo.pricing
    }

    renderHighlightTag(text: string | undefined, className?: string) {
        return text && <div class={`hotel-highlight-tag ${className}`}>
            <span> {text} </span>
        </div>
    }


    /**
     *
     * 外部有click事件的直接使用外部的时间，例如mWeb的map-card至少点击选中
     */
    onClick(e: MouseEvent) {
        if (!this.$listeners.click && this.href && this.formatTarget) {
            goUrl(isDlp() ? this.cleanHref : this.href, this.formatTarget)
        } else {
            this.$emit('click', e)
        }

        e.preventDefault()
        return false
    }

    triggerClick() {
        const vm = this
        setTimeout(() => {
            (vm.$el as HTMLElement).click()
        }, 200)
    }


    mounted() {
        // 延迟执行，优先渲染
        setTimeout(this.initEvent, 500)
    }

    beforeMount() {
        this._axios = this.$attrs.axios || (window as any)?.$axios;
    }

    beforeDestroy() {
        typeof this._destroySwiper === 'function' && this._destroySwiper()
    }

    initEvent() {
        if (!this.$el || !this.$el.querySelector) {
            return
        }
        const imgContainer = this.$el.querySelector('.header-img-container') as HTMLElement
        const imgWrapper = this.$el.querySelector('.header-img-wrapper') as HTMLElement
        if (imgContainer && imgWrapper) {
            this._destroySwiper = initSwiper({
                containerEl: imgContainer,
                wrapperEl: imgWrapper,
                leftArrowEl: this.$el.querySelector('.header-left-arrow') as HTMLElement,
                rightArrowEl: this.$el.querySelector('.header-right-arrow') as HTMLElement,
                onChange: (value: number) => this.imageIndex = value - 1,
                platform: this.platform || 'mobile'
            })
        }

        adjustElV2(this.$el as HTMLElement, this.isMobile, this.type, this.scene)
    }

}
