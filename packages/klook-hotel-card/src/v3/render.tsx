import { Component, Prop } from 'vue-property-decorator'
import Base from "./base";
import '@klook/klook-scroll-snap/dist/esm/index.css'
import KImage from '@klook/image'

import ArrowSvg from '../arrow-svg.vue'
import FavoriteSvg from './components/favorite.vue'
import Ellipsis from '../components/ellipsis/index.vue'
import {CardTag} from "@/v3/components/component";

type SavedData = {
  hotel_id: number
  status: boolean,
  message?: string
}
@Component({
  components: {
    KImage,
    ArrowSvg,
    FavoriteSvg,
    Ellipsis
  }
})
export default class Card extends Base {

  render() {
    const { type, platform, scene, cardLayout } = this
    const { has_stock } = this.pricing
    return <div class={`hotel-card hotel-card-v3 hotel-card-${type} hotel-card-${platform} hotel-card-${scene} hotel-card-${cardLayout} ${!has_stock ? 'card-sold-out' : ''}`} onClick={this.onClick} style={this.cardStyle} target={this.formatTarget}>
      {this.renderHotelHeader()}
      <div class="hotel-card-main">
        <div class="hotel-body-section">
          {this.renderHotelInfo()}
        </div>

        <div class="hotel-bottom-section">
          {/* {this.renderPromotionTag()} 不展示倒计时，合并到pricetag  */}
          {this.$scopedSlots.bottomLeft ? this.$scopedSlots.bottomLeft() : this.renderHotelTag()}  {/* 目前这里只有stayPlusTag + 人工tag + reverse信息 */}
          {this.renderPlaceHolder()}
          {this.renderPriceInfo()}
        </div>
      </div>
    </div>
  }


  renderHotelHeader() {
    return <div class="hotel-card-header">
      {this.renderStayPlusTag()}
      {this.renderImageHeader()}
      {this.renderAdd2Favor()}
      {this.renderImageCount()}
    </div>
  }


  renderStayPlusTag() {
    const { stay_plus_list } = this.formatCardInfo
    const hasStayPlus = stay_plus_list && stay_plus_list.length && stay_plus_list[0].icon // 后端返回[]不是null
    return hasStayPlus && <div class="hotel-stayplus-tag">
       <img src={stay_plus_list[0].icon} />
      </div> || null
  }

  renderAdd2Favor() {
    return this.showFavorite && <favorite-svg
      class="hotel-add2favor"
      isFavorite={this.formatCardInfo.wish}
      cardInfo={this.formatCardInfo}
      onSaved={(data: SavedData) => this.$emit('saved', data, this.$el)}
      {...{ directives: [{ name: 'galileo-click-tracker', value: { spm: 'HotelsWishLIST' } }] }}
      data-spm-virtual-item="__virtual?trg=manual"
      data-spm-module={this.favoriteSpm}
    />
  }

  renderImageSingle() {
    return <div class='hotel-image-single'>
      {this.renderImage(this.imageList[0], 0, this.noLazy, this.cardLayout === 'horizontal' ? 750 : 350)}
    </div>
  }

  renderImage(img: string, key: number = 0, priority: boolean = false, width = 375) {
    return <k-image scene="HotelCard" src={img} config={{ width }} key={key} priority={priority} />
  }

  renderImageHeader() {
    if (this.cardLayout === 'horizontal') {
      return this.renderImageSingle()
    }
    return this.renderImageWrap()
  }

  renderImageWrap() {
    let _list = this.imageList || []
    if (_list.length >= 2) {
      _list = [_list[_list.length - 1], ..._list, _list[0]]
    }
    return <div class="hotel-image-swipe">
      <div class="header-img-container">
        <div
          class="header-img-wrapper"
          style={{ transform: `translate3d(${_list.length > 1 ? '-100%' : '0px'}, 0px, 0px)` }}>
          {_list.map((img: any, index: number) => this.renderImage(img, index, this.noLazy && index === (_list.length > 1 ? 1 : 0))
          )}
        </div>
      </div>
      {!this.isMobile && _list.length > 1 && <div>
        <div class="card-flex header-left-arrow">
          <arrow-svg />
        </div>
        <div class="card-flex header-right-arrow">
          <arrow-svg />
        </div>
      </div>}
    </div>
  }

  renderImageCount() {
    return this.imageList.length > 1 && <div class="image-pagination">
      {this.imageIndex + 1}/{this.imageList.length}
    </div>
  }


  renderHotelInfo() {
    const { campaign_tag } = this.formatCardInfo
    return <div class="hotel-info">
      { campaign_tag && <card-tag tag={campaign_tag}></card-tag>}
      {this.renderHotelName()}
      {this.renderReview()}
      {this.renderLocation()}
    </div>
  }


  /**
   *
   * mWeb的normal下list类型和web的long类型都是全部展示
   *
   * web的normal是 中文+星级 + new tab 2行，然后英文酒店一行
   * small 和地图 是不展示英文(数据源remove)，中文+星级 + new tab 2行
   */
  renderHotelName() {
    const { type, isMobile, scene } = this
    const { name, local_name, desc } = this.formatCardInfo.hotel_info

    const showMixEnglish = this.isAllInfoCard && local_name
    const isSmallCard = type === 'small'
    // showMixEnglish 全部ui展示
    return <div class="hotel-info-name">
      <div class="hotel-name-section">
        <ellipsis
         class={{ 'hotel-name': true, 'has-english': showMixEnglish }} 
         text={showMixEnglish ? local_name : name} 
         lineHeight={1.5}
         initFontSize={this.type === 'long' ? 18 : 14}
         row={this.isAllInfoCard ? 0 : 2}
         scopedSlots={{
          content: ({showText}: any) => {
            // 全部展示情况， text部分展示是local_name（当地语言），然后酒店名称放为前缀展示
            return showMixEnglish ?
            <span class="text">{ showText }</span>
            : <h3 class="text"><a href={this.cleanHref} onClick={(e: Event) => e.preventDefault()}>{showText}</a></h3>
          }
         }}
         >
          {showMixEnglish ? <h3 slot="prefix" class="prefix"><a href={this.cleanHref} onClick={(e: Event) => e.preventDefault()}>{name}</a></h3> : ''}
          <template slot="suffix">
            {this.renderHotelNameTagWithStar()}
          </template>
        </ellipsis>
        {this.showRightIcon && <div class="hotel-right-icon" onClick={() => this.$emit('right-icon-click')}><arrow-svg /></div>}
      </div>

      {isSmallCard && desc && <div class="hotel-smallcard-name-tag">
        <div>{desc}</div>
      </div>}
      {!showMixEnglish && local_name && <div class="hotel-name-english">
        {local_name}
      </div>}
    </div>
  }

  renderHotelNameTagWithStar() {
    const { desc } = this.formatCardInfo.hotel_info
    const star = this.formatCardInfo.hotel_info.star
    const isSmallCard = this.type === 'small'
    const tags = this.formatCardInfo.head_tag_list

    return <span class="hotel-name-tag-star">
       { this.renderStar()}
       { tags && <span class="hotel-name-tags"> {tags.map(tag => <card-tag tag={tag} platform={this.platform} />)} </span> }
       { desc && !isSmallCard && <span class="hotel-name-desc"><span>{ desc }</span></span> }
    </span>
  }

  renderStar() {
    const star = this.formatCardInfo.hotel_info.star
    return star >= 2 && <span class="hotel-stars">
      {Array.from({ length: star }, (item: number, index: number) => {
        return <img key={index} src="https://res.klook.com/image/upload/v1678947231/UED%20Team%EF%BC%88for%20DE%20only%EF%BC%89/System%20Icon/Hotel/%E9%85%92%E5%BA%97%E6%98%9F%E7%BA%A7/icon_operational_star_fill_l.png" alt="" />
      })}
    </span>
  }

  renderReview() {
    const { avg_score, max_score, score_desc, review_num_desc } = this.formatCardInfo.hotel_info

    const showScore = +avg_score
    return showScore && <div class="hotel-review">
      {showScore && <div class="hotel-review-score">
        <span>{avg_score}</span>
        <span class="slash">/</span>
        <span>{max_score}</span>
      </div>}
      {score_desc && <div class="hotel-review-desc">
        {score_desc}
      </div>}
      {review_num_desc && <div class="hotel-review-count">
        {review_num_desc}
      </div>}
    </div> || null
  }

  renderLocation() {
    const location = this.formatCardInfo.hotel_info.location
    const { hotel_id, name, image_list, latitude, longitude } = this.formatCardInfo.hotel_info

    return location && <ellipsis class="hotel-location" text={location.desc} row={2}>
      {location.icon && <img slot="prefix" src={location.icon} />}
      {this.showMap && latitude && longitude && <span slot="suffix" class="view-map" onClick={this.viewMap}>{this._t?.('29523')}</span>}
    </ellipsis>
  }

  viewMap(e: MouseEvent) {
    const { hotel_id, name, image_list, latitude, longitude } = this.formatCardInfo.hotel_info
    const mapProps = { hotel_id, name, image_list, latitude, longitude }
    this.$emit('view-map', mapProps)
    e.preventDefault()
    e.stopPropagation()
  }

  renderHotelTag() {
    const { feature_tag_list, book_tag, promo_tag_list, stock_tag } = this.formatCardInfo
    const hasFeatureTags = feature_tag_list && feature_tag_list.length > 0
    const hasPromoTags = promo_tag_list && promo_tag_list.length > 0
    const cardSize = this.type === 'long' ? 'medium' : 'small'
    return (hasFeatureTags || book_tag || stock_tag || hasPromoTags) && <div class="hotel-tag-section">
      {hasFeatureTags && <div class="hotel-tag-feature hotel-tag-wrap">
        {
          feature_tag_list.map((item, index) => <card-tag key={index} tag={item} />)
        }
      </div> || null}

      {stock_tag && <div class="hotel-tag-price-tip hotel-tag-wrap"><card-tag tag={stock_tag} size={cardSize}></card-tag></div>}

      {book_tag && <div class="hotel-tag-booking hotel-tag-wrap">
        {
          <card-tag tag={book_tag}  size={cardSize} />
        }
      </div>}

      {hasPromoTags && <div class="hotel-tag-promo hotel-tag-wrap">
        {
          promo_tag_list.map((item, index) => <card-tag key={index} tag={item} size={cardSize} />)
        }
        </div>}
    </div>
  }

  renderPriceInfo() {
    const { format_price, currency_symbol, original_price_desc, tax_desc, check_in_desc, discount_type, has_stock, reward_desc, post_format_tax_price } = this.pricing

    const isLongType = this.type === 'long'

    return has_stock ? <div class="hotel-price-info">
      {this.renderHotelPriceTag()}
      <div class="price-info">
        <div class="price-info-value">
          <span class="price-origin">{original_price_desc}</span>
          <span class="price-sale">
            <i>{currency_symbol}</i>
            <b class="price-amount">{format_price} </b>
          </span>
        </div>
        {tax_desc && <div class="tax-desc">
          <span>{tax_desc}</span>
          { post_format_tax_price && <span class="tax-desc-price">{ `${currency_symbol}${post_format_tax_price}`}</span> }
        </div>}
      </div>
      {reward_desc && <div class="reward-desc">{reward_desc}</div> }
      {isLongType && <div class="price-button"><klk-button size="large">
        {this._t?.('hotel.view_detail')} </klk-button></div>}
      {check_in_desc && <div class="date-tip">{check_in_desc}</div>}
      {discount_type === 'login' && <div class="login-tip">{this._t?.('19280')}</div>}
    </div> : <div class="hotel-price-info-sold-out">
      {this._t?.('sold_out')}
    </div>
  }

  renderHotelPriceTag() {
    const { hotel_id } = this.formatCardInfo.hotel_info
    const price_tags = this.pricing.price_tag_list

    const priceTagLength = price_tags ? price_tags.length : 0

    return price_tags && priceTagLength && <div class={['price-tag']}>
      {
        price_tags.map((item, index) => {
          const isMemberTag = item.type === 'reward' || item.id === 'Explorer'
          const isRowMember = priceTagLength >= 3 && isMemberTag
          const renderTag = () => <card-tag key={index} tag={item} type="common" oid={hotel_id} />
          return isRowMember ? <div class="member-price-tag"> {renderTag()}</div> : renderTag()
        })
      }
    </div> || null
  }

  renderPlaceHolder() {
    return  <div class="hotel-placeholder"></div>
  }

}
