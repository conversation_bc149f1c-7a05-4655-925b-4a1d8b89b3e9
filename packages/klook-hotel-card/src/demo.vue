<template>
  <div>
    <div class="demo-wrapper">
      <klk-form class="demo-form">
        宽度放大 {{ zoom / 20 }}
        <klk-slider v-model="zoom" :min="1" :max="30" style="margin: 0 20px" />
        <klk-form-item label="商品图片数量">
          <klk-counter v-model="cardInfo.imgLen" />
          <klk-checkbox v-model="isSkeleton">切换骨骼效果</klk-checkbox>
        </klk-form-item>
        <klk-form-item label="酒店星级🌟"
          ><klk-input v-model="cardInfo.star" />
        </klk-form-item>
        <klk-form-item label="商品描述信息"
          ><klk-input v-model="cardInfo.descTag" />
        </klk-form-item>
        <klk-form-item label="商品标题"
          ><klk-input v-model="cardInfo.titleTag" />
        </klk-form-item>
        <klk-form-item label="商品辅助信息"
          ><klk-input v-model="cardInfo.additionTag" />
        </klk-form-item>
        <klk-form-item label="商品购买信息">
          <div
            class="purchase-tag"
            v-for="(item, index) in cardInfo.purchaseTags"
            :key="index"
            style="dispaly: flex; align-items: center"
          >
            <label>Tag {{ index }}</label>
            <klk-input v-model="item.desc" />
          </div>
        </klk-form-item>
        <klk-form-item label="商品市场价">
          <klk-input v-model="marketPrice" />
        </klk-form-item>
        <klk-form-item label="商品售价">
          <klk-input v-model="sellingPrice" />
        </klk-form-item>
        <klk-form-item label="商品价格强相关信息">
          <klk-input v-model="cardInfo.priceTag.desc" />
        </klk-form-item>
        <klk-form-item label="售罄文案">
          <klk-input v-model="soldOutText" />
        </klk-form-item>
        <klk-form-item label="状态">
          <klk-input v-model="cardInfo.status" />
        </klk-form-item>
        <klk-form-item label="flexible日期提示信息">
          <klk-input v-model="cardInfo.checkInInfo.desc" />
          <klk-input
            v-model="cardInfo.checkInInfo.text_color"
            placeholder="颜色， #437DFF"
          />
        </klk-form-item>
      </klk-form>
      <div style="padding: 50px; background: #f5f5f5">
        <div style="padding-top: 5px; height: 60px">HotelCard</div>
        <hotel-card
          :card-info="cardInfoV2"
          class="demo-hotel-card"
          :sold-out-text="soldOutText"
          :is-skeleton="isSkeleton"
          :width="(343 * zoom) / 20"
          platform="mobile"
          href="https://www.google.com"
          target="_blank"
        />
        <div style="padding-top: 25px; height: 60px">详情页Add on 卡片</div>
        <hotel-card
          :card-info="cardInfoV3"
          class="demo-hotel-card"
          :sold-out-text="soldOutText"
          :is-skeleton="isSkeleton"
          :width="(343 * zoom) / 20"
          platform="mobile"
          href="https://www.google.com"
          target="_blank"
        />
        <div style="height: 30px" />
        <div style="display: flex">
          <hotel-card
            :card-info="cardInfoV2"
            class="demo-hotel-card"
            style="marginleft: 10px"
            type="small"
            :width="(148 * zoom) / 20"
            :sold-out-text="soldOutText"
            :is-skeleton="isSkeleton"
            href="https://www.google.com"
            target="_self"
          />
        </div>
      </div>
    </div>
    <hotel-long-card
      :card-info="cardInfoV2"
      class="demo-hotel-card"
      type="long"
      :sold-out-text="soldOutText"
      :is-skeleton="isSkeleton"
      href="https://www.google.com"
      target="_self"
    />
  </div>
</template>

<script lang="ts">
import { Component, Vue, Watch } from "vue-property-decorator";
import HotelCard, { HotelLongCard } from "./index";
import { adjustEl } from "./util";

const imgs = [
  "https://i.travelapi.com/hotels/1000000/190000/189900/189840/2b6958fe_z.jpg",
  "https://i.travelapi.com/hotels/1000000/190000/189900/189840/3a5b3467_z.jpg",
  "https://i.travelapi.com/hotels/1000000/190000/189900/189840/08d46079_z.jpg",
  "https://i.travelapi.com/hotels/1000000/190000/189900/189840/7bcebcc0_z.jpg",
  "https://i.travelapi.com/hotels/1000000/190000/189900/189840/f5eb2dd8_z.jpg",
  "https://i.travelapi.com/hotels/2000000/1670000/1667200/1667164/7d235775_z.jpg",
  "https://i.travelapi.com/hotels/2000000/1670000/1667200/1667164/4b3b7885_z.jpg",
  "https://i.travelapi.com/hotels/2000000/1670000/1667200/1667164/0648b849_z.jpg",
  "https://i.travelapi.com/hotels/2000000/1670000/1667200/1667164/1a4bbb2b_z.jpg",
  "https://i.travelapi.com/hotels/7000000/6260000/6253700/6253685/04aef54b_z.jpg",
];

function formatPriceThousands(price: any) {
  var tmp;
  price = (price || "0").toString();

  if (price.indexOf(".") < 0) {
    tmp = price.replace(/(?=(?!(\b))(\d{3})+$)/g, ",");
  } else {
    price = price.split(".");
    tmp =
      price[0].toString().replace(/(?=(?!(\b))(\d{3})+$)/g, ",") +
      "." +
      price[1];
  }

  return tmp;
}

export const cardInfo = {
  top_tags: [
    {
      type: "1",
      name: "Brand flash sale",
      icon: "https://cdn.klook.com/s/dist_web/favicons/favicon-32x32.png",
      text_color: "#fff",
      bg_color: "#4D40CC",
    },
    {
      type: "2",
      name: "Today's Limited Time OfferOff",
      text_color: "#fff",
      bg_color: "#FF5B00",
      time_left: 57480,
    },
  ],
  promotion_tags: [
    {
      icon: "https://res.klook.com/image/upload/v1639464552/UED%20Team%EF%BC%88for%20DE%20only%EF%BC%89/System%20Icon/Hotel/Rate%20Code/icon_food_wine_coffee_xs.png",
      id: null,
      name: "560 people have booke560 people have booked today560 people have booked todayd today",
      text_color: "#212121",
      bg_color: "",
      border_color: null,
      end_time: null,
    },
    {
      icon: "https://res.klook.com/image/upload/v1639464551/UED%20Team%EF%BC%88for%20DE%20only%EF%BC%89/System%20Icon/Hotel/Rate%20Code/icon_feedback_success_green_xs.png",
      id: null,
      name: "限时免费取消",
      text_color: "#0DA06A",
      bg_color: "",
      border_color: null,
      end_time: null,
    },
    {
      icon: "https://res.klook.com/image/upload/v1639464552/UED%20Team%EF%BC%88for%20DE%20only%EF%BC%89/System%20Icon/Hotel/Rate%20Code/icon_food_wine_coffee_xs.png",
      id: null,
      name: "Today's Limited Time Offer",
      text_color: "#FF9D26",
      bg_color: "",
      border_color: null,
      end_time: null,
    },
    {
      icon: "https://res.klook.com/image/upload/v1639464551/UED%20Team%EF%BC%88for%20DE%20only%EF%BC%89/System%20Icon/Hotel/Rate%20Code/icon_feedback_success_green_xs.png",
      id: null,
      name: "Book 2 consecutive nights, get 1 free",
      text_color: "#FF5B00",
      bg_color: "",
      border_color: null,
      end_time: null,
    },
  ],
  operation_tags: [
    {
      icon: "https://res.klook.com/image/upload/v1639464552/UED%20Team%EF%BC%88for%20DE%20only%EF%BC%89/System%20Icon/Hotel/Rate%20Code/icon_food_wine_coffee_xs.png",
      id: null,
      name: "Super invincible sea view room, very good.",
      desc: "",
      text_color: "#212121",
      bg_color: "rgba(255, 255, 255, 0.85)",
      border_color: null,
      end_time: null,
    },
  ],
  feature_tags: [
    {
      type: 1,
      id: "437",
      icon: null,
      name: "舒压 & 自然",
      desc: null,
      text_color: "#086071",
      bg_color: "#E6EFF1",
      border_color: null,
      time_left: null,
    },
    {
      type: 1,
      id: "401",
      icon: null,
      name: "观光导览",
      desc: null,
      text_color: "#757575",
      bg_color: "#F5F5F5",
      border_color: null,
      time_left: null,
    },
    {
      type: 1,
      id: "404",
      icon: null,
      name: "按摩水疗",
      desc: null,
      text_color: "#757575",
      bg_color: "#F5F5F5",
      border_color: null,
      time_left: null,
    },
    {
      type: 1,
      id: "406",
      icon: null,
      name: "交通服务",
      desc: null,
      text_color: "#757575",
      bg_color: "#F5F5F5",
      border_color: null,
      time_left: null,
    },
    {
      type: 1,
      id: "196",
      icon: null,
      name: "非常高的性价比",
      desc: null,
      text_color: "#757575",
      bg_color: "#F5F5F5",
      border_color: null,
      time_left: null,
    },
    {
      type: 1,
      id: "194",
      icon: null,
      name: "特别好的 城市酒店",
      desc: null,
      text_color: "#757575",
      bg_color: "#F5F5F5",
      border_color: null,
      time_left: null,
    },
  ],
  hotel_info: {
    hotel_id: 434808,
    name: "The Concierge at Wind Residences Tagaytay",
    name_icon: 'https://res.klook.com/image/upload/v1682501612/ued/Hotel/stay_tag.png',
    image_list: imgs.slice(0, 5),
    star: 5,
    location_desc: "Taito,Hong Kong - 150 m from metro station",
    rating: {
      avg_score: "4.8",
      max_score: "5",
      score_desc: "超赞",
    },
  },
  pricing: {
    tax_desc: "新增 税费文案信息",
    price: "69.4",
    currency_symbol: "HK$",
    format_price: "6X.X",
    price_desc: "From{format_price}",
    original_price_desc: "HK$ 121.8",
    price_tags: [
      {
        type: 1,
        id: null,
        icon: "https://res.klook.com/image/upload/%E5%88%87%E5%9B%BE%E9%80%89%E8%BF%99%E4%B8%AA_App_Icon_czrmyd.png",
        name: "rewards2",
        desc: null,
        text_color: "#fff",
        bg_color: "#FF9C00",
        bg_color_left: "#FF9C00",
        bg_color_right: "#E29B11",
        border_color: null,
        time_left: null,
      },
    ],
    promotion_info: {
      icon: null,
      name: "Save -¥200",
      text_color: "#FFFFFF",
      bg_color: "#FF5B00",
      promotion: {
        title: "Offer Description",
        desc: "May 1st - May 4th, 3 nights in total",
        original_per_night_price: "870",
        original_price_desc: "3 nights total,HK$ 2,610",
        per_night_price: "850",
        price_desc: "3 nights total,HK$ 2,550",
        currency_symbol: "HK$",
        discount: {
          price: "120",
          currency_symbol: "HK$",
          sign: "-",
          discount_list: [
            {
              tag: {
                name: "50% off for a limited time",
                text_color: "#FF5B00",
                border_color: "#FF5B00",
                desc: "First order for new users only",
              },
              price_desc: "-HK$40",
            },
            {
              tag: {
                name: "50% off for a limited time limited time limited time limited time",
                text_color: "#FF5B00",
                border_color: "#FF5B00",
                desc: "First order for new users only First order for new users only First order for new users only First order for new users only",
              },
              price_desc: "-HK$40",
            },
            {
              tag: {
                name: "50% off for a limited time",
                text_color: "#FF5B00",
                border_color: "#FF5B00",
                desc: "First order for new users only",
              },
              price_desc: "-HK$40",
            },
          ],
        },
        gift: {
          gift_list: [
            {
              name: "住3晚送1张住宿券",
              icon: "https://cdn.klook.com/s/dist_web/favicons/favicon-32x32.png",
              text_color: "#FF5B00",
            },
            {
              name: "住3晚送1张住宿券",
              icon: "https://cdn.klook.com/s/dist_web/favicons/favicon-32x32.png",
              text_color: "#FF5B00",
            },
            {
              name: "住3晚送1张住宿券",
              icon: "https://cdn.klook.com/s/dist_web/favicons/favicon-32x32.png",
              text_color: "#FF5B00",
            },
          ],
        },
        tip: {
          name: "Only 2 rooms left!",
          icon: "https://cdn.klook.com/s/dist_web/favicons/favicon-32x32.png",
          text_color: "#FF5B00",
        },
      },
    },
    price_info: {
      icon: "https://res.klook.com/image/upload/v1655206607/UED%20Team%EF%BC%88for%20DE%20only%EF%BC%89/System%20Icon/Hotel/Promotional%20information/icon_nav_chevron_right_xxxs.png",
      name: "Save ¥ 128.3",
      text_color: "#FF5B00",
      bg_color: "#FFF0E5",
      border_color: null,
      price_detail: {
        title: "price_details",
        currency_symbol: "¥",
        price_items: [
          {
            type: "taxes_and_fee",
            title: "Tax and service fee2",
            content: "HK$ 259.22",
            item_list: [
              {
                title: "Tax and service fee2",
                content: "HK$ 259.22",
                desc: "desc desc",
              },
              {
                title: "Tax and service fee2",
                content: "HK$ 259.22",
                desc: "desc desc",
              },
            ],
          },
          {
            type: "discount",
            title: "discount",
            content: "HK$ 259.2",
            content_color: "#ff5b00",
            item_list: [
              {
                title: "Tax and service fee",
                content: "HK$ 259.2",
                desc: "desc desc",
              },
              {
                title: "Tax and service fee2",
                content: "HK$ 259.22",
                desc: "",
              },
            ],
          },
          {
            type: "credit_and_gift",
            title: "credit_and_gift",
            content: "HK$ 259.2",
            content_color: "",
            item_list: [
              {
                title: "Tax and service fee",
                content: "HK$ 259.2",
                desc: "desc desc",
              },
              {
                title: "Tax and service fee2",
                content: "HK$ 259.22",
                desc: "",
              },
            ],
          },
        ],
        pay_online: {
          title: "Paid online",
          total_price: "HK$ 3,480.0",
          total_price_desc: "total",
          per_night_price: "HK$ 870.0",
          per_night_price_desc: "per room/night",
          taxes_desc: "Incl. taxes",
        },
        gift: {
          title: "Extra Offer",
          gift_list: [
            {
              icon: "",
              name: "Stay 3 nights get 1 voucher",
              text_color: "",
              desc: "Stay 3 nights get 1 voucher",
            },
          ],
        },
      },
    },
    check_in_info: {
      check_in_tip: "June 12-June 13",
      text_color: "#212121",
    },
    has_stock: true,
  }
};

@Component({
  name: "DemoForm",
  components: {
    HotelCard,
    HotelLongCard,
  },
})
export default class HotelCardDemo extends Vue {
  cardInfo: any = {
    imgLen: 3,
    titleTag:
      "Mutianyu Great Wall Day Tour by Bus with Options Mutianyu Great Wall",
    star: 5,
    descTag: "May be some description",
    additionTag: "Taito,Hong Kong - 150 m from metro station",
    srvTag: {
      desc: "Eligible for SingapoRediscovers Vouchers",
      text_color: "#fff",
      bg_color: "#F4333D",
    },
    ratingTag: {
      avg_score: "4.8",
      max_score: "5",
      score_desc: "超赞",
    },
    purchaseTags: [
      { desc: "Hotel Voucher", text_color: "#FF9D26", bg_color: "#FFF4ED" },
      { desc: "Rate benifit tag", bg_color: "#F5F5F5", text_color: "#757575" },
      {
        desc: "Rate benifit tag特别长春吃吃吃吃吃吃吃吃吃",
        bg_color: "#FF5722",
        text_color: "#fff",
      },
      {
        desc: "测试看看dfsfafasfs",
        bg_color: "#FF9D26",
        text_color: "#fff",
        icon: "#klk-icon-icon_navigation_close",
      },
    ],
    marketPrice: { price_desc: "{0}", format_price: "HK$ 99.2" },
    sellingPrice: { price_desc: "From {0}", format_price: "HK$ 99.2" },
    priceTag: {
      icon: "https://res.klook.com/image/upload/%E5%88%87%E5%9B%BE%E9%80%89%E8%BF%99%E4%B8%AA_App_Icon_czrmyd.png",
      desc: "Member Price1",
      text_color: "#8A0300",
      bg_color: "#FBE5DF",
      bg_left_color: "",
      bg_right_color: "",
      type: 1,
      id: "12345",
    },
    status: 1, // 0表示售罄
    checkInInfo: {
      desc: "",
      // text_color: '#437DFF'
      text_color: "#437DFF",
    },
    vipLoginTip: {
      desc: "Log in for the Klook member price",
      text_color: "#999999",
    },
  };

  cardInfoV2: any = cardInfo

  addOnData = {
    hotel_id: "427442",
    img_url:
      "https://res.klook.com/image/upload/activities/cejtm9xa7l1dv092bphs.jpg",
    voucher_id: null,
    activity_id: "1224",
    title: "【乐园餐饮】儿童美食套餐劵",
    format_participant: "847,003件已售",
    stock_info: null,
    deeplink: "https://www.klook.com/zh-CN/activity/524-hong/",
    sell_price: {
      currency: "HKD",
      currency_symbols: "HK$",
      amount: "100",
      format_price: "HK$ 100",
      ori_amount: null,
      ori_format_price: null,
      price_desc: "{format_price}起",
      save_price_tag: null,
      vip_login_tips: null,
    },
    tag_list: [],
  };

  get cardInfoV3() {
    return {
      hotel_info: {
        name_icon: 'https://res.klook.com/image/upload/v1682501612/ued/Hotel/stay_tag.png',
        hotel_id: this.addOnData.hotel_id,
        name: this.addOnData.title,
        image_list: [this.addOnData.img_url],
        location_desc: this.addOnData.format_participant,
      },
      pricing: {
        ...this.addOnData.sell_price,
        currency_symbol: "",
        price_tags: this.addOnData.tag_list,
        has_stock: this.addOnData.sell_price.amount !== "0",
      },
    };
  }
  isSkeleton: boolean = false;
  zoom: number = 20;
  marketPrice = "99999999999999999999.2";
  sellingPrice = "111111111111111111111.1";
  soldOutText: string = "";

  formatPrice(value: number) {
    return `HK$ ${formatPriceThousands(value)}`;
  }

  get cardInfoValue() {
    return Object.assign({}, this.cardInfo, {
      imgList: imgs.slice(0, this.cardInfo.imgLen || 5),
      sellingPrice: Object.assign({}, this.cardInfo.marketPrice, {
        format_price: `HK$ ${this.sellingPrice}`,
      }),
      marketPrice: Object.assign({}, this.cardInfo.marketPrice, {
        format_price: `HK$ ${this.marketPrice}`,
      }),
    });
  }

  get platform() {
    return navigator.userAgent.match(/iPhone|iPad|iPod|Mobile/i)
      ? "mobile"
      : "desktop";
  }

  get simpleCardInfo() {
    return Object.assign({}, this.cardInfoValue, {
      descTag: undefined,
      purchaseTags: undefined,
      priceTag: undefined,
      additionTag: undefined,
      imgList: this.cardInfoValue.imgList.slice(0, 1),
      topTags: [
        {
          type: "promotion_timer",
          sub_type: null,
          type_desc: null,
          id: null,
          icon: null,
          desc: "To end {time_left}",
          desc_en: null,
          text_color: "#0B9163",
          bg_color: "#E9F8F1",
          bg_color_left: null,
          bg_color_right: null,
          border_color: null,
          scrap_color: null,
          src: null,
          time_left: "509509",
        },
      ],
      srvTag: null,
      ratingTag: { ...this.cardInfoValue.ratingTag, score_desc: "" },
    });
  }

  @Watch("cardInfo", { deep: true, immediate: true })
  onValueChange() {
    this.$emit("input", this.cardInfo);
    this.isSkeleton = true;
    this.$nextTick(() => (this.isSkeleton = false));
    setTimeout(() => {
      this.$el &&
        adjustEl(this.$el.querySelector(".demo-hotel-card") as HTMLElement, "");
    }, 500);
  }
}
</script>

<style lang="scss">
.markdown-body img {
  background-color: initial;
}
.demo-wrapper {
  display: flex;
  align-items: flex-start;

  .demo-hotel-card {
    flex-shrink: 0;
  }
}
.demo-form {
  flex: 1 1 0;

  .klk-form-item {
    flex-direction: row;
    align-items: center;

    label {
      margin-right: 12px;
      width: 100px;
    }
  }

  .klk-form-item-content {
    min-width: 300px;
  }

  .tag-form-item {
    flex-direction: column;
    align-items: flex-start;
  }

  .tag-wrapper {
    display: flex;
    align-items: center;
    flex-wrap: wrap;

    &-item {
      display: flex;
      align-items: center;
      padding: 4px 10px;

      svg {
        margin-left: 6px;
        cursor: pointer;
      }
    }
  }
}
</style>
