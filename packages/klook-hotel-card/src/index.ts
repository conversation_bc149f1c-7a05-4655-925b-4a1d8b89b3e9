import Vue, { CreateElement } from 'vue'
import { CardTag } from './v3/components/component'
import { formatCardData } from './util'
import InnerCardV3 from './v3/index.vue'
import CardSkeletonV2 from './v3/components/card-skeleton/index.vue'
// 入口处直接渲染ab种类卡片。这样就不用在每个引用的地方修改

export default Vue.extend({
  name: 'HotelCard',
  props: ['platform', 'isSkeleton', 'type', 'width', 'height', 'cardInfo', 'href', 'target', 'soldOutText', 'noLazy', 'scene', 'layout', 'showRightIcon', 'showFavorite', 'showMap', 'favoriteSpm', 'skeletonMode', 'isAb', 't'],
  components: {
    InnerCardV3,
    CardSkeletonV2
  },
  render(h: CreateElement) {
    const props = this.$props
    // const isAb = props.isAb

    const isAb = props.isAb
    if (this.isSkeleton) {
      return h('card-skeleton-v2', { props })
    }
    const scopedSlots: Record<string, any> = {
      default: () => this.$slots.default,
      header: () => this.$slots.header,
      contentBottom: () => this.$slots.contentBottom,
    }
    if (this.$slots['bottomLeft'] || this.$slots['bottom-left']) {
      scopedSlots.bottomLeft = () => this.$slots['bottomLeft'] || this.$slots['bottom-left']
    }
    return this.cardInfo?.hotel_info && h('inner-card-v3', {
        props,
        scopedSlots,
        on: this.$listeners
      })
  }
})

export {
  CardTag,
  formatCardData
}

