<template>
  <div :class="className" :style="style">
    <!-- !!!prefix 和 suffix 如果是图片，可以div写死宽度后absolute载入img-->
    <!-- 标签之间能加空格，因为顶级line-height不能设置0来规避空格占位 -->
    <slot name="prefix"></slot><slot v-if="showText"  name="content" :show-text="showText"><span class="text">{{ showText }}</span></slot><span v-show="showEllipsis" class="ellipsis">{{ ellipseStr }}</span><slot name="suffix"
      ><span class="suffix">{{ suffix }}</span></slot>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch } from "vue-property-decorator";
import throttle from "lodash/throttle";

@Component({
  components: {},
})
export default class Ellipsis extends Vue {
  @Prop({ type: String, default: "" }) text!: any;
  @Prop({ type: Number, default: 0 }) row!: number; // 几行， 如果没有行数或suffix就是普通渲染
  @Prop({ type: String, default: "..." }) ellipseStr!: string; // 省略号
  @Prop({ type: String, default: "" }) suffix!: string; // 结尾的重点单词
  @Prop({ type: Boolean, default: true }) resize!: boolean; // 是否resize触发变化
  @Prop({ type: Number, default: null }) initFontSize!: null
  @Prop({ type: Number, default: null }) lineHeight!: null

  throttleResize: any = throttle(() => {
    this.onResize();
  }, 200);

  fontSize: number = 14;

  showText: string = this.text;

  showEllipsis = false;

  firstLoad = true;

  get className() {
    return {
      'hotel-ellipsis': true,
      'has-ellipsis': this.showEllipsis,
      'hasSuffix': this.hasSuffix,
    }
  }

  get maxHeight() {
    return this.row ? this.fontSize * (this.lineHeight || 1.5) * this.row : 0;
  }

  get hasSuffix() {
    return this.suffix || this.$slots.suffix;
  }

  get style() {
    if (!this.row) {
      return;
    }
    // 没有Suffix则直接简单处理
    return !this.hasSuffix
      ? {
          overflow: "hidden",
          "text-overflow": "ellipsis",
          "white-space": "wrap",
          display: "-webkit-box",
          "-webkit-line-clamp": this.row,
          "-webkit-box-orient": "vertical",
        }
      : {
          maxHeight: this.maxHeight ? `${this.maxHeight}px` : undefined,
          overflow: "hidden",
          wordBreak: "break-word",
          lineHeight: 1.5,
        };
  }

  init() {
    const preFontSize = this.fontSize;
    this.setFontSize();
    // this.showText = this.text
    this.showEllipsis = false;
    this.firstLoad = true;
    // 如果字体和文本未变化， 不会主动触发 Watch, 则需要手动触发showText改变
    if (this.showText === this.text && preFontSize === this.fontSize) {
      return this.sliceText();
    }
    this.showText = this.text;
  }

  mounted() {
    this.init();
    this.resize && window.addEventListener("resize", this.throttleResize);
  }

  beforeDestroy() {
    this.resize && window.removeEventListener("resize", this.throttleResize);
  }

  setFontSize() {
    this.fontSize = this.initFontSize || 
      parseInt(window.getComputedStyle?.(this.$el).fontSize || "14") || 0;
  }

  onResize() {
    this.init();
  }

  sliceText() {
    // 如果是不限制行数，或者没有suffix则不进行动态隐藏
    if (!this.row || !this.hasSuffix) {
      return;
    }
    this.$nextTick(() => {
      const scrollHeight = this.$el.scrollHeight;
      // 优化一下，不是一个个减 @damon， 大于2行（前缀和后缀） + row的高度 则直接直接取 this.showText *
      if (scrollHeight <= this.maxHeight && this.firstLoad) {
        // 首次刚好放的下去
        this.firstLoad = false;
        this.showEllipsis = false;
        return;
      }
      this.firstLoad = false;
      this.showEllipsis = true;

      if (scrollHeight > 2 * this.maxHeight) {
        this.showText = this.showText.slice(
          0,
          -Math.floor(this.showText.length / 2)
        );
      } else if (scrollHeight > this.maxHeight) {
        this.showText = this.showText.slice(0, -1);
      }
    });
  }

  @Watch("text")
  // @damon suffix slot 改变没监听
  @Watch("suffix")
  initChange() {
    this.init();
  }

  @Watch("showText")
  @Watch("fontSize")
  showTextChange() {
    this.sliceText();
  }
}
</script>

<style lang="scss" scoped>
.hotel-ellipsis {
  .suffix {
    font-weight: $fontWeight-bold;
  }
}
</style>
