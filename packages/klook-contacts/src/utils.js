// 委托事件判断target
export function getRealCurrentTarget(currentTarget, target, validFn) {
  while (target) {
    if (validFn(target)) {
      return target
    }
    if (target === currentTarget || target.parentNode === currentTarget) {
      break
    } else {
      target = target.parentNode
    }
  }

  return null
}

// 通过首字母对数据分组 数据分组
export function groupByLetter(data, searchVal, searchFunc) {
  const tempCache = Object.create(null)

  return data.reduce((preVal, curVal) => {
    // 过滤搜索数据
    if(searchVal !== '') {
      if(searchFunc) {
        if(searchFunc(searchVal, curVal)) { return preVal }
      } else if(!(~curVal.label.toLocaleUpperCase().indexOf(searchVal.toLocaleUpperCase()))) {
        return preVal
      }
    }

    const letter = curVal.groupKey ? String(curVal.groupKey).split(' ')[0] : String(curVal.label)[0].toLocaleUpperCase()
    if (tempCache[letter] !== undefined) {
      preVal[tempCache[letter]].value.push(curVal)
    } else {
      tempCache[letter] = preVal.length
      preVal.push({
        name: curVal.groupKey || letter,
        letter,
        value: [curVal]
      })
    }

    return preVal
  }, [])
}
