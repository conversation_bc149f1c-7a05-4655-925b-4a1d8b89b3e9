<template>
  <div
    class="contacts-wrap"
    :style="{
      height: height
    }"
    :class="{
      [`contacts-wrap-${platform}`]: true,
    }"
    @touchmove="handleTouchMove"
  >
    <div
      :ref="isFixedSearch ? '' : 'scrollDom'"
      class="contacts-content"
      :class="{
        'contacts-static-search': !isFixedSearch,
        'contacts-fixed-search': isFixedSearch,
        'hide-scroll-bar': realShowNav,
      }"
      :style="{
        height: height
      }"
      @scroll="handleScroll"
    >
      <div v-if="isShowSearch && isGroup" class="search-wrap">
        <div
          class="contacts-search-box"
          data-spm-module="AreaSearchBar"
          data-spm-virtual-item="__virtual"
          v-galileo-click-tracker="{ spm: 'AreaSearchBar', componentName: 'contacts' }"
        >
          <klk-input
            v-model="searchVal"
            :placeholder="searchPlaceholder"
            :size="platform === 'mobile' ? 'normal' : 'large'"
            class="search-input"
            clearable
          >
            <svg
              slot="prepend"
              class="search-icon"
              xmlns="http://www.w3.org/2000/svg"
              width="20"
              height="20"
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path fill-rule="evenodd" d="M1.667 8.75a7.083 7.083 0 1112.458 4.614.7.7 0 01.196.134l3.084 3a.7.7 0 11-.977 1.004l-3.083-3a.698.698 0 01-.157-.23A7.083 7.083 0 011.667 8.75zm7.083 5.683a5.683 5.683 0 110-11.366 5.683 5.683 0 010 11.366z" clip-rule="evenodd" />
            </svg>
          </klk-input>
        </div>
        <slot name="searchAppend"></slot>
      </div>
      <slot></slot>
      <div
        :ref="!isFixedSearch ? '' : 'scrollDom'"
        class="list-box"
        :class="{
          'hide-scroll-bar': realShowNav
        }"
        @click="handleSelect"
        @scroll="handleScroll"
      >
        <div
          v-for="item in groupingData"
          :key="item.name"
          :class="{
            groupItem: true,
            js_contactsBoxGroup: true
          }"
        >
          <h4 class="js_contactsBoxGroupTitle group-title">{{ item.name }}</h4>
          <div class="group-list">
            <div
              v-for="(sub, i) in item.value"
              :key="sub.value + sub.label"
              :class="{
                js_valueItem: true,
                valueItem: true,
                itemActive: isActive(sub.value, item.isCurGroup)
              }"
              :data-value="sub.value"
              :data-spm-module="`Area_LIST?oid=area_${sub.id}&idx=${i}&len=${allData.length}`"
              data-spm-virtual-item="__virtual"
              v-galileo-click-tracker="{ spm: 'Area_LIST', componentName: 'contacts' }"
            >
              <span class="group-label">{{ sub.label }}</span>
              <svg
                v-if="isActive(sub.value, item.isCurGroup)"
                class="itemActive-icon"
                xmlns="http://www.w3.org/2000/svg"
                width="20"
                height="20"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path fill-rule="evenodd" d="M16.429 5.918a.833.833 0 01-.014 1.178l-7.051 6.892a1.667 1.667 0 01-2.406-.08l-3.412-3.804a.833.833 0 111.24-1.113L8.2 12.796l7.051-6.892a.833.833 0 011.179.014z" clip-rule="evenodd" />
              </svg>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div
      v-if="isGroup && realShowNav"
      ref="navBarDom"
      :class="{
        js_contactsNavBar: true,
        navBar: true,
        [`navBar-${navModel}`]: true
      }"
      @touchstart="handleTouch($event, true)"
      @touchend="handleTouch($event, false)"
    >
      <template v-for="(item, index) in groupingData">
        <span
          v-if="item.letter"
          :key="item.name"
          :data-index="index"
          :class="{
            js_navItem: true,
            navActive: index === curNavIndex
          }"
        >{{ item.letter }}</span>
      </template>
    </div>
    <div v-show="isShowIndicator" class="indicator-box">
      {{ curIndicatorText }}
    </div>
  </div>
</template>

<script>
import throttle from 'lodash/throttle'
import { groupByLetter, getRealCurrentTarget } from './utils'

export default {
  props: {
    platform: {
      type: String,
      default: 'mobile'
    },
    value: {
      type: null,
      default: undefined
    },
    height: {
      type: String,
      default: '80vh'
    },
    topData: {
      type: Array,
      default: () => []
    },
    topLetter: {
      type: String,
      default: '#'
    },
    topName: {
      type: String,
      default: 'Recommended'
    },
    // 当前选择项的值
    currentValue: {
      type: null,
      default: ''
    },
    currentName: {
      type: String,
      default: 'Current Selection'
    },
    allData: {
      type: Array,
      default: () => []
    },
    // 是否显示搜索
    isShowSearch: {
      type: Boolean,
      default: true
    },
    isFixedSearch: {
      type: Boolean,
      default: true
    },
    // search placeholder
    searchPlaceholder: {
      type: String,
      default: 'Search'
    },
    // 是否对数据进行分组
    isGroup: {
      type: Boolean,
      default: true
    },
    // 是否显示导航器
    isShowNav: {
      type: Boolean,
      default: true
    },
    // 导航器模式
    navModel: {
      type: String,
      default: 'touchmove' // touchmove | scroll
    },
    // 指示器显示时间
    indicatorDuration: {
      type: Number,
      default: 1500
    },
    // 搜索筛选逻辑
    searchFunc: {
      type: Function,
      default: null
    }
  },
  data() {
    return {
      isShowIndicator: false,
      searchVal: '',
      curNavIndex: 0
    }
  },
  computed: {
    realShowNav() {
      return this.platform === 'mobile' && this.isShowNav
    },
    curIndicatorText() {
      return this.groupingData[this.curNavIndex]?.letter
    },
    isTouchModel() {
      return this.navModel === 'touchmove'
    },
    // 当前选项
    curItem() {
      if (this.currentValue && (!this.isShowSearch || this.searchVal === '')) {
        // 当前选项
        const curItem = this.allData.filter(item => item.value === this.currentValue)

        if (curItem.length > 0) {
          return {
            isCurGroup: true,
            name: this.currentName,
            letter: '',
            value: curItem
          }
        }
      }

      return null
    },
    // 分组数据
    groupingData() {
      const preItems = []

      // 不分组数据
      if (!this.isGroup) {
        return [
          {
            name: '',
            letter: '',
            value: this.allData
          }
        ]
      }

      // 非搜索状态
      if ((!this.isShowSearch || this.searchVal === '')) {
        const hasTopData = this.topData && this.topData.length > 0

        // 当前选项
        if (this.curItem) {
          preItems.push(this.curItem)
        }

        // 推荐热门选项
        if (hasTopData) {
          preItems.push({
            isTop: true,
            name: this.topName,
            letter: this.topLetter,
            value: this.topData
          })
        }
      }

      return [
        ...preItems,
        ...groupByLetter(this.allData, this.searchVal, this.searchFunc)
      ]
    }
  },
  watch: {
    groupingData() {
      this.$nextTick(() => {
        this.updatePosition()
      })
    }
  },
  mounted() {
    this.setNavBarSelect(0)

    // 记录组件状态
    this.$cache = {
      // 是否开启滚动监听
      shouldObserverScroll: true,

      // 是否开启touchmove监听
      shouldObserverTouchMove: false,

      // 分组高度缓存
      itemsPosition: [],

      // 高度缓存，高度不变不重新计算分组高度
      scrollHeight: 0,

      // 按屏幕计算右侧导航 x 偏移量
      touchmoveX: 0
    }

    this.$nextTick(() => {
      this.updatePosition()
    })

    const resizeEvent = throttle(() => {
      this.resize()
    }, 250, {
      trailing: true
    })

    // 变更重新计算即可
    window.addEventListener('resize', resizeEvent)
    this.$once('hook:beforeDestroy', () => {
      window.removeEventListener('resize', resizeEvent)
    })
  },
  beforeDestroy() {
    clearTimeout(this.$cache.indicatorDuration)
  },
  methods: {
    isActive(val, isCur) {
      return isCur || val === this.value
    },
    resize() {
      this.updatePosition()
    },

    // 快捷导航点击
    setAnchorPoint(target) {
      const index = +(target.dataset.index || 0)

      if (this.curNavIndex === index) {
        return
      }

      // 停止滚动监听
      // 优化性能，以及可能产生滚动定位计算和当前选中点的偏差
      this.setScrollObserve(false)

      this.setNavBarSelect(index)

      // 通过缓存的高度做计算
      this.$refs.scrollDom.scrollTop = this.$cache.itemsPosition[index]

      this.showIndicator()

      setTimeout(() => {
        this.setScrollObserve(true)
      }, 16)
    },

    showIndicator() {
      if (this.indicatorDuration <= 0) { return }

      clearTimeout(this.$cache.indicatorTimer)
      this.$cache.indicatorTimer = setTimeout(() => {
        this.isShowIndicator = false
      }, this.indicatorDuration)

      this.isShowIndicator = true
    },

    handleClickAnchorPoint(event) {
      getRealCurrentTarget(event.currentTarget, event.target, (t) => {
        // 点击右边栏关键词
        if (t.className && typeof t.className === 'string' && ~t.className.indexOf('js_navItem')) {
          this.setAnchorPoint(t)
          return true
        }
      })
    },

    // 委托事件
    handleSelect(event) {
      event.stopPropagation()
      getRealCurrentTarget(event.currentTarget, event.target, (t) => {
        // 选中数据
        if (t.className && typeof t.className === 'string' && ~t.className.indexOf('js_valueItem')) {
          this.$emit('input', t.dataset.value)
          this.$emit('onSelect', t.dataset.value)
          return true
        }
      })
    },

    getCurrentSection(scrollTop) {
      for (let i = this.$cache.itemsPosition.length - 1; i >= 0; i -= 1) {
        const cur = this.$cache.itemsPosition[i]
        if ((cur || 0) <= scrollTop) {
          return i
        }
      }

      return 0
    },

    // 设置导航条选中index
    setNavBarSelect(index) {
      if (!this.realShowNav) { return }

      this.curNavIndex = index === 0 ? (this.curItem ? 1 : 0) : index
    },

    // 计算每个 section position top
    updatePosition() {
      if (!this.allData.length > 0) { return }

      const scrollDom = this.$refs.scrollDom

      // 如果滚动容器高度没发生变化  不重新计算
      const curScrollHeight = scrollDom.scrollHeight

      // 重新计算 touchmoveX
      if (this.realShowNav && this.isTouchModel) {
        const barClientRect = this.$refs.navBarDom.getBoundingClientRect()
        this.$cache.touchmoveX = barClientRect.left + (barClientRect.width || 6) / 2
      }

      // 高度不变复用缓存
      if (this.$cache.scrollHeight === curScrollHeight) { return }

      // 更新总高度
      // 更新各个group的高度
      this.$cache.scrollHeight = curScrollHeight
      this.$cache.itemsPosition = Array.from(scrollDom.querySelectorAll('.js_contactsBoxGroup')).map((item) => {
        return item.offsetTop
      })
    },

    // 分组
    handleScroll() {
      if (!this.getScrollObserve()) { return }

      const scrollDom = this.$refs.scrollDom
      const scrollTop = Math.round(scrollDom.scrollTop) // 有些浏览器可能不四舍五入

      const currentSectionIndex = this.getCurrentSection(scrollTop)
      this.setNavBarSelect(currentSectionIndex)
    },

    // 滚动事件监听开启关闭
    setScrollObserve(value) {
      this.$cache.shouldObserverScroll = value
    },
    getScrollObserve() {
      return this.$cache.shouldObserverScroll && this.groupingData
    },

    // touchmove 事件监听开关
    setTouchMoveObserve(value) {
      this.$cache.shouldObserverTouchMove = value
    },
    getTouchMoveObserve() {
      return this.$cache.shouldObserverTouchMove
    },
    handleTouch(event, value) {
      // touch start 也需要选中
      if (value) { this.handleClickAnchorPoint(event) }

      // 不需要监听
      if (!this.isTouchModel) { return }

      event.preventDefault()
      this.setTouchMoveObserve(value)

      !value && this.$inhouse.track('custom', 'body', {
        spm: 'BriefAreaSelection',
        ext: {
          BriefText: this.curIndicatorText || '',
          BriefIndex: this.curNavIndex - 1 || '',
          BriefLength: (this.groupingData && this.groupingData.length - 1) || 0
        }
      })
    },
    handleTouchMove(event) {
      // 不需要监听
      if (!this.isTouchModel || !this.getTouchMoveObserve()) { return }

      event.stopPropagation()
      const target = document.elementFromPoint(this.$cache.touchmoveX, event.changedTouches[0].clientY)

      if (target && target.classList && target.className.includes('js_navItem')) {
        this.setAnchorPoint(target)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
/* search box */
.search-wrap {
  padding: 12px 20px;
  background: $color-bg-3;
}

.contacts-search-box {
  position: relative;
}

.search-icon {
  color: $color-text-disabled;
  margin-left: 12px;
}

.contacts-static-search {
  position: relative;
  overflow: auto;
  overscroll-behavior: contain;
  -webkit-overflow-scrolling: touch;
}

.contacts-fixed-search {
  .list-box {
    position: relative;
    overflow: auto;
    padding: 0;
    overscroll-behavior: contain;
    -webkit-overflow-scrolling: touch;
  }
}

.hide-scroll-bar {
  scrollbar-width: none; /* firefox */
  -ms-overflow-style: none; /* IE 10+ */
  &::-webkit-scrollbar { /* for Chrome */
    display: none;
  }
}

.list-box {
  padding: 0;
}

.groupItem {
  padding: 0 20px;
}

.group-title {
  margin: 0;
  position: sticky;
  top: -0.5px;
  z-index: 1;
  padding: 24px 0 8px;
  color: $color-text-primary;
  background: $color-bg-1;

  @include font-body-m-bold;
}

.valueItem {
  position: relative;

  @include font-body-m-regular;

  padding: 12px 0;
  color: $color-text-primary;
  border-bottom: 1px solid $color-border-dim;

  .itemActive-icon {
    display: block;
    position: absolute;
    color: $color-brand-primary;
    right: 12px;
    top: 12px;
  }

  &.itemActive {
    .group-label {
      color: $color-brand-primary;
    }
  }
}

.group-label {
  //white-space: nowrap;
  //text-overflow: ellipsis;
  //overflow: hidden;
  display: block;
  padding-right: 25px;
}

.contacts-wrap {
  position: relative;
  display: flex;
  flex-direction: column;

  .list-box {
    flex: 1;
  }
}

.contacts-content {
  position: relative;
  display: flex;
  flex-direction: column;
}

/* contacts-wrap navBar */
.navBar {
  position: absolute;
  box-sizing: border-box !important;
  padding: 0 2px 0 0;
  top: 50%;
  transform: translate(0, -50%);
  right: 0;
  color: $color-brand-primary;
  max-height: 100%;

  & > span {
    @include font-caption-m-regular;

    box-sizing: content-box;
    display: block;
    width: 18px;
    border-radius: $radius-circle;
    text-align: center;

    &.navActive {
      background: $color-brand-primary;
      color: $color-common-white;
    }
  }

  &.navBar-scroll {
    overflow: auto;
    overscroll-behavior: contain;
    -webkit-overflow-scrolling: touch;
  }

  &.navBar-touchmove {
    & > span {
      width: 1.5vh;
      line-height: 1.5vh; /* stylelint-disable-line */
      padding: 2px;
    }
  }
}

.indicator-box {
  position: absolute;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2;
  width: 80px;
  height: 80px;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  border-radius: $radius-circle;
  color: $color-text-reverse;
  background: $color-overlay-default-3;
  pointer-events: none;

  @include font-heading-xl;
}

.contacts-wrap-desktop {
  .group-list {
    display: flex;
    flex-wrap: wrap;
  }

  .search-wrap {
    padding: 12px 0;
  }

  .groupItem {
    padding: 0;
  }

  .valueItem {
    width: 33.33%;
    border: 1px solid transparent;
    padding: 12px;
    border-radius: $radius-m;
    cursor: pointer;

    @include font-body-m-semibold;

    &:hover {
      background: $color-bg-3;
    }
  }

  .itemActive {
    border-color: $color-brand-primary;
  }
}
</style>
