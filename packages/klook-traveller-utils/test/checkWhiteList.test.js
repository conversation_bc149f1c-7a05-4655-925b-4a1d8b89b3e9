import { checkWhiteList } from '../src/urlWhitelist';

describe('checkWhiteList function', () => {
  // 测试白名单中的域名
  test('should return true for whitelisted domains', () => {
    expect(checkWhiteList('klook.com')).toBe(true);
    expect(checkWhiteList('www.klook.com')).toBe(true);
    expect(checkWhiteList('https://www.klook.com')).toBe(true);
    expect(checkWhiteList('https://klook.com/about')).toBe(true);
    expect(checkWhiteList('www.klook.cn')).toBe(true);
    expect(checkWhiteList('www.klook.io')).toBe(true);
    expect(checkWhiteList('www.klook.com.kh')).toBe(true);
    expect(checkWhiteList('travel.japanrailpassnow.com')).toBe(true);
    expect(checkWhiteList('https://travel.japanrailpassnow.com/zh-HK/?aid=35290&utm_medium=affiliate-alwayson&utm_source=non-network&utm_campaign=35290&utm_term&utm_content')).toBe(true);
    expect(checkWhiteList('www.fws.klooktest.io')).toBe(true);
    expect(checkWhiteList('travel.japanrailpassnow.co.uk')).toBe(true);
    expect(checkWhiteList('www.klook.top')).toBe(true);
    expect(checkWhiteList('klook.co.nz')).toBe(true);
    expect(checkWhiteList('www.klookapp.com')).toBe(true);
    expect(checkWhiteList('taipei-travel.com')).toBe(true);
    expect(checkWhiteList('www.klook.service')).toBe(true);
    expect(checkWhiteList('zurich-travel.com')).toBe(true);
    expect(checkWhiteList('klkcache.com')).toBe(true);
    expect(checkWhiteList('trip.ai')).toBe(true);
    expect(checkWhiteList('myklook.com')).toBe(true);
    expect(checkWhiteList('www.flickket.com')).toBe(true);
    expect(checkWhiteList('flickket.com')).toBe(true);
    expect(checkWhiteList('vietnam-travel-experience.com')).toBe(true);
    expect(checkWhiteList('klkcache.com')).toBe(true);
    expect(checkWhiteList('klook.com.au')).toBe(true);
    expect(checkWhiteList('hong-kong-travel.com')).toBe(true);
    expect(checkWhiteList('sydney-travel-guide.com')).toBe(true);
    expect(checkWhiteList('thingstodo.cheapoair.com')).toBe(true);
    expect(checkWhiteList('robert-y.partner.klook.com')).toBe(true);
    expect(checkWhiteList('teamlabfutureparkandanimalsofflowerssymbioticlivesinnagoya.partner.flickket.com')).toBe(true);
    expect(checkWhiteList('zoori-residence-inn-tagaytay.partner.klook.com')).toBe(true);
    expect(checkWhiteList('taipei101.partner.klook.com')).toBe(true);
    expect(checkWhiteList('seoul-travel-guide.com')).toBe(true);
    expect(checkWhiteList('seoul-travel.com')).toBe(true);
    expect(checkWhiteList('switzerland-travel-guide.com')).toBe(true);
    expect(checkWhiteList('switzerland-travel.com')).toBe(true);
    expect(checkWhiteList('klook.jp')).toBe(true);
    expect(checkWhiteList('klook.com.kh')).toBe(true);
    expect(checkWhiteList('klookcareers.com')).toBe(true);
    expect(checkWhiteList('klook.us')).toBe(true);
  });

  // 测试非白名单中的域名
  test('should return false for non-whitelisted domains', () => {
    expect(checkWhiteList('google.com')).toBe(false);
    expect(checkWhiteList('example.org')).toBe(false);
    expect(checkWhiteList('11flickket.com')).toBe(false);
    expect(checkWhiteList('klookapp1.com')).toBe(false);
    expect(checkWhiteList('klook.cnm')).toBe(false);
    expect(checkWhiteList('fakeklook.com')).toBe(false);
  });
});