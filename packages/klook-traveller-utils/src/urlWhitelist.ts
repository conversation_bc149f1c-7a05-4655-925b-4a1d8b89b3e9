export function urlWhiteList() {
  return [
    /(^|\.)klook.(cn|com|io|ai|asia|ca|co|top|dk|es|hk|jp|service|us)$/,
    /(^|\.)(flickket|klkcache|klktech|klookapi|klookcareers|klookholiyay|klookmail|klookstatic|klookyourway|jomklookholiday|japanrailpassnow|klookapp|vietnam-travel-experience|klktechtest|fliptiq|myklook)\.com$/,
    /(^|\.)(klookapp|klktechtest|klooktest|klktech)\.(cn|io)$/,
    /(^|\.)(klooktest|klook)\.top$/,
    /(^|\.)klooktest\.com$/,
    /(^|\.)(australia|bali|bangkok|edinburgh|england|hokkaido|hong-kong|iceland|italy|japan|korea|kuala-lumpur|kyoto|london|melbourne|myklook|okinawa|osaka|philippines|phuket|san-francisco|seoul|spain|stockholm|switzerland|sydney|taipei|taiwan|vietnam|zurich)-travel\.com$/,
    /(^|\.)(bali|bangkok|italy|hokkaido|kyoto|london|san-francisco|seoul|okinawa|osaka|spain|sydney|switzerland)-travel-guide\.com$/,
    /(^|\.)trip\.ai$/,
    /(^|\.)klook\.com\.(kh|au|hk)$/,
    /(^|\.)klook\.co\.nz$/,
    /(^|\.)thingstodo\.webjet\.com\.au$/,
    /(^|\.)thingstodo\.webjet\.co\.nz$/,
    /(^|\.)thingstodo\.cheapoair\.com$/,
    /(^|\.)travel\.japanrailpassnow\.com$/,
    /(^|\.)travel\.japanrailpassnow\.co\.uk$/,
    /(^|\.)travel\.japanrailpass\.com\.au$/,
    /(^|\.)farsighted\.io$/,
    /(^|\.)bania\.travel$/,
  ];
}
/**
 * 提取 URL 中的host部分
 * @param {string} url 完整的 URL
 * @returns {string} host部分
 */
export function extractHostname(url: string): string {
  const regex = /^(?:https?:\/\/)?([^:/\n?]+)/;
  const match = url.match(regex);
  if (match && match[1]) {
    return match[1];
  }
  return '';
}
/**
 * 判断是否为白名单域名
 * @param  {string} domain
 * @return {Boolean}
 */
export function checkWhiteList(url: string): boolean {
  const whiteList = urlWhiteList();
  const hostname = extractHostname(url);

  if (!url || !hostname) {
    return false;
  }

  for (let i = 0; i < whiteList.length; i++) {
    if (whiteList[i].test(hostname)) {
      return true;
    }
  }
  return false;
}

export default {};
