/**
 * 判断两个版本字符串的大小
 * @param  {string} v1 原始版本 例：4.2.0
 * @param  {string} v2 目标版本 例：5.1.0
 * @return {number} 如果原始版本大于目标版本，则返回大于0的数值, 如果原始小于目标版本则返回小于0的数值。0当然是两个版本都相等拉。
 */
export function compareVersion(v1: string, v2: string): number {
  const _v1 = v1.split("."),
    _v2 = v2.split("."),
    _r = (_v1[0] as any) - (_v2[0] as any);

  return _r == 0 && v1 != v2
    ? compareVersion(_v1.splice(1).join("."), _v2.splice(1).join("."))
    : _r;
}
export default {};
