// The regular expression checks for the following conditions:
  // - The email address must start with a letter or number, followed by any combination of letters, numbers, or special characters.
  // - The email address must contain an "@" symbol followed by a domain name.
  // - The domain name must start with a letter, followed by any combination of letters, numbers, or hyphens.
  // - The domain name must end with a top-level domain (TLD) such as ".com", ".org", etc.
export const emailRegex = /^[a-zA-Z0-9!#$%&'*+/=?^_`{|}~-][a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]*@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+$/
/**
 * Validates an email address.
 * @param {string} email - The email address to validate.
 * @returns {boolean} - True if the email address is valid, false otherwise.
 */
export function emailValidate(email:string) {
  return emailRegex.test(email)
}

// The regular expression checks for the following conditions:
// - The username must start with a letter, followed by any combination of letters, spaces, periods, apostrophes, commas, or hyphens.
// - The username must not contain any digits.
export const userNameRegex = /^[\p{L}][\p{Zs},.'\u2019-]?([\p{L}]+[\p{Zs},.'\u2019-]?)*$/gu
/**
 * Validates a username.
 * @param {string} username - The username to validate.
 * @returns {boolean} - True if the username is valid, false otherwise.
 */
export function userNameValidate(username:string) {

  return userNameRegex.test(username)
}
export default {}
