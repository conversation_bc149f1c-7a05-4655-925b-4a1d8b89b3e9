import { supportLanguageList } from './supportLanguageList';

function parseUrl(urlString: string) {
  const urlParts = urlString.split('?');
  
  const mainUrl = urlParts[0];
  const queryString = urlParts[1] || '';
  
  const protocolEndIndex = mainUrl.indexOf('//');
  const protocol = mainUrl.slice(0, protocolEndIndex);
  const afterProtocol = mainUrl.slice(protocolEndIndex + 2);
  
  let host = '';
  let port = '';
  let path = '';
  let hash = '';
  
  if (afterProtocol.includes('/')) {
    const pathStartIndex = afterProtocol.indexOf('/');
    const hostAndPort = afterProtocol.slice(0, pathStartIndex);
    const pathAndHash = afterProtocol.slice(pathStartIndex);

    host = hostAndPort.split(':')[0];
    port = hostAndPort.split(':')[1] || '';

    if (pathAndHash.includes('#')) {
      const hashStartIndex = pathAndHash.indexOf('#');
      path = pathAndHash.slice(1, hashStartIndex);
      hash = `#${pathAndHash.slice(hashStartIndex + 1)}`;
    } else {
      path = pathAndHash.slice(1);
    }
  } else {
    host = afterProtocol.split(':')[0];
    port = afterProtocol.split(':')[1] || '';
  }

  const query: any = {};
  queryString.split('&').forEach(pair => {
    const [key, value] = pair.split('=');
    if (key && value) {
      query[key] = value;
    }
  });
  
  return {
    protocol: protocol,
    host: host,
    port: port || '',
    pathname: `/${path}`,
    query: query,
    hash: hash
  };
}

function formatUrl(urlObj:any) {
  let formattedUrl = `${urlObj.protocol}//${urlObj.host}`;
  
  if (urlObj.port) {
    formattedUrl += `:${urlObj.port}`;
  }
  
  formattedUrl += urlObj.pathname;
  
  const queryString = Object.keys(urlObj.query).map(key => `${key}=${urlObj.query[key]}`).join('&');
  if (queryString) {
    formattedUrl += `?${queryString}`;
  }
  
  formattedUrl += urlObj.hash;
  
  return formattedUrl;
}


const getUrlLanguage = (pathname: string) => {
  const matches = pathname.match(
    new RegExp(getRegLangStr(supportLanguageList))
  );
  return matches && matches[1];
};

function getRegLangStr(supportLanguageList: string[]) {
  // ^/(|de|en-AU|en-CA|en-GB|en-HK|en-IN|en-MY|en-NZ|en-PH|en-SG|en-US|es|fr|id|it|ja|ko|ru|th|vi|zh-CN|zh-HK|zh-TW|ms-MY)/
  const langPathArr = supportLanguageList.reduce((pre: string[], lang: string) => {
    lang === "en" ? pre.unshift("") : pre.push(`${lang}`);
    return pre;
  }, []);
  return `^/(${langPathArr.join("|")})/`;
}

function isFullLink(url:string) {
  // 使用正则表达式检查链接是否包含协议和域名
  const regex = /^(?:https?:\/\/)?[\w.-]+(?:\.[\w.-]+)+[\w\-._~:/?#[\]@!\$&'()*+,;=%]+$/;
  return regex.test(url);
}

interface IHostConfig {
  targetHost: string;
  market: string;
  whiteList?: string[];
}

export function genReplaceKlookHost(IHostConfig: IHostConfig) {
  const { targetHost, market, whiteList = ['www.klook.com', 'www.klook.cn'] } = IHostConfig;
  const replaceKlookHost = (url: string) => {
    // 0.判断是否完整链接 如果不是 直接返回
    if (!isFullLink(url)) return url;
    // 1.获取当前链接的host和pathname
    const currentUrl = parseUrl(url)
    let { host, pathname } = currentUrl;
    // 2.判断是否有效host
    const isValidHost = whiteList.includes(host);
    if (!isValidHost) return url;

    // 2.1 如果host相同，直接返回
    if (host === targetHost) return url;
    currentUrl.host = targetHost;
    
    // 3.如果market是cn,语言替换成zh-CN
    if (market === "cn") {
      const urlLanguage = getUrlLanguage(pathname);
      pathname = !urlLanguage ? `/zh-CN${pathname}` : pathname.replace(urlLanguage, "zh-CN");
      currentUrl.pathname = pathname
    }
    return formatUrl({ ...currentUrl });
  };
  return {
    replaceKlookHost
  }
}

export default {}
