// 创建日历并向系统添加日历提醒

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore
import { createEvent, EventAttributes } from 'ics'

function hashUid(content: string) {
  // 初始化哈希值
  let hash = 0;
  // 遍历输入内容的每个字符
  for (let i = 0; i < content.length; i++) {
    hash = (hash << 5) + hash + content.charCodeAt(i);
    hash = hash & hash; // 保持结果为 32 位整数
  }
  // 将哈希值转换为正数
  hash = Math.abs(hash);
  // 将哈希值转换为十六进制字符串
  return hash.toString(16);
}

export default async function addCalendar(fileName: string, eventData: EventAttributes) {
  const filename = fileName
  try {
    const uid = hashUid(JSON.stringify(eventData))
    const file = await new Promise<File>((resolve, reject) => {
      createEvent({ ...eventData, uid}, (error: Error | undefined, value: string) => {
        if (error) {
          reject(error)
          console.error('Create Event Error: ', error)
        }
        resolve(new File([value], filename, { type: 'text/calendar' }))
      })
    })
    const url = URL.createObjectURL(file);

    // trying to assign the file URL to a window could cause cross-site
    // issues so this is a workaround using HTML5
    const anchor = document.createElement('a');
    anchor.href = url;
    anchor.download = filename;

    document.body.appendChild(anchor);
    anchor.click();
    document.body.removeChild(anchor);

    URL.revokeObjectURL(url);
  } catch (error) {
    console.log('Add Calendar Func Wrong: ', error)
  }
}
