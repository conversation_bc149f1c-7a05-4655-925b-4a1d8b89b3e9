<template>
  <div v-if="dataInfo && dataInfo.hot_airports && dataInfo.hot_airports.length > 0" class="popular-airports">
    <h3 class="popular-airports__title">{{ dataInfo.title }}</h3>
    <klk-card-swiper class="popular-airports__swiper" item-width="51.3vw" :item-gap="10" :scroll-mode="true">
      <klk-card-swiper-item v-for="(item, index) in dataInfo.hot_airports" :key="index">
        <div class="popular-airports__item">
          <div class="popular-airports__name">{{ item.airportName }}({{ item.iataCode }}) {{ $t('17102-to') }} {{ item.cityName }}</div>
          <div class="popular-airports__price">
            <em>{{ $t('10638-From') }}</em>
            <span>{{ item.price.symbol }} {{ item.price.price }}</span>
          </div>
          <!-- <a class="popular-airports__link" :href="`${langPath}/airport-transfers/service/${item.iataCode.toLowerCase()}`"></a> -->
          <a class="popular-airports__link" :href="`https://${host}${langPath}/airport-transfers/service/${item.seoUrl}/`"></a>
        </div>
      </klk-card-swiper-item>
    </klk-card-swiper>
  </div>
</template>

<script lang="ts">
import {Component, Prop} from 'vue-property-decorator'
import Base from "@/common/base";

@Component
export default class PopularAirports extends Base {
  @Prop({ type: Object, default: () => null }) data!: any

  dataInfo: any = this.data
  langPath = ''

  host = ''

  mounted() {
    if (this.getKlkLanguage() !== 'en') {
      this.langPath = `/${this.getKlkLanguage()}`
    }

    if (this.getKlkHost()) {
      this.host = this.getKlkHost()
    }
  }
}
</script>

<style lang="scss" scoped>
@mixin text-ellipsis($line-clamp: 2) {
  display: -webkit-box;
  -webkit-line-clamp: $line-clamp;
  -webkit-box-orient: vertical;
  text-overflow: ellipsis;
  overflow: hidden;
}
.popular-airports {
  //&__title {
  //  margin: 32px 20px 0;
  //}

  &__swiper {
    margin-top: 16px;
  }

  &__item {
    height: 125px;
    border: 1px solid $color-border-dim;
    padding: 16px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    border-radius: $radius-xl;
    position: relative;
    background-color: $color-bg-1;
  }

  &__name {
    @include font-body-s-bold;
    @include text-ellipsis(3);
  }

  &__price {
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
    align-content: center;

    em {
      @include font-caption-m-regular;
      color: $color-text-secondary;
      font-style: normal;
      align-self: center;
    }

    span {
      @include font-body-s-semibold;
      color: #ff5b00;
      margin-left: 4px;
    }
  }

  &__link {
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
  }
}
</style>
<style lang="scss">
.popular-airports {

  .klk-card-swiper-items-wrapper {
    overflow: hidden;
  }
  .klk-card-swiper-items {
    width: 100%;
    display: flex;
    flex-wrap: nowrap;
    overflow-x: auto;
    overflow-y: hidden;
    padding: 0 0 20px;
    margin-bottom: -20px;
  }
}
</style>
