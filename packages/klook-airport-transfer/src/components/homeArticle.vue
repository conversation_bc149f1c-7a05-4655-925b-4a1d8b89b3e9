<template>
  <div v-if="dataInfo && dataInfo.article_list && dataInfo.article_list.length > 0" class="guides">
    <h3 class="guides__title">{{ dataInfo.title }}</h3>
    <klk-card-swiper class="guides__swiper" :item-width="275" :item-gap="20">
      <klk-card-swiper-item v-for="(item, index) in dataInfo.article_list" :key="index">
        <div class="guides__item">
          <div class="guides__item-header">
            <div class="guides__item-desc" v-html="item.title"></div>
            <div class="guides__item-author">
              <img class="guides__item-avatar" :src="item.author_img" alt="">
              <div class="guides__item-authorname">{{ item.author_name }}</div>
            </div>
          </div>
          <div class="guides__item-footer">
            <img class="guides__item-img" :src="item.article_image" alt="">
          </div>
          <a class="guides__link" :href="item.article_url"></a>
        </div>
      </klk-card-swiper-item>
    </klk-card-swiper>
  </div>
</template>

<script lang="ts">
import {Component, Prop} from 'vue-property-decorator'
import Base from "@/common/base";

@Component
export default class Guides extends Base {
  @Prop({ type: Object, default: () => null }) data!: any

  dataInfo: any = this.data
}
</script>

<style lang="scss" scoped>
@mixin text-ellipsis($line-clamp: 2) {
  display: -webkit-box;
  -webkit-line-clamp: $line-clamp;
  -webkit-box-orient: vertical;
  text-overflow: ellipsis;
  overflow: hidden;
}
.guides {
  width: 1160px;
  margin: 48px auto 0;

  &__title {
    @include font-heading-m;
  }

  &__swiper {
    margin-top: 24px;
  }

  &__item {
    height: 269px;
    border: 1px solid $color-border-dim;
    padding: 16px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    border-radius: $radius-xl;
    position: relative;

    &-desc {
      @include font-body-m-semibold;
      @include text-ellipsis(2);
    }

    &-author {
      display: flex;
      flex-direction: row;
      align-items: center;
      margin-top: 7px;
    }

    &-avatar {
      flex: 16px 0 0;
      height: 16px;
      border-radius: $radius-circle;
      overflow: hidden;
    }

    &-authorname {
      @include font-body-s-regular;
      @include text-ellipsis(1);
      margin-left: 6px;
    }

    &-footer {
      width: 100%;
      height: 148px;
      border-radius: $radius-l;
      overflow: hidden;
    }

    &-img {
      width: 100%;
      height: 100%;
      border-radius: $radius-l;
    }
  }

  &__link {
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
  }
}
</style>
