<template>
  <div class="review-filter-sort">
    <div class="review-filter-sort-button" @click="open">
      <span class="review-filter-sort-title">
        {{ $t('search_sort_by') }}
      </span>
      <span class="review-filter-sort-text">
        {{ text }}
      </span>
      <IconTriangleDown class="review-filter-sort-icon" theme="filled" size="16" fill="#333333" />
    </div>

    <klk-bottom-sheet
      v-if="visible"
      show-close
      transfer
      :title="$t('28270')"
      :visible.sync="visible"
      class="review-filter-sort-modal"
    >
      <div
        v-for="item in sortData"
        :key="item.show_text"
        :class="['review-filter-sort-modal-item', {selected: value === item.sort_type}]"
        @click="handleClick(item.sort_type)"
      >
        <span>
          {{ item.show_text }}
        </span>

        <SvgIcon
          v-show="value === item.sort_type"
          name="common#icon_other_click_s_new"
          width="20"
          height="20"
        ></SvgIcon>
      </div>
    </klk-bottom-sheet>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator'
import { IconTriangleDown } from '@klook/klook-icons'

@Component({
  components: {
    IconTriangleDown
  }
})
export default class ReviewFilterSort extends Vue {
  @Prop() value!: string
  @Prop() sortData!: any[]

  visible = false

  open() {
    this.visible = true
  }

  get text() {
    let text = ''
    this.sortData.forEach((item) => {
      const { show_text, sort_type } = item
      if (this.value === sort_type) {
        text = show_text
      }
    })
    return text
  }

  handleClick(val: number) {
    this.$emit('change-sort', val)
    this.visible = false
  }
}
</script>

<style lang="scss" scoped>
.review-filter-sort {
  display: inline-block;
  &-button {
    display: flex;
    align-items: center;
  }

  &-title {
    @include font-body-s-regular;
    color: $color-text-secondary;
  }

  &-text {
    margin-left: 4px;
    @include font-body-s-regular;
    color: $color-text-primary;
  }

  &-icon {
    margin-left: 8px;
    display: flex;
    align-items: center;
  }

  &-modal {
    &-item {
      padding: 10px 0;
      display: flex;
      justify-content: space-between;
      align-items: center;
      position: relative;
      @include font-body-m-regular;
      color: $color-text-primary;

      &.selected {
        @include font-body-m-semibold;
        color: $color-brand-primary;
      }
    }
  }
}
</style>
<style lang="scss">
.review-filter-sort-modal .klk-bottom-sheet-mask {
  background-color: $color-overlay-default-2;
}

.review-filter-sort-modal .klk-bottom-sheet-inner {
  min-height: 40%;
}
</style>
