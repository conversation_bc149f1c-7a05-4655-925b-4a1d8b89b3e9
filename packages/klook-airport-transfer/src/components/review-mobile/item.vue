<template>
  <div :class="$style.item">
    <div :class="$style.avatar">
      <div v-lazy:background-image="avatar(renderData.review.avatar)" :class="$style.avatar_img" />
      <div itemprop="author">
        <p :class="$style.name" itemprop="name"> {{ renderData.review.user_name || '--' }}</p>
        <p :class="$style.time"> {{ $t('11366-airport_transfer_review_date') }} {{ getStandarDateStr(new Date(renderData.review_time)) }} </p>
      </div>
    </div>
    <div :class="$style.contain">
      <div class="item_header-score">
        <p class="num">{{ renderData.review.rating }}<span class="num_base">/5</span></p>
        <span class="text" itemprop="name">{{ renderData.score_intro }}</span>
      </div>
      <div class="item_header-day">
        {{ $t('11363-airport_transfer_review_from', [renderData.airport_name, renderData.airport_code]) }}
      </div>
      <div :class="$style.content">
        <p v-show="showOrigin">
          {{ renderData.review.content }}
        </p>
        <p v-show="!showOrigin">
          {{ renderData.review.translate_content }}
        </p>
        <p v-if="needTranslateButton" :class="$style.transBtn" @click="showOrigin = !showOrigin">
          {{ showOrigin ? $t('activity.v2.translate.btn') : $t('activity.v2.translate.show.original') }}
        </p>
      </div>
      <div :class="$style.help">
        <div v-if="!renderData.review.has_liked" :class="$style.help_btn" @click="handleLike">
          <klk-icon type="icon_social_thumbs_on" size="15" :class="$style.help_icon" />
          {{ $t('my_reviews.helpful_text') }}
        </div>
        <klk-icon v-else type="icon_social_thumbs_on" size="15" :class="[$style.help_icon, $style.active]" />
        <span v-if="renderData.review.like_count" :class="$style.help_count" v-html="$t('my_reviews.helpful_with_num', [renderData.review.like_count])" />
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Prop, Watch } from 'vue-property-decorator'
import { getStandardDateFormat } from '../../common/datetime'
import { formatAvatarUrl } from '../../common/utils'
import Base from '../../common/base'

@Component({
  components: {}
})
export default class ReviewItem extends Base {
  @Prop({ type: Boolean, default: () => false }) isLoggedIn!: boolean
  @Prop({ required: true, default: () => {} }) info !: any

  showOrigin: boolean = false
  renderData: any = null
  language: string = ''

  @Watch('info', { deep: true, immediate: true })
  handleInfoChange(val: any) {
    this.renderData = val
  }

  get needTranslateButton() {
    return this.renderData?.review?.need_translate
  }

  mounted() {
    this.language = this.getKlkLanguage()
  }

  getStandarDateStr(date: Date) {
    return getStandardDateFormat(
      date,
      this.$t.bind(this),
      this.language,
      1
    )
  }

  avatar(avatar: string) {
    return formatAvatarUrl(avatar)
  }

  handleLike() {
    const nowPageUrl = window.location.pathname
    if (this.isLoggedIn) {
      const qs = `review_id=${this.renderData.review.review_id}`
      const headers = { 'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8' }
      this._axios.$post('/v1/usrcsrv/review/like/add', qs, { headers }).then((res: Data.Res) => {
        if (res.success) {
          this.renderData.review.has_liked = true
          this.renderData.review.like_count++
        } else if (res.error.code === '4001') {
          window.location.href = `${this.language !== 'en' ? `/${this.language}` : ''}/signin?signin_jump=${encodeURIComponent(nowPageUrl)}`
        }
      })
    } else {
      window.location.href = `${this.language !== 'en' ? `/${this.language}` : ''}/signin?signin_jump=${encodeURIComponent(nowPageUrl)}`
    }
  }
}
</script>

<style module lang="scss">
  .item {
    background: $color-bg-widget-normal;
    padding: 16px;
    box-shadow: inset 0 -1px 0 0 rgba(0, 0, 0, 0.15);

    .avatar {
      display: flex;
      align-items: center;
    }

    .avatar_img {
      flex: none;
      margin-right: 12px;
      flex: none;
      width: 50px;
      height: 50px;
      border-radius: $radius-circle;
      background: $color-bg-widget-darker-3;
      background-size: cover;
    }

    .name {
      color: $color-text-primary;

      @include font-body-s-semibold();
    }

    .time {
      color: $color-text-secondary;

      @include font-caption-m-regular();
    }

    .rating {
      display: flex;
      align-items: center;
      padding: 12px 0 0;
    }

    .score {
      color: $color-brand-secondary;

      @include font-body-m-bold();
    }

    .score_base {
      @include font-caption-s-semibold();
    }

    .rating_desc {
      margin-left: 6px;
      color: $color-text-primary;

      @include font-body-s-semibold();
    }

    .contain {
      .from {
        color: $color-text-placeholder;
        font-size: $fontSize-caption-m;
        line-height: $lineHeight-compact;
        overflow: hidden;
        text-overflow: ellipsis;
        position: relative;
        margin-top: 4px;
        max-height: 28px;
      }

      .content {
        word-break: break-word;
        margin-top: 12px;

        @include font-body-s-regular();
      }

      .transBtn {
        color: $color-info;
        margin-top: 12px;
        font-size: $fontSize-caption-s;
      }

      .help {
        margin-top: 16px;
        color: $color-text-secondary;
        font-size: $fontSize-caption-m;
        display: flex;
        align-items: center;
        min-height: 30px;

        &_btn {
          margin-right: 8px;
          padding: 6px 8px;
          border: 1px solid $color-border-normal;
          border-radius: $radius-s;
          display: flex;
          align-items: center;
        }

        &_icon {
          font-size: $fontSize-body-m;
          color: $color-text-secondary;
          margin-right: 4px;
          margin-top: -2px;

          &.active {
            color: $color-brand-primary;
            flex-shrink: 0;
          }
        }

        &_count {
          color: $color-text-secondary;
          font-size: $fontSize-caption-m;
          display: flex;
          align-items: center;
          min-height: 30px;

          i {
            font-style: normal;
            margin-right: 5px;
          }
        }
      }

      .reply {
        margin-top: 16px;
        background-color: $color-bg-page;
        word-break: break-word;
        padding: 16px;
        position: relative;
        line-height: $lineHeight-compact;

        &_customer {
          font-size: $fontSize-body-m;
          margin-bottom: 8px;
        }

        &_content {
          font-size: $fontSize-body-s;
        }
      }
    }
  }
</style>
<style lang="scss" scoped>
  .item_header {
    display: flex;
    position: relative;

    &-score {
      padding-top: 12px;
      display: flex;
      align-items: center;

      .num {
        color: $color-brand-secondary;
        margin-right: 6px;

        @include font-body-m-bold();
      }

      .num_base {
        @include font-caption-s-semibold();
      }

      .text {
        color: $color-text-primary;

        @include font-body-s-semibold();
      }
    }

    &-day {
      padding-top: 8px;
      display: flex;
      align-items: center;
      color: $color-text-secondary;
      font-size: 14px;
    }
  }
</style>
