<template>
  <div v-if="initData && initData.items && initData.items.length" class="review review-swip">
    <h2>{{ title }}</h2>
    <div class="review_header">
      <div class="review_header-score">
        <span itemprop="ratingValue">{{ firstData.average_rating || 0 }}</span>/<span>5</span>
      </div>
      <div class="review_header-right">
        <p class="review_header-desc">{{ firstData.score_intro }}</p>
        <p class="review_header-total_count">{{ $t('global.star.reviews', firstData.review_count) }}</p>
      </div>
    </div>
    <klk-card-swiper class="review-swiper" :item-width="itemWidth" :item-gap="10" :scroll-mode="true">
      <klk-card-swiper-item v-for="(item, index) in firstData.items" :key="index">
        <out-item class="review_item" :review="item" />
      </klk-card-swiper-item>
    </klk-card-swiper>
    <div
      class="review_all"
      data-spm-module="Review_HP"
      v-galileo-click-tracker="galileoClick1"
      data-spm-virtual-item="__virtual?typ=entry"
      @click="showAll = true"
    >
      {{ $t('activity.v2.label.review.view_more.num', firstData.review_count) }}
    </div>
    <review-all
      :is-logged-in="isLoggedIn"
      :first-data="firstData"
      :show.sync="showAll"
      :api-config="apiConfig"
      :transfer-context="transferContext"
      :sourcePage="sourcePage"
    />
  </div>
</template>
<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator'
import Swiper from 'swiper'
import { ITransferContext, SourcePage } from '../../../types/airport-transfer'
import ReviewAll from './review-all.vue'
import outItem from './outer-item.vue'
import { ldjson } from '../../common/utils'
import { WebClick_111 } from '../../../share/galileo/auto-click'

const __SWIPER = {
  end: 1,
  begin: 0,
  mid: 2
}
@Component({
  components: {
    outItem,
    ReviewAll
  }
})
export default class ReviewMobile extends Vue {
  @Prop({ type: Object, default: () => ({ items: [] }) }) initData!: any
  @Prop({ type: Object, default: () => ({ path: null, qs: {} }) }) apiConfig!: any
  @Prop({ type: String, default: '' }) title!: string
  @Prop({ type: Boolean, default: () => false }) isLoggedIn!: boolean
  @Prop({ type: Object, default: null }) transferContext?: ITransferContext
  @Prop({ type: String, default: () => '' }) sourcePage!: SourcePage

  showAll = false
  firstData = this.initData || {}
  swiperStatus = __SWIPER.begin

  head() {
    if (this.initData) {
      const reviewRating = {
        '@context': 'https://schema.org',
        '@type': 'Product',
        name: this.initData.title,
        aggregateRating: {
          '@type': 'AggregateRating',
          ratingValue: this.initData.average_rating || '',
          bestRating: '5',
          worstRating: '1',
          reviewCount: this.initData.review_count || ''
        }
      }
      return {
        script: [ldjson(reviewRating)]
      }
    }
  }

  get itemWidth() {
    if (this.firstData.item && this.firstData.item.length === 1) {
      return 'calc(100vw - 40px)'
    } else {
      return 'calc(100vw - 60px)'
    }
  }

  mounted() {
    const _that = this
    const swiper = new Swiper('.swiper_body', {
      slidesPerView: 3,
      spaceBetween: 20.5,
      navigation: {
        nextEl: '.swiper-button-next',
        prevEl: '.swiper-button-prev'
      }
    })
    swiper.on('slideChange', function () {
      if (this.isBeginning) {
        _that.swiperStatus = __SWIPER.begin
      } else if (this.isEnd) {
        _that.swiperStatus = __SWIPER.end
      } else {
        _that.swiperStatus = __SWIPER.mid
      }
    })
  }

    get galileoClick1() {
        return { spm: WebClick_111, autoTrackSpm: true }
    }
}
</script>
<style lang="scss" scoped>
.review {
  position: relative;
  h2 {
    font-size: 20px;
    line-height: 24px;
    color: $color-text-primary;
    margin-top: 32px;
  }

  &_header {
    display: flex;
    align-items: center;
    margin: 16px 0 12px;

    &-score {
      color: $color-brand-secondary;

      // @include font-body-m-bold();

      span:first-child {
        font-size: 28px;
        line-height: 1;
        font-weight: $fontWeight-semibold;
      }
    }

    &-right {
      margin-left: 16px;

      @include font-heading-xs();
    }

    &-desc {
      font-size: 16px;
      color: $color-text-primary;

    }

    &-total_count {
      font-size: 14px;
      color: $color-text-secondary;
      font-weight: normal;
    }
  }
}

.review_item {
  border-radius: $radius-l;
  background: $color-bg-widget-normal;
  // width: 320px;
}

.review_all {
  text-align: center;
  cursor: pointer;
  margin-top:16px;
  width: 100%;
  border: 1px solid #4a4a4a;
  border-radius: $radius-l;
  color: #212121;
  height: 44px;
  line-height: 44px;
}

.review-swiper {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE 10+ */
  ::-webkit-scrollbar {
    display: none;
  }

  .klk-card-swiper-items-wrapper {
    overflow: hidden;
  }
  .klk-card-swiper-items {
    width: 100%;
    display: flex;
    flex-wrap: nowrap;
    overflow-x: auto;
    overflow-y: hidden;
    padding: 0 20px 20px;
    margin-bottom: -20px;
  }
}

.reviews__view_more {
    padding: 16px;
    margin-top: 10px;
    color: $color-text-primary;
    text-align: center;
    border-top: 0.5px solid $color-border-normal;
  }
</style>
