<template>
  <transition name="slide">
    <div v-if="show" v-lockBody:ptpreview="show" data-spm-page="AirportTransfer_Review_Detail" :class="$style.all">
      <header :class="$style.all_header">
        <klk-icon
          type="icon_navigation_close"
          size="22"
          :class="[$style.all_icon, $style.all_icon_close]"
          @click="closeAll"
        />
        <p>{{ title }}</p>
      </header>
      <div ref="__LAZY" :class="$style.all_main" @scroll="handleScroll">
        <div class="review_header">
          <div class="review_header-score">
            <span itemprop="ratingValue">{{ detail.average_rating || 0 }}</span>/<span itemprop="bestRating">5</span>
          </div>
          <div class="review_header-right">
            <p class="review_header-desc">{{ detail.score_intro }}</p>
            <p class="review_header-total_count">{{ $t('global.star.reviews', detail.review_count || 0) }}</p>
          </div>
        </div>
        <!-- 子评论 -->
        <div :class="$style.all_sub_review" v-if="detail.sub_review_list && detail.sub_review_list.length">
          <review-category
            v-for="subItem in detail.sub_review_list"
            :key="subItem.sub_review_rule_id"
            :class="$style.all_sub_review_item"
            :category="subItem.display_title"
            :rating="Number(subItem.average_rating)"
            :total="5"
          />
        </div>
        <div :class="$style.all_filter_container">
          <review-filter-sort
            v-model="sortType"
            v-if="sortData && sortData.length"
            :sort-data="sortData"
            @change-sort="changeSort"
          />
          <div :class="$style.all_filter">
            <div
              v-for="(item, i) in filter"
              :key="item.tag_type"
              :data-spm-module="`Review_Filter_LIST?oid=TagType_${item.tag_type}&idx=${i}&len=${filter.length}`"
              v-galileo-click-tracker="galileoClick1"
              data-spm-virtual-item="__virtual?typ=entry"
              :class="$style.all_filter_items"
            >
              <span
                v-if="item.show_tag"
                :class="item.selected && $style.active"
                @click="handleFilter(i)"
              >{{ item.tag_text }}</span>
            </div>
          </div>
        </div>
        <div :class="$style.all_list">
          <template v-if="!isEnded || (detail.items && detail.items.length)">
            <review-item v-for="(item, index) in detail.items" :key="`${item.id}-${index}`" :info="item" :is-logged-in="isLoggedIn" />
            <div v-if="showLoading" :class="$style.loading">
              <klk-loading></klk-loading>
            </div>
            <div :class="$style.all_no_more" v-else-if="!detail.has_more">{{ $t('14258') }}</div>
          </template>
          <template v-else>
            <div :class="$style.error_content">
              <Empty></Empty>
              <div :class="$style.error_tip">{{ $t('48169-empty') }}</div>
              <div :class="$style.review_reset_filter" @click="resetFilter">{{ $t('48052') }}</div>
            </div>
          </template>
        </div>
      </div>
    </div>
  </transition>
</template>

<script lang="ts">
import throttle from 'lodash/throttle'
import { Component, Prop, Watch } from 'vue-property-decorator'
import ReviewItem from './item.vue'
import ReviewFilterSort from './filter-review-sort.vue'
import lockBody from './directive/lock-body.directive'
import Base from '../../common/base'
import ReviewCategory from '../review-category/index.vue'
import { ITransferContext, SourcePage, IFilter } from '../../../types/airport-transfer'
import Empty from '../svg/empty.vue'
import { WebClick_101 } from '../../../share/galileo/auto-click'

@Component({
  directives: { lockBody },
  components: { ReviewItem, ReviewCategory, Empty, ReviewFilterSort }
})
export default class ReviewAll extends Base {
  @Prop({ type: Boolean, default: false }) show!: boolean
  @Prop({ type: String, default: () => '' }) title!: string
  @Prop({ type: Object, default: () => ({ path: null, qs: { size: 10 } }) }) apiConfig!: any
  @Prop({ type: Object, default: () => {} }) firstData!: any
  @Prop({ type: Boolean, default: () => false }) isLoggedIn!: boolean
  @Prop({ type: Object, default: null }) transferContext?: ITransferContext
  @Prop({ type: String, default: () => '' }) sourcePage!: SourcePage

  detail: any = {
    average_rating: this.firstData?.average_rating || '-', // 平均分,
    review_count: this.firstData?.review_count || '-', // 评价总数,
    score_intro: this.firstData?.score_intro, // 好评坏评
    items: []
  }

  isEnded: boolean = false
  showLoading: boolean = true
  currentPage: number = 1

  filter: IFilter[] = []

  sortType: number = 1

  isReset = false // 是否是通过重置的方式回到首屏（fix：解决触发滚动导致连续请求两页数据的问题）

  sortData: any[] = []

  initFilterAndSort() {
    this._axios.$get('/v2/transferairportapisrv/reviews/list/option/', {
      params: {
        origin: this.sourcePage,
        ...this.transferContext
      }
    }).then((res: any) => {
      if (res?.success && res.result) {
        this.sortData = res.result.sort_list || []
        this.filter = res.result.tag_list.map((tag: any) => {
          return {
            ...tag,
            selected: !tag.show_tag // 默认隐藏的tag置为选中状态，否则置为非选中
          }
        }) || []
        // 获取筛选器成功后再获取数据
        this.getReviewData()
      }
    }).catch((err: any) => {
      console.error('get filter failed' + err?.message)
    })
  }

  @Watch('show', { immediate: true })
  handleshowChange(v: boolean) {
    if (v) {
      this.resetQs()
      this.initFilterAndSort()
    }
  }

  handleFilter(index: number) {
    this.$set(this.filter[index], 'selected', !this.filter[index].selected)
    this.resetPage()
    this.getReviewData()
  }

  changeSort(sort: any) {
    this.sortType = sort
    this.resetPage()
    this.getReviewData()
  }

  loadingMore(e: any) {
    // 加载更多数据
    // fix bug重置条件时回滚到第一屏，不再触发scroll的搜索
    if (this.isEnded || this.isReset) { return }
    const { scrollTop, scrollHeight, offsetHeight } = e.target || (this.$refs.__LAZY as HTMLElement)
    if (offsetHeight + scrollTop >= scrollHeight - 5) {
      if (!this.isEnded) {
        this.currentPage++
        this.getReviewData()
      }
    }
  }

  getReviewData() {
    if (this.isEnded) { return }
    this.showLoading = true
    const filterType = this.filter.filter((tag: any) => {
      return tag.selected
    }).map((tag: any) => {
      return {
        tagType: tag.tag_type,
        tagValue: tag.tag_value
      }
    })
    this._axios.$get(this.apiConfig.path + `/?req=${encodeURIComponent(JSON.stringify({
      ...this.apiConfig.qs,
      tagList: filterType,
      page: this.currentPage,
      sortType: this.sortType
    }))}`).then((res: Data.Res) => {
      if (res.success && res.result) {
        const result = res.result
        const { items = [] } = result || {}
        if (this.currentPage > 1) {
          this.detail = {
            ...res.result,
            items: [...this.detail.items, ...items]
          }
        } else {
          this.detail = res.result
        }
        if (!result.has_more) {
          this.isEnded = true
        }
      }
    }).catch(() => {
      this.isEnded = true
    }).finally(() => {
      this.showLoading = false
      if (this.isReset) { // 在重置数据请求完成之后解锁滚动加载
        this.isReset = false
      }
    })
  }

  handleScroll = throttle(this.loadingMore, 500)


  resetFilter() {
    this.resetQs()
    this.getReviewData()
  }

  initFilter() {
    this.filter = this.filter.map((tag: any) => {
      return {
        ...tag,
        selected: !tag.show_tag // 默认隐藏的tag置为选中状态，否则置为非选中
      }
    })
  }

  resetQs() {
    this.sortType = 1
    this.initFilter()
    this.resetPage()
  }

  resetPage() {
    this.currentPage = 1
    this.isEnded = false
    this.isReset = true
    this.detail = {
      ...this.detail,
      items: []
    }
  }

  closeAll() {
    this.$emit('update:show', false)
  }

    get galileoClick1() {
        return { spm: WebClick_101, autoTrackSpm: true }
    }
}
</script>

<style module lang="scss">
.all {
  position: fixed;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  background: $color-bg-widget-normal;
  z-index: 101;

  &_header {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 2;
    width: 100%;
    background-color: $color-bg-widget-normal;
    height: 44px;
    border-bottom: 1px solid $color-border-normal;
    text-align: center;
    font-size: $fontSize-body-l;
    line-height: 44px;
    color: $color-text-primary;
  }

  &_sub_review {
    display: flex;
    flex-wrap: wrap;
    padding: 0 16px;
    justify-content: space-between;
    margin-bottom: 20px;
    &_item {
      width: 148px;
      margin-right: 20px;
      margin-bottom: 4px;
    }
  }

  &_icon {
    position: absolute;
    color: $color-text-secondary;
    top: 50%;
    margin-top: -12px;
    width: 24px;
    height: 24px;
    font-size: $fontSize-heading-xs;

    &_close {
      left: 16px;
    }

    &_img {
      right: 16px;
    }
  }

  &_list {
    .loading {
      position: relative;
      height: 50px;
    }
  }

  &_over {
    border-bottom: 1px solid rgba(0, 0, 0, 0.08);
  }

  &_filter_container {
    position: sticky;
    top: 0;
    z-index: 1;
    padding: 12px 16px 8px 16px;
    background-color: $color-bg-widget-normal;
  }

  &_filter {
    display: flex;
    flex-wrap: wrap;
    margin-top: 12px;

    &_items {
      margin: 10px 0;
      span {
        height: 28px;
        font-size: $fontSize-caption-m;
        line-height: 16px;
        padding: 6px 12px;
        border-radius: 14px;
        border: 1px solid rgba(0, 0, 0, 0.87);
        margin-right: 8px;
        margin-bottom: 6px;

        &.active {
          border: none;
          color: $color-text-primary-onDark;
          background-color: $color-brand-primary;
        }
      }
    }
  }

  &_no_more {
    @include font-caption-m-regular;
    text-align: center;
    color: $color-text-placeholder;
    margin: 30px 0;
  }

  &_main {
    overflow: scroll;
    padding-top: 44px;
    -webkit-overflow-scrolling: touch;
    box-sizing: border-box;
    width: 100%;
    height: 100%;

    .error_content {
      margin-top: 100px;
      text-align: center;
    }

    .error_tip {
      @include font-body-s-regular;
      text-align: center;
      color: $color-text-primary;
      margin-top: 16px;
    }

    .review_reset_filter {
      @include font-body-s-regular;
      margin: 0 auto;
      margin-top: 20px;
      color: $color-text-link;
      cursor: pointer;
    }
  }
}
</style>
<style scoped lang="scss">
.slide-enter-active {
  transition: all 0.3s ease;
}

.slide-leave-active {
  transition: all 0.2s cubic-bezier(1, 0.5, 0.8, 1);
}

.slide-enter,
.slide-leave-to {
  transform: translate3d(0, 100%, 0);
  visibility: visible;
}
.review {
  width: 1160px;
  position: relative;
  margin: 0 auto;
  h2 {
    font-size: $fontSize-heading-m;
    line-height: 36px;
    color: $color-text-primary;
    margin: 0 0 24px;
  }

  &_header {
    display: flex;
    align-items: center;
    padding: 20px;

    &-score {
      color: $color-brand-secondary;

      @include font-body-m-bold();

      span:first-child {
        line-height: 1;
        @include font-heading-m()
      }
    }

    &-right {
      margin-left: 16px;

      @include font-heading-xs();
      display: flex;
      align-items: center;
    }

    &-desc {
      color: $color-text-primary;

      @include font-body-m-bold();
      margin-right: 8px;
    }

    &-total_count {
      color: $color-text-secondary;

      @include font-body-s-regular();
    }
  }
}
</style>
