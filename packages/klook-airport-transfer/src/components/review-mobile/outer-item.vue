<template>
  <div class="outer_review">
    <div class="item_header" itemprop="review">
      <div
        v-lazy:background-image="avatar(review.review.avatar)"
        class="item_header-avatar"
      >
      </div>
      <div class="item_header-right">
        <div class="right_name" itemprop="name">{{ review.review.user_name || '--' }}</div>
        <div class="right_date">
          {{ $t('11366-airport_transfer_review_date') }} {{ getStandarDateStr(new Date(review.review_time)) }}
        </div>
      </div>
    </div>
    <div class="item_header-score">
      <p class="num">{{ review.review.rating }}<span class="num_base">/5</span></p>
      <span class="text" itemprop="name">{{ review.score_intro }}</span>
    </div>
    <div class="item_header-day">
      {{ $t('11374-airport_transfer_review_name', [review.airport_name, review.airport_code]) }}
    </div>
    <div class="item_content">
      <div class="body_content">
        {{ translated ? review.review.translate_content : review.review.content }}
      </div>
      <div
        v-if="needTranslateButton"
        class="body_btn"
        @click="translated = !translated"
      >
        {{
          translated
            ? $t('activity.v2.translate.show.original')
            : $t('activity.v2.translate.btn')
        }}
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Prop, Component } from 'vue-property-decorator'
import { getStandardDateFormat } from '../../common/datetime'
import { formatAvatarUrl } from '../../common/utils'
import Base from '../../common/base'

@Component({})
export default class FaqWidget extends Base {
    @Prop({ required: true, type: Object, default: () => ({}) }) review!: any

    translated: boolean = true
    language: string = ''

    get needTranslateButton() {
      return this.review?.review?.need_translate
    }

    mounted() {
      this.language = this.getKlkLanguage()
    }

    avatar(avatar: string) {
      return formatAvatarUrl(avatar)
    }

    getStandarDateStr(date: Date) {
      return getStandardDateFormat(
        date,
        this.$t.bind(this),
        this.language,
        1
      )
    }
}
</script>

<style lang="scss" scoped>
.outer_review {
  padding: 16px;
  height: 100%;

  .item_header {
    display: flex;
    position: relative;

    &-score {
      padding-top: 12px;
      display: flex;
      align-items: center;

      .num {
        color: $color-brand-secondary;
        margin-right: 6px;

        @include font-body-m-bold();
      }

      .num_base {
        @include font-caption-s-semibold();
      }

      .text {
        color: $color-text-primary;

        font-weight: normal;
      }
    }
    &-score {
      padding-top: 12px;
      display: flex;
      align-items: center;

      .num {
        color: $color-brand-secondary;
        margin-right: 6px;

        @include font-body-m-bold();
      }

      .num_base {
        @include font-caption-s-semibold();
      }

      .text {
        color: $color-text-primary;

        @include font-body-s-semibold();
      }
    }

    &-day {
      padding-top: 8px;
      display: flex;
      align-items: center;
      color: $color-text-secondary;
      font-size: 14px;
    }

    &-avatar {
      height: 40px;
      width: 40px;
      border-radius: $radius-circle;
      background-size: cover;
      margin-right: 12px;
      flex: none;
    }

    &-right {
      .right_name {
        color: $color-text-primary;

        @include font-body-s-semibold();
      }

      .right_date {
        color: $color-text-secondary;

        @include font-caption-m-regular();
      }
    }
  }

  .activity_title {
    margin-top: 8px;
    color: $color-text-secondary;

    @include font-caption-m-regular();
  }

  .item_content {
    word-break: break-all;
    margin-top: 12px;
    box-sizing: border-box;
    color: $color-text-primary;
    max-height: 160px;
    overflow-y: auto;

    @include font-body-s-regular();

    .body_btn {
      font-size: $fontSize-caption-m;
      color: $color-info;
      cursor: pointer;
    }
  }
}
</style>
