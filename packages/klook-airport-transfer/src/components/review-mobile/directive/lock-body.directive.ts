const __cachEl: Record<string, any> = {}
function setLockBody(val: boolean, arg: string, el?: any) {
  if (!el || !__cachEl) { return }
  const _postion = document.body.style.position
  let timer
  if (val) {
    if (_postion === 'fixed') { return }
    __cachEl[arg].value = val
    __cachEl[arg].top = document.scrollingElement?.scrollTop
    timer = setTimeout(() => {
      // 缓冲视觉效果
      document.body.style.position = 'fixed'
    }, 500)
  } else {
    if (_postion === 'static') { return }
    clearTimeout(timer)
    __cachEl[arg].value = val
    document.body.style.position = 'static'
    document.scrollingElement && (document.scrollingElement.scrollTop = __cachEl[arg].top)
  }
}

export default {
  inserted(el: HTMLElement, binding: any) {
    const { arg, value } = binding
    const lockArg = arg || 'lock'
    if (__cachEl[lockArg] === undefined) {
      __cachEl[lockArg] = {
        top: 0,
        value
      }
    }
    setLockBody(value, lockArg, el)
  },
  update(el: HTMLElement, binding: any) {
    const { arg, value } = binding
    const lockArg = arg || 'lock'
    setLockBody(value, lockArg, el)
  },
  unbind(el: HTMLElement, binding: any) {
    if (el && binding.arg && __cachEl[binding.arg]) {
      document.body.style.position = 'static'
      document.scrollingElement && (document.scrollingElement.scrollTop = __cachEl[binding.arg].top)
      delete __cachEl[binding.arg]
    }
  }
}
