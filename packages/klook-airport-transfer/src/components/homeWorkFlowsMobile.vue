<template>
  <div v-if="dataInfo && dataInfo.work_flows && dataInfo.work_flows.length > 0" class="steps">
    <h3 class="steps__title">{{ dataInfo.title }}</h3>
    <div class="steps__content">
      <ul class="steps__content-list">
        <li v-for="(item, index) in dataInfo.work_flows" :key="`flow-${index}`" class="step-item">
          <div class="step-circle"></div>
          <div class="step-desc">
            <div class="desc-title">{{ item.title }}</div>
          </div>
        </li>
      </ul>
    </div>
  </div>
</template>

<script lang="ts">
import {Component, Prop} from 'vue-property-decorator'
import Base from "@/common/base";

@Component
export default class Steps extends Base {
  @Prop({ type: Object, default: () => null }) data!: any

  dataInfo: any = this.data
}
</script>

<style lang="scss" scoped>
.steps {
  padding: 0 20px;

  &__title {
    margin-top: 32px;
  }

  &__content {
    margin-top: 16px;

    &-list {
      counter-reset: count;

      .step-item {
        display: flex;
        position: relative;

        &:last-child {
          &::before {
            display: none;
          }

          .step-desc {
            padding-bottom: 0;
          }
        }

        &::before {
          content: '';
          position: absolute;
          width: 1px;
          height: 100%;
          background-color: $color-success;
          left: 12px;
          top: 0;
        }

        .step-circle {
          position: relative;
          margin-right: 12px;
          width: 24px;
          height: 24px;
          border-radius: $radius-circle;
          background-color: $color-success;
          display: flex;
          justify-content: center;

          &::before {
            @include font-body-s-bold;
            counter-increment: count;
            content: counter(count);
            color: $color-white;
            position: relative;
            top: 2px;
          }
        }

        .step-desc {
          flex: 1;
          padding-bottom: 32px;
          position: relative;
          top: 2px;

          .desc-title {
            @include font-body-s-semibold;
          }

          .desc-content {
            word-break: break-word;
            font-size: $fontSize-body-s;
            line-height: 21px;
            color: $color-text-secondary;
          }
        }
      }
    }
  }
}
</style>
