<template>
  <div class="review-category">
    <div v-if="category" class="review-category__title">{{ category }}</div>
    <div class="review-category__progress">
      <div class="review-category__progress-content">
        <div class="review-category__progress-base" />
        <div class="review-category__progress-top" :style="`width: ${(rating / total) * 100}%;`" />
      </div>
      <div class="review-category__progress-end">{{ rating }}</div>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator'

@Component
export default class ReviewCategory extends Vue {
  @Prop({ type: String, default: '' }) category?: string
  @Prop({ type: Number, default: 0 }) rating!: number
  @Prop({ type: Number, default: 100 }) total!: number
}
</script>

<style lang="scss" scoped>
.review-category{
  @include font-caption-m-regular;
  color: $color-text-secondary;
  &__progress {
    display: flex;
    align-items: center;
    &-content {
      position: relative;
      flex: 1;
      height: 6px;
    }
    &-base, &-top {
      position: absolute;
      border-radius: 6px;
      height: 6px;
    }
    &-base {
      width: 100%;
      background-color: $color-bg-3;
    }
    &-top {
      background-color: $color-caution;
      border-radius: 6px;
    }
    &-end {
      margin-left: 12px;
    }
  }
}

</style>
