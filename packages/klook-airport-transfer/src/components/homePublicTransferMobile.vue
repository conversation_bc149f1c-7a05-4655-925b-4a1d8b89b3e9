<template>
  <div v-if="showPublic" class="public">
    <h3 class="public-title">{{ publicTitle }}</h3>
    <div class="public-search" @click="eventHandle">
      <i class="public-search__search-icon">
        <img src="https://res.klook.com/image/upload/web3.0/airport-transfer/icon_edit_search_s_y7bgut.svg" alt="" width="20" height="20">
      </i>
      <input v-model="searchWord" :placeholder="$t('101033')" readonly>
    </div>
    <div v-if="publicError || !publicList">
      <Klk-empty-panel
        v-if="!publicList && publicError"
        platform="mobile"
        :content="$t('101034-no_data')"
        icon-src="https://res.klook.com/image/upload/web3.0/airport-transfer/Empty_Slate_error_404_260_ojr61g.png"
        :primary-btn-text="$t('14305-retry')"
        @primary-btn-click="getPublicList"
      >
      </Klk-empty-panel>
      <Klk-empty-panel
        v-else
        platform="mobile"
        :content="$t('100498-no_public_list')"
        icon-src="https://res.klook.com/image/upload/web3.0/ill_spot_hero_transport_tickets_edrvrq.svg"
      >
      </Klk-empty-panel>
    </div>
    <template v-else>
      <div v-if="searchWord && isRecommend" class="public__recommend-tips">{{ $t('100498-no_public_list') }} {{ $t('102249-no_public_tips') }}</div>
      <div v-if="publicList && publicList.length > 0" class="public-list">
        <template v-for="(item, index) in publicList">
          <div 
            v-if="index < showPublicNum" 
            :key="index"
            v-galileo-click-tracker="{ spm: `PublicAirportTransferCard_LIST?oid=${item.activity_id}&len=${publicList.length}&idx=${index}`, autoTrackSpm: true }"
            class="public-list__item" 
            :data-spm-module="`PublicAirportTransferCard_LIST?oid=${item.activity_id}&len=${publicList.length}&idx=${index}`"
            data-spm-virtual-item="__virtual?typ=entry"
            @click="bookingTTD(item)"
          >
            <div class="public-list__item-left">
              <div class="public-list__item-city">{{ item.city_name }}</div>
              <div class="public-list__item-airport">{{ item.car_name }}</div>
              <div class="public-list__item-price">{{ currencySymbol }} {{ item.sell_price_with_exchange_formatted }}</div>
            </div>
            <div class="public-list__item-right">
              <div class="public-list__item-img" :style="`background-image: url(${item.car_image_url})`"></div>
            </div>
          </div>
        </template>
      </div>
      <div v-if="!isRecommend && airportInfo && publicList.length > showPublicNum" class="public-more" @click="goSearchResult">
        <klk-button type="outlined" block>{{ $t('84654-view_all') }}</klk-button>
      </div>
    </template>
  </div>
</template>

<script lang="ts">
import { Component, Prop } from 'vue-property-decorator'
import KlkEmptyPanel from '@klook/empty-panel'
import Base from "@/common/base";

import '@klook/empty-panel/dist/esm/index.css'

@Component({
  components: {
    KlkEmptyPanel
  }
})
export default class PublicTransfer extends Base {
  @Prop({ type: Object, default: () => null }) data!: any
  count: number = 4
  searchWord: string = ''
  publicTitle: string = ''
  publicInfo: any = this.data
  cityId: any = null
  airportInfo: any = null
  publicParams: any = null
  publicError: boolean = false
  showPublic: boolean = false

  get publicList() {
    return this.publicInfo?.public_transfer?.car_info_list
  }

  get showPublicNum() {
    return this.publicInfo?.show_number || 4
  }

  get currencySymbol() {
    return this.publicInfo?.currency_info_vo?.currency_symbol
  }

  get noPublicResult() {
    return this.searchWord && !this.publicList
  }

  get isRecommend() {
    return this.publicInfo?.public_transfer?.recommend
  }

  created() {
    this.publicTitle = this.$t('100492-public_transfer_recommend')
    const query: any = this.$route.query
    const city_id = Number(query.city_id)
    if (!city_id) {
      this.publicInfo = this.data
      this.showPublic = true
    }
  }

  getPublicListApi(params: any) {
    return this._axios
      .$get('/v1/transferairportapisrv/transfer/app_search/public', { params, throwError: true })
      .then((res: Data.Res) => {
        return Promise.resolve(res)
      })
      .catch((error: any) => {
        return Promise.reject(error)
      })
  }

  async getPublicList(data: any) {
    if (data) {
      this.publicParams = {
        city_id: data.backupKlookCityId,
        iata_code: data.iataCode
      }
    }
    await this.getPublicListApi(this.publicParams).then((res: Data.Res) => {
      if (res.success && res.result) {
        this.publicInfo = res.result
        this.publicError = false
        this.showPublic = true
      }
    }).catch((err: any) => {
      this.publicError = true
      this.showPublic = true
      this.publicInfo = null
      if (err.code === '990001') {
        this.$inhouse.track('custom', 'body', {
          spm: 'AirportTransfer_Home_get_public_network_faild'
        })
      }
    })
  }

  bookingTTD(item: any) {
    let langPath = ''
    const lang = this.getKlkLanguage()
    if (lang !== 'en') {
      langPath = `/${lang}`
    }
    const href = `${langPath}/activity/${item.activity_id}-${item.seo_url}/#krt=${item.klook_referral_id}`
    window.location.href = href
  }

  goSearchResult() {
    const item = this.airportInfo
    const from = `${item.airportName}(${item.iataCode})`
    let langPath = ''
    const lang = this.getKlkLanguage()
    if (lang !== 'en') {
      langPath = `/${lang}`
    }
    const query = `?flightDirection=1&from=${from}&to=&address=&lat=${item.latitude}&long=${item.longitude}&poiId=&poi_lat=&poi_long=&time=&pas=2&code=${item.iataCode}&place=&ac=${item.isActive}&kcid=${item.backupKlookCityId}&is_from_public=1`
    const href = `${langPath}/airport-transfers/results/${query}`
    window.location.href = href
  }

  eventHandle() {
    this.$emit('eventHandle', {
      publicTransferMobile: {
        searchWord: this.searchWord
      }
    })
  }

  eventListener(info: any) {
    this.airportInfo = info
    this.searchWord = `${info.airportName}(${info.iataCode})`
    this.publicTitle = this.$t('100497-recommend_about', {airport_name: this.searchWord})
    this.cityId = info.backupKlookCityId
    this.getPublicList(info)
  }
}
</script>

<style lang="scss" scoped>
.public {
  //padding: 0 20px;

  //&-title {
  //  margin-top: 32px;
  //}

  &-search {
    min-height: 36px;
    flex: 1;
    background-color: $color-bg-page;
    border-radius: $radius-pill;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    padding: 0 12px;
    background-color: $color-bg-1;
    margin-top: 16px;

    &__search-icon, &__clear-icon {
      display: flex;
    }

    input {
      flex: 1;
      border: none;
      outline: none;
      caret-color: #ff5722;
      line-height: 36px;
      background: transparent;
    }
  }

  &__recommend-tips {
    @include font-body-s-regular;
    color: $color-text-secondary;
    margin-top: 12.5px;
  }

  &-list {
    border-radius: $radius-xl;
    background-color: $color-bg-1;
    padding: 0 16px;
    margin-top: 12px;

    &__item {
      padding: 16px 0;
      display: flex;
      flex-direction: row;
      justify-content: space-between;

      &:nth-child(n+2) {
        border-top: 1px solid $color-border-dim;
      }

      &-left {
        flex: 1;
      }

      &-right {
        flex: 60px 0 0;
      }

      &-city {
        @include font-caption-1;
        color: $color-brand-primary-dark;
      }

      &-airport, &-price {
        @include font-body-s-bold;
        color: $color-text-primary;
      }

      &-img {
        flex: 60px 0 0;
        height: 60px;
        border-radius: $radius-xl;
        background-repeat: no-repeat;
        background-size: cover;
        background-position: center center;
        overflow: hidden;
      }
    }
  }

  &-more {
    margin-top: 12px;
  }
}
</style>
