<template>
  <!-- 接机方式切换tab -->
  <klk-tabs v-galileo-click-tracker:tab-click="{ spm: 'SwitchPickupMethod'}" class="search-form__pick-type" :value="isByFlight ? 'byFlight' : 'byFixTime'" @change="handleChangeTab">
    <klk-tab-pane
      :data-spm-module="`SwitchPickupMethod?trg=manual&ext=${encodeURIComponent(JSON.stringify({
        tab: 'FlightNo'
      }))}`"
      v-galileo-click-tracker="galileoClick1"
      data-spm-virtual-item="__virtual"
      name="byFlight"
      :label="$t('203776-flight_time')"
    />
    <klk-tab-pane
      :data-spm-module="`SwitchPickupMethod?trg=manual&ext=${encodeURIComponent(JSON.stringify({
        tab: 'FixedTime'
      }))}`"
      v-galileo-click-tracker="galileoClick2"
      data-spm-virtual-item="__virtual"
      name="byFixTime"
      :label="$t('203777-fix_time')"
    />
  </klk-tabs>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator'
import { WebClick_91, WebClick_92 } from '../../../share/galileo/auto-click'

@Component
export default class PickTypeTab extends Vue {
  @Prop({ type: Boolean, default: false }) isByFlight!: boolean
  @Prop({ type: String, default: '' }) curPickType!: string

  handleChangeTab(val: string) {
    this.$emit('change-pick-type', val)
  }

    get galileoClick1() {
        return { spm: WebClick_91, autoTrackSpm: true }
    }

    get galileoClick2() {
        return { spm: WebClick_92, autoTrackSpm: true }
    }
}
</script>

<style lang="scss" scoped>
.search-form {
  &__pick-type {
    ::v-deep .klk-tabs-item {
      @include font-body-s-bold-v2;
      height: 28px !important;
    }
    ::v-deep .klk-tabs-body {
      margin-top: 12px !important;
    }
  }
}
</style>
