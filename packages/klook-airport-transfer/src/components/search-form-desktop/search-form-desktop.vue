<template>
  <div class="search-form" :class="{'hide-radius-left-top': hideTopLeft, 'hide-radius-above': hideSearchFormRadiusAbove}">
    <div class="search-form__tabs">
      <klk-radio
        v-model="isPickTab"
        size="small"
        :data-spm-module="`BusinessType?trg=manual&ext=${JSON.stringify({type: 'AirportPickup'})}`"
        v-galileo-click-tracker="galileoClick1"
        data-spm-virtual-item="__virtual?typ=entry"
        @change="toggleTab(true)"
      >
        {{ $t('13491-airport_transfer_home_tab_pick') }}
      </klk-radio>
      <klk-radio
        v-model="isDropTab"
        size="small"
        :data-spm-module="`BusinessType?trg=manual&ext=${JSON.stringify({type: 'AirportDropoff'})}`"
        v-galileo-click-tracker="galileoClick2"
        data-spm-virtual-item="__virtual?typ=entry"
        @change="toggleTab(false)"
      >
        {{ $t('15032-airport_transfer_home_form_from') }}
      </klk-radio>
    </div>
    <!-- 接机方式切换tab -->
    <div v-if="showPickType && isPickTab" class="search-form__pick-type-wrapper">
      <pick-type-tab
        :is-by-flight="isByFlight"
        :cur-pick-type="curPickType"
        @change-pick-type="changePickType"
      />
    </div>
    <div :class="['search-form__wraper', showPickType && isPickTab && 'search-form__wrapper_by_flight']">
      <!-- 接机搜索 -->
      <template v-if="isPickTab">
        <!-- 航班号搜索 -->
        <klk-poptip
          v-if="isByFlight"
          v-model="formValid.flightNo.show"
          class="search-form__place search-form__place-clickd search-form__flex-grow"
          :content="formValid.flightNo.content"
          placement="top-start"
          trigger="none"
          dark
        >
          <div
            class="search-form__place-from"
            data-spm-module="FlightNoInput"
            v-galileo-click-tracker="galileoClick3"
            data-spm-virtual-item="__virtual?typ=entry"
            @click="showSearchByFlightModal = true"
          >
            <div class="search-form__place-from-airport-label">{{ $t('6991-from') }}</div>
            <div v-if="!fromPlaceByFlight" class="search-form__place-from-airport-placeholder">{{ $t('203778-from_placeholder') }}</div>
            <div class="search-form__place-from-airport">
              {{ fromPlaceByFlight }}
            </div>
            <div class="search-form__place-from-time">{{ pickUpTimeByflight }}</div>
          </div>
        </klk-poptip>
        <!-- 航班号搜索 -->
        <klk-poptip
          v-else
          v-model="formValid.from.show"
          class="search-form__place search-form__place-clickd search-form__flex-grow"
          :content="formValid.from.content"
          placement="top-start"
          trigger="none"
          dark
        >
          <div
            v-click-outside="hideSearchAirportPage"
            :data-spm-module="`PickupPlaceInput?trg=manual&ext=${JSON.stringify({type: 'AirportPickup'})}`"
            @click="handleFocus('pickfrom')"
          >
            <div class="search-form__place-from-placeholder">{{ $t('6991-from') }}</div>
            <input
              v-model="formData.from"
              type="text"
              :placeholder="$t('6991-from')"
              class="search-form__item"
              name="searchFromInput"
              autocomplete="off"
              aria-autocomplete="none"
              @focus="openSearch(true, 'from')"
              @input="handleInputChange(formData.from, 'from')"
            >
          </div>
          <search-page
            v-if="showSearchAirportPage"
            :search-type="searchType"
            :loading="searchLoading"
            :search-list="searchList"
            :airport-group="airportGroup"
            :search-word="searchWord"
            :offset-left="offsetLeft"
            :is-pick-tab="isPickTab"
            :search-history="searchHistory"
            @selectSearchItem="selectSearchItem"
            @getSearchHistory="getSearchHistory"
            @clearHistory="clearHistory"
          ></search-page>
        </klk-poptip>
        <klk-poptip
          v-model="formValid.to.show"
          :class="['search-form__place', 'search-form__place-to', 'search-form__place-clickd', 'search-form__flex-grow', isByFlight && 'search-form__place-to-by-flight']"
          :content="formValid.to.content"
          placement="top-start"
          trigger="none"
          dark
        >
          <div
            v-click-outside="hideSearchPoiPage"
            :data-spm-module="`DropoffPlaceInput?trg=manual&ext=${JSON.stringify({type: 'AirportPickup'})}`"
            @click="handleFocus('pickto')"
          >
            <div class="search-form__place-from-placeholder">{{ $t('6993-to') }}</div>
            <input
              v-model="formData.to"
              type="text"
              :placeholder="$t('15212-airport_transfer_home_form_address')"
              name="searchToInput"
              autocomplete="off"
              aria-autocomplete="none"
              class="search-form__item"
              @focus="openSearch(false, 'to')"
              @input="handleInputChange(formData.to, 'to')"
            >
          </div>
          <search-page
            v-if="showSearchPoiPage"
            :search-type="searchType"
            :loading="searchLoading"
            :search-list="searchList"
            :search-word="searchWord"
            :offset-left="offsetLeft"
            :is-pick-tab="isPickTab"
            @selectSearchItem="selectSearchItem"
          ></search-page>
        </klk-poptip>
      </template>
      <!-- 送机搜索 -->
      <template v-else>
        <klk-poptip
          v-model="formValid.from.show"
          class="search-form__place search-form__place-clickd search-form__flex-grow"
          :content="formValid.from.content"
          placement="top-start"
          trigger="none"
          dark
        >
          <div
            v-click-outside="hideSearchPoiPage"
            :data-spm-module="`PickupPlaceInput?trg=manual&ext=${JSON.stringify({type: 'AirportDropoff'})}`"
            @click="handleFocus('dropfrom')"
          >
            <div class="search-form__place-from-placeholder">{{ $t('6991-from') }}</div>
            <input
              v-model="formData.from"
              type="text"
              :placeholder="$t('15212-airport_transfer_home_form_address')"
              name="searchFromInput"
              autocomplete="off"
              aria-autocomplete="none"
              class="search-form__item"
              @focus="openSearch(false, 'from')"
              @input="handleInputChange(formData.from, 'from')"
            >
          </div>
          <search-page
            v-if="showSearchPoiPage"
            :search-type="searchType"
            :loading="searchLoading"
            :search-list="searchList"
            :search-word="searchWord"
            :offset-left="offsetLeft"
            :is-pick-tab="isPickTab"
            @selectSearchItem="selectSearchItem"
          ></search-page>
        </klk-poptip>
        <klk-poptip
          v-model="formValid.to.show"
          class="search-form__place search-form__place-clickd search-form__flex-grow"
          :content="formValid.to.content"
          placement="top-start"
          trigger="none"
          dark
        >
          <div
            v-click-outside="hideSearchAirportPage"
            :data-spm-module="`DropoffPlaceInput?trg=manual&ext=${JSON.stringify({type: 'AirportDropoff'})}`"
            @click="handleFocus('dropto')"
          >
            <div class="search-form__place-from-placeholder">{{ $t('6993-to') }}</div>
            <input
              v-model="formData.to"
              type="text"
              :placeholder="$t('6993-to')"
              name="searchToInput"
              autocomplete="off"
              aria-autocomplete="none"
              class="search-form__item"
              @focus="openSearch(true, 'to')"
              @input="handleInputChange(formData.to, 'to')"
            >
          </div>
          <search-page
            v-if="showSearchAirportPage"
            :search-type="searchType"
            :loading="searchLoading"
            :search-list="searchList"
            :airport-group="airportGroup"
            :search-word="searchWord"
            :offset-left="offsetLeft"
            :is-pick-tab="isPickTab"
            :search-history="searchHistory"
            @selectSearchItem="selectSearchItem"
            @getSearchHistory="getSearchHistory"
            @clearHistory="clearHistory"
          ></search-page>
        </klk-poptip>
      </template>
      <!-- 日期选择 -->
      <template v-if="!isByFlight || isDropTab">
        <klk-poptip
          v-model="formValid.date.show"
          class="search-form__place"
          :content="formValid.date.content"
          placement="top-start"
          trigger="none"
          dark
        >
          <div
            v-click-outside="hideDateTimePicker"
            :data-spm-module="`pick-up date & time?trg=manual`"
            v-galileo-click-tracker.stop="{ spm: 'PickupDateTime', enforce: 'post' }"
            data-spm-virtual-item="__virtual?typ=entry"
            @click="handleFocus('selectdate')"
          >
            <div class="search-form__place-from-placeholder">{{ $t('13409-date_time') }}</div>
            <div
              v-if="formData.date && formData.time"
              :class="[{'active': showDateTimePicker}]"
              class="search-form__place-fill picked search-form__item search-form__date"
              @click="openDateTimePicker"
            >
              {{ formData.date }} {{ formData.time }}
            </div>
            <div
              v-else
              class="search-form__place-null picked search-form__item search-form__date"
              :class="[{'active': showDateTimePicker}]"
              @click="openDateTimePicker"
            >
              {{ $t('13409-date_time') }}
            </div>
            <date-time-picker
              v-show="showDateTimePicker"
              :date="timeData"
              :time="pickerTime"
              :is-pick-tab="isPickTab"
              :show-pick-up-tips="isPickTab && currentPage === 'home'"
              @cancle="cancle"
              @selectDate="selectDate"
              @selectTime="selectTime"
            ></date-time-picker>
          </div>
        </klk-poptip>
      </template>
      <!-- 乘客人数选择 -->
      <klk-poptip
        v-model="showPassengerLimitMaxTips"
        class="search-form__place"
        :content="$t('13182-pas_limit_50')"
        placement="top-start"
        trigger="none"
        dark
      >
        <div
          v-click-outside="hidePassengerCounter"
          data-spm-module="PessangerNumInput?trg=manual"
          v-galileo-click-tracker="galileoClick5"
          data-spm-virtual-item="__virtual?typ=entry"
          @click="handleFocus('passengercount')"
        >
          <div
            v-if="formData.passenger"
            class="search-form__place-fill picked search-form__item search-form__pas"
            :class="[{'active': showPassengerCounter}]"
            @click="openPassengerCounter"
          >
            <div class="search-form__pas-text">{{ $t('6797-passenger', formData.passenger) }}</div>
            <div class="search-form__pas-icon">
              <klk-icon type="icon_navigation_chevron_down_xs" size="16" />
            </div>
          </div>
          <div
            v-else
            class="search-form__place-null picked search-form__item search-form__pas"
            :class="[{'active': showPassengerCounter}]"
            @click="openPassengerCounter"
          >
            <div class="search-form__pas-text">{{ $t('14427-passengers') }}</div>
            <div class="search-form__pas-icon">
              <klk-icon type="icon_navigation_chevron_down_xs" size="16" />
            </div>
          </div>
          <div v-if="showPassengerCounter" class="search-form__passenger">
            <div class="search-form__passenger-box">
              <span class="search-form__passenger-title">{{ $t('14427-num_pas') }}</span>
              <span class="search-form__passenger-subtitle">{{ ($t('14070-including_pas')) }}</span>
            </div>
            <div class="search-form__passenger-counter">
              <klk-counter
                v-galileo-click-tracker="{ spm: 'PassengerSelect?trg=manual', autoTrackSpm: true }"
                data-spm-module="PassengerSelect?trg=manual"
                data-spm-virtual-item="__virtual"
                v-model="pas"
                :min="1"
                :max="50"
                size="small"
                @change="savePas"
                @disabled-click="limitMax"
              ></klk-counter>
            </div>
          </div>
        </div>
      </klk-poptip>
      <div class="search-form__btn">
        <klk-button
          size="large"
          block
          class="search-form__item"
          :data-spm-module="`SearchBtn?trg=manual&ext=${JSON.stringify({type: isPickTab ? 'AirportPickup' : 'AirportDropoff', serviceType: isByFlight ? 'FlightNo' : 'FixTime'})}`"
          v-galileo-click-tracker="galileoClick6"
          data-spm-virtual-item="__virtual?typ=entry"
          @click="search"
        >
          {{ $t('car_rental_home_search') }}
        </klk-button>
      </div>
      <search-by-flight
        :flight-info="flightInfo"
        :visible.sync="showSearchByFlightModal"
        @update-flight-info="updateFlightInfo"
      />
    </div>
    <client-only>
      <klk-modal
        v-if="tips"
        :title="tips.title"
        title-align="center"
        button-align="center"
        modal-class="search-form__modal"
        :open.sync="tipsModalVisible"
        :ok-label="tips.btn"
        :show-cancel-button="false"
        @on-confirm="closeTipsModal"
        @close="closeTipsModal"
      >
        <div class="klk-modal-content">
          {{ tips.content }}
        </div>
      </klk-modal>
    </client-only>
  </div>
</template>

<script lang="ts">
import { Component, Prop, Watch } from 'vue-property-decorator'
import dayjs from 'dayjs'
import debounce from 'lodash/debounce'
import SearchFormBase from "../../common/search-form/search-form-base"

import SearchPage from './search-page.vue'
import DateTimePicker from './date-time-picker.vue'
import SearchByFlight from './search-by-flight.vue'
import PickTypeTab from './pick-type-tab.vue'
import { WebClick_71, WebClick_72, WebClick_73, WebClick_74, WebClick_75, WebClick_76 } from '../../../share/galileo/auto-click'

interface urlParams extends Object {
  flightDirection: number,
  from: string,
  to: string,
  address: string,
  lat: string,
  long: string,
  poiId: string,
  poi_lat: string,
  poi_long: string,
  date: any,
  time: string,
  pas: number,
  code: string,
  place: string,
  ac: string,
  kcid: string,
  flightNo?: string
}

interface formData extends Object {
  from: string,
  to: string,
  date: string,
  time: string,
  passenger: number
}

interface searchAirportObject extends Object {
  searchWord: string
}

interface searchPoiObject extends Object {
  input: string,
  latitude: string,
  longitude: string,
  sessiontoken: string,
  airportCode: string
}

enum Type {
  PICK = 'pick',
  DROP = 'drop'
}

enum searchType {
  AIRPORT = 'airport',
  POI = 'poi'
}

const searchApi = {
  airportGroups: '/v1/transferairportapisrv/airport/groups',
  searchPoi: '/v1/transferairportapisrv/poi/autocomplete_v1',
  searchPoiDetail: '/v1/transferairportapisrv/poi/detail_v1',
  validatePoi: '/v1/transferairportapisrv/poi/is_blocked' // 校验poi是否合法
}

@Component({
  components: {
    SearchPage,
    DateTimePicker,
    SearchByFlight,
    PickTypeTab
  }
})
export default class SearchForm extends SearchFormBase {
  @Prop({ type: Array, default: () => null }) categoryList!: any[]
  @Prop({ type: Boolean, default: true }) desktopCalCulateComplete!: boolean

  isAirport: boolean = true
  showDateTimePicker: boolean = false
  showPassengerCounter: boolean = false
  showAirportPage: boolean = false
  showPoiPage: boolean = false
  searchLoading: boolean = false
  isFormValidate: boolean = false
  pickfrom: boolean = false
  pickto: boolean = false
  dropfrom: boolean = false
  dropto: boolean = false
  selectdate: boolean = false
  passengercount: boolean = false

  // 特殊情况下poi/机场item点击后的弹窗提示
  tipsModalVisible: boolean = false
  tips: any = {
    content: null,
    title: null,
    btn: null,
    action: 0 // 1 - 清空poi，2 - 清空机场，0 - 不做操作
  }

  // 默认为空，所以只能用any类型，因为后面会用Date类型进行填充
  timeData: any = null
  pickerTime: any = null
  pas: number = 2
  airportGroup: any[] = []
  searchList: any = null
  formErrorTip: string = ''
  offsetLeft: string = '0'
  // 用来判断搜索框上面两个角的圆角样式情况
  hideSearchFormRadiusLeftTop: boolean = false
  hideSearchFormRadiusAbove: boolean = false

  get hideTopLeft() {
    return  this.desktopCalCulateComplete && this.hideSearchFormRadiusLeftTop
  }

  // 搜索的是机场还是POI
  get searchType() {
    return this.isAirport ? searchType.AIRPORT : searchType.POI
  }

  get showSearchAirportPage() {
    return this.showAirportPage && this.airportGroup.length > 0
  }

  get showSearchPoiPage() {
    return this.showPoiPage
  }

  @Watch('pick.from')
  isPickFrom(val: boolean) {
    if (val) {
      this.pickfrom = true
    }
  }

  @Watch('pick.to')
  isPickTo(val: boolean) {
    if (val) {
      this.pickto = true
    }
  }

  @Watch('drop.from')
  isDropFrom(val: boolean) {
    if (val) {
      this.dropfrom = true
    }
  }

  @Watch('drop.to')
  isDropTo(val: boolean) {
    if (val) {
      this.dropto = true
    }
  }

  @Watch('formData.date')
  isSelectDate(val: boolean) {
    if (val) {
      this.selectdate = true
    }
  }

  @Watch('formData.passenger')
  isPassenger(val: boolean) {
    if (val) {
      this.passengercount = true
    }
  }

  created() {
    this.formValid.from.content = this.$t('6811-airport_transfer_home_error_pick_loc')
    this.formValid.to.content = this.$t('13119-airport_transfer_home_error_drop_loc')
    this.formValid.date.content = this.$t('13409-date_time')
    this.formValid.time.content = this.$t('14952-airport_transfer_home_error_time')
    this.formValid.passenger.content = this.$t('13053-airport_transfer_home_error_passenger')
    this.formValid.flightNo.content = this.$t('6811-airport_transfer_home_error_pick_loc')
    console.log('👨🏻‍💻', this.desktopCalCulateComplete, this.hideSearchFormRadiusLeftTop)
    this.getCategoryList()
  }

  async mounted() {
    try {
      await this.getAirportGroups()
        .then((res: any) => {
          if (res.success && res.result) {
            this.airportGroup = res.result.airportGroupList
          }
        })
    } catch (error) {}
    this.initTime()
  }

  // 根据聚合页L1和L2的数量来展示搜索框上面的圆角样式
  getCategoryList() {
    // 如果L1列表数量小于等于1时，搜索框展示四个圆角
    if (this.categoryList.length <= 1) {
      this.hideSearchFormRadiusLeftTop = false
      this.hideSearchFormRadiusAbove = false
      this.getCategorySubList()
    } else {
      this.hideSearchFormRadiusLeftTop = true
      this.getCategorySubList()
    }
  }

  getCategorySubList() {
    this.categoryList.forEach((item) => {
      if (item.default_select) {
        if (item.sub_category_list.length <= 1) {
          this.hideSearchFormRadiusLeftTop = true
        } else {
          this.hideSearchFormRadiusAbove = true
        }
      }
    })
  }

  //接口调用
  getAirportGroups() {
    return this._axios
      .$get(searchApi.airportGroups)
      .then((res: Data.Res) => {
        return Promise.resolve(res)
      })
      .catch((error: any) => {
        return Promise.reject(error)
      })
  }
  getSearchPoi(params: any) {
    return this._axios
      .$get(searchApi.searchPoi, { params })
      .then((res: Data.Res) => {
        return Promise.resolve(res)
      })
      .catch((error: any) => {
        return Promise.reject(error)
      })
  }
  getSearchPoiDetail(params: any) {
    return this._axios.$get(searchApi.searchPoiDetail, { params })
      .then((res: Data.Res) => {
        return Promise.resolve(res)
      })
      .catch((error: any) => {
        return Promise.reject(error)
      })
  }
  validatePoi(payload: any) {
    return this._axios.$post(searchApi.validatePoi, payload)
      .then((res: Data.Res) => {
        return Promise.resolve(res)
      })
      .catch((error: any) => {
        return Promise.reject(error)
      })
  }

  // 关闭日期时间选择
  cancle() {
    this.hideDateTimePicker()
  }

  // 切换接送机TAB
  toggleTab(bool: boolean) {
    // 清除关键字，防止切换后带上另一个tag的搜索关键字
    this.searchWord = ''
    this.isPickTab = bool
    this.updateFormData()
    this.deleteFormError()
  }

  // 防抖搜索
  handleValue = debounce(this.getSearchList, 500)

  handleInputChange(val: string, type: string) {
    console.log('handleInputChange', val)
    this.searchWord = val
    this.searchLoading = true
    if (val === '') {
      this.resetData(type)
    }
    this.handleValue()
  }

  // 打开搜索页
  openSearch(isAirport: boolean, type: string) {
    const from = this.type === Type.PICK ? 'from' : 'to'
    const to = this.type === Type.PICK ? 'to' : 'from'
    const input = type === 'from' ? this.formData.from : this.formData.to
    console.log('inputinput', input)
    this.searchWord = input || (isAirport ? this[this.type][from] : this[this.type][to])
    console.log('isAirport', isAirport, 'this.type', this.type, 'this[this.type][from]', this[this.type][from], 'this[this.type][to]', this[this.type][to], 'this.searchWord', this.searchWord)
    this.isAirport = isAirport
    this.showAirportPage = isAirport
    this.showPoiPage = !isAirport
    this.searchWord && this.handleInputChange(this.searchWord, type)
  }

  // 关键字搜索
  getSearchList() {
    const keyword = this.searchWord
    console.log('getSearchList', keyword)
    // const from = this.type === Type.PICK ? 'from' : 'to'
    // const to = this.type === Type.PICK ? 'to' : 'from'
    if (keyword) {
      let params:any = null
      if (this.searchType === searchType.AIRPORT) {
        // this.resetAirportData()
        params = {
          searchWord: keyword
        }
        this.getSearchAirportList(params)
      } else {
        // this.resetPoiData()
        params = {
          input: keyword,
          latitude: this[this.type].lat,
          longitude: this[this.type].long,
          sessiontoken: this.transfer_sessionToken,
          airportCode: this[this.type].code && this[this.type].code.toUpperCase()
        }
        this.getSearchPoiList(params)
      }
    } else {
      this.searchLoading = false
      this.searchList = null
    }
  }

  // 搜索机场
  getSearchAirportList(params: searchAirportObject) {
    this.getSearchAirport(params)
      .then((res: any) => {
        if (res.success && res.result) {
          this.searchList = res.result
          this.searchLoading = false
        }
      })
      .catch(() => {
        this.searchLoading = false
      })
  }

  // 搜索POI
  getSearchPoiList(params: searchPoiObject) {
    this.getSearchPoi(params)
      .then((res: any) => {
        if (res.success && res.result) {
          this.searchList = res.result
          this.searchLoading = false
          if (!res.result?.poiList?.length) {
            // 上报inhouse
            this.$inhouse.track('custom', 'body', {
              spm: 'Sug_noResult',
              ext: encodeURIComponent(JSON.stringify({
                type: this.type,
                iataCode: this[this.type].code,
                query: params.input
              }))
            })
          }
        }
      })
      .catch(() => {
        this.searchLoading = false
      })
  }

  // 选择搜索项
  selectSearchItem(item: any) {
    if (this.searchType === searchType.AIRPORT) {
      // 判断是否需要弹窗交互 2024.12.10 poi的提示逻辑需要单独请求接口处理，所以这里只判断机场类型的提示
      if (item.tips) {
        if (item.tips.type === 'toast') {
          this.$toast(item.tips.content)
        } else if (item.tips.type === 'confirm-modal') {
          this.tips = {
            ...item.tips,
            action: 0
          }
          this.tipsModalVisible = true
        }
        return
      }
      this.getAirportItem(item)
      this.getPublicList(item)
    } else {
      this.getPoiItem(item)
    }
  }

  // 关闭提示弹窗
  closeTipsModal() {
    this.tipsModalVisible = false
    if (this.tips.action === 1) {
      // 清空当前poi
      this[this.type] = {
        ...this[this.type],
        poiId: '',
        poi_lat: '',
        poi_long: '',
        place: '',
        address: ''
      }
      if (this.type === Type.PICK) {
        this[this.type].to = ''
      } else {
        this[this.type].from = ''
      }
      this.updateFormData()
    }
    this.tips = {
      content: null,
      title: null,
      btn: null,
      action: 0
    }
  }

  // 提交验证报错后，置空相关报错项
  resetData(type: string) {
    const itemObj: any = {}
    if (type === 'from') {
      itemObj.from = ''
    } else if (type === 'to') {
      itemObj.to = ''
    }
    this[this.type] = {
      ...this[this.type],
      ...itemObj
    }
  }

  // 选择POI列表数据
  async getPoiItem(item: any) {
    if (item.googlePoi) {
      const params = {
        placeId: item.placeId || '',
        sessionToken: this.transfer_sessionToken || ''
      }
      try {
        const {
          result,
          error,
          success
        } = await this.getSearchPoiDetail(params)
        if (!success) {
          this.$toast(error.message)
          return
        }
        item = Object.assign(item, { lat: result.lat, lng: result.lng })
      } catch(e) {
        return
      }
    }
    const itemObj: any = {
      poiId: item.poiId || '',
      poi_lat: item.lat || '',
      poi_long: item.lng || '',
      place: item.placeId || '',
      address: item.secondaryText || ''
    }
    if (this.type === Type.PICK) {
      itemObj.to = item.mainText
      this.searchWord = itemObj.to
    } else {
      itemObj.from = item.mainText
      this.searchWord = itemObj.from
    }
    this[this.type] = {
      ...this[this.type],
      ...itemObj
    }
    this.updateFormData()
    // 校验poi是否为黑名单
    this.checkPoi({
      ...itemObj,
      countryCode: item.countryCode
    })
  }

  checkPoi(item: any) {
    const req = {
      latitude: item.poi_lat,
      longitude: item.poi_long,
      poi_name: this.type === Type.PICK ? item.to : item.from,
      poi_id: item.poiId,
      place_id: item.place,
      country_code: item.countryCode
    }
    this.validatePoi(req).then((res: any) => {
      if (res.success) {
        // 获取当前搜索框内的poi
        const type = this.type === Type.PICK ? 'to' : 'from'
        const currentPoi = this.formData[type]
        // 只有当当前搜索框内的poi需要屏蔽时，才弹提示，防止用户快速切换poi导致提示错误
        const { is_blocked, poi_name, tips } = res.result
        if (is_blocked && poi_name === currentPoi) {
          if (tips.type === 'toast') {
            this.$toast(tips.content)
            // 清空当前poi
            this[this.type] = {
              ...this[this.type],
              poiId: '',
              poi_lat: '',
              poi_long: '',
              place: '',
              address: ''
            }
            if (this.type === Type.PICK) {
              this[this.type].to = ''
            } else {
              this[this.type].from = ''
            }
            this.updateFormData()
          } else if (tips.type === 'confirm-modal') {
            this.tips = {
              ...tips,
              action: 1
            }
            this.tipsModalVisible = true
          }
        }
      }
    })
  }

  // 点击输入框事件
  handleFocus(type: any) {
    this[type] = true
  }

  // 关闭机场搜索页
  hideSearchAirportPage(e: any) {
    // 如果点击了重新定位按钮，则不收起
    const locationArea = document.getElementById('current-location')
    if (locationArea && locationArea.contains(e?.target)) {
      return
    }
    // 在选中机场的时候这里需要等this.formData数据更新后再做判断,故需要 settimeout
    const t3 = setTimeout(() => {
      this.searchList = null
      this.showAirportPage = false
      this.$nextTick(() => {
        if (!this.pick.from) {
          this.pickfrom = false
        }
        if (!this.drop.to) {
          this.dropto = false
        }
      })
      clearTimeout(t3)
    }, 0)
  }

  // 关闭POI搜索页
  hideSearchPoiPage(e: any) {
    // 如果点击了重新定位按钮，则不收起
    const locationArea = document.getElementById('current-location')
    if (locationArea && locationArea.contains(e?.target)) {
      return
    }
    const t4 = setTimeout(() => {
      this.searchList = null
      this.showPoiPage = false
      this.$nextTick(() => {
        if (!this.pick.to) {
          this.pickto = false
        }
        if (!this.drop.from) {
          this.dropfrom = false
        }
      })
      clearTimeout(t4)
    }, 0)
  }

  // 打开日期时间选择
  openDateTimePicker() {
    this.showDateTimePicker = true
    this.initTime()
  }

  // 关闭日期时间选择
  hideDateTimePicker() {
    const t5 = setTimeout(() => {
      this.showDateTimePicker = false
      this.$nextTick(() => {
        if (!this.formData.date) {
          this.selectdate = false
        }
      })
      clearTimeout(t5)
    }, 0)
  }

  // 选择日期
  selectDate(date: Date) {
    this[this.type].date = date
    // 日期GA埋点
    this.$sendGTMCustomEvent(`Airport Transfer Vertical Screen|Select Parameter: Date|${dayjs(date).format('YYYY-MM-DD')}`)
    this.updateFormData()
  }

  // 根据当前时间初始化时间选择框
  initTime() {
    /*
      判断是否已有选中时间
      1. 如有，用默认选中已有时间
      2. 如没有，默认时间为当前日期+3，时间为09:00
    */
    const time = this[this.type].time
    const date = this[this.type].date
    if (date) {
      this.timeData = new Date(date)
    } else {
      this.timeData = dayjs().add(3, 'day').toDate()
      this[Type.PICK].date = this.timeData
      this[Type.DROP].date = this.timeData
      this.formData.date = this.formatDate(this.timeData)
    }
    if (time) {
      const hour = Number(time.split(':')[0])
      const minute = Number(time.split(':')[1])
      const _h = hour < 10 ? `0${hour}` : `${hour}`
      const _m = minute < 10 ? `0${minute}` : `${minute}`
      this.pickerTime = {
        hour: _h,
        minute: _m
      }
    } else {
      const hour = '09'
      this.pickerTime = {
        hour: `${hour}`,
        minute: '00'
      }
      this[Type.PICK].time = `${hour}:00`
      this[Type.DROP].time = `${hour}:00`
      this.formData.time = this[this.type].time
    }
  }

  // 确认时间选择
  selectTime(time: string) {
    this[this.type].time = time
    // 时间GA埋点
    this.$sendGTMCustomEvent(`Airport Transfer Vertical Screen|Select Parameter: Time|${time}`)
    this.hideDateTimePicker()
    this.updateFormData()
  }

  // 打开乘客选择器
  openPassengerCounter() {
    this.pas = this[this.type].pas
    this.showPassengerCounter = true
  }

  // 关闭乘客选择器
  hidePassengerCounter() {
    const t6 = setTimeout(() => {
      this.showPassengerCounter = false
      this.$nextTick(() => {
        if (!this.formData.passenger) {
          this.passengercount = false
        }
      })
      clearTimeout(t6)
    }, 0)
  }

  // 最大乘客数提示
  limitMax(btn: any) {
    if (btn === 'increase') {
      this.showPassengerLimitMaxTips = true
    }
    const t2 = setTimeout(() => {
      this.showPassengerLimitMaxTips = false
      clearTimeout(t2)
    }, 2000)
  }

  // 确认乘客数
  savePas() {
    this[this.type].pas = this.pas
    // 乘客数GA埋点
    this.$sendGTMCustomEvent(`Airport Transfer Vertical Screen|Select Parameter: Passenger|${this.pas}`)
    this.updateFormData()
  }

  // 更新Url
  updateUrlParams(data: urlParams) {
    const date = data.date ? dayjs(data.date).format('YYYY-MM-DD') + `${data.time ? ' ' + data.time : ''}` : ''
    const url = `?flightDirection=${data.flightDirection}&from=${encodeURIComponent(data.from)}&to=${encodeURIComponent(data.to)}&address=${encodeURIComponent(data.address)}&lat=${data.lat}&long=${data.long}&poiId=${data.poiId}&poi_lat=${data.poi_lat}&poi_long=${data.poi_long}&time=${date}&pas=${data.pas}&code=${data.code}&place=${data.place}&ac=${data.ac}&kcid=${data.kcid}`
    window.history.replaceState({}, '', url)
  }

  // 表单验证
  validateForm() {
    this.isFormValidate = true
    let key: keyof formData
    // 按航班号搜索时，单独校验from框信息
    if (this.isByFlight) {
      if (!this.flightInfo.hasFlight) {
        this.showValidateError('flightNo')
        this.formErrorTip = this.formErrorTip !== '' ? this.formErrorTip : this.formValid.flightNo?.content
        this.isFormValidate = false
        return false
      } else if (this.flightInfo.iataCode !== this.pick.code || !dayjs(this.flightInfo.arrivalTime).isSame(this.pick.time) || !dayjs(this.flightInfo.arrivalDate).isSame(this.pick.date)) {
        // 航班信息与表单不一致
        // 更新表单信息
        this.updateFlightInfo(this.flightInfo)
      }
    }
    for (key in this.formData) {
      if (!this.formData[key]) {
        this.showValidateError(key)
        // // 报错文案为空时重新赋值
        this.formErrorTip = this.formErrorTip !== '' ? this.formErrorTip : this.formValid[key].content
        this.isFormValidate = false
        return false
      } else if (key === 'from' && this[this.type].from !== this.formData.from) {
        this.showValidateError(key)
        this.isFormValidate = false
        return false
      } else if (key === 'to' && this[this.type].to !== this.formData.to) {
        this.showValidateError(key)
        this.isFormValidate = false
        return false
      }
    }
    // 表单验证GA埋点
    this.$sendGTMCustomEvent(`Airport Transfer Vertical Screen|Notification|${this.formErrorTip}`)
  }

  showValidateError(key: string) {
    this.resetData(key)
    this.formValid[key].show = true
    const t1 = setTimeout(() => {
      this.formValid[key].show = false
      clearTimeout(t1)
    }, 2000)
  }

  // 开始搜索
  search() {
    this.validateForm()
    if (this.isFormValidate) {
      // 聚合页搜索历史记录，记录是哪个tab
      this.$emit('handle-add-history')
      let langPath = ''
      const lang = this.getKlkLanguage()
      if (lang !== 'en') {
        langPath = `/${lang}`
      }
      const data = {
        ...this[this.type],
        from: this.isPickTab ? this[this.type].from : this[this.type].to,
        to: this.isPickTab ? this[this.type].to : this[this.type].from,
        flightNo: this.isByFlight ? this.flightInfo.flightNo : '',
        originCode: this.isByFlight ? this.flightInfo.originCode : '',
        departureName: this.isByFlight ? this.flightInfo.departureName : ''
      }
      if (this.currentPage === 'home') {
        const date = data.date && dayjs(data.date).format('YYYY-MM-DD') + `${data.time ? ' ' + data.time : ''}`
        const query = `?flightDirection=${data.flightDirection || ''}&from=${encodeURIComponent(data.from || '')}&to=${encodeURIComponent(data.to || '')}&address=${encodeURIComponent(data.address || '')}&lat=${data.lat || ''}&long=${data.long || ''}&poiId=${data.poiId || ''}&poi_lat=${data.poi_lat || ''}&poi_long=${data.poi_long || ''}&time=${date || ''}&pas=${data.pas || ''}&code=${data.code || ''}&place=${data.place || ''}&ac=${data.ac || ''}&kcid=${data.kcid || ''}&flightNo=${data.flightNo}&originCode=${data.originCode}&departureName=${data.departureName}`
        const href = `${langPath}/airport-transfers/results/${query}`
        // 表单提交GA埋点
        const timeDelta = dayjs(data.date).diff(dayjs().format('MM/DD/YYYY'), 'day')
        const gaSumitData = `To: ${data.to} - From: ${data.from} - Date: ${dayjs(data.date).format('YYYY-MM-DD')} - Time: ${data.time} - Passengers: ${data.pas} - Place ID: ${data.place} - lat: ${data.lat} - lng: ${data.long}`
        this.$sendGTMCustomEvent(`Airport Transfer Vertical Screen|Search Submit|${gaSumitData}|${timeDelta}`)
        // 缓存历史记录
        this.saveHistory()
        window.open(href, '_blank')
      } else if (this.currentPage === 'srp') {
        // 缓存历史记录
        this.saveHistory()
        this.$emit('search', data)
      }
    }
  }
  // CHP3.0事件监听
  eventListener(info: any) {
    this.getAirportItem(info)
  }

    get galileoClick1() {
        return { spm: WebClick_71, autoTrackSpm: true }
    }

    get galileoClick2() {
        return { spm: WebClick_72, autoTrackSpm: true }
    }

    get galileoClick3() {
        return { spm: WebClick_73, autoTrackSpm: true }
    }

    get galileoClick4() {
        return { spm: WebClick_74, autoTrackSpm: true }
    }

    get galileoClick5() {
        return { spm: WebClick_75, autoTrackSpm: true }
    }

    get galileoClick6() {
        return { spm: WebClick_76, autoTrackSpm: true }
    }
}
</script>

<style lang="scss" scoped>
@mixin text-ellipsis($line-clamp: 2) {
  display: -webkit-box;
  -webkit-line-clamp: $line-clamp;
  -webkit-box-orient: vertical;
  text-overflow: ellipsis;
  overflow: hidden;
}
.search-form {
  width: 1160px;
  padding: 20px;
  border-radius: $radius-l;
  background-color: $color-bg-1;
  box-shadow: $shadow-normal-5;
  margin: 0 auto;

  &__item {
    height: 74px;
  }

  &.hide-radius-left-top {
    border-radius: $radius-none $radius-l $radius-l $radius-l;
  }

  &.hide-radius-above {
    border-radius: 0 0 $radius-l $radius-l;
  }

  &.show-radius-left-top {
    border-radius: $radius-l;
  }

  &__tabs {
    position: relative;
    z-index: 10;
    display: flex;
    flex-direction: row;
    margin-bottom: 16px;

    span {
      @include font-body-s-regular;
      line-height: normal;
      text-align: center;
      cursor: pointer;
      margin-right: 48px;
    }
  }

  &__wraper {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    position: relative;
  }

  &__flex-grow {
    flex-grow: 1;
    max-width: 398px;
  }

  &__place {
    position: relative;
    margin-right: 12px;
    //flex: 1 0 0;

    svg {
      position: absolute;
      top: 19px;
      left: 12px;
    }

    input {
      @include font-body-s-bold;
      @include text-ellipsis(1);
      width: 100%;
      border:  1px solid transparent;
      caret-color: $color-brand-primary;
      background-color: $color-bg-3;
      border-radius: $radius-l;
      outline: none;
      padding-left: 20px;

      &::-webkit-input-placeholder {
        color: $color-text-secondary;
        font-weight: 400;
        opacity: 1;
      }

      &:focus {
        border: 1px solid $color-brand-primary;
      }
    }

    &-from-placeholder {
      @include font-caption-m-regular;
      color: $color-text-placeholder;
      position: absolute;
      margin: 20px 0 0 16px;
    }

    &-fill,&-null {
      @include font-body-s-bold;
      @include text-ellipsis(1);
      display: flex;
      align-items: center;
      border:  1px solid transparent;
      caret-color: $color-brand-primary;
      background-color: $color-bg-3;
      border-radius: $radius-l;
      outline: none;
      padding-left: 20px;

      &.active {
        border: 1px solid $color-brand-primary;
      }

      &.picked {
        padding: 20px 0 0 16px;
      }
    }

    &-null {
      color: $color-text-secondary;
      font-weight: 400;
    }
  }

  &__place-clickd {
    input {
      padding: 20px 0 0 16px;
      font-weight: $fontWeight-bold;
    }
  }

  &__date {
    width: 158px;
  }

  &__pas {
    padding-top: 0 !important;
    padding-right: 16px !important;
    width: 168px;
    &-text {
      flex-grow: 1;
    }
    &-icon {
      height: 16px;
    }
  }

  &__passenger {
    background-color: $color-bg-1;
    border-radius: $radius-xl;
    box-shadow: $shadow-normal-4;
    min-width: 340px;
    padding: 16px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    position: absolute;
    left: 0;
    top: 80px;

    &-box {
      display: flex;
      flex-direction: column;
    }

    &-title {
      @include font-body-s-regular;
    }

    &-subtitle {
      @include font-body-s-regular;
      color: $color-text-secondary;
    }
  }

  &__btn {
    flex: 160px 0 0;

    button {
      border-radius: $radius-l;
      padding: 10px;
    }
  }

  &__modal {
    .klk-modal-content {
      text-align: center;
    }
  }

  // 航班号前置样式-接机方式切换tab
  &__pick-type-wrapper {
    padding: 4px 4px 0 4px;
    // fix time接送tab样式
    &-fix-time {
      position: absolute;
      margin: 4px 4px 0 4px;
    }
  }

  // 航班号前置样式
  &__wrapper_by_flight {

    .search-form {

      &__place-from {
        width: 100%;
        height: 74px;
        padding: 12px 16px;
        caret-color: $color-brand-primary;
        background-color: $color-bg-3;
        border-radius: $radius-l;
        border:  1px solid transparent;
        outline: none;
        cursor: pointer;
        display: flex;
        flex-direction: column;
        justify-content: center;

        &-airport {
          @include font-body-s-bold;
          @include text-ellipsis(1);
          color: $color-text-primary;
        }

        &-airport-label {
          @include font-body-xs-regular;
          color: $color-text-placeholder;
        }

        &-airport-placeholder {
          @include font-body-s-regular-v2;
          color: $color-text-placeholder;
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
          margin-top: 2px;
        }

        &-time {
          @include font-body-xs-regular;
          @include text-ellipsis(1);
          color: $color-text-primary;
        }
      }

      &__place-to-by-flight {
        input {
          width: 100%;
        }
      }
    }
  }
}
</style>
