<template>
  <div>
    <div v-if="visible">
      <klk-modal
        modal-class="search-by-flight__modal-desktop"
        :title="$t('203800-select_by_flight')"
        :open.sync="visible"
        :width="600"
        button-align="block"
        :show-cancel-button="false"
        closable
        :show-default-footer="false"
        @close="closeModal"
      >
        <klk-tabs :value="formType" underline data-spm-page="SearchFlight" @change="changeTab">
          <klk-tab-pane
            :data-spm-module="`FlightTab?trg=manual&ext=${encodeURIComponent(JSON.stringify({
              tab: 'ArrivalFlight'
            }))}`"
            data-spm-virtual-item="__virtual"
            :label="$t('203785-arrival_flight_number')"
            name="formFlightNo"
            width="50%"
          />
          <klk-tab-pane
            :data-spm-module="`FlightTab?trg=manual&ext=${encodeURIComponent(JSON.stringify({
              tab: 'FlighOD'
            }))}`"
            data-spm-virtual-item="__virtual"
            :label="$t('208178-Departure & arrival airports')"
            name="formOD"
            width="50%"
          />
        </klk-tabs>
        <div v-if="isNoODSearchResult" class="search-by-flight__od-no-result">
          <div class="search-by-flight__od-no-result-content">
            <airplanpicAirpla></airplanpicAirpla>
            <p>{{ $t('209031') }}</p>
          </div>
          <div class="search-by-flight__od-no-result-btn">
            <klk-button @click="handleSearchByFlight">{{ $t('208181') }}</klk-button>
            <klk-button type="outlined" @click="handleCancel">{{ $t('208182') }}</klk-button>
          </div>
        </div>
        <div v-else data-spm-page="SearchFlight">
          <klk-alert type="info" class="search-by-flight__tips">
            {{ isByFlightNo ? $t('203782-tips') : $t('210319') }}
          </klk-alert>
          <div class="search-by-flight__form">
            <!-- 航班号 -->
            <div v-show="isByFlightNo">
              <span class="search-by-flight__label">
                {{ $t('13996') }}
              </span>
              <klk-input
                v-model="formData.flightNo"
                data-spm-module="InputArrivalFlight"
                v-galileo-click-tracker="galileoClick1"
                data-spm-virtual-item="__virtual"
                clearable
                name="flightNo"
                :placeholder="$t('203778-flight_no')"
                :class="['search-by-flight__form-item']"
                @blur="updateFormDataToState('flightNo')"
              />
            </div>
            <!-- OD搜索 -->
            <div v-show="isByOD" class="search-by-flight__od_container">
              <div>
                <span class="search-by-flight__label">
                  {{ $t('209030') }}
                </span>
                <div
                  v-click-outside="(e) => hideSearchAirportPage(e, 'isDepartureIata')"
                  @click="handleFocus('isDepartureIata')"
                >
                  <klk-input
                    v-model="formData.departureIata"
                    data-spm-module="InputArrivalFlight"
                    data-spm-virtual-item="__virtual"
                    clearable
                    name="departureIata"
                    append-icon="icon_navigation_chevron_down_xxs"
                    :placeholder="$t('208179-Please input the Departure airport')"
                    :class="['search-by-flight__form-item']"
                    @focus="openSearch('departureIata')"
                    @input="handleInputChange(formData.departureIata, 'departureIata')"
                  />
                </div>
                <search-page
                  v-if="showSearchAirportPage && isDepartureIata"
                  :search-type="searchType"
                  :loading="searchLoading"
                  :search-list="searchList"
                  :airport-group="airportGroup"
                  :search-word="searchWord"
                  :offset-top="96"
                  :z-index="3"
                  :max-height="500"
                  :is-pick-tab="isPickTab"
                  arrival-or-departure="departure"
                  :search-history="searchHistory"
                  @selectSearchItem="selectSearchItem"
                  @getSearchHistory="getSearchHistory"
                  @clearHistory="clearHistory"
                ></search-page>
              </div>
              <div class="icon_switch">
                <iconSwitch @click.native="switchOD"></iconSwitch>
              </div>
              <div
                v-click-outside="(e) => hideSearchAirportPage(e, 'isArrivalIata')"
                @click="handleFocus('isArrivalIata')"
              >
                <klk-input
                  v-model="formData.arrivalIata"
                  data-spm-module="InputArrivalFlight"
                  data-spm-virtual-item="__virtual"
                  clearable
                  name="arrivalIata"
                  append-icon="icon_navigation_chevron_down_xxs"
                  :placeholder="$t('208180-Please input the Arrival airport')"
                  :class="['search-by-flight__form-item']"
                  @focus="openSearch('arrivalIata')"
                  @input="handleInputChange(formData.arrivalIata, 'arrivalIata')"
                />
                <search-page
                  v-if="showSearchAirportPage && isArrivalIata"
                  :search-type="searchType"
                  :loading="searchLoading"
                  :search-list="searchList"
                  :airport-group="airportGroup"
                  :search-word="searchWord"
                  :offset-top="152"
                  :z-index="3"
                  :max-height="450"
                  :is-pick-tab="isPickTab"
                  arrival-or-departure="arrival"
                  :search-history="searchHistory"
                  @selectSearchItem="selectSearchItem"
                  @getSearchHistory="getSearchHistory"
                  @clearHistory="clearHistory"
                ></search-page>
              </div>
            </div>
            <!-- 日期选择 -->
            <div
              v-click-outside="closeDatePicker"
              class="search-by-flight__form-item-time-wrapper"
            >
              <span class="search-by-flight__label">
                {{ $t('113100') }}
              </span>
              <div
                :data-spm-module="`InputDepartureTime?ext=${encodeURIComponent(JSON.stringify({
                  tab: 'ArrivalFlight'
                }))}`"
                v-galileo-click-tracker="galileoClick2"
                data-spm-virtual-item="__virtual"
                :class="['search-by-flight__form-item', 'search-by-flight__form-item-row', 'select', showDateTimePicker && 'active']"
                @click="showDateTimePicker = true"
              >
                <div v-if="!formData.pickupDate" class="search-by-flight__form-item-placeholder">
                  <span>{{ $t('203801-flight_time') }}</span>
                </div>
                <div v-else class="search-by-flight__form-item-text">
                  <span v-if="formData.pickupDate">{{ dateFormatter(formData.pickupDate) }} </span>
                </div>
                <klk-icon type="icon_navigation_chevron_down_xxs" size="20" />
              </div>
              <!-- 日期选择 -->
              <div v-if="showDateTimePicker" class="search-by-flight__time-picker">
                <date-time-picker
                  v-show="showDateTimePicker"
                  :date="curPickDate"
                  :time="curPickTime"
                  :hide-time-picker="true"
                  :is-pick-tab="false"
                  :show-pick-up-tips="true"
                  @cancle="closeDatePicker"
                  @selectDate="selectDate"
                  @selectTime="selectTime"
                ></date-time-picker>
              </div>
            </div>
          </div>
          <div class="search-by-flight__footer">
            <klk-button
              :data-spm-module="`Search?ext=${encodeURIComponent(JSON.stringify({
                tab: isByFlightNo ? 'ArrivalFlight' : 'FlighOD'
              }))}`"
              :disabled="isFlightSearchDisabled()"
              v-galileo-click-tracker="galileoClick3"
              data-spm-virtual-item="__virtual"
              :loading="finallyLoading"
              @click="searchFlight"
            >
              {{ $t('car_rental_home_search') }}
            </klk-button>
          </div>
        </div>
      </klk-modal>
    </div>
    <client-only>
      <klk-modal
        :open.sync="showFlightModal"
        :title="$t('203800-select_by_flight')"
        :width="600"
        :show-default-footer="false"
        closable
        scrollable
        @close="closeFlightModal"
      >
        <div slot="header" class="search-by-flight__flight-card-header">
          <div class="search-by-flight__flight-card-header-right">
            <div class="search-by-flight__flight-card-title">{{ $t('203800-select_by_flight') }}</div>
            <div>
              <span>{{ $t("12983") }}</span>
              <span>{{ defaultArrivalDate }}</span>
            </div>
          </div>
        </div>
        <div
          v-if="showFlightModal"
          data-spm-page="SelectFlight"
        >
          <flight-card
            :is-by-od="isByOD"
            :flight-info-list="flightList"
            @update-flight-info="updateFlightInfo"
          ></flight-card>
        </div>
      </klk-modal>
    </client-only>
    <ConfirmModal
      :tips="tips"
      :tips-modal-visible.sync="tipsModalVisible"
    ></ConfirmModal>
  </div>
</template>

<script lang="ts">
import { Component } from 'vue-property-decorator'
import dayjs from 'dayjs'
import SearchPage from './search-page.vue'
import DateTimePicker from './date-time-picker.vue'
import SearchByFlightCommon from '../../common/search-form/search-by-flight-common'
import airplanpicAirpla from '../svg/airplanpicAirpla.vue'
import iconSwitch from '../svg/iconSwitch.vue'
import { WebClick_81, WebClick_82, WebClick_83 } from '../../../share/galileo/auto-click'

type InputSearchType = 'airport' | 'airline'

@Component({
  components: {
    SearchPage,
    DateTimePicker,
    airplanpicAirpla,
    iconSwitch
  }
})
export default class SearchByFlight extends SearchByFlightCommon {
  get curPickDate() {
    return this.formData.pickupDate || dayjs().add(3, 'day').toDate()
  }

  get curPickTime() {
    return {
      hour: '09',
      minute: '00'
    }
  }

    get galileoClick1() {
        return { spm: WebClick_81, autoTrackSpm: true }
    }

    get galileoClick2() {
        return { spm: WebClick_82, autoTrackSpm: true }
    }

    get galileoClick3() {
        return { spm: WebClick_83, autoTrackSpm: true }
    }
}
</script>

<style lang="scss" scoped>
.search-by-flight {
  &__od_container {
    position: relative;
    .icon_switch {
      width: 36px;
      height: 36px;
      position: absolute;
      top: 107px;
      left: 50%;
      transform: translate(-50%, -50%);
      z-index: 2;
      cursor: pointer;
    }
  }
  &__flight-card-header {
    margin: 0 10px 20px;
  }
  &__label {
    display: inline-block;
    margin-top: 16px;
  }
  &__od-no-result{
    &-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin: 39px 0 55px;
    }
    &-btn {
      display: flex;
      justify-content: space-between;
      gap: 12px;
      .klk-button {
        flex: 1;
      }
    }
  }
  &__tips {
    padding: 8px 12px;
    ::v-deep .klk-alert-content {
      @include font-paragraph-xs-regular;
      color: $color-text-link;
    }
  }
  &__flight-card {
    &-header {
      @include font-body-s-regular-v2;
      color: $color-text-primary;
    }
    &-title {
      @include font-heading-s;
    }
  }
  &__form {
    &-item {
      margin-top: 16px;

      &.select {
        border:  1px solid $color-border-normal;
        border-radius: $radius-l;
        padding: 0 12px;
        min-height: 44px;
      }

      &-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        cursor: pointer;

        &.active {
          border: 1px solid $color-border-active;
        }
      }

      &-placeholder {
        @include font-paragraph-m-regular;
        color: $color-text-placeholder;
      }

      &-text {
        @include font-paragraph-m-regular;
        color: $color-text-primary;
      }

      &-time-wrapper {
        position: relative;
      }
    }
  }
  &__airport-list, &__airline-list {
    z-index: 1;
    top: 60px;
    width: 100%;
    ::v-deep.search__list, ::v-deep.search__loading {
      width: 100% !important;
    }
    &.search {
      max-height: 400px;
    }
  }
  &__time-picker {
    position: absolute;
    top: 96px;
    left: 0;
    ::v-deep .date-time-pick {
      top: 0 !important;
    }
  }
  &__error {
    &-item {
      border: 1px solid $color-brand-primary !important;
    }

    &-item-input {
      ::v-deep .klk-input-inner {
        border: 1px solid $color-brand-primary !important;
      }
    }

    &-text {
      @include font-paragraph-s-regular;
      color: $color-brand-primary;
    }
  }
  &__footer {
    margin-top: 24px;
    width: 100%;
    .klk-button {
      width: 100%;
    }
  }
}
</style>
<style>
.search-by-flight__modal-desktop{
  top: -20px;
}
::v-deep .klk-tabs-control {
  display: none;
}
::v-deep .klk-tabs-body {
  margin-top: unset;
}
::v-deep .klk-modal-body {
  margin: 0 32px !important;
}
</style>
