<template>
  <div
    class="search"
    :class="{'airport': searchType === 'airport'}"
    :style="{
      position: positionType,
      left: formatNumber(offsetLeft),
      top: formatNumber(offsetTop),
      'z-index': zIndex,
      'max-height': `${maxHeight}px`,
    }"
  >
    <div v-if="showAdditionalInfo" class="search__additional-info">
      <template v-if="isDeparture || !isPickTab">
        <div id="current-location" class="search__current-location">
          <div class="search__current-location--header">
            <div>{{ $t('192362-currentLocation') }}</div>
            <div
              v-if="locationBtnStatus === 'success'"
              class="search__current-location--search"
              @click.stop="initLocation"
            >
              <SvgIcon class="icon" name="mobile-ptp#icon-relocate" width="16" height="16" />
              {{ $t('192363-relocate') }}
            </div>
          </div>
          <div
            v-if="locationBtn[locationBtnStatus]"
            class="search__current-location--status"
            @click="locationBtn[locationBtnStatus].handler"
          >
            {{ locationBtn[locationBtnStatus].text }}
          </div>
        </div>
        <div v-if="locationBtnStatus === 'success'" class="search__current-location--content">
          <div
            v-for="(item, index) in currentCityAirport"
            :key="item.iataCode"
            class="search__additional-info-item"
            :data-spm-module="`Sug_recAirport_LIST?oid=${item.iataCode}&idx=${index}&len=${currentCityAirport.length}`"
            v-galileo-click-tracker="galileoClick1"
            data-spm-virtual-item="__virtual?typ=entry"
            @click="selectSearchItem(item)"
          >
            <p class="search__additional-info-item--name">{{ item.airportName }}({{ item.iataCode }})</p>
          </div>
        </div>
      </template>
      <!-- 机场历史搜索记录 -->
      <div v-if="searchHistoryByType && searchHistoryByType.length">
        <div class="search__history--header">
          <div>{{ $t('192361-history') }}</div>
          <div class="remove" @click="clearHistory">{{ $t('192364-remove') }}</div>
        </div>
        <div class="search__history--content">
          <div
            v-for="(item, index) in searchHistoryByType"
            :key="item.iataCode"
            class="search__additional-info-item"
            :data-spm-module="`Sug_recentAirport_LIST?oid=${item.iataCode}&idx=${index}&len=${searchHistoryByType.length}`"
            v-galileo-click-tracker="galileoClick2"
            data-spm-virtual-item="__virtual?typ=entry"
            @click="selectSearchItem(item)"
          >
            <p class="search__additional-info-item--name">{{ item.airportName }}({{ item.iataCode }})</p>
          </div>
        </div>
      </div>
    </div>
    <div v-if="!hideHotAirport && !searchWord && searchType === 'airport'" class="airport-group">
      <div class="airport-group__nav">
        <div
          v-for="(item, index) in airportGroup"
          :key="`group_${index}`"
          :class="{'active': index === currentActiveIndex}"
          class="airport-group__nav-item"
          @mouseover="currentActiveIndex = index"
        >
          {{ item.groupName }}
        </div>
      </div>
      <div class="airport-group__content">
        <div
          v-for="(city, index) in currentActiveGroup"
          :key="`city_${index}`"
          class="airport-group__content-item"
        >
          <div class="airport-group__content-city">{{ city.cityName }}</div>
          <div class="airport-group__content-airports">
            <span
              v-for="(airport, aidx) in city.operatedAirportItemList"
              :key="`airport_${aidx}`"
              class="airport-group__content-airport"
              :data-spm-module="`PopularAirport_LIST?oid=${airport.iataCode}&ext=${JSON.stringify({type: isPickTab ? 'AirportPickup' : 'AirportDropoff'})}`"
              v-galileo-click-tracker="galileoClick3"
              data-spm-virtual-item="__virtual?typ=entry"
              @click="selectSearchItem(airport)"
            >
              {{ airport.airportName }}（{{ airport.iataCode }}）
            </span>
          </div>
        </div>
      </div>
    </div>
    <div v-if="loading" :class="!searchWord && searchType === 'airport' ? 'search-group__loading' : 'search__loading'">
      <klk-loading></klk-loading>
    </div>
    <div v-else>
      <div v-if="airportList" class="search__list">
        <div
          v-for="item in searchList.airportSearchItemList"
          :key="item.iataCode"
          v-galileo-click-tracker="{ spm: `AirportSearchResult_LIST?oid=${item.iataCode}&ext=${JSON.stringify({type: isPickTab ? 'AirportPickup' : 'AirportDropoff'})}`, autoTrackSpm: true }"
          class="search__list-item"
          :data-spm-module="`AirportSearchResult_LIST?oid=${item.iataCode}&ext=${JSON.stringify({type: isPickTab ? 'AirportPickup' : 'AirportDropoff'})}`"
          data-spm-virtual-item="__virtual?typ=entry"
          @click="selectSearchItem(item)"
        >
          <p class="search__list-item--name">{{ item.airportName }}({{ item.iataCode }})</p>
          <span class="search__list-item--address">{{ item.cityName }}</span>
        </div>
      </div>
      <div v-if="poiList" class="search__list">
        <div
          v-for="(item, index) in searchList.poiList"
          :key="index"
          v-galileo-click-tracker="{ spm: `POISearchResult_LIST?idx=${index}&len=${searchList.poiList.length}&oid=${item.googlePoi ? item.placeId : item.poiId}&ext=${JSON.stringify({type: isPickTab ? 'AirportPickup' : 'AirportDropoff', query: searchWord, sug: item.mainText, poiType: item.placeTypeName})}`, autoTrackSpm: true }"
          class="search__list-item"
          :data-spm-module="`POISearchResult_LIST?idx=${index}&len=${searchList.poiList.length}&oid=${item.googlePoi ? item.placeId : item.poiId}&ext=${JSON.stringify({type: isPickTab ? 'AirportPickup' : 'AirportDropoff', query: encodeURIComponent(searchWord), sug: encodeURIComponent(item.mainText), poiType: encodeURIComponent(item.placeTypeName)})}`"
          data-spm-virtual-item="__virtual?typ=entry"
          @click.stop="selectSearchItem(item)"
        >
          <div class="search__list-item--wrapper">
            <div v-if="item.icon" class="search__list-item--left">
              <img :src="item.icon" width="16" :alt="item.placeTypeName" />
            </div>
            <div class="search__list-item--right">
              <div class="search__list-item--header">
                <span class="search__list-item--header-text search__list-item--name">{{ item.mainText }}</span>
                <span v-if="item.placeTypeName" class="search__list-item--header-type">{{ item.placeTypeName }}</span>
              </div>
              <span class="search__list-item--poi-address">{{ item.secondaryText }}</span>
            </div>
          </div>
        </div>
      </div>
      <div v-if="noResult" class="search__list">
        <div class="search__list-item">{{ $t('14794-no_result') }}</div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Prop } from 'vue-property-decorator'
import SearchPageBase from "../../common/search-form/search-page-base"
import { WebClick_61, WebClick_62, WebClick_63 } from '../../../share/galileo/auto-click'

@Component
export default class SearchPage extends SearchPageBase {
  @Prop({ type: Array, default: () => [] }) airportGroup!: any[]
  @Prop({ type: Object, default: () => null }) searchList!: any
  @Prop({ type: String, default: '' }) searchType!: string
  @Prop({ type: Boolean, default: false }) loading!: boolean
  @Prop({ type: String, default: '' }) searchWord!: string
  @Prop({ type: Number, default: 0 }) offsetLeft!: number
  @Prop({ type: Number, default: 80 }) offsetTop!: number
  @Prop({ type: Number, default: 1 }) zIndex!: number
  @Prop({ type: String, default: 'absolute' }) positionType!: string
  @Prop({ type: Number, default: 600 }) maxHeight!: number

  currentActiveIndex: number = 0

  get currentActiveGroup() {
    return (this.airportGroup?.length && this.airportGroup[this.currentActiveIndex].operatedCityList) || []
  }

  get airportList() {
    return this.searchWord && this.searchType === 'airport' && this?.searchList?.airportSearchItemList?.length
  }

  get poiList() {
    return this.searchWord && this.searchType === 'poi' && this?.searchList?.poiList?.length
  }

  get noResult() {
    return this.searchWord && this.searchList && ((!this.searchList.airportSearchItemList || (this.searchList.airportSearchItemList && this.searchList.airportSearchItemList.length === 0)) && !this.searchList.poiList)
  }

  get showAdditionalInfo() {
    return !this.hideExtraInfo && this.searchType === 'airport' && !this.searchWord
  }

  // 选择列表项
  selectSearchItem(item: any) {
    this.$emit('selectSearchItem', item)
  }

  formatNumber(num: number) {
    return num && `${num}px`
  }

  mounted() {
    if (!this.hideExtraInfo) {
      this.$emit('getSearchHistory')
      this.initLocation()
    }
  }

    get galileoClick1() {
        return { spm: WebClick_61, autoTrackSpm: true }
    }

    get galileoClick2() {
        return { spm: WebClick_62, autoTrackSpm: true }
    }

    get galileoClick3() {
        return { spm: WebClick_63, autoTrackSpm: true }
    }
}
</script>

<style lang="scss" scoped>
@mixin text-ellipsis($line-clamp: 2) {
  display: -webkit-box;
  -webkit-line-clamp: $line-clamp;
  -webkit-box-orient: vertical;
  text-overflow: ellipsis;
  overflow: hidden;
}
.search {
  background-color: $color-bg-1;
  border-radius: $radius-xl;
  box-shadow: $shadow-normal-5;
  overflow: auto;
  position: absolute;
  top: 80px;
  left: 0;
  max-height: 600px;
  scrollbar-width: none; /* Firefox 64 */
  -ms-overflow-style: none; /* Internet Explorer 11 */
  &::-webkit-scrollbar { /** WebKit */
    display: none;
  }

  &-group__loading {
    position: absolute;
    left: 50%;
    top: 50%;
  }

  &__loading {
    width: 479px;
    padding: 0 16px;
    margin-top: 12px;
    height: 50px;
  }

  &__list {
    width: 479px;
    padding: 16px 0;

    &-item {
      padding: 8px 16px;
      cursor: pointer;

      &:hover {
        background-color: $color-bg-3;
      }

      &:first-child {
        margin-top: 0;
      }

      &--wrapper {
        display: flex;
        justify-content: space-between;
      }

      &--left {
        width: 20px;
        padding: 0 2px;
        margin-right: 4px;
      }

      &--right {
        flex-grow: 1;
      }

      &--name {
        @include font-body-s-regular;
      }

      &--header {
        display: flex;
        align-items: flex-start;
        justify-content: space-between;
        &-text {
          @include text-ellipsis(2);
          flex-shrink: 1;
        }
        &-type {
          @include font-body-s-regular;
          flex-grow: 1;
          color: $color-caution;
          text-align: right;
          margin-left: 4px;
          white-space: nowrap;
        }
      }

      &--address, &--poi-address {
        @include font-caption-m-regular;
        @include text-ellipsis(2);
        color: $color-text-secondary;
        margin-top: 4px;
      }

      &--poi-address {
        max-width: 400px;
      }
    }
  }

  &__additional-info {
    margin-top: 12px;
    padding-bottom: 20px;
    border-bottom: 1px solid $color-bg-widget-darker-2;
    width: 797px;
    .search__additional-info {
      &-item {
        padding: 8px;
        margin: 5px 6px;
        background-color: $color-bg-3;
        border-radius: $radius-xxl;
        width: fit-content;
        cursor: pointer;

        &:hover {
          background-color: $color-bg-page !important;
        }

        &--name {
          @include font-body-s-semibold;
        }
      }
    }
  }

  &__current-location {
    overflow: hidden;
    &--header {
      font-size: $fontSize-body-s;
      padding: 8px 16px;
      line-height: $lineHeight-compact;
      font-weight: $fontWeight-bold;
      display: flex;
      justify-content: space-between;
    }
    &--content {
      display: flex;
      flex-wrap: wrap;
      padding: 0 6px;
    }
    &--search {
      color: $color-info;
      font-size: $fontSize-body-s;
      cursor: pointer;
      display: flex;
      align-items: center;
      .icon {
        margin-right: 4px;
      }
    }
    &--status {
      @include font-body-s-semibold;
      margin: 5px 12px;
      padding: 8px;
      background-color: $color-bg-3;
      border-radius: $radius-xxl;
      width: fit-content;
      cursor: pointer;
      display: flex;
      align-items: center;
      width: fit-content;

      &:hover {
        background-color: $color-bg-page !important;
      }
    }
  }

  &__history {
    &--header {
      font-size: $fontSize-body-s;
      margin-top: 12px;
      padding: 8px 16px;
      line-height: $lineHeight-compact;
      font-weight: $fontWeight-bold;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .remove {
        @include font-body-s-semibold();
        cursor: pointer;
        color: $color-info;
      }
    }
    &--content {
      display: flex;
      flex-wrap: wrap;
      padding: 0 6px;
    }
  }
}
.airport-group {
  width: 797px;
  height: 408px;
  display: flex;
  flex-direction: row;

  &__nav {
    width: 230px;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    border-right: 1px solid $color-bg-widget-darker-2;

    &-item {
      @include font-body-s-bold;
      flex: 1 0 0;
      border-bottom: 1px solid $color-bg-widget-darker-2;
      display: flex;
      align-items: center;
      padding-left: 22px;

      &:last-child {
        border: none;
      }

      &.active {
        background-color: $color-brand-primary;
        color: $color-white;
        position: relative;

        &::before {
          content: '';
          width: 16px;
          height: 16px;
          background: $color-brand-primary;
          position: absolute;
          right: -8px;
          -webkit-transform: rotate(45deg);
          transform: rotate(45deg);
          z-index: 1;
        }
      }
    }
  }

  &__content {
    flex: 1 0 0;
    height: 100%;
    padding: 4px 20px 20px 22px;
    overflow-y: scroll;

    &-item {
      display: flex;
      flex-direction: row;
      margin-top: 16px;
    }

    &-city {
      @include font-body-s-regular;
      color: $color-text-secondary;
      width: 125px;
      margin-right: 12px;
    }

    &-airports {
      display: flex;
      flex-direction: column;
    }

    &-airport {
      @include font-body-s-bold;
      cursor: pointer;

      &:hover {
        color: $color-brand-primary;
      }

      &:nth-child(n+2) {
        margin-top: 7px;
      }
    }
  }
}
</style>
