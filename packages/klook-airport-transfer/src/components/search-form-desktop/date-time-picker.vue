<template>
  <div class="date-time-pick">
    <div class="date-pick">
      <klk-date-picker
        :date.sync="date"
        width="auto"
        view-switchable
        :tip="$t('10939-tip')"
        :min-date="minDate"
        :max-date="maxDate"
        @select="selectDate"
      ></klk-date-picker>
    </div>
    <div v-if="!hideTimePicker" class="time-pick">
      <div>
        <div class="time-pick__title">{{ $t('14084-time') }}</div>
        <div v-if="showPickUpTips" class="time-pick__tips">{{ $t('194302-pickUpTimeTips') }}</div>
        <div class="time-pick__content">
          <klk-select
            v-model="hour"
            placeholder="Please Select"
            :max-height="240"
            style="width: 110px;"
          >
            <klk-option v-for="h in getHours()" :key="'hour' + h.value" :value="h.value" :label="h.label"></klk-option>
          </klk-select>
          <span>:</span>
          <klk-select
            v-model="minute"
            placeholder="Please Select"
            :max-height="240"
            style="width: 110px;"
          >
            <klk-option v-for="m in getMinutes()" :key="'minute' + m.value" :value="m.value" :label="m.label"></klk-option>
          </klk-select>
        </div>
      </div>
      <div class="time-pick__btns">
        <div class="time-pick__btns-cancel" @click="cancle">{{ $t('global.tips.cancelTxt') }}</div>
        <klk-button size="small" @click="selectDateTime">{{ $t('15762-ok') }}</klk-button>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch } from 'vue-property-decorator'
import dayjs from 'dayjs'

@Component
export default class DateTimePicker extends Vue {
  @Prop({ type: Date, default: () => null }) date!: any
  @Prop({ type: Object, default: () => null }) time!: any
  @Prop({ type: Boolean, default: true }) isPickTab!: boolean
  @Prop({ type: Boolean, default: false }) showPickUpTips!: boolean
  @Prop({ type: Boolean, default: false }) hideTimePicker!: boolean // 是否展示时间选择
  minDate: Date = dayjs().subtract(1, 'day').toDate()
  maxDate: Date = dayjs().add(1, 'year').toDate()
  hour: string = '09'
  minute: string = '00'
  selectedDate: Date = new Date()

  @Watch('time', { immediate: true })
  setTime(val: any) {
    if (val) {
      this.hour = this.formatHour(val.hour)
      this.minute = val.minute // 传过来的分钟会补0，这里不需要处理，小时在mounted重新获取了，需要额外处理
    }
  }

  @Watch('date', { immediate: true })
  setDate(val: Date) {
    if (val) {
      this.selectedDate = val
    }
  }

  formatMinute(t: any) {
    const _t = String(t)
    return _t.length < 2 ? '0' + _t : _t
  }

  formatHour(t: any) {
    const _t = String(t)
    if (_t === '24') {
      return '00'
    }
    return _t.length < 2 ? '0' + _t : _t
  }

  getCurHour() {
    return this.time
  }

  getCurMinute() {
    return this.time
  }

  getHours() {
    const hours: any[] = []
    for (let h = 0, num = 23; h <= num; h++) {
      hours.push({
        value: this.formatHour(h),
        label: this.formatHour(h)
      })
    }
    return hours
  }

  getMinutes() {
    const minutes: any[] = []
    for (let m = 0, num = 59; m <= num; m++) {
      minutes.push({
        value: this.formatMinute(m),
        label: this.formatMinute(m)
      })
    }
    return minutes
  }

  selectDate(date: Date) {
    this.selectedDate = date
    if (this.hideTimePicker) {
      this.$emit('selectDate', this.selectedDate)
      this.$emit('cancle', this.selectedDate)
    }
  }

  selectDateTime() {
    this.$emit('selectDate', this.selectedDate)
    this.$emit('selectTime', this.hour + ':' + this.minute)
  }

  cancle() {
    this.$emit('cancle')
  }
}
</script>

<style lang="scss" scoped>
.date-time-pick {
  background-color: $color-bg-1;
  border-radius: $radius-xl;
  box-shadow: $shadow-normal-4;
  min-width: 340px;
  display: flex;
  flex-direction: row;
  position: absolute;
  left: 0;
  top: 80px;
  .date-pick {
    min-width: 340px;
  }

  .time-pick {
    width: 260px;
    padding: 16px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    &__title {
      @include font-body-s-regular;
      color: $color-text-placeholder;
      margin-bottom: 6px;
    }

    &__tips {
      @include font-paragraph-xs-regular;
      color: $color-text-placeholder;
      margin-bottom: 6px;
    }

    &__content {
      margin-top: 9px;
      display: flex;
      flex-direction: row;
      align-items: center;

      span {
        padding: 0 6px;
      }

      ::v-deep .klk-poptip-popper {
        background-color: $color-bg-1;
      }
    }

    &__btns {
      display: flex;
      flex-direction: row;
      align-items: center;
      align-self: flex-end;

      &-cancel {
        @include font-body-s-semibold;
        width: 72px;
        height: 36px;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-right: 18px;
        cursor: pointer;
      }
    }
  }
}
</style>
