<template>
  <div v-if="showPublic" class="public">
    <h3 class="public-title">{{ $t('100491-public_transfer_recommend') }}</h3>
    <div class="public-wrap">
      <div class="public-left">
        <div class="public-search">
          <div class="public-search__title">{{ $t('100494-airport_search') }}</div>
          <div v-if="selectedWord" class="public-search__selected">
            <span>{{ selectedWord }}</span>
            <img src="https://res.klook.com/image/upload/web3.0/airport-transfer/close_c1qpk7.svg" width="20" height="20" alt="" @click="clearSelected">
          </div>
          <input
            v-else
            ref="searchInput"
            v-model="searchWord"
            v-click-outside="hideSearchPage"
            class="public-search__input"
            type="text"
            :placeholder="$t('101033')"
            v-galileo-click-tracker="{ spm: 'PublicAirportTransfer_SearchForm', autoTrackSpm: true }"
            data-spm-module="PublicAirportTransfer_SearchForm"
            data-spm-virtual-item="__virtual?typ=entry"
            @focus="openSearchPage"
            @input="handleInputChange()"
          >
          <div v-if="showSearchPage" class="public-search__list">
            <div v-if="!loadingAirport && !noAirportResult" class="public-search__list-title">{{ $t('101036-search_result') }}</div>
            <div v-if="loadingAirport" class="public-search__loading">
              <klk-loading></klk-loading>
            </div>
            <div v-else class="public-search__list-content">
              <ul v-if="airportList && airportList.length > 0">
                <li
                  v-for="item in airportList"
                  :key="item.iataCode"
                  v-galileo-click-tracker="{ spm: `AirportSearchResult_LIST?oid=${item.iataCode}&ext=${JSON.stringify({ transportresult: 'public transfer', searchword: searchWord})}`, autoTrackSpm: true }"
                  :data-spm-module="`AirportSearchResult_LIST?oid=${item.iataCode}&ext=${JSON.stringify({ transportresult: 'public transfer', searchword: searchWord})}`"
                  data-spm-virtual-item="__virtual?typ=entry"
                  @click="selectAirportItem(item)"
                >
                  {{ item.airportName }}({{ item.iataCode }})
                </li>
              </ul>
              <ul v-if="noAirportResult">
                <li>{{ $t('14794-no_result') }}</li>
              </ul>
            </div>
          </div>
        </div>
        <div v-if="popularAirportList && popularAirportList.length > 0" class="public-popular-airport">
          <div class="public-popular-airport__title">{{ $t('100495-popular_airport') }}</div>
          <ul class="public-popular-airport__list">
            <li
              v-for="(item, index) in popularAirportList"
              :key="index"
              :data-spm-module="`PopularAirport_LIST?oid=${item.iataCode}`"
              v-galileo-click-tracker="galileoClick1"
              data-spm-virtual-item="__virtual?typ=entry"
              @click="selectAirportItem(item)"
            >
              {{ item.airportName }}({{ item.iataCode }})
            </li>
          </ul>
        </div>
      </div>
      <div class="public-right">
        <div v-if="publicError || !publicList" class="public-error">
          <Klk-empty-panel
            v-if="!publicList && publicError"
            platform="desktop"
            :content="$t('101034-no_data')"
            icon-src="https://res.klook.com/image/upload/web3.0/airport-transfer/Empty_Slate_error_404_260_ojr61g.png"
            :primary-btn-text="$t('14305-retry')"
            @primary-btn-click="getPublicList"
          >
          </Klk-empty-panel>
          <Klk-empty-panel
            v-else
            platform="desktop"
            :content="$t('100498-no_public_list')"
            icon-src="https://res.klook.com/image/upload/web3.0/ill_spot_hero_transport_tickets_edrvrq.svg"
          >
          </Klk-empty-panel>
        </div>
        <template v-else>
          <div class="public-recommend">{{ publicTitle }}</div>
          <div ref="publicRight" class="public-list">
            <template v-if="loadingTtd">
              <div v-for="item in cardCount" :key="item" class="public-list__item" style="height: 154px">
                <div>
                  <klk-skeleton-block :width="65" :height="18" :style="{ 'margin-bottom': '4px' }"></klk-skeleton-block>
                  <klk-skeleton-block :width="461" :height="18" :style="{ 'margin-bottom': '4px' }"></klk-skeleton-block>
                  <klk-skeleton-block :width="461" :height="18" :style="{ 'margin-bottom': '4px' }"></klk-skeleton-block>
                  <klk-skeleton-block :width="112" :height="18" :style="{ 'margin-bottom': '4px' }"></klk-skeleton-block>
                  <klk-skeleton-block :width="112" :height="18" :style="{ 'margin-bottom': '0' }"></klk-skeleton-block>
                </div>
                <div>
                  <klk-skeleton-block :width="100" :height="100"></klk-skeleton-block>
                </div>
              </div>
            </template>
            <template v-else>
              <div 
                v-for="(item, index) in publicList" 
                :key="index" 
                ref="publicItem"
                v-galileo-click-tracker="{ spm: `PublicAirportTransferCard_LIST?oid=${item.activity_id}&len=${publicList.length}&idx=${index}`, autoTrackSpm: true }"
                class="public-list__item" 
                :data-spm-module="`PublicAirportTransferCard_LIST?oid=${item.activity_id}&len=${publicList.length}&idx=${index}`"
                data-spm-virtual-item="__virtual?typ=entry"
                @click="bookingTTD(item)"
              >
                <div class="public-list__item-left">
                  <div class="public-list__item-city">{{ item.city_name }}</div>
                  <div class="public-list__item-airport">{{ item.car_name }}</div>
                  <div class="public-list__item-data">
                    <div class="public-list__item-score">
                      <img src="https://res.klook.com/image/upload/web3.0/airport-transfer/icon_operational_star_fill_xs_thtqch.svg" width="16" height="16">
                      <em>{{ item.review_star_count }}</em>
                      <span v-if="item.review_count">({{ item.review_count }})</span>
                    </div>
                    <div class="public-list__item-count">{{ item.participate_format }}</div>
                  </div>
                  <div v-if="language" class="public-list__item-avail" v-html="availableTime(item.available_time)"></div>
                  <div class="public-list__item-price">
                    <span class="public-list__item-price--sale">{{ currencySymbol }}{{ item.sell_price_with_exchange_formatted }}</span>
                    <del class="public-list__item-price--orginal">{{ currencySymbol }}{{ item.origin_price_formatted }}</del>
                  </div>
                </div>
                <div class="public-list__item-right">
                  <div class="public-list__item-img" :style="`background-image: url(${item.car_image_url})`"></div>
                </div>
              </div>
            </template>
          </div>
          <div class="public-scroll__tip" :style="{'opacity': `${showScrollTip ? 1 : 0}`, 'transform': `translateY(${showScrollTip ? '0%' : '100%'})`}">{{ $t('car_rental_home_scroll_down') }}</div>
        </template>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Prop, Ref } from 'vue-property-decorator'
import debounce from 'lodash/debounce'
import dayjs from 'dayjs'
import KlkEmptyPanel from '@klook/empty-panel'
import { getStandardDateFormat } from '../common/datetime'
import Base from "@/common/base";
import { WebClick_11 } from '../../share/galileo/auto-click'

@Component({
  components: {
    KlkEmptyPanel
  }
})
export default class PublicTransfer extends Base {
  @Prop({ type: Object, default: () => null }) data!: any
  @Ref() searchInput!: any
  cardCount = 3
  showScrollTip: boolean = false
  contentObserver: any = null
  observerItem: any = null
  publicInfo: any = this.data
  publicInfoRecommend: any = this.data
  searchWord: string = ''
  selectedWord: string = ''
  loadingAirport: boolean = true
  loadingTtd: boolean = false
  showSearchPage: boolean = false
  airportList: any = null
  publicTitle: string = ''
  publicParams: any = null
  publicError: boolean = false
  language: string = ''
  showPublic: boolean = false

  get publicList() {
    return this.publicInfo?.public_transfer
  }

  get popularAirportList() {
    return this.publicInfo?.popular_airports
  }

  get currencySymbol() {
    return this.publicInfo?.currency_info_vo?.currency_symbol
  }

  get noAirportResult() {
    return this.searchWord && this.airportList && this.airportList.length === 0
  }

  created() {
    this.publicTitle = this.$t('100493-top_5_recommend')
    const query: any = this.$route.query
    const city_id = Number(query.city_id)
    if (!city_id) {
      this.publicInfo = this.data
      this.loadingTtd = false
      this.showPublic = true
    }
    this.publicInfoRecommend = this.data
  }

  mounted() {
    this.language = this.getKlkLanguage()
    this.createObserver()
  }

  handleInputChange() {
    this.handleValue()
  }

  // 防抖搜索
  handleValue = debounce(this.getAirportList, 500)

  getAirportListApi(params: any) {
    return this._axios
      .$get('/v1/transferairportapisrv/airport/autocomplete', { params, throwError: true })
      .then((res: Data.Res) => {
        return Promise.resolve(res)
      })
      .catch((error: any) => {
        return Promise.reject(error)
      })
  }

  getAirportList() {
    const keyword = this.searchWord
    if (keyword) {
      const params = {
        searchWord: keyword
      }
      this.showSearchPage = true
      this.loadingAirport = true
      this.getAirportListApi(params)
        .then((res: Data.Res) => {
          if (res.success && res.result) {
            this.loadingAirport = false
            this.airportList = res.result.airportSearchItemList
          }
        })
        .catch(() => {
          this.loadingAirport = false
        })
    } else {
      this.showSearchPage = false
      this.loadingAirport = false
    }
  }

  getPublicListApi(params: any) {
    return this._axios
      .$get('/v1/transferairportapisrv/transfer/search/public', { params })
      .then((res: Data.Res) => {
        return Promise.resolve(res)
      })
      .catch((error: any) => {
        return Promise.reject(error)
      })
  }

  openSearchPage() {
    if (this.searchWord) {
      this.showSearchPage = true
    }
  }

  hideSearchPage() {
    const t3 = setTimeout(() => {
      this.showSearchPage = false
      clearTimeout(t3)
    }, 0)
  }

  selectAirportItem(item: any) {
    this.selectedWord = ((item.airportName && item.iataCode) && `${item?.airportName}(${item?.iataCode})`) || ''
    this.publicTitle = item.airportName ? this.$t('100496', { airport_name: this.selectedWord }) : this.$t('100493-top_5_recommend')
    if (item.airportName) {
      this.getPublicList(item)
    }
    this.fillSearchForm(item)
  }

  availableTime(date: string) {
    let str = ''
    const language = this.language
    if (date) {
      date = date.split(' ')[0]
      if (date === dayjs().format('YYYY-MM-DD')) {
        str = this.$t('book.same.day')
      } else if (date === dayjs().add(1, 'day').format('YYYY-MM-DD')) {
        str = this.$t('book.tomorrow')
      } else {
        str = this.$t('book.time.text', getStandardDateFormat(date, this.$t.bind(this), language, 3))
      }
    } else {
      str = this.$t('book.time.soldout')
    }
    return str
  }

  clearSelected() {
    this.selectedWord = ''
    this.searchWord = ''
    this.publicTitle = this.$t('100493-top_5_recommend')
    this.publicInfo = this.publicInfoRecommend
    this.publicError = false
    this.$nextTick(() => {
      this.searchInput.focus()
    })
    this.selectAirportItem({})
  }

  async getPublicList(data: any) {
    if (data) {
      this.publicParams = {
        city_id: data.backupKlookCityId,
        iata_code: data.iataCode
      }
    }
    this.loadingTtd = true
    await this.getPublicListApi(this.publicParams).then((res: Data.Res) => {
      if (res.success && res.result) {
        this.publicInfo = res.result
        if (this.publicInfo.recommend) {
          this.publicTitle = this.$t('100498-no_public_list') + ' ' + this.$t('102249-no_public_tips')
        }
        if (!this.publicInfo.public_transfer) {
          this.publicInfo = this.publicInfoRecommend
          this.publicTitle = this.$t('100498-no_public_list') + ' ' + this.$t('102249-no_public_tips')
        }
        this.publicError = false
        this.loadingTtd = false
        this.showPublic = true
      }
    }).catch((err: any) => {
      this.publicError = true
      this.loadingTtd = false
      this.showPublic = true
      this.publicInfo = null
      if (err.code === '990001') {
        this.$inhouse.track('custom', 'body', {
          spm: 'AirportTransfer_Home_get_public_network_faild'
        })
      }
    })
  }

  updated() {
    if (this.contentObserver && this.observerItem) {
      this.contentObserver.unobserve(this.observerItem)
    }
    this.createObserver()
  }

  beforeDestory() {
    if (this.contentObserver) {
      this.observerItem && this.contentObserver.unobserve(this.observerItem)
      this.contentObserver.disconnect()
    }
    this.observerItem = null
    this.contentObserver = null
  }

  createObserver() {
    this.$nextTick(() => {
      const itemList: any = this.$refs.publicItem
      const area: any = this.$refs.publicRight
      if (Array.isArray(itemList) && area) {
        this.observerItem = itemList[itemList.length - 1]
      }
      try {
        this.contentObserver = new IntersectionObserver((entries: any) => {
          if (entries[0].intersectionRatio > 0) {
            this.showScrollTip = false
          } else {
            this.showScrollTip = true
          }
        }, {
          root: area
        })
        if (this.observerItem && this.contentObserver) {
          this.contentObserver.observe(this.observerItem)
        }
      } catch (err) {
        //
      }
    })
  }

  bookingTTD(item: any) {
    let langPath = ''
    const lang = this.getKlkLanguage()
    if (lang !== 'en') {
      langPath = `/${lang}`
    }
    const href = `${langPath}/activity/${item.activity_id}-${item.seo_url}/#krt=${item.klook_referral_id}`
    window.open(href, '_blank')
  }

  // 联动search form
  fillSearchForm(info: any) {
    this.$emit('eventHandle', {
      publicTransfer: {
        airportInfo: info
      }
    })
  }

  eventListener(info: any) {
    this.selectAirportItem(info)
  }

    get galileoClick1() {
        return { spm: WebClick_11, autoTrackSpm: true }
    }
}
</script>

<style lang="scss" scoped>
.public {
  width: 1160px;
  margin: 48px auto 0;

  &-title {
    @include font-heading-m;
  }

  &-wrap {
    margin-top: 24px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
  }

  &-left {
    background-color: $color-bg-1;
    border: 1px solid $color-border-dim;
    border-radius: $radius-xl;
    width: 285px;
    height: fit-content;
    padding: 18px 0;
  }

  &-search {
    position: relative;
    padding: 0 20px;

    &__title {
      @include font-body-m-semibold;
    }

    &__input {
      background-color: $color-bg-3;
      border-radius: $radius-l;
      border: 1px solid transparent;
      outline: none;
      caret-color: $color-brand-primary;
      padding: 0 10px;
      width: 100%;
      height: 42px;
      margin-top: 16px;

      &:focus {
        border: 1px solid $color-brand-primary;
      }
    }

    &__selected {
      background-color: $color-bg-3;
      border-radius: $radius-l;
      width: 100%;
      min-height: 42px;
      padding: 14px 12px;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      margin-top: 16px;

      span {
        @include font-body-s-bold;
        color: $color-text-primary;
      }

      img {
        flex: 20px 0 0;
        cursor: pointer;
      }
    }

    &__list {
      background-color: $color-bg-1;
      border-radius: $radius-xl;
      box-shadow: $shadow-normal-4;
      width: 376px;
      max-height: 412px;
      overflow-y: scroll;
      padding: 20px 0;
      position: absolute;
      left: 20px;
      top: 88px;
      z-index: 1;

      &-title {
        @include font-caption-m-semibold;
        padding: 0 20px;
        color: $color-text-secondary;
      }

      ul {
        margin-top: 6px;
      }

      li {
        @include font-body-s-regular;
        display: flex;
        align-items: center;
        padding: 0 20px;
        min-height: 44px;
        cursor: pointer;

        &:hover {
          background-color: $color-bg-3;
        }
      }
    }
  }

  &-popular-airport {
    margin-top: 20px;

    &__title {
      @include font-body-s-regular;
      color: $color-text-placeholder;
      padding: 0 20px;
    }

    &__list {
      li {
        @include font-body-s-regular;
        padding: 8px 20px;
        cursor: pointer;

        &:hover {
          background-color: $color-bg-3;
        }
      }
    }
  }

  &-right {
    width: 855px;
    max-height: 565px;
    position: relative;
  }

  &-recommend {
    @include font-body-m-semibold;
    border: 1px solid $color-border-normal;
    border-radius: $radius-l;
    background-color: $color-bg-1;
    width: 100%;
    min-height: 44px;
    padding: 10px 20px;
  }

  &-list {
    width: 100%;
    max-height: 521px;
    overflow-x: hidden;
    overflow-y: scroll;
    scrollbar-width: none; /* Firefox 64 */
    -ms-overflow-style: none; /* Internet Explorer 11 */
    &::-webkit-scrollbar { /** WebKit */
      display: none;
    }

    &__item {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      border: 1px solid $color-border-normal;
      border-radius: $radius-l;
      background-color: $color-bg-1;
      padding: 14px 24px;
      margin-top: 12px;
      cursor: pointer;

      &-city {
        @include font-caption-1;
        color: $color-brand-primary-dark;
      }

      &-airport {
        @include font-body-m-bold;
        color: $color-text-primary;
        margin-top: 4px;
      }

      &-data {
        display: flex;
        align-items: center;
        margin-top: 4px;
      }

      &-score {
        display: flex;
        align-items: center;

        em {
          @include font-body-s-semibold;
          color: $color-accent-9;
          font-style: normal;
          margin-left: 2px;
        }

        span {
          @include font-body-s-regular;
          color: $color-text-secondary;
          margin-left: 2px;
        }
      }

      &-count {
        color: $color-text-secondary;
        margin-left: 12px;
      }

      &-avail {
        @include font-body-s-regular;
        color: $color-success;
        margin-top: 4px;
      }

      &-price {
        display: flex;
        flex-direction: row;
        align-items: flex-end;
        margin-top: 4px;

        &--sale {
          @include font-body-m-bold;
          color: $color-text-primary;
        }

        &--orginal {
          @include font-body-s-regular;
          color: $color-text-secondary;
          margin-left: 2px;
        }
      }

      &-right {
        flex: 100px 0 0;
      }

      &-img {
        flex: 100px 0 0;
        height: 100px;
        border-radius: $radius-xl;
        background-repeat: no-repeat;
        background-size: cover;
        background-position: center center;
        overflow: hidden;
      }
    }
  }

  &-scroll__tip {
    @include font-body-s-regular;
    width: 100%;
    height: 48px;
    line-height: 48px;
    text-align: center;
    background: linear-gradient(180deg, rgba(255, 255, 255, 0.6) 0%, #FFFFFF 100%);
    color: #ff5722;
    position: absolute;
    left: 0;
    bottom: 0;
    transition: all 0.25s ease-in-out;
  }
}
</style>
