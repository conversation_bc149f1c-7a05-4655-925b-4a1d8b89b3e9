<template>
  <div v-if="dataInfo && dataInfo.work_flows && dataInfo.work_flows.length > 0" class="steps">
    <h3 class="steps__title">{{ dataInfo.title }}</h3>
    <div class="steps__content">
      <template v-for="(item, index) in dataInfo.work_flows">
        <div :key="`flow-${index}`" class="steps__item" @click="eventHandle">
          <img :src="item.icon" width="80" height="80">
          <p>{{ item.title }}</p>
        </div>
        <div v-if="index < data.work_flows.length - 1" :key="`next-${index}`" class="steps__next">
          <svg-icon name="desktop-private-transfer#chevron_right" size="24"></svg-icon>
        </div>
      </template>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Prop } from 'vue-property-decorator'
import Base from '@/common/base'

@Component
export default class HomeWorkFlows extends Base {
  @Prop({ type: Object, default: () => null }) data!: any

  dataInfo: any = this.data
}
</script>

<style lang="scss" scoped>
.steps {
  width: 1160px;
  margin: 64px auto 0;

  &__title {
    @include font-heading-m;
  }

  &__content {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    padding-top: 52px;
  }

  &__item {
    display: flex;
    flex-direction: column;
    align-items: center;

    p {
      margin-top: 20px;
    }
  }

  &__next {
    flex: 24px 0 0;
    margin-top: 28px;
  }
}
</style>
