<template>
  <!-- 接机方式切换tab -->
  <div
    class="search-form__pick-type"
  >
    <div
      :data-spm-module="`SwitchPickupMethod?trg=manual&ext=${encodeURIComponent(JSON.stringify({
        tab: 'FlightNo'
      }))}`"
      v-galileo-click-tracker="galileoClick1"
      data-spm-virtual-item="__virtual"
      :class="['search-form__pick-type-option', isByFlight ? 'search-form__pick-type-option-active' : 'search-form__pick-type-option-inactive']"
      :style="optionWidth && `width: ${optionWidth}`"
      @click="$emit('change-pick-type', 'byFlight')"
    >
      <span class="search-form__pick-type-text">{{ $t('203776-flight_time') }}</span>
    </div>
    <div
      :data-spm-module="`SwitchPickupMethod?trg=manual&ext=${encodeURIComponent(JSON.stringify({
        tab: 'FixedTime'
      }))}`"
      v-galileo-click-tracker="galileoClick2"
      data-spm-virtual-item="__virtual"
      :class="['search-form__pick-type-option', !isByFlight ? 'search-form__pick-type-option-active' : 'search-form__pick-type-option-inactive']"
      :style="optionWidth && `width: ${optionWidth}`"
      @click="$emit('change-pick-type', 'byFixTime')"
    >
      <span class="search-form__pick-type-text">{{ $t('203777-fix_time') }}</span>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator'
import { WebClick_41, WebClick_42 } from '../../../share/galileo/auto-click'

@Component
export default class PickTypeTab extends Vue {
  @Prop({ type: Boolean, default: false }) isByFlight!: boolean
  @Prop({ type: String, default: '' }) curPickType!: string
  @Prop({ type: String, default: '' }) optionWidth!: string
  @Prop({ type: String, default: '262px' }) wrapperWidth!: string

    get galileoClick1() {
        return { spm: WebClick_41, autoTrackSpm: true }
    }

    get galileoClick2() {
        return { spm: WebClick_42, autoTrackSpm: true }
    }
}
</script>

<style lang="scss" scoped>
@mixin ellipsis() {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  word-wrap: break-word;
}
.search-form {
  &__pick-type {
    display: flex;

    &-option {
      @include font-body-xs-bold;
      @include ellipsis;
      padding: 4px 12px;
      color: $color-text-secondary;
      cursor: pointer;
      text-align: center;
    }

    &-option-active {
      color: $color-brand-primary;
      background-color: $color-bg-1;
      border-radius: $radius-m;
      box-shadow: $shadow-normal-1;

      .search-form__pick-type-text {
        @include font-body-xs-bold;
        color: $color-brand-primary;
      }
    }

    &-text {
      margin-right: 4px;
    }
  }
}
</style>
