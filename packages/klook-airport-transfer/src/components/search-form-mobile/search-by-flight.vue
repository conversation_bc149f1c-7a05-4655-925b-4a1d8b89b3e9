<template>
  <div>
    <klk-modal
      :open.sync="visible"
      fullscreen
      :padding="0"
      modal-class="search-by-flight"
      transition="slide-bottom"
      :show-default-footer="false"
      :delay-time="0"
    >
      <div slot="header" class="search-by-flight__header">
        <div @click="closeModal">
          <IconBack class="search-by-flight__header-icon" theme="outline" size="24" fill="#212121" />
        </div>
        <div class="search-by-flight__header-title">{{ $t('203800-select_by_flight') }}</div>
      </div>
      <div v-if="visible" data-spm-page="SearchFlight">
        <klk-tabs :value="formType" underline class="search-by-flight__tabs" @change="changeTab">
          <klk-tab-pane
            :data-spm-module="`FlightTab?trg=manual&ext=${encodeURIComponent(JSON.stringify({
              tab: 'ArrivalFlight'
            }))}`"
            data-spm-virtual-item="__virtual"
            :label="$t('203785-arrival_flight_number')"
            name="formFlightNo"
            width="50%"
          >
          </klk-tab-pane>
          <klk-tab-pane
            :data-spm-module="`FlightTab?trg=manual&ext=${encodeURIComponent(JSON.stringify({
              tab: 'FlighOD'
            }))}`"
            data-spm-virtual-item="__virtual"
            :label="$t('208178-Departure & arrival airports')"
            name="formOD"
            width="50%"
          >
          </klk-tab-pane>
        </klk-tabs>
        <div class="search-by-flight__body">
          <klk-alert type="info" class="search-by-flight__tips">
            {{ isByFlightNo ? $t('203782-tips') : $t('210319') }}
          </klk-alert>
          <div class="search-by-flight__form">
            <!-- 航班号 -->
            <div v-show="isByFlightNo">
              <span class="search-by-flight__label">
                {{ $t('13996') }}
              </span>
              <klk-input
                v-model="formData.flightNo"
                data-spm-module="InputArrivalFlight"
                v-galileo-click-tracker="galileoClick1"
                data-spm-virtual-item="__virtual"
                name="flightNo"
                clearable
                :placeholder="$t('203778-flight_no')"
                :class="['search-by-flight__form-item']"
                @blur="updateFormDataToState('flightNo')"
              />
            </div>
            <div v-show="isByOD" class="search-by-flight__od_container">
              <span class="search-by-flight__label">
                {{ $t('209030') }}
              </span>
              <klk-input
                v-model="formData.departureIata"
                data-spm-module="InputArrivalFlight"
                data-spm-virtual-item="__virtual"
                clearable
                name="departureIata"
                append-icon="icon_navigation_chevron_down_xxs"
                :placeholder="$t('208179-Please input the Departure airport')"
                :class="['search-by-flight__form-item']"
                @focus="handleCodeFocus('departureIata', 'isDepartureIata', 'isArrivalIata')"
              />
              <div class="icon_switch">
                <iconSwitch @click.native="switchOD"></iconSwitch>
              </div>
              <klk-input
                v-model="formData.arrivalIata"
                data-spm-module="InputArrivalFlight"
                data-spm-virtual-item="__virtual"
                clearable
                name="arrivalIata"
                append-icon="icon_navigation_chevron_down_xxs"
                :placeholder="$t('208180-Please input the Arrival airport')"
                :class="['search-by-flight__form-item']"
                @focus="handleCodeFocus('arrivalIata', 'isArrivalIata', 'isDepartureIata')"
              />
              <klk-modal
                :open.sync="showSearchByFlightModal"
                fullscreen
                :padding="0"
                class="search-form__airport-modal"
                transition="slide-bottom"
                :show-default-footer="false"
                :delay-time="0"
              >
                <search-page
                  :hot-airports="hotAirports"
                  :search-type="searchType"
                  :search-list="searchList"
                  :loading="searchLoading"
                  :search-word="searchWord"
                  :is-pick-tab="isPickTab"
                  :arrival-or-departure="getArrivalOrDeparture()"
                  :is-from-public="false"
                  :search-history="searchHistory"
                  @hideSearch="hideSearch"
                  @getSearchList="getSearchList"
                  @selectSearchItem="selectSearchItem"
                  @getSearchHistory="getSearchHistory"
                  @clearHistory="clearHistory"
                ></search-page>
              </klk-modal>
            </div>
            <!-- 日期选择 -->
            <div
              class="search-by-flight__form-item-time-wrapper"
            >
              <span class="search-by-flight__label">
                {{ $t('113100') }}
              </span>
              <div
                :data-spm-module="`InputDepartureTime?ext=${encodeURIComponent(JSON.stringify({
                  tab: 'ArrivalFlight'
                }))}`"
                v-galileo-click-tracker="galileoClick2"
                data-spm-virtual-item="__virtual"
                :class="['search-by-flight__form-item', 'search-by-flight__form-item-row', 'select', showDateTimePicker && 'active']"
                @click="showDateTimePicker = true"
              >
                <div v-if="!formData.pickupDate" class="search-by-flight__form-item-placeholder">
                  <span>{{ $t('203801-flight_time') }}</span>
                </div>
                <div v-else class="search-by-flight__form-item-text">
                  <span v-if="formData.pickupDate">{{ dateFormatter(formData.pickupDate) }} </span>
                </div>
                <IconChevronDown class="search-by-flight__form-icon" theme="outline" size="20" fill="#212121" />
              </div>
              <!-- 日期选择 -->
              <klk-bottom-sheet
                class="search-by-flight__date"
                :visible.sync="showDateTimePicker"
                :transfer="true"
                :can-pull-close="false"
              >
                <date-picker
                  :visible="showDateTimePicker"
                  :hide-time-picker="true"
                  :date="curPickDate"
                  :show-pick-up-tips="true"
                  :is-pick-tab="false"
                  @confirm-date-and-time="selectDateTime"
                ></date-picker>
              </klk-bottom-sheet>
            </div>
          </div>
          <div slot="footer" class="search-by-flight__footer">
            <klk-button
              :data-spm-module="`Search?ext=${encodeURIComponent(JSON.stringify({
                tab: isByFlightNo ? 'ArrivalFlight' : 'FlighOD'
              }))}`"
              v-galileo-click-tracker="galileoClick3"
              data-spm-virtual-item="__virtual"
              :disabled="isFlightSearchDisabled()"
              :loading="finallyLoading"
              @click="searchFlight"
            >
              {{ $t('car_rental_home_search') }}
            </klk-button>
          </div>
        </div>
      </div>
      <!-- 机票卡片选择 -->
      <klk-bottom-sheet
        class="search-by-flight__flight-card"
        :visible.sync="showFlightModal"
        :transfer="true"
        :can-pull-close="false"
      >
        <div slot="header" class="search-by-flight__flight-card-header">
          <klk-icon type="icon_navigation_close_m" :size="24" @click="closeFlightModal"></klk-icon>
          <div class="search-by-flight__flight-card-header-right">
            <div class="search-by-flight__flight-card-title">{{ $t('203800-select_by_flight') }}</div>
            <div>
              <span>{{ $t("12983") }}</span>
              <span>{{ defaultArrivalDate }}</span>
            </div>
          </div>
        </div>
        <div
          v-if="showFlightModal"
          data-spm-page="SelectFlight"
        >
          <flight-card
            :is-by-od="isByOD"
            :flight-info-list="flightList"
            @update-flight-info="updateFlightInfo"
          ></flight-card>
        </div>
      </klk-bottom-sheet>
      <klk-modal
        modal-class="search-by-flight__modal"
        :open.sync="isNoODSearchResult"
        :width="600"
        :show-default-footer="false"
        button-align="block"
        closable
        @close="isNoODSearchResult = false"
      >
        <div v-if="isNoODSearchResult" class="search-by-flight__od-no-result">
          <div class="search-by-flight__od-no-result-content">
            <airplanpicAirpla></airplanpicAirpla>
            <p>{{ $t('209031') }}</p>
          </div>
          <div class="search-by-flight__od-no-result-btn">
            <klk-button @click="handleSearchByFlight">{{ $t('208181') }}</klk-button>
            <klk-button type="outlined" @click="handleCancel">{{ $t('208182') }}</klk-button>
          </div>
        </div>
      </klk-modal>
    </klk-modal>
  </div>
</template>

<script lang="ts">
import { Component } from 'vue-property-decorator'
import { IconBack } from '@klook/klook-icons'
import dayjs from 'dayjs'
import DatePicker from './date-picker.vue'
import SearchPage from './search-page.vue'
import SearchByFlightCommon from '../../common/search-form/search-by-flight-common'
import airplanpicAirpla from '../svg/airplanpicAirpla.vue'
import iconSwitch from '../svg/iconSwitch.vue'
import { WebClick_31, WebClick_32, WebClick_33 } from '../../../share/galileo/auto-click'

const searchApi = {
  hotAirport: '/v1/transferairportapisrv/airport/hot',
}

@Component({
  components: {
    IconBack,
    DatePicker,
    SearchPage,
    airplanpicAirpla,
    iconSwitch
  }
})
export default class SearchByFlight extends SearchByFlightCommon {
  hotAirports: any = {}
  // 默认定位时间T+3 09:00
  get curPickDate() {
    return this.formData.pickupDate || dayjs().add(3, 'day').toDate()
  }

  async mounted() {
    try {
      await this._axios.$get(searchApi.hotAirport, {})
        .then((res: any) => {
          if (res.success && res.result) {
            this.hotAirports = res.result
          }
        })
    } catch (error) {}
  }

  // 关闭搜索页
  hideSearch() {
    this.searchList = {}
    this.showSearchByFlightModal = false
  }

    get galileoClick1() {
        return { spm: WebClick_31, autoTrackSpm: true }
    }

    get galileoClick2() {
        return { spm: WebClick_32, autoTrackSpm: true }
    }

    get galileoClick3() {
        return { spm: WebClick_33, autoTrackSpm: true }
    }
}
</script>

<style lang="scss" scoped>
.search-by-flight {
  &__od_container {
    position: relative;
    .icon_switch {
      width: 36px;
      height: 36px;
      position: absolute;
      top: 107px;
      left: 50%;
      transform: translate(-50%, -50%);
      z-index: 2;
      cursor: pointer;
    }
  }
  &__od-no-result{
    &-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin: 0 0 24px;
      p {
        text-align: center;
      }
    }
    &-btn {
      display: flex;
      flex-direction: column;
      gap: 12px;
    }
  }
  &__label {
    display: inline-block;
    margin-top: 16px;
  }
  &__header {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 10px 0;
    &-icon {
      width: 24px;
      height: 24px;
    }

    &-title {
      @include font-paragraph-m-bold;
      flex: 1;
      width: calc(100% - 24px);
      margin-left: 8px;
      text-align: center;
    }
  }

  &__tips {
    padding: 8px 12px;
    margin-bottom: 8px;
    ::v-deep .klk-alert-content {
      @include font-paragraph-xs-regular;
      color: $color-text-link;
    }
  }

  &__flight-card {
    &-header {
      display: flex;
      align-items: center;
    }
    &-header-right {
      text-align: center;
      flex: 1;
    }
    &-title {
      @include font-paragraph-m-bold;
      color: $color-text-primary;
    }
  }

  &__body {
    padding: 0 20px 20px;
  }

  &__form {
    &-item {
      margin-top: 16px;

      &.select {
        border:  1px solid $color-border-normal;
        border-radius: $radius-l;
        padding: 0 12px;
        min-height: 44px;
      }

      &-row {
        display: flex;
        justify-content: space-between;
        align-items: center;

        &.active {
          border: 1px solid $color-border-active;
        }
      }

      &-placeholder {
        @include font-paragraph-m-regular;
        color: $color-text-placeholder;
      }
    }
  }

  &__error {
    &-item {
      border: 1px solid $color-brand-primary !important;
    }

    &-item-input {
      ::v-deep .klk-input-inner {
        border: 1px solid $color-brand-primary !important;
      }
    }

    &-text {
      @include font-paragraph-s-regular;
      color: $color-brand-primary;
    }
  }

  &__date {
    ::v-deep .klk-bottom-sheet-body {
      padding: 0 !important;
    }
  }

  &__footer {
    margin-top: 32px;
    width: 100%;
    .klk-button {
      width: 100%;
    }
  }
}
</style>
<style>
.search-by-flight {
  margin-top: 0;
  padding-top: 0;
}
.search-by-flight__modal{
  top: -20px;
  padding: 0 0 24px;
}
</style>
