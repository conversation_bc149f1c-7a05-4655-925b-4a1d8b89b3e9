<template>
  <div class="search-form">
    <template>
      <div class="search-form__tabs">
        <klk-tabs :value="isPickTab ? 'pick' : 'drop'" @change="toggleTab($event === 'pick')">
          <klk-tab-pane
            :data-spm-module="`BusinessType?trg=manual&ext=${JSON.stringify({type: 'AirportPickup'})}`"
            v-galileo-click-tracker="galileoClick1"
            data-spm-virtual-item="__virtual?typ=entry"
            :label="$t('13491-airport_transfer_home_tab_pick')"
            name="pick"
          >
          </klk-tab-pane>
          <klk-tab-pane
            :data-spm-module="`BusinessType?trg=manual&ext=${JSON.stringify({type: 'AirportDropoff'})}`"
            v-galileo-click-tracker="galileoClick2"
            data-spm-virtual-item="__virtual?typ=entry"
            :label="$t('15032-airport_transfer_home_form_from')"
            name="drop"
          >
          </klk-tab-pane>
        </klk-tabs>
      </div>
      <!-- 接机方式切换tab -->
      <div v-if="showPickType && isPickTab" class="search-form__pick-type">
        <pick-type-tab
          class="search-form__pick-type-wrapper"
          :is-by-flight="isByFlight"
          :cur-pick-type="curPickType"
          option-width="50%"
          wrapper-width="100%"
          @change-pick-type="changePickType"
        />
      </div>
    </template>
    <div :class="['search-form__wraper', showPickType && isPickTab && 'search-form__wrapper_by_flight']">
      <template v-if="isPickTab">
        <!-- 航班号搜索 -->
        <div
          v-if="isByFlight"
          class="search-form__place search-form__place-flight from search-form__place-hide-radius"
          data-spm-module="FlightNoInput"
          v-galileo-click-tracker="galileoClick3"
          data-spm-virtual-item="__virtual?typ=entry"
          :class="{'error': formValid.flightNo.error}"
          @click="showSearchByFlightModal = true"
        >
          <div class="search-form__place-fill">
            <span v-if="fromPlaceByFlight && pickUpTimeByflight" class="search-form__place-tip">
              {{ $t('15477-label') }}
            </span>
            <span v-else class="search-form__place-flight-placeholder">
              {{ $t('203778-placeholder') }}
            </span>
            <p class="search-form__place-name search-form__place-flight-name">{{ fromPlaceByFlight }}</p>
            <span class="search-form__place-flight-time">{{ pickUpTimeByflight }}</span>
          </div>
        </div>
        <!-- 航班号搜索 -->
        <div
          v-else
          v-galileo-click-tracker="{ spm: `PickupPlaceInput?trg=manual&ext=${JSON.stringify({type: 'AirportPickup'})}`, autoTrackSpm: true }"
          :class="['search-form__place', 'from', showPickType && 'search-form__place-hide-radius', formValid.from.error && 'error']"
          :data-spm-module="`PickupPlaceInput?trg=manual&ext=${JSON.stringify({type: 'AirportPickup'})}`"
          data-spm-virtual-item="__virtual?typ=entry"
          @click="openSearch(true)"
        >
          <div v-if="formData.from" class="search-form__place-fill">
            <span class="search-form__place-tip">{{ $t('15477-from') }}</span>
            <p class="search-form__place-name">{{ formData.from }}</p>
          </div>
          <div v-else class="search-form__place-null">{{ $t('15477-from') }}{{ $t('15845-airport_transfer_home_form_airport') }}</div>
        </div>
        <div
          class="search-form__place to"
          :class="{'error': formValid.to.error}"
          :data-spm-module="`DropoffPlaceInput?trg=manual&ext=${JSON.stringify({type: 'AirportPickup'})}`"
          v-galileo-click-tracker="galileoClick4"
          data-spm-virtual-item="__virtual?typ=entry"
          @click="openSearch(false)"
        >
          <div v-if="formData.to" class="search-form__place-fill">
            <span class="search-form__place-tip">{{ $t('14722-to') }}</span>
            <p class="search-form__place-name">{{ formData.to }}</p>
          </div>
          <div v-else class="search-form__place-null">{{ $t('14722-to') }}{{ $t('15212-airport_transfer_home_form_address') }}</div>
        </div>
      </template>
      <template v-else>
        <div
          v-galileo-click-tracker="{ spm: `PickupPlaceInput?trg=manual&ext=${JSON.stringify({type: 'AirportDropoff'})}`, autoTrackSpm: true }"
          class="search-form__place from"
          :class="{'error': formValid.from.error}"
          :data-spm-module="`PickupPlaceInput?trg=manual&ext=${JSON.stringify({type: 'AirportDropoff'})}`"
          data-spm-virtual-item="__virtual?typ=entry"
          @click="openSearch(false)"
        >
          <div v-if="formData.from" class="search-form__place-fill">
            <span class="search-form__place-tip">{{ $t('15477-from') }}</span>
            <p class="search-form__place-name">{{ formData.from }}</p>
          </div>
          <div v-else class="search-form__place-null">{{ $t('15477-from') }}{{ $t('15212-airport_transfer_home_form_address') }}</div>
        </div>
        <div
          v-galileo-click-tracker="{ spm: `DropoffPlaceInput?trg=manual&ext=${JSON.stringify({type: 'AirportDropoff'})}`, autoTrackSpm: true }"
          class="search-form__place to"
          :class="{'error': formValid.to.error}"
          :data-spm-module="`DropoffPlaceInput?trg=manual&ext=${JSON.stringify({type: 'AirportDropoff'})}`"
          data-spm-virtual-item="__virtual?typ=entry"
          @click="openSearch(true)"
        >
          <div v-if="formData.to" class="search-form__place-fill">
            <span class="search-form__place-tip">{{ $t('14722-to') }}</span>
            <p class="search-form__place-name">{{ formData.to }}</p>
          </div>
          <div v-else class="search-form__place-null">{{ $t('14722-to') }}{{ $t('15845-airport_transfer_home_form_airport') }}</div>
        </div>
      </template>
      <div v-if="!isByFlight || isDropTab" class="search-form__date-pas" :class="{'error': formValid.date.error || formValid.time.error}">
        <div
          class="search-form__date-time"
          :data-spm-module="`PickupDateInput?trg=manual&ext=${JSON.stringify({type: isPickTab ? 'AirportDropoff' : 'AirportDropoff'})}`"
          v-galileo-click-tracker="galileoClick5"
          data-spm-virtual-item="__virtual?typ=entry"
          @click="openDatePicker"
        >
          <div v-if="formData.date && formData.time" class="search-form__date-time--fill">
            <span class="search-form__place-tip">{{ $t('14879-date') }}</span>
            <p class="search-form__place-name">{{ formData.date }},{{ formData.time }}</p>
          </div>
          <div v-else class="search-form__place-null">{{ $t('14879-date') }}</div>
        </div>
      </div>
      <div class="search-form__pas" :class="{'error': formValid.passenger.error}">
        <div
          data-spm-module="PessangerNumInput?trg=manual"
          v-galileo-click-tracker="galileoClick6"
          data-spm-virtual-item="__virtual?typ=entry"
        >
          <span>{{ $t('203780-passengers') }}</span>
        </div>
        <div class="search-form__passenger-counter">
          <klk-counter
            data-spm-module="PassengerSelect?trg=manual"
            v-galileo-click-tracker="galileoClick7"
            data-spm-virtual-item="__virtual"
            :value="pas"
            :min="1"
            :max="50"
            size="small"
            @change="savePas"
            @disabled-click="limitMax"
          ></klk-counter>
        </div>
      </div>
      <div class="search-form__btn">
        <klk-button
          block
          :data-spm-module="`SearchBtn?trg=manual&ext=${JSON.stringify({type: isPickTab ? 'AirportPickup' : 'AirportDropoff', serviceType: isByFlight ? 'FlightNo' : 'FixTime'})}`"
          v-galileo-click-tracker="galileoClick8"
          data-spm-virtual-item="__virtual?typ=entry"
          @click="search"
        >
          {{ $t('car_rental_home_search') }}
        </klk-button>
      </div>
      <!-- 日期时间选择 -->
      <klk-bottom-sheet
        :visible.sync="showDataPicker"
        :can-pull-close="false"
        :transfer="true"
      >
        <date-picker
          ref="dateTimePicker"
          :visible="showDataPicker"
          :date="selectCurrentDate"
          :time="timeData"
          :is-pick-tab="isPickTab"
          :show-pick-up-tips="isPickTab && currentPage === 'home'"
          @hideDatePicker="hideDatePicker"
          @confirm-date-and-time="confirmDateAndTime"
        ></date-picker>
      </klk-bottom-sheet>
      <!-- 机场搜索页 -->
      <klk-modal
        :open.sync="showAirportPage"
        fullscreen
        :padding="0"
        class="search-form__airport-modal"
        transition="slide-bottom"
        :show-default-footer="false"
        :delay-time="0"
      >
        <search-page
          :hot-airports="hotAirports"
          :search-type="searchType"
          :search-list="searchList"
          :loading="searchLoading"
          :search-word="searchWord"
          :is-pick-tab="isPickTab"
          :is-from-public="isFromPublic"
          :search-history="searchHistory"
          @hideSearch="hideSearch"
          @getSearchList="getSearchList"
          @selectSearchItem="selectSearchItem"
          @getSearchHistory="getSearchHistory"
          @clearHistory="clearHistory"
        ></search-page>
      </klk-modal>
      <!-- 航班号搜索页 -->
      <search-by-flight
        :visible.sync="showSearchByFlightModal"
        :flight-info="flightInfo"
        :current-page="currentPage"
        @update-flight-info="updateFlightInfo"
      />
    </div>
    <client-only>
      <klk-modal
        modal-class="search-form__tips-modal"
        v-if="tips"
        :title="tips.title"
        title-align="center"
        button-align="center"
        :open.sync="tipsModalVisible"
        :ok-label="tips.btn"
        :show-cancel-button="false"
        @on-confirm="closeTipsModal"
        @close="closeTipsModal"
      >
        <div class="klk-modal-content">
          {{ tips.content }}
        </div>
      </klk-modal>
    </client-only>
  </div>
</template>

<script lang="ts">
import { Component, Prop, Ref } from 'vue-property-decorator'
import dayjs from 'dayjs'
import SearchFormBase from "../../common/search-form/search-form-base"

import SearchPage from './search-page.vue'
import DatePicker from './date-picker.vue'
import SearchByFlight from './search-by-flight.vue'
import PickTypeTab from './pick-type-tab.vue'
import { WebClick_21, WebClick_22, WebClick_23, WebClick_24, WebClick_25, WebClick_26, WebClick_27, WebClick_28 } from '../../../share/galileo/auto-click'

interface urlParams extends Object {
  flightDirection: number,
  from: string,
  to: string,
  address: string,
  lat: string,
  long: string,
  poiId: string,
  poi_lat: string,
  poi_long: string,
  date: any,
  time: string,
  pas: number,
  code: string,
  place: string,
  ac: string,
  kcid: string
}

interface formData extends Object {
  from: string,
  to: string,
  date: string,
  time: string,
  passenger: number
}

interface searchAirportObject extends Object {
  searchWord: string
}

interface searchPoiObject extends Object {
  input: string,
  latitude: string,
  longitude: string,
  airportCode: string,
  sessiontoken: string
}

enum Type {
  PICK = 'pick',
  DROP = 'drop'
}

enum searchType {
  AIRPORT = 'airport',
  POI = 'poi'
}

const searchApi = {
  hotAirport: '/v1/transferairportapisrv/airport/hot',
  searchPoi: '/v1/transferairportapisrv/poi/autocomplete_v1',
  searchPoiDetail: '/v1/transferairportapisrv/poi/detail_v1',
  validatePoi: '/v1/transferairportapisrv/poi/is_blocked' // 校验poi是否合法
}

@Component({
  components: {
    SearchPage,
    DatePicker,
    SearchByFlight,
    PickTypeTab
  }
})
export default class SearchForm extends SearchFormBase {
  @Prop({ type: Boolean, default: false }) isAggregateTabs!: boolean
  @Ref() dateTimePicker!: any

  isAirport: boolean = true
  showDataPicker: boolean = false
  showTimePicker: boolean = false
  showAirportPage: boolean = false
  searchLoading: boolean = false
  isFormValidate: boolean = false
  isMaxPas: boolean = false
  // 判断是否是从首页公共交通进入
  isFromPublic: boolean = false

  // 特殊情况下poi/机场item点击后的弹窗提示
  tipsModalVisible: boolean = false
  tips: any = {
    content: null,
    title: null,
    btn: null,
    action: 0 // 1 - 清空poi，2 - 清空机场，0 - 不做操作
  }

  // 默认为空，所以只能用any类型，因为后面会用Date类型进行填充
  timeData: any = null
  hotAirports: any = {}
  searchList: any = {}
  selectCurrentDate: Date = dayjs().add(3, 'day').toDate()
  formErrorTip: string = ''

  // 搜索的是机场还是POI
  get searchType() {
    return this.isAirport ? searchType.AIRPORT : searchType.POI
  }

  get pas() {
    return this[this.type].pas
  }

  created() {
    this.formValid.from.content = this.$t('6811-airport_transfer_home_error_pick_loc')
    this.formValid.to.content = this.$t('13119-airport_transfer_home_error_drop_loc')
    this.formValid.date.content = this.$t('13623-airport_transfer_home_error_date')
    this.formValid.time.content = this.$t('14952-airport_transfer_home_error_time')
    this.formValid.passenger.content = this.$t('13053-airport_transfer_home_error_passenger')
    this.formValid.flightNo.content = this.$t('6811-airport_transfer_home_error_pick_loc')
    // is_from_public代表从首页公共交通进入搜索结果页
    this.isFromPublic = !!this.$route.query.is_from_public
  }

  async mounted() {
    try {
      await this.getSearchHotAirport()
        .then((res: any) => {
          if (res.success && res.result) {
            this.hotAirports = res.result
          }
        })
    } catch (error) {}
    // 兼容ota transform动画，将时间选择组件append到body上
    // document.body.append(this.dateTimePicker.$el)
    this.initTime()
  }

  // 根据当前时间初始化时间选择框
  initTime() {
    /*
      判断是否已有选中时间
      1. 如有，用默认选中已有时间
      2. 如没有，默认时间为当前日期+3，时间为09:00
    */
    const time = this[this.type].time
    const date = this[this.type].date

    if (!date) {
      this.timeData = dayjs().add(3, 'day').toDate()
      this[Type.PICK].date = this.timeData
      this[Type.DROP].date = this.timeData
      this.formData.date = this.formatDate(this.timeData)
    }

    if (!time) {
      const hour = '09'
      this[Type.PICK].time = `${hour}:00`
      this[Type.DROP].time = `${hour}:00`
      this.formData.time = this[this.type].time
    }
  }


  //接口调用
  getSearchHotAirport() {
    return this._axios
      .$get(searchApi.hotAirport)
      .then((res: Data.Res) => {
        return Promise.resolve(res)
      })
      .catch((error: any) => {
        return Promise.reject(error)
      })
  }
  getSearchPoi(params: any) {
    return this._axios
      .$get(searchApi.searchPoi, { params })
      .then((res: Data.Res) => {
        return Promise.resolve(res)
      })
      .catch((error: any) => {
        return Promise.reject(error)
      })
  }
  getSearchPoiDetail(params: any) {
    return this._axios.$get(searchApi.searchPoiDetail, { params })
      .then((res: Data.Res) => {
        return Promise.resolve(res)
      })
      .catch((error: any) => {
        return Promise.reject(error)
      })
  }
  validatePoi(payload: any) {
    return this._axios.$post(searchApi.validatePoi, payload)
      .then((res: Data.Res) => {
        return Promise.resolve(res)
      })
      .catch((error: any) => {
        return Promise.reject(error)
      })
  }

  // 切换接送机TAB
  toggleTab(bool: boolean) {
    this.isPickTab = bool
    this.updateFormData()
    this.deleteFormError()
    // 转为异步操作，优化INP
    setTimeout(() => {
      // GA埋点
      this.$sendGTMCustomEvent(`Airport Transfer Vertical Screen|Select ${bool ? 'Pick-up' : 'Drop-off'}`)
    }, 0)
  }

  // 打开搜索页
  openSearch(isAirport: boolean) {
    const from = this.type === Type.PICK ? 'from' : 'to'
    const to = this.type === Type.PICK ? 'to' : 'from'
    this.searchWord = isAirport ? this[this.type][from] : this[this.type][to]
    this.isAirport = isAirport
    // 将从公共交通搜索框进入设为false
    this.isFromPublic = false
    this.showAirportPage = true
  }

  // 关键字搜索
  getSearchList(keyword: string) {
    if (keyword) {
      let params: any = null
      this.searchLoading = true
      if (this.searchType === searchType.AIRPORT) {
        params = {
          searchWord: keyword
        }
        this.getSearchAirportList(params)
      } else {
        params = {
          input: keyword,
          latitude: this[this.type].lat,
          longitude: this[this.type].long,
          airportCode: this[this.type].code && this[this.type].code.toUpperCase(),
          sessiontoken: this.transfer_sessionToken
        }
        this.getSearchPoiList(params)
      }
    } else {
      this.searchList = {}
    }
  }

  // 搜索机场
  getSearchAirportList(params: searchAirportObject) {
    this.getSearchAirport(params)
      .then((res: any) => {
        if (res.success && res.result) {
          this.searchList = res.result
        }
        this.searchLoading = false
      })
      .catch(() => {
        this.searchLoading = false
      })
  }

  // 搜索POI
  getSearchPoiList(params: searchPoiObject) {
    this.getSearchPoi(params)
      .then((res: any) => {
        if (res.success && res.result) {
          this.searchList = res.result
          if (!res.result?.poiList?.length) {
            // 上报inhouse
            this.$inhouse.track('custom', 'body', {
              spm: 'Sug_noResult',
              ext: encodeURIComponent(JSON.stringify({
                type: this.type,
                iataCode: this[this.type].code,
                query: params.input
              }))
            })
          }
        }
        this.searchLoading = false
      })
      .catch(() => {
        this.searchLoading = false
      })
  }

  // 选择搜索项
  selectSearchItem(item: any) {
    if (this.searchType === searchType.AIRPORT) {
      // 判断是否需要弹窗交互 2024.12.10 poi的提示逻辑需要单独请求接口处理，所以这里只判断机场类型的提示
      if (item.tips) {
        if (item.tips.type === 'toast') {
          this.$toast(item.tips.content)
        } else if (item.tips.type === 'confirm-modal') {
          this.tips = item.tips
          this.tipsModalVisible = true
        }
        return
      }
      this.getAirportItem(item)
      // 同步更新公共交通模块的内容
      this.getPublicList(item)
    } else {
      this.getPoiItem(item)
    }
  }

  // 关闭提示弹窗
  closeTipsModal() {
    this.tipsModalVisible = false
    if (this.tips.action === 1) {
      // 清空当前poi
      this[this.type] = {
        ...this[this.type],
        poiId: '',
        poi_lat: '',
        poi_long: '',
        place: '',
        address: ''
      }
      if (this.type === Type.PICK) {
        this[this.type].to = ''
      } else {
        this[this.type].from = ''
      }
      this.updateFormData()
    }
    this.tips = {
      content: null,
      title: null,
      btn: null,
      action: 0
    }
  }

  // 选择POI列表数据
  async getPoiItem(item: any) {
    if (item.googlePoi) {
      const params = {
        placeId: item.placeId,
        sessionToken: this.transfer_sessionToken
      }
      try {
        const {
          result,
          error,
          success
        } = await this.getSearchPoiDetail(params)
        if (!success) {
          this.$toast(error.message)
          return
        }
        item = Object.assign(item, { lat: result.lat, lng: result.lng })
      } catch(e) {
        return
      }
    }
    const itemObj: any = {
      poiId: item.poiId || '',
      poi_lat: item.lat || '',
      poi_long: item.lng || '',
      place: item.placeId || '',
      address: item.secondaryText || ''
    }
    if (this.type === Type.PICK) {
      itemObj.to = item.mainText
    } else {
      itemObj.from = item.mainText
    }
    this[this.type] = {
      ...this[this.type],
      ...itemObj
    }
    this.updateFormData()
    // 校验poi是否为黑名单
    this.checkPoi({
      ...itemObj,
      countryCode: item.countryCode
    })
  }

  checkPoi(item: any) {
    const req = {
      latitude: item.poi_lat,
      longitude: item.poi_long,
      poi_name: this.type === Type.PICK ? item.to : item.from,
      poi_id: item.poiId,
      place_id: item.place,
      country_code: item.countryCode
    }
    this.validatePoi(req).then((res: any) => {
      if (res.success) {
        // 获取当前搜索框内的poi
        const type = this.type === Type.PICK ? 'to' : 'from'
        const currentPoi = this.formData[type]
        // 只有当当前搜索框内的poi需要屏蔽时，才弹提示，防止用户快速切换poi导致提示错误
        const { is_blocked, poi_name, tips } = res.result
        if (is_blocked && poi_name === currentPoi) {
          if (tips.type === 'toast') {
            this.$toast(tips.content)
            // 清空当前poi
            this[this.type] = {
              ...this[this.type],
              poiId: '',
              poi_lat: '',
              poi_long: '',
              place: '',
              address: ''
            }
            if (this.type === Type.PICK) {
              this[this.type].to = ''
            } else {
              this[this.type].from = ''
            }
            this.updateFormData()
          } else if (tips.type === 'confirm-modal') {
            this.tips = {
              ...tips,
              action: 1
            }
            this.tipsModalVisible = true
          }
        }
      }
    })
  }

  // 关闭搜索页
  hideSearch() {
    this.searchList = {}
    this.showAirportPage = false
  }

  // 打开日期选择
  openDatePicker() {
    const date = this[this.type].date
    const time = this[this.type].time
    if (date) {
      this.selectCurrentDate = new Date(date)
    } else {
      this.selectCurrentDate = dayjs().add(3, 'day').toDate()
    }
    if (time) {
      const hour = Number(time.split(':')[0])
      const minute = Number(time.split(':')[1])
      this.timeData = dayjs().hour(hour).minute(minute).toDate()
    } else {
      this.timeData = dayjs().add(1, 'hour').minute(0).toDate()
    }
    this.showDataPicker = true

    this.dateTimePicker.scrollToCurrentMonth()
  }

  // 关闭日期选择
  hideDatePicker() {
    this.showDataPicker = false
  }


  // 接送时间确认
  confirmDateAndTime(date: Date, time: Date) {
    this.selectCurrentDate = date
    this[this.type].date = date
    const _time = dayjs(time).format('HH:mm')
    this[this.type].time = _time
    this.updateFormData()
    // 转为异步操作，优化INP
    setTimeout(() => {
      this.hideDatePicker()
      // 时间GA埋点
      this.$sendGTMCustomEvent(`Airport Transfer Vertical Screen|Select Parameter: Time|${time}`)
      // 日期GA埋点
      this.$sendGTMCustomEvent(`Airport Transfer Vertical Screen|Select Parameter: Date|${dayjs(date).format('YYYY-MM-DD')}`)
    }, 0)
  }

  // 最大乘客数提示
  limitMax(btn: any) {
    if (btn === 'increase') {
      this.isMaxPas = true
      this.$toast(this.$t('13182-pas_limit_50'))
    }
  }

  // 确认乘客数
  savePas(pas: number) {
    this.isMaxPas = false
    this[this.type].pas = pas
    // 乘客数GA埋点
    this.$sendGTMCustomEvent(`Airport Transfer Vertical Screen|Select Parameter: Passenger|${this.pas}`)
    this.updateFormData()
  }

  // 更新Url
  updateUrlParams(data: urlParams) {
    const date = data.date ? dayjs(data.date).format('YYYY-MM-DD') + `${data.time ? ' ' + data.time : ''}` : ''
    const from = this.isPickTab ? data.from : data.to
    const to = this.isPickTab ? data.to : data.from
    const url = `?flightDirection=${data.flightDirection}&from=${encodeURIComponent(from)}&to=${encodeURIComponent(to)}&address=${encodeURIComponent(data.address)}&lat=${data.lat}&long=${data.long}&poiId=${data.poiId}&poi_lat=${data.poi_lat}&poi_long=${data.poi_long}&time=${date}&pas=${data.pas}&code=${data.code}&place=${data.place}&ac=${data.ac}&kcid=${data.kcid}`
    window.history.replaceState({}, '', url)
  }

  // 表单验证
  validateForm() {
    this.isFormValidate = true
    let key: keyof formData
    // 按航班号搜索时，单独校验from框信息
    if (this.isByFlight) {
      if (!this.flightInfo.hasFlight) {
        this.formValid.flightNo.error = true
        this.formErrorTip = this.formErrorTip !== '' ? this.formErrorTip : this.formValid.flightNo?.content
        this.isFormValidate = false
      } else if (this.flightInfo.iataCode !== this.pick.code || !dayjs(this.flightInfo.arrivalTime).isSame(this.pick.time) || !dayjs(this.flightInfo.arrivalDate).isSame(this.pick.date)) {
        // 航班信息与表单不一致
        // 更新表单信息
        this.updateFlightInfo(this.flightInfo)
      }
    }
    for (key in this.formData) {
      if (!this.formData[key]) {
        this.formValid[key].error = true
        // 报错文案为空时重新赋值
        this.formErrorTip = this.formErrorTip !== '' ? this.formErrorTip : this.formValid[key].content
        this.isFormValidate = false
      } else {
        this.formValid[key].error = false
      }
    }
    this.formErrorTip && this.$toast(this.formErrorTip)
    // this.showFormErrorTip = !this.isFormValidate && true
    // 表单验证GA埋点
    this.$sendGTMCustomEvent(`Airport Transfer Vertical Screen|Notification|${this.formErrorTip}`)
    const t1 = setTimeout(() => {
      // this.showFormErrorTip = false
      // 清空报错文案，为了承接其他的报错文案
      this.formErrorTip = ''
      clearTimeout(t1)
    }, 3000)
  }

  // 开始搜索
  search() {
    this.validateForm()
    if (this.isFormValidate) {
      // 聚合页搜索历史记录，记录是哪个tab
      this.$emit('handle-add-history')
      const data = {
        ...this[this.type],
        from: this.isPickTab ? this[this.type].from : this[this.type].to,
        to: this.isPickTab ? this[this.type].to : this[this.type].from,
        flightNo: this.isByFlight ? this.flightInfo.flightNo : '',
        originCode: this.isByFlight ? this.flightInfo.originCode : '',
        departureName: this.flightInfo.departureName || ''
      }
      if (this.currentPage === 'home') {
        // 缓存历史记录
        this.saveHistory()
        this.homeSearch(data)
      } else {
        // 缓存历史记录
        this.saveHistory()
        this.$emit('search', data)
      }
    }
  }

  homeSearch(data: any) {
    let langPath = ''
    const lang = this.getKlkLanguage()
    if (lang !== 'en') {
      langPath = `/${lang}`
    }
    const date = data.date && dayjs(data.date).format('YYYY-MM-DD') + `${data.time ? ' ' + data.time : ''}`
    const query = `?flightDirection=${data.flightDirection}&from=${encodeURIComponent(data.from)}&to=${encodeURIComponent(data.to)}&address=${encodeURIComponent(data.address)}&lat=${data.lat}&long=${data.long}&poiId=${data.poiId}&poi_lat=${data.poi_lat}&poi_long=${data.poi_long}&time=${date}&pas=${data.pas}&code=${data.code}&place=${data.place}&ac=${data.ac}&kcid=${data.kcid}&flightNo=${data.flightNo}&originCode=${data.originCode}&departureName=${data.departureName}`
    const href = `${langPath}/airport-transfers/results/${query}`
    // 表单提交GA埋点
    const timeDelta = dayjs(data.date).diff(dayjs().format('MM/DD/YYYY'), 'day')
    const gaSumitData = `To: ${data.to} - From: ${data.from} - Date: ${dayjs(data.date).format('YYYY-MM-DD')} - Time: ${data.time} - Passengers: ${data.pas} - Place ID: ${data.place} - lat: ${data.lat} - lng: ${data.long}`
    this.$sendGTMCustomEvent(`Airport Transfer Vertical Screen|Search Submit|${gaSumitData}|${timeDelta}`)
    // console.log(href)
    window.location.href = href
  }

  // CHP3.0事件监听
  openSearchForChp(word: any) {
    this.searchWord = word
    this.isAirport = true
    this.showAirportPage = true
    this.isFromPublic = true
  }

  eventListener(searchWord: string) {
    this.openSearchForChp(searchWord)
  }

    get galileoClick1() {
        return { spm: WebClick_21, autoTrackSpm: true }
    }

    get galileoClick2() {
        return { spm: WebClick_22, autoTrackSpm: true }
    }

    get galileoClick3() {
        return { spm: WebClick_23, autoTrackSpm: true }
    }

    get galileoClick4() {
        return { spm: WebClick_24, autoTrackSpm: true }
    }

    get galileoClick5() {
        return { spm: WebClick_25, autoTrackSpm: true }
    }

    get galileoClick6() {
        return { spm: WebClick_26, autoTrackSpm: true }
    }

    get galileoClick7() {
        return { spm: WebClick_27, autoTrackSpm: true }
    }

    get galileoClick8() {
        return { spm: WebClick_28, autoTrackSpm: true }
    }
}
</script>

<style lang="scss" scoped>
@mixin text-ellipsis($line-clamp: 2) {
  display: -webkit-box;
  -webkit-line-clamp: $line-clamp;
  -webkit-box-orient: vertical;
  text-overflow: ellipsis;
  overflow: hidden;
}
.search-form {
  position: relative;
  top: 0;
  left: 0;
  border-radius: $radius-l;
  margin: 0 20px;
  //box-shadow: $shadow-normal-2;
  background-color: $color-bg-1;

  &__header-tabs {
    .carrental-transfer {
      width: 100%;
      height: 46px;
      background-image: url('https://res.klook.com/image/upload/car-rental/Home_Tab_1_nbut0a.png');
      background-size: 100% 100%;
      display: flex;
      flex-direction: row;
      align-items: center;
      .tab {
        flex: 1 0 0;
        text-align: center;
        font-weight: $fontWeight-bold;
        color: $color-text-secondary;
        font-size: $fontSize-body-s;

        &.active {
          color: $color-brand-primary;
        }
      }
    }
  }

  &__header-tab {
    width: 100%;
    height: 46px;
    background-image: url('https://res.klook.com/image/upload/car-rental/Home_Tab_1_nbut0a.png');
    background-size: 100% 100%;
    display: flex;
    flex-direction: row;
    align-items: center;
    .tab {
      flex: 1 0 0;
      text-align: center;
      font-weight: $fontWeight-bold;
      color: $color-text-secondary;
      font-size: $fontSize-body-s;

      &.active {
        color: $color-brand-primary;
      }
    }
  }

  &__tabs {
    padding: 20px 16px 0;

    ::v-deep .klk-tabs-items {
      justify-content: space-around;
    }
    ::v-deep .klk-tabs-item {
      @include font-body-s-regular-v2;
      height: 36px !important;
      max-width: calc(50% - 24px);
    }
    ::v-deep .klk-tabs-body {
      margin-top: 12px !important;
    }
  }

  &__pick-type {
    padding: 0px 16px;
    &-wrapper {
      padding: 4px;
      background-color: $color-bg-3;
      border-top-left-radius: $radius-l;
      border-top-right-radius: $radius-l;
    }
  }

  &__wraper {
    padding: 0 16px 16px;
  }

  &__place {
    background-color: $color-bg-3;
    border-radius: $radius-l;
    width: 100%;
    min-height: 57px;
    height: auto;
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 8px 12px;

    &:not(:first-of-type) {
      margin-top: 8px;
    }

    &.error {
      background-color: $color-brand-primary-light;
      border: 0.5px solid $color-error;
    }

    &-null {
      @include font-body-s-regular-v2;
      color: $color-text-placeholder;
    }

    &-tip {
      @include font-caption-m-regular;
      color: $color-text-secondary;
    }

    &-name {
      @include font-body-s-bold;
      @include text-ellipsis(2);
      margin-top: 2px;
    }
  }

  &__date-pas {
    background-color: $color-bg-3;
    border-radius: $radius-l;
    width: 100%;
    height: 57px;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    padding: 8px 12px;
    margin-top: 8px;

    &.error {
      background-color: $color-brand-primary-light;
      border: 0.5px solid $color-error;
    }

    &--null {
      @include font-caption-m-regular;
      color: $color-text-placeholder;
    }

    &--tip {
      @include font-caption-m-regular;
      color: $color-text-secondary;
    }

    &--value {
      @include font-body-s-bold;
      margin-top: 2px;
    }
  }

  &__date-time {
    display: flex;
    flex: 1;
    align-items: center;
  }

  &__pas {
    @include font-body-s-regular-v2;
    background-color: $color-bg-3;
    border-radius: $radius-l;
    width: 100%;
    height: 57px;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    padding: 8px 12px;
    margin-top: 8px;
    &.error {
      background-color: $color-brand-primary-light;
      border: 0.5px solid $color-error;
    }

    ::v-deep .klk-counter-increase, ::v-deep .klk-counter-decrease {
      background-color: $color-bg-1;
    }
  }

  &__btn {
    margin-top: 16px;
  }

  &__passenger-warning {
    padding: 12px 20px;
    display: flex;
    flex-direction: row;
    align-items: center;
    background-color: $color-brand-secondary-light;
    color: $color-caution;

    span {
      @include font-body-s-regular;
      margin-left: 8px;
    }
  }

  &__passenger {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    padding: 16px 20px;

    &-tips {
      padding: 12px 20px;
      display: flex;
      flex-direction: row;
      align-items: center;
      background-color: $color-brand-secondary-light;
      color: $color-caution;

      img {
        margin-right: 8px;
      }
    }

    &-box {
      display: flex;
      flex-direction: column;
    }

    &-title {
      @include font-body-m-semibold;
    }

    &-subtitle {
      @include font-caption-m-regular;
      margin-top: 2px;
    }
  }

  &__warning-tips {
    width: 100%;
    padding: 16px;
    line-height: 20px;
    font-size: $fontSize-body-m;
    color: $color-text-primary-onDark;
    background-color: $color-brand-secondary;
    position: fixed;
    top: 48px;
    left: 0;
  }

  // 航班号前置样式
  &__wrapper_by_flight {
    .search-form {
      &__place-hide-radius {
        border-top-left-radius: 0;
        border-top-right-radius: 0;
      }

      &__place-flight {
        padding: 4px 12px;
        height: auto;
      }

      &__place-flight-name {
        @include text-ellipsis(2);
      }

      &__place-flight-placeholder {
        @include font-body-s-regular-v2;
        color: $color-text-placeholder;
      }

      &__place-flight-time {
        @include font-body-xs-regular;
        @include text-ellipsis(2);
        color: $color-text-primary;
      }
    }
  }
}
::v-deep .klk-modal {
  padding: 0;
}

::v-deep .klk-modal.search-form__tips-modal {
  padding-top: 24px;
  .klk-modal-content {
    text-align: center;
  }
}

::v-deep .klk-bottom-sheet-inner {

  .klk-bottom-sheet-body {
    padding: 0 !important;
  }
}

.search-form__info-tips {
  ::v-deep .klk-bottom-sheet-inner {
    padding-top: 24px !important;
    .klk-bottom-sheet-body {
      padding: 0 20px 32px !important;
    }
  }
}
</style>
