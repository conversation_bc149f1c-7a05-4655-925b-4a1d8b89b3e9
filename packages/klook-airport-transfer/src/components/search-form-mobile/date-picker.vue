<template>
  <div class="date-picker">
    <div class="date-picker__header">
      <!-- <i class="date-picker__header-close" @click="hideDatePicker">
        <SvgIcon name="mobile-private-transfer#icon_edit_close_m" width="24" height="24" />
      </i> -->
      <div class="date-picker__header-title">{{ $t('13779-select_date') }}</div>
    </div>
    <div class="date-picker__calendar" :style="{height: pickerHeight}">
      <klk-date-picker
        v-if="pickerLazyLoad.pickerVisible"
        ref="datePicker"
        :date.sync="date"
        width="auto"
        :min-date="minDate"
        :max-date="maxDate"
        :vertical-scroll="true"
        @select="selectDate"
      ></klk-date-picker>
      <klk-loading v-else></klk-loading>
    </div>
    <div class="date-picker__footer" :style="{height: hideTimePicker ? '100px' : '166px'}">
      <div v-if="!hideTimePicker">
        <div v-if="showPickUpTips" class="date-picker__tips">{{ $t('194302-pickUpTimeTips') }}</div>
        <div
          class="date-picker__time"
          :data-spm-module="`PickupTimeInput?trg=manual&ext=${JSON.stringify({type: isPickTab ? 'AirportDropoff' : 'AirportDropoff'})}`"
          v-galileo-click-tracker="galileoClick1"
          data-spm-virtual-item="__virtual?typ=entry"
          @click="openTimePicker"
        >
          <div class="date-picker__time-title">{{ $t('14084-time') }}</div>
          <div class="date-picker__time-content">
            <span>{{ timeFormat }}</span>
            <img src="https://res.klook.com/image/upload/web3.0/airport-transfer/icon_navigation_chevron_down_xs_km1beh.svg" width="16" height="16" alt="">
          </div>
        </div>
      </div>
      <div class="date-picker__confirm-btn">
        <klk-button type="primary" block @click="confirmDateTime">{{ $t('12439-confirm') }}</klk-button>
      </div>
    </div>
    <klk-datetime-picker
      ref="dateTimePicker"
      v-model="timeData"
      type="time"
      :title="$t('14084-time')"
      :open.sync="showTimePicker"
      @confirm="selectTime"
    ></klk-datetime-picker>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop, Ref, Watch } from 'vue-property-decorator'
import dayjs from 'dayjs'
import { WebClick_51 } from '../../../share/galileo/auto-click'

@Component
export default class DatePicker extends Vue {
  @Prop({ type: Date, default: () => null }) date!: any
  @Prop({ type: Date, default: () => null }) time!: any
  @Prop({ type: Number, default: 0 }) scrollTop!: number
  @Prop({ type: Boolean, default: true }) isPickTab!: boolean
  @Prop({ type: Boolean, default: false }) visible!: boolean
  @Prop({ type: Boolean, default: false }) showPickUpTips!: boolean
  @Prop({ type: Boolean, default: false }) hideTimePicker!: boolean // 是否展示时间选择
  @Ref() datePicker!: any

  pickerHeight: string = '449px'
  minDate: Date = dayjs().subtract(1, 'day').toDate()
  maxDate: Date = dayjs().add(1, 'year').toDate()
  showTimePicker: boolean = false
  selectCurrentDate: Date = new Date()
  timeData: any = dayjs().add(1, 'hour').minute(0).toDate()
  timeFormat: string = dayjs().add(1, 'hour').minute(0).format('HH:mm')
  pickerLazyLoad: any = {
    pickerVisible: false,
    timer: null
  }

  @Watch('visible')
  onDatePickerVisibleChange(val: boolean) {
    if (val) {
      clearTimeout(this.pickerLazyLoad.timer)
      this.pickerLazyLoad.timer = setTimeout(() => {
        this.pickerLazyLoad.pickerVisible = val
      }, 0)
    }
  }

  @Watch('date', { immediate: true })
  onSelectCurrentDateChange() {
    this.selectCurrentDate = this.date
  }

  @Watch('time', { immediate: true })
  onSelectTimeChange() {
    this.timeFormat = dayjs(this.time).format('HH:mm')
    this.timeData = this.time
  }

  mounted() {
    const h = window.innerHeight
    this.pickerHeight = h * 0.9 - 230 + 'px'
  }

  beforeDestroy() {
    if (this.pickerLazyLoad.timer) {
      clearTimeout(this.pickerLazyLoad.timer)
    }
  }

  hideDatePicker() {
    this.$emit('update:hideDatePicker', false)
  }

  scrollToCurrentMonth() {
    setTimeout(() => {
      this.datePicker?.scrollToCurrentMonth()
    }, 0)
  }

  openTimePicker() {
    this.showTimePicker = true
  }

  // 确认时间选择
  selectTime(date: Date) {
    this.timeFormat = dayjs(date).format('HH:mm')
  }

  selectDate(date: Date) {
    this.selectCurrentDate = date
    // this.$emit('selectDate', date)
    // this.hideDatePicker()
  }

  confirmDateTime() {
    if (this.hideTimePicker) {
      this.$emit('confirm-date-and-time', this.selectCurrentDate)
    } else {
      this.$emit('confirm-date-and-time', this.selectCurrentDate, this.timeData)
    }
  }

  // CHP3.0事件监听
  openSearchForChp(word: any) {
    this.searchWord = word
    this.isAirport = true
    this.showAirportPage = true
    this.isFromPublic = true
  }

  eventListener(searchWord: string) {
    this.openSearchForChp(searchWord)
  }

    get galileoClick1() {
        return { spm: WebClick_51, autoTrackSpm: true }
    }
}
</script>

<style lang="scss" scoped>
.date-picker {
  overflow-y: scroll;

  &__header {
    height: 40px;
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;

    &-close {
      width: 48px;
      height: 48px;
      display: flex;
      justify-content: center;
      align-items: center;
      position: absolute;
      top: 0;
      left: 0;
      z-index: 1;
    }

    &-title {
      @include font-body-m-semibold;
    }
  }

  &__warning {
    margin: 16px;
    padding: 12px 16px;
    display: flex;
    flex-direction: row;
    align-items: center;
    background-color: $color-brand-secondary-light;
    color: $color-text-secondary;
    border-radius: $radius-m;

    span {
      margin-left: 8px;
    }
  }

  &__calendar {
    ::v-deep .klk-date-picker-vertical-scroll {
      min-height: auto;
    }
  }

  &__footer {
    padding: 20px 20px 0;
    box-shadow: $shadow-normal-3;
  }

  &__tips {
    @include font-paragraph-xs-regular;
    color: $color-text-placeholder;
    margin-bottom: 10px;
  }

  &__time {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    height: 44px;
    background-color: $color-bg-3;
    border-radius: $radius-l;
    padding: 0 16px;

    &-title {
      flex: 1;
      display: flex;
      align-items: center;
    }

    &-content {
      flex: 60px 0 0;
      display: flex;
      flex-direction: row;
      align-items: center;

      span {
        @include font-body-s-bold;
        margin-right: 8px;
      }
    }
  }

  &__confirm-btn {
    margin-top: 20px;
  }
}
</style>
