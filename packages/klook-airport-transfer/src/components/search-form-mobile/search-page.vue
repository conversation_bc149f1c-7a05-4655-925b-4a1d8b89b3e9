<template>
  <div class="search">
    <div class="search__header">
      <i class="search__header-close" @click="hideSearch">
        <img src="https://res.klook.com/image/upload/web3.0/airport-transfer/icon_edit_close_m_tpgfkh.svg" alt="" width="24" height="24">
      </i>
      <div class="search__header-input">
        <input
          ref="searchInput"
          v-model="inputValue"
          name="searchPageChpSearchInput"
          autocomplete="off"
          aria-autocomplete="none"
          @input="handleInputChange"
        >
        <i v-if="inputValue" class="search__header-clear-icon" @click="clearInputValue">
          <img src="https://res.klook.com/image/upload/web3.0/airport-transfer/icon_edit_clear_fill_xs_jqn72w.svg" alt="" width="20" height="20">
        </i>
      </div>
    </div>
    <div v-if="loading" class="search__loading">
      <klk-loading></klk-loading>
    </div>
    <div v-else>
      <div v-if="showAdditionalInfo">
        <div v-if="isDeparture || !isPickTab" class="search__list search__list-current-location">
          <div>
            <div class="search__list-current-location--header">
              <div class="search__list-title">{{ $t('192362-currentLocation') }}</div>
              <div
                v-if="locationBtnStatus === 'success'"
                class="search__list-current-location--search"
                @click.stop="initLocation"
              >
                {{ $t('192363-relocate') }}
              </div>
            </div>
            <div
              v-if="locationBtn[locationBtnStatus]"
              class="search__list-current-location--status"
              @click="locationBtn[locationBtnStatus].handler"
            >
              {{ locationBtn[locationBtnStatus].text }}
            </div>
          </div>
          <div v-if="locationBtnStatus === 'success'">
            <div
              v-for="(item, index) in currentCityAirport"
              :key="item.iataCode"
              v-galileo-click-tracker="{ spm: `Sug_recAirport_LIST?oid=${item.iataCode}&idx=${index}&len=${currentCityAirport.length}`, autoTrackSpm: true }"
              class="search__list-item"
              :data-spm-module="`Sug_recAirport_LIST?oid=${item.iataCode}&idx=${index}&len=${currentCityAirport.length}`"
              data-spm-virtual-item="__virtual?typ=entry"
              @click="selectSearchItem(item)"
            >
              <div class="search__list-item--name search__list-current-location--name">
                <SvgIcon class="icon" name="common#icon_bottom_navigation_destinations_fill_m" size="14" color="#ff5722"></SvgIcon>
                <p>{{ item.airportName }}({{ item.iataCode }})</p>
              </div>
              <span class="search__list-item--address">{{ item.cityName }}</span>
            </div>
          </div>
        </div>
        <!-- 机场历史搜索记录 -->
        <div v-if="searchHistoryByType && searchHistoryByType.length && searchType === 'airport'" class="search__list search__list-history">
          <div class="search__list-history--header">
            <div class="search__list-title">{{ $t('192361-history') }}</div>
            <div class="remove" @click="clearHistory">{{ $t('192364-remove') }}</div>
          </div>
          <div
            v-for="(item, index) in searchHistoryByType"
            :key="item.iataCode"
            v-galileo-click-tracker="{ spm: `Sug_recentAirport_LIST?oid=${item.iataCode}&idx=${index}&len=${searchHistoryByType.length}`, autoTrackSpm: true }"
            class="search__list-item"
            :data-spm-module="`Sug_recentAirport_LIST?oid=${item.iataCode}&idx=${index}&len=${searchHistoryByType.length}`"
            data-spm-virtual-item="__virtual?typ=entry"
            @click="selectSearchItem(item)"
          >
            <p class="search__list-item--name">{{ item.airportName }}({{ item.iataCode }})</p>
            <span class="search__list-item--address">{{ item.cityName }}</span>
          </div>
        </div>
      </div>
      <div v-if="showHotAirport" class="search__list">
        <div class="search__list-title">{{ hotAirports.groupName }}</div>
        <template v-for="city in hotAirports.operatedCityList">
          <div
            v-for="(item, index) in city.operatedAirportItemList"
            :key="item.iataCode + index"
            v-galileo-click-tracker="{ spm: `PopularAirport_LIST?oid=${item.iataCode}&ext=${JSON.stringify({type: isPickTab ? 'AirportPickup' : 'AirportDropoff', transportresult: isFromPublic ? 'public transfer' : 'private transfer'})}`, autoTrackSpm: true }"
            class="search__list-item"
            :data-spm-module="`PopularAirport_LIST?oid=${item.iataCode}&ext=${JSON.stringify({type: isPickTab ? 'AirportPickup' : 'AirportDropoff', transportresult: isFromPublic ? 'public transfer' : 'private transfer'})}`"
            data-spm-virtual-item="__virtual?typ=entry"
            @click="selectSearchItem(item)"
          >
            <p class="search__list-item--name">{{ item.airportName }}({{ item.iataCode }})</p>
            <span class="search__list-item--address">{{ item.cityName }}</span>
          </div>
        </template>
      </div>
      <div v-if="airportList" class="search__list">
        <div class="search__list-title">{{ $t('13965-suggestions') }}</div>
        <div
          v-for="item in searchList.airportSearchItemList"
          :key="item.iataCode"
          class="search__list-item"
          :data-spm-module="`AirportSearchResult_LIST?oid=${item.iataCode}&ext=${JSON.stringify({type: isPickTab ? 'AirportPickup' : 'AirportDropoff', transportresult: isFromPublic ? 'public transfer' : 'private transfer', searchword: inputValue})}`"
          data-spm-virtual-item="__virtual?typ=entry"
          @click="selectSearchItem(item)"
        >
          <p class="search__list-item--name">{{ item.airportName }}({{ item.iataCode }})</p>
          <span class="search__list-item--address">{{ item.cityName }}</span>
        </div>
      </div>
      <div v-if="poiList" class="search__list">
        <div
          v-for="(item, index) in searchList.poiList"
          :key="index"
          class="search__list-item"
          :data-spm-module="`POISearchResult_LIST?idx=${index}&len=${searchList.poiList.length}&oid=${item.googlePoi ? item.placeId : item.poiId}&ext=${JSON.stringify({type: isPickTab ? 'AirportPickup' : 'AirportDropoff', query: encodeURIComponent(searchWord), sug: encodeURIComponent(item.mainText), poiType: encodeURIComponent(item.placeTypeName)})}`"
          data-spm-virtual-item="__virtual?typ=entry"
          @click.stop="selectSearchItem(item)"
        >
          <div class="search__list-item--wrapper">
            <div v-if="item.icon" class="search__list-item--left">
              <img :src="item.icon" width="16" :alt="item.placeTypeName" />
            </div>
            <div class="search__list-item--right">
              <div class="search__list-item--header">
                <span class="search__list-item--header-text search__list-item--name">{{ item.mainText }}</span>
                <span v-if="item.placeTypeName" class="search__list-item--header-type">{{ item.placeTypeName }}</span>
              </div>
              <span class="search__list-item--poi-address">{{ item.secondaryText }}</span>
            </div>
          </div>
        </div>
      </div>
      <div v-if="noResult" class="search__no-reslut">
        <Klk-empty-panel
          :content="$t('14507', inputValue)"
          icon-src="https://res.klook.com/image/upload/web3.0/ill_spot_hero_transport_tickets_edrvrq.svg"
        >
        </Klk-empty-panel>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Prop, Ref } from 'vue-property-decorator'
import debounce from 'lodash/debounce'
import KlkEmptyPanel from "@klook/empty-panel";
import "@klook/empty-panel/dist/esm/index.css";
import SearchPageBase from "../../common/search-form/search-page-base"

@Component({
  components: {
    KlkEmptyPanel
  }
})
export default class SearchPage extends SearchPageBase {
  @Prop({ type: Object, default: () => null }) hotAirports!: object
  @Prop({ type: Object, default: () => null }) searchList!: any
  @Prop({ type: String, default: '' }) searchType!: string
  @Prop({ type: Boolean, default: false }) loading!: boolean
  @Prop({ type: String, default: '' }) searchWord!: string
  @Prop({ type: Boolean, default: false }) isFromPublic!: boolean
  @Ref() searchInput!: any

  showSearchList: boolean = false

  inputValue: string = this.searchWord

  get airportList() {
    return this.inputValue && this.showSearchList && this.searchType === 'airport' && this?.searchList?.airportSearchItemList?.length
  }

  get poiList() {
    return this.inputValue && this.showSearchList && this.searchType === 'poi' && this?.searchList?.poiList?.length
  }

  get noResult() {
    return this.inputValue && this.showSearchList && ((!this.searchList.airportSearchItemList || (this.searchList.airportSearchItemList && this.searchList.airportSearchItemList.length === 0)) && !this.searchList.poiList)
  }

  get showAdditionalInfo() {
    return !this.hideExtraInfo && this.searchType === 'airport' && !this.inputValue
  }

  get showHotAirport() {
    return !this.inputValue && this.searchType === 'airport' && !this.hideHotAirport
  }

  mounted() {
    if (!this.hideExtraInfo) {
      // 获取历史记录
      this.$emit('getSearchHistory')
      // 初始化地理位置
      this.initLocation()
    }
    // 自动获取输入框焦点
    this.$nextTick(() => {
      setTimeout(() => {
        this.searchInput && this.searchInput.focus()
      }, 300)
    })
    // 默认有值时，根据值调用相关接口
    this.handleInputChange()
  }

  // 防抖搜索
  handleValue = debounce(this.getSearchList, 500)

  handleInputChange() {
    this.showSearchList = false
    this.handleValue(this.inputValue)
  }

  getSearchList(keyword: string) {
    this.showSearchList = true
    this.$emit('getSearchList', keyword)
  }

  // 选择列表项
  selectSearchItem(item: any) {
    // 如果是从首页公共交通搜索框进来的，让其直接跳转到搜索结果页
    if (this.isFromPublic && this.searchType === 'airport') {
      const from = `${item.airportName}(${item.iataCode})`
      let langPath = ''
      const lang = this.getKlkLanguage()
      if (lang !== 'en') {
        langPath = `/${lang}`
      }
      const query = `?flightDirection=1&from=${from}&to=&address=&lat=${item.latitude}&long=${item.longitude}&poiId=&poi_lat=&poi_long=&time=&pas=2&code=${item.iataCode}&place=&ac=${item.isActive}&kcid=${item.backupKlookCityId}&is_from_public=1`
      const href = `${langPath}/airport-transfers/results/${query}`
      window.location.href = href
    } else {
      this.$emit('selectSearchItem', item)
      this.hideSearch()
    }
  }

  // 清空输入框
  clearInputValue() {
    this.inputValue = ''
    this.$emit('getSearchList', this.inputValue)
    setTimeout(() => {
      this.searchInput && this.searchInput.focus()
    }, 0)
  }

  // 关闭搜索页
  hideSearch() {
    this.$emit('hideSearch')
  }
}
</script>

<style lang="scss" scoped>
@mixin text-ellipsis($line-clamp: 2) {
  display: -webkit-box;
  -webkit-line-clamp: $line-clamp;
  -webkit-box-orient: vertical;
  text-overflow: ellipsis;
  overflow: hidden;
}
.search {
  &__header {
    width: 100%;
    height: 44px;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    padding: 0 12px;
    background-color: $color-bg-1;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 10;

    &-close {
      flex: 44px 0 0;
      height: 44px;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    &-input {
      min-height: 36px;
      flex: 1;
      background-color: $color-bg-page;
      border-radius: $radius-pill;
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: center;
      padding: 0 12px;
    }

    &-search-icon, &-clear-icon {
      display: flex;
    }

    input {
      flex: 1;
      border: none;
      outline: none;
      caret-color: #ff5722;
      line-height: 36px;
      background: transparent;
    }
  }

  &__loading {
    position: absolute;
    padding: 0 16px;
    margin-top: 124px;
    width: 100%;
    height: 300px;
  }

  &__list {
    padding: 0 20px;
    background-color: white;

    &-title {
      @include font-body-s-bold;
      margin-top: 20px;
    }

    &-item {
      padding: 12px 0;
      &:not(:last-child) {
        border-bottom: 1px solid $color-bg-widget-darker-2;
      }

      &--wrapper {
        display: flex;
        justify-content: space-between;
      }

      &--left {
        width: 20px;
      }

      &--right {
        flex-grow: 1;
      }

      &--name {
        @include font-body-s-regular;
      }

      &--header {
        display: flex;
        align-items: flex-start;
        justify-content: space-between;
        &-text {
          @include text-ellipsis(2);
          flex-shrink: 1;
        }
        &-type {
          @include font-caption-m-regular;
          flex-grow: 1;
          color: $color-caution;
          text-align: right;
          margin-left: 4px;
          white-space: nowrap;
        }
      }

      &--address {
        @include font-caption-m-regular;
        color: $color-text-secondary;
        margin-top: 4px;
      }

      &--poi-address {
        @include font-caption-m-regular;
        @include text-ellipsis(2);
        color: $color-text-secondary;
        margin-top: 4px;
        max-width: 290px;
      }
    }

    &-current-location {
      border-bottom: 1px solid $color-bg-widget-darker-2;
      &--header {
        display: flex;
        justify-content: space-between;
      }
      &--search {
        @include font-body-s-bold;
        color: $color-info;
        cursor: pointer;
        display: flex;
        align-items: center;
        .icon {
          margin-right: 4px;
        }
      }
      &--status {
        @include font-body-s-regular;
        color: $color-text-primary;
        font-weight: $fontWeight-regular;
        border-radius: $radius-l;
        background: $color-bg-3;
        border: 0;
        cursor: pointer;
        padding: 7px 12px;
        margin: 12px 0;
        width: fit-content;
        display: flex;
        align-items: center;

        .icon {
          margin-right: 4px;
        }

        &:hover {
          background-color: $color-bg-page !important;
        }
      }
      &--name {
        display: flex;
        align-items: center;
        .icon {
          margin-right: 4px;
        }
      }
    }

    &-history {
      border-bottom: 1px solid $color-bg-widget-darker-2;
      &--header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        .remove {
          @include font-body-s-bold;
          color: $color-info;
          margin: 20px 0 12px 0;
        }
      }
    }
  }

  .search__list:first-of-type {
    margin-top: 8px;
  }
  .search__list-title:first-of-type {
    margin-top: 8px;
  }

  &__no-reslut {
    margin-top: 168px;
  }
}
</style>
