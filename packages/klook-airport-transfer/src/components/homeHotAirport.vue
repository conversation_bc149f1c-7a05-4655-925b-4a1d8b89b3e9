<template>
  <div v-if="dataInfo && dataInfo.hot_airports && dataInfo.hot_airports.length > 0" class="popular-airports">
    <h3 class="popular-airports__title">{{ dataInfo.title }}</h3>
    <div class="popular-airports__items">
      <div v-for="(item, index) in dataInfo.hot_airports" :key="index" class="popular-airports__item">
        <div class="popular-airports__name">{{ item.airportName }}({{ item.iataCode }}) {{ $t('17102-to') }} {{ item.cityName }}</div>
        <div class="popular-airports__price">
          <em>{{ $t('10638-From') }}</em>
          <span>{{ item.price.symbol }} {{ item.price.price }}</span>
        </div>
        <!-- <a class="popular-airports__link" :href="`${langPath}/airport-transfers/service/${item.iataCode.toLowerCase()}`"></a> -->
        <a class="popular-airports__link" :href="`https://${host}${langPath}/airport-transfers/service/${item.seoUrl}/`"></a>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Prop } from 'vue-property-decorator'
import Base from "@/common/base";

@Component
export default class PopularAirports extends Base {
  @Prop({ type: Object, default: () => null }) data!: any

  dataInfo: any = this.data
  langPath = ''

  host = null

  mounted() {
    if (this.getKlkLanguage() !== 'en') {
      this.langPath = `/${this.getKlkLanguage()}`
    }

    if (this.getKlkHost()) {
      this.host = this.getKlkHost()
    }
  }
}
</script>

<style lang="scss" scoped>
@mixin text-ellipsis($line-clamp: 2) {
  display: -webkit-box;
  -webkit-line-clamp: $line-clamp;
  -webkit-box-orient: vertical;
  text-overflow: ellipsis;
  overflow: hidden;
}
.popular-airports {
  width: 1160px;
  margin: 48px auto 0;

  &__title {
    @include font-heading-m;
  }

  &__items {
    margin-top: 4px;
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
  }

  &__item {
    width: 275px;
    height: 116px;
    border: 1px solid $color-border-dim;
    padding: 20px;
    margin-top: 20px;
    margin-left: 20px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    border-radius: $radius-xl;
    position: relative;

    &:nth-child(4n+1) {
      margin-left: 0;
    }
  }

  &__name {
    @include font-body-m-bold;
    @include text-ellipsis(2);
  }

  &__price {
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
    align-content: center;

    em {
      @include font-caption-m-regular;
      color: $color-text-secondary;
      font-style: normal;
      align-self: center;
    }

    span {
      @include font-body-s-semibold;
      margin-left: 4px;
    }
  }

  &__link {
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
  }
}
</style>
