<template>
  <div v-if="initData && initData.items && initData.items.length" class="review-container">
    <h2 class="review-title">{{ title }}</h2>
    <div class="review-content">
      <div class="review">
        <div class="review_header">
          <div class="review_header-score">
            <span>{{ firstData.average_rating }}</span><em>/</em> 5
          </div>
          <div class="review_header-cont">
            <div class="desc">{{ firstData.score_intro }}</div>
            <div class="total-count">{{ $t('global.star.reviews', firstData.review_count) }}</div>
          </div>
        </div>
        <div class="review_body">
          <div class="swiper-wrapper swiper-no-swiping">
            <div
              v-for="(item, index) in firstData.items"
              :key="index"
              class="swiper-slide"
              @click="showMore = true"
            >
              <div class="item_header">
                <div v-lazy:background-image="getAvatarUrl(item.review.avatar)" class="item_header-avatar"></div>
                <div class="item_header-right">
                  <div class="right_name">{{ item.review.user_name || '--' }}</div>
                  <div class="right_date">{{ getStandarDateStr(new Date(item.review_time)) }}</div>
                </div>
              </div>
              <div class="item_review">
                <div class="item_review-score">
                  <span>{{ item.review.rating }}</span><em>/</em>5
                </div>
                <div class="item_review-desc">{{ item.score_intro }}</div>
              </div>
              <klk-poptip placement="top" :content="`${item.airport_name}(${item.airport_code})`" trigger="hover">
                <div class="item_from">{{ $t('11374-airport_transfer_review_name', [item.airport_name, item.airport_code]) }}</div>
              </klk-poptip>
              <div class="item_content">
                <div class="body_content">
                  {{ !item.showOrigin ? item.review.translate_content : item.review.content }}
                </div>
              </div>
            </div>
          </div>
          <div
            class="view-more"
            data-spm-module="Review_HP"
            v-galileo-click-tracker="galileoClick1"
            data-spm-virtual-item="__virtual?typ=entry"
            @click="showMore = true"
          >
            {{ $t('activity.v2.label.review.view_more.num', firstData.review_count) }}
          </div>
        </div>
        <div class="swiper-next">
          <svg-icon
            name="common#icon_navigation_chevron_right_xs"
            color="gray-800"
            size="16"
          ></svg-icon>
        </div>
        <div class="swiper-prev">
          <svg-icon
            name="common#icon_navigation_chevron_left_xs"
            color="gray-800"
            size="16"
          ></svg-icon>
        </div>
        <review-drawer
          :first-data="firstData"
          :is-logged-in="isLoggedIn"
          :show.sync="showMore"
          :api-config="apiConfig"
          :transfer-context="transferContext"
          :sourcePage="sourcePage"
        />
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Prop } from 'vue-property-decorator'
import Swiper from 'swiper'
import ReviewDrawer from './drawer.vue'
import { ITransferContext, SourcePage } from '../../../types/airport-transfer'
import { getStandardDateFormat } from '../../common/datetime'
import { formatAvatarUrl, ldjson } from '../../common/utils'
import Base from "@/common/base";
import { WebClick_121 } from '../../../share/galileo/auto-click'

@Component({
  components: {
    ReviewDrawer
  }
})
export default class ReviewDesktop extends Base {
  @Prop({ type: Object, default: () => ({ items: [] }) }) initData!: any
  @Prop({ type: Object, default: () => ({ path: null, qs: {} }) }) apiConfig!: any
  @Prop({ type: String, default: '' }) title!: string
  @Prop({ type: Boolean, default: () => false }) isLoggedIn!: boolean
  @Prop({ type: Object, default: null }) transferContext?: ITransferContext
  @Prop({ type: String, default: () => '' }) sourcePage!: SourcePage

  showMore: boolean = false
  firstData: any = this.initData
  showOrigin: boolean = false
  $swiper: any = null
  language: string = ''

  head() {
    if (this.initData) {
      const reviewRating = {
        '@context': 'https://schema.org',
        '@type': 'Product',
        name: this.initData.title,
        aggregateRating: {
          '@type': 'AggregateRating',
          ratingValue: this.initData.average_rating || '',
          bestRating: '5',
          worstRating: '1',
          reviewCount: this.initData.review_count || ''
        }
      }
      return {
        script: [ldjson(reviewRating)]
      }
    }
  }

  mounted() {
    this.language = this.getKlkLanguage()
    this.$nextTick(() => {
      this.$swiper = new Swiper('.review_body', {
        slidesPerView: 3,
        spaceBetween: 20.5,
        slidesPerGroup: 3,
        navigation: {
          nextEl: '.swiper-next',
          prevEl: '.swiper-prev'
        }
      })
    })
  }

  getStandarDateStr(date: Date) {
    return getStandardDateFormat(
      date,
      this.$t.bind(this),
      this.language,
      1
    )
  }

  needTranslate(translateLang: string, lang: string) {
    const format = (str: string) => str.replace(/[^a-zA-Z]*/g, '').toLowerCase()
    return format(translateLang || '') !== format(lang || '')
  }

  getAvatarUrl(avatar: string) {
    return formatAvatarUrl(avatar)
  }

  handleOrigin(index: number) {
    if (this.firstData && this.firstData.items[index]) {
      this.firstData.items[index].showOrigin = !this.firstData.items[index].showOrigin
    }
  }

    get galileoClick1() {
        return { spm: WebClick_121, autoTrackSpm: true }
    }
}
</script>

<style lang="scss" scoped>
.review-container{
  width: 1160px;
  margin: 86px auto 0 auto;

  .review-title {
    font-size: $fontSize-heading-m;
    font-weight: $fontWeight-bold;
    color: $color-text-primary;
  }

  .review-content {
    margin-top: 30px;
    display: flex;
  }
}

.review {
  width: 100%;
  position: relative;

  &_header {
    display: flex;
    align-items: center;

    &-score {
      color: $color-brand-secondary;
      @include font-body-m-bold;

      em {
        @include font-body-s-regular;
      }

      span {
        @include font-heading-xl;
      }
    }

    &-cont {
      display: flex;
      flex-direction: column;
      margin-left: 16px;

      .desc {
        @include font-heading-xs;
        color: $color-text-primary;
      }

      .total-count {
        @include font-caption-m-regular;
        color: $color-text-secondary;
      }
    }
  }

  &_body {
    margin-top: 37px;
    overflow: hidden;

    .swiper-wrapper {
      display: inline-flex;
    }

    .swiper-slide {
      width: 373px;
      box-sizing: border-box;
      padding: 16px;
      background-color: $color-bg-widget-normal;
      border-radius: $radius-l;
      margin-right: 20px;
      border: 1px solid $color-border-normal;
      cursor: pointer;

      .item_header {
        position: relative;
        display: flex;

        &-avatar {
          height: 36px;
          width: 36px;
          border-radius: $radius-circle;
          background: url('//cdn.klook.com/upload/img200X200/40ef9019--e-l.jpg') no-repeat;
          background-size: cover;
          margin-right: 12px;
          flex-shrink: 0;
        }

        &-right {
          .right_name {
            color: $color-text-primary;
            @include font-body-s-semibold;
          }

          .right_date {
            overflow: hidden;
            max-width: 260px;
            white-space: nowrap;
            text-overflow: ellipsis;
            color: $color-text-secondary;
            @include font-caption-m-regular;
          }
        }

        &-review-score {
          display: flex;
          align-items: center;
          position: absolute;
          right: 0;
          top: 0;

          .price {
            font-size: $fontSize-body-s;
            line-height: 16px;
            color: $color-brand-secondary;
          }

          .score {
            display: inline-block;
            margin-left: 6px;
            color: $color-text-primary;
            font-size: $fontSize-body-s;
            line-height: 16px;
          }
        }
      }

      .item_review {
        display: flex;
        flex-direction: row;
        align-items: center;
        margin-top: 12px;

        &-score {
          color: $color-brand-secondary;
          @include font-caption-s-semibold;
          position: relative;
          bottom: 1px;

          em {
            width: 7px;
            font-size: 8px;
            display: inline-block;
            text-align: center;
            line-height: $lineHeight-relaxed;
          }

          span {
            @include font-body-m-bold;
          }
        }

        &-desc {
          margin-left: 6px;
          color: $color-text-primary;
          @include font-body-s-semibold;
        }
      }

      .item_from {
        margin-top: 8px;
        color: $color-text-secondary;
        @include font-caption-m-regular;
      }

      .item_content {
        height: 80px;
        word-break: break-all;
        margin-top: 12px;
        box-sizing: border-box;

        .body_content {
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 4;//表示行数
          -webkit-box-orient: vertical;
          @include font-body-s-regular;
        }
      }
    }

    .view-more {
      width: 300px;
      height: 36px;
      line-height: 36px;
      margin: 32px auto 0;
      text-align: center;
      border: 1px solid $color-border-active;
      border-radius: $radius-m;
      font-size: $fontSize-body-s;
      font-weight: $fontWeight-semibold;
      cursor: pointer;
    }
  }

  .swiper-next,
  .swiper-prev {
    background: $color-bg-widget-normal;
    border: 1px solid $color-border-normal;
    /* stylelint-disable */
    border-radius: 100px;
    /* stylelint-enable */
    height: 32px;
    width: 32px;
    position: absolute;
    top: calc(50% + 10px);
    transform: translateY(-50%);
    right: -42px;
    display: flex;
    align-items: center;
    justify-content: center;
    outline: 0;
    cursor: pointer;
    font-size: 16px;

    svg {
      color: #4a4a4a;
    }
  }

  .swiper-prev {
    left: -42px;
  }
}

.swiper-button-disabled {
  display: none !important;
}
</style>
