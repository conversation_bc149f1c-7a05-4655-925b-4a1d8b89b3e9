<template>
  <div class="item">
    <div class="item_score">
      <span>{{ renderData.score_intro }}</span>
      <span>{{ renderData.review.rating }}</span>
    </div>
    <div v-lazy:background-image="getAvatarUrl(renderData.review.avatar)" class="item_avatar" />
    <div class="item_right">
      <div class="item_author">{{ renderData.review.user_name || '--' }}</div>
      <div class="item_date">{{ $t('11366-airport_transfer_review_date') }} {{ getStandarDateStr(new Date(renderData.review_time)) }}</div>
      <div class="item_detail">{{ $t('11363-airport_transfer_review_from', [renderData.airport_name, renderData.airport_code]) }}</div>
      <div class="item_main">
        {{ !showOrigin ? renderData.review.translate_content : renderData.review.content }}
      </div>
      <p v-if="needTranslateButton" class="item_btn" @click="showOrigin = !showOrigin">
        {{ showOrigin ? $t('activity.v2.translate.btn') : $t('activity.v2.translate.show.original') }}
      </p>
      <!-- 图片展示，功能已移除 -->
      <!-- <div class="item_imgs">
        <div
          v-for="(img,i) in renderData.review.review_image_list"
          :key="i"
          v-lazy:background-image="img.image_resize_url"
          class="item_imgs-item"
          @click="handleImgClick(i)"
        ></div>
      </div>
      <div v-if="renderData.showSwiper" class="imgSwipe">
        <div class="swiper-container">
          <div class="swiper-wrapper">
            <div v-for="(img,i) in renderData.review.review_image_list" :key="i" class="swiper-slide">
              <div @click="handleZoom">
                <img :src="img.image_url">
              </div>
            </div>
          </div>
          <div class="swiper-button-next">
            <svg-icon name="desktop-private-transfer#icon_arrow2" size="12"></svg-icon>
          </div>
          <div class="swiper-button-prev">
            <svg-icon name="desktop-private-transfer#icon_arrow2" size="12"></svg-icon>
          </div>
        </div>
      </div> -->
      <div class="item_footer">
        <svg-icon v-if="renderData.review.has_liked" class="icon-like icon-like--active" name="mobile-common#icon-review-like" size="15"></svg-icon>
        <div v-if="!renderData.review.has_liked" class="like_btn" @click="handleLike">
          <svg-icon class="icon-like" name="mobile-common#icon-review-like" size="15"></svg-icon>
          <span>{{ $t('my_reviews.helpful_text') }}</span>
        </div>
        <span v-if="renderData.review.like_count > 0" class="like_text" :class="{like: !renderData.review.has_liked}" v-html="$t('my_reviews.helpful_with_num', renderData.review.like_count )"></span>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Prop, State } from 'vue-property-decorator'
import Swiper from 'swiper'
import { getStandardDateFormat } from '../../common/datetime'
import { formatAvatarUrl } from '../../common/utils'
import Base from "@/common/base";

@Component({
  components: {}
})
export default class ReviewDesktop extends Base {
  @Prop({ type: Object, default: () => ({ review: { review_image_list: [] } }) }) info!: any
  @Prop({ type: Boolean, default: () => false }) isLoggedIn!: boolean

  renderData: any = { ...this.info, showSwiper: false }
  $swiper: any = null
  showOrigin: boolean = false
  language:string = ''

  get needTranslateButton() {
    return this.renderData?.review?.need_translate
  }

  mounted() {
    this.language = this.getKlkLanguage()
  }

  getStandarDateStr(date: Date) {
    return getStandardDateFormat(
      date,
      this.$t.bind(this),
      this.language,
      1
    )
  }

  handleZoom() {
    this.renderData.showSwiper = false
  }

  handleImgClick(index: number) {
    this.renderData.showSwiper = true
    this.initSwiper(index)
  }

  initSwiper(index: number) {
    this.$nextTick(() => {
      this.$swiper = new Swiper('.swiper-container', {
        initialSlide: index,
        navigation: {
          nextEl: '.swiper-button-next',
          prevEl: '.swiper-button-prev'
        }
      })
    })
  }

  getAvatarUrl(avatar: string) {
    return formatAvatarUrl(avatar)
  }

  handleLike() {
    if (this.isLoggedIn) {
      const qs = `review_id=${this.renderData.review.review_id}`
      const headers = { 'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8' }
      this._axios.$post('/v1/usrcsrv/review/like/add', qs, { headers }).then((res: Data.Res) => {
        if (res.success) {
          this.renderData.review.has_liked = true
          this.renderData.review.like_count++
        } else if (res.error.code === '4001') {
          window.location.href = `${this.language !== 'en' ? `/${this.language}` : ''}/signin?signin_jump=${encodeURIComponent(window.location.pathname)}`
        }
      })
    } else {
      window.location.href = `${this.language !== 'en' ? `/${this.language}` : ''}/signin?signin_jump=${encodeURIComponent(window.location.pathname)}`
    }
  }
}
</script>

<style lang="scss" scoped>
.item {
  margin-bottom: 22px;
  display: flex;
  position: relative;

  &:last-of-type {
    .item_right {
      border-bottom: none;
    }
  }

  &_right {
    margin-left: 24px;
    width: 506px;
    border-bottom: 1px solid $color-border-normal;

    .item_author {
      display: flex;
      align-items: center;
      font-weight: $fontWeight-semibold;
      font-size: $fontSize-body-s;
      line-height: 21px;
      color: $color-text-primary;
    }

    .item_date {
      font-size: $fontSize-body-s;
      line-height: 21px;
      color: $color-text-secondary;
    }

    .item_detail {
      font-size: $fontSize-body-s;
      line-height: 16px;
      color: $color-text-secondary;
    }

    .item_main {
      margin-top: 11px;
      font-size: $fontSize-body-s;
      line-height: 21px;
      color: $color-text-primary;
      word-break: break-all;
    }

    .item_btn {
      font-size: $fontSize-body-s;
      color: $color-info;
      margin-top: 8px;
      cursor: pointer;
    }

    .item_imgs {
      display: flex;
      margin-top: 12px;
      flex-wrap: wrap;

      &-item {
        height: 76px;
        width: 76px;
        background-color: $color-bg-widget-darker-3;
        margin-bottom: 8px;
        margin-right: 8px;
        background-size: cover;
        cursor: zoom-in;

        &:nth-of-type(6n) {
          margin-right: 0;
        }
      }
    }

    .imgSwipe {
      width: 100%;
      margin-top: 24px;
      height: 382px;
      background-color: $color-common-black;

      .swiper-container {
        overflow: hidden;
        width: 100%;
        height: 100%;
        position: relative;
      }

      .swiper-wrapper {
        display: flex;
        width: 100%;
      }

      .swiper-button-prev {
        left: 0;
        transform: rotate(180deg);
      }

      .swiper-button-disabled {
        display: none !important;
      }

      .swiper-button-prev,
      .swiper-button-next {
        height: 382px;
        width: 70px;
        position: absolute;
        right: 0;
        top: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        background-image: none;

        .svg-icon {
          height: 40px;
          width: 40px;
          color: $color-text-primary-onDark;
          opacity: $opacity-overlay-mobile;
          position: absolute;
          left: 50%;
          top: 50%;
          transform: translate(-50%, -50%);
        }
      }

      .swiper-slide {
        > div {
          width: 507px;
          display: flex;
          justify-content: center;
          align-items: center;
          cursor: zoom-out;
        }

        img {
          height: 382px;
          width: auto;
          max-width: 100%;
        }
      }
    }
  }

  &_score {
    position: absolute;
    right: 0;
    top: 3.5px;
    color: $color-text-primary;
    font-size: $fontSize-body-s;
  }

  &_avatar {
    flex-shrink: 0;
    width: 50px;
    height: 50px;
    border-radius: $radius-circle;
    background: $color-bg-widget-darker-3;
    background-size: cover;
    background-position: center;
  }

  &_footer {
    margin-top: 24px;
    display: flex;
    padding-bottom: 24px;
    .icon-like {
      color: $color-text-primary;
    }

    .icon-like--active {
      color: $color-brand-primary;
      margin-right: 8px;
    }

    .like_btn {
      border: 1px solid $color-border-normal;
      padding: 8px 12px;
      display: flex;
      cursor: pointer;

      span {
        margin-left: 8px;
      }
    }

    .like_text {
      &.like {
        display: flex;
        align-items: center;
        margin-left: 12px;
      }
    }
  }
}
</style>
<style>
.like_text i.like_num {
  font-style: normal;
  margin-right: 5px;
}
</style>
