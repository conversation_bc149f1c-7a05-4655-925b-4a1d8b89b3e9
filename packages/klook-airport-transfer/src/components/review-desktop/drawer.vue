<template>
  <klk-drawer :visible="show" direction="right" @close="close">
    <div v-if="show" data-spm-page="AirportTransfer_Review_Detail" ref="drawerBody" class="drawer">
      <div ref="close" class="close-wrapper" @click="close">
        <svg-icon class="icon" name="common#icon-close" size="22"></svg-icon>
        <span class="close">{{ $t('close') }}</span>
      </div>

      <div class="score">
        <span>{{ detail.average_rating || 0 }}</span>
        <span>/</span>
        <span>5</span>
        <span>{{ detail.score_intro }}</span>
      </div>
      <div class="all" v-html="$t('global.star.reviews', detail.review_count || 0)"></div>
      <div class="sub-review" v-if="detail.sub_review_list && detail.sub_review_list.length">
        <review-category
          v-for="subItem in detail.sub_review_list"
          :key="subItem.sub_review_rule_id"
          class="sub-review-item"
          :category="subItem.display_title"
          :rating="Number(subItem.average_rating)"
          :total="5"
        />
      </div>
      <div class="review-filter">
        <div
          class="review-filter-sort"
          v-if="sortData && sortData.length"
        >
          <span class="review-filter-sort-title">
            {{ $t('search_sort_by') }}
          </span>
          <klk-dropdown
            placement="bottom-start"
            class="review-filter-sort-dropdown"
            :checkable="true"
            @click="changeSort"
            v-model="sortType"
          >
            <div  class="review-filter-sort-option">
              {{ sortOption }} <IconTriangleDown class="review-filter-sort-icon" theme="filled" size="16" fill="#333333" />
            </div>
            <klk-dropdown-menu slot="list">
              <klk-dropdown-item v-for="(item) in sortData" :key="item.sort_type" :name="item.sort_type">
                <span>{{ item.show_text }}</span>
              </klk-dropdown-item>
            </klk-dropdown-menu>
          </klk-dropdown>
        </div>
        <div class="btns">
          <div
            v-for="(item, index) in filter"
            :key="index"
            :data-spm-module="`Review_Filter_LIST?oid=TagType_${item.tag_type}&idx=${index}&len=${filter.length}`"
            v-galileo-click-tracker="galileoClick1"
            data-spm-virtual-item="__virtual?typ=entry"
            class="btns-item"
          >
            <span v-if="item.show_tag" class="btns-filter" :class="item.selected && 'active'" @click="handleFilter(index)">{{ item.tag_text }}</span>
          </div>
        </div>
      </div>
      <div class="content" @scroll="handleScroll">
        <template v-if="showLoading && currentPage === 1">
          <klk-loading />
        </template>
        <template v-else>
          <template v-if="detail.items && detail.items.length > 0">
            <review-item v-for="(item, index) in detail.items" :key="index" :info="item" :is-logged-in="isLoggedIn" />
            <div class="no-more" v-if="!detail.has_more">{{ $t('14258') }}</div>
          </template>
          <template v-else>
            <div class="error-content">
              <Empty></Empty>
              <div class="error-tip">{{ $t('48169-empty') }}</div>
              <div class="review-reset-filter" @click="resetFilter">{{ $t('48052') }}</div>
            </div>
          </template>
        </template>
      </div>
    </div>
  </klk-drawer>
</template>

<script lang="ts">
import { Component, Prop, Watch } from 'vue-property-decorator'
import { IconTriangleDown } from '@klook/klook-icons'
import ReviewItem from './item.vue'
import Base from '../../common/base'
import { ITransferContext, SourcePage, IFilter } from '../../../types/airport-transfer'
import ReviewCategory from '../review-category/index.vue'
import Empty from '../svg/empty.vue'
import { WebClick_131 } from '../../../share/galileo/auto-click'

@Component({
  components: {
    ReviewItem,
    ReviewCategory,
    Empty,
    IconTriangleDown
  }
})
export default class Draweresktop extends Base {
  @Prop({ type: Boolean, default: false }) show!: boolean
  @Prop({ type: String, default: () => '' }) sourcePage!: SourcePage
  @Prop({ type: Object, default: () => ({ path: null, qs: { size: 10 } }) }) apiConfig!: any
  @Prop({ type: Boolean, default: () => false }) isLoggedIn!: boolean
  @Prop({ type: Object, default: () => {} }) firstData!: any
  @Prop({ type: Object, default: null }) transferContext?: ITransferContext

  sortType: number = 1

  timer: any = null
  showLoading: boolean = true
  currentPage: number = 1
  isLoadAll: boolean = false
  sortData: any[] = []
  detail: any = {
    average_rating: this.firstData?.average_rating || '-', // 平均分,
    review_count: this.firstData?.review_count || '-', // 评价总数,
    score_intro: this.firstData?.score_intro, // 好评坏评
    items: []
  }

  filter: IFilter[] = []

  isReset = false // 是否是通过重置的方式回到首屏（fix：解决触发滚动导致连续请求两页数据的问题）

  get sortOption() {
    return this.sortData.find(item => item.sort_type === this.sortType)?.show_text
  }

  initFilterAndSort() {
    this._axios.$get('/v2/transferairportapisrv/reviews/list/option/', {
      params: {
        origin: this.sourcePage,
        ...this.transferContext
      }
    }).then((res: any) => {
      if (res?.success && res.result) {
        this.sortData = res.result.sort_list || []
        this.filter = res.result.tag_list.map((tag: any) => {
          return {
            ...tag,
            selected: !tag.show_tag // 默认隐藏的tag置为选中状态，否则置为非选中
          }
        }) || []
        // 获取筛选器成功后再获取数据
        this.getData()
      }
    }).catch((err: any) => {
      console.error('get filter failed' + err?.message)
    })
  }

  @Watch('show')
  showChange(val: string) {
    document.body.style.overflow = val ? 'hidden' : 'auto'
    if (val) {
      this.sortType = 1 // fix：重置sortype，触发组件的高亮事件
      this.resetQS()
      this.initFilterAndSort()
    }
  }

  getData() {
    if (this.isLoadAll) { return }
    const filterType = this.filter.filter((tag: any) => {
      return tag.selected
    }).map((tag: any) => {
      return {
        tagType: tag.tag_type,
        tagValue: tag.tag_value
      }
    })
    this.showLoading = true
    this._axios.$get(this.apiConfig.path + `/?req=${encodeURIComponent(JSON.stringify({
      ...this.apiConfig.qs,
      tagList: filterType,
      page: this.currentPage,
      sortType: this.sortType
    }))}`).then((res: Data.Res) => {
      if (res.success && res.result) {
        const { items } = res.result
        if (this.currentPage > 1) {
          this.detail = {
            ...res.result,
            items: [...this.detail.items, ...items]
          }
        } else {
          this.detail = res.result
        }
        if (!res.result.has_more) {
          this.isLoadAll = true
        }
      }
    }).catch((e: any) => {
      throw (e)
    }).finally(() => {
      this.showLoading = false
      if (this.isReset) { // 在重置数据请求完成之后解锁滚动加载
        this.isReset = false
      }
    })
  }

  handleFilter(index: number) {
    this.$set(this.filter[index], 'selected', !this.filter[index].selected)
    this.resetPage()
    this.getData()
  }

  changeSort(sort: any) {
    this.sortType = sort
    this.resetPage()
    this.getData()
  }

  handleScroll(e: any) {
    // fix bug重置条件时回滚到第一屏，不再触发scroll的搜索
    if (this.isReset) {
      return
    }
    const { scrollTop, scrollHeight, offsetHeight } = e.target
    if (scrollTop + offsetHeight >= scrollHeight - 100) {
      this.$once('beforeDestroy', () => clearTimeout(this.timer))
      if (!this.timer) { 
        this.currentPage++
        this.getData()
      }
      this.timer = setTimeout(() => {
        this.timer = null
        clearTimeout(this.timer)
      }, 500)
    }
  }

  close() {
    this.sortType = 0 // fix：重置sortype，触发组件的高亮事件
    this.$emit('update:show', false)
  }

  initFilter() {
    this.filter = this.filter.map((tag: any) => {
      return {
        ...tag,
        selected: !tag.show_tag // 默认隐藏的tag置为选中状态，否则置为非选中
      }
    })
  }

  resetFilter() {
    this.resetQS()
    this.getData()
  }

  resetQS() {
    this.resetPage()
    this.initFilter()
    this.sortType = 1
  }

  resetPage() {
    this.isReset = true
    this.currentPage = 1
    this.isLoadAll = false
    this.detail = {
      ...this.detail,
      items: []
    }
  }

    get galileoClick1() {
        return { spm: WebClick_131, autoTrackSpm: true }
    }
}
</script>

<style lang="scss" scoped>
.drawer {
  width: 690px;
  padding: 0 0 0 50px;

  .close-wrapper {
    display: flex;
    align-items: center;
    position: absolute;
    left: 50px;
    top: 24px;
    cursor: pointer;

    i {
      display: inline-block;
      width: 16px;
      height: 16px;
      color: $color-text-secondary;
    }

    .close {
      display: inline-block;
      margin-left: 8px;
      font-size: $fontSize-body-s;
      line-height: 17px;
      color: $color-text-secondary;
      text-transform: uppercase;
      font-family: HelveticaNeue;
    }
  }

  .score {
    margin-top: 78px;

    span {
      color: $color-text-primary;
      font-size: $fontSize-body-s;
      font-weight: $fontWeight-semibold;

      &:first-child {
        font-size: $fontSize-heading-s;
      }

      &:last-of-type {
        font-size: $fontSize-body-m;
        margin-left: 5px;
      }
    }
  }

  .all {
    color: $color-text-primary;
    font-size: $fontSize-body-s;
    margin-top: 27px;
  }

  .sub-review {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    margin-top: 25px;
    &-item {
      width: 229px;
      margin-right: 80px;
      margin-bottom: 4px;
    }
  }

  .review-filter {
    padding-bottom: 22px;
    margin-top: 29px;
    padding-right: 56px;
    .btns {
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      padding-top: 16px;

      &-item {
        margin: 10px 0;
      }

      &-filter {
        cursor: pointer;
        background: $color-bg-page;
        /* stylelint-disable */
        border-radius: 16px;
        /* stylelint-enable */
        font-size: $fontSize-body-s;
        color: $color-text-primary;
        padding: 5px 22px;
        margin: 0 12px 10px 0;

        &.active {
          background-color: $color-brand-primary;
          color: $color-text-primary-onDark;
        }
      }
    }

    .review-filter-sort {
      display: inline-block;
      &-option {
        display: flex;
        align-items: center;
      }

      &-title {
        @include font-body-s-regular;
        color: $color-text-secondary;
      }

      &-text {
        margin-left: 4px;
        @include font-body-s-regular;
        color: $color-text-primary;
      }

      &-icon {
        margin-left: 8px;
      }
    }
  }

  .content {
    height: calc(100vh - 260px);
    overflow: auto;
    padding-right: 56px;

    .klook-symbol {
      height: 50px;
      width: 50px;
      color: $color-brand-primary;
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
      animation: rotate 0.5s linear infinite;
    }

    .no-more {
      @include font-body-m-regular;
      text-align: center;
      color: $color-text-placeholder;
      margin: 26px 0;
    }

    .error-content {
      text-align: center;
      .error-pic {
        display: block;
        margin: 0 auto;
      }

      .error-tip {
        @include font-body-s-regular;
        margin-top: 16px;
        text-align: center;
        color: $color-text-primary;
      }

      .review-reset-filter {
        @include font-body-s-regular;
        margin: 0 auto;
        margin-top: 20px;
        color: $color-text-link;
        cursor: pointer;
      }
    }
  }
}

@keyframes fade {
  from {
    opacity: $opacity-transparent;
  }

  to {
    opacity: $opacity-solid;
  }
}

@keyframes rotate {
  from {
    transform: translate(-50%, -50%) rotate(0);
  }

  to {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}
.slide-enter-active {
  transition: all 0.2s ease-in;
}

.slide-leave-active {
  transition: all 0.2s cubic-bezier(1, 0.5, 0.8, 1);
}

.slide-enter,
.slide-leave-to {
  transform: translateX(600px);
  opacity: $opacity-transparent;
}
</style>
