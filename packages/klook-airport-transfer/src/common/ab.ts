import { Component, Vue } from 'vue-property-decorator'
import { checkExperimentHit } from './utils'

@Component
export default class AbExperiment extends Vue {
  getKepler() {
    return window?.__KLOOK__?.state?.common?.kepler || {}
  }

  isHitAbExperiment(experimentsGroupName: string, experimentName: string) {
    const checkExperimentFn: Function = checkExperimentHit(this.getKepler().experimentsGroup, experimentsGroupName)
    return checkExperimentFn(experimentName)
  }
}
