import { Vue, Component } from 'vue-property-decorator'
import localStorageEx from './localstorage'
import uuid from 'uuid/v4'

const l = localStorageEx()
const TRANSFER_SESSION_TOKEN = 'transfer_sessiontoken'
const EXPIRE_TIME = 1000 * 60 * 5
@Component
export default class Base extends Vue {

  $t!: Function
  $toast!: Function
  $sendGTMCustomEvent!: Function
  $route!: any
  _axios!: any
  $inhouse!: any
  transfer_sessionToken: string = ''
  beforeMount() {
    if (l.getItem(TRANSFER_SESSION_TOKEN)) {
      this.transfer_sessionToken = l.getItem(TRANSFER_SESSION_TOKEN)
    } else {
      this.transfer_sessionToken = uuid()
      l.setItem(TRANSFER_SESSION_TOKEN, uuid(), EXPIRE_TIME)
    }
    this._axios = this.$attrs.axios || window.$axios
    this.$inhouse = window.tracker.inhouse
  }

  getKlkHost() {
    return window?.__KLOOK__?.state?.klook?.host || 'www.klook.com'
  }

  getKlkLanguage() {
    return window?.__KLOOK__?.state?.klook?.language || 'en-US'
  }

  getKlkPlatform() {
    return window?.__KLOOK__?.state?.klook?.platform || 'desktop'
  }
}
