import { Component, Prop } from 'vue-property-decorator'
import Base from '../base'

@Component({})
export default class SearchPageBase extends Base {
  @Prop({ type: Object, default: () => {} }) searchHistory?: any
  @Prop({ type: Boolean, default: true }) isPickTab!: boolean
  @Prop({ type: String, default: '' }) arrivalOrDeparture!: string
  @Prop({ type: Boolean, default: false }) hideExtraInfo?: boolean // 是否展示搜索历史和附近机场
  @Prop({ type: Boolean, default: false }) hideHotAirport?: boolean // 是否展示热门机场
  locationBtnStatus = 'loading'
  currentCityAirport: any[] = []

  get searchHistoryByType() {
    if (this.arrivalOrDeparture) {
      return this.searchHistory && Object.keys(this.searchHistory).length ? this.searchHistory[this.arrivalOrDeparture] : []
    }
    return this.searchHistory && Object.keys(this.searchHistory).length ? this.searchHistory[this.isPickTab ? 'pick' : 'drop'] : []
  }

  get isDeparture() {
    return this.arrivalOrDeparture === 'departure'
  }

  get locationBtn() {
    return {
      loading: {
        text: this.$t('17938-loading'),
        handler: () => {}
      },
      failed: {
        text: this.$t('loading_failed'),
        handler: this.initLocation
      },
      empty: {
        text: this.$t('192366-empty'),
        handler: this.initLocation
      },
      unenabled: {
        text: this.$t('192365-nopermission'),
        handler: () => {}
      }
    }
  }

  initLocation() {
    this.locationBtnStatus = 'loading'
    if (!(navigator && navigator.permissions)) {
      this.locationBtnStatus = 'unenabled'
      return
    }
    navigator.permissions.query({ name: 'geolocation' }).then((permissionsStatus) => {
      if (permissionsStatus.state === 'granted' || permissionsStatus.state === 'prompt') {
        // 有权限 / 申请权限
        navigator.geolocation.getCurrentPosition((position) => {
          this.searchLocation(position.coords.latitude, position.coords.longitude)
        }, () => {
          this.searchLocation()
        }, {
          maximumAge: 60000,
          timeout: 5000,
          enableHighAccuracy: false
        })
      } else if (permissionsStatus.state === 'denied') {
        // 无权限
        this.locationBtnStatus = 'unenabled'
      }
    })
  }

  searchLocation(lat?: number, lon?: number) {
    if (!lat || !lon) {
      this.locationBtnStatus = 'failed'
      return
    }
    this.locationBtnStatus = 'loading'
    this.currentCityAirport = []
    this._axios.get('/v1/transferairportapisrv/airport/get_city_airport', {
      params: {
        latitude: lat,
        longitude: lon
      }
    }).then(({ data }: Data.Res) => {
      if (data?.success) {
        this.currentCityAirport = data.result.list
        if (data.result.list.length) {
          this.locationBtnStatus = 'success'
        } else {
          this.locationBtnStatus = 'empty'
        }
      } else {
        this.currentCityAirport = []
        this.locationBtnStatus = 'failed'
      }
    }).catch(() => {
      this.currentCityAirport = []
      this.locationBtnStatus = 'failed'
    })
  }

  clearHistory() {
    this.$emit('clearHistory')
  }
}
