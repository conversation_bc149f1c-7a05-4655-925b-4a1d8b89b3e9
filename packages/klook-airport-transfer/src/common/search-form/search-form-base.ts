import { Prop, Component, Mixins } from 'vue-property-decorator'
import dayjs from 'dayjs'
import { IconWarnCircle } from '@klook/klook-icons'
import localStorageEx from '../localstorage'
import Base from '../base'
import { getStandardDateFormat } from '../datetime'
import AB from '../ab'

const HISTORY_CACHE_KEY_PREFIX = 'pat_airport_search_history' // 机场搜索缓存key
const EXPIRE_TIME = 365 * 12 * 60 * 60 * 1000

type pickType = 'byFlight' | 'byFixTime'
interface airportHistory {
  airportName: string,
  iataCode: string,
  longitude: string,
  latitude: string,
  isActive: string,
  backupKlookCityId: number
}

interface searchHistory {
  pick?: airportHistory[],
  drop?: airportHistory[]
}

interface urlParams extends Object {
  flightDirection: number,
  from: string,
  to: string,
  address: string,
  lat: string,
  long: string,
  poiId: string,
  poi_lat: string,
  poi_long: string,
  date: any,
  time: string,
  pas: number,
  code: string,
  place: string,
  ac: string,
  kcid: string
}

interface formData extends Object {
  from: string,
  to: string,
  date: string,
  time: string,
  passenger: number
}

enum Type {
  PICK = 'pick',
  DROP = 'drop'
}

interface searchAirportObject extends Object {
  searchWord: string
}

interface IFlightInfo {
  hasFlight: boolean,
  searchType: 'byFlightNo' | 'byAirline',
  flightNo: string,
  arrivalDate: Date | '',
  arrivalTime: string,
  departureDate?: Date | '', // 航班出发时间
  departureTime?: string,
  iataCode: string, // 到达机场三字码
  airportName: string, // 到达机场名
  departureName: string, // 出发机场名
  originCode: string, // 出发机场三字码
  latitude: string,
  longitude: string,
  isActive: string,
  backupKlookCityId: string,
  airlineName?: string,
  source?: 'history'
}

const searchApi = {
  searchAirport: 'v1/transferairportapisrv/airport/autocomplete',
  searchByFlightNo: '/v1/transferairportapisrv/flight/search_new' // 按航班号搜索航班
}

@Component({
  components: {
    IconWarnCircle
  }
})
export default class SearchFormBase extends Mixins(Base, AB) {
  @Prop({ type: String, default: 'home' }) currentPage!: string
  searchWord: string = ''
  isPickTab: boolean = true
  searchHistory: searchHistory = {
    pick: [],
    drop: []
  }

  // 接机数据
  pick: urlParams = {
    flightDirection: 1,
    from: '',
    to: '',
    address: '',
    lat: '',
    long: '',
    poiId: '',
    poi_lat: '',
    poi_long: '',
    date: '',
    time: '',
    pas: 2,
    code: '',
    place: '',
    ac: '',
    kcid: ''
  }

  // 送机数据
  drop: urlParams = {
    flightDirection: 2,
    from: '',
    to: '',
    address: '',
    lat: '',
    long: '',
    poiId: '',
    poi_lat: '',
    poi_long: '',
    date: '',
    time: '',
    pas: 2,
    code: '',
    place: '',
    ac: '',
    kcid: ''
  }

  // 航班号前置数据
  showPickType: boolean = false // 是否展示切换接机搜索方式的tab
  curPickType: pickType = 'byFixTime'
  showSearchByFlightModal: boolean = false // 是否展示按航班号搜索弹层
  flightInfo: IFlightInfo = {
    hasFlight: false, // 判断是否选到了航班
    searchType: 'byFlightNo',
    flightNo: '',
    iataCode: '',
    arrivalDate: '',
    arrivalTime: '',
    airportName: '',
    departureName: '',
    originCode: '',
    latitude: '',
    longitude: '',
    isActive: '',
    backupKlookCityId: ''
  }

  // 表单数据
  formData: formData = {
    from: '',
    to: '',
    date: '',
    time: '',
    passenger: 2
  }

  // 表单验证字段
  formValid: any = {
    from: {
      show: false,
      content: 'Please select your from',
      error: false
    },
    flightNo: {
      show: false,
      content: 'Please select your flight',
      error: false
    },
    to: {
      show: false,
      content: 'Please select your to',
      error: false
    },
    date: {
      show: false,
      content: 'Please select your date',
      error: false
    },
    time: {
      show: false,
      content: 'Please select your date',
      error: false
    },
    passenger: {
      show: false,
      content: 'Please select your time',
      error: false
    }
  }

  // 获取缓存key
  get historyCacheKey() {
    return `${HISTORY_CACHE_KEY_PREFIX}_${this.getKlkLanguage()}`
  }

  // 当前是接机还是送机
  get type() {
    return this.isPickTab ? Type.PICK : Type.DROP
  }

  getSearchAirport({ searchWord }: any) {
    return this._axios
      .$get(searchApi.searchAirport, { params: { searchWord } })
      .then((res: Data.Res) => {
        return Promise.resolve(res)
      })
      .catch((error: any) => {
        return Promise.reject(error)
      })
  }

  // get airport by iata
  async getAirportByIata(searchWord: string): Promise<any> {
    try {
      const response = await this.getSearchAirport({ searchWord })
      return response // 返回接口数据
    } catch (error) {
      console.error('Error fetching airport data:', error)
      return null
    }
  }

  get isDropTab() {
    return !this.isPickTab
  }

  get flightNoCacheKey() {
    return `${HISTORY_CACHE_KEY_PREFIX}_flightNo_${this.getKlkLanguage()}`
  }

  get hitPickTypeExperiment() {
    // 本地环境经常拿不到实验组，直接默认本地环境命中实验
    // TODO
    return this.isHitAbExperiment('PAT_Flight_No_Default_Landing', 'Variant Group') || process.env.APP_ENV === 'development'
  }

  get hitShowPickTypeExperiment() {
    // 本地环境经常拿不到实验组，直接默认本地环境命中实验
    // TODO
    return this.isHitAbExperiment('PAT_Flight_No', 'Variant Group') || process.env.APP_ENV === 'development'
  }

  get isByFlight() {
    return this.curPickType === 'byFlight' && this.isPickTab && this.showPickType
  }

  // 计算按航班号搜索时出发地的展示文案
  get fromPlaceByFlight() {
    if (this.flightInfo.hasFlight) {
      return `${this.flightInfo.airportName}(${this.flightInfo.iataCode})`
    } else {
      return ''
    }
  }

  // 计算按航班号搜索时出发地接送时间的展示文案
  get pickUpTimeByflight() {
    if (this.flightInfo.hasFlight) {
      return this.$t('203802', { pickup_time: `${this.formatDate(this.flightInfo.arrivalDate as Date)} ${this.flightInfo.arrivalTime}`, Flight_No: this.flightInfo.flightNo })
    } else {
      return ''
    }
  }


  // 是否是樱花季产品卡片过来
  get isSearchByCode() {
    const query: any = this.$route.query
    // 有航班信息就不用code查询接口
    return (this.isPickTab && !query.from) || (this.isDropTab && !query.to)
  }

  // 缓存历史数据
  saveHistory() {
    try {
      const l = localStorageEx()
      // 存储航班号信息，用于首页进行默认填充
      if (this.isByFlight) {
        const flightInfo = {
          ...this.flightInfo,
          arrivalDate: dayjs(this.flightInfo.arrivalDate).format('YYYY-MM-DD'),
          departureDate: this.flightInfo.departureDate ? dayjs(this.flightInfo.departureDate).format('YYYY-MM-DD') : null,
          departureTime: this.flightInfo.departureTime ? this.flightInfo.departureTime : ''
        }
        l.setItem(this.flightNoCacheKey, JSON.stringify(flightInfo), EXPIRE_TIME)
      }
      // 存储机场信息
      const { from, to, code, long, lat, ac, kcid, originCode, departureName } = this[this.type]
      const _from = from.match(/(.+?)(?=\()/)?.[0] || from
      const _to = to.match(/(.+?)(?=\()/)?.[0] || to
      const data = {
        airportName: this.isPickTab ? _from : _to,
        iataCode: code,
        originCode,
        departureName,
        longitude: long,
        latitude: lat,
        isActive: ac,
        backupKlookCityId: kcid
      }
      let cacheData = l.getItem(this.historyCacheKey)
      cacheData = cacheData ? JSON.parse(cacheData) : { pick: [], drop: [] }
      const curTypeCacheData = cacheData[this.type] || []
      const index = curTypeCacheData.findIndex((item: any) => item.iataCode === data.iataCode)
      // 去重并调整顺序
      if (index > -1) {
        curTypeCacheData.splice(index, 1)
      }
      curTypeCacheData.unshift(data)
      curTypeCacheData.splice(5)
      l.setItem(this.historyCacheKey, JSON.stringify(cacheData), EXPIRE_TIME)
      this.getSearchHistory()
    } catch (e) {
      console.error(e)
    }
  }

  getSearchHistory() {
    try {
      const l = localStorageEx()
      let cacheData = l.getItem(this.historyCacheKey)
      cacheData = cacheData ? JSON.parse(cacheData) : { pick: [], drop: [] }
      this.searchHistory = cacheData
    } catch (e) {
      console.error(e)
      this.searchHistory = {}
    }
  }

  // 清除历史记录
  clearHistory() {
    try {
      const l = localStorageEx()
      let cacheData = l.getItem(this.historyCacheKey)
      cacheData = cacheData ? JSON.parse(cacheData) : { pick: [], drop: [] }
      cacheData[this.type] = []
      l.setItem(this.historyCacheKey, JSON.stringify(cacheData), EXPIRE_TIME)
      this.searchHistory = cacheData
    } catch (e) {
    }
  }

  // 时间多语言格式化方法
  formatDate(date: Date) {
    const type = 3
    const language = this.getKlkLanguage()
    return getStandardDateFormat(date, this.$t.bind(this), language, type)
  }

  // 更新表单数据
  updateFormData(formData?: urlParams) {
    const data = formData || this[this.type]
    const date = data.date ? this.formatDate(data.date) : ''
    this.formData = {
      from: data.from,
      to: data.to,
      date,
      time: data.time,
      passenger: data.pas
    }
    this.deleteFieldError()
  }

  // 填充表单后，去除表单报错
  deleteFieldError() {
    let key: string
    for (key in this.formValid) {
      if (this.formData[key]) {
        this.formValid[key].error = false
      }
    }
  }

  // 去除表单报错
  deleteFormError() {
    let key: string
    for (key in this.formValid) {
      this.formValid[key].error = false
    }
  }

  // 联动public列表
  getPublicList(info: any) {
    let params = {
    }
    if (this.getKlkPlatform() === 'mobile') {
      params = {
        searchFormMobile: {
          airportInfo: info
        }
      }
    } else {
      params = {
        searchForm: {
          airportInfo: info
        }
      }
    }
    this.$emit('eventHandle', params)
  }

  // 选择机场列表数据
  getAirportItem(item: any) {
    const itemObj: any = {
      lat: item.latitude || '',
      long: item.longitude || '',
      code: item.iataCode || '',
      ac: item.isActive || '',
      kcid: item.backupKlookCityId || ''
    }
    if (this.type === Type.PICK) {
      itemObj.from = item.airportName ? `${item.airportName}(${item.iataCode})` : ''
      this[Type.DROP] = {
        ...this[Type.DROP],
        to: itemObj.from,
        lat: itemObj.lat,
        long: itemObj.long,
        code: itemObj.code,
        kcid: itemObj.kcid
      }
      this.searchWord = itemObj.from
    } else {
      itemObj.to = item.airportName ? `${item.airportName}(${item.iataCode})` : ''
      this[Type.PICK] = {
        ...this[Type.PICK],
        from: itemObj.to,
        lat: itemObj.lat,
        long: itemObj.long,
        code: itemObj.code,
        kcid: itemObj.kcid
      }
      this.searchWord = itemObj.to
    }
    this[this.type] = {
      ...this[this.type],
      ...itemObj
    }
    this.updateFormData()
  }

  // 切换接机方式
  changePickType(pickType: pickType) {
    this.curPickType = pickType
    this.deleteFieldError()
    // 当切换到航班号搜索时，需要校验是否需要清空航班搜索信息（如果航班号无效则需要清空）
    if (pickType === 'byFlight') {
      // 樱花季产品卡片跳转过来，直接切换到航班号搜索不处理
      if (this.isSearchByCode) {
        return
      }
      if (this.pick.from !== `${this.flightInfo.airportName}(${this.flightInfo.iataCode})` || dayjs(this.pick.date).format('YYYYMMDD') !== dayjs(this.flightInfo.arrivalDate).format('YYYYMMDD') || this.pick.time !== this.flightInfo.arrivalTime) {
        this.resetFlightInfo()
      }
    }
  }

  // 清空航班号搜索信息
  resetFlightInfo() {
    this.flightInfo = {
      hasFlight: false,
      searchType: 'byFlightNo',
      flightNo: '',
      iataCode: '',
      arrivalDate: '',
      arrivalTime: '',
      airportName: '',
      departureName: '',
      originCode: '',
      latitude: '',
      longitude: '',
      isActive: '',
      backupKlookCityId: ''
    }
  }

  // 更新航班号信息，选择了航班后调用
  updateFlightInfo(flightInfo: IFlightInfo) {
    this.flightInfo = flightInfo
    // 更新searchForm机场信息
    this.getAirportItem(flightInfo)
    // 更新searchForm接送时间信息
    this.pick.time = flightInfo.arrivalTime
    this.pick.date = flightInfo.arrivalDate
    this.updateFormData()
  }

  checkAndfillDefaultFlightNo() {
    try {
      const l = localStorageEx()
      let cacheData = l.getItem(this.flightNoCacheKey)
      cacheData = cacheData ? JSON.parse(cacheData) : null
      if (cacheData && cacheData.hasFlight) {
        this.curPickType = 'byFlight'
        this.updateFlightInfo({
          ...cacheData,
          arrivalDate: cacheData.arrivalDate ? new Date(cacheData.arrivalDate) : null,
          departureDate: cacheData.departureDate ? new Date(cacheData.departureDate) : null,
          source: 'history'
        })
      }
    } catch (e) {
      console.error(e)
    }
  }

  // 根据query更新搜索框状态
  async queryToSearchData() {
    const query: any = this.$route.query
    const flight_direction = Number(query.flightDirection)
    const city_id = Number(query.city_id)
    const code = query.code
    if (city_id) {
      this._axios
        .$get('/v1/transferairportapisrv/airport/by_city_airport?city_id=' + city_id)
        .then((res: Data.Res) => {
          if (res.success && res.result) {
            this.curPickType = 'byFixTime'
            const item = res.result
            const itemObj: any = {
              lat: item.latitude || '',
              long: item.longitude || '',
              code: item.iataCode || '',
              ac: item.isActive || '',
              kcid: item.backupKlookCityId
            }
            itemObj.from = `${item.airportName}(${item.iataCode})`
            this[Type.DROP] = {
              ...this[Type.DROP],
              to: itemObj.from,
              lat: itemObj.lat,
              code: itemObj.code,
              long: itemObj.long,
              kcid: itemObj.kcid
            }
            this.searchWord = itemObj.from
            this[this.type] = {
              ...this[this.type],
              ...itemObj
            }
            this.updateFormData()
            this.getPublicList(item)
          }
        })
        .catch(() => {})
    } else if (flight_direction) {
      this.isPickTab = flight_direction === 1
      this[this.type] = {
        ...this[this.type],
        ...query,
        from: this.isPickTab ? query.from : query.to,
        to: this.isPickTab ? query.to : query.from,
        date: query.time ? new Date(query.time.split(' ')[0]) : '',
        time: query.time ? query.time.split(' ')[1] : '',
        pas: isNaN(query.pas) ? 2 : Number(query.pas)
      }
      if (this.isPickTab) {
        this[Type.DROP] = {
          ...this[Type.DROP],
          to: query.from,
          lat: query.lat,
          code: query.code,
          long: query.long,
          kcid: query.kcid
        }

        // 链接带参数的情况进入页面，curPickType都以链接参数为准，覆盖实验效果
        // 如果url携带了航班号及航班必要参数，自动填充航班信息并切换tab到航班号搜索
        if (query.flightNo && query.originCode && this.showPickType && query.from && query.code && query.time) {
          this.curPickType = 'byFlight'
          this.flightInfo = {
            hasFlight: true,
            searchType: 'byFlightNo',
            flightNo: query.flightNo,
            airportName: query.from?.match(/(.+?)(?=\()/)?.[0],
            departureName: query.departureName,
            iataCode: query.code,
            originCode: query.originCode,
            arrivalTime: query.time ? query.time.split(' ')[1] : '',
            arrivalDate: query.time ? new Date(query.time.split(' ')[0]) : '',
            latitude: query.lat,
            longitude: query.long,
            isActive: query.ac,
            backupKlookCityId: query.kcid
          }
        } else {
          this.curPickType = 'byFixTime'
        }
      }
      // 首页携带code字段表示是樱花季产品卡片跳转过来的，需要切到fixTime
      if (this.currentPage === 'home' && code && this.isSearchByCode) {
        this.curPickType = 'byFixTime'
        const res = await this.getAirportByIata(code)
        const airport = res?.result?.airportSearchItemList?.[0]
        this.flightInfo = {
          hasFlight: false,
          searchType: 'byFlightNo',
          flightNo: '',
          iataCode: airport?.iataCode || '',
          arrivalDate: '',
          arrivalTime: '',
          airportName: airport?.airportName || '',
          departureName: airport?.departureName || '',
          originCode: '',
          latitude: airport?.latitude || '',
          longitude: airport?.longitude || '',
          isActive: airport?.isActive || '',
          backupKlookCityId: airport?.backupKlookCityId || ''
        }
        this.getAirportItem(airport)
        return
      }
      this.updateFormData()
    }
    // 首页接机，没有航班信息，也没有机场信息，尝试获取历史记录并填充（若有），同时切到flightNo
    if (this.isPickTab && this.showPickType && !this.flightInfo.hasFlight && this.currentPage === 'home' && !code) {
      this.checkAndfillDefaultFlightNo()
    }
  }

  mounted() {
    // 接送tab展示逻辑判断
    if ((this.currentPage === 'home' || this.currentPage === 'srp') && this.hitShowPickTypeExperiment) {
      this.showPickType = true
      if (this.hitPickTypeExperiment) {
        this.curPickType = 'byFlight'
      } else {
        this.curPickType = 'byFixTime'
      }
    } else {
      this.showPickType = false
    }
    this.queryToSearchData()
  }
}
