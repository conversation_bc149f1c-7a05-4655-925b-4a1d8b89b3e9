<template>
  <client-only>
    <klk-modal
      v-if="tips"
      :title="tips.title"
      title-align="center"
      button-align="center"
      modal-class="search-form__modal"
      :open.sync="tipsModalVisible"
      :ok-label="tips.btn"
      :show-cancel-button="false"
      @on-confirm="closeTipsModal"
      @close="closeTipsModal"
    >
      <div class="klk-modal-content">
        {{ tips.content }}
      </div>
    </klk-modal>
  </client-only>
</template>

<script lang="ts">
import { Component, Prop } from 'vue-property-decorator'
import Base from '../base'

@Component
export default class confirmModal extends Base {
  @Prop({ type: Object, default: () => {} }) tips?: { title: any, btn: any, content: any }
  @Prop({ type: Boolean, default: () => false }) tipsModalVisible!: boolean


  closeTipsModal() {
    this.$emit('update:tipsModalVisible', false)
  }
}
</script>

<style lang="scss" scoped>
.search-form {
  &__modal {
    .klk-modal-content {
      text-align: center;
    }
  }
}
</style>
