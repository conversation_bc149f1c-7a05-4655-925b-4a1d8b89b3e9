<template>
  <div>
    <div
      v-for="(flightInfo, index) in flightInfoList"
      :key="index"
      :data-spm-module="`${isByOd ? 'FlightODCard_LIST' : 'FlightCard_LIST'}?idx=${index}&len=${flightInfoList.length}`"
      data-spm-virtual-item="__virtual?typ=entry"
      :class="['flight-card', getKlkPlatform() === 'desktop' ? 'flight-card__desktop' : 'flight-card__mobile']"
      @click="updateFlightInfo(flightInfo)"
    >
      <div class="flight-card__header">
        <div class="flight-card__flight-no">{{ flightInfo.flight_code }}</div>
      </div>
      <div class="flight-card__body">
        <div class="flight-card__flight-time">
          <div class="flight-card__flight-time-start">{{ flightInfo.departure_time.split(' ')[1] }}</div>
          <div class="flight-card__flight-time-icon">
            <img src="https://res.klook.com/image/upload/v1739864561/ued/Other/icon_flight_fill.svg" width="20" height="20" />
          </div>
          <div class="flight-card__flight-time-end">
            <div class="flight-card__flight-time-end-time">{{ flightInfo.arrival_time.split(' ')[1] }}</div>
            <div v-if="!isSameDay(flightInfo)" class="flight-card__flight-time-end-date">{{ flightInfo.arrival_time.split(' ')[0].split('-').slice(1).join('-') }}</div>
          </div>
        </div>
        <div class="flight-card__airport-wrapper">
          <div class="flight-card__airport">{{ flightInfo.dep_name }}({{ flightInfo.dep_iata }})</div>
          <div class="flight-card__airport flight-card__airport-destination">{{ flightInfo.arr_name }}({{ flightInfo.arr_iata }})</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Prop } from 'vue-property-decorator'
import dayjs from 'dayjs'
import Base from '../base'
import { WebClick_141 } from '../../../share/galileo/auto-click'

interface IFlightInfo {
  flight_code: string,
  dep_iata: string,
  dep_name: string,
  arr_iata: string,
  arr_name: string,
  departure_time: string,
  arrival_time: string,
  arrival_airport_latitude: string,
  arrival_airport_longitude: string,
  arrival_klook_city_id: string
}

interface searchAirportObject extends Object {
  searchWord: string
}

const searchApi = {
  searchAirport: '/v1/transferairportapisrv/airport/autocomplete'
}

@Component
export default class FlightCard extends Base {
  @Prop({ type: Array, default: [] }) flightInfoList!: IFlightInfo[]
  @Prop({ type: Boolean, default: false }) isByOd!: boolean

  isSameDay(item: any) {
    return (item.departure_time.split(' ')[0] === item.arrival_time.split(' ')[0])
  }

  updateFlightInfo(flightInfo: IFlightInfo) {
    const { dep_name, arr_name, arr_iata, dep_iata, arrival_time, departure_time, flight_code, arrival_airport_latitude, arrival_airport_longitude, arrival_klook_city_id } = flightInfo
    if (!arr_iata || !dep_iata) {
      this.$toast(this.$t('209839'))
      return
    }
    this.$emit('update-flight-info', {
      latitude: arrival_airport_latitude,
      longitude: arrival_airport_longitude,
      klookCityId: arrival_klook_city_id,
      departureName: dep_name, // 起飞机场名称
      airportName: arr_name, // 到达机场名称
      iataCode: arr_iata, // 到达机场三字码
      originCode: dep_iata, // 起飞机场三字码
      departureTime: departure_time ? dayjs(departure_time).format('HH:mm') : '',
      departureDate: departure_time ? new Date(departure_time) : null,
      arrivalTime: arrival_time ? dayjs(arrival_time).format('HH:mm') : '',
      arrivalDate: arrival_time ? new Date(arrival_time) : null,
      hasFlight: true,
      flightNo: flight_code
    })
  }

  getSearchAirport(params: searchAirportObject) {
    return this._axios
      .$get(searchApi.searchAirport, { params })
      .then((res: Data.Res) => {
        return Promise.resolve(res)
      })
      .catch((error: any) => {
        return Promise.reject(error)
      })
  }

    get galileoClick1() {
        return { spm: WebClick_141, autoTrackSpm: true }
    }
}
</script>

<style lang="scss" scoped>
.flight-card {
  background-color: $color-info-background;
  border-radius: $radius-l;
  padding: 12px 16px;
  cursor: pointer;
  margin: 0 8px 12px;

  &__header {
    display: flex;
    justify-content: space-between;
  }

  &__flight-no {
    @include font-body-m-bold;
    color: $color-text-link;
  }

  &__flight-time {
    @include font-heading-s;
    color: $color-text-primary;
    word-break: break-all;
    display: flex;
    align-items: center;
    justify-content: center;
    &-start {
      @include font-heading-s;
      flex: 1;
    }
    &-icon {
      flex: 0.5;
      width: 48px;
      height: fit-content;
      position: relative;
      display: flex;
      justify-content: center;

      &::before, &::after {
        content: '';
        width: 12px;
        height: 1px;
        background-color: #ccc;
        position: absolute;
      }

      &::before {
        top: 9px;
        left: 0px;
      }

      &::after {
        top: 9px;
        right: 0px;
      }
    }
    &-end {
      display: flex;
      flex: 1;
      justify-content: flex-end;
      &-time {
        @include font-heading-s;
        color: $color-text-primary;
      }
      &-date {
        @include font-paragraph-s-regular;
        color: $color-text-secondary;
      }
    }
  }

  &__airport-wrapper {
    display: flex;
    justify-content: space-between;
    margin-top: 8px;
  }

  &__airport {
    color: $color-text-secondary;
    width: calc((100% - 42px) / 2);
    word-break: break-word;
    &-destination {
      text-align: right;
    }
  }
}
.flight-card__desktop:last-of-type {
  margin-bottom: 37px;
}
</style>
