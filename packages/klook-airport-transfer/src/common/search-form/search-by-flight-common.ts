import { Component, Prop, Watch } from 'vue-property-decorator'
import { getStandardDateFormat } from '../datetime'
import { IconChevronDown } from '@klook/klook-icons'
import dayjs from 'dayjs'
import debounce from 'lodash/debounce'
import localStorageEx from '../localstorage'
import FlightCard from './flight-card.vue'
import Base from '../base'
import ConfirmModal from './confirm-modal.vue'

const HISTORY_CACHE_KEY_PREFIX = 'pat_airport_search_history' // 机场搜索缓存key
const EXPIRE_TIME = 365 * 12 * 60 * 60 * 1000

interface IFlightInfo {
  hasFlight: boolean,
  searchType: 'byFlightNo' | 'byOD'
  flightNo: string,
  arrivalDate: any,
  arrivalTime: string,
  departureDate?: any,
  departureTime?: string,
  iataCode: string, // 到达机场三字码
  originCode: string, // 出发机场三字码
  airportName: string,
  departureName: string,
  latitude: string,
  longitude: string,
  isActive: string,
  backupKlookCityId: string,
  airlineName?: string,
  source?: 'history'
}

interface IFormFlightNo {
  flightNo: string,
  pickupDate: Date | null,
}


interface IFormOD {
  departureIata: string,
  arrivalIata: string,
  pickupDate: Date | null
}

interface IFormData {
  flightNo?: string,
  departureIata?: string,
  arrivalIata?: string,
  pickupDate: Date | null
}


interface airportHistory {
  airportName: string,
  departureName: string,
  iataCode: string,
  longitude: string,
  latitude: string,
  isActive: string,
  backupKlookCityId: number
}

interface searchHistory {
  arrival?: airportHistory[],
  departure?: airportHistory[]
}

interface searchAirportObject extends Object {
  searchWord?: string | undefined
}

type FormType = 'formFlightNo' | 'formOD' // fix：20250321需求变更，移除了airline搜索，先保留这个控制变量，未来可能新增OD搜索

const searchApi = {
  searchByFlightNo: '/v1/transferairportapisrv/flight/search_by_flight_number', // 按航班号搜索航班
  searchByOD: '/v1/transferairportapisrv/flight/search_by_od', // 按航班号搜索航班
  searchAirport: '/v1/transferairportapisrv/airport/autocomplete',
  airportGroups: '/v1/transferairportapisrv/airport/groups'
}

@Component({
  components: {
    IconChevronDown,
    FlightCard,
    ConfirmModal
  }
})
export default class SearchByFlightMixin extends Base {
  @Prop({ type: Boolean, default: false }) visible!: boolean
  @Prop({ type: Object, default: {} }) flightInfo!: IFlightInfo
  @Prop({ type: String, default: 'home' }) currentPage!: string

  pickfrom: boolean = false
  pickto: boolean = false
  dropfrom: boolean = false
  dropto: boolean = false

  airportGroup: any[] = []

  isAirport: boolean = true

  isPickTab: boolean = true

  offsetLeft: string = '0'

  offsetTop: string = '220'

  searchWord: string = ''

  formType: FormType = 'formFlightNo'

  searchType: string = 'airport' // 搜索类型

  showSearchByFlightModal: boolean = false // 是否展示按航班号搜索弹层

  // 特殊情况下poi/机场item点击后的弹窗提示
  tipsModalVisible: boolean = false
  tips: any = {
    content: null,
    title: null,
    btn: null,
    action: 0 // 1 - 清空poi，2 - 清空机场，0 - 不做操作
  }

  formFlightNo: IFormFlightNo = {
    flightNo: '',
    pickupDate: null
  }

  formData: IFormData = {
    flightNo: '',
    departureIata: '',
    arrivalIata: '',
    pickupDate: null
  }

  formOD: IFormOD = {
    departureIata: '',
    arrivalIata: '',
    pickupDate: null
  }

  showDateTimePicker: boolean = false // 是否展示日历选择

  searchLoading: boolean = false

  showFlightModal: boolean = false // 选择航班弹窗是否可见
  flightList: any[] = []

  searchList: any = null

  searchHistory: searchHistory = {
    arrival: [],
    departure: []
  }

  isByFlight: boolean = true // 是否通过航班号搜索

  isArrivalIata: boolean = false // 是否是到达机场

  isDepartureIata: boolean = false // 是否是出发机场

  isNoODSearchResult: boolean = false // 是否无OD搜索结果

  finallyLoading: boolean = false // 是否展示加载中

  get defaultArrivalDate() {
    if (this.flightList && this.flightList[0] && this.flightList[0].departure_time) {
      return this.flightList[0].departure_time.split(' ')[0]
    }
    return ''
  }
  // 获取机场缓存key
  get historyCacheKey() {
    return `${HISTORY_CACHE_KEY_PREFIX}_${this.getKlkLanguage()}`
  }

  get flightNoCacheKey() {
    return `${HISTORY_CACHE_KEY_PREFIX}_flightNo_${this.getKlkLanguage()}`
  }

  get isByFlightNo() {
    return this.formType === 'formFlightNo'
  }

  get isByOD() {
    return this.formType === 'formOD'
  }

  get showSearchAirportPage() {
    return this.airportGroup.length > 0
  }

  // 是否是樱花季产品卡片过来
  get isSearchByCode() {
    const query: any = this.$route.query
    const isPickTab = query.flightDirection === 1
    // 有航班信息就不用code查询接口
    return (isPickTab && !query.from) || (!isPickTab && !query.to)
  }

  get arrivalOrDeparture() {
    return this.isArrivalIata ? 'arrival' : 'departure'
  }

  @Watch('visible')
  handleVisible(val: boolean) {
    if (val) {
      // 重置数据
      console.log('handleVisible', this.flightInfo)
      this.showDateTimePicker = false
      if (this.flightInfo) {
        // 打开弹窗时，使用传入的flightinfo回填
        const pickupDate = (this.flightInfo.departureDate && this.flightInfo.source !== 'history') ? dayjs(new Date(this.flightInfo.departureDate)).toDate() : null
        this.formFlightNo = {
          flightNo: this.flightInfo.flightNo,
          pickupDate
        }
        this.formOD = {
          departureIata: this.flightInfo.originCode,
          arrivalIata: this.flightInfo.iataCode,
          pickupDate
        }
        this.formData = {
          flightNo: this.flightInfo.flightNo,
          arrivalIata: this.flightInfo.airportName && `${this.flightInfo.airportName}(${this.flightInfo.iataCode})`,
          departureIata: this.flightInfo.departureName && `${this.flightInfo.departureName}(${this.flightInfo.originCode})`,
          pickupDate
        }
      }
      // 禁用页面滚动
      const modalElement = document.querySelector('.search-by-flight__modal')
      if (modalElement) {
        document.body.style.overflow = 'hidden'
      }
    } else {
      // 恢复页面滚动
      const modalElement = document.querySelector('.search-by-flight__modal')
      if (modalElement) {
        document.body.style.overflow = ''
      }
      this.isArrivalIata = false
      this.isDepartureIata = false
      this.isNoODSearchResult = false
      this.formType = 'formFlightNo'
    }
    this.finallyLoading = false
  }

  handleCodeFocus(value: string, v1: string, v2: string) {
    this.searchWord = this.formData[value]
    this.searchLoading = true
    this.showSearchByFlightModal = true
    this[v1] = true
    this[v2] = false
  }

  // 选择搜索项
  selectSearchItem(item: any) {
    if (item.tips) {
      if (item.tips.type === 'toast') {
        this.$toast(item.tips.content)
      } else if (item.tips.type === 'confirm-modal') {
        this.tips = {
          ...item.tips,
          action: 0
        }
        this.tipsModalVisible = true
      }
    }
    this.selectAirport(item)
  }

  selectAirport(item: any) {
    console.log('selectAirport', item)
    const t = this.isArrivalIata ? 'arrivalIata' : 'departureIata'
    this.formData[t] = `${item.airportName}(${item.iataCode})`
    this.updateFormDataToState(t, item.iataCode)

    // 构造缓存数据
    const data: airportHistory = {
      airportName: item.airportName,
      departureName: item.departureName || '',
      iataCode: item.iataCode,
      longitude: item.longitude || '',
      latitude: item.latitude || '',
      isActive: item.isActive || '',
      backupKlookCityId: item.backupKlookCityId || 0
    }

    // 写入缓存，区分 arrival 和 departure
    const type = this.isArrivalIata ? 'arrival' : 'departure'
    this.saveHistory(type, data)

    const t3 = setTimeout(() => {
      this.isArrivalIata = false
      this.isDepartureIata = false
      clearTimeout(t3)
    }, 0)
  }

  // 关闭机场搜索页
  hideSearchAirportPage(e: any, type: string) {
    // 如果点击了重新定位按钮，则不收起
    const locationArea = document.getElementById('current-location')
    if (locationArea && locationArea.contains(e?.target)) {
      return
    }
    // 在选中机场的时候这里需要等this.formData数据更新后再做判断,故需要 settimeout
    const t3 = setTimeout(() => {
      this.searchList = null
      this[type] = false
      clearTimeout(t3)
    }, 0)
  }

    // 点击输入框事件
  handleFocus(type: string) {
    this[type] = true
  }

  getArrivalOrDeparture() {
    return this.isArrivalIata ? 'arrival' : 'departure'
  }

  updateFormDataToState(key?: string, value?: string) {
    if (key) {
      this[this.formType][key] = value || this.formData[key]
    } else {
      Object.keys(this[this.formType]).forEach((key) => {
        this[this.formType][key] = value || this.formData[key]
      })
    }
  }

  switchOD() {
    if (!(this.formData.departureIata || this.formData.arrivalIata)) {
      return
    }
    this.$nextTick(() => {
      // 交换出发地和目的地
      const temp = this.formData.departureIata
      const updatedFormData = {
        ...this.formData,
        departureIata: this.formData.arrivalIata,
        arrivalIata: temp
      }
      this.formData = updatedFormData // 替换整个对象以触发响应式更新

      const tempOD = this.formOD.departureIata
      this.formOD.departureIata = this.formOD.arrivalIata
      this.formOD.arrivalIata = tempOD
    })
  }

  async searchFlight() {
    this.finallyLoading = true
    this.searchLoading = true
    try {
      let res = null
      let params: any = {
        departureDate: dayjs(this.formData.pickupDate).format('YYYY-MM-DD')
      }
      if (this.formType === 'formFlightNo') {
        params = {
          ...params,
          flightNumber: this.formFlightNo.flightNo
        }
        res = await this._axios.$get(searchApi.searchByFlightNo, { params })
      } else {
        params = {
          ...params,
          departureIata: this.formOD.departureIata,
          arrivalIata: this.formOD.arrivalIata
        }
        res = await this._axios.$get(searchApi.searchByOD, { params })
      }
      if (res.success && res.result) {
        if (res.result.found) {
          this.flightList = res.result.flights
          this.showFlightModal = true
          if (this.getKlkPlatform() === 'desktop') {
            // desktop端关闭上层弹窗
            this.closeModal()
          }
        } else {
          // 搜索无结果
          this.handleSearchError(res?.error?.message)
        }
      } else {
        this.handleSearchError(res?.error?.message)
      }
    } catch (error: any) {
      console.error(`get flight no error: ${error}`)
      this.handleSearchError(error?.message)
    }
    this.searchLoading = false
    this.finallyLoading = false
  }

  handleSearchError(msg?: any) {
    if (this.isByOD) {
      this.isNoODSearchResult = true
      return
    }
    this.$toast(msg || this.$t('203803'))
  }

  changeTab(type: string) {
    this.finallyLoading = false
    this.formType = type as FormType
    if (type === 'formFlightNo') {
      this.handleCancel()
    }
  }

  handleCancel() {
    this.isNoODSearchResult = false
  }

  openSearch(type: string) {
    const input = type === 'arrivalIata' ? this.formData.arrivalIata : this.formData.departureIata
    this.searchWord = input || ''
    this.isArrivalIata = type === 'isArrivalIata'
    this.isDepartureIata = type === 'isDepartureIata'
    console.log('openSearch', input)
    this.searchWord && this.handleInputChange(this.searchWord, type)
  }

  handleInputChange(val: string, type: string) {
    this.searchWord = val
    this.searchLoading = true
    if (val === '') {
      this.resetData(type)
    }
    this.handleValue()
  }

    // 防抖搜索
  handleValue = debounce(this.getSearchList, 500)

    // 关键字搜索
  getSearchList(value?: string) {
    const keyword = value || this.searchWord
    if (keyword) {
      this.searchLoading = true
      let params = null
      params = {
        searchWord: keyword
      }
      this.getSearchAirportList(params)
    } else {
      this.searchLoading = false
      this.searchList = null
    }
  }

    // 搜索机场
  getSearchAirportList(params: searchAirportObject) {
    this._axios.$get(searchApi.searchAirport, {params})
      .then((res: any) => {
        if (res.success && res.result) {
          this.searchList = res.result
          this.searchLoading = false
        }
      })
      .catch((e: any) => {
        console.error(`get search airport error: ${e}`)
        this.searchLoading = false
      })
  }

    // 提交验证报错后，置空相关报错项
  resetData(type: string) {
    this.formData[type] = ''
  }

  dateFormatter(date: Date | string) {
    const type = 3
    const language = this.getKlkLanguage()
    return getStandardDateFormat(date, this.$t.bind(this), language, type)
  }

  // mweb
  selectDateTime(date: Date, time: Date) {
    this.selectDate(date)
    if (time) {
      this.formData.pickupTime = time
      this.updateFormDataToState('pickupTime')
    }
    this.closeDatePicker()
  }

  // web
  selectDate(date: Date) {
    this.formData.pickupDate = date
    this.updateFormDataToState('pickupDate')
  }

  // web
  selectTime(time: string) {
    const hour = Number(time.split(':')[0])
    const minute = Number(time.split(':')[1])
    this.formData.pickupTime = dayjs().hour(hour).minute(minute).toDate()
    this.updateFormDataToState('pickupTime')
    this.closeDatePicker()
  }

  closeDatePicker() {
    this.showDateTimePicker = false
  }

  updateFlightInfo(flightInfo: IFlightInfo) {
    const _flightInfo = {
      ...flightInfo
    }
    this.$emit('update-flight-info', _flightInfo)
    this.closeFlightModal()
    this.closeModal()
  }

  closeFlightModal() {
    this.flightList = []
    this.showFlightModal = false
  }

  closeModal() {
    this.$emit('update:visible', false)
  }

  saveHistory(type: 'arrival' | 'departure', data: airportHistory) {
    try {
      const l = localStorageEx()
      let cacheData = l.getItem(this.historyCacheKey)
      cacheData = cacheData ? JSON.parse(cacheData) : { arrival: [], departure: [] }

      // 获取当前类型的缓存数据
      const curTypeCacheData = cacheData[type] || []
      const index = curTypeCacheData.findIndex((item: any) => item.iataCode === data.iataCode)

      // 去重并调整顺序
      if (index > -1) {
        curTypeCacheData.splice(index, 1)
      }
      curTypeCacheData.unshift(data)
      curTypeCacheData.splice(5) // 限制缓存最多存储 5 条记录

      // 更新缓存数据
      cacheData[type] = curTypeCacheData
      l.setItem(this.historyCacheKey, JSON.stringify(cacheData), EXPIRE_TIME)

      // 更新本地搜索历史
      this.getSearchHistory()
    } catch (e) {
      console.error(e)
    }
  }

  getSearchHistory() {
    try {
      const l = localStorageEx()
      let cacheData = l.getItem(this.historyCacheKey)
      cacheData = cacheData ? JSON.parse(cacheData) : { arrival: [], departure: [] }
      this.searchHistory = cacheData
    } catch (e) {
      console.error(e)
      this.searchHistory = {}
    }
  }

  clearHistory() {
    try {
      const l = localStorageEx()
      let cacheData = l.getItem(this.historyCacheKey)
      cacheData = cacheData ? JSON.parse(cacheData) : { arrival: [], departure: [] }
      const type = this.isArrivalIata ? 'arrival' : 'departure'
      if (type) {
        // 清除指定类型的历史记录
        cacheData[type] = []
      } else {
        // 清除所有历史记录
        cacheData = { arrival: [], departure: [] }
      }

      // 更新缓存
      l.setItem(this.historyCacheKey, JSON.stringify(cacheData), EXPIRE_TIME)

      // 更新组件状态
      this.searchHistory = cacheData
    } catch (e) {
      console.error(e)
    }
  }

  isFlightSearchDisabled() {
    if (this.formType === 'formFlightNo') {
      return !this.formData.flightNo || !this.formData.pickupDate
    }
    if (this.formType === 'formOD') {
      return !this.formData.departureIata || !this.formData.arrivalIata || !this.formData.pickupDate
    }
    return false
  }

  handleSearchByFlight() {
    this.isNoODSearchResult = false
    this.formType = 'formFlightNo'
  }

  async mounted() {
    try {
      await this._axios.$get(searchApi.airportGroups, {})
        .then((res: Data.Res) => {
          if (res.success && res.result) {
            this.airportGroup = res.result.airportGroupList
          }
        })
    } catch (error) {}
  }
}
