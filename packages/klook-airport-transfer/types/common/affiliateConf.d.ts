export interface IAffiliateConf {
  affiliateConf: IAffiliateConf
  affiliate_type: string
  aid_extra_info: IAid_extra_info
  backendLanguage: string
  cobrand: ICobrand
  cookie_duration: number
  currency: string
  currencyRate: ICurrencyRate
  currencySymbol: string
  host: string
  ip: string
  ipCountry: string
  isBot: boolean
  isIOS: string
  isKlookApp: boolean
  keplerId: string
  landing_url: string
  language: string
  pageId: string
  pageTemplate: string
  partner_name: any
  partner_web_config: any
  pid: any
  platform: string
  platformMp: string
  point_earn: any
  qsAffiliateConf: IQsAffiliateConf
  redirectUrl: string
  retina: number
  secondaryNav: ISecondaryNav
  supportLanguages: string[]
  uid: number
  utilConfig: IUtilConfig
  webp: number
  websiteConfig: IWebsiteConfig
  wid: number
}

export interface IUtilConfig {
  activityBreadcrumb: number
  activityRecommend: number
  activityRelated: number
  activityTop: number
  banner_ad: number
  booking_option_notice: number
  chat_with_klook: number
  domain: string
  downland_bar: number
  faq_klook: number
  footer: number
  global_notice: number
  hello_user_name: number
  index_invite: number
  internalLink: number
  pay_success_book_url: boolean
  search: number
  shopping_cart: number
  showSecondaryNav: number
  show_pkg_card_icons: number
  use_credits: number
  user_center_path: number
}

export interface ICurrencyRate {
  [k: string]: number
}

export interface ISecondaryNav {
  all_category: IAll_category
  home_item: IHome_item
  operational_category: IOperational_category[]
  vertical_category: IVertical_category[]
}

export interface IHome_item {
  business_name: string
  corner_marker_url: string
  deep_link: string
  deep_link_title: string
  extra_info: any
  hover_icon_src: string
  icon_src: string
  partner_extra_info: any
  sub_menu_list: any
  title: string
  type: string
}

export interface IAll_category {
  business_name: string
  corner_marker_url: string
  deep_link: string
  deep_link_title: string
  extra_info: any
  hover_icon_src: string
  icon_src: string
  partner_extra_info: any
  sub_menu_list: ISub_menu_list[]
  title: string
  type: string
}

export interface ISub_menu_list {
  business_name: string
  corner_marker_url: string
  deep_link: string
  deep_link_title: string
  extra_info: any
  hover_icon_src: string
  icon_src: string
  partner_extra_info: any
  sub_menu_list: ISub_menu_list[]
  title: string
  type: string
}

export interface IVertical_category {
  business_name: string
  corner_marker_url: string
  deep_link: string
  deep_link_title: string
  extra_info: any
  hover_icon_src: string
  icon_src: string
  partner_extra_info: any
  sub_menu_list: ISub_menu_list[]
  title: string
  type: string
}

export interface IOperational_category {
  business_name: string
  corner_marker_url: string
  deep_link: string
  deep_link_title: string
  extra_info: any
  hover_icon_src: string
  icon_src: string
  partner_extra_info: any
  sub_menu_list: any[]
  title: string
  type: string
}

export interface IAid_extra_info {
  aff_klick_id: string
}

export interface ICobrand {
  cobrand_height_mweb: number
  cobrand_height_web: number
  logo_url: string
  logo_white_url: string
  support_klook_logo: string
  support_klook_logo_white: string
}

export interface IQsAffiliateConf {
  affiliate_type: string
  aid_extra_info: IAid_extra_info
  cobrand: ICobrand
  cookie_duration: number
  landing_url: string
  partner_name: any
  partner_web_config: any
  pid: any
  point_earn: any
  redirectUrl: string
  uid: number
  wid: number
}

export interface IWebsiteConfig {
  business_license: boolean
  change_site_tip: boolean
  default_currency: string
  default_languages: string
  footer_change_site: boolean
  href: string
  ip_country: string
  only_server_support_currencies: any[]
  pay_way: string
  price_guarantee: boolean
  support_currencies: string[]
  support_languages: string[]
  website: string
}
