import replace from 'rollup-plugin-replace'
import typescript from 'rollup-plugin-typescript2'
import vue from 'rollup-plugin-vue';
import babel from 'rollup-plugin-babel'

import postcss from 'rollup-plugin-postcss'
import nodeResolve from 'rollup-plugin-node-resolve'
import commonjs from 'rollup-plugin-commonjs';
import json from '@rollup/plugin-json';


const distPath = 'dist'
import clear from 'rollup-plugin-clear'
import pkg from "./package.json";
clear({ targets: [distPath] })

const {
  name,
  version,
  author
} = require('./package.json')

const banner =
  `/**
  * v${version}
  * (c) ${new Date().getFullYear()} ${author}
  */`
const configs = {
  esm: {
    dir: '/esm/',
    output: 'dist/esm/',
    format: 'esm',
    target: 'es2015',
    env: 'production',
    genDts: true
  },
  cjs: {
    dir: '/cjs/',
    output: 'dist/cjs/',
    format: 'cjs',
    target: 'es2015',
    isSSR: true
  }
}

const input = [
  './src/index.ts',
  './src/components/search-form-mobile/search-form-mobile.vue',
  './src/components/homeWorkFlows.vue',
  './src/components/homeWorkFlowsMobile.vue',
  './src/components/homeArticle.vue',
  './src/components/homeArticleMobile.vue',
  './src/components/homeHotAirport.vue',
  './src/components/homeHotAirportMobile.vue',
  './src/components/homePublicTransferMobile.vue',
  './src/components/homePublicTransfer.vue',
]

const genTsPlugin = (configOpts) => typescript({
  useTsconfigDeclarationDir: true,
  tsconfigOverride: {
    compilerOptions: {
      target: configOpts.target,
      declaration: configOpts.genDts
    },
    exclude: ['**/__tests__', 'test-dts']
  },
  abortOnError: false
})

const genPlugins = (configOpts) => {
  const plugins = []
  if (configOpts.env) {
    plugins.push(replace({
      'process.env.NODE_ENV': JSON.stringify(configOpts.env)
    }))
  }
  plugins.push(nodeResolve({
    extensions: ['.mjs', '.js', '.jsx', '.vue', '.tsx']
  }))

  plugins.push(commonjs())
  plugins.push(replace({
    'process.env.MODULE_FORMAT': JSON.stringify(configOpts.format)
  }))
  if (configOpts.plugins && configOpts.plugins.pre) {
    plugins.push(...configOpts.plugins.pre)
  }
  plugins.push(genTsPlugin(configOpts))

  plugins.push(
    babel({
      runtimeHelpers: true,
      exclude: 'node_modules/**',
      extensions: ['.js', '.jsx', '.ts', '.tsx'],
      presets: ["@vue/babel-preset-jsx"]
    })
  )

  plugins.push(vue({
    css: false,
    normalizer: '~vue-runtime-helpers/dist/normalize-component.js',
    template: {
      isProduction: true,
      optimizeSSR: configOpts.isSSR
    },
    style:{
      preprocessOptions: {
        scss: {
          data: '@import "node_modules/@klook/klook-ui/lib/styles/token/index.scss";',
        }
      },
      postcssPlugins:[
        require('autoprefixer')({
          overrideBrowserslist: [
            '> 1%',
            'last 5 versions',
            'ios >= 7',
            'android > 4.4',
            'not ie < 10'
          ]
        }),
        require('cssnano')({
          safe: true
        })
      ]
    }
  }))

  plugins.push(postcss({
    extract: true,
    plugins: [
      require('autoprefixer')()
    ]
  }))

  plugins.push(json());


  if (configOpts.plugins && configOpts.plugins.post) {
    plugins.push(...configOpts.plugins.post)
  }
  return plugins
}

const genConfig = (configOpts) => ({
  input,
  output: {
    banner,
    dir: distPath + configOpts.dir,
    // file: configOpts.output,
    format: configOpts.format,
    name: name,
    sourcemap: false,
    exports: 'named',
    globals: configOpts.globals,
  },
  external(id) {
    let externalList = Object.keys(pkg.peerDependencies)
    if(configOpts.format === 'esm'){
      externalList = externalList.concat('@klook/empty-panel/dist/esm/index.css')
    }
    return externalList.includes(id)
  },
  plugins: genPlugins(configOpts)
})

const genAllConfigs = (configs) => (Object.keys(configs).map(key => genConfig(configs[key])))

export default genAllConfigs(configs)
