<template>
  <div
    class="exp-itinerary"
    :class="{ 'is-large': currentSize === 'large' }"
  >
    <PickUpPoints 
      v-if="isValidPickupInfo"
      :info="pickupInfos"
      :itinerary-pois="itineraryPois"
      :current-poi="currentPoi"
      :show-entry="false"
      @close="currentPoi = null"
    />
    <!-- <ItineraryMap
      v-if="validateMap"
      :itinerary-data="itineraryData"
      :current-poi="currentPoi"
      :platform="platform"
      :map-type="mapType"
      @close="currentPoi = null"
    /> -->

    <Map
      v-if="mapVisible"
      :show.sync="mapVisible"
      v-bind="mapAtrrs"
    />

    <div
      v-if="itineraryData.departure_more"
      class="itinerary-top-tips"
    >
      <IconTipsSvg class="icon" />
      <div class="text">{{ itineraryData.departure_more }}</div>
    </div>
    <ItineraryDay
      v-for="(day, index) in dayList"
      :key="`${day.day_name}-${index}`"
      :day-data="day"
      :is-one-day="isOneDay"
      :no-divider="index === 0"
      :showImageFn="showImageFn"
      :hackCloseBtn="hackCloseBtn"
      :itinerary-pois="itineraryPois"
    />
    <ItineraryFooter
      v-if="footerList && footerList.length > 0"
      :footer-data="footerList"
    />

    <template v-if="allImages && allImages.length > 0">
      <ImgPreviewDesktop
        v-if="isDesktop"
        v-bind="imageGalleryObj"
        :activity-id="calcAid"
        :hackCloseBtn="hackCloseBtn"
        :current-poi="currentImages"
        @close="imageGalleryObj.ons.close"
        @swiperChange="swiperChange"
      >
      </ImgPreviewDesktop>
      <div
        v-else
        id="TNA_ItineraryPhoto_page_spm" 
        :data-spm-page="pageSpm"
      >
        <ImgPreviewMobile
          :images="allImages"
          :index="currentIndex"
          :visible.sync="visible"
          :current-poi="currentImages"
          @close="currentImages = null"
          @swiperChange="swiperChange"
        >
          <template slot="description">
            <div class="merchant-image-description">
              {{ currentImages.text }}
            </div>
          </template>
        </ImgPreviewMobile>
      </div>
    </template>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop, Provide, Watch } from 'vue-property-decorator'
import VueLazyLoad from 'vue-lazyload';

import ItineraryDay from './itinerary-day.vue'
import ItineraryFooter from './itinerary-footer.vue'
import ItineraryMap from './components/itinerary-map/index.vue'
import ImgPreviewMobile from './components/pic-preview/mobile/index.vue'
import ImgPreviewDesktop from './components/pic-preview/desktop/image-gallery/index.vue'
import Map from './components/map.vue'
import PickUpPoints from './components/pick-up-points/index.vue'

import { isServer, getCropDefined } from './utils'

import IconTipsSvg from './imgs/icon-tips.svg'

import { ItineraryData } from './types'

const lazyLoadingImage = 'https://res.klook.com/image/upload/image_logo_mx7wgd.png'

Vue.use(VueLazyLoad, {
  adapter: {
    loaded(listender: any) {
      const el = listender.el
      const ratio = el.getAttribute('ratio')

      if (!isServer && ratio) {
        const logo = el?.querySelector?.('.v-lazy-logo') || el?.parentElement?.querySelector?.('.v-lazy-logo')
        logo && (logo.style.display = 'none')
      }
    }
  },
  error: lazyLoadingImage
})

@Component({
  components: {
    ItineraryDay,
    ItineraryFooter,
    ItineraryMap,
    ImgPreviewMobile,
    ImgPreviewDesktop,
    IconTipsSvg,
    Map,
    PickUpPoints
  }
})
export default class KlookExperienceItinerary extends Vue {
  @Prop({ default: null }) customComp!: Record<string, object>
  @Prop({ default: 'mobile' }) platform!: string
  @Prop({ default: '' }) size!: 'small' | 'large' // 在 desktop 的部分场景，要兼容small size
  @Prop({ default: () => '' }) getTranslateFunc!: Function // this.$t.bind(this)
  @Prop({ default: () => () => null }) getAxiosFunc!: Function // this.$t.bind(this)
  @Prop({ default: false }) disableLazyload!: boolean
  @Prop({ type: Object, default: () => ({}) }) itineraryData!: ItineraryData
  @Prop({ default: false }) useItineraryMap!: any
  @Prop({ type: String, default: 'mapbox' }) mapType!: 'mapbox' | 'google'
  @Prop() showImageFn?: Function
  @Prop({ default: false }) hackCloseBtn?: boolean
  @Prop({ type: String, default: '' }) componentType!: string
  @Prop() grounpTitleClick!: Function
  @Prop({ type: Boolean, default: false }) isPrint!: boolean
  @Prop({ default: () => '' }) inhouseTrack!: Function // 埋点
  @Prop({ type: Boolean, default: false }) useImageSwiper!: boolean
  @Prop({ default: false }) simplified!: boolean // desktop 右侧栏需要简化版本
  @Prop({ type: [Number, String], default: 248 }) cardSwiperItemWidth!: number | string
  @Prop({ type: String, default: '' }) imageGroupStyle!: string
  @Prop() language!: string | undefined
  @Prop({ type: Object, default: null }) pickupInfos!: any

  currentPoi = null

  get isValidPickupInfo() {
    if (this.isPrint) {
      return false
    }
    return this.isMobile && (this.validateMap || this.pickupInfos)
  }

  @Watch('visible')
  visibleChange(v: boolean) {
    if (v) {
      if (typeof this.inhouseTrack === 'function') {
        this.inhouseTrack('pageview', '#TNA_ItineraryPhoto_page_spm', { force: true })
      }
    }
  }

  get pageSpm() {
    return `TNA_ItineraryPhoto?oid=package_${this.packageId}&trg=manual`
  }

  get itineraryPois() {
    return this.itineraryData?.pois?.poi_data ?? []
  }

  @Provide('language2provide')
  get calcLanguage() {
    return this.language
  }

  @Provide('provideCardSwiperItemWidth')
  get provideCardSwiperItemWidth() {
    const cardSwiperItemWidth = this.cardSwiperItemWidth
    if (typeof cardSwiperItemWidth === 'number') {
      return `${cardSwiperItemWidth}px`
    }
    return this.cardSwiperItemWidth
  }
  
  @Provide('imageGroupType')
  get imageGroupType() {
    return this.imageGroupStyle
  }

  @Provide('isImageSwiper')
  get isImageSwiper() {
    return this.useImageSwiper
  }

  @Provide('mapStyle')
  get mapStyle() {
    return this.mapType
  }

  // 好像没有用了，暂时先保留
  @Provide('isSimplified')
  get isSimplified() {
    return this.simplified
  }

  @Provide('isMobile')
  get isMobile() {
    return this.platform === 'mobile'
  }

  @Provide('isPrinting')
  get isPrinting() {
    return this.isPrint
  }

  mapVisible: boolean = false
  mapAtrrs = {}
  
  @Provide('handleTitleClick')
  handleTitleClick(data: any, emit: boolean, isPoi: boolean = false) {
    if (emit && typeof this.grounpTitleClick === 'function') {
      this.grounpTitleClick(data)
    }
    if (isPoi) {
      const [poiData] = (data?.components ?? []).filter((item: any) => item.type === 'poi')
      const [poi] = poiData?.data?.poi_data ?? []
      if (this.isMobile) {
        this.handleShowMap(poi)
      } else {
        if (poi?.map) {
          const { location: address, address_desc: addressDesc } = poi?.map ?? {}
          this.setMapShow({
            address,
            addressDesc
          })
        }
      }
    }
  }

  @Provide('setMapShow')
  setMapShow(data: any) {
    this.mapVisible = true
    this.mapAtrrs = data
  }

  @Provide('isPickUp')
  get isPickUp() {
    const types = ['pick_up_meet_up', 'pick_up_meet_up_v2']
    return types.includes(this.componentType)
  }

  @Provide('isNewPickUp')
  get isNewPickUp() {
    const types = ['pick_up_meet_up_v2']
    return types.includes(this.componentType)
  }

  @Provide('customMap')
  get customMap() {
    return this.useItineraryMap
  }

  @Provide('handleShowMap')
  handleShowMap(map: any) {
    this.currentPoi = map
  }

  @Provide('handleEmit')
  handleEmit(event: string, data: any) {
    this.$emit(event, data)
  }

  @Provide('customCompObj')
  get customCompObj() {
    return this.customComp
  }

  @Provide('customInhouseTrack')
  customInhouseTrack(...args: any) {
    return this.inhouseTrack(...args)
  }

  @Provide('platform')
  get currentPlatform() {
    return ['mobile', 'desktop'].includes(this.platform) ? this.platform : 'mobile'
  }

  @Provide('translateI18n')
  translateI18n(key: string, ...values: any) {
    const $t = this.getTranslateFunc()
    return $t(key, ...values)
  }


  @Provide('customAxios')
  get customAxios() {
    return this.getAxiosFunc()
  }

  @Provide('packageId')
  get packageId() {
    return this.itineraryData?.package_id ?? 0
  }

  // 生成pdf的时候图片不能用懒加载
  @Provide('disableLazyload')
  get shouldDisableLazyload() {
    return this.disableLazyload
  }

  @Provide('componentSize')
  get currentSize() {
    if (['small', 'large'].includes(this.size)) {
      return this.size
    }
    return null
  }

  get calcAid() {
    return 0
  }
  
  @Provide('currentMapType')
  get currentMapType() {
    return this.mapType
  }

  visible: boolean = false
  currentIndex: number = 0
  currentImages: any = {}

  imageGalleryObj: any = {
    dontGetImages: true,
    showGallery: false,
    currentIndex: 0,
    bannerList: [],
    ons: {
      close: () => {
        const { imageGalleryObj } = this
        imageGalleryObj.itineraryImages = []
        imageGalleryObj.currentIndex = 0
        imageGalleryObj.showGallery = false
      }
    }
  }

  @Provide('showImagesViewer')
  showImagesViewer(poi: any, index: number) {
    const id = `${poi.attr_id}_${index}`
    this.currentIndex = this.allImages.findIndex((item: any) => item.id === id)
    if (this.isDesktop) {
      this.showImageFnHandler(this.currentIndex)
    } else {
      this.swiperChange(this.currentIndex)
      this.visible = true
    }
  }

  get calcCropDefined() {
    const { platform } = this
    return getCropDefined(platform)
  }

  @Provide('getCropUrl2provide')
  getCropUrl(src = '', options?: { crop: string }) {
    // 接口返回数据：原图、带裁剪参数的图片
    const { uploadUrl, previewCrop, adminCrop } = this.calcCropDefined
    let imgCrop = options?.crop || previewCrop
    if (src.indexOf(imgCrop) !== -1) {
      // 防止重复拼接参数
      return src
    }
    let str = src
    if (src.indexOf(adminCrop) !== -1) {
      // 替换裁剪参数
      str = src.replace(adminCrop, imgCrop)
    } else {
      // 原图增加裁剪参数
      str = src.replace(uploadUrl + '/', uploadUrl + imgCrop)
    }
    return str
  }

  getOfficialImages(list: any[]) {
    const arr = list || []
    return arr.map((item, index) => {
      const { src, id } = item
      const previewImgSrc = this.getCropUrl(src)
      return {
        image_id: id,
        img_resize_url: previewImgSrc,
        img_url: previewImgSrc
      }
    })
  }

  showImageFnHandler(index: number = 0) {
    const { imageGalleryObj } = this
    imageGalleryObj.bannerList = this.allOfficialImages
    imageGalleryObj.showGallery = true
    imageGalleryObj.currentIndex = index || 0
    this.swiperChange(index)
  }

  swiperChange(index: number) {
    this.currentImages = this.allImages[index] || null    
  }

  get validateMap() {
    const itineraryMap = this.itineraryData?.pois?.poi_data ?? []
    return this.isMobile && Array.isArray(itineraryMap) && itineraryMap.length
  }

  get isDesktop() {
    return this.platform === 'desktop'
  }

  get dayList() {
    return this.itineraryData?.days || []
  }

  get footerList() {
    return this.itineraryData?.foot_info || []
  }

  get isOneDay() {
    return this.dayList.length === 1
  }

  get allOfficialImages() {
    return this.getOfficialImages(this.allImages)
  }

  get allImages() {
    if (this.isNewPickUp) {
      return this.getAllImagesV2(this.dayList)
    }
    return this.getAllImages(this.dayList)
  }

  getAllImagesV2(list: any = []) {
    return list.reduce((images: any, element: any) => {
      const groups = element.groups || []
      const groupsData = groups.reduce((gAcc: any, gCurr: any) => {
        const components = gCurr.components || []
        const texts = components.find((item: any) => item.type === 'texts')
        const textData = (texts?.data?.texts ?? []).find((item: any) => {
          const types = ['text', 'bold']
          return types.includes(item.type)
        })
        const subSroup = components.find((item: any) => item.type === 'sub_group')
        const sub = subSroup?.data?.sub_data ?? []
        const imgs = sub.reduce((acc: any, curr: any) => {
          const title = curr?.map?.name ?? ''
          const data = {...curr, text: textData?.text ?? '', title }
          const imgList = (curr.imgs || []).reduce((imgAcc: any, img: any, index: number) => {
            if (img.type === 'default') {
              return imgAcc
            }
            const id = `${data.attr_id}_${index}`
            const previewImgSrc = this.getCropUrl(img.src)
            return [...imgAcc, {...img, ...data, id, src: previewImgSrc}]
          }, [])
          return [...acc, ...imgList]
        }, [])
        return [...gAcc, ...imgs]
      }, [])
      return [...images, ...groupsData]
    }, [])
  }

  getAllImages(list: any = []) {
    return list.reduce((images: any, element: any) => {
      const groups = element.groups || []
      const groupsData = groups.reduce((gAcc: any, gCurr: any) => {
        const components = gCurr.components || []
        const texts = components.find((item: any) => item.type === 'texts')
        const textData = (texts?.data?.texts ?? []).find((item: any) => item.type === 'text')
        const poiData = components.find((item: any) => item.type === 'poi')
        const poi = poiData?.data?.poi_data ?? []
        const imgs = poi.reduce((acc: any, curr: any) => {
          const data = {...curr, text: textData?.text ?? '' }
          const imgList = (curr.imgs || []).reduce((imgAcc: any, img: any, index: number) => {
            if (img.type === 'default') {
              return imgAcc
            }
            const id = `${data.attr_id}_${index}`
            const previewImgSrc = this.getCropUrl(img.src)
            return [...imgAcc, {...img, ...data, id, src: previewImgSrc}]
          }, [])
          return [...acc, ...imgList]
        }, [])
        return [...gAcc, ...imgs]
      }, [])
      return [...images, ...groupsData]
    }, [])
  }
}
</script>

<style lang="scss">
// 设置在 html 上用于禁止页面滚动
.klk-lock-body-scroll {
  body {
    width: 100%;
    position: fixed !important;
    height: 100% !important;
  }
}
</style>

<style lang="scss" scoped>
.itinerary-top-tips {
  display: flex;
  padding-bottom: 12px;
  .icon {
    width: 16px;
    height: 16px;
    flex: none;
    margin: 2px 8px 0 0;
  }
  .text {
    flex: 1;
    @include font-body-s-regular;
  }
}
.merchant-image-description {
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  color: $color-text-reverse;
  padding: 16px 20px calc(env(safe-area-inset-bottom) + 16px) 20px;
  max-height: 133px;
  overflow: auto;
  z-index: 1;
  box-sizing: border-box;
  background-color: rgba(0, 0, 0, 0.8);
  border-radius: $radius-xl $radius-xl 0 0;
  @include font-body-s-regular;
}
</style>
