export interface ItineraryComponentTextData {
  icons?: Icon[]
  texts?: {
    type: string
    text: string
  }[]
}

export interface Icon {
  icon: string
  text: string
}

export interface PoiImgs {
  type: string
  src: string
}

interface MapLocation {
  location: string
  google_place_id: string
  city_name: string
  address_desc: string
}

export interface ItineraryComponentPoiData {
  type: string
  data: {
    poi_data?: {
      title: string
      summary_infos?: string[]
      icons?: Icon[]
      imgs?: PoiImgs[]
      map?: MapLocation
    }[]
  }
}

type ItineraryComponentData = ItineraryComponentTextData | ItineraryComponentPoiData

export interface ItineraryDayGroupData {
  group_key: string
  group_id: number
  left_icon: string
  time: string
  group_name: string
  tags: any[] | null
  components: ItineraryComponentData[],
  track_info: any
}

export interface ItineraryDayData {
  day_name: string
  pre_summaries: string[]
  day_icon: string
  tips: {
    departure_tips: string
    departure_more: string
    return_tips: string
  }
  groups: ItineraryDayGroupData[]
  pick_up_info?: any
}

export interface ItineraryData{
  package_id: number
  days: ItineraryDayData[]
  foot_info: string[],
  pois?: any
}
