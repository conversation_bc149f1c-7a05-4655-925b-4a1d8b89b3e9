<template>
  <div class="itinerary-footer">
    <!-- <klk-divider class="itinerary-footer-divider" /> -->

    <p
      v-for="(item, index) in footerData"
      :key="index"
      class="itinerary-footer-item"
    >
      {{ item }}
    </p>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator'
import KlkDivider from '@klook/klook-ui/lib/divider'
import '@klook/klook-ui/lib/styles/components/divider.scss'

@Component({
  components: {
    KlkDivider
  }
})
export default class ItineraryDay extends Vue {
  @Prop() footerData!: string[]
}
</script>

<style lang="scss" scoped>
.itinerary-footer {

  &-divider {
    margin-bottom: 20px;
  }

  &-item {
    @include font-body-s-regular;
    color: $color-text-secondary;
  }
}
</style>
