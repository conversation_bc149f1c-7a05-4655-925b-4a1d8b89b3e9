<template>
  <klk-modal 
    closable
    :open.sync="visible"
    :show-default-footer="false"
    width="80%"
    @close="handleClose"
    class="fullscreen-modal"
  >
    <div>
      <slot></slot>
    </div>
  </klk-modal>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator'

@Component
export default class FullscreenModal extends Vue {
  @Prop({ type: Boolean, default: false }) visible!: boolean

  handleClose() {
    this.$emit('close')
    this.$emit('update:visible')
  }
}
</script>

<style scoped lang="scss">
.fullscreen-modal {
  ::v-deep .klk-modal {
    padding: 0;
    margin: 0;

    .klk-modal-body {
      padding: 0;
    }
  }
}
</style>
