<template>
  <div
    class="pick-up-mobile" 
    :data-spm-module="spmModule"
  >
    <PickUpInfo v-if="showEntry && info" :info="info" @showMap="handleShowMap" />

    <FullScreenModal
      :visible.sync="visible"
      class="pick-up-mobile-full-screen-modal"
    >
      <Map
        v-if="createdMap"
        :map-info="mapInfo"
        :map-type="currentMapType || 'google'"
        :itinerary-pois="itineraryPois"
        :current-poi="currentPoi"
        :pickup-info="info"
        @close="visible = false"
      />
    </FullScreenModal>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Inject } from 'vue-property-decorator'
import Map from './map.vue'
import FullScreenModal from './full-screen-modal.vue'
import PickUpInfo from './info.vue'
import { isServer } from '../../../utils'
import type { PickUpInfoData } from '../types/index'

@Component({
  components: {
    Map,
    FullScreenModal,
    PickUpInfo
  }
})
export default class  PickUpMobile extends Vue {
  @Prop({ default: () => ({}) }) info!: PickUpInfoData
  @Prop({ type: Array, default: () => [] }) itineraryPois!: any[]
  @Inject("customInhouseTrack") customInhouseTrack!: Function;
  @Inject("currentMapType") currentMapType!: string;
  @Prop({ type: Boolean, default: true }) showEntry!: boolean
  @Prop({ type: Object, default: null }) currentPoi!: any

  createdMap: boolean = false
  visible: boolean = false

  get mapInfo() {
    return this.info?.map_data || {}
  }

  get spmModule() {
    const types = (this.mapInfo?.map || []).reduce((acc: string[], curr) => {
      const { pick_up_type, is_customized_area } = curr
      if (pick_up_type === 1) {
        acc.push('pick_up_fixed')
      } else {
        acc.push(is_customized_area ? 'pick_up_customized' : 'pick_up_scope')
      }
      return acc
    }, [])
    const typeStr = Array.from(new Set(types)).join(',')
    return `PickMeetUpInformation?ext=${JSON.stringify({ type: typeStr })}`
  }

  @Watch('currentPoi', { deep: true, immediate: true })
  currentPoiChange(val: any) {    
    if (val) {
      this.handleShowMap()
    }
  }

  @Watch('visible')
  onVisibleChange(val: boolean) {
    if (val) {
      !isServer && this.$nextTick(() => {
        const target = document.querySelector('.pick-up-mobile-full-screen-modal') as HTMLElement
        this.customInhouseTrack('pageview', target, { force: true })
      })
    } else {
      this.$emit('close')
    }
  }

  handleShowMap() {
    this.visible = true
    this.createdMap = true
  }
}
</script>
