<template>
  <div class="keywords-search-modal" data-spm-page="LocationSearch_ResultPage">
    <div class="keywords-search-modal-header">
      <div class="keywords-search-modal-header_right">
        <SearchGlassIcon class="search-icon"/>
        <input
          ref="searchInput"
          v-model.trim="keywords"
          class="search-input"
          :placeholder="translateI18n('104304')"
          @input="handleInput"
        />
        <div class="icon-box">
          <SearchClearIcon
            v-show="keywords"
            class="search-clear-icon"
            @click.native.stop="handleClear"
          />
        </div>
      </div>
      <div
        class="keywords-search-modal-header_cancel-button"
        @click.stop="back"
      >
        {{ translateI18n('161204') }}
      </div>
    </div>

    <div class="keywords-search-modal-body">
      <div v-if="status === 'fetching'" class="loading-box">
        <klk-loading
          show-overlay
          :overlay-color="'rgba(255, 255, 255, 0.7)'"
        ></klk-loading>
      </div>
      <div>
        <div
          v-if="isNotMatch"
          class="search-keywords-none"
        >
          <div v-if="isScope" class="search-keywords-none-scope">
            <p class="search-keywords-none-text"> {{ translateI18n('104307') }}</p>
          </div>
          <div v-else class="search-keywords-none-point">
            <div class="search-keywords-none-text">
              {{ translateI18n('104307') }}
            </div>
            <div v-if="hasResult" class="search-keywords-none-point-below">
              <div class="gap" />
              <p class="sugguest"> {{ translateI18n('104308') }}</p>
            </div>
          </div>
        </div>

        <div class="search-keywords-result-list">
          <div
            v-for="(item, index) in renderList"
            :key="index"
            class="search-keywords-result-list-item"
            :data-spm-module="getSearchResultSpm(item)"
            data-spm-virtual-item="__virtual"
            :class="{'selected': checkIsActive(item)}"
            @click.stop="handleItemClick(item, index)"
          >
            <div class="search-keywords-result-list-item-info">
              <LocationInfoCard :info="item" />
            </div>
            <div class="search-keywords-result-list-item-right">
              <IconSelected v-if="checkIsActive(item)" class="icon-selected"/>
              <klk-loading
                v-if="currentLoadingIndex === index"
                show-overlay
                :overlay-color="'rgba(255, 255, 255, 0.7)'"
              ></klk-loading>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import {  Component, Inject, Prop } from "vue-property-decorator";
import SearchClearIcon from "../../../imgs/search-clear-icon.svg";
import debounce from "lodash/debounce";
import KlkLoading from "@klook/klook-ui/lib/loading";
import "@klook/empty-panel/dist/esm/index.css";
import LocationInfoCard from "./location-info.vue";
import type { KeywordsSearchItem } from '../types/index'
import SearchGlassIcon from "../../../imgs/search-glass-icon.svg";
import IconSelected from "../../../imgs/icon_selected.svg";

import Toast from '@klook/klook-ui/lib/toast'
import '@klook/klook-ui/lib/styles/components/toast.scss'

import SearchMixin from '../base/poi-search'

@Component({
  components: {
    SearchClearIcon,
    SearchGlassIcon,
    KlkLoading,
    LocationInfoCard,
    IconSelected
  },
})
export default class KeywordsSearch extends SearchMixin {
  @Prop({ default: null }) selectedItem!: KeywordsSearchItem
  @Prop({ default: false }) isScope!: boolean
  @Prop({ default: '' }) customKeywords!: string
  @Inject("translateI18n") translateI18n!: Function;
  @Inject("customAxios") customAxios!: any;
  @Inject("packageId") packageId!: number;

  status: "default" | "fetching" | "success" = "default";
  handleKeyWordsChange = debounce(this.getSearchResult, 400);

  get hasResult() {
    return this.searchResultList?.length > 0
  }

  // @Watch('selectedItem', { immediate: true })
  // initKeywordsChange(v: KeywordsSearchItem | null) {
  //   if (v) {
  //     this.keywords = v?.location_name || ''
  //     this.handleKeyWordsChange()
  //   }
  // }

  handleInput() {
    this.handleKeyWordsChange()
  }

  checkIsActive(item: any) {
    const selectedItem = this.selectedItem
    if (!selectedItem) {
      return false
    }
    const location = item?.detail_data?.location ?? ''
    const selectedLocation = selectedItem?.location ?? ''
    return selectedLocation && location === selectedLocation
  }

  reset() {
    this.keywords = ''
    this.searchResultList = []
    this.isNotMatch = false
  }

  async getSearchResult() {
    this.status = "fetching";

    const params = {
      input: this.keywords,
      package_id: this.packageId,
      show_meet_up: 1,
      current_google_place_id: this.selectedItem?.google_place_id || ''
    }

    this.isNotMatch = false

    this.searchResultList = await this.getSearchFun(params)
    this.isNotMatch = !this.searchResultList.length && !!this.keywords
    // const list = await this.getSearchFun(params)
    // this.searchResultList = this.setOrder([...list])
    this.status = 'success'
  }

  setOrder(data = []) {
    const selectedItem = this.selectedItem
    if (selectedItem) {
      const location = selectedItem.location
      const list = data.reduce((acc: any[], curr: any) => {
        const currLoaction = curr.detail_data?.location ?? ''
        if (currLoaction === location) {
          acc.unshift(this.initResult(curr))
        } else {
          acc.push(curr)
        }
        return acc
      }, [])

      return list
    }

    return data
  }

  autoFocus() {
    if (this.keywords) {
      return
    }

    this.focusInput()
  }

  focusInput() {
    const searchInput = this.$refs.searchInput as any
    searchInput?.focus() && searchInput.focus()
  }

  handleClear() {
    this.autoFocus()
    this.reset()
  }

  back() {
    this.$emit("closeFullScreenModal");
  }

  async handleItemClick(item: any, index: number = -1) {
    const { detail_data = null, place_id, is_default } = item || {}

    let detail = null
    this.currentLoadingIndex = index
    if (is_default || detail_data) {
      detail = detail_data
    } else {
      detail = await this.getPoiDetail({
        package_id: this.packageId,
        place_id,
      })
      
      this.$set(item, 'detail_data', detail)
    }
    this.currentLoadingIndex = -1

    const data_type = (detail as any)?.data_type ?? -1

    if (data_type === 3) {
      return Toast(this.translateI18n('161196'))
    }

    this.$emit('selected', detail)
  }

  getSearchResultSpm(item: KeywordsSearchItem) {
    const { data_type } = item
    let val = 'Available'

    if (data_type === 2) {
      val = 'Extra fee'
    } else if (data_type === 3) {
      val = 'Not offered'
    }

    return`LocationSearch_SelectResult?trg=manual&ext=${JSON.stringify({ InScope: val })}`
  }

  mounted() {
    this.autoFocus()
    // !this.isScope && this.handleKeyWordsChange()
  }
}
</script>

<style lang="scss" scoped>
.keywords-search-modal {
  display: flex;
  flex-direction: column;
  height: 40vh;

  &-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    height: 60px;
    align-items: center;
    padding: 0 20px;
  
    &_cancel-button {
      margin-left: 12px;
      @include font-body-s-bold;
    }

    &_right {
      display: flex;
      height: 36px;
      padding: 3px 3px 3px 12px;
      box-sizing: border-box;
      align-items: center;
      justify-content: space-between;
      flex: 1;
      padding: 0 10px;
      background: $color-bg-3;
      border-radius: $radius-xxl;
      border: 1px solid transparent;

      &.active {
        border: 1px solid $color-orange-500;
      }

      .search-icon {
        width: 20px;
        height: 20px;
        margin-right: 8px;
      }

      .search-input {
        outline: none;
        caret-color: $color-orange-500;
        border: none;
        width: 100%;
        background: transparent;
        width: calc(100% - 30px);
        white-space: nowrap;
      }

      .icon-box {
        margin-left: 12px;
        flex: none;
        display: flex;
        align-items: center;
        justify-content: flex-end;

        .search-clear-icon {
          width: 16px;
          height: 16px;
          flex: none;
        }
      }
    }
  }

  &-body {
    position: relative;
    height: calc(100% - 60px);
    overflow: hidden scroll;

    .loading-box {
      position: relative;
      height: 260px;
    }

    .search-keywords {
      &-result-list {
        padding-top: 12px;

        &-item {
          padding: 8px 20px;
          display: flex;
          align-items: center;

          &-info {
            flex: 1;
          }

          &-right {
            width: 20px;
            height: 20px;
            position: relative;
          }

          .icon-selected {
            width: 20px;
            height: 20px;
            flex: none;
          }

          &.selected {
            background: $color-brand-primary-light-2;
            // border-radius: $radius-m;
          }
        }
      }

      &-none {
        &-text {
          padding: 20px 12px;
          color: $color-neutral-700;
          font-size: $fontSize-body-s;
          line-height: 21px;
        }

        &-point {
          &-below {
            .gap {
              width: 100vw;
              height: 8px;
              margin-left: -8px;
              background: $color-neutral-100;
            }

            .sugguest {
              font-weight: $fontWeight-semibold;
              font-size: $fontSize-body-s;
              line-height: 21px;
              color: $color-neutral-900;
              padding: 24px 12px 0;
            }
          }
        }
      }
    }
  }
}
</style>
