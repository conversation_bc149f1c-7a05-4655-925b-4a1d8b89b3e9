<template>
  <div v-if="actualShow" :class="cClass" :style="style">
    <div class="full-screen-modal-mask"></div>
    <div class="full-screen-modal-inner" :style="innerStyle">
      <div class="full-screen-modal-inner-header">
        <slot name="header" />
      </div>
      <div class="full-screen-modal-inner-body">
        <slot></slot>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch } from 'vue-property-decorator'
import { isServer } from '../../../utils'
import eventBus from '../../../utils/event-bus'


const ANIMATION_TIME = 350

@Component
export default class FullScreenModal extends Vue {
  @Prop() title!: string
  @Prop() visible!: boolean

  transitionEndFlag: boolean = false
  actualShow: boolean = false
  show: boolean = false
  appendBody: boolean = false
  transitionEnd: boolean = false

  get innerStyle() {
    return {
      transform: this.show ? 'translateY(0px)' : 'translateY(100%)'
    }
  }

  get cClass() {
    return {
      'full-screen-modal': true,
      'full-screen-modal-hidden': !this.show
    }
  }

  @Watch('visible')
  onVisibleChange(val: boolean) {
    if (!isServer) {
      if (val) {
        this.open()
      } else {
        this.close()
      }
    }
  }

  close() {
    this.show = false
    this.lockOuterScroll(false)
    setTimeout(() => {
      this.actualShow = false
    }, ANIMATION_TIME)
  }

  open() {
    this.actualShow = true
    this.lockOuterScroll(true)
    setTimeout(() => {
      this.show = true
    }, 0)
  }

  lockOuterScroll(bool: boolean) {
    document.body.style.overflow = bool ? 'hidden' : 'auto'
  }

  updateVisible() {
    this.$emit('update:visible', false)
  }

   appendPopupElToBody () {
    if (this.appendBody) return;
      this.$nextTick(() => {
      document.body.appendChild(this.$el);
      this.appendBody = true;
    });
  }

  mounted() {
    eventBus.$on('closeFullScreenModal', this.updateVisible)
    this.appendPopupElToBody()
  }

  beforeDestroy() {
    eventBus.$off('closeFullScreenModal', this.updateVisible)
  }
}
</script>

<style lang="scss" scoped>
.full-screen-modal {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  top: 0;
  z-index: 3001 !important;
  transition: visibility $motion-duration-l $motion-timing-ease;

  &-mask {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    top: 0;
    opacity: $opacity-solid;
    background-color: $color-overlay-default-3;
    transition: opacity $motion-duration-l $motion-timing-ease;
  }

  &-inner {
    position: absolute;
    overflow: hidden;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 100%;
    transform: translateY(100%);
    transition: transform $motion-duration-l $motion-timing-ease;
    display: flex;
    flex-direction: column;
    background: $color-white;

    &-body {
      max-height: 100%;
      flex: 1;
      // padding: 12px 0;
      overflow-y: auto;
    }
  }

  &.full-screen-modal-hidden {
     visibility: hidden;

    .full-screen-modal-mask {
      opacity: $opacity-transparent;
    }
  }
}
</style>
