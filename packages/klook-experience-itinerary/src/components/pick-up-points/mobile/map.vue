<template>
  <klk-map ref="mapRef" v-bind="mapConfig" @click.native="handleMapClick">
    <klk-map-geo-fence
      v-for="(conf, index) in geoFenceConf"
      :key="index"
      :geo-fence-conf="conf"
      :isWgs84="true"
    />
    <klk-map-marker
      v-for="(marker, index) in allMarkers"
      ref="marker"
      :key="index"
      :center="marker.center"
      :options="marker.options"
      :class="{
        'marker-active': currentMarker === index,
        'is-area': isArea(marker)
      }"
      :z-index="isArea(marker) ? 200 : currentMarker === index ? 202 : 100"
      v-bind="getBindTracker(marker.tracker)"
    >
      <template v-if="!isArea(marker)">
        <img
          v-show="currentMarker != index"
          class="icon-img"
          :src="marker.groupIcon.unselect"
          @click.stop="markerClick(index)"
        />
        <div v-show="currentMarker === index" class="icon-img-wrap">
          <div
            v-if="marker.isTemporary && marker.tips_text"
            class="pick-up-map-area-marker-wrap"
          >
            <div
              class="pick-up-map-area-marker"
              :style="marker.style"
            >
              {{ marker.tips_text }}
            </div>
            <div class="marker-after" :style="marker.style"></div>
          </div>
          <img
            class="icon-img"
            :class="{ active: currentMarker === index }"
            :src="marker.groupIcon.select"
          />
        </div>
      </template>
      <div
        class="pick-up-map-area-marker-wrap"
        v-else-if="marker.poptip"
      >
        <div
          class="pick-up-map-area-marker"
          :style="marker.style"
        >
          {{ marker.poptip }}
        </div>
        <div class="marker-after" :style="marker.style"></div>
      </div>
      <div v-else></div>
    </klk-map-marker>

    <div v-if="showSelectedLocation" class="map-card-box">
      <MapCard :data="currentSelectedLocation" />
    </div>

    <div class="top-operator-box">
      <KlkMapCircleButton
        v-show="!showSelectedLocation"
        class="close-button"
        data-spm-module="CloseMap?trg=manual"
        data-spm-virtual-item="__virtual"
        @click.stop.native="handleClose"
      >
        <klk-icon type="icon_navigation_close_m" size="20"></klk-icon>
      </KlkMapCircleButton>
      <KlkMapCircleButton
        v-show="showSelectedLocation"
        @click.stop.native="handleBack"
      >
        <klk-icon type="icon_navigation_chevron_left_xs" size="18"></klk-icon>
      </KlkMapCircleButton>
    </div>

   <div
    v-show="!showSelectedLocation"
    :class="[visible && 'active', 'search-full-srceen-modal']"
    @click.stop
  >
    <KeyWordsSearch
      ref="keywordsSearchRef"
      :selected-item="currentSelectedLocation"
      :is-scope="isScope"
      :mapData="locationList"
      @closeFullScreenModal="handleModalClose"
      @selected="handleSelected"
    />
   </div>

  <KlkMapUserLocation
    v-show="!showSelectedLocation"
    v-bind="getBindTracker({
      type: 'module',
      spm: 'ItineraryMapFindMyPositionButton',
      exposure: false
    })"
    class="user-location"
    :style="locationBottom"
  />

  <div v-show="!showSelectedLocation" class="klk-scroll-snap-map" @touchmove.stop>
    <klk-scroll-snap ref="scrollSnap" @change="setMarkerIndex">
      <SnapCard
        v-for="(item, index) in allCards"
        :key="index"
        :style="cardStyle"
        :data="item"
        @search="showModal"
      />
    </klk-scroll-snap>
  </div>
  </klk-map>
</template>

<script lang="ts">
import { Component, Prop, Inject, Ref } from "vue-property-decorator";
import {
  KlkMap,
  KlkMapMarker,
  KlkMapCircleButton,
  KlkMapUserLocation,
  MapTypes,
  mapUtils,
  KlkMapGeoFence
} from "@klook/map";
import "@klook/map/dist/esm/index.css";

import KlkIcon from "@klook/klook-ui/lib/icon";
import "@klook/klook-ui/lib/styles/components/icon.scss";

import KlkButton from '@klook/klook-ui/lib/button'
import '@klook/klook-ui/lib/styles/components/button.scss'

import MapCard from "./map-card.vue";

import SearchGlassIcon from "../../../imgs/search-glass-icon.svg";
import SearchClearIcon from "../../../imgs/search-clear-icon.svg";
import KeyWordsSearch from "./keywords-search.vue"
import type { KeywordsSearchItem } from '../types/index'
import MapBase from '../base/map'

import KlkScrollSnap from '@klook/klook-scroll-snap'
import '@klook/klook-scroll-snap/dist/esm/index.css'

import SnapCard from '../../itinerary-map/components/map-card/index.vue'

@Component({
  components: {
    KlkMap,
    KlkMapMarker,
    KlkMapCircleButton,
    KlkMapUserLocation,
    MapCard,
    SearchGlassIcon,
    SearchClearIcon,
    KeyWordsSearch,
    KlkIcon,
    KlkMapGeoFence,
    KlkButton,
    KlkScrollSnap,
    SnapCard
  },
})
export default class PickUpPointMap extends MapBase {
  @Prop({ type: Object, default: () => ({}) }) mapInfo!: any;
  @Inject("customInhouseTrack") customInhouseTrack!: Function;
  @Prop({ type: String, default: "mapbox" }) mapType!: "mapbox" | "google"
  @Inject("language2provide") language!: string;
  @Prop({ type: Array, default: () =>[] }) itineraryPois!: any[]
  @Ref() scrollSnap!: any
  @Ref() keywordsSearchRef!: any
  @Prop() currentPoi!: any

  visible: boolean = false;
  currentSelectedLocation: any = null
  searchSelectedLocation: any = null // 单独记录从搜索那边选择过来的数据
  currentMarker: number = -1;
  $map: MapTypes.MapTool | undefined;
  geoFenceConf: any = []
  markerList: any[] = []

  get locationBottom() {
    const bottom = this.scrollSnapHeight + 16
    return {
      bottom: `${bottom}px`
    }
  }

  handleBack() {
    this.currentSelectedLocation = null
  }

  handleModalClose() {
    this.visible = false
    this.currentSelectedLocation = null
    this.searchSelectedLocation = null
    this.currentMarker = -1
    this.handleClear()
  }

  get showSelectedLocation() {
    // return !!this.currentSelectedLocation
    return false
  }

  get allMarkers() {
    const poisMarker = this.initPoisMarker(this.itineraryPois || [])
    return [...this.markerList, ...poisMarker]
  }

  get allCards() {
    const mapCards = this.initCards(this.markerList || [])
    const poiCards = this.itineraryPois.map((item: any) => {
      return {
        ...item,
        card_id: item.attr_id
      }
    })
    return [...mapCards, ...poiCards]
  }

  get locationList() {
    return (this.mapInfo.map || []).map((item: any) => {
      return {
        ...item
      }
    })
  }

  // 点和范围混合了，好像也没用了
  get isScope() {
    return this.mapInfo.pick_up_type === 2
  }

  get mapConfig() {
    let center = ''
    const locationList = this.locationList || []
    const itineraryPois = this.itineraryPois || []
    if (locationList[0]) {
      center = locationList[0].location
    } else if (itineraryPois[0]) {
      center = itineraryPois[0].map?.location
    }

    return {
      language: this.language,
      type: this.mapType,
      // 本地调试地图需要打开该token, 开发环境记得注释掉
      // googleConf: {
      //   token: "AIzaSyByoaOJMATcSHo6iZ-cofp9vlHU8t64ukw"
      // },
      height: "100%",
      interactive: 'greedy',
      zoom: 16,
      center: mapUtils.formatLatLng(center)
    }
  }

  get fencePointList() {
    const list = this.geoFenceConf.reduce((accu: any, curr: any) => {
      (curr.polygonList || []).forEach((list: any[]) => {
        if (Array.isArray(list)) {
          if (Array.isArray(list[0])) {
            accu.push(...list.map((item: any) => mapUtils.formatLatLng(item.reverse().join(','))))
          } else if (list.length === 2) {
            accu.push(mapUtils.formatLatLng(list.reverse().join(',')))
          }
        }
      })
      return accu
    }, [])

    return Object.freeze(list)
  }

  scrollSnapHeight: number = 0

  get cardStyle() {
    const height = this.scrollSnapHeight - 16
    const itineraryPois = this.itineraryPois
    const width = itineraryPois.length > 1 ? {} : {
      width: 'calc(100% - 40px)'
    }
    return {
      height: `${height}px`,
      ...width
    }
  }

  calcHeight() {
    const scrollSnap = this.scrollSnap as any
    const elem = scrollSnap.$el
    this.scrollSnapHeight = elem.offsetHeight
  }

  stopChange = false

  setMarkerIndex(index: number) {
    const data = this.allCards[index]

    if (this.stopChange || !data) {
      this.stopChange = false
      return
    }

    const current = this.allMarkers.findIndex((item: any) => data.card_id === item.id)
    this.currentMarker = current
    this.flyTo(current)

    const marker = this.allMarkers[current]
    this.currentSelectedLocation = marker?.data ?? null
  }

  handleClose() {
    this.visible = false
    this.$emit('close')
  }

  async getGeoFenceConf() {
    const areaIdList = this.getValidAreaId(this.locationList || [])
    if (areaIdList.length) {
      const res = await this.getGeomByIdList({ area_id_list: areaIdList.join(',') })
      this.geoFenceConf =  this.initFenceData(res?.result?.geom_list || [], this.locationList)
    }
  }

  async initMarkerAndFence() {
    await this.getGeoFenceConf()
    this.markerList = (this.locationList || []).map((item: any) => this.formatMarker(item))
  }

  handleCurrentPoiFlyto() {
    const val = this.currentPoi
    const id = val?.attr_id ?? 0
    if (!val || !id) {
      return
    }
    const allMarkers = this.allMarkers || []
    const currentIndex = allMarkers.findIndex((item: any) => item.id === id)

    if (currentIndex >= 0) {
      this.$nextTick(() => {
        this.markerClick(currentIndex)
      })
    }
  }

  initMap() {
    const map = this.$refs.mapRef as any;
    map?.$getMapTool && map.$getMapTool.then(async ($map: MapTypes.MapTool) => {
      this.$map = $map;

      // 隐藏默认点marker，其他地图元素也可以用这个方法
      if (typeof this.$map?.map?.setOptions === "function") {
        const noPoi = [
          {
            featureType: "poi",
            elementType: "labels.icon",
            stylers: [{ visibility: "off" }],
          },
        ];
        this.$map.map.setOptions({ styles: noPoi });
      }

      await this.fitMapBounds()
      this.handleCurrentPoiFlyto()
    });
  }

  async fitMapBounds(center?: any) {
    if (this.$map) {
      const points = center ? [center] : [...this.fencePointList, ...this.allMarkers.map((item: any) => item.center)]
      if (points.length) {
        const box = await mapUtils.turfBbox(points, this.mapType as MapTypes.Type)
        const bounds = this.$map.createBounds(box.sw, box.ne, { formatLngLat: false })
        this.$map.fitBounds(bounds, {
          padding: {
            top: this.allMarkers.length ? 80 : 20,
            left: 20,
            bottom: 160,
            right: 20,
          },
        })
      }
    }
  }

  formatMarker(data: any, isTemporary = false) {
    return this.initMarker(data, isTemporary, {
      tracker: {
        type: 'module',
        spm: 'ViewPickUpPoint',
        exposure: true,
        query: {
          ext: JSON.stringify({
            Type: isTemporary ? 'customed' : 'fixed'
          }),
        },
      }
    })
  }

  markerClick(index: number) {
    const data = this.allMarkers[index]
    const current = this.allCards.findIndex((item: any) => item.card_id === data.id)
    this.currentMarker = index;
    this.currentSelectedLocation = data?.data ?? null
    this.flyTo(index);
    this.stopChange = true
    this.visible = false
    const scrollSnap = this.scrollSnap as any
    scrollSnap && scrollSnap!.slideTo(current)
  }

  flyTo(index: number) {
    const marker = this.allMarkers[index];
    if (!marker || !this.$map) {
      return;
    }
    const latLng = marker.center;
    this.$map!.flyTo(latLng)
  }

  handleClear() {
    this.markerList = this.markerList.filter(marker => !marker.isTemporary)

    this.$nextTick(() => {
      this.fitMapBounds()
    })
  }

  handleMapClick() {
    const fitMap = !!this.searchSelectedLocation
    this.visible = false

    if (fitMap) {
      this.currentSelectedLocation = null
      this.searchSelectedLocation = null
      this.currentMarker = -1
      this.handleClear()
    }
  }

  addMarker(data: any) {
    this.markerList = this.markerList.filter(marker => !marker.isTemporary)
    let index = this.markerList.findIndex((item: any) => item.location === data.location)

    if (index === -1) {
      const target = this.formatMarker(data, true)
      this.markerList.push(target)
      index = this.markerList.length - 1
    }
    this.fitMapBounds(this.markerList[index]?.center ?? null)
    this.currentMarker = index
    this.flyTo(index)
  }

  showModal() {
    this.visible = true
    // 自动聚焦，防止页面抖动
    setTimeout(() => {
      this.keywordsSearchRef?.focusInput?.()
    }, 400)
  }

  handleSelected(item: KeywordsSearchItem) {
    this.currentSelectedLocation = item
    this.searchSelectedLocation = item
    this.addMarker(item)
  }

  async mounted() {
    await this.initMarkerAndFence()
    this.initMap()
    this.calcHeight()
  }
}
</script>

<style lang="scss" scoped>
@import '../scss/map.scss';

.icon-img {
  width: 38px;
  height: 44px;
  object-fit: contain;

  &.active {
    transform: scale(1.36);
    transform-origin: 50% bottom;
    animation: MarkerScale 0.26s ease-in 0s 1;
  }
}

.top-operator-box {
  top: 0;
  padding: 20px;
  width: 100%;
  position: absolute;
  display: flex;
  align-items: center;

  .close-button {
    flex: none;
  }

  .fake-input-box {
    margin-left: 16px;
    display: flex;
    align-items: center;
    padding: 0 16px;
    height: 40px;
    border-radius: 9999px;
    background: #fff;
    box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.14);
    flex: 1;
    // padding: 20 + closeicon : 40px + margin-left: 16px
    width: calc(100% - 96px);
    transition: all 0.32s ease-in-out;

    &-left {
      display: flex;
      align-items: center;
      flex: 1;
      overflow: hidden;

      .glass-icon {
        flex: none;
        width: 20px;
        height: 20px;
      }

      .search-input {
        margin-left: 8px;
        flex: 1;
        font-size: $fontSize-body-s;
        line-height: 21px;
        color: $color-neutral-600;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;

        &.is-keywords {
          color: $color-neutral-900;
        }
      }
    }

    &-right {
      display: flex;
      align-items: center;
      padding: 0 4px;
      margin-left: 8px;

      .clear-icon {
        flex: none;
        width: 16px;
        height: 16px;
      }
    }

    &.no-width {
      overflow: hidden;
      flex: none;
      width: 0 !important;
      padding: 0;
    }
  }
}

.marker-item-outer {
  box-shadow: $shadow-normal-4;
  border: 1px solid $color-border-normal;
  border-radius: $radius-circle;
  position: relative;
  &.active {
    border: 2px solid $color-border-normal;
    transform: scale(1.4);
    transform-origin: 50% bottom;
    transition: transform ease-in-out 0.25s;
    .marker-item-outer-foot {
      border: 2px solid $color-border-normal;
    }
  }
  .marker-item-outer-foot {
    width: 12px;
    height: 12px;
    transform: translateX(-50%) rotate(45deg);
    transform-origin: center;
    position: absolute;
    bottom: -4px;
    left: 50%;
    z-index: 1;
    border: 1px solid $color-border-normal;
    border-radius: 1px;
  }
  .marker-item {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    border-radius: $radius-circle;
    z-index: 2;
  }
}

::v-deep .klkMap_marker.marker-active {
  z-index: 202 !important;
}

::v-deep .klkMap_marker.is-area {
  z-index: 200 !important;
}

// 新版本搜索样式
.search-full-srceen-modal {
  position: fixed;
  width: 100vw;
  bottom: 0;
  left: 0;
  z-index: 3333;
  min-height: 40vh;
  max-height: 75vh;
  background: $color-bg-1;
  border-radius: $radius-xxl $radius-xxl 0 0;
  transition: transform 0.3s ease-in-out;
  transform: translateY(200%);

  &.active {
    transform: translateY(0);
  }
}

.klk-scroll-snap-map {
  position: absolute;
  bottom: 0px;
  left: 0;
  right: 0;
  z-index: 222;
}

.icon-img-wrap {
  position: relative;

  .pick-up-map-area-marker-wrap {
    top: -56px;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
  }
}

.user-location {
  bottom: 174px;
  left: unset !important;
  right: 16px;
}
</style>
