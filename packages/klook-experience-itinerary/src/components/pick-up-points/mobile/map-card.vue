<template>
  <div class="map-card">
    <slot :data="data">
      <LocationInfoCard class="card-info" :info="data" :show-copy-icon="true" />
    </slot>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator'
import LocationInfoCard from "./location-info.vue";

@Component({
  components: {
    LocationInfoCard
  }
})
export default class PickUpInfoMapCard extends Vue {
  @Prop({ type: Object, default: () => {} }) data!: any;
}

</script>

<style lang="scss" scoped>
.map-card {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  border-radius: 20px 20px 0px 0px;
  box-shadow: 0px -1px 8px 0px rgba(0, 0, 0, 0.11);
  padding: 20px;
}
</style>

