<template>
  <div
    v-if="valid"
    class="pick-up-info"
  >
    <h3 class="pick-up-info-title">{{ info.title }}</h3>
    <p class="pick-up-info-tips">{{ info.description }}</p>
    <div class="pick-up-info-text-list">
      <span
        v-for="(text, index) in point_list"
        :key="index"
        class="pick-up-info-text-list-item"
      >
         <span>{{ text }}</span>
      </span>
    </div>
     <div
        v-if="seeMore && !isScope"
        class="pick-up-info-see-more"
        @click="showMap"
      >
       <span>{{ seeMoreText }}</span>
     </div>
     <div
      class="pick-up-info-btn"
      data-spm-module="ViewPickUpPoint"
      data-spm-virtual-item="__virtual"
      @click="showMap"
    >
      <img class="pick-up-info-btn-img" :src="mapData.title_icon" alt="">
      <span class="pick-up-info-btn-text">{{ mapData.title }}</span>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop, Inject } from 'vue-property-decorator'
import isEmpty from 'lodash/isEmpty'

@Component
export default class PickUpInfo extends Vue {
  @Inject("translateI18n") translateI18n!: Function;
  @Inject("currentMapType") currentMapType!: string;
  @Prop({ default: () => {} }) info!: any

  limit: number = 10

  get valid() {
    return !isEmpty(this.info)
  }

  get pick_up_type() {
    return !this.isScope ? 'pick_up_fixed' :'pick_up_scope'
  }

  get point_list() {
    return this.info?.point_list || []
  }

  get seeMore() {
    return Number(this.info.point_count) > this.limit
  }

  get seeMoreText() {
    const num = Number(this.info.point_count) - this.limit
    return `${this.translateI18n('see_more')} (${num}+)`
  }

  get mapData() {
    return this.info?.map_data || {}
  }

  get isScope() {
    return this.mapData?.pick_up_type === 2
  }

  showMap() {
    this.$emit('showMap')
  }
}
</script>

<style lang="scss" scoped>
@mixin text-ellipsis($line-clamp: 2) {
  display: -webkit-box;
  -webkit-line-clamp: $line-clamp;
  -webkit-box-orient: vertical;
  text-overflow: ellipsis;
  overflow: hidden;
}

.pick-up-info {
  padding-bottom: 16px;

  &-title {
    @include font-heading-xs;
    color: $color-text-primary;
    display: flex;
    align-items: center;
  }

  &-tips {
    margin-top: 8px;
    font-size: $fontSize-body-s;
    line-height: 21px;
  }

  &-see-more {
    text-decoration: underline;
    font-size: $fontSize-body-s;
    font-weight: $fontWeight-bold;
    line-height: 21px;
    padding: 8px 0 4px;
  }

  &-text-list {
    &-item {
      margin-top: 4px;
      font-size: $fontSize-body-s;
      line-height: 1.44;
      @include text-ellipsis(1);

      &:before {
        content: '';
        display: inline-block;
        width: 6px;
        height: 6px;
        margin-right: 10px;
        border-radius: 50%;
        background: $color-neutral-800;
        vertical-align: 2px;
      }
    }
  }

  &-btn {
    margin-top: 12px;
    height: 44px;
    font-size: 0;
    color: $color-neutral-900;
    background-color: transparent;
    border: 1px solid $color-neutral-800;
    border-radius: $radius-l;
    padding: 10px 20px;
    display: flex;
    align-items: center;
    justify-content: center;

    &-img {
      width: 24px;
      height: 24px;
    }

    &-text {
      font-size: $fontSize-body-m;
      line-height: 24px;
      font-weight: $fontWeight-bold;
      color: $color-neutral-900;
      margin-left: 8px;
    }
  }
}
</style>

