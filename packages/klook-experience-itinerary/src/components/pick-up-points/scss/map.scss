.pick-up-map-area-marker-wrap {
  display: flex;
  flex-direction: column;
  align-items: center;

  .pick-up-map-area-marker {
    border-width: 1px;
    border-style: solid;
    padding: 6px 12px 6px 12px;
    font-size: $fontSize-body-s;
    border-radius: $radius-xl;
    white-space: nowrap;
  }
  
  .marker-after {
    width: 10px;
    height: 10px;
    border-top-width: 0 !important;
    border-right-width: 0 !important;
    border-left-width: 1px;
    border-bottom-width: 1px;
    border-style: solid;
    position: relative;
    z-index: 1;
    transform: rotate(-45deg);
    transform-origin: 50% 50%;
    margin-top: -6px;
  }
}

@keyframes MarkerScale {
  from {
    transform: scale(1);
  }
  to {
    transform: scale(1.4);
  }
}

@keyframes FadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}