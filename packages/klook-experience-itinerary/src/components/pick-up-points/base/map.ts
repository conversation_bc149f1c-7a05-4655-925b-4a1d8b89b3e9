import { Component, Vue, Inject, Prop } from 'vue-property-decorator'
import type { MapDataItem, SearchResultItem, IMapDataItemTips, IAreaProperties } from '../types/index'
import { mapUtils } from "@klook/map"
import { handleIHTrack } from "../../../utils/inHouseTrack";

@Component
export default class MapBase extends Vue {
  @Inject({ default: () => null }) customAxios!: any
  @Inject({ default: () => () => '' }) translateI18n!: Function;
  @Prop({ type: Object, default: () => null }) pickupInfo!: any
  
  getGeomByIdList(params: any) {
    return this.customAxios.$get('/v1/experiencesrv/area/strative_area_service/get_geom_by_id_list', {
      params
    })
  }

  initFenceData(fence: any[], location: MapDataItem[] = []) {
    const list = fence.reduce((accu: any, curr: any) => {
      const area_id = curr.area_id
      const geojson = location.find((item: MapDataItem) => item.area_id === area_id)
      const area_properties = geojson?.area_properties || {} as IAreaProperties
      const strokeColor = area_properties.stroke
      const fillColor = area_properties.fill
      const coordinates = curr?.geom?.coordinates || []
      coordinates.forEach((polygonList: any) => {
        accu.push({
          polygonList: curr?.geom?.type === 'Polygon' ? [polygonList] : polygonList,
          polygonConfig: {
            strokeColor,
            fillColor
          }
        })
      })
      return accu
    }, [])
    return Object.freeze(list)
  }

  isArea(marker: any) {
    const { pick_up_type } = marker
    return pick_up_type === 2
  }

  getUuid() {
    return parseInt(String(Math.random() * 10000000))
  }

  initMarker(data: MapDataItem | SearchResultItem, isTemporary = false, options: any = {}) {
    const {
      location,
      icon,
      address_desc,
      area_id,
      tips = {},
      pick_up_type,
      group_icon_v2,
      itinerary_attr_value_id = 0
    } = data
    const {
      text = '',
      bg_color = '',
      border_color = '', 
      font_color = '',
      border_width = ''
    } = tips as IMapDataItemTips
    const poptip = this.isArea(data) ? text : address_desc
    // const style = this.isArea(data) ? { backgroundColor: bg_color, borderColor: border_color, color: font_color } : {}
    const style = {
      backgroundColor: bg_color,
      borderColor: border_color,
      color: font_color,
      borderWidth: border_width + 'px'
    }
    const groupIcon = group_icon_v2 || { select: icon, unselect: icon }
    return {
      data,
      location,
      isTemporary,
      center: mapUtils.formatLatLng(location),
      poptip,
      tips_text: text,
      options: {
        anchor: 'bottom',
      },
      area_id,
      style,
      pick_up_type,
      groupIcon,
      ...options,
      id: itinerary_attr_value_id || this.getUuid()
    }
  }


  initPoisMarker(pois: any[] = []) {
    const itineraryMap = pois
    return itineraryMap.map((item: any) => {
      const { group_key = '',  icon_url = '', map: { name: poptip, location: center }, group_icon_v2, attr_id } = item
      const ext = {
        marker_type: group_key
      }
      const tracker = {
        type: 'module',
        spm: 'ItineraryMapMarkerClick',
        exposure: false,
        query: {
          ext: JSON.stringify(ext)
        }
      }
      const groupIcon = group_icon_v2 || { select: icon_url, unselect: icon_url }
      return {
        data: item,
        location: center,
        isTemporary: false,
        center: mapUtils.formatLatLng(center),
        poptip,
        options: {
          anchor: 'bottom'
        },
        area_id: 0,
        style: {},
        pick_up_type: 1,
        tracker,
        groupIcon,
        icon_url,
        id: attr_id
      }
    })
  }

  initCards(pois: any[] = []) {
    const cards: any[] = []
    const { points, areas } = pois.reduce((acc: any, curr: any) => {
      const { pick_up_type } = curr
      if (pick_up_type === 2) {
        acc.areas.push(curr)
      } else {
        const { location, address_desc, location_name, group_name = '', group_time = '' } = curr.data
        const { id } = curr
        acc.points.push({
          imgs: [],
          title: location_name,
          group_time,
          group_name,
          icons: [],
          map: {
            address_desc,
            location
          },
          card_id: id
        })
      }
      return acc
    }, { points: [], areas: [] })

    if (areas.length) {
      const { i18n = {} } = this.pickupInfo || {}
      const { search_title = '', search_description = '', search_text = '' } = i18n
      cards.push(
        {
          is_static_card: true,
          imgs: [],
          title: search_title,
          group_time: '',
          group_name: '',
          icons: [],
          map: {
            address_desc: search_description,
            location: ''
          },
          search_placeholder: search_text
        }
      )
    }

    return [...cards, ...points]
  }

  getBindTracker(tracker: any) {
    return handleIHTrack(tracker);
  }

  getValidAreaId(areas: MapDataItem[] = []) {
    return areas.reduce((acc: number[], item: MapDataItem) => {
      const id = item.area_id
      if (!!id) {
        acc.push(id)
      }
      return acc
    }, [])
  }
  
}