import { Component, Vue, Inject, Prop } from 'vue-property-decorator'
import apis from '../../../utils/apis'

@Component
export default class PoiSearch extends Vue {
  @Prop({ type: Array, default: () => [] }) mapData!: any
  @Inject("translateI18n") translateI18n!: Function;
  @Inject("customAxios") customAxios!: any;
  @Inject("packageId") packageId!: number;
  @Inject("isMobile") isMobile!: boolean;

  cancelToken: any = null
  cancelDetailToken: any = null
  searchResultList: any[] = []
  keywords: string = ''
  currentLoadingIndex: number = -1
  isNotMatch: boolean = false

  get defaultResult() {
    const mapData = this.mapData || []
    return mapData.reduce((acc: any[], curr: any) => {
      const { pick_up_type } = curr
      if (pick_up_type === 1) {
        acc.push(this.initResult(curr))
      }
      return acc
    }, [])
  }

  get renderList() {
    const defaultList = this.isMobile ? [] : this.defaultResult
    // return this.keywords ? this.searchResultList : this.defaultResult
    return this.keywords ? this.searchResultList : defaultList
  }

  initResult(data: any = {}) {
    const { location_name: title, address_desc: sub_title, google_place_id: place_id } = data
    return {
      title,
      sub_title,
      place_id,
      is_default: true,
      detail_data: { ...data }
    }
  }

  async getSearchFun(params: any) {
    if (this.cancelToken) {
      this.cancelToken.cancel('cancel')
    }

    this.cancelToken = this.customAxios?.CancelToken?.source?.() || null

    const res = await this.customAxios.$get(apis.autocomplete, {
      params,
      cancelToken: this.cancelToken?.token
    })

    const result = res?.result?.suggestions ?? []

    return result.map((item: any) => {
      return {
        ...item
      }
    })
  }

  async getPoiDetail(params: any) {
    if (this.cancelDetailToken) {
      this.cancelDetailToken.cancel('cancel')
    }

    this.cancelDetailToken = this.customAxios?.CancelToken?.source?.() || null

    const res = await this.customAxios.$get(apis.getPlaceDetail, {
      params,
      cancelToken: this.cancelDetailToken?.token
    })
  
    return res?.result ?? null
  }
  

}