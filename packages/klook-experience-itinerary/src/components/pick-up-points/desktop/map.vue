<template>
  <div :class="[fullscreen && 'pick-up-map-fixed_container']" @click="handleFullscreen">
    <div class="pick-up-map-wrapper" :class="{ fullscreen: fullscreen }" @click.stop>
      <CloseSvg v-if="fullscreen" class="map-close" @click.native="handleFullscreen" />
      <klk-loading v-if="isFenceLoading" />
      <klk-map
        v-else
        ref="mapRef"
        v-bind="mapConfig"
        class="pick-up-map"
      >
        <klk-map-geo-fence
          v-for="(conf, index) in geoFenceConf"
          :key="index"
          :geo-fence-conf="conf"
          :isWgs84="true"
        />
        <klk-map-marker
          v-for="(marker, index) in markerList"
          :key="index"
          :center="marker.center"
          :options="marker.options"
          :class="{
            'marker-active': checkSelected(marker),
            'is-area': isArea(marker)
          }"
          :z-index="isArea(marker) ? 200 : checkSelected(marker) ? 202 : 100"
          v-bind="getBindTracker(marker.tracker)"
        >
          <klk-poptip
            v-if="!isArea(marker)"
            :value="getPoptips(marker)"
            :z-index="9999"
            :offset="[0, 22]"
            trigger="none"
          >
            <div v-if="marker.poptip || marker.tips_text" slot="content">
              <div v-if="marker.tips_text" class="marker-tips">
                {{ translateI18n('161201') }}: {{ marker.tips_text }}
              </div>
              <div v-if="marker.poptip">
                {{ translateI18n('12032') }}: {{ marker.poptip }}
              </div>
            </div>
            <img
              v-show="!checkSelected(marker)"
              class="pick-up-map-marker"
              :src="marker.groupIcon.unselect"
              @click="handleMarkerClick(marker)"
            />
            <img
              v-show="checkSelected(marker)"
              class="pick-up-map-marker"
              :class="{ 'active': checkSelected(marker) }"
              :src="marker.groupIcon.select"
            />
          </klk-poptip>
          <div
            class="pick-up-map-area-marker-wrap"
            v-else-if="marker.poptip"
          >
            <div
              class="pick-up-map-area-marker"
              :style="marker.style"
            >
              {{ marker.poptip }}
            </div>
            <div class="marker-after" :style="marker.style"></div>
          </div>
          <div v-else></div>
        </klk-map-marker>
        <klk-map-user-location
          v-bind="
            getBindTracker({
              type: 'module',
              spm: 'ItineraryMapFindMyPositionButton',
              exposure: false,
            })
          "
          class="user-location"
        />
        <div class="pick-up-map-zoom">
          <div class="pick-up-map-zoom-plus" @click="handleZoom(1)">
            <klk-icon type="icon_other_plus_xs" size="20"></klk-icon>
          </div>
          <klk-divider></klk-divider>
          <div class="pick-up-map-zoom-minus" @click="handleZoom(-1)">
            <klk-icon type="icon_other_minus_xs" size="20"></klk-icon>
          </div>
        </div>
        <div
          v-if="!fullscreen"
          class="icon-wrap enlarge"
          @click.stop="handleFullscreen"
        >
          <EnlargeIcon class="enlarge-icon"/>
        </div>
      </klk-map>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Ref, Prop, Watch, Inject } from 'vue-property-decorator'
import {
  KlkMap,
  KlkMapMarker,
  KlkMapGeoFence,
  KlkMapUserLocation,
  MapTypes,
  mapUtils,
} from "@klook/map"
import '@klook/map/dist/esm/index.css'
import type { MapDataItem, SearchResultItem } from '../types/index'
import KlkIcon from '@klook/klook-ui/lib/icon'
import KlkDivider from '@klook/klook-ui/lib/divider'
import KlkLoading from "@klook/klook-ui/lib/loading"
import '@klook/klook-ui/lib/styles/components/icon.scss'
import '@klook/klook-ui/lib/styles/components/divider.scss'
import '@klook/klook-ui/lib/styles/components/loading.scss'

import MapBase from '../base/map'

import EnlargeIcon from '../../../imgs/enlarge_icon.svg'
import CloseSvg from "../../../imgs/icon_edit_close_desktop.svg";


@Component({
  components: {
    KlkIcon,
    KlkDivider,
    KlkLoading,
    KlkMap,
    KlkMapMarker,
    KlkMapGeoFence,
    KlkMapUserLocation,
    EnlargeIcon,
    CloseSvg
  }
})
export default class PickUpMap extends MapBase {
  @Prop({ default: () => ([]) }) locationList!: MapDataItem[]
  @Prop({ default: null }) selected!: MapDataItem | SearchResultItem
  @Prop({ default: null }) extraPoint!: MapDataItem | SearchResultItem
  @Prop({ type: String, default: "mapbox" }) mapType!: "mapbox" | "google"
  @Inject("language2provide") language!: string;
  @Ref() mapRef!: any

  // hack 新加 marker 位置不准的问题
  hidePoptip: boolean = false
  map: MapTypes.MapTool | undefined;
  markerList: any[] = []
  geoFenceConf: any = []
  isFenceLoading = false

  fullscreen = false

  get locationPointList() {
    return (this.locationList || []).map((item: any) => mapUtils.formatLatLng(item.location))
  }

  get fencePointList() {
    const list = this.geoFenceConf.reduce((accu: any, curr: any) => {
      (curr.polygonList || []).forEach((list: any[]) => {
        if (Array.isArray(list)) {
          accu.push(...list.map((item: any) => mapUtils.formatLatLng(item.reverse().join(','))))
        }
      })
      return accu
    }, [])
    return Object.freeze(list)
  }

  get mapConfig() {
    const mapType = this.mapType;
    // const mapType = 'mapbox';
    const center = this.locationPointList[0];
    return {
      language: this.language,
      center,
      type: mapType,
      // 本地调试地图需要打开该token, 开发环境记得注释掉
      // googleConf: {
      //   token: "AIzaSyByoaOJMATcSHo6iZ-cofp9vlHU8t64ukw",
      //   libraries: 'drawing,places'
      // },
      height: "100%",
      interactive: "cooperative",
      fullscreenControl: false,
      zoom: 16,
    };
  }

  @Watch('extraPoint')
  selectedChange(val: MapDataItem | SearchResultItem) {
    this.modifyMarker(val)
  }

  resizeMap() {
    if (this.map?.map?.resize) {
      this.map.map.resize()
    }
  }

  handleFullscreen() {
    this.fullscreen = !this.fullscreen
    this.$nextTick(() => {
      // 全屏（css 实现）查看的时候mapbox需要重新计算地图的大小
      // google: 会自动计算，所以不处理
      // mapbox: this.map.map.resize()
      // google: this.map.resize()
      this.resizeMap()
      this.fitMapBounds()
    })
  }

  async mounted() {
    await this.initMarkerAndFence()
    this.initMap()
  }

  getPoptips(marker: any) {
    if (this.isArea(marker)) {
      return true
    }
    return !this.hidePoptip && this.checkSelected(marker)
  }

  checkSelected(marker: any) {
    if (this.isArea(marker)) {
      return true
    }
    return this.selected?.location === marker?.location
  }

  async initMarkerAndFence() {
    await this.getGeoFenceConf()
    this.markerList = (this.locationList || []).map((item) => this.formatMarker(item))
  }

  formatMarker(data: MapDataItem | SearchResultItem, isTemporary = false) {
    return this.initMarker(data, isTemporary, {
      tracker: {
        type: 'module',
        spm: 'ViewPickUpPoint',
        exposure: true,
        query: {
          ext: JSON.stringify({
            Type: isTemporary ? 'customed' : 'fixed'
          }),
        },
      }
    })
  }

  initMap() {
    this.mapRef?.$getMapTool && this.mapRef.$getMapTool.then((map: MapTypes.MapTool) => {
      this.map = map

      // 隐藏默认点marker，其他地图元素也可以用这个方法
      if (typeof this.map?.map?.setOptions === "function") {
        const noPoi = [
          {
            featureType: "poi",
            elementType: "labels.icon",
            stylers: [{ visibility: "off" }],
          },
        ]
        this.map.map.setOptions({ styles: noPoi })
      }
      this.resizeMap()
      this.fitMapBounds()
    })
  }

  async fitMapBounds() {
    if (this.map) {
      // 单个点的情况
      if (this.extraPoint?.location) {
        this.flyTo(this.formatMarker(this.extraPoint, true))
        return
      }

      const points = [...this.fencePointList, ...this.markerList.map((item: any) => item.center)]
      if (points.length) {
        const box = await mapUtils.turfBbox(points, this.mapType as MapTypes.Type)
        const bounds = this.map.createBounds(box.sw, box.ne, { formatLngLat: false })
        this.map.fitBounds(bounds, {
          padding: {
            // top: this.markerList.length ? 100 : 20,
            top: 20,
            left: 20,
            bottom: 20,
            right: 20,
          },
        })
      }
    }
  }

  flyTo(marker: any) {
    const { center } = marker
    if (!this.map || !center) {
      return
    }
    this.map!.flyTo(center)
  }

  handleMarkerClick(marker: any) {
    // this.flyTo(marker)
    // this.fitMapBounds()
    this.$emit('update:selected', marker.data)
  }

  modifyMarker(item: null | MapDataItem | SearchResultItem) {
    this.markerList = this.markerList.filter(marker => !marker.isTemporary)

    // 清空或者自定义的点
    if (item && item.location && !this.markerList.find(marker => marker.location === item.location)) {
      this.markerList.push(this.formatMarker(item, true))
    }

    this.fitMapBounds()
    this.hackPoptip()
  }

  hackPoptip() {
    // hack poptip 导致位置不准的问题
    this.hidePoptip = true
    this.$nextTick(() => {
      this.hidePoptip = false
    })
  }

  handleZoom(num: number) {
    if (this.map?.map?.getZoom) {
      const zoom = this.map.map.getZoom()
      this.map.setZoom(zoom + num)
    }
  }

  async getGeoFenceConf() {
    const areaIdList = this.getValidAreaId(this.locationList || [])
    if (areaIdList.length) {
      this.isFenceLoading = true
      const res = await this.getGeomByIdList({ area_id_list: areaIdList.join(',') })
      this.geoFenceConf =  this.initFenceData(res?.result?.geom_list || [], this.locationList)
      this.isFenceLoading = false
    }
  }
}
</script>

<style lang="scss" scoped>
@import '../scss/map.scss';

.pick-up-map {
  overflow: hidden;
  border-radius: $radius-l;

  &-fixed_container {
    position: fixed;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background: rgba(0, 0, 0, 0.46);
    padding: 10vh 10vw;
    z-index: 3000;
    animation: FadeIn 0.26s ease-in 0s 1;
  }

  &-wrapper {
    width: 100%;
    height: 300px;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    border-radius: $radius-l;
    background: $color-bg-1;

    &.fullscreen {
      width: 100%;
      height: 80vh;
    }

    .map-close {
      position: absolute;
      top: 0px;
      right: -48px;
      width: 32px;
      height: 32px;
      z-index: 9;
      cursor: pointer;
    }
  }

  &-marker {
    width: 28px;
    height: 32px;
    object-fit: contain;

    &.active {
      transform: scale(1.4);
      transform-origin: 50% bottom;
      animation: MarkerScale 0.26s ease-in 0s 1;
    }
  }

  &-zoom {
    position: absolute;
    bottom: 20px;
    right: 16px;
    width: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    background-color: $color-bg-1;
    border-radius: $radius-m;
    box-shadow: $shadow-normal-4;
    z-index: 222;

    &-plus, &-minus {
      padding: 6px;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 32px;
      height: 32px;
      cursor: pointer;
    }
  }
}

::v-deep .klkMap_marker.marker-active {
  z-index: 202 !important;
}

::v-deep .klkMap_marker.is-area {
  z-index: 200 !important;
}

.icon-wrap {
  width: 32px;
  height: 32px;
  position: absolute;
  background-color: $color-bg-1;
  border-radius: $radius-m;
  z-index: 222;
  box-shadow: $shadow-normal-4;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;

  &.enlarge {
    top: 16px;
    right: 16px;
  }

  .enlarge-icon {
    width: 16px;
    height: 16px;
  }
}

.user-location {
  bottom: 104px;
  right: 16px;
  left: auto;
  width: 32px;
  height: 32px;
  padding: 0;
}

.marker-tips {
  text-align: left;
}
</style>
