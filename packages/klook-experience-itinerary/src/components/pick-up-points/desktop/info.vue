<template>
  <div class="pick-up-info">
    <div class="pick-up-info-title">
      {{ info.title }}
    </div>
    <p class="pick-up-info-sentence">
      {{ info.description }}
    </p>
    <div class="pick-up-info-points">
      <div
        v-for="text in info.point_list"
        :key="text"
        class="pick-up-info-point"
      >
        {{ text }}
      </div>
    </div>
    <div
      v-if="!isScope && isSimplified && seeMoreNum > 0"
      class="pick-up-info-more"
      @click="handleSeeMore"
    >
      {{ translateI18n('city_page_view_more') }}({{ seeMoreNum }}+)
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop, Inject } from 'vue-property-decorator'
import type { PickUpInfoData } from '../types/index'

@Component
export default class PickUpInfo extends Vue {
  @Prop({ default: () => ({}) }) info!: PickUpInfoData
  @Prop({ default: false }) isScope!: boolean
  @Inject({ default: false }) isSimplified!: boolean
  @Inject({ default: () => '' }) translateI18n!: Function
  @Inject({ default: () => 1 }) handleEmit!: Function

  limit = 10

  get seeMoreNum() {
    return this.info.point_count - this.limit
  }

  handleSeeMore() {
    if (this.isSimplified) {
      this.handleEmit('pickUpClick')
    }
  }
}
</script>

<style lang="scss" scoped>
// 多行省略
@mixin text-ellipsis($line-clamp: 2) {
  display: -webkit-box;
  -webkit-line-clamp: $line-clamp;
  -webkit-box-orient: vertical;
  text-overflow: ellipsis;
  overflow: hidden;
}

.pick-up-info {
  &-title {
    @include font-heading-xs;
    color: $color-text-primary;
  }

  &-sentence {
    @include font-body-s-regular;
    margin-top: 8px;
    color: $color-text-primary;
  }

  &-point {
    @include font-body-s-regular;
    @include text-ellipsis(1);
    color: $color-text-primary;

    &:before {
      content: '';
      display: inline-block;
      width: 6px;
      height: 6px;
      margin-right: 10px;
      border-radius: 50%;
      background: $color-neutral-800;
      vertical-align: 2px;
    }
  }

  &-more {
    @include font-body-s-bold;
    margin-top: 8px;
    display: inline-block;
    color: $color-text-primary;
    text-decoration: underline;
    cursor: pointer;
  }
}
</style>
