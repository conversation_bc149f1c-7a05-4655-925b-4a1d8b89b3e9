<template>
  <div
    class="pick-up-desktop"
    :data-spm-module="spmModule"
  >
    <PickUpInfo
      v-if="showEntry && info"
      :info="info"
      :is-scope="isScope"
      style="margin-bottom: 12px;"
    />
    <SearchKeywords
      :selected.sync="selected"
      :is-scope="true"
      :map-data="locationList"
      style="margin-bottom: 16px;"
      @change="handleSearchChange"
      @clear="handleClear"
    />
    <!-- 设置最小高度，防止全屏的时候页面抖动 -->
    <div v-if="!isSimplified" class="pickup-wrapper">
      <PickUpMap
        :selected.sync="selected"
        :location-list="locationList"
        :extra-point="extraPoint"
        :map-type="currentMapType || 'google'"
        @fullscreen="handleFullscreen"
      />
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop, Inject, Watch } from 'vue-property-decorator'
import PickUpInfo from './info.vue'
import SearchKeywords from './search-keywords.vue'
import PickUpMap from './map.vue'
import type { PickUpInfoData, MapDataItem, SearchResultItem } from '../types/index'

@Component({
  components: {
    PickUpInfo,
    SearchKeywords,
    PickUpMap
  }
})
export default class PickUpDesktop extends Vue {
  @Prop({ default: null }) info!: PickUpInfoData
  @Inject({ default: false }) isSimplified!: boolean
  @Inject({ default: () => null }) customInhouseTrack!: Function;
  @Inject("currentMapType") currentMapType!: string;

  selected: MapDataItem | SearchResultItem | null = null
  extraPoint: MapDataItem | SearchResultItem | null = null

  showModal:boolean = false

  handleFullscreen() {
    this.showModal = true
  }

  get spmModule() {
    const types = this.locationList.reduce((acc: string[], curr) => {
      const { pick_up_type, is_customized_area } = curr
      if (pick_up_type === 1) {
        acc.push('pick_up_fixed')
      } else {
        acc.push(is_customized_area ? 'pick_up_customized' : 'pick_up_scope')
      }
      return acc
    }, [])
    const typeStr = Array.from(new Set(types)).join(',')
    return `PickMeetUpInformation?ext=${JSON.stringify({ type: typeStr })}`
  }

  // 范围接载点
  // 点和范围混合了，好像也没用了
  get isScope() {
    return this.info?.map_data?.pick_up_type === 2
  }

  get locationList() {
    return this.info?.map_data?.map || []
  }

  @Watch('selected')
  selectedChange(newVal: any, oldVal: any) {
    if (newVal && oldVal && newVal !== oldVal) {
      this.customInhouseTrack('custom', 'body', { spm: 'SwitchPickUpPoint' })
    }
  }

  handleSearchChange(val: MapDataItem | SearchResultItem | null) {
    this.extraPoint = val
  }

  handleClear() {
    // 如果一样的话，就取消选中
    if (this.selected && this.extraPoint && this.selected.location === this.extraPoint.location) {
      this.selected = null
    }
    this.extraPoint = null
  }
}
</script>

<style lang="scss" scoped>
.pickup-wrapper {
  margin-bottom: 16px;
  min-height: 300px;
}
</style>
