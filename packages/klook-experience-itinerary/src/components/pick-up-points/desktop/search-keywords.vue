<template>
  <div :class="[
    'search-keywords',
    !isSimplified && 'js-search-pick-up-keywards-desktop',
    isPopReversed && 'is-pop-reversed'
  ]">
    <div class="search-keywords-wrap">
      <klk-input
        v-model.trim="keywords"
        clearable
        prepend-icon="icon_edit_search_s"
        :placeholder="translateI18n('104032')"
        :class="[
          'search-keywords-input',
          isWarningError && 'is-error'
        ]"
        data-spm-module="LocationSearch"
        data-spm-virtual-item="__virtual"
        @input="handleInput"
        @focus="handleFocus"
        @clear="handleClear"
      />
      <div v-show="popVisible" class="search-keywords-pop">
        <div v-if="showLoading" class="search-keywords-loading">
          <klk-loading />
        </div>

        <div v-else class="search-keywords-content">
          <div v-show="isNotMatch" class="search-keywords-none">
            <div class="search-keywords-none-text" data-spm-module="LocationSearch_NoResult">
              {{ isSearchNone ? translateI18n('106268') : translateI18n('104307') }}
            </div>
            <div
              v-if="isSearchNone"
              class="search-keywords-none-button"
            >
              <klk-button
                type="outlined"
                data-spm-module="LocationSearch_FreeText"
                data-spm-virtual-item="__virtual"
                @click="handleSelect(keywords)"
              >
                {{ translateI18n('106269') }}
              </klk-button>
            </div>
            <template v-else-if="searchResultList.length">
              <klk-divider class="search-keywords-none-divider"></klk-divider>
              <div
                class="search-keywords-none-title"
              >
                {{ translateI18n('104308') }}
              </div>
            </template>
          </div>
          <!-- 埋点用的标签 -->
          <div v-bind="locationSearchResultTracker"></div>
          <div
            v-for="(item, index) in renderList"
            :key="index"
            :class="['search-keywords-item', checkSelected(item) && 'active']"
            :data-spm-module="`LocationSearch_SelectResult?ext=${JSON.stringify({
              InScope: inScopeList[item.data_type]
            })}`"
            data-spm-virtual-item="__virtual"
            @click="handleSelect(item)"
          >
            <div class="search-keywords-item-title" :title="item.title">
              {{ item.title }}
            </div>
            <component
              :is="tagComponent"
              v-if="getTags(item).length"
              :list="getTags(item)"
              :line="1"
              class="search-keywords-item-tags"
            />
            <div class="search-keywords-item-desc" :title="item.sub_title">
              {{ item.sub_title }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <div
      v-if="warningText"
      :class="['search-keywords-warning', isWarningError && 'is-error']"
    >
      <klk-icon
        size="16"
        type="icon_feedback_warning"
        class="search-keywords-warning-icon"
      />
      <span class="search-keywords-warning-text">
        {{ warningText }}
      </span>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Inject, Prop } from 'vue-property-decorator'
import KlkInput from '@klook/klook-ui/lib/input'
import KlkLoading from '@klook/klook-ui/lib/loading'
import KlkDivider from '@klook/klook-ui/lib/divider'
import KlkButton from '@klook/klook-ui/lib/button'
import Toast from '@klook/klook-ui/lib/toast'
import '@klook/klook-ui/lib/styles/components/input.scss'
import '@klook/klook-ui/lib/styles/components/loading.scss'
import '@klook/klook-ui/lib/styles/components/divider.scss'
import '@klook/klook-ui/lib/styles/components/button.scss'
import '@klook/klook-ui/lib/styles/components/toast.scss'
import type { SearchResultItem, MapDataItem } from '../types/index'
import Tag from '../../tag.vue'
import SearchMixin from '../base/poi-search'

@Component({
  components: {
    KlkInput,
    KlkLoading,
    KlkDivider,
    KlkButton
  }
})
export default class SearchKeywords extends SearchMixin {
  @Prop({ default: false }) isScope!: boolean
  @Prop({ default: null }) selected!: MapDataItem | SearchResultItem
  @Inject({ default: null }) customCompObj!: any
  @Inject({ default: () => 1 }) customAxios!: any
  @Inject({ default: false }) isSimplified!: boolean
  @Inject({ default: () => 1 }) handleEmit!: Function
  @Inject({ default: () => '' }) translateI18n!: Function
  @Inject({ default: 0 }) packageId!: number;

  isWarningError = false
  warningText = ''
  popVisible = false
  showLoading = false
  cancelToken: any = null
  isPopReversed = false // 从上面冒出弹窗(此功能暂废弃)
  inScopeList = ['', 'Available', 'Extra fee', 'Not offered']
  searchTimer: any = 0

  get tagComponent() {
    return this.customCompObj?.tagComponent || Tag
  }

  get locationSearchResultTracker() {
    if (!this.isNotMatch && this.searchResultList.length) {
      return {
        'data-spm-module': 'LocationSearch_Result'
      }
    }
    return {}
  }

  get isSearchNone() {
    // itinerary 暂不支持手动输入
    return false
    // 只有scope才支持手动输入
    // return this.isScope && !this.searchResultList?.length
  }

  // scope 的情况，如果关键词为空，则不需要打开下拉框
  get shouldHidePop() {
    return this.isScope && !this.keywords
  }

  get isNotOffered() {
    // 1:free_pick_up 2:extra_fee_need  3:not offered
    return this.selected?.data_type === 3
  }

  get isExtraFee() {
    // 1:free_pick_up 2:extra_fee_need  3:not offered
    return this.selected?.data_type === 2
  }

  // 控制颜色
  get isRealWarningError() {
    return this.isNotOffered
  }

  // 控制文案
  get realWarningText() {
    if (this.isNotOffered) {
      return this.translateI18n('104310')
    }

    if (this.isExtraFee) {
      return this.translateI18n('106531')
    }

    return ''
  }

  get selectedLocation() {
    return this.selected?.location || ''
  }

  // @Watch('selected')
  // selectedChange(val: MapDataItem | SearchResultItem) {
  //   if (val) {
  //     this.keywords = val?.location_name
  //   }
  // }

  mounted() {
    document.addEventListener('click', this.handleClickOutside)
  }

  beforeDestroy() {
    document.removeEventListener('click', this.handleClickOutside)
  }

  validateSearch() {
    // 更新报错状态
    this.isWarningError = this.isRealWarningError
    this.warningText = this.realWarningText
  }

  handleFocus() {
    // 如果是简化模式，则只 emit 事件
    if (this.isSimplified) {
      this.handleEmit('pickUpClick')
      return
    }
    this.handleSearch()
  }

  cancelSearch() {
    if (this.cancelToken) {
      this.cancelToken.cancel('cancel')
      this.cancelToken = null
    }
  }

  handleSearch() {    
    if (!this.keywords) {
      if (this.defaultResult.length) {
        this.openPop()
      } else {
        this.closePop()
      }
      this.isNotMatch = false
      return
    }

    // 如果下拉框没有打开，则要显示 loading
    if (!this.popVisible) {
      this.showLoading = true
    }

    this.openPop()
    clearTimeout(this.searchTimer)
    this.searchTimer = setTimeout(this.search, 300)
  }

  async search() {
    const params = {
      input: this.keywords,
      package_id: this.packageId,
      show_meet_up: 1,
      current_google_place_id: this.selected?.google_place_id || ''
    }
    this.isNotMatch = false

    this.searchResultList = await this.getSearchFun(params)
    this.isNotMatch = !this.searchResultList.length && !!this.keywords
    // const list = await this.getSearchFun(params)
    // this.searchResultList = this.setOrder([...list])
    this.showLoading = false
  }

  handleClear() {
    this.keywords = ''
    this.$emit('clear')
  }

  handleInput() {
    // 清空报错状态
    this.isWarningError = false
    this.warningText = ''
    this.handleSearch()
  }

  checkSelected(item: SearchResultItem) {
    return item?.location === this.selectedLocation
  }

  setOrder(data = []) {
    const selectedItem = this.selected
    if (selectedItem) {
      const location = selectedItem.location
      const list = data.reduce((acc: any[], curr: any) => {
        const currLoaction = curr.detail_data?.location ?? ''
        if (currLoaction === location) {
          acc.unshift(this.initResult(curr))
        } else {
          acc.push(curr)
        }
        return acc
      }, [])

      return list
    }

    return data
  }

  handleClickOutside(e: any) {
    // 判断用户的点击行为是否在 input 框和弹层上
    // 若不是，则收起弹层
    if (this.popVisible && this.$el) {
      const inputDom = this.$el.querySelector('.search-keywords-input')
      const popDom = this.$el.querySelector('.search-keywords-pop')
      if (!inputDom?.contains(e.target) && !popDom?.contains(e.target)) {
        this.cancelSearch()
        this.closePop()
      }
    }
  }

  async handleSelect(item: any) {
    const { detail_data = null, place_id, is_default } = item || {}

    let detail = null
    if (is_default || detail_data) {
      detail = detail_data
    } else {
      item.loading = true
      detail = await this.getPoiDetail({
        package_id: this.packageId,
        place_id,
      })
      
      this.$set(item, 'detail_data', detail)
      item.loading = false
    }
    const data_type = (detail as any)?.data_type ?? -1

    if (data_type === 3) {
      return Toast(this.translateI18n('161196'))
    }

    this.keywords = item.title
    this.$emit('update:selected', detail)
    this.$emit('change', detail)
    this.closePop()

    setTimeout(this.validateSearch, 30)
  }

  openPop() {
    this.popVisible = true
  }

  closePop() {
    this.searchResultList = []
    this.popVisible = false
  }

  getTags(data: any) { 
    return data?.detail_data?.tag ?? []
  }
}
</script>

<style lang="scss" scoped>
// 多行省略
@mixin text-ellipsis($line-clamp: 2) {
  display: -webkit-box;
  -webkit-line-clamp: $line-clamp;
  -webkit-box-orient: vertical;
  text-overflow: ellipsis;
  overflow: hidden;
}

.search-keywords {
  &-wrap {
    position: relative;
  }

  &-input {
    width: 400px;
    max-width: 100%;

    &:hover,
    &.klk-input-is-focus {
      ::v-deep .klk-input-inner {
        border-color: $color-brand-primary;
      }
    }

    &.is-error {
      ::v-deep .klk-input-inner {
        border-color: $color-error;
      }
    }
  }

  &-warning {
    margin-top: 8px;
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;
    color: $color-text-secondary;

    &.is-error {
      color: $color-error;
    }

    &-icon {
      margin: 2px 8px 0 0;
      flex-shrink: 0;
    }

    &-text {
      @include font-body-s-regular;
    }
  }

  &-pop {
    padding: 0 8px;
    position: absolute;
    left: 0;
    bottom: -10px;
    width: 100%;
    max-height: 320px;
    border-radius: $radius-l;
    background-color: $color-bg-1;
    box-sizing: border-box;
    border: 1px solid $color-border-normal;
    overflow-y: auto;
    transform: translateY(100%);
    z-index: 500;
    min-height: 40px;
  }

  &-loading {
    min-height: 100px;
  }

  &-content {
    padding: 12px 0;
  }

  &-none {
    padding: 0 12px;

    &-text {
      @include font-body-s-regular;
      margin: 8px 0 8px;
      color: $color-text-secondary;
    }

    &-divider {
      margin: 16px 0;
    }

    &-button {
      margin: 8px 0;
    }

    &-title {
      @include font-body-s-semibold;
      margin: 20px 0 8px;
      color: $color-text-primary;
    }
  }

  &-item {
    padding: 6px 12px;
    border-radius: $radius-m;
    background-color: $color-bg-1;
    cursor: pointer;

    &:hover {
      background-color: $color-bg-2;
    }

    &.active {
      background-color: $color-brand-primary-light-2;
    }

    &-title {
      @include font-body-m-regular;
      @include text-ellipsis(1);
      margin-bottom: 4px;
      color: $color-text-primary;
    }

    &-tags {
      margin-bottom: 4px;
    }

    &-desc {
      @include font-body-s-regular;
      @include text-ellipsis(1);
      color: $color-text-secondary;
    }
  }

  &.is-pop-reversed {
    .search-keywords-pop {
      top: -10px;
      bottom: unset;
      transform: translateY(-100%);
    }
    .search-keywords-content {
      display: flex;
      flex-direction: column-reverse;
    }
    .search-keywords-none {
      display: flex;
      flex-direction: column-reverse;
    }
  }
}
</style>
