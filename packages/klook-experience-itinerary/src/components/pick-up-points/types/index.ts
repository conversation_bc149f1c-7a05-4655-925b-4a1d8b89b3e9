export interface IMapDataItemTips {
  bg_color: string
  border_color: string
  font_color: string
  text: string,
  border_width: string
}

export interface IGroupIcon {
  select: string
  unselect: string
}

export interface IAreaProperties {
  stroke: string
  stroke_width: 2
  stroke_opacity: number
  fill:string
  fill_opacity: number
}

export interface MapDataItem {
  location: string
  icon: string
  google_place_id: string
  location_name: string
  city_name: string
  address_desc: string
  data_type: number
  area_id: number
  tag: any[]
  pick_up_type?: number
  tips?: IMapDataItemTips
  area_properties?: IAreaProperties
  group_icon_v2?: IGroupIcon
  id?: number
  is_customized_area?: number
  itinerary_attr_value_id?: number
}

export interface SearchResultItem {
  location: string
  icon: string
  location_name: string
  address_desc: string
  google_place_id: string
  data_type: number
  area_id: number
  tag: any[]
  pick_up_type?: number
  tips?: IMapDataItemTips
  group_icon_v2?: IGroupIcon
  id?: number
  is_customized_area?: number
  itinerary_attr_value_id?: number
}

export interface PickUpInfoData {
  title: string
  description: string
  point_list: string[]
  point_count: number
  map_data: {
    map_type: 'string',
    pick_up_type: number, // 1:point 2:scope
    map: MapDataItem[]
  }
}

export interface KeywordsSearchItem {
  location: string,
  address_desc: string,
  location_name: string,
  place_id: string,
  map_type: number,
  amap_code: string,
  data_type: number,
  tag: any[],
  google_place_id: string
}

export interface MapData {
  location: string,
  google_place_id: string,
  address_desc: string,
  icon: string,
  area_id: number,
  administrative_level: number
}