<template>
  <component
    :is="componentName"
    v-bind="$attrs"
    v-on="$listeners"
  />
</template>

<script lang="ts">
import { Component, Vue, Inject, Prop } from 'vue-property-decorator'
import PickUpPointsDesktop from './desktop/index.vue'
import PickUpPointsMobile from './mobile/index.vue'

@Component({
  components: {
    PickUpPointsDesktop,
    PickUpPointsMobile
  }
})
export default class PickUpPoints extends Vue {
  @Inject("isMobile") isMobile!: boolean;

  get componentName() {
    return this.isMobile ? PickUpPointsMobile : PickUpPointsDesktop
  }
}
</script>
