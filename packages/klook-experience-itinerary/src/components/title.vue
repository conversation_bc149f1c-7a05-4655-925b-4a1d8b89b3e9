<template>
  <div
    v-if="item.title || showMap"
    class="title-wrapper"
    :class="[{ 'map-title-wrapper': showMap }]"
  >
    <div class="text-box" @click="handleMap">
      <div class="text">
        <span
          v-if="showMap && !isPrinting"
          class="map1 map"
          :class="[isOver24Height ? 'show' : 'hidden']"
          :data-spm-module="`ItineraryMap?${spmParams}`"
          data-spm-virtual-item="__virtual?type=entry"
        >
          {{ translateI18n('16271') }}
        </span>

        <span v-if="item.title" ref="contentRef"  class="title">{{ item.title }}</span>

        <span
          v-if="showMap && !isPrinting"
          class="map"
          :class="[{'no-margin-left': !item.title}]"
          :data-spm-module="`ItineraryMap?${spmParams}`"
          data-spm-virtual-item="__virtual?type=entry"
        >
        {{ translateI18n('16271') }}
        </span>
      </div>
    </div>

    <Map
      v-if="mapVisible"
      :show.sync="mapVisible"
      v-bind="mapAtrrs"
    />
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop , Inject, Ref} from 'vue-property-decorator'
import { isServer } from '../utils'
import Map from './map.vue'

@Component({
  components: {
    Map
  }
})
export default class Index extends Vue {
  @Prop({ default: () => ({}) }) item!: any
  @Prop({ default: () => ({}) }) trackInfo!: any
  @Inject({ default: () => '' }) translateI18n!: Function
  @Inject('packageId') packageId!: string
  @Inject("platform") platform!: string;
  @Ref() contentRef!: any
  @Inject('customMap') customMap!: boolean;
  @Inject({ default: () => null }) handleShowMap!: Function;
  @Inject('isPrinting') isPrinting!: boolean;


  mapVisible: boolean = false
  isOver24Height: boolean = false
  elementObserver: any = null
  shouldCreateObserver = false

  get showMap() {
    return this.item?.map?.location
  }

  get mapAtrrs() {
    if (this.item?.map) {
      const { location: address, address_desc: addressDesc } = this.item.map || {}
      return {
        address,
        addressDesc
      }
    }

    this.mapVisible && (this.mapVisible = false)

    return {}
  }

  get spmParams() {
    return `oid=package_${this.packageId}&ext=${JSON.stringify({ MapType: this.trackInfo.type })}`
  }

  handleMap() {
    if (this.customMap) {
      this.handleShowMap(this.item)
      return
    }
    if (this.showMap) {
      this.mapVisible = true
    }
  }

  getTextHeight() {
    if (!isServer) {
      const target = this.$el && this.$el.querySelector && this.$el.querySelector('.title')
      if (target) {
        const { height } = target.getBoundingClientRect()
        if(height === 0) {
          this.shouldCreateObserver = true
        } else if (height > 24) {
          this.isOver24Height = true
        }
      }
    }
  }

  createObserver() {
    // 解决 display:none 获取不到宽度的问题，只检测一次
    if ('IntersectionObserver' in window) {
      this.elementObserver = new IntersectionObserver(
        (entries) => {
          entries.forEach((entry) => {
            // 如果元素可见
            if (entry.intersectionRatio > 0) {
              this.getTextHeight()
              this.destroyObserver()
            }
          })
        },
        { threshold: [0.1] }
      )

      this.contentRef && this.elementObserver.observe(this.contentRef)
    }
  }

  destroyObserver() {
    this.elementObserver && this.contentRef && this.elementObserver.unobserve(this.contentRef)
  }

  beforeDestroy() {
    this.destroyObserver()
  }

  mounted() {
    this.$nextTick(() => {
      this.getTextHeight()

      if (this.shouldCreateObserver) {
        this.createObserver()
      }
    })
  }
}
</script>

<style lang="scss" scoped>
.title-wrapper {

  .text-box {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    text-overflow: ellipsis;
    overflow: hidden;

    .text {
      font-size: $fontSize-body-s;
      line-height: 21px;
      font-weight: $fontWeight-bold;
      color: $color-neutral-900;
      position: relative;

      .title {
        white-space: pre-line;
        word-break: break-word;
      }

      .map {
        text-decoration: underline;
        margin-left: 6px;
        cursor: pointer;

        &.map1 {
          float: right;
          clear: both;
        }

        &.no-margin-left {
          margin-left: 0 !important;
        }
      }

      &::after{
        position: absolute;
        content: '';
        display: inline-block;
        width: 100%;
        height: 100%;
        background: #fff;
      }

      &::before  {
        content: '';
        display: block;
        float: right;
        width: 0;
        height: 21px;
      }
    }
  }

  &.map-title-wrapper {
    cursor: pointer;
  }
}

.show {
  display: inline-block;
}

.hidden {
  display: none;
}
</style>
