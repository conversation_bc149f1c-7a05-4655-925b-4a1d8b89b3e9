<template>
  <div class="view-more" :class="{ 'has-shadow': !hideShadow }">
    <span class="view-more_text">
      <slot />
    </span>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator'

@Component
export default class ViewMore extends Vue {
  @Prop() hideShadow!: boolean
}
</script>

<style lang="scss" scoped>
.view-more {
  @include font-body-s-bold;
  position: relative;
  width: 100%;
  display: flex;
  align-items: center;
  color: $color-text-primary;
  text-decoration: underline;

  &.has-shadow {
    &::before {
      content: '';
      position: absolute;
      top: -30px;
      left: 0;
      width: 100%;
      height: 16px;
      background-image: linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.5) 52.08%, #FFFFFF 100%);
      pointer-events: none;
    }

    &::after {
      content: '';
      position: absolute;
      top: -15px;
      left: 0;
      width: 100%;
      height: 16px;
      background: $color-bg-widget-normal;
      pointer-events: none;
    }
  }

  .view-more_text {
    cursor: pointer;
    text-decoration: underline;
  }
}
</style>
