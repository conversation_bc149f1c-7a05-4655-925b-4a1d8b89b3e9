<template>
  <div v-if="valid" class="icons-wrapper" :class="{'flex-group': isImageSwiper}">
    <div
      v-for="(item, index) in iconList"
      :key="index"
      class="icon-item"
    >
      <img class="icon" :src="item.icon" alt="" />
      <span class="text">{{ item.text }}</span>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop, Inject } from 'vue-property-decorator'
import { Icon } from '../types'

@Component
export default class Index extends Vue {
  @Prop({ default: () => [] }) iconList!: Icon[]
  @Inject('isImageSwiper') isImageSwiper!: boolean;
  
  get valid() {
    return this.iconList?.length
  }
}
</script>

<style lang="scss" scoped>
.icons-wrapper {

  &.flex-group {
    display: flex;
    flex-wrap: wrap;

    .icon-item {
      max-width: 100%;
      padding-right: 20px;

      .text {
        max-width: calc(100% - 24px);
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }

  .icon-item {
    display: flex;
    margin-bottom: 4px;

    .text {
      font-size: $fontSize-body-s;
      line-height: 21px;
      color: $color-neutral-900;
    }

    .icon {
      width: 16px;
      height: 16px;
      margin-right: 8px;
      margin-top: 2px;
    }

    &:first-of-type {
      margin-top: 0;
    }
  }
}
</style>
