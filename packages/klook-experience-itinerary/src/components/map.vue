<template>
  <transition v-if="isDesktop" name="fade">
    <div class="page-activity-map-container" @click.stop="hideMap">
      <div class="page-activity-map-wrap" @click.stop>
        <Klk-map
          :language="language"
          height="100%"
          :center="cloneAddress"
          :zoom="zoom" 
          :type="mapStyle"
        >
          <klk-map-marker :center="cloneAddress">
            <span v-if="addressDesc" slot="poptip">
              {{ addressDesc }}
            </span>
          </klk-map-marker>
        </Klk-map>
      </div>
      <div class="map-close" @click.stop="hideMap">
        <CloseSvg style="width: 100%; height: 100%" />
      </div>
    </div>
  </transition>
  <klk-modal v-else fullscreen :open.sync="mapShow">
    <div class="page-activity-map-container">
      <Klk-map :language="language" height="100%" :center="cloneAddress" :zoom="zoom" :type="mapStyle">
        <klk-map-marker :center="cloneAddress">
          <span v-if="addressDesc" slot="poptip">
            {{ addressDesc }}
          </span>
        </klk-map-marker>
      </Klk-map>
      <div class="map-close" @click="hideMap">
        <CloseSvg style="width: 18px; height: 18px" />
      </div>
    </div>
  </klk-modal>
</template>

<script lang="ts">
import { Vue, Component, Prop, Watch, Inject } from "vue-property-decorator";
import { KlkMap, KlkMapMarker } from "@klook/map";
import "@klook/map/dist/esm/index.css";
import CloseSvg from "../imgs/icon_edit_close_mobile.svg";


@Component({
  components: {
    KlkMapMarker,
    KlkMap,
    CloseSvg,
  },
})
export default class ActivityMapLayer extends Vue {
  @Prop({ default: "" }) address: string | undefined;
  @Prop({ default: "" }) addressDesc: string | undefined;
  @Prop() show: boolean | undefined;
  @Prop({ default: 16 }) zoom!: number;
  @Inject("platform") platform!: string;
  @Inject("mapStyle") mapStyle!: string;
  @Inject("language2provide") language!: string;

  @Watch("show")
  onShowChange() {
    this.showMap();
  }

  get cloneAddress() {
    return this.address ? this.address.split(",").reverse().join(",") : "";
  }

  get isDesktop() {
    return this.platform === "desktop";
  }

  mapShow: boolean = false;

  showMap() {
    this.mapShow = true;
  }

  hideMap() {
    this.mapShow = false;
    this.$emit("update:show", false);
  }

  mounted() {
    this.address && this.showMap();
  }
}
</script>

<style scoped lang="scss">
.fade-enter-active,
.fade-leave-active {
  transition: all 0.4s ease;
}

.fade-enter,
.fade-leave-to {
  opacity: $opacity-transparent;
}

.page-activity-map-container {
  position: fixed;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  transition: opacity ease 333ms;
  background: rgba(0, 0, 0, 0.65);
  width: 100%;
  height: 100%;
  padding: 10vh 10vw;

  .page-activity-map-wrap {
    position: relative;
    border-radius: $radius-xl;
    overflow: hidden;
    width: 100%;
    height: 100%;
    background-color: $color-bg-widget-normal;
  }

  .map-close {
    position: absolute;
    top: 10vh;
    right: calc(10vw - 48px);
    width: 32px;
    height: 32px;
    z-index: 9;
    cursor: pointer;
  }

  #activity-big-map {
    width: 100%;
    height: 100%;
  }
  ::v-deep .klk-poptip-popper {
    text-align: center;
  }
}
</style>
