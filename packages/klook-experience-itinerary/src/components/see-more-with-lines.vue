<template>
  <div
    class="see-more-with-lines"
    :class="{
      'cursor-pointer': isShowMoreBtn && !hideMoreButton
    }"
    @click="showMore()"
  >
    <p
      ref="contentRef"
      class="content"
      :class="{ 'not-truncated': isShowMore }"
      :style="{
        '-webkit-line-clamp': maxLines,
        'white-space': 'pre-line'
      }"
      v-html="content"
    ></p>
    <div v-show="isShowMoreBtn && !hideMoreButton" class="more-btn-wrap">
      <span class="more-btn">
        <span>
          {{ viewMoreText }}
        </span>
        <span v-if="showMoreIcon" class="more-btn-icon">
          <ArrowRightSvg style="margin-left: 4px; width: 14px; height: 14px" />
        </span>
      </span>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Ref, Inject } from 'vue-property-decorator'
import ArrowRightSvg from '../imgs/arrow-standard-right-new.svg'
import { isServer } from '../utils'

@Component({
  components: {
    ArrowRightSvg
  }
})
export default class SeeMoreWithLines extends Vue {
  @Prop({ default: 2 }) maxLines!: number
  @Prop() content!: string[]
  @Prop({ default: false }) hideLess!: boolean
  @Prop({ default: false }) showMoreIcon!: boolean
  @Prop({ default: false }) visible!: boolean // 慎重使用
  @Inject({ default: () => '' }) translateI18n!: Function
  @Ref() contentRef!: any

  isShowMore = true
  isShowMoreBtn = false
  elementObserver: any = null
  shouldCreateObserver = false

  get viewMoreText() {
    return this.isShowMore ? this.translateI18n('collapse_all') : this.translateI18n('city_page_view_more')
  }

  // 支持展开后隐藏收起按钮
  get hideMoreButton() {
    return this.isShowMore && this.hideLess
  }

  // @Watch('content')
  // contentChange() {
  //   this.$nextTick(this.checkHeight)
  // }

  // @Watch('visible')
  // visibleChange(val: boolean) {
  //   // 只在没有showmore的情况下检查
  //   if (val && !this.isShowMoreBtn) {
  //     this.$nextTick(this.checkHeight)
  //   }
  // }

  // mounted() {
  //   this.checkHeight()

  //   if (this.shouldCreateObserver) {
  //     this.createObserver()
  //   }
  // }

  beforeDestroy() {
    this.destroyObserver()
  }

  showMore() {
    if (!this.hideMoreButton) {
      this.isShowMore = !this.isShowMore
      this.$emit('show-more')
    }
  }

  checkHeight() {
    // 如果已经显示了查看更多，则不再检测
    if (this.isShowMoreBtn) {
      return
    }

    if (!isServer) {
      const contentDom = this.contentRef as any
      if (contentDom && (contentDom.scrollHeight > contentDom.offsetHeight)) {
        this.isShowMoreBtn = true
      } else if (contentDom && (contentDom.scrollHeight === 0)) {
        this.shouldCreateObserver = true
      }
    }
  }

  createObserver() {
    // 解决 display:none 获取不到宽度的问题，只检测一次
    if ('IntersectionObserver' in window) {
      this.elementObserver = new IntersectionObserver(
        (entries) => {
          entries.forEach((entry) => {
            // 如果元素可见
            if (entry.intersectionRatio > 0) {
              this.checkHeight()
              this.destroyObserver()
            }
          })
        },
        { threshold: [0.1] }
      )

      this.contentRef && this.elementObserver.observe(this.contentRef)
    }
  }

  destroyObserver() {
    this.elementObserver && this.contentRef && this.elementObserver.unobserve(this.contentRef)
  }
}
</script>

<style lang="scss" scoped>
.see-more-with-lines {
  &.cursor-pointer {
    cursor: pointer;
  }
}

.content {
  @include font-body-s-regular;

  display: -webkit-box;
  color: $color-text-primary;
  -webkit-box-orient: vertical;
  -moz-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-bottom: 4px;

  &.not-truncated {
    -webkit-line-clamp: unset!important;
  }

  &:last-of-type {
    margin-bottom: 0;
  }
}

.more-btn-wrap {
  margin-top: 4px;
  display: flex;
  justify-content: flex-start;
}

.more-btn {
  @include font-body-s-bold;
  display: flex;
  align-items: center;
  cursor: pointer;
  color: $color-text-primary;
  text-decoration: underline;

  &-icon {
    display: flex;
    align-items: center;
  }
}
</style>
