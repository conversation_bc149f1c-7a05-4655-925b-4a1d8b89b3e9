<template>
  <div class="map-wrapper">
    <div
      v-if="itineraryPOI.thumbnail_img && itineraryPOI.thumbnail_text"
      class="map-container"
      v-bind="buttonTracker"
      @click="toggleMap"
    >
      <div class="static-map">
        <img :src="itineraryPOI.thumbnail_img" class="static-map-img">
      </div>
      <klk-button
        class="map-button"
        size="small"
        type="secondary"
        icon="icon_travel_location_fill"
        round
      >
        {{ itineraryPOI.thumbnail_text }}
      </klk-button>
    </div>

    <klk-bottom-sheet
      :visible.sync="showMap"
      :can-pull-close="false"
      height="100%"
      :z-index="2030"
      transfer
      class="map-bottom-sheet"
    >
      <div class="map-wrap">
        <Map
          v-if="createdMap"
          :itinerary-map="itineraryMap"
          :current-poi="currentPoi"
          :map-type="mapType"
          @close="closeMap"
        />
      </div>
    </klk-bottom-sheet>
  </div>
</template>

<script lang="ts">
import { Vue, Component, Prop, Watch } from 'vue-property-decorator'
import KlkButton from '@klook/klook-ui/lib/button'
import '@klook/klook-ui/lib/styles/components/button.scss'

import KlkBottomSheet from '@klook/klook-ui/lib/bottom-sheet'
import '@klook/klook-ui/lib/styles/components/bottom-sheet.scss'

import Map from './components/map/index.vue'
import { handleIHTrack } from '../../utils/inHouseTrack'

@Component({
  components: {
    Map,
    KlkButton,
    KlkBottomSheet
  }
})
export default class ItineraryMap extends Vue {
  @Prop() itineraryData!: any
  @Prop() currentPoi!: any
  @Prop() mapType!: String


  showMap: boolean = false
  createdMap: boolean = false

  get itineraryPOI() {
    return this.itineraryData?.pois ?? {}
  }

  get itineraryMap() {
    return this.itineraryPOI?.poi_data ?? []
  }

  get buttonTracker() {
    return handleIHTrack({
      type: 'module',
      spm: 'ItineraryMapPreview'
    })
  }

  @Watch('currentPoi', { deep: true, immediate: true })
  currentPoiChange(val: any) {
    if (val) {
      this.toggleMap()
    }
  }

  closeMap() {
    this.toggleMap()
    this.$emit('close')
  }

  staticMapHash(str: string): any {
    let hash = 0
    if (str.length === 0) {
      return hash
    }
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i)
      hash = (hash << 5) - hash + char
      hash = hash & hash
    }
    return String(hash)
  }

  toggleMap() {
    this.showMap = !this.showMap
    this.createdMap = true
  }
}
</script>

<style lang="scss" scoped>
@mixin map-position() {
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  position: absolute;
}

.map-wrap {
  height: 100%;
  width: 100%;
}

.map-container {
  border-radius: $radius-xl;
  height: 100px;
  width: 100%;
  margin-bottom: 24px;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  overflow: hidden;

  .static-map {
    @include map-position;
    z-index: 1;
    .static-map-img {
      width: 100%;
      height: 100px;
      object-fit: cover;
    }
  }
  .map-button {
    max-width: calc(100% - 32px);
    position: relative;
    z-index: 2;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

.map-bottom-sheet ::v-deep .klk-bottom-sheet-inner {
  padding: 0 !important;
  max-height: 100% !important;
  border-radius: 0 !important;
  .klk-bottom-sheet-body {
    padding: 0 !important;
  }
}
</style>
