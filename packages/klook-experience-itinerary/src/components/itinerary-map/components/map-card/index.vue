<template>
  <div class="card-wrap">
    <img
      v-if="info.image"
      :src="info.image"
      class="image"
      v-bind="getBindTracker({
        type: 'module',
        spm: 'ItineraryMapCardImage',
        exposure: false
      })"
    >
    <div class="info-wrap">
      <div class="info-top">
        <div class="info-top-left">
          <div v-if="info.title" class="bold-head line-clamp">{{ info.title }}</div>
          <div class="head ellipsis">
            {{ info.group_time }}
            <span v-if="info.group_time" class="dot"></span>
            {{ info.group_name }}
          </div>
          <div v-for="(item, idx) in info.icons" :key="idx" class="head">{{ item.text }}</div>
        </div>
      </div>
      <div
        v-if="info.address"
        class="sub-head"
        v-bind="getBindTracker({
          type: 'module',
          spm: 'ItineraryMapCardAddress',
          exposure: false
        })"
        @click="copyAddress"
      >
        <div class="address line-clamp">{{ info.address }}</div>
        <div v-if="!info.is_static_card" class="copy-wrap">
          <CopyIconSvg style="width: 16px; height: 16px"/>
        </div>
      </div>
      <div
        v-if="info.is_static_card"
        class="search-area"
        v-bind="getBindTracker({
          type: 'module',
          spm: 'LocationSearch',
          exposure: true
        })"
        @click.stop="$emit('search')"
      >
        <SearchGlassIcon class="search-icon" />
        <div class="search-text">{{ info.search_placeholder }}</div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Vue, Component, Prop, Inject } from 'vue-property-decorator'
import { copyToClipboard, setNewImageSize } from '../../../../utils'
import { handleIHTrack } from '../../../../utils/inHouseTrack'

import CopyIconSvg from '../../../../imgs/icon_other_copy_s.svg'
import SearchGlassIcon from '../../../../imgs/search-glass-icon.svg'

import Toast from '@klook/klook-ui/lib/toast'



@Component({
  components: {
    CopyIconSvg,
    SearchGlassIcon
  }
})
export default class MapCard extends Vue {
  @Prop() data!: any
  @Inject({ default: () => null }) translateI18n!: Function

  async copyAddress() {
    const text = this.info.address
    const result = await copyToClipboard(text)
    
    if (result) {
      Toast(this.translateI18n('copy_success'))
    }
  }

  getBindTracker(tracker: any) {
    return handleIHTrack(tracker)
  }

  get info() {
    const { title, icons, map, group_name, group_time, imgs, is_static_card, search_placeholder } = this.data || {}
    const location = map?.location ?? ''
    const address = map?.address_desc ?? ''
    const image = imgs?.[0]?.src ?? ''
    // 只要1条数据
    const iconList = Array.isArray(icons) ? icons.slice(0, 1) : []
    return {
      title,
      icons: iconList,
      address,
      group_time,
      group_name,
      location,
      image: image && setNewImageSize(image, 'image/upload/', 240, 240, 0),
      is_static_card,
      search_placeholder
    }
  }
}

</script>

<style lang="scss" scoped>
.search-area {
  display: flex;
  padding: 8px 20px;
  align-items: center;
  border-radius: $radius-m;
  background-color: $color-bg-3;
  margin-top: 12px;

  .search-icon {
    width: 16px;
    height: 16px;
    margin-right: 3px;
  }

  .search-text {
    @include font-body-s-regular;
  }
}

.card-wrap {
  background: $color-bg-1;
  box-shadow: $shadow-normal-4;
  border-radius: $radius-xl;
  display: flex;
  padding: 12px;
  height: 150px !important;

  .image {
    width: 80px;
    height: 80px;
    object-fit: cover;
    flex: none;
    border-radius: $radius-l;
    margin-right: 12px;
  }
  .info-wrap {
    flex: 1;
    color: $color-text-primary;
    .info-top {
      display: flex;
      &-left {
        flex: 1;
        padding-right: 12px;
        .head {
          @include font-body-s-regular;
          .dot {
            width: 3px;
            height: 3px;
            border-radius: $radius-circle;
            background: $color-text-primary;
            display: inline-block;
            vertical-align: middle;
          }
        }
        .bold-head {
          max-height: 42px;
          @include font-body-s-bold;
        }
      }
    }
    .sub-head {
      padding-top: 4px;
      color: $color-text-secondary;
      display: flex;
      @include font-caption-m-regular;
      .address {
        flex: 1;
        max-height: 40px;
      }
      .copy-wrap {
        flex: none;
        align-self: flex-end;
        width: 32px;
        height: 19px;
        display: flex;
        justify-content: flex-end;
        background: linear-gradient(270deg, #FFFFFF 0%,#FFFFFF 50%, rgba(255, 255, 255, 0) 100%);
      }
    }
  }
}
.ellipsis {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  word-break: break-all;
}

.line-clamp {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}
</style>
