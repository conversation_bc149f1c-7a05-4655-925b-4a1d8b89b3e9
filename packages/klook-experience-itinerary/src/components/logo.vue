<template>
  <img :src="src" alt="" :style="style" class="v-lazy-logo" />
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch } from 'vue-property-decorator'
import { isServer } from '../utils'


@Component
export default class Index extends Vue {
  @Prop({ type: Number }) width!: number
  @Prop({ type: String }) ratio!: string

  style = {}
  src = 'https://res.klook.com/image/upload/image_logo_mx7wgd.png'

  @Watch('width', { immediate: true })
  widthChange(width: number) {
    if (!isServer && this.ratio) {
      let [imgWidthRatio] = this.ratio.split(':')
      let imgWidth
      // 防止ratio中间有间隔的情况
      imgWidthRatio = imgWidthRatio.trim()

      switch (imgWidthRatio) {
        case '16':
          imgWidth = (width * 3) / 16
          break
        case '3':
        case '4':
          imgWidth = width / 4
          break
        case '1':
          imgWidth = width / 3
          break
      }

      this.style = {
        width: imgWidth + 'px'
      }
    }
  }
}
</script>

<style scoped lang="scss">
.v-lazy-logo {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 0;
}
</style>
