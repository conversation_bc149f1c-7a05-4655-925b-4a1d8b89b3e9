<template>
  <div v-if="valid" class="icons-wrapper">
    <div
      v-for="(item, index) in icons"
      :key="index"
      class="icon-item"
      :class="classList"
    >
      <img class="icon" :src="item.icon" alt="" />
      <span class="text">{{ item.text }}</span>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop, Inject } from 'vue-property-decorator'

@Component
export default class IconGroup extends Vue {
  @Prop({ default: () => [] }) icons!: any []
  @Inject('isImageSwiper') isImageSwiper!: boolean;

  get valid() {
    return this.icons?.length
  }

  get classList() {
    if (this.isImageSwiper) {
      return ['const-width']
    }
    const len = this.icons?.length || 0
    return len % 2 ? ['odd-icons'] : []
  }
}
</script>

<style lang="scss" scoped>
.icons-wrapper {
  display: flex;
  flex-wrap: wrap;

  .icon-item {
    display: flex;
    width: 50%;
    padding-bottom: 4px;

    &.const-width {
      width: auto;
      max-width: 100%;

      .text {
        max-width: calc(100% - 24px);
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }

    &.odd-icons:nth-last-child(1) {
      width: 100%;
    }
  
    &:nth-of-type(2n + 1) {
      padding-right: 12px;
    }
  
    .text {
      color: $color-text-primary;
      @include font-body-s-regular;
    }

    .icon {
      width: 16px;
      height: 16px;
      margin-right: 8px;
      margin-top: 2px;
    }

    &:first-of-type {
      margin-top: 0;
    }
  }
}
</style>
