<template>
  <div class="itinerary-text">
    <template v-if="componentData.icons && componentData.icons.length > 0">
      <div class="itinerary-text-icons">
        <IconGroup :icons="componentData.icons" />
      </div>
    </template>
    <div
      v-if="textsList.length"
      class="itinerary-text-content"
    >
      <div 
        v-for="(item, index) in textsList" 
        :key="`${item.type}-${index}`" 
        class="itinerary-text-content-wrap"
      >
        <div v-if="isNewPickUp" class="itinerary-text-content-item-icon">
          <img 
            v-if="item.icon" 
            :src="item.icon" 
            class="itinerary-text-content-item-icon-img"
          >
        </div>
        <SeeMoreWithLines
          v-if="item.type === 'text'"
          :content="item.text"
          :hide-less="true"
          :max-lines="9999"
          :hide-more-button="true"
          class="itinerary-text-content-item"
          :class="[isNewPickUp ? 'item-desc' : 'item-text']"
          @show-more="handleShowMore"
        />
        <div
          v-else-if="['desc', 'dark_desc'].includes(item.type)"
          class="itinerary-text-content-item item-text"
          :class="[isNewPickUp ? 'item-desc' : 'item-text']"
        >
          {{ item.text }}
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop, Inject } from 'vue-property-decorator'
import { ItineraryComponentTextData } from '../../types'
import SeeMoreWithLines from '../see-more-with-lines.vue'
import IconGroup from '../icon-group/index.vue'
import cloneDeep from 'lodash/cloneDeep'

@Component({
  components: {
    SeeMoreWithLines,
    IconGroup
  }
})
export default class ItineraryComponentText extends Vue {
  @Prop() componentData!: ItineraryComponentTextData
  @Inject('platform') platform!: string
  @Inject("isMobile") isMobile!: boolean;
  @Inject("isNewPickUp") isNewPickUp!: boolean;

  handleShowMore() {
    this.$emit('check-height')
  }

  get textsList() {
    return cloneDeep(this.componentData?.texts ?? []).map((item: any) => {
      if (item.type === 'bold') {
        const reg = new RegExp(item.bold, 'g')
        item.text = item.text.replace(reg, `<b>${item.bold}</b>`)
        item.type = 'text'
      }
      return item
    })
  }
}
</script>

<style lang="scss" scoped>
.itinerary-text {
  &-icons {
    margin-bottom: 8px;

    &:last-child {
      margin-bottom: 0;
    }

    &-item {
      margin-bottom: 4px;
      display: flex;
      align-items: flex-start;
      justify-content: flex-start;

      &:last-child {
        margin-bottom: 0;
      }

      .item-icon {
        margin: 2px 8px 0 0;
        display: block;
        width: 16px;
        height: 16px;
      }

      .item-text {
        @include font-body-s-regular;
        color: $color-text-primary;
        /* stylelint-disable-next-line */
        line-height: 20px;
      }
    }
  }

  &-content {
    &-wrap {
      display: flex;
      align-items: flex-start;
      margin-bottom: 4px;

      &:nth-last-child(1) {
        margin-bottom: 0;
      }
    }

    &-item-icon {
      width: 20px;
      margin-right: 12px;
      flex: none;

      &-img {
        display: block;
        width: 100%;
      }
    }
  
    &-item {
      flex: 1;
      @include font-body-s-regular;
      word-break: break-word;

      &.item-text {
        color: $color-text-primary;
      }

      &.item-desc {
        color: $color-text-secondary;
        @include font-body-m-regular;
      
        ::v-deep .content {
          color: $color-text-secondary;
          @include font-body-m-regular;
        }
      }
    }
  }
}
</style>
