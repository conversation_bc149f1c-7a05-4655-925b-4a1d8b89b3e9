<template>
  <div v-if="valid" class="itinerary-map-wrapper">
    <poiItem
      v-for="(item, index) in poiData"
      :key="index"
      :item="item"
      :track-info="trackInfo"
      :not-icon="notIcon"
      :hide-title="hideTitle"
      @mounted="poiItemMounted"
      @showImageViewer="handleShowImageViewer"
    />
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop, Inject } from 'vue-property-decorator'
import KlkPoptip from '@klook/klook-ui/lib/poptip'
import '@klook/klook-ui/lib/styles/components/poptip.scss'
import poiItem from '../poi-item.vue'
import { PoiImgs } from '../../types'
import { isServer } from '../../utils'

if (!isServer) {
  const VueAwesomeSwiper = require('vue-awesome-swiper/dist/ssr')
  Vue.use(VueAwesomeSwiper)
}


Vue.use(KlkPoptip)

@Component({
  components: {
    poiItem
  }
})
export default class POIIndex extends Vue {
  @Prop({ default: false }) hackCloseBtn?: boolean
  @Prop() showImageFn!: Function
  @Prop({ default: () => ({}) }) componentData!: any
  @Prop({ default: () => ({}) }) trackInfo!: any
  @Prop({ default: false }) notIcon?: boolean
  @Prop({ default: false }) hideTitle?: boolean

  @Inject('platform') platform!: string
  @Inject('showImagesViewer') showImagesViewer!: Function

  hidden: boolean = false

  poiItemMounted(hidden: boolean = true) {
    this.hidden = hidden
  }

  get valid() {
    const length = this.componentData?.poi_data?.length
    if (length) {
      // 长度为1时，attraction 隐藏了标题，样式会有问题
      if (length === 1 && this.hidden) {
        return false
      } else {
        return true
      }
    } else {
      return false
    }
  }

  get poiData() {
    return this.componentData?.poi_data || []
  }


  handleShowImageViewer(imgs: PoiImgs[], title: string, index?: number, currentPoi?: any) {
    if (typeof this.showImageFn === 'function') {
      this.showImageFn(imgs, title, index)
      return
    }
    this.showImagesViewer(currentPoi, index)
  }
}
</script>

<style lang="scss" scoped>
  .merchant-image-description {
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    color: $color-text-reverse;
    padding: 16px 20px calc(env(safe-area-inset-bottom) + 16px) 20px;
    max-height: 133px;
    overflow: auto;
    z-index: 1;
    box-sizing: border-box;
    background-color: rgba(0, 0, 0, 0.8);
    border-radius: $radius-xl $radius-xl 0 0;
    @include font-body-s-regular;
  }
</style>

