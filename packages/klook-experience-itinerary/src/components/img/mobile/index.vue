<template>
   <div v-if="valid" class="image-container">
    <div class="image-container-wrapper">
      <div class="image-container-wrapper_left" >
        <img
          v-for="(item, index) in imagesObject.left"
          :key="index"
          class="banner-images"
          :src="item"
          :style="leftImgStyle"
          @click="imgClickEvent(0)"
        />
      </div>

      <div v-if="imgs.length > 2" class="image-container-wrapper_right">
        <div
          v-for="(item, index) in imagesObject.right"
          :key="index"
          class="container-item"
        >
          <img
            class="banner-images"
            :src="item"
            :style="rightImgStyle"
            @click="imgClickEvent(index + 1)"
          >
        </div>
      </div>
    </div>
    <div v-if="imgCount" class="img-count" @click="imgClickEvent(0)">{{ imgCount }}</div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop, Inject } from 'vue-property-decorator'
import { isServer, replacedImgSrc, getCropDefined } from '../../../utils'
const mobileCropDefined = getCropDefined('mobile')
const initUnit: any = {
  width: '0',
  height: '0',
  backgroundSize: '0'
}

@Component
export default class ImageContainer extends Vue {
  @Prop() imgs!: string[]
  @Prop({ default: false }) notIcon?: boolean
  @Inject({ default: null }) getCropUrl2provide!: Function;

  leftImgStyle = initUnit
  rightImgStyle = initUnit
  borderRadius = '16px'

  imagesObject:{ left: string[], right: string[] } =  {
    left: [],
    right: []
  }


  mounted() {
    this.calcImgStyle()
  }

  get valid() {
    return this.imgs?.length
  }

  get imgCount() {
    let count: any = null

    if (this.valid) {
      const imgLength = this.imgs.length

      if (imgLength === 2) {
        count = '+1'
      }

      if (imgLength > 3) {
        count = `+${imgLength - 3}`
      }
    }

    return count
  }

  imgClickEvent(index: number) {
    this.$emit('viewImages', index)
  }

  getCropUrl(src: string) {
    const { getCropUrl2provide } = this
    const str = getCropUrl2provide ? getCropUrl2provide(src, { crop: mobileCropDefined.cardCrop }) : src
    return str
  }

  calcImgStyle() {
    if (!isServer && this.valid) {
      this.$nextTick(() => {
        // 左侧icon不存在时，padding为 40px
        const paddingSize = this.notIcon ? 40 : 84
        const imgLength = this.imgs.length
        const screenWidth = this.$el.scrollWidth || document.documentElement.clientWidth - paddingSize
        const unit = 'px'

        if (imgLength < 3) {
          this.leftImgStyle = {
            width: screenWidth + unit,
            height: screenWidth * 9 / 16 + unit,
            backgroundSize: screenWidth * 3 / 16 + unit,
            borderRadius: this.borderRadius
          }

          this.imagesObject.left = [replacedImgSrc({
            type: '3',
            width: screenWidth,
            src: this.getCropUrl(this.imgs[0])
          })]
        } else {
          // 小图:高宽一致  设为 y; 大图: 宽度 设为 x , 大图高度为 x * 3 / 4
          // x和y逻辑关系: 2y + 2 = x * 3 / 4
          const x = (screenWidth - 1) * 8 / 11
          const y = x * 3 / 8 - 1

          this.leftImgStyle = {
            width: x + unit,
            height: x * 3 / 4 + unit,
            backgroundSize: x / 4  + unit,
            borderTopLeftRadius: this.borderRadius,
            borderBottomLeftRadius: this.borderRadius
          }

          this.rightImgStyle = {
            width: y + unit,
            height: y + unit,
            backgroundSize: y / 3 + unit
          }

          this.imagesObject.right = [this.imgs[1], this.imgs[2]].map(img =>
            replacedImgSrc({
              type: '2',
              width: y,
              src: this.getCropUrl(img)
            }))

          this.imagesObject.left = [replacedImgSrc({
            type: '1',
            width: x,
            src: this.getCropUrl(this.imgs[0])
          })]
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
  .image-container {
    position: relative;

    &-wrapper {
      border-radius: 16px;
      overflow: hidden;
      display: flex !important;
      justify-content: center;
      width: 100%;

      .banner-images {
        object-fit: cover;
        background-repeat: no-repeat;
        background-position: center center;
        background-image: url('https://res.klook.com/image/upload/image_logo_mx7wgd.png');
        background-color: $color-neutral-200;
        cursor: pointer;
      }

      &_left {
        display: flex;
        position: relative;
      }

      &_right {
        display: flex;
        flex-direction: column;
        margin-left: 2px;

        .container-item {
          display: flex;
          margin-bottom: 2px;
          position: relative;

          img {
            border-top-right-radius: 16px;
          }

          &:last-of-type {
            margin-bottom: 0;

            img {
              border-top-right-radius: 0;
              border-bottom-right-radius: 16px;
            }
          }
        }
      }
    }

    .img-count {
      position: absolute;
      background: rgba(0,0,0, .6);
      right: 8px;
      bottom: 8px;
      color: $color-white;
      padding: 4px;
      border-radius: $radius-s;
      font-weight: $fontWeight-semibold;
      font-size: $fontSize-caption-m;
      line-height: 17px;
      width: 24px;
      height: 24px;
      text-align: center;
      z-index: 10;
    }
  }
</style>
