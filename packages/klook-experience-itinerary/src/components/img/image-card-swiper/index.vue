<template>
  <div>
     <klk-card-swiper 
      :item-gap="16"
      controller-position="inside"
      class="card-swiper-container"
    >
      <klk-card-swiper-item 
        v-for="(images, index) in imagesGroup" 
        :key="index" 
        class="item-container"
        :style="getItemWidth(images)"
      >
        <template v-for="(item, idx) in images">
          <div
            :key="`${item.id}_${idx}`"
            class="card-swiper-item"
            v-lazy:background-image.container="item.src"
            ratio="1:1"
            :style="getImageWidth(images)"
            v-bind="trackInfo"
            @click="$emit('viewImages', item.id)"
          >
            <Logo :ratio="'1:1'" :width="48" />
          </div>
          <div v-if="idx < images.length - 1" class="enmty" :key="item.id"></div>
        </template>
      </klk-card-swiper-item>
    </klk-card-swiper>
  </div>
</template>
<script lang="ts">
import { Component, Vue, Prop, Inject } from 'vue-property-decorator'

// @ts-ignore
import { CardSwiper, CardSwiperItem } from '@klook/klook-ui/lib/card-swiper'

import Logo from '../../pic-preview/desktop/image-gallery/image-swiper/logo.vue'

@Component({
  components: {
    KlkCardSwiper: CardSwiper,
    KlkCardSwiperItem: CardSwiperItem,
    Logo
  }
})
export default class ImageCardSwiper extends Vue {
  @Prop() images!: string[]
  @Prop({ type: Object, default: () => ({}) }) trackInfo!: string
  @Inject('provideCardSwiperItemWidth') provideCardSwiperItemWidth!: number

  get imagesGroup() {
    return this.images.reduce((acc: any, curr, index) => {
      const i = Math.floor(index / 3)
      const image = {
        id: index,
        src: curr
      }
      if (Array.isArray(acc[i])) {
        acc[i].push(image)
      } else {
        acc[i] = [image]
      }
      return acc
    }, [])
  }

  getItemWidth(images: any []) {
    const length = images?.length ?? 0
    const gap = (length - 1) * 16
    const width = `calc((100% - 32px) * ${length}/ 3 + ${gap}px)`
    return {
      width: width
    }
  }

  getImageWidth(images: any []) {
    const length = images?.length ?? 0
    const gap = (length - 1) * 16
    const width = `calc((100% - ${gap}px) / ${length})`
    return {
      width: width
    }
  }
}
</script>

<style lang="scss" scoped>

.card-swiper-container {
  width: 100%;

  ::v-deep .klk-card-swiper-prev-btn {
    left: 16px;
  }

  ::v-deep .klk-card-swiper-next-btn {
    right: 16px;
  }
}

.item-container {
  display: flex;
  flex-wrap: wrap;
}

.enmty {
  width: 16px;
  height: 140px;
}

.card-swiper-item {
  position: relative;
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center center;
  border-radius: $radius-xl;
  cursor: pointer;
  height: 140px;
}
</style>