<script>
function getSize (size) {
  if (!size) return 0;
  const index = size.indexOf('px');
  if (index === -1) return 0;
  return Number(size.substring(0, index));
}

export default {
  name: 'ItineraryExpand',
  functional: true,
  render(createElement, context) {
    const data = {
      props: {
        name: 'itinerary-expand',
        mode: 'out-in'
      },
      on: {
        beforeEnter (el) {
          el.dataset.oldPaddingTop = el.style.paddingTop;
          el.dataset.oldPaddingBottom = el.style.paddingBottom;
          el.dataset.oldOverflow = el.style.overflow;
          el.style.paddingTop = '0';
          el.style.paddingBottom = '0';
          el.style.height = '0';
        },
        enter (el) {
          el.style.display = 'block';
          el.style.overflow = 'hidden';
          el.style.height = el.scrollHeight + getSize(el.dataset.oldPaddingTop) + getSize(el.dataset.oldPaddingBottom) + 'px';
          el.style.paddingTop = el.dataset.oldPaddingTop;
          el.style.paddingBottom = el.dataset.oldPaddingBottom;
        },
        afterEnter (el) {
          el.style.display = '';
          // uc浏览器上设置height会闪屏
          el.style.height = '';
          el.style.overflow = el.dataset.oldOverflow;
          el.style.paddingTop = el.dataset.oldPaddingTop;
          el.style.paddingBottom = el.dataset.oldPaddingBottom;
        },
        beforeLeave (el) {
          el.dataset.oldPaddingTop = el.style.paddingTop;
          el.dataset.oldPaddingBottom = el.style.paddingBottom;
          el.dataset.oldOverflow = el.style.overflow;

          el.style.display = 'block';
          if (el.scrollHeight !== 0) {
            el.style.height = el.scrollHeight + 'px';
          }
          el.style.overflow = 'hidden';
        },
        leave (el) {
          if (el.scrollHeight !== 0) {
            setTimeout(() => {
              el.style.height = 0;
              el.style.paddingTop = 0;
              el.style.paddingBottom = 0;
            });
          }
        },
        afterLeave (el) {
          el.style.display = 'none';
          el.style.height = '';
          el.style.overflow = el.dataset.oldOverflow;
          el.style.paddingTop = el.dataset.oldPaddingTop;
          el.style.paddingBottom = el.dataset.oldPaddingBottom;
        }
      },
    };
    return createElement('transition', data, context.children)
  },
};
</script>

<style lang="scss">
.itinerary-expand-enter-active,
.itinerary-expand-leave-active {
  transition: all .45s cubic-bezier(0.23, 1, 0.32, 1);
  backface-visibility: hidden;
  transform: translate3d(0, 0, 0);
}
</style>
