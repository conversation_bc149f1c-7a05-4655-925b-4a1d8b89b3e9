<template>
  <div
    v-if="!hide"
    class="itinerary-map-item mobile"
  >
    <div v-if="!hideTitle" class="itinerary-map-item__right" :class="{'has-img' : hasNormalImg()}">
      <TitleV2 :item="item" :track-info="trackInfo" />
    </div>
    <div v-if="images && images.length > 0 && !isPrinting" class="itinerary-map-item__left">
      <ImageCardSwiper
        v-if="isImageSwiper"
        :images="filterDefaultImg()"
        :data-spm-module="TrackInfo"
        data-spm-virtual-item="__virtual?type=entry"
        @viewImages="(index) => viewImages(index)"
      />
      <MobileImg
        v-else
        class="mobile-img-component"
        :imgs="filterDefaultImg()"
        :not-icon="notIcon"
        :data-spm-module="TrackInfo"
        data-spm-virtual-item="__virtual?type=entry"
        @viewImages="(index) => viewImages(index)"
      />
    </div>

    <div class="itinerary-map-item__right" :class="[{'has-img' : hasNormalImg() }, platform]">
      <Summary :summary-list="item.summary_infos" />
      <Icons :icon-list="item.icons" />
      <div
        v-if="tips"
        class="desc"
      >
        {{ item.tips.text }}
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop, Inject } from "vue-property-decorator";
import Title from "./title.vue";
import TitleV2 from "./title-v2/index.vue";
import Summary from "./summary.vue";
import Icons from "./icons.vue";
import MobileImg from './img/mobile/index.vue'
import ImageCardSwiper from './img/image-card-swiper/index.vue'
import { getCropDefined } from '../utils'
const mobileCropDefined = getCropDefined('mobile')

@Component({
  components: {
    Title,
    Summary,
    Icons,
    MobileImg,
    TitleV2,
    ImageCardSwiper
  },
})
export default class MapItem extends Vue {
  @Prop({ default: () => ({}) }) item!: any;
  @Prop({ default: () => ({}) }) trackInfo!: any;
  @Inject("platform") platform!: string;
  @Inject("componentSize") componentSize!: string;
  @Inject("packageId") packageId!: number;
  @Prop({ default: false }) hideTitle?: boolean
  @Prop({ default: false }) notIcon?: boolean
  @Inject('isPrinting') isPrinting!: boolean;
  @Inject('isImageSwiper') isImageSwiper!: boolean;
  @Inject({ default: null }) getCropUrl2provide!: Function;


  get images() {
    return this.item?.imgs || [];
  }

  get hide() {
    const { imgs, summary_infos, icons } = this.item
    const valid = [
      imgs?.length,
      summary_infos?.length,
      icons?.length
    ]
    return valid.every((item: any) => !item) && this.hideTitle
  }

  get tips() {
    return this.item.tips?.text || ''
  }

  get TrackInfo() {
    return  `ItineraryPhoto?oid=package_${this.packageId}&ext=${JSON.stringify({
      PhotoType: this.trackInfo.type
    })}`
  }

  viewImages(index: number) {
    if (
      this.images?.length &&
      this.images.every((item: any) => item?.type === "normal")
    ) {
      this.$emit("showImageViewer",
        this.images,
        this.item.title,
        typeof index === 'number' ? index : null,
        this.item
      );
    }
  }

  filterDefaultImg() {
    const { getCropUrl2provide } = this
    return (this.item?.imgs || []).filter((item: any) => item.type !== 'default').map((item: any) => {
      return getCropUrl2provide ? getCropUrl2provide(item.src, { crop: mobileCropDefined.cardCrop }) : item.src
    }) || []
  }

  hasNormalImg() {
    const list = this.filterDefaultImg()
    return !!list?.length
  }

  mounted() {
    this.$emit('mounted', this.hide)
  }
}
</script>

<style lang="scss" scoped>
$prefix: '.itinerary-map-item';

#{$prefix} {
  display: flex;
  padding-top: 20px;

  .mobile-img-component, .desktop-img-component {
    display: none;
  }

  &__left {
    flex: none;
    margin-right: 12px;

    ::v-deep .banner-img {
      border-radius: $radius-xl;
    }

    .img-count {
      position: absolute;
      color: #fff;
      padding: 4px;
      font-size: $fontSize-caption-m;
      line-height: 17px;
      font-weight: $fontWeight-semibold;
      background: rgba(0, 0, 0, 0.6);
      border-radius: $radius-s;
      right: 4px;
      bottom: 4px;
      min-width: 24px;
      text-align: center;
      height: 24px;
      cursor: pointer;
    }

  }

  &__right {
    &.mobile:first-child {
      padding-top: 8px;
    }
  
    .summary-wrapper, .icons-wrapper {
      margin-top: 4px;
    }

    &.desktop {
      .summary-wrapper {
        margin-top: 8px;
      }
    }

    .desc {
      line-height: 21px;
      color: $color-neutral-900;
      margin-top: 4px;
      word-break: break-word;
      font-size: $fontSize-body-s;
    }

  }

  &.is-large {
    padding-top: 24px;

    .itinerary-map-item__left {
      margin-right: 16px;
    }

    .itinerary-map-item__right {

      .desc {
        font-size: $fontSize-body-m;
        line-height: 24px;
      }
      .summary-wrapper, .icons-wrapper {
        margin-top: 8px;
      }

    }

    ::v-deep {
      .title-wrapper .text , .summary-wrapper, .icons-wrapper .text {
        font-size: $fontSize-body-m;
        line-height: 24px;
      }

      .title-wrapper {
        .text-box {
          -webkit-line-clamp: 1;
        }
        .text {
          &::after {
            background: #fff;
          }
          &::before {
            height: 0;
          }
        }
      }
      .icons-wrapper {
        .icon-item {
          .icon {
            width: 20px;
            height: 20px;
            margin-right: 10px;
          }
        }
      }
    }
  }

  &.mobile {
    flex-direction: column;
    padding-top: 16px;

    .mobile-img-component {
      display: block;
    }

    #{$prefix}__left {
      margin-right: 0;
    }

    #{$prefix}__right.has-img {
      .title-wrapper {
        margin-top: 8px;
        &.mobile {
          margin-top: unset;
          margin-bottom: 4px;
        }
        &.desktop {
          margin-top: unset;
          margin-bottom: 8px;
        }
      }
    }

  }

  &.desktop {
    .desktop-img-component {
      display: block;
    }
  }

  &:first-of-type {
    padding-top: 0;
  }
}
</style>
