<template>
  <div v-if="valid" class="summary-wrapper">
    <span 
      v-for="(item, index) in summaryList" 
      :key="index"
      class="item"
    >
      <span> {{ item }} </span>
    </span>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator'

@Component
export default class Index extends Vue {
  @Prop({ default: () => ([]) }) summaryList!: string[]

  get valid() {
    return this.summaryList?.length > 0
  }
}
</script>

<style lang="scss" scoped>
.summary-wrapper {
  font-size: $fontSize-body-s;
  line-height: 21px;
  color: $color-neutral-900;

  .item {

    &::after {
      display: inline-block;
      content: '';
      vertical-align: middle;
      width: 3px;
      height: 3px;
      border-radius: 50%;
      margin:0 4px;
      background: $color-neutral-900;
    }

    &:last-of-type::after {
      display: none;
    }
  }
}
</style>
