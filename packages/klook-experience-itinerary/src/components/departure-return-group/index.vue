<template>
  <div>
    <div 
      v-for="(item, index) in subData" 
      class="group-wrap" 
      :key="`${item.attr_id}_${index}`"
    >
      <img
        :src="item.title_icon"
        class="group-icon"
      >
      <div class="group-right">
        <div class="group-head">{{ item.title }}</div>
        <div v-if="validGroup(item)" class="group-data" :class="[platform]">
          <div 
            v-if="getTitle(item)"
            class="group-title" 
            :class="{ 'is-invalid-poi': !isValidePoi(item) }" 
            v-bind="getTracker(item)"
            @click="handleMap(item)"
          >
            <div class="group-title-text">{{ getTitle(item) }}</div>
            <ArrowRightSvg class="group-title-arr" />
          </div>
          <div class="address" v-if="isPrinting && getAddress(item)">{{ getAddress(item) }}</div>
          <div v-if="getImages(item).length && !isPrinting" class="image-container">
            <ImageCardSwiper
              v-if="realImageGroupType === 'cardswiper'"
              :images="getImages(item)"
              :trackInfo="trackInfoData"
              @viewImages="(i) => viewImages(item, i)"
            />
            <klk-carousel 
              v-if="realImageGroupType === 'carousel'"
              v-bind="carouselOptions"
            >
              <klk-carousel-item
                v-for="(img, idx) in getImages(item)"
                :key="`${item.attr_id}_${idx}`"
              >
                <div 
                  class="carousel-item" 
                  v-lazy:background-image.container="img" 
                  v-bind="trackInfoData"
                  @click="viewImages(item, idx)"
                >
                  <Logo :ratio="'1:1'" :width="48" />
                </div>
              </klk-carousel-item>
            </klk-carousel>
          </div>
          <div v-if="item.details" class="group-text">{{ item.details }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop, Inject } from 'vue-property-decorator'
import ArrowRightSvg from '../../imgs/arrow-standard-right-new.svg'
import ImageCardSwiper from '../img/image-card-swiper/index.vue'
// @ts-ignore
import { Carousel, CarouselItem } from '@klook/klook-ui/lib/carousel'
import Logo from '../pic-preview/desktop/image-gallery/image-swiper/logo.vue'
import isEmpty from 'lodash/isEmpty'

import eventBus from '../../utils/event-bus'
import { getCropDefined } from '../../utils'
const mobileCropDefined = getCropDefined('mobile')

@Component({
  components: {
    ArrowRightSvg,
    ImageCardSwiper,
    KlkCarousel: Carousel,
    KlkCarouselItem: CarouselItem,
    Logo
  }
})
export default class DepartureReturnGroup extends Vue {
  @Prop({ default: () => [] }) componentData!: any
  @Prop({ default: () => ({}) }) trackInfo!: any
  @Prop() pickUpInfo!: any
  @Prop({ type: String, default: ''}) groupKey!: string
  @Inject('isImageSwiper') isImageSwiper!: boolean;
  @Inject('showImagesViewer') showImagesViewer!: Function
  @Inject('platform') platform!: string
  @Inject('isMobile') isMobile!: boolean
  @Inject('isPrinting') isPrinting!: boolean;
  @Inject({ default: () => null }) setMapShow!: Function;
  @Inject('customMap') customMap!: boolean;
  @Inject({ default: () => null }) handleShowMap!: Function;
  @Inject({ default: 'carousel' }) imageGroupType!: string;
  @Inject({ default: 0 }) packageId!: string | number;
  @Inject({ default: null }) getCropUrl2provide!: Function;


  filterDefaultImg(imgs: any [] = []) {
    const { getCropUrl2provide } = this
    return imgs.filter((item: any) => item.type !== 'default').map((item: any) => {
      return getCropUrl2provide ? getCropUrl2provide(item.src, { crop: mobileCropDefined.cardCrop }) : item.src
    }) || []
  }

  get trackInfoData() {
    const tracker = `ItineraryPhoto?oid=package_${this.packageId}&ext=${JSON.stringify({
      PhotoType: this.trackInfo?.type ?? ''
    })}`
    return {
      'data-spm-module': tracker,
      'data-spm-virtual-item': '__virtual'
    }
  }

  get isEmptyPickUpInfo () {
    return isEmpty(this.pickUpInfo)
  }

  get realImageGroupType() {
    const types = ['cardswiper', 'carousel']
    const imageGroupType = this.imageGroupType
    return types.includes(imageGroupType) ? imageGroupType : 'carousel'
  }

  getTracker(item: any) {
    const isValidePoi = this.isValidePoi(item)
    if (!isValidePoi) {
      return {}
    }
    const groupKey = this.groupKey
    const keys: any = {
      itinerary_departure: 'departure_normal',
      itinerary_return: 'return_normal'
    }
    const isPickUp = item.type === 0
    const MapType = isPickUp && !this.isEmptyPickUpInfo && this.isMobile ? 'pickup_map' : keys[groupKey]
    if (!MapType) {
      return {}
    }
    const packageId = `package_${this.packageId}`
    const ext = JSON.stringify({ MapType })
    return {
      'data-spm-module': `ItineraryMap?oid=${packageId}&ext=${ext}`,
      'data-spm-virtual-item': '__virtual'
    }
  }

  validGroup(item: any) {
    const hasTitle = this.getTitle(item)
    const hasImage = this.getImages(item).length
    const detail = item.details
    return hasTitle || hasImage || detail
  }

  handleMap(item: any) {
    if (item?.map?.location || (this.isMobile && !this.isEmptyPickUpInfo)) {
      if (this.customMap) {
        this.handleShowMap(item)
        return
      }
      
      this.setMapShow(this.getMapAtrrs(item))
    }
  }

  getMapAtrrs(item: any) {
    const { location: address, address_desc: addressDesc } = item.map
    return {
      address,
      addressDesc
    }
  }

  get subData() {
    const data = this.componentData?.sub_data ?? []
    return data
  }

  getImages(item: any) {
    const imgs = item?.imgs ?? []
    return this.filterDefaultImg(imgs)
  }

  isValidePoi(item: any) {
    const isPickUp = item.type === 0
    if (isPickUp && !this.isEmptyPickUpInfo && this.isMobile) {
      return true
    }
    const location = item?.map?.location ?? ''
    return !!location
  }

  getAddress(item: any) {
    return item?.map?.address_desc ?? ''
  }

  getTitle(item: any) {
    const isEmptyPickUpInfo = this.isEmptyPickUpInfo
    const isPickUp = item.type === 0
    if (!isEmptyPickUpInfo && isPickUp) {
      return this.pickUpInfo?.description_v2 ?? ''
    }
    return item?.map?.name ?? ''
  }

  get carouselOptions() {
    
    return {
      hideControllers: this.isMobile,
      loop: false,
      controllerPosition: 'inside',
      autoplay: false,
      size: 'small'
    }
  }

  viewImages(data: any, index: number) {
    const currentPoi = this.componentData?.map ?? null
    const attr_id = data.attr_id    
    this.showImagesViewer({ ...currentPoi, attr_id }, index)
  }

}
</script>

<style lang="scss" scoped>
.group-wrap {
  display: flex;
  color: $color-text-primary;
  padding-bottom: 16px;

  &:nth-last-child(1) {
    padding-bottom: 0;
  }

  .group-icon {
    width: 24px;
    height: 24px;
    margin-right: 12px;
    flex: none;
  }

  .group-right {
    flex: 1;
    max-width: calc(100% - 36px);

    .group-head {
      margin-bottom: 8px;
      @include font-body-m-bold;
    }

    .group-data {
      .address {
        margin-bottom: 16px;
      }

      div:last-child {
        margin-bottom: 0;
      }
    
      &.mobile {
        padding: 16px;
        background: $color-bg-3;
        border-radius: $radius-xl;
      }

      .image-container {
        margin-bottom: 16px;
        border-radius: $radius-l;
        overflow: hidden;

        ::v-deep .klk-carousel-indicators-backgound {
          height: 38px;
        }

        .carousel-item {
          position: relative;
          padding-top: 56.25%;
          border-radius: $radius-l;
          background-size: cover;
          cursor: pointer;

          &[lazy=loaded] ::v-deep {
            .v-lazy-logo {
              display: none;
            }
          }
        }
      }
    
      .group-title {
        display: flex;
        cursor: pointer;
        margin-bottom: 8px;
        @include font-body-m-bold;

        &.is-invalid-poi {
          cursor: default;
          
          .group-title-arr {
            display: none;
          }
        }

        &-text {
          max-width: calc(100% - 20px);
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          text-overflow: ellipsis;
          overflow: hidden;
        }

        &-arr {
          width: 16px;
          height: 16px;
          margin-top: 4px;
          flex: none;
          margin-left: 8px;
        }
      }

      .group-text {
        white-space: pre-line;
        @include font-body-m-regular;
      }
    }
  }
}
</style>
