<template>
  <div class="layer-head">
    <SvgIcon
      :name="icon"
      class="icon-close"
      @click.native="close"
    ></SvgIcon>

    <span class="title">
      {{ title }}
    </span>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator'

@Component
export default class LayerHeader extends Vue {
  @Prop() title !: string

  close() {
    this.$emit('close')
  }
}
</script>

<style lang="scss">
  .layer-head {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 48px;
    color: $color-text-primary;
    padding: 0 16px;
    /* stylelint-disable */
    box-shadow: inset 0 -1px 0 0 rgba(0, 0, 0, 0.12);
    /* stylelint-enable */

    .icon-close {
      width: 24px;
      height: 24px;
      color: $color-text-primary;
      position: absolute;
      left: 16px;
    }

    .title {
      font-weight: $fontWeight-bold;
      font-size: $fontSize-body-m;
    }
  }
</style>
