<template>
  <ActivityLayer
    class="activity-image-viewer"
    :visible.sync="visible"
    :transfer="transfer"
    :not-fixed="notFixed"
    color="black"
    transition="fade"
    @close="$emit('update:visible', false)"
  >
    <div class="reviews-container" :class="{ 'one-img-box' : !imgLengthOverOne }">
      <closeSvg
        class="activity-image_icon-close"
        @click.native="$emit('update:visible', false)"
      />
      <div class="reviews-container-left">
        <div class="reviews-container-left-wrapper">
          <h2 v-if="title" class="title">{{ title }}</h2>
          <!-- 大图 swiper -->
          <div class="big-image-swiper">
            <div
              ref="$bigImageSwiper"
              v-swiper:$bigImageSwiper="bigImageSwiperOption"
              class="swiper-container"
            >
              <div class="swiper-wrapper">
                <div
                  v-for="(item, idx) in replaceImges"
                  :key="idx"
                  class="swiper-slide"
                  >
                  <div class="img-box">
                    <div
                      v-lazy:background-image.container="item.src"
                      ratio="1:1"
                    >
                      <Logo :ratio="'1:1'" :width="560" />
                      <img v-lazy="item.src" alt=""/>
                    </div>
                  </div>
                </div>
              </div>
              <div class="swiper-pagination swiper-pagination-fraction">
                {{ highlightSwiperSlideIndex + 1 }} / {{ images.length }}
              </div>
              <div class="next-prev swiper-next">
                <ArrowSvg class="arrow-svg" />
              </div>
              <div class="next-prev swiper-prev">
                <ArrowSvg class="arrow-svg" />
              </div>
            </div>
          </div>
          <!-- 小图 swiper -->
          <div class="small-image-swiper" >
            <div  class="small-image-swiper-wrapper">
              <div
                ref="$smallImageSwiper"
                v-swiper:$smallImageSwiper="smallImageSwiperOption"
                class="swiper-container"
              >
              <div class="swiper-wrapper swiper-no-swiping">
                <div
                  v-for="(item, idx) in replaceImges"
                  :key="idx"
                  v-lazy:background-image.container="item.src"
                  ratio="1:1"
                  class="swiper-slide swiper-no-swiping"
                  :class="highlightSwiperSlideIndex === idx ? 'high-light-swiper-slide': ''"
                  @click="highlightSwiperSlide(idx)"
                >
                  <Logo ratio="1:1" :width="72">
                </div>
              </div>
            </div>
            <div class="small-next-prev small-swiper-next">
              <div class="gradient-wrapper">
                <div class="gradient-2"></div>
                <div class="gradient-1"></div>
              </div>
              <ArrowSvg class="arrow-svg" />
            </div>
            <div class="small-next-prev small-swiper-prev">
              <div class="gradient-wrapper">
                <div class="gradient-2"></div>
                <div class="gradient-1"></div>
              </div>
              <ArrowSvg class="arrow-svg" />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>


<script lang="ts">
import { Vue, Component, Prop } from "vue-property-decorator";
import ArrowSvg from "../../../imgs/arrow-right-new.svg";
import CloseSvg from "../../../imgs/icon_edit_close_desktop.svg";
import ActivityLayer from "../layer/activity-layer.vue"
import Logo from "../../logo.vue"


@Component({
  components: {
    ArrowSvg,
    CloseSvg,
    ActivityLayer,
    Logo
  },
})
export default class ActivityGallery extends Vue {
  @Prop({ default: () => ([]) }) images!: any[];
  @Prop() visible!: boolean;
  @Prop({ default: "" }) title!: string;
  imageList: any[] = [];
  reviews: any[] = [];
  currentReview: any = null;
  total: number = 0;
  galleryShow: boolean = false;
  $bigImageSwiper: any = null;
  $smallImageSwiper: any = null;
  highlightSwiperSlideIndex: number = 0;
  bigImageSwiperOption: object = {
    navigation: {
      nextEl: ".swiper-next",
      prevEl: ".swiper-prev",
    },
    lazy: {
      loadPrevNext: true,
      loadOnTransitionStart: true,
    },
    on: {
      init: () => {
        this.initSwiper("$bigImageSwiper");
      },
      slideChange: () => {
        this.BigSwiperSlideChange();
        this.slideChangeTransitionEnd("$bigImageSwiper");
      },
    },
  };

  get imgLengthOverOne() {
    return this.images && this.images.length > 1
  }

  get replaceImges() {
    if (this.images?.length) {
      return this.images.map((item) => {
        const reg = /q_\d+,c_fill,w_\d+/
        const replaceStr = 'q_80,c_fill,w_1200'

        if (reg.test(item.src)) {
          item.src = item.src.replace(reg, replaceStr)
        } else {
          item.src = item.src.replace('image/upload/', `image/upload/${replaceStr}/`)
        }

        return item
      })
    }

    return []
  }

  BigSwiperSlideChange() {
    const idx = this.$bigImageSwiper.realIndex;
    this.highlightSwiperSlideIndex = idx;
    this.$smallImageSwiper.slideTo(idx);
  }


  smallImageSwiperOption: object = {
    slidesPerView: "auto",
    centeredSlides: false,
    navigation: {
      nextEl: ".small-swiper-next",
      prevEl: ".small-swiper-prev",
    },
    lazy: {
      loadPrevNext: true,
      loadOnTransitionStart: true,
    },
    spaceBetween: 10,
    slidesPerGroup: 8,
    noSwiping: true,
    on: {
      init: () => {
        this.initSwiper("$smallImageSwiper");
      },
      slideChange: () => {
        this.slideChangeTransitionEnd("$smallImageSwiper");
      },
    },
  };

  highlightSwiperSlide(idx: number) {
    this.$bigImageSwiper.slideTo(idx);
    this.highlightSwiperSlideIndex = idx;
  }

  slideChangeTransitionEnd(swiper: string) {
    const selfSwiper = (this as any)[swiper];
    if (selfSwiper.isEnd) {
      selfSwiper.navigation.$nextEl.css("display", "none");
    } else {
      selfSwiper.navigation.$nextEl.css("display", "block");
    }
    if (selfSwiper.isBeginning) {
      selfSwiper.navigation.$prevEl.css("display", "none");
    } else {
      selfSwiper.navigation.$prevEl.css("display", "block");
    }
  }

  initSwiper(swiper: string) {
    this.$nextTick(() => {
      const selfSwiper = (this as any)[swiper];
      if (selfSwiper.isBeginning) {
        selfSwiper.navigation.$prevEl.css("display", "none");
      } else {
        selfSwiper.navigation.$prevEl.css("display", "block");
      }
    });
  }
}
</script>

<style lang="scss" scoped>
.page-activity-reviews-total-score {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;

  div:nth-child(2) {
    font-size: $fontSize-heading-s;
    font-weight: $fontWeight-regular;
  }

  span {
    position: relative;
    top: -3px;
  }
}

.page-activity-reviews-pagination {
  display: flex;
  justify-content: center;
  margin-top: 32px;
}

.page-activity-reviews-images-container {
  height: 152px;
  padding: 16px;
  background-color: $color-bg-3;
  border-radius: $radius-l;
  display: flex;
  justify-content: space-between;
  cursor: pointer;

  .page-activity-reviews-images-view-more-btn {
    div:nth-child(1) {
      @include font-heading-s;
      margin-bottom: 8px;
    }

    div:nth-child(2) {
      @include font-body-s-regular;
      margin-bottom: 44px;
    }

    div:nth-child(3) {
      @include font-body-s-semibold;

      display: flex;
      justify-content: center;
      width: max-content;
      align-items: center;
      color: $color-text-link;

      svg {
        margin-left: 4px;
      }
    }
  }

  .page-activity-reviews-image-list {
    display: flex;

    div {
      cursor: pointer;
      position: relative;
      background-color: $color-bg-widget-darker-3;
      width: 120px;
      height: 120px;
      border-radius: $radius-m;
      margin-right: 16px;
      background-size: cover;

      &:last-child {
        margin-right: 0;
      }
    }
  }
}

.activity-layer {

  .reviews-container {
    width: 1100px;
    height: 100%;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    border-radius: $radius-l;
    margin: 0 auto;

    .activity-image_icon-close {
      position: absolute;
      z-index: 1000;
      width: 32px;
      height: 32px;
      right:40px;
      top: 40px;
      cursor: pointer;
    }

    .reviews-container-left {
      position: relative;
      width: 100%;
      height: 100%;
      background-color: $color-common-black;
      border-radius: $radius-l 0 0 $radius-l;

      .reviews-container-left-wrapper {
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
      }

      .title {
        max-width: 886px;
        font-size: $fontSize-body-m;
        font-weight: $fontWeight-bold;
        line-height: 24px;
        color: $color-white;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        text-overflow: ellipsis;
        overflow: hidden;
        text-align: center;
        margin: 0 auto 40px;
        white-space: pre-line;
        word-break: break-word;
      }
    }

    .big-image-swiper {
      height: 560px;
      width: 958px;
      position: relative;
      margin: 0 auto;

      .swiper-container {
        position: static;
        height: 100%;
        width: 886px;

        .swiper-slide {
          overflow-y: auto;

          .img-box {
            height: 100%;
            display: table;
            width: 100%;

            > div {
              display: table-cell;
              text-align: center;
              vertical-align: middle;

              &[lazy=loading] {
                background-color: $color-neutral-200;
              }

              img {
                width: 100%;
              }
            }
          }
        }

        .next-prev {
          position: absolute;
          top: 0;
          height: 560px;
          width: 52px;
          cursor: pointer;
          z-index: 1;

          svg {
            position: absolute;
            top: 50%;
            right: 0;
            transform: translate(0, -50%);
            color: $color-text-placeholder-onDark;
          }

          &:hover {
            svg {
              color: $color-text-primary-onDark;
            }
          }
        }

        .swiper-next {
          right: 0;
        }

        .swiper-prev {
          left: 0;

          svg {
            left: 0;
            transform: translate(0, -50%) rotate(180deg);
          }
        }

        .swiper-pagination {
          padding: 4px;
          position: absolute;
          left: auto;
          right: 44px;
          bottom: 8px;
          width: auto;

          @include font-caption-m-regular;
          border-radius: 6px;
          background: $color-overlay-default-2;
          color: $color-text-reverse;
          font-weight: 500;
          line-height: 18px;
          height: 24px;
        }
      }
    }

    .small-image-swiper {
      position: relative;
      width: 892px;
      height: 72px;
      margin: 20px auto 0;

      &-wrapper {
        margin: 0 auto;
        position: relative;
        width: 886px;
        height: 100%;

        // 最多只有五张图片，不需要显示next-prev的箭头
        // &:hover .small-next-prev {
        //   visibility: visible;
        //   pointer-events: auto;
        // }
      }

      .swiper-container {
        width: 100%;
        height: 100%;
      }

      .swiper-slide {
        box-sizing: border-box;
        width: 72px;
        height: 72px;
        background-size: cover;
        background-repeat: no-repeat;
        background-position: center center;
        cursor: pointer;
        border-radius: $radius-l;
        opacity: $opacity-overlay-desktop;

         &[lazy=loading] {
          background-color: $color-neutral-200;
        }
      }

      .high-light-swiper-slide {
        opacity: $opacity-solid;
        border: solid 3px $color-border-active-reverse;
      }

      .small-next-prev {
        position: absolute;
        top: 0;
        height: 72px;
        width: 20px;
        cursor: pointer;
        z-index: 1;
        outline: none;
        visibility: hidden;
        pointer-events: none;

        .gradient-wrapper {
          display: flex;
          align-items: center;
          width: 100%;
          height: 100%;
        }

        .gradient-1 {
          width: 12px;
          height: 100%;
          /* stylelint-disable-next-line */
          background: linear-gradient(90deg, rgba(0, 0, 0, 0.48) 0%, #000000 100%);
        }

        .gradient-2 {
          width: 8px;
          height: 100%;
          /* stylelint-disable-next-line */
          background: linear-gradient(
            90deg,
            rgba(0, 0, 0, 0) 0%,
            rgba(0, 0, 0, 0.48) 100%
          );
        }

        svg {
          position: absolute;
          top: 50%;
          right: 0;
          transform: translate(0, -50%);
          color: $color-text-placeholder-onDark;
        }

        &:hover {
          svg {
            color: $color-text-primary-onDark;
          }
        }
      }

      .small-swiper-next {
        right: -1px;
      }

      .small-swiper-prev {
        left: -1px;

        .gradient-wrapper {
          transform: rotate(180deg);
        }

        svg {
          left: 0;
          transform: translate(0, -50%) rotate(180deg);
        }
      }
    }

    .cls-flex {
      display: flex;
      align-items: center;
    }

    .arrow-svg {
      width: 20px;
      height: 20px;
    }

    &.one-img-box {
      .swiper-pagination, .small-image-swiper, .next-prev {
        display: none !important;
      }
    }
  }
}
</style>
