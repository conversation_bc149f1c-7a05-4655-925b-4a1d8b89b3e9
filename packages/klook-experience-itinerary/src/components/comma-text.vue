<template>
  <div class="comma-text" :class="{'is-print': isPrinting}">
    <span
      v-for="(item, index) in dataList"
      :key="index"
      class="comma-text-item"
      :class="{ 'is-icon': item.type !== 'text' }"
    >
      <template v-if="item.type === 'text'">
        <span>{{ item.content }}</span>
      </template>
      <slot v-else />
    </span>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop, Inject } from 'vue-property-decorator'

@Component
export default class CommaText extends Vue {
  @Prop() textData!: string[]
  @Inject('isPrinting') isPrinting!: boolean;

  get dataList() {
    return (this.textData || []).reduce((accu, curr) => {
      if (accu.length) {
        accu.push({
          type: 'icon',
          content: ''
        })
      }

      accu.push({
        type: 'text',
        content: curr
      })
      return accu
    }, [] as Record<string, string>[])
  }
}
</script>

<style lang="scss" scoped>
.comma-text {
  @include font-body-s-regular;
  color: $color-text-primary;
  display: -webkit-inline-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;

  &.is-print {
    display: inline;
  }

  &-item {
    vertical-align: middle;

    &.is-icon {
      padding: 0 8px;
    }
  }
}
</style>
