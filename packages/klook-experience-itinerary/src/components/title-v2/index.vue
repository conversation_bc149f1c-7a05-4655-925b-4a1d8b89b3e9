<template>
  <div
    v-if="item.title || showMap"
    class="title-wrapper"
    :class="[{ 'map-title-wrapper': showMap }, platform]"
  >
    <div
      v-if="item.title"
      class="text-box"
      @click="handleMap"
      :class="{'show-map': showMap }"
      :data-spm-module="`ItineraryMap?${spmParams}`"
      data-spm-virtual-item="__virtual?type=entry"
    >
      <img
        v-if="item.title_icon"
        class="title-icon"
        :src="item.title_icon"
      />
      <div class="title-text">{{ item.title }}</div>
    </div>

  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop , Inject, Ref} from 'vue-property-decorator'
import { isServer } from '../../utils'

@Component
export default class Index extends Vue {
  @Prop({ default: () => ({}) }) item!: any
  @Prop({ default: () => ({}) }) trackInfo!: any
  @Inject({ default: () => '' }) translateI18n!: Function
  @Inject('packageId') packageId!: string
  @Inject("platform") platform!: string;
  @Ref() contentRef!: any
  @Inject('customMap') customMap!: boolean;
  @Inject({ default: () => null }) handleShowMap!: Function;
  @Inject({ default: () => null }) setMapShow!: Function;

  mapVisible: boolean = false
  isOver24Height: boolean = false
  elementObserver: any = null
  shouldCreateObserver = false

  get showMap() {
    return this.item?.map?.location
  }

  get mapAtrrs() {
    if (this.item?.map) {
      const { location: address, address_desc: addressDesc } = this.item.map || {}
      return {
        address,
        addressDesc
      }
    }

    this.mapVisible && (this.mapVisible = false)

    return {}
  }

  get spmParams() {
    return `oid=package_${this.packageId}&ext=${JSON.stringify({ MapType: this.trackInfo.type })}`
  }

  handleMap() {
    if (this.customMap) {
      this.handleShowMap(this.item)
      return
    }
    
    if (this.showMap) {
      this.setMapShow(this.mapAtrrs)
    }
  }

  getTextHeight() {
    if (!isServer) {
      const target = this.$el && this.$el.querySelector && this.$el.querySelector('.title')
      if (target) {
        const { height } = target.getBoundingClientRect()
        if(height === 0) {
          this.shouldCreateObserver = true
        } else if (height > 24) {
          this.isOver24Height = true
        }
      }
    }
  }

  createObserver() {
    // 解决 display:none 获取不到宽度的问题，只检测一次
    if ('IntersectionObserver' in window) {
      this.elementObserver = new IntersectionObserver(
        (entries) => {
          entries.forEach((entry) => {
            // 如果元素可见
            if (entry.intersectionRatio > 0) {
              this.getTextHeight()
              this.destroyObserver()
            }
          })
        },
        { threshold: [0.1] }
      )

      this.contentRef && this.elementObserver.observe(this.contentRef)
    }
  }

  destroyObserver() {
    this.elementObserver && this.contentRef && this.elementObserver.unobserve(this.contentRef)
  }

  beforeDestroy() {
    this.destroyObserver()
  }

  mounted() {
    this.$nextTick(() => {
      this.getTextHeight()

      if (this.shouldCreateObserver) {
        this.createObserver()
      }
    })
  }
}
</script>

<style lang="scss" scoped>
.title-wrapper {

  .text-box {
    display: flex;

    .title-icon {
      width: 20px;
      height: 20px;
      margin-right: 8px;
      flex: none;
      object-fit: contain;
    }

    .title-text {
      flex: 1;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      text-overflow: ellipsis;
      overflow: hidden;
      font-size: $fontSize-body-s;
      line-height: 21px;
      font-weight: $fontWeight-bold;
      color: $color-text-primary;
      word-break: break-word;
    }

    &.show-map {
      text-decoration: underline;
    }

  }

  &.map-title-wrapper {
    cursor: pointer;
  }
}

.show {
  display: inline-block;
}

.hidden {
  display: none;
}
</style>
