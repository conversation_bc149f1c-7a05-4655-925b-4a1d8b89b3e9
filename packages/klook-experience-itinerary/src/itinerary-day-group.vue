<template>
  <div v-if="isValid" class="itinerary-day-group" :class="[{ 'no-icon': emptyIcon}, platform]">
    <div
      ref="contentRef"
      :style="{ 'max-height': maxHeight }"
      class="itinerary-day-group-wrap"
      :class="{ 'collapsed': isShowMore }"
    >
      <template v-if="!emptyIcon">
        <div class="itinerary-day-group-line"></div>
        <img
          :src="groupData.left_icon"
          alt="icon"
          class="itinerary-day-group-icon"
        />
      </template>

      <div v-if="groupNameList.length" class="itinerary-day-group-title-tags">
        <div
          class="itinerary-day-group-title-wrap"
          :class="{'is-print': isPrinting, pointer: showRightArrow}"
          v-bind="trackData"
          @click="handleTitleClick(groupData, showRightArrow, isPoiTitle)"
        >
          <CommaText
            :text-data="groupNameList"
            class="itinerary-day-group-title"
            :class="{
              'itinerary-day-group-poi-title': isPoiTitle
            }"
          >
            <i class="itinerary-point"></i>
          </CommaText>
           <ArrowRightSvg
            v-if="showRightArrow" 
            class="right-arr" 
          />
        </div>

        <component
          :is="tagComponent"
          v-if="normalTagsList && normalTagsList.length > 0"
          :list="normalTagsList"
          :line="1"
          :track="{ spm: 'itinerary_Tag' }"
          class="itinerary-day-group-tags"
        />
      </div>

      <div v-if="customTagsList && customTagsList.length > 0" class="itinerary-day-group-custom-tag-wrapper">
        <IconGroup :icons="customTagsList" />
      </div>
        <!-- <div
          v-else
          class="itinerary-day-group-custom-tag-wrapper"
        >
          <div v-for="(item, index) in customTagsList" :key="index">
            <img :src="item.icon" alt="icon" />
            <span class="text">{{ item.text }}</span>
          </div>
        </div> -->
      <div
        v-if="groupData.components && groupData.components.length > 0"
        class="itinerary-day-group-content"
      >
        <component
          :is="componentMap[component.type]"
          v-for="(component, compIndex) in groupData.components"
          :key="`${component.type}-${compIndex}`"
          :track-info="groupData.track_info"
          :group-key="groupData.group_key"
          :component-data="component.data"
          :showImageFn="showImageFn"
          :hackCloseBtn="hackCloseBtn"
          class="itinerary-day-group-item"
          :not-icon="emptyIcon"
          :hide-title="isPoiTitle"
          :pick-up-info="pickUpInfo"
          @check-height="deferCheckHeight"
        >
        </component>
      </div>
    </div>
    <ViewMore
      v-show="isShowMoreBtn"
      :hide-shadow="!isShowMore"
      class="itinerary-day-group-more"
      @click.native="toggleViewMore"
    >
      {{ viewMoreText }}
    </ViewMore>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop, Inject, Ref } from 'vue-property-decorator'
import { ItineraryDayGroupData } from './types'
import ItineraryText from './components/itinerary-components/text.vue'
import ItineraryPoi from './components/itinerary-components/poi.vue'
import CommaText from './components/comma-text.vue'
import Tag from './components/tag.vue'
import ViewMore from './components/view-more.vue'
import IconGroup from './components/icon-group/index.vue'
import cloneDeep from 'lodash/cloneDeep'
import { isServer } from './utils'
import ArrowRightSvg from './imgs/arrow-standard-right-new.svg'

// v2 版本group
import DepartureReturnGroup from './components/departure-return-group/index.vue'


@Component({
  components: {
    ItineraryText,
    ItineraryPoi,
    CommaText,
    ViewMore,
    IconGroup,
    ArrowRightSvg,
    DepartureReturnGroup
  }
})
export default class ItineraryDayGroup extends Vue {
  @Prop() showImageFn!: Function
  @Prop({ default: false }) hackCloseBtn?: boolean
  @Prop() groupData!: ItineraryDayGroupData
  @Prop() noLine!: boolean
  @Prop() pickUpInfo!: any
  @Ref() contentRef!: any
  @Inject('customCompObj') customCompObj!: any
  @Inject({ default: () => '' }) translateI18n!: Function
  @Inject("platform") platform!: string;
  @Inject("isPickUp") isPickUp!: boolean;
  @Inject("isNewPickUp") isNewPickUp!: boolean;
  @Inject("handleTitleClick") handleTitleClick!: Function;
  @Inject('isPrinting') isPrinting!: boolean;


  componentMap = {
    texts: ItineraryText,
    poi: ItineraryPoi,
    sub_group: DepartureReturnGroup
  }
  isShowMore = false
  isShowMoreBtn = false
  elementObserver: any = null
  shouldCreateObserver = false
  foldHeight = 360

  get isValid() {
    const { groupNameList = [], customTagsList = []  } = this
    const components = this.groupData?.components ?? []
    return !!(groupNameList.length || customTagsList.length || components.length)
  }

  get isPoiTitle () {
    const group_key = this.groupData?.group_key ?? ''
    const group_name = this.groupData?.group_name ?? ''
    const keys = ['itinerary_attraction']
    const [poiData] = (this.groupData?.components ?? []).filter((item: any) => item.type === 'poi')
    // @ts-ignore
    const [poi] = poiData?.data?.poi_data ?? []
    const location = poi?.map?.location ?? ''
    return keys.includes(group_key) && !!group_name && !!location
  }

  get trackData() {
    const group_key = this.groupData?.group_key ?? ''
    const keys = ['itinerary_departure']
    if (keys.includes(group_key) && !this.isPickUp) {
      return {
        'data-spm-module': 'MoreDepartureInfo',
        'data-spm-virtual-item': '__virtual'
      }
    }
    if (this.isPoiTitle) {
      const MapType = this.groupData?.track_info?.type 
      const ext = JSON.stringify({ MapType })
      return {
        'data-spm-module': `ItineraryMap?ext=${ext}`,
        'data-spm-virtual-item': '__virtual'
      }
    }
    return {}
  }

  get showRightArrow() {
    const group_key = this.groupData?.group_key ?? ''
    const keys = ['itinerary_departure', 'itinerary_return']
    return keys.includes(group_key) && !this.isPickUp
  }

  get emptyIcon() {
    return !this.groupData.left_icon || this.isNewPickUp
  }

  get viewMoreText() {
    const textid = this.isShowMore ? 'city_page_view_more' : 'collapse_all'
    return this.translateI18n(textid)
  }

  get isDesktop() {
    return this.platform === 'desktop'
  }

  get hasLeftIconUrl() {
    const { tags = [], group_key } = this.groupData || {}

    if (['itinerary_departure', 'itinerary_return'].includes(group_key) && tags?.length){
      return tags.some((tag) => tag?.icon?.left_icon_url)
    }

    return false
  }

  get sourceData() {
    return cloneDeep(this.groupData.tags) || []
  }

  get normalTagsList() {
    if (this.isDesktop) {
      return this.sourceData
    }

    return this.sourceData.filter((item) => !item?.icon?.left_icon_url)
  }

  get customTagsList() {
    if (this.hasLeftIconUrl) {
      return this.sourceData.filter((item) => item?.icon?.left_icon_url).map((item) => ({icon: item.icon.left_icon_url ,text: item.text}))
    }

    return []
  }

  get tagComponent() {
    if (this.customCompObj?.tagComponent) {
      return this.customCompObj?.tagComponent
    }

    return Tag
  }

  get groupNameList() {
    const { groupData } = this
    return [groupData?.time, groupData?.group_name].filter(Boolean)
  }

  get maxHeight() {
    return this.isShowMore ? `${this.foldHeight}px` : 'none'
  }

  mounted() {
    // if (this.isDesktop) {
    //   this.checkHeight()

    //   if (this.shouldCreateObserver) {
    //     this.createObserver()
    //   }
    // }
  }

  beforeDestro() {
    this.destroyObserver()
  }

  deferCheckHeight() {
    this.$nextTick(this.checkHeight)
  }

  checkHeight() {
    // 如果已经显示了查看更多，则不再检测
    if (this.isShowMoreBtn) {
      return
    }

    if (!isServer) {
      const contentDom = this.contentRef as any
      if (contentDom && (contentDom.scrollHeight > this.foldHeight)) {
        this.isShowMoreBtn = true
        this.isShowMore = true
      } else if (contentDom && (contentDom.scrollHeight === 0)) {
        // 如果高度为0，则是 display: none 的场景
        this.shouldCreateObserver = true
      }
    }
  }

  createObserver() {
    // 解决 display:none 获取不到宽度的问题，只检测一次
    if ('IntersectionObserver' in window) {
      this.elementObserver = new IntersectionObserver(
        (entries) => {
          entries.forEach((entry) => {
            // 如果元素可见
            if (entry.intersectionRatio > 0) {
              this.checkHeight()
              this.destroyObserver()
            }
          })
        },
        { threshold: [0.1] }
      )

      this.contentRef && this.elementObserver.observe(this.contentRef)
    }
  }

  destroyObserver() {
    this.elementObserver && this.contentRef && this.elementObserver.unobserve(this.contentRef)
  }

  toggleViewMore() {
    this.isShowMore = !this.isShowMore
  }
}
</script>

<style lang="scss" scoped>
.itinerary-day-group-poi-title {
  cursor: pointer;

  ::v-deep .comma-text-item:nth-last-of-type(1) {
    text-decoration: underline;
  }
}

.right-arr {
  flex: none;
  width: 20px;
  height: 20px;
  margin: 4px 0 0 8px;
  align-self: flex-start;
  vertical-align: middle;
}

.itinerary-day-group {
  padding: 0 0 16px 44px;
  position: relative;

  &.mobile {
    padding: 0 0 12px 44px;
  }

  &.no-icon {
    padding: 0 0 24px 0;

    &.desktop {
      padding: 0 0 16px 0;
    }
  }

  &-wrap {
    overflow: hidden;

    &.collapsed {
      margin-bottom: 16px;
    }
  }

  &-line {
    position: absolute;
    left: 16px;
    top: 0;
    height: 100%;
    width: 1px;
    background-color: $color-border-normal;
  }

  &-icon {
    position: absolute;
    left: 0;
    top: 0;
    display: block;
    width: 32px;
    height: 32px;
  }

  &-title-wrap {
    padding: 4px 0;
    display: flex;
    align-items: center;

    &.is-print {
      display: block;

      .right-arr {
        margin: 0 0 0 8px;
      }
    }

    &.pointer {
      cursor: pointer;
    }
  }

  &-title {
    @include font-body-m-bold;
    color: $color-text-primary;

    .itinerary-point {
      text-align: center;
      border-radius: $radius-circle;
      width: 4px;
      height: 4px;
      background-color: $color-text-primary;
      display: inline-block;
      vertical-align: middle;
    }
  }

  &-tags {
    margin: 4px 0;
  }

  &-title-tags {
    margin-bottom: 8px;
    overflow: hidden;
  }

  &-content {
    margin: 8px 0 0;
  }

  &-item {
    margin-bottom: 8px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  &-more {
    margin: 0 0 16px;
  }

  &-custom-tag-wrapper {
    margin-bottom: 16px;

    > div {
      display: flex;
      align-items: center;

      img {
        width: 16px;
        height: 16px;
        object-fit: cover;
        vertical-align: middle;
      }

      .text {
        font-size: 14px;
        color: #212121;
        line-height: 21px;
        font-weight: 400;
        margin-left: 8px;
      }
    }
  }
}
</style>
