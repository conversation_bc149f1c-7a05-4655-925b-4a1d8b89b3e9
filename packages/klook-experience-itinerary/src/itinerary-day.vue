<template>
  <section class="itinerary-day">
    <!-- <klk-divider v-if="!noDivider" class="itinerary-day-divider" /> -->

    <div
      v-if="!isOneDay && dayData.day_name"
      class="itinerary-day-title"
      :class="{ 'collapsed': collapsed }"
      @click="$event => changeCollapse($event)"
    >
      <div class="itinerary-day-title-wrapper">
        <img
          v-if="dayData.day_icon"
          :src="dayData.day_icon"
          alt="icon"
          class="itinerary-day-icon"
        />
        <span>{{ dayData.day_name }}</span>
      </div>
      <div>
        <span v-if="hasPreSummaries" class="itinerary-day-title-arrow">
          <ArrowDownSvg v-show="collapsed" />
          <ArrowUpSvg v-show="!collapsed" />
        </span>
      </div>
    </div>

    <div
      v-if="showNav"
      class="itinerary-day-nav"
      @click="collapsed = !collapsed"
    >
      <CommaText :text-data="dayData.pre_summaries" class="itinerary-day-nav-text">
        <ArrowRightSvg style="width: 16px; height: 16px;" />
      </CommaText>
    </div>

    <div v-else class="itinerary-day-groups">
      <div v-if="dayData.text" class="itinerary-day-header" :class="{ 'itinerary-day-header-new': isNewPickUp }">
        <img v-if="dayData.icon" class="itinerary-day-header-icon" :src="dayData.icon" />
        <div>{{ dayData.text }}</div>
      </div>
      <div v-if="isMobile" class="itinerary-day-tips">
        <div v-if="dayData.tips.departure_tips" class="itinerary-day-tips-text">{{ dayData.tips.departure_tips }}</div>
        <div v-if="dayData.tips.departure_more" class="itinerary-day-tips-text">{{ dayData.tips.departure_more }}</div>
        <div v-if="dayData.tips.return_tips" class="itinerary-day-tips-text">{{ dayData.tips.return_tips }}</div>
      </div>
      <PickUpPoints
        v-if="isValidPickupInfo(0, true)"
        :info="pickUpInfo"
        :itinerary-pois="itineraryPois"
        :show-entry="false"
      />
      <div
        v-for="(group, index) of dayData.groups"
        :id="getId(group.group_id)"
        :key="`${group.group_id}-${index}`"
        class="itinerary-day-block"
      >
        <div
          v-if="tipsList[index] && (tipsList[index].length > 0) && !isMobile"
          class="itinerary-day-tips desktop"
        >
          <IconTips class="itinerary-day-tips-icon" />
          <div class="itinerary-day-tips-content">
            <p
              v-for="(text, i) of tipsList[index]"
              :key="i"
              class="itinerary-day-tips-text"
            >
              {{ text || '' }}
            </p>
          </div>
        </div>

        <PickUpPoints
          v-if="isValidPickupInfo(index, false)"
          :info="pickUpInfo"
          :itinerary-pois="itineraryPois"
          :show-entry="false"
        />

        <ItineraryGroup
          :group-data="group"
          :tips-text="tipsList[index]"
          :class="{ 'no-line': isLast(index) }"
          :showImageFn="showImageFn"
          :hackCloseBtn="hackCloseBtn"
          :pick-up-info="pickUpInfo"
        />
      </div>
    </div>
  </section>
</template>

<script lang="ts">
import { Component, Vue, Prop, Inject } from 'vue-property-decorator'
import KlkDivider from '@klook/klook-ui/lib/divider'
import { ItineraryDayData } from './types'
import CommaText from './components/comma-text.vue'
import ItineraryGroup from './itinerary-day-group.vue'
import PickUpPoints from './components/pick-up-points/index.vue'
import IconTips from './imgs/icon-tips.svg'
import ArrowRightSvg from './imgs/arrow-standard-right-new.svg'
import ArrowDownSvg from './imgs/arrow-standard-down-new.svg'
import ArrowUpSvg from './imgs/arrow-standard-up-new.svg'
import '@klook/klook-ui/lib/styles/components/divider.scss'
import '@klook/klook-ui/lib/styles/transitions.scss'

@Component({
  components: {
    CommaText,
    ItineraryGroup,
    KlkDivider,
    ArrowRightSvg,
    ArrowDownSvg,
    ArrowUpSvg,
    IconTips,
    PickUpPoints
  }
})
export default class ItineraryDay extends Vue {
  @Prop() dayData!: ItineraryDayData
  @Prop({ default: false }) isOneDay!: boolean // 单日的话不显示day
  @Prop({ default: false }) noDivider!: boolean
  @Prop() showImageFn?: Function
  @Prop({ default: false }) hackCloseBtn?: boolean
  @Prop({ type: Array, default: () => [] }) itineraryPois!: any[]
  @Inject("isPickUp") isPickUp!: boolean;
  @Inject("isMobile") isMobile!: boolean;
  @Inject("isNewPickUp") isNewPickUp!: boolean;
  @Inject('isPrinting') isPrinting!: boolean;

  isValidPickupInfo(index: number, spec: boolean = false) {
    if (this.isMobile || this.isPrinting) {
      return false
    }
    const info = this.dayData?.pick_up_info ?? null
    if (info) {
      if (spec) {
        return !this.dayData.groups || !this.dayData.groups.length
      } else {
        return index === 0
      }
    }
    return false
  }

  collapsed = false

  getId(group_id: any) {
    return this.isPickUp ? `group_${group_id}` : ''
  }

  get hidePickUpInfo() {
    return this.isMobile && this.isNewPickUp
  }

  get hasPreSummaries() {
    return !!this.dayData?.pre_summaries?.length
  }

  get showNav() {
    return !this.isOneDay && this.hasPreSummaries && this.collapsed
  }

  // 第一次出现的 return 或者 departure 需要tips
  get tipsList() {
    const { groups, tips } = this.dayData || {}
    const list: string[][] = []

    let hasReturnTips = false
    let hasDepartureTips = false
    for (const group of (groups || [])) {
      if (!hasReturnTips && group?.group_key === 'itinerary_return') {
        hasReturnTips = true
        list.push([
          tips?.return_tips || ''
        ].filter(Boolean))
      } else if (!hasDepartureTips && group?.group_key === 'itinerary_departure') {
        hasDepartureTips = true
        list.push([
          tips?.departure_tips || '',
          tips?.departure_more || ''
        ].filter(Boolean))
      } else {
        list.push([])
      }
    }

    return list
  }

  isLast(index: number) {
    return index + 1 === this.dayData?.groups?.length
  }

  get pickUpInfo() {
    return this.dayData?.pick_up_info || {}
  }

  changeCollapse(event: any) {
    this.collapsed = !this.collapsed
    event && event.stopPropagation()
  }
}
</script>

<style lang="scss" scoped>
.itinerary-day {
  &-header {
    display: flex;
    align-items: center;
    padding-bottom: 8px;
    @include font-body-m-bold;

    &.itinerary-day-header-new {
      @include font-heading-xs;
    }

    &-icon {
      width: 20px;
      margin-right: 8px;
    }
  }

  &-icon {
    margin-right: 8px;
    width: 20px;
    height: 20px;
    flex-shrink: 0;
  }

  &-divider {
    margin-bottom: 20px;
  }

  &-title {
    @include font-body-m-bold;
    padding-bottom: 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: $color-text-primary;
    cursor: pointer;

    &.collapsed {
      padding-bottom: 12px;
    }

    &-wrapper {
      display: flex;
      justify-content: flex-start;
      align-items: center;
    }

    &-arrow {
      margin-left: 16px;
      display: flex;
      align-items: center;
      cursor: pointer;

      svg {
        width: 16px;
        height: 16px;
      }
    }
  }

  &-nav {
    margin-bottom: 20px;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -moz-box-orient: vertical;
    -webkit-line-clamp: 3;
    overflow: hidden;
    text-overflow: ellipsis;
    cursor: pointer;

    &-text {
      @include font-body-s-semibold;
      color: $color-text-primary;

      ::v-deep .is-icon {
        margin: 0 4px;
      }
    }
  }

  &-groups {
    ::v-deep .no-line {
      .itinerary-day-group-line {
        background-color: transparent;
      }
    }
  }

  &-tips {

    &.desktop {
      margin-bottom: 12px;
      display: flex;
      align-items: flex-start;
      justify-content: flex-start;
    }

    &-icon {
      margin-right: 8px;
      width: 20px;
      height: 20px;
      flex-shrink: 0;
    }

    &-text {
      color: $color-text-secondary;
      @include font-body-s-regular;

    }
  }

  &-block {
    &:first-child {
      .itinerary-day-tips {
        margin-top: 0;
      }
    }
  }
}
</style>
