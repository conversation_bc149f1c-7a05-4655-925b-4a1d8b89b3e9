{"name": "@klook/{{name}}", "version": "0.0.1", "description": "A Common Component based on Vue of Klook", "author": "{{author}}", "homepage": "https://design.klook.io", "keywords": ["vue", "component", "ui", "framework"], "main": "dist/{{name}}.common.js", "module": "dist/{{name}}.esm.js", "unpkg": "dist/{{name}}.js", "jsdelivr": "dist/{{name}}.js", "files": ["dist", "lib", "src", "types"], "license": "UNLICENSED", "publishConfig": {"registry": "https://knpm.klook.io", "access": "public"}, "scripts": {"build": "NODE_ENV=production rollup --config ./rollup.config.js", "lint": "NODE_ENV=production eslint --ext .js,.vue src", "test": "NODE_ENV=test jest -i --updateSnapshot", "test:coverage": "NODE_ENV=test jest -i --coverage --updateSnapshot", "prepush": "yarn run lint", "prepublishOnly": "bash prepublishOnly.sh", "commit": "npx git-cz", "commitmsg": "commitlint -E GIT_PARAMS"}, "devDependencies": {"@babel/core": "^7.17.9", "@babel/preset-env": "^7.16.11", "@babel/plugin-transform-runtime": "^7.17.0", "@types/jest": "^26.0.0", "@types/webpack-env": "^1.14.0", "@vue/test-utils": "^1.0.0-beta.32", "jest": "^25.5.4", "rimraf": "^3.0.0", "rollup": "^2.70.1", "rollup-plugin-buble": "^0.19.8", "rollup-plugin-commonjs": "^10.0.2", "rollup-plugin-node-resolve": "^5.2.0", "rollup-plugin-replace": "^2.2.0", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-typescript2": "^0.31.2", "rollup-plugin-vue": "^5.1.9", "terser": "^4.1.3", "vue-jest": "^3.0.4", "vue-property-decorator": "^8.3.0", "vue-template-compiler": "2.6.11", "ts-jest": "^26.1.0", "ts-node": "^8.10.2", "typescript": "^4.6.3"}, "dependencies": {"vue": "2.6.11"}, "peerDependencies": {"vue": "2.6.11"}}