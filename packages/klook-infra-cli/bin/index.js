const path = require('path');
const fs = require('fs-extra');
const prompts = require('prompts');

const templates = {
  component: path.join(__dirname, '../templates/component')
};

const camelizeRE = /-(\w)/g;
const camelize = str => {
  return str.replace(camelizeRE, (_, c) => c ? c.toUpperCase() : '');
};

const capitalize = str => {
  return str.charAt(0).toUpperCase() + str.slice(1);
};

(async () => {
  const res = await prompts([{
      type: 'text',
      name: 'name',
      message: 'What\'s your package name?(exclude "@klook/")',
    },
    {
      type: 'text',
      name: 'author',
      message: 'Who\'s the author of this package?',
    },
  ]);

  if (!res.name) {
    process.exit(0);
  }

  const targetDir = path.join(__dirname, `../../${res.name}`);

  if (fs.existsSync(targetDir)) {
    const r = await prompts({
      type: 'text',
      name: 'y',
      message: `target directory already exists, do you want to cover it?(y/n)`,
    });
    if (r.y.toLowerCase() !== 'y') {
      console.log('done');
      return;
    }
  }

  fs.removeSync(targetDir);

  fs.copySync(templates.component, targetDir);
  const pkgPath = path.join(targetDir, './package.json');
  const readmePath = path.join(targetDir, './README.md');
  let pkg = fs.readFileSync(pkgPath, {
    encoding: 'utf-8'
  });
  pkg = pkg
    .replace(/\{\{name\}\}/g, res.name)
    .replace(/\{\{author\}\}/g, res.author);
  fs.writeFileSync(pkgPath, pkg);
  const readme = fs.readFileSync(readmePath, {
    encoding: 'utf-8'
  });
  fs.writeFileSync(readmePath, readme.replace(/\{\{name\}\}/g, res.name));

  function renderDir(sourceDir) {
    fs.readdirSync(sourceDir).forEach((filename) => {
      let filePath = path.join(sourceDir, filename),
        stat = fs.lstatSync(filePath)
      if (stat.isFile()) {
        return render(filePath)
      } else if (stat.isDirectory()) {
        renderDir(filePath)
      } else {
        console.error('Unknown file type')
      }
    })
  }

  function render(filePath) {
    const fileContent = fs.readFileSync(filePath, {
      encoding: 'utf-8'
    });
    fs.writeFileSync(filePath, fileContent.replace(/\{\{name\}\}/g, capitalize(camelize(res.name))));
  }

  renderDir(path.join(targetDir, './src'))

  console.log('Everything is Ready, your package at: ', targetDir);
})();
