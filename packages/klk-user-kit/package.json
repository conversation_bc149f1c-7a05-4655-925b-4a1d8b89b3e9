{"name": "@klook/user-kit", "version": "2.0.3", "description": "A Component .", "author": "Thomas,Florence", "homepage": "https://design.klook.io", "module": "dist/esm/index.js", "main": "dist/commonjs/index.js", "typings": "types/index.d.ts", "unpkg": "dist/<EMAIL>", "jsdelivr": "dist/<EMAIL>", "files": ["dist", "lib", "src", "types", "locales"], "license": "UNLICENSED", "publishConfig": {"registry": "https://knpm.klook.io", "access": "public"}, "scripts": {"build": "klk-builder build", "build:umd": "klk-builder build -c=\"builder.umd.config.ts\"", "watch": "klk-builder build -w", "lint": "NODE_ENV=production eslint --ext .js,.vue src", "test": "NODE_ENV=test jest -i --updateSnapshot", "test:coverage": "NODE_ENV=test jest -i --coverage --updateSnapshot", "prepush": "yarn run lint", "prepublishOnly": "bash prepublishOnly.sh", "commit": "npx git-cz", "commitmsg": "commitlint -E GIT_PARAMS"}, "devDependencies": {"@klook/captcha": "2.0.0", "@klook/klook-builder": "^0.0.12"}, "peerDependencies": {"@klook/klook-ui": "^1.36.0", "@klook/klook-icons": "^0.13.1", "vue": "2.6.11", "vue-property-decorator": "^8.3.0", "@klook/klk-traveller-utils": "^1.7.0", "@klook/platform-countdown": "^0.0.5", "@klook/contacts": "^1.0.2", "@klook/site-config": "1.7.4", "@klook/captcha": "2.0.0"}}