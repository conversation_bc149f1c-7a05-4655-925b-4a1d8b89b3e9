import Vue from "vue";

export declare class klkUserKitCodeInput extends Vue {
  inputSize: string
}
export declare class klkUserKitCodeInputFormItem extends Vue {
  formItemAttrs: object
  inputAttrs: object
}
export declare class klkUserKitEmailInput extends Vue {
  getSuggestList: Function
}
export declare class klkUserKitEmailInputFormItem extends Vue {
  formItemAttrs: object
  inputAttrs: object
}
export declare class klkUserKitPasswordInput extends Vue {
  passwordRegList: Array<any>
  formErrorShake: boolean
}
export declare class klkUserKitPasswordInputFormItem extends Vue {
  formItemAttrs: object
  inputAttrs: object
  value: string
  modelName: string
}
export declare class klkUserKitPhoneInput extends Vue {
  areaCode: string
  phoneNum: string
}
export declare class klkUserKitPhoneInputFormItem extends Vue {
  formItemAttrs: object
  inputAttrs: object
  areaCode: string
  phoneNum: string
}
export declare class klkUserKitCommonResult extends Vue {
  platform: string
  title: string
  desc: string
  confirmBtn: string
  confirmBtnLoading: boolean
  cancelBtn: string
  cancelBtnLoading: boolean
}
export declare class klkUserKitSendVerifyCodeBtn extends Vue {
  btnAttrs: object
  paramsAttrs: object
  validateFunc: Function
}
export declare class klkUserKitVerifyCodeInputPanel extends Vue {
  title: string
  description: string
  bindMethod: number
  verifyAttrs: object
  sendVerifyCodeParams: object
  hasHelpMeBtn: boolean
  hasChangeBtn: boolean
  pageSpm: string
}
export declare class klkUserKitSendVerifyEmailMixin extends Vue {
  sendVerifyEmail: Function
}
export declare class klkUserFormItem extends Vue {
  downErrorMessage: string
  upErrorMessage: string
}

export declare function getLogoUrl(methodType: string, logoType: string) : void;

export declare function getBlockBtnStyle(methodType: string, logoType: string) : void;

export enum MethodsMap {
  email = 1,
  wechat = 2,
  facebook = 3,
  kakao= 5,
  phone= 6,
  google= 10,
  apple= 13,
  naver= 23
}
