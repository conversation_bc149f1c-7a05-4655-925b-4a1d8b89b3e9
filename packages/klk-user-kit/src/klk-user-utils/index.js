import isSupportAutofocus from '@klook/klk-traveller-utils/lib/isSupportAutofocus'

const logoMap = {
  // email
  1: {
    defaultLogo: 'https://res.klook.com/image/upload/v1702006275/UED_new/Platform/platform_%E7%99%BB%E5%BD%95%E6%B3%A8%E5%86%8C%E9%87%8D%E6%9E%84_2312/mail_fill_default_24x24px.png',
    colorLogo: 'https://res.klook.com/image/upload/v1702006275/UED_new/Platform/platform_%E7%99%BB%E5%BD%95%E6%B3%A8%E5%86%8C%E9%87%8D%E6%9E%84_2312/mail_color_24x24px.png',
    grayLogo: 'https://res.klook.com/image/upload/v1702006276/UED_new/Platform/platform_%E7%99%BB%E5%BD%95%E6%B3%A8%E5%86%8C%E9%87%8D%E6%9E%84_2312/mail_fill_gray_24x24px.png'
  },
  // wechat
  2: {
    defaultLogo: 'https://res.klook.com/image/upload/v1702006278/UED_new/Platform/platform_%E7%99%BB%E5%BD%95%E6%B3%A8%E5%86%8C%E9%87%8D%E6%9E%84_2312/wechat_fill_default_24x24px.png',
    colorLogo: 'https://res.klook.com/image/upload/v1702006278/UED_new/Platform/platform_%E7%99%BB%E5%BD%95%E6%B3%A8%E5%86%8C%E9%87%8D%E6%9E%84_2312/wechat_color_24x24px.png',
    grayLogo: 'https://res.klook.com/image/upload/v1702006279/UED_new/Platform/platform_%E7%99%BB%E5%BD%95%E6%B3%A8%E5%86%8C%E9%87%8D%E6%9E%84_2312/wechat_fill_gray_24x24px.png'
  },
  // facebook
  3: {
    defaultLogo: 'https://res.klook.com/image/upload/v1702006772/UED_new/Platform/platform_%E7%99%BB%E5%BD%95%E6%B3%A8%E5%86%8C%E9%87%8D%E6%9E%84_2312/Facebook_fill_default_24x24px.png',
    colorLogo: 'https://res.klook.com/image/upload/v1702006274/UED_new/Platform/platform_%E7%99%BB%E5%BD%95%E6%B3%A8%E5%86%8C%E9%87%8D%E6%9E%84_2312/Facebook_color_24x24px.png',
    grayLogo: 'https://res.klook.com/image/upload/v1702006275/UED_new/Platform/platform_%E7%99%BB%E5%BD%95%E6%B3%A8%E5%86%8C%E9%87%8D%E6%9E%84_2312/Facebook_fill_gray_24x24px.png'
  },
  // kakao
  5: {
    defaultLogo: 'https://res.klook.com/image/upload/v1702006275/UED_new/Platform/platform_%E7%99%BB%E5%BD%95%E6%B3%A8%E5%86%8C%E9%87%8D%E6%9E%84_2312/KaKao_Talk_fill_default_24x24px.png',
    colorLogo: 'https://res.klook.com/image/upload/v1702006275/UED_new/Platform/platform_%E7%99%BB%E5%BD%95%E6%B3%A8%E5%86%8C%E9%87%8D%E6%9E%84_2312/KaKao_Talk_color_24x24px.png',
    grayLogo: 'https://res.klook.com/image/upload/v1702006275/UED_new/Platform/platform_%E7%99%BB%E5%BD%95%E6%B3%A8%E5%86%8C%E9%87%8D%E6%9E%84_2312/KaKao_Talk_fill_gray_24x24px.png'
  },
  // phone
  6: {
    defaultLogo: 'https://res.klook.com/image/upload/v1702006278/UED_new/Platform/platform_%E7%99%BB%E5%BD%95%E6%B3%A8%E5%86%8C%E9%87%8D%E6%9E%84_2312/phone_fill_default_24x24px.png',
    colorLogo: 'https://res.klook.com/image/upload/v1702006277/UED_new/Platform/platform_%E7%99%BB%E5%BD%95%E6%B3%A8%E5%86%8C%E9%87%8D%E6%9E%84_2312/phone_color_24x24px.png',
    grayLogo: 'https://res.klook.com/image/upload/v1702006278/UED_new/Platform/platform_%E7%99%BB%E5%BD%95%E6%B3%A8%E5%86%8C%E9%87%8D%E6%9E%84_2312/phone_fill_gray_24x24px.pngt'
  },
  // google
  10: {
    defaultLogo: 'https://res.klook.com/image/upload/v1702006275/UED_new/Platform/platform_%E7%99%BB%E5%BD%95%E6%B3%A8%E5%86%8C%E9%87%8D%E6%9E%84_2312/Google_color_24x24px.png',
    colorLogo: 'https://res.klook.com/image/upload/v1702006275/UED_new/Platform/platform_%E7%99%BB%E5%BD%95%E6%B3%A8%E5%86%8C%E9%87%8D%E6%9E%84_2312/Google_color_24x24px.png',
    grayLogo: 'https://res.klook.com/image/upload/v1702006274/UED_new/Platform/platform_%E7%99%BB%E5%BD%95%E6%B3%A8%E5%86%8C%E9%87%8D%E6%9E%84_2312/Google_fill_gray_24x24px.png'
  },
  // apple
  13: {
    defaultLogo: 'https://res.klook.com/image/upload/v1702006769/UED_new/Platform/platform_%E7%99%BB%E5%BD%95%E6%B3%A8%E5%86%8C%E9%87%8D%E6%9E%84_2312/apple_fill_default_24x24px.png',
    colorLogo: 'https://res.klook.com/image/upload/v1702006274/UED_new/Platform/platform_%E7%99%BB%E5%BD%95%E6%B3%A8%E5%86%8C%E9%87%8D%E6%9E%84_2312/apple_color_24x24px.png',
    grayLogo: 'https://res.klook.com/image/upload/v1702006274/UED_new/Platform/platform_%E7%99%BB%E5%BD%95%E6%B3%A8%E5%86%8C%E9%87%8D%E6%9E%84_2312/apple_fill_gray_24x24px.png'
  },
  // naver
  23: {
    defaultLogo: 'https://res.klook.com/image/upload/v1702006276/UED_new/Platform/platform_%E7%99%BB%E5%BD%95%E6%B3%A8%E5%86%8C%E9%87%8D%E6%9E%84_2312/naver_fill_default_24x24px.png',
    colorLogo: 'https://res.klook.com/image/upload/v1702006276/UED_new/Platform/platform_%E7%99%BB%E5%BD%95%E6%B3%A8%E5%86%8C%E9%87%8D%E6%9E%84_2312/naver_color_24x24px.png',
    grayLogo: 'https://res.klook.com/image/upload/v1702006277/UED_new/Platform/platform_%E7%99%BB%E5%BD%95%E6%B3%A8%E5%86%8C%E9%87%8D%E6%9E%84_2312/naver_fill_gray_24x24px.png'
  }
}

const getLogoUrl = (methodType, logoType) => {
  return logoMap[methodType][logoType]
}

const blockBtnStyleMap = {
  // email
  1: {
    type: 'outlined',
    borderColor: '#4A4A4A'
  },
  // wechat
  2: {
    type: 'primary',
    bgColor: '#20C36F',
    hoverBgColor: 'rgba(32, 195, 111, 0.85)',
    textColor: '#FFF'
  },
  // facebook
  3: {
    type: 'primary',
    bgColor: '#1877F2',
    hoverBgColor: 'rgba(24, 119, 242, 0.85)',
    textColor: '#FFF'
  },
  // kakao
  5: {
    type: 'primary',
    bgColor: '#FEE500',
    hoverBgColor: 'rgba(254, 229, 0, 0.85)',
    textColor: '#212121'
  },
  // phone
  6: {
    type: 'outlined',
    borderColor: '#4A4A4A'
  },
  // google
  10: {
    type: 'primary',
    bgColor: '#EEE',
    hoverBgColor: 'rgba(238, 238, 238, 0.85)',
    textColor: '#212121'
  },
  // apple
  13: {
    type: 'primary',
    bgColor: '#000',
    hoverBgColor: 'rgba(0, 0, 0, 0.85)',
    textColor: '#FFF'
  },
  // naver
  23: {
    type: 'primary',
    bgColor: '#20C36F',
    hoverBgColor: 'rgba(32, 195, 111, 0.85)',
    textColor: '#FFF'
  }
}

const getBlockBtnStyle = (methodType) => {
  return blockBtnStyleMap[methodType]
}

const MethodsMap = {
  email: 1,
  wechat: 2,
  facebook: 3,
  kakao: 5,
  phone: 6,
  google: 10,
  apple: 13,
  naver: 23
}

const tryFocusInput = (input) => {
  isSupportAutofocus() && input?.focus()
}

export { getLogoUrl, getBlockBtnStyle, MethodsMap, tryFocusInput }
