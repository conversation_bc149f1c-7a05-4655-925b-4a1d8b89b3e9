<template>
  <KlkUserFormItem
    v-bind="formItemAttrs"
  >
    <KlkUserCodeInput
      ref="codeInput"
      v-bind="inputAttrs"
      @input="codeChange"
      @focus="$emit('focus')"
      @blur="$emit('blur')"
      @on-complete="codeComplete"
    />
  </KlkUserFormItem>
</template>

<script>
import KlkUserFormItem from '../klk-user-form-item/klk-user-form-item.vue'
import KlkUserCodeInput from './klk-user-code-input.vue'

export default {
  name: 'KlkUserFormItemCodeInput',
  components: {
    KlkUserCodeInput,
    KlkUserFormItem
  },
  props: {
    formItemAttrs: {
      type: Object,
      default: () => { }
    },
    inputAttrs: {
      type: Object,
      default: () => { }
    }
  },
  data() {
    return {
    }
  },
  methods: {
    codeChange(value) {
      this.$emit('input', value)
    },
    codeComplete(value) {
      this.$emit('on-complete', value)
    },
    focusInput() {
      this.$refs?.codeInput?.handleInputFocus()
    }
  }
}
</script>
