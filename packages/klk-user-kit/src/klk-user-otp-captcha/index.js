import './gt.js'

const quickCaptchaVerify = async (action, params) => {
  try {
    if (!action) { throw new Error('action is required') }
    const captchaObj = await quickInitCaptcha(params)
    return await quickGetCaptcha(captchaObj)
  } catch (error) {
    throw new Error(error?.message || error)
  }
}

const quickInitCaptcha = async (params) => {
  const lang = getGeetestLang()
  const options = {
    lang,
    offline: false,
    new_captcha: true,
    product: 'bind',
    pure: 1,
    ...params
  }
  return await new Promise((resolve, reject) => {
    options.onError = (e) => { reject({
      message: "networkError",
    }) }
    window.initGeetest(options, (captchaObj) => {
      captchaObj.onReady(() => resolve(captchaObj))
      captchaObj.onError(() => { reject(new Error('Something was wrong with geetest init')) })
    })
  })
}

const quickGetCaptcha = async (captchaObj) => {
  try {
    if (!captchaObj || !captchaObj.onReady) { throw new Error('Something was wrong with geetest init') }
    return await new Promise((resolve, reject) => {
      captchaObj
        .onSuccess(() => {
          const {
            geetest_challenge,
            geetest_validate,
            geetest_seccode
          } = captchaObj.getValidate()
          resolve({
            geetest_challenge,
            geetest_validate,
            geetest_seccode
          })
        })
        .onError((error) => { reject(new Error(error || 'Something was wrong with geetest')) })
        .onClose((error) => { reject(new Error(error || 'close geetest')) })
      captchaObj.verify()
    })
  } catch (error) {
    throw new Error(error)
  }
}

/**
 * 极验sdk支持77种语言，这里和我们界面语言做一个映射，兜底是英语en;
 */
const getGeetestLang = () => {
  const map = {
    'zh-CN': 'zh-cn',
    'zh-HK': 'zh-hk',
    'zh-TW': 'zh-tw',
    ja: 'ja',
    ko: 'ko',
    id: 'id',
    th: 'th',
    vi: 'vi',
    es: 'es',
    'en-GB': 'en-gb',
    fr: 'fr',
    ru: 'ru'
  }
  const _pageLang =
    window.KLK_LANG ||
    (((window.__KLOOK__ || {}).state || {}).klook || {}).language
  return map[_pageLang] || 'en'
}

export {
  quickCaptchaVerify
}
