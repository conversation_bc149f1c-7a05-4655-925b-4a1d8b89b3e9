<template>
  <div
    class="klk-user-otp-input-panel"
    data-spm-module="VerificationCode"
  >
    <klk-loading
      v-show="validateLoading"
      class="loading-container"
      :show-overlay="true"
      overlay-color="rgba(255, 255, 255, 0.70)"
    />
    <div v-if="title" class="title">{{ title }}</div>
    <div v-if="description" class="description">
      <span class="des-text">{{ description }}</span>
      <div class="send-address">
        {{ sendAddress }}
        <klk-link
          v-if="hasChangeBtn"
          data-spm-item="Change"
          v-galileo-click-tracker="{ spm: 'VerificationCode.Change', componentName: 'user-kit' }"
          class="btn change-btn"
          color="#212121"
          href="javascript:;"
          @click="handleChangeMethodClick"
        >
          {{ __t('111415') }}
        </klk-link>
      </div>
    </div>
    <klk-form @submit.native.prevent :model="{ code: code }">
      <KlkUserFormItemCodeInput
        ref="codeInputFormItem"
        v-model="code"
        :input-attrs="{ value: code, inputSize: 'big' }"
        :form-item-attrs="{ prop: 'code', downErrorMessage, upErrorMessage }"
        @on-complete="handleCodeInputComplete"
        @focus="$emit('focus')"
        @blur="$emit('blur')"
      />
    </klk-form>
    <div class="operate-panel">
      <div v-show="resendLoading" class="loading-box">
        <klk-loading></klk-loading>
      </div>
      <klk-link
        v-show="canResend && !resendLoading"
        class="btn resend-btn"
        data-spm-item="Resend"
        v-galileo-click-tracker="{ spm: 'VerificationCode.Resend', componentName: 'user-kit' }"
        color="#212121"
        href="javascript:;"
        @click="handleResendClick"
      >
        {{ __t('112575') }}
      </klk-link>
      <klk-link
        v-if="hasCallMe && isResendBtnClick"
        v-show="canResend && !resendLoading"
        class="btn call-btn"
        data-spm-item="CallMe"
        v-galileo-click-tracker="{ spm: 'VerificationCode.CallMe', componentName: 'user-kit' }"
        color="#212121"
        href="javascript:;"
        @click="handleCallClick"
      >
        {{ __t('111417') }}
      </klk-link>
      <klkPlatformCountDown
        v-show="!canResend && endTime"
        :type="3"
        :end-time="endTime"
        :end-call-back="countDownEnd"
        class="btn count-down"
      >
        <p
          slot-scope="{ countDownTime }"
          v-html="countDownText(countDownTime)"
        />
      </klkPlatformCountDown>
      <klk-link
        v-if="hasHelpMeBtn"
        class="btn help-btn"
        data-spm-item="GetHelp"
        v-galileo-click-tracker="{ spm: 'VerificationCode.GetHelp', componentName: 'user-kit' }"
        href="javascript:;"
        color="#212121"
        @click="handleHelpClick"
      >
        {{ __t('112283') }}
      </klk-link>
    </div>
  </div>
</template>

<script>
import { Link, Loading, Form, Toast } from '@klook/klook-ui'
import klkPlatformCountDown from '@klook/platform-countdown'
import klkUserKitSendVerifyEmailMixin from '../klk-user-mixins/send-verify-email-mixin'
import KlkUserFormItemCodeInput from '../klk-user-code-input/klk-user-form-item-code-input.vue'
import localMixin from '../klk-user-mixins/local'
import { MethodsMap } from '../klk-user-utils/index'

export default {
  name: 'KlkUserVerifyCodeInputPanel',
  components: {
    KlkLink: Link,
    KlkLoading: Loading,
    KlkForm: Form,
    klkPlatformCountDown,
    KlkUserFormItemCodeInput
  },
  mixins: [localMixin, klkUserKitSendVerifyEmailMixin],
  props: {
    type: {
      type: String,
      default: 'sms'
    },
    title: {
      type: String,
      default: ''
    },
    description: {
      type: String,
      default: ''
    },
    bindMethod: {
      type: Number,
      default: -999
    },
    verifyAttrs: {
      type: Object,
      default: () => ({})
    },
    sendVerifyCodeParams: {
      type: Object,
      default: () => ({})
    },
    hasHelpMeBtn: {
      type: Boolean,
      default: true
    },
    hasChangeBtn: {
      type: Boolean,
      default: true
    },
    codeOnly: {
      type: Boolean,
      default: false
    },
    pageSpm: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      isResendBtnClick: false,
      code: '',
      verifyAttrsData: {},
      verifyUseRid: '',
      canResend: false,
      upErrorMessage: '',
      downErrorMessage: '',
      resendLoading: false,
      validateLoading: false
    }
  },
  computed: {
    endTime() {
      return this.verifyAttrsData?.next_timestamp_sec || 0
    },

    sendAddress() {
      return this.verifyAttrsData?.rcv || ''
    },

    hasCallMe() {
      return this.verifyAttrs?.support_type.includes(2)
    },

    _MethodsMap() {
      return MethodsMap
    }
  },
  mounted() {
    this.verifyAttrsData = JSON.parse(JSON.stringify(this.verifyAttrs))
  },
  methods: {
    async resendBtnClick(sendCodeType) {
      if (this.resendLoading) { return false }
      this.resendLoading = true
      this.$refs?.codeInputFormItem?.focusInput()
      const params = {
        ...this.sendVerifyCodeParams,
        is_resend: true,
        type: 1
      }
      if (sendCodeType === 'call') {
        params.type = 2
      } else if (this.bindMethod === MethodsMap.email || (this.type === 'email' && this.codeOnly)) {
        params.type = 3
      } else if(this.type === 'email' && !this.codeOnly){
        delete params.type
      }
      try {
        const { res } = await this.sendVerifyEmail(params, this.type === 'sms' || this.codeOnly ? 'code' : 'codeAndLink')
        this.resendLoading = false
        if (res && res.success && res.result) {
          const { next_timestamp_sec, rid, otp_token } = res.result || {}
          this.verifyAttrsData.next_timestamp_sec = next_timestamp_sec || ''
          this.verifyAttrsData.verifyUseRid = rid || ''
          this.verifyAttrsData.otp_token = otp_token || ''
          sendCodeType === 'call' && Toast({
            message: this.__t('80289'),
            icon: 'icon_communication_phone',
            iconColor: 'white',
            duration: 4000
          })
          this.canResend = false
          this.clearError()
        } else {
          this.handleError(res.error || null)
          this.canResend = true
        }
      } catch (error) {
        this.resendLoading = false
        this.canResend = true
        this.handleError(null)
      }
    },

    countDownEnd() {
      this.canResend = true
    },

    ajaxPostJSON(url, data) {
      if (window.$axios && window.$axios.$post) {
        const post = window.$axios.$post.bind(window.$axios)
        return post(url, data, {
          needCaptcha: true,
        })
      } else {
        return console.error('window.$axios is required!!!')
      }
    },

    async codeInputComplete(val) {
      this.clearError()
      try {
        this.validateLoading = true
        const { success, result, error } = await this.ajaxPostJSON('/v1/userapisrv/public/verification/code/validate', {
          rid: this.verifyAttrsData.verifyUseRid,
          action: this.sendVerifyCodeParams.action,
          otp_token: this.verifyAttrsData.otp_token,
          otp: val
        })
        if (success && result && result.auth_token) {
          this.$emit('verifySuccess', result.auth_token)
        } else {
          this.handleError(error || null)
        }
      } catch (err) {
        this.handleError(null)
      } finally {
        this.validateLoading = false
      }
    },

    handleError(error) {
      if (error) {
        if (error.code === '2999') {
          this.downErrorMessage = error.message
        } else {
          this.upErrorMessage = error.message
        }
      } else {
        this.upErrorMessage = this.__t('111839')
      }
      this.$inhouse.track('custom', 'body', {
        spm: `${this.pageSpm}.VerificationCode.VerificationError`,
        ext: {
          ErrorCode: error?.code || '',
          ErrorText: error?.message || this.__t('111839'),
          Scene: this.bindMethod === this._MethodsMap.phone ? 'phone' : 'email'
        }
      })
    },

    clearError() {
      this.upErrorMessage = ''
      this.downErrorMessage = ''
    },

    handleChangeMethodClick() {
      this.$emit('changeOtpMethod', 'InputPanel')
    },

    handleResendClick() {
      this.code = ''
      this.isResendBtnClick = true
      this.resendBtnClick(this.type)
    },

    handleCallClick() {
      this.code = ''
      this.resendBtnClick('call')
    },

    handleHelpClick() {
      this.$emit('helpBtnClick', true)
    },

    countDownText(time) {
      return this.__t('111416', { countdown_number: `<span class="time-number">${time.s}</span>` })
    },

    handleCodeInputComplete(val) {
      this.codeInputComplete(val)
    }
  }
}
</script>
<style lang="scss" scoped>
.klk-user-otp-input-panel {
  color: $color-text-primary;

  .loading-container {
    border-radius: $radius-xl;
  }

  .title {
    @include font-heading-m();
  }

  .description {
    display: flex;
    flex-wrap: wrap;
    color: $color-text-secondary;
    margin-top: 12px;
  }

  .des-text {
    @include font-body-m-regular();

    margin-right: 8px;
  }

  .send-address {
    @include font-body-m-bold();

    display: flex;
    align-items: center;
    color: $color-text-primary;
  }

  .change-btn {
    margin-left: 8px;
  }

  .operate-panel {
    display: flex;
    margin-top: 12px;
    align-items: center;
  }

  .loading-box {
    position: relative;
    width: 24px;
    height: 24px;

    ::v-deep .klk-loading {
      color: #212121;

      .klk-loading-icon svg {
        width: 16px;
        height: 16px;
      }
    }
  }

  .btn {
    @include font-body-m-regular();

    color: $color-text-primary;
  }

  .count-down {
    color: $color-text-disabled;
  }

  ::v-deep .time-number {
    display: inline-block;
    width: 23px;
    text-align: center;
  }

  .call-btn {
    margin-left: 12px;
  }

  .help-btn {
    margin-left: auto;
  }
}
</style>
