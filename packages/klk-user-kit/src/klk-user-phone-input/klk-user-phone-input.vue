<template>
  <klk-input
    ref="phoneInput"
    v-bind="$attrs"
    v-model="phoneNumInput"
    class="phone-input"
    type="number"
    clearable
    @input="inputPhoneNum"
  >
    <span slot="prepend" ref="countryCodeRef" class="area-prepend">
      <klk-select
        ref="countryCodeSelect"
        v-model="areaCodeInput"
        :bottom-sheet="mobilePlatform"
        style-type="lined"
        :placeholder="__t('45616')"
        :bottom-sheet-title="__t('115172')"
        :max-height="maxHeight"
        @change="selectCountryCode"
      >
        <!-- mweb端展示klk-contacts -->
        <template v-if="mobilePlatform">
          <klk-contacts
            v-if="showDropdown()"
            v-model="areaCodeInput"
            :all-data="mobileOptList"
            :search-placeholder="__t('115155')"
            :current-name="__t('115180')"
            :current-value="areaCodeInput"
            :is-show-search="true"
            :search-func="searchFunc"
          />
        </template>
        <!-- desktop端展示默认的options，加上Input组件做成过滤 -->
        <template v-else>
          <klk-input
            id="search-input"
            v-model.trim="searchQuery"
            :placeholder="__t('115155')"
            clearable
          />
          <klk-option-group
            v-for="(group, i) in desktopOptList"
            :key="i"
            :value="i"
          >
            <div class="group-label" v-show="group.options && group.options.length">
              <div>
                <span>{{ group.label }}</span>
              </div>
            </div>
            <klk-option
              v-for="item in group.options"
              :key="item.id"
              :value="item.value"
              :label="`+${item.value}`"
            >
              <div class="country-code-option">
                <span>{{ item.countryCode }}</span>
                <span>+{{ item.value }}</span>
              </div>
            </klk-option>
          </klk-option-group>

          <div 
            v-show="!filtereDesktopOptList || !filtereDesktopOptList.length" 
            class="search-no-data" >
              {{ __t('13390') }}
          </div>
        </template>
        <klk-option
          v-for="item in allOptions"
          :key="item.id"
          :value="item.value"
          :label="`+${item.value}`"
          class="option-hidden"
        />
      </klk-select>
    </span>
  </klk-input>
</template>

<script>
import { languageConfig } from '@klook/site-config'
import getContactsCountryList from '@klook/klk-traveller-utils/lib/contactsCountryList'
import { tryFocusInput } from '../klk-user-utils/index'
import localMixin from '@/klk-user-mixins/local'
import { Input, Select } from '@klook/klook-ui'

export default {
  name: 'KlkUserPhoneInput',
  components: {
    KlkContacts: () => Promise.all([
        import('@klook/contacts'),
        // @ts-ignore
        import('@klook/contacts/dist/esm/index.css')
    ]).then(([component]) => component),
    KlkInput: Input,
    KlkSelect: Select,
    KlkOption: Select.Option,
    KlkOptionGroup: Select.OptionGroup
  },
  mixins: [localMixin],
  props: {
    areaCode: {
      type: String,
      default: ''
    },
    phoneNum: {
      type: String,
      default: ''
    },
    platform: {
      type: String,
      default: 'mobile'
    },
    autofocus: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      areaCodeInput: '',
      phoneNumInput: '',
      allCountryCodes: [],
      prefers: [],
      others: [],
      searchQuery: null,
      maxHeight: 300
    }
  },
  computed: {
    allOptions() {
      return this.mobilePlatform ? this.mobileOptList : this.allCountryCodes
    },
    mobileOptList() {
      return this.allCountryCodes.map(country => this.formatOpt(country))
    },
    desktopOptList() {
      const preferOpts = this.prefers.filter(country => !this.searchFunc(this.searchQuery, this.formatOpt(country)))
      const otherOpts = this.others.filter(country => !this.searchFunc(this.searchQuery, this.formatOpt(country)))
      return [
        {
          label: this.__t('115154'),
          options: preferOpts || []
        },
        {
          label: this.__t('115258'),
          options: otherOpts || []
        }
      ]
    },
    filtereDesktopOptList() {
      return this.desktopOptList.reduce( (acc, curr) => {
        return acc.concat(curr.options);
      }, []);
    },
    mobilePlatform() {
      return this.platform === 'mobile'
    },
  },
  mounted() {
    this.getAllCountryCodes()
    this.areaCodeInput = this.areaCode
    this.phoneNumInput = this.phoneNum
    setTimeout(() => {
      if (this.autofocus) { tryFocusInput(this.$refs?.phoneInput) }
      this.calCountryCodeMaxHeight()
    })
  },
  methods: {
    showDropdown() {
      return this.$refs?.countryCodeSelect?.showDropdown || false
    },
    calCountryCodeMaxHeight() {
      const viewportHeight = window.innerHeight
      const countryCodeRef = this.$refs.countryCodeRef
      if (!countryCodeRef) { return }
      const { bottom } = countryCodeRef.getBoundingClientRect()
      this.maxHeight = this.mobilePlatform ? viewportHeight : viewportHeight - bottom - 30
    },
    formatOpt(opt) {
      return {
        ...opt,
        value: opt.value || opt.id,
        label: `${opt.name} (+${opt.value})`
      }
    },
    searchFunc(searchVal, curVal) {
      // 根据用户填写的search value，过滤区域号
      if (!searchVal) { return false }
      return !~(curVal.label.toLocaleUpperCase() + String(curVal.value).toLocaleUpperCase()).indexOf(
        searchVal.toLocaleUpperCase()
      )
    },
    setDefaultCountryCode() {
      if (this.prefers.length) { this.selectCountryCode(this.prefers[0].value) }
    },
    selectCountryCode(code) {
      // 关闭dropdown
      this.$refs?.countryCodeSelect?.close()
      this.areaCodeInput = code
      this.$emit('update:area-code', code)
    },
    inputPhoneNum(value) {
      this.$emit('update:phone-num', value)
    },
    async getAllCountryCodes() {
      if (this.$attrs.axios || window.$axios) {
        this.allCountryCodes = await getContactsCountryList({ $axios: window.$axios, lang: this.language })
          .catch(err =>
            console.error(err)
          ).then((res) => {
            return res
          })

        // 根据当前语言，展示preferred区域号
        const preferCountryCode = languageConfig.getPreferCountryListByLangCode(this.language || 'en')
        this.prefers = this.allCountryCodes
          .filter(function (option) {
            return preferCountryCode.includes(option.country)
          })
          .sort(function (a, b) {
            return (preferCountryCode.indexOf(a.country) - preferCountryCode.indexOf(b.country))
          })
        // 其他区域号
        this.others = this.allCountryCodes.filter(function (option) {
          return !preferCountryCode.includes(option.country)
        })

        // 默认选中第一个区域号
        if (!this.areaCode) {
          this.setDefaultCountryCode()
        }
      } else {
        console.error('window.$axios is required!!!')
        return Promise.reject(new Error('window.$axios is required!!!'))
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.phone-input ::v-deep .area-prepend {
    width: 100px;
}

::v-deep .klk-select-lined .klk-select-reference {
    border: none;
    border-radius: 12px;
}

::v-deep .klk-select .klk-poptip-popper {
    width: 100%;
    transform: translate3d(0px, 49px, 0px) !important;
}

::v-deep .klk-select .klk-poptip-popper-inner {
    width: 100% !important;
}

.country-code-option {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.group-label {
    padding: 20px 0;
    position: relative;

    span {
        color: $color-text-secondary;
        font-weight: $fontWeight-regular;
        padding: 0 20px;
        display: inline-block;
        z-index: 1000;
        position: relative;
        background-color: #fff;
    }

    &::after {
        width: 100%;
        height: 1px;
        content: "";
        display: block;
        background-color: #eee;
        position: absolute;
        right: 20px;
        top: 30px;
    }
}

::v-deep .search-wrap {
    background: none !important;
    padding: 0 !important;

    .search-input .klk-input-inner {
      border-color: $color-border-normal !important;
    }
}

#search-input {
  padding: 10px 20px 0;

  ::v-deep .klk-input-inner {
    border-color: $color-border-normal !important;
  }
}

.search-no-data {
  font-size: 16px;
  padding: 20px;
}

.option-hidden {
    display: none;
}
</style>
