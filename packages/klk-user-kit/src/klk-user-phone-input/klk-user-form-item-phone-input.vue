<template>
  <KlkUserFormItem
    v-bind="formItemAttrs"
    :rules="formRules"
  >
    <KlkUserPhoneInput
      v-bind="inputAttrs"
      :area-code.sync="areaCodeInput"
      :phone-num.sync="phoneInput"
      @update:area-code="areaCodeChange"
      @update:phone-num="phoneNumChange"
    />
  </KlkUserFormItem>
</template>

<script>
import KlkUserFormItem from '../klk-user-form-item/klk-user-form-item.vue'
import KlkUserPhoneInput from './klk-user-phone-input.vue'
import localMixin from '../klk-user-mixins/local'

export default {
  name: 'KlkUserFormItemPhoneInput',
  components: {
    KlkUserPhoneInput,
    KlkUserFormItem
  },
  mixins: [localMixin],
  props: {
    formItemAttrs: {
      type: Object,
      default: () => { }
    },
    inputAttrs: {
      type: Object,
      default: () => { }
    },
    areaCode: {
      type: String,
      default: ''
    },
    phoneNum: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      defaultValidator: { validator: this.validatePhone, trigger: 'submit' },
      areaCodeInput: '',
      phoneInput: ''
    }
  },
  computed: {
    formRules() {
      return [...(this.formItemAttrs.rules || []), this.defaultValidator]
    }
  },
  created() {
    this.areaCodeInput = this.areaCode
    this.phoneInput = this.phoneNum
  },
  methods: {
    areaCodeChange(value) {
      this.areaCodeInput = value
      this.$emit('update:area-code', value)
    },
    phoneNumChange(value) {
      this.phoneInput = value
      this.$emit('update:phone-num', value)
    },
    phoneValid() {
      const reg = this.areaCodeInput === '86' ? /^\d{11}$/ : /^\d{6,18}$/
      return reg.test(this.phoneInput)
    },
    validatePhone(_, __, callback) {
      if (!this.phoneValid()) {
        callback && callback(new Error(this.__t('111409')))
      } else {
        callback && callback()
      }
    }
  }
}
</script>
