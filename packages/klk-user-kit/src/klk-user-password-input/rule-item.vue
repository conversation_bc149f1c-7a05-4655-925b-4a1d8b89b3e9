<template>
  <div
    class="rule-item"
    :class="{ 'shake-animation': formErrorShake && !isValidateSuccess }"
    @animationend="shakeAnimationend"
  >
    <IconClear v-show="!isValidateSuccess" theme="filled" size="16" :fill="colorError" />
    <IconCheckCircle v-show="isValidateSuccess" theme="filled" size="16" :fill="colorSuccess" />
    <p class="rule-message">{{ ruleOptions.rule_desc }}</p>
  </div>
</template>

<script>
import { $colorError, $colorSuccess } from '@klook/klook-ui/lib/utils/design-token-esm'
import IconClear from '@klook/klook-icons/lib/IconClear'
import IconCheckCircle from '@klook/klook-icons/lib/IconCheckCircle'

export default {
  name: 'RuleItem',
  components: {
    IconClear,
    IconCheckCircle
  },
  props: {
    ruleOptions: {
      type: Object,
      default: () => null
    },
    value: {
      type: String,
      default: ''
    },
    formErrorShake: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      colorError: $colorError,
      colorSuccess: $colorSuccess
    }
  },
  computed: {
    isValidateSuccess() {
      return new RegExp(this.ruleOptions.rule_regexp).test(this.value)
    }
  },
  mounted() {
  },
  methods: {
    shakeAnimationend() {
      this.$emit('shakeAnimationend', 'end')
    }
  }
}
</script>
<style lang="scss" scoped>
.rule-item {
  @include font-body-s-regular();

  color: $color-text-secondary;
  display: flex;
  align-items: center;

  .rule-message {
    margin-left: 4px;
  }

}

@keyframes shake {
  0% { transform: translateX(0); }
  10%, 30%, 50%, 70%, 90% { transform: translateX(-4px); }
  20%, 40%, 60%, 80% { transform: translateX(4px); }
  100% { transform: translateX(0); }
}

.shake-animation {
  animation: shake 0.4s;
}

</style>
