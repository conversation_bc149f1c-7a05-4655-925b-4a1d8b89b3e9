<template>
  <KlkUserFormItem
    :class="{ 'klk-user-form-item-password-input': !isErrorMsg }"
    v-bind="formItemAttrs"
    :rules="[{ validator: validatePassword, trigger: 'submit' }]"
  >
    <KlkUserPasswordInput
      v-bind="inputAttrs"
      :error-type="errorType"
      @input="changeInput"
      @focus="$emit('focus')"
      @blur="$emit('blur')"
      @shakeAnimationend="shakeAnimationend"
    />
  </KlkUserFormItem>
</template>

<script>
import KlkUserFormItem from '../klk-user-form-item/klk-user-form-item.vue'
import KlkUserPasswordInput from './klk-user-password-input.vue'

export default {
  name: 'KlkUserFormItemPasswordInput',
  components: {
    KlkUserFormItem,
    KlkUserPasswordInput
  },
  props: {
    errorType:{
      type: String,
      default: 'special'
    },
    formItemAttrs: {
      type: Object,
      default: () => {}
    },
    inputAttrs: {
      type: Object,
      default: () => {}
    },
    value: {
      type: String,
      default: ''
    },
    modelName: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
    }
  },
  computed: {
    isErrorMsg() {
      return this.formItemAttrs.errorMsg && this.formItemAttrs.errorMsg !== ''
    }
  },
  mounted() {},
  methods: {
    shakeAnimationend() {
      this.$emit('shakeAnimationend', 'end')
    },

    changeInput(value) {
      this.$emit('input', value)
    },

    validatePassword(_, value, callback) {
      for (let i = 0; i < this.inputAttrs.passwordRegList.length; i++) {
        if (!new RegExp(this.inputAttrs.passwordRegList[i].rule_regexp).test(value)) {
          callback(new Error(this.inputAttrs.passwordRegList[i].rule_desc))
          return false
        }
      }
      callback()
    }
  }
}
</script>
<style lang="scss">
.klk-user-form-item-password-input {
  .klk-form-item-error {
    display: none;
  }
}
</style>
