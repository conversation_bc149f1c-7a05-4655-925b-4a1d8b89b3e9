<template>
  <div class="klk-user-password-input">
    <klk-input
      ref="pwdInput"
      class="input-box"
      type="password"
      v-bind="$attrs"
      clearable
      @focus="$emit('focus')"
      @blur="$emit('blur')"
      @input="changeInput"
    >
      <IconLock slot="prepend" class="prepend-icon" theme="outline" size="20" :fill="colorTextPrimary" />
    </klk-input>
    <template v-if="errorType === 'special'">
      <RuleItem
        v-for="(item, index) in passwordRegList"
        :key="index"
        class="rule-box"
        :rule-options="item"
        :value="passWord"
        :form-error-shake="formErrorShake"
        @shakeAnimationend="shakeAnimationend"
      />
    </template>
  </div>
</template>

<script>
import { tryFocusInput } from '../klk-user-utils/index'
import { $colorTextPrimary } from '@klook/klook-ui/lib/utils/design-token-esm'
import IconLock from '@klook/klook-icons/lib/IconLock'
import RuleItem from './rule-item.vue'
import { Input } from '@klook/klook-ui'

export default {
  name: 'KlkUserPasswordInput',
  components: {
    IconLock,
    RuleItem,
    KlkInput: Input
  },
  props: {
    errorType:{
      type: String,
      default: 'special'
    },
    passwordRegList: {
      type: Array,
      default: () => []
    },
    formErrorShake: {
      type: Boolean,
      default: false
    },
    defaultPwVisible: {
      type: Boolean,
      default: true
    },
    autofocus: { // 如果默认明文展示 (defaultPwVisible = true)，组件会直接focus，这就不会生效
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      colorTextPrimary: $colorTextPrimary,
      passWord: ''
    }
  },
  computed: {},
  mounted() {
    setTimeout(() => {
      const pwdInput = this.$refs?.pwdInput
      if (pwdInput) {
        if (this.defaultPwVisible) { pwdInput.togglePasswdVisible() } // 默认密码是明文展示
        if (this.autofocus) { tryFocusInput(pwdInput) }
      }
    })
  },
  methods: {
    shakeAnimationend() {
      this.$emit('shakeAnimationend', 'end')
    },

    changeInput(value) {
      this.passWord = value
      this.$emit('input', value)
      // TODO: 校验结果
      // for (let i = 0; i < this.passwordRegList.length; i++) {
      //   if (!new RegExp(this.passwordRegList[i].rule).test(value)) {
      //     this.$emit('validate', false)
      //     return false
      //   }
      // }
      // this.$emit('validate', true)
    }
  }
}
</script>
<style lang="scss" scoped>
.klk-user-password-input {
  .input-box {
    margin-bottom: 8px;
  }

  .prepend-icon {
    margin-left: 12px;
  }
}
</style>
