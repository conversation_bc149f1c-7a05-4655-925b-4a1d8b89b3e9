<template>
  <klk-button :loading="isLoading" v-bind="btnAttrs" @click="btnClick"><slot></slot></klk-button>
</template>

<script>
import sendVerifyEmailMixin from '../klk-user-mixins/send-verify-email-mixin'
import { Button } from '@klook/klook-ui'

export default {
  name: 'KlkUserSendVerifyCodeBtn',
  components: {
    KlkButton: Button
  },
  mixins: [sendVerifyEmailMixin],
  props: {
    btnAttrs: {
      type: Object,
      default: () => ({})
    },
    paramsAttrs: {
      type: Object,
      default: () => ({})
    },
    validateFunc: {
      type: Function,
      default: () => {
        return true
      }
    }
  },
  data() {
    return {
      isLoading: false
    }
  },
  computed: {
  },
  mounted() {
  },
  methods: {
    async btnClick() {
      const validateResult = await this.validateFunc()
      if (!validateResult) { return false }
      try {
        this.isLoading = true
        const { res, verifyUseRid } = await this.sendVerifyEmail(this.paramsAttrs, 'code')
        this.isLoading = false
        if (res && res.success) {
          this.$emit('sendVerifyCodeSuccess', { res, verifyUseRid, sendVerifyCodeParams: this.paramsAttrs })
        } else {
          this.$emit('sendVerifyCodeError', res.error || null)
        }
      } catch (e) {
        this.$emit('sendVerifyCodeError', null)
        this.isLoading = false
      }
    }
  }
}
</script>
