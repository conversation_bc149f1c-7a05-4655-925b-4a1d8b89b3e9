
import klkUserKitCodeInput from './klk-user-code-input/klk-user-code-input.vue'
import klkUserKitCodeInputFormItem from './klk-user-code-input/klk-user-form-item-code-input.vue'

import klkUserKitPasswordInput from './klk-user-password-input/klk-user-password-input.vue'
import klkUserKitPasswordInputFormItem from './klk-user-password-input/klk-user-form-item-password-input.vue'

import klkUserKitPhoneInput from './klk-user-phone-input/klk-user-phone-input.vue'
import KlkUserKitPhoneInputFormItem from './klk-user-phone-input/klk-user-form-item-phone-input.vue'

import klkUserKitEmailInput from './klk-user-email-input/klk-user-email-input.vue'
import KlkUserKitEmailInputFormItem from './klk-user-email-input/klk-user-form-item-email-input.vue'

import KlkUserKitVerifyCodeInputPanel from './klk-user-verify-code-input-panel/klk-user-verify-code-input-panel.vue'

import klkUserKitCommonResult from './klk-user-common-result/klk-user-common-result.vue'
import klkUserKitSendVerifyCodeBtn from './klk-user-send-verify-code-btn/klk-user-send-verify-code-btn.vue'
import klkUserKitSendVerifyEmailMixin from './klk-user-mixins/send-verify-email-mixin'
import klkUserFormItem  from "@/klk-user-form-item/klk-user-form-item.vue";
import { createLanguageComponent } from '@/klk-user-utils/utils'

const klkUserKitPhoneInputFormItem = createLanguageComponent(KlkUserKitPhoneInputFormItem)
const klkUserKitEmailInputFormItem = createLanguageComponent(KlkUserKitEmailInputFormItem)
const klkUserKitVerifyCodeInputPanel = createLanguageComponent(KlkUserKitVerifyCodeInputPanel)

export {
  klkUserKitCodeInput,
  klkUserKitCodeInputFormItem,
  klkUserKitEmailInput,
  klkUserKitEmailInputFormItem,
  klkUserKitPasswordInput,
  klkUserKitPasswordInputFormItem,
  klkUserKitPhoneInput,
  klkUserKitPhoneInputFormItem,

  klkUserKitCommonResult,
  klkUserKitSendVerifyCodeBtn,
  klkUserKitVerifyCodeInputPanel,
  klkUserKitSendVerifyEmailMixin,
  klkUserFormItem
}

export * from './klk-user-utils/index'
// export * from './klk-user-otp-captcha/index'
