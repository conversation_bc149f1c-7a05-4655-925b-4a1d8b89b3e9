<template>
  <div class="result-component" :class="`result-component-${platform}`">
    <slot v-if="$slots.icon" class="icon" name="icon"></slot>
    <div v-if="title" class="title">{{ title }}</div>
    <div v-if="desc || $slots.desc" class="desc">
      <slot name="desc">{{ desc }}</slot>
    </div>
    <div v-if="confirmBtn || cancelBtn || $slots.btn" class="btn-group">
      <slot name="btn">
        <klk-button
          v-if="confirmBtn"
          data-spm-item="Confirm"
          v-galileo-click-tracker="{ spm: 'Confirm', componentName: 'user-kit', autoTrackSpm: true }"
          class="confirm-btn"
          type="primary"
          block
          :loading="confirmBtnLoading"
          @click="handleConfirmBtn"
        >
          {{ confirmBtn }}
        </klk-button>
        <klk-button
          v-if="cancelBtn"
          data-spm-item="Cancel"
          v-galileo-click-tracker="{ spm: 'Cancel', componentName: 'user-kit', autoTrackSpm: true }"
          class="cancel-btn"
          type="outlined"
          block
          :loading="cancelBtnLoading"
          @click="handleCancelBtn"
        >
          {{ cancelBtn }}
        </klk-button>
      </slot>
    </div>
  </div>
</template>

<script>
import { Button } from "@klook/klook-ui"

export default {
  name: 'KlkUserCommonResult',
  components: {
    KlkButton: Button
  },
  props: {
    platform: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: ''
    },
    desc: {
      type: String,
      default: ''
    },
    confirmBtn: {
      type: String,
      default: ''
    },
    confirmBtnLoading: {
      type: Boolean,
      default: false
    },
    cancelBtn: {
      type: String,
      default: ''
    },
    cancelBtnLoading: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {}
  },
  methods: {
    handleConfirmBtn() {
      this.$emit('confirm')
    },

    handleCancelBtn() {
      this.$emit('cancel')
    }
  }
}
</script>

<style lang="scss" scoped>
.result-component {
  color: $color-text-primary;
  text-align: center;

  .title {
    @include font-heading-m();

    margin-top: 32px;
  }

  .desc {
    @include font-body-m-regular();

    margin: 12px 0 32px 0;
  }

  .cancel-btn {
    margin-top: 12px;
  }
}

.result-component-mobile {
  .error-title {
    margin-top: 16px;
  }
}

.result-component-desktop {
  .error-title {
    margin-top: 32px;
  }
}
</style>
