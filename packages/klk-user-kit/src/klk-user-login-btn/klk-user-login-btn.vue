<template>
  <klk-button
    :block="isBlockStyle"
    :type="btnType"
    size="large"
    :class="`login-btn login-btn--${btnType} login-btn--${usage}`"
    :style="{
      '--bgColor': blockBtnStyle.bgColor,
      '--borderColor': blockBtnStyle.borderColor,
      '--hoverBgColor': blockBtnStyle.hoverBgColor
    }"
    @click="handleBtnClick"
  >
    <div class="btn-content" :style="btnType === 'primary' ? { color: blockBtnStyle.textColor } : {}">
      <img :src="logoUrl" alt="social icon" width="24" height="24" />
      <div :class="`btn-text btn-text--${usage}`">
        <slot></slot>
      </div>
      <div v-if="isBlockStyle && isLastUsed" class="btn-tag">{{ __t('30455') }}</div>
    </div>
  </klk-button>
</template>

<script>
import { MethodsMap, getLogoUrl, getBlockBtnStyle } from '../klk-user-utils/index'
import localMixin from '@/klk-user-mixins/local'
import { Button } from '@klook/klook-ui'

export default {
  name: 'KlkUserLoginBtn',
  components: {
    KlkButton: Button
  },
  mixins: [localMixin],
  props: {
    usage: {
      type: String,
      default: 'block',
      validator(val) {
        return ['block', 'inline'].includes(val)
      }
    },
    isLastUsed: {
      type: Boolean,
      default: false
    },
    methodType: {
      type: Number,
      default: () => -999
    }
  },
  data() {
    return {
      loading: false
    }
  },
  computed: {
    isBlockStyle() {
      return this.usage === 'block'
    },
    logoUrl() {
      return getLogoUrl(this.methodType, 'colorLogo')
    },
    btnType() {
      return this.isBlockStyle ? this.blockBtnStyle.type : 'outlined'
    },
    blockBtnStyle() {
      return getBlockBtnStyle(this.methodType)
    }
  },
  methods: {
    async handleBtnClick() {
      this.loading = true
      let verifyResult = null
      if (this.methodType === MethodsMap.google) {
        const { GoogleBase } = await import('@klook/klook-traveller-login')
        this.loading = false
        verifyResult = await new GoogleBase().getAccessToken()
      }
      if (this.methodType === MethodsMap.wechat) {
        const { WechatBase } = await import('@klook/klook-traveller-login')
        this.loading = false
        verifyResult = await new WechatBase().bind()
      }
      if (this.methodType === MethodsMap.facebook) {
        const { FacebookBase } = await import('@klook/klook-traveller-login')
        this.loading = false
        verifyResult = await new FacebookBase().getAccessToken()
      }
      if (this.methodType === MethodsMap.kakao) {
        const { KakaoBase } = await import('@klook/klook-traveller-login')
        this.loading = false
        verifyResult = await new KakaoBase().getAccessToken()
      }
      if (this.methodType === MethodsMap.naver) {
        const { NaverBase } = await import('@klook/klook-traveller-login')
        this.loading = false
        verifyResult = await new NaverBase().getAccessToken()
      }
      if (this.methodType === MethodsMap.apple) {
        const { AppleAuth } = await import('@klook/klook-traveller-login')
        console.log('apple')
        this.loading = false
        verifyResult = await new AppleAuth().getAccessToken()
      }
      const access_token = MethodsMap.google === this.methodType ? verifyResult?.data?.google_code : verifyResult?.data?.access_token
      this.$emit('on-click', {
        thirdParty: ![MethodsMap.google, MethodsMap.email].includes(this.methodType),
        accessToken: access_token
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.login-btn {
    &--primary {
        background: var(--bgColor);
        border: 1px solid var(--bgColor);

        &:hover {
            background: var(--hoverBgColor);
        }
    }

    &--outlined {
        border: 1px solid var(--borderColor);
    }

    &--block {
        padding: 10px 12px;
    }

    &--inline {
        padding: 12px 16px;
        border: 1px solid $color-border-normal;
    }
}

.btn-content {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .btn-text {
        @include font-body-m-bold();

        &--inline {
            margin-left: 12px;
        }
    }

    .btn-tag {
        @include font-caption-m-semibold();
        padding: 0 6px;
        height: 20px;
        border-radius: $radius-s;
        border: 1px solid $color-text-placeholder;
        background: $color-overlay-default-3;
        color: $color-text-reverse;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }
}
</style>
