<template>
  <div class="form-item">
    <klk-alert
      v-if="upErrorMessage"
      class="up-error"
      type="error"
      show-icon
    >
      {{ upErrorMessage }}
    </klk-alert>
    <klk-form-item
      ref="formItem"
      class="form-item-content"
      :class="{
        'form-item-content--error': downErrorMessage,
      }"
      v-bind="$attrs"
    >
      <div
        slot="error"
        slot-scope="scope"
        class="down-error"
      >
        <template v-if="!downErrorMessage">
          <IconCautionCircle class="error-icon" theme="filled" size="16" :fill="colorError" />
          <span class="error-text">{{ scope.error }}</span>
        </template>
      </div>
      <slot></slot>
    </klk-form-item>
    <div v-if="downErrorMessage" class="down-error down-backend-error">
      <IconCautionCircle class="error-icon" theme="filled" size="16" :fill="colorError" />
      <span class="error-text">{{ downErrorMessage }}</span>
    </div>
  </div>
</template>

<script>
import { Alert, Form } from '@klook/klook-ui'
import { $colorError } from '@klook/klook-ui/lib/utils/design-token-esm'
import IconCautionCircle from '@klook/klook-icons/lib/IconCautionCircle'

export default {
  name: 'KlkUserFormItem',
  components: {
    KlkAlert: Alert,
    KlkFormItem: Form.FormItem,
    IconCautionCircle
},
  props: {
    downErrorMessage: {
      type: String,
      default: ''
    },
    upErrorMessage: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      colorError: $colorError
    }
  },
  computed: {},
  methods: {}
}
</script>

<style lang="scss" scoped>
.form-item {
  .up-error {
    margin-top: 12px;

    ::v-deep .klk-icon {
      color: $color-error;
    }
  }

  .form-item-content {
    margin: 32px 0 0 !important;

    &--error {
      ::v-deep .klk-input-inner, ::v-deep .klk-otp-validate_label {
        border-color: $color-error;
      }
    }
  }


  .down-error {
    display: flex;
    align-items: center;
    color: $color-error;

    .error-icon {
      margin-right: 4px;
    }

    .error-text {
      @include font-body-s-regular();
    }
  }

  .down-backend-error {
    margin-top: 4px;
  }
}
</style>
