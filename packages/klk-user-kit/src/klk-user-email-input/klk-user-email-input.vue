<template>
  <klk-auto-complete
    ref="emailInput"
    v-bind="$attrs"
    clearable
    :fetch-suggestions="getSuggestList"
    @input="onInputChange"
  >
    <IconMail
      slot="prepend"
      class="prepend-icon"
      theme="outline"
      size="20"
      :fill="colorTextPrimary"
    />
    <span v-if="appendText" slot="append" class="append-text">{{ appendText }}</span>
  </klk-auto-complete>
</template>

<script>
import { $colorTextPrimary } from "@klook/klook-ui/lib/utils/design-token-esm";
import IconMail from "@klook/klook-icons/lib/IconMail";
import { tryFocusInput } from '../klk-user-utils/index'
import { AutoComplete } from "@klook/klook-ui"

export default {
  name: "KlkUserEmailInput",
  components: {
    IconMail,
    KlkAutoComplete: AutoComplete
  },
  props: {
    getEmailSuffix: {
      type: Function,
      default: null,
    },
    autofocus: {
      type: Boolean,
      default: true
    },
    appendText: {
      type: String,
      default: '' 
    }
  },
  data() {
    return {
      colorTextPrimary: $colorTextPrimary,
    }
  },
  methods: {
    formatList(list, username) {
      return list.map((suggest, index) => {
        return {
          value: `${username}@${suggest}`,
          attrsDom: {
            "data-spm-item": `EmailSuggest_LIST?ext=${JSON.stringify({
              SuggestEmail: suggest,
              Index: index,
              Length: list.length,
            })}`,
          },
        };
      });
    },
    async getSuggestList(emailInput, cb) {
      // 输入@ 之前不联想
      if (!emailInput.includes("@")) {
        cb([]);
        return;
      }

      // 调后端接口获取邮箱域名联想列表
      const suffixList = await this.getEmailSuffix();

      const [username, emailSuffix] = emailInput.split("@");
      const filteredRes = emailSuffix
        ? suffixList.flatMap((suffix) =>
            suffix.startsWith(emailSuffix) && suffix !== emailSuffix.toLowerCase()
              ? suffix
              : []
          ) // @后默认展示的列表，后端返回的前4个
        : suffixList.slice(0, 4); // @后加更多字母，展示符合的联想后缀

      // 配置埋点
      const suggestList = this.formatList(filteredRes, username)
      cb(suggestList);
    },
    onInputChange(value) {
      this.$emit("input", value);
    },
  },
  mounted() {
    if (this.autofocus) {
      setTimeout(() => {
        tryFocusInput(this.$refs?.emailInput)
      })
    }
  },
};
</script>

<style lang="scss" scoped>
.prepend-icon {
  display: flex;
  margin-left: 12px;
}

.append-text{
  margin-right: 12px;
  display: flex;
}
</style>
