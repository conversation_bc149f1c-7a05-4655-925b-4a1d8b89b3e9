<template>
  <KlkUserFormItem
    v-bind="formItemAttrs"
    :rules="formRules"
  >
    <KlkUserEmailInput
      v-bind="inputAttrs"
      @input="emailChange"
    />
  </KlkUserFormItem>
</template>

<script>
import KlkUserFormItem from '../klk-user-form-item/klk-user-form-item.vue'
import KlkUserEmailInput from './klk-user-email-input.vue'
import localMixin from '@/klk-user-mixins/local'

export default {
  name: 'KlkUserFormItemEmailInput',
  components: {
    KlkUserEmailInput,
    KlkUserFormItem
  },
  mixins: [localMixin],
  props: {
    formItemAttrs: {
      type: Object,
      default: () => { }
    },
    inputAttrs: {
      type: Object,
      default: () => { }
    }
  },
  data() {
    return {
      defaultValidator: { validator: this.validateEmail, trigger: 'submit' },
      emailInput: ''
    }
  },
  computed: {
    formRules() {
      return [...(this.formItemAttrs.rules || []), this.defaultValidator]
    }
  },
  created() {
    this.emailInput = this.inputAttrs.value
  },
  methods: {
    emailChange(value) {
      this.emailInput = value
      this.$emit('input', value)
    },
    emailValid() {
      const reg = /^[a-zA-Z0-9_-]+(\.([a-zA-Z0-9_-])+)*@[a-zA-Z0-9_-]+[.][a-zA-Z0-9_-]+([.][a-zA-Z0-9_-]+)*$/
      return reg.test(this.emailInput)
    },
    validateEmail(_, __, callback) {
      if (!this.emailValid()) {
        callback && callback(new Error(this.__t('111429')))
      } else {
        callback && callback()
      }
    }
  }
}
</script>
