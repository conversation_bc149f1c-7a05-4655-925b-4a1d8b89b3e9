
export default {
  props: {
  },

  data() {
    return {}
  },

  computed: {
  },

  methods: {
    dataToBase64(data) {
      const utf8Bytes = new TextEncoder().encode(JSON.stringify(data))
      return window.btoa(
        String.fromCharCode.apply(null, utf8Bytes)
      )
        .replace(/\+/g, '-')
        .replace(/\//g, '_')
    },

    ajaxPostJSON(url, data, CSRF = false) {
      if (window.$axios && window.$axios.$post) {
        const post = window.$axios.$post.bind(window.$axios)
        return post(url, data, { CSRF, needCaptcha: true })
      } else {
        return console.error('window.$axios is required!!!')
      }
    },

    ajaxGet(url, data) {
      if (window.$axios && window.$axios.$get) {
        const get = window.$axios.$get.bind(window.$axios)
        return get(url, data)
      } else {
        return console.error('window.$axios is required!!!')
      }
    },

    // type: link | code (发送链接还是验证码邮件)
    async sendVerifyEmail(params, type) {
      try {
        const sendParams = {
          ...params,
        }
        // 调用发送验证码接口
        const apis = {
          code: '/v2/userapisrv/public/verification/code/send',
          link: '/v2/userapisrv/public/verification/link/send',
          codeAndLink: '/v2/userapisrv/public/verification/code_and_link/send'
        }
        const res = await this.ajaxPostJSON(apis[type], sendParams)

        if (!res.success) {
          const { captcha_id: captchaId, seq_no: seqNo } = (res.error || {}).data || {};

          if (res.error.code === '9000' && res.error.data && captchaId && seqNo) {
            const { gtCaptchaVerify } = await import('@klook/captcha');
            return gtCaptchaVerify(res.error.data).then(result => {
              const newParams = JSON.parse(JSON.stringify(params))
              newParams._rc = result._rc
              // 再次发送验证码
              return this.sendVerifyEmail(newParams, type)
            }).catch(e => {
              return {
                res: {
                  error: {
                    code: e?.code,
                    message: e?.message
                  }
                },
                verifyUseRid: ''
              }
            });
          }
        }
        // 赋值最新的rid，后面verify接口要用
        const verifyUseRid = res.result?.rid || res.error?.data?.rid || ''
        return { res, verifyUseRid }
      } catch (e) {
        throw new Error(e?.message || e)
      }
    },

    async sendVerifyPassword(params, type) {
      try {
        const sendParams = {
          ...params,
        }
        // 调用发送验证码接口
        const apis = {
          email: '/v2/userapisrv/public/login/email_pwd',
          mobile: '/v2/userapisrv/public/login/mobile_pwd'
        }
        const res = await this.ajaxPostJSON(apis[type], sendParams, true)
        if (!res.success) {
          const { captcha_id: captchaId, seq_no: seqNo } = (res.error || {}).data || {};

          if (res.error.code === '9000' && res.error.data && captchaId && seqNo) {
            const { gtCaptchaVerify } = await import('@klook/captcha');
            return gtCaptchaVerify(res.error.data).then(result => {
              const newParams = JSON.parse(JSON.stringify(params))
              newParams._rc = result._rc
              // 再次发送验证码
              return this.sendVerifyPassword(newParams, type)
            }).catch(e => {
              return {
                res: {
                  error: {
                    code: e?.code,
                    message: e?.message
                  }
                },
                verifyUseRid: ''
              }
            });;
          }
        }
        // 赋值最新的rid，后面verify接口要用
        const verifyUseRid = res.result?.rid || res.error?.data?.rid || ''
        return { res, verifyUseRid }
      } catch (e) {
        throw new Error(e?.message || e)
      }
    },

    async initAndVerifyCaptcha(params) {
      try {
        const { quickCaptchaVerify } = await import('../klk-user-otp-captcha/index')
        return await quickCaptchaVerify('ota_notification', params)
      } catch (e) {
        throw new Error(e?.message || e)
      }
    }
  }
}
