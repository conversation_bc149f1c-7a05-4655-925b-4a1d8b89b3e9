import { definedConfig } from '@klook/klook-builder/lib/builder/helper'
import pkg from './package.json'

function tryJonFile(path, errorHandler) {
  try {
    return require(path)
  } catch (_error) {
    errorHandler && errorHandler(_error)
    return {}
  }
}

const localConfig = tryJonFile('./config.local.js', (e) => {
  console.warn('Config: config.local.js require error', e)
})

export default definedConfig({
  entry: './src/index.ts',
  type: 'rollup',

  outputPath: process.env.npm_config_debug ? localConfig.devDir || './dist' : './dist',
  external: Object.keys(pkg.peerDependencies),

  // 通过对象形式可以覆盖公共配置
  format: {
    esm: {},
    commonjs: {}
    // umd: {
    //   name: 'TestThomasUmd'
    // }
  }
})
