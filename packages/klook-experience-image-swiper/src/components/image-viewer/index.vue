<template>
  <Layer
    class="activity-image-viewer"
    :visible="visible"
    :transfer="transfer"
    :not-fixed="notFixed"
    color="black"
    transition="fade"
  >
    <component
      :is="componentName" 
      :images="images"
      :title="title"
      :locales-translate="localesTranslate" 
      :show-close="showClose"
      :init-index="initIndex"
      :fieldKey="realFieldKey"
      @slideChange="slideChange"
      @close="handleClose"
    >
      <template slot="content" slot-scope="data">
        <slot name="content" :data="data"></slot>
      </template>
      <template slot="title">
        <slot name="title">{{ title }}</slot>
      </template>
    </component>
  </Layer>
</template>

<script lang="ts">
import { Component } from 'vue-property-decorator'
import ViewerBase from '../../base/viewer'

@Component
export default class ImageViewer extends ViewerBase {}
</script>

<style lang="scss" scoped>
.activity-image-viewer {
  position: fixed;
  top: 0;
  left: 0;
  background: #000000;
  width: 100%;
  height: 100%;
  z-index: 9999 !important;
}
</style>
