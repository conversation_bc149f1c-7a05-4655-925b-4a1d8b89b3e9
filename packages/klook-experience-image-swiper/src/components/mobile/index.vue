<template>
  <div 
    ref="$swiper" 
    v-swiper:$swiper="swiperOptions" 
    class="activity-image-viewer_swiper_container swiper-container"
  >
    <div class="image-viewer-top">
      <CloseSvg
        v-if="showClose"
        class="activity-image_icon-close"
        @click.native="close"
      />
      <div ref="pagination" class="swiper-pagination activity-swiper-pagination"></div>
    </div>
    <div class="image-viewer-title">
      <slot name="title">{{ title }}</slot>
    </div>
    <div class="swiper-wrapper">
      <div v-for="(image, index) in images" :key="index" class="swiper-slide">
        <div class="activity-image-wrapper">
          <div class="activity-image-content">
            <ImageZoomer>
              <img :src="image[realFieldKey.img_url]" alt="activity-image" class="activity-image">
            </ImageZoomer>
          </div>
        </div>

        <div class="merchant-image-description">
          <slot name="content" :image="image"></slot>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Watch } from 'vue-property-decorator'
import SwiperBase from '../../base'
import ImageZoomer from '../image-zoomer/index.vue'
import CloseSvg from '../../assets/svg/icon-close.svg'

@Component({
  components: {
    ImageZoomer,
    CloseSvg
  }
})
export default class MobileImagesSwiper extends SwiperBase {

  $swiper: any = null

  swiperOptions = {
    slidesPerView: 1,
    initialSlide: this.initIndex,
    paginationClickable: true,
    spaceBetween: 16,
    pagination: {
      el: '.swiper-pagination',
      type: 'fraction'
    },
    on: {
      slideChange: () => {
        this.slideChange()
      }
    }
  }
  
  slideChange() {
    const swiper = this.$swiper
    const index = swiper?.realIndex ?? 0
    const image = this.images[index] || null
    this.$emit('slideChange', { index, current: image, swiper})
  }

  @Watch('initIndex', { immediate: true })
  initIndexChange(val: number) {
    this.$nextTick(() => {
      this.$swiper && this.$swiper.slideTo(val)
    })
  }

  close() {
    this.$emit('close')
  }
}
</script>

<style lang="scss" scoped>
.activity-image-viewer_swiper_container {
  height: 100%;

  .image-viewer-title {
    position: absolute;
    z-index: 2;
    top: 56px;
    left: 0;
    width: 100%;
    padding: 16px 20px;
    color: $color-text-reverse;
    text-align: center;
  }

  .swiper-wrapper {
    .swiper-slide {
      height: 100%;
      display: flex;
      align-items: center;

      .vue-zoomer {
        padding: 400px 0;
        margin: -400px 0;
        width: 100%;
      }
    }
  }

  .activity-image {
    width: 100%;

    &-wrapper {
      padding: 400px 0;
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100%;
      width: 100%;
      overflow-y: auto;
    }

    &-content {
      max-height: 100%;
      width: 100%;
    }
  }

  .image-viewer-top {
    display: flex;
    height: 64px;
    align-items: center;
    justify-content: center;
    color: $color-text-reverse;
    position: absolute;
    z-index: 2;
    top: 0;
    width: 100%;
    left: 0;

    .activity-image_icon-close {
      position: absolute;
      left: 16px;
      top: 20px;
      z-index: 11;
      width: 24px;
      height: 24px;
    }

    .activity-swiper-pagination {
      position: static;
    }
  }

  .merchant-image-description {
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    padding-bottom: calc(env(safe-area-inset-bottom) + 16px);
    max-height: 133px;
    overflow: auto;
    z-index: 1;
    box-sizing: border-box;
  }
}
</style>
