<template>
  <div class="exp-common-comp-swiper-wrapper-desktop">
      <CloseSvg
        v-if="showClose"
        class="activity-image_icon-close"
        @click.native="close"
      />
    <div class="exp-swiper-title">
      <slot name="title">{{ title }}<slot>
    </div>
    <div class="exp-swiper-container">
      <div class="exp-swiper-container-body">
        <div
          ref="$bigImageSwiper"
          v-swiper:$bigImageSwiper="bigImageSwiperOption"
          class="swiper-container js-swiper-container"
        >
          <div class="swiper-wrapper">
            <div
              v-for="(item, idx) in images"
              :key="idx"
              v-lazy:background-image.container="item[realFieldKey.img_url]"
              ratio="1:1"
              class="swiper-slide"
              :class="highlightSwiperSlideIndex === idx && 'current-slide-visible'"
            >
              <div
                v-show="imageScroll && isScreened && highlightSwiperSlideIndex === idx" 
                class="swiper-slide__img-wrap"
              >
                <img v-lazy="item[realFieldKey.img_url]" class="swiper-slide__img">
              </div>
            </div>
          </div>
          <div
            v-if="imageScroll && showEnlargeEntry"
            class="swiper-screen"
            @click.stop="handleScreen"
          >
            <Enlarge class="enlarge" />
            <span class="swiper-screen-text">{{ screenText }}</span>
          </div>
          <div class="swiper-pagination swiper-pagination-fraction">
            {{ highlightSwiperSlideIndex + 1 }} / {{ images.length }}
          </div>
          <div class="next-prev swiper-next">
            <span class="next-prev__icon">
              <ArrowSvg class="arrow-svg" />
            </span>
          </div>
          <div class="next-prev swiper-prev">
            <span class="next-prev__icon">
              <ArrowSvg class="arrow-svg" />
            </span>
          </div>
        </div>
      </div>
      <div class="exp-swiper-container-content">
        <slot name="content"></slot>
      </div>
    </div>
    <div class="small-swiper-container">
      <div
        ref="$smallImageSwiper"
        v-swiper:$smallImageSwiper="smallImageSwiperOption"
        class="swiper-container"
      >
        <div class="swiper-wrapper swiper-no-swiping">
          <div
            v-for="(item, idx) in images"
            :key="idx"
            v-lazy:background-image.container="item[realFieldKey.img_resize_url]"
            ratio="1:1"
            class="swiper-slide swiper-no-swiping"
            :class="highlightSwiperSlideIndex === idx ? 'high-light-swiper-slide': ''"
            @click="highlightSwiperSlide(idx)"
          >
          </div>
        </div>
      </div>
      <div class="small-control small-swiper-next">
        <ArrowSvg class="arrow-svg" />
      </div>
      <div class="small-control small-swiper-prev">
        <ArrowSvg class="arrow-svg" />
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Prop, Watch } from 'vue-property-decorator'
import SwiperBase from '../../base'

import ArrowSvg from '../../assets/svg/arrow-right.svg'
import Enlarge from '../../assets/svg/enlarge.svg'
import CloseSvg from '../../assets/svg/icon-close.svg'

@Component({
  components: {
    ArrowSvg,
    Enlarge,
    CloseSvg
  }
})
export default class DesktopImagesSwiper extends SwiperBase {
  @Prop({ default: true }) imageScroll!: boolean

  $smallImageSwiper: any = null;
  $bigImageSwiper: any = null;
  highlightSwiperSlideIndex: number = 0;
  isScreened: boolean = false

  @Watch('initIndex', { immediate: true })
  initIndexChange(val: number) {
    this.$nextTick(() => {
      this.$bigImageSwiper && this.$bigImageSwiper.slideTo(val)
      this.handleImageLoad(val)
    })
  }

  get screenText() {
    return this.isScreened ? this.translateI18n('102478') : this.translateI18n('102477')
  }

  close() {
    this.$emit('close')
  }

  smallImageSwiperOption: object = {
    slidesPerView: "auto",
    centeredSlides: false,
    navigation: {
      nextEl: ".small-swiper-next",
      prevEl: ".small-swiper-prev",
    },
    lazy: {
      loadPrevNext: true,
      loadOnTransitionStart: true,
    },
    spaceBetween: 10,
    slidesPerGroup: 8,
    noSwiping: true,
    on: {
      init: () => {
        this.initSwiper("$smallImageSwiper");
      },
      slideChange: () => {
        this.slideChangeTransitionEnd("$smallImageSwiper");
        this.emitChange('smallSlideChange','$smallImageSwiper')
      },
    },
  };

  bigImageSwiperOption: object = {
    navigation: {
      nextEl: ".swiper-next",
      prevEl: ".swiper-prev",
    },
    lazy: {
      loadPrevNext: true,
      loadOnTransitionStart: true,
    },
    on: {
      init: () => {
        this.initSwiper("$bigImageSwiper");
      },
      slideChange: () => {
        this.BigSwiperSlideChange();
        this.slideChangeTransitionEnd("$bigImageSwiper");
        this.emitChange('slideChange','$bigImageSwiper')
      },
      slideChangeTransitionStart: this.handleSlideStart,
    },
  }

  emitChange(name: string, swiper: string) {
    const $swiper = (this as any)[swiper];
    const index = $swiper?.realIndex ?? 0
    const image = this.images[index] || null
    this.$emit(name, { index, current: image, swiper})
  }

  handleSlideStart() {
    this.isScreened = false
  }

  slideChangeTransitionEnd(swiper: string) {
    const selfSwiper = (this as any)[swiper];
    if (selfSwiper.isEnd) {
      selfSwiper.navigation.$nextEl.css("display", "none");
      this.$emit('end')
    } else {
      selfSwiper.navigation.$nextEl.css("display", "flex");
    }
    if (selfSwiper.isBeginning) {
      selfSwiper.navigation.$prevEl.css("display", "none");
    } else {
      selfSwiper.navigation.$prevEl.css("display", "flex");
    }

    const realIndex = selfSwiper.realIndex
  
    if (this.images.length == realIndex + 1) {
      this.$emit('end')
    }
  }

  initSwiper(swiper: string) {
    this.$nextTick(() => {
      const selfSwiper = (this as any)[swiper];
      if (selfSwiper.isBeginning) {
        selfSwiper.navigation.$prevEl.css("display", "none");
      } else {
        selfSwiper.navigation.$prevEl.css("display", "flex");
      }
    });
  }

  BigSwiperSlideChange() {
    const idx = this.$bigImageSwiper.realIndex
    this.highlightSwiperSlideIndex = idx
    this.$smallImageSwiper.slideTo(idx)
    this.$emit('bigSwiperChange', idx)
    this.handleImageLoad(idx)
  }

  highlightSwiperSlide(idx: number) {
    this.$bigImageSwiper.slideTo(idx);
    this.highlightSwiperSlideIndex = idx;
  }

  handleScreen() {
    this.isScreened = !this.isScreened
  }

  showEnlargeEntry: boolean = false
  timer: any = null
  
  handleImageLoad(idx: number) {
    const _this = this
    this.showEnlargeEntry = false
    if (!this.imageScroll) { return }

    this.timer = setTimeout(() => {
      const image: any = this.images[idx]
      const realFieldKey = this.realFieldKey
      const img_url = image[realFieldKey.img_url]
      if (!img_url) {
        return
      }
      const width = image[realFieldKey.width]
      const height = image[realFieldKey.height]
      if (width && height) {
        this.getImgSize(image)
        return
      }      
      const img = new Image()
      img.src = img_url
      img.onload = function(){
        image[realFieldKey.width] = img.width
        image[realFieldKey.height] = img.height
        _this.getImgSize({
          width: img.width,
          height: img.height
        })
      }
    }, 350)
  }

  getImgSize(source: any) {
    const elem = this.$el
    const container = elem.querySelector('.js-swiper-container .swiper-wrapper')
    if (container) {
      const { clientWidth: boxWidth, clientHeight: boxHeight } = container

      if (source) {
        const { width: imageWidth, height: imageHeight } = source

        if (imageWidth && imageHeight) {
          const boxRatio = boxWidth / boxHeight
          const imageRatio = imageWidth / imageHeight
          this.showEnlargeEntry = imageRatio <= boxRatio
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.exp-common-comp-swiper-wrapper-desktop {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 40px;
  position: relative;

  .activity-image_icon-close {
    position: absolute;
    z-index: 2;
    top: 40px;
    right: 40px;
    width: 36px;
    height: 36px;
    cursor: pointer;
  }

  .exp-swiper {
    &-title {
      font-size: $fontSize-body-m;
      font-weight: 500;
      color: $color-text-reverse;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: calc(100% - 120px);
      text-align: center;
      white-space: nowrap;
      margin: 0 auto 40px;
      flex: none;
    }

    &-container {
      display: flex;
      flex: 1;
      max-height: calc(100% - 160px);

      &-body {
        flex: 1;
      }

      &-content {
        max-width: 350px;
        flex: none;
      }
    }
  }

  .small-swiper-container {
    position: relative;
    width: calc(100% - 128px);
    height: 72px;
    margin: 20px auto 0;

    .swiper-slide {
      box-sizing: border-box;
      width: 72px;
      height: 72px;
      background-size: cover;
      background-repeat: no-repeat;
      background-position: center center;
      cursor: pointer;
      border-radius: $radius-m;
      opacity: 0.6;

      &[lazy=loading] {
        background-color: $color-bg-3;
        background-image: url(https://res.klook.com/image/upload/image_logo_mx7wgd.png) !important;
        background-position: center;
        background-size: 60%;
        background-origin: border-box;
      }

      &.high-light-swiper-slide {
        opacity: 1;
        border: 3px solid $color-text-reverse;
      }
    }

    .small-control {
      position: absolute;
      top: 50%;
      cursor: pointer;
      z-index: 10;
      outline: none;
      visibility: visible;
      width: 32px;
      height: 32px;
      border-radius: 50%;
      background-color: hsla(0,0%,100%,.16);
      display: flex;
      align-items: center;
      justify-content: center;
      transform: translateY(-50%);

      &:hover {
        border: 1px solid $color-text-reverse;
      }

      .arrow-svg {
        width: 20px;
        height: 20px;
      }
    }

    .small-swiper-next {
      right: -48px;
    }

    .small-swiper-prev {
      left: -48px;

      .arrow-svg {
        transform: rotate(180deg);
      }
    }
  }

  .exp-swiper-container-body {
    flex: 1;
    position: relative;
    max-width: 100%;

    .swiper-container {
      padding: 0 64px;
      max-width: 100%;
      height: 100%;
      max-height: 100%;
      overflow: hidden;

      .swiper-slide {
        position: relative;
        background-size: contain;
        background-repeat: no-repeat;
        background-position: center center;
        display: flex;
        align-items: center;
        visibility: hidden;

        &.current-slide-visible {
          visibility: visible;
        }

        &[lazy=loading] {
          background-color: $color-bg-3;
          background-image: url(https://res.klook.com/image/upload/image_logo_mx7wgd.png) !important;
          background-position: center;
          background-size: 300px 300px;
          background-origin: border-box;
        }

        &__img-wrap {
          width: 100%;
          max-height: 100%;
          overflow: auto;
          cursor: pointer;

          &::-webkit-scrollbar {
            -webkit-appearance: none;
            background-color: transparent;
            width: 7px;
          }

          &:hover::-webkit-scrollbar {
            -webkit-appearance: none;
            background: transparent;
            width: 7px;
          }

          &:hover::-webkit-scrollbar-track {
            background: transparent;
            width: 7px;
          }

          &:hover::-webkit-scrollbar-thumb {
            border-radius: 4px;
            background-color: rgba(255, 255, 255, .3);
            box-shadow: 0 0 1px rgba(255, 255, 255, .5);
          }
        }

        &__img {
          display: block;
          width: 100%;
        }
      }

      .next-prev {
        position: absolute;
        top: 0;
        height: 100%;
        width: 64px;
        cursor: pointer;
        z-index: 1;

        &__icon {
          width: 48px;
          height: 48px;
          line-height: 48px;
          border-radius: 50%;
          background-color: rgba(255, 255, 255, 0.16);
          position: absolute;
          top: 50%;
          right: 0;
          transform: translate(0, -50%);
          display: flex;
          align-items: center;
          justify-content: center;

          .arrow-svg {
            width: 24px;
            height: 24px;
          }

          &:hover {
            border: 1px solid $color-text-reverse;
            background-color: rgba(255, 255, 255, 0.25);
          }
        }

      }

      .swiper-next {
        right: 0;
      }

      .swiper-prev {
        left: 0;
        transform: rotate(180deg);
      }

      .swiper-pagination {
        padding: 3px 4px;
        position: absolute;
        left: auto;
        right: calc(64px + 8px);
        bottom: 8px;
        width: auto;
        color: $color-text-reverse;
      }

      .swiper-screen {
        padding: 8px;
        position: absolute;
        left: 50%;
        bottom: 8px;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        border-radius: $radius-m;
        color: $color-text-reverse;
        background-color: rgba(0, 0, 0, 0.45);
        transform: translateX(-50%);
        z-index: 100;
        cursor: pointer;

        .enlarge {
          width: 16px;
          height: 16px;
        }

        &:hover {
          background-color: rgba(0, 0, 0, 0.6);
        }

        &-text {
          margin-left: 8px;
        }
      }
    }

    .exp-swiper-container-content {
      max-width: 350px;
    }
  }
}
</style>
