<template>
  <transition name="fade">
    <div v-show="visible" class="activity-layer">
      <div class="activity-layer_mask" @click="clickMask"></div>

      <transition :name="transition">
        <div v-show="visible" ref="content" :style="contentStyle" class="activity-layer_content">
          <div v-show="visible" :class="['activity-layer-header_wrap', { fixed }]">
            <div class="activity-layer-header" :style="headerStyle">
              <slot name="title">
                <LayerHeader v-if="headerVisible || title" :title="title" :icon="icon" @close="close"></LayerHeader>
              </slot>
            </div>
          </div>

          <slot v-if="visible"></slot>
          <slot v-show="visible" name="firstRenderContent"></slot>
        </div>
      </transition>
    </div>
  </transition>
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch } from 'vue-property-decorator'
import LayerHeader from '../layer-header/index.vue'

@Component({
  components: {
    LayerHeader
  }
})
export default class Layer extends Vue {
  @Prop({ type: String, default: '100%' }) height!: string
  @Prop() headerVisible!: boolean
  @Prop({
    type: String,
    default: 'slide-up',
    validator(value: string): boolean {
      return ['slide-up', 'slide-right', 'fade'].includes(value)
    }
  }) transition!: string

  @Prop() readonly visible!: boolean
  @Prop() overlayClosable!: boolean
  @Prop() title!: string
  @Prop() icon!: string
  @Prop() fixed!: boolean
  @Prop() color!: boolean
  @Prop() transfer!: boolean
  @Prop({ default: false }) notFixed!: boolean

  fixScrollY = 0
  transfered = false
  originBodyPosition: string = ''

  preventDefault(e: any) {
    e.preventDefault();
  }
  
  disableScroll() {
    if (this.notFixed) {
      return
    }

    this.$el.addEventListener('touchmove', this.preventDefault, { passive: false });
    this.$el.addEventListener('mousewheel', this.preventDefault, { passive: false });
  }
  
  get contentStyle() {
    const style: any = {}

    if (this.height) {
      style.height = this.height
    }

    if (this.color) {
      style.backgroundColor = this.color
    }

    return style
  }

  get headerStyle() {
    if (!this.fixed) {
      return {}
    }

    const height = parseFloat(this.height)
    return {
      top: /%/.test(this.height) ? `${100 - height}%` : `calc(100% - ${height}px)`
    }
  }

  clickMask() {
    if (this.overlayClosable) {
      this.$emit('close')
    }
  }

  close() {
    this.$emit('close')
  }

  mounted() {
    if (this.transfer && !this.transfered) {
      document.body.appendChild(this.$el)
      this.transfered = true
    }
    this.disableScroll()
  }

  beforeDestroy() {
    if (this.transfer && this.transfered && this.$el) {
      if (this.$el.parentNode) {
        this.$el.parentNode.removeChild(this.$el)
      }
    }
  }
}
</script>

<style lang="scss" scoped>

.activity-layer {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 999;
  background-color: $color-bg-overlay-black-mobile;

  .activity-layer_mask {
    width: 100%;
    height: 100%;
    background-color: $color-bg-overlay-black-mobile;
  }

  .activity-layer_content-wrap {
    height: 100%;
    width: 100%;
  }

  .activity-layer-header_wrap.fixed {
    height: 48px;
    width: 100%;

    .activity-layer-header {
      background: $color-bg-widget-normal;
      position: fixed;
      left: 0;
      top: 0;
      height: 48px;
      width: 100%;
      z-index: 2;
    }
  }

  .activity-layer_content {
    width: 100%;
    height: 100%;
    position: fixed;
    z-index: 1;
    bottom: 0;
    left: 0;
    overflow-y: auto;
    overflow-x: hidden;
    -webkit-overflow-scrolling: touch;
    background-color: $color-bg-widget-normal;
  }

  .fade-enter,
  .fade-leave-to {
    opacity: 0;
  }

  .fade-enter-active,
  .fade-leave-active {
    transition: all 0.1s ease;
  }

  .slide-up-enter,
  .slide-up-leave-to {
    transform: translateY(100%);
  }

  .slide-up-enter-active,
  .slide-up-leave-active {
    transition: all 0.3s ease;
  }

  .slide-left-enter,
  .slide-left-leave-to {
    transform: translateX(100%);
  }

  .slide-left-enter-active,
  .slide-left-leave-active {
    transition: all 0.3s ease;
  }
}
</style>
