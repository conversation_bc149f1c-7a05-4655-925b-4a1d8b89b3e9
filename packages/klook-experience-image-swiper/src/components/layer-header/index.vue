<template>
  <div class="layer-head">
    <CloseSvg
      class="icon-close"
      @click.native="close"
    />

    <span class="title">
      {{ title }}
    </span>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator'
import CloseSvg from '../../assets/svg/icon-close.svg'

@Component({
  components: {
    CloseSvg
  }
})
export default class LayerHeader extends Vue {
  @Prop() title !: string
  @Prop({ type: String, default: 'mobile-activity#icon-close' }) icon !: string

  close() {
    this.$emit('close')
  }
}
</script>

<style lang="scss">
.layer-head {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 48px;
  color: $color-neutral-900;
  padding: 0 16px;
  /* stylelint-disable */
  box-shadow: inset 0 -1px 0 0 rgba(0, 0, 0, 0.12);
  /* stylelint-enable */

  .icon-close {
    width: 24px;
    height: 24px;
    color: $color-neutral-900;
    position: absolute;
    left: 16px;
  }

  .title {
    font-weight: 500;
    font-size: $fontSize-body-m;
  }
}
</style>
