import { Component, Prop, Vue, Provide } from 'vue-property-decorator'
import Layer from '../components/layer/index.vue'
import MobileImagesSwiper from '../components/mobile/index.vue'
import DesktopImagesSwiper from '../components/desktop/index.vue'

@Component({
  components: {
    Layer,
    MobileImagesSwiper,
    DesktopImagesSwiper
  }
})
export default class ViewerBase extends Vue {
  @Prop({ type: String, default: 'mobile' }) platform!: string
  @Prop({ type: Boolean, default: true }) showClose!: boolean
  @Prop({ type: Boolean, default: false }) visible!: boolean
  @Prop({ type: Boolean, default: true }) transfer!: boolean
  @Prop() photoType!: string
  @Prop({ default: false }) notFixed!: boolean
  @Prop({ type: Array, default: () => [] }) images!: Images []
  @Prop({ default: () => () => {}}) localesTranslate!: Function
  @Prop({ type: String, default: '' }) title!: string
  @Prop({ type: Number, default: 0 }) initIndex!: number

  @Prop(
    {
      type: Object,
      default: () => ({
        img_url: 'img_url',
        img_resize_url: 'img_resize_url',
        width: 'width',
        height: 'height'
      })
    }
  ) fieldKey!: any

  get realFieldKey() {
    const df = {
      img_url: 'img_url',
      img_resize_url: 'img_resize_url',
      width: 'width',
      height: 'height'
    }
    return { ...df, ...this.fieldKey }
  }

  @Provide('translateI18n')
  translateI18n(key: string, ...values: any) {
    const $t = this.localesTranslate()
    return $t(key, ...values)
  }

  get isMobile() {
    return this.platform === 'mobile'
  }

  get componentName() {
    return this.isMobile ? 'MobileImagesSwiper' : 'DesktopImagesSwiper'
  }

  slideChange(data: any) {    
    this.$emit('slideChange', data)
  }

  handleClose() {
    this.$emit('close')
    this.$emit('update:visible', false)
  }
}
