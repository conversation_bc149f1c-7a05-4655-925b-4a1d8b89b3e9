import { Vue, Prop } from 'vue-property-decorator'

import VueLazyLoad from 'vue-lazyload';

const isServer = typeof window === 'undefined'
if (!isServer) {
  const VueAwesomeSwiper = require('vue-awesome-swiper/dist/ssr')
  Vue.use(VueAwesomeSwiper)
}

const lazyLoadingImage = 'https://res.klook.com/image/upload/image_logo_mx7wgd.png'

Vue.use(VueLazyLoad, {
  adapter: {
    loaded(listender: any) {
      const el = listender.el
      const ratio = el.getAttribute('ratio')

      if (!isServer && ratio) {
        const logo = el?.querySelector?.('.v-lazy-logo') || el.parentElement?.querySelector?.('.v-lazy-logo')
        logo && (logo.style.display = 'none')
      }
    }
  },
  // loading: lazyLoadingImage,
  error: lazyLoadingImage
})


export default class SwiperBase extends Vue {
  @Prop({ default: () => [] }) images!: Images []
  @Prop({ type: String, default: '' }) title!: string
  @Prop({ type: Number, default: 0 }) initIndex!: number
  @Prop({ default: () => () => {}}) localesTranslate!: Function
  @Prop({ type: Boolean, default: true }) showClose!: boolean
  @Prop(
    {
      type: Object,
      default: () => ({
        img_url: 'img_url',
        img_resize_url: 'img_resize_url',
        width: 'width',
        height: 'height'
      })
    }
  ) fieldKey!: any

  get realFieldKey() {
    const df = {
      img_url: 'img_url',
      img_resize_url: 'img_resize_url',
      width: 'width',
      height: 'height'
    }
    return { ...df, ...this.fieldKey }
  }
  
  translateI18n(key: string, ...values: any) {
    const $t = this.localesTranslate()
    return $t(key, ...values)
  }
}