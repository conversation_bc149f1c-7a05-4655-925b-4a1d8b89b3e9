import en_BS from "./en.json";
import en_US from "./en-US.json";
import en_AU from "./en-AU.json";
import en_NZ from "./en-NZ.json";
import en_GB from "./en-GB.json";
import en_IN from "./en-IN.json";
import en_SG from "./en-SG.json";
import en_CA from "./en-CA.json";
import en_PH from "./en-PH.json";
import en_MY from "./en-MY.json";
import ms_MY from "./ms-MY.json";
import en_HK from "./en-HK.json";
import zh_CN from "./zh-CN.json";
import zh_TW from "./zh-TW.json";
import zh_HK from "./zh-HK.json";
import de_DE from "./de.json";
import it_IT from "./it.json";
import fr_FR from "./fr.json";
import ru_RU from "./ru.json";
import es_ES from "./es.json";
import ko_KR from "./ko.json";
import th_TH from "./th.json";
import vi_VN from "./vi.json";
import id_ID from "./id.json";
import ja_JP from "./ja.json";

export {
  en_BS,
  en_US,
  en_AU,
  en_NZ,
  en_GB,
  en_IN,
  en_SG,
  en_CA,
  en_PH,
  en_MY,
  en_HK,
  zh_CN,
  zh_TW,
  zh_HK,
  de_DE,
  it_IT,
  fr_FR,
  ru_RU,
  es_ES,
  ko_KR,
  th_TH,
  vi_VN,
  id_ID,
  ja_JP,
  ms_MY
};

export default {
  en: en_BS,
  "en-US": en_US,
  "en-AU": en_AU,
  "en-NZ": en_NZ,
  "en-GB": en_GB,
  "en-IN": en_IN,
  "en-SG": en_SG,
  "en-CA": en_CA,
  "en-PH": en_PH,
  "en-MY": en_MY,
  "ms-MY": ms_MY,
  "en-HK": en_HK,
  "zh-CN": zh_CN,
  "zh-TW": zh_TW,
  "zh-HK": zh_HK,
  de: de_DE,
  it: it_IT,
  fr: fr_FR,
  ru: ru_RU,
  es: es_ES,
  ko: ko_KR,
  th: th_TH,
  vi: vi_VN,
  id: id_ID,
  ja: ja_JP
};
