<template>
  <div class="klook-simplify-checkout-terms">
    <SpecialTerms
      ref="specialTerms"
      :class="{'klook-simplify-checkout-terms__hide-check-all': !showCheckAll }"
      :platform="platform"
      :scene="scene"
      @loaded="handleLoaded"
      :show-check-all="showCheckAll"
      :textData="textData"
    />

    <div>
      <klk-modal
        :class="`klook-simplify-checkout-register klook-simplify-checkout-register-${platform}`"
        v-if="platform === 'desktop'"
        :data-spm-module="`InfoConfirmPopup?ext=${JSON.stringify({
          IfOption: !allDisplayOnly,
        })}`"
        :open.sync="showTerms"
        :closable="true"
        :show-default-footer="false"
        :scrollable="false"
        :width="436"
        :title="__t('177434')"
        @close="showTerms = false"
      >
        <div>
          <div class="klook-simplify-checkout-register-account-desc">
            {{ __t("177432") }}
          </div>

          <div class="klook-simplify-checkout-register-account-wrap">
            <div v-if="mobile">
              <IconMobile theme="outline" size="16" fill="#212121" />
              <span class="klook-simplify-checkout-register-account">{{
                mobile
              }}</span>
            </div>
            <div v-if="email">
              <IconMail theme="outline" size="16" fill="#212121" />
              <span class="klook-simplify-checkout-register-account">{{
                email
              }}</span>
            </div>
          </div>

          <SpecialTerms
            v-if="!allDisplayOnly"
            ref="specialTermsDesktop"
            :platform="platform"
            scene="login_new"
            :textData="textData"
          />

          <div class="klook-simplify-checkout-login-method-terms_footer">
            <klk-button
              :data-spm-item="`Confirm?ext=${JSON.stringify({
                OptionIn: OptionIn,
              })}`"
              block
              @click="onConfirm"
            >
              {{ __t("177451") }}
            </klk-button>
            <klk-button
              data-spm-item="Cancel"
              block
              type="outlined"
              @click="showTerms = false"
            >
              {{ __t("177452") }}
            </klk-button>
          </div>
        </div>
      </klk-modal>

      <klk-bottom-sheet
        :class="`klook-simplify-checkout-register klook-simplify-checkout-register-${platform}`"
        show-close
        v-if="showTerms && platform === 'mobile'"
        :data-spm-module="`InfoConfirmPopup?ext=${JSON.stringify({
          IfOption: !allDisplayOnly,
        })}`"
        header-divider
        :title="__t('177434')"
        :visible.sync="showTerms"
      >
        <div>
          <div class="klook-simplify-checkout-register-account-desc">
            {{ __t("177432") }}
          </div>

          <div class="klook-simplify-checkout-register-account-wrap">
            <div v-if="mobile">
              <IconMobile theme="outline" size="16" fill="#212121" />
              <span class="klook-simplify-checkout-register-account">{{
                mobile
              }}</span>
            </div>
            <div v-if="email">
              <IconMail theme="outline" size="16" fill="#212121" />
              <span class="klook-simplify-checkout-register-account">{{
                email
              }}</span>
            </div>
          </div>

          <SpecialTerms
            v-if="!allDisplayOnly"
            ref="specialTermsDesktop"
            :platform="platform"
            scene="login_new"
            :textData="textData"
          />

          <div class="klook-simplify-checkout-login-method-terms_footer">
            <klk-button
              :data-spm-item="`Confirm?ext=${JSON.stringify({
                OptionIn: OptionIn,
              })}`"
              block
              @click="onConfirm"
            >
              {{ __t("177451") }}
            </klk-button>
            <klk-button
              data-spm-item="Cancel"
              block
              type="outlined"
              @click="showTerms = false"
            >
              {{ __t("177452") }}
            </klk-button>
          </div>
        </div>
      </klk-bottom-sheet>
    </div>
  </div>
</template>

<script lang="ts">
import { Vue, Component, Prop, Provide } from "vue-property-decorator";
import { genText } from "./locale/index";
import messages from "../locales/index.js";
import SpecialTerms from "@klook/klook-special-terms";
import { IconMail, IconMobile } from "@klook/klook-icons";

// import "@klook/klook-special-terms/dist/esm/index.css";
import {
  BottomSheet as KlkBottomSheet,
  Modal as KlkModal,
  Button as KlkButton,
} from "@klook/klook-ui";

@Component({
  name: "SimplifyCheckoutTerms",
  components: {
    SpecialTerms,
    KlkBottomSheet,
    KlkModal,
    KlkButton,
    IconMail,
    IconMobile,
  },
})
export default class SimplifyCheckoutTerms extends Vue {
  @Prop({
    default: "desktop",
  })
  platform!: "mobile" | "desktop";

  @Prop({
    default: "simplify_checkout",
  })
  scene!: string;

  @Prop({
    default: "",
  })
  mobile!: string;

  @Prop({
    default: "",
  })
  email!: string;

  @Prop({
    default: false,
  })
  showCheckAll?: boolean;

  @Provide() __t: any = this.getTranslate();

  _axios!: any;
  showTerms = false;
  termsResolve: any = null;
  termsReject: any = null;
  simplifyCheckoutTermIds = "";
  OptionIn = "";

  getTranslate() {
    return this.__t;
  }

  allDisplayOnly = false;

  handleLoaded() {
    // @ts-ignore
    const termList = this.$refs.specialTerms?.termList || [];
    this.$emit("loaded", termList);
  }

  onConfirm() {
    let termRef = this.$refs.specialTermsDesktop;
    if (this.platform === "mobile") {
      termRef = this.$refs.specialTermsMobile;
    }

    // @ts-ignore
    this.$refs.specialTerms.termList[0].isChecked = true;
    // @ts-ignore
    this.$refs.specialTerms.changeBox();

    if (termRef) {
      // @ts-ignore
      termRef.validator((valid, termIds) => {
        if (valid) {
          const simplifyCheckoutTermArr =
            this.simplifyCheckoutTermIds.split(",");
          const termIdArr = termIds.split(",");
          // 确认即表示用户同意337条款
          const allTermArr = simplifyCheckoutTermArr.concat(termIdArr).concat(["337"]);
          this.termsResolve(allTermArr.join(","));
          this.showTerms = false;
        } else {
          this.termsReject(337)
        }
        this.OptionIn = termIds;
      });
    } else {
      this.showTerms = false;
      // @ts-ignore
      this.$refs.specialTerms.validator((valid, termsIds) => {
        if (!valid) return this.termsReject(337);
        this.termsResolve(termsIds);
      });
      this.OptionIn = "";
    }
  }

  getChecked() {
    // @ts-ignore
    return this.$refs.specialTerms?.checkList || [];
  }

  validator() {
    return new Promise((resolve, reject) => {
      this.termsResolve = resolve;
      this.termsReject = reject;

      // @ts-ignore
      this.$refs.specialTerms.validator((valid, termsIds) => {
        const termIdsArr = termsIds.split(",");
        // 用户没有勾选确认信息条款或者非Global（CN或KR）市场则弹窗
        if (
          this.scene === "simplify_checkout" &&
          (!termIdsArr.includes("337") || !this.allDisplayOnly)
        ) {
          this.showTerms = true;
          this.simplifyCheckoutTermIds = termsIds;
          return this.termsReject(null);
        }

        if (valid) {
          this.termsResolve(termsIds);
        } else {
          this.termsReject(null)
        }
      });
    });
  }

  beforeCreate(this: any) {
    const locales = messages as any;
    const lang = this.$attrs.language || "en";
    this.__t = locales[lang] ? genText(locales[lang]) : genText(locales["en"]);
    // @ts-ignore
    this._axios =
      this.$attrs.axios || (typeof window !== "undefined" && window?.$axios);
  }

  mounted() {
    if (this.scene === "simplify_checkout") {
      try {
        this._axios
          .$get("/v1/userapisrv/public/login/init")
          .then((res: any) => {
            const terms = res.result.terms || [];
            this.allDisplayOnly = terms.every(
              (item: { display_only: boolean }) => item.display_only === true
            );
          });
      } catch (e) {
        console.log(e);
      }
    }
  }

  get textData() {
    return {
      requiredTerms: this.__t("14777"),
      agreeTip: this.__t("12033"),
      confirm: this.__t("79918"),
      noThanks: this.__t("79921"),
      agree: this.__t("79920"),
    };
  }
}
</script>

<style lang="scss">
.klook-simplify-checkout-terms {
  &-desktop {
    .klk-login-term-list {
      margin-top: 22px;
    }
  }

  .klook-simplify-checkout-terms__hide-check-all {
    .tnc-list .klk-checkbox .klk-markdown {
      font-size: 16px;
    }

    .tnc-item {
      .klk-checkbox .klk-markdown, .klk-checkbox .klk-markdown a  {
        font-size: 16px;
      }
    }
  }

  .tnc-item {
      .klk-checkbox .klk-markdown, .klk-checkbox .klk-markdown a  {
        color: rgba(33, 33, 33, 1);
      }

      &.tnc-item-required_error {
        .klk-markdown, .klk-checkbox .klk-markdown a {
          color: #f44622;
        }
      }
    }
}

.klook-simplify-checkout-register {
  &-mobile {
    .klook-simplify-checkout-register-account-desc {
      margin-top: 16px;
    }
  }

  .klook-simplify-checkout-register-account-desc {
    margin-bottom: 16px;
  }

  .klook-simplify-checkout-register-account-wrap {
    padding: 16px;
    border-radius: 16px;
    background: rgba(250, 250, 250, 1);

    .klook-simplify-checkout-register-account {
      padding-left: 8px;
    }
  }

  &-mobile {
    .klk-login-term-list {
      margin-top: 16px;
    }

    .klook-simplify-checkout-login-method-terms_title {
      font-size: 16px;
      font-weight: 400;
    }
  }

  .klook-simplify-checkout-login-method-terms_footer {
    margin-top: 16px;
    .klk-button {
      margin-bottom: 12px;
    }
  }
}
</style>
