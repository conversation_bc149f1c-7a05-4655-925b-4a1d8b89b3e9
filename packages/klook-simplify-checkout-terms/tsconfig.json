{"compilerOptions": {"target": "esnext", "module": "esnext", "strict": true, "jsx": "preserve", "importHelpers": true, "moduleResolution": "node", "esModuleInterop": true, "allowSyntheticDefaultImports": true, "experimentalDecorators": true, "allowJs": true, "baseUrl": ".", "types": ["webpack-env", "jest"], "paths": {"@/*": ["./src/*"], "~": ["/"]}, "lib": ["esnext", "dom", "dom.iterable", "scripthost"]}, "exclude": ["node_modules", "unpackage"]}