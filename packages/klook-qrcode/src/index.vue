<template>
  <div
    v-loading="isLoading"
    data-klk-loading-show-loading-bg="false"
    data-klk-loading-show-overlay="true"
    data-klk-loading-overlay-color="rgba(255,255,255)"
    class="qrcode"
    :style="{ width: currentWidth, height: currentHeight }"
  >
    <div class="qrcode-logo"></div>
    <canvas class="qrcode-canvas"></canvas>
  </div>
</template>

<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator'

@Component
export default class Qrcode extends Vue {
  @Prop({ default: '120' }) width!: string
  @Prop({ default: '120' }) height!: string
  @Prop({ default: '' }) link!: string
  @Prop({ default: () => '' }) linkFunc!: Function
  @Prop({ default: 'M' }) errorCorrectionLevel!: 'L' | 'M' | 'Q' | 'H'
  @Prop({ default: 0 }) margin!: number
  @Prop({ default: '' }) refreshFlag!: string // 刷新参数

  isLoading = false

  get currentWidth() {
    const { width } = this
    return width.startsWith('%') ? width : `${width}px`
  }

  get currentHeight() {
    const { height } = this
    return height.startsWith('%') ? height : `${height}px`
  }

  mounted() {
    this.genQrcode()
    this.$watch(() => `${this.link}${this.refreshFlag}`, this.genQrcode)
  }

  async genQrcode() {
    this.isLoading = true

    const canvas = this.$el.querySelector('.qrcode-canvas')
    const qrcodeLink = this.link || await this.linkFunc()
    if (!canvas || !qrcodeLink) {
      this.isLoading = false
      return
    }

    const QRCode = await import('qrcode')
    const { margin, errorCorrectionLevel } = this
    QRCode.toCanvas(
      canvas,
      qrcodeLink,
      { margin, errorCorrectionLevel },
      (error: any) => {
        this.isLoading = false
        error && console.error(error)
      }
    )
  }
}
</script>

<style lang="scss" scoped>
.qrcode {
  position: relative;

  &-logo {
    position: absolute;
    transform: translate(-50%, -50%);
    top: 50%;
    left: 50%;
    width: 30%;
    height: 30%;
    background-image: url("https://res.klook.com/image/upload/klook_laf0fq.png");
    background-size: cover;
  }

  &-canvas {
    width: 100%!important;
    height: 100%!important;
  }
}
</style>
