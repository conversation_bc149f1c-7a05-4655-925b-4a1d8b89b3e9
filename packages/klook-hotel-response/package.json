{"name": "@klook/hotel-response", "version": "0.0.5-beta.1", "description": "A Common Component based on Vue of Klook", "author": "damon", "homepage": "https://storybook.klook.io", "keywords": ["vue", "component", "ui", "framework"], "files": ["lib", "esm", "type"], "browserslist": ["last 10 versions", "> 1%", "IE >= 10"], "license": "UNLICENSED", "publishConfig": {"registry": "https://knpm.klook.io", "access": "public"}, "scripts": {"build": "NODE_ENV=production rollup --config ./rollup.config.js", "lint": "NODE_ENV=production eslint --ext .js,.vue src", "test": "NODE_ENV=test jest -i --updateSnapshot", "test:coverage": "NODE_ENV=test jest -i --coverage --updateSnapshot", "prepush": "yarn run lint", "prepublishOnly": "bash prepublishOnly.sh", "commit": "npx git-cz", "commitmsg": "commitlint -E GIT_PARAMS"}, "main": "lib/index.js", "module": "esm/index.js", "typings": "type/index.d.ts", "devDependencies": {"@babel/core": "^7.9.0", "@babel/plugin-proposal-class-properties": "^7.16.7", "@babel/plugin-proposal-decorators": "^7.17.9", "@babel/plugin-transform-runtime": "^7.4.0", "@babel/preset-env": "^7.9.5", "@babel/preset-typescript": "^7.9.0", "@klook/galileo-vue": "^1.2.14", "@klook/hotel-card": "^2.2.14", "@klook/klook-card": "^0.4.10", "@klook/image": "^0.1.19", "@klook/klook-responsive": "0.0.3", "@klook/klook-ui": "^1.30.0", "@types/jest": "^26.0.0", "@types/lodash": "^4.14.182", "@types/uuid": "3.4.6", "@types/webpack-env": "^1.14.0", "@vue/babel-helper-vue-jsx-merge-props": "^1.2.1", "@vue/babel-preset-jsx": "^1.2.4", "@vue/cli-plugin-babel": "^4.3.1", "@vue/test-utils": "^1.0.0-beta.32", "autoprefixer": "^8.0.0", "babel-helper-vue-jsx-merge-props": "^2.0.3", "babel-plugin-syntax-jsx": "^6.18.0", "babel-plugin-transform-vue-jsx": "^4.0.1", "babel-preset-env": "^1.7.0", "cssnano": "^5.1.8", "jest": "^25.5.4", "postcss": "^8.4.13", "postcss-loader": "^6.2.1", "rimraf": "^3.0.0", "rollup": "^1.32.1", "rollup-plugin-babel": "^4.4.0", "rollup-plugin-buble": "^0.19.8", "rollup-plugin-clear": "^2.0.7", "rollup-plugin-commonjs": "^10.0.2", "rollup-plugin-node-resolve": "^5.2.0", "rollup-plugin-peer-deps-external": "^2.2.4", "rollup-plugin-postcss": "^4.0.1", "rollup-plugin-replace": "^2.2.0", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-typescript2": "^0.31.2", "rollup-plugin-vue": "^5.1.9", "terser": "^4.1.3", "ts-jest": "^26.1.0", "ts-node": "^8.10.2", "typescript": "^4.6.4", "vue": "2.6.11", "vue-class-component": "^7.2.6", "vue-jest": "^3.0.4", "vue-property-decorator": "^9.1.2", "vue-template-compiler": "2.6.11", "vue-tsx-support": "^3.0.2"}, "peerDependencies": {"@klook/galileo-vue": "^1.2.14", "@klook/hotel-card": "^2.2.14", "@klook/klook-card": "^0.4.10", "@klook/image": "^0.1.19", "@klook/klook-ui": "^1.30.0", "dayjs": "1.10.6", "lodash": "4.17.15", "vue": "2.6.11"}}