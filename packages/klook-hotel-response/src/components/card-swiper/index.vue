<template>
    <div class="hotel-response-card-swiper">
        <h2 v-if="title" class="title">
            {{ title }}
        </h2>
        <CardSwiper :controller-offset="12">
            <CardSwiperItem v-for="(cardInfo, index) in list" :key="index"
                class="klk-col-md-3 klk-col-lg-3 klk-col-xl-4 klk-col-sm-1-5">
                <card v-galileo-click-tracker="{spm: spm, enable: !!spm}"  class="klk-card-swiper-item" v-bind="getCardProps(cardInfo, index, list.length)" />
            </CardSwiperItem>
        </CardSwiper>
    </div>

</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator'

import { CardSwiper, CardSwiperItem } from '@klook/klook-responsive'
import Card from '@klook/hotel-card'
import '@klook/klook-responsive/dist/cjs/index.css';
import ResponsiveControl from '../../mixin/responsive-control'
import '@klook/hotel-card/dist/cjs/index.css'
@Component({
    components: {
        CardSwiper,
        CardSwiperItem,
        Card
    }
})
export default class HotelCardSwiper extends ResponsiveControl {
    @Prop({ type: Object, default: null }) ihtAttrs !: any
    @Prop({ type: Array, default: null }) list !: any
    @Prop({ type: String, default: null }) title !: string
    @Prop({ type: Object, default: null }) listResult !: any
    
    get isMobile() {
        return this.screenPlatform !== 'desktop'
    }

    get spm() {
        return this.ihtAttrs?.cardModule
    }

    getCardProps(cardInfo: any, idx: number, len: number) {
        const target = this.isMobile ? '_self' : '_blank'
        return {
            href: cardInfo.deep_link,
            platform: this.isMobile ? 'mobile' : 'desktop',
            target,
            cardInfo,
            isAb: !!this.listResult?.is_new_card_style,
            'data-spm-module': `${this.ihtAttrs?.cardModule}?oid=hotel_${cardInfo.hotel_info?.hotel_id}&idx=${idx}&len=${len}`
        }
    }

}
</script>

<style lang="scss" scoped>
@import '../../style/_mixin.scss';
.hotel-response-card-swiper {
    .title {
        @include font-heading-s;
        padding: 20px 0;
    }
    width: 100%;

    @include screen-size('lg', 'xl') {
      margin: 0 auto;
    }

    ::v-deep .hotel-card {
        height: 100%; 
    }
}
</style>