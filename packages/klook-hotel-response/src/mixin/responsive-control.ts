import { Component, Vue } from 'vue-property-decorator'
import debounce from 'lodash/debounce'
import { ScreenPlatform } from '../../type/index'

@Component
export default class ResponsiveControl extends Vue {

  debouncedHandleResize = debounce(this.handleResize, 200)

  screenPlatform: ScreenPlatform = 'mobile'

  mounted() {
    this.handleResize() // 初始化时调用一次以设置初始值
    window.addEventListener('resize', this.debouncedHandleResize)
  }

  beforeDestroy() {
    window.removeEventListener('resize', this.debouncedHandleResize)
  }

  handleResize() {
    const width = window.innerWidth
    let size = 'md'

    if (width < 768) {
      size = 'sm'
    } else if (width >= 768 && width < 992) {
      size = 'md'
    } else if (width >= 992 && width < 1200) {
      size = 'lg'
    } else if (width >= 1200) {
      size = 'xl'
    }

    if (size === 'sm') {
      this.setScreenPlatform('mobile')
    }
    if (size === 'md' || size === 'lg') {
      this.setScreenPlatform('tablet')
    }
    if (size === 'xl') {
      this.setScreenPlatform('desktop')
    }
  }

  setScreenPlatform(screenPlatform: ScreenPlatform) {
    this.screenPlatform = screenPlatform
  }
}
