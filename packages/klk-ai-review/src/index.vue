<template>
  <div
    class="ai-review-summary"
    :class="{
      'ai-review-summary--mobile': realPlatform === 'mobile'
    }"
  >
    <div>
      <div class="ai-review-summary__title">
        <img class="ai-review-summary__title-icon" :src="icon">
        <span class="ai-review-summary__title-text">{{ title }}</span>
      </div>
      <div class="ai-review-summary__mini-info">
        {{ miniInfo.tips }}
        <klk-poptip
          v-if="realPlatform !== 'mobile'"
          placement="bottom-start"
          trigger="hover"
          dark
          :title="miniInfo.pop_info.title"
          :max-height="400"
        >
          <p slot="content">{{ miniInfo.pop_info.content }}</p>
          <IconInformation class="ai-review-summary__mini-info-show" theme="outline" size="16" />
        </klk-poptip>
        <span v-else @click="showMiniInfo">
          <IconInformation class="ai-review-summary__mini-info-show" theme="outline" size="12" />
        </span>
      </div>
    </div>

    <div class="ai-review-summary__cards">
      <klk-res-swiper :controller-offset="10">
        <klk-res-swiper-item
          v-for="(summary, index) in summaryList"
          :key="index"
          :class="cardColumnClass"
          :data-spm-module="`AIReviewSummary_LIST?idx=${index}&len=${summaryList.length}`"
        >
          <klkAIReviewCard
            :helpful-api="helpfulApi"
            :hide-helpful-action="hideHelpfulAction"
            :platform="realPlatform"
            :summary="summary"
          ></klkAIReviewCard>
        </klk-res-swiper-item>
      </klk-res-swiper>
    </div>

    <klk-bottom-sheet
      v-if=" realPlatform === 'mobile' "
      show-close
      :visible.sync="showBottomSheet"
      :mask-closable="true"
      height="40%"
    >
      <div class="ai-review-summary__mini-info-bottom-sheet-title">{{ miniInfo.pop_info.title }}</div>
      <klk-markdown :content="miniInfo.pop_info.content"></klk-markdown>
    </klk-bottom-sheet>
  </div>
</template>
<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator'
import IconInformation from '@klook/klook-icons/lib/IconInformation'
import * as Poptip from '@klook/klook-ui/lib/poptip'
import * as BottomSheet from '@klook/klook-ui/lib/bottom-sheet'
import * as Markdown from '@klook/klook-ui/lib/markdown'
import { CardSwiper, CardSwiperItem } from './klook-responsive/index'
import { AiReviewSummaryItem, AiReviewMiniInfoData } from './types/index'
import klkAIReviewCard from './components/ai-review-card.vue'

@Component({
  name: 'AiReview',
  components: {
    IconInformation,
    KlkPoptip: Poptip.default,
    KlkBottomSheet: BottomSheet.BottomSheet,
    KlkMarkdown: Markdown.Markdown,
    KlkResSwiper: CardSwiper as any,
    KlkResSwiperItem: CardSwiperItem as any,
    klkAIReviewCard
  }
})
export default class AiReview extends Vue {
  @Prop({ required: true }) title!: string
  @Prop({ required: true }) icon!: string
  @Prop({ required: true }) miniInfo!: AiReviewMiniInfoData
  @Prop({ required: true }) summaryList!: Array<AiReviewSummaryItem>
  @Prop({ required: false }) platform?: string
  @Prop({ required: false, default: false }) hideHelpfulAction!: Boolean
  @Prop({ required: false, default: '/v1/usrcsrv/review/summary/helpful' }) helpfulApi!: string

  get realPlatform() {
    return this.platform || this?.$store?.state?.klook?.platform || 'desktop'
  }

  showBottomSheet = false


  get isSingleCard() {
    return this.summaryList.length === 1
  }

  get cardColumnClass() {
    return this.isSingleCard
      ? 'klk-col-md-1 klk-col-lg-1 klk-col-xl-1 klk-col-sm-1'
      : 'klk-col-md-2 klk-col-lg-2 klk-col-xl-2 klk-col-sm-1-2'
  }

  showMiniInfo() {
    this.showBottomSheet = true
  }
}
</script>
<style lang="scss" scoped>
.ai-review-summary {
  display: flex;
  width: 100%;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  background: $color-bg-1;
  //border: #55ac7d 1px solid;
  .ai-review-summary__title{
    display: flex;
    align-items: center;
  }
  .ai-review-summary__title-icon{
    width: 24px;
    height: 24px;
    margin: 0 6px 0 0;
  }
  .ai-review-summary__title-text{
    text-align: left;
    color: $color-accent-4;
    @include font-heading-xs-v2;
  }
  .ai-review-summary__mini-info{
    text-align: left;
    color: $color-text-secondary;
    @include font-paragraph-s-regular;
    display: flex;
    margin: 4px 2px 8px 0;
  }
  .ai-review-summary__mini-info-show {
    margin: 0 0 0 2px;
    color: $color-text-placeholder;
    vertical-align: sub;
  }
  .ai-review-summary__cards {
    display: flex;
    align-items: center;
    align-self: stretch;
  }
  .ai-review-summary__mini-info-bottom-sheet-title {
    text-align: center;
    color: $color-text-primary;
    @include font-heading-xs-v2;
    padding-bottom: 16px;
  }
}
.ai-review-summary--mobile{
  margin: 0;
  width: 100%;
  .ai-review-summary__title-text{
    @include font-paragraph-m-bold;
  }
  .ai-review-summary__mini-info{
    @include font-paragraph-xs-regular;
    margin: 2px 0 3px 0;
  }
  .ai-review-summary__mini-info-show {
    margin: 0 0 0 4px;
  }
}

.ai-review-summary {
  ::v-deep {
    .responsive-card-swiper-wrap .responsive-card-slider {
      margin-left: -6px !important;
      margin-right: -6px !important;
    }
    @media (max-width: 767px) {
      .responsive-card-swiper-wrap {
        padding: 0;
        margin: 0;
        width: 100%;
      }
      .responsive-card-item {
        padding-left: 4px;
        padding-right: 4px;
        &:last-child {
          padding-right: 0 !important;
        }
      }
      .responsive-card-swiper-wrap .responsive-card-slider {
        margin-left: -4px !important;
        margin-right: -4px !important;
      }
    }
    @media (min-width: 768px) {
      .responsive-card-swiper-wrap .klk-card-swiper-next-btn {
        right: -44px !important;
        width: 32px;
        height: 32px;
      }
      .responsive-card-swiper-wrap .klk-card-swiper-prev-btn {
        left: -44px !important;
        width: 32px;
        height: 32px;
      }
      .responsive-card-item {
        padding-left: 6px;
        padding-right: 6px;
      }
    }
  }
}

</style>
