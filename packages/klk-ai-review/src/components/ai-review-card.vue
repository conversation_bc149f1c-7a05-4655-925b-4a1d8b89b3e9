<template>
  <div class="ai-review-card">
    <div class="ai-review-card__title">{{ summary.title }}</div>
    <div class="ai-review-card__content">{{ summary.content }}</div>
    <div v-if="!hideHelpfulAction" class="ai-review-card__actions">
      <div data-spm-item="Helpful" v-galileo-click-tracker="{ spm: 'AIReviewSummary_LIST.Helpful', componentName: 'ai-review'}">
        <IconThumbsUp
          class="ai-review-card__card-action-helpful"
          :class="{ 'ai-review-card__action--animating': isAnimating && summary.helpful_status === 1 }"
          size="16"
          :theme="summary.helpful_status === 1 ? 'filled' : 'outline'"
          :fill="summary.helpful_status === 1 ? colors.colorIconFilled : colors.colorTextSecondary"
          @click.native="handleFeedback(1)"
        />
      </div>
      <div data-spm-item="Unhelpful" v-galileo-click-tracker="{ spm: 'AIReviewSummary_LIST.Unhelpful', componentName: 'ai-review'}">
        <IconThumbsDown
          class="ai-review-card__card-action-unhelpful"
          :class="{ 'ai-review-card__action--animating': isAnimating && summary.helpful_status === 2 }"
          size="16"
          :theme="summary.helpful_status === 2 ? 'filled' : 'outline'"
          :fill="summary.helpful_status === 2 ? colors.colorIconFilled : colors.colorTextSecondary"
          @click.native="handleFeedback(2)"
        />
      </div>
    </div>
  </div>
</template>
<script lang="ts">
import { Component, Vue, Prop, Watch } from 'vue-property-decorator'
import IconThumbsUp from '@klook/klook-icons/lib/IconThumbsUp'
import IconThumbsDown from '@klook/klook-icons/lib/IconThumbsDown'
import { $colorTextSecondary, $colorAccent4 } from '@klook/klook-ui/lib/utils/design-token-esm'
import { AiReviewSummaryItem } from './../types/index'

@Component({
  name: 'klkAIReviewCard',
  components: {
    IconThumbsUp,
    IconThumbsDown
  }
})
export default class klkAIReviewCard extends Vue {
  @Prop({ required: true }) summary!: AiReviewSummaryItem
  @Prop({ required: false, default: false }) hideHelpfulAction!: Boolean
  @Prop({ required: false, default: '/v1/usrcsrv/review/summary/helpful' }) helpfulApi!: string
  @Prop({ required: false }) platform?: string

  isAnimating = false

  get colors() {
    return {
      colorTextSecondary: $colorTextSecondary,
      colorIconFilled: $colorAccent4
    }
  }

  @Watch('summary.helpful_status')
  onHelpfulStatusChange() {
    this.triggerAnimation()
  }

  triggerAnimation() {
    this.isAnimating = true
    setTimeout(() => {
      this.isAnimating = false
    }, 300) // 动画持续时间
  }

  handleFeedback(newStatus: number) {
    // 状态一致不做处理
    if (this.summary.helpful_status == newStatus) {
      return
    }

    const params = {
      id: this.summary.id,
      is_helpful: newStatus === 1
    }

    if (!window.$axios || !window.$axios.$post) {
      throw new Error('no axios')
    }

    window.$axios.$post(this.helpfulApi, params).then(({ success, result }) => {
      if (!success) {
        throw new Error(result.message)
      }

      // update helpful status
      this.summary.helpful_status = newStatus
    }).catch((error: Error) => {
      console.log('Failed to update feedback:', error)
      // 出错了 反馈给用户 tips
      this.$toast({
        message: error.message
      })
    })
  }
}
</script>
<style lang="scss" scoped>
@mixin text-ellipsis($line-clamp: 2) {
  display: -webkit-box;
  -webkit-line-clamp: $line-clamp;
  -webkit-box-orient: vertical;
  text-overflow: ellipsis;
  overflow: hidden;
}
.ai-review-card {
  display: flex;
  padding: 16px 16px 8px 16px;
  flex-direction: column;
  align-items: flex-start;
  border-radius: $radius-xl;
  background: rgba(236, 243, 252, 0.50);
  width:100%;
  height: 100%;
  flex-direction: column;
  justify-content: space-between;
}
.ai-review-card__title {
  text-align: left;
  color: $color-text-primary;
  @include font-paragraph-s-bold;
  @include text-ellipsis(2);
  height: 42px;
  margin: 0 0 8px 0;
}
.ai-review-card__content {
  text-align: left;
  color: $color-text-primary;
  @include font-paragraph-s-regular;
  @include text-ellipsis(10);
}
.ai-review-card__actions {
  display: flex;
  padding: 0;
  justify-content: flex-end;
  flex-direction: row;
  align-items: flex-end;
  align-self: stretch;
  flex: 1 0 0;
}
.ai-review-card__card-action-helpful,
.ai-review-card__card-action-unhelpful {
  padding: 8px;
  transition: transform 0.5s ease, fill 0.8s ease;
  cursor: pointer;
  &:last-child {
    margin-right: 0;
  }
}

.ai-review-card__action--animating {
  animation: feedbackAnimation 1.5s ease-in-out;
}
@keyframes feedbackAnimation {
  0% {
    fill: $color-neutral-400;
    transform: scale(1);
  }
  1% {
    fill: $color-purple-900;
    transform: scale(1.25);
  }
  40% {
    fill: $color-purple-900;
    transform: scale(0.875);
  }
  100% {
    fill: $color-purple-900;
    transform: scale(1);
  }
}
</style>
