{"name": "@klook/ai-review", "version": "0.0.2", "description": "A Component used for presenting ai review in cardswpider mode.", "author": "<PERSON>", "homepage": "https://design.klook.io", "main": "dist/commonjs/index.js", "module": "dist/esm/index.js", "typings": "dist/esm/index.d.ts", "files": ["dist", "lib", "src", "locales"], "license": "UNLICENSED", "publishConfig": {"registry": "https://knpm.klook.io", "access": "public"}, "scripts": {"build": "klk-builder build", "watch": "klk-builder build -w", "lint": "NODE_ENV=production eslint --ext .js,.vue src", "test": "NODE_ENV=test jest -i --updateSnapshot", "test:coverage": "NODE_ENV=test jest -i --coverage --updateSnapshot", "prepush": "yarn run lint", "prepublishOnly": "bash prepublishOnly.sh", "commit": "npx git-cz", "commitmsg": "commitlint -E GIT_PARAMS"}, "devDependencies": {"@babel/core": "^7.17.9", "@klook/klook-builder": "1.1.1", "@klook/klook-ui": "^1.36.0", "vue-property-decorator": "^9.1.2"}, "peerDependencies": {"@klook/klook-icons": "0.18.0", "@klook/klook-ui": "^1.36.0", "vue": "2.x", "vue-property-decorator": "^8.3.0"}}