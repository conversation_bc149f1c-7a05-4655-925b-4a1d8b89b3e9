import replace from 'rollup-plugin-replace'
import typescript from 'rollup-plugin-typescript2'
import vue from 'rollup-plugin-vue'
import svg from '../build/vue-inline-svg/index';
import postcss from 'rollup-plugin-postcss'
import nodeResolve from 'rollup-plugin-node-resolve'
import clear from 'rollup-plugin-clear'
import json from '@rollup/plugin-json'
import copy from 'rollup-plugin-copy'
import pkg from '../package.json'
import resolveAsyncComponent from '../plugin/rollup-plugin-async-component'

const commonjs = require('rollup-plugin-commonjs')
const babel = require('rollup-plugin-babel')
// import alias from '@rollup/plugin-alias';
// import path from 'path'

// import {
//   terser
// } from 'rollup-plugin-terser'

// const ar = process.argv;
//
// let target = ar.find(v => v.includes('target='))
//
// if (target) {
//   target = target.split('=')[1]
//
//   console.log(target, '-------------- target -------------')
//
//   const targetDir = ['esm', 'lib', 'dist'].map(v => `${target}/${v}`)
//   clear({ targets: targetDir });
// }

const target = '/Users/<USER>/project/platform/node_modules/@klook/klk-traveller-footer'

const targetDir = ['esm', 'lib', 'dist'].map(v => `${target}/${v}`)
clear({ targets: targetDir })

const {
  name,
  version,
  author
} = require('../package.json')

const banner =
  `/**
  * v${version}
  * (c) ${new Date().getFullYear()} ${author}
  */`
const configs = {
  // umd: {
  //   input: { 'index.js': 'src/index.ts' },
  //   dir: 'dist',
  //   output: 'umd',
  //   format: 'umd',
  //   inlineDynamicImports: true,
  //   target: 'es5',
  //   env: 'production'
  // },
  // umdMin: {
  //   dir: 'dist/umd/',
  //   output: 'dist/umd/index.min.js',
  //   inlineDynamicImports: true,
  //   format: 'umd',
  //   target: 'es5',
  //   plugins: {
  //     post: [terser()]
  //   },
  //   env: 'production'
  // },
  esm: {
    input: { 'index.js': 'src/index.ts' },
    dir: `${target}/esm`,
    format: 'esm',
    target: 'es5',
    env: 'production',
    genDts: true
  },
  cjs: {
    input: { 'index.js': 'src/index.ts' },
    dir: `${target}/lib`,
    inlineDynamicImports: false,
    format: 'cjs',
    target: 'es5'
  }
}

const genTsPlugin = configOpts => typescript({
  useTsconfigDeclarationDir: true,
  tsconfigOverride: {
    compilerOptions: {
      target: configOpts.target,
      declaration: configOpts.genDts
    },
    exclude: ['**/__tests__', 'test-dts']
  },
  objectHashIgnoreUnknownHack: true,
  abortOnError: false
})

const genPlugins = (configOpts) => {
  const plugins = []
  if (configOpts.env) {
    plugins.push(replace({
      'process.env.NODE_ENV': JSON.stringify(configOpts.env)
    }))
  }
  plugins.push(nodeResolve({
    extensions: ['.mjs', '.js', '.jsx', '.vue']
  }))
  plugins.push(commonjs({
    include: /node_modules/
  }))
  plugins.push(replace({
    'process.env.MODULE_FORMAT': JSON.stringify(configOpts.format)
  }))
  if (configOpts.plugins && configOpts.plugins.pre) {
    plugins.push(...configOpts.plugins.pre)
  }
  plugins.push(genTsPlugin(configOpts))

  plugins.push(svg({
    svgoConfig: {
      plugins: [
        { removeXMLNS: true },
        { removeViewBox: false },
        { removeDimensions: true },
        {
          removeAttrs: {
            elemSeparator: '#',
            attrs: 'svg#xmlns:xlink'
          }
        }
      ]
    }
  }))

  plugins.push(vue({
    css: false,
    normalizer: '~vue-runtime-helpers/dist/normalize-component.js',
    template: {
      isProduction: true
    },
    style: {
      postcssPlugins: [
        require('autoprefixer')()
      ],
      preprocessStyles: true,
      preprocessOptions: {
        scss: {
          data: '@import "./node_modules/@klook/klook-ui/src/styles/token/index.scss";@import "./src/style/_base.scss";'
        }
      }
    }
  }))

  plugins.push(babel({
    include: ['src/**', 'node_modules/**'],
    extensions: ['.js', '.vue'],
    runtimeHelpers: true
  }))

  // 不必提取css
  plugins.push(postcss({
    extract: true,
    plugins: [
      require('autoprefixer')()
    ]
  }))

  if (configOpts.plugins && configOpts.plugins.post) {
    plugins.push(...configOpts.plugins.post)
  }

  plugins.push(json())

  plugins.push(resolveAsyncComponent())

  plugins.push(copy({
    targets: [
      // { src, dest },
      { src: 'types/index.d.ts', dest: configOpts.dir }
      // { src: 'build/index.js', dest }
    ]
  }))

  return plugins
}

const genConfig = configOpts => ({
  input: configOpts.input,
  inlineDynamicImports: configOpts.inlineDynamicImports,
  output: {
    banner,
    dir: configOpts.dir,
    // file: configOpts.output,
    format: configOpts.format,
    chunkFileNames: 'chunks/[name]-[hash].js',
    entryFileNames: '[name]',
    name,
    sourcemap: false,
    exports: 'named',
    esModule: true,
    globals: {
      vue: 'Vue'
    }
  },
  external(id) {
    return Object.keys(pkg.peerDependencies).includes(id.split('/')[0]) || id.includes('@klook/')
  },
  plugins: genPlugins(configOpts)
})

const genAllConfigs = configs => (Object.keys(configs).map(key => genConfig(configs[key])))

export default genAllConfigs(configs)
