#!/bin/sh
unset npm_config_prefix
# abort on errors
set -e

# use nvm
source ~/.nvm/nvm.sh
echo "当前 nvm 版本"
nvm --version

echo "当前 node 版本"
node -v
echo "当前 npm 版本"
npm -v

NODE_VERSION="12"
nvm use $NODE_VERSION || nvm install $NODE_VERSION

echo "切换到 node 版本"
node -v
echo "切换到 npm 版本"
npm -v

rm -rf ./node_modules/

npm i pnpm@6 -g

echo "pnpm 版本"
pnpm -v

rm -rf ./node_modules/

echo "pnpm 安装依赖"
pnpm install
echo "pnpm 构建依赖"
pnpm build
