export namespace Types {
  export type Platform = 'mobile' | 'desktop'

  export interface ILink {
    link: string,
    content: string,
    attr?: {
      target: string
    },
    on?: {
      [prop: string]: Function
    },
    isCrawable?: boolean
  }

  export type InternalLinks = {
    title: string,
    links: ILink[]
  }[]

  export interface Website {
    title: string
    value: string
    site: string
    languagesKey: string
    language: string
  }
}

declare global {
  namespace Data {
    type Language =
      | 'de'
      | 'en'
      | 'en-AU'
      | 'en-CA'
      | 'en-GB'
      | 'en-HK'
      | 'en-IN'
      | 'en-MY'
      | 'en-NZ'
      | 'en-PH'
      | 'en-SG'
      | 'ms-MY'
      | 'en-US'
      | 'es'
      | 'fr'
      | 'id'
      | 'it'
      | 'ja'
      | 'ko'
      | 'ru'
      | 'th'
      | 'vi'
      | 'zh-CN'
      | 'zh-HK'
      | 'zh-TW'
    interface IUser {
      globalId: string
      avatar: string
      unreview: number
    }
  }
}
