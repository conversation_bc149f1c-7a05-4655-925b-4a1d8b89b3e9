import { Vue, Component, Inject } from 'vue-property-decorator'

@Component
export default class Base extends Vue {
  @Inject() __t!: Function
  @Inject() __href!: Function
  @Inject() language!: Data.Language
  @Inject() getUser!: () => Data.IUser
  
  _axios!: any
  get user() {
    return this.getUser()
  }

  get isLoggedIn() {
    return !!this.user;
  }
  beforeMount() {
    this._axios = this.$attrs.axios || window.$axios
  }
}
