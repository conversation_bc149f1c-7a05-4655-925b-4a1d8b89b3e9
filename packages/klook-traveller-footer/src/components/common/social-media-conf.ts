// 社交媒体图表和链接的配置
// 按照语言来分类
const socialMediaIconSrcMap = {
  Meta: "https://res.klook.com/image/upload/v1744785757/ued/Other/social%20media/Meta.png",
  Youtube:
    "https://res.klook.com/image/upload/v1747821955/ued/Other/social%20media/Youtube1.png",
  Instagram:
    "https://res.klook.com/image/upload/v1744785757/ued/Other/social%20media/Instagram.png",
  X: "https://res.klook.com/image/upload/v1744785757/ued/Other/social%20media/X.png",
  TikTok:
    "https://res.klook.com/image/upload/v1744785757/ued/Other/social%20media/TikTok.png",
  Threads:
    "https://res.klook.com/image/upload/v1744785757/ued/Other/social%20media/Threads.png",
  Telegram:
    "https://res.klook.com/image/upload/v1744785757/ued/Other/social%20media/Telegram.png",
  XiaoHongShu:
    "https://res.klook.com/image/upload/v1745389990/ued/Other/social%20media/xiaoshongshu.png",
  NaverPost:
    "https://res.klook.com/image/upload/v1744785757/ued/Other/social%20media/Naver_Post.png",
  NaverBlog:
    "https://res.klook.com/image/upload/v1744785757/ued/Other/social%20media/Naver_Blog.png",
  Line: "https://res.klook.com/image/upload/v1744785757/ued/Other/social%20media/Line.png",
};

// Meta accounts
const metaAccounts = {
  global: {
    icon: "Meta",
    icon_alt: "Meta",
    icon_src: socialMediaIconSrcMap.Meta,
    link: "https://www.facebook.com/klookglobal/",
  },
  anz: {
    icon: "Meta",
    icon_alt: "Meta",
    icon_src: socialMediaIconSrcMap.Meta,
    link: "https://www.facebook.com/klookanz/",
  },
  us: {
    icon: "Meta",
    icon_alt: "Meta",
    icon_src: socialMediaIconSrcMap.Meta,
    link: "https://www.facebook.com/klookUS/",
  },
  zhHk: {
    icon: "Meta",
    icon_alt: "Meta",
    icon_src: socialMediaIconSrcMap.Meta,
    link: "https://www.facebook.com/klookhk/",
  },
  india: {
    icon: "Meta",
    icon_alt: "Meta",
    icon_src: socialMediaIconSrcMap.Meta,
    link: "https://www.facebook.com/klookindia/",
  },
  enMy: {
    icon: "Meta",
    icon_alt: "Meta",
    icon_src: socialMediaIconSrcMap.Meta,
    link: "https://www.facebook.com/klookmy/",
  },
  id: {
    icon: "Meta",
    icon_alt: "Meta",
    icon_src: socialMediaIconSrcMap.Meta,
    link: "https://www.facebook.com/klookid/",
  },
  th: {
    icon: "Meta",
    icon_alt: "Meta",
    icon_src: socialMediaIconSrcMap.Meta,
    link: "https://www.facebook.com/klookth/",
  },
  ph: {
    icon: "Meta",
    icon_alt: "Meta",
    icon_src: socialMediaIconSrcMap.Meta,
    link: "https://www.facebook.com/klookph/",
  },
  sg: {
    icon: "Meta",
    icon_alt: "Meta",
    icon_src: socialMediaIconSrcMap.Meta,
    link: "https://www.facebook.com/klooksg/",
  },
  vi: {
    icon: "Meta",
    icon_alt: "Meta",
    icon_src: socialMediaIconSrcMap.Meta,
    link: "https://www.facebook.com/klookvn/",
  },
  tw: {
    icon: "Meta",
    icon_alt: "Meta",
    icon_src: socialMediaIconSrcMap.Meta,
    link: "https://www.facebook.com/klooktw/",
  },
};

// YouTube accounts
const youtubeAccounts = {
  global: {
    icon: "Youtube",
    icon_alt: "Youtube",
    icon_src: socialMediaIconSrcMap.Youtube,
    link: "https://www.youtube.com/@Klook_Travel/",
  },
  zhHk: {
    icon: "Youtube",
    icon_alt: "Youtube",
    icon_src: socialMediaIconSrcMap.Youtube,
    link: "https://www.youtube.com/@klookhk8644/",
  },
  india: {
    icon: "Youtube",
    icon_alt: "Youtube",
    icon_src: socialMediaIconSrcMap.Youtube,
    link: "https://www.youtube.com/@klookin/",
  },
  id: {
    icon: "Youtube",
    icon_alt: "Youtube",
    icon_src: socialMediaIconSrcMap.Youtube,
    link: "https://www.youtube.com/channel/UCA6g-ByMaAwf4MrpGGxKgvQ",
  },
  ko: {
    icon: "Youtube",
    icon_alt: "Youtube",
    icon_src: socialMediaIconSrcMap.Youtube,
    link: "https://www.youtube.com/@klook_kr",
  },
  ja: {
    icon: "Youtube",
    icon_alt: "Youtube",
    icon_src: socialMediaIconSrcMap.Youtube,
    link: "https://www.youtube.com/@klookjapan",
  },
};

// Instagram accounts
const instagramAccounts = {
  global: {
    icon: "Instagram",
    icon_alt: "Instagram",
    icon_src: socialMediaIconSrcMap.Instagram,
    link: "https://www.instagram.com/klooktravel/",
  },
  anz: {
    icon: "Instagram",
    icon_alt: "Instagram",
    icon_src: socialMediaIconSrcMap.Instagram,
    link: "https://www.instagram.com/klookanz/",
  },
  us: {
    icon: "Instagram",
    icon_alt: "Instagram",
    icon_src: socialMediaIconSrcMap.Instagram,
    link: "https://www.instagram.com/klook_usa/",
  },
  zhHk: {
    icon: "Instagram",
    icon_alt: "Instagram",
    icon_src: socialMediaIconSrcMap.Instagram,
    link: "https://www.instagram.com/klookhk/",
  },
  india: {
    icon: "Instagram",
    icon_alt: "Instagram",
    icon_src: socialMediaIconSrcMap.Instagram,
    link: "https://www.instagram.com/klookindia/",
  },
  enMy: {
    icon: "Instagram",
    icon_alt: "Instagram",
    icon_src: socialMediaIconSrcMap.Instagram,
    link: "https://www.instagram.com/klookmy/",
  },
  id: {
    icon: "Instagram",
    icon_alt: "Instagram",
    icon_src: socialMediaIconSrcMap.Instagram,
    link: "https://www.instagram.com/klook.id/",
  },
  th: {
    icon: "Instagram",
    icon_alt: "Instagram",
    icon_src: socialMediaIconSrcMap.Instagram,
    link: "https://www.instagram.com/klooktravel_th/",
  },
  ph: {
    icon: "Instagram",
    icon_alt: "Instagram",
    icon_src: socialMediaIconSrcMap.Instagram,
    link: "https://www.instagram.com/klook_ph/",
  },
  sg: {
    icon: "Instagram",
    icon_alt: "Instagram",
    icon_src: socialMediaIconSrcMap.Instagram,
    link: "https://www.instagram.com/klooksg/",
  },
  ko: {
    icon: "Instagram",
    icon_alt: "Instagram",
    icon_src: socialMediaIconSrcMap.Instagram,
    link: "https://www.instagram.com/klook.kr/",
  },
  ja: {
    icon: "Instagram",
    icon_alt: "Instagram",
    icon_src: socialMediaIconSrcMap.Instagram,
    link: "https://www.instagram.com/klookjp/",
  },
  vi: {
    icon: "Instagram",
    icon_alt: "Instagram",
    icon_src: socialMediaIconSrcMap.Instagram,
    link: "https://www.instagram.com/klook_vn/",
  },
  tw: {
    icon: "Instagram",
    icon_alt: "Instagram",
    icon_src: socialMediaIconSrcMap.Instagram,
    link: "https://www.instagram.com/klooktw/",
  },
};

// TikTok accounts
const tiktokAccounts = {
  global: {
    icon: "TikTok",
    icon_alt: "TikTok",
    icon_src: socialMediaIconSrcMap.TikTok,
    link: "https://www.tiktok.com/@klooktravel",
  },
  anz: {
    icon: "TikTok",
    icon_alt: "TikTok",
    icon_src: socialMediaIconSrcMap.TikTok,
    link: "https://www.tiktok.com/@klook_anz",
  },
  us: {
    icon: "TikTok",
    icon_alt: "TikTok",
    icon_src: socialMediaIconSrcMap.TikTok,
    link: "https://www.tiktok.com/@klook_usa",
  },
  id: {
    icon: "TikTok",
    icon_alt: "TikTok",
    icon_src: socialMediaIconSrcMap.TikTok,
    link: "https://www.tiktok.com/@klook.id",
  },
  th: {
    icon: "TikTok",
    icon_alt: "TikTok",
    icon_src: socialMediaIconSrcMap.TikTok,
    link: "https://www.tiktok.com/@klookth",
  },
  ph: {
    icon: "TikTok",
    icon_alt: "TikTok",
    icon_src: socialMediaIconSrcMap.TikTok,
    link: "https://www.tiktok.com/@klook_ph",
  },
  sg: {
    icon: "TikTok",
    icon_alt: "TikTok",
    icon_src: socialMediaIconSrcMap.TikTok,
    link: "https://www.tiktok.com/@klooksg",
  },
  vi: {
    icon: "TikTok",
    icon_alt: "TikTok",
    icon_src: socialMediaIconSrcMap.TikTok,
    link: "https://www.tiktok.com/@klook_vietnam",
  },
  enMy: {
    icon: "TikTok",
    icon_alt: "TikTok",
    icon_src: socialMediaIconSrcMap.TikTok,
    link: "https://www.tiktok.com/@klook.my",
  },
};

const xAccounts = {
  global: {
    icon: "X",
    icon_alt: "X",
    icon_src: socialMediaIconSrcMap.X,
    link: "https://x.com/klooktravel",
  },
  ja: {
    icon: "X",
    icon_alt: "X",
    icon_src: socialMediaIconSrcMap.X,
    link: "https://x.com/klookjp",
  },
};

// Threads accounts
const threadsAccounts = {
  zhHk: {
    icon: "Threads",
    icon_alt: "Threads",
    icon_src: socialMediaIconSrcMap.Threads,
    link: "https://www.threads.net/@klookhk",
  },
  vi: {
    icon: "Threads",
    icon_alt: "Threads",
    icon_src: socialMediaIconSrcMap.Threads,
    link: "https://www.threads.net/@klook_vn",
  },
  tw: {
    icon: "Threads",
    icon_alt: "Threads",
    icon_src: socialMediaIconSrcMap.Threads,
    link: "https://www.threads.net/@klooktw",
  },
};

// Naver accounts (Korea-specific)
const naverAccounts = {
  post: {
    icon: "NaverPost",
    icon_alt: "Naver Post",
    icon_src: socialMediaIconSrcMap.NaverPost,
    link: "http://post.naver.com/klooktravel",
  },
  blog: {
    icon: "NaverBlog",
    icon_alt: "Naver Blog",
    icon_src: socialMediaIconSrcMap.NaverBlog,
    link: "http://blog.naver.com/klooktravel",
  },
};

// Line accounts (Thailand-specific)
const lineAccounts = {
  th: {
    icon: "Line",
    icon_alt: "LINE",
    icon_src: socialMediaIconSrcMap.Line,
    link: "https://lin.ee/Gkyt2KM",
  },
};

const telegramAccounts = {
  enMy: {
    icon: "Telegram",
    icon_alt: "Telegram",
    icon_src: socialMediaIconSrcMap["Telegram"],
    link: "https://t.me/klookmy",
  },
  sg: {
    icon: "Telegram",
    icon_alt: "Telegram",
    icon_src: socialMediaIconSrcMap["Telegram"],
    link: "https://t.me/klooktravelsg",
  },
};

const xiaohongshuAccounts = {
  cnMy: {
    icon: "XiaoHongShu",
    icon_alt: "Xiao Hong Shu",
    icon_src: socialMediaIconSrcMap.XiaoHongShu,
    link: "https://www.xiaohongshu.com/user/profile/62288db30000000010009ae7",
  },
};

const generalSocialMediaAccounts = [
  metaAccounts.global,
  youtubeAccounts.global,
  instagramAccounts.global,
  xAccounts.global,
  tiktokAccounts.global,
];

export const globalSocialMediaConfig = {
  de: generalSocialMediaAccounts,
  "en-AU": [
    metaAccounts.anz,
    youtubeAccounts.global,
    instagramAccounts.anz,
    xAccounts.global,
    tiktokAccounts.anz,
  ],
  "en-CA": [
    metaAccounts.us,
    youtubeAccounts.global,
    instagramAccounts.us,
    xAccounts.global,
    tiktokAccounts.us,
  ],
  "en-GB": generalSocialMediaAccounts,
  "en-HK": [
    metaAccounts.zhHk,
    youtubeAccounts.zhHk,
    instagramAccounts.zhHk,
    threadsAccounts.zhHk,
  ],
  "en-IN": [
    metaAccounts.india,
    youtubeAccounts.india,
    instagramAccounts.india,
    xAccounts.global,
    tiktokAccounts.global,
  ],
  "en-MY": [
    metaAccounts.enMy,
    youtubeAccounts.global,
    instagramAccounts.enMy,
    xAccounts.global,
    tiktokAccounts.enMy,
    telegramAccounts.enMy,
    xiaohongshuAccounts.cnMy,
  ],
  "en-NZ": [
    metaAccounts.anz,
    youtubeAccounts.global,
    instagramAccounts.anz,
    xAccounts.global,
    tiktokAccounts.anz,
  ],
  "en-PH": [
    metaAccounts.ph,
    youtubeAccounts.global,
    instagramAccounts.ph,
    xAccounts.global,
    tiktokAccounts.ph,
  ],
  "en-SG": [
    metaAccounts.sg,
    youtubeAccounts.global,
    instagramAccounts.sg,
    xAccounts.global,
    tiktokAccounts.sg,
    telegramAccounts.sg,
  ],
  "en-US": [
    metaAccounts.us,
    youtubeAccounts.global,
    instagramAccounts.us,
    xAccounts.global,
    tiktokAccounts.us,
  ],
  en: generalSocialMediaAccounts,
  es: generalSocialMediaAccounts,
  fr: generalSocialMediaAccounts,
  id: [
    metaAccounts.id,
    youtubeAccounts.id,
    instagramAccounts.id,
    xAccounts.global,
    tiktokAccounts.id,
  ],
  it: generalSocialMediaAccounts,
  ja: [youtubeAccounts.ja, instagramAccounts.ja, xAccounts.ja],
  ko: [
    naverAccounts.blog,
    youtubeAccounts.ko,
    instagramAccounts.ko,
  ],
  "ms-MY": [
    metaAccounts.enMy,
    youtubeAccounts.global,
    instagramAccounts.enMy,
    xAccounts.global,
    tiktokAccounts.enMy,
    telegramAccounts.enMy,
  ],
  ru: generalSocialMediaAccounts,
  th: [
    metaAccounts.th,
    instagramAccounts.th,
    tiktokAccounts.th,
    lineAccounts.th,
  ],
  vi: [
    metaAccounts.vi,
    instagramAccounts.vi,
    tiktokAccounts.vi,
    threadsAccounts.vi,
  ],
  "zh-CN": [
    metaAccounts.enMy,
    youtubeAccounts.global,
    instagramAccounts.enMy,
    xAccounts.global,
    tiktokAccounts.enMy,
    telegramAccounts.enMy,
    xiaohongshuAccounts.cnMy,
  ],
  "zh-HK": [
    metaAccounts.zhHk,
    youtubeAccounts.zhHk,
    instagramAccounts.zhHk,
    threadsAccounts.zhHk,
  ],
  "zh-TW": [metaAccounts.tw, instagramAccounts.tw, threadsAccounts.tw],
};
