<template>
  <span>
    <span
      class="footer-list-item-inner"
      v-if="!link.isCrawable"
      v-bind="link.attr || {}"
      @click="$emit('redirectUrl', {
          url: link.link,
          target: link.attr && link.attr.target
        })
      "
      v-on="link.on || {}"
    >
      {{ link.content }}
    </span>
    <a
      v-else
      class="footer-list-item-inner"
      v-bind="link.attr || {}"
      :href="link.link"
      v-on="link.on || {}"
    >
      {{ link.content }}
    </a>
  </span>
</template>

<script>
export default {
  props: {
    link: Object
  }
};
</script>

<style></style>
