import { Component, Prop } from 'vue-property-decorator'
import Base from './base'
import { redirectUrl, RedirectOption } from './util'
import payMethodIconMap from '../common/pay-method-icon-map.json'
import currencyPayMethodMap from '../common/currency-pay-method-map.json'
@Component
export default class PlatformBase extends Base {
  @Prop({ default: 'USD' }) currency!: string
  @Prop() isPoweredVisible!: boolean
  @Prop() isBusinessLicenseVisible!: boolean
  @Prop({ default: true }) isSocialCountVisible!: boolean
  @Prop({ default: true }) isAskBtnVisible!: boolean
  @Prop({ default: true }) isLinksVisible!: boolean
  @Prop({ default: true }) isDesktopEntryVisible!: boolean
  @Prop({ default: true }) isOnlySocialVisible!: boolean
  @Prop({ default: true }) isCountryVariable!: boolean
  @Prop({ default: true }) isAboutUsLinkVisible!: boolean
  @Prop({ default: true }) isCareerLinkVisible!: boolean
  @Prop({ default: false }) isCnSite!: boolean
  @Prop() paymentMethods: string[]

  get isMainland() {
    return this.language.includes('zh-CN') && this.currency.includes('CNY')
  }

  // 获取到支付logo的URL
  get payIcons() {
    // 优先根据币种映射
    if (this.currency) {
      let payMethods = (currencyPayMethodMap as any)[this.isCnSite ? 'cn' : 'global']?.[this.currency] || []
      // 如果有传入 paymentMethods，则进一步过滤
      if (this.paymentMethods && this.paymentMethods.length) {
        payMethods = payMethods.filter((payMethod: string) => this.paymentMethods.includes(payMethod))
      }
      const icons = payMethods
        .map((method: string) => payMethodIconMap[method as keyof typeof payMethodIconMap]?.default)
        .filter(Boolean)
      return icons || []
    }
    return []
  }

  redirectUrl(option: RedirectOption) {
    redirectUrl({ url: option.url, target: option.target })
  }
}
