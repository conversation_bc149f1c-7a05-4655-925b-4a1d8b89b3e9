<template>
  <div class="social-media-wrapper">
    <a
      v-for="(item, index) in globalSocialMediaConfig[language]"
      :key="index"
      :title="item.icon_alt"
      :aria-label="item.icon_alt"
      :href="item.link"
      :style="{
        backgroundImage: 'url(' + item.icon_src + ')',
      }"
      target="_blank"
      data-spm-module="FooterSociaMediaLink"
      data-spm-virtual-item="__virtual"
      v-galileo-click-tracker="socialMediaTrackerInfo"
      ><span></span
    ></a>
  </div>
</template>

<script>
import { globalSocialMediaConfig } from "./social-media-conf.ts";
import { GalileoVuePlugin } from '@klook/galileo-vue'
import Vue from 'vue'

Vue.use(GalileoVuePlugin)
const UniversalClickEventData = 'com.klook.galileo.UniversalClick:1.0.0'

export default {
  name: "SocialMedia",
  props: {
    language: {
      type: String,
      default: "en",
    },
  },
  data() {
    return {
      globalSocialMediaConfig,
    };
  },
  methods: {
    socialMediaTrackerInfo() {
      return {
        spm: 'FooterSociaMediaLink',
        binding: true,
        schema: {
          version: UniversalClickEventData
        }
      }
    },
  }
};
</script>
<style scoped>
.social-media-wrapper a {
  margin-right: 20px;
  display: inline-block;
  width: 20px;
  height: 20px;
  background-size: 20px 20px;
}
.social-media-wrapper a:last-child {
  margin-right: 0;
}
</style>
