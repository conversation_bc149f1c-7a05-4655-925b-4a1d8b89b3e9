import jumpTo from '@klook/klk-traveller-utils/lib/jumpTo'

export interface RedirectOption {
  url: string,
  target?: string
}

export const CurencyTextIdMap: any = {
  AED: '42',
  AUD: '34',
  CAD: '2497',
  CHF: '1889',
  CNY: '33',
  DKK: '1885',
  EGP: '3576',
  EUR: '37',
  FJD: '2496',
  GBP: '35',
  HKD: '29',
  IDR: '46',
  INR: '1891',
  ISK: '1884',
  JOD: '2041',
  JPY: '41',
  KHR: '2037',
  KRW: '44',
  LAK: '36',
  LBP: '2042',
  MAD: '3575',
  MGA: '3578',
  MMK: '2038',
  MOP: '3216',
  MUR: '2498',
  MXN: '2499',
  MYR: '40',
  NOK: '1887',
  NZD: '39',
  OMR: '2040',
  PHP: '43',
  QAR: '12476',
  RUB: '1886',
  SEK: '1888',
  SGD: '32',
  THB: '38',
  TRY: '12477',
  TWD: '31',
  USD: '30',
  VND: '45',
  ZAR: '3577',
  ILS: '74681'
}
export function getCurrencyTextId(currency: any) {

  return CurencyTextIdMap[currency]
}


export function digitalHubConfig(language: any): any {
  const digitalHubConfig = {
    ko: {
      text: '디지털 솔루션',
      href: '/article/14934-digital-solutions'
    },
    ja: {
      text: 'デジタルソリューション',
      href: '/article/14933-digital-solutions'
    },
    'zh-HK': {
      text: '數碼解決方案',
      href: '/article/14932-digital-solutions'
    },
    'zh-TW': {
      text: '數位解決方案',
      href: '/article/14931-digital-solutions'
    },
    'zh-CN': {
      text: '数字化解决方案',
      href: '/article/14930-digital-solutions'
    }
  } as any
  // return digitalHubConfig[language]
  return null
}

// 语言和博客链接映射表，用于页脚的 klook blog
type LanguageBlogMap = Partial<Record<Data.Language, string>>
export const languageBlogMap: LanguageBlogMap = {
  en: '/blog/',
  de: '/de/blog/',
  it: '/it/blog/',
  fr: '/fr/blog/',
  ru: '/ru/blog/',
  es: '/es/blog/',
  'en-AU': '/en-AU/blog/',
  'en-GB': '/en-GB/blog/',
  'en-IN': '/en-IN/blog/',
  'en-MY': '/en-MY/blog/',
  'en-NZ': '/blog/',
  'en-PH': '/en-PH/blog/',
  'en-SG': '/en-SG/blog/',
  'en-US': '/en-US/blog/',
  'ms-MY': '/ms-MY/blog/',
  ko: 'https://blog.naver.com/klooktravel',
  th: '/th/blog/',
  vi: '/vi/blog/',
  id: '/id/blog/',
  ja: '/ja/blog/',
  'zh-CN': '/zh-CN/blog/',
  'zh-HK': '/zh-HK/blog/',
  'zh-TW': '/zh-TW/blog/'
}

export function getBlogUrlByLanguage(language: Data.Language) {
  return languageBlogMap[language] || (languageBlogMap.en as string)
}

export function redirectUrl(option: RedirectOption) {
  jumpTo(option.url, option.target || '_self')
}
export function getSustainabilityEntrance(lang: string) {
  return [
    'en',
    'en-AU',
    'en-CA',
    'en-GB',
    'en-HK',
    'en-IN',
    'en-MY',
    'en-NZ',
    'en-PH',
    'en-SG',
    'en-US',
    'zh-HK',
    'zh-TW',
    'zh-CN',
    'ja',
    'vi',
    'vn',
    'ko',
    'id',
    'my',
    'th'
  ].includes(lang)
}
