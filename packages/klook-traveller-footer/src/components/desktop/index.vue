<template>
  <div v-if="isMiniStyle" class="w-default-footer w-default-footer_mini">
    <div class="w-default-footer_divider"></div>
    <Copyright
      :is-cn-site="isCnSite"
      :full-year="fullYear"
      :show-mobile-entry="showMobileEntry"
      :is-business-license-visible="isBusinessLicenseVisible"
      @handleMobileEntryClick="handleMobileEntryClick"
      @redirectUrl="redirectUrl"
    />
  </div>
  <div v-else class="w-default-footer">
    <div class="w-default-footer_divider"></div>

    <div v-if="isPoweredVisible" class="powered_by_klook">
      <img v-lazy="poweredByKlookLink" alt="powered by klook">
    </div>

    <div class="w-default-footer_bottom">
      <div class="w-default-footer_inner">
        <FooterInternalLinks
          :is-social-count-visible="isSocialCountVisible"
          :is-about-us-link-visible="isAboutUsLinkVisible"
          :is-partner-link-visible="isPartnerLinkVisible"
          :is-career-link-visible="isCareerLinkVisible"
          :email="email"
          :links="links"
          :wrap-length="wrapLength"
          :market="market"
        ></FooterInternalLinks>
        <div class="w-default-footer_other">
          <div v-if="payIcons && payIcons.length" class="w-default-footer_payment">
            <span>{{ __t('1598') }}</span>
            <ul>
              <li
                v-for="icon in payIcons"
                :key="icon"
                :style="{ backgroundImage: `url(${formatPicUrl(icon, 80, 48)})` }"
              ></li>
            </ul>
          </div>
        </div>
      </div>
    </div>
    <div class="w-default-footer_divider"></div>
    <Copyright
      :is-cn-site="isCnSite"
      :full-year="fullYear"
      :show-mobile-entry="showMobileEntry"
      :is-business-license-visible="isBusinessLicenseVisible"
      @handleMobileEntryClick="handleMobileEntryClick"
      @redirectUrl="redirectUrl"
    />
  </div>
</template>

<script lang="ts">
import { Component, Prop } from 'vue-property-decorator'
import transformImageUrl from '@klook/klk-traveller-utils/lib/transformImageUrl'
import PlatformBase from '../common/platform-base'
import getCookieConfig from '@klook/klk-traveller-utils/lib/getCookieConfig'
import { Types } from '../../../types/types'
import FooterInternalLinks from './internal-link.vue'
import Copyright from './copy-right.vue'

@Component({
  inheritAttrs: false,
  components: {
    FooterInternalLinks,
    Copyright
  }
})
export default class KlkTravellerDesktopFooter extends PlatformBase {
  @Prop() links!: Types.InternalLinks | Function
  @Prop() email!: string
  @Prop({ default: true }) isPartnerLinkVisible!: boolean
  @Prop({ default: true }) isMiniStyle!: boolean
  @Prop({ default: 'normal' }) cookieType!: 'normal' | 'europe'
  @Prop({ default: 100 }) wrapLength!: number
  @Prop({ default: "global" }) market!: string


  showMobileEntry = false
  // visible = false

  get fullYear() {
    return new Date().getFullYear()
  }

  get poweredByKlookLink() {
    return 'https://res.klook.com/image/upload/hybrid/klook_poweredby_logo_horizontal.svg'
  }

  get realWebp() {
    // 若有webp配置则返回，否则为0
    return (this as any).$store?.state?.klook?.webp || 0
  }

  async mounted() {
    this.showMobileEntry = await window.Cookies && window.Cookies.get(getCookieConfig('platform').key) === 'desktop'
  }

  formatPicUrl(url: string, width: number, height: number) {
    return transformImageUrl(url, {
      width: width * 2,
      height: height * 2,
      webp: this.realWebp,
      fill: 'fit',
      quantity: 100
    })
  }

  handleMobileEntryClick(e: Event) {
    this.redirectUrl({
      url: this.__href('/'),
    })
    e.preventDefault()
    window.Cookies.remove(getCookieConfig('platform').key, { path: '/' })
    window.location.reload()
  }

  // changeWebsite() {
  //   this.visible = true
  // }
}
</script>

<style lang="scss">
$prefix: '.w-default-footer';
$content-width: 1160px;

@mixin retina-sprite($url, $url2, $width, $height) {
  background-image: url(#{$url});
  width: $width;
  height: $height;

  @media (-webkit-min-device-pixel-ratio: 2),
  (min-resolution: 192dpi) {
    background-image: url(#{$url2});
    background-size: $width $height;
  }
}

#{$prefix} {
  min-width: 1280px;
  background-color: $color-bg-widget-normal;
  font-size: $fontSize-body-s;
  color: $color-text-secondary;

  .powered_by_klook {
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: $color-bg-widget-normal;
    color: $color-text-secondary;
    padding: 8px 0;

    img {
      height: 24px;
    }
  }
}

#{$prefix}_top {
}

#{$prefix}_bottom {
  padding: 32px 0 32px;
}

#{$prefix}_beian {
  padding: 24px 0;
  text-align: left;
  @include font-caption-m-regular();

  em {
    font-style: normal;
  }

  p {
    @include font-caption-m-regular();
    margin: 0;
    margin-bottom: 4px;
    max-width: 84%;
    &:last-child {
      margin-bottom: 0px;
    }
  }

  a {
    color: $color-text-secondary;
  }

  a, span {
    margin-right: 24px;
  }

  .w-default-footer_beian_grade-filing_link {
    display: inline-block;
    text-decoration: none;
  }

  .w-default-footer_beian_grade-filing_icon {
    float: left;
    vertical-align: text-top;
  }

  .w-default-footer_beian_grade-filing_text {
    float: left;
    margin: 0;
    margin-left: 4px;
    vertical-align: middle;
  }
}

#{$prefix}_inner {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  width: $content-width;
  margin-left: auto;
  margin-right: auto;
}

#{$prefix}_beian_inner {
  width: $content-width;
  margin-left: auto;
  margin-right: auto;
  .footer-link-item {
    cursor: pointer;
    &:hover {
      text-decoration: none;
    }
  }
}

#{$prefix}_divider {
  background: $color-common-black;

  /* stylelint-disable-next-line */
  opacity: 0.07;
  height: 1px;
}

#{$prefix}_copyright {
  @include font-caption-m-regular();

  width: 220px;
  margin-bottom: 30px;
  color: $color-text-secondary;
  white-space: pre-wrap;

  a {
    @include font-caption-m-regular();

    color: $color-text-secondary;
  }
}

#{$prefix}_mini {
  #{$prefix}_copyright {
    @include font-body-s-regular();

    width: $content-width;
    margin: 24px auto;
    white-space: normal;
  }
}

#{$prefix}_business-license {
  display: block;
  margin-top: 12px;
}

#{$prefix}_change-website {
  display: block;
  margin-top: 12px;
  cursor: pointer;
}

#{$prefix}_dathongbao {
  margin-top: 12px;
  margin-bottom: 24px;
}
#{$prefix}_mobile-entry {
  display: block;
  width: 158px;
  height: 40px;
  line-height: 40px;
  border: 1px solid $color-white;
  border-radius: $radius-m;
  text-align: center;
}

#{$prefix}_other {
  width: 273px;
  flex: none;
  margin-left: 60px;
}

#{$prefix}_ask {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 42px;
  margin-bottom: 30px;
  border: 1px solid $color-border-active;
  color: $color-text-secondary;
  font-size: $fontSize-body-m;

  &:hover {
    border-color: $color-white;
    color: $color-text-primary-onDark;
  }
}

#{$prefix}_payment {
  span {
    @include font-body-s-semibold();

    color: $color-text-primary;
  }

  ul {
    display: flex;
    flex-wrap: wrap;
    margin-top: 12px;
  }

  li {
    margin-right: 12px;
    margin-bottom: 12px;
    border: 1px solid $color-border-dim;
    border-radius: $radius-s;
    width: 40px;
    height: 25px;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
  }
}

#{$prefix}_ko {
  width: $content-width;
  padding-top: 20px;
  padding-bottom: 30px;
  margin-left: auto;
  margin-right: auto;
  line-height: 1.8;
  font-size: $fontSize-caption-m;
  text-align: center;

  a {
    color: $color-text-secondary;
    text-decoration: underline;
  }
}

#{$prefix}_hotline {
  font-size: $fontSize-body-s;
  color: $color-text-secondary;
  margin-top: 20px;

  div:nth-child(1) {
    font-weight: $fontWeight-semibold;
    line-height: 17px;
    margin-bottom: 4px;
  }

  div:nth-child(2) {
    line-height: 17px;
    margin-bottom: 4px;
  }

  div:nth-child(3) {
    line-height: 26px;
  }
}
</style>
