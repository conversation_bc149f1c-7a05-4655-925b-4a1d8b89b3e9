<template>
  <div class="w-default-footer_beian">
    <div class="w-default-footer_beian_inner">
      <div
        v-if="!isCnSite && language !== 'ko' && language !== 'vi'"
        class="klook-footer-inner-wrapper"
      >
        <p>
          <span
            >© 2014-{{ fullYear }}&nbsp;<em v-html="__t('23909')"></em
          ></span>
        </p>
        <social-media :language="language" />
      </div>
      <div v-if="isCnSite">
        <p>
          <span
            >© 2014-{{ fullYear }}&nbsp;<em v-html="__t('23909')"></em
          ></span>
          <template v-if="isBusinessLicenseVisible">
            <span
              class="footer-link-item"
              @click="
                $emit('redirectUrl', {
                  url: 'https://res.klook.com/image/upload/web3.0/%E8%90%A5%E4%B8%9A%E6%89%A7%E7%85%A7.pdf',
                  target: '_blank',
                })
              "
              >营业执照</span
            >
            <span
              class="footer-link-item"
              @click="
                $emit('redirectUrl', {
                  url: 'https://res.klook.com/image/upload/web3.0/%E6%B7%B1%E5%9C%B3%E5%B8%82%E8%B6%A3%E6%B8%B8%E7%8E%A9%E6%97%85%E8%A1%8C%E7%A4%BE-%E8%90%A5%E4%B8%9A%E6%89%A7%E7%85%A7_%E8%AE%B8%E5%8F%AF%E8%AF%81.pdf',
                  target: '_blank',
                })
              "
              >旅行社业务经营许可证</span
            >
          </template>
          <span
            class="footer-link-item"
            @click="
              $emit('redirectUrl', {
                url: 'https://beian.miit.gov.cn',
                target: '_blank',
              })
            "
            >ICP证：合字B2-20220505</span
          >
          <span
            class="footer-link-item"
            @click="
              $emit('redirectUrl', {
                url: 'https://beian.miit.gov.cn',
                target: '_blank',
              })
            "
            >粤ICP备14100233号</span
          >
          <span
            class="w-default-footer_beian_grade-filing_link footer-link-item"
            @click="
              $emit('redirectUrl', {
                url: 'http://www.beian.gov.cn/portal/registerSystemInfo?recordcode=44030402006016',
                target: '_blank',
              })
            "
          >
            <img
              class="w-default-footer_beian_grade-filing_icon"
              src="https://res.klook.com/image/upload/v1675654921/q2h6btwacsl9z552gc3g.jpg"
            />
            粤公网安备 44030402006016号
          </span>
        </p>
        <p>
          <span
            >地址：深圳市福田区沙头街道天安社区泰然五路9号天安科技创业园A座901</span
          >
          <span>客服电话：400-009-6616</span>
          <span>营业性演出许可证：440300120162</span>
        </p>
      </div>
      <div v-if="language === 'ko'">
        <div class="klook-footer-inner-wrapper">
          <p>
            <span
              >© 2014-{{ fullYear }}&nbsp;<em v-html="__t('23909')"></em
            ></span>
          </p>
          <social-media :language="language" />
        </div>
        <div>
          <span>클룩트래블테크놀러지(유)</span>
          <span>대표 린자오웨이</span>
          <span>서울 서초구 서초대로 77길 17, 12층</span>
          <span>사업자등록번호 301-86-34203</span>
          <span>고객 문의 02-3478-4131</span>
        </div>
        <p>
          <span
            class="footer-link-item"
            @click="
              $emit('redirectUrl', {
                url: 'https://www.ftc.go.kr/bizCommPop.do?wrkr_no=3018634203',
              })
            "
            >사업자정보확인</span
          >
          <span>통신판매업신고번호 제2021-서울서초-0989호</span>
          <span>관광사업자등록번호 제2021-000006호</span>
        </p>
      </div>
      <div v-if="language === 'vi'" class="w-default-footer_dathongbao">
        <div class="klook-footer-inner-wrapper">
          <p>
            <span>© 2014-{{ fullYear }}&nbsp;<em v-html="__t('23909')"></em></span>
          </p>
          <social-media :language="language" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import SocialMedia from "../common/social-media.vue";
export default {
  inject: ["__t", "__href", "language"],
  components: {
    SocialMedia,
  },
  props: {
    isCnSite: Boolean,
    fullYear: [String, Number],
    showMobileEntry: Boolean,
    isBusinessLicenseVisible: Boolean,
  },
};
</script>

<style lang="scss" scoped>
.klook-footer-inner-wrapper {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}
</style>
