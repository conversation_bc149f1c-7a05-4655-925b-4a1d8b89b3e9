<template>
  <div
    class="footer_email_wrapper"
    data-spm-module="FooterSubscribe"
  >
    <div class="tips">
      <span>{{ __t('108') }}</span>
      <p
        v-html="getTermsText"
      ></p>
    </div>
    <div class="input">
      <input
        v-model="email"
        type="text"
        :placeholder="__t('443')"
        :disabled="disabled"
        @focus.once="initGeet"
        @keyup.enter="handleSubmit"
      />
      <button
        type="button"
        data-spm-item="SubscribeBtn"
        v-galileo-click-tracker="{ spm: 'FooterSubscribe.SubscribeBtn', componentName: 'klk-traveller-footer' }"
        @click="handleSubmit();clickPane('Email Subscription|Newsletter Subscribed')"
      >
        {{ __t('109') }}
      </button>
      <div
        v-show="loading"
        class="loading"
      ></div>
    </div>
    <div
      v-show="message.value"
      :class="{ message: true, [message.type]: true }"
    >
      <YesSvg v-show="message.type === 'success'" width="12" height="12"></YesSvg>
      <span>{{ message.value }}</span>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Prop } from 'vue-property-decorator'
import urlTemplate from '@klook/klk-traveller-utils/lib/urlTemplate'
import Base from '../common/base'
import YesSvg from '../../svg/yes.svg'

@Component({
  components: {
    YesSvg
  }
})
export default class EmailSubscribe extends Base {
  @Prop() userEmail!: string

  email = ''
  disabled = false
  loading = false
  message = {
    type: 'success',
    value: ''
  }

  get footerSubscribeTitleData() {
    return {
      condition_link_pre: `<a href="${this.__href('/conditions/')}" target="_blank">`,
      condition_link_post: '</a>',
      policy_link_pre: `<a href="${this.__href('/policy/')}" target="_blank">`,
      policy_link_post: '</a>'
    }
  }

  get isLoggedIn() {
    return !!this.userEmail
  }

  checkEmail(email: string) {
    const reg = /^[a-zA-Z0-9_-]+(\.([a-zA-Z0-9_-])+)*@[a-zA-Z0-9_-]+[.][a-zA-Z0-9_-]+([.][a-zA-Z0-9_-]+)*$/
    return reg.test(email)
  }

  async initGeet() {
    const { simpleCaptchaInit } = await import('@klook/captcha')
    simpleCaptchaInit('subscribe', this, true)
  }

  subscribe(email: string, captcha: any, cb: Function) {
    let emailSubscribe = '/v1/websrv/subscribe/:email/send'
    const ownEmail = this.userEmail || ''

    if (this.isLoggedIn && ownEmail === email) {
      emailSubscribe = '/v1/websrv/subscribe/:email/create'
    }
    const url = urlTemplate(emailSubscribe, { email })

    let params = new URLSearchParams()

    for (let key in captcha) {
      params.append(key, captcha[key])
    }



    this._axios && this._axios
      .$post(url, params, {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      })
      .then((res: any) => {
        // tetris: res.data; nuxt: res
        const data = res.data || res
        if (data.success) {
          cb(null)
        } else {
          cb(data.error)
        }
      })
      .catch((error: any) => {
        cb(error)
      })
  }

  async handleSubmit() {
    if(this.loading) return false // 禁止多次点击
    const { email } = this

    this.message = {
      type: 'success',
      value: ''
    }

    if (!email) {
      return
    }

    if (!this.checkEmail(email)) {
      this.message = {
        type: 'error',
        value: this.__t('457')
      }
      return
    }

    this.disabled = true
    this.loading = true

    const { simpleCaptchaVerify, simpleCaptchaErrHandler } = await import('@klook/captcha')
    simpleCaptchaVerify('subscribe', this).then((captcha: any) => {
      return Object.assign({}, captcha)
    }).then((res) => {
      this.subscribe(email, res, (error: any) => {
        this.disabled = false
        this.loading = false

        if (!error) {
          this.message = {
            type: 'success',
            value: this.__t('458')
          }
        } else {
          this.message = {
            type: 'error',
            value: error.message
          }
        }
      })
    }).catch((error: any) => {
      this.disabled = false
      this.loading = false
      simpleCaptchaErrHandler(error, this)
    })
  }

  get getTermsText() {
    return typeof window === 'object' ? this.__t('17790', { ...this.footerSubscribeTitleData }) : ''
  }

  clickPane(event: string) {
    window.tracker && window.tracker.gtm.sendGTMCustomEvent(event)
  }
}
</script>

<style lang="scss">
$color-main: #ff5722;
$color-gray2: #e0e0e0;

.footer_email_wrapper {
  position: relative;
  .tips {
    margin-right: 18px;

    span {
      @include font-body-s-semibold();

      color: $color-text-primary;
    }

    p {
      @include font-caption-m-regular();

      margin-top: 6px;
      color: $color-text-secondary;
    }

    a {
      color: $color-text-secondary;
      text-decoration: underline;
    }
  }

  .input {
    margin-top: 12px;
    display: flex;
    position: relative;

    input {
      width: 259px;
      height: 42px;
      padding: 7px 12px;
      border-radius: $radius-m $radius-none $radius-none $radius-m;
      border: none;
      outline: none;
      font-size: $fontSize-body-s;
      background-color: $color-bg-page;

      &:focus {
        border: 1px solid #ff5722;
      }

      &::placeholder {
        @include font-caption-m-regular;
      }
    }

    button {
      @include font-caption-m-semibold();

      width: 101px;
      height: 42px;
      background-color: $color-brand-primary;
      border: 1px solid $color-brand-primary;
      border-radius: $radius-none $radius-m $radius-m $radius-none;
      outline: none;
      color: $color-text-primary-onDark;
      cursor: pointer;
      transition: all 0.3s;

      &:hover {
        opacity: $opacity-hover;
      }
    }
  }

  .message {
    display: flex;
    align-items: center;
    right: 0;
    bottom: -24px;

    &.success {
      color: $color-text-secondary;
    }

    &.error {
      color: $color-error;
    }

    svg {
      margin-right: 5px;
      flex: none;
    }
  }

  .loading {
    position: absolute;
    top: 9px;
    left: 225px;
    width: 24px;
    height: 24px;
    border-top: 3px solid $color-gray2;
    border-right: 3px solid $color-gray2;
    border-bottom: 3px solid $color-gray2;
    border-left: 3px solid $color-main;
    border-radius: $radius-circle;
    animation: spin 1.1s infinite linear;
  }

  @keyframes spin {
    from {
      transform: rotate(0deg);
    }

    to {
      transform: rotate(360deg);
    }
  }
}
</style>
