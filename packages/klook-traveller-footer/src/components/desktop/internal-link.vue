<template>
  <div class="w-default-footer_internal-link">
    <div
      v-for="item in renderLinks"
      :key="item.title"
      class="w-default-footer_links"
    >
      <span class="w-default-footer_label">{{ item.title }}</span>

      <div v-if="item.links.length < (wrapLength + 1)" class="list-container">
        <div
          v-for="link in item.links || []"
          :key="link.link"
          class="list-item"
        >
          <FooterLink :link="link" @redirectUrl="redirectUrl" />
        </div>
      </div>
      <div
        v-else
        class="list-container links-wrap"
        :style="{ height: `${calculateHeight()}px` }"
      >
        <div class="list-wrap-item">
          <div
            v-for="link in item.links.slice(0, wrapLength) || []"
            :key="link.link"
            class="list-item"
          >
            <FooterLink :link="link" @redirectUrl="redirectUrl" />
          </div>
        </div>
        <div class="list-wrap-item">
          <div
            v-for="link in item.links.slice(wrapLength) || []"
            :key="link.link"
            class="list-item"
          >
            <FooterLink :link="link" @redirectUrl="redirectUrl" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Prop } from 'vue-property-decorator'
import Base from '../common/base'
import {
  getBlogUrlByLanguage,
  getSustainabilityEntrance,
  digitalHubConfig
  , redirectUrl, RedirectOption
} from '../common/util'
import { Types } from '../../../types/types'

import FooterLink from '../common/footer-link.vue'
import isGiftcardEntranceVisible from '@klook/klk-traveller-utils/lib/isGiftcardEntranceVisible';

@Component({
  components: {
    FooterLink
  }
})
export default class FooterInternalLinks extends Base {
  @Prop() links!: Types.InternalLinks | Function
  @Prop() isAboutUsLinkVisible!: boolean
  @Prop() isPartnerLinkVisible!: boolean
  @Prop() isBusinessLicenseVisible!: boolean
  @Prop() isCareerLinkVisible!: boolean
  @Prop({ default: true }) isSocialCountVisible!: boolean
  @Prop() email!: string
  @Prop({ default: 100 }) wrapLength!: number
  @Prop({ default: 'global' }) market!: string

  get renderLinks() {
    if (!this.links) {
      return this.internalLinks
    }

    if (typeof this.links === 'function') {
      return this.links(this.internalLinks)
    }

    return this.links
  }

  get internalLinks() {
    let links: { title: string; links: any[] }[] = []

    if (this.isAboutUsLinkVisible) {
      links = links.concat([
        {
          title: this.__t('512'),
          links: this.aboutLinks
        }
      ])
    }

    if (this.isPartnerLinkVisible) {
      links = links.concat([
        {
          title: this.__t('513'),
          links: this.partnershipLinks
        }
      ])
    }

    links = links.concat([
      {
        title: this.__t('13454'),
        links: this.termsLinks
      }
    ])

    return links
  }

  get aboutLinks() {
    const language = this.language

    let aboutLinks: Types.ILink[] = [
      {
        link: this.__href('/about/'),
        content: this.__t('14366')
      }
    ]

    if (language.includes('en')) {
      aboutLinks = aboutLinks.concat({
        link: this.__href('/newsroom/'),
        content: this.__t('5106')
      })
    }

    if (language !== 'zh-CN' && this.blogUrl) {
      aboutLinks = aboutLinks.concat({
        link: this.blogUrl,
        content: this.__t('65'),
        isCrawable: true
      })
    }

    if (language === 'ko') {
      aboutLinks = aboutLinks.concat({
        link: this.__href('/blog/'),
        content: '클룩 가이드',
        isCrawable: true
      })
    }

    if (this.isCareerLinkVisible) {
      aboutLinks = aboutLinks.concat({
        link: 'https://www.klookcareers.com/',
        content: this.__t('15329'),
        attr: {
          target: '_blank'
        }
      })
    }

    if (this.showGiftCardEntrance) {
      aboutLinks = aboutLinks.concat({
        link: this.__href('/klook-gift-card/'),
        content: this.__t('15966'),
        attr: {
          target: '_blank'
        },
        on: {
          click: this.sendGiftCardMixpanel
        }
      })
    }

    if (['zh-HK', 'zh-TW', 'ja'].includes(language)) {
      aboutLinks = aboutLinks.concat({
        link: this.__href('/newsroom/'),
        content: this.__t('168511')
      })
    }

    if (this.showSustainabilityEntrance) {
      aboutLinks = aboutLinks.concat({
        link: this.__href('/sustainability-our-mission/'),
        content: this.__t('193220')
      })
    }
    return aboutLinks
  }

  get partnershipLinks() {
    const language = this.language
    const market = this.market
    const merchantHost = market === 'global' ? 'https://merchant.klook.com' : 'https://merchant.klook.cn'

    let PartnershipLinks: Types.ILink[] = [{
      link: `${merchantHost}/introduction?from=klook&lang=${this.language}`,
      content: this.__t('29511'),
      attr: {
        target: '_blank'
      }
    }, {
      link: `${merchantHost}/login`,
      content: this.__t('68')
    }]

    if (market === 'global') {
      PartnershipLinks = PartnershipLinks.concat({
        link: 'https://affiliate.klook.com/',
        content: this.__t('12475')
      })
    }

    if (language !== 'zh-CN') {
      PartnershipLinks = PartnershipLinks.concat({
        link:
          'https://docs.google.com/forms/d/e/1FAIpQLSdL2Ih8naxwUEQDs4Zl5GPtbJYR1sdD7azMwVEi9vDiX85ReQ/viewform',
        content: this.__t('571')
      })
    }

    PartnershipLinks = PartnershipLinks.concat({
      link: this.agentWebUrl,
      content: this.__t('528')
    })

    if (this.isEnLanguage) {
      // PartnershipLinks = PartnershipLinks.concat([
      //   {
      //     link: this.__href('/article/14632-digital-solutions/', 'en-US'),
      //     content: 'Digital Solutions'
      //   }
      // ])

      const partnerHubLink = ['en-AU', 'en-NZ'].includes(language)
        ? '/tetris/promo/klook-anz-partner-hub/'
        : '/tetris/promo/klook-partner-hub/'
      PartnershipLinks = PartnershipLinks.concat([
        {
          link: this.__href(partnerHubLink),
          content: 'Klook Partner Hub'
        }
      ])
    }

    if (this.digitalHubConfig) {
      PartnershipLinks = PartnershipLinks.concat([
        {
          link: this.__href(this.digitalHubConfig.href),
          content: this.digitalHubConfig.text
        }
      ])
    }

    PartnershipLinks = PartnershipLinks.concat({
      link: this.__href('/partner/'),
      content: this.__t('201301')
    })

    return PartnershipLinks
  }

  get termsLinks() {
    const language = this.language

    let termsLinks: Types.ILink[] = [
      {
        link: this.__href('/conditions/'),
        content: this.__t('69')
      },
      {
        link: this.__href('/policy/'),
        content: this.__t('15121')
      },
      {
        link: this.__href('/cookiepolicy/'),
        content: this.__t('14811')
      },
      {
        link: this.__href('/bugbounty/'),
        content: this.__t('8306')
      }
    ]

    if (language === 'ja') {
      termsLinks = termsLinks.concat({
        link: this.__href('/law/'),
        content: '特定商取引法に基づく表記'
      })
    }

    if (language === 'vi') {
      termsLinks = termsLinks.concat({
        link: this.__href('/policy/'),
        content: 'Chính sách và quy định',
        attr: {
          target: '_blank'
        }
      })
    }

    if (language === 'ko') {
      termsLinks = termsLinks.concat({
        link: this.__href('/krlocationbasedservices/'),
        content: '위치기반서비스 이용약관',
        attr: {
          target: '_blank'
        }
      })
    }

    if (!['de', 'it', 'fr', 'ru', 'es'].includes(language)) {
      termsLinks = termsLinks.concat({
        link: this.__href('/animalwelfarepolicy/'),
        content: this.__t('100693'),
        attr: {
          target: '_blank'
        }
      })
    }

    return termsLinks
  }

  get blogUrl() {
    const url = getBlogUrlByLanguage(this.language)
    return /^https?/.test(url) ? url : this.__href(url, 'en')
  }

  get showGiftCardEntrance() {
    return isGiftcardEntranceVisible(this.language)
  }

  get showSustainabilityEntrance() {
    return getSustainabilityEntrance(this.language)
  }

  get hkToTw() {
    if (this.language === 'zh-HK') {
      return 'zh-TW'
    }
    return this.language
  }

  get agentWebUrl() {
    if (this.market === 'global') {
      if (['zh-CN', 'zh-TW'].includes(this.hkToTw)) {
        return `https://klook.klktech.com/${this.hkToTw}/`
      }
      return 'https://klook.klktech.com/'
    }
    return 'https://klook.klktech.cn/zh-CN/'
  }

  get isEnLanguage() {
    return [
      'en',
      'en-AU',
      'en-CA',
      'en-GB',
      'en-HK',
      'en-IN',
      'en-MY',
      'en-NZ',
      'en-PH',
      'en-SG',
      'en-US'
    ].includes(this.language)
  }

  get digitalHubConfig() {
    return digitalHubConfig(this.language)
  }

  // todo: add mixpanel
  sendGiftCardMixpanel() {
    window.tracker &&
      window.tracker.sendMixpanel({
        name: 'Gift Card Page',
        props: {
          'Entrance Path': 'Footer'
        }
      })
  }

  redirectUrl(option: RedirectOption) {
    redirectUrl({ url: option.url, target: option.target })
  }

  calculateHeight() {
    const wrapLength = this.wrapLength
    const lineHeight = 26

    return wrapLength * lineHeight
  }
}
</script>

<style lang="scss">
$color-gray: #888;

.w-default-footer_internal-link {
  display: flex;
  align-items: flex-start;
}

.w-default-footer_links {
  padding: 0;
  flex: 1;
  margin-right: 40px;

  .w-default-footer_label {
    @include font-body-s-semibold();
    color: $color-text-primary;
  }

  .links-wrap {
    display: flex;
    .list-wrap-item {
      margin-right: 24px;
    }
    .list-wrap-item:last-child {
      margin-right: 0;
    }
  }

  div.list-container {
    margin-top: 12px;
    flex-wrap: wrap;
  }

  div.list-item {
    list-style-type: none;
    margin-bottom: 8px;
    min-width: 180px;
  }

  .footer-list-item-inner {
    cursor: pointer;
    @include font-caption-m-regular();
    color: $color-text-secondary;
    &:hover {
      text-decoration: none;
    }
  }
}
</style>
