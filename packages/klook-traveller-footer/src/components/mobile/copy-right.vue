<template>
  <div>
    <social-media :language="language" />
    <div
      v-if="!isCnSite && language !== 'ko' && language !== 'vi'"
      class="default-footer_copyright"
    >
      <span>© 2014-{{ fullYear }}</span>
      <span v-html="__t('23909')"></span>
    </div>
    <div v-if="isCnSite" class="default-footer_beian">
      <div class="default-footer_item">
        <span>© 2014-{{ fullYear }}</span>
        <span v-html="__t('23909')"></span>
      </div>
      <div class="default-footer_item" v-if="isBusinessLicenseVisible">
        <span
          class="footer-link-item"
          @click="
            $emit('redirectUrl', {
              url:
                'https://res.klook.com/image/upload/web3.0/%E8%90%A5%E4%B8%9A%E6%89%A7%E7%85%A7.pdf',
              target: '_blank'
            })
          "
          >营业执照</span
        >
        <span
          class="footer-link-item"
          @click="
            $emit('redirectUrl', {
              url:
                'https://res.klook.com/image/upload/web3.0/%E6%B7%B1%E5%9C%B3%E5%B8%82%E8%B6%A3%E6%B8%B8%E7%8E%A9%E6%97%85%E8%A1%8C%E7%A4%BE-%E8%90%A5%E4%B8%9A%E6%89%A7%E7%85%A7_%E8%AE%B8%E5%8F%AF%E8%AF%81.pdf',
              target: '_blank'
            })
          "
          >旅行社业务经营许可证</span
        >
      </div>
      <div class="default-footer_item">
        <span
          class="footer-link-item"
          @click="
            $emit('redirectUrl', {
              url: 'https://beian.miit.gov.cn',
              target: '_blank'
            })
          "
          >ICP证：合字B2-20220505
        </span>
      </div>

      <div class="default-footer_item">
        <span
          class="footer-link-item"
          @click="
            $emit('redirectUrl', {
              url: 'https://beian.miit.gov.cn',
              target: '_blank'
            })
          "
          >粤ICP备14100233号
        </span>
      </div>

      <div class="default-footer_beian_grade-filing">
        <span
          class="default-footer_beian_grade-filing_link footer-link-item"
          @click="
            $emit('redirectUrl', {
              url:
                'http://www.beian.gov.cn/portal/registerSystemInfo?recordcode=44030402006016',
              target: '_blank'
            })
          "
        >
          <img
            class="default-footer_beian_grade-filing_icon"
            src="https://res.klook.com/image/upload/v1675654921/q2h6btwacsl9z552gc3g.jpg"
          />
          粤公网安备 44030402006016号
        </span>
      </div>
      <div class="default-footer_item">
        <span
          >地址：深圳市福田区沙头街道天安社区泰然五路9号天安科技创业园A座901</span
        >
      </div>
      <div class="default-footer_item">
        <span>客服热线</span>
        <a href="tel:************">************</a>
      </div>
      <div class="default-footer_item">
        <span>营业性演出许可证：440300120162</span>
      </div>
    </div>

    <!-- ko start -->
    <div v-if="language === 'ko'" class="default-footer_beian">
      <div class="default-footer_item">
        <span>© 2014-{{ fullYear }}</span>
        <span v-html="__t('23909')"></span>
      </div>
      <div class="default-footer_item">
        <span>클룩트래블테크놀러지(유)</span> <br />
        <span>대표 린자오웨이</span> <br />
        <span>서울 서초구 서초대로 77길 17, 12층</span>
      </div>
      <div class="default-footer_item">
        <span>사업자등록번호 301-86-34203</span><br />
        <span>고객 문의 02-3478-4131</span>
      </div>
      <div class="default-footer_item">
        <span
          class="footer-link-item"
          @click="
            $emit('redirectUrl', {
              url: 'https://www.ftc.go.kr/bizCommPop.do?wrkr_no=3018634203'
            })
          "
          style="text-decoration: underline; color: #999"
          >사업자정보확인</span
        >
        <br />
        <span>통신판매업신고번호 제2021-서울서초-0989호</span>
        <br />
        <span>관광사업자등록번호 제2021-000006호</span>
      </div>
    </div>
    <!-- ko end -->
    <div v-if="language === 'vi'" class="default-footer_beian">
      <div class="w-default-footer_dathongbao default-footer_item">
        <span>© 2014-{{ fullYear }}</span>
        <span v-html="__t('23909')"></span>
      </div>
    </div>
  </div>
</template>

<script>
import SocialMedia from "../common/social-media.vue";

export default {
  inject: ["__t", "__href", "language"],
  components: {
    SocialMedia,
  },
  props: {
    isSocialCountVisible: Boolean,
    isDesktopEntryVisible: Boolean,
    isBusinessLicenseVisible: Boolean,
    isCnSite: Boolean,
    fullYear: [String, Number],
  }
};
</script>

<style lang="scss" scoped>
.social-media-wrapper {
  margin-bottom: 24px;
}
</style>

