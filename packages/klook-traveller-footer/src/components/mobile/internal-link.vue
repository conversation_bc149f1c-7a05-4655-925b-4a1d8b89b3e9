<template>
  <div class="mobile-w-default-footer_internal-link">
    <div
      v-for="item in renderLinks"
      :key="item.title"
      class="mobile-w-default-footer_links"
    >
      <span class="w-default-footer_label">{{ item.title }}</span>

      <ul>
        <li v-for="link in item.links || []" :key="link.link">
          <FooterLink @redirectUrl="redirectUrl" :link="link" />
        </li>
      </ul>
    </div>
    <div v-if="payIcons && payIcons.length" class="mobile-w-default-footer_links">
      <span class="w-default-footer_label">{{ __t('1598') }}</span>
      <div class="w-default-footer_payment">
        <ul>
          <li
            v-for="icon in payIcons"
            :key="icon"
            :style="{ backgroundImage: `url(${formatPicUrl(icon, 80, 48)})` }"
          ></li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Prop } from 'vue-property-decorator'
import transformImageUrl from '@klook/klk-traveller-utils/lib/transformImageUrl'
import Base from '../common/base'
import {
  getBlogUrlByLanguage,
  getSustainabilityEntrance,
  digitalHubConfig
} from '../common/util'
import { Types } from '../../../types/types'
import isGiftcardEntranceVisible from '@klook/klk-traveller-utils/lib/isGiftcardEntranceVisible';


import { redirectUrl, RedirectOption } from '../common/util'
import FooterLink from "../common/footer-link.vue"

@Component({
  components: {
    FooterLink
  },
})
export default class FooterInternalLinks extends Base {
  @Prop() links!: Types.InternalLinks | Function
  @Prop() isAboutUsLinkVisible!: boolean
  @Prop() isPartnerLinkVisible!: boolean
  @Prop() isBusinessLicenseVisible!: boolean
  @Prop() isCareerLinkVisible!: boolean
  @Prop({ default: true }) isSocialCountVisible!: boolean
  @Prop() email!: string
  @Prop() payIcons!: Array<any>
  @Prop({ default: "global" }) market!: string

  get renderLinks() {
    if (!this.links) { return this.internalLinks }

    if (typeof this.links === 'function') {
      return this.links(this.internalLinks)
    }

    return this.links
  }

  get internalLinks() {
    let links: {title: string, links: any[]}[] = []

    if (this.isAboutUsLinkVisible) {
      links = links.concat([{
        title: this.__t('512'),
        links: this.aboutLinks
      }])
    }

    if (this.isPartnerLinkVisible) {
      links = links.concat([{
        title: this.__t('513'),
        links: this.partnershipLinks
      }])
    }

    links = links.concat([{
      title: this.__t('13454'),
      links: this.termsLinks
    }])

    return links
  }

  get aboutLinks() {
    const language = this.language

    let aboutLinks: Types.ILink[] = [{
      link: this.__href('/about/'),
      content: this.__t('14366')
    }]

    if (language.includes('en')) {
      aboutLinks = aboutLinks.concat({
        link: this.__href('/newsroom/'),
        content: this.__t('5106')
      })
    }

    if (language !== 'zh-CN' && this.blogUrl) {
      aboutLinks = aboutLinks.concat({
        link: this.blogUrl,
        content: this.__t('65'),
        isCrawable: true
      })
    }

    if (language === 'ko') {
      aboutLinks = aboutLinks.concat({
        link: this.__href('/blog/'),
        content: '클룩 가이드',
        isCrawable: true
      })
    }
    if (this.isCareerLinkVisible) {
      aboutLinks = aboutLinks.concat({
        link: 'https://www.klookcareers.com/',
        content: this.__t('15329'),
        attr: {
          target: '_blank'
        }
      })
    }

    if (this.showGiftCardEntrance) {
      aboutLinks = aboutLinks.concat({
        link: this.__href('/klook-gift-card/'),
        content: this.__t('15966'),
        attr: {
          target: '_blank'
        },
        on: {
          click: this.sendGiftCardMixpanel
        }
      })
    }

    if (['zh-HK', 'zh-TW', 'ja'].includes(language)) {
      aboutLinks = aboutLinks.concat({
        link: this.__href('/newsroom/'),
        content: this.__t('168511')
      })
    }

    if (this.showSustainabilityEntrance) {
      aboutLinks = aboutLinks.concat({
        link: this.__href('/sustainability-our-mission/'),
        content: this.__t('193220'),
      })
    }

    return aboutLinks
  }

  get partnershipLinks() {
    const language = this.language

    let PartnershipLinks: Types.ILink[] = []

    if (language !== 'zh-CN') {
      PartnershipLinks = PartnershipLinks.concat({
        link: 'https://docs.google.com/forms/d/e/1FAIpQLSdL2Ih8naxwUEQDs4Zl5GPtbJYR1sdD7azMwVEi9vDiX85ReQ/viewform',
        content: this.__t('571')
      })
    }

    if (this.isEnLanguage) {
      // PartnershipLinks = PartnershipLinks.concat([{
      //   link: this.__href('/article/14632-digital-solutions/', 'en-US'),
      //   content: 'Digital Solutions'
      // }])

      const partnerHubLink = ['en-AU', 'en-NZ'].includes(language) ? '/tetris/promo/klook-anz-partner-hub/' : '/tetris/promo/klook-partner-hub/'
      PartnershipLinks = PartnershipLinks.concat([{
        link: this.__href(partnerHubLink),
        content: 'Klook Partner Hub'
      }])
    }

    if (this.digitalHubConfig) {
      PartnershipLinks = PartnershipLinks.concat([{
        link: this.__href(this.digitalHubConfig.href),
        content: this.digitalHubConfig.text
      }])
    }

    return PartnershipLinks
  }

  get termsLinks() {
    const language = this.language

    let termsLinks: Types.ILink[] = [{
      link: this.__href('/conditions/'),
      content: this.__t('69')
    }, {
      link: this.__href('/policy/'),
      content: this.__t('15121')
    }, {
      link: this.__href('/cookiepolicy/'),
      content: this.__t('14811')
    }, {
      link: this.__href('/bugbounty/'),
      content: this.__t('8306')
    }]

    if (language === 'ja') {
      termsLinks = termsLinks.concat({
        link: this.__href('/law/'),
        content: '特定商取引法に基づく表記'
      })
    }

    if (language === 'vi') {
      termsLinks = termsLinks.concat({
        link: this.__href('/policy/'),
        content: 'Chính sách và quy định',
        attr: {
          target: '_blank'
        }
      })
    }

    if (language === 'ko') {
      termsLinks = termsLinks.concat({
        link: this.__href('/krlocationbasedservices/'),
        content: '위치기반서비스 이용약관',
        attr: {
          target: '_blank'
        }
      })
    }

    if (!['de', 'it', 'fr', 'ru', 'es'].includes(language)) {
      termsLinks = termsLinks.concat({
        link: this.__href("/animalwelfarepolicy/"),
        content: this.__t("100693"),
        attr: {
          target: "_blank"
        }
      });
    }

    return termsLinks
  }

  get blogUrl() {
    const url = getBlogUrlByLanguage(this.language)
    return /^https?/.test(url) ? url : this.__href(url, 'en')
  }

  get showGiftCardEntrance() {
    return isGiftcardEntranceVisible(this.language)
  }

  get showSustainabilityEntrance() {
    return getSustainabilityEntrance(this.language)
  }

  get hkToTw() {
    if (this.language === 'zh-HK') {
      return 'zh-TW'
    }
    return this.language
  }

  get agentWebUrl() {
    if (this.market === 'global') {
      if (['zh-CN', 'zh-TW'].includes(this.hkToTw)) {
        return `https://klook.klktech.com/${this.hkToTw}/`
      }
      return 'https://klook.klktech.com/'
    }
    return 'https://klook.klktech.cn/zh-CN/'
  }

  get isEnLanguage() {
    return [
      'en',
      'en-AU',
      'en-CA',
      'en-GB',
      'en-HK',
      'en-IN',
      'en-MY',
      'en-NZ',
      'en-PH',
      'en-SG',
      'en-US'
    ].includes(this.language)
  }

  get digitalHubConfig() {
    return digitalHubConfig(this.language)
  }

  get realWebp() {
    // 若有webp配置则返回，否则为0
    return (this as any).$store?.state?.klook?.webp || 0
  }

  formatPicUrl(url: string, width: number, height: number) {
    return transformImageUrl(url, {
      width,
      height,
      webp: this.realWebp,
      fill: 'fit',
      quantity: 100
    })
  }

  // todo: add mixpanel
  sendGiftCardMixpanel() {
    window.tracker && window.tracker.sendMixpanel({
      name: 'Gift Card Page',
      props: {
        'Entrance Path': 'Footer'
      }
    })
  }

  redirectUrl(option: RedirectOption) {
    redirectUrl({ url: option.url, target: option.target })
  }
}
</script>

<style lang="scss">
$color-gray: #888;

@mixin retina-sprite($url, $url2, $width, $height) {
  background-image: url(#{$url});
  width: $width;
  height: $height;

  @media (-webkit-min-device-pixel-ratio: 2),
  (min-resolution: 192dpi) {
    background-image: url(#{$url2});
    background-size: $width $height;
  }
}

.mobile-w-default-footer_internal-link {
  width: 100%;
}

.mobile-w-default-footer_links {
  font-size: $fontSize-body-s;
  margin-bottom: 28px;

  .w-default-footer_label {
    @include font-body-s-semibold();

    color: $color-text-primary;
  }

  ul {
    display: flex;
    flex-wrap: wrap;
    margin-top: 8px;
    margin-left: -8px;
    margin-right: -8px;
  }

  li {
    list-style-type: none;
    margin-bottom: 8px;
    .footer-list-item-inner {
      display: inline-block;
      padding: 0 8px;
      border-right: 1px solid $color-border-normal;
      cursor: pointer;
      @include font-caption-m-regular();
      line-height: 1;
      color: $color-text-secondary;
      &:hover {
        text-decoration: none;
      }
    }
    &:last-child {
      .footer-list-item-inner {
        border-right: none;
      }
    }
  }
}
.w-default-footer_payment {
  span {
    @include font-body-s-semibold();

    color: $color-text-primary;
  }

  ul {
    display: flex;
    flex-wrap: wrap;
    margin-left: 0;
    margin-top: 12px;
  }

  li {
    margin-right: 12px;
    margin-bottom: 12px;
    border: 1px solid $color-border-dim;
    border-radius: $radius-s;
    width: 40px;
    height: 25px;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
  }
}
</style>
