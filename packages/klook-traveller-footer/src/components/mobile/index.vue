<template>
  <div class="default-footer">
    <div v-if="isPoweredVisible" class="powered_by_klook">
      <img v-lazy="poweredByKlookLink" alt="powered by klook" />
    </div>
    <div class="default-footer_inner">
      <template v-if="market === 'global' && isLanguageCurrencyVariable">
        <client-only>
          <div class="default-footer_selector">
            <klk-select
              v-if="isLanguageVariable"
              :bottom-sheet="true"
              :bottom-sheet-title="__t('15399')"
              :bottom-sheet-props="{
                transfer: true,
                popupClassName: 'language_select_popup'
              }"
              bottom-sheet-height="100%"
              class="language_select"
              data-spm-module="FooterSelectLanguage"
              :value="language"
              ref="languageSelect"
              placeholder="Please Select"
              @click.native="openedHandle('languageSelect')"
              @change="changeLanguageHandle"
            >
              <div>
                <div v-if="isChangeSiteTipVisible" class="locale_tip_box">
                  <p class="tip_text">{{ __t("16637") }}</p>
                  <a class="change_site_link" @click="settingWebsite">
                    {{
                    __t("16635")
                    }}
                  </a>
                </div>
                <klk-option
                  v-for="(lang, i) in languageOptList"
                  :class="lang.value === language ? 'klk-option-selected' : ''"
                  :key="lang.value + i"
                  :value="lang.value"
                  :label="lang.title"
                />
              </div>
            </klk-select>
            <klk-select
              v-if="isCurrencyVariable"
              :bottom-sheet="true"
              :bottom-sheet-title="__t('82')"
              :bottom-sheet-props="{
                transfer: true,
                popupClassName: 'currency_select_popup'
              }"
              bottom-sheet-height="100%"
              class="currency_select"
              data-spm-module="FooterSelectCurrency"
              :value="currency"
              ref="currencySelect"
              placeholder="Please Select"
              @click.native="openedHandle('currencySelect')"
              @change="changeCurrencyHandle"
            >
              <div>
                <div v-if="isChangeSiteTipVisible" class="locale_tip_box">
                  <p class="tip_text">{{ __t("16638") }}</p>
                  <a class="change_site_link" @click="settingWebsite">
                    {{
                    __t("16635")
                    }}
                  </a>
                </div>
                <klk-option-group
                  v-for="(currencyListItem, i) in currencyOptList"
                  :key="currencyListItem.title + i"
                  :label="currencyListItem.title"
                >
                  <klk-option
                    v-for="(currencyItem, i) in currencyListItem.children"
                    :class="
                      currencyItem.value === currency
                        ? 'klk-option-selected'
                        : ''
                    "
                    :key="currencyItem.value + i"
                    :value="currencyItem.value"
                    :label="currencyItem.title"
                  />
                </klk-option-group>
              </div>
            </klk-select>
          </div>
        </client-only>
        <div
          v-if="isAskBtnVisible"
          data-spm-module="HelpCentreInFootie"
          data-spm-virtual-item="__virtual"
          v-galileo-click-tracker="{ spm: 'HelpCentreInFootie', componentName: 'klk-traveller-footer' }"
          class="default-footer_btn-ask"
          @click="handleClickAskBtn"
        >{{ __t("1336") }}</div>
      </template>
      <FooterInternalLinks
        v-if="!isMiniStyle"
        :is-social-count-visible="isSocialCountVisible"
        :is-about-us-link-visible="isAboutUsLinkVisible"
        :is-partner-link-visible="isPartnerLinkVisible"
        :is-career-link-visible="isCareerLinkVisible"
        :email="email"
        :links="links"
        :pay-icons="payIcons"
        :market="market"
      ></FooterInternalLinks>
      <Copyright
        :is-social-count-visible="isSocialCountVisible"
        :is-desktop-entry-visible="isDesktopEntryVisible"
        :is-business-license-visible="isBusinessLicenseVisible"
        :is-cn-site="isCnSite"
        :full-year="fullYear"
        @onClickViewDesktop="
          redirectUrl({ url: __href('/') });
          handleViewDesktop();
          clickPane('Others|Visit Desktop Site Button Clicked');
        "
        @redirectUrl="redirectUrl"
      />
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Prop } from "vue-property-decorator";
import sessionStorage from "@klook/klk-traveller-utils/lib/sessionStorage";
import getCookieConfig from "@klook/klk-traveller-utils/lib/getCookieConfig";
import convertToBackendLanguage from "@klook/klk-traveller-utils/lib/convertToBackendLanguage";
import replaceQueryParams from "@klook/klk-traveller-utils/lib/replaceQueryParams";
import KlkIcon from "@klook/klook-ui/lib/icon/index.js";
import { Select, Option, OptionGroup } from "@klook/klook-ui/lib/select";
import { lockOuterScroll } from "@klook/klook-ui/lib/utils/dom";

import PlatformBase from "../common/platform-base";
import {
  digitalHubConfig,
  getCurrencyTextId,
  getBlogUrlByLanguage,
  CurencyTextIdMap
} from "../common/util";
import IconFacebook from "../../svg/icon-facebook.svg";
import IconInstagram from "../../svg/icon-instagram.svg";
import IconTwitter from "../../svg/icon-twitter.svg";

import FooterInternalLinks from "./internal-link.vue";
import Copyright from "./copy-right.vue";

interface ICurrencyLists {
  suggestCurrencies: string[];
  otherCurrencies: string[];
}

@Component({
  inheritAttrs: false,
  components: {
    KlkIcon,
    KlkSelect: Select,
    KlkOption: Option,
    KlkOptionGroup: OptionGroup,
    IconFacebook,
    IconInstagram,
    IconTwitter,
    FooterInternalLinks,
    Copyright
  }
})
export default class KlkTravellerMobileFooter extends PlatformBase {
  @Prop({ default: "USD" }) currency!: string;
  @Prop({ default: true }) isLanguageCurrencyVariable!: boolean;
  @Prop({ default: true }) isLanguageVariable!: boolean;
  @Prop({ default: true }) isCurrencyVariable!: boolean;
  @Prop({
    default: () => ({
      suggestCurrencies: [],
      otherCurrencies: []
    })
  })
  supportCurrencies!: ICurrencyLists;
  // 支持的语言
  @Prop({ default: () => [] }) renderLanguages!: any;
  @Prop() isChangeSiteTipVisible!: boolean;
  @Prop({ default: true }) isMiniStyle!: boolean;
  @Prop({ default: "global" }) market!: string;
  @Prop({ default: true }) isPartnerLinkVisible!: boolean;

  visible = false;

  get langs() {
    return this.renderLanguages;
  }

  get currencyOptList() {
    const {
      suggestCurrencies = [],
      otherCurrencies = []
    } = this.supportCurrencies;
    const allCurrencyList =
      Object.entries(CurencyTextIdMap).map(([key, value]) => {
        return {
          title: `${this.__t(value)} | ${key}`,
          value: key
        };
      }) || [];
    const cryOptList = [
      {
        title: this.__t("82"),
        children: allCurrencyList.filter((cur: any) => {
          if (suggestCurrencies.length) {
            return suggestCurrencies.includes(cur.value);
          }
          return false;
        })
      }
    ];

    const otherCryOptList = allCurrencyList.filter((cur: any) => {
      if (otherCurrencies.length) {
        return otherCurrencies.includes(cur.value);
      }
      return false;
    });
    if (otherCryOptList.length) {
      cryOptList.push({
        title: this.__t("83"),
        children: otherCryOptList
      });
    }
    return cryOptList;
  }

  lockOuterScroll = lockOuterScroll;
  get languageOptList() {
    const langOpts: any[] = [];
    this.langs.forEach((lang: any) => {
      if (lang.children) {
        lang.children.forEach((childlang: any) => {
          langOpts.push({ ...childlang });
        });
      } else {
        langOpts.push(lang);
      }
    });
    return langOpts;
  }
  get isEnLanguage() {
    return [
      "en",
      "en-AU",
      "en-CA",
      "en-GB",
      "en-HK",
      "en-IN",
      "en-MY",
      "en-NZ",
      "en-PH",
      "en-SG",
      "en-US"
    ].includes(this.language);
  }

  get digitalHubConfig() {
    return digitalHubConfig(this.language);
  }

  get fullYear() {
    return new Date().getFullYear();
  }

  get poweredByKlookLink() {
    return "https://res.klook.com/image/upload/hybrid/klook_poweredby_logo_horizontal.svg";
  }

  get rendeCurrency() {
    return `${this.__t(`${getCurrencyTextId(this.currency)}`)} | ${
      this.currency
    }`;
  }

  get blogUrl() {
    const url = getBlogUrlByLanguage(this.language);
    return /^https?/.test(url) ? url : this.__href(url, "en");
  }

  syncPageData(config: any) {
    const { qs, url } = config;
    if (!this.isLoggedIn) {
      return Promise.resolve();
    }
    const headers = {
      "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8"
    };
    return this._axios
      ? this._axios.$post(url, qs, { headers, loading: true })
      : Promise.resolve();
  }
  currencyChangeReload(currency: string) {
    const cookie = getCookieConfig("currency");
    const href = window.location.href.split("?")[0];

    window.Cookies &&
      window.Cookies.set(cookie.key, currency, {
        path: "/",
        expires: new Date(Date.now() + cookie.lifetime)
      });
    window.location.replace(href + replaceQueryParams("_currency"));
    // window.location.reload();
  }
  languageChangeReload(language: Data.Language) {
    const originalPathName = window.location.href.replace(
      window.location.origin,
      ""
    );
    const currentLanguage = this.language;
    let originalPath = originalPathName;
    const repalceReg = new RegExp(
      `^/${currentLanguage}/|^/${currentLanguage}$`
    );
    if (originalPathName.match(repalceReg)) {
      originalPath = originalPathName.replace(`/${currentLanguage}`, "");
    }

    const cookie = getCookieConfig("language");
    window.Cookies &&
      window.Cookies.set(cookie.key, language, {
        path: "/",
        expires: new Date(Date.now() + cookie.lifetime)
      });

    if (language === "en") {
      window.location.href = originalPath;
    } else {
      window.location.href = `/${language}${originalPath}`;
    }
  }
  changeLanguageHandle(language: Data.Language) {
    this.syncPageData({
      qs: `language=${convertToBackendLanguage(language)}`,
      url: "/v3/userserv/user/profile_service/change_language"
    })
      .then(() => {
        if (window.tracker) {
          window.tracker.gtm.sendGTMCustomEvent(
            `Language Setting|Language Switched|${language}`
          );
          const targetNode =
            window.tracker.inhouse.queryData({
              spmType: "page",
              node: (this.$refs.languageSelect as Vue).$el,
              nearSpm: true
            }) || {};
          const PageName = targetNode?.value|| null;
          window.tracker.inhouse.track(
            "custom",
            targetNode?.spmInfo?.page || "body",
            {
              spm: "{Page}.FooterSelectLanguage.Change",
              ext: {
                Language: language,
                PageName
              }
            }
          );
        }
      })
      .finally(() => {
        this.languageChangeReload(language);
      });
  }
  changeCurrencyHandle(currency: string) {
    this.syncPageData({
      qs: `currency=${currency}`,
      url: "/v3/userserv/user/profile_service/change_currency"
    })
      .then(() => {
        if (window.tracker) {
          window.tracker.gtm.sendGTMCustomEvent(
            `Currency Setting|Currency Switched|${currency}`
          );
          const targetNode =
            window.tracker.inhouse.queryData({
              spmType: "page",
              node: (this.$refs.currencySelect as Vue).$el,
              nearSpm: true
            }) || {};
          const PageName = targetNode?.value|| null;
          window.tracker.inhouse.track(
            "custom",
            targetNode?.spmInfo?.page || "body",
            {
              spm: "{Page}.FooterSelectCurrency.Change",
              ext: {
                Currency: currency,
                PageName
              }
            }
          );
        }
      })
      .finally(() => {
        this.currencyChangeReload(currency);
      });
  }
  savePath() {
    const fullPath = window.location.pathname + window.location.search;
    sessionStorage.setItem("selectLanguageJump", fullPath);
  }

  handleViewDesktop() {
    window.Cookies &&
      window.Cookies.set(getCookieConfig("platform").key, "desktop", {
        path: "/"
      });
  }

  clickPane(event: string) {
    window.tracker && window.tracker.gtm.sendGTMCustomEvent(event);
  }

  handleClickAskBtn() {
    this.redirectUrl({
      url: this.__href("/faq/?ref_source=FooterHelpCenter")
    });
    window.tracker &&
      window.tracker.gtm.sendGTMCustomEvent(
        "Help Center|Ask Klook Button Clicked"
      );
  }
  settingWebsite() {
    window.location.href = this.__href("/edit_profile?residence=show");
  }
  //锁住滚动
  openedHandle(refKey: string) {
    lockOuterScroll(this.$refs[refKey], false);
  }
}
</script>
<style lang="scss" scoped>
.locale_tip_box {
  padding: 16px 28px;
}
.tip_text {
  font-size: 16px;
  line-height: 144%;
  color: #757575;
}
.change_site_link {
  font-weight: 600;
  font-size: 16px;
  line-height: 144%;
  margin-top: 8px;
  color: #437dff;
}
</style>
<style lang="scss">
.currency_select_popup,
.language_select_popup {
  .klk-bottom-sheet-body {
    overscroll-behavior: contain;
  }
}
.default-footer {
  .default-footer_inner {
    padding: 32px 20px 20px;
    background-color: $color-bg-widget-normal;
    border-top: 1px solid $color-divider-solid-normal;
  }

  .powered_by_klook {
    display: flex;
    padding: 8px 0;
    background: $color-bg-widget-normal;
    align-items: center;
    justify-content: center;

    img {
      height: 24px;
    }
  }

  .default-footer_selector {
    display: flex;
    justify-content: space-between;

    .default-footer_item:nth-of-type(1):nth-last-of-type(1) {
      width: 100%;
    }
    .language_select,
    .currency_select {
      .klk-select-reference {
        min-height: 36px;
      }
    }
    .klk-select:nth-of-type(2) {
      margin-left: 10px;
    }
  }

  .default-footer_language,
  .default-footer_currency {
    position: relative;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 36px;
    padding-left: 8px;
    padding-right: 28px;
    color: $color-text-primary;
    border: 1px solid $color-border-normal;
    border-radius: $radius-m;
    width: calc(50% - 5px);

    .current {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      margin-right: 6px;
    }

    i {
      position: absolute;
      top: 50%;
      right: 18px;
      transform: translateY(-50%);
    }
  }

  .default-footer_popup {
    display: none;
    position: fixed;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    z-index: 9999;
    background-color: $color-bg-overlay-black-mobile;

    &--show {
      display: block;
    }
  }

  .default-footer_popup-content {
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    padding: 0 16px;
    background-color: $color-bg-widget-normal;

    li {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 15px 0;
      font-size: $fontSize-body-m;
      border-bottom: 1px solid $color-border-normal;

      &::after {
        display: none;
        content: "";
        width: 12px;
        height: 6px;
        border-left: 1px solid $color-brand-primary;
        border-bottom: 1px solid $color-brand-primary;
        transform: translate(0, -50%) rotate(-45deg);
      }

      &.selected::after {
        display: block;
      }
    }
  }

  .default-footer_btn-ask {
    @include font-body-s-bold;
    display: block;
    height: 36px;
    line-height: 36px;
    margin: 12px 0 36px;
    color: $color-text-primary;
    border: 1px solid $color-border-normal;
    border-radius: $radius-l;
    text-align: center;
    cursor: pointer;
    &:hover {
      text-decoration: none;
    }
  }

  .default-footer_links {
    margin-bottom: -10px;
    display: flex;
    flex-wrap: wrap;
    font-size: $fontSize-caption-s;

    li {
      margin-bottom: 10px;
      color: $color-text-placeholder;
    }

    a {
      color: $color-text-primary;
    }

    &-divide {
      margin-left: 8px;
      margin-right: 8px;
    }
  }

  .default-footer_social {
    display: flex;
    justify-content: center;
    margin: 20px 0 0 0;

    li + li {
      margin-left: 28px;
    }

    a {
      color: $color-icon-normal;
    }
  }

  .default-footer_desktop-entry {
    display: block;
    color: $color-text-secondary;
    font-size: $fontSize-caption-m;
    text-align: left;
    margin-top: 20px;
    margin-bottom: 12px;
  }

  .default-footer_hotline {
    @include font-caption-m-regular;

    position: relative;
    color: $color-text-primary;
    width: 100%;
    text-align: left;
    margin: 20px 0;

    div:nth-child(1) {
    }

    div:nth-child(2) {
      a {
        color: $color-text-primary;
      }
    }

    div:nth-child(3) {
    }
  }

  .default-footer_copyright {
    margin-top: 12px;
    @include font-caption-m-regular();
    color: $color-text-secondary;
    white-space: pre-wrap;

    a {
      color: inherit;
    }
  }

  .default-footer_beian {
    @include font-caption-m-regular();

    a {
      color: $color-text-secondary;
    }

    span {
      color: $color-text-secondary;
    }

    .default-footer_beian_grade-filing_link {
      display: flex;
      align-items: center;
    }

    .footer-link-item {
      cursor: pointer;
      &:hover {
        text-decoration: none;
      }
    }
  }

  .default-footer_business-license {
    display: block;
    color: $color-text-secondary;
    font-size: $fontSize-caption-m;
  }

  // .default-footer_change-website {
  //   display: block;
  //   margin-top: 12px;
  //   color: $color-text-secondary;
  //   font-size: $fontSize-caption-m;
  // }

  .default-footer_ko {
    margin-top: 24px;
    padding-top: 20px;
    /* stylelint-disable */
    border-top: 1px solid #4d4d4d;
    /* stylelint-enable */
    color: $color-text-secondary;
    font-size: $fontSize-caption-m;
    line-height: 18px;

    a,
    span {
      text-decoration: underline;
      color: inherit;
    }
  }
}
.default-footer_mini {
  overflow: hidden;
  .default-footer_copyright {
    @include font-body-s-regular();

    margin: 40px 20px 16px 20px;
  }
}
.w-default-footer_dathongbao {
  margin-top: 12px;
  margin-bottom: 24px;
}
</style>
