<template>
  <component
    :is="componentName"
    v-bind="$attrs"
    :platform="platform"
    v-on="$listeners"
  />
</template>

<script lang="ts">
import { Vue, Component, Prop, Provide } from 'vue-property-decorator'
import href from '@klook/klk-traveller-utils/lib/href'
import { translate } from './locale'
interface ICurrencyLists {
  suggestCurrencies: string[]
  otherCurrencies: string[]
}

@Component({
  inheritAttrs: false,
  components: {
    MobileFooterV2: () => import('./components/mobile/index.vue' /* webpackChunkName: 'mobile-footer-v2' */),
    DesktopFooterV2: () => import('./components/desktop/index.vue' /* webpackChunkName: 'desktop-footer-v2' */),
  }
})
export default class KlkTravellerFooter extends Vue {
  @Prop() platform!: string
  @Prop() userInfo!: Data.IUser
  @Prop({ default: true }) isV2!: boolean

  @Provide() language = this.getLang()
  @Provide() __t = this.getTranslate()
  @Provide() __href = this.getHref()
  @Provide() getUser = () => this.userInfo

  getLang() {
    return this.$attrs.language
  }

  getTranslate() {
    return this.__t
  }

  getHref() {
    return this.__href
  }

  beforeCreate(this: any) {
    const language = this.$attrs.language

    this.__t = translate(this.$attrs.locales)

    const getHref = (language: Data.Language) => (pathname: string, lang: Data.Language, baseLink?: string) => {
      lang = lang || language
      return href(pathname, lang as string, baseLink)
    }
    this.__href = getHref(language)
  }

  get componentName() {
    if (this.platform === 'mobile') {
      return 'MobileFooterV2'
    }

    return 'DesktopFooterV2'
  }
}
</script>
