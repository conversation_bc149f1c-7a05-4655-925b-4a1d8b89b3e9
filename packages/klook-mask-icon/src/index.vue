<template>
  <div
    :class="['klk-mask-icon',disabled ? 'disabled_icon' : '']"
    :style="{ width: iconBoxSize, height: iconBoxSize, padding: hotSize + 'px'}"
    :data-spm-module="`PrivacySwitch?ext=${JSON.stringify({ Current: maskStatus === 2 ? 'Off' : 'On' })}`"
    :data-spm-virtual-item="`__virtual`"
    v-galileo-click-tracker="{ spm: 'PrivacySwitch', componentName: 'klk-mask-icon' }"
    @click="maskStatusChange"
  >
    <klk-loading v-if="isLoading" />
    <template v-else>
      <IconViewOn v-if="disabled" theme="outline" :size="size" :fill="[$colorTextDisabled]" />
      <IconViewOff v-else-if="maskStatus === 2" theme="outline" :size="size" :fill="[$colorTextPrimary]" />
      <IconViewOn v-else theme="outline" :size="size" :fill="[$colorTextPrimary]" />
    </template>
  </div>
</template>

<script lang="ts">
import { Vue, Component, Prop } from 'vue-property-decorator';
import KlkToast from '@klook/klook-ui/lib/toast/index.js';
import KlkLoading from '@klook/klook-ui/lib/loading';
import IconViewOff from '@klook/klook-icons/lib/IconViewOff';
import IconViewOn from '@klook/klook-icons/lib/IconViewOn';
import { $colorTextPrimary, $colorTextDisabled } from '@klook/klook-ui/lib/utils/design-token-esm';

type ChangeStatusConfig = {
  setNextStatus: number;
  setCallbackHandle: (config: any) => Promise<any>
}

type SetStatusParams = {
  status: number;
  callback: (config: any) => Promise<any>
}

@Component({
  components: {
    KlkLoading,
    IconViewOff,
    IconViewOn
  }
})
export default class MaskIcon extends Vue {
  @Prop({ default: '' }) platform!: string;
  @Prop({ default: '' }) language!: string;
  @Prop({ default: false }) needTwoFa!: boolean; // 是否需要双因素验证
  @Prop({ default: '' }) twoFaAction!: string; // 双因素验证操作名称
  @Prop({ default: 24 }) size!: number;
  @Prop({ default: 4 }) hotSize!: number;
  @Prop({ default: false }) showErrorTip!: boolean;
  @Prop({ default: '' }) spmStr!: string;
  @Prop() inhouse!:any;
  @Prop({ default: false }) disabled!: boolean;
  @Prop({ default: () => Promise.resolve() }) onChange!: (config: any) => Promise<any>;

  maskStatus: number = 2; // 1  原文，2 密文。与后端状态机对齐
  isLock: boolean = false;
  isLoading: boolean = false;
  setLoadingTimer: any = null;

  get iconBoxSize() {
    if (!(isNaN(this.size) || isNaN(this.hotSize))) {
      return this.size + 2 * this.hotSize + 'px';
    }
    return '32px';
  }

  get $colorTextPrimary() {
    return $colorTextPrimary;
  }

  get $colorTextDisabled() {
    return $colorTextDisabled;
  }

  get realInhouse() {
    return this?.inhouse || this?.$inhouse || (typeof window !== 'undefined' && window?.inhouse) || null;
  }

  get state() {
    return this?.$store?.state || null
  }

  get realPlatform() {
    return this.platform || this.state?.klook?.platform || 'desktop'
  }

  get realLanguage() {
    return this.language || this.state?.klook?.language || 'zh-CN'
  }

  async twoFaVerification(cb: Function) {
    const { twoFaVerify } = await import('@klook/klook-traveller-login')
    twoFaVerify({
      platform: this.realPlatform,
      language: this.realLanguage,
      action: this.twoFaAction,
      verified: (verifyToken) => {
        cb(verifyToken)
      }
    })
  }

  async handleCallback(setCallbackHandle, status, checkResult) {
    if (setCallbackHandle) {
      await setCallbackHandle(checkResult);
    } else if (this.onChange) {
      await this.onChange(checkResult);
    }

    this.maskStatus = status;
    this.realInhouse?.track('custom', '.klk-mask-icon', {
      force: true,
      spm: `${this.spmStr ? this.spmStr + '.' : ''}PrivacySwitchSuccess`,
      Type: status === 2 ? 'TurnOff' : 'TurnOn'
    });
  }

  async maskStatusChange(setStatusConfig : ChangeStatusConfig) {
    if (this.disabled) {
      return;
    }
    const { setNextStatus, setCallbackHandle } = setStatusConfig || {};
    if (this.isLock) {
      return;
    }
    const nextStatus = setNextStatus !== undefined ? setNextStatus : this.maskStatus === 2 ? 1 : 2;

    try {
      this.isLock = true;
      this.clearLoadingTimer();
      this.setLoadingTimer = setTimeout(() => {
        this.isLoading = true;
      }, 180);
      const checkResult = { status: nextStatus, verifyToken: '' };
      if (nextStatus === 1 && this.needTwoFa) {
        // 第二次验证 双因素
        this.twoFaVerification(async (verifyToken) => {
          await this.handleCallback(setCallbackHandle, nextStatus, { ...checkResult, verifyToken })
        })
      } else {
        await this.handleCallback(setCallbackHandle, nextStatus, checkResult)
      }
    } catch (err: any) {
      this.realInhouse?.track('custom', '.klk-mask-icon', {
        force: true,
        spm: `${this.spmStr ? this.spmStr + '.' : ''}PrivacySwitchFail`,
        Type: nextStatus === 2 ? 'TurnOff' : 'TurnOn',
        error_code: err?.code || '',
        error_message: err?.message || ''
      });
      this.showErrorTip && err?.message && KlkToast(err?.message);
      console.warn(err);
    } finally {
      this.isLoading = false;
      this.isLock = false;
      this.clearLoadingTimer();
    }
  }

  async getCurrentStatus() {
    return await { status: this.maskStatus };
  }

  async setMaskStatus(config: SetStatusParams) {
    const { status, callback } = config;
    return await this.maskStatusChange({ setNextStatus: status, setCallbackHandle: callback });
  }

  clearLoadingTimer() {
    if (this.setLoadingTimer) {
      clearTimeout(this.setLoadingTimer);
      this.setLoadingTimer = null;
    }
  }

  beforeDestroy() {
    this.clearLoadingTimer();
  }
}
</script>
<style lang="scss" scoped>
.klk-mask-icon{
  position: relative;
  display: inline-flex;
  align-items: center;
}
.disabled_icon {
  cursor: not-allowed;
}
</style>
