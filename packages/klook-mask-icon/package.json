{"name": "@klook/klk-mask-icon", "version": "0.0.4", "description": "traveller mask icon", "author": "traveller platform", "homepage": "https://design.klook.io", "keywords": ["vue", "component", "mask-icon"], "main": "lib/index.js", "browser": "esm/index.js", "module": "esm/index.js", "typings": "types/index.d.ts", "files": ["dist", "lib", "esm", "src", "locales", "types"], "license": "UNLICENSED", "publishConfig": {"registry": "https://knpm.klook.io", "access": "public"}, "scripts": {"build": "rimraf lib esm dist && NODE_ENV=production rollup --config ./build/rollup.config.js", "build:dev": "NODE_ENV=production rollup --config ./build-dev/rollup.config.js && NODE_ENV=production PLATFORM=web rollup --config ./build-dev/rollup.css.config.js && NODE_ENV=production PLATFORM=mobile rollup --config ./build-dev/rollup.css.config.js", "watch": "rollup --config ./rollup.watch.config.js --watch", "lint": "NODE_ENV=production eslint --ext .js,.vue src", "test": "NODE_ENV=test jest -i --updateSnapshot", "test:coverage": "NODE_ENV=test jest -i --coverage --updateSnapshot", "prepush": "yarn run lint", "prepublishOnly": "bash prepublishOnly.sh", "commit": "npx git-cz", "commitmsg": "commitlint -E GIT_PARAMS"}, "browserslist": ["last 10 versions", "> 1%", "IE >= 10"], "devDependencies": {"@babel/core": "^7.17.9", "@babel/plugin-transform-runtime": "^7.17.0", "@babel/preset-env": "^7.16.11", "@klook/i18n": "^1.0.5", "@klook/klk-traveller-utils": "1.8.0", "@klook/klook-icons": "0.13.1", "@klook/klook-ui": "^1.33.3", "@klook/klook-traveller-login": "0.7.1", "@klook/transform-location-babel-plugin": "^1.0.2", "@nuxtjs/eslint-config-typescript": "1.0.0", "@rollup/plugin-json": "^4.1.0", "@types/jest": "^26.0.0", "@types/webpack-env": "^1.14.0", "@vue/test-utils": "^1.0.0-beta.32", "autoprefixer": "^9.8.6", "jest": "^25.5.4", "rimraf": "^3.0.0", "rollup": "^1.19.4", "rollup-plugin-babel": "^4.4.0", "rollup-plugin-buble": "^0.19.8", "rollup-plugin-clear": "^2.0.7", "rollup-plugin-commonjs": "^10.0.2", "rollup-plugin-copy": "^3.4.0", "rollup-plugin-delete": "^1.2.0", "rollup-plugin-node-resolve": "^5.2.0", "rollup-plugin-postcss": "^4.0.2", "rollup-plugin-replace": "^2.2.0", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-typescript2": "^0.31.2", "rollup-plugin-vue": "^5.1.9", "rollup-plugin-vue-inline-svg": "^1.1.2", "terser": "^4.1.3", "ts-jest": "^26.1.0", "ts-node": "^8.10.2", "typescript": "^4.6.3", "vue": "2.6.11", "vue-jest": "^3.0.4", "vue-property-decorator": "^8.3.0", "vue-template-compiler": "2.6.11"}, "peerDependencies": {"@klook/klook-ui": "^1.33.3", "vue": "2.6.11", "vue-property-decorator": "^8.3.0", "@klook/klk-traveller-utils": "1.8.0", "@klook/klook-icons": "0.13.1", "@klook/klook-traveller-login": "0.7.1"}, "dependencies": {}}