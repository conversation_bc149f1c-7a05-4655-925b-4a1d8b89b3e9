<template>
  <div class="responsive-card-item" v-bind="$attrs">
    <slot></slot>
  </div>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'

@Component
export default class CardSwiperItem extends Vue {}
</script>

<style lang="scss">
@import '../../styles/index.scss';
$card-gap: 20px;
$mobile-card-gap: 12px;

@mixin calculate_card_width_half($perView: 1, $card-gap: 10px, $firstItemWidthPercentage: 89.6) {
  width: calc(($firstItemWidthPercentage / $perView) * 1%);
  padding-left: calc($card-gap / 2);
  padding-right: calc($card-gap / 2);
}
@mixin calculate_card_width_full($perView: 1, $card-gap: 10px) {
  width: calc((100 / $perView) * 1%);
  padding-left: calc($card-gap / 2);
  padding-right: calc($card-gap / 2);
}

.responsive-card-item {
  flex: none;
  @include calculate_card_width_half(1, $mobile-card-gap);

  @include screen-size('md', 'lg') {
    @include calculate_card_width_full(3, $card-gap);
  }
  @include screen-size('xl') {
    @include calculate_card_width_full(3, $card-gap);
  }

  &.klk-col-sm-1-2 {
    @include screen-size('sm') {
      @include calculate_card_width_half(1, $mobile-card-gap);
    }
  }
  &.klk-col-sm-1-5 {
    @include screen-size('sm') {
      @include calculate_card_width_half(1, $mobile-card-gap, 60);
    }
  }
  &.klk-col-sm-2-2 {
    @include screen-size('sm') {
      @include calculate_card_width_half(1, $mobile-card-gap);
    }
  }
  &.klk-col-sm-2-5 {
    @include screen-size('sm') {
      @include calculate_card_width_half(2, $mobile-card-gap, 60);
    }
  }

  &.klk-col-md-1 {
    @include screen-size('md') {
      @include calculate_card_width_full(1, $card-gap);
    }
  }
  &.klk-col-md-2 {
    @include screen-size('md') {
      @include calculate_card_width_full(2, $card-gap);
    }
  }
  &.klk-col-md-3 {
    @include screen-size('md') {
      @include calculate_card_width_full(3, $card-gap);
    }
  }
  &.klk-col-md-4 {
    @include screen-size('md') {
      @include calculate_card_width_full(4, $card-gap);
    }
  }

  &.klk-col-lg-1 {
    @include screen-size('lg') {
      @include calculate_card_width_full(1, $card-gap);
    }
  }
  &.klk-col-lg-2 {
    @include screen-size('lg') {
      @include calculate_card_width_full(2, $card-gap);
    }
  }
  &.klk-col-lg-3 {
    @include screen-size('lg') {
      @include calculate_card_width_full(3, $card-gap);
    }
  }
  &.klk-col-lg-4 {
    @include screen-size('lg') {
      @include calculate_card_width_full(4, $card-gap);
    }
  }

  &.klk-col-xl-1 {
    @include screen-size('xl') {
      @include calculate_card_width_full(1, $card-gap);
    }
  }
  &.klk-col-xl-2 {
    @include screen-size('xl') {
      @include calculate_card_width_full(2, $card-gap);
    }
  }
  &.klk-col-xl-3 {
    @include screen-size('xl') {
      @include calculate_card_width_full(3, $card-gap);
    }
  }
  &.klk-col-xl-4 {
    @include screen-size('xl') {
      @include calculate_card_width_full(4, $card-gap);
    }
  }
}
</style>
