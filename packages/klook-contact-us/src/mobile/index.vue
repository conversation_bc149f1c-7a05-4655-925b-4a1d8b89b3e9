<template>
  <div>
    <klk-bottom-sheet v-bind="bottomSheetProps" :data-spm-page="`CmptContactUs?oid=booking_${bookingRefNo}`"
      data-spm-virtual-item="__virtual" class="klook-contact-us-wrap klook-contact-us-mobile" :visible="visible"
      @close="onVisibleChange(false)" @open="onVisibleChange(true)">

      <div class="klook-traveller-contact-us" v-if="customContacts && customContacts.contact_list.length"
        :data-spm-module="`ContactCustom`" data-spm-virtual-item="__virtual">
        <div class="klook-traveller-contact-us-title">{{ customContacts.title }}</div>
        <contact-item v-for="(item, index) in customContacts.contact_list" :item="item" @click.native="contactClick(item)"
          :key="index"></contact-item>
      </div>

      <div class="klook-traveller-contact-us" v-if="partnerContactList && partnerContactList.length"
        :data-spm-module="`ContactOperator`" data-spm-virtual-item="__virtual">
        <div class="klook-traveller-contact-us-title">{{ __t('109011') }}</div>
        <contact-item v-for="(item, index) in partnerContactList" :item="item" @click.native="contactClick(item)"
          :key="index"></contact-item>
      </div>

      <div class="klook-traveller-contact-us" v-if="klookContactList && klookContactList.length"
        :data-spm-module="`ContactKlook`" data-spm-virtual-item="__virtual">
        <div class="klook-traveller-contact-us-title">{{ __t('109012') }}</div>
        <contact-item v-for="(item, index) in klookContactList" :item="item" @click.native="contactClick(item)"
          :key="index"></contact-item>
      </div>
    </klk-bottom-sheet>

    <klk-modal :open.sync="showWechat" closable class="wechat-modal" :ok-label="__t('109150')"
      @on-close="showWechat = false" @on-cancel="showWechat = false" @on-confirm="openWechat">
      <div class="wechat-content">
        <div class="logo">
          <logo-svg></logo-svg>
        </div>

        <div class="title"> {{ wechatName }}
          <span @click="copy(wechatName)">{{ __t('109148') }}</span>
        </div>
        <div class="desc">{{ wechatDesc }}</div>
      </div>
    </klk-modal>
  </div>
</template>

<script lang="ts">
import {
  Vue,
  Component,
  Prop,
  Watch
} from 'vue-property-decorator'
import { Icon as KlkIcon } from '@klook/klook-ui';
import { BottomSheet as KlkBottomSheet } from '@klook/klook-ui';
import Modal from '@klook/klook-ui/lib/modal'
import { IconCopy } from '@klook/klook-icons';
import LogoSvg from '../svg/logo.vue';
import Base from '../base';
import contactItem from '../components/contact-item.vue';
import '@klook/klook-ui/lib/modal/style'

@Component({
  name: "KlookContactUsMobile",
  components: {
    KlkIcon,
    KlkBottomSheet,
    KlkModal: Modal,
    LogoSvg,
    IconCopy,
    contactItem
  }
})
export default class KlookContactUs extends Base {
  @Prop({
    default: false
  }) visible!: boolean;

  @Prop({
    default: false
  }) isKlookApp: boolean;

  @Prop()
  partnerContactList!: Array<any>

  @Prop()
  klookContactList!: Array<any>

  @Prop()
  customContacts!: Record<string, any>

  @Prop()
  bookingRefNo!: string;

  @Prop({
    default: () => { }
  })
  bottomSheetProps!: Record<string, any>

  showWechat = false;

  wechatName = "KLOOK度假玩乐精选";
  wechatDesc = "截图保存后扫一扫，打开您的专属客服，享更多福利"

  contactClick(item: any) {
    this.$emit('contact-item-click', item);
    if (item.type === 'weChat') {
      return this.showWechat = true
    }
    if (item.type === 'phone' && item.deeplink) {
      return window.location.href = item.deeplink;
    }
    if (item.deeplink) {
      if (this.isKlookApp) {
        window.location.href = item.deeplink;
      } else {
        window.open(item.deeplink);
      }
    }
  }

  openWechat() {
    this.copy(this.wechatName);
    location.href = 'weixin://'
  }

  copy(text: string) {
    if (navigator.clipboard) {
      // clipboard api 复制
      navigator.clipboard.writeText(text);
    } else {
      var textarea = document.createElement('textarea');
      document.body.appendChild(textarea);
      // 隐藏此输入框
      textarea.style.position = 'fixed';
      textarea.style.clip = 'rect(0 0 0 0)';
      textarea.style.top = '10px';
      // 赋值
      textarea.value = text;
      // 选中
      textarea.select();
      // 复制
      document.execCommand('copy', true);
      // 移除输入框
      document.body.removeChild(textarea);
    }

    this?.$toast(this.__t('109149'));
  }

  onVisibleChange(val: boolean) {
    this.$emit('visibleChange', val)
  }
}
</script>

<style lang="scss">
.klook-contact-us-mobile {
  .klook-traveller-contact-us {
    margin-top: 12px;
  }
}

.wechat-modal {
  .wechat-content {
    text-align: center;

    .logo {
      margin-top: 16px;
    }

    .title {
      margin: 16px 0;
      font-size: 16px;
      font-weight: 600;
      color: #212121;

      span {
        cursor: pointer;
        padding-left: 16px;
        text-decoration: underline;
      }
    }

    .desc {
      color: #757575;
      font-size: 14px;
      margin-bottom: 16px;
    }
  }
}</style>
