<template>
  <div class="klook-traveller-contact-us-desktop" :data-spm-page="`CmptContactUs?oid=booking_${bookingRefNo}`">
    <div class="klook-traveller-contact-us" v-if="customContacts && customContacts.contact_list.length"
      :data-spm-module="`ContactCustom`" data-spm-virtual-item="__virtual">
      <div class="klook-traveller-contact-us-title">{{ customContacts.title }}</div>
      <contact-item v-for="(item, index) in customContacts.contact_list" :item="item" @click.native="contactClick(item)"
        :key="index"></contact-item>
    </div>
    
    <div class="klook-traveller-contact-us" v-if="partnerContactList && partnerContactList.length"
      :data-spm-module="`ContactOperator`" data-spm-virtual-item="__virtual">
      <div class="klook-traveller-contact-us-title">{{ __t('109011') }}</div>
      <contact-item v-for="(item, index) in partnerContactList" :item="item" @click.native="contactClick(item)"
        :key="index"></contact-item>
    </div>

    <div class="klook-traveller-contact-us" v-if="klookContactList && klookContactList.length"
      :data-spm-module="`ContactKlook`" data-spm-virtual-item="__virtual">
      <div class="klook-traveller-contact-us-title">{{ __t('109012') }}</div>
      <contact-item v-for="(item, index) in klookContactList" :item="item" @click.native="contactClick(item)"
        :key="index"></contact-item>
    </div>
  </div>
</template>

<script lang="ts">
import {
  Vue,
  Component,
  Prop,
  Watch
} from 'vue-property-decorator'
import { Icon as KlkIcon } from '@klook/klook-ui';
import { Drawer as KlkDrawer } from '@klook/klook-ui';
import KlkPoptip from '@klook/klook-ui/lib/poptip'
import LogoSvg from '../svg/logo.vue';
import Base from '../base';
import contactItem from '../components/contact-item.vue';

@Component({
  name: "KlookContactUsDesktop",
  components: {
    KlkIcon,
    KlkDrawer,
    KlkPoptip,
    LogoSvg,
    contactItem
  }
})
export default class KlookContactUs extends Base {
  @Prop({
    default: false
  }) visible!: boolean;

  @Prop()
  partnerContactList!: Array<any>

  @Prop()
  klookContactList!: Array<any>

  @Prop()
  customContacts!: Record<string, any>

  @Prop()
  bookingRefNo!: string;

  contactClick(item: any) {
    this.$emit('contact-item-click', item);
    if (item.type === 'phone' && item.deeplink) {
      return window.location.href = item.deeplink;
    }
    if (item.deeplink) {
      window.open(item.deeplink);
    }
  }

  close() {
    this.$emit('update:visible', false);
  }

  @Watch('visible')
  onVisibleChange(val: boolean) {
    this.$emit('update:visible', val);
  }
}
</script>

<style lang="scss">
.klook-traveller-contact-us-desktop {

  .klook-traveller-contact-us {
    margin-bottom: 16px;
    padding: 20px;
  }

  .klook-traveller-contact-us-title {
    font-size: 20px;
  }
}
</style>
