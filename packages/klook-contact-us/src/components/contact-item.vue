<template>
  <div :class="['contact-us-container', `contact-us-container-${providedPlatform}`]"
    :style="{ 'cursor': item.deeplink ? 'pointer' : '' }">
    <div class="contact-us-left">
      <img :src="item.icon" alt="">
    </div>
    <div class="contact-us-main">
      <div class="contact-us-title">{{ item.title }}
        <template v-if="providedPlatform === 'mobile'">
          <IconCopy v-if="item.copyable" class="copy" @click.native.stop.prevent="copy(item.title)" theme="outline"
            size="20" fill="#212121" />
        </template>

        <template v-else>
          <klk-poptip :content="__t('109148')" placement="top" dark>
            <IconCopy v-if="item.copyable" class="copy" @click.native.stop.prevent="copy(item.title)" theme="outline"
              size="20" fill="#212121" />
          </klk-poptip>
        </template>
      </div>
      <div class="contact-us-detail">{{ item.detail }}</div>
    </div>
    <div class="contact-us-right" v-if="item.deeplink || item.type === 'weChat'">
      <template v-if="providedPlatform === 'desktop' && item.type === 'weChat'">
        <LogoSvg></LogoSvg>
      </template>

      <template v-else="providedPlatform === 'mobile'">
        <IconNext theme="outline" size="16" fill="#757575" />
      </template>
    </div>
  </div>
</template>

<script lang="ts">
import {
  Component,
  Prop,
  Inject
} from 'vue-property-decorator'
import KlkIcon from '@klook/klook-ui/lib/icon';
import KlkPoptip from '@klook/klook-ui/lib/poptip'
import { IconCopy, IconNext } from '@klook/klook-icons';
import LogoSvg from '../svg/logo.vue';
import Base from '../base';

@Component({
  name: "KlookContactUsItem",
  components: {
    KlkIcon,
    IconCopy,
    IconNext,
    KlkPoptip,
    LogoSvg
  }
})
export default class KlookContactUsItem extends Base {
  @Prop()
  item!: any;

  @Inject() providedPlatform!: string;

  copy(text: string) {
    if (navigator.clipboard) {
      // clipboard api 复制
      navigator.clipboard.writeText(text);
    } else {
      var textarea = document.createElement('textarea');
      document.body.appendChild(textarea);
      // 隐藏此输入框
      textarea.style.position = 'fixed';
      textarea.style.clip = 'rect(0 0 0 0)';
      textarea.style.top = '10px';
      // 赋值
      textarea.value = text;
      // 选中
      textarea.select();
      // 复制
      document.execCommand('copy', true);
      // 移除输入框
      document.body.removeChild(textarea);
    }

    this?.$toast(this.__t('109149'));
  }
}
</script>

<style lang="scss"></style>
