<template>
  <component :is="contactUsComp" v-bind="$attrs" v-on="$listeners" :visible="visible" @visibleChange="onVisibleChange" :platform="platform">
  </component>
</template>

<script lang="ts">
import {
  Vue,
  Component,
  Prop,
  Watch,
  Provide
} from 'vue-property-decorator'

import mobileComp from './mobile/index.vue';
import desktopComp from './desktop/index.vue';
import { genText } from "./locale/index";
import messages from "../locales/index.js";

@Component({
  name: "KlookContactUs",
  components: {
    mobileComp,
    desktopComp
  }
})
export default class KlookContactUs extends Vue {
  @Prop({
    default: false
  }) visible!: boolean;

  @Prop({
    default: 'mobile'
  }) platform!: 'mobile' | 'desktop';

  @Provide() __t: any = this.getTranslate()
  @Provide() providedPlatform = this.platform;

  getTranslate() {
    return this.__t;
  }

  onVisibleChange(val: boolean) {
    this.$emit('update:visible', val)
  }

  beforeCreate(this: any) {
    const locales = messages as any;
    const lang = this.$attrs.language || 'en';
    this.__t = locales[lang]
      ? genText(locales[lang])
      : genText(locales["en"]);
  }

  get contactUsComp() {
    return this.platform === 'mobile' ? 'mobileComp' : 'desktopComp'
  }
}
</script>

<style lang="scss">
@import '~@klook/klook-ui/dist/klook-ui.css';
$primaryColor: #F5F5F5;

.klook-contact-us-wrap {

  .klk-bottom-sheet-inner,
  .klk-bottom-sheet-header,
  .klk-drawer-content {
    background: $primaryColor;
  }

  .klk-drawer-content {
    padding: 0px 16px 0;
    width: 40%;
    min-width: 420px;
    max-width: 750px;

    .close-btn {
      cursor: pointer;
      margin: 20px 0;
    }
  }
}

.klook-traveller-contact-us {
  background-color: #fff;
  padding: 16px;
  border-radius: 16px;
  margin-bottom: 12px;

  &-title {
    font-size: 16px;
    color: #212121;
    font-weight: 600;
    margin-bottom: 8px;
  }

  .contact-us-container {
    cursor: default;
    display: flex;
    align-items: center;
    border-radius: 6px;

    &:hover {
      text-decoration: none;
      color: inherit;
    }

    .contact-us-left {

      img {
        width: 24px;
        height: 24px;
      }
    }

    .contact-us-main {
      flex: 1;
      padding: 0 12px;
      margin: 8px 0;

      .contact-us-title {
        display: flex;
        margin-bottom: 4px;
        font-size: 16px;
        color: #212121;
        font-weight: 400;
      }

      .contact-us-detail {
        font-size: 14px;
        white-space: pre-wrap;
        color: #757575;
      }

      .copy {
        cursor: pointer;
        vertical-align: middle;
        text-decoration: underline;
        padding-left: 6px;
      }
    }

    .contact-us-right {
      line-height: 1;
      .klk-icon {
        vertical-align: middle;
      }
    }
  }

  &-desktop {

    .contact-us-container {
      .contact-us-main {
        margin: 8px 0;
      }
    }
  }
}
</style>
