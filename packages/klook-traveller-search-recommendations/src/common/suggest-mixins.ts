import { Component, Prop, Vue } from 'vue-property-decorator'
import transformImageUrl from '@klook/klk-traveller-utils/lib/transformImageUrl'

@Component
export default class SuggestMixins extends Vue {
  @Prop() webp!: number;
  declare $store: any;

  get realWebp() {
    if (this.webp !== undefined) {
      return this.webp
    }
    return this.$store?.state?.klook?.webp || 0
  }

  formatPicUrl(url: string, width: number, height: number) {
    return transformImageUrl(url, { width, height, webp: !!this.realWebp })
  }

  formatWebpPicUrl(url: string) {
    return transformImageUrl(url)
  }
}
