<template>
  <div v-if="cardData" class="activity-list-card">
    <div
      v-if="cardData.icon_url"
      v-lazy:background-image.container="formatPicUrl(cardData.icon_url, 80, 80)"
      class="activity-img"
    />
    <div class="content">
      <div class="title-content">
        <div class="title" v-html="cardData.title" />
        <img v-if="cardData.title_image" class="icon" :src="formatWebpPicUrl(cardData.title_image)">
        <div v-if="cardData.from_price" class="price">{{ cardData.from_price }}</div>
      </div>
      <div class="desc-content">
        <div class="desc">{{ cardData.sub_title }}</div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Prop } from 'vue-property-decorator';
import SuggestMixins from '../common/suggest-mixins'

@Component({
  components: {
  }
})
export default class ActivityListCard extends SuggestMixins {
  @Prop({ required: true, default: () => null }) cardData!: any
}
</script>

<style lang="scss" scoped>
.activity-list-card {
  display: flex;
  align-items: center;

  .activity-img {
    background-repeat: no-repeat;
    background-size: contain;
    background-position: center center;
    flex-shrink: 0;
    width: 40px;
    height: 40px;
    margin-right: 12px;
    border-radius: $radius-m;
  }

  .content {
    width: calc(100% - 52px);
  }

  .title-content {
    display: flex;
    align-items: center;
  }

  .title {
    @include font-body-m-regular();

    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    text-overflow: ellipsis;
    overflow: hidden;
  }

  .icon {
    flex-shrink: 0;
    margin-left: 8px;
    height: 24px;
  }

  .price {
    @include font-paragraph-s-bold();

    flex-shrink: 0;
    color: $color-text-primary;
    margin-left: 16px;
  }

  .desc-content {
    display: flex;
    align-items: center;
  }

  .desc {
    @include font-body-s-regular();

    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    text-overflow: ellipsis;
    overflow: hidden;
    color: $color-text-placeholder;
    margin-right: auto;
  }
}
</style>
