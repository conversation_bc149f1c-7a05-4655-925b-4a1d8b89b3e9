<template>
  <div v-if="cardData" class="general-list-card">
    <div
      v-if="cardData.icon_url"
      v-lazy:background-image.container="formatPicUrl(cardData.icon_url, 48, 48)"
      class="icon"
    />
    <div class="content">
      <div class="title" v-html="cardData.title" />
      <div class="desc">{{ cardData.sub_title }}</div>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Prop } from 'vue-property-decorator';
import SuggestMixins from '../common/suggest-mixins'

@Component({
  components: {
  }
})
export default class GeneralListCard extends SuggestMixins {
  @Prop({ required: true, default: () => null }) cardData!: any
}
</script>

<style lang="scss" scoped>
.general-list-card {
  display: flex;
  align-items: center;

  .icon {
    background-repeat: no-repeat;
    background-size: contain;
    background-position: center center;
    flex-shrink: 0;
    width: 24px;
    height: 24px;
    margin: 8px 20px 8px 8px;
  }

  .content {
    display: flex;
    align-items: baseline;
    flex-wrap: wrap;
    gap: 12px;
  }

  .title {
    @include font-body-m-regular();

    flex: 1 1 auto;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    text-overflow: ellipsis;
    overflow: hidden;
  }

  .desc {
    @include font-body-s-regular();

    color: $color-text-placeholder;
  }
}
</style>
