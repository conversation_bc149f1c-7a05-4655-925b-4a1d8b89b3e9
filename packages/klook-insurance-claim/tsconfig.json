{"compilerOptions": {"target": "es6", "module": "esnext", "outDir": "./dist", "strict": true, "jsx": "preserve", "importHelpers": true, "moduleResolution": "node", "esModuleInterop": true, "allowSyntheticDefaultImports": true, "experimentalDecorators": true, "allowJs": true, "baseUrl": ".", "types": ["webpack-env", "jest"], "paths": {"@/*": ["./src/*"], "~": ["/"]}, "lib": ["esnext", "dom", "dom.iterable", "scripthost"]}, "include": ["./*.ts", "src/**/*.ts", "src/**/*.tsx", "src/**/*.vue", "src/index.js", "src/index.ts"], "exclude": ["node_modules", "unpackage"]}