import replace from 'rollup-plugin-replace';
import typescript from 'rollup-plugin-typescript2';
import vue from 'rollup-plugin-vue';
import nodeResolve from 'rollup-plugin-node-resolve';

import babel from 'rollup-plugin-babel';
import postcss from 'rollup-plugin-postcss';
import commonjs from '@rollup/plugin-commonjs';
import pkg from './package.json';

const { name, version, author } = require('./package.json');

const banner = `/**
  * v${ version }
  * (c) ${ new Date().getFullYear() } ${ author }
  */`;
const input = [
  './src/index.ts',
];
const configs = {
  esm: {
    dir: 'dist/esm',
    format: 'esm',
    target: 'es2015',
    env: 'production',
    genDts: true,
  },
  cjs: {
    dir: 'dist/cjs',
    format: 'cjs',
    target: 'es2015',
    isSSR: true
  }
};

// const externals = [
//   'vue-property-decorator',
//   'vue',
//   'dayjs',
//   'lodash',
//   '@klook/klook-ui',
//   '@klook/klook-ui/lib/utils/design-token-esm',
//   '@klook/klook-traveller-login',
//   '@klook/klook-contact-us'
// ];

const genTsPlugin = configOpts =>
  typescript({
    useTsconfigDeclarationDir: true,
    tsconfigOverride: {
      compilerOptions: {
        target: configOpts.target,
        declaration: configOpts.genDts,
      },
    },
    abortOnError: false,
  });

const genPlugins = configOpts => {
  const plugins = [
    nodeResolve({
      extensions: ['.mjs', '.js', '.jsx', '.vue', '.tsx']
    }),
    commonjs({
      include: /node_modules/,
    }),
  ];

  if (configOpts.env) {
    plugins.push(
      replace({
        'process.env.NODE_ENV': JSON.stringify(configOpts.env),
      }),
    );
  }
  plugins.push(
    replace({
      'process.env.MODULE_FORMAT': JSON.stringify(configOpts.format),
    }),
  );
  if (configOpts.plugins && configOpts.plugins.pre) {
    plugins.push(...configOpts.plugins.pre);
  }
  plugins.push(genTsPlugin(configOpts));

  plugins.push(
    babel({
      runtimeHelpers: true,
      exclude: 'node_modules/**',
      extensions: ['.js', '.jsx', '.ts', '.tsx'],
      presets: ['@vue/babel-preset-jsx']
    })
  );


  plugins.push(
    vue({
      css: false,
      normalizer: '~vue-runtime-helpers/dist/normalize-component.js',
      template: {
        isProduction: false,
        optimizeSSR: configOpts.isSSR
      },
      style: {
        postcssPlugins: [require('autoprefixer')()],
        preprocessStyles: true,
        preprocessOptions: {
          scss: {
            data: '@import "../klook-ui/src/styles/token/index.scss";',
          },
        },
      },
    }),
  );

  // 不必提取css
  plugins.push(
    postcss({
      extract: false,
      plugins: [require('autoprefixer')()],
    }),
  );

  if (configOpts.plugins && configOpts.plugins.post) {
    plugins.push(...configOpts.plugins.post);
  }
  return plugins;
};

const genConfig = configOpts => ({
  input,
  output: {
    banner,
    dir: configOpts.dir,
    format: configOpts.format,
    name: name,
    sourcemap: false,
    exports: 'named',
    globals: configOpts.globals,
  },
  external(id) {
    // cjs: 不外置scss/css，其它外置
    // esm: 所有的都外置
    // cjs-klook-ui: 都不外置
    // esm-klook-ui: 都不外置
    if (
      configOpts.widthKlookUI &&
      (id.includes('@klook/') || id.includes('vue-') || id.includes('swiper'))
    ) {
      return false;
    }

    if (
      configOpts.format === 'cjs' && id.includes('@klook/') &&
      (id.endsWith('.scss') || id.endsWith('.css'))
    ) {
      return false;
    }

    return (
      Object.keys(pkg.peerDependencies).includes(id.split('/')[0]) ||
      id.includes('@klook/')
    );
  },
  plugins: genPlugins(configOpts),
});

const genAllConfigs = configs => Object.keys(configs).map(key => genConfig(configs[key]));

export default genAllConfigs(configs);
