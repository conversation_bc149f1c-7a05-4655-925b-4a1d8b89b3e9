import Vue from 'vue';

export class GoodsClaim extends Vue {
  /** Install component into Vue */
  static install (vue: typeof Vue): void
}

export class BundleSelect extends Vue {
  /** Install component into Vue */
  static install (vue: typeof Vue): void
}
export class ClaimDetail extends Vue {
  /** Install component into Vue */
  static install (vue: typeof Vue): void
}

export class ClaimList extends Vue {
  /** Install component into Vue */
  static install (vue: typeof Vue): void
}

export class ClaimSuccess extends Vue {
  /** Install component into Vue */
  static install (vue: typeof Vue): void
}

export class Faq extends Vue {
  /** Install component into Vue */
  static install (vue: typeof Vue): void
}

export class UploadPhoto extends Vue {
  /** Install component into Vue */
  static install (vue: typeof Vue): void
}

export default class KlookInsuranceClaim extends Vue {
  /** Install component into Vue */
  static install (vue: typeof Vue): void
}

export interface OrderInfo {
  ins_order_no: string;
  claim_version:number;
  data_source:string;
  insurance_type:number;
  addon_booking_ref_no:string;
  goods_code:string;
  ticket_title:string;
  ticket_date:string;
  booking_no:string
}