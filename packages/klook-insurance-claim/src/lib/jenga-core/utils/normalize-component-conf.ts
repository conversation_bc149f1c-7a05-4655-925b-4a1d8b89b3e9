/**
 * Created by <PERSON><PERSON> on 2021/4/23 16:07.
 */

import { SectionTypes } from '../types'

export default function normalizeComponentConf(userComponentConf: SectionTypes.ComponentConf): SectionTypes.ResolvedComponentConf {
  return Object.entries(userComponentConf).reduce((preVal, [platform, platformConf]) => {
    return {
      ...preVal,
      ...Object.entries(platformConf).reduce((preComponents, [componentName, componentConfRef]) => {
        const componentConf = componentConfRef as SectionTypes.ComponentItemObj

        return {
          ...preComponents,
          [`${platform}${componentName}`]: {
            viewComponent: componentConf.viewComponent || componentConf,
            skeletonComponent: componentConf.skeletonComponent,
            wrapperClass: Array.isArray(componentConf.wrapperClass) ? componentConf.wrapperClass.reduce((pre, cur) => {
              return {
                ...pre,
                [cur]: true
              }
            }, {}) : (
              componentConf.wrapperClass === String(componentConf.wrapperClass) ? {
                [componentConf.wrapperClass]: true
              } : componentConf.wrapperClass
            )
          }
        }
      }, {})
    }
  }, {})
}
