<template>
  <div
    :style="{
      display: 'block',
      height: `${containerData.height}px`,
    }"
  ></div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';

@Component({})
export default class Divider extends Vue {
  @Prop({ type: String, default: 'desktop' }) platform!: string;
  @Prop({ type: Object, default: () => ({}) }) containerData!: any;
}
</script>
