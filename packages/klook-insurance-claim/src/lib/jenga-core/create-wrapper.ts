import { Component, Vue, Prop } from 'vue-property-decorator'
import { VNode, CreateElement } from 'vue'

import LazyComponent from './components/lazy-component.vue'
import DefaultSkeleton from './components/default-skeleton.vue'

import genWrapperList from './components/gen-wrapper-list'

import normalizeComponentConf from './utils/normalize-component-conf'

import { SectionTypes, CreateOptions } from './types'

import { CONTAINER_MAP } from './config'

import './style.scss'

// 判断 plain object 或者 function包含cid属性
// https://github.com/vuejs/vue/blob/dev/src/core/vdom/create-component.js#L130
const isSyncComponent = (Ctor: any): boolean => (typeof Ctor !== 'function') || Ctor.cid !== undefined

export default function createWrapper(createOptions: CreateOptions) {
  const { componentConf, platform = 'desktop' } = createOptions

  const resolvedComponentConf: SectionTypes.ResolvedComponentConf = normalizeComponentConf(componentConf)

  @Component
  class WrapperItem extends Vue {
    @Prop({ type: Object, default: () => ({}) }) itemData !: any
    @Prop({ type: Object, default: () => ({}) }) extraData !: any
    @Prop({ type: Boolean, default: false }) forceLoading !: boolean

    isLoadedScript = false
    hidden = false

    get isAsync() {
      return this.itemData?.body.content?.load_type === 'async'
    }

    get curComponentName() {
      const { handler, data_type } = this.itemData?.body.content || {}
      return handler ? `${handler}${data_type}` : ''
    }

    get curComponent() {
      const componentName = this.curComponentName
      const ViewCtor = (resolvedComponentConf[componentName]?.viewComponent as any)?.default || resolvedComponentConf[componentName]?.viewComponent
      const SkeletonCtor = resolvedComponentConf[componentName]?.skeletonComponent || DefaultSkeleton
      const wrapperClass = resolvedComponentConf[componentName]?.wrapperClass

      return {
        SkeletonCtor,
        ViewCtor,
        wrapperClass
      }
    }

    get moduleData() {
      return this.itemData?.body.content?.data
    }

    get containerData() {
      return this.itemData?.data
    }

    // inhouse 埋点数据
    get ihtData() {
      return {
        spmPrefix: [
          this.itemData.meta.track.iht_spm,
          this.itemData.meta.type,
          '',
          this.itemData.body.content.data_type
        ].join(':')
      }
    }

    get sectionName() {
      return this.itemData.meta.name
    }

    hide() {
      this.hidden = true
    }

    render(h: CreateElement): VNode | null {
      // 加载异常
      // 详细的加载状态 ？
      if (this.hidden) {
        return null
      }

      // 容器
      const containerType = this.itemData?.meta.type
      const curContainer = CONTAINER_MAP[containerType]

      // 组件
      const { ViewCtor, SkeletonCtor, wrapperClass } = this.curComponent

      // 异常数据处理， dev 才做判断 ?
      if (!curContainer) {
        // eslint-disable-next-line no-console
        console.warn(`Unknown container：${containerType}`)
        return null
      }
      if ((!ViewCtor && curContainer.hasChild)) {
        // eslint-disable-next-line no-console
        console.warn(`Unknown component：${this.curComponentName}`)
        return null
      }
      // 同步楼层 moduleData 数据为空
      if (!this.isAsync && this.moduleData === null) {
        // eslint-disable-next-line no-console
        console.warn(`【${this.curComponentName}】Empty data：【${this.moduleData}】`)
        return null
      }

      // item attrs
      const wrapperItemAttrs: SectionTypes.WrapperItemAttrs = {
        className: {
          ...wrapperClass,
          wrapper__item: true,
          [`wrapper__item-${this.curComponentName || containerType}`]: true
        },
        id: `s-${this.sectionName}`
      }

      // view component
      const getViewVNode = (attrs: SectionTypes.WrapperItemAttrs = {}) => h(curContainer.component, {
        attrs: {
          platform,
          containerData: this.containerData,
          id: attrs.id
        },
        class: {
          ...attrs.className,
          wrapper__container: true,
          [`wrapper__container-${containerType}`]: true
        },
        key: 'view'
      }, curContainer.hasChild ? [
        h(ViewCtor, {
          attrs: {
            platform,
            extraData: this.extraData,
            ihtData: this.ihtData,
            moduleData: this.moduleData,
            hideSection: () => {
              this.hide()
            }
          },
          on: {
            onHide: () => {
              this.hide()
            }
          }
        })
      ] : [])

      // this.scopedSlots.skeleton ?
      const getSkeletonVNode = (slot = 'skeleton') => h(SkeletonCtor, {
        slot,
        key: 'skeleton',
        props: {
          ...(platform === 'mobile' ? {
            itemNum: 2,
            rows: 2
          } : {
            itemNum: 4,
            rows: 1
          })
        },
        class: {
          wrapper__skeleton: true
        }
      })

      // 强制loading 状态
      if (this.forceLoading) {
        return h('div', {
          attrs: {
            id: wrapperItemAttrs.id
          },
          class: wrapperItemAttrs.className
        }, [getSkeletonVNode('default')])
      }

      // 异步组件
      if (this.isAsync) {
        return h(LazyComponent, {
          attrs: {
            id: wrapperItemAttrs.id
          },
          class: wrapperItemAttrs.className,
          props: {
            beforeLoad: async () => {
              try {
                const [data] = await Promise.all([
                  // 加载数据和预加载异步组件
                  this.$axios.$get(this.itemData.body.content.src).then(({ success, result }) => {
                    if (success) {
                      return result
                    }
                    throw new Error('Error!')
                  }),

                  // 异步组件预加载资源
                  ...(isSyncComponent(ViewCtor)) ? [] : [(ViewCtor as any)()]
                ])

                this.isLoadedScript = true
                // 请求数据合并回data
                this.itemData.body.content.data = data

                // 异步楼层 null
                if (data === null) {
                  this.hide()
                }
              } catch (e) {
                // 加载异常
                this.hide()
              }
            }
          }
        }, [
          ...this.isLoadedScript ? [getViewVNode()] : [], // 控制需要显示时才加载
          getSkeletonVNode()
        ])
      }

      // 同步直接渲染
      return getViewVNode(wrapperItemAttrs)
    }
  }

  return {
    WrapperItem,
    WrapperList: genWrapperList(WrapperItem, createOptions)
  }
}
