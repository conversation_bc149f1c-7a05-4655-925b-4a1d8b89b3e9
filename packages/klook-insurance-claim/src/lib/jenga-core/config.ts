/**
 * Created by Liu.Jun on 2021/4/25 11:59.
 */

import { SectionTypes } from './types';

import AnyContainer from './container/any.vue';
import CommonContainer from './container/common.vue';
import TabContainer from './container/tab.vue';
import DividerContainer from './container/divider.vue';

// 支持4种容器类型
export const CONTAINER_MAP: SectionTypes.ContainerConf = {
  any: {
    component: AnyContainer,
    hasChild: true,
  },
  common: {
    component: CommonContainer,
    hasChild: true,
  },
  tab: {
    component: TabContainer,
    hasChild: true,
  },
  divider: {
    component: DividerContainer,
    hasChild: false,
  },
};
