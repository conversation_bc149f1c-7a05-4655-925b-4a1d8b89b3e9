import { Component, Prop, Vue } from 'vue-property-decorator'
import { CreateElement, VNode, VueConstructor } from 'vue'
import { CreateOptions } from '~/components/traveller/lazy-section/types'

export default (WrapperItem: VueConstructor, createOptions: CreateOptions) => {
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const { componentConf, ...sectionParams } = createOptions

  @Component({
    inheritAttrs: false,
    provide: {
      sectionParams
    }
  })
  class WrapperList extends Vue {
    @Prop({ type: Array, default: () => ([]) }) listData!: any[]

    render(h: CreateElement): VNode {
      return h('div', {
        staticClass: 'wrapper__list-container'
      }, this.listData.map((item, index) => h(WrapperItem, {
        key: index,
        attrs: {
          ...this.$attrs,
          itemData: item
        }
      })
      ))
    }
  }

  return WrapperList
}
