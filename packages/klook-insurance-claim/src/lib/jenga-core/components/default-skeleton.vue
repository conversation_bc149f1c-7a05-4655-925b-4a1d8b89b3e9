<template>
  <klk-skeleton
    class="default-skeleton-wrapper"
    animate
  >
    <klk-skeleton-block :height="40"></klk-skeleton-block>
    <div
      v-for="(row, i) in rows"
      :key="i"
      class="default-skeleton-list"
    >
      <div
        v-for="(item, index) in items"
        :key="index"
        class="default-skeleton-item"
      >
        <klk-skeleton-block height="154"></klk-skeleton-block>
        <klk-skeleton-block></klk-skeleton-block>
        <div class="default-skeleton-item-line">
          <klk-skeleton-block></klk-skeleton-block>
          <klk-skeleton-block></klk-skeleton-block>
        </div>
      </div>
    </div>
  </klk-skeleton>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator'

@Component
export default class DefaultSkeleton extends Vue {
  @Prop({ type: Number, default: 4 }) itemNum!: number
  @Prop({ type: Number, default: 1 }) rows!: number

  get items() {
    return new Array(this.itemNum).fill(null).map((_v, i) => i)
  }
}

</script>

<style lang="scss" scoped>
.default-skeleton-wrapper {
  padding-top: 72px;
}

.default-skeleton-list {
  display: flex;

  +.default-skeleton-list {
    margin-top: 20px;
  }
}

.default-skeleton-item {
  flex: 1;

  & + .default-skeleton-item {
    margin-left: 2%;
  }
}

.default-skeleton-item-line {
  width: 70%;
}
</style>
