/*
 * Copyright By Klook
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

// PC理赔toast需要展示在抽屉的中间,所以需要将toast挂载到抽屉内
export function toast(that, options, parentEleId) {
  const defaultParentEleId = 'claim-drawer';
  const claimDrawer = document.getElementById(parentEleId || defaultParentEleId);
  if (claimDrawer) {
    that.$toast(options, claimDrawer);
    return;
  }
  that.$toast(options);
}

const selectText = (editableEl, selectionStart, selectionEnd) => {
  const isIOS = navigator.userAgent.match(/ipad|iPod|iphone/i);
  if (isIOS) {
    const range = document.createRange();
    range.selectNodeContents(editableEl);

    const selection = window.getSelection(); // current text selection
    selection.removeAllRanges();
    selection.addRange(range);
    editableEl.setSelectionRange(selectionStart, selectionEnd);
  } else {
    editableEl.select();
  }
};

export const copyToClipboard = value => {
  return new Promise(resolve => {
    const el = document.createElement('textarea'); // temporary element
    el.value = value;

    el.style.position = 'absolute';
    el.style.left = '-9999px';
    el.readOnly = true; // avoid iOs keyboard opening
    el.contentEditable = 'true';

    document.body.appendChild(el);

    selectText(el, 0, value.length);

    if (document.execCommand('copy')) {
      document.body.removeChild(el);
      resolve(true);
    } else {
      resolve(false);
    }
  });
};
