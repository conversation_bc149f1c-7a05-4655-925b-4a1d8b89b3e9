export const CLAIM_VERSION_ENM = {
  old: 1,
  new: 2,
};

export const defaultClaimVersion = CLAIM_VERSION_ENM.old;

// 至少上传三张图片
export const minUploadImages = 3;
// 最多上传10张图片
export const maxUploadImages = 10;
// 图片上传接口
export const uploadApi = '/v1/insuranceclaimapisrv/outer/file/upload';

// 理赔接口
export const oldClaimApi = '/v1/order/insurance/claims';
export const newClaimApi = '/v1/insuranceclaimapisrv/outer/claim/submit';
export const CLAIM_API_ENUM = {
  [CLAIM_VERSION_ENM.old]: oldClaimApi,
  [CLAIM_VERSION_ENM.new]: newClaimApi,
};

// unit 接口
export const oldUnitApi = '/v1/usrcsrv/insurance/claims/unit';
export const newUnitApi = '/v1/insuranceclaimapisrv/outer/claim/unit';
export const UNIT_API_ENUM = {
  [CLAIM_VERSION_ENM.old]: oldUnitApi,
  [CLAIM_VERSION_ENM.new]: newUnitApi,
};

export const unitGroupApi = '/v1/insuranceclaimapisrv/outer/claim/unit/group';

export const MOBILE_ROUTE_ENUM = {
  ClaimDetailPage: '/insurance/claim/claim-detail/',
  ClaimListPage: '/insurance/claim/claim-list/',
  ClaimFaqPage: '/insurance/claim/claimFaq/',
  ClaimSuccessPage: '/insurance/claim/claim-success/',
  ClaimUploadPhotoPage: '/insurance/claim/upload-photo/',
};

export const BOOKING_TYPE_ENUM = {
  perBooking: 1,
  perUnitType: 2,
  perUnit: 3,
};
