<template>
  <div
    :data-spm-page="`Insurance_ClaimSubmissionComplete?trg=manual&oid=booking_${insuranceOrderNo}&ext=${pageTrackExt}`"
    class="goods-claim-submission claim-success-page-track"
  >
    <ClaimFormHeader
      :is-in-app="isInApp"
      :platform="platform"
      :iconBack="true"
      title=" "
      @click="onConfirm"
    ></ClaimFormHeader>
    <div class="claim-success" :class="isDesktop ? 'claim-success-desktop' : 'claim-success-mobile'">
      <img
        class="success-img"
        src="https://res.klook.com/image/upload/v1723631481/ued/Insurance/Success.png"
      />
      <div class="submited-text">{{ $t('176358') }}</div>
      <div class="submited-email">{{ feedbackInfo.email_text }}</div>
      <klk-button class="confirm-btn" type="primary" @click="onConfirm">{{ $t('176360') }}</klk-button>
      <klk-steps direction="vertical">
        <klk-step
          :title="feedbackInfo.claim_time_title_text"
          :content="feedbackInfo.claim_time_content_text"
          status="finish"
        ></klk-step>
        <klk-step
          :title="feedbackInfo.processing_title_text"
          :content="feedbackInfo.processing_content_text"
          status="process"
        ></klk-step>
      </klk-steps>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import ClaimFormHeader from '../header.vue';

@Component({ components: { ClaimFormHeader } })
export default class ClaimSuccess extends Vue {
  @Prop({ type: String, default: 'desktop' }) platform!: string;

  @Prop({ type: Boolean, default: false }) isInApp?: boolean;

  @Prop({ type: Object, default: () => ({}) }) feedbackInfo!: Record<string, any>;

  get insuranceOrderNo() {
    return this.feedbackInfo.ins_order_no;
  }

  get pageTrackExt() {
    const str = JSON.stringify({
      MainBookingID: this.feedbackInfo.main_booking_ref_no,
      PlanCode: this.feedbackInfo.plan_code,
      ActivityID: this.feedbackInfo.activity_id,
    });
    return str
  }

  get isDesktop() {
    return this.platform === 'desktop';
  }

  mounted() {
    setTimeout(() => {
      window?.tracker?.inhouse?.track('pageview', '.claim-success-page-track')
    })
  }

  onConfirm() {
    this.$emit('confirm');
  }
}
</script>

<style lang="scss" scoped>
.claim-success {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 32px 20px;
  .success-img {
    width: 160px;
    height: 160px;
  }
  .submited-text {
    font-size: 20px;
    font-weight: 600;
    line-height: 26.4px;
    margin: 24px auto 12px;
  }
  .submited-email {
    font-size: 16px;
    font-weight: 400;
    line-height: 24px;
    margin-bottom: 24px;
    text-align: center;
    width: 90%;
    white-space: normal;
    word-break: break-word;
  }
  .confirm-btn {
    margin-bottom: 44px;
  }
}
.claim-success-desktop {
  padding: 32px 40px;
}
</style>
