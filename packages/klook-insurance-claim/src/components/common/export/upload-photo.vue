<template>
  <div class="upload-photo-box" :class="isDesktop ? 'upload-photo-box-desktop' : 'upload-photo-box-mobile'">
    <div class="upload-photo">
      <div class="upload-photo_box">
        <img class="example-img" :src="uploadInfo.pic_url" />
        <div class="tips-box" v-html="uploadInfo.guide_desc" />
      </div>
    </div>
    <div v-if="allowUpload" class="upload-btn-box">
      <div class="line" />
      <klk-button
        class="upload-btn"
        type="primary"
        :loading="uploading"
        @click="$emit('upload')"
      >
        {{ $t('3004') }}
      </klk-button>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch } from 'vue-property-decorator';
import ClaimFormHeader from '../header.vue';
import { uploadApi, maxUploadImages } from '../../../constant';
import { toast } from '../../../utils';

@Component({ components: { ClaimFormHeader } })
export default class UploadPhoto extends Vue {
  @Prop({ type: String, default: 'desktop' }) platform!: string;

  @Prop({ type: Boolean, default: true }) allowUpload?: boolean;

  @Prop({
    type: Boolean,
    default: false,
  })
  isInApp?: boolean;

  maxUploadImages = maxUploadImages;

  @Prop({
    type: Object,
    default: () => ({
      pic_url: 'https://res.klook.com/image/upload/v1723631481/ued/Insurance/Success.png',
    }),
  })
  uploadInfo!: Record<string, any>;

  uploading: boolean = false;

  uploadApi = uploadApi;

  claimData = {};

  images = [];

  get isDesktop() {
    return this.platform === 'desktop';
  }
}
</script>

<style lang="scss" scoped>
.upload-photo-box {
  height: 100vh;
  position: relative;

  .upload-btn-box {
    position: fixed;
    right: 0;
    bottom: 0;
    margin: 48px 0 0px;
    background: #fff;
    padding-bottom: 16px;

    .line {
      width: 100%;
      height: 1px;
      background-color: #e6e6e6;
      margin-bottom: 16px;
    }
  }

  .upload-btn {
    width: calc(680px - 40px);
    margin: 0 20px;

    .klk-upload-box {
      position: absolute;
      left: 0;
      top: 0;
      width: 100% !important;
      height: 100% !important;

      ::v-deep .klk-upload {
        width: 100% !important;
        height: 100% !important;
      }

      ::v-deep .klk-upload-btn {
        width: 100% !important;
        height: 100% !important;
        opacity: 0 !important;
      }
      ::v-deep .klk-upload-item {
        opacity: 0 !important;
      }
    }
  }
}

.upload-photo {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  &_box {
    padding: 20px 32px;
  }
  .tips-title {
    font-size: 14px;
    font-weight: 600;
    line-height: 26.4px;
    margin: 24px auto 12px;
  }
  .tips-content {
    font-size: 14px;
    font-weight: 400;
    line-height: 24px;
    margin-bottom: 24px;
  }

  .example-img {
    margin: 20px 0 16px;
  }
}
.upload-photo-box-desktop {
  .example-img {
    width: 326px;
  }
  .tips-box {
    width: 326px;
    white-space: pre-line;
  }
  .upload-photo {
    height: calc(100vh - 156px);
  }
}
.upload-photo-box-mobile {
  height: auto;
  margin-bottom: 48px;

  .example-img {
    width: 100%;
  }
  .tips-box {
    white-space: pre-line;
  }

  .upload-btn {
    width: calc(100vw - 40px);
  }
  .upload-photo_box {
    padding: 20px 12px;
  }
}
.claim-drawer-form {
  z-index: 1999 !important;
  ::v-deep .klk-toast {
    right: 290px !important;
    transform: translate(0, -50%) !important;
    left: unset !important;
  }
}
</style>
