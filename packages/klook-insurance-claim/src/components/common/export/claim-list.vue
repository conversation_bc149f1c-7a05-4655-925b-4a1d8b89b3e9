<template>
  <div
    :data-spm-page="`Insurance_ClaimResultList?trg=manual&oid=booking_${insOrderNo}&ext=${JSON.stringify(trackCommonExt)}`"
    class="claim-detail claim-list-page-track"
  >
    <ClaimFormHeader
      :is-in-app="isInApp"
      :title="$t('176761')"
      :platform="platform"
      :show-bottom-border="!isDesktop"
      @click="$emit('header-click')"
    >
      <div @click="showFaq">
        <IconQuestion theme="outline" size="20" class="close_icon" :fill="colorTextPrimary" />
      </div>
    </ClaimFormHeader>
    <div class="claim-list">
      <div
        v-for="(item, index) in claimList"
        :key="index"
        class="claim-item"
        :data-spm-module="`ClaimCard?oid=claim_${item.application_no}&ext=${JSON.stringify(trackCommonExt)}`"
        :data-spm-virtual-item="`__virtual?oid=claim_${item.application_no}&ext=${JSON.stringify(trackCommonExt)}`"
        @click="toDetail(item)"
      >
        <div class="unit">{{ item.sku_text }}</div>
        <div class="submit-time">{{ item.create_time_utc }}</div>
        <div class="claim-status">
          <klk-steps v-if="item.status_text_list.length > 0" :current="1" direction="vertical">
            <klk-step
              v-for="(statusItem, index) in item.status_text_list"
              :key="index"
              :status="statusMap[statusItem.web_status] || statusMap[1]"
              :title="statusItem.application_status_text"
            >
              <div slot="content">
                <div class="update-info update-time">{{ statusItem.log_time }}</div>
                <div v-html="statusItem.application_status_ext_text" class="update-info update-desc" />
                <div class="detail-btn">{{ $t('176295') }}</div>
              </div>
            </klk-step>
          </klk-steps>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import { IconCheckCircle, IconQuestion } from '@klook/klook-icons';
import { $colorTextPrimary } from '@klook/klook-ui/lib/utils/design-token-esm';
import ClaimFormHeader from '../header.vue';
import { MOBILE_ROUTE_ENUM } from '../../../constant';
import { toast } from '../../../utils';

@Component({
  components: {
    IconCheckCircle,
    ClaimFormHeader,
    IconQuestion,
  },
})
export default class ClaimList extends Vue {
  @Prop({ type: Object, default: null }) klook?: Object;

  @Prop({ type: String, default: 'desktop' }) platform!: string;

  @Prop({ type: Boolean, default: false }) isInApp?: boolean;

  @Prop({ type: String, default: '' }) insOrderNo!: string;

  @Prop({ type: Number, default: 0 }) insuranceType!: number;

  colorTextPrimary = $colorTextPrimary;

  claimList: Record<string, any>[] = [];

  statusMap: Record<string, string> = {
    1: 'wait',
    2: 'process',
    3: 'finish',
  };

  get isDesktop() {
    return this.platform === 'desktop';
  }

  get _klook() {
    return this.klook || (window as any).__KLOOK__.state.klook;
  }

  get languagePath() {
    return this._klook.language === 'en' ? '' : `/${this._klook.language}`;
  }

  get trackCommonExt() {
    const item = this.claimList && this.claimList.length ? this.claimList[0] : {};
    return {
      MainBookingID: item.main_booking_ref_no,
      PlanCode: item.plan_code,
      ActivityID: item.activity_id,
    }
  }

  trackPage() {
    setTimeout(() => {
      window?.tracker?.inhouse?.track('pageview', '.claim-list-page-track');
    });
  }

  async getClaimList() {
    if (!this.insOrderNo) {
      return;
    }
    try {
      const res = await this.$axios.get('/v1/insuranceclaimapisrv/outer/claim/getClaimDetailList', {
        params: {
          ins_order_no: this.insOrderNo,
        },
      });
      const data = res.data || {};
      if (data.success && data.result) {
        this.claimList = data.result;
      } else {
        const { message } = data?.error || {};
        toast(
          this,
          {
            message,
            duration: 3000,
          },
          'insurance_claims_dialog-detail',
        );
      }
      this.trackPage()
    } catch (message) {
      this.trackPage()
      toast(
        this,
        {
          message,
          duration: 3000,
        },
        'insurance_claims_dialog-detail',
      );
    }
  }

  mounted() {
    this.getClaimList();
  }

  showFaq() {
    if (this.isDesktop) {
      this.$emit('show-faq');
      return;
    }
    window.location.href = `${window.location.origin}${this.languagePath}${MOBILE_ROUTE_ENUM.ClaimFaqPage}?insuranceType=${this.insuranceType}`;
  }

  toDetail(detail: Record<string, any>) {
    if (this.isDesktop) {
      this.$emit('to-detail', detail);
      return;
    }
    window.location.href = `${window.location.origin}${this.languagePath}${MOBILE_ROUTE_ENUM.ClaimDetailPage}?applicationNo=${detail.application_no}&insuranceType=${this.insuranceType}`;
  }
}
</script>

<style lang="scss" scoped>
.claim-list {
  padding: 12px;
  background-color: #eeeeee;
  height: calc(100vh - 64px);
  overflow: auto;

  .claim-item {
    width: 100%;
    text-align: left;
    border-radius: 16px;
    background-color: #fff;
    padding: 16px;
    margin-bottom: 12px;
    .unit {
      font-size: 16px;
      font-weight: 600;
      line-height: 24px;
    }
    .submit-time {
      font-size: 14px;
      font-weight: 400;
      line-height: 21px;
      border-bottom: 1px solid #e6e6e6;
      margin-bottom: 12px;
      padding-bottom: 12px;
    }
    .claim-status {
      display: flex;
      justify-content: flex-start;
      align-items: flex-start;
    }
    .status-icon {
      margin-right: 8px;
      font-size: 24px;
    }
    .status {
      font-size: 16px;
      font-weight: 400;
      line-height: 24px;
      height: 24px;
    }
    .update-info {
      font-size: 14px;
      font-weight: 400;
      line-height: 21px;
      margin: 4px 0;
    }
    .detail-btn {
      text-decoration: underline;
      font-size: 14px;
      font-weight: 400;
      cursor: pointer;
    }
  }
}
</style>
