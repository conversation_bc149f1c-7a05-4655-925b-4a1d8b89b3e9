<template>
  <div>
    <ClaimFormHeader :is-in-app="isInApp" :platform="platform" title=" " @click="onClose"></ClaimFormHeader>

    <div class="bundle-select" :class="isDesktop ? 'bundle-select-desktop' : 'bundle-select-mobile'">
      <div class="bundle-select_title">
        {{ $t('199970') }}
      </div>
      <!-- <div class="bundle-select_desc">
        {{ $t('199971') }}
      </div> -->
      <klk-radio-group v-model="gotoActivity" class="bundle-select_select">
        <div @click="gotoActivity = 'SatG'">
          <klk-radio :group-value="'SatG'" class="bundle-select_selectItem">
            {{ $t('199973') }}
          </klk-radio>
        </div>
        <div @click="gotoActivity = 'NSR'">
          <klk-radio :group-value="'NSR'" class="bundle-select_selectItem">
            {{ $t('199972') }}
          </klk-radio>
        </div>
      </klk-radio-group>
      <klk-button
        type="primary"
        reverse
        block
        class="bundle-select_button"
        :disabled="!gotoActivity"
        @click="handleNextClick"
      >
        {{ $t('200734') }}
      </klk-button>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import ClaimFormHeader from '../header.vue';

@Component({ components: { ClaimFormHeader } })
export default class BundleSelect extends Vue {
  gotoActivity: string = ''; // 'SatG' 'NSR'

  @Prop({ type: String, default: 'desktop' }) platform!: string;

  @Prop({ type: Boolean, default: false }) isInApp?: boolean;

  step: number = 1; // 1: 选择阶段，2：填表阶段

  get isDesktop() {
    return this.platform === 'desktop';
  }

  onClose() {
    this.$emit('close');
  }

  handleNextClick() {
    this.$emit('confirm', this.gotoActivity);
  }
}
</script>

<style lang="scss" scoped>
.bundle-select {
  //display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  &_title {
    @include font-heading-xs-v2;
    color: $color-text-primary;
  }
  &_desc {
    @include font-body-s-regular;
    color: $color-text-primary;
    margin-top: 4px;
  }
  &_select {
    display: flex;
    flex-direction: column;
  }
  &_selectItem {
    border: 1px solid $color-border-normal;
    border-radius: 12px;
    padding: 16px 12px;
    width: 100%;
  }
  &_button {
    margin-top: 10px;
  }
}
.bundle-select-desktop {
  padding: 32px 80px;
}
.bundle-select-mobile {
  height: calc(100vh - 287px);
  padding: 20px;
}
</style>
