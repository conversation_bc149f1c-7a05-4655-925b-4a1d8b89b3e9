<template>
  <div>
    <ClaimFormHeader
      :is-in-app="isInApp"
      :title="$t('437')"
      :platform="platform"
      :show-bottom-border="!isDesktop"
      @click="$emit('header-click')"
    >
      <!-- <ContactUs :platform="platform" :language="_klook.language" /> -->
    </ClaimFormHeader>
    <div v-if="terms_condition" class="tnc">
      <div
        v-for="(tacItem, index) in terms_condition"
        :key="index"
        class="tac"
        :class="{ title: tacItem.type == 'sub_title' }"
        v-html="renderMarkDownHtml(tacItem.content)"
      />
    </div>

    <div class="faq" :class="isDesktop ? 'faq-desktop' : 'faq-mobile'">
      <template v-if="!hideFAQ">
        <dl
          v-for="(qa, index) in faqs"
          :key="index"
          class="claim_faq_dl"
          :class="{ show: qa.show }"
          @click="togglefaq(index)"
        >
          <dt>
            <span>{{ qa.question }}</span>
            <klk-icon type="icon_navigation_chevron_down_xs" class="faq_icon" :size="16" color="#666" />
          </dt>
          <!-- <dd>{{ qa.answer }}</dd> -->
          <dd><klk-markdown :content="qa.answer"></klk-markdown></dd>
        </dl>
      </template>
    </div>
  </div>
</template>

<script lang="ts">
// 引入图标
import { Component, Vue, Prop } from 'vue-property-decorator';
import { IconCustomerService } from '@klook/klook-icons';
import { $colorTextPrimary } from '@klook/klook-ui/lib/utils/design-token-esm';
import SectionWrapper from '../section-wrapper.vue';
import ClaimFormHeader from '../header.vue';
import ContactUs from '../ContactUs.vue';
import cloneDeep from 'lodash/cloneDeep';

@Component({
  components: {
    SectionWrapper,
    ClaimFormHeader,
    IconCustomerService,
    ContactUs,
  },
})
export default class FAQ extends Vue {
  @Prop({
    type: Array,
    default: () => [],
  })
  terms_condition?: Array<Record<string, string | number>>;

  @Prop({
    type: Object,
    default: null,
  })
  klook?: Object;

  @Prop({
    type: String,
    default: '',
  })
  langPath!: string;

  @Prop({
    type: Boolean,
    default: false,
  })
  hideFAQ?: boolean;

  @Prop({
    type: Boolean,
    default: false,
  })
  isInApp?: boolean;

  @Prop({
    type: String,
    default: 'desktop',
  })
  platform!: string;

  @Prop({
    type: Number,
    default: 0,
  })
  insuranceType!: number;

  faqs: any = [];

  colorTextPrimary = $colorTextPrimary;

  async getFaqs() {
    if (!this.insuranceType) {
      return;
    }
    const res = await this.$axios.get('/v1/insuranceclaimapisrv/outer/faq/getFaqs', {
      params: {
        insurance_type: this.insuranceType,
      },
    });
    const data = res.data || {};
    if (data.success && data.result) {
      this.faqs = data.result.faq_info_list;
    }
  }

  get _klook() {
    return this.klook || (window as any).__KLOOK__.state.klook;
  }

  get isDesktop() {
    return this.platform === 'desktop';
  }

  mounted() {
    this.getFaqs();
  }

  renderMarkDownHtml(content) {
    return content;
  }

  togglefaq(index) {
    this.faqs.forEach((x, i) => {
      if (i === index) {
        x.show === true ? (x.show = false) : (x.show = true);
        return;
      }
      x.show = false;
    });
    Vue.set(this, 'faqs', cloneDeep(this.faqs));
  }
}
</script>

<style lang="scss" scoped>
.tnc {
  margin: 0 auto;

  > div {
    padding-left: 12px;
    font-size: $fontSize-body-s;
    line-height: 1.5;
    color: $color-text-secondary;
    position: relative;

    &:not(:last-child) {
      margin-bottom: 4px;
    }

    &::before {
      content: '';
      width: 4px;
      height: 4px;
      background-color: $color-common-black;
      position: absolute;
      left: 0;
      top: 7px;
      /* stylelint-disable */
      border-radius: 50%;
      /* stylelint-enable */
    }

    a {
      color: $color-info;
    }

    &.title {
      padding-left: 0;
      margin-bottom: 12px;
      color: $color-text-primary;

      &::before {
        display: none;
      }
    }
  }
}

.faq {
  box-sizing: border-box;
  margin: 0 auto;
  padding: 0 20px;

  &::after {
    content: '';
    display: block;
    clear: both;
    height: 0;
    overflow: hidden;
  }

  h3 {
    font-size: $fontSize-body-m;
    font-weight: $fontWeight-semibold;
    line-height: 1.5;
    color: $color-text-primary;
    margin-bottom: 12px;
  }

  dl {
    border-bottom: solid 1px $color-border-normal;

    .faq_icon {
      position: absolute;
      right: 0;
      top: 50%;
      transform: translate(0, -50%);
    }

    s &:first-of-type {
      border-top: solid 1px $color-border-normal;
    }

    &.show {
      dt {
        .faq_icon {
          transform: translate(0, -50%) rotate(180deg);
        }
      }

      dd {
        display: block;
      }
    }

    dt {
      font-size: 16px;
      line-height: 1.3;
      color: $color-text-primary;
      padding: 20px 40px 20px 0;
      font-weight: 600;
      position: relative;

      svg {
        position: absolute;
        right: 0;
        top: 50%;
        transform: translate(0, -50%);
      }
    }

    dd {
      display: none;
      padding: 16px;
      line-height: 1.5;
      font-size: $fontSize-body-s;
      margin: 0;
      background-color: $color-bg-widget-darker-3;
      margin-bottom: 16px;
    }
  }

  .viewmore {
    margin: 24px 20px;
    display: block;
    float: left;
    font-size: $fontSize-body-s;
    line-height: 16px;
    font-weight: $fontWeight-bold;
    color: $color-info;

    span {
      vertical-align: top;
    }
  }
}
.faq-mobile {

}
</style>
