<template>
  <div
    :data-spm-page="`Insurance_ClaimSubmission?trg=manual&oid=booking_${insuranceOrderNo}&ext=${pageTrackExt}`"
    class="goods-claim-submission"
  >
    <div v-if="!loading">
      <component
        :is="componentMap[this.inpathType]"
        :platform="platform"
        :order-info="{ ...orderInfo, ...unitInfo }"
        :is-in-app="isInApp"
        :claim-title="claimTitle"
        @success="success"
        @header-click="$emit('header-click')"
        @show-faq="$emit('show-faq')"
        @back="$emit('back')"
      />
    </div>
    <div class="loading-container" v-if="loading">
      <klk-loading></klk-loading>
    </div>
  </div>
</template>

<script lang="ts">
// 引入图标
import { Component, Vue, Prop, Watch } from 'vue-property-decorator';
import { OrderInfo } from '../../../../types';
import SatG from '../satg.vue';
import NSR from '../nsr.vue';
import { CLAIM_VERSION_ENM, newUnitApi } from '../../../constant';
import { toast } from '../../../utils';

@Component
export default class GoodsClaim extends Vue {
  @Prop({ type: String, default: 'desktop' }) platform!: string;

  @Prop({ type: Boolean, default: false }) isInApp?: boolean;

  @Prop({ type: Object, default: { claim_version: CLAIM_VERSION_ENM.old, goods_code: '' } })
  orderInfo!: OrderInfo;

  @Prop({ type: String, default: 0 }) bundleInpathType!: String;

  @Prop({ type: Object, default: () => ({}) }) uploadData!: Record<string, any>;

  loading = false;

  inpathType = 'NSR';

  unitInfo: any = {};

  pageTrackExt: string = ''; // page埋点ext参数

  claimTitle = ''

  @Watch('orderInfo', { immediate: true })
  queryUnitList() {
    if (this.orderInfo.ins_order_no) {
      this.fetchUnitList();
    }
  }

  mounted() {
    // pageView埋点依赖接口数据，延迟一点上报，2s后不管接口是否成功都上报
    setTimeout(() => {
      this.trackPage();
    }, 2000);
  }

  success(feedbackInfo: Record<string, any>) {
    this.$emit('success', feedbackInfo);
  }

  componentMap = {
    SatG: SatG,
    NSR: NSR,
    Flex: NSR,
  };

  get insuranceOrderNo() {
    return this.orderInfo?.ins_order_no
  }

  trackPage() {
    this.pageTrackExt = JSON.stringify({
      MainBookingID: this.unitInfo?.main_booking_ref_no,
      PlanCode: this.unitInfo?.plan_code,
      ActivityID: this.unitInfo?.activity_id,
    })
    setTimeout(() => {
      window?.tracker?.inhouse?.track('pageview', '.goods-claim-submission')
    });
  }

  async handleLogin() {
    if (this.$cookies && this.$store && this.$store && this.$href) {
      const { loginWithSDK } = await import('@klook/klook-traveller-login');
      loginWithSDK({
        aid: this.$cookies.get('aid'),
        isMP: this.$store.state.klook?.platformMp !== '',
        platform: this.$store.state.klook?.platform,
        language: this.$store.state.klook?.language,
        currency: this.$store.state.klook?.currency,
        market: this.$store.state.klook?.market,
        bizName: 'Platform',
        purpose: 'insurance_claim',
        cancel: () => {},
        success: () => {
          this.$store.dispatch('auth/getProfile');
          window.location.reload();
        },
      }).then((supportLogin: boolean) => {
        if (!supportLogin) {
          window.location.href = this.$href(
            `/signin/?signin_jump=${encodeURIComponent(window.location.href)}`,
          );
        }
      });
    } else {
      console.error('need this.$cookies && this.klook && this.$store && this.$href');
    }
  }

  fetchUnitList() {
    this.loading = true;
    this.$axios
      .get(newUnitApi, { params: { ins_order_no: this.orderInfo.ins_order_no, bundle_inpath_type: this.bundleInpathType } })
      .then(async resp => {
        this.loading = false;
        if (!resp?.data?.success) {
          if (resp?.data?.error?.code === '4001') {
            this.handleLogin();
            return;
          }
          toast(
            this,
            {
              message: resp?.data?.error?.message,
              duration: 3000,
            },
            'insurance_claims_dialog-form',
          );
        }

        const { inpath_type, bundle_customized_title } = resp.data.result || {};
        this.inpathType = inpath_type;
        this.claimTitle = bundle_customized_title
        this.unitInfo = resp.data.result;
      })
      .catch((message: string) => {
        toast(
          this,
          {
            message,
            duration: 3000,
          },
          'insurance_claims_dialog-form',
        );
      })
      .finally(() => {
        this.loading = false;
      });
  }
}
</script>

<style lang="scss" scoped>
.loading-container {
  height: 100vh;
  position: relative;
}
</style>
