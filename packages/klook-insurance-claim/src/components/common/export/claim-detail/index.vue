<template>
  <div :data-spm-page="`Insurance_ClaimResultDetial?trg=manual&oid=claim_${claimId}&ext=${pageTrackExt}`" class="claim-detail-page-track">
    <ClaimFormHeader
      :is-in-app="isInApp"
      :title="$t('176761')"
      :iconBack="true"
      :platform="platform"
      :show-bottom-border="!isDesktop"
      @click="$emit('header-click')"
    >
      <div @click="showFaq">
        <IconQuestion theme="outline" size="20" class="close_icon" :fill="colorTextPrimary" />
      </div>
    </ClaimFormHeader>
    <div class="claim-detail">
      <client-only>
        <WrapperList
          :list-data="sectionsData"
          :extra-data="{
            wrapperItemData: getWrapperItemData,
            claimDetail,
            applicationNo,
          }"
        ></WrapperList>
      </client-only>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Prop } from 'vue-property-decorator';
import { IconQuestion } from '@klook/klook-icons';
import { $colorTextPrimary } from '@klook/klook-ui/lib/utils/design-token-esm';
import ClaimFormHeader from '../../header.vue';
import { MOBILE_ROUTE_ENUM } from '../../../../constant';
import createWrapper from '../../../../lib/jenga-core/create-wrapper';
import Base from './base';
import componentConf from './component-conf';
import { EventBus } from '../../../../lib/jenga-core/utils/event-bus';
import { toast } from '../../../../utils';

const { WrapperList } = createWrapper({
  componentConf,
  platform: 'desktop',
});

@Component({
  components: {
    WrapperList,
    ClaimFormHeader,
    IconQuestion,
  },
})
export default class ClaimDetail extends Base {
  @Prop({ type: Object, default: null }) klook?: Object;

  @Prop({ type: String, default: 'desktop' }) platform!: string;

  @Prop({ type: Boolean, default: false }) isInApp?: boolean;

  @Prop({ type: String, default: '' }) applicationNo!: string;

  @Prop({ type: Number, default: 0 }) insuranceType!: number;

  get isDesktop() {
    return this.platform === 'desktop';
  }

  get _klook() {
    return this.klook || (window as any).__KLOOK__.state.klook;
  }

  customerServiceVisible = false;

  colorTextPrimary = $colorTextPrimary;

  claimDetail: Record<string, any> = {
    status_text_list: [],
    claim_files: [],
  };

  claimList = [1, 2];

  claimId: string = '';

  pageTrackExt: any = {};

  trackPage() {
    this.claimId = this.claimDetail?.application_no;
    this.pageTrackExt = JSON.stringify({
      BookingID: this.claimDetail?.ins_order_no,
      MainBookingID: this.claimDetail?.main_booking_ref_no,
      PlanCode: this.claimDetail?.plan_code,
      ActivityID: this.claimDetail?.activity_id,
    })
    setTimeout(() => {
      window?.tracker?.inhouse?.track('pageview', '.claim-detail-page-track');
    });
  }

  async getClaimDetail() {
    if (!this.applicationNo) {
      return;
    }
    try {
      const res = await this.$axios.get('/v1/insuranceclaimapisrv/outer/claim/getOneClaimDetail', {
        params: {
          application_no: this.applicationNo,
        },
      });
      const data = res.data || {};
      if (data.success && data.result) {
        this.claimDetail = data.result;
      } else {
        const { message } = data?.error || {};
        toast(
          this,
          {
            message,
            duration: 3000,
          },
          'insurance_claims_dialog-detail',
        );
      }
      this.trackPage()
    } catch (message) {
      this.trackPage()
      toast(
        this,
        {
          message,
          duration: 3000,
        },
        'insurance_claims_dialog-detail',
      );
    }
  }

  showFaq() {
    if (this.isDesktop) {
      this.$emit('show-faq');
      return;
    }
    const languagePath = this._klook.language === 'en' ? '' : `/${this._klook.language}`;
    window.location.href = `${window.location.origin}${languagePath}${MOBILE_ROUTE_ENUM.ClaimFaqPage}?insuranceType=${this.insuranceType}`;
  }

  mounted() {
    this.getClaimDetail();
  }

  beforeCreate() {
    EventBus.$on('withdraw-success', () => {
      // ios局部刷新step组件样式有问题
      if (navigator.userAgent.match(/ipad|iPod|iphone/i)) {
        window.location.reload();
      } else {
        this.getClaimDetail();
      }
    });
  }

  handleContactClick() {}
}
</script>

<style lang="scss" scoped>
.claim-detail {
  background-color: #eeeeee;
  min-height: calc(100vh - 64px);
  padding-bottom: 20px;

  .claim-process {
    margin-top: 1px;
    width: 100%;
    text-align: left;
    background-color: #fff;
    padding: 20px;
    text-align: left;
    .goods-code {
      font-size: 20px;
      font-weight: 600;
      line-height: 24px;
    }
    .activity {
      font-size: 16px;
      font-weight: 400;
      line-height: 24px;
      margin: 4px 0;
      word-break: break-word;
    }
    .copy {
      margin: 4px 0 16px;
      text-decoration: underline;
      cursor: pointer;
    }
    .cancel-btn {
      margin-top: 12px;
      font-size: 14px;
      font-weight: 600;
      height: 36px;
      padding: 0 16px;
    }
    .step-content {
      display: flex;
      align-items: center;
    }
    ::v-deep .klk-poptip-reference {
      display: flex;
      align-items: center;
    }
    .infomation-icon {
      margin-left: 4px;
    }
    .price-poptip {
      ::v-deep .klk-poptip-popper-inner {
        max-height: unset !important;
        padding: 16px 20px 24px !important;
      }
    }
    .refund-detail-poptip-content {
      border-radius: 16px;
      padding: 16px, 20px, 24px, 20px;
      min-width: 320px;
      .refund-detail-item {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        font-weight: 400;
        font-size: 14px;
        &_title {
          height: 24px;
        }
        &_content {
          height: 24px;
        }
        &_right-box {
          text-align: right;
        }
        &_right-desc {
          color: #757575;
        }
        margin: 8px 0;
      }
      .refund-detail-sum {
        font-weight: 600;
        font-size: 16px;
        margin-top: 0;
      }
      .refund-detail-info {
        background-color: #f5f5f5;
        margin-top: 10px;
        border-radius: 12px;
        padding: 12px;
        position: relative;

        &_triangle {
          position: absolute;
          right: 35px;
          top: -16px;
          width: 0;
          height: 0;
          border-bottom: 8px solid #f5f5f5;
          border-top: 8px solid transparent;
          border-left: 8px solid transparent;
          border-right: 8px solid transparent;
        }
      }
    }
  }
  .info-box {
    padding: 24px 20px;
    background-color: #fff;
    border-radius: 16px;
    font-weight: 600;
    margin: 12px;
  }
  .info-title {
    margin-bottom: 12px;
    font-size: 20px;
  }
  .info-subtitle {
    font-size: 16px;
  }
  .info-email {
    font-size: 16px;
    font-weight: 400;
  }
  .claim-reason {
    .info-subtitle {
      font-size: 14px;
      color: #8a8a8a;
    }
    .reason-detail {
      font-size: 16px;
      font-weight: 400;
      line-height: 24px;
      margin-bottom: 16px;
    }
    .reason-detail:last-child {
      margin-bottom: 0;
    }
    .reason-img {
      width: 69px;
      height: 69px;
      margin-top: 8px;
    }
  }
  ::v-deep .klk-steps-vertical .klk-step-main {
    position: unset;
  }
}
</style>
