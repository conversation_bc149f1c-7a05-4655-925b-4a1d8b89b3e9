/*
 * Copyright By Klook
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

import { Vue } from 'vue-property-decorator';
import { pageData } from './data';

export default class ClaimDetailBase extends Vue {
  
  get sectionsData() {
    return pageData(this).page.body.sections || [];
  }

  get isApp() {
    return this.klook.isKlookApp;
  }

  // jenga 拿不到楼层外层数据临时通过一个方法来获取
  getWrapperItemData(sectionName: string) {
    return this.sectionsData.find((section: any) => {
      return section?.body?.content?.data_type === sectionName;
    });
  }
}
