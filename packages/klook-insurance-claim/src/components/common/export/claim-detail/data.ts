/*
 * Copyright By Klook
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

export const pageData = (that: Record<string, any>) => {
  return {
    page: {
      page_id: '{en_US}:{desktop}:booking_detail:{UNZ608615}',
      meta: {
        type: 'claim_detail',
      },
      body: {
        sections: [
          {
            meta: {
              type: 'any',
              track: {
                type: 'module',
                spm: 'ClaimDetail',
                exposure: true,
                action: true,
                query: {
                  oid: '',
                  trg: '',
                  ext: '{}',
                  evt: '',
                  mod: '',
                  typ: '',
                  idx: 0,
                  len: 0,
                },
              },
            },
            body: {
              content: {
                handler: 'ClaimDetail',
                data_type: 'DetailProcess',
                load_type: 'sync',
                src: '',
                title: '',
                data: {
                  copyIdText: that.$t('176361'),
                  status_text_list:[],
                  status:''
                },
              },
            },
          },
          {
            meta: {
              type: 'any',
              track: {
                type: 'module',
                spm: 'ClaimDetail',
                exposure: true,
                action: true,
                query: {
                  oid: '',
                  trg: '',
                  ext: '{}',
                  evt: '',
                  mod: '',
                  typ: '',
                  idx: 0,
                  len: 0,
                },
              },
            },
            body: {
              content: {
                handler: 'ClaimDetail',
                data_type: 'DetailUnit',
                load_type: 'sync',
                src: '',
                title: '',
                data: {
                  unitTitle: that.$t('176289'),
                  sku_text:''
                },
              },
            },
          },
          {
            meta: {
              type: 'any',
              track: {
                type: 'module',
                spm: 'ClaimDetail',
                exposure: true,
                action: true,
                query: {
                  oid: '',
                  trg: '',
                  ext: '{}',
                  evt: '',
                  mod: '',
                  typ: '',
                  idx: 0,
                  len: 0,
                },
              },
            },
            body: {
              content: {
                handler: 'ClaimDetail',
                data_type: 'DetailSubmitor',
                load_type: 'sync',
                src: '',
                title: '',
                data: {
                  submitorTitle: that.$t('176292'),
                  full_name:'',
                  email:''
                },
              },
            },
          },
          {
            meta: {
              type: 'any',
              track: {
                type: 'module',
                spm: 'ClaimDetail',
                exposure: true,
                action: true,
                query: {
                  oid: '',
                  trg: '',
                  ext: '{}',
                  evt: '',
                  mod: '',
                  typ: '',
                  idx: 0,
                  len: 0,
                },
              },
            },
            body: {
              content: {
                handler: 'ClaimDetail',
                data_type: 'DetailReason',
                load_type: 'sync',
                src: '',
                title: '',
                data: {
                  reasonTitle: that.$t('176293'),
                  reasonSubtitle: that.$t('176294'),
                  detailText: that.$t('176295'),
                  claim_files:[],
                  
                },
              },
            },
          },
        ],
      },
    },
  };
};
