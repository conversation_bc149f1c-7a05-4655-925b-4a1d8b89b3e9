<template>
  <div class="claim-unit info-box">
    <div class="info-title">{{ moduleData.unitTitle }}</div>
    <div class="info-subtitle">{{ claimDetail.sku_text }}</div>
  </div>
</template>

<script lang="ts">
import { Component, } from 'vue-property-decorator';
import BaseComponent from '../../../../../lib/jenga-core/components/base-component';
import { IconCheckCircle, IconCustomerService, IconInformation, IconQuestion } from '@klook/klook-icons';
import { $colorTextPrimary } from '@klook/klook-ui/lib/utils/design-token-esm';

@Component({
  components: {
    IconCheckCircle,
    IconCustomerService,
    IconInformation,
    IconQuestion,
  },
})
export default class ClaimDetail extends BaseComponent {

  colorTextPrimary = $colorTextPrimary;


  get claimDetail() {
    return this.extraData?.claimDetail;
  }

}
</script>

<style lang="scss" scoped>
.claim-detail {
  background-color: #eeeeee;
  height: calc(100vh - 64px);

  .claim-process {
    margin-top: 1px;
    width: 100%;
    text-align: left;
    background-color: #fff;
    padding: 20px;
    text-align: left;
    .goods-code {
      font-size: 20px;
      font-weight: 600;
      line-height: 24px;
    }
    .activity {
      font-size: 16px;
      font-weight: 400;
      line-height: 24px;
      margin: 4px 0;
      word-break: break-word;
    }
    .copy {
      margin: 4px 0 16px;
      text-decoration: underline;
      cursor: pointer;
    }
    .cancel-btn {
      margin-top: 12px;
      font-size: 14px;
      font-weight: 600;
      height: 36px;
      padding: 0 16px;
    }
    .step-content {
      display: flex;
      align-items: center;
    }
    ::v-deep .klk-poptip-reference {
      display: flex;
      align-items: center;
    }
    .infomation-icon {
      margin-left: 4px;
    }
    .price-poptip {
      ::v-deep .klk-poptip-popper-inner {
        max-height: unset !important;
        padding: 16px 20px 24px !important;
      }
    }
    .refund-detail-poptip-content {
      border-radius: 16px;
      padding: 16px, 20px, 24px, 20px;
      min-width: 320px;
      .refund-detail-item {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        font-weight: 400;
        font-size: 14px;
        &_title {
          height: 24px;
        }
        &_content {
          height: 24px;
        }
        &_right-box {
          text-align: right;
        }
        &_right-desc {
          color: #757575;
        }
        margin: 8px 0;
      }
      .refund-detail-sum {
        font-weight: 600;
        font-size: 16px;
        margin-top: 0;
      }
      .refund-detail-info {
        background-color: #f5f5f5;
        margin-top: 10px;
        border-radius: 12px;
        padding: 12px;
        position: relative;

        &_triangle {
          position: absolute;
          right: 35px;
          top: -16px;
          width: 0;
          height: 0;
          border-bottom: 8px solid #f5f5f5;
          border-top: 8px solid transparent;
          border-left: 8px solid transparent;
          border-right: 8px solid transparent;
        }
      }
    }
  }
  .info-box {
    padding: 24px 20px;
    background-color: #fff;
    border-radius: 16px;
    font-weight: 600;
    margin: 12px;
  }
  .info-title {
    margin-bottom: 12px;
    font-size: 20px;
  }
  .info-subtitle {
    font-size: 16px;
  }
  .info-email {
    font-size: 16px;
    font-weight: 400;
  }
  .claim-reason {
    .info-subtitle {
      font-size: 14px;
      color: #8a8a8a;
    }
    .reason-detail {
      font-size: 16px;
      font-weight: 400;
      line-height: 24px;
      margin-bottom: 16px;
    }
    .reason-detail:last-child {
      margin-bottom: 0;
    }
    .reason-img {
      width: 69px;
      height: 69px;
      margin-top: 8px;
    }
  }
  ::v-deep .klk-steps-vertical .klk-step-main {
    position: unset;
  }
}
</style>
