<template>
  <div class="claim-process">
    <div class="goods-code">{{ claimDetail.claim_detail_title }}</div>
    <div class="activity">{{ claimDetail.activity_name_en }}</div>
    <div class="copy" @click="copyInfo(claimDetail.application_no)">{{ moduleData.copyIdText }}</div>
    <klk-steps v-if="claimDetail.status_text_list.length > 0" :current="1" direction="vertical">
      <klk-step
        v-for="(item, index) in claimDetail.status_text_list"
        :key="index"
        :status="statusMap[item.web_status] || statusMap[1]"
        :title="item.application_status_text"
        :content="item.application_status_ext_text"
      >
        <div v-if="item.application_status_text" slot="title">
          <div class="step-content">
            {{ item.application_status_text }}
            <klk-poptip
              v-if="item.accept_detail"
              placement="bottom"
              :preventOverflow="true"
              trigger="click"
              class="price-poptip"
            >
              <IconInformation
                v-if="item.accept_detail"
                theme="outline"
                size="20"
                class="infomation-icon"
                :fill="colorTextPrimary"
              />
              <div slot="content" class="refund-detail-poptip-content">
                <div
                  v-if="item.accept_detail.compensated_amount"
                  class="refund-detail-sum refund-detail-item"
                >
                  <div class="refund-detail-item_title">{{ $t('176400') }}</div>
                  <div class="refund-detail-item_content">
                    {{ item.accept_detail.sys_currency }}
                    {{ item.accept_detail.compensated_amount }}
                  </div>
                </div>
                <div class="refund-detail-info">
                  <div class="refund-detail-info_triangle" />
                  <div v-if="item.accept_detail.cash_amount" class="refund-detail-item">
                    <div class="refund-detail-item_title">{{ $t('176563') }}</div>
                    <div class="refund-detail-item_content">
                      {{ item.accept_detail.sys_currency }} {{ item.accept_detail.cash_amount }}
                    </div>
                  </div>
                  <div v-if="item.accept_detail.credit_amount" class="refund-detail-item">
                    <div class="refund-detail-item_title">{{ $t('172783') }}</div>
                    <div class="refund-detail-item_right-box">
                      <div class="refund-detail-item_content">{{ item.accept_detail.credit_num }}</div>
                      <div class="refund-detail-item_right-desc">
                        Amount: {{ item.accept_detail.sys_currency }}
                        {{ item.accept_detail.credit_amount }}
                      </div>
                    </div>
                  </div>
                  <div v-if="item.accept_detail.gift_card_amount" class="refund-detail-item">
                    <div class="refund-detail-item_title">{{ $t('109023') }}</div>
                    <div class="refund-detail-item_content">
                      {{ item.accept_detail.sys_currency }}
                      {{ item.accept_detail.gift_card_amount }}
                    </div>
                  </div>
                </div>
              </div>
            </klk-poptip>
          </div>
        </div>
        <div slot="content">
          <div>{{ item.application_status_ext_text }}</div>
          <div>{{ item.log_time }}</div>
        </div>
      </klk-step>
    </klk-steps>
    <klk-button
      v-if="claimDetail.claim_status === 1"
      type="outlined"
      class="cancel-btn"
      :data-spm-module="trackWithdrawButtonModuleSpm"
      :data-spm-virtual-item="trackWithdrawButtonItemSpm"
      @click="withdrawClaim"
      >{{ $t('176398') }}</klk-button
    >
  </div>
</template>

<script lang="ts">
import { Component, Prop } from 'vue-property-decorator';
import BaseComponent from '../../../../../lib/jenga-core/components/base-component';
import { EventBus } from '../../../../../lib/jenga-core/utils/event-bus';
import { IconCheckCircle, IconCustomerService, IconInformation, IconQuestion } from '@klook/klook-icons';
import { $colorTextPrimary } from '@klook/klook-ui/lib/utils/design-token-esm';
import { toast, copyToClipboard } from '../../../../../utils';

@Component({
  components: {
    IconCheckCircle,
    IconCustomerService,
    IconInformation,
    IconQuestion,
  },
})
export default class ClaimDetail extends BaseComponent {
  @Prop({ type: Object, default: null }) klook?: Object;

  get _klook() {
    return this.klook || (window as any).__KLOOK__.state.klook;
  }

  colorTextPrimary = $colorTextPrimary;

  get claimDetail() {
    return this.extraData?.claimDetail;
  }

  get trackCommonExt() {
    return {
      MainBookingID: this.claimDetail.main_booking_ref_no,
      PlanCode: this.claimDetail.plan_code,
      ActivityID: this.claimDetail.activity_id,
    };
  }

  get trackWithdrawButtonModuleSpm() {
    return `WithdrawSubmittedClaim?oid=claim_${this.claimDetail.application_no}&ext=${JSON.stringify(this.trackCommonExt)}`;
  }

  get trackWithdrawButtonItemSpm() {
    return `__virtual?oid=claim_${this.claimDetail.application_no}&ext=${JSON.stringify(this.trackCommonExt)}`;
  }

  statusMap: Record<string, string> = {
    1: 'wait',
    2: 'process',
    3: 'finish',
  };

  handleContactClick() {}

  async copyInfo(copyText: string) {
    const result = await copyToClipboard(copyText);
    if (result) {
      console.info('copyInfo', result, this);
      toast(
        this,
        {
          message: this.$t('176361'),
          duration: 3000,
        },
        'insurance_claims_dialog-detail',
      );
    }
  }

  async withdrawClaim() {
    const dialogRes = await this.$dialog(
      'You can always claim again within the claim validity',
      this.$t('192976'),
      {
        okLabel: this.$t('176398'),
        cancelLabel: this.$t('176285'),
        showCancelButton: true,
        buttonAlign: 'block',
        titleAlign: 'center',
        width: 310,
      },
    );
    if (!dialogRes) {
      return;
    }
    let res: any = {};
    try {
      res = await this.$axios.get('/v1/insuranceclaimapisrv/outer/claim/cancelClaim', {
        params: {
          application_no: this.extraData.applicationNo,
        },
      });
    } catch (message) {
      toast(
        this,
        {
          message,
          duration: 3000,
        },
        'insurance_claims_dialog-detail',
      );
    }
    const data = res.data || {};
    const { error } = data || {};
    if (data.success && data.result) {
      toast(
        this,
        {
          message: this.$t('176762'),
          duration: 3000,
        },
        'insurance_claims_dialog-detail',
      );
      setTimeout(() => {
        EventBus.$emit('withdraw-success');
      });
    } else {
      this.$alert(error.message, {
        okLabel: this.$t('15762-sign_ok'),
      }).then(() => {
        EventBus.$emit('withdraw-success');
      });
    }
  }
}
</script>

<style lang="scss" scoped>
.claim-drawer {
  z-index: 1999 !important;
}
.claim-detail {
  background-color: #eeeeee;
  height: calc(100vh - 64px);

  .claim-process {
    margin-top: 1px;
    width: 100%;
    text-align: left;
    background-color: #fff;
    padding: 20px;
    text-align: left;
    .goods-code {
      font-size: 20px;
      font-weight: 600;
      line-height: 24px;
    }
    .activity {
      font-size: 16px;
      font-weight: 400;
      line-height: 24px;
      margin: 4px 0;
      word-break: break-word;
    }
    .copy {
      margin: 4px 0 16px;
      text-decoration: underline;
      cursor: pointer;
    }
    .cancel-btn {
      margin-top: 12px;
      font-size: 14px;
      font-weight: 600;
      height: 36px;
      padding: 0 16px;
      border-radius: 8px;
    }
    .step-content {
      display: flex;
      align-items: center;
    }
    ::v-deep .klk-poptip-reference {
      display: flex;
      align-items: center;
    }
    .infomation-icon {
      margin-left: 4px;
    }
    .price-poptip {
      ::v-deep .klk-poptip-popper-inner {
        max-height: unset !important;
        padding: 16px 20px 24px !important;
      }
    }
    .refund-detail-poptip-content {
      border-radius: 16px;
      padding: 16px, 20px, 24px, 20px;
      min-width: 320px;
      .refund-detail-item {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        font-weight: 400;
        font-size: 14px;
        &_title {
          height: 24px;
        }
        &_content {
          height: 24px;
        }
        &_right-box {
          text-align: right;
        }
        &_right-desc {
          color: #757575;
        }
        margin: 8px 0;
      }
      .refund-detail-sum {
        font-weight: 600;
        font-size: 16px;
        margin-top: 0;
      }
      .refund-detail-info {
        background-color: #f5f5f5;
        margin-top: 10px;
        border-radius: 12px;
        padding: 12px;
        position: relative;

        &_triangle {
          position: absolute;
          right: 35px;
          top: -16px;
          width: 0;
          height: 0;
          border-bottom: 8px solid #f5f5f5;
          border-top: 8px solid transparent;
          border-left: 8px solid transparent;
          border-right: 8px solid transparent;
        }
      }
    }
  }

  ::v-deep .klk-steps-vertical .klk-step-main {
    position: unset;
  }
}
</style>
