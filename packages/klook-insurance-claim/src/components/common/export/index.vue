<template>
  <div
    class="claim"
    :class="isDesktop ? 'claim-desktop' : 'claim-mobile'"
    :data-spm-module="`ClaimSDK?oid=mainbooking_${bookingNo}&ext=${JSON.stringify({PlanCode: detailPlanCodes})}`"
  >
    <div class="title">
      {{ $t('13554') }}
    </div>
    <div v-for="(item, index) in insuranceAddonList" :key="index" class="item">
      <div class="info">
        <div class="info-left">
          <p class="name">
            {{ item.product_name }}
          </p>
        </div>
        <span
          v-if="item.status && item.status.status_text"
          class="status"
          :style="{ color: item.status.status_text_color }"
          >{{ item.status.status_text }}</span
        >
      </div>

      <div class="unit-box" v-if="item.claim_detail_list">
        <div v-for="(subItem, subItemIndex) in item.claim_detail_list" class="unit-row-box">
          <div class="unit-row">
            <div>{{ subItem.sku_text }}</div>
            <div v-html="subItem.status_text" />
          </div>
          <div
            v-if="subItemIndex === item.claim_detail_list.length - 1"
            class="unit-row claim-detail"
            :data-spm-module="`ClaimDetialBtn?oid=booking_${item.insurance_order_no}&len=${insuranceAddonList.length}&idx=${index}&ext=${JSON.stringify({
              PlanCode: item.plan_code,
              MainBookingID: bookingNo,
            })}`"
            :data-spm-virtual-item="`__virtual?oid=booking_${item.insurance_order_no}&len=${insuranceAddonList.length}&idx=${index}&ext=${JSON.stringify({
              PlanCode: item.plan_code,
              MainBookingID: bookingNo,
            })}`"
            @click="
              showClaimDetail({
                applicationNo: subItem.application_no,
                insOrderNo: item.insurance_order_no,
                claimDetailList: item.claim_detail_list,
                insuranceType: item.insurance_type,
              })
            "
          >
            {{ $t('176761') }}
          </div>
        </div>
      </div>
      <p v-else class="unit">
        {{ item.unit }}
      </p>

      <div class="options">
        <div class="expired_wrap">
          <span v-if="item.claim_declaration_text" class="expired_text">{{
            item.claim_declaration_text
          }}</span>
          <klk-poptip
            :content="item.claim_declaration_icon_text"
            :preventOverflow="true"
            v-bind="poptipProps"
          >
            <IconInformation
              v-if="item.claim_declaration_icon_text"
              theme="outline"
              size="20"
              class="expired_icon"
              :data-spm-module="`ClaimInfo?oid=booking_${item.insurance_order_no}&ext=${JSON.stringify({
                PlanCode: item.plan_code,
                MainBookingID: bookingNo,
              })}`"
              :data-spm-virtual-item="`__virtual?oid=booking_${item.insurance_order_no}&ext=${JSON.stringify({
                PlanCode: item.plan_code,
                MainBookingID: bookingNo,
              })}`"
              :fill="colorTextPrimary"
            />
          </klk-poptip>
        </div>
        <klk-button
          v-if="item.claim_btn_status === 'visible' || item.claim_btn_status === 'not visible'"
          type="outlined"
          :disabled="item.claim_btn_status === 'not visible'"
          class="link claim-btn"
          :data-spm-module="`ClaimBtn?oid=booking_${item.insurance_order_no}&len=${insuranceAddonList.length}&idx=${index}&ext=${JSON.stringify({
            PlanCode: item.plan_code,
            MainBookingID: bookingNo,
            Deeplink: item.claim_url,
          })}`"
          :data-spm-virtual-item="`__virtual?oid=booking_${item.insurance_order_no}&len=${insuranceAddonList.length}&idx=${index}&ext=${JSON.stringify({
            PlanCode: item.plan_code,
            MainBookingID: bookingNo,
            Deeplink: item.claim_url,
          })}`"
          @click="handleClickClaim(item)"
        >
          {{ $t('14739') }}
        </klk-button>

        <a
          v-if="item.tc_url"
          href="javascript:void(0)"
          class="link  mr-16"
          :data-spm-module="`TermsContionBtn?trg=manual&oid=booking_${item.insurance_order_no}&len=${insuranceAddonList.length}&idx=${index}&ext=${JSON.stringify({
            PlanCode: item.plan_code,
            MainBookingID: bookingNo,
            Deeplink: item.tc_url,
          })}`"
          :data-spm-virtual-item="`__virtual?oid=booking_${item.insurance_order_no}&len=${insuranceAddonList.length}&idx=${index}&ext=${JSON.stringify({
            PlanCode: item.plan_code,
            MainBookingID: bookingNo,
            Deeplink: item.tc_url,
          })}`"
          @click="openPdf(item.tc_url)"
        >
          {{ $t('14058') }}
        </a>
        <span v-else-if="item.tc_description" class="tc_description  mr-16">{{ item.tc_description }}</span>
        <a
          v-if="item.claim_guide_url"
          href="javascript:void(0)"
          class="link"
          @click="openPdf(item.claim_guide_url)"
        >
          {{$t('176927')}}
        </a>
      </div>
      <div v-if="index !== insuranceAddonList.length - 1" class="divider" />
    </div>

    <div v-if="actionList.length" class="addon-entry">
      <klk-button
        v-for="(item, index) in actionList"
        :key="index"
        v-bind="bindIHTrack(item.track)"
        :size="isMobile ? 'mini' : 'small'"
        :type="item.type"
        :disabled="item.disabled"
        @click="handleBtnAction(item)"
      >
        {{ item.text }}
      </klk-button>
    </div>

    <!-- 保险理赔流程 -->
    <ClaimLayer ref="mod_claim_layer" @success="handleClaimSuccess" />
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import ClaimLayer from '../../desktop/claim-layer.vue';
import { handleIHTrack } from '../../../inHouseTrack.js';
import { IconInformation } from '@klook/klook-icons';
import { $colorTextPrimary } from '@klook/klook-ui/lib/utils/design-token-esm';
import { MOBILE_ROUTE_ENUM } from '../../../constant';

@Component({
  components: {
    ClaimLayer,
    IconInformation,
  },
})
export default class KlookInsuranceClaim extends Vue {
  @Prop({ type: Object, default: null }) klook!: Object;

  @Prop({ type: String, default: 'desktop' }) platform!: string;

  @Prop({ type: String, default: '' }) orderGuide!: string;

  @Prop({ type: String, default: '' }) bookingNo!: string;

  insuranceAddonList: Record<string, any>[] = [];
  actionList = [];
  colorTextPrimary = $colorTextPrimary;

  addonDetailData: any = {};

  get detailPlanCodes() {
    const list = this.addonDetailData?.insurance_addon_list || [];
    return list.map((item) => item.plan_code);
  }

  get _klook() {
    return this.klook || (window.__KLOOK__ && window.__KLOOK__.state && window.__KLOOK__.state.klook) || {};
  }

  get isDesktop() {
    return this.platform === 'desktop';
  }

  get isMobile() {
    return this.platform === 'mobile';
  }

  get poptipProps() {
    if (this.isMobile) {
      return {
        trigger: 'click',
        placement: 'top-end',
        'max-width': 200,
      };
    } else {
      return {};
    }
  }

  mounted() {
    // 板块标题文案由 sdk 输出
    this.$emit('update:sectionTitle', this.$t('13554'));
    this.queryBookingAddonDetail();
  }

  bindIHTrack(...args) {
    return handleIHTrack(...args);
  }

  async queryBookingAddonDetail() {
    const res = await this.$axios.get('/v1/insuranceapisrv/outer/booking/addon/detail', {
      params: {
        order_guid: this.orderGuide,
        booking_no: this.bookingNo,
      },
    });

    const data = res.data || {};
    if (data.success && data.result) {
      this.insuranceAddonList = data.result.insurance_addon_list || [];
      this.actionList = data.result.action_list || [];
      this.addonDetailData = data.result;

      // 存在保险数据通知外层展示
      const isShow = this.insuranceAddonList.length > 0 || this.actionList.length > 0;
      // 卡片是拿到接口数据以后再判断是否展示卡片，所以曝光埋点上报时已经有数据了，所以不需要对曝光埋点做延迟处理
      this.$emit('update:isShow', isShow);
    }
  }

  handleClickClaim(item) {
    console.log('handleClickClaim', item)
    // 兼容一期走 klook 的理赔流程
    const klookClaimUrlReg = /insurance\/claim/;
    if (klookClaimUrlReg.test(item.claim_url) && this.isDesktop) {
      this.$refs.mod_claim_layer.handleShowLayer({
        language: this._klook.language,
        ins_order_no: item.insurance_order_no,
        activity_name: '',
        data_source: item.data_source,
        insurance_type: item.insurance_type,
        addon_booking_ref_no: item.addon_booking_ref_no,
        goods_code: item.insurance_goods_code,
        claim_version: item.claim_version,
        booking_no: this.bookingNo,
        goods_category: item.goods_category
      });
    } else {
      window.location.href = item.claim_url;
    }
  }

  handleClaimSuccess() {
    // 理赔成功事件
    this.$emit('success');
  }

  handleBtnAction(item) {
    if (!item.deep_link) {
      return;
    }
    window.location.href = item.deep_link;
  }

  // 判断是否展示理赔过期提示
  isShowExpiredTip(item) {
    return item.is_claim_expired && item.expired_reason;
  }
  openPdf(url) {
    import('@klook/jsbridge')
      .then(({ default: KlookJSBridge }) => {
        return KlookJSBridge.call('platform.openPdf', url);
      })
      .catch(() => {
        if (window.open) {
          window.open(url);
          return;
        }
        window.location.href = url;
      });
  }

  showClaimDetail(detailParams: Record<string, any>) {
    if (this.isDesktop) {
      this.$refs.mod_claim_layer.handleShowClaimDetail(detailParams);
      return;
    }
    const { claimDetailList, applicationNo, insOrderNo, insuranceType } = detailParams;
    const languagePath = this._klook.language === 'en' ? '' : `/${this._klook.language}`;
    if (claimDetailList.length === 1) {
      window.location.href = `${window.location.origin}${languagePath}${MOBILE_ROUTE_ENUM.ClaimDetailPage}?applicationNo=${applicationNo}&insuranceType=${insuranceType}`;
    } else {
      window.location.href = `${window.location.origin}${languagePath}${MOBILE_ROUTE_ENUM.ClaimListPage}?insOrderNo=${insOrderNo}&insuranceType=${insuranceType}`;
    }
  }
}
</script>

<style lang="scss" scoped>
.claim {
  padding: 16px;
  border-radius: 16px;
  background: #fff;

  .unit {
    margin-top: 4px;
  }

  .title {
    padding-bottom: 8px;
    margin-bottom: 12px;

    color: #212121;

    font-size: var(--font-size-title);
    font-weight: 600;
    line-height: 132%;
  }

  .item {
    .divider {
      width: 100%;
      height: 1px;
      margin: 12px 0;
      background: #eee;
    }

    .info {
      display: flex;
      justify-content: space-between;

      .info-left {
        font-size: 16px;
        line-height: 150%;
        /* 24px */
        color: $color-text-primary;
        word-break: break-word;

        .name {
          font-weight: 600;
          font-size: var(--font-size-name);
        }
      }

      .status {
        flex: 0 0 auto;
        margin-left: 12px;
        font-size: 16px;
        font-weight: 400;
        line-height: 150%;
        /* 24px */
      }
    }

    .options {
      margin-top: 12px;

      display: flex;
      flex-wrap: wrap;
      justify-content: flex-start;
      align-items: center;
      gap: 16px;

      color: #212121;

      .link {
        display: flex;
        align-items: center;
        height: 36px;
        text-decoration: underline;
        font-size: 16px;
        font-weight: 400;
        line-height: 150%;
        /* 24px */
      }

      .claim-btn {
        padding: 8px 16px;
        border: 1px solid #4a4a4a;
        height: 36px;
        border-radius: 8px;
        text-decoration: none;
        display: flex;
        justify-content: center;
      }

      .tc_description {
        flex: 0 0 100%;
        font-size: 16px;
        font-weight: 400;
        line-height: 150%;
        /* 24px */
      }

      .expired_wrap {
        flex: 0 0 100%;
        display: flex;
        align-items: center;

        .expired_text {
          font-size: 16px;
          font-weight: 400;
          line-height: 150%;
          /* 24px */
        }

        .expired_icon {
          margin-left: 8px;
        }
      }
    }
  }
  .unit-box {
    background-color: #f5f5f5;
    border-radius: 12px;
    padding: 0 12px;
    margin-top: 16px;

    .unit-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 0;
      border-bottom: 1px solid #e6e6e6;
      gap: 16px;

      :first-child {
        word-break: normal;
      }
      :last-child {
        text-align: right;
        word-break: normal;
      }
    }

    .claim-detail {
      text-decoration: underline;
      cursor: pointer;
      width: fit-content;
    }
    .unit-row-box {
      border-bottom: 1px solid #e6e6e6;
    }

    .unit-row:last-child {
      border: none;
    }
    .unit-row-box:last-child {
      border: none;
    }
  }
}
.claim-desktop {
  --font-size-title: 24px;
  --font-size-name: 20px;
}

.claim-mobile {
  --font-size-title: 20px;
  --font-size-name: 16px;

  .item {
    .options {
      gap: 0;
    }

    .claim-btn {
      margin-right: 16px;
    }
    .mr-16 {
      margin-right: 16px;
    }
  }
}
</style>
