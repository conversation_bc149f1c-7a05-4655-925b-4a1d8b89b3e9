<template>
  <div class="goods-info">
    <div class="goods-info-left">
      <div class="goods-title">
        {{ goodsName }}
      </div>
      <div
        v-if="activityName"
        class="goods-desc"
      >
        {{ activityName }}
      </div>
      <div
        v-if="ticketDate"
        class="goods-desc"
      >
        {{ ticketDate }}
      </div>
    </div>
    <div class="goods-info-right">
      <i>
        <svg-icon name="desktop-common#insurance-label" />
      </i>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';


@Component
export default class GoodsInfo extends Vue {

  @Prop({ type: String, default: '' }) goodsName!: string;

  @Prop({ type: String, default: '' }) activityName?: string;

  @Prop({ type: String, default: '' }) ticketDate?: string;

}
</script>

<style lang="scss" scoped>
.goods-info {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  padding: 20px;
  background-color: #fff;

  .goods-title {
    font-size: 24px;
    font-weight: 600 !important;
    margin-bottom: 8px;
    padding-right: 12px;
    word-break: break-word;
  }

  .goods-desc {
    font-size: 16px;
    font-weight: 400;
    padding-right: 12px;
    word-break: break-word;
    margin-bottom: 8px;
  }
}

.goods-info-right {
  color: $color-text-primary;

  i {
    width: 48px;
    height: 48px;
    display: inline-block;
    background-color: #009688;
    /* stylelint-disable */
    border-radius: 50%;
    /* stylelint-enable */
    position: relative;
    margin-right: 10px;
    vertical-align: top;

    svg {
      fill: #fff;
      width: 28px;
      height: 28px;
      position: absolute;
      left: 50%;
      top: 50%;
      margin: -14px 0 0 -14px;
    }
  }
}

.claim-form-mobile {
  --font-size-title: 20px;
  --font-size-name: 16px;

  .goods-info {
    border-top: 1px solid #eeeeee;
  }
}
</style>
