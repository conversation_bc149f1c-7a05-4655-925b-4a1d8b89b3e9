<template>
  <div
    class="claim-form-header"
    :class="{
      'claim-form-header-desktop': isDesktop,
      'border-bottom': showBottomBorder,
      'claim-form-header-mobile': !isDesktop,
    }"
  >
    <span v-if="!isInApp" class="claim-form-header-box" @click="$emit('click')">
      <IconClose
        v-if="!iconBack"
        theme="outline"
        size="20"
        class="close_icon"
        :fill="colorTextPrimary"
      />
      <IconBack v-else theme="outline" size="20" class="close_icon" :fill="colorTextPrimary" />
    </span>
    <div v-else />

    <span v-if="title" class="title">{{ title }}</span>
    <span v-else class="title">{{ $t('176286') }}</span>
    <div class="blank">
      <slot />
    </div>
  </div>
</template>

<script lang="ts">
// 引入图标
import { IconClose, IconBack } from '@klook/klook-icons';
import { Component, Vue, Prop } from 'vue-property-decorator';
// 引入 design token
import { $colorTextPrimary } from '@klook/klook-ui/lib/utils/design-token-esm';

@Component({
  components: {
    IconClose,
    IconBack,
  },
})
export default class Header extends Vue {
  @Prop({ type: String, default: '' }) title?: string;

  @Prop({ type: String, default: 'desktop' }) platform!: string;

  @Prop({ type: Boolean, default: false }) iconBack?: boolean;

  @Prop({ type: Boolean, default: false }) isInApp?: boolean;

  @Prop({ type: Boolean, default: false }) showBottomBorder?: boolean;

  colorTextPrimary = $colorTextPrimary;

  get isDesktop() {
    return this.platform === 'desktop';
  }
}
</script>

<style lang="scss" scoped>
.claim-form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  height: 64px;
  position: sticky;
  top: 0;
  left: 0;
  background-color: #ffffff;
  z-index: 1000;

  .claim-form-header-box {
    display: inline-block;
    height: 20px;
    margin-right: 12px;
  }

  .close_icon {
    cursor: pointer;
  }

  .title {
    font-size: 16px;
    padding: 1px 0;
    height: fit-content;
    display: inline-block;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

.claim-form-header-desktop {
  border-bottom: 1px solid #EEEEEE;
  .title {
    margin-left: 25px;
  }
}

.border-bottom {
  border-bottom: 1px solid #eeeeee;
}

.claim-form-header-mobile {
  --font-size-title: 20px;
  --font-size-name: 16px;

  padding: 10px 25px;
  height: 48px;
}
</style>
