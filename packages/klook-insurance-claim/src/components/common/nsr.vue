<template>
  <div>
    <ClaimForm
      :platform="platform"
      :title="title"
      :order-info="orderInfo"
      :claim-api="claimApi"
      :is-in-app="isInApp"
      :should-query-unit-group="shouldQueryUnitGroup"
      inpath-type="NSR"
      @header-click="$emit('header-click')"
      @success="success"
      @show-faq="$emit('show-faq')"
      @back="$emit('back')"
    />
  </div>
</template>

<script lang="ts">
// 引入图标
import { Component, Vue, Prop } from 'vue-property-decorator';
import ClaimForm from '../common/claim-form.vue';
import { OrderInfo } from '../../../types';
import { CLAIM_API_ENUM, UNIT_API_ENUM, CLAIM_VERSION_ENM } from '../../constant';

@Component({
  components: {
    ClaimForm,
  },
})
export default class NSR extends Vue {
  @Prop({ type: String, default: 'desktop' }) platform!: string;

  @Prop({ type: String, default: '' }) orderGuide!: string;

  @Prop({ type: String, default: '' }) bookingNo!: string;

  @Prop({ type: Boolean, default: false }) isInApp?: boolean;

  @Prop({ type: Object, default: { claim_version: CLAIM_VERSION_ENM.old } }) orderInfo!: OrderInfo;

  @Prop({ type: Object, default: () => ({}) }) uploadData!: Record<string, any>;

  @Prop({ type: String, default: '' }) claimTitle!: string;

  get title() {
    return this.claimTitle || this.$t('15745-insurance_klook_flex4');
  }

  get claimApi() {
    return CLAIM_API_ENUM[this.orderInfo.claim_version as any];
  }

  get shouldQueryUnitGroup() {
    return Number(this.orderInfo.claim_version) === CLAIM_VERSION_ENM.old;
  }

  success(feedbackInfo: Record<string, any>) {
    this.$emit('success', feedbackInfo);
  }
}
</script>

<style lang="scss" scoped></style>
