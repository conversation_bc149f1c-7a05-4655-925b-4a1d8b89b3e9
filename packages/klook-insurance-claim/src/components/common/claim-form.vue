<template>
  <div id="claim-form" class="claim-form" :class="isDesktop ? 'claim-form-desktop' : 'claim-form-mobile'">
    <ClaimFormHeader :is-in-app="isInApp" :platform="platform" @click="handleClose">
      <div @click="showFaq">
        <IconQuestion theme="outline" size="20" class="close_icon" :fill="colorTextPrimary" />
      </div>
    </ClaimFormHeader>
    <div ref="contentBox" class="claim-form-body">
      <GoodsInfo
        :goods-name="title"
        :activity-name="orderInfo.activity_name"
        :ticket-date="orderInfo.ticket_date"
      />
      <div class="grey-content-box">
        <SelectUnit
          ref="select_unit"
          :platform="platform"
          :unit-required-tips="unitRequiredTips"
          :unit-list="claim.unitList"
          :default-form-value="claim.form"
          @change="onUnitChange"
        />
        <div ref="travellerInfo" />
        <TravellerInfo
          ref="traveller_info"
          :platform="platform"
          :disabled="!claim.canSubmit"
          :default-form-value="claim.form.useInfo"
          @on-edit="onEditTravellerInfo"
        />
        <div ref="claimReason" />
        <ClaimReasonSatg
          v-if="inpathType === 'SatG'"
          ref="claim_reason"
          :platform="platform"
          :required-reason="requiredReason"
          :show-upload="showUpload"
          :disabled="!claim.canSubmit"
          :claim-reason-options="claim.claimReasonOptions"
          :default-form-value="claim.form"
          :track-data="trackData"
        />
        <ClaimReasonNSR
          v-else
          ref="claim_reason"
          :platform="platform"
          :required-reason="requiredReason"
          :show-upload="showUpload"
          :disabled="!claim.canSubmit"
          :claim-reason-options="claim.claimReasonOptions"
          :default-form-value="claim.form"
          :track-data="trackData"
        />
      </div>
    </div>
    <div class="btn-box">
      <klk-button
        :class="['claim_submit', { submiting: claim.submiting }]"
        :disabled="!claim.canSubmit"
        type="primary"
        :loading="claim.submiting"
        :data-spm-module="trackSubmitButtonModuleSpm"
        :data-spm-virtual-item="trackSubmitButtonItemSpm"
        @click="handleSubmit"
      >
        {{ $t('15272-insurance_submit') }}
        <div v-show="claim.submiting" class="uil-ring-css" />
      </klk-button>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch } from 'vue-property-decorator';
// 引入 design token
import { $colorTextPrimary } from '@klook/klook-ui/lib/utils/design-token-esm';
import { CLAIM_VERSION_ENM, unitGroupApi, MOBILE_ROUTE_ENUM } from '../../constant';
import ClaimFormHeader from './header.vue';
import GoodsInfo from './goods-info.vue';
import SelectUnit from './select-unit.vue';
import ClaimReasonSatg from './claim-reason/index-satg.vue';
import ClaimReasonNSR from './claim-reason/index-nsr.vue';
import TravellerInfo from './traveller-info/index.vue';
import FAQ from './export/faq.vue';
import { toast } from '../../utils';
import { IconQuestion } from '@klook/klook-icons';
import isEqual from 'lodash/isEqual';

@Component({
  components: {
    ClaimFormHeader,
    GoodsInfo,
    SelectUnit,
    TravellerInfo,
    FAQ,
    IconQuestion,
    ClaimReasonSatg,
    ClaimReasonNSR,
  },
})
export default class ClaimForm extends Vue {
  @Prop({ type: Object, default: null }) klook?: Object;

  @Prop({ type: String, default: '' }) title?: string;

  @Prop({ type: String, default: 'desktop' }) platform!: string;

  @Prop({ type: String, default: '' }) langPath!: string;

  @Prop({ type: Boolean, default: false }) hideFAQ?: boolean;

  @Prop({ type: Object, default: { insurance_type: '' } }) orderInfo!: Record<string, any>;

  @Prop({ type: String, default: '' }) claimApi!: string;

  @Prop({ type: Boolean, default: false }) showUpload?: boolean;

  @Prop({ type: Boolean, default: false }) requiredReason?: boolean;

  @Prop({ type: Boolean, default: false }) shouldQueryUnitGroup?: boolean;

  @Prop({ type: Boolean, default: false }) isInApp?: boolean;

  @Prop({ type: Object, default: () => ({}) }) uploadData!: Record<string, any>;

  @Prop({ type: String, default: 'SatG' }) inpathType?: string;

  colorTextPrimary = $colorTextPrimary;

  unitRequiredTips = '';

  claim: Record<string, any> = {
    canSubmit: false,
    form: { gender: '', useInfo: {} },
    unitList: [],
    genders: [],
    sku_ids: [],
    terms_condition: [],
    faqs: [{ question: 'fefewfewfessdfdsf', answer: 'fewrewrewrewrw' }],
    submiting: false,
    claimReasonOptions: [],
  };

  get isDesktop() {
    return this.platform === 'desktop';
  }

  get _klook() {
    return this.klook || (window as any).__KLOOK__.state.klook;
  }

  get trackData() {
    return {
      bookingId: this.orderInfo?.main_booking_ref_no,
      insOrderNo: this.orderInfo?.ins_order_no,
      planCode: this.orderInfo?.plan_code,
      activityId: this.orderInfo?.activity_id,
    };
  }

  get trackCommonExt() {
    return {
      MainBookingID: this.orderInfo?.main_booking_ref_no,
      PlanCode: this.orderInfo?.plan_code,
      ActivityID: this.orderInfo?.activity_id,
    };
  }

  get trackSubmitButtonModuleSpm() {
    return `ClaimSubmission?trg=manual&oid=booking_${this.orderInfo?.ins_order_no}&ext=${JSON.stringify(this.trackCommonExt)}`;
  }

  get trackSubmitButtonItemSpm() {
    return `__virtual?oid=booking_${this.orderInfo?.ins_order_no}&ext=${JSON.stringify(this.trackCommonExt)}`;
  }

  mounted() {
    this.handleUnitInfo();
  }

  beforeCreate() {
    // 拦截App返回事件
    import('@klook/jsbridge').then(({ default: KlookJSBridge }) => {
      KlookJSBridge.register('platform.handleAppBackPressed', () => {
        this.handleClose().then(() => {
          // eslint-disable-next-line
          return new Promise((reslove, reject) => {
            return reslove(true);
          });
        });
      });
    });
  }

  @Watch('orderInfo', { immediate: true })
  queryUnitList() {
    this.handleUnitInfo();
  }

  async handleLogin() {
    if (this.$cookies && this.$store && this.$store && this.$href) {
      const { loginWithSDK } = await import('@klook/klook-traveller-login');
      loginWithSDK({
        aid: this.$cookies.get('aid'),
        isMP: this.$store.state.klook?.platformMp !== '',
        platform: this.$store.state.klook?.platform,
        language: this.$store.state.klook?.language,
        currency: this.$store.state.klook?.currency,
        market: this.$store.state.klook?.market,
        bizName: 'Platform',
        purpose: 'insurance_claim',
        cancel: () => {},
        success: () => {
          this.$store.dispatch('auth/getProfile');
          window.location.reload();
        },
      }).then((supportLogin: boolean) => {
        if (!supportLogin) {
          window.location.href = this.$href(
            `/signin/?signin_jump=${encodeURIComponent(window.location.href)}`,
          );
        }
      });
    } else {
      console.error('need this.$cookies && this.klook && this.$store && this.$href');
    }
  }

  async handleUnitInfo() {
    const {
      claim_unit = [],
      cache = {},
      terms_condition,
      faq_klook_flex = [],
      claim_reasons = [],
    } = this.orderInfo || {};
    const { app_unit_list = [], last_name, first_name, email } = cache || {};
    if (this.shouldQueryUnitGroup) {
      const unitGroupRes = await this.$axios.get(unitGroupApi, {
        params: { ins_order_no: this.orderInfo.ins_order_no },
      });
      // 从unitGroupApi中匹配分组信息
      claim_unit.forEach((unitItem: Record<string, string | number>) => {
        const currentGroup = unitGroupRes?.data?.result?.find(
          (groupItem: Record<string, string | number>) =>
            groupItem.unit_detail_no === unitItem.unit_detail_no,
        );
        if (currentGroup) {
          unitItem.group_choose = currentGroup.group_choose;
        }
      });
    }

    this.claim.canSubmit = false;
    this.claim.unitList = Array.isArray(claim_unit)
      ? claim_unit.map(item => {
          if (item.can_check) {
            this.claim.canSubmit = true;
          }
          item.choosed = false;

          return item;
        })
      : [];
    let group_chooses = this.claim.unitList
      ?.filter((item: Record<string, string | number>) =>
        app_unit_list?.find(
          (cacheUnit: Record<string, string | number>) =>
            cacheUnit.unit_detail_no === item.unit_detail_no && item.can_check,
        ),
      )
      .map((filterItem: Record<string, string | number>) => filterItem.group_choose);
    group_chooses = [...new Set(group_chooses)];

    this.claim.faqs = faq_klook_flex?.map(item => ({
      ...item,
      show: false,
    }));
    this.claim.terms_condition = terms_condition;
    this.$nextTick(() => {
      this.updateATagTargetInApp();
    });

    const userTitle = cache.title?.toUpperCase();
    this.claim.claimReasonOptions = claim_reasons;
    this.claim.form = {
      ...cache,
      group_chooses,
      useInfo: {
        gender: this.claim.genders.some((item: Record<string, string>) => item.value === userTitle)
          ? userTitle
          : 'MR',
        last_name,
        first_name,
        email,
      },
    };
  }

  onUnitChange() {
    this.unitRequiredTips = '';
  }

  async handleSubmit() {
    // 校验投保信息必选
    if (!this.$refs.select_unit.validateFormValues()) {
      this.unitRequiredTips = this.$t('78176');
      this.$refs.contentBox.scrollIntoView();
      return;
    }

    const travellerRes = await this.$refs.traveller_info.validateFormValues();
    if (!travellerRes) {
      this.$refs.travellerInfo.scrollIntoView();
      return;
    }

    await this.$refs.claim_reason.validateFormValues();
    const claimReasonFormValue = this.$refs.claim_reason.getFormValues();
    const travellerFormValue = this.$refs.traveller_info.getFormValues();

    // 根据分组取选中组的sku_id
    const group_chooses = Array.from(new Set(this.$refs.select_unit.getFormValues()));
    const app_unit_list: Record<string, any>[] = this.claim.unitList.filter(
      (item: Record<string, string | number>) => group_chooses.includes(item.group_choose) && item.can_check,
    );

    const formData = {
      claim_version: this.orderInfo.claim_version || CLAIM_VERSION_ENM.old,
      insurance_no: this.orderInfo.ins_order_no,
      app_unit_list,
      data_source: this.orderInfo.data_source,
      insurance_type: this.orderInfo.insurance_type,
      addon_booking_ref_no: this.orderInfo.addon_booking_ref_no,
      goods_code: this.orderInfo.goods_code,
      email: travellerFormValue.email,
      first_name: travellerFormValue.first_name,
      last_name: travellerFormValue.last_name,
      title: travellerFormValue.gender,
      reason: claimReasonFormValue.detail,
      files: claimReasonFormValue.images,
      cu_reason_types: claimReasonFormValue.reasons,
      main_booking_ref_no: this.orderInfo.main_booking_ref_no,
    };

    if (!this.claim.submiting) {
      this.claim.submiting = true;
    } else {
      return;
    }
    this.claim.claim_error_mes = '';
    let res: any = {};
    try {
      res = await this.$axios.post(this.claimApi, formData);
    } catch (message) {
      toast(
        this,
        {
          message,
          duration: 3000,
        },
        'claim-drawer-form',
      );
      this.claim.submiting = false;
      return;
    }

    this.claim.submiting = false;

    const data = res.data || {};
    if (data.success) {
      if (this.isDesktop) {
        this.$emit('success', data.result || {});
        return;
      }
      const languagePath = this._klook.language === 'en' ? '' : `/${this._klook.language}`;
      window.location.href = `${window.location.origin}${languagePath}${
        MOBILE_ROUTE_ENUM.ClaimSuccessPage
      }?feedbackInfo=${encodeURIComponent(JSON.stringify(data.result || {}))}`;
    } else {
      const { code, message } = data?.error || {};
      if (code === '4001') {
        this.handleLogin();
        return;
      }
      if (code === '11015' || code === '99999') {
        this.$alert(message, {
          okLabel: this.$t('15762-sign_ok'),
        }).then(() => {
          this.$emit('back');
        });
        return;
      }
      toast(
        this,
        {
          message: data.error?.message,
          duration: 3000,
        },
        'claim-drawer-form',
      );
    }
  }

  updateATagTargetInApp() {
    // 处理后端返回的a标签target属性
    if (this.isInApp) {
      const claimPage = document.getElementById('claim-form');
      if (claimPage) {
        const aTagArr = [...claimPage.getElementsByTagName('a')];
        aTagArr.forEach(function (el) {
          el.target = '_self';
        });
      }
    }
  }

  onEditTravellerInfo(travellerInfo: Record<string, any>) {
    this.claim.form = { ...this.claim.form, ...travellerInfo, useInfo: { ...travellerInfo } };
  }

  async handleClose() {
    const { cache = {} } = this.orderInfo || {};
    const claimReasonFormValue = this.$refs.claim_reason.getFormValues();
    const travellerFormValue = this.$refs.traveller_info.getFormValues();

    // 根据分组取选中组的sku_id
    const group_chooses = Array.from(new Set(this.$refs.select_unit.getFormValues()));
    const app_unit_list: Record<string, any>[] = this.claim.unitList
      .filter(
        (item: Record<string, string | number>) =>
          group_chooses.includes(item.group_choose) && item.can_check,
      )
      .map((item: Record<string, string | number>) => ({
        sku_id: item.sku_id,
        unit_detail_no: item.unit_detail_no,
      }));

    const newData: Record<string, any> = {
      app_unit_list,
      email: travellerFormValue.email,
      first_name: travellerFormValue.first_name,
      last_name: travellerFormValue.last_name,
      title: travellerFormValue.gender,
      reason: claimReasonFormValue.detail,
      files: claimReasonFormValue.images,
      cu_reason_types: claimReasonFormValue.reasons,
    };

    // 相应的表单值改变才提示是否保存草稿
    let compareKeys = [
      'app_unit_list',
      'email',
      'first_name',
      'last_name',
      'title',
      'reason',
      'files',
      'cu_reason_types',
    ];
    const isObject = (obj: Record<string, any>) => {
      return Object.prototype.toString.call(obj).slice(1, 7) === 'object';
    };
    // 过滤掉空数组、空对象和假值(newData和cache同为空数组、空对象或者假值则不比较)
    compareKeys = compareKeys.filter(
      item =>
        !(
          (!newData[item] ||
            (Array.isArray(newData[item]) && newData[item]?.length === 0) ||
            (isObject(newData[item]) && !Object.keys(newData[item]).length)) &&
          (!cache[item] ||
            (Array.isArray(cache[item]) && cache[item]?.length === 0) ||
            (isObject(cache[item]) && !Object.keys(cache[item]).length))
        ),
    );
    // title是特殊情况，每次进页面都会有默认值
    if (newData['title'] === 'MR' && cache['title'] === '') {
      compareKeys = compareKeys.filter(item => item !== 'title');
    }
    const isUnEqual: any = compareKeys.find(compareKey => !isEqual(cache[compareKey], newData[compareKey]));
    if (isUnEqual) {
      const dialogRes = await this.$dialog(this.$t('176282'), {
        okLabel: this.$t('176284'),
        cancelLabel: this.$t('176285'),
        showCancelButton: true,
        buttonAlign: 'block',
        titleAlign: 'center',
        width: 310,
      });
      if (dialogRes) {
        const formData = {
          claim_version: this.orderInfo.claim_version || CLAIM_VERSION_ENM.old,
          insurance_no: this.orderInfo.ins_order_no,
          data_source: this.orderInfo.data_source,
          insurance_type: this.orderInfo.insurance_type,
          addon_booking_ref_no: this.orderInfo.addon_booking_ref_no,
          goods_code: this.orderInfo.goods_code,
          main_booking_ref_no: this.orderInfo.main_booking_ref_no,
          ...newData,
        };
        await this.$axios.post('/v1/insuranceclaimapisrv/outer/claim/saveClaimDraft', formData);
      }
    }
    this.$emit('header-click');
  }

  showFaq() {
    if (this.isDesktop) {
      this.$emit('show-faq');
      return;
    }
    const languagePath = this._klook.language === 'en' ? '' : `/${this._klook.language}`;
    window.location.href = `${window.location.origin}${languagePath}${MOBILE_ROUTE_ENUM.ClaimFaqPage}?insuranceType=${this.orderInfo.insurance_type}`;
  }
}
</script>

<style lang="scss" scoped>
.claim-form {
  position: relative;

  .grey-content-box {
    background-color: #eeeeee;
    padding: 12px;
  }

  .btn-box {
    padding: 12px 10px;
    position: sticky;
    bottom: 0;
    left: 0;
    background-color: #ffffff;
    border-top: 1px solid #eeeeee;
    display: flex;
    z-index: 1000;
  }

  .claim_submit {
    display: block;
    box-sizing: content-box;
    border: 1px solid $color-brand-primary;
    width: 100%;
    height: 26px;
    line-height: 30px;
    text-align: center;
    font-weight: $fontWeight-semibold;
    color: $color-text-primary-onDark;
    font-size: $fontSize-body-s;
    border-radius: $radius-l;
    background-color: $color-brand-primary;
    overflow: hidden;
    position: relative;

    &:hover {
      background-color: $color-brand-primary;
    }

    .uil-ring-css {
      position: absolute;
      left: 50%;
      top: 50%;
      width: 20px;
      height: 20px;
      margin-top: -10px;
      margin-left: -10px;
      display: inline-block;

      div {
        width: 20px;
        height: 20px;
      }
    }

    &:disabled {
      border: 1px solid $color-border-normal;
      background-color: $color-bg-page;
      color: $color-text-secondary;

      &:hover {
        cursor: default;
        background-color: $color-bg-page;
      }
    }
  }
}

.claim-form-desktop {
  --font-size-title: 24px;
  --font-size-name: 20px;
  height: 100vh;
  background: #eeeeee;

  .btn-box {
    position: fixed;
    right: 0;
    left: unset;
    width: 680px;
    bottom: 0;
  }

  .claim-form-body {
    padding-bottom: 73px;
  }

}

.claim-form-mobile {
  --font-size-title: 20px;
  --font-size-name: 16px;
}
</style>
