<template>
  <klk-form
    ref="form"
    class="claim-reason"
    :class="isDesktop ? 'claim-reason-desktop' : 'claim-reason-mobile'"
    :model="form"
    :rules="validateRules"
  >
    <section-wrapper :title="$t('176293')" :style="{ marginTop: '12px' }">
      <div ref="reasonBox" class="reasons-box">
        <klk-form-item prop="selectedReason" :label="$t('176294')" :style="{ width: '100%' }">
          <div v-for="(item, index) in form.reasons" :key="item.index" class="reason-item">
            <div class="reason-item_content">
              <div class="top-reason">
                {{ item.content }}
              </div>
              <div v-for="subItem in item.children" :key="subItem.index" class="leaf-reason">
                {{ subItem.content }}
              </div>
            </div>
            <div
              class="reason-item_close-box"
              @click="removeReason({ reasonIndex: index, reasonTopOption: item })"
            >
              <IconClose class="icon-close reason-item_close" />
            </div>
          </div>
          <klk-button
            type="outlined"
            size="small"
            class="add-btn"
            :disabled="disabled"
            :data-spm-module="trackAddButtonModuleSpm"
            :data-spm-virtual-item="trackAddButtonItemSpm"
            @click="toSelectReason"
          >
            <IconAddCircle class="icon-add" />
            {{ $t('17443') }}
          </klk-button>
        </klk-form-item>
        <klk-form-item
          prop="detail"
          :label="$t('5978-insurance_reason_for')"
          :style="{ width: '100%', marginBottom: 0 }"
        >
          <klk-input
            ref="detail"
            v-model="form.detail"
            type="textarea"
            name="detail"
            :maxlength="500"
            :disabled="disabled"
            :placeholder="$t('13658-insurance_let_us')"
            class-name="required"
          />
        </klk-form-item>
        <klk-bottom-sheet
          :visible.sync="showReasonSelect"
          :mask-closable="false"
          :title="$t('176293')"
          :header-divider="true"
          :can-pull-close="false"
          :data-spm-page="trackReasonPageSpm"
          class="reason-select-track"
        >
          <div slot="header-left" @click="showReasonSelect = false">
            <IconClose class="icon-close" />
          </div>
          <div slot="header-right" @click="onCompleteSelectReason">
            <klk-button type="outlined" class="add-btn" size="small"> {{ $t('47672') }} </klk-button>
          </div>

          <div class="reason-content">
            <template>
              <div
                v-for="topOption in claimReasonOptions"
                :key="topOption.index"
                class="top-row"
                :class="{
                  'select-item': !topOption.children || topOption.children.length === 0,
                  'select-item_active': checkReasonItemActive({ topOption }, true),
                }"
                @click="handleClickTopReason(topOption)"
              >
                <div class="top-row_label">
                  {{ topOption.content }}
                </div>
                <div v-if="topOption.level1_desc" class="top-row_desc">
                  {{ topOption.level1_desc }}
                </div>
                <div v-if="topOption.children && topOption.children.length > 0" class="leaf-row">
                  <div
                    v-for="leafOption in topOption.children"
                    :key="leafOption.index"
                    class="select-item"
                    :class="{ 'select-item_active': checkReasonItemActive({ topOption, leafOption }) }"
                    @click="handleSelectReason({ topOption, leafOption })"
                  >
                    <div class="leaf-row_label">
                      {{ leafOption.content }}
                    </div>
                    <div v-if="leafOption.level1_desc" class="leaf-row_desc">
                      {{ leafOption.level1_desc }}
                    </div>
                    <IconCheckCircle theme="filled" class="icon-check" />
                  </div>
                </div>
                <IconCheckCircle theme="filled" class="icon-check" />
              </div>
            </template>
          </div>
        </klk-bottom-sheet>
      </div>
    </section-wrapper>
    <section-wrapper
      v-for="item in form.reasons"
      :key="item.index"
      :title="$t('179271')"
      :style="{ marginTop: '12px' }"
    >
      <div v-if="!item.children || !item.children.length" class="klk-form-item-box">
        <klk-form-item :prop="item.content" :label="item.content" :style="{ width: '100%' }">
          <div class="klk-upload-click-box"></div>
        </klk-form-item>
        <div class="form-item-label_example" @click="toExamplePage(item)">{{ $t('193481') }}</div>
      </div>
      <div v-else>
        <div v-for="subItem in item.children" :key="subItem.subIndex">
          <div v-for="extItem in subItem.ext" :key="extItem.index" class="klk-form-item-box">
            <klk-form-item
              :prop="formItemPropPreffix + extItem.ext_index"
              :label="extItem.text_desc"
              :style="{ width: '100%' }"
            >
              <div
                class="klk-upload-click-box" 
                id="klk-upload-click-box"
              >
                <client-only>
                  <div
                    :data-spm-module="trackUploadButtonModuleSpm"
                    :data-spm-virtual-item="trackUploadButtonItemSpm"
                    class="custom-upload-track"
                    @click.stop="handleCustomUploadClick"
                  >
                  <Customupload
                    :ref="'imageUpload' + extItem.ext_index"
                    name="upload_file"
                    class-name="required"
                    class="klk-upload-box"
                    accept="image/jpg, image/jpeg, image/png"
                    :action="uploadApi"
                    :data="claimData"
                    :before-upload="beforeUpload"
                    :max-file-size="10000"
                    :limit="maxUploadImages"
                    :multiple="true"
                    :file-list="defaultUploadValueMap[formItemPropPreffix + extItem.ext_index]"
                    @success="
                      (value, file) =>
                        uploadSuccess(value, file, {
                          reason_type: subItem.index,
                          ...extItem,
                          reason_ext_index: extItem.ext_index,
                          minUploadImages: extItem.pic_min_num,
                          currentRef: 'imageUpload' + extItem.ext_index,
                        })
                    "
                    @exceed-size="onExceedSize"
                    @error="onError"
                    @trigger-click="
                      toUploadPhoto({
                        reason_type: subItem.index,
                        ...extItem,
                        reason_ext_index: extItem.ext_index,
                        minUploadImages: extItem.pic_min_num,
                        currentRef: 'imageUpload' + extItem.ext_index,
                      })
                    "
                    @remove="
                      (newFile, oldFile) =>
                        handleUploadRemoved(newFile, oldFile, {
                          ext_index: extItem.ext_index,
                          reason_type: subItem.index,
                        })
                    "
                    @file-repeat="onFileRepeat"
                  >
                  </Customupload>
                  </div>
                </client-only>
              </div>
            </klk-form-item>
            <div class="form-item-label_example" @click="toExamplePage(extItem)">{{ $t('193481') }}</div>
          </div>
        </div>
      </div>

      <klk-drawer
        v-if="isDesktop && showUploadPhoto"
        id="claim-drawer"
        direction="right"
        class="claim-drawer upload-modal-track"
        mask-color="transparent"
        :visible.sync="showUploadPhoto"
        :mask-closable="true"
        :padding="0"
        :width="680"
        :data-spm-page="trackUploadPageSpm"
        @close="handleCloseUpload"
      >
        <div id="insurance_claims_dialog" class="insurance_claims_dialog">
          <ClaimFormHeader
            :platform="platform"
            :iconBack="true"
            :show-bottom-border="!isDesktop"
            :title="currentUploadInfo.text_desc"
            @click="handleCloseUpload"
          ></ClaimFormHeader>
          <UploadPhoto
            :platform="platform"
            :allow-upload="allowUpload"
            :upload-info="currentUploadInfo"
            class="upload-photo-track"
            :data-spm-module="trackUploadConfirmButtonModuleSpm"
            :data-spm-virtual-item="trackUploadConfirmButtonItemSpm"
            @back="handleCloseUpload"
            @upload="upload"
          />
        </div>
      </klk-drawer>
      <klk-bottom-sheet
        v-else
        :visible.sync="showUploadPhoto"
        :can-pull-close="false"
        :mask-closable="false"
        :title="currentUploadInfo.text_desc"
        :header-divider="true"
        class="upload-bottom-sheet upload-modal-track"
        :data-spm-page="trackUploadPageSpm"
        height="calc(100vh)"
        max-height="100%"
      >
        <div slot="header-left" @click="handleCloseUpload">
          <IconClose class="icon-close" />
        </div>
        <UploadPhoto
          :platform="platform"
          :upload-info="currentUploadInfo"
          :allow-upload="allowUpload"
          :data-spm-module="trackUploadConfirmButtonModuleSpm"
          :data-spm-virtual-item="trackUploadConfirmButtonItemSpm"
          class="upload-photo-track"
          @back="handleCloseUpload"
          @upload="upload"
        />
      </klk-bottom-sheet>
    </section-wrapper>
  </klk-form>
</template>

<script lang="ts">
// 引入图标
import { Component, Vue, Prop, Watch } from 'vue-property-decorator';
import SectionWrapper from '../section-wrapper.vue';
import { IconAddCircle, IconClose, IconCheckCircle, IconPlus } from '@klook/klook-icons';
import UploadPhoto from '../export/upload-photo.vue';
import cloneDeep from 'lodash/cloneDeep';
import { maxUploadImages, uploadApi } from '../../../constant';
import Customupload from '../customUpload';
import { toast } from '../../../utils';
import ClaimFormHeader from '../header.vue';

interface ReasonOption {
  index: number; // reason id
  parent_index: number;
  content: string; // reason 文案
  required_pic: false;
  pic_min_num: number;
  children: ReasonOption[];
  level1_desc?: string;
  ext: Record<string, any>[];
}

interface ReasonSelectProps {
  topOption: ReasonOption;
  leafOption?: ReasonOption;
}

interface ReasonForm {
  reasons: ReasonOption[];
  images: Record<string, any>[];
  detail: string;
}

@Component({
  components: {
    SectionWrapper,
    IconAddCircle,
    IconClose,
    IconCheckCircle,
    IconPlus,
    UploadPhoto,
    Customupload,
    ClaimFormHeader,
  },
})
export default class ClaimReason extends Vue {
  @Prop({ type: Object, default: null }) klook?: Object;

  @Prop({ type: String, default: 'desktop' }) platform!: string;

  @Prop({ type: Boolean, default: false }) requiredReason?: boolean;

  @Prop({ type: Boolean, default: false }) disabled?: boolean;

  @Prop({ type: Array, default: [] }) claimReasonOptions!: ReasonOption[];

  @Prop({ type: Object, default: {} })
  defaultFormValue!: Record<string, any>;

  @Prop({ type: Object, default: () => {
    return {}
  } }) trackData!: any;

  showUploadPhoto: boolean = false;

  allowUpload: boolean = true;

  uploadApi = uploadApi;

  // reasons 理由选择控件选择完成后保存的理由
  form: Record<string, any> = { images: [], reasons: [], detail: '' };

  formItemPropPreffix = 'images';

  claimData = {};

  showReasonSelect = false;

  maxUploadImages = maxUploadImages;

  // 理由选择控件保存的理由
  selectReasons: ReasonOption[] = [];

  currentUploadInfo: Record<string, any> = {};

  validateRules: Record<string, any> = {};

  defaultUploadValueMap: Record<string, any> = {};

  selectedReasonId: string = '';

  @Watch('showUploadPhoto')
  onShowUploadPhotoChange(val: boolean) {
    if (val) {
      setTimeout(() => {
        window?.tracker?.inhouse?.track('pageview', '.upload-modal-track')
      })
    }
  }

  get isDesktop() {
    return this.platform === 'desktop';
  }

  get _klook() {
    return this.klook || (window as any).__KLOOK__.state.klook;
  }

  get trackCommonExt() {
    return {
      MainBookingID: this.trackData?.bookingId,
      PlanCode: this.trackData?.planCode,
      ActivityID: this.trackData?.activityId,
    }
  }

  get trackUploadConfirmButtonModuleSpm() {
    const ext = {
      ...this.trackCommonExt,
      ClaimReasonID: this.selectedReasonId,
    }
    return `ClaimMaterialSubmission?trg=manual&oid=booking_${this.trackData?.insOrderNo}&ext=${JSON.stringify(ext)}`;
  }

  get trackUploadConfirmButtonItemSpm() {
    const ext = {
      ...this.trackCommonExt,
      ClaimReasonID: this.selectedReasonId,
    }
    return `__virtual?trg=manual&oid=booking_${this.trackData?.insOrderNo}&ext=${JSON.stringify(ext)}`;
  }

  get trackUploadButtonModuleSpm() {
    return `AddClaimMaterial?trg=manual&oid=booking_${this.trackData?.insOrderNo}&ext=${JSON.stringify(this.trackCommonExt)}`;
  }

  get trackUploadButtonItemSpm() {
    return `__virtual?&trg=manual&oid=${this.trackData?.insOrderNo}&ext=${JSON.stringify(this.trackCommonExt)}`;
  }

  get trackAddButtonModuleSpm() {
    return `AddClaimReason?trg=manual&oid=booking_${this.trackData?.insOrderNo}&ext=${JSON.stringify(this.trackCommonExt)}`;
  }

  get trackAddButtonItemSpm() {
    return `__virtual?oid=${this.trackData?.insOrderNo}&ext=${JSON.stringify(this.trackCommonExt)}`;
  }

  get trackReasonPageSpm() {
    return `Insurance_ClaimReason?trg=manual&oid=booking_${this.trackData?.insOrderNo}&ext=${JSON.stringify(this.trackCommonExt)}`;
  }

  get trackUploadPageSpm() {
    const ext = {
      ...this.trackCommonExt,
      ClaimReasonID: this.selectedReasonId,
    }

    return `Insurance_ClaimMaterialSubmission?trg=manual&oid=booking_${this.trackData?.insOrderNo}&ext=${JSON.stringify(ext)}`;
  }

  mounted() {
    this.form = {
      images: this.defaultFormValue.files || [],
      reasons: this.defaultFormValue.cu_reason_types || [],
      detail: this.defaultFormValue.reason,
      selectedReason: '',
    };

    this.validateRules = {
      selectedReason: [
        {
          required: true,
          message: this.$t('112502'),
        },
      ],
    };

    // 对已选择的每一个原因对应的上传图片控件加校验规则
    this.form.reasons.forEach(topReason => {
      this.form.selectedReason = '1';
      if (topReason?.children?.length) {
        topReason.children.forEach(leafReason => {
          if (leafReason?.ext?.length) {
            leafReason?.ext.forEach(extItem => {
              let key = this.formItemPropPreffix + extItem.ext_index;

              const currenReasonImages = this.defaultFormValue.files?.filter(
                item => item.reason_ext_index === extItem.ext_index,
              );
              // 设置当前上传图片控件的值
              Vue.set(this.form, key, currenReasonImages);
              this.defaultUploadValueMap[key] = cloneDeep(currenReasonImages).map(item => item.location_uri);

              Vue.set(this.validateRules, key, [
                {
                  type: 'array',
                  min: extItem.pic_min_num,
                  required: true,
                  message: this.$t('5971'),
                },
              ]);
            });
          }
        });
      }
    });
    this.$nextTick(() => {
      this.$refs.form.clearValidate();
    });
  }

  getFormValues() {
    let images: Record<string, any>[] = [];
    const formKeys = Object.keys(this.form);
    const imageUploadKeys = formKeys.filter(
      item => item.startsWith(this.formItemPropPreffix) && item !== this.formItemPropPreffix,
    );
    // 收集所有的图片上传组件的值
    imageUploadKeys.forEach(key => {
      if (this.form[key]?.length > 0) {
        images = [...images, ...this.form[key]];
      }
    });
    return { ...this.form, images };
  }

  validateFormValues() {
    return new Promise((resolve, reject) => {
      this.$refs.form
        .validate()
        .then(() => {
          resolve(true);
        })
        .catch((error: any) => {
          this.$refs.reasonBox.scrollIntoView();
          reject(error);
        });
    });
  }

  toSelectReason(val) {
    this.selectReasons = cloneDeep(this.form.reasons);
    this.showReasonSelect = true;
    setTimeout(() => {
      window?.tracker?.inhouse?.track('pageview', '.reason-select-track')
    })
  }

  handleClickTopReason(topOption: ReasonOption) {
    if (topOption.children && topOption.children.length > 0) {
      return;
    }
    this.handleSelectReason({ topOption });
  }

  // 点击选择理由
  handleSelectReason(reasonSelectProps: ReasonSelectProps) {
    const { topOption, leafOption } = reasonSelectProps;
    // 是否有二级理由
    if (leafOption) {
      const selectedTopReasonIndex = this.selectReasons.findIndex(
        item => item && item.index === topOption.index,
      );
      if (selectedTopReasonIndex >= 0) {
        const selectedTopReason = this.selectReasons[selectedTopReasonIndex];
        const selectedLeafReasonIndex =
          selectedTopReason.children &&
          selectedTopReason.children.findIndex(item => item.index === leafOption.index);
        // 已选中则反选
        if (selectedLeafReasonIndex >= 0) {
          Vue.set(this.selectReasons, selectedTopReasonIndex, undefined);
          return;
        }
        if (selectedLeafReasonIndex < 0) {
          const newTopOption = { ...topOption, children: [leafOption] };
          Vue.set(this.selectReasons, selectedTopReasonIndex, newTopOption);
          return;
        }
      }
      Vue.set(this.selectReasons, this.selectReasons.length, { ...topOption, children: [leafOption] });
      return;
    }
    const selectedTopReasonIndex = this.selectReasons.findIndex(
      item => item && item.index === topOption.index,
    );
    // 已选中则反选
    if (selectedTopReasonIndex >= 0) {
      Vue.set(this.selectReasons, selectedTopReasonIndex, undefined);
      return;
    }
    Vue.set(this.selectReasons, this.selectReasons.length, topOption);
  }

  // 是否选中
  checkReasonItemActive(currentReasonSelectProps: ReasonSelectProps, isTop?: boolean) {
    const { topOption, leafOption } = currentReasonSelectProps;
    if (isTop && topOption.children?.length > 0) {
      return false;
    }
    if (!leafOption) {
      const selectedReason = this.selectReasons.find(item => item && item.index === topOption.index);
      return selectedReason;
    }
    const selectedTopReason = this.selectReasons.find(item => item && item.index === topOption.index);

    const selectedLeafReason = selectedTopReason?.children?.find(
      (item: ReasonOption) => item.index === leafOption.index,
    );
    return selectedLeafReason;
  }

  onCompleteSelectReason() {
    // 重新选择原因后初始化校验规则
    this.validateRules = {
      selectedReason: [
        {
          required: true,
          message: this.$t('112502'),
        },
      ],
    };
    this.form.reasons = cloneDeep(this.selectReasons.filter(item => item !== undefined));

    const formKeys = Object.keys(this.form);
    const oldImageUploadKeys = formKeys.filter(
      item => item.startsWith(this.formItemPropPreffix) && item !== this.formItemPropPreffix,
    );
    const newImageUploadKeys: string[] = [];
    // 对已选择的每一个原因对应的上传图片控件加校验规则
    this.form.reasons.forEach(topReason => {
      this.form.selectedReason = '1';
      this.$refs.form.clearValidate();
      if (topReason?.children?.length) {
        topReason.children.forEach(leafReason => {
          if (leafReason?.ext?.length) {
            leafReason?.ext.forEach(extItem => {
              let key = this.formItemPropPreffix + extItem.ext_index;
              newImageUploadKeys.push(key);
              Vue.set(this.validateRules, key, [
                {
                  type: 'array',
                  min: extItem.pic_min_num,
                  required: true,
                  message: this.$t('5971'),
                },
              ]);
            });
          }
        });
      }
    });
    // 将被删除原因对应的图片上传form item值置为空
    oldImageUploadKeys.forEach(key => {
      if (!newImageUploadKeys.includes(key)) {
        this.form[key] = undefined;
      }
    });
    this.selectedReasonId = this.form.reasons.map(item => {
      const childrenIndex = item.children.map(child => child.index);
      return childrenIndex.join(',');
    }).join(',');
    this.showReasonSelect = false;
  }

  removeReason(props: Record<string, any>) {
    const { reasonIndex, reasonTopOption } = props;
    this.form.reasons.splice(reasonIndex, 1);

    const ext = reasonTopOption?.children?.[0]?.ext;
    // 删除对应上传图片控件记录的图片
    if (ext?.length) {
      ext.forEach((item: Record<string, any>) => {
        this.form[this.formItemPropPreffix + item.ext_index] = undefined;
      });
    }
    Vue.set(this.form, 'reasons', cloneDeep(this.form.reasons));
    if (!this.form.reasons?.length) {
      this.form.selectedReason = '';
    }
  }

  handleCustomUploadClick() {
    setTimeout(() => {
      window?.tracker?.inhouse?.track('action', '.custom-upload-track')
    })
  }

  toUploadPhoto(currentUploadInfo: Record<string, any> | undefined) {
    this.currentUploadInfo = currentUploadInfo || {};
    this.showUploadPhoto = true;
    this.allowUpload = true;
  }

  toExamplePage(currentUploadInfo: Record<string, any> | undefined) {
    this.currentUploadInfo = currentUploadInfo || {};
    this.showUploadPhoto = true;
    this.allowUpload = false;
  }

  handleCloseUpload() {
    this.showUploadPhoto = false;
    this.currentUploadInfo = {};
  }

  upload() {
    setTimeout(() => {
      window?.tracker?.inhouse?.track('action', '.upload-photo-track')
    })
    this.$refs[this.currentUploadInfo.currentRef]?.[0]?.upload();
  }

  /**
   *
   * @param newFile 新上传的图片
   * @param oldFile 缓存图片，回显
   * @param uploadInfo
   */
  handleUploadRemoved(
    newFile: Record<string, any>,
    oldFile: Record<string, any>,
    uploadInfo: Record<string, any>,
  ) {
    const file = newFile || oldFile;
    const { ext_index } = uploadInfo;
    if (ext_index) {
      const formKey = this.formItemPropPreffix + ext_index;
      if (newFile) {
        const keys = ['name', 'size', 'type', 'lastModified', 'webkitRelativePath'];
        this.form[formKey] = this.form[formKey]?.filter(function (item) {
          return !keys.every(function (key) {
            return item.file?.[key] === file[key];
          });
        });
      } else {
        this.form[formKey] = this.form[formKey]?.filter(item => item.location_uri !== file.url);
      }
      this.$nextTick(() => {
        this.$refs.form.validate();
      });
    }
  }

  beforeUpload(file) {
    this.claimData = {
      content_type: 'application/javascript',
    };
    this.showUploadPhoto = false;
    return true;
  }

  uploadSuccess(value, file, currentUploadInfo) {
    if (value.error) {
      const { message } = value.error;
      toast(
        this,
        {
          message,
          duration: 3000,
        },
        'claim-drawer-form',
      );
      return;
    }
    const res = value.result;
    const uploadImgInfo = {
      ...res,
      file,
      name: res.storage_name,
      url: res.location_uri,
      file_type: file.type,
    };
    const currentKey = this.formItemPropPreffix + currentUploadInfo.reason_ext_index;
    const currenReasonImages = this.form[currentKey] || [];
    currenReasonImages.push({ ...uploadImgInfo, ...currentUploadInfo });
    // 设置当前上传图片控件的值
    Vue.set(this.form, currentKey, currenReasonImages);
    this.$refs.form.clearValidate();
    this.$nextTick(() => {
      this.$refs.form.validate();
    });
  }

  onExceedSize() {
    // TODO 先写死英文，待新增text id后更新
    toast(
      this,
      {
        message: 'The image you upload is too large. Please select image that less than 10MB.',
        duration: 3000,
      },
      'claim-drawer-form',
    );
  }

  onError() {
    toast(
      this,
      {
        message: this.$t('122713'),
        duration: 3000,
      },
      'claim-drawer-form',
    );
  }
  onFileRepeat(file) {
    toast(
      this,
      {
        message: `File: ${file.name} already exists, please select another`,
        duration: 3000,
      },
      'claim-drawer-form',
    );
  }
}
</script>

<style lang="scss" scoped>
.reason-content {
  margin-top: 20px;
}

.claim-reason-mobile {
  .upload-bottom-sheet {
    ::v-deep .klk-bottom-sheet-inner {
      max-height: 100% !important;
      border-radius: 0 !important;
    }
    ::v-deep .can-pull-close::before {
      width: 0 !important;
    }

    ::v-deep .header-title-text {
      font-weight: 500 !important;
    }
  }

  ::v-deep .klk-form-item-label {
    white-space: normal;
    word-break: break-word;
    width: 80%;
  }

  .leaf-row {
    &_label {
      width: 90%;
      word-break: break-word;
    }
  }
}

.reasons-box {
  margin-top: 12px;
}

.klk-form-item-box {
  position: relative;
  margin-top: 12px;

  .klk-form-item:not(:last-child) {
    margin-bottom: 0 !important;
  }
}

.form-item-label {
  &_example {
    position: absolute;
    right: 0;
    top: 0;
    font-size: 14px;
    font-weight: 400;
    text-decoration: underline;
    cursor: pointer;
  }
}

.first-option {
  padding-left: 12px;
  font-weight: 600 !important;
  font-size: 14px !important;
}

::v-deep .klk-option {
  border: 1px solid #eee !important;
  margin: 12px 8px;
}

::v-deep .klk-option-group {
  .klk-option-label {
    padding: 0 !important;
  }

  .klk-option {
    padding: 0 !important;
  }

  .klk-option-selected {
    position: relative;
  }

  .klk-option-checked-icon {
    position: absolute;
    top: 2px;
    right: 20px;
  }
}

.option-content {
  height: 48px;
  line-height: 48px;
  width: 100%;
  padding: 0 20px;
}

.claim-reason-desktop {
  ::v-deep .klk-bottom-sheet-inner {
    width: 680px !important;
    left: unset !important;
    right: 0 !important;
  }
}

.icon-add {
  margin-right: 8px;
  display: flex;
  align-items: center;
}

.add-btn {
  display: flex;
  align-items: center;
  justify-content: center;

  ::v-deep span {
    display: flex;
    align-items: center;
  }
}

.top-row {
  margin-bottom: 28px;

  &_label {
    font-size: 16px;
    font-weight: 600 !important;
    line-height: 24px;
    width: 90%;
    word-break: break-word;
  }

  &_desc {
    font-size: 14px;
    font-weight: 400;
    line-height: 21px;
    color: #8a8a8a;
    width: 90%;
    word-break: break-word;
  }

  .icon-check {
    display: none;
  }
}

.select-item {
  border: 1px solid #e6e6e6;
  border-radius: 12px;
  padding: 12px;
  margin: 12px 0;
  cursor: pointer;
  position: relative;

  .icon-check {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    right: 12px;
    display: none;
    color: #212121;
    width: 20px;
    width: 20px;
    height: 20px;
    border-radius: 20px;
    font-size: 20px;

    circle {
      display: none;
    }
  }

  &_active {
    border: 1px solid #ff5b00;
    background-color: #fff0e5;

    .icon-check {
      display: block;
    }
  }
}

.leaf-row {
  &_label {
    font-size: 14px;
    font-weight: 400;
    line-height: 21px;
  }

  &_desc {
    font-size: 12px;
    font-weight: 400;
    line-height: 21px;
    color: #8a8a8a;
  }
}

.icon-close {
  cursor: pointer;
}

.reason-item {
  padding: 12px 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #ff5b001f;
  margin-bottom: 16px;
  border-radius: 12px;
  width: fit-content;

  .top-reason {
    font-size: 14px;
    font-weight: 600 !important;
    line-height: 21px;
  }

  .leaf-reason {
    font-size: 12px;
    line-height: 18px;
  }
  &_close-box {
    display: flex;
  }
  &_close {
    margin-left: 8px;
    font-size: 16px;
  }
}

.klk-upload-enable {
  ::v-deep .klk-upload-btn {
    cursor: pointer !important;
  }
}

.klk-upload-click-box {
  position: relative;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;

  .klk-upload-click {
    width: 80px;
    height: 80px;
    cursor: pointer;
    border-radius: 12px;
    border: 1px solid #f5f5f5;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #8a8a8a;
    background: #f5f5f5;

    .icon-plus {
      width: 24px;
      height: 24px;
      font-size: 24px;
    }
  }

  img {
    width: 80px;
    height: 80px;
    border-radius: 12px;
  }

  ::v-deep .klk-upload-item-close {
    z-index: 0 !important;
  }

  .img-box {
    position: relative;

    .icon-close-box {
      position: absolute;
      width: 18px;
      height: 18px;
      right: -6px;
      top: -6px;
      opacity: 0.8;
      background-color: #000;
      color: #fff;
      font-size: 12px;
      border-radius: 20px;
      display: inline-flex;
      justify-content: center;
      align-items: center;
    }
  }
}

.claim-drawer {
  z-index: 1999 !important;
}
</style>
