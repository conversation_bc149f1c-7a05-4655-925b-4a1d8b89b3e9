<template>
  <klk-form
    ref="form"
    class="claim-form"
    :class="isDesktop ? 'claim-form-desktop' : 'claim-form-mobile'"
    :model="form"
    :rules="validateRules"
  >
    <section-wrapper :title="$t('176293')" :style="{ marginTop: '12px' }">
      <div ref="reasonBox" class="reasons-box">
        <klk-form-item prop="selectedReason" :label="$t('176294')" :style="{ width: '100%' }">
          <div v-for="(item, index) in form.reasons" :key="item.index" class="reason-item">
            <div class="reason-item_content">
              <div class="top-reason">
                {{ item.content }}
              </div>
              <div v-for="subItem in item.children" :key="subItem.index" class="leaf-reason">
                {{ subItem.content }}
              </div>
            </div>
            <div class="reason-item_close-box" @click="removeReason(index)">
              <IconClose class="icon-close reason-item_close" />
            </div>
          </div>
          <klk-button
            type="outlined"
            size="small"
            class="add-btn"
            :disabled="disabled"
            :data-spm-module="trackAddButtonModuleSpm"
            :data-spm-virtual-item="trackAddButtonItemSpm"
            @click="toSelectReason"
          >
            <IconAddCircle class="icon-add" />
            {{ $t('17443') }}
          </klk-button>
        </klk-form-item>
        <klk-form-item prop="detail" :label="$t('5978-insurance_reason_for')" :style="{ width: '100%' }">
          <klk-input
            ref="detail"
            v-model="form.detail"
            type="textarea"
            name="detail"
            :maxlength="500"
            :disabled="disabled"
            :placeholder="$t('13658-insurance_let_us')"
            class-name="required"
          />
        </klk-form-item>
        <klk-bottom-sheet
          :visible.sync="showReasonSelect"
          :mask-closable="false"
          :title="$t('176293')"
          :data-spm-page="trackReasonPageSpm"
          class="reason-select-track"
        >
          <div slot="header-left" @click="showReasonSelect = false">
            <IconClose class="icon-close" />
          </div>
          <div slot="header-right" @click="onCompleteSelectReason">
            <klk-button type="outlined" class="add-btn"> {{ $t('47672') }} </klk-button>
          </div>

          <div class="reason-content">
            <template>
              <div
                v-for="topOption in claimReasonOptions"
                :key="topOption.index"
                class="top-row"
                :class="{
                  'select-item': !topOption.children || topOption.children.length === 0,
                  'select-item_active': checkReasonItemActive({ topOption }, true),
                }"
                @click="handleClickTopReason(topOption)"
              >
                <div class="top-row_label">
                  {{ topOption.content }}
                </div>
                <div v-if="topOption.level1_desc" class="top-row_desc">
                  {{ topOption.level1_desc }}
                </div>
                <div v-if="topOption.children && topOption.children.length > 0" class="leaf-row">
                  <div
                    v-for="leafOption in topOption.children"
                    :key="leafOption.index"
                    class="select-item"
                    :class="{ 'select-item_active': checkReasonItemActive({ topOption, leafOption }) }"
                    @click="handleSelectReason({ topOption, leafOption })"
                  >
                    <div class="leaf-row_label">
                      {{ leafOption.content }}
                    </div>
                    <div v-if="leafOption.level1_desc" class="leaf-row_desc">
                      {{ leafOption.level1_desc }}
                    </div>
                    <IconCheckCircle theme="filled" class="icon-check" />
                  </div>
                </div>
                <IconCheckCircle theme="filled" class="icon-check" />
              </div>
            </template>
          </div>
        </klk-bottom-sheet>
      </div>
    </section-wrapper>
    <section-wrapper v-if="form.reasons.length > 0" :title="$t('179271')" :style="{ marginTop: '12px' }">
      <div class="klk-form-item-box">
        <klk-form-item prop="images" :style="{ width: '100%' }">
          <div class="klk-upload-click-box">
            <client-only>
              <Customupload
                ref="imageUpload"
                name="upload_file"
                class-name="required"
                class="klk-upload-box"
                accept="image/jpg, image/jpeg, image/png"
                :action="uploadApi"
                :data="claimData"
                :before-upload="beforeUpload"
                :max-file-size="10000"
                :limit="maxUploadImages"
                :multiple="true"
                :file-list="fileList"
                :data-spm-module="trackUploadButtonModuleSpm"
                :data-spm-virtual-item="trackUploadButtonItemSpm"
                @remove="uploadRemoved"
                @success="uploadSuccess"
                @exceed-size="onExceedSize"
                @error="onError"
                @file-repeat="onFileRepeat"
              />
            </client-only>
          </div>
        </klk-form-item>
      </div>
    </section-wrapper>
  </klk-form>
</template>

<script lang="ts">
// 引入图标
import { Component, Vue, Prop } from 'vue-property-decorator';
import SectionWrapper from '../section-wrapper.vue';
import { IconAddCircle, IconClose, IconCheckCircle, IconPlus } from '@klook/klook-icons';
import UploadPhoto from '../export/upload-photo.vue';
import cloneDeep from 'lodash/cloneDeep';
import { uploadApi, maxUploadImages } from '../../../constant';
import Customupload from '../customUpload';
import { toast } from '../../../utils';

interface ReasonOption {
  index: number; // reason id
  parent_index: number;
  content: string; // reason 文案
  required_pic: false;
  pic_min_num: number;
  children: ReasonOption[];
  level1_desc?: string;
  ext: Record<string, any>[];
}

interface ReasonSelectProps {
  topOption: ReasonOption;
  leafOption?: ReasonOption;
}

interface ReasonForm {
  reasons: ReasonOption[];
  images: Record<string, any>[];
  detail: string;
}

@Component({
  components: {
    SectionWrapper,
    IconAddCircle,
    IconClose,
    IconCheckCircle,
    IconPlus,
    UploadPhoto,
    Customupload,
  },
})
export default class ClaimReason extends Vue {
  @Prop({ type: Object, default: null }) klook?: Object;

  @Prop({ type: String, default: 'desktop' }) platform!: string;

  @Prop({ type: Boolean, default: false }) requiredReason?: boolean;

  @Prop({ type: Boolean, default: false }) disabled?: boolean;

  @Prop({ type: Array, default: [] }) claimReasonOptions!: ReasonOption[];

  @Prop({ type: Object, default: {} })
  defaultFormValue!: Record<string, any>;

  @Prop({ type: Object, default: () => {
    return {}
  } }) trackData!: any;

  selectedReasonId: string = '';

  showUploadPhoto: boolean = false;

  uploadApi = uploadApi;

  allowUpload: boolean = true;

  form: Record<string, any> = { images: [], reasons: [], detail: '' };

  formItemPropPreffix = 'images';

  maxUploadImages = maxUploadImages;

  claimData = {};

  showReasonSelect = false;

  selectReasons: ReasonOption[] = [];

  currentUploadIndexInfo: Record<string, any> = {};

  fileList: string[] = [];

  validateRules: Record<string, any> = {};

  get isDesktop() {
    return this.platform === 'desktop';
  }

  get _klook() {
    return this.klook || (window as any).__KLOOK__.state.klook;
  }

  get trackCommonExt() {
    return {
      MainBookingID: this.trackData?.bookingId,
      PlanCode: this.trackData?.planCode,
      ActivityID: this.trackData?.activityId,
    }
  }

  get trackUploadButtonModuleSpm() {
    return `AddClaimMaterial?trg=manual&oid=booking_${this.trackData?.insOrderNo}&ext=${JSON.stringify(this.trackCommonExt)}`;
  }

  get trackUploadButtonItemSpm() {
    return `__virtual?oid=booking_${this.trackData?.insOrderNo}&ext=${JSON.stringify(this.trackCommonExt)}`;
  }

  get trackAddButtonModuleSpm() {
    return `AddClaimReason?trg=manual&oid=booking_${this.trackData?.insOrderNo}&ext=${JSON.stringify(this.trackCommonExt)}`;
  }

  get trackAddButtonItemSpm() {
    return `__virtual?oid=booking_${this.trackData?.insOrderNo}&ext=${JSON.stringify(this.trackCommonExt)}`;
  }

  get trackReasonPageSpm() {
    return `Insurance_ClaimReason?trg=manual&oid=booking_${this.trackData?.insOrderNo}&ext=${JSON.stringify(this.trackCommonExt)}`;
  }


  mounted() {
    this.form = {
      images: cloneDeep(this.defaultFormValue.files || []),
      reasons: this.defaultFormValue.cu_reason_types || [],
      detail: this.defaultFormValue.reason,
      selectedReason: this.defaultFormValue.cu_reason_types?.length > 0 ? '1' : '',
    };

    this.fileList = cloneDeep(this.defaultFormValue.files || [])?.map(
      (item: Record<string, any>) => item.location_uri,
    );

    this.validateRules = {
      selectedReason: [
        {
          required: true,
          message: this.$t('112502'),
        },
      ],
    };
    this.$nextTick(() => {
      this.$refs.form.clearValidate();
    });
  }

  getFormValues() {
    return this.form;
  }

  validateFormValues() {
    return new Promise((resolve, reject) => {
      this.$refs.form
        .validate()
        .then(() => {
          resolve(true);
        })
        .catch((error: any) => {
          this.$refs.reasonBox.scrollIntoView();
          reject(error);
        });
    });
  }

  toSelectReason(val) {
    this.selectReasons = cloneDeep(this.form.reasons);
    this.showReasonSelect = true;
    setTimeout(() => {
      window?.tracker?.inhouse?.track('pageview', '.reason-select-track')
    })
  }

  handleClickTopReason(topOption: ReasonOption) {
    if (topOption.children && topOption.children.length > 0) {
      return;
    }
    this.handleSelectReason({ topOption });
  }

  // 点击选择理由
  handleSelectReason(reasonSelectProps: ReasonSelectProps) {
    const { topOption, leafOption } = reasonSelectProps;
    // 是否有二级理由
    if (leafOption) {
      const selectedTopReasonIndex = this.selectReasons.findIndex(
        item => item && item.index === topOption.index,
      );
      if (selectedTopReasonIndex >= 0) {
        const selectedTopReason = this.selectReasons[selectedTopReasonIndex];
        const selectedLeafReasonIndex =
          selectedTopReason.children &&
          selectedTopReason.children.findIndex(item => item.index === leafOption.index);
        // 已选中则反选
        if (selectedLeafReasonIndex >= 0) {
          Vue.set(this.selectReasons, selectedTopReasonIndex, undefined);
          return;
        }
        if (selectedLeafReasonIndex < 0) {
          const newTopOption = { ...topOption, children: [leafOption] };
          Vue.set(this.selectReasons, selectedTopReasonIndex, newTopOption);
          return;
        }
      }
      Vue.set(this.selectReasons, this.selectReasons.length, { ...topOption, children: [leafOption] });
      return;
    }
    const selectedTopReasonIndex = this.selectReasons.findIndex(
      item => item && item.index === topOption.index,
    );
    // 已选中则反选
    if (selectedTopReasonIndex >= 0) {
      Vue.set(this.selectReasons, selectedTopReasonIndex, undefined);
      return;
    }
    Vue.set(this.selectReasons, this.selectReasons.length, topOption);
  }

  // 是否选中
  checkReasonItemActive(currentReasonSelectProps: ReasonSelectProps, isTop?: boolean) {
    const { topOption, leafOption } = currentReasonSelectProps;
    if (isTop && topOption.children?.length > 0) {
      return false;
    }
    if (!leafOption) {
      const selectedReason = this.selectReasons.find(item => item && item.index === topOption.index);
      return selectedReason;
    }
    const selectedTopReason = this.selectReasons.find(item => item && item.index === topOption.index);

    const selectedLeafReason = selectedTopReason?.children?.find(
      (item: ReasonOption) => item.index === leafOption.index,
    );
    return selectedLeafReason;
  }

  onCompleteSelectReason() {
    this.form.reasons = cloneDeep(this.selectReasons.filter(item => item !== undefined));
    if (this.form.reasons.length > 0) {
      this.form.selectedReason = '1';
      this.$refs.form.clearValidate();
    }
    this.selectedReasonId = this.form.reasons.map(item => {
      const childrenIndex = item.children.map(child => child.index);
      return childrenIndex.join(',');
    }).join(',');
    this.showReasonSelect = false;
  }

  removeReason(topReasonIndex: number) {
    this.form.reasons.splice(topReasonIndex, 1);
    Vue.set(this.form, 'reasons', cloneDeep(this.form.reasons));
    if (!this.form.reasons?.length) {
      this.form.selectedReason = '';
      this.form.images = [];
    }
  }

  beforeUpload(file) {
    this.claimData = {
      content_type: 'application/javascript',
    };
    return true;
  }

  uploadSuccess(value, file) {
    if (value.error) {
      const { message } = value.error;
      toast(
        this,
        {
          message,
          duration: 3000,
        },
        'claim-drawer-form',
      );
      return;
    }
    const res = value.result;
    this.form.images.push({
      ...res,
      file,
      name: res.storage_name,
      url: res.location_uri,
      file_type: file.type,
    });
  }

  /**
   *
   * @param newFile 新上传的图片
   * @param oldFile 缓存图片，回显
   * @param uploadInfo
   */
  uploadRemoved(newFile, oldFile) {
    const file = newFile || oldFile;
    // 根据源码通过这5个key来判断文件是否唯一
    if (newFile) {
      const keys = ['name', 'size', 'type', 'lastModified', 'webkitRelativePath'];
      this.form.images = this.form.images.filter(function (item) {
        return !keys.every(function (key) {
          return item.file?.[key] === file[key];
        });
      });
    } else {
      this.form.images = this.form.images.filter(item => item.location_uri !== file.url);
    }
  }

  onExceedSize() {
    // TODO 先写死英文，待新增text id后更新
    toast(
      this,
      {
        message: 'The image you upload is too large. Please select image that less than 10MB.',
        duration: 3000,
      },
      'claim-drawer-form',
    );
  }

  onError() {
    toast(
      this,
      {
        message: this.$t('122713'),
        duration: 3000,
      },
      'claim-drawer-form',
    );
  }

  onFileRepeat(file) {
    toast(
      this,
      {
        message: `File: ${file.name} already exists, please select another`,
        duration: 3000,
      },
      'claim-drawer-form',
    );
  }
}
</script>

<style lang="scss" scoped>
.claim-form-mobile {
  ::v-deep .klk-form-item-label {
    white-space: normal;
    word-break: break-word;
    width: 80%;
  }
}

.reasons-box {
  margin-top: 12px;
}

.klk-form-item-box {
  position: relative;
}

.form-item-label {
  &_example {
    position: absolute;
    right: 0;
    top: 0;
    font-size: 14px;
    font-weight: 400;
    text-decoration: underline;
    cursor: pointer;
  }
}

.first-option {
  padding-left: 12px;
  font-weight: 600 !important;
  font-size: 14px !important;
}

::v-deep .klk-option {
  border: 1px solid #eee !important;
  margin: 12px 8px;
}

::v-deep .klk-option-group {
  .klk-option-label {
    padding: 0 !important;
  }

  .klk-option {
    padding: 0 !important;
  }

  .klk-option-selected {
    position: relative;
  }

  .klk-option-checked-icon {
    position: absolute;
    top: 2px;
    right: 20px;
  }
}

.option-content {
  height: 48px;
  line-height: 48px;
  width: 100%;
  padding: 0 20px;
}

.claim-form-desktop {
  ::v-deep .klk-bottom-sheet-inner {
    width: 680px !important;
    left: unset !important;
    right: 0 !important;
  }
}

.icon-add {
  margin-right: 8px;
  display: flex;
  align-items: center;
}

.add-btn {
  display: flex;
  align-items: center;
  justify-content: center;

  ::v-deep span {
    display: flex;
    align-items: center;
  }
}

.top-row {
  margin-bottom: 28px;

  &_label {
    font-size: 16px;
    font-weight: 600;
    line-height: 24px;
    width: 90%;
    word-break: break-word;
  }

  &_desc {
    font-size: 14px;
    font-weight: 400;
    line-height: 21px;
    color: #8a8a8a;
    width: 90%;
    word-break: break-word;
  }

  .icon-check {
    display: none;
  }
}

.select-item {
  border: 1px solid #e6e6e6;
  border-radius: 12px;
  padding: 12px;
  margin: 12px 0;
  cursor: pointer;
  position: relative;

  .icon-check {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    right: 12px;
    display: none;
    color: #212121;
    width: 20px;
    height: 20px;
    border-radius: 20px;
    font-size: 20px;

    circle {
      display: none;
    }
  }

  &_active {
    border: 1px solid #ff5b00;
    background-color: #fff0e5;

    .icon-check {
      display: block;
    }
  }
}

.leaf-row {
  &_label {
    font-size: 14px;
    font-weight: 400;
    line-height: 21px;
  }

  &_desc {
    font-size: 12px;
    font-weight: 400;
    line-height: 21px;
    color: #8a8a8a;
  }
}

.icon-close {
  cursor: pointer;
}

.reason-item {
  padding: 12px 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #ff5b001f;
  margin-bottom: 16px;
  border-radius: 12px;
  width: fit-content;

  .top-reason {
    font-size: 14px;
    font-weight: 600;
    line-height: 21px;
  }

  .leaf-reason {
    font-size: 12px;
    line-height: 18px;
  }

  &_close-box {
    display: flex;
  }

  &_close {
    margin-left: 8px;
  }
}

.klk-upload-enable {
  ::v-deep .klk-upload-btn {
    cursor: pointer !important;
  }
}

.klk-upload-click-box {
  position: relative;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 12px;

  .klk-upload-click {
    width: 80px;
    height: 80px;
    cursor: pointer;
    border-radius: 12px;
    border: 1px solid #f5f5f5;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #8a8a8a;
    background: #f5f5f5;

    .icon-plus {
      width: 24px;
      height: 24px;
      font-size: 24px;
    }
  }

  img {
    width: 80px;
    height: 80px;
    border-radius: 12px;
  }

  ::v-deep .klk-upload-item-close {
    z-index: 0 !important;
  }

  .img-box {
    position: relative;

    .icon-close-box {
      position: absolute;
      width: 18px;
      height: 18px;
      right: -6px;
      top: -6px;
      opacity: 0.8;
      background-color: #000;
      color: #fff;
      font-size: 12px;
      border-radius: 20px;
      display: inline-flex;
      justify-content: center;
      align-items: center;
    }
  }
}
</style>
