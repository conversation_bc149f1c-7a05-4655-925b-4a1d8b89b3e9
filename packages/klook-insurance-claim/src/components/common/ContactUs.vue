<template>
  <div>
    <klk-poptip
      v-if="isDesktop"
      ref="custom_service_poptip"
      placement="left-start"
      :prevent-overflow="true"
      class="custom-service-poptip"
    >
      <IconCustomerService theme="outline" size="20" class="close_icon" :fill="colorTextPrimary" />
      <div slot="content" class="wishlist-poptip">
        <KlkContactUs
          :language="language"
          :platform="platform"
          :klook-contact-list="customServiceData"
          @contact-item-click="handleContactClick"
        />
      </div>
    </klk-poptip>
    <div v-else>
      <div @click="showContactUs = true">
        <IconCustomerService theme="outline" size="20" class="close_icon" :fill="colorTextPrimary" />
      </div>
      <KlkContactUs
        :language="language"
        :platform="platform"
        :klook-contact-list="customServiceData"
        :visible.sync="showContactUs"
        @contact-item-click="handleContactClick"
      />
    </div>
  </div>
</template>

<script lang="ts">
// 引入图标
import { Component, Vue, Prop } from 'vue-property-decorator';
import { IconCustomerService } from '@klook/klook-icons';
import { $colorTextPrimary } from '@klook/klook-ui/lib/utils/design-token-esm';
import KlkContactUs from '@klook/klook-contact-us';
import '@klook/klook-contact-us/dist/esm/index.css';
import { toast } from '../../utils';

@Component({
  components: {
    IconCustomerService,
    KlkContactUs,
  },
})
export default class ContactUs extends Vue {
  @Prop({
    type: String,
    default: 'desktop',
  })
  platform!: string;

  @Prop({
    type: String,
    default: 'en-US',
  })
  language!: string;

  showContactUs = false;

  colorTextPrimary = $colorTextPrimary;

  customServiceData = [];

  get isDesktop() {
    return this.platform === 'desktop';
  }

  async getCustomServiceData() {
    try {
      const res = await this.$axios.get('/v1/insuranceapisrv/outer/contactInfo/getContactInfo', {});
      const data = res.data || {};
      if (data.success && data.result) {
        this.customServiceData = data.result;
      } else {
        const { message } = data?.error || {};
        toast(
          this,
          {
            message,
            duration: 3000,
          },
          'insurance_claims_dialog-detail',
        );
      }
    } catch (message) {
      toast(
        this,
        {
          message,
          duration: 3000,
        },
        'insurance_claims_dialog-detail',
      );
    }
  }

  mounted() {
    this.getCustomServiceData();
  }

  handleContactClick() {}
}
</script>

<style lang="scss" scoped>
.custom-service-poptip {
  ::v-deep .klk-poptip-popper-inner {
    max-height: unset !important;
    padding: 0 !important;
  }
  ::v-deep .klook-traveller-contact-us {
    padding: 16px !important;
    margin-bottom: 0 !important;
  }
}
</style>
