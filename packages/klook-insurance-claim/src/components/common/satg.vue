<template>
  <div>
    <ClaimForm
      :platform="platform"
      :title="title"
      :order-info="orderInfo"
      :claim-api="claimApi"
      :required-reason="true"
      :show-upload="true"
      :hide-f-a-q="true"
      :is-in-app="isInApp"
      inpath-type="SatG"
      @header-click="$emit('header-click')"
      @success="success"
      @show-faq="$emit('show-faq')"
      @back="$emit('back')"
    />
  </div>
</template>

<script lang="ts">
// 引入图标
import { Component, Vue, Prop } from 'vue-property-decorator';
import ClaimForm from './claim-form.vue';
import { OrderInfo } from '../../../types';
import {
  newClaimApi,
} from '../../constant';

@Component({
  components: {
    ClaimForm
  }
})
export default class SatG extends Vue {

  @Prop({ type: String, default: 'desktop' }) platform!: string;

  @Prop({ type: String, default: '' }) orderGuide!: string;

  @Prop({ type: String, default: '' }) bookingNo!: string;

  @Prop({ type: Object, default: {} }) orderInfo!: OrderInfo;

  @Prop({ type: Boolean, default: false }) isInApp?: boolean;

  @Prop({ type: Object, default: () => ({}) }) uploadData!: Record<string,any>;

  @Prop({ type: String, default: '' }) claimTitle!: string;

  claimApi = newClaimApi;

  success(feedbackInfo: Record<string, any>) {
    this.$emit('success', feedbackInfo);
  }

  get title(){
    return this.claimTitle || this.$t('112499');
  }
}
</script>
