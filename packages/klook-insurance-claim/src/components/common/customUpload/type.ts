export enum STATUS {
  UPLOADING = 1,
  <PERSON><PERSON><PERSON>,
  FAILED
}

export interface FileItem {
  status: STATUS,
  url: string,    // which is an ObjectURL
  file: File,
  progress: number
}

export interface FileList {
  [index: number]: Array<FileItem>
}

export interface UploadOptions {
  headers: Object,
  withCredentials: Boolean,
  file: File,
  data: Object,
  filename: string,
  action: string,
  onProgress: Function,
  onSuccess: Function,
  onError: Function
}
