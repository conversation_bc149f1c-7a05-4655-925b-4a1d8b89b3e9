<template>
  <div>
    <div class="klk-upload">
      <!-- file list -->
      <template v-if="!hideFileList">
        <UploadItem
          v-for="(item, i) in items"
          :key="i"
          :item="item"
          @click="clickItem"
          @remove="removeItem"
          @retry="retryUpload"
        />
      </template>
      <!-- default trigger -->
      <slot>
        <div
          v-if="items.length < limit"
          class="klk-upload-btn"
          :disabled="disabled"
          :style="btnStyle"
          @click="triggerClick"
        >
          <input
            ref="input"
            type="file"
            hidden
            :accept="accept"
            :multiple="multiple"
            @change="onFileChange"
            @click.stop="stopPropagationClick"
          />
          <IconPlus theme="filled" size="24" fill="currentColor" />
        </div>
      </slot>
    </div>
    <div v-if="uploadTips" class="klk-upload-tips">{{ uploadTips }}</div>
    <!-- image viewer -->
    <klk-image-viewer
      :open.sync="openImageViewer"
      :value="viewIndex"
      :images="images"
      :width="960"
    ></klk-image-viewer>
  </div>
</template>

<script>
import UploadItem, { STATUS } from './UploadItem.vue';
import upload from './upload.ts';
import { IconPlus } from '@klook/klook-icons';

let defaultCloudinaryOpts = {
  // "upload_preset": "upload_preset",
};
export function setCloudinaryOptions(options = {}) {
  defaultCloudinaryOpts = Object.assign({}, defaultCloudinaryOpts, options);
}

export default {
  name: 'klk-upload',
  components: {
    UploadItem,
    IconPlus,
  },
  props: {
    action: {
      type: String,
      required: true,
    },
    name: {
      type: String,
      default: 'file',
    },
    size: {
      type: Number,
      default: 80,
    },
    limit: {
      type: Number,
      default: 6,
    },
    uploadTips: String,
    multiple: Boolean,
    accept: String,
    onlyUpload: Boolean,
    cloudinary: Boolean,
    cloudinaryOptions: Object,
    headers: Object,
    withCredentials: Boolean,
    data: Object,
    disabled: Boolean,
    fileList: {
      type: Array,
      default: () => [],
      validator(list) {
        return list.every(item => typeof item === 'string');
      },
    },
    maxFileSize: Number,
    beforeUpload: Function,
    beforeRemove: Function,
    hideFileList: Boolean,
    extensions: Array,
    removable: {
      type: Boolean,
      default: true,
    },
    repeatable: Boolean,
  },
  data() {
    return {
      items: [],
      openImageViewer: false,
      viewIndex: 0,
    };
  },
  computed: {
    btnStyle() {
      return {
        width: `${this.size}px`,
        height: `${this.size}px`,
      };
    },
    doneItems() {
      return this.items.filter(item => item.status === STATUS.DONE);
    },
    images() {
      return this.doneItems.map(item => item.url);
    },
  },
  watch: {
    fileList: {
      handler(list) {
        this.items = list.map(l => this.getItem({ status: STATUS.DONE, url: l }));
      },
      immediate: true,
    },
  },
  beforeDestroy() {
    this.items.forEach(item => {
      item.file = null;
      item.req = null;
      if (item.url && item.url.startsWith('blob:')) {
        URL.revokeObjectURL(item.url);
      }
    });
  },
  methods: {
    triggerClick() {
      if (this.$listeners['trigger-click']) {
        this.$emit('trigger-click');
        return;
      }
      this.upload();
    },
    stopPropagationClick(e) {
      // 阻止冒泡用，避免影响外层的点击事件
    },
    upload() {
      if (this.$refs.input && !this.disabled) {
        this.$refs.input.value = null;
        this.$refs.input.click();
      }
    },
    onFileChange(e) {
      const files = e.target.files || [];
      if (!files.length) return;
      this.uploadFiles(Array.from(files));
    },
    uploadFiles(files) {
      files = this.multiple ? files.slice(0, this.limit - this.items.length) : files.slice(0, 1);
      files.forEach(file => this.uploadFile(file));
    },
    async uploadFile(file, givenItem) {
      if (!givenItem && this.items.length >= this.limit) return;
      if (!this.isFileValid(file)) return;
      // before upload
      const canUpload = await this.callBeforeHook(this.beforeUpload, file);
      if (!canUpload) return;
      const item = givenItem || this.getItem({ file });
      if (!givenItem) {
        if (this.onlyUpload) {
          if (this.items.length >= this.limit) {
            this.$emit('only-upload-exceeds-limit');
            return;
          }
          this.items.push({
            url: URL.createObjectURL(file),
            file: file,
            status: STATUS.DONE,
            req: null,
          });
          this.$emit('only-upload-success', file);
          return;
        } else {
          this.items.push(item);
        }
      }
      // cloudinaryOptions
      const cloudinaryOptions = this.cloudinaryOptions
        ? Object.assign({}, defaultCloudinaryOpts, this.cloudinaryOptions)
        : {};
      const options = {
        headers: this.headers,
        withCredentials: this.withCredentials,
        file,
        data: {
          ...cloudinaryOptions,
          ...this.data,
        },
        filename: this.name,
        action: this.action,
        onProgress: e => {
          // console.log('>>> onProgress', e);
          item.progress = e.percent;
          this.$emit('progress', e, file);
        },
        onSuccess: res => {
          // console.log('>>> onSuccess', res, file);
          item.url = URL.createObjectURL(file);
          item.status = STATUS.DONE;
          // delete item.file;
          // item.file = null;
          item.req = null;
          this.$emit('success', res, file, item);
        },
        onError: err => {
          // console.log('>>> onError', err);
          item.status = STATUS.FAILED;
          item.req = null;
          if (!this.$listeners['error']) {
            this.showError(err.toString());
          }
          this.$emit('error', err, file);
        },
      };
      const req = upload(options);
      item.req = req;
      if (req && req.then) {
        req.then(options.onSuccess, options.onError);
      }
    },
    // 检测文件是否有效
    isFileValid(file) {
      // check accept types
      if (
        this.accept &&
        !this.accept
          .split(',')
          .map(ac => ac.trim())
          .includes(file.type)
      ) {
        if (!this.$listeners['type-invalid']) {
          this.showError(`Invalid file type: ${file.type}, available: ${this.accept}`);
        }
        this.$emit('type-invalid', file);
        return false;
      }
      // check extensions
      if (this.extensions && this.extensions.length) {
        const index = file.name.lastIndexOf('.');
        if (index > -1) {
          const ext = file.name.slice(index);
          if (!this.extensions.includes(ext)) return false;
        }
      }
      // check size (kb)
      if (this.maxFileSize && file.size / 1024 > this.maxFileSize) {
        const curFileSize = (file.size / 1024).toFixed(2);
        if (!this.$listeners['exceed-size']) {
          this.showError(`Exceed max file size: ${this.maxFileSize} KB, current size: ${curFileSize} KB`);
        }
        this.$emit('exceed-size', file, curFileSize);
        return false;
      }
      // check file exists
      if (!this.repeatable && this.isFileExists(file)) {
        if (!this.$listeners['file-repeat']) {
          this.showError(`File: ${file.name} already exists, please select another`);
        }
        this.$emit('file-repeat', file);
        return false;
      }
      return true;
    },
    /*
      检测文件是否已存在
      TODO: 这里为了性能考虑仅仅对比文件属性，更好的方式是通过读取文件内容计算出文件哈希 (md5) 进行对比：
      const fileReader = new FileReader();
      fileReader.readAsBinaryString(file);
      fileReader.onload = e => {
        const md5 = MD5(e.target.result)
      }
      但是计算 md5 需要引入额外的前端 md5 库，会增大组件库体积，仅对比文件内容则需存储/读取文件内容，在文件较大时太慢
    */
    isFileExists(file) {
      const keys = ['name', 'size', 'type', 'lastModified', 'webkitRelativePath'];
      return this.doneItems.some(item => {
        if (!item.file) return false;
        return keys.every(key => item.file[key] === file[key]);
      });
    },
    async removeItem(item) {
      const canRemove = await this.callBeforeHook(this.beforeRemove, item.file);
      if (!canRemove) return;
      const index = this.items.indexOf(item);
      if (index > -1) {
        this.items.splice(index, 1);
        item.req && item.req.abort();
        this.$emit('remove', item.file, item);
      }
    },
    clickItem(item) {
      if (item.status !== STATUS.DONE || !item.url) return;
      const index = this.images.indexOf(item.url);
      if (index < 0) return;
      this.viewIndex = index;
      this.openImageViewer = true;
    },
    retryUpload(item) {
      // console.log('>>> retryUpload', item);
      if (!item.file) return;
      // reset
      item.status = STATUS.UPLOADING;
      item.url = '';
      item.progress = 0;
      this.$emit('retry', item.file);
      this.uploadFile(item.file, item);
    },
    showError(message) {
      this.$toast({
        message,
        duration: 3000,
        maxWidth: 320,
      });
    },
    getItem(item = {}) {
      return Object.assign(
        {
          status: STATUS.UPLOADING,
          url: '',
          progress: 0,
          file: null,
        },
        item,
      );
    },
    clear() {
      this.items = [];
    },
    async callBeforeHook(hook, file) {
      let result = true;
      if (typeof hook === 'function') {
        try {
          const res = await hook(file);
          if (res === false) result = false;
        } catch (e) {
          e && this.showError(e.message);
          result = false;
        }
      }
      return result;
    },
  },
};
</script>
