<template>
  <div class="klk-upload-item" :style="cStyle" @click.self="$emit('click', item)">
    <span v-if="showClose" class="klk-upload-item-close" @click="$emit('remove', item)">
      <IconClear theme="filled" size="20" style="opacity: 0.8;" :fill="$colorTextPrimary" />
    </span>
    <div v-if="item.status !== STATUS.DONE" class="klk-upload-item-mask">
      <template v-if="item.status === STATUS.FAILED">
        <klk-icon type="icon_edit_update" size="24" @click="$emit('retry', item)" />
        <div class="klk-upload-item-err">Failed</div>
      </template>
      <template v-else-if="item.status === STATUS.UPLOADING">
        <Loading />
        <div v-if="item.progress" class="klk-upload-item-progress">
          {{ item.progress.toFixed(2) }}%
        </div>
      </template>
    </div>
  </div>
</template>

<script>
import Loading from './Loading.vue';
import { IconClear } from '@klook/klook-icons'
import { $colorTextPrimary } from '@klook/klook-ui/lib/utils/design-token-esm';

export const STATUS = {
  UPLOADING: 1,
  DONE: 2,
  FAILED: 3
};

export default {
  components: {
    Loading,
    IconClear
  },
  props: {
    item: {
      type: Object,
      required: true
    },
  },
  data () {
    return {
      STATUS
    };
  },
  computed: {
    $colorTextPrimary() {
      return $colorTextPrimary
    },
    cStyle () {
      const { size } = this.$parent;
      return {
        width: `${size}px`,
        height: `${size}px`,
        backgroundImage: `url(${this.item.url})`
      };
    },
    showClose () {
      const { removable, disabled } = this.$parent;
      return removable && !disabled;
    }
  },
};
</script>
