<template>
  <div
    class="common-section"
  >
    <div v-if="title">
      <span class="common-section_title">
        {{ title }}
      </span>
    </div>
    <slot />
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';

@Component
export default class SectionWrapper extends Vue {
  @Prop({ type: String, default: '' }) title!: string;
}
</script>

<style lang="scss" scoped>
  .common-section{
    background-color: #fff;
    padding: 24px 20px;
    border-radius: 16px;

    &_title{
      font-size: 20px;
      font-weight: 600;
    }
  }
</style>
