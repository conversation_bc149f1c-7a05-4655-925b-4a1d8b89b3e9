<template>
  <div>
    <klk-form ref="form" class="claim_form" :model="form">
      <section-wrapper :title="$t('15957-insurance_select_unit')" :style="{ marginTop: 0 }">
        <div v-if="bookingTypeMap.bookinkType === BOOKING_TYPE_ENUM.perUnit">
          <div v-if="unitRequiredTips" class="tips">
            {{ unitRequiredTips }}
          </div>
          <klk-form-item prop="groupChooses" :style="{ width: '100%' }">
            <klk-checkbox-group
              ref="groupChooses"
              v-model="groupChooses"
              class="checkbox-group required"
              type="textarea"
              name="groupChooses"
              @change="$emit('change')"
            >
              <div v-for="(item, index) in unitList" :key="index" class="unit-checkbox-box">
                <klk-checkbox :disabled="!item.can_check" :group-value="item.group_choose">
                  <div>
                    <div class="title">
                      {{ item.sku_name }}
                    </div>
                  </div>
                </klk-checkbox>
                <span v-if="!item.can_check && item.can_not_check_str" class="claim_unit_status">
                  {{ item.can_not_check_str }}
                </span>
              </div>
            </klk-checkbox-group>
          </klk-form-item>
        </div>
        <div v-else-if="bookingTypeMap.bookinkType === BOOKING_TYPE_ENUM.perUnitType">
          <div v-if="unitRequiredTips" class="tips">
            {{ unitRequiredTips }}
          </div>
          <klk-form-item prop="groupChooses" :style="{ width: '100%' }">
            <klk-checkbox-group
              ref="groupChooses"
              v-model="groupChooses"
              class="checkbox-group required"
              type="textarea"
              name="groupChooses"
              @change="$emit('change')"
            >
              <div
                v-for="(key, index) in Object.keys(bookingTypeMap.skuCountMap)"
                :key="index"
                class="unit-checkbox-box"
              >
                <klk-checkbox
                  :disabled="!bookingTypeMap.skuCountMap[key].can_check"
                  :group-value="bookingTypeMap.skuCountMap[key].group_choose"
                >
                  <div>
                    <div class="title">
                      {{ bookingTypeMap.skuCountMap[key].count + ' x' }}
                      {{ key }}
                    </div>
                  </div>
                </klk-checkbox>
                <span
                  v-if="
                    !bookingTypeMap.skuCountMap[key].can_check &&
                    bookingTypeMap.skuCountMap[key].can_not_check_str
                  "
                  class="claim_unit_status"
                >
                  {{ bookingTypeMap.skuCountMap[key].can_not_check_str }}
                </span>
              </div>
            </klk-checkbox-group>
          </klk-form-item>
        </div>
        <div v-else>
          <div class="perbooking-title">{{ bookingTypeMap.skuCountMapStr }}</div>
          <div class="perbooking-content">{{ $t('176290') }}</div>
        </div>
      </section-wrapper>
    </klk-form>
  </div>
</template>

<script lang="ts">
// 引入图标
import { Component, Vue, Prop, Watch } from 'vue-property-decorator';
import SectionWrapper from './section-wrapper.vue';
import { BOOKING_TYPE_ENUM } from '../../constant';

@Component({
  components: {
    SectionWrapper,
  },
})
export default class SelectUnit extends Vue {
  @Prop({ type: String, default: 'desktop' }) platform!: string;

  @Prop({ type: String }) unitRequiredTips?: string;

  @Prop({ type: Array, default: [] }) unitList!: Array<Record<string, string | number>>;

  @Prop({ type: Object, default: {} })
  defaultFormValue!: Record<string, any>;

  form = {};

  groupChooses = [];

  BOOKING_TYPE_ENUM = BOOKING_TYPE_ENUM;

  requiredTips: string | undefined = '';

  // per booking,per unit type,per unit
  get bookingTypeMap() {
    const skuCountMap: Record<string, any> = {};
    let skuCountMapStr = '';
    this.unitList.forEach(item => {
      if (skuCountMap[item.sku_name]) {
        skuCountMap[item.sku_name].count += 1;
      } else {
        const skuObj = {
          count: 1,
          can_check: item.can_check,
          group_choose: item.group_choose,
          can_not_check_str: item.can_not_check_str,
        };
        skuCountMap[item.sku_name] = skuObj;
      }
    });
    let bookinkType = BOOKING_TYPE_ENUM.perUnit;
    const allGroupChooseArr = this.unitList.map(item => item.group_choose);
    const unDuplicateGroupChooseArr = [...new Set(allGroupChooseArr)];
    // per booking
    if (unDuplicateGroupChooseArr.length === 1) {
      bookinkType = BOOKING_TYPE_ENUM.perBooking;
      skuCountMapStr = Object.keys(skuCountMap)
        .map(skuName => `${skuCountMap[skuName].count} x ${skuName}`)
        .join(',');
      // per unit type
    } else if (
      unDuplicateGroupChooseArr.length > 1 &&
      unDuplicateGroupChooseArr.length < allGroupChooseArr.length
    ) {
      bookinkType = BOOKING_TYPE_ENUM.perUnitType;
      // per unit
    } else {
      bookinkType = BOOKING_TYPE_ENUM.perUnit;
    }
    return { bookinkType, skuCountMap, skuCountMapStr };
  }

  get claimUnitStatusMap() {
    return {
      1: this.$t('15083-insurance_used'),
      2: this.$t('15927-insurance_refunded'),
      3: this.$t('15544-insurance_pending'),
      4: this.$t('14065-insurance_claimed'),
    };
  }

  get isDesktop() {
    return this.platform === 'desktop';
  }

  validateFormValues() {
    if (this.bookingTypeMap.bookinkType === BOOKING_TYPE_ENUM.perBooking) {
      return true;
    }
    return this.groupChooses.length > 0;
  }

  getFormValues() {
    if (this.bookingTypeMap.bookinkType === BOOKING_TYPE_ENUM.perBooking) {
      return [this.unitList[0].group_choose];
    }
    return this.groupChooses;
  }

  mounted() {
    this.groupChooses = this.defaultFormValue.group_chooses;
  }
}
</script>

<style lang="scss" scoped>
.unit-checkbox-box {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  font-weight: 400;
  padding: 12px 0;

  .klk-checkbox {
    margin-right: 12px;
    display: flex;
    align-items: center;
    width: 100%;
    height: 100%;
    margin-top: 0 !important;
    margin-bottom: 0 !important;
  }

  ::v-deep .klk-checkbox-label {
    display: inline-block;
    width: 100%;
  }

  .title {
    font-size: 16px;
    line-height: 24px;
    color: $color-text-primary;
  }

  .content {
    font-size: 14px;
    line-height: 21px;
    color: $color-text-secondary;
    margin-top: 4px;
  }
}

.tips {
  margin-top: 8px;
  font-size: $fontSize-body-s;
  color: #eb4221;
  line-height: 16px;
}

.unit-checkbox-box-between {
  justify-content: space-between;
  flex-direction: row-reverse;
}

.checkbox-group .unit-checkbox-box:not(:last-child) {
  border-bottom: 1px solid #eeeeee;
}

.checkbox-group .unit-checkbox-box:last-child {
  padding-bottom: 0;

  ::v-deep .klk-checkbox {
    margin-bottom: 0;
  }
}
.perbooking-title {
  font-size: 16px;
  font-weight: 600;
  line-height: 24px;
  text-align: left;
}
.perbooking-content {
  font-size: 14px;
  font-weight: 400;
  line-height: 21px;
  text-align: left;
  margin-top: 4px;
}
.claim_unit_status {
  white-space: nowrap;
}
</style>
