<template>
  <section-wrapper :title="$t('176292')" :style="{ marginTop: '12px' }">
    <div v-if="unitRequiredTips" class="tips">
      {{ unitRequiredTips }}
    </div>
    <div class="subtitle">
      <div class="subtitle_name">{{ `${form.first_name} ${form.last_name}` }}</div>
      <div @click="toEdit()"><IconEdit class="edit-icon" /></div>
    </div>
    <div class="desc">{{ form.email }}</div>
    <klk-drawer
      v-if="isDesktop && showTravellerForm"
      direction="right"
      class="claim-drawer"
      id="claim-drawer"
      mask-color="transparent"
      :visible.sync="showTravellerForm"
      :mask-closable="false"
      :padding="0"
      :width="680"
      @close="showTravellerForm = false"
    >
      <div id="insurance_claims_dialog" class="insurance_claims_dialog">
        <ClaimFormHeader
          :title="$t('176292')"
          :platform="platform"
          :iconBack="true"
          @click="showTravellerForm = false"
        >
          <klk-button type="outlined" size="small" @click="onEdit">{{ $t('47672') }}</klk-button>
        </ClaimFormHeader>
        <TravellerForm :default-form-value="form" :platform="platform" ref="traveller_info" />
      </div>
    </klk-drawer>
    <klk-bottom-sheet
      v-else
      :visible.sync="showTravellerForm"
      :header-divider="true"
      :can-pull-close="false"
      :mask-closable="false"
      :title="$t('176292')"
      @close="showTravellerForm = false"
    >
      <div slot="header-left" @click="showTravellerForm = false">
        <IconClose class="icon-close" />
      </div>
      <div slot="header-right" @click="onEdit">
        <klk-button type="outlined" size="small" @click="onEdit">{{ $t('47672') }}</klk-button>
      </div>
      <div id="insurance_claims_dialog">
        <TravellerForm :default-form-value="form" :platform="platform" ref="traveller_info" />
      </div>
    </klk-bottom-sheet>
  </section-wrapper>
</template>

<script lang="ts">
// 引入图标
import { Component, Vue, Prop, Watch } from 'vue-property-decorator';
import { IconEdit, IconClose } from '@klook/klook-icons';
import SectionWrapper from '../section-wrapper.vue';
import ClaimFormHeader from '../header.vue';
import TravellerForm from './traveller-form.vue';
import cloneDeep from 'lodash/cloneDeep';

@Component({
  components: {
    SectionWrapper,
    IconEdit,
    ClaimFormHeader,
    TravellerForm,
    IconClose,
  },
})
export default class TravellerInfo extends Vue {
  @Prop({ type: String, default: 'desktop' }) platform!: string;

  @Prop({ type: Boolean, default: false }) disabled?: boolean;

  @Prop({ type: Boolean, default: false }) isInApp?: boolean;

  @Prop({ type: Object, default: { first_name: '', gender: '', last_name: '', email: '' } })
  defaultFormValue!: Record<string, string>;

  form: Record<string, string> = { first_name: '', gender: '', last_name: '', email: '' };

  showTravellerForm = false;

  unitRequiredTips = '';

  validateRes = {};

  get isDesktop() {
    return this.platform === 'desktop';
  }

  @Watch('defaultFormValue', { immediate: true })
  updateDefaultFormValue() {
    this.form = cloneDeep(this.defaultFormValue);
  }

  async onEdit() {
    const validateRes = await this.$refs.traveller_info.validateFormValues();
    if (!validateRes) {
      return;
    }
    this.$emit('on-edit', this.$refs.traveller_info.getFormValues());
    this.form = this.$refs.traveller_info.getFormValues();
    this.showTravellerForm = false;
  }

  toEdit() {
    this.showTravellerForm = true;
    this.unitRequiredTips = '';
    this.form = cloneDeep(this.defaultFormValue);
  }

  getFormValues() {
    return this.form;
  }
  validateFormValues() {
    const hasFalseValue = Object.keys(this.form).find(key => !this.form[key]);
    if (hasFalseValue) {
      this.unitRequiredTips = this.$t('13134');
    } else {
      this.unitRequiredTips = '';
    }
    return new Promise((resolve, reject) => {
      hasFalseValue ? resolve(false) : resolve(true);
    });
  }
}
</script>

<style lang="scss" scoped>
.subtitle {
  font-size: 16px;
  font-weight: 600;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 12px auto 8px;

  &_name {
    width: 80%;
    white-space: normal;
    word-break: break-word;
  }

  .edit-icon {
    font-size: 20px;
    width: 20px;
    height: 20px;
    cursor: pointer;
  }
}
.desc {
  font-size: 14px;
  font-weight: 400;
  width: 80%;
  white-space: normal;
  word-break: break-word;
}
.traveller-box {
  padding: 16px 20px;
}
.tips {
  margin-top: 8px;
  font-size: $fontSize-body-s;
  color: #eb4221;
  line-height: 16px;
}
.claim-form-header-mobile {
  padding: 0;
}
</style>
