<template>
  <klk-form ref="form" class="claim_form" :model="form" :rules="validateRules">
    <div
      class="traveller-box"
      :class="isDesktop ? 'traveller-box-desktop' : 'traveller-box-mobile'"
      ref="travellerInfo"
    >
      <klk-form-item :label="$t('14036-insurance_title')" prop="gender">
        <klk-select
          ref="claimSelectGender"
          v-model="form.gender"
          name="claim_gender"
          class-name="claim_gender_selector"
          :disabled="disabled"
          :show-required-star="true"
          :placeholder="$t('15632-global.select.palceholder')"
        >
          <klk-option
            v-for="option in genders"
            :key="option.value"
            :value="option.value"
            :label="option.text"
          />
        </klk-select>
      </klk-form-item>

      <klk-form-item :label="$t('5948-insurance_first_name')" prop="first_name">
        <klk-input
          ref="claimFirstName"
          v-model="form.first_name"
          name="claimFirstName"
          class-name="required"
          :maxlength="maxlength"
          :disabled="disabled"
          :placeholder="$t('14044-sign_please')"
        />
      </klk-form-item>

      <klk-form-item :label="$t('15873-insurance_last_name')" prop="last_name">
        <klk-input
          ref="claimSurName"
          v-model="form.last_name"
          name="claimSurName"
          :maxlength="maxlength"
          :disabled="disabled"
          class-name="required"
          :placeholder="$t('14044-sign_please')"
        />
      </klk-form-item>

      <klk-form-item :label="$t('14828-insurance_email_address')" prop="email">
        <klk-input
          ref="claimEmail"
          v-model="form.email"
          :maxlength="maxlength"
          :disabled="disabled"
          name="claimEmail"
          class-name="required"
          :placeholder="$t('14044-sign_please')"
        />
      </klk-form-item>
    </div>
  </klk-form>
</template>

<script lang="ts">
// 引入图标
import { Component, Vue, Prop, Watch } from 'vue-property-decorator';
import cloneDeep from 'lodash/cloneDeep';

@Component({
  components: {},
})
export default class TravellerInfo extends Vue {
  @Prop({ type: String, default: 'desktop' }) platform!: string;

  @Prop({ type: Boolean, default: false }) disabled?: boolean;

  form: Record<string, string> = { first_name: '', gender: '', last_name: '', email: '' };

  @Prop({ type: Object, default: { first_name: '', gender: '', last_name: '', email: '' } })
  defaultFormValue!: Record<string, string>;

  showTravellerForm = false;

  maxlength = 50;

  get isDesktop() {
    return this.platform === 'desktop';
  }

  get genders() {
    return [
      {
        value: 'MR',
        text: this.$t('578-pay.user_title.mr'),
      },
      {
        value: 'MISS',
        text: this.$t('581-pay.user_title.miss'),
      },
      {
        value: 'MRS',
        text: this.$t('579-pay.user_title.mrs'),
      },
    ];
  }

  get validateRules() {
    return {
      first_name: [
        {
          required: true,
          message: this.$t('78177'),
        },
      ],
      last_name: [
        {
          required: true,
          message: this.$t('78178'),
        },
      ],
      email: [
        {
          required: true,
          message: this.$t('78179'),
          type: 'email',
        },
      ],
    };
  }

  @Watch('defaultFormValue', { immediate: true })
  updateDefaultFormValue() {
    this.form = cloneDeep(this.defaultFormValue);
  }

  getFormValues() {
    return this.form;
  }

  validateFormValues() {
    return new Promise((resolve, reject) => {
      this.$refs.form
        .validate()
        .then(() => {
          resolve(true);
        })
        .catch((error: any) => {
          this.$refs.travellerInfo.scrollIntoView();
          reject(error);
        });
    });
  }
}
</script>

<style lang="scss" scoped>
.subtitle {
  font-size: 16px;
  font-weight: 600;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 12px auto 8px;
}
.desc {
  font-size: 14px;
  font-weight: 400;
}
.traveller-box {
  padding: 16px 20px;
}
.traveller-box-mobile {
  padding: 16px 0;
}
</style>
