<template>
  <div>
    <klk-drawer
      v-if="showClaimBundleForm"
      direction="right"
      class="claim-drawer-form"
      id="claim-drawer-form"
      :visible.sync="showClaimBundleForm"
      :mask-closable="false"
      :padding="0"
      :width="680"
      @close="showClaimBundleForm = false"
    >
      <div id="insurance_bundle_claims_dialog-form" class="insurance_claims_dialog insurance_bundle_claims_dialog">
          <BundleSelect
            platform="desktop"
            @close="hideBundleSelect"
            @confirm="handleShowNextClaimLayer"
          />
      </div>
    </klk-drawer>

    <klk-drawer
      v-if="showClaimForm"
      direction="right"
      class="claim-drawer-form"
      id="claim-drawer-form"
      :visible.sync="showClaimForm"
      :mask-closable="false"
      :padding="0"
      :width="680"
      @close="showClaimForm = false"
    >
      <div id="insurance_claims_dialog-form" class="insurance_claims_dialog">
        <ClaimSuccess
          v-if="claimSuccess && showClaimForm"
          platform="desktop"
          :feedbackInfo="feedbackInfo"
          @confirm="confirmClaimSuccess"
        />
        <!-- 为了内部的pageview埋点能在正确时机上报，加上v-if="showClaimForm" -->
        <GoodsClaim
          v-if="!claimSuccess && showClaimForm"
          platform="desktop"
          :order-info="claim"
          :bundle-inpath-type="bundleInpathType"
          @success="handleClaimSuccess"
          @header-click="showClaimForm = false"
          @show-faq="showFaq = true"
          @back="refresh"
        />
      </div>
    </klk-drawer>

    <klk-drawer
      direction="right"
      class="claim-drawer"
      id="claim-drawer-detail"
      :visible.sync="showClaimDetail"
      :mask-closable="false"
      :padding="0"
      :width="680"
      @close="showClaimDetail = false"
    >
      <div
        v-if="detailParams.applicationNo || detailParams.insOrderNo"
        id="insurance_claims_dialog-detail"
        class="insurance_claims_dialog"
      >
        <ClaimDetail
          v-if="detailParams.claimDetailList.length === 1"
          :application-no="detailParams.applicationNo"
          :insurance-type="detailParams.insuranceType"
          platform="desktop"
          @show-faq="showFaq = true"
          @header-click="backToList"
        />
        <ClaimList
          v-else
          platform="desktop"
          :ins-order-no="detailParams.insOrderNo"
          :insurance-type="detailParams.insuranceType"
          @header-click="showClaimDetail = false"
          @show-faq="showFaq = true"
          @to-detail="handleToDetail"
        />
      </div>
    </klk-drawer>

    <klk-drawer
      direction="right"
      class="claim-drawer"
      id="claim-drawer"
      mask-color="transparent"
      :visible.sync="showFaq"
      :mask-closable="false"
      :padding="0"
      :width="680"
      @close="showFaq = false"
    >
      <div id="insurance_claims_dialog" class="insurance_claims_dialog">
        <FAQ
          v-if="claim.insurance_type"
          platform="desktop"
          :lang-path="languagePath"
          :insurance-type="claim.insurance_type"
          @header-click="showFaq = false"
        />
      </div>
    </klk-drawer>
  </div>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';
import { defaultClaimVersion } from '../../constant';
import GoodsClaim from '../common/export/goods-claim.vue';
import ClaimList from '../common/export/claim-list.vue';
import ClaimDetail from '../common/export/claim-detail/index.vue';
import ClaimSuccess from '../common/export/claim-success.vue';
import BundleSelect from '../common/export/bundle-select.vue';
import FAQ from '../common/export/faq.vue';

@Component({
  components: {
    GoodsClaim,
    ClaimList,
    ClaimDetail,
    ClaimSuccess,
    FAQ,
    BundleSelect
  },
})
export default class ClaimLayer extends Vue {
  showClaimBundleForm: boolean = false;
  bundleInpathType: string = '' // 'NSR' or 'SatG'

  showClaimForm: boolean = false;
  showClaimDetail: boolean = false;
  claimSuccess: boolean = false;
  showFaq: boolean = false;
  languagePath: string = 'en-US/';
  detailParams: Record<string, any> = {
    claimDetailList: [],
  };

  feedbackInfo: Record<string, any> = {};

  claim = {
    activity_id: '',
    claim_unit: [],
    faqs: [],
    sku_ids: [],
    user_info: {},
    ins_order_no: '',
    data_source: 0,
    insurance_type: 0,
    addon_booking_ref_no: 0,
    goods_code: '',
  };

  isBundle(claimInfo){
    if (claimInfo.goods_category === 6) {
      return true
    }
    return false
  }

  async handleShowNextClaimLayer(type: 'SatG' | 'NSR') {
    console.log('handleShowNextClaimLayer type:', type)
    this.bundleInpathType = type
    this.showClaimBundleForm = false;
    this.showClaimForm = true;
  }

  async hideBundleSelect() {
    this.showClaimBundleForm = false;
  }

  async handleShowLayer(claimInfo) {
    this.claim = { ...claimInfo, claim_version: claimInfo.claim_version || defaultClaimVersion };
    this.languagePath = `${claimInfo.language}/`;

    if(this.isBundle(claimInfo)) {
      this.showClaimBundleForm = true;
    } else {
      this.showClaimForm = true;
    }
  }
  handleClaimSuccess(feedbackInfo: Record<string, any>) {
    this.claimSuccess = true;
    this.feedbackInfo = feedbackInfo;
  }

  refresh() {
    window.location.reload();
  }

  confirmClaimSuccess() {
    this.$emit('success');
    this.showClaimForm = false;
  }

  handleShowClaimDetail(detailParams: Record<string, any>) {
    this.claim = { ...this.claim, insurance_type: detailParams.insuranceType };
    this.detailParams = detailParams;
    this.showClaimDetail = true;
  }

  handleToDetail(detail: Record<string, any>) {
    this.detailParams = {
      ...this.detailParams,
      originalClaimDetailList: this.detailParams.claimDetailList,
      claimDetailList: [detail],
      applicationNo: detail.application_no,
    };
  }
  backToList() {
    if (!this.detailParams?.originalClaimDetailList) {
      this.showClaimDetail = false;
      return;
    }
    this.detailParams = {
      ...this.detailParams,
      claimDetailList: this.detailParams.originalClaimDetailList,
    };
  }
}
</script>

<style lang="scss">
.claim-drawer {
  z-index: 1999 !important;
}
.claim-drawer-form {
  z-index: 1999 !important;
  .klk-toast {
    right: 170px !important;
    transform: translate(0, -50%) !important;
    left: unset !important;
    width: 320px !important;
  }
}
.insurance_claims_dialog {
  width: 680px;
  height: 100%;
}
</style>
