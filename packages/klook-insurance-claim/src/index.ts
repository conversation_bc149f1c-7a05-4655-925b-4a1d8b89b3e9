import KlookInsuranceClaim from './components/common/export/index.vue';
import GoodsClaim from './components/common/export/goods-claim.vue';
import BundleSelect from './components/common/export/bundle-select.vue'
import Faq from './components/common/export/faq.vue';
import UploadPhoto from './components/common/export/upload-photo.vue';
import ClaimSuccess from './components/common/export/claim-success.vue';
import ClaimDetail from './components/common/export/claim-detail/index.vue';
import ClaimList from './components/common/export/claim-list.vue';

KlookInsuranceClaim.install = function (Vue) {
  Vue.component(KlookInsuranceClaim.name, KlookInsuranceClaim);
};

export default KlookInsuranceClaim;
export { GoodsClaim, Faq, UploadPhoto, ClaimSuccess, ClaimDetail, ClaimList, BundleSelect };
