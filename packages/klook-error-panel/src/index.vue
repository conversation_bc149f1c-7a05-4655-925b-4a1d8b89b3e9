<template>
  <Klk-empty-panel
    :title="title"
    :content="content"
    :iconWidth="200"
    :iconHeight="138"
    :icon-src="imageUrl"
    :secondary-btn-text="errBtnText"
    :secondary-btn-link="redirectUrl"
    :secondary-btn-attrs="btnAttrs"
    v-on="$listeners"
  >
  </Klk-empty-panel>
</template>

<script lang="ts">
import { Vue, Component, Prop } from "vue-property-decorator";
import KlkEmptyPanel from "@klook/empty-panel";
import "@klook/empty-panel/dist/esm/index.css";
@Component({
  name: "KlookErrorPanel",
  components: {
    KlkEmptyPanel,
  },
})
export default class KlookErrorPanel extends Vue {
  @Prop({ type: Number, default: () => 404 }) status!: number;
  @Prop({ type: String }) title!: string;
  @Prop({ type: String }) content!: string;
  @Prop({ type: String, default: () => "/" })
  redirectUrl!: string;
  @Prop({ type: Object }) btnAttrs!: object;

  @Prop({ type: String }) btnText!: string;
  @Prop({ type: Function }) btnRefreshFn!: Function;
  @Prop({ type: Number, default: () => 1000 }) btnRefreshInterval!: number;
  @Prop({ type: Number, default: () => 5 }) btnRefreshTimes!: number;

  errBtnText = "";
  refreshTime = 0;
  img404 =
    "https://res.klook.com/image/upload/fl_lossy.progressive,q_auto/v1663832213/gc9aubc62mzsqta469a7.png";
  img500 =
    "https://res.klook.com/image/upload/fl_lossy.progressive,q_auto/v1663832446/gy9h23qm0nsxbsplblsw.png";

  get imageUrl() {
    return this.status === 404 ? this.img404 : this.img500;
  }

  btnTextRefresh(time: number) {
    this.errBtnText = this.btnRefreshFn(time);

    if (time) {
      this.refreshTime -= 1;

      const timerId = setTimeout(() => {
        this.btnTextRefresh(this.refreshTime);
      }, this.btnRefreshInterval);

      this.$once("hook:beforeDestroy", () => {
        clearTimeout(timerId);
      });
    }
  }

  created(){
    // 防止 SSR 首屏闪烁
    if (this.btnText) {
      this.errBtnText = this.btnText;
    }
    if (this.btnRefreshFn) {
      this.errBtnText = this.btnRefreshFn(this.btnRefreshTimes);
    }
  }

  mounted() {
    if (this.btnRefreshFn) {
      this.refreshTime = this.btnRefreshTimes;
      this.btnTextRefresh(this.btnRefreshTimes);
    }
  }
}
</script>
