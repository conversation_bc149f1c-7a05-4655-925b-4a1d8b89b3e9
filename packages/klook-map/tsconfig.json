{"compilerOptions": {"target": "esnext", "module": "esnext", "strict": true, "jsx": "preserve", "importHelpers": true, "moduleResolution": "node", "esModuleInterop": true, "allowSyntheticDefaultImports": true, "experimentalDecorators": true, "allowJs": true, "declaration": true, "baseUrl": "src", "outDir": "dist", "rootDir": ".", "types": ["webpack-env", "jest"], "paths": {"@/*": ["./*"], "~": ["/"]}, "lib": ["esnext", "dom", "dom.iterable", "scripthost"]}, "exclude": ["node_modules", "unpackage", "dist"]}