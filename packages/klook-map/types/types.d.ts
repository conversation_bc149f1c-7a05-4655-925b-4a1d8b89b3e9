/**
 * Created by <PERSON><PERSON>Jun on 2021/3/24 18:21.
 */

export namespace MapTypes {
  // 格式化后经纬度
  export type FormatLngLat = {
    lng: number,
    lat: number
  }

  // 经纬度
  export type LngLat = string | [number, number] | {
    lng: number,
    lat: number
  }

  // 坐标
  export interface Point {
    x?: number
    y?: number
  }

  export interface MapTool {
    initMap: () => any
    mark: (lngLat: MapTypes.LngLat, markerOptions: MarkerTypes.Options) => MarkerTypes.Tool
    removeMarker: (marker: MarkerTypes.Tool) => void
    clearMarker: () => void
    flyTo: (latLng: MapTypes.LngLat) => void
    panTo: (latLng: MapTypes.LngLat) => void
    setCenter: (center: MapTypes.LngLat) => void
    setZoom: (zoom: number) => void
    setMinZoom: (zoom: number) => void
    setMaxZoom: (zoom: number) => void
    fitBounds: (bound: MapTypes.LngLatBounds.LngLatBoundLike | MapTypes.LngLatBounds.LatLngBounds, options?: MapTypes.LngLatBounds.Options) => void
    getBounds: () => MapTypes.LngLatBounds.LatLngBounds
    isContains: (latLng: MapTypes.LngLat, padding?: MapTypes.LngLatBounds.Padding) => boolean
    resize: () => MapTypes.MapTool
    getBoundsSwNe: () => {
      sw: MapTypes.LngLat,
      ne: MapTypes.LngLat,
      bounds: MapTypes.LngLatBounds.LatLngBounds
    }
    bindBoundsChangeEvent: (boundChangeListener: Function) => Function
    isMovingOrZooming: () => boolean
    getContainer: () => HTMLElement
    getMapRangeCoordinates: (padding?: MapTypes.LngLatBounds.Padding, options?: MapTypes.LngLatBounds.MapRangeCoordinatesOption) => {
      sw: MapTypes.LngLat,
      ne: MapTypes.LngLat,
    }
    [key: string]: any
  }

  export enum Type {
    google = 'google',
    mapbox = 'mapbox',
    amap = 'amap'
  }

  export interface Conf {
    token?: string
  }

  export interface UserMapConf {
    type: MapTypes.Type,
    googleConf?: MapTypes.Conf,
    mapboxConf?: MapTypes.Conf,
    [propName: string]: any
  }

  export interface MapOpts {
    center?: LngLat,
    zoom?: number,
    minZoom?: number,
    maxZoom?: number,
    interactive?: boolean | string,
  }
  export namespace LngLatBounds {
    export type Padding = number | { top?: number, bottom?: number, left?: number, right?: number }
    export type PaddingObj = { top?: number, bottom?: number, left?: number, right?: number }
    export type LngLatBoundLike = FormatLngLat[]
    export type Options = {
      padding?: Padding
    }
    export type CreateOptions = {
      formatLngLat?: boolean
    }

    export type MapRangeCoordinatesOption = {
      reverse2wgs?: boolean,
      mapType?: MapTypes.Type
    }

    export interface LatLngBounds {
      contains: (FormatLngLat: MapTypes.LngLat) => true
      extend?: (FormatLngLat: MapTypes.LngLat) => LatLngBounds
      toString: () => string
      equals: (bound: LatLngBounds) => boolean
    }
  }
}

export namespace MarkerTypes {
  export type Anchor = 'center'
    | 'top'
    | 'bottom'
    // | 'left'
    // | 'right'
    // | 'top-left'
    // | 'top-right'
    // | 'bottom-left'
    // | 'bottom-right'

  export interface Tool {
    init: () => void
    onMarkerReady: () => Promise<any>
    getLngLat: () => MapTypes.LngLat
    setLngLat: (lngLat: MapTypes.LngLat) => void
    // getOffset: () => any
    // setOffset: (offset: MapTypes.Point) => any
    isDraggable: () => boolean
    remove: () => void
    update: (newOptions: MarkerTypes.Options) => void
    [key: string]: any
  }

  export interface Options {
    type?: 'point1' | 'point2',
    element?: Element
    offset?: MapTypes.Point
    anchor?: Anchor,
    color?: string,
    draggable?: boolean,
    hoveAnimate?: boolean, // 只针对type point2 hover时变成 point1

    poptip?: string, // poptip 文案
    poptipWidth?: number, // 宽度
    poptipMaxWidth?: number, // 高度
    popTrigger?: 'hover' | 'click' | 'focus' | 'none' // any 表固定显示
  }
}

export namespace UserLocationTypes {
  export interface Tool {
    init: () => void
    setLngLat: (lngLat: MapTypes.LngLat) => void
    remove: () => void
  }

  export interface Options {
    positionOptions?: {
      enableHighAccuracy?: Boolean
    },
    showUserLocation?: Boolean,
    trackUserLocation?: Boolean,
    showUserHeading?: Boolean,
    element: Element
  }

  export interface flyOption {
    zoom: number
  }
}
