import Vue from 'vue'
import { MapTypes } from '../types'

/** Map Component */
export declare class Map extends Vue {
  // 类型 google mapbox
  type?: MapTypes.Type

  // google token 配置
  googleConf?: MapTypes.Conf

  // mapbox token 配置
  mapboxConf?: MapTypes.Conf

  // 容器宽度 css width 默认 100%
  width?: string

  // 容器高度 css height 默认 400px
  height?: string

  // 地图中心位置
  center: MapTypes.LngLat

  // 地图缩放比例 默认12
  zoom: number

  minZoom?: number

  maxZoom?: number

  // 是否可交互，建议不可交互直接使用 static map
  interactive?: boolean | string
}
