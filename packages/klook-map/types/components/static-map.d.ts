import Vue from 'vue'
import { MapTypes } from '../types'

/** StaticMap Component */
export declare class StaticMap extends Vue {
  // 类型 google mapbox
  type?: MapTypes.Type

  // google token 配置
  googleConf?: MapTypes.Conf

  // mapbox token 配置
  mapboxConf?: MapTypes.Conf

  // 图片宽度 px
  width?: number

  // 图片宽度 px
  height?: number

  // 地图中心位置
  center: MapTypes.LngLat

  // 地图缩放比例 默认12
  zoom: number

  // 倍图 默认为 2
  scale?: number

  // 是否懒加载 配置true 需要依赖 v-lazy 指令
  lazy?: boolean

  // 是否显示默认marker
  defaultMarker?: boolean
}
