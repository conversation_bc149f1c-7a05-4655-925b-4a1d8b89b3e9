/**
 * Created by <PERSON><PERSON>Jun on 2021/3/30 18:13.
 */
import { MapTypes } from './types'

export declare function formatLngLat(lngLat: MapTypes.LngLat): MapTypes.FormatLngLat;
export declare function formatLatLng(latLng: MapTypes.LngLat): MapTypes.FormatLngLat;
export declare function zoomTransform(type: MapTypes.Type, googleZoom: number): number;
export declare function wgs2gcj(latLng: MapTypes.LngLat): MapTypes.LngLat;
export declare function isInChinaMandland(latLng: MapTypes.FormatLngLat): boolean;
export declare function turfBbox(items: MapTypes.LngLat[], type?: MapTypes.Type): Promise<{ sw: any; ne: any; }>
export declare function getMapRangeCoordinates(sw: {
    lng: number,
    lat: number
  }, ne: {
    lng: number,
    lat: number
  }, width: number,
    height: number,
    padding: any,
    options?: MapTypes.LngLatBounds.MapRangeCoordinatesOption): {
    sw: any
    ne: any
  }
