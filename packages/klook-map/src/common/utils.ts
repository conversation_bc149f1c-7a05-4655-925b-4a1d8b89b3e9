/**
 * utils ..
 */

export function genSingle(fn: (...args: any) => any) {
  let isInit = false
  let result: any
  return function proxySingle(this: any, ...args: any) {
    if (!isInit) {
      isInit = true
      result = fn.apply(this, args)
    }
    return result
  }
}

export function removeArrayAt(target: any[], index: number) {
  return !!target.splice(index, 1).length
}

export function removeArrayItem(target: any[], item: any) {
  // 移除数组中第一个匹配传参的那个元素，返回布尔表示成功与否
  const index = target.indexOf(item)
  if (~index) {
    return removeArrayAt(target, index)
  }
  return false
}

// 只处理对象
export function deepMergeObj(target: any = {}, source: any = {}) {
  const result = { ...target, ...source }
  const keys = Object.keys(result)

  for (const key of keys) {
    const left = target[key]
    const right = source[key]
    if (typeof (left) === 'object' && typeof (right) === 'object') {
      result[key] = deepMergeObj(left, right)
    }
  }
  return result
}

export function loadScript(url: string) {
  return new Promise<void>((resolve) => {
    const head = document.getElementsByTagName('head')[0] || document.documentElement
    const script: any = document.createElement('script')

    script.src = url

    let loadFlag = false

    script.onload = script.onreadystatechange = function () {
      if (
        !loadFlag &&
        (!this.readyState ||
          this.readyState === 'loaded' ||
          this.readyState === 'complete')
      ) {
        loadFlag = true

        resolve()

        script.onload = script.onreadystatechange = null
        if (head && script.parentNode) {
          head.removeChild(script)
        }
      }
    }

    head.insertBefore(script, head.firstChild)
  })
}

export function loadCss(url: string) {
  const link = document.createElement('link')
  link.type = 'text/css'
  link.rel = 'stylesheet'
  link.href = url
  document.getElementsByTagName('head')[0].appendChild(link)
}

export function qs(query: Record<string, string | number>): string {
  return Object.entries(query).reduce((preVal: string[], [key, value]) => {
    return [
      ...preVal,
      ...value === undefined ? [] : [
        `${key}=${encodeURIComponent(value)}`
      ]
    ]
  }, []).join('&')
}

export function customDebounce(this: any, callback: any, delay: number, immediate: boolean = false) {
  let timer: any
  return () => {
    if (immediate && !timer) { callback.call(this) }
    if (timer) { clearTimeout(timer) }
    timer = setTimeout(() => {
      callback.call(this)
      timer = null
    }, delay)
  }
}

export const hasClass = (el: HTMLElement, name: string) => {
  const re = new RegExp('\\b' + name + '\\b')
  return re.test(el.getAttribute('class') || '')
}

export const addClass = (el: HTMLElement, name: string) => {
  if (!el) {
    return
  }
  if (!hasClass(el, name)) {
    el.setAttribute('class', `${el.getAttribute('class')} ${name}`)
  }
}

export const removeClass = (el: HTMLElement, name: string) => {
  if (!el) {
    return
  }
  const re = new RegExp('\\b' + name + '\\b')
  const str = el.getAttribute('class') || ''
  el.setAttribute('class', str.replace(re, ''))
}
