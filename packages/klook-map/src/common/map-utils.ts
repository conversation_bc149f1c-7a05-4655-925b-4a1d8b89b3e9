/**
 * Created by <PERSON><PERSON> on 2021/3/30 18:13.
 */

import { add, multiply, division } from '@klook/accurate'
import { MapTypes } from '../types'
import {borderHk, borderManland, borderMo, borderMoTwo, borderTw} from './border'
import { wgs2bd09, wgs2gcj, gcj2wgs } from './transform'

export { wgs2gcj, gcj2wgs }

// 坐标系类型及转换定义
export const mapEncodingDict = {
  wgs: 'wgs',
  gcj: 'gcj',
  gcj2wgs: 'gcj2wgs',
  wgs2gcj: 'wgs2gcj'
}

export const lngLat = (lngLatLiteral: { lng: any; lat: any; }) => {
  if (!(lngLatLiteral instanceof Object && !Array.isArray(lngLatLiteral))) {
    throw new TypeError('lngLatLiteral validate error')
  }
  const { lng, lat } = lngLatLiteral
  if (+lng > 180 || +lng < -180 || +lat > 90 || +lat < -90) {
    throw new Error('lngLatLiteral validate error')
  }
  return lngLatLiteral
}

// 两种数据格式的坐标系转换函数
export const transformLocation = (location: any, type = '') => {
  if (type === mapEncodingDict.gcj2wgs) {
    return gcj2wgs(lngLat(location))
  }

  if (type === mapEncodingDict.wgs2gcj) {
    return wgs2gcj(lngLat(location))
  }

  return location
}

function isInArea(point: MapTypes.FormatLngLat, vs: number[][]) {
  // ray-casting algorithm based on
  // https://wrf.ecse.rpi.edu//Research/Short_Notes/pnpoly.html

  const x = point.lng
  const y = point.lat
  let inside = false

  for (let i = 0, j = vs.length - 1; i < vs.length; j = i++) {
    const xi = vs[i][0]
    const yi = vs[i][1]
    const xj = vs[j][0]
    const yj = vs[j][1]
    const intersect =
      yi > y !== yj > y && x < ((xj - xi) * (y - yi)) / (yj - yi) + xi

    if (intersect) { inside = !inside }
  }

  return inside
}

export const isInChinaMandland = (pt: MapTypes.FormatLngLat) =>
  isInArea(pt, borderManland)
const isInTaiwan = (pt: MapTypes.FormatLngLat) => isInArea(pt, borderTw)
const isInHkOrMacao = (pt: MapTypes.FormatLngLat) =>
  isInArea(pt, borderHk) || isInArea(pt, borderMo) || isInArea(pt, borderMoTwo)

/**
 *
 */
const TRANSFORM_MAP = {
  [MapTypes.Type.google]: [{ p: isInChinaMandland, t: wgs2gcj }],
  [MapTypes.Type.mapbox]: [],
  [MapTypes.Type.amap]: [
    { p: isInHkOrMacao, t: wgs2gcj },
    { p: isInChinaMandland, t: wgs2gcj }
  ],
  [MapTypes.Type.tencent]: [
    { p: isInHkOrMacao, t: wgs2gcj },
    { p: isInChinaMandland, t: wgs2gcj }
  ],
  [MapTypes.Type.baidu]: [
    {
      p: isInChinaMandland,
      t: wgs2bd09
    },
    {
      p: isInTaiwan,
      t: wgs2bd09
    },
    {
      p: isInHkOrMacao,
      t: wgs2bd09
    }
  ]
}

/**
 * 逆向将转化的经纬度，还原为wgs后端可以标准识别的经纬度
 */
const REVERSE_TRANSFORM_MAP = {
  [MapTypes.Type.google]: [{ p: isInChinaMandland, t: gcj2wgs }],
  [MapTypes.Type.mapbox]: [],
  [MapTypes.Type.amap]: [
    { p: isInHkOrMacao, t: gcj2wgs },
    { p: isInChinaMandland, t: gcj2wgs }
  ],
  [MapTypes.Type.tencent]: [
    { p: isInHkOrMacao, t: gcj2wgs },
    { p: isInChinaMandland, t: gcj2wgs }
  ],
  // 加入后再补充bd092wgs
  [MapTypes.Type.baidu]: [
  ]
}

/**
 * 根据地图类型和坐标位置转换地图坐标, 不传类型则只做格式化
 * 地图底图用的坐标	中国大陆	港澳	台湾	其他地区
 * Google Map	GCJ-02	WGS84	WGS84	WGS84
 * MapBox	WGS84	WGS84	WGS84	WGS84
 * Amap	GCJ-02	GCJ-02	WGS84	不支持
 * Tecent Map 	GCJ-02	GCJ-02	WGS84	WGS84
 * Baidu Map	BD09	BD09	BD09	WGS84
 * @param lngLat 坐标
 * @param type 地图类型
 * @returns 转换后的坐标
 */
export function formatLngLat(
  lngLat: MapTypes.LngLat,
  type?: MapTypes.Type
): MapTypes.FormatLngLat {
  let res: MapTypes.FormatLngLat = { lng: 0, lat: 0 }
  if (
    !isNaN((lngLat as MapTypes.FormatLngLat).lng) && (lngLat as MapTypes.FormatLngLat).lng !== null &&
    !isNaN((lngLat as MapTypes.FormatLngLat).lat) && (lngLat as MapTypes.FormatLngLat).lat !== null
  ) {
    res = lngLat as MapTypes.FormatLngLat
  } else {
    const lagLatArr = Array.isArray(lngLat)
      ? lngLat
      : String(lngLat).split(',')

    const [lng, lat] = lagLatArr

    res = {
      lng: +lng,
      lat: +lat
    }
  }
  if (!type) {
    return res
  }

  const group = TRANSFORM_MAP[type]
  if (group && group.length > 0) {
    for (let i = 0; i < group.length; i++) {
      const element = group[i]
      if (element.p(res)) {
        return element.t(res)
      }
    }
  }
  return res
}

// 高德只支持这个格式 ...
export function formatLngLatArray(lngLat: MapTypes.LngLat, type?: MapTypes.Type): [number, number] {
  const res = formatLngLat(lngLat, type)
  return [res.lng, res.lat]
}

// lng lat 格式转换
export function formatLatLng(latLng: MapTypes.LngLat): MapTypes.FormatLngLat {
  if (
    !isNaN((latLng as MapTypes.FormatLngLat).lng) && (latLng as MapTypes.FormatLngLat).lng !== null &&
    !isNaN((latLng as MapTypes.FormatLngLat).lat) && (latLng as MapTypes.FormatLngLat).lat !== null
  ) {
    return latLng as MapTypes.FormatLngLat
  }

  const lagLatArr = Array.isArray(latLng) ? latLng : String(latLng).split(',')

  const [lat, lng] = lagLatArr

  return {
    lng: +lng,
    lat: +lat
  }
}

export function formatGeoPath(path: any, type: MapTypes.Type){
  if(!isNaN(path[0])&&!isNaN(path[1])){
    const newPath = formatLngLatArray(path,type)
    path[0] = newPath[0]
    path[1] = newPath[1]
  }else {
    path.forEach((item: any)=>
      formatGeoPath(item, type)
    )
  }
}

export function getCenterLatLng(locations: any, isFormat: boolean = false) {
  let minLat = 90
  let maxLat = -90
  let minLng = 180
  let maxLng = -180

  // 遍历所有经纬度，找到最小和最大的经度和纬度值
  locations.forEach((location: any) => {
    const lat = !isFormat ? location.lat : location[1]
    const lng = !isFormat ? location.lng : location[0]
    if (lat < minLat) {
      minLat = lat
    }
    if (lat > maxLat) {
      maxLat = lat
    }
    if (lng < minLng) {
      minLng = lng
    }
    if (lng > maxLng) {
      maxLng = lng
    }
  })

  // 计算中心点的经度和纬度
  const centerLat = (minLat + maxLat) / 2
  const centerLng = (minLng + maxLng) / 2

  return !isFormat ? { lat: centerLat, lng: centerLng } : [centerLng, centerLat]
}
// 尽量让同一个zoom在google 和 mapbox都看起来差不多大
// 转换比例 凭感觉写的
// 不够精准 那调用者自形在外部使用不同类型map时传入不同的zoom来控制
export function zoomTransform(type: MapTypes.Type, googleZoom: number) {
  if (type === 'mapbox') {
    return Math.round(googleZoom / 1.14)
  }

  return googleZoom
}

/**
 根据经纬度绘制多边形，得到角坐标（已经格式化）
 */
export async function turfBbox(items: MapTypes.LngLat[], type?: MapTypes.Type): Promise<{ sw: any; ne: any; }> {
  const bbox = (await import('@turf/bbox'))
  const box = bbox.default({
    type: 'Feature',
    geometry: {
      type: 'Polygon',
      coordinates: [
        items.map((item: MapTypes.LngLat) => {
          const { lng, lat } = formatLngLat(item, type)
          return [lng, lat] as [number, number]
        })
      ]
    }
  })

  return {
    sw: {
      lng: +box[0],
      lat: +box[1]
    },
    ne: {
      lng: +box[2],
      lat: +box[3]
    }
  }
}

/**
 * 没有也返回4个角，主要amap是传入数组4个角[上、下、左、右]
 */
export function formatMapPadding(padding: MapTypes.LngLatBounds.Padding): MapTypes.LngLatBounds.PaddingObj {
  if (!padding) {
    padding = 0
  }
  if (Object.prototype.toString.call(padding) === '[object Number]') {
    padding = {
      top: padding as number,
      bottom: padding as number,
      left: padding as number,
      right: padding as number
    }
  } else {
    const { top = 0, bottom = 0, left = 0, right = 0 } = padding as MapTypes.LngLatBounds.PaddingObj
    padding = {
      top,
      bottom,
      left,
      right
    }
  }
  return padding || { top: 0, bottom: 0, left: 0, right: 0 }
}
/**
  * // 去除或加上padding的距离， 获取四个角的经纬度； 默认是去除padding
  * @param sw 包含边界的经纬度
  * @param ne
  * @param width 地图dom宽高
  * @param height
  * @param padding 去除的边界大小
  * @param options 可选， 对于google等地图圈定方形点位正好都在某些时候需要进行再次转化为wgs84，是为了提交给后端接口wgs84接口， 如果只是地图渲染则不需要
  * @returns
  */
export function getMapRangeCoordinates(sw: {
  lng: number,
  lat: number
}, ne: {
  lng: number,
  lat: number
}, width: number, height: number, padding: MapTypes.LngLatBounds.Padding = 0, options?: MapTypes.LngLatBounds.MapRangeCoordinatesOption) {
  padding = formatMapPadding(padding)

  const format = (paddingProp: 'left' | 'top' | 'bottom' | 'right', rect: number, lngLatProp: 'lng' | 'lat', lngLat: any, type?: string) => {
    const distance = multiply(
      division((padding as any)[paddingProp] || 0, rect),
      add(ne[lngLatProp], -sw[lngLatProp])
    )

    let value = add(
      type === 'minus' ? -distance : distance,
      lngLat[lngLatProp]
    )
    const getValue = (value: number, max: number, min: number) => {
      if (value > max) {
        value = max
      } else if (value < min) {
        value = min
      }
      return value
    }
    if (lngLatProp === 'lng') {
      value = getValue(value, 180, -180)
    } else {
      value = getValue(value, 90, -90)
    }
    return value
  }

  sw = {
    lng: format('left', width, 'lng', sw),
    lat: format('bottom', height, 'lat', sw)
  }
  ne = {
    lng: format('right', width, 'lng', ne, 'minus'),
    lat: format('top', height, 'lat', ne, 'minus')
  }

  if (options?.reverse2wgs) {
    [sw, ne] = [sw, ne].map((item: MapTypes.FormatLngLat) => getReverseLngLat(item, options?.mapType))
  }

  return {
    sw,
    ne
  }
}

export function getReverseLngLat(lngLat: {
  lng: number;
  lat: number;
}, type?: MapTypes.Type): MapTypes.FormatLngLat {
  if (!type) {
    return lngLat
  }

  const group = REVERSE_TRANSFORM_MAP[type]
  if (group && group.length > 0) {
    for (let i = 0; i < group.length; i++) {
      const element = group[i]
      const reverseLngLat = element.t(lngLat)
      if (element.p(reverseLngLat)) {
        return reverseLngLat
      }
    }
  }
  return lngLat
}
