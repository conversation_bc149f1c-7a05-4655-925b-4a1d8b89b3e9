<!DOCTYPE html>
<html>
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>China border GPS data</title>
    <script src="https://unpkg.com/maplibre-gl@1.15.2/dist/maplibre-gl.js"></script>
    <link
      href="https://unpkg.com/maplibre-gl@1.15.2/dist/maplibre-gl.css"
      rel="stylesheet"
    />
    <script src="./border.js"></script>
    <style>
      #map {
        width: 100%;
        height: 600px;
      }
    </style>
  </head>

  <body>
    <button data-border="borderManland">mainland</button>
    <button data-border="borderHk">hk</button>
    <button data-border="borderMo">marco</button>
    <button data-border="borderTw">taiwan</button>
    <div id="map"></div>
    <script>
      const data = { borderManland, borderHk, borderMo, borderTw };
      Array.from(document.getElementsByTagName("button")).forEach(btn => {
        btn.addEventListener("click", () => {
          const d = data[btn.dataset.border];
          mapInit(d)
        });
      });

      var mapObj;
      var map = new maplibregl.Map({
          container: "map",
          style:
            "https://api.maptiler.com/maps/streets/style.json?key=get_your_own_OpIi9ZULNHzrESv6T2vL",
          center: [104, 28],
          zoom: 3
        });
      function mapInit(data) {
        data.forEach(element => {
          var marker = new maplibregl.Marker()
          .setLngLat(element)
          .addTo(map);
        });
   
        // mapObj = new AMap.Map("map", {
        //   //二维地图显示视口
        //   view: new AMap.View2D({
        //     zoom: 4 //地图显示的缩放级别
        //   })
        // });

        // AMap.event.addListener(mapObj, "complete", function() {
        //   for (var i = 0; i < borderData.length; i++) {
        //     addMarker(borderData[i][0], borderData[i][1]);
        //   }
        // });
      }
    </script>
  </body>
</html>
