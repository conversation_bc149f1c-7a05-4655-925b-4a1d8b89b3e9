const earthR = 6378137.0

function transform(x: number, y: number) {
  const xy = x * y
  const absX = Math.sqrt(Math.abs(x))
  const xPi = x * Math.PI
  const yPi = y * Math.PI
  const d = 20.0 * Math.sin(6.0 * xPi) + 20.0 * Math.sin(2.0 * xPi)

  let lat = d
  let lng = d

  lat += 20.0 * Math.sin(yPi) + 40.0 * Math.sin(yPi / 3.0)
  lng += 20.0 * Math.sin(xPi) + 40.0 * Math.sin(xPi / 3.0)

  lat += 160.0 * Math.sin(yPi / 12.0) + 320 * Math.sin(yPi / 30.0)
  lng += 150.0 * Math.sin(xPi / 12.0) + 300.0 * Math.sin(xPi / 30.0)

  lat *= 2.0 / 3.0
  lng *= 2.0 / 3.0

  lat += -100.0 + 2.0 * x + 3.0 * y + 0.2 * y * y + 0.1 * xy + 0.2 * absX
  lng += 300.0 + x + 2.0 * y + 0.1 * x * x + 0.1 * xy + 0.1 * absX

  return { lat, lng }
}
function delta(lat: number, lng: number) {
  // eslint-disable-next-line no-loss-of-precision
  const ee = 0.00669342162296594323
  const d = transform(lng - 105.0, lat - 35.0)
  const radLat = (lat / 180.0) * Math.PI
  let magic = Math.sin(radLat)
  magic = 1 - ee * magic * magic
  const sqrtMagic = Math.sqrt(magic)
  d.lat =
    (d.lat * 180.0) / (((earthR * (1 - ee)) / (magic * sqrtMagic)) * Math.PI)
  d.lng = (d.lng * 180.0) / ((earthR / sqrtMagic) * Math.cos(radLat) * Math.PI)
  return d
}

export function wgs2gcj({ lat: wgsLat, lng: wgsLng }: any) {
  const d = delta(wgsLat, wgsLng)
  return { lat: wgsLat + d.lat, lng: wgsLng + d.lng }
}

export function gcj2wgs({ lat: gcjLat, lng: gcjLng }: any) {
  const d = delta(gcjLat, gcjLng)
  return { lat: gcjLat - d.lat, lng: gcjLng - d.lng }
}

function gcj02tobd09({ lng, lat }: { lat: number; lng: number }) {
  // eslint-disable-next-line no-loss-of-precision
  const x_PI = (3.14159265358979324 * 3000.0) / 180.0
  const z = Math.sqrt(lng * lng + lat * lat) + 0.00002 * Math.sin(lat * x_PI)
  const theta = Math.atan2(lat, lng) + 0.000003 * Math.cos(lng * x_PI)
  const bd_lng = z * Math.cos(theta) + 0.0065
  const bd_lat = z * Math.sin(theta) + 0.006
  return { lng: bd_lng, lat: bd_lat }
}

export function wgs2bd09({ lat, lng }: any) {
  const d = wgs2gcj({ lat, lng })
  return gcj02tobd09(d)
}
