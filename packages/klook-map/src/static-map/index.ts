/**
 * Created by <PERSON><PERSON> on 2021/4/26 16:55.
 */

import { formatLngLat } from '../common/map-utils'
import { MapTypes } from '../types'
import { deepMergeObj, qs } from '../common/utils'
import defaultConf from '../map/defautl-conf'

type Options = {
  center: MapTypes.LngLat
  scale?: number,
  width?: number,
  height?: number,
  zoom?: number,
  lazy?: boolean,
  language?: string,
  keplerId?: string
}

type Strategy = Record<MapTypes.Type.google|MapTypes.Type.mapbox, (option: Options) => {
  center: string
  scale?: number,
  width?: number,
  height?: number,
  zoom?: number,
  lazy?: boolean,
  keplerId?: string
}>

export type Type = 'google' | 'mapbox' | 'amap' | 'baidu' | 'tencent';

export interface ISignatureParam {
  center: string;
  scale?: number;
  zoom?: number;
  size?: string;
  type?: Type;
  width?: number;
  height?: number;
  language?: string;
  symbolInside?: string;
  symbolOutside?: string;
}

function hash(string: string) {
  let hash = 0;
  if (string.length == 0) {
    return hash;
  }
  for (let i = 0; i < string.length; i++) {
    let char = string.charCodeAt(i);
    hash = ((hash<<5)-hash)+char;
    hash = hash & hash;
  }
  return hash;
}

function generateSignature(url: string, keplerId: string = '') {
  if (!keplerId) {
    return ''
  }

  const originUrl = `${url}:${keplerId}`;

  return hash(originUrl);
}

function getQueryString(options: ISignatureParam) {
  const { symbolInside: i = '=', symbolOutside: o = '&' } = options;
  return [
    'zoom',
    'scale',
    'center',
    'type',
    'width',
    'height',
    'language',
    'size',
  ].reduce((acc, v) => {
    const value = (options as any)[v];

    if (!value) {
      return acc;
    }

    return acc ? `${acc}${o}${v}${i}${value}` : `${v}${i}${value}`;
  }, '');
}

const paramsStrategy: Strategy = {
  [MapTypes.Type.mapbox]: (options: Options) => {
    const { lat, lng } = formatLngLat(options.center, MapTypes.Type.mapbox)

    return {
      ...options,
      center: `${lat},${lng}`
    }
  },
  [MapTypes.Type.google]: (options: Options) => {
    const { lat, lng } = formatLngLat(options.center, MapTypes.Type.google)

    const center = `${lat},${lng}`

    return {
      ...options,
      center,
      scale: options.scale || 1,
      zoom: options.zoom || 12,
    }
  },
}

function handleMap(options: Options, type: MapTypes.Type.google | MapTypes.Type.mapbox) {
  const baseUrl = '/v1/webbffapi/public/map/staticMap'

  const queryOptions = paramsStrategy[type](options)

  const query = getQueryString({ ...queryOptions, type })
  const signature = generateSignature(query, options.keplerId)

  return `${baseUrl}?${query}&signature=${signature}`
}

export default (mapConf: MapTypes.UserMapConf, options: Options): string => {
  const trueOptions = {
    width: 100,
    height: 100,
    ...options
  }

  const userMapConf = deepMergeObj(defaultConf, mapConf) as MapTypes.UserMapConf
  return handleMap(trueOptions, userMapConf.type)
}
