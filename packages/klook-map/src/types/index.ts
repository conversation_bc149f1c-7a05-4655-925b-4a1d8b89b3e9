/**
 * Created by <PERSON><PERSON>Jun on 2021/3/24 18:21.
 */

export namespace MapTypes {
  // 格式化后经纬度
  export type FormatLngLat = {
    lng: number,
    lat: number
  }

  // 经纬度
  export type LngLat = string | [number, number] | {
    lng: number,
    lat: number
  }

  // 坐标
  export interface Point {
    x?: number
    y?: number
  }

  export interface MapTool {
    initMap: () => any
    mark: (lngLat: MapTypes.LngLat, markerOptions: MarkerTypes.Options) => MarkerTypes.Tool
    removeMarker: (marker: MarkerTypes.Tool) => void
    clearMarker: () => void
    flyTo: (latLng: MapTypes.LngLat) => void
    panTo: (latLng: MapTypes.LngLat) => void
    setCenter: (center: MapTypes.LngLat) => void
    setZoom: (zoom: number) => void
    setMinZoom: (zoom: number) => void
    setMaxZoom: (zoom: number) => void
    setGeoLocate: (element: Element, userLocationOptions: UserLocationTypes.Options, fn: UserLocationTypes.Callback, flyOption: UserLocationTypes.flyOption) => UserLocationTypes.Tool
    fitBounds: (bound: MapTypes.LngLatBounds.LngLatBoundLike, options?: MapTypes.LngLatBounds.Options) => void
    getBounds: () => MapTypes.LngLatBounds.LatLngBounds
    isContains: (latLng: MapTypes.LngLat, padding?: MapTypes.LngLatBounds.Padding) => boolean
    resize: () => MapTypes.MapTool
    getBoundsSwNe: () => {
      sw: MapTypes.LngLat,
      ne: MapTypes.LngLat,
      bounds: MapTypes.LngLatBounds.LatLngBounds
    }
    bindBoundsChangeEvent: (boundChangeListener: Function) => Function
    isMovingOrZooming: () => boolean
    getContainer: () => HTMLElement
    getMapRangeCoordinates: (padding?: MapTypes.LngLatBounds.Padding) => {
      sw: MapTypes.LngLat,
      ne: MapTypes.LngLat,
    }
    initGeoFence(fenceDeleteBtn: Element, geoFenceConf: MapTypes.GeoFenceConf, isWgs84: Boolean): void
    initSearchBar(searchBar: HTMLInputElement, searchBarContainer: HTMLElement): void
  }

  export enum Type {
    google = 'google',
    mapbox = 'mapbox',
    amap = 'amap',
    baidu = 'baidu',
    tencent = 'tencent'
  }

  export enum AreaType {
    mainland,
    taiwan,
    macaoAndHk,
    other,
  }

  export interface Conf {
    token?: string
    libraries?: string
    secret?: string
  }

  export interface GeoFenceConf {
    canEdit?: boolean // 地理围栏是否可编辑
    canDelete?: boolean // 地理围栏是否可删除
    showMarker?: boolean // 是否展示Marker
    polygonList: any[] // 初始化图形
    clickZoom: number // 点击后定位缩放
    type: string
    markerClickCb?: Function // mark点击后回调
    markerConfig?: {
      src?: string
      width?: number
      height?: number
    }
    polygonConfig: {
      fillColor: string
      strokeColor: string
      fillOpacity: number
      lineWidth: number
    } // polygon Config
  }

  export interface UserMapConf {
    type: MapTypes.Type.google | MapTypes.Type.mapbox,
    googleConf?: MapTypes.Conf,
    mapboxConf?: MapTypes.Conf,
    amapConf?: MapTypes.Conf,
    [propName: string]: any
  }

  export interface MapOpts {
    [x: string]: any
    center?: LngLat,
    zoom?: number,
    minZoom?: number,
    maxZoom?: number,
    interactive?: boolean | string,
    dragRotate?: boolean
    fullscreenControl?: boolean
  }


  export namespace LngLatBounds {
    export type Padding = number | { top?: number, bottom?: number, left?: number, right?: number }
    export type PaddingObj = { top?: number, bottom?: number, left?: number, right?: number }
    export type LngLatBoundLike = FormatLngLat[]
    export type Options = {
      padding?: Padding
    }
    export type CreateOptions = {
      formatLngLat?: boolean // 是否需要格式化： 已经格式化的，无需再次格式化
    }

    export type MapRangeCoordinatesOption = {
      reverse2wgs?: boolean,
      mapType?: MapTypes.Type
    }

    export interface LatLngBounds {
      contains: (FormatLngLat: MapTypes.LngLat) => true
      extend?: (FormatLngLat: MapTypes.LngLat) => LatLngBounds
      toString: () => string
      equals: (bound: LatLngBounds) => boolean
    }
  }
}

export namespace MarkerTypes {
  export type Anchor = 'center'
    | 'top'
    | 'bottom'
    // | 'left'
    // | 'right'
    // | 'top-left'
    // | 'top-right'
    // | 'bottom-left'
    // | 'bottom-right'

  export interface Tool {
    init: () => void
    onMarkerReady: () => Promise<any>
    getLngLat: () => MapTypes.LngLat
    setLngLat: (lngLat: MapTypes.LngLat) => void
    isDraggable: () => boolean
    remove: () => void
    update: (newOptions: MarkerTypes.Options) => void
    setzIndex: (zindex: number) => void
  }

  export interface Options {
    type?: 'point1' | 'point2',
    element?: Element
    offset?: MapTypes.Point
    anchor?: Anchor,
    color?: string,
    draggable?: boolean,
    hoveAnimate?: boolean, // 只针对type point2 hover时变成 point1
    zIndex?: number,

    poptip?: string, // poptip 文案
    poptipWidth?: number, // 宽度
    poptipMaxWidth?: number, // 高度
    popTrigger?: 'hover' | 'click' | 'focus' | 'none', // any 表固定显示
    src?: string, // 自定义图标
    style?: any // 自定义样式
    popAutoShow?: boolean // 是否能够自动展示poptip，特殊情况为popTrigger = none时候都是需要手动打开的
    flip?: boolean
    flipOptions?: object
  }
}

export namespace UserLocationTypes {
  export interface Tool {
    init: () => void
    setLngLat: (lngLat: MapTypes.LngLat) => void
    remove: () => void
  }

  export interface Options {
    positionOptions?: {
      enableHighAccuracy?: Boolean
    },
    showUserLocation?: Boolean, // By default a dot will be shown on the map at the user's location. Set to false to disable.
    trackUserLocation?: Boolean, // GeolocateControl becomes a toggle button. only mapbox
    showUserHeading?: Boolean, // only mapbox
  }

  export interface flyOption {
    zoom: number
  }

  export interface Callback {
    error?: Function
    success?: Function
    setLoading?: Function
  }
}
