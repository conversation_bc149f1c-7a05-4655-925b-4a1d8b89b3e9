<template>
  <div
    class="static-map-wrapper"
    :style="{
      width: `${width}px`,
      height: `${height}px`,
    }"
  >
    <img
      v-if="lazy"
      v-lazy="staticMapLink"
      class="static-map-img"
      alt=""
      :style="{
        width: `${width}px`,
        height: `${height}px`,
      }"
    >
    <img
      v-else
      :src="staticMapLink"
      class="static-map-img"
      alt=""
      :style="{
        width: `${width}px`,
        height: `${height}px`,
      }"
    >
    <slot>
      <img v-if="defaultMarker" :src="point1" class="static-map-marker" alt="">
    </slot>
  </div>
</template>

<script lang="ts">
import { Vue, Prop, Component } from 'vue-property-decorator'
import genStaticMapLink from '../static-map'
import { zoomTransform } from '../common/map-utils'
import { MapTypes } from '../types'
import { point1 } from '../points'

@Component({})
export default class StaticMap extends Vue {
  @Prop() language!: string | undefined
  @Prop({ type: String, default: MapTypes.Type.mapbox }) type!: MapTypes.Type.google|MapTypes.Type.mapbox
  @Prop({ type: Object, default: () => ({}) }) googleConf!: MapTypes.Conf
  @Prop({ type: Object, default: () => ({}) }) mapboxConf!: MapTypes.Conf

  @Prop({ type: [String, Array, Object], required: true }) center!: MapTypes.LngLat
  @Prop({ type: Number, default: 350 }) width!: number
  @Prop({ type: Number, default: 350 }) height!: number
  @Prop({ type: Number, default: 10 }) zoom!: number
  @Prop({ type: Number, default: 2 }) scale!: number
  @Prop({ type: Boolean, default: false }) lazy!: boolean
  @Prop({ type: Boolean, default: true }) defaultMarker!: boolean
  @Prop({ type: String }) keplerId!: string

  point1 = point1

  get staticMapLink() {
    const { googleConf, mapboxConf, language } = this
    return genStaticMapLink(
      {
        type: this.type,
        googleConf,
        mapboxConf
      }, {
        language,
        center: this.center,
        width: this.width,
        height: this.height,
        zoom: zoomTransform(this.type, this.zoom),
        scale: this.scale,
        lazy: this.lazy,
        keplerId: this.keplerId
      })
  }
}
</script>

<style scoped>
.static-map-wrapper {
  position: relative;
}
.static-map-marker {
  width: 40px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  pointer-events: none;
  background: transparent;
}
.static-map-img {
  vertical-align: top;
}
</style>
