<template>
  <div class="klkMap-wrap" :style="{width, height}">
    <div ref="mapContainer" class="map_container"></div>
    <slot></slot>
  </div>
</template>

<script lang="ts">
import { Vue, Prop, Component, Watch } from 'vue-property-decorator'
import { MapTypes } from '../types'
import initMap from '../map'
import { zoomTransform } from '../common/map-utils'
import { customDebounce } from '../common/utils'

@Component({
  provide() {
    return {
      map: this
    }
  }
})
export default class Map extends Vue {
  @Prop() language!: string | undefined
  @Prop({ type: String, default: MapTypes.Type.mapbox }) type!: MapTypes.Type
  @Prop({ type: Object, default: () => ({}) }) googleConf!: MapTypes.Conf
  @Prop({ type: Object, default: () => ({}) }) mapboxConf!: MapTypes.Conf
  @Prop({ type: Object, default: () => ({}) }) amapConf!: MapTypes.Conf

  @Prop({ type: String, default: '100%' }) width!: string
  @Prop({ type: String, default: '400px' }) height!: string

  @Prop({ type: [String, Array, Object], required: true }) center!: MapTypes.LngLat
  @Prop({ type: Number, default: 12 }) zoom!: number
  @Prop({ type: Number, default: 1 }) minZoom!: number
  @Prop({ type: Number, default: 20 }) maxZoom!: number
  @Prop({ type: [Boolean, String], default: true }) interactive!: boolean | string
  @Prop({ type: Boolean, default: true }) dragRotate!: boolean // 可以拖拽旋转， 使用bound等sdk时候需要设置为false
  @Prop({ type: Boolean, default: false }) fullscreenControl!: boolean // 是否全屏

  @Prop({ type: [Object, Boolean], default: false }) emitBoundsChange!: {
    debounce?: {
      delay: number,
      immediate: boolean
    }
  } | boolean

  $map: MapTypes.MapTool | undefined
  $resolveMap: any = undefined
  $getMapTool: Promise<MapTypes.MapTool> | undefined = undefined

  @Watch('center')
  setCenter(center: MapTypes.LngLat) {
    this.callMap(() => this.$map!.setCenter(center))
  }

  @Watch('zoom')
  setZoom(zoom: number) {
    zoom = zoomTransform(this.type, zoom)
    this.callMap(() => this.$map!.setZoom(zoom))
  }

  @Watch('minZoom')
  setMinZoom(zoom: number) {
    zoom = zoomTransform(this.type, zoom)
    this.callMap(() => this.$map!.setMinZoom(zoom))
  }

  @Watch('maxZoom')
  setMaxZoom(zoom: number) {
    zoom = zoomTransform(this.type, zoom)
    this.callMap(() => this.$map!.setMaxZoom(zoom))
  }

  flyTo(lngLat: MapTypes.LngLat) {
    this.callMap(() => this.$map!.flyTo(lngLat))
  }

  panTo(lngLat: MapTypes.LngLat) {
    this.callMap(() => this.$map!.panTo(lngLat))
  }

  // 初始化地图后执行
  callMap(fn: () => any) {
    if (this.$map) {
      return fn.call<this>(this)
    }
  }

  created() {
    // 子组件先mounted 所以在created时提供方法
    if (!this.$isServer) {
      this.$getMapTool = new Promise<MapTypes.MapTool>((resolve) => {
        this.$resolveMap = resolve
      })
    }
  }

  handleEmitClick(){
    const func = ()=>{
      this.$emit('click')
    }
    if(this.type === 'amap'){
      (this.$map as any).map.on('click', func)
      this.$once('hook:beforeDestroy', () => {
        (this.$map as any).map.off('click', func)
      })
    }
    if(this.type === 'google'){
      const listener = (this.$map as any).map.addListener('click', func)
      this.$once('hook:beforeDestroy', () => {
        (this.$map as any).mapSdk.event.removeListener(listener)
      })
    }
  }

  mounted() {
    const {
      type,
      googleConf,
      mapboxConf,
      amapConf,

      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      width,
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      height,
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      emitBoundsChange,

      ...mapOpts
    } = this.$props

    if (mapOpts.zoom) {
      mapOpts.zoom = zoomTransform(type, mapOpts.zoom)
    }
    if (mapOpts.minZoom) {
      mapOpts.minZoom = zoomTransform(type, mapOpts.minZoom)
    }
    if (mapOpts.maxZoom) {
      mapOpts.maxZoom = zoomTransform(type, mapOpts.maxZoom)
    }

    initMap({
      container: this.$refs.mapContainer as Element,
      mapConf: {
        type,
        googleConf,
        mapboxConf
      },
      mapOpts
    }).then((map: MapTypes.MapTool) => {
      // resolve map 实例
      this.$map = map
      this.$resolveMap(map)
      this.handleEmitClick()
      this.bindBoundsChangeEvent()
    })
  }

  bindBoundsChangeEvent() {
    if (!this.emitBoundsChange) {
      return
    }
    const $map  = this.$map as MapTypes.MapTool
    const emitEvent = () => {
      if ($map.isMovingOrZooming()) {
        return
      }
      this.$emit('bounds-change', {
        map: this.$map
      })
    }

    let boundChangeListener: Function

    if ((this.emitBoundsChange as any)?.debounce) {
      const { delay, immediate } = (this.emitBoundsChange as any).debounce
      boundChangeListener = customDebounce.call(this, emitEvent, delay, immediate)
    } else {
      boundChangeListener = emitEvent
    }

    const removeListener = $map.bindBoundsChangeEvent(boundChangeListener)

    this.$once('hook:beforeDestroy', removeListener)
  }
}

</script>

<style scoped>
.klkMap-wrap {
  position: relative;
}
.map_container {
  position: relative;
  width: 100%;
  height: 100%;
}
  /* geolocate  */
  ::v-deep .mapboxgl-ctrl-geolocate {
    display: none;
  }

  ::v-deep .klkMap_user_location-marker {
    background-color: #1da1f2;
    border-radius: 50%;
    height: 15px;
    width: 15px;
    position: absolute;
    border: 1px #ffffff solid;
  }

  ::v-deep .klkMap_marker-active-add-index {
    z-index: 9999 !important;
  }

</style>
