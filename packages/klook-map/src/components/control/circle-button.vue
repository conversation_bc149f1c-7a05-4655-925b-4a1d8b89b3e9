<template>
  <div class="klkMap_circle_button">
    <slot>
    </slot>
  </div>
</template>


<script lang="ts">
import { Vue, Component } from "vue-property-decorator";

@Component
export default class PureButton extends Vue {
}
</script>


<style scoped lang="scss">
.klkMap_circle_button {
    background-color: #FFFFFF;
    box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.14);
    border-radius: 9999px;
    width: 40px;
    height: 40px;
    padding: 8px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: #212121
  }

</style>