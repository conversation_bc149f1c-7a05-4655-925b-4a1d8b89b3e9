<template>
  <Klk-map-circle-button class="klkMap_user_location">
    <svg
      v-show="!loading"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M12 14C13.1046 14 14 13.1046 14 12C14 10.8954 13.1046 10 12 10C10.8954 10 10 10.8954 10 12C10 13.1046 10.8954 14 12 14Z"
        fill="#212121"
      />
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M11.9996 1.5C11.5026 1.5 11.0996 1.90294 11.0996 2.4V2.54211C6.57142 2.96782 2.96755 6.57184 2.54207 11.1001H2.4C1.90294 11.1001 1.5 11.503 1.5 12.0001C1.5 12.4972 1.90294 12.9001 2.4 12.9001H2.54208C2.96765 17.4283 6.57148 21.0322 11.0996 21.4579V21.6C11.0996 22.0971 11.5026 22.5 11.9996 22.5C12.4967 22.5 12.8996 22.0971 12.8996 21.6V21.458C17.4281 21.0326 21.0323 17.4285 21.4579 12.9001H21.6C22.0971 12.9001 22.5 12.4972 22.5 12.0001C22.5 11.503 22.0971 11.1001 21.6 11.1001H21.4579C21.0324 6.57158 17.4282 2.96741 12.8996 2.54204V2.4C12.8996 1.90294 12.4967 1.5 11.9996 1.5ZM11.0996 4.15075V6.1C11.0996 6.59706 11.5026 7 11.9996 7C12.4967 7 12.8996 6.59706 12.8996 6.1V4.15066C16.5437 4.56373 19.4361 7.45604 19.8493 11.1001H17.9C17.4029 11.1001 17 11.503 17 12.0001C17 12.4972 17.4029 12.9001 17.9 12.9001H19.8493C19.436 16.5441 16.5436 19.4363 12.8996 19.8493V17.9C12.8996 17.4029 12.4967 17 11.9996 17C11.5026 17 11.0996 17.4029 11.0996 17.9V19.8493C7.45594 19.4359 4.56397 16.5438 4.15071 12.9001H6.1C6.59706 12.9001 7 12.4972 7 12.0001C7 11.503 6.59706 11.1001 6.1 11.1001H4.15069C4.56387 7.4563 7.45588 4.56414 11.0996 4.15075Z"
        fill="currentColor"
      />
    </svg>
    <klk-loading v-show="loading"></klk-loading>
  </Klk-map-circle-button>
</template>

<script lang="ts">
import { Vue, Inject, Component, Prop } from 'vue-property-decorator'
import { MapTypes, UserLocationTypes } from '../../types/index'
import KlkMapCircleButton from './circle-button.vue'

@Component({
  components: {
    KlkMapCircleButton
  }
})
export default class MapUserLocation extends Vue {
  @Prop({ type: Object, default: null }) options!: UserLocationTypes.Options
  @Prop({ type: Object, default: null }) flyOption!: UserLocationTypes.flyOption

  @Inject() readonly map!: any

  $control: UserLocationTypes.Tool | undefined = undefined

  loading = false

  mounted() {
    this.map.$getMapTool?.then((mapInstance: MapTypes.MapTool) => {
      this.$control = this.userLocation(mapInstance)
    })
  }

  userLocation(mapInstance: MapTypes.MapTool) {
    this.$control = mapInstance.setGeoLocate(
      this.$el as Element,
      {
        ...this.options
      },
      {
        error: () => this.$emit('error'),
        success: (lngLat: MapTypes.FormatLngLat) => this.$emit('success', lngLat),
        setLoading: (loading: boolean) => {
          this.loading = loading
        }
      },
      this.flyOption
    )

    // vue destroy 之后再移除
    this.$once('hook:destroyed', () => {
      this.$control?.remove()
    })

    return this.$control
  }
}
</script>

<style scoped lang="scss">
.klkMap_user_location {
  position: absolute;
  left: 20px;
  bottom: 40px;
  z-index: 199; //比默认active maker 102大
}
</style>
