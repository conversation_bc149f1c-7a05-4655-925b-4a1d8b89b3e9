<template>
  <div ref="searchBarContainer" class="search-bar-container">
    <input ref="searchBar" class="search-input" type="text" />
  </div>
</template>

<script lang="ts">
import { Vue, Inject, Component } from 'vue-property-decorator'
import { MapTypes } from '../../types/index'

@Component
export default class MapSearchBar extends Vue {
  @Inject() readonly map!: any
  $searchBar: any

  mounted() {
    this.map.$getMapTool.then(($map: MapTypes.MapTool) => {
      this.$searchBar = $map.initSearchBar && $map.initSearchBar(this.$refs.searchBar as HTMLInputElement, this.$refs.searchBarContainer as HTMLElement)
    })
  }

  clearSearchBar() {
    this.$searchBar.clearSearchBar()
  }

  beforeDestroy() {
    this.clearSearchBar()
  }
}
</script>

<style scoped>
.search-bar-container {
  position: absolute;
  width: 100%;
  top: 10px;
  left: 10px;
}
.search-input {
  width: 60%;
  height: 40px;
  line-height: 40px;
  border: 0;
  padding: 0 10px;
  display: block;
  border-radius: 2px;
  outline: none;
  font-size: 14px;
}

.search-input:focus {
  outline: none;
}
</style>
