<template>
  <div
    ref="marker"
    :class="cClass"
    :style="markerStyle"
    @mouseenter="handleMouseEnter"
    @mouseleave="handleMouseLeave"
    @click="handleMarkerClick"
  >
    <slot>
      <klk-poptip
        v-model="showPopTip"
        :trigger="trueOptions.popTrigger"
        :width="trueOptions.poptipWidth"
        :max-width="trueOptions.poptipMaxWidth"
        :flip="trueOptions.flip"
        :flip-options="trueOptions.flipOptions"
        :z-index="9999"
        :append-to-body="false"
        :offset="poptipOffset"
        style="vertical-align: top;"
        @show="$emit('markerPopTipShow')"
        @hide="$emit('markerPopTipHide')"
      >
        <slot slot="content" name="poptip">
          <div class="klkMap_marker-poptip" @click="handleMarkerClick">
            {{ trueOptions.poptip }}
          </div>
        </slot>
        <!-- 强制加动画 -->
        <div
          class="marker-box"
          :class="`marker-box-${trueOptions.type}`"
          :style="trueOptions.style"
        >
          <transition name="marker-fade">
            <img
              v-if="trueOptions.type === 'point1'"
              :key="trueOptions.type"
              :src="trueOptions.src"
              :style="trueOptions.style"
              class="marker-default-icon"
              alt=""
            >
            <img
              v-else
              :key="trueOptions.type"
              :src="trueOptions.src"
              :style="trueOptions.style"
              class="marker-default-icon"
              alt=""
            >
          </transition>
        </div>
      </klk-poptip>
    </slot>
  </div>
</template>

<script lang="ts">
import { Vue, Inject, Component, Prop, Watch } from 'vue-property-decorator'
import { MapTypes, MarkerTypes } from '../../types/index'
import { point1, point2 } from '../../points'
import { addClass, removeClass } from '../../common/utils'

@Component
export default class MapMarker extends Vue {
  // 参见文档 http://www.mapbox.cn/mapbox-gl-js/api/#marker
  @Prop({ type: [String, Array, Object], required: true }) center!: MapTypes.LngLat
  @Prop({ default: () => ({}), type: Object }) options!: MarkerTypes.Options
  @Prop({ type: String, default: '' }) status!: 'default' | 'disabled' | 'selected' | 'clicked'
  @Prop({ type: Boolean, default: false }) isActiveAddIndex!: boolean
  @Prop({ type: Number, default: undefined }) zIndex!: number

  @Inject() readonly map!: any

  $marker: MarkerTypes.Tool | undefined = undefined

  // 初始化之后再显示
  // klk-poptip 组件bug，导致默认显示样式异常
  // nextTick 后更新显示
  showMarker = false

  // 手动控制 PopTip
  showPopTip = false

  // 需要 支持hover 动画
  isHover = false

  // 两种类型marker点
  pointTypes = Object.freeze({
    point1: {
      src: point1,
      type: 'point1',
      anchor: 'bottom',
      markerOffset: {
        x: 0,
        y: 10
      },
      style: {
        verticalAlign: 'top',
        width: '36px',
        height: '50px'
      }
    },
    point2: {
      src: point2,
      type: 'point2',
      anchor: 'center',
      markerOffset: {
        x: 0,
        y: 0
      },
      style: {
        verticalAlign: 'top',
        width: '16px',
        height: '16px'
      }
    }
  })

  get isAMap() {
    return this.map.type === MapTypes.Type.amap
  }

  get markerStyle() {
    const style: Record<string, string> = {}

    if (!this.isAMap) {
      style.position = 'absolute'
    }

    if (!this.showMarker) {
      style.visibility = 'hidden'
    }

    return style
  }

  get cClass() {
    const statusClass = this.status ? [
      `klkMap_marker-status--${this.status}`,
      'klkMap_marker-status'
    ] : []
    return [{
      klkMap_marker: true,
      'klkMap_marker-active': this.isActive
    }, ...statusClass]
  }

  get poptipOffset() {
    const { hoveAnimate, type } = this.trueOptions

    return (hoveAnimate && type === 'point2') ? 42 : 10
  }

  get isActiveExcludeHover() {
    return this.showPopTip || this.status === 'selected'
  }

  get isActive() {
    return this.isHover || this.isActiveExcludeHover
  }

  get trueOptions() {
    // 是否开始hover动画换图
    const hoveAnimate = !!this.options.hoveAnimate && this.options.type === 'point2'

    const pointType = (
      hoveAnimate && this.isActive
    ) ? 'point1' : this.options.type || 'point1'

    const point = this.pointTypes[pointType]

    const options: MarkerTypes.Options = {
      anchor: point?.anchor as MarkerTypes.Anchor,
      offset: point?.markerOffset,
      src: point?.src,
      style: point?.style,
      poptip: undefined,
      popTrigger: 'none', // 固定显示
      draggable: false,
      flip: false,

      // 只能point2 变point1生效
      hoveAnimate,

      zIndex: this.zIndex,
      ...this.options,
      type: pointType

    }

    return options
  }

  @Watch('center')
  valueChange(center: MapTypes.LngLat) {
    if (this.$marker) {
      this.$marker.setLngLat(center)
    }
  }

  @Watch('zIndex')
  setZIndex(zIndex: number) {
    if (this.$marker) {
      this.$marker.setzIndex(zIndex)
    }
  }

  @Watch('isActive')
  activeChange(state: boolean) {
    this.$nextTick(() => {
      if (this.isActiveAddIndex) {
        const className = 'klkMap_marker-active-add-index'
        const func = state ? addClass : removeClass
        func((this.isAMap ? this.$el.parentNode : this.$el) as HTMLElement, className)
      }
    })
  }

  mark(mapInstance: MapTypes.MapTool) {
    this.$marker = mapInstance.mark(this.center, {
      ...this.trueOptions,
      element: this.$refs.marker as Element
    })

    // vue destroy 之后再移除
    this.$once('hook:destroyed', () => {
      // map 方法释放，同时释放map内marker引用
      mapInstance.removeMarker(this.$marker!)
    })

    return this.$marker
  }

  mounted() {
    this.map.$getMapTool.then((mapInstance: MapTypes.MapTool) => {
      const $marker = this.mark(mapInstance)

      $marker.onMarkerReady().then(() => {
        this.showMarker = true

        if (
          this.trueOptions.popTrigger === 'none' &&
          this.trueOptions.popAutoShow !== false &&
          (this.trueOptions.poptip || this.$scopedSlots.poptip)
        ) {
          // 有内容固定展示 poptip
          // google mpa marker 异步创建
          this.showPopTip = true
        }
        // 初始被选中时候也应该凸出active, 不然也会被遮挡, 不在watch immediate中处理是因为aMap初始时候还没有上级aMap的marker
        this.activeChange(this.isActive)
      })
    })
  }

  // 更新type需要重新计算地图marker位置
  @Watch('trueOptions.type')
  shouldUpdateMarker() {
    // hover换了图片 需要重新marker位置
    if (this.$marker) {
      this.$nextTick(() => {
        this.$marker!.update({
          offset: this.trueOptions.offset,
          anchor: this.trueOptions.anchor
        })
      })
    }
  }

  handleMouseEnter(e: Event) {
    if (this.trueOptions.hoveAnimate) {
      this.isHover = true
    }
    this.activeChange(true)
    this.$emit('markerMouseEnter', e)
  }

  handleMouseLeave(e: Event) {
    this.isHover = false
    this.activeChange(this.isActiveExcludeHover)
    this.$emit('markerMouseLeave', e)
  }

  handleMarkerClick(e: Event) {
    this.$emit('markerClick', e)
  }
}

</script>

<style scoped lang="scss">
.klkMap_marker {
  z-index: 100;
  cursor: pointer;
  ::v-deep {
    .klkMap_marker {
      vertical-align: top;
    }
    .klk-poptip-popper-inner {
      margin: 0;
      padding: 12px;
      width: 300px;
      width: max-content; // hack ...
      .klkMap_marker-poptip {
        margin: -12px;
        padding: 12px;
      }
    }
  }
}
.klkMap_marker-active {
  z-index: 101;
}
.marker-box {
  position: relative;
  &.marker-box-point2 {
    border-radius: 50%;
    box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.38);
  }
}
.marker-default-icon {
  position: absolute;
  width: 100%;
  height: 100%;
  left: 50%;
  transform: translateX(-50%);
  bottom: 0;
  background: transparent;
}

.marker-fade-enter-active,
.marker-fade-leave-active {
  transition: opacity 0.1s;
}
.marker-fade-enter,
.marker-fade-leave-to {
  opacity: 0;
}

/* klkMap_marker-status  */
.klkMap_marker {
  &.klkMap_marker-status {
    ::v-deep .klk-poptip {
      .marker-box {
        border: 1px solid #E6E6E6;
        box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.14);
        border-radius: 9999px;
        background-color: #FFFFFF;
        position: relative;
        font-weight: 600;
        font-size: 14px;
        line-height: 150%;
        padding: 3px 10px;
        top: -8px;
        white-space: nowrap;
        color: #212121;
        &::after {
          position: absolute;
          content: "";
          border-left: 8px solid transparent;
          border-right: 8px solid transparent;
          bottom: -8px;
          border-top: 8px solid;
          left: 50%;
          margin-left: -8px;
          color: #FFFFFF;
          z-index: 2;
        }
        &:hover {
          transform-origin: bottom;
          transform: scale(1.1);
          transition: transform 0.1s;
        }
      }
    }
    &:hover {
      z-index: 9999;
    }

    &.klkMap_marker-status--disabled {
      ::v-deep .klk-poptip {
        .marker-box {
          color: #A8A8A8;
        }
      }
    }

    &.klkMap_marker-status--clicked {
      ::v-deep .klk-poptip {
        .marker-box {
          color: #757575;
          background-color: #FFFFFF;
          &::after {
            color: #FFFFFF;
          }
        }
      }
    }

    &.klkMap_marker-status--selected {
      ::v-deep .klk-poptip {
        .marker-box {
          padding: 1px 8px;
          border: 3px solid #FF5B00;
          &::after {
            bottom: -7px;
          }

          &::before {
            position: absolute;
            content: "";
            border-left: 11px solid transparent;
            border-right: 11px solid transparent;
            bottom: -12px;
            border-top: 11px solid;
            left: 50%;
            margin-left: -11px;
            color: #FF5B00;
            z-index: 1;
          }
        }
      }
    }
  }
}

</style>
