<template>
  <div v-if="geoFenceConf.canEdit && map.type === 'amap'" class="amp-button-wrapper">
    <button class="amp-controls-btn" style="margin-bottom: 5px" @click="$geoFence.createPolygon()">新建</button>
    <button class="amp-controls-btn" style="margin-bottom: 5px" @click="$geoFence.$polyEditor.open()">开始编辑</button>
    <button class="amp-controls-btn" style="margin-bottom: 5px" @click="$geoFence.deletePolygon()">删除</button>
    <button class="amp-controls-btn" style="margin-bottom: 5px" @click="$geoFence.$polyEditor.close()">结束编辑</button>
  </div>
  <div
    v-else-if="geoFenceConf.canDelete && geoFenceConf.canEdit"
    ref="fenceDeleteBtn"
    class="fence-delete-btn"
  >
    <img
      src="https://res.klook.com/image/upload/q_auto/c_fill,w_200,h_200/v1687333187/gaxl2ykgy5cffvifbn8w.png"
    />
  </div>
</template>

<script lang="ts">
import { Vue, Inject, Prop, Component, Watch } from 'vue-property-decorator'
import { MapTypes } from '../../types/index'

@Component
export default class MapGeoFence extends Vue {
  @Prop({ type: Object, default: () => ({}) })
    geoFenceConf!: MapTypes.GeoFenceConf

  @Prop({ type: Boolean, default: false }) isWgs84!: boolean

  @Inject() readonly map!: any
  $geoFence: any

  @Watch('geoFenceConf.polygonConfig', { deep: true })
  onPolygonConfigChange(newConfig: any) {
    // 当 polygonConfig 发生变化时，更新地理围栏样式
    if (this.$geoFence && this.$geoFence.updatePolygonStyle && newConfig) {
      this.$geoFence.updatePolygonStyle(newConfig)
    }
  }

  handleMarkerClick() {
    this.$emit('marker-click')
  }

  mounted() {
    this.geoFenceConf.markerClickCb = this.handleMarkerClick
    this.map.$getMapTool?.then(($map: MapTypes.MapTool) => {
      this.$geoFence =
        $map.initGeoFence &&
        $map.initGeoFence(this.$refs.fenceDeleteBtn as Element, this.geoFenceConf, this.isWgs84)
    })
  }

  getGeoFenceData() {
    const data = this.$geoFence.getGeoFenceData()
    return data
  }

  beforeDestroy() {
    this.$geoFence.clearGeoFence()
  }
}
</script>

<style scoped>
.fence-delete-btn {
  height: 24px;
  cursor: pointer;
  padding: 4px;
  border-radius: 2px;
  background: #fff;
  margin-bottom: 19px;
  box-shadow: rgba(0, 0, 0, 0.3) 0px 1px 4px -1px;
  right: 60px !important;
}

.fence-delete-btn img {
  display: block;
  height: 100%;
}

.amp-button-wrapper {
  display: flex;
  flex-direction: column;
  min-width: 0;
  word-wrap: break-word;
  background-color: #fff;
  background-clip: border-box;
  border-radius: 4px;
  width: 120px;
  border-width: 0;
  box-shadow: 0 2px 6px 0 rgba(114, 124, 245, .5);
  position: absolute;
  bottom: 12px;
  right: 12px;
  -ms-flex: 1 1 auto;
  flex: 1 1 auto;
  padding: 9px 15px;

  .amp-controls-btn {
    display: inline-block;
    font-weight: 400;
    text-align: center;
    white-space: nowrap;
    vertical-align: middle;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    border: 1px solid #25A5F7;
    transition: color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out;
    background-color: transparent;
    background-image: none;
    color: #25A5F7;
    padding: 4px 6px;
    line-height: 1.5;
    border-radius: 12px;
    -webkit-appearance: button;
    cursor: pointer;

      &:hover {
        color: #fff;
        background-color: #25A5F7;
        border-color: #25A5F7;
      }
  }
}
</style>
