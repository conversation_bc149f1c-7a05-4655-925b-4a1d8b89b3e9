/**
 * Created by <PERSON><PERSON>Jun on 2021/3/25 11:10.
 */

import { MapTypes } from '../types/index'
import { deepMergeObj } from '../common/utils'
import GoogleMapGl from './google-map'
import MapboxGl from './mapbox-gl'
import Amap from './amap'

import defaultConf from './defautl-conf'

// 构造函数类型
const CtorMap = {
  [MapTypes.Type.google]: GoogleMapGl,
  [MapTypes.Type.mapbox]: MapboxGl,
  [MapTypes.Type.amap]: Amap

}

export default async ({
  container,
  mapConf,
  mapOpts = {}
}: {
  container: Element,
  mapConf?: MapTypes.UserMapConf,
  mapOpts?: MapTypes.MapOpts
}) => {
  const userMapConf = deepMergeObj(defaultConf, mapConf) as MapTypes.UserMapConf

  const MapCtor = CtorMap[userMapConf.type]

  const instance = new MapCtor(container, userMapConf[`${userMapConf.type}Conf`] as MapTypes.Conf, mapOpts)
  await instance.initMap()

  return instance
}
