/**
 * Created by <PERSON><PERSON>Jun on 2021/3/25 14:09.
 */

// token

import { MapTypes } from '../types/index'

const defaultConf: MapTypes.UserMapConf = {
  type: MapTypes.Type.mapbox,

  googleConf: {
    token: 'AIzaSyAg8OY6H5QUhag2qFobbaVMrQ8HivE9y3U'
  },

  mapboxConf: {
    token: 'pk.eyJ1Ijoia2xvb2siLCJhIjoiY2x2ajhtZG1oMW5keTJpbnYxbmx5bno1aiJ9.RtbgEpisNstj_C4kzpadjQ'
  },

  amapConf: {
    token: 'd2087f935ee04d3c8adc98991ee2a422',
    secret: '7b89b10eefb3d7432e461674bde68a56'
  }
}

export default defaultConf
