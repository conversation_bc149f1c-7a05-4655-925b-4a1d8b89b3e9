/**
 * Created by <PERSON><PERSON> on 2021/3/26 18:39.
 */

import { MapTypes, MarkerTypes } from '../../types'
import { formatLngLat, formatLngLatArray } from '../../common/map-utils'

export default abstract class MarkerAbstract {
  mapSdk: any
  map: any
  lngLat: any
  // statusPromise: Promise<any> | null = null
  opts: MarkerTypes.Options

  setLngLatByType(lngLat: MapTypes.LngLat, type: MapTypes.Type) {
    // 高德使用数组
    this.lngLat = type === MapTypes.Type.amap ? formatLngLatArray(lngLat, type) : formatLngLat(lngLat, type)
  }

  protected constructor(mapSdk: any, map: any, lngLat: MapTypes.LngLat, opts: MarkerTypes.Options, type: MapTypes.Type) {
    this.setLngLatByType(lngLat, type)

    this.mapSdk = mapSdk
    this.map = map
    this.opts = opts
    this.init()
  }

  abstract init(): void
  abstract onMarkerReady(): Promise<any>
  abstract getLngLat(): MapTypes.LngLat
  abstract setLngLat(lngLat: MapTypes.LngLat): void
  abstract isDraggable(): boolean
  abstract remove(): void
  abstract update(newOptions: MarkerTypes.Options): void
}
