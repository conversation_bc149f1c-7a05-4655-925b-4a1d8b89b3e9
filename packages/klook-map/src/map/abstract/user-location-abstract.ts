/**
 * Created by <PERSON><PERSON>Jun on 2021/3/26 18:39.
 */

import { MapTypes, UserLocationTypes } from '../../types'
import { formatLngLat } from '../../common/map-utils'

export default abstract class UserLocationAbstract {
  mapSdk: any
  map: any
  lngLat: MapTypes.LngLat  | null = null  // 首次
  opts: UserLocationTypes.Options
  fn: UserLocationTypes.Callback
  type: MapTypes.Type
  element: Element  | null = null 
  flyOption: UserLocationTypes.flyOption | null = null

  constructor(mapSdk: any, map: any, element: Element, opts: UserLocationTypes.Options, fn: UserLocationTypes.Callback = {}, flyOption: UserLocationTypes.flyOption, type: MapTypes.Type) {
    this.type = type
    this.mapSdk = mapSdk
    this.map = map
    this.opts = opts
    this.fn = fn
    this.element = element
    this.flyOption = flyOption
    this.init()
  }

  abstract init(): void
  abstract setLngLat(lngLat: MapTypes.LngLat): void
  abstract remove(): void
}
