import { MapTypes } from '../../types'

export default abstract class GeoFence {
  id: string
  mapSdk: any
  map: any
  type: MapTypes.Type
  fenceDeleteBtn: Element
  geoFenceConf: MapTypes.GeoFenceConf
  isWgs84: Boolean

  constructor(
    mapSdk: any,
    map: any,
    type: MapTypes.Type,
    fenceDeleteBtn: Element,
    geoFenceConf: MapTypes.GeoFenceConf,
    isWgs84: Boolean
  ) {
    this.type = type
    this.mapSdk = mapSdk
    this.map = map
    this.fenceDeleteBtn = fenceDeleteBtn
    this.geoFenceConf = geoFenceConf
    this.isWgs84 = isWgs84
    this.id = Math.random().toString(16).slice(2)
    this.init()
  }

  abstract init(): void;
  abstract initGeoFence(): void;
  abstract updatePolygonStyle?(polygonConfig: any): void;
}
