import { MapTypes } from '../../types'

export default abstract class SearchBar {
  mapSdk: any
  map: any
  searchBar: HTMLInputElement
  searchBarContainer: HTMLElement

  constructor(mapSdk: any, map: any, searchBar: HTMLInputElement, searchBarContainer: HTMLElement) {
    this.mapSdk = mapSdk
    this.map = map
    this.searchBar = searchBar
    this.searchBarContainer = searchBarContainer
    this.init()
  }

  abstract init(): void;
}
