/**
 * Created by <PERSON><PERSON>Jun on 2021/3/24 15:49.
 */

import { MarkerTypes, MapTypes } from '../../types'
import { removeArrayItem } from '../../common/utils'

export default abstract class MapAbstract {
  container: Element
  mapConf: MapTypes.Conf
  mapOpts: MapTypes.MapOpts

  protected markers: any[] = []

  constructor(container: Element, conf: MapTypes.Conf, opts: MapTypes.MapOpts) {
    this.container = container
    this.mapConf = conf
    this.mapOpts = opts
  }

  abstract initMap(): any

  // 打点
  abstract mark(lngLat: MapTypes.LngLat, markerOptions: MarkerTypes.Options): MarkerTypes.Tool

  // 移除单个marker
  removeMarker(marker: MarkerTypes.Tool) {
    marker.remove()
    removeArrayItem(this.markers, marker)
  }

  // 清空所有marker
  clearMarker() {
    this.markers.forEach(item => item.remove())
    this.markers.length = 0
  }

  abstract setCenter(center: MapTypes.LngLat): void
  abstract setZoom(zoom: number): void
  abstract setMinZoom(zoom: number): void
  abstract setMaxZoom(zoom: number): void

  // fly to
  abstract flyTo(latLng: MapTypes.LngLat): void

  // pan to
  abstract panTo(latLng: MapTypes.LngLat): void

  abstract fitBounds(bound: MapTypes.LngLatBounds.LngLatBoundLike, options?: MapTypes.LngLatBounds.Options): void

  abstract getBounds(): MapTypes.LngLatBounds.LatLngBounds

  abstract getBoundsSwNe(): {
    sw: MapTypes.LngLat,
    ne: MapTypes.LngLat,
    bounds: MapTypes.LngLatBounds.LatLngBounds
  }

  abstract createBounds(sw: MapTypes.LngLat, ne: MapTypes.LngLat, options?: MapTypes.LngLatBounds.CreateOptions): MapTypes.LngLatBounds.LatLngBounds

  abstract isContains(latLng: MapTypes.LngLat, padding?: MapTypes.LngLatBounds.Padding): boolean

  abstract resize(): this

  abstract bindBoundsChangeEvent(boundChangeListener: Function): Function

  abstract isMovingOrZooming(): boolean

  abstract getContainer(): HTMLElement
  abstract initGeoFence(fenceDeleteBtn: Element, geoFenceConf: MapTypes.GeoFenceConf, isWgs84: Boolean): void
  abstract initSearchBar(searchBar: HTMLInputElement, searchBarContainer: HTMLElement): void

  abstract getMapRangeCoordinates(padding?: MapTypes.LngLatBounds.Padding): {
    sw: MapTypes.LngLat,
    ne: MapTypes.LngLat,
  }
}
