import SearchBar from '../abstract/search-bar'
import { loadScript, loadCss, genSingle } from '../../common/utils'

const loadSdk = genSingle(async () => {
  loadCss(
    'https://api.mapbox.com/mapbox-gl-js/plugins/mapbox-gl-geocoder/v5.0.0/mapbox-gl-geocoder.css'
  )
  await loadScript(
    'https://api.mapbox.com/mapbox-gl-js/plugins/mapbox-gl-geocoder/v5.0.0/mapbox-gl-geocoder.min.js'
  )
})
export default class mapboxSearchBar extends SearchBar {
  async init() {
    await loadSdk()
    this.searchBar && this.searchBar.remove()
    this.map.once('idle', () => {
      if (this.searchBarContainer) {
        const geocoder = new window.MapboxGeocoder({
          accessToken: this.mapSdk.accessToken,
          mapboxgl: this.mapSdk
        })
        this.searchBarContainer.appendChild(geocoder.onAdd(this.map))
      }
    })
  }

  clearSearchBar() {
    this.searchBarContainer && this.searchBarContainer.remove()
  }
}
