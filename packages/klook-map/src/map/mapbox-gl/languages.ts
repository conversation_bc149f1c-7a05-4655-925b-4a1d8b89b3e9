/**
 * https://www.npmjs.com/package/@mapbox/mapbox-gl-language
 * Mapbox Streets 支持的语言。
 * **/

const languageList = [
  // { language: 'ar', list: [''], text: 'Arabic' },
  { language: 'zh-Hans', list: ['zh-CN'], text: 'Chinese Simplified' },
  { language: 'zh-Hant', list: ['zh-HK', 'zh-TW'], text: 'Chinese Traditional' },
  // { language: 'en', list: [''], text: 'English' },
  { language: 'fr', list: ['fr'], text: 'French' },
  { language: 'de', list: ['de'], text: 'German' },
  { language: 'it', list: ['it'], text: 'Italian' },
  { language: 'ja', list: ['ja'], text: 'Japanese' },
  { language: 'ko', list: ['ko'], text: 'Korean' },
  // { language: 'multilingual', list: [''], text: 'Multilingual' },
  // { language: 'pt', list: [''], text: 'Portuguese' },
  { language: 'ru', list: ['ru'], text: 'Russian' },
  { language: 'es', list: ['es'], text: 'Spanish' },
  { language: 'vi', list: ['vi'], text: 'Vietnamese' }
]
export const getMapLanguage = function(lang: string){
  const item = languageList.find((o) => {
    return o.list.includes(lang)
  })
  return item?.language || ''
}