/**
 * Created by <PERSON><PERSON>Jun on 2021/3/24 15:57.
 */

import MapboxLanguage from '@mapbox/mapbox-gl-language'
import MapAbstract from '../abstract/map-abstract'

import { loadScript, loadCss, genSingle } from '../../common/utils'
import { formatLngLat, getMapRangeCoordinates } from '../../common/map-utils'
import { MapTypes, MarkerTypes, UserLocationTypes } from '../../types/index'
import Marker from './marker'
import UserLocation from './user-location'
import GeoFence from './geo-fence'
import SearchBar from './search-bar'
import { getMapLanguage } from './languages'

const loadSdk = genSingle(() => {
  loadCss('https://api.tiles.mapbox.com/mapbox-gl-js/v1.13.2/mapbox-gl.css')
  return loadScript('https://api.tiles.mapbox.com/mapbox-gl-js/v1.13.2/mapbox-gl.js').then(() => {
    return (window as any).mapboxgl
  })
})

export default class MapboxGl extends MapAbstract implements MapTypes.MapTool {
  public mapSdk: any
  public map: any
  GeoFence: any
  onLoaded = () => {
    return new Promise<void>((resolve) => {
      this.map.on('load', function () {
        resolve()
      })
    })
  }

  async initMap() {
    this.mapSdk = await loadSdk()
    this.mapSdk.accessToken = this.mapConf.token
    const defaultOpts = {
      style: 'mapbox://styles/mapbox/streets-v11', // style URL
      attributionControl: false // 右下角版权
    }
    this.map = new this.mapSdk.Map({
      ...defaultOpts,
      container: this.container,
      ...this.mapOpts,
      center: formatLngLat(this.mapOpts.center!, MapTypes.Type.mapbox)
    })

    const { language } = this.mapOpts
    if (language) {
      const mapLanguage = getMapLanguage(language)
      if (mapLanguage) {
        // 设置语言
        const langConfig = new MapboxLanguage({
          defaultLanguage: mapLanguage
        })
        this.map.addControl(langConfig)
      }
    }

    if (this.mapOpts?.fullscreenControl) {
      this.map.addControl(new this.mapSdk.FullscreenControl())
    }

    // map load 之后返回
    await this.onLoaded()
    return this.map
  }

  mark(lngLat: MapTypes.LngLat, markerOptions: MarkerTypes.Options): MarkerTypes.Tool {
    const marker = new Marker(this.mapSdk, this.map, lngLat, markerOptions)
    this.markers.push(marker)

    return marker
  }

  initGeoFence(fenceDeleteBtn: Element, geoFenceConf: MapTypes.GeoFenceConf, isWgs84: Boolean): void {
    this.GeoFence = new GeoFence(
      this.mapSdk,
      this.map,
      fenceDeleteBtn,
      geoFenceConf,
      isWgs84
    )
    return this.GeoFence
  }

  initSearchBar(searchBar: HTMLInputElement, searchBarContainer: HTMLElement) {
    return new SearchBar(this.mapSdk, this.map, searchBar, searchBarContainer)
  }

  setCenter(center: MapTypes.LngLat) {
    this.map.setCenter(formatLngLat(center, MapTypes.Type.mapbox))
  }

  setZoom(zoom: number) {
    this.map.zoomTo(zoom)
  }

  setMinZoom(zoom: number) {
    this.map.setMinZoom(zoom)
  }

  setMaxZoom(zoom: number) {
    this.map.setMaxZoom(zoom)
  }

  flyTo(lngLat: MapTypes.LngLat) {
    this.map.flyTo({
      center: formatLngLat(lngLat, MapTypes.Type.mapbox),
      speed: 1.2,
      maxDuration: 1000
    })
  }

  panTo(lngLat: MapTypes.LngLat) {
    this.map.panTo(formatLngLat(lngLat, MapTypes.Type.mapbox))
  }

  setGeoLocate(element: Element, userLocationOptions: UserLocationTypes.Options, fn: UserLocationTypes.Callback, flyOption: UserLocationTypes.flyOption) {
    return new UserLocation(this.mapSdk, this.map, element, userLocationOptions, fn, flyOption)
  }

  fitBounds(bound: MapTypes.LngLatBounds.LngLatBoundLike | MapTypes.LngLatBounds.LatLngBounds, options?: MapTypes.LngLatBounds.Options) {
    this.map.fitBounds(bound, options || {})
  }

  getBounds() {
    return this.map.getBounds()
  }

  getBoundsSwNe() {
    const bounds = this.map.getBounds()
    const sw = bounds.getSouthWest()
    const ne = bounds.getNorthEast()
    return {
      sw,
      ne,
      bounds
    }
  }

  createBounds(sw: MapTypes.LngLat, ne: MapTypes.LngLat, options?: MapTypes.LngLatBounds.CreateOptions) {
    [sw, ne] = [sw, ne].map((item: MapTypes.LngLat) => {
      return options?.formatLngLat === false ? item : formatLngLat(item, MapTypes.Type.mapbox)
    })
    return new this.mapSdk.LngLatBounds(
      sw,
      ne
    )
  }

  isContains(latLng: MapTypes.LngLat, padding?: MapTypes.LngLatBounds.Padding): boolean {
    if (padding) {
      const { sw, ne } = this.getMapRangeCoordinates(padding)
      return this.createBounds(sw, ne, {
        formatLngLat: false
      }).contains(formatLngLat(latLng, MapTypes.Type.mapbox))
    }
    return this.getBounds().contains(formatLngLat(latLng, MapTypes.Type.mapbox))
  }

  resize() {
    return this.map.resize()
  }

  getContainer() {
    return this.map.getContainer()
  }

  bindBoundsChangeEvent(boundChangeListener: Function): Function {
    // mapbox主动使用函数panto不会触发任何下列事件, 但是flyTo会触发zoomend
    const events = ['zoomend']
    events.forEach(event => this.map.on(event, boundChangeListener))
    const removeListener = () => events.forEach(event => this.map.off(event, boundChangeListener))
    return removeListener
  }

  isMovingOrZooming() {
    return this.map.isMoving() || this.map.isZooming()
  }

  // 去除padding的距离， 获取四个角的经纬度
  getMapRangeCoordinates(padding?: MapTypes.LngLatBounds.Padding, options?: MapTypes.LngLatBounds.MapRangeCoordinatesOption) {
    const { sw, ne } = this.getBoundsSwNe()
    const mapContainer = this.getContainer()
    return getMapRangeCoordinates(sw, ne, mapContainer.offsetWidth, mapContainer.offsetHeight, padding || 0, options)
  }
}
