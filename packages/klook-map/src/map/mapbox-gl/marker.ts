/**
 * Created by <PERSON><PERSON>Jun on 2021/3/26 18:38.
 */

import { MapTypes, MarkerTypes } from '../../types/index'
import MarkerAbstract from '../abstract/marker-abstract'
import { formatLngLat } from '../../common/map-utils'

export default class MapboxMarker extends MarkerAbstract implements MarkerTypes.Tool {
  marker: any

  constructor(mapSdk: any, map: any, lngLat: MapTypes.LngLat, opts: MarkerTypes.Options) {
    super(mapSdk, map, lngLat, opts, MapTypes.Type.mapbox)
  }

  init(): void {
    this.marker = new this.mapSdk.Marker(this.opts)
      .setLngLat(this.lngLat)

    if(this.opts.zIndex){
      this.setzIndex(this.opts.zIndex)
    }

    this.marker.addTo(this.map)
  }

  // mapbox marker 是同步
  onMarkerReady() {
    return Promise.resolve()
  }

  getLngLat(): MapTypes.LngLat {
    return this.marker.getLngLat()
  }

  setLngLat(lngLat: MapTypes.LngLat): void {
    this.marker.setLngLat(formatLngLat(lngLat, MapTypes.Type.mapbox))
  }

  isDraggable(): boolean {
    return this.marker.isDraggable()
  }

  remove(): void {
    this.marker.remove()
  }

  setzIndex(zindex: number) {
    const ele = this.marker.getElement()
    if(ele){
      ele.style.zIndex = this.opts.zIndex
    }
    this.opts.zIndex = this.opts.zIndex
  }

  // mapbox 不直接暴露设置 anchor的方法
  // 统一通过内部方法实现，避免内部会多次 _update
  update(newOptions: MarkerTypes.Options) {
    if (newOptions.offset) {
      this.marker._offset = newOptions.offset
    }

    if (newOptions.anchor) {
      this.marker._anchor = newOptions.anchor
    }

    if(newOptions.zIndex) {
      this.setzIndex(newOptions.zIndex)
    }

    this.marker._update(newOptions)
  }
}
