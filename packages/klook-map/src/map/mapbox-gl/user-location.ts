import { MapTypes, UserLocationTypes } from '../../types/index'
import UserLocationAbstract from '../abstract/user-location-abstract'

export default class MapboxUserControl extends UserLocationAbstract implements UserLocationTypes.Tool {
  control: any

  geoLocate: any

  constructor(mapSdk: any, map: any, element: Element, opts: UserLocationTypes.Options, fn: UserLocationTypes.Callback = {}, flyOption: UserLocationTypes.flyOption) {
    super(mapSdk, map, element, opts, fn, flyOption, MapTypes.Type.mapbox)
  }

  init(): void {
    const geoLocateControl = new this.mapSdk.GeolocateControl({
      fitBoundsOptions: {
        maxZoom: this.flyOption?.zoom || 15
      },
      positionOptions: {
        enableHighAccuracy: false,
        timeout: 3000,
        maximumAge: 60000
      },
      showUserLocation: true,
      trackUserLocation: true,
      showUserHeading: false,
      ...this.opts
    })
    // Add the control to the map.
    this.map.addControl(geoLocateControl, 'bottom-left')

    this.geoLocate = () => {
      geoLocateControl.trigger()
    }

    this.element!.addEventListener('click', this.geoLocate)

    geoLocateControl.on('geolocate', (position: any) => {
      this.fn?.success && this.fn.success({
        lat: position.coords.latitude,
        lng: position.coords.longitude
      })
    })

    geoLocateControl.on('error', () => {
      this.fn?.error && this.fn.error()
    })
  }

  remove() {
    this.element!.removeEventListener('click', this.geoLocate)
  }

  setLngLat(lngLat: MapTypes.LngLat) {
    this.lngLat = lngLat
  }
}
