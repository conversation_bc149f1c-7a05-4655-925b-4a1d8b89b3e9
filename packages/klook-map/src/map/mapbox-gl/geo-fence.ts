import { MapTypes } from '../../types/index'
import GeoFence from '../abstract/geo-fence'
import { getCenterLatLng } from '../../common/map-utils'
import { loadScript, loadCss, genSingle } from '../../common/utils'
import { point1 } from '../../points'

const loadSdk = genSingle(async () => {
  loadCss(
    'https://api.mapbox.com/mapbox-gl-js/plugins/mapbox-gl-draw/v1.4.0/mapbox-gl-draw.css'
  )
  await loadScript(
    'https://api.mapbox.com/mapbox-gl-js/plugins/mapbox-gl-draw/v1.4.0/mapbox-gl-draw.js'
  )
})
interface Markers {
  marker: any;
  clickHandler: any;
}
export default class MapBoxGeoFence extends GeoFence {
  $geoFence: any
  markers: Markers[] = []

  constructor(
    mapSdk: any,
    map: any,
    fenceDeleteBtn: Element,
    geoFenceConf: MapTypes.GeoFenceConf,
    isWgs84: Boolean
  ) {
    super(
      mapSdk,
      map,
      MapTypes.Type.mapbox,
      fenceDeleteBtn,
      geoFenceConf,
      isWgs84
    )
  }

  get initPolygonData() {
    return {
      type: 'Feature',
      properties: { initPolygon: true }, // 标记为初始化的多边形
      geometry: {
        type: this.geoFenceConf.type || 'Polygon',
        coordinates: this.geoFenceConf?.polygonList
      }
    }
  }

  async init() {
    await loadSdk()
    this.fenceDeleteBtn && this.fenceDeleteBtn.remove()
    if (this.geoFenceConf.canEdit) {
      this.editGeoFence()
    } else {
      this.initGeoFence()
    }
  }

  updatePolygonStyle(polygonConfig: any) {
    const { fillColor, strokeColor, fillOpacity, lineWidth } = polygonConfig || {}

    // 更新填充样式
    if (this.map.getLayer('fence' + this.id)) {
      this.map.setPaintProperty('fence' + this.id, 'fill-color', fillColor || '#FF0000')
      this.map.setPaintProperty('fence' + this.id, 'fill-opacity', fillOpacity || 0.35)
    }

    // 更新边框样式
    if (this.map.getLayer('fence-outline' + this.id)) {
      this.map.setPaintProperty('fence-outline' + this.id, 'line-color', strokeColor || '#FF0000')
      this.map.setPaintProperty('fence-outline' + this.id, 'line-width', [
        'interpolate',
        ['exponential', 2],
        ['zoom'],
        10,
        lineWidth || 2,
        18,
        4
      ])
    }
  }

  initGeoFence() {
    const { fillColor, strokeColor, fillOpacity, lineWidth } = this.geoFenceConf?.polygonConfig || {}
    this.map.addSource('fence' + this.id, {
      type: 'geojson',
      data: {
        type: 'FeatureCollection',
        features: [this.initPolygonData]
      }
    })

    this.map.addLayer({
      id: 'fence' + this.id,
      type: 'fill',
      source: 'fence' + this.id,
      paint: {
        'fill-color': fillColor || '#FF0000',
        'fill-opacity': fillOpacity || 0.35
      }
    })
    this.map.addLayer({
      id: 'fence-outline' + this.id,
      type: 'line',
      source: 'fence' + this.id,
      paint: {
        'line-color': strokeColor || '#FF0000',
        'line-width': [
          'interpolate',
          ['exponential', 2],
          ['zoom'],
          10,
          lineWidth || 2, // 最小缩放级别时的线条宽度
          18,
          4 // 最大缩放级别时的线条宽度
        ],
        'line-opacity': 0.8 // 线条透明度
      }
    })

    this.geoFenceConf?.polygonList.forEach((item: any, index: number) => {
      this.setMarker(item, index)
    })
  }

  setMarker(position: any, uuid: number | string) {
    if (!this.geoFenceConf.showMarker) { return null }
    const customIcon = document.createElement('img')
    const { src, width = 36 } = this.geoFenceConf.markerConfig || {}
    customIcon.src = src || point1
    customIcon.style.width = width + 'px'
    customIcon.style.background = 'none'
    const center = getCenterLatLng(position, true)
    const marker = new this.mapSdk.Marker({ element: customIcon })
      .setLngLat(center)
      .addTo(this.map)

    const clickHandler = () => {
      const lngLat = marker.getLngLat()
      this.map.flyTo({
        center: lngLat,
        zoom: this.geoFenceConf.clickZoom || 8,
        speed: 1.5
      })
      if (this.geoFenceConf.markerClickCb && typeof this.geoFenceConf.markerClickCb === 'function') {
        this.geoFenceConf.markerClickCb()
      }
    }

    marker.getElement().addEventListener('click', clickHandler)
    marker.uuid = uuid
    this.markers.push({ marker, clickHandler })
  }

  clearGeoFence() {
    this.markers.forEach(({ marker, clickHandler }) => {
      marker.getElement().removeEventListener('click', clickHandler)
      marker.remove()
    })
    this.$geoFence = null
    if (this.map.getLayer('fence' + this.id)) {
      this.map.removeLayer('fence' + this.id)
    }

    if (this.map.getLayer('fence-outline' + this.id)) {
      this.map.removeLayer('fence-outline' + this.id)
    }

    if (this.map.getSource('fence' + this.id)) {
      this.map.removeSource('fence' + this.id)
    }

    this.map.off('draw.create', this.createGeoFence.bind(this))
    this.map.off('draw.delete', this.deleteGeoFence.bind(this))
  }

  initMapboxDraw() {
    this.$geoFence = new window.MapboxDraw({
      displayControlsDefault: false,
      controls: {
        polygon: true,
        trash: this.geoFenceConf.canDelete
      },
      defaultMode: 'draw_polygon',
      styles: [
        // line stroke
        {
          id: 'gl-draw-line',
          type: 'line',
          filter: [
            'all',
            ['==', '$type', 'LineString'],
            ['==', 'active', 'true']
          ],
          layout: {
            'line-cap': 'round',
            'line-join': 'round'
          },
          paint: {
            'line-color': '#D20C0C',
            'line-dasharray': [0.2, 2],
            'line-width': 2
          }
        },
        // polygon fill
        {
          id: 'gl-draw-polygon-fill',
          type: 'fill',
          filter: ['all', ['==', '$type', 'Polygon'], ['==', 'active', 'true']],
          paint: {
            'fill-color': '#D20C0C',
            'fill-outline-color': '#D20C0C',
            'fill-opacity': 0.1
          }
        },
        // polygon mid points
        {
          id: 'gl-draw-polygon-midpoint',
          type: 'circle',
          filter: ['all', ['==', '$type', 'Point'], ['==', 'meta', 'midpoint']],
          paint: {
            'circle-radius': 3,
            'circle-color': '#fbb03b'
          }
        },
        // polygon outline stroke
        // This doesn't style the first edge of the polygon, which uses the line stroke styling instead
        {
          id: 'gl-draw-polygon-stroke-active',
          type: 'line',
          filter: ['all', ['==', '$type', 'Polygon'], ['==', 'active', 'true']],
          layout: {
            'line-cap': 'round',
            'line-join': 'round'
          },
          paint: {
            'line-color': '#D20C0C',
            'line-dasharray': [0.2, 2],
            'line-width': 2
          }
        },
        // vertex point halos
        {
          id: 'gl-draw-polygon-and-line-vertex-halo-active',
          type: 'circle',
          filter: ['all', ['==', 'meta', 'vertex'], ['==', '$type', 'Point']],
          paint: {
            'circle-radius': 5,
            'circle-color': '#FFF'
          }
        },
        // vertex points
        {
          id: 'gl-draw-polygon-and-line-vertex-active',
          type: 'circle',
          filter: ['all', ['==', 'meta', 'vertex'], ['==', '$type', 'Point']],
          paint: {
            'circle-radius': 3,
            'circle-color': '#D20C0C'
          }
        },

        // INACTIVE
        // line stroke
        {
          id: 'gl-draw-line-inactive',
          type: 'line',
          filter: [
            'all',
            ['==', '$type', 'LineString'],
            ['==', 'active', 'false']
          ],
          layout: {
            'line-cap': 'round',
            'line-join': 'round'
          },
          paint: {
            'line-color': '#000',
            'line-width': 3
          }
        },
        // polygon fill
        {
          id: 'gl-draw-polygon-fill-inactive',
          type: 'fill',
          filter: [
            'all',
            ['==', '$type', 'Polygon'],
            ['==', 'active', 'false']
          ],
          paint: {
            'fill-color': '#000',
            'fill-outline-color': '#000',
            'fill-opacity': 0.1
          }
        },
        // polygon outline
        {
          id: 'gl-draw-polygon-stroke-inactive',
          type: 'line',
          filter: [
            'all',
            ['==', '$type', 'Polygon'],
            ['==', 'active', 'false']
          ],
          layout: {
            'line-cap': 'round',
            'line-join': 'round'
          },
          paint: {
            'line-color': '#000',
            'line-width': 3
          }
        }
      ]
    })
    this.map.addControl(this.$geoFence)
  }

  editGeoFence() {
    this.initMapboxDraw()
    if (this.geoFenceConf?.polygonList) {
      this.geoFenceConf?.polygonList.forEach((item: any) => {
        const id = this.$geoFence.add({
          type: 'Feature',
          properties: { initPolygon: true }, // 标记为初始化的多边形
          geometry: {
            type: 'Polygon',
            coordinates: [item]
          }
        })
        this.setMarker(item, id[0])
      })
    }

    this.map.on('draw.delete', this.deleteGeoFence.bind(this))
    this.map.on('draw.create', this.createGeoFence.bind(this))
  }

  createGeoFence(event: { features: any }) {
    const { features } = event
    features.forEach((feature: any) => {
      const coordinates = feature.geometry.coordinates
      coordinates.forEach((item: any) => {
        this.setMarker(item, feature.id)
      })
    })
  }

  deleteGeoFence(event: { features: any }) {
    const { features } = event
    features.forEach((feature: any) => {
      this.markers.forEach(({ marker, clickHandler }) => {
        if (feature.id === marker.uuid) {
          marker.remove()
          marker.getElement().removeEventListener('click', clickHandler)
        }
      })
    })
  }

  getGeoFenceData() {
    const geoFenceData = {
      formatInitPath: [] as any,
      formatNewPath: [] as any
    }
    const getAllData = this.$geoFence.getAll()
    getAllData?.features.forEach((feature: any) => {
      const { geometry, properties, id } = feature
      geometry.coordinates.forEach((item: any) => {
        if (item && item.length > 2) {
          if (properties.initPolygon) {
            geoFenceData.formatInitPath.push({
              path: item,
              uuid: id
            })
          } else {
            geoFenceData.formatNewPath.push({
              path: item,
              uuid: id
            })
          }
        }
      })
    })
    return geoFenceData
  }
}
