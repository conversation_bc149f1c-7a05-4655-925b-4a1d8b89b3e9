/**
 * Created by <PERSON><PERSON> on 2021/3/24 15:57.
 */

import MapAbstract from '../abstract/map-abstract'
import { loadScript, genSingle } from '../../common/utils'
import { formatLngLat, getMapRangeCoordinates } from '../../common/map-utils'

import { MapTypes, MarkerTypes, UserLocationTypes } from '../../types/index'
import Marker from './marker'
import UserLocation from './user-location'
import GeoFence from './geo-fence'
import SearchBar from './search-bar'

// token 会以初次的为准
const loadSdk = genSingle((mapConf: any, mapOpts: any) => {
  const { libraries } = mapConf || {}
  const { language } = mapOpts || {}
  const languageParam = language ? `&language=${language}` : ''
  const librariesParam = libraries ? `&libraries=${libraries}` : ''
  const mapSdk = `https://maps.googleapis.com/maps/api/js?key=${mapConf.token}${languageParam}${librariesParam}`
  return loadScript(mapSdk).then(() => {
    return (window as any).google.maps
  })
})

export default class GoogleMap extends MapAbstract implements MapTypes.MapTool {
  public mapSdk: any
  public map: any

  async initMap() {
    this.mapSdk = await loadSdk(this.mapConf as any, this.mapOpts as any)

    const { interactive, center, ...otherOptions } = this.mapOpts

    // 禁用google map floating相关的操作按钮
    const defaultOpts = {
      zoomControl: false,
      mapTypeControl: false,
      scaleControl: false,
      streetViewControl: false,
      rotateControl: false,
      fullscreenControl: false,
      disableDefaultUI: true,
      clickableIcons: false
    }

    this.map = new this.mapSdk.Map(this.container, {
      ...defaultOpts,
      ...otherOptions,
      center: formatLngLat(center!, MapTypes.Type.google),
      gestureHandling: interactive
    })

    return this.map
  }

  initGeoFence(fenceDeleteBtn: Element, geoFenceConf: MapTypes.GeoFenceConf, isWgs84: Boolean) {
    return new GeoFence(
      this.mapSdk,
      this.map,
      fenceDeleteBtn,
      geoFenceConf,
      isWgs84
    )
  }

  initSearchBar(searchBar: HTMLInputElement, searchBarContainer: HTMLElement) {
    return new SearchBar(this.mapSdk, this.map, searchBar, searchBarContainer)
  }

  mark(lngLat: MapTypes.LngLat, markerOptions: MarkerTypes.Options) {
    const marker = new Marker(this.mapSdk, this.map, lngLat, markerOptions)
    this.markers.push(marker)
    return marker
  }

  setCenter(center: MapTypes.LngLat) {
    this.map.setCenter(formatLngLat(center, MapTypes.Type.google))
  }

  setZoom(zoom: number) {
    this.map.setOptions({
      zoom
    })
  }

  setMinZoom(minZoom: number) {
    this.map.setOptions({
      minZoom
    })
  }

  setMaxZoom(maxZoom: number) {
    this.map.setOptions({
      maxZoom
    })
  }

  flyTo(lngLat: MapTypes.LngLat) {
    this.panTo(lngLat)
  }

  panTo(lngLat: MapTypes.LngLat) {
    this.map.panTo(formatLngLat(lngLat, MapTypes.Type.google))
  }

  setGeoLocate(element: Element, userLocationOptions: UserLocationTypes.Options, fn: UserLocationTypes.Callback, flyOption: UserLocationTypes.flyOption) {
    return new UserLocation(this.mapSdk, this.map, element, userLocationOptions, fn, flyOption || { zoom: 15 })
  }

  fitBounds(bound: MapTypes.LngLatBounds.LatLngBounds | MapTypes.LngLatBounds.LngLatBoundLike, options?: MapTypes.LngLatBounds.Options) {
    this.map.fitBounds(bound, options?.padding || 0)
  }

  getBounds() {
    return this.map.getBounds()
  }

  getBoundsSwNe() {
    const bounds = this.map.getBounds()
    const sw = bounds.getSouthWest().toJSON()
    const ne = bounds.getNorthEast().toJSON()
    return {
      sw,
      ne,
      bounds
    }
  }

  // 如果是已经formatLngLat后（turfBbox生成的）的点生成的矩形区域，不需要再次formatLngLat
  createBounds(sw: MapTypes.LngLat, ne: MapTypes.LngLat, options?: MapTypes.LngLatBounds.CreateOptions) {
    // new this.mapSdk.LatLng(sw),
    [sw, ne] = [sw, ne].map((item: MapTypes.LngLat) => {
      return options?.formatLngLat === false ? item : formatLngLat(item, MapTypes.Type.google)
    })
    return new this.mapSdk.LatLngBounds(
      sw,
      ne
    )
  }

  isContains(latLng: MapTypes.LngLat, padding?: MapTypes.LngLatBounds.Padding): boolean {
    if (padding) {
      const { sw, ne } = this.getMapRangeCoordinates(padding)
      return this.createBounds(sw, ne, {
        formatLngLat: false
      }).contains(formatLngLat(latLng, MapTypes.Type.google))
    }

    return this.map.getBounds().contains(formatLngLat(latLng, MapTypes.Type.google))
  }

  resize() {
    this.mapSdk.event.trigger(this.map, 'resize')
    return this.map
  }

  getContainer() {
    return this.map.getDiv()
  }

  bindBoundsChangeEvent(boundChangeListener: Function): Function {
    const listener = this.map.addListener('bounds_changed', boundChangeListener)
    const removeListener = () => this.mapSdk.event.removeListener(listener)
    return removeListener
  }

  // 不支持 待查文档
  isMovingOrZooming() {
    return false
  }

  // 使用通用方法
  getMapRangeCoordinates(padding: any = 0, options?: MapTypes.LngLatBounds.MapRangeCoordinatesOption) {
    const { sw, ne } = this.getBoundsSwNe()

    const mapContainer = this.getContainer()
    return getMapRangeCoordinates(sw, ne, mapContainer.offsetWidth, mapContainer.offsetHeight, padding || 0, options)
  }
}
