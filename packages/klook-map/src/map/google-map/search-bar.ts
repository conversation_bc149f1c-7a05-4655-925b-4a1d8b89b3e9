import SearchBar from '../abstract/search-bar'

export default class GoogleSearchBar extends SearchBar {
  $searchBox: any

  init(): void {
    if (this.searchBar) {
      this.$searchBox = new this.mapSdk.places.SearchBox(this.searchBar)
      this.map.controls[this.mapSdk.ControlPosition.TOP_LEFT].push(
        this.searchBar
      )

      this.boundsChanged()
      this.placesChanged()
    }
  }

  boundsChanged() {
    this.map.addListener('bounds_changed', () => {
      this.$searchBox.setBounds(this.map.getBounds())
    })
  }

  placesChanged() {
    let markers: any = []

    this.$searchBox.addListener('places_changed', () => {
      const places = this.$searchBox.getPlaces()

      if (places.length === 0) {
        return
      }

      markers.forEach((marker: any) => {
        marker.setMap(null)
      })
      markers = []

      const bounds = new this.mapSdk.LatLngBounds()

      places.forEach((place: any) => {
        if (place.geometry.viewport) {
          bounds.union(place.geometry.viewport)
        } else {
          bounds.extend(place.geometry.location)
        }
      })
      this.map.fitBounds(bounds)
    })
  }

  clearSearchBar() {
    this.mapSdk.event.clearListeners(this.$searchBox, 'places_changed')
    this.mapSdk.event.clearListeners(this.map, 'bounds_changed')
    this.mapSdk.event.clearInstanceListeners(this.searchBar)
    this.searchBar.value = ''
  }
}
