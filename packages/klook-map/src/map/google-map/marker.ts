/**
 * Created by <PERSON><PERSON> on 2021/3/26 18:38.
 */

import { genSingle } from '../../common/utils'
import { formatLngLat } from '../../common/map-utils'

import { MapTypes, MarkerTypes } from '../../types/index'
import MarkerAbstract from '../abstract/marker-abstract'

// 单例 避免反复构造class
const getMarkerOverLayerCtor = genSingle((mapSdk: any) => {
  return class MarkerOverLayer extends mapSdk.OverlayView {
    constructor(lngLat: MapTypes.LngLat, opts: MarkerTypes.Options, map: any, markerResolve: () => void) {
      super()
      this.lngLat = lngLat
      this.opts = opts
      this.map = map
      this.markerResolve = markerResolve

      this.width = this.opts.element!.offsetWidth
      this.height = this.opts.element!.offsetHeight
    }

    onAdd() {
      const panes = this.getPanes()
      panes.floatPane.appendChild(this.opts.element)
    }

    update(newOptions: MarkerTypes.Options) {
      this.width = this.opts.element!.offsetWidth
      this.height = this.opts.element!.offsetHeight

      // 更新配置
      Object.assign(this.opts, newOptions)

      this.draw()
    }

    draw() {
      const overlayProjection = this.getProjection()
      const point = overlayProjection?.fromLatLngToDivPixel(new mapSdk.LatLng(this.lngLat))
      if (point) {
        this.opts.element.style.left = `${(point.x - this.width / 2) + this.opts.offset.x}px`

        let top = 0
        // 简单处理 center / bottom 两种方式
        if (this.opts.anchor === 'center') {
          top = (point.y - this.height / 2)
        } else if (this.opts.anchor === 'bottom') {
          top = (point.y - this.height)
        }

        this.opts.element.style.top = `${top + this.opts.offset.y}px`
      }

      if(this.opts.zIndex){
        this.opts.element.style.zIndex = this.opts.zIndex
      }

      // 首次绘制执行 marker resolve
      if (this.markerResolve) {
        this.markerResolve()
        this.markerResolve = null
      }
    }

    onRemove() {
      if (this.opts.element && this.opts.element.parentNode) {
        this.opts.element.parentNode.removeChild(this.opts.element)
      }
    }

    remove() {
      this.setMap(null)
    }

    toggleDOM() {
      if (this.getMap()) {
        this.setMap(null)
      } else {
        this.setMap(this.map)
      }
    }
  }
})

export default class GoogleMapMarker extends MarkerAbstract implements MarkerTypes.Tool {
  marker: any
  constructor(mapSdk: any, map: any, lngLat: MapTypes.LngLat, opts: MarkerTypes.Options) {
    super(mapSdk, map, lngLat, opts, MapTypes.Type.google)
  }

  // google marker 为异步创建
  // 这里需要处理回调逻辑
  private statusPromise: Promise<any> | undefined

  init(): void {
    let markerResolve
    this.statusPromise = new Promise((resolve) => {
      markerResolve = resolve
    })

    const Marker = getMarkerOverLayerCtor(this.mapSdk)
    this.marker = new Marker(this.lngLat, this.opts, this.map, markerResolve)

    this.marker.setMap(this.map)
  }

  // 添加marker ready
  onMarkerReady() {
    return this.statusPromise!
  }

  getLngLat(): MapTypes.LngLat {
    return this.lngLat
  }

  setzIndex(zIndex: number) {
    this.marker.update({zIndex})
  }

  update(newOptions: MarkerTypes.Options) {
    this.marker.update(newOptions)
  }

  setLngLat(lngLat: MapTypes.LngLat): void {
    this.marker.lngLat = formatLngLat(lngLat, MapTypes.Type.google)
    this.marker.draw()
  }

  isDraggable(): boolean {
    // 不支持 待查文档
    return false
  }

  remove(): void {
    this.marker.remove()
  }
}
