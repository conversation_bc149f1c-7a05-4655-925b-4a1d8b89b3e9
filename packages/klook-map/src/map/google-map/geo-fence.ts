import { MapTypes } from '../../types/index'
import GeoFence from '../abstract/geo-fence'
import { formatLngLat, getCenterLatLng } from '../../common/map-utils'

export default class GoogleGeoFence extends GeoFence {
  $geoFence: any
  selectedShape: any
  overlaysIndex: number = 0
  allOverlays: any = []

  constructor(
    mapSdk: any,
    map: any,
    fenceDeleteBtn: Element,
    geoFenceConf: MapTypes.GeoFenceConf,
    isWgs84: Boolean
  ) {
    super(mapSdk, map, MapTypes.Type.google, fenceDeleteBtn, geoFenceConf, isWgs84)
  }

  init(): void {
    // 当前组件只支持展示的围栏处理Wgs84，编辑没有处理Wgs84 ！！！
    if (this.geoFenceConf.canEdit) {
      this.canEdit()
    }
    if (this.geoFenceConf?.polygonList?.length) {
      this.initPolygon()
    }
  }

  canEdit() {
    this.initDelete()
    this.initGeoFence()
  }

  getGeoFenceData() {
    const pathData = {
      formatInitPath: [],
      formatNewPath: []
    } as any;

    (this.allOverlays || []).forEach((item: any) => {
      const { formatPath } = this.initPath(item.overlay)
      pathData.formatNewPath.push({
        path: formatPath,
        uuid: item.uuid
      })
    });

    (this.geoFenceConf?.polygonList || []).forEach((item: any) => {
      const { formatPath } = this.initPath(item.ploygon)
      pathData.formatInitPath.push({
        path: formatPath,
        uuid: item.uuid
      })
    })
    return pathData
  }

  clearGeoFence() {
    (this.allOverlays || []).forEach((item: any) => {
      item.marker && this.mapSdk.event.clearListeners(item.marker, 'click')
      item.ploygon && this.mapSdk.event.clearListeners(item.overlay, 'click')
    });

    (this.geoFenceConf?.polygonList || []).forEach((item: any) => {
      item.marker && this.mapSdk.event.clearListeners(item.marker, 'click')
      item.ploygon && this.mapSdk.event.clearListeners(item.ploygon, 'click')
    })
    this.$geoFence && this.mapSdk.event.clearListeners(this.$geoFence, 'overlaycomplete')
    this.selectedShape?.shape?.setEditable(false)
    this.$geoFence && this.$geoFence.setMap(null)
    this.$geoFence = null
  }

  initPath(shape: any) {
    const path = [] as any
    const formatPath = [] as any
    shape
      ?.getPath()
      ?.getArray()
      ?.forEach((i: any) => {
        path.push({ lat: i.lat(), lng: i.lng() })
        formatPath.push([i.lng(), i.lat()])
      })
    return {
      path,
      formatPath
    }
  }

  overlaycompleteEvent() {
    this.mapSdk.event.addListener(
      this.$geoFence,
      'overlaycomplete',
      (event: any) => {
        if (event.type !== this.mapSdk.drawing.OverlayType.MARKER) {
          this.$geoFence.setDrawingMode(null)
          const newShape = event.overlay
          newShape.type = event.type
          newShape.uuid = this.overlaysIndex
          this.overlaysIndex++
          const marker = this.setMarker(this.initPath(newShape).path)
          this.allOverlays.push({
            overlay: newShape,
            uuid: newShape.uuid,
            marker
          })

          this.mapSdk.event.addListener(newShape, 'click', () => {
            this.setSelection(newShape, marker, newShape.uuid, 'new')
          })
          this.setSelection(newShape, marker, newShape.uuid, 'new')
        }
      }
    )
  }

  initGeoFence() {
    if (this.$geoFence) { return false }
    this.$geoFence = new this.mapSdk.drawing.DrawingManager({
      drawingControl: true,
      drawingControlOptions: {
        position: this.mapSdk.ControlPosition.RIGHT_BOTTOM,
        drawingModes: [this.mapSdk.drawing.OverlayType.POLYGON]
      }
    })
    this.$geoFence.setMap(this.map)
    this.overlaycompleteEvent()
  }

  formatCoordinate(coords: any) {
    if (coords[0].lat) {
      return coords
    }
    const lngLat = [] as any
    coords.forEach((item: [number, number]) => {
      lngLat.push(formatLngLat(item, this.isWgs84 && MapTypes.Type.google))
    })
    return lngLat
  }

  setMarker(position: any) {
    if (!this.geoFenceConf.showMarker) { return null }
    const defaultUrl = 'https://maps.gstatic.com/mapfiles/api-3/images/spotlight-poi3_hdpi.png'
    const { src = defaultUrl, width = 41, height = 59 } = this.geoFenceConf.markerConfig || {}
    const icon = {
      url: src,
      scaledSize: new this.mapSdk.Size(width, height),
      origin: new this.mapSdk.Point(0, 0),
      anchor: new this.mapSdk.Point(25, 25)
    }
    const marker = new this.mapSdk.Marker({
      map: this.map,
      position: getCenterLatLng(position),
      icon
    })

    this.mapSdk.event.addListener(marker, 'click', () => {
      const position = marker.getPosition()
      const newCenter = {
        lat: position.lat(),
        lng: position.lng()
      }

      this.map.setCenter(newCenter)
      this.map.setZoom(this.geoFenceConf.clickZoom || 8)
      if (this.geoFenceConf.markerClickCb && typeof this.geoFenceConf.markerClickCb === 'function') {
        this.geoFenceConf.markerClickCb()
      }
    })
    return marker
  }

  updatePolygonStyle(polygonConfig: any) {
    const { strokeColor, fillColor, fillOpacity, lineWidth } = polygonConfig
    this.geoFenceConf?.polygonList?.forEach((i: { marker: any; ploygon: any; uuid: number }) => {
      if (i.ploygon) {
        i.ploygon.setOptions({
          strokeColor: strokeColor || '#FF0000',
          strokeOpacity: 0.8,
          strokeWeight: lineWidth || 2,
          fillColor: fillColor || '#FF0000',
          fillOpacity: fillOpacity || 0.35
        })
      }
    })
  }

  initPolygon() {
    const { strokeColor, fillColor, fillOpacity, lineWidth } = this.geoFenceConf?.polygonConfig
    this.geoFenceConf?.polygonList?.forEach(async (i: { marker: any; ploygon: any; uuid: number }, index: any) => {
      i.marker = this.setMarker(this.formatCoordinate(i))
      i.ploygon = await new this.mapSdk.Polygon({
        map: this.map,
        paths: this.formatCoordinate(i),
        strokeColor: strokeColor || '#FF0000',
        strokeOpacity: 0.8,
        strokeWeight: lineWidth || 2,
        fillColor: fillColor || '#FF0000',
        fillOpacity: fillOpacity || 0.35
      })

      i.uuid = index //  设置唯一ID

      this.mapSdk.event.addListener(i.ploygon, 'click', () => {
        this.setSelection(i.ploygon, i.marker, i.uuid, 'init')
      })
    })
  }

  initDelete() {
    if (this.geoFenceConf.canDelete) {
      if (this.fenceDeleteBtn) {
        this.map.controls[this.mapSdk.ControlPosition.BOTTOM_RIGHT].push(
          this.fenceDeleteBtn
        )
        this.fenceDeleteBtn.addEventListener('click', () => {
          this.deleteSelectedShape()
        })
      }
    }
  }

  clearSelection() {
    if (this.selectedShape) {
      this.selectedShape.shape.setEditable(false)
      this.selectedShape = null
    }
  }

  setSelection(shape: any, marker: any, uuid: number, type: string) {
    if (this.geoFenceConf.canEdit) {
      this.clearSelection()
      this.selectedShape = {
        shape,
        marker,
        type,
        uuid
      }
      shape.setEditable(true)
    }
  }

  deleteSelectedShape() {
    if (this.selectedShape) {
      this.selectedShape.shape.setMap(null)
      if (this.selectedShape.type === 'init') {
        this.deleteShape(this.geoFenceConf.polygonList)
      }
      if (this.selectedShape.type === 'new') {
        this.deleteShape(this.allOverlays)
      }
    }
  }

  deleteShape(list: any) {
    for (let i = 0; i < list.length; i++) {
      if (list[i].uuid === this.selectedShape.uuid) {
        list.splice(i, 1)
        this.selectedShape.marker && this.selectedShape.marker.setMap(null)
        break
      }
    }
  }
}
