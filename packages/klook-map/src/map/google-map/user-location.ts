import { MapTypes, UserLocationTypes, MarkerTypes } from '../../types/index'
import UserLocationAbstract from '../abstract/user-location-abstract'
import { formatLngLat } from '../../common/map-utils'
import Marker from './marker'

export default class GoogleUserControl extends UserLocationAbstract implements UserLocationTypes.Tool {
  $marker: MarkerTypes.Tool | undefined = undefined

  geoLocateFunc = () => {}

  constructor(mapSdk: any, map: any, element: Element, opts: UserLocationTypes.Options, fn: UserLocationTypes.Callback = {}, flyOption: UserLocationTypes.flyOption) {
    super(mapSdk, map, element, opts, fn, flyOption, MapTypes.Type.google)
  }

  init(): void {
    this.marker()

    this.geoLocateFunc = this.geoLocate.bind(this)

        this.element!.addEventListener('click', this.geoLocateFunc)
  }

  setCenter() {
    const lngLat = formatLngLat(this.lngLat as MapTypes.LngLat, MapTypes.Type.google)

    if (!this.flyOption || (Object.keys(this.flyOption).length === 1 && this.flyOption.zoom === this.map.getZoom())) {
      this.map.panTo(lngLat)
    } else {
      // 无动画,  要动画参考 https://developers.google.com/maps/documentation/javascript/examples/move-camera-ease?hl=en
      this.map.moveCamera({
        center: lngLat,
        ...this.flyOption
      })
    }
  }

  geoLocate() {
    this.getLocation().then(() => {
      this.marker()
      this.setCenter()
    })
  }

  marker() {
    if (this.lngLat) {
      const element = document.createElement('div')
      element.className = 'klkMap_user_location-marker'
      this.map.getDiv().appendChild(element)
      this.$marker = new Marker(this.mapSdk, this.map, this.lngLat, {
        element,
        anchor: 'center',
        offset: {
          x: 0,
          y: 0
        }
      })
    }
  }

  setLngLat(lngLat: MapTypes.LngLat) {
    this.lngLat = lngLat
    this.$marker && this.$marker?.setLngLat(this.lngLat)
  }

  remove() {
        this.element!.removeEventListener('click', this.geoLocateFunc)
        this.$marker && this.$marker?.remove()
  }

  getLocation() {
    return new Promise((resolve, reject) => {
      if (navigator.geolocation) {
        this.fn?.setLoading && this.fn.setLoading(true)
        navigator.geolocation.getCurrentPosition(
          (position) => {
            // 这里不转，在maker实例化过程中内部转，防止转化两次
            this.lngLat = {
              lat: position.coords.latitude,
              lng: position.coords.longitude
            }
            this.fn?.setLoading && this.fn.setLoading(false)
            this.fn?.success && this.fn.success(this.lngLat)
            resolve(this.lngLat)
          },
          () => {
            this.fn?.setLoading && this.fn.setLoading(false)
            this.fn?.error && this.fn.error()
            reject()
          },
          Object.assign({
            enableHighAccuracy: false,
            timeout: 3000,
            maximumAge: 60000
          }, this.opts.positionOptions)
        )
      } else {
        this.fn?.error && this.fn.error()
        this.fn?.setLoading && this.fn.setLoading(false)
        reject()
      }
    })
  }
}
