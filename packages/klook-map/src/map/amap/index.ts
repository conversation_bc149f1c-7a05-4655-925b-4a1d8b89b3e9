/**
 * Created by <PERSON><PERSON> on 2021/3/24 15:57.
 */

import MapAbstract from '../abstract/map-abstract'

import { loadScript, genSingle } from '../../common/utils'
import { formatLngLatArray, getMapRangeCoordinates, formatMapPadding } from '../../common/map-utils'
import { MapTypes, MarkerTypes, UserLocationTypes } from '../../types/index'
import SearchBar from './search-bar'
import AmapMarker from './marker'
import UserLocation from './user-location'
import GeoFence from './geo-fence'

const loadSdk = genSingle(async (mapConf: MapTypes.Conf) => {
  window._AMapSecurityConfig = {
    securityJsCode: mapConf.secret!
  }

  // 在jsapi地图脚本加载之前设置，否则无效

  window.forceWebGL = true

  await loadScript('https://webapi.amap.com/loader.js')

  return window.AMapLoader.load({
    key: mapConf.token!, // 申请好的Web端开发者 Key，调用 load 时必填
    version: '2.0' // 指定要加载的 JS API 的版本，缺省时默认为 1.4.15
  })
})

export default class Amap extends MapAbstract {
  public mapSdk: any
  public map: any
  GeoFence: any

  async initMap() {
    this.mapSdk = await loadSdk(this.mapConf as any)

    this.map = new this.mapSdk.Map(this.container, {
      // style: 'mapbox://styles/mapbox/streets-v11', // style URL
      // attributionControl: false // 右下角版权
      viewMode: '2D',

      rotateEnable: this.mapOpts.dragRotate,
      center: formatLngLatArray(this.mapOpts?.center!, MapTypes.Type.amap),
      zoom: this.mapOpts?.zoom,
      showOversea: true

    // 高德不支持最大最小缩放设置
      // minZoom: this.mapOpts?.minZoom,
      // maxZoom: this.mapOpts?.maxZoom
    })

    // todo: 高德地图不支持这个控件 fullscreenControl
    // https://lbs.amap.com/api/javascript-api-v2/guide/abc/plugins-list
    // if (this.mapOpts?.fullscreenControl) {
    //   this.map.addControl(new this.mapSdk.FullscreenControl());
    // }

    return this.map
  }

  mark(lngLat: MapTypes.LngLat, markerOptions: MarkerTypes.Options): MarkerTypes.Tool {
    const marker = new AmapMarker(this.mapSdk, this.map, lngLat, markerOptions)
    this.markers.push(marker)

    return marker
  }

  initGeoFence(fenceDeleteBtn: Element, geoFenceConf: MapTypes.GeoFenceConf, isWgs84: Boolean) {
    return new GeoFence(
      this.mapSdk,
      this.map,
      fenceDeleteBtn,
      geoFenceConf,
      isWgs84
    )
  }

  initSearchBar(searchBar: HTMLInputElement, searchBarContainer: HTMLElement) {
    return new SearchBar(this.mapSdk, this.map, searchBar, searchBarContainer)
  }

  setCenter(center: MapTypes.LngLat) {
    this.map.setCenter(formatLngLatArray(center, MapTypes.Type.amap))
  }

  setZoom(zoom: number) {
    this.map.setZoom(zoom)
  }

  setMinZoom(zoom: number) {
    console.log('amap minZoom not support', zoom)
  }

  setMaxZoom(zoom: number) {
    console.log('amap maxZoom not support', zoom)
  }

  flyTo(lngLat: MapTypes.LngLat) {
    this.panTo(lngLat)
  }

  panTo(lngLat: MapTypes.LngLat) {
    this.map.panTo(formatLngLatArray(lngLat, MapTypes.Type.amap))
  }

  setGeoLocate(element: Element, userLocationOptions: UserLocationTypes.Options, fn: UserLocationTypes.Callback, flyOption: UserLocationTypes.flyOption) {
    return new UserLocation(this.mapSdk, this.map, element, userLocationOptions, fn, flyOption)
  }

  /**
   * 高德地图没有直接的fitBounds方法
   */
  fitBounds(bound: MapTypes.LngLatBounds.LngLatBoundLike | MapTypes.LngLatBounds.LatLngBounds, options?: MapTypes.LngLatBounds.Options) {
    const padding = formatMapPadding(options?.padding || 0)
    this.map.setBounds(
      bound,
      false,
      [padding.top, padding.bottom, padding.left, padding.right]
    )
  }

  getBounds() {
    return this.map.getBounds()
  }

  getBoundsSwNe() {
    const bounds = this.map.getBounds()
    const sw = bounds.getSouthWest()
    const ne = bounds.getNorthEast()
    return {
      sw,
      ne,
      bounds
    }
  }

  // 如果是已经formatLngLat后（或者是turfBbox已经格式化生成的）的点生成的矩形区域或者是地图getBound生成的，不需要再次formatLngLat,  但是需要是数组形式的经纬度
  createBounds(sw: MapTypes.LngLat, ne: MapTypes.LngLat, options?: MapTypes.LngLatBounds.CreateOptions) {
    [sw, ne] = [sw, ne].map((item: MapTypes.LngLat) => {
      if (options?.formatLngLat === false) {
        if (Array.isArray(item)) {
          return item
        } else {
          const { lng, lat } = item as {
            lng: number,
            lat: number
          }
          return [lng, lat]
        }
      } else {
        return formatLngLatArray(item, MapTypes.Type.amap)
      }
    })
    return new this.mapSdk.Bounds(
      sw,
      ne
    )
  }

  isContains(latLng: MapTypes.LngLat, padding?: MapTypes.LngLatBounds.Padding): boolean {
    if (padding) {
      const { sw, ne } = this.getMapRangeCoordinates(padding)
      return this.createBounds(sw, ne, {
        formatLngLat: false
      }).contains(formatLngLatArray(latLng, MapTypes.Type.amap))
    }
    return this.getBounds().contains(formatLngLatArray(latLng, MapTypes.Type.amap))
  }

  resize() {
    return this.map.resize()
  }

  getContainer() {
    return this.map.getContainer()
  }

  bindBoundsChangeEvent(boundChangeListener: Function): Function {
    // amp的panto方法目前不触发任何事件, 用多个事件组合代替
    const events = ['zoomend', 'moveend', 'touchend', 'mousewheel', 'zoomchange']
    events.forEach(event => this.map.on(event, boundChangeListener))
    const removeListener = () => events.forEach(event => this.map.off(event, boundChangeListener))
    return removeListener
  }

  // 不支持
  isMovingOrZooming() {
    return false
  }

  // 去除padding的距离， 获取内部四个角的经纬度
  getMapRangeCoordinates(padding?: MapTypes.LngLatBounds.Padding, options?: MapTypes.LngLatBounds.MapRangeCoordinatesOption) {
    const { sw, ne } = this.getBoundsSwNe()
    const mapContainer = this.getContainer()
    return getMapRangeCoordinates(sw, ne, mapContainer.offsetWidth, mapContainer.offsetHeight, padding || 0, options)
  }
}
