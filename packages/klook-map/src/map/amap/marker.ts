/**
 * Created by <PERSON><PERSON> on 2021/3/26 18:38.
 */

import { MapTypes, MarkerTypes } from '../../types/index'
import MarkerAbstract from '../abstract/marker-abstract'

export default class AmapMarker extends MarkerAbstract implements MarkerTypes.Tool {
  marker: any

  constructor(mapSdk: any, map: any, lngLat: MapTypes.LngLat, opts: MarkerTypes.Options) {
    super(mapSdk, map, lngLat, opts, MapTypes.Type.amap)
  }

  formatOffset(offset: MarkerTypes.Options['offset']): any {
    if ((offset?.x || offset?.y)) {
      return [offset?.x, offset?.y]
    }

    return undefined
  }

  formatAnchor(anchor: MarkerTypes.Options['anchor']) {
    const anchorMapping = {
      top: 'top-center',
      bottom: 'bottom-center',
      center: 'center'
    }

    return anchorMapping[anchor || 'center'] as any
  }

  init(): void {
    const offset = this.formatOffset(this.opts.offset)

    const markerOptions = {
      // this.lngLat 已经做个坐标偏移
      position: this.lngLat,
      content: this.opts.element,
      draggable: this.opts.draggable,
      anchor: this.formatAnchor(this.opts.anchor),
      ...offset ? { offset } : undefined,
      ...this.opts.zIndex ? { zIndex: this.opts.zIndex } : undefined
    }

    this.marker = new this.mapSdk.Marker(markerOptions)

    this.map.add(this.marker)
  }

  // amap marker 是同步
  onMarkerReady() {
    return Promise.resolve()
  }

  getLngLat(): MapTypes.LngLat {
    return this.lngLat
  }

  setLngLat(lngLat: MapTypes.LngLat): void {
    this.setLngLatByType(lngLat, MapTypes.Type.amap)
    this.marker.setPosition(this.lngLat)
  }

  isDraggable(): boolean {
    return this.marker.getDraggable()
  }

  setzIndex(zindex: number) {
    this.marker.setzIndex(zindex)
  }

  remove(): void {
    this.map.remove(this.marker) // 清除 marker
  }

  update(newOptions: MarkerTypes.Options) {
    if (newOptions.offset) {
      this.marker._opts.offset = this.formatOffset(newOptions.offset)
      // this.marker.setOffset(this.formatOffset(newOptions.offset))
    }

    if (newOptions.anchor) {
      this.marker._opts.anchor = this.formatAnchor(newOptions.anchor)
      // this.marker.setAnchor(newOptions.anchor)
    }

    if (newOptions.zIndex) {
      this.marker._opts.zindex = newOptions.zIndex
    }

    // setContent 内部也会调用 updateOverlay
    this.marker.setContent(this.opts.element)

    // this.marker.updateOverlay()
  }
}
