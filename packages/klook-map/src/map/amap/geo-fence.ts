import GeoFence from '../abstract/geo-fence'
import { MapTypes } from '../../types'
import { formatGeoPath, getCenterLatLng } from '../../common/map-utils'
import { point1 } from '../../points'

export default class AmpGeoFence extends GeoFence {
  $polyEditor: any
  constructor(
    mapSdk: any,
    map: any,
    fenceDeleteBtn: Element,
    geoFenceConf: MapTypes.GeoFenceConf,
    isWgs84: Boolean
  ) {
    super(mapSdk, map, MapTypes.Type.google, fenceDeleteBtn, geoFenceConf, isWgs84)
  }

  init(): void {
    // 当前组件只支持展示的围栏处理Wgs84，编辑没有处理Wgs84 ！！！
    if (this.geoFenceConf?.polygonList?.length) {
      if (this.geoFenceConf.canEdit) {
        this.initEdit()
      } else {
        this.initGeoFence()
      }
    }
  }

  updatePolygonStyle(polygonConfig: any) {
    const { strokeColor, fillColor, fillOpacity, lineWidth } = polygonConfig
    this.geoFenceConf?.polygonList.forEach((item: any) => {
      if (item.ploygon) {
        item.ploygon.setOptions({
          strokeColor: strokeColor || '#FF0000',
          strokeOpacity: 0.8,
          strokeWeight: lineWidth || 2,
          fillColor: fillColor || '#FF0000',
          fillOpacity: fillOpacity || 0.35
        })
      }
    })
  }

  initGeoFence() {
    const { strokeColor, fillColor, fillOpacity, lineWidth } = this.geoFenceConf?.polygonConfig
    this.geoFenceConf?.polygonList.forEach((item) => {
      formatGeoPath(item, MapTypes.Type.amap)
      item.ploygon = new this.mapSdk.Polygon({
        path: item,
        strokeColor: strokeColor || '#FF0000',
        strokeOpacity: 0.8,
        strokeWeight: lineWidth || 2,
        fillColor: fillColor || '#FF0000',
        fillOpacity: fillOpacity || 0.35
      })

      if (this.geoFenceConf.showMarker) {
        item.marker = this.setMarker(item)
        this.map.add(item.marker)
      }
      this.map.add(item.ploygon)
    })
  }

  initEdit() {
    this.map.plugin(['AMap.PolygonEditor'], () => {
      this.$polyEditor = new this.mapSdk.PolygonEditor(this.map)
    })

    this.geoFenceConf?.polygonList.forEach((item) => {
      item.Polygon = new this.mapSdk.Polygon({
        path: item
      })
      item.Polygon.setExtData({
        initPolygon: true
      })
      this.$polyEditor.addAdsorbPolygons(item.Polygon)
      item.Polygon.on('dblclick', () => {
        this.$polyEditor.setTarget(item.Polygon)
        this.$polyEditor.open()
      })
      this.map.add(item.Polygon)
    })

    this.$polyEditor.on('add', (data: any) => {
      const polygon = data.target
      polygon.setExtData({
        initPolygon: false
      })
      this.$polyEditor.addAdsorbPolygons(polygon)
      polygon.on('dblclick', () => {
        this.$polyEditor.setTarget(polygon)
        this.$polyEditor.open()
      })
    })
  }

  createPolygon() {
    this.$polyEditor.close()
    this.$polyEditor.setTarget()
    this.$polyEditor.open()
  }

  deletePolygon() {
    const target = this.$polyEditor.getTarget()
    if (target) {
      this.$polyEditor.close()
      this.map.remove(target)
    }
  }

  formatPath(polygon: any) {
    const formatPath = [] as any
    polygon?.getPath()?.forEach((i: any) => {
      formatPath.push([i.lng, i.lat])
    })
    return formatPath
  }

  getGeoFenceData() {
    const geoFenceData = {
      formatInitPath: [] as any,
      formatNewPath: [] as any
    }
    const allOverlays = this.map.getAllOverlays('polygon')
    allOverlays.forEach((polygon: any) => {
      if (polygon.getExtData().initPolygon) {
        geoFenceData.formatInitPath.push({
          path: this.formatPath(polygon),
          uuid: polygon._amap_id
        })
      } else {
        geoFenceData.formatNewPath.push({
          path: this.formatPath(polygon),
          uuid: polygon._amap_id
        })
      }
    })
    return geoFenceData
  }

  setMarker(position: any) {
    const defaultUrl = point1
    const { src = defaultUrl, width = 41, height = 59 } = this.geoFenceConf.markerConfig || {}

    // 创建 AMap.Icon 实例：
    const icon = new this.mapSdk.Icon({
      size: new this.mapSdk.Size(width, height), // 图标尺寸
      image: src, // Icon 的图像
      imageSize: new this.mapSdk.Size(width, height)
    })

    const markerOptions = {
      clickable: true,
      position: getCenterLatLng(position, true),
      icon
    }

    const marker = new this.mapSdk.Marker(markerOptions)
    const clickHandler = () => {
      const lngLat = getCenterLatLng(position, true)
      this.map.panTo(lngLat)
      if (this.geoFenceConf.markerClickCb && typeof this.geoFenceConf.markerClickCb === 'function') {
        this.geoFenceConf.markerClickCb()
      }
    }

    marker.on('click', clickHandler)

    position.clickHandler = clickHandler

    return marker
  }

  clearGeoFence() {
    this.geoFenceConf?.polygonList.forEach((item) => {
      if (item.marker) {
        item.clickHandler && item.marker.off('click', item.clickHandler)
        this.map.remove(item.marker)
      }
    });
    (this.map.getAllOverlays('polygon') || []).forEach((polygon: any) => {
      this.map.remove(polygon)
    })
  }
}
