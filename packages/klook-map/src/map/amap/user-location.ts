import { MapTypes, UserLocationTypes } from '../../types/index'
import UserLocationAbstract from '../abstract/user-location-abstract'

export default class AMapUserControl extends UserLocationAbstract implements UserLocationTypes.Tool {
  control: any

  geoLocateFunc = () => {}

  constructor(mapSdk: any, map: any, element: Element, opts: UserLocationTypes.Options, fn: UserLocationTypes.Callback = {}, flyOption: UserLocationTypes.flyOption) {
    super(mapSdk, map, element, opts, fn, flyOption, MapTypes.Type.amap)
  }

  init() {
    this.geoLocateFunc = this.geoLocate.bind(this)
    this.element!.addEventListener('click', this.geoLocateFunc)
  }

  geoLocate(): void {
    // 高德地图无法外部主动trigger定位
    if (this.control) {
      this.map.removeControl(this.control)
    }
    this.fn?.setLoading && this.fn.setLoading(true)
    this.mapSdk.plugin('AMap.Geolocation', () => {
      this.control = new this.mapSdk.Geolocation({
        position: 'LB',
        timeout: 60000,
        enableHighAccuracy: false, // 是否使用高精度定位，默认：true
        maximumAge: 60000,
        zoomToAccuracy: true, //  定位成功后调整地图视野范围使定位位置及精度范围视野内可见，默认：false
        showButton: false, // 是否显示定位按钮，默认为false
        panToLocation: true, // 定位成功后是否自动移动到响应位置
        // fitBoundsOptions: {
        //   maxZoom: this.flyOption?.zoom || 15
        // },
        showMarker: typeof this.opts.trackUserLocation !== 'undefined' ? this.opts.trackUserLocation : true, // 是否显示定位点，默认为true
        ...this.opts.positionOptions
      })

      this.map.addControl(this.control)

      this.control.getCurrentPosition((status: 'complete' | 'error', result: any) => {
        this.fn?.setLoading && this.fn.setLoading(false)

        if (status === 'complete') {
          const { position } = result
          this.fn?.success && this.fn.success({
            lat: position.lat,
            lng: position.lng
          })
        } else {
          this.fn?.error && this.fn.error()
        }
      })
    })
  }

  remove() {
    this.element?.removeEventListener('click', this.geoLocateFunc)
    this.control && this.map?.removeControl(this.control)
  }

  setLngLat(lngLat: MapTypes.LngLat) {
    this.lngLat = lngLat
  }
}
