import SearchBar from '../abstract/search-bar'

export default class AmapSearchBar extends SearchBar {
  init(): void {
    this.mapSdk.plugin(['AMap.PlaceSearch', 'AMap.AutoComplete'], () => {
      const placeSearch = new this.mapSdk.PlaceSearch({
        map: this.map
      }) // 构造地点查询类

      const auto = new this.mapSdk.AutoComplete({
        input: this.searchBar
      })
      auto.on('select', (e: any) => {
        placeSearch.setCity(e.poi.adcode)
        placeSearch.search(e.poi.name) // 关键字查询查询
      })// 注册监听，当选中某条记录时会触发
    })
  }

  clearSearchBar() {
    // 高德不需要销毁
    // https://lbs.amap.com/api/javascript-api-v2/documentation#autocomplete
  }
}
