import { MapTypes } from "..";
import { formatLngLat } from "../common/map-utils";
const taipei = [121.56517, 25.037798] as [number, number];
const hk = [114.195053, 22.32673] as [number, number];
const gcj02Hk = [114.19995278539092,22.323932171330824] as [number, number];
const marco = [113.577669, 22.156838] as [number, number];
const gcj02Marco = [113.58269950846226,22.15385974844553]
const gcj02Marco2 = [113.55781821752487,22.145130053610465]
const marco2 = [113.552741,22.148069] as [number, number];
const shenzhen = [113.868164,22.571282] as [number, number];
const gcj02Shenzhen = [113.87307913223763,22.56825402571481] as [number, number];
const bd09Shenzhen = [113.87953590737088,22.574572645135483] as [number, number];
const seoul = [126.8360648, 37.5028818] as [number, number];
describe("KlookMap", () => {
  test("format not mainlandd lnglat", () => {
    expect(formatLngLat(taipei, MapTypes.Type.google).lat).toBeCloseTo(taipei[1],4)
    expect(formatLngLat(hk, MapTypes.Type.google).lat).toBeCloseTo(hk[1],4)
    expect(formatLngLat(marco, MapTypes.Type.google).lat).toBeCloseTo(marco[1],4)
    expect(formatLngLat(seoul, MapTypes.Type.google).lat).toBeCloseTo(seoul[1],4)
  });
  test("format  mainlandd lnglat", () => {
    expect(formatLngLat(shenzhen, MapTypes.Type.google).lat).toBeCloseTo(gcj02Shenzhen[1],4)
  });

  test("area using amap",()=>{
    expect(formatLngLat(shenzhen, MapTypes.Type.google).lat).toBeCloseTo(gcj02Shenzhen[1],4)
    expect(formatLngLat(hk,MapTypes.Type.amap).lat).toBeCloseTo(gcj02Hk[1],4)
    expect(formatLngLat(marco,MapTypes.Type.amap).lat).toBeCloseTo(gcj02Marco[1],4)
    expect(formatLngLat(marco2,MapTypes.Type.amap).lat).toBeCloseTo(gcj02Marco2[1],4)
  })


  test("area using baidu",()=>{
    expect(formatLngLat(shenzhen,MapTypes.Type.baidu).lat).toBeCloseTo(bd09Shenzhen[1],4)
  })
});
