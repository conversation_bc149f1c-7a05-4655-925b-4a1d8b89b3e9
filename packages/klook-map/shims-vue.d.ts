declare module '*.vue' {
  import Vue from 'vue'

  export default Vue
}

declare module '@klook/klook-ui/*' {
  // eslint-disable-next-line no-undef
  export default any
}

  interface Window {
    MapboxGeocoder: {
      new (options: {
        accessToken: string,
        mapboxgl: any
      }): any
    }
    _AMapSecurityConfig: {
      securityJsCode: string
    }
    forceWebGL: boolean
    MapboxDraw: any,
    AMapLoader: {
      load: (options: {
        key: string,
        version: string,
        plugins?: any[],
      }) => Promise<any>
    }
  }
