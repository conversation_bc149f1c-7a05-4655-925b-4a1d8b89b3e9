<template>
  <div :class="['klook-booking-info-wrap', `klook-booking-info-wrap-${platform}`]" :data-spm-module="`CmptBkgBasicInfo`">
    <div class="klook-booking-info-title">{{ __t('109040') }}</div>
    <div class="klook-booking-info">
      <div class="form-item" v-for="(booking, index) in bookingBasicInfo">
        <div class="label">{{ booking.title_text }}</div>
        <div class="info">
          <div v-if="booking.action_type == 3" class="content">{{ booking.content_text }}
            <template v-if="platform === 'desktop'">
              <klk-poptip :content="__t('109148')" placement="top" dark>
                <span @click="copy(booking.content_text)">
                  <IconCopy class="copy" size="20" fill="#212121"></IconCopy>
                </span>
              </klk-poptip>
            </template>
            <template v-else>
              <span @click="copy(booking.content_text)">
                <IconCopy class="copy" size="20" fill="#212121"></IconCopy>
              </span>
            </template>
          </div>

          <div v-else class="content">{{ booking.content_text }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import {
  Vue,
  Component,
  Prop,
  Provide
} from 'vue-property-decorator'
import KlkPoptip from '@klook/klook-ui/lib/poptip'
import { IconCopy } from '@klook/klook-icons';
import { genText } from "./locale/index";
import messages from "../locales/index.js";

@Component({
  name: "KlookBookingInfo",
  components: {
    KlkPoptip,
    IconCopy
  }
})
export default class KlookBookingInfo extends Vue {
  @Prop({
    default: 'mobile'
  })
  platform!: 'desktop' | 'mobile'

  @Prop({
    default: () => []
  })
  bookingBasicInfo!: Array<any>

  @Provide() __t: any = this.getTranslate()

  getTranslate() {
    return this.__t;
  }

  beforeCreate(this: any) {
    const locales = messages as any;
    const lang = this.$attrs.language || 'en';
    this.__t = locales[lang]
      ? genText(locales[lang])
      : genText(locales["en"]);
  }

  copy(text: string) {
    if (navigator.clipboard) {
      // clipboard api 复制
      navigator.clipboard.writeText(text);
    } else {
      var textarea = document.createElement('textarea');
      document.body.appendChild(textarea);
      // 隐藏此输入框
      textarea.style.position = 'fixed';
      textarea.style.clip = 'rect(0 0 0 0)';
      textarea.style.top = '10px';
      // 赋值
      textarea.value = text;
      // 选中
      textarea.select();
      // 复制
      document.execCommand('copy', true);
      // 移除输入框
      document.body.removeChild(textarea);
    }

    this?.$toast(this.__t('109149'));
  }
}
</script>

<style lang="scss">
@import '~@klook/klook-ui/dist/klook-ui.css';

.klook-booking-info-wrap {
  width: 100%;
  border-radius: 12px;
  background-color: #fff;
  // box-shadow: 0px 0px 12px 0px rgba(0, 0, 0, 0.03);
  padding: 16px;

  &-desktop {
    padding: 20px;
  }

  .klook-booking-info-title {
    color: #212121;
    margin-bottom: 8px;
    font-size: 20px;
    font-weight: 600;
  }

  .form-item {
    display: flex;
    justify-content: space-between;
    padding: 8px 0;

    .label {
      max-width: 50%;
      font-size: 16px;
      color: #757575;
    }

    .info {
      max-width: 50%;
      text-align: right;
    }

    .content {
      color: #212121;
      font-size: 16px;

      .copy {
        cursor: pointer;
        vertical-align: middle;
      }
    }
  }
}
</style>
