<template>
  <div
    v-if="visible"
    :data-spm-module="`${spm.module}${
      spm.module.indexOf('?') > -1 ? '&' : '?'
    }trg=manual`"
    class="app-download-mobile-topFixed"
  >
    <div
      v-if="showClose"
      :data-spm-module="spm.closeBtn"
      data-spm-virtual-item="__virtual"
      v-galileo-click-tracker="{ spm: spm.closeBtn, componentName: 'klk-app-download' }"
      @click="handleClose"
    >
      <div
        class="close-decorator"
      ></div>
      <icon-close
        theme="outline"
        size="10"
        fill="#ffffff"
        class="close-button"
      />
    </div>

    <div
      class="container"
      :style="{ 'background-color': bgColor }"
    >
      <div v-lazy:background-image="initData.bannerImg" class="banner"></div>

      <div class="wrap">
        <CollapseTransition
          :enter="(el) => changeMainTranslateY(el, 'expand')"
          :leave="(el) => changeMainTranslateY(el, 'collapse')"
        >
          <div>
            <div class="content">
              <p class="title">
                {{ title }}
              </p>
              <slot name="time-left">
                <div v-if="timeDesc" class="time">
                  <time-svg />
                  <span> {{ timeDesc }}</span>
                </div>
              </slot>
            </div>
          </div>
        </CollapseTransition>
      </div>
      <div class="button-area">
        <slot name="download-button">
          <klk-button
            v-if="downloadText"
            :class="['button']"
            size="mini"
            round
            :data-spm-module="spm.download"
            data-spm-virtual-item="__virtual"
            v-galileo-click-tracker="{ spm: spm.download, componentName: 'klk-app-download' }"
            @click="download"
          >
            {{ downloadText }}
          </klk-button>
        </slot>
      </div>
    </div>
    <div class="ota-shape ota-shape-one"></div>
    <div class="ota-shape ota-shape-two"></div>
  </div>
</template>

<script lang="ts">
import { Component, Prop, Watch } from "vue-property-decorator";

import CollapseTransition from "../common/collapse-transition";

import TimeSvg from "../svg/icon_time_s.svg";
import Base from "../common/base";

import IconClose from "@klook/klook-icons/lib/IconClose";

@Component({
  components: {
    CollapseTransition,
    TimeSvg,
    IconClose,
  },
})
export default class AppDownloadMobileTop extends Base {
  @Prop({ type: [String, Number], default: "48px" }) headerHeight!:
    | string
    | number; // header的高度
  @Prop({ type: Boolean, default: false }) autoTransform!: boolean; // 是否监听animate变化，animate修改的同时设置autoTransform为true（优化CLS）
  @Prop({ type: String, default: ".layout-default_main" })
  mainContainer!: string;

  @Prop({ type: String, default: "padding" }) animate!: "padding" | "transform";

  @Prop({ type: Boolean, default: true })
  bodyFixedNoScroll!: string;

  @Prop({ type: Number, default: 0 }) debounceTime!: number;

  data() {
    const observer: any = null;

    return {
      observer,
      visible: false,
      mainPaddingTop: [],
      main: [],
    };
  }

  created() {
    if (!(this as any).$isServer) {
      (this as any).visible = true;
    }
  }

  mounted() {
    (this as any).main = Array.from(
      document.querySelectorAll((this as any).mainContainer)
    );

    // 进入页面展示优化cls
    (this as any).changeMainTranslateY();
  }

  beforeDestroy() {
    (this as any).destroy();
  }

  cacheParams: any = {
    el: null,
    type: "",
  };

  @Watch("animate")
  animateWatch() {
    const { cacheParams, autoTransform } = this;
    if (autoTransform) {
      this.changeMainTranslateY(cacheParams.el, cacheParams.type);
    }
  }

  get defaultHeaderHeight() {
    let defHeight = this.headerHeight;
    if (typeof defHeight === "number") {
      defHeight = defHeight + "px";
    }
    return defHeight;
  }

  changeMainTranslateY(el: any, type: "expand" | "collapse") {
    this.cacheParams.el = el;
    this.cacheParams.type = type;
    (this as any).$nextTick(() => {
      const height = (this as any).$el.clientHeight;
      const offset = el ? el.scrollHeight : 0;

      const transform = type === "expand" ? height + offset : height - offset;

      (this as any).main.map((v: any, index: number) => {
        // 记录首次的padding
        !el &&
          ((this as any).mainPaddingTop[index] = Number(
            this.getStyle(v, "paddingTop").replace("px", "")
          ));
        if (this.autoTransform) {
          if (this.animate === "transform") {
            if (v.style.paddingTop) {
              v.style.paddingTop = this.defaultHeaderHeight; // 解决初始化设置paddingTop时，cls升高问题
            }
            v.style.transform = `translateY(${transform}px)`;
          } else {
            v.style.transform = "none";
            v.style.paddingTop = `${
              transform + +(this as any).mainPaddingTop[index]
            }px`;
          }
        } else {
          this.animate === "transform"
            ? (v.style.transform = `translateY(${transform}px)`)
            : (v.style.paddingTop = `${
                transform + +(this as any).mainPaddingTop[index]
              }px`);
        }
      });

      (this as any).$emit("translate", {
        transform,
        type: type || "expand",
      });
    });
  }

  getStyle(dom: any, attr: any) {
    return dom.currentStyle
      ? dom.currentStyle[attr]
      : getComputedStyle(dom, null)[attr];
  }

  handleClose() {
    this.$emit("close");

    setTimeout(() => {
      (this as any).visible = false;

      (this as any).destroy();
    }, 200);
  }

  destroy() {
    (this as any).main.map((v: any, index: number) => {
      // todo 在nuxtweb 接入时候测试
      v &&
        (this.animate === "transform"
          ? (v.style.transform = "none")
          : (v.style.paddingTop = (this as any).mainPaddingTop[index] + "px"));
    });

    (this as any).$emit("translate", {
      transform: 0,
      type: "destroy",
    });
  }

  debounce(callback: any, delay: number, immediate = true) {
    let timer: any;
    return () => {
      if (immediate && !timer) callback.apply(this);
      if (timer) clearTimeout(timer);
      timer = setTimeout(() => {
        callback.apply(this);
        timer = null;
      }, delay);
    };
  }

  download() {
    this.genOneLinkUrl(this.$el as HTMLElement, { ...this.customQuery }).then(
      (url: string) => {
        if (url) {
          window.location.href = url;
        }
      }
    );
  }
}
</script>

<style lang='scss' scoped>
@import "../style/_variables.scss";

.app-download-mobile-topFixed {
  width: 100%;
  min-height: 58px;
  display: block;
  position: relative;
  overflow: hidden;

  .close-button {
    position: absolute;
    top: 3px;
    left: 3px;
    z-index: 4;
    color: $color-white;
  }

  .close-decorator {
    position: absolute;
    top: -20px;
    left: -20px;
    border: 20px solid rgba(0, 0, 0, 0.4);
    transform: rotate(45deg);
  }

  .container {
    display: flex;
    padding: 10px 20px;
    width: 100%;
    height: 100%;
    background-repeat: no-repeat;
    background-position: right top;
    background-size: contain;
    align-items: center;
  }

  .banner {
    flex: none;
    padding: 8px 8px 0px 8px;
    background-position: right top;
    background-repeat: no-repeat;
    background-size: contain;
    flex: none;
    width: 48px;
    height: 48px;
    position: relative;
    z-index: 1;
  }

  .wrap {
    padding: 0 0 0 16px;
    max-width: calc(100% - 161px);
    position: relative;
    z-index: 1;
  }

  .title {
    height: auto;
    color: $color-text-primary-onDark;
    margin-top: 0;

    @include font-body-s-bold;
    @include text-ellipsis(2);
  }

  .time {
    display: flex;
    align-items: center;
    svg {
      color: $color-caution;
      width: 20px;
      height: 20px;
      margin-right: 4px;
    }
    span {
      color: $color-caution;
      @include font-body-m-bold;
    }
  }

  .button {
    @include font-body-s-semibold;
  }

  .button-area {
    align-self: center;
    margin-left: auto;
  }

  .ota-shape {
    position: absolute;
    background-repeat: no-repeat;
    background-size: contain;
  }

  .ota-shape-one {
    background-image: url("https://res.klook.com/image/upload/Vector_iv2ntc.png");
    width: 55px;
    height: 13px;
    top: 0;
    right: 0;
  }
  .ota-shape-two {
    background-image: url("https://res.klook.com/image/upload/Vector_1_ushtrz.png");
    width: 91px;
    height: 13px;
    bottom: 0;
    right: 54px;
  }

  div[lazy="error"] {
    background-size: auto !important;
  }
}
</style>

<style lang='scss'>
.collapse-transition {
  transition: 0.2s height ease-in-out, 0.2s padding-top ease-in-out,
    0.2s padding-bottom ease-in-out;
}
</style>
