<template>
  <div
    v-if="otaData && otaData.type"
    class="ota"
    :class="['app-download', otaData.position]"
  >
    <AppDownloadMobileTopStorage
      v-if="isTopFixed"
      v-bind="otaData"
      class="download"
      v-on="$listeners"
    >
      <div v-if="otaData.downButtonImage" slot="download-button">
        <img :src="otaData.downButtonImage" alt="" />
      </div>
    </AppDownloadMobileTopStorage>

    <AppDownloadStorage
      v-if="!isTopFixed && !isFoldableFloat && otaData.showClose"
      v-bind="otaData"
      class="download"
      :style="otaStyle"
      v-on="$listeners"
    >
      <div v-if="otaData.downButtonImage" slot="download-button">
        <img :src="otaData.downButtonImage" alt="" />
      </div>
    </AppDownloadStorage>
    <AppDownload
      v-if="!isTopFixed && !isFoldableFloat && !otaData.showClose"
      v-bind="otaData"
      class="download"
      :style="otaStyle"
      v-on="$listeners"
    >
      <div v-if="otaData.downButtonImage" slot="download-button">
        <img :src="otaData.downButtonImage" alt="" />
      </div>
    </AppDownload>
    <FoldableFloat
      v-if="!isTopFixed && isFoldableFloat"
      v-bind="otaData"
      class="download"
      :style="otaStyle"
      v-on="$listeners"
    >
    </FoldableFloat>
  </div>
</template>

<script lang="ts">
import { Vue, Component, Prop } from "vue-property-decorator";
import l from "@klook/klk-traveller-utils/lib/localStorage";
import { OtaInfoOption } from "../../types/types";

@Component({
  components: {
    AppDownloadMobileTopStorage: () => import("../mobile/top-storage.vue"),
    AppDownloadStorage: () => import("./download-storage.vue"),
    AppDownload: () => import("./download.vue"),
    FoldableFloat: () => import("../desktop/foldable-float.vue"),
  },
})
export default class ApiDownload extends Vue {
  mo!: MutationObserver;

  @Prop() location!: string;
  @Prop() language!: string;
  @Prop() type!: string; // 关闭后依旧展示
  @Prop() infoData!: OtaInfoOption; // 外部传进来的数据

  otaStyle = {};
  _axios!: any;

  get isTopFixed() {
    return (
      this.otaData.type === "topFixed" && this.otaData.position === "topFixed"
    );
  }

  get isFoldableFloat() {
    return this.otaData.type === "foldableFloat";
  }

  infoOtaData: OtaInfoOption = {};

  get realLanguage() {
    return (
      this.language ||
      (window as any)?.__KLOOK__?.state?.klook?.language ||
      ""
    );
  }

  get downLoadData() {
    return this.infoData || this.infoOtaData || {};
  }

  get spm() {
    const tracking = this.downLoadData.tracking || {};
    const spm = {} as any;
    const spmName = {
      download: "download",
      close: "closeBtn",
      module: "module",
      float_icon: "floatIcon",
    } as any;
    for (const item in tracking) {
      const i = tracking[item];
      if (i) {
        spm[spmName[item]] = i.spm;
        if (i.extra) {
          spm[spmName[item]] = `${i.spm}?ext=${encodeURIComponent(
            JSON.stringify(i.extra)
          )}`;
        }
      }
    }
    return spm;
  }

  get otaData() {
    const { style, bg, banner, onelink, time, download_button, ota_closed, icon_config } =
      this.downLoadData;
    return {
      platform: style?.position === "topFixed" ? "mobile" : "desktop",
      animate: "transform",
      mainContainer: ".layout-default_main",
      language: this.realLanguage,
      bgImg: bg?.img,
      bgColor: bg?.color,
      bannerImg: banner?.img,
      qrCodeTip: onelink?.qr_desc, // 二维码上的提示文案
      timeLeft: time?.has_countdown ? time?.to_time - +new Date() / 1000 : 0, // 倒计时，剩余时间，单位秒
      title: banner?.title,
      desc: banner?.desc,
      oneLinkUrl: onelink?.link,
      downButtonImage: download_button?.img, // 下载按钮图片
      downloadText: download_button?.desc,
      spm: this.spm,

      showClose: style?.show_close, // 是否展示close
      position: style?.position, // web：header、rightBottomFixed、staticBanner、bottomFixed，mweb:small、topFixed
      ...this.$attrs,
      type: style?.scale, // small、medium、large、bottomFixed、topFixed,
      klkLocalDownloadBarTime: ota_closed ? ota_closed * 1000 : 7 * 24 * 60 * 60 * 1000,

      floatTitle: icon_config?.title || '',
      floatDesc: icon_config?.desc || '',
    };
  }

  get position() {
    return this.downLoadData?.style?.position;
  }

  translate(data: any) {
    this.$emit("translate", data);
  }

  beforeMount() {
    this._axios = this.$attrs.axios || (window as any)?.$axios;
  }

  getOTAData() {
    if (this.infoData || !this.location) {
      return false;
    }
    this._axios &&
      this._axios
        .$get(`/v1/usrcsrv/ota/config?location=${this.location}`)
        .then((res: any) => {
          if (res.success && res.result) {
            this.infoOtaData = res.result || {};
            this.$nextTick(() => {
              if (
                this.position === "rightBottomFixed" ||
                this.position === "bottomFixed"
              ) {
                this.mutationDOM();
              }
            });
          } else {
            this.$emit('update-visibility', 'hidden');
          }
        }).catch(() => {
          this.$emit('update-visibility', 'hidden');
        });
  }

  mutationDOM() {
    new MutationObserver(() => {
      const cookieDOM = document.querySelector(
        "#cookie-agreement"
      ) as HTMLElement;
      const rightBottomFixed = document.querySelector(
        ".rightBottomFixed"
      ) as HTMLElement;
      const bottomFixed = document.querySelector(".bottomFixed") as HTMLElement;
      const height = (cookieDOM && cookieDOM.offsetHeight) || 0;
      if (cookieDOM) {
        if (rightBottomFixed || bottomFixed) {
          this.otaStyle = {
            "margin-bottom": height + "px",
          };
        }
      }

      if (!cookieDOM) {
        if (rightBottomFixed || bottomFixed) {
          this.otaStyle = {
            "margin-bottom": 0,
          };
        }
      }
    }).observe(document, {
      subtree: true,
      childList: true,
    });
  }

  mounted() {
    if (l.getItem("downloadBar") === "close" && this.type !== "static") {
      this.$emit('update-visibility', 'hidden');
      return;
    }
    this.getOTAData();
  }
}
</script>

<style lang="scss" scoped>
.app-download {
  &.rightBottomFixed {
    position: fixed;
    right: 32px;
    bottom: 32px;
    z-index: 600;
  }

  &.bottomFixed {
    position: fixed;
    z-index: 100;
    left: 0;
    bottom: 0;
  }
}
</style>
