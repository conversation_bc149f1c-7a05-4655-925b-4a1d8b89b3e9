<script lang="tsx">
import {Component, Prop, Watch} from "vue-property-decorator";
import render from "./render";

@Component({})
export default class AppDownload extends render {
  @Prop({ type: Number, default: 0 }) debounceTime!: number;

  mounted() {
    this.adjustUi();
    this.genQrCode()

    this.$watch('customQuery',
    ()=>{
      this.genQrCode()
    },{
      deep: true
    })
  }

  adjustUi() {
    if(this.type === 'bottomFixed') {
      const bannerEl = this.$el.querySelector('.ota-banner') as HTMLElement
      const containerEl = this.$el.querySelector('.ota-container') as HTMLElement
      containerEl.style['minHeight'] = `${ 0.8 * (bannerEl.offsetHeight)}px`
    }
  }

  async genQrCode(){
    this.isLoading = true
    const url = await this.genOneLinkUrl(this.$el as HTMLElement,
    {
        needConcatKepler: false,
        needConcatLink: !!this.customQuery.oneLinkRedirectUrl,
        ...this.customQuery
    })
    if(url){
      await this.handleGenQrCode(url).finally(()=>{ this.isLoading = false });
    }else {
      this.isLoading = false
    }
  }

  handleGenQrCode(qrcodeLink: string) {
    return new Promise<void>((resolve,reject)=>{
      if (this.qrCodeImg || !this.$el) {
        return;
      }
      const canvas = this.$el.querySelector?.(".app-download-canvas");
      if (!canvas) return;

      // @ts-ignore
      const promiseSource = import('qrcode/lib/browser.js')
      // 异步加载QRCode
      promiseSource.then((QRCode) => {
        QRCode.toCanvas(
          canvas,
          qrcodeLink,
          {
            margin: 0,
            errorCorrectionLevel: this.qrCodeQ,
          },
          function (error: any) {
            if (error) {
              console.error(error)
              reject(error)
            }else {
              resolve()
            }
          }
        );
      });
    })
  }
}
</script>

<style lang="scss" scoped>
@import "../style/_variables.scss";

.select-country-code {
  ::v-deep .klk-select-suffix {
    display: none !important;
  }
  ::v-deep .klk-poptip-popper {
    position: relative !important;
    transform: none !important;
    display: block !important;
    box-shadow: none !important;
    opacity: 1 !important;
  }

  .selected-icon {
    float: right;
  }
}

.app-download-desktop {
  position: relative;
  .ota-container {
    display: flex;
    width: 100%;
    height: 100%;
    background-repeat: no-repeat;
    background-position: right top;
    background-size: contain;
  }

  .ota-banner {
    padding: 8px 8px 0px 8px;
    // background-color: $color-neutral-100;
    border-radius: 24px 24px 0px 0px;
    background-position: right top;
    background-repeat: no-repeat;
    background-size: contain;
    flex: none;
    display: flex;
    align-items: center;
    justify-content: center;
    // border-bottom: 1px solid $color-neutral-200;
    position: relative;
    z-index: 1;
  }

  .send-info {
    @include font-body-s-bold;
    color: $color-common-white;
    .tip {
      @include font-body-m-bold;

      @include text-ellipsis(3);
    }
    .error-tips{
      @include font-body-s-regular();
      display: flex;
      align-items: center;
      margin: -6px 0 12px 0;

      span {
        @include text-ellipsis(3);
      }
      svg{
        margin-right: 8px;
        flex: 0 0 16px;
      }
    }
    .send-change {
      text-decoration: underline;
      cursor: pointer;
      display: inline-block;
    }

    .ota-button-send {
      display: flex;
      white-space: nowrap;
      justify-content: center;
      border-radius: $radius-l;
    }
    .ota-data {
      margin: 12px 0;
      display: flex;
      select {
        background: none;
        outline: none;
        border: none;
        font-weight: normal;
      }
      ::v-deep.klk-input-inner {
        border: none;
        input {
          font-weight: normal;
        }
      }

      .data-info {
        width: 210px;
        background: $color-bg-widget-normal;
        display: flex;
        margin-right: 4px;
        border-radius: $radius-l;
        padding-left: 12px;
        overflow: hidden;
        .select-code {
          @include font-body-m-regular;
          color:$color-text-primary;
          white-space:nowrap;
          display: flex;
          justify-content: center;
          align-items: center;
          cursor: pointer;
        }
      }
    }

    .ota-data-tip {
      @include font-body-s-regular;

      margin: 8px 0 18px 0;

      ::v-deep span {
        font-weight: $fontWeight-bold;
      }
    }
  }
  .ota-or {
    @include font-caption-m-regular;
    margin: 0 16px;
    color: $color-common-white;
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    height: 100%;

    span {
      display: block;
      width: 100%;
      white-space: nowrap;
    }

    &::after,
    &::before {
      content: "";
      display: block;
      width: 1px;
      height: calc(50% - 16px);
      background-color: $color-bg-widget-normal;
    }
    &::after {
      margin-top: auto;
    }
  }
  .download_info_con {
    display: flex;
  }
  .download_info {
    display: flex;
    align-items: center;

    .app-download-area1 {
      @include font-caption-m-regular;

      width: 100px;
      flex:1;
      color: $color-common-white;

      .code-logo {
        position: absolute;
        transform: translate(-50%, -50%);
        top: 50%;
        left: 50%;
        background-image: url("https://res.klook.com/image/upload/klook_laf0fq.png");
        background-size: cover;
      }

      .app-download-qrcode {
        width: 100px;
        height: 100px;
        padding: 8px;
        margin-top: 8px;
        background: $color-bg-widget-normal;
        border-radius: $radius-xl;
        position: relative;

        .code-logo {
          width: 20px;
          height: 20px;
        }

        canvas {
          width: 100% !important;
          height: 100% !important;
        }
      }
    }
  }

  .wrap {
    position: relative;
    z-index: 1;
  }

  .desc {
    color: $color-white;

    & > p {
      display: flex;
      margin: 0;
      align-items: flex-start;

      img {
        width: 16px;
        height: 16px;
        margin-right: 8px;
        margin-top: 3px;
        background-color: transparent;
      }

      span {
        @include font-body-m-regular
      }

      margin-bottom: 12px;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  .app-download-are-tip {
    @include text-ellipsis(3);
  }

  .time {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    svg {
      color: $color-caution;
      margin-right: 4px;
    }
    span {
      color: $color-caution;
      @include font-heading-s;
    }
  }

  .close {
    color: $color-white;
    width: 24px;
    height: 24px;
    right: 21px;
    top: 21px;
    position: absolute;
    cursor: pointer;
    z-index: 9;
  }

  .button {
    margin-top: 16px;
  }

  // qrcode style
  .app-download-area {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 12px 8px;
    border-radius: 20px 20px 0px 0px;
    width: 100%;
    height: 100%;
    background: $color-white;
    .app-download-are-tip {
      @include font-body-s-regular;
      color: $color-text-primary;
      margin-bottom: 12px;
      text-align: center;
    }
    .app-download-qrcode {
      position: relative;
      display: flex;
      justify-content: center;
      margin-left: auto;
      margin-right: auto;
      .code-logo {
        position: absolute;
        transform: translate(-50%, -50%);
        top: 50%;
        left: 50%;
        background-image: url("https://res.klook.com/image/upload/klook_laf0fq.png");
        background-size: cover;
      }
    }

    .app-download_market {
      display: flex;
      justify-content: center;
      margin-top: 12px;
      svg {
        width: 16px;
        height: 20px;
        &:last-child {
          margin-left: 12px;
          width: 24px;
        }
      }
    }
  }

  .shape1 {
    position: absolute;
    left: -30px;
    top: 24px;
    color: #1FDFDF;
    width: 85px;
    height: 50px;
    transition: transform 240ms cubic-bezier(0.22, 0, 0.08, 1) 320ms;
  }

  .shape2 {
    position: absolute;
    right: 20px;
    top: -90px;
    width: 119px;
    height: 125px;
    color: #FF5B00;
    transition: transform 240ms cubic-bezier(0.22, 0, 0.08, 1);
  }


  .shape3 {
    position: absolute;
    color: #FFC102;
    bottom: -295px;
    right: 96px;
    width: 251px;
    height: 319px;
    transition: transform 240ms cubic-bezier(0.22, 0, 0.08, 1) 160ms;
  }

  &:hover {
    .shape1 {
      transform: translate(-48px, 24px);
    }
    .shape2 {
      transform: translate(28px, -30px);
    }
    .shape3 {
      transform: translate(16px, 14px);
    }
  }
}

.app-download-mobile {
  position: relative;
  border: 1px solid $color-border-normal;
  border-radius: $radius-xl;

  .ota-container {
    background-repeat: no-repeat;
    background-position: right top;
    background-size: contain;
  }

  .ota-banner {
    padding: 8px 8px 0px 8px;
    background-position: right top;
    background-repeat: no-repeat;
    background-size: contain;
    flex: none;
    width: 48px;
    height: 48px;
    position: relative;
    z-index: 1;
  }

  .wrap {
    position: relative;
    z-index: 1;
  }

  .desc {
    padding: 0 16px 24px 0;
    color: $color-white;

    & > p {
      display: flex;
      margin: 0;
      align-items: flex-start;

      img {
        width: 16px;
        height: 16px;
        margin-top: 2px;
        margin-right: 8px;
        background-color: transparent;
      }

      margin-bottom: 8px;

      span {
        @include font-body-s-regular;
      }

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  .time {
    display: flex;
    align-items: center;
    margin-bottom: 2px;
    svg {
      color: $color-caution;
      width: 16px;
      height: 15px;
      margin-right: 4px;
    }
    span {
      color: $color-caution;
      @include font-body-s-bold;
    }
  }

  .shape1 {
    display: none;
    position: absolute;
    left: -30px;
    top: 24px;
    color: #1FDFDF;
    transition: transform 240ms cubic-bezier(0.22, 0, 0.08, 1) 320ms;
  }

  .shape2 {
    display: none;
    position: absolute;
    right: 20px;
    top: -77px;
    color: #FF5B00;
    transition: transform 240ms cubic-bezier(0.22, 0, 0.08, 1);
  }


  .shape3 {
    display: none;
    position: absolute;
    color: #FFC102;
    bottom: -293px;
    right: 96px;
    transition: transform 240ms cubic-bezier(0.22, 0, 0.08, 1) 160ms;
  }

 .close {
    color: $color-white;
    width: 24px;
    height: 24px;
    right: 16px;
    top: 16px;
    position: absolute;
    cursor: pointer;
    z-index: 9;
  }
}

.medium-data {
    position: relative;
    z-index: 2;
    // padding: 32px 32px 0 32px;
  }

.app-download-mobile-medium {
  border-radius: $radius-s;
  overflow: hidden;

  .medium-data {
    padding: 32px 32px 0 32px;
  }
  &.with-close {
    .ota-container {
      padding-right: 64px;
    }
  }

  .ota-container {
    padding: 24px 31px 24px 24px;
    background-repeat: no-repeat;
    background-position: right top;
  }

  .title {
    margin: 0;
    color: $color-white;
    @include font-heading-s;
    @include text-ellipsis(2);
  }

  .time {
    margin-bottom: 16px;
    svg {
      width: 30px;
      height: 30px;
      margin-right: 4px;
    }
    span {
      @include font-heading-s;
    }
  }

  .desc {
    margin-top: 16px;
    max-width:70%;
  }

 .close {
    width: 24px;
    height: 24px;
    right: 16px;
    top: 16px;
  }
}

.app-download-mobile-small {
  border-radius: unset;
  .ota-container {
    padding: 10px 20px 10px 8px;
    background-repeat: no-repeat;
    background-position: right top;
    display: flex;
    align-items: center;
  }

  .wrap {
    margin-left: 8px;
    // display: flex;
    // align-items: center;
    // flex: 1;
    // justify-content: space-between;
    & > div:first-child {
      margin-right: 8px;
    }
  }

  .title {
    margin: 0;
    color: $color-white;
    @include font-caption-m-semibold;
    @include text-ellipsis(2);
  }
  .desc {
    margin-top: 2px;
    padding: 0;
    & > p {
      opacity: 0.7;
      @include font-caption-m-regular;
      @include text-ellipsis(2);
    }
  }
  .button {
    flex-shrink: 0;
    width: auto;
  }

  .close {
    width: 16px;
    height: 16px;
    right: 6px;
    top: 6px;
  }
}

.app-download-desktop-large {
  width: 100%;
  border-radius: $radius-xl;
  overflow: hidden;
  position: relative;

  .ota-container{
    min-height: 400px;
  }

  .download_info {
    margin-top: 34px;
  }

  .ota-banner {
    margin: 40px 0 0 40px;
    width: 204px;
    height: 100%;
    position: absolute;
  }

  .wrap {
    max-width: 40%;
    padding: 24px 0px 24px 24px;
    margin-left:240px;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }
  .send-info .ota-data .data-info {
    width: 230px;
  }

  .title {
    margin: 0 0 16px 0;
    color: $color-white;
    @include font-heading-l;
    @include text-ellipsis(3);
  }

  .desc {
    & > p {
      margin-bottom: 12px;
    }
  }

  .button {
    margin-top: 16px;
  }

  .app-download-area {
    .app-download-qrcode {
      position: relative;
      width: 120px;
      height: 120px;
      canvas {
        width: 100% !important;
        height: 100% !important;
      }
      .code-logo {
        width: 33px;
        height: 33px;
      }
    }
  }
}

.app-download-desktop-medium {
  width: 600px;
  min-height: 280px;
  border-radius: $radius-xl;
  overflow: hidden;
  border: 1px solid $color-border-normal;
  position: relative;
  .medium-data {
    padding: 32px 32px 0 32px;
  }

  &.with-close {
    .wrap {
      padding-right: 64px;
    }
  }

  .title-desc .title {
    @include font-heading-l;
  }
  .ota-banner {
    margin: 32px 0 0 16px;
    padding-right: 0;
    width: 180px;
  }

  .wrap {
    padding: 40px 0 24px 0;
    display: flex;
  }

  .send-info{
    order: 2;
  }
  // .ota-or{
  //   margin-left:0;
  //   margin-right:24px;
  //   &::before {
  //     display: none;
  //   }
  //   &::after {
  //     height: calc(100% - 28px);
  //   }
  // }

  .title {
    margin: 0 0 16px 0;
    color: $color-white;
    @include font-heading-s;
    @include text-ellipsis(3);
  }

  .desc {
    & > p {
      margin-bottom: 12px;
    }
  }

  .button {
    margin-top: 16px;
  }

  .app-download-area {
    .app-download-qrcode {
      position: relative;
      width: 120px;
      height: 120px;
      canvas {
        width: 100% !important;
        height: 100% !important;
      }
      .code-logo {
        width: 33px;
        height: 33px;
      }
    }
  }
}

.app-download-desktop-bottomFixed {
  width: 100%;
  position: fixed;
  width: 100%;
  min-height: 80px;
  z-index: 99;
  bottom: 0;
  left: 0;

  &.with-tip {
    .ota-banner {
      width: 180px;
      height: 130%;
    }
    .wrap {
      padding-left: 204px;
      flex-direction: initial;
      align-items: center;
      .title-desc {
        margin-right: 40px;
      }
      .download_info {
        padding-right: 24px;
      }
    }
  }

  .send-info .ota-data .data-info {
    width: 244px;
  }
  .ota-container {
    width: 1160px;
    margin-left: auto;
    margin-right: auto;
    position: relative;
    background-size: cover;
  }

  .ota-banner {
    margin: 0;
    width: 160px;
    background-position: right top;
    background-repeat: no-repeat;
    background-size: 100%;
    flex: none;
    position: absolute;
    left: 0;
    bottom: 0;
  }

  .wrap {
    padding: 24px 24px 24px 184px;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }

  .title {
    margin: 0 0 6px 0;
    color: $color-white;
    @include font-heading-s;
    @include text-ellipsis(2);
  }

  .desc {
    & > p {
      margin-bottom: 6px;
      @include text-ellipsis(4);
    }
  }

  .close {
    color: $color-white;
    right: 16px;
    top: 16px;
  }

  .app-download-area {
    display: flex;
    align-items: center;
    padding: 16px 8px;
    .app-download-qrcode {
      position: relative;
      width: 120px;
      height: 120px;
      background-color: $color-white;
      border-radius: $radius-l;
      canvas {
        width: 100% !important;
        height: 100% !important;
      }
      .code-logo {
        width: 33px;
        height: 33px;
      }
    }

    .app-download-are-tip {
      margin-bottom: 8px;
    }
    .app-download_market {
      margin-top: 8px;
    }
  }

  .shape {
    position: absolute;
    width: 100%;
    height: 100%;
    overflow: hidden;
  }
}

.app-download-desktop-onlySend {
  padding: 20px 22px;
  border-radius: $radius-xxl;
  border: 1px solid $color-border-normal;

  .download_info {
    width: 100%;
    .app-download-area1 {
      width: 136px;
      .app-download-qrcode {
        width: 136px;
        height: 136px;
      }
    }
  }

  .send-info {
    flex: auto;
    width: 100%;
    .ota-data {
      .data-info {
        width: 276px;
      }
    }
  }

  .ota-or {
    margin: 0 20px;
  }
}
.app-download-poptip {
  position: fixed;
  bottom: 120px;
  right: 40px;
  z-index: 400;
  .float-icon {
    width: 50px;
    height: 50px;
    background-size: cover;
    cursor: pointer;
  }

  ::v-deep .klk-poptip-popper-inner {
    margin: 0;
    padding: 0;

    input{
      font-weight: normal;
    }
  }
  .klk-poptip-popper {
    border-radius: $radius-xl;
  }
}

.app-download-desktop-float {
  @extend .app-download-desktop-medium;

  .medium-data {
    padding: 32px 32px 0 32px;
  }

  .ota-banner {
    margin: 0 0 0 16px;
  }
}

div[lazy="error"] {
  background-size: auto !important;
}

</style>
