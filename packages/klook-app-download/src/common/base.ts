import { Vue, Component, Prop, Watch, Provide } from 'vue-property-decorator';
import { Description } from '../../types/index'
import { genText } from '../locale/index.js';
import messages from '../../locales/index.js';
import genOneLinkUrl from '@klook/klk-traveller-utils/lib/onelinkUrl'

@Component
export default class Base extends Vue {
   @Prop({ type: String }) platform!: 'mobile' | 'desktop'
   @Prop({ type: String }) bgImg!: string
   @Prop({ type: String, default: '#4D40CC' }) bgColor!: string
   @Prop({ type: String }) bannerImg!: string
   @Prop({ type: String }) title!: string
   @Prop({ type: Array, default: () => [] }) desc!: string[] | string | Description[]
   @Prop({ type: String }) downloadText!: string
   @Prop({ type: String }) link!: string
   @Prop({ type: Number, default: 0 }) timeLeft!: number
   @Prop({ type: Boolean, default: true }) showClose!: boolean
   @Prop({ type: String, default: 'topFixed' }) type!: 'topFixed' | 'bottomFixed' | 'float' | 'small' | 'medium' | 'large' | 'onlySend'
   @Prop({ type: String, default: 'en' }) language!: string
   @Prop({ type: String, default: '' }) oneLinkSpm!: string;
   @Prop({ type: String, default: '' }) pageOpenID!: string;
   @Prop({ type: String, default: '' }) oneLinkUrl!: string;
   @Prop({ type: String, default: '' }) oneLinkRedirectUrl!: string;
   @Prop({ type: String, default: '' }) oneLinkKeplerID!: string;
   @Prop({
      type: Object, default: () => ({
         download: 'DownloadAPPBanner_Button',
         closeBtn: 'DownloadAPPBanner_Close',
         module: 'DownloadAPP',
         floatIcon: 'DownloadAPP_Button',
      })
   }) spm!: {
      download?: string
      closeBtn?: string
      module?: string,
      floatIcon?: string
   }

   isLoading: boolean = true

   seconds: number = this.timeLeft

   _tid: any

   genOneLinkUrl:any = genOneLinkUrl

   __t: any

   get initData() {
    const data = {} as any;
    if (this.bgImg) {
      data.bgImg = this.bgImg;
    }
    if (this.bannerImg) {
      data.bannerImg = this.bannerImg;
    }
    switch (this.type) {
      case "topFixed":
        if (!data.bannerImg) {
          data.bannerImg = "https://res.klook.com/image/upload/logo3_rz65yu.png";
        }
        break;
      case "bottomFixed":
        if (!data.bannerImg) {
          data.bannerImg = "https://res.klook.com/image/upload/v1721376026/UED_new/Platform/platform_ota_2407/img_iphone_gray_2x.png";
        }
        break;
      case "float":
        break;
      case "small":
        if (!data.bannerImg) {
          data.bannerImg = "https://res.klook.com/image/upload/fl_lossy.progressive,q_auto/logo3_rz65yu.png";
        }
        break;
      case "medium":
        if (!data.bgImg && this.platform === 'mobile') {
          data.bgImg = "https://res.klook.com/image/upload/fl_lossy.progressive,q_auto/ban_pmtqju.png";
        }
        break;
      case "large":
        if (!data.bgImg) {
          data.bgImg = "https://res.klook.com/image/upload/fl_lossy.progressive,q_auto/bottom2x_ft5osi.png";
        }
        if (!data.bannerImg) {
          data.bannerImg = "https://res.klook.com/image/upload/v1721376026/UED_new/Platform/platform_ota_2407/img_iphone_gray_2x.png";
        }
        break;
    }
    return data;
  }

   get timeDesc() {
      const { seconds, timeLeft } = this
      return timeLeft <= 0 ? null : ( seconds <= 0 ? '00:00:00' : this.formatTimeCountDown(seconds))
   }

   get descriptions() {
      let descriptions: Description[] = []
      const { desc } = this
      switch (Object.prototype.toString.call(desc).slice(8, -1)) {
          case 'String':
              descriptions.push({
                  desc: desc as string
              })
              break
          case 'Array':
              descriptions = (desc as any[]).map((item: string | Description) =>
                  typeof item === 'string' ? {
                      desc: item as string
                  } : item)
              break;
      }

      return descriptions
  }

   get customQuery(){
    const oneLinkUrl = this.oneLinkUrl || ''
    const oneLinkSpm = this.oneLinkSpm || '' // klota_spm
    const pageOpenID = this.pageOpenID || '' // klota_poid
    const oneLinkRedirectUrl = this.oneLinkRedirectUrl || '' // klota_altdp
    return {
      isDownloadPage: true,
      oneLinkUrl,
      oneLinkSpm,
      pageOpenID,
      oneLinkRedirectUrl
    }
   }

   @Watch('timeLeft')
   timeChange() {
      this.seconds = this.timeLeft
      clearTimeout(this._tid)
      this.$nextTick(() => {
         this.startTimer()
      })
   }


  startTimer() {
    this.seconds--
    if (this.seconds > 0) {
      this._tid = setTimeout(this.startTimer, 1000)
    } else {
      this.$emit('time-end');
      // if (this.type === 'topFixed') {
      //    (this as any).changeMainTranslateY(this.$el.querySelector('.time'), 'minus')
      // }
    }
  }

  formatTimeCountDown(timeDiff: number) {
    const dayBase = 60 * 60 * 24

    const arr = []

    const day = Math.floor(timeDiff / dayBase)

    arr.push(this.preFixZero(Math.floor(timeDiff / 3600 % 24)))
    arr.push(this.preFixZero(Math.floor(timeDiff / 60 % 60)))
    arr.push(this.preFixZero(Math.floor(timeDiff % 60)))

    return `${day > 0 ? `${(this as any).__t(day > 1 ? '47260' : '47259', { no: day })} ` : ''}${arr.join(':')}`
  }

  preFixZero(num: number) {
    return num < 10 ? `0${num}` : `${num}`
  }

  beforeDestroy() {
    clearTimeout(this._tid)
  }

  mounted() {
    this.seconds > 0 && this.startTimer()
    this.$emit('mounted')
  }

  created(this: any) {
    this.__t = genText((messages as any)[this.language])
  }
}
