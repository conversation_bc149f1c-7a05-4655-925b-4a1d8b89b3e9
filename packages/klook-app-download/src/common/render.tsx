import { Component, Prop } from 'vue-property-decorator'
import getAllCountryList from '@klook/klk-traveller-utils/lib/allCountryList'
import { languageConfig } from '@klook/site-config'
import Base from "./base";
import CloseSvg from "../svg/icon_edit_close_s.svg";
import AndroidSvg from "../svg/android.svg";
import IosSvg from "../svg/ios.svg";
import TimeSvg from "../svg/icon_time_s.svg";

import Shape1Svg from "../svg/shape1.svg";
import Shape2Svg from "../svg/shape2.svg";
import Shape3Svg from "../svg/shape3.svg";
import Shape from "../svg/shape.svg";
import Tip from "../svg/tip.svg";
import IconOtherClick from "../svg/icon_other_click_s.svg";
import Down from '../svg/icon_navigation_triangle_down_fill_xs.svg'
import Tips from '../svg/icon_tips_tips_s.svg'

import { gtCaptchaVerify } from '@klook/captcha'

let allCountryCodePromise: any = null

@Component({
    components: {
        Tip,
        Shape,
        IconOtherClick,
        CloseSvg,
        AndroidSvg,
        IosSvg,
        TimeSvg,
        Shape1Svg,
        Shape2Svg,
        Shape3Svg
    }
})
export default class DownloadBase extends Base {
    @Prop({ type: String }) floatImg!: string
    @Prop({ type: String }) qrCodeImg!: string //  desktop自定义二维码图片
    @Prop({ type: String }) qrCodeTip!: string //  desktop自定义二维码图片提示
    @Prop({ type: String }) ipCountry!: string // 当前地区
    @Prop({ type: String, default: 'M' }) qrCodeQ!: 'L' | 'M' | 'Q' | 'H' // 二维码质量
    @Prop({ type: Object, default: () => ({}) }) popTip!: object // poptip属性

    visible: boolean = true
    isEmail: boolean = true
    showSendModal: boolean = false
    isSendLoading: boolean = false
    errorTip: boolean = false
    showOtaInput: boolean = true
    allCountryCode: any = []
    captcha: any = {}
    currentCode: string = '1'
    currentName: string = 'CA'
    phoneNumber: string = ''
    email: string = ''

    _axios!: any

    beforeMount() {
        this._axios = this.$attrs.axios || window.$axios;
    }

    get mainClass() {
        return `app-download-${this.platform}-${this.type}`
    }

    get isMobile() {
        return this.platform === 'mobile'
    }

    // 手机号验证
    checkPhone(phoneNumber: string) {
      if (!phoneNumber || typeof phoneNumber !== 'string') {
        return false
      }
      const reg = this.currentCode === '86' ? /^\d{11}$/ : /^\d{6,18}$/
      this.errorTip = !reg.test(phoneNumber)
    }

    // email验证
    checkEmail(email: string) {
      const reg = /^[a-zA-Z0-9_-]+(\.([a-zA-Z0-9_-])+)*@[a-zA-Z0-9_-]+[.][a-zA-Z0-9_-]+([.][a-zA-Z0-9_-]+)*$/
      this.errorTip = !reg.test(email)
    }

    mounted() {
      // this.getAllCountryCodeProxy().then((res: any) => {
      //   this.allCountryCode = res
      //   const ipCountry = this.ipCountry || (window as any)?.__KLOOK__?.state?.klook?.ipCountry || (window as any).$tetris?.runtime?.country || 'BS';
      //   if (ipCountry) {
      //     let currentCode = this.allCountryCode.filter((item: any) => {
      //       return item.country_code === ipCountry;
      //     });
      //     this.currentCode = currentCode[0].country_number;
      //     this.currentName = currentCode[0].country_code;
      //   }
      // })
    }

    getAllCountryCodeProxy() {
      if (allCountryCodePromise && allCountryCodePromise.length) return allCountryCodePromise;

      allCountryCodePromise = this.getAllCountryCode()
      return allCountryCodePromise
    }

    async getAllCountryCode() {
      try {
        return await getAllCountryList({ $axios: this._axios, lang: this.language })
      } catch {
        return []
      }
    }

    initCountryCode() {
      const preferCountryCode = languageConfig.getPreferCountryListByLangCode(this.language as any);
      const prefers = this.allCountryCode.filter((option: any) => {
        return preferCountryCode.indexOf(option.country_code) >= 0;
      }).sort(function (a: any, b: any) {
        return preferCountryCode.indexOf(a.country_code) - preferCountryCode.indexOf(b.country_code);
      });
      const others = this.allCountryCode.filter((option: any) => {
        return preferCountryCode.indexOf(option.country_code) < 0;
      });

      return [
        {
          label: this.__t('48265'),
          options: prefers || [],
        },
        {
          label: this.__t('country.otherCountriesOrDistricts'),
          options: others || [],
        },
      ]
    }


    async sendData(data: any) {
      if (this.errorTip) return
      if (this.isEmail && !this.email) return
      if (!this.isEmail && !this.phoneNumber) return
      this.isSendLoading = true;
      try {
        const axios = this._axios
        if (!axios) {
          return false
        }
        const params = {
          "type": this.isEmail ? "email" : "sms",
          "address": this.isEmail ? this.email : this.currentCode + this.phoneNumber,
        }
        const { result, success, error } = await axios.$post('/v2/usrcsrv/ota/send_msg/download', data? data :  params,{
          needCaptcha: true
        })
        if (success) {
          this.showOtaInput = false;
        } else if (error && error.code === '9000' && error.data) {
          gtCaptchaVerify(error.data).then((res: any) => {
            // 验证成功
            this.sendData({
              ...params,
              _rc: res._rc, // 请求追加_rc参数再次请求
            })
          }).catch((err: any) => {
              console.error(err)
          })
          return;
        } else if(error.message) {
          (this as any).$toast(error.message);
        }
      } finally {
        this.isSendLoading = false;
      }
    }

    async sendCode() {
      this.showSendModal = true
    }

    renderSendInfo() {
      if (this.isMobile) {
        return null
      }
      const tabName = this.isEmail ? "SMS" : "Email"
      const countryCode = this.initCountryCode()
      let content = !this.isEmail ? <div class="data-info">
        <div class="select-code" onClick={() => { this.sendCode() }}>
          +{this.currentCode} <Down />
        </div>
        <klk-input name="klkOtaPhone" value={this.phoneNumber} placeholder={this.__t('12009')}
          onInput={(val: any) => { this.checkPhone(val); this.phoneNumber = val }}
        ></klk-input>
      </div> :
        <div class="data-info">
          <klk-input name="klkOtaEmail" value={this.email} placeholder={this.__t('16182')}
            onInput={(val: any) => { this.checkEmail(val); this.email = val }}
          ></klk-input>
        </div>

      const errorTip = this.errorTip ? <div class="error-tips"><Tips /><span>{this.isEmail? this.__t('79297') : this.__t('79296')}</span></div> : null

      let otaBtnDisabled = this.isEmail && !this.email || !this.isEmail && (!this.currentCode || !this.phoneNumber)
      const showOtaInput = this.showOtaInput ? <div class="ota-data">
      {content}
      <klk-button 
        class="ota-button-send" 
        disabled={ otaBtnDisabled || this.errorTip }  
        data-spm-item={ this.isEmail ? "EmailDownloadAppSendBtn" : "SmsDownloadAppSendBtn" } 
        v-galileo-click-tracker={{
          spm: this.isEmail ? 'EmailDownloadAppSendBtn' : 'SmsDownloadAppSendBtn',
          componentName: 'klk-app-download',
          autoTrackSpm: true
        }} 
        onClick={ this.sendData } 
        loading={ this.isSendLoading }
      >{ this.__t('79295') }</klk-button>
    </div> : <div class="ota-data-tip" domPropsInnerHTML={this.__t('79302', { 'number_or_email': `<span>${this.isEmail ? this.email : ' +' + this.currentCode + this.phoneNumber}</span>` })}></div>
      return <div class="send-info">
        <div class="tip">{this.__t(this.isEmail ? '79298' : '79293')}</div>
        { showOtaInput }
        {errorTip}
        <klk-modal
          open={this.showSendModal}
          esc-press-closable={true}
          closable={true}
          show-default-footer={false}
          onClose={() => { this.showSendModal = false }}
        >
          <div class="klk-modal-content select-country-code">
            <klk-select size="small" style-type="filled" filterable
              onChange={(val: any) => { this.currentCode = val.country_number;this.currentName = val.country_code; this.showSendModal = false }}
            >
              <Shape class="selected-icon" slot="prefix" />
              {countryCode.map((item: any, index: number) => {
                return (
                  <klk-option-group key={index} value={index} label={item.label}>
                    {item.options.map((item1: any) => {
                      return (<klk-option
                        key={item1.country_number + '' + index + '' + item1.id}
                        value={item1}
                        label={item1.name + item1.country_number}
                        class={this.currentCode === item1.country_number && this.currentName === item1.country_code && 'klk-option-selected'}
                      >{item1.name} +({item1.country_number})
                        {this.currentCode === item1.country_number && this.currentName === item1.country_code && <IconOtherClick class="selected-icon" />}
                      </klk-option>)
                    })}
                  </klk-option-group>
                )
              })}
            </klk-select>
          </div>
        </klk-modal>
      </div>
    }

    renderOr() {
      if (this.isMobile) {
        return null
      }
      return <div class="ota-or"><span>{this.__t('79300')}</span></div>
    }

    render() {
        if (!this.visible) return
        return this.type !== 'float'
            ? this.renderMain()
            : <div class="app-download-poptip">
                <klk-poptip placement="left" trigger="click" maxWidth={600} maxHeight={1000} onShow={this.popShow} data-spm-module={this.spm.floatIcon}
                    data-spm-virtual-item="__virtual" v-galileo-click-tracker="{ spm: this.spm.floatIcon, componentName: 'klk-app-download' }" attrs={this.popTip}>
                    {this.$slots['float-icon'] || <div class="float-icon" v-lazy:background-image={this.floatImg} ></div>}
                    {this.renderMain()}
                </klk-poptip>
            </div>
    }

    renderMain() {
        const { initData, bgColor, mainClass, platform, showClose, qrCodeTip, type } = this
        const isRenderQr = (type === 'large' || type === 'bottomFixed' || type === 'onlySend') && !this.isMobile
        const isMedium = (type === 'medium' || type === 'float') && !this.isMobile
        const directives = [
            { name: 'lazy', value: initData.bgImg, arg: 'background-image', modifiers: { container: true } }
        ]
        if (type === 'onlySend') {
          return <div style={{ 'background-color': bgColor }} class={`${mainClass} app-download-${platform} ${showClose ? 'with-close' : ''} ${qrCodeTip ? 'with-tip' : ''}`} slot="content" data-spm-module={this.spm.module}
            data-spm-virtual-item="__virtual?trg=manual" v-galileo-click-tracker="{ spm: this.spm.module, componentName: 'klk-app-download' }">
            <div class="download_info_con">
              <div class="download_info">
                {this.renderSendInfo()}
                {this.renderOr()}
                {isRenderQr ? this.renderQrMarket() : null}
              </div>
            </div>
          </div>
        }
        return <div style={{ 'background-color': bgColor }} class={`${mainClass} app-download-${platform} ${showClose ? 'with-close' : ''} ${qrCodeTip ? 'with-tip' : ''}`} slot="content" data-spm-module={this.spm.module}
        data-spm-virtual-item="__virtual?trg=manual" v-galileo-click-tracker="{ spm: this.spm.module, componentName: 'klk-app-download' }">
          <div class="medium-data">
              {isMedium ? this.renderTime() : null}
              {isMedium ? <div class="title-desc">
                {this.renderTitle()}
                {this.renderDesc()}
              </div> : null}
            </div>
            <div class="ota-container" {...{ directives: initData.bgImg? directives: [] }} >

                {this.renderBannerImg()}

            <div class="wrap">
              {!isMedium ? <div class="title-desc">
              {!isMedium ? this.renderTime() : null}
                {this.renderTitle()}
                {this.renderDesc()}
                {this.renderDownloadBtn()}
              </div> : null}
              <div class="download_info_con">
                <div class="download_info">
                  {this.renderSendInfo()}
                  {this.renderOr()}
                  {isRenderQr ? this.renderQrMarket() : null}
                </div>
              </div>
            </div>

                {this.renderClose()}

                <div class="shape">
                    <shape1-svg class="shape1" />
                    <shape2-svg class="shape2" />
                    <shape3-svg class="shape3" />
                </div>
            </div>
        </div>
    }

    renderBannerImg() {
        const { bannerImg, type, initData } = this
        const isRenderQr = type === 'large' || type === 'bottomFixed'
        if(this.isMobile && !bannerImg) {
            return null
        }
      const directives = [
        { name: 'lazy', value: initData.bannerImg, arg: 'background-image', modifiers: { container: true } }
      ]
      return <div class="ota-banner" {...{ directives: initData.bannerImg ? directives: [] }}>
        {!isRenderQr ? this.renderQrMarket() : null}
      </div>
    }

    renderQrMarket() {
        const { qrCodeImg, qrCodeTip, type, isLoading } = this
        if(this.isMobile) {
          return null
        }

        const backgroundImage = qrCodeImg?
        <div class="app-download-qrcode"  v-loading={ isLoading }
                v-lazy:background-image={qrCodeImg}
                data-klk-loading-show-loading-bg="false"
                data-klk-loading-show-overlay="true"
                data-klk-loading-overlay-color="rgba(255,255,255)">
                {!qrCodeImg && <div class="code-logo"></div>}
                {!qrCodeImg && <canvas class="app-download-canvas"></canvas>}
            </div>:<div class="app-download-qrcode"  v-loading={ isLoading }
                data-klk-loading-show-loading-bg="false"
                data-klk-loading-show-overlay="true"
                data-klk-loading-overlay-color="rgba(255,255,255)" >
                {!qrCodeImg && <div class="code-logo"></div>}
                {!qrCodeImg && <canvas class="app-download-canvas"></canvas>}
            </div>
        const isRenderQr = type === 'large' || type === 'bottomFixed' || type === 'onlySend'
        return this.platform === 'desktop' && <div class={!isRenderQr ? 'app-download-area' : 'app-download-area1'}>
            {qrCodeTip && <div class="app-download-are-tip"> {qrCodeTip}</div>}
            {backgroundImage}
        </div>
    }

    renderTitle() {
        return <p class="title">{this.title}</p>
    }

    renderTime() {
        return this.$slots['time-left'] || (this.timeDesc && <div class="time">
            <time-svg />
            <span> {this.timeDesc}</span>
        </div>)
    }

    renderClose() {
        return this.showClose && <close-svg
            class="close"
            data-spm-module={this.spm.closeBtn}
            data-spm-virtual-item="__virtual"
            v-galileo-click-tracker="{ spm: this.spm.closeBtn, componentName: 'klk-app-download' }"
            nativeOnClick={this.handleClose} />
    }

    renderDesc() {
        const { descriptions } = this
        return descriptions.length && <div class="desc">
            {descriptions.map(({ img, desc }, index) => (
                <p key={index}>
                    {img && <img v-lazy={img} />}
                    <span> {desc} </span>
                </p>
            ))}
        </div> || null
    }

    renderDownloadBtn() {
        const size = this.platform === 'desktop' ? 'normal' : (this.type === 'small' ? 'mini' : 'small')
        const { downloadText } = this
        return this.$slots['download-button'] || (downloadText && <klk-button
            data-spm-module={this.spm.download}
            data-spm-virtual-item="__virtual"
            v-galileo-click-tracker="{ spm: this.spm.download, componentName: 'klk-app-download' }"
            class="button"
            size={size}
            block={this.type !== 'small'}
            round={this.type === 'small'}
            onClick={this.download}
        >
            {downloadText}
        </klk-button>)
    }

    popShow() {
        // hack 在 poptip中使用vue-lazyload需要滚动页面才生效
        // (this as any).$Lazyload.lazyLoadHandler()
        window.scrollTo(0, (document.documentElement.scrollTop || window.pageYOffset || document.body.scrollTop) + 1)
    }

    download() {
        this.genOneLinkUrl(this.$el as HTMLElement, { ...this.customQuery })
          .then((url: string)=>{
          if(url){
            window.location.href = url
          }
        })
    }

    handleClose() {
        this.visible = false
        this.$emit('close');
    }
}
