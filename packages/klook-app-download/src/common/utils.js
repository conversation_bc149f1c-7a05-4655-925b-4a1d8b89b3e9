import l from "@klook/klk-traveller-utils/lib/localStorage";

export function createStorageOTA(comp) {
  return {
    data() {
      return {
        visible: true
      };
    },
    created() {
      if (this.$isServer || navigator.userAgent.includes('klook')) {
        this.visible = false;
        return;
      }
      if (this.$attrs.showClose !== false && l.getItem("downloadBar")) {
        this.$emit("update-visibility", "hidden");
        this.visible = false;
        return;
      }
      this.$emit("update-visibility", "visible");
    },
    render(h) {
      const _this = this;
      if (!this.visible) {
        return null;
      }

      return h(comp, {
        attrs: _this.$attrs,
        on: {
          ..._this.$listeners,
          close(...args) {
            if (_this.$listeners.close) {
              _this.$listeners.close(...args);
            }
            _this.$emit("update-visibility", "hidden");
            l.setItem("downloadBar", "close", _this.$attrs.klkLocalDownloadBarTime);
          }
        }
      });
    }
  };
}
