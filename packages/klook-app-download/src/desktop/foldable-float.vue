<template>
  <div class="app-download-foldable">
    <div
      class="foldable-trigger"
      :class="{ 'foldable-collapse': isCollapse }"
      :data-spm-module="spm.floatIcon"
      data-spm-virtual-item="__virtual"
      v-galileo-click-tracker="{ spm: spm.floatIcon, componentName: 'klk-app-download' }"
    >
      <div
        v-if="floatTitle || floatDesc"
        @click="showModal"
        class="foldable-trigger-info"
        :class="{ 'foldable-trigger-collapse': isCollapse }"
        :style="{ 'background-color': bgColor }"
      >
        <div class="foldable-trigger-title">{{ floatTitle }}</div>
        <div class="foldable-trigger-desc">{{ floatDesc }}</div>
      </div>
      <div @click="showModal">
        <foldable-svg class="foldable-icon" />
      </div>
      <div
        class="foldable-arrow foldable-arrow-right"
        :data-spm-item="`Btn?mod=stop&ext=${JSON.stringify({'Click_Type':'Hide'})}`"
        v-galileo-click-tracker="{ spm: spm.floatIcon + '.Btn', componentName: 'klk-app-download' }"
        @click="switchIcon"
        v-show="!isCollapse"
      >
        <klk-icon
          type="icon_navigation_chevron_right"
          :size="16"
          color="#ffffff"
        ></klk-icon>
      </div>
      <div
        class="foldable-arrow foldable-arrow-left"
        :data-spm-item="`Btn?mod=stop&ext=${JSON.stringify({'Click_Type':'View'})}`"
        v-galileo-click-tracker="{ spm: spm.floatIcon + '.Btn', componentName: 'klk-app-download' }"
        @click="switchIcon"
        v-show="isCollapse"
      >
        <klk-icon
          type="icon_navigation_chevron_left"
          :size="16"
          color="#ffffff"
        ></klk-icon>
      </div>
    </div>

    <klk-modal
      :open="showAppDownloadModal"
      :esc-press-closable="true"
      :closable="false"
      :show-default-footer="false"
      width="800px"
      modal-class="app-download-modal"
    >
      <div
        class="download-app-container"
        :data-spm-module="spm.module"
      >
        <div
          class="close-button"
          v-if="showClose"
          @click="showAppDownloadModal = false"
          :data-spm-item="spm.closeBtn"
          v-galileo-click-tracker="{ spm: spm.module + '.' + spm.closeBtn, componentName: 'klk-app-download' }"
        ><close-svg /></div>
        <div class="download-app-left" :style="{ 'background-color': bgColor }">
          <p class="title">{{ this.title }}</p>
          <div v-if="descriptions.length" class="desc">
            <p v-for="(item, index) in descriptions" :key="index">
              <img v-if="item.img" v-lazy="item.img" />
              <span>{{ item.desc }}</span>
            </p>
          </div>
          <shape1-svg class="shape1" />
          <gift-svg class="shape3" />
        </div>
        <div class="download-app-right">
          <div class="app-download-area1">
            <div v-if="qrCodeTip" class="app-download-are-tip">
              {{ qrCodeTip }}
            </div>
            <div v-if="qrCodeImg">
              <div
                class="app-download-qrcode"
                v-loading="isLoading"
                v-lazy:background-image="qrCodeImg"
                :data-klk-loading-show-loading-bg="false"
                :data-klk-loading-show-overlay="true"
                data-klk-loading-overlay-color="rgba(255,255,255)"
              ></div>
            </div>
            <div v-else>
              <div
                class="app-download-qrcode"
                v-loading="isLoading"
                :data-klk-loading-show-loading-bg="false"
                :data-klk-loading-show-overlay="true"
                data-klk-loading-overlay-color="rgba(255,255,255)"
              >
                <div class="code-logo"></div>
                <canvas class="app-download-canvas"></canvas>
              </div>
            </div>
          </div>
          <div class="ota-or">
            <span>{{ __t("79300") }}</span>
          </div>
          <div class="send-info">
            <div class="tip">{{ __t("79298") }}</div>

            <div v-if="showOtaInput" class="ota-data">
              <div class="data-info">
                <klk-input
                  name="klkOtaEmail"
                  v-model="email"
                  :placeholder="__t('16182')"
                  append-icon="icon_communication_email"
                  @input="onInput"
                ></klk-input>
              </div>

              <klk-button
                class="ota-button-send"
                :disabled="!email || errorTip"
                data-spm-item="EmailDownloadAppSendBtn"
                v-galileo-click-tracker="{ spm: spm.module + '.EmailDownloadAppSendBtn', componentName: 'klk-app-download' }"
                @click="sendData"
                :loading="isSendLoading"
              ></klk-button>
            </div>

            <div
              v-else
              class="ota-data-tip"
              v-html="
                __t('79302', { number_or_email: `<span>${email}</span>` })
              "
            ></div>

            <div v-if="errorTip" class="error-tips">
              <Tips />
              <span>{{ __t("79297") }}</span>
            </div>
          </div>
        </div>
      </div>
    </klk-modal>
  </div>
</template>
<script lang="ts">
import { Component, Prop } from "vue-property-decorator";
import genOneLinkUrl from "@klook/klk-traveller-utils/lib/onelinkUrl";
import Base from "../common/base";

import Tips from "../svg/icon_tips_tips_s.svg";
import Shape1Svg from "../svg/shape1.svg";
import FoldableSvg from "../svg/foldable.svg";
import GiftSvg from "../svg/gift.svg";
import CloseSvg from "../svg/icon_edit_close_s.svg";
import { gtCaptchaVerify } from '@klook/captcha'

@Component({
  components: {
    Tips,
    Shape1Svg,
    FoldableSvg,
    GiftSvg,
    CloseSvg,
  },
})
export default class AppDownloadFoldableFloat extends Base {
  @Prop({ type: String }) floatImg!: string;
  @Prop({ type: String, default: "M" }) qrCodeQ!: "L" | "M" | "Q" | "H"; // 二维码质量
  @Prop({ type: String, default: "topFixed" }) type!:
    | "topFixed"
    | "bottomFixed"
    | "float"
    | "small"
    | "medium"
    | "large"
    | "onlySend";
  @Prop({ type: String }) qrCodeImg!: string; //  desktop自定义二维码图片
  @Prop({ type: String }) qrCodeTip!: string; //  desktop自定义二维码图片提示
  @Prop({ type: String, default: "#4D40CC" }) bgColor!: string;
  @Prop({ type: String, default: "" })
  floatTitle!: string;
  @Prop({ type: String, default: "" })
  floatDesc!: string;

  @Prop({ type: String, default: "Get 5% off your 1st booking" })
  title!: string;
  @Prop({
    type: Array,
    default: () => [
      "Enjoy a hassle-free booking on the app when you get the discount!",
    ],
  })
  desc!: string[] | string;
  @Prop({ type: Boolean, default: true }) showClose!: boolean;

  isSendLoading: boolean = false;
  errorTip: boolean = false;
  showOtaInput: boolean = true;
  email: string = "";

  showAppDownloadModal: boolean = false;

  isCollapse: boolean = false;

  _axios!: any;

  genOneLinkUrl:any = genOneLinkUrl

  beforeMount() {
    this._axios = this.$attrs.axios || window.$axios;
  }

  mounted() {}

  switchIcon() {
    this.isCollapse = !this.isCollapse;
  }

  showModal() {
    this.showAppDownloadModal = true;
    this.$nextTick(() => {
      // @ts-ignore
      this.genQrCode();
    });
  }

  async genQrCode() {
    try {
      this.isLoading = true;
      const url = await this.genOneLinkUrl(this.$el as HTMLElement, {
        needConcatKepler: false,
        needConcatLink: !!this.customQuery.oneLinkRedirectUrl,
        ...this.customQuery,
      });
      if (url) {
        await this.handleGenQrCode(url).finally(() => {
          this.isLoading = false;
        });
      } else {
        this.isLoading = false;
      }
    } catch (e) {
      this.isLoading = false;
      console.error(e);
    }
  }

  handleGenQrCode(qrcodeLink: string) {
    return new Promise<void>((resolve, reject) => {
      const canvas = document.querySelector?.(
        ".app-download-modal .app-download-canvas"
      );
      if (!canvas) return;

      // @ts-ignore
      const promiseSource = import("qrcode/lib/browser.js");
      // 异步加载QRCode
      promiseSource.then((QRCode) => {
        QRCode.toCanvas(
          canvas,
          qrcodeLink,
          {
            margin: 0,
            errorCorrectionLevel: this.qrCodeQ,
          },
          function (error: any) {
            if (error) {
              console.error(error);
              reject(error);
            } else {
              resolve();
            }
          }
        );
      });
    });
  }

  async sendData(data:any) {
    if (this.errorTip) return;
    if (!this.email) return;
    this.isSendLoading = true;
    try {
      const axios = this._axios;
      if (!axios) {
        return false;
      }
      const params = {
        type: "email",
        address: this.email
      }
      const { result, success, error } = await axios.$post("/v2/usrcsrv/ota/send_msg/download", data? data :  params,
        {
          needCaptcha: true
        }
      );
      if (success) {
        this.showOtaInput = false;
      }else if (error && error.code === '9000' && error.data) {
        gtCaptchaVerify(error.data).then((res: any) => {
          // 验证成功
          this.sendData({
            ...params,
            _rc: res._rc, // 请求追加_rc参数再次请求
          })
        }).catch((err: any) => {
          console.error(err)
        })
        return;
      } else if (error.message) {
        (this as any).$toast(error.message);
      }
    } finally {
      this.isSendLoading = false;
    }
  }
}
</script>

<style lang="scss" scoped>
@import "../style/_variables.scss";
.app-download-foldable {
  .foldable-trigger {
    position: fixed;
    right: 27px;
    bottom: calc(7.5% + 170px);
    margin: auto;
    transition: right 0.3s;
    z-index: 1000;
    &.foldable-collapse {
      right: -24px;
    }
  }
  .foldable-arrow {
    cursor: pointer;
    position: absolute;
    top: 10px;
    width: 32px;
    height: 32px;
    border-radius: 25px;
    background-color: $color-overlay-default-3;
    display: flex;
    justify-content: center;
    align-items: center;
    &.foldable-arrow-right {
      right: -10px;
    }
    &.foldable-arrow-left {
      left: 0px;
    }
  }
  .foldable-icon {
    position: relative;
    width: 71px;
    height: 105px;
    background-size: cover;
    cursor: pointer;
  }
}

.foldable-trigger-info {
  position: absolute;
  right: -27px;
  top: 88px;
  width: 140px;
  padding: 16px;
  border-radius: $radius-xxl 0 0 $radius-xxl;
  transition: all 0.3s;

  .foldable-trigger-title {
    color: $color-white;
    @include font-body-m-bold;
    @include text-ellipsis(2);
    margin: 0;
    transition: font-size 0.3s;
    font-size: 16px;
  }
  .foldable-trigger-desc {
    color: $color-white;
    @include font-body-xs-regular;
    @include text-ellipsis(3);
    margin: 0;
    transition: font-size 0.3s;
    font-size: 12px;
  }

  &.foldable-trigger-collapse {
    right: -51px;
    width: 130px;
    .foldable-trigger-title,
    .foldable-trigger-desc {
      font-size: 0;
    }
  }
}

::v-deep .app-download-modal {
  padding: 0;
  .klk-modal-body {
    padding: 0;
  }
  .download-app-container {
    display: flex;
    .download-app-left {
      overflow: hidden;
      position: relative;
      width: 504px;
      border-radius: 20px 0 0 20px;
      padding: 23px 36px 150px 36px;
      // min-height: 325px;
      .title {
        color: $color-white;
        @include font-heading-l;
        @include text-ellipsis(2);
        margin: 0;
      }
      .desc {
        color: $color-white;
        @include font-body-m-regular;
        @include text-ellipsis(2);
        margin-top: 4px;
        p {
          margin: 0;
        }
      }
      .shape1 {
        position: absolute;
        left: -30px;
        bottom: 64px;
        color: #1fdfdf;
        width: 85px;
        height: 50px;
        transition: transform 240ms cubic-bezier(0.22, 0, 0.08, 1) 320ms;
        transform: scale(2);
      }
      .shape3 {
        position: absolute;
        bottom: 0;
        right: 50px;
        width: 275px;
        height: 134px;
      }
    }
    .download-app-right {
      flex: 1;
      padding: 32px 0;
      .ota-or {
        position: relative;
        @include font-caption-m-regular;
        height: 28px;
        line-height: 28px;
        margin: 0 16px;
        display: flex;
        justify-content: center;
        flex-wrap: wrap;

        span {
          position: relative;
          display: block;
          width: 100%;
          white-space: nowrap;
          text-align: center;
        }

        &::before,
        &::after {
          position: absolute;
          content: "";
          display: block;
          width: calc(50% - 16px);
          height: 1px;
          top: 50%;
          background-color: $color-bg-widget-darker-3;
        }
        &::before {
          left: 0;
        }
        &::after {
          right: 0;
        }
      }
      .send-info {
        padding: 0 24px;
        .tip {
          @include font-body-s-bold;
          @include text-ellipsis(1);
          margin-bottom: 12px;
          text-align: center;
        }
      }
      .ota-data {
        position: relative;
        .ota-button-send {
          position: absolute;
        }
        .ota-button-send {
          position: absolute;
          right: 0;
          height: 100%;
          top: 0;
          min-width: 45px;
          opacity: 0;
        }
      }
      .ota-data-tip {
        @include font-body-s-regular;

        margin: 8px 0 18px 0;

        ::v-deep span {
          font-weight: $fontWeight-bold;
        }
      }
    }
    .app-download-area1 {
      @include font-caption-m-regular;

      width: 100%;
      flex: 1;

      .app-download-are-tip {
        @include font-body-s-bold;
        color: $color-text-primary;
        text-align: center;
        max-width: 248px;
        margin: 0 auto;
      }

      .code-logo {
        position: absolute;
        transform: translate(-50%, -50%);
        top: 50%;
        left: 50%;
        background-image: url("https://res.klook.com/image/upload/klook_laf0fq.png");
        background-size: cover;
      }

      .app-download-qrcode {
        width: 122.95px;
        height: 119.53px;
        padding: 8px;
        background: $color-bg-widget-normal;
        border-radius: $radius-xl;
        position: relative;
        margin: 0 auto;

        .code-logo {
          width: 20px;
          height: 20px;
        }

        canvas {
          width: 100% !important;
          height: 100% !important;
        }
      }
    }
  }
  .close-button {
    position: absolute;
    top: 0;
    right: -36px;
    cursor: pointer;
    color: $color-white;
  }
}

div[lazy="error"] {
  background-size: auto !important;
}
</style>
