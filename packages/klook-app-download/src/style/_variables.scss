// $color-gray3: #f5f5f5;
// $color-main: #ff5722;
// $color-white: white;
// $color-gray2: #e0e0e0;

// $zindex-trigger-layer: 200; // 提示气泡 Poptip，下拉菜单 Dropdown ，级联选择器 Cascader Select， 选择器 Select， 局部加载 Loading

$appstore-cn-group: 'https://res.klook.com/image/upload/ios_zh_oulprx.png', 'https://res.klook.com/image/upload/ios_zh_2x_d5mbj8.png';
$appstore-tw-group: 'https://res.klook.com/image/upload/ios_zht_iwzqlo.png', 'https://res.klook.com/image/upload/ios_zht_2x_qsoigu.png';
$appstore-en-group: 'https://res.klook.com/image/upload/ios_en_elx3fg.png', 'https://res.klook.com/image/upload/ios_en_2x_hens4i.png'
;
$android-cn-group: 'https://res.klook.com/image/upload/and_zh_p69cj0.png', 'https://res.klook.com/image/upload/and_zh_2x_exp451.png';
$android-tw-group: 'https://res.klook.com/image/upload/and_zht_v0v3pd.png', 'https://res.klook.com/image/upload/and_zht_2x_bns3sj.png';
$android-en-group: 'https://res.klook.com/image/upload/and_en_s9qilr.png', 'https://res.klook.com/image/upload/and_en_2x_ksnlyt.png';
// 多行省略
@mixin text-ellipsis($line-clamp: 2) {
  display: -webkit-box;
  -webkit-line-clamp: $line-clamp;
  -webkit-box-orient: vertical;
  text-overflow: ellipsis;
  overflow: hidden;
}

@mixin retina-sprite($retina-group) {
  $normal-url: nth($retina-group, 1);
  $retina-url: nth($retina-group, 2);
  background-image: url(#{$normal-url});

  @media (-webkit-min-device-pixel-ratio: 2),
  (min-resolution: 192dpi) {
    background-image: url(#{$retina-url});
  }
}
