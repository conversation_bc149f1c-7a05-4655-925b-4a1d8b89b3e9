import Vue from 'vue'
import { OtaInfoOption } from './types'

// components
export const AppDownloadMobileTop: Vue.Component;

export const AppDownload: Vue.Component;
export interface Description {
    img?: string, 
    desc: string 
}
export const AppDownloadMobileTopStorage: Vue.Component;
export const AppDownloadStorage: Vue.Component;

export declare class ApiAppDownload extends Vue {
  location: string
  language: string
  type: string
  infoData: OtaInfoOption
}

export default ApiAppDownload