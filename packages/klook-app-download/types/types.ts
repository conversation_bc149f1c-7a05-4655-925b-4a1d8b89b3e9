export interface OtaInfoOption {
  style?: {
    show_close: Boolean
    scale: 'small' | 'medium' | 'large' | 'bottomFixed' | 'topFixed' | 'foldableFloat'
    position: // web
    | 'header'
      | 'rightBottomFixed'
      | 'staticBanner'
      | 'bottomFixed'
      // mweb
      | 'small'
      | 'topFixed'
  }
  time?: {
    has_countdown: Boolean // 是否有倒计时
    from_time?: any
    to_time?: any
  }
  onelink?: {
    qr_desc?: string
    link?: string // 生成二维码链接
  }
  banner?: {
    img?: string // banner_img
    title?: string
    desc?: any[]
  }
  download_button?: {
    img?: string
    desc?: string
  }
  bg?: {
    img?: string
    color: string
  }
  ota_closed?: number | null
  tracking?:
    | {
        download?: {
          spm?: string
          extra?: {
            [key: string]: any
          }
        }
        close?: {
          spm?: string
          extra?: {
            [key: string]: any
          }
        }
        module?: {
          spm?: string
          extra?: {
            [key: string]: any
          }
        }
      }
    | any
  icon_config?: {
    title?: string
    desc?: string
  }
}