{"name": "@klook/klk-app-download", "version": "2.1.5", "description": "A Common Component based on Vue of Klook", "author": "damon.dai", "homepage": "https://design.klook.io", "keywords": ["vue", "component", "ui", "framework"], "main": "dist/cjs/index.js", "module": "dist/esm/index.js", "files": ["dist", "types"], "typings": "types/index.d.ts", "license": "UNLICENSED", "publishConfig": {"registry": "https://knpm.klook.io", "access": "public"}, "scripts": {"build": "NODE_ENV=production rollup --config ./build/rollup.config.js", "lint": "NODE_ENV=production eslint --ext .js,.vue src", "test": "NODE_ENV=test jest -i --updateSnapshot", "test:coverage": "NODE_ENV=test jest -i --coverage --updateSnapshot", "build:locale": "NODE_ENV=production rollup --config ./build/rollup.locale.config.js", "prepush": "yarn run lint", "prepublishOnly": "bash prepublishOnly.sh", "commit": "npx git-cz", "commitmsg": "commitlint -E GIT_PARAMS"}, "devDependencies": {"@babel/core": "^7.17.9", "@babel/plugin-proposal-class-properties": "^7.16.7", "@babel/plugin-proposal-decorators": "^7.17.9", "@babel/plugin-transform-runtime": "^7.17.0", "@klook/klk-traveller-utils": "1.8.6", "@babel/preset-env": "^7.16.11", "@klook/klook-ui": "^1.38.7", "@rollup/pluginutils": "3.0.9", "@types/jest": "^26.0.0", "@types/webpack-env": "^1.14.0", "@klook/captcha": "2.0.0", "@klook/site-config": "1.3.1", "@vue/test-utils": "^1.0.0-beta.32", "jest": "^25.5.4", "rimraf": "^3.0.0", "rollup": "^2.70.1", "rollup-plugin-babel": "^4.4.0", "rollup-plugin-buble": "^0.19.8", "rollup-plugin-commonjs": "^10.0.2", "rollup-plugin-node-resolve": "^5.2.0", "rollup-plugin-replace": "^2.2.0", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-typescript2": "^0.31.2", "rollup-plugin-vue": "^5.1.9", "rollup-plugin-postcss": "4.0.2", "autoprefixer": "^9.8.6", "svgo": "1.3.2", "terser": "^4.1.3", "ts-jest": "^26.1.0", "ts-node": "^8.10.2", "typescript": "^4.6.3", "vue-jest": "^3.0.4", "vue-lazyload": "1.3.3", "vue": "2.6.11", "vue-property-decorator": "^8.3.0", "vue-template-compiler": "2.6.11", "vue-template-es2015-compiler": "1.9.1", "rollup-plugin-clear": "^2.0.7", "babel-helper-vue-jsx-merge-props": "^2.0.3", "qrcode": "1.5.1", "@klook/klook-icons": "^0.13.1"}, "dependencies": {}, "peerDependencies": {"@klook/captcha": "2.0.0", "@klook/site-config": "1.3.1", "qrcode": "1.5.1", "@klook/klook-ui": "^1.38.7", "@klook/klk-traveller-utils": "^1.4.4", "vue": "2.6.11", "vue-lazyload": "1.3.3", "@klook/klook-icons": "^0.13.1", "vue-property-decorator": "^8.3.0"}}