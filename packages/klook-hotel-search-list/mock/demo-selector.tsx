import { Vue, Component, Prop } from 'vue-property-decorator'
import { SelectorDesktop, LayerCalendar, LayerDestination, LayerGuest } from '@klook/hotel-selector'
import { IconHotel, IconChevronDown, IconNumberOfRooms, IconUser, IconNext, IconTriangleDown } from '@klook/klook-icons'

@Component({
  components: {
    SelectorDesktop, 
    LayerCalendar, 
    LayerDestination, 
    LayerGuest,
    IconHotel, IconChevronDown, IconNumberOfRooms, IconUser, IconNext, IconTriangleDown
  }
})
export default class RoomPrice extends Vue {
    @Prop({ default: false }) isMobile!: boolean
    @Prop({ default: null }) paramsInfo!: object
    @Prop({ default: null }) selectorOptions!: object
    @Prop({ default: null }) onSearch!: Function
    @Prop({ default: 0 }) top!: number

    render() {
      return (this.isMobile ? <div class='hotel-search-list-header'>
        <div class="hotel-selector-mobile-poi">
            <layer-destination attrs={this.selectorOptions.destinationConfig}
                options={this.paramsInfo}
                scopedSlots={{
                    default: ({ valueDesc }: any) => {
                        return <div class="selector-destination">
                            <icon-hotel theme="outline" size={20} class="hotel-icon" />
                            <div class="content">{valueDesc}</div>
                            {/* v-if="destinationList && destinationList.length > 1" */}
                            <icon-chevron-down class="icon-down" theme="outline" size={20} />
                        </div>
                    }
                }} />
            <div class="hotel-selector-mobile-main">
                <layer-calendar class="selector-calendar" scopedSlots={{
                    default: ({ valueDesc }: any) => {
                        return <div class="selector-calendar-main">
                            {valueDesc}
                        </div>
                    }
                }} />
                <layer-guest class="selector-guest" attrs={this.selectorOptions.calendarConfig} scopedSlots={{
                    default: ({ valueDesc }: any) => {
                        return <div class="selector-guest-main">
                            <div class="rooms">
                                <icon-number-of-rooms
                                    theme="filled"
                                    size="16"
                                    class="hotel-icon"
                                />
                                {this.paramsInfo.room_num}
                            </div>
                            <div class="adults">
                                <icon-user theme="filled" size="16" class="hotel-icon" />
                                {this.paramsInfo.adult_num + this.paramsInfo.child_num}
                            </div>
                        </div>
                    }
                }} />
            </div>
            <klk-button
                class="hotel-selector-mobile-search"
                block
                size="small"
                data-spm-module="SearchButton"
                nativeOnClick={this.onSearch}
            >
                {this.$t('car_rental_home_search')}
            </klk-button>
        </div>
    </div> : <div class='hotel-search-list-header'  style={{top: this.top || 0}}>
        <selector-desktop
            ref="selector"
            paramsInfo={this.paramsInfo}
            attrs={this.selectorOptions}
            onSearch={this.onSearch}
        />
    </div>)
    }
}

