
import Vue from 'vue';

export type ScreenPlatform = 'mobile' | 'desktop' | 'tablet'

export interface ListParams {
  filter_list: string[]
  rateGroup?: string
  sort: string
  limit: 20
  current_page?: number
  is_map?: boolean
  range_coordinates?: string
}

export interface HotelFilter {
  hotel_item_view_filter?: string
  sort_list?: SubFilterItem[]
  filter_list?: SubFilterItem[]
  area_filter_list?: SubFilterItem[],
  area_filter_title?: string,
  filter_selected?: string[],
  sort_selected?: string
  count?: string
}

export interface SubFilterItem {
  id: string,
  title?: string,
  title_en?: string,
  sub_title?: string // 副标题
  icon_url?: string // 图标
  selected_icon_url?: string // 选中的图标url，目前仅热门设施有用
  sub_filter_list: null | SubFilterItem[],
  ui_type?: number
  disabled?: boolean
  popular_facility_list?: null | SubFilterItem[], // 热门设施，只有设施项才有
  price_filter?: {
    currency: string
    end: string
    start: string
    step: string
    currency_symbol: string
    counts: number[] // 每个步长酒店数量
  }
}

export type EmptyType = 'loading' | 'emptyLoading' | 'error2' | 'empty' | 'filterEmpty' | ''

export interface ListResult {
  search_id?: string
  report: any,
  current_page: number,
  total_count: number,
  content: any[]
  is_new_card_style?: boolean
}

export type PoiInfo = {
  stype: 'place' | string // 目前这个poi都是place
  city_name: string
  country_name?: string
  city_id: string | number
  svalue: string // svalue目前是和city_id一样
}

export interface CalendarInfo {
  check_in?: string
  check_out?: string
  calendar_type?: number
  flexible_type?: number
  flexible_day?: number | string
  flexible_date_list?: string
}

export type ParamsInfo = PoiInfo & CalendarInfo

type install = (vue: typeof Vue) => void

export const HotelSearchList: {
  install: install
}

export default HotelSearchList
