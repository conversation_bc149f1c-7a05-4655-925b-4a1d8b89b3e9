import { PageSpm, ObjectIdName, SpmExtraInfo, IHTPropObj, IHTProp, ModuleIHTProp, PageIHTProp, TrackIHTProp, UpdateIHTProp } from './const'
import { queryString } from '../index'

/**
 *
 * @param el const domTest2 = document.querySelector('.hotel-universal-layer_main .klk-anchor-content .klk-anchor-item:nth-child(2)') 动态给nav增加spm
 * @param spm
 */
export const setElementSpm = (el: HTMLElement, spm: string) => {
  if (!el) {
    return
  }
  el.setAttribute('data-spm-module', spm)
  el.setAttribute('data-spm-virtual-item', '__virtual')
}

// 方法二：统一获取page spm来源， 这里可以直接代码只用对象key使用
export const getPageSpm = (spm: PageSpm) => {
  return spm
}

// 获取结构化后的object格式
export const getObjectId = (pre: ObjectIdName | null, value: number | string) => {
  return `${pre}_${value}`
}

// 限制dom使用方式中强制提示encodeURIComponent？
const getExtraInfo = (extraInfo: SpmExtraInfo = {}, inHtml: boolean = false) => {
  const extraInfoString = JSON.stringify(extraInfo)
  return inHtml ? encodeURIComponent(extraInfoString) : extraInfoString
}

const getIHT = ({
  spm,
  oidName,
  oidValue,
  ext,
  manual,
  isOverlay
}: IHTProp) => {
  const propObj: IHTPropObj = {}

  if (oidName && oidValue) {
    propObj.oid = getObjectId(oidName, oidValue)
  } else if (!(!oidName && !oidValue)) {
    // eslint-disable-next-line no-console
    // console.warn(`${spm} need oidName and oidValue`)
  }

  if (ext) {
    propObj.ext = getExtraInfo(ext, true)
  }

  if (manual) {
    propObj.trg = 'manual'
  }

  // 如果是手动的且spm是Overlay，则加上 _f自动修正clickId
  if (isOverlay || (manual && /Overlay$/ig.test(spm))) {
    propObj._f = '1'
  }

  const propStr = queryString.obj2str(propObj) as string

  return `${spm}${propStr ? `?${propStr}` : propStr}`
}

export const getPageIHTStr = (ihtProp: PageIHTProp) => {
  const spmString = getIHT(ihtProp)

  return spmString as string
}
export const getPageIHT = (ihtProp: PageIHTProp) => {
  const spmString = getPageIHTStr(ihtProp)
  return {
    'data-spm-page': spmString
  }
}

// 默认曝光， 若果不曝光加manual: false
export const getModuleIHTStr = (ihtProp: ModuleIHTProp) => {
  let spmString = getIHT(ihtProp)

  const { idx, len, spm } = ihtProp

  if (/list$/i.test(spm)) {
    if (typeof idx === 'undefined' || typeof len === 'undefined') {
      // eslint-disable-next-line no-console
      console.warn(`${spm} idx or len is undefined`)
    } else {
      const hasQuestion = spmString.includes('?')
      spmString += `${hasQuestion ? '&' : '?'}idx=${idx}&len=${len}`
    }
  }

  return spmString
}

export const getModuleIHT = (ihtProp: ModuleIHTProp) => {
  const spmString = getModuleIHTStr(ihtProp)

  const ihtObj = {
    'data-spm-module': spmString
  } as {
    'data-spm-module': string
    'data-spm-virtual-item'?: string
  }
  // 默认是可以自动点击action的virtualItem
  const { isVirtualItem = true, isClick = true } = ihtProp

  if (isVirtualItem) {
    ihtObj['data-spm-virtual-item'] = `__virtual${isClick ? '' : '?trg=manual'}`
  }

  return ihtObj
}

export function updateBinding(this: Vue, el: HTMLElement | string, {
  oidName,
  oidValue,
  ext
}: UpdateIHTProp) {
  const attrs: {
    ext: string,
    oid?: string
  } = {
    ext: getExtraInfo(ext)
  }
  if (oidName && oidValue) {
    attrs.oid = getObjectId(oidName, oidValue)
  }
  (window as any).$klook.$inhouse.updateBinding(el, attrs)
}
// custom单独调用

export function track(this: Vue, type: 'action' | 'pageview' | 'exposure' | 'custom', el: HTMLElement | string, {
  force,
  spm,
  ext
}: TrackIHTProp = {}) {
  let attrs: {
    [key: string]: any
  } | null = null

  if (force || spm || ext) {
    attrs = {}
    if (force) {
      attrs.force = force
    }
    if (spm) {
      attrs.spm = spm
    }
    if (ext) {
      // custom事件的ext属性平铺展示
      attrs = Object.assign(attrs, {
        ...(ext)
      })
    }
  }

  ;(window as any).$klook.$inhouse.track(type, el, attrs)
}
