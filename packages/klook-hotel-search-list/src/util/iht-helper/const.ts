// https://klook.larksuite.com/docx/OtEadaTCkoSLxSxlMlTuEhmBs1d

export type PageSpm =
  'HotelAPI_ActivityOverlay' |
  'HotelAPI_BookingDetail' |
  'HotelAPI_ExclusivePrivilegeOverlay' |
  'HotelAPI_HotelChainDetail' |
  'HotelAPI_HotelChainnumberroduction' |
  'HotelAPI_HotelChainListing' |
  'HotelAPI_HotelDetail' |
  'HotelAPI_HotelDetailOtherInfoOverlay' |
  'HotelAPI_HotelFilterOverlay' |
  'HotelAPI_ListingMap' |
  'HotelAPI_DetailMap' |
  'HotelAPI_HotelListing' |
  'HotelAPI_HotelVoucherDetail' |
  'HotelAPI_HotelVoucherDetail' |
  'HotelAPI_HotelVoucherReservation' |
  'HotelAPI_PackageDeals' |
  'HotelAPI_PackagesDetail' |
  'HotelAPI_Payment' |
  'HotelAPI_PaymentApplyForRefund' |
  'HotelAPI_PaymentDetail' |
  'HotelAPI_PaymentRefundDetail' |
  'HotelAPI_PictureFloat' |
  'HotelAPI_Promotion' |
  'HotelAPI_HotelChainIntroduction' |
  'HotelAPI_RatePlanDetailOverlay' |
  'HotelAPI_RateplanOverlay' |
  'HotelAPI_SearchStart' |
  'HotelAPI_StayDetail' |
  'HotelAPI_StayPlus' |
  'HotelAPI_StayPlusListing' |
  'HotelAPI_StaySelectActivity' |
  'HotelAPI_StaySelectRoom' |
  'HotelAPI_Vertical' |
  'HotelAPI_RoomDetailOverlay' |
  'Staycation_CitySelection' |
  'Staycation_Vertical' |
  'StayPlus_DestinationOverlay' |
  'StayPlus_DescriptionOverlay' |
  'HotelAPI_HotelInfoOverlay' |
  'HotelAPI_HotelReview' |
  'HotelAPI_CityHubPage' |
  'HotelAPI_PaymentFreeUpgradeSuccess' |
  'HotelAPI_PaymentFreeUpgradeFail' |
  'HotelAPI_BookingDetailShareWindow';

export type ObjectIdName =
  'activity' |
  'area' |
  'banner' |
  'booking' |
  'city' |
  'filtername' |
  'hotel' |
  'voucher' |
  'order' |
  'packagetheme' |
  'rateplan' |
  'review' |
  'room' |
  'staycationtheme' |
  'theme' |
  'rate' |
  'package' |
  'poi' |
  'search';

export type ModuleSpm =
'ActiveDealHotel_LIST' |
'ActivityDetail' |
'AddGuest' |
'AddressCard' |
'AddSpecialRequest' |
'AllCity' |
'AmendmentPolicyDetail' |
'Apply' |
'Area' |
'BookBtn' |
'BottomMap' |
'CallUs' |
'ChangeActivity' |
'ChatWithUsBtn' |
'ChangeYourDate' |
'ChangeRoomsNumber' |
'CheckAvailability' |
'CheckRatePlanAvailability' |
'CheckRoomType' |
'Confirm' |
'ContactHotel' |
'CopyHotelAddress' |
'CopyHotelEmail' |
'CopyHotelPhone' |
'CopyOrderNumber' |
'CreditPolicy' |
'DiscountPromotionBannerGetPromoCode' |
'ExclusivePrivilegeBannerDetails' |
'ExclusivePrivilegeBannerDiscount_LIST' |
'ExclusivePrivilegeViewDetails' |
'ExploreMoreActivity_LIST' |
'ExporeMoreActivity_LIST' |
'Filter_LIST' |
'FilterEdit' |
'FilterViewResult' |
'FlexibleDateHints' |
'FlexibleMonth' |
'FlexiblePeriod' |
'FlexibleStayNight' |
'GiftcardSwitch' |
'GiftcardSwitchClose' |
'Hotel_SearchSuggest_LIST' |
'HotelChain' |
'HotelDetail' |
'HotelDetailTag' |
'HotelOffer_LIST' |
'HotelPolicy' |
'HotelPopularFacilities' |
'HotelsNearbyHotel_LIST' |
'HotelsVideo' |
'HotelsWishLIST' |
'HotelListExpand' |
'HotRatePlan_LIST' |
'CloseButton' |
'Location' |
'FloatButton' |
'Poi_Type_LIST' |
'Poi_LIST' |
'HotelVoucher_LIST' |
'HotelVoucherDetail' |
'HotelVoucherPolicy' |
'ImportantInfo' |
'KoreanTnC' |
'LBSChangeCity' |
'ListingHotel_LIST' |
'ListingPackage_LIST' |
'LoginNow' |
'Map' |
'MemberRateTagGold' |
'MemberRateTagMember' |
'Message' |
'Navigation' |
'NoRatePlanMatchTip' |
'OffersTag' |
'Ops_LIST' |
'OpsHotelVoucher_LIST' |
'PackageBookBtn_LIST' |
'PackageComparison' |
'PackageDealsFilter_LIST' |
'PackageRecommended_LIST' |
'PackageTheme_LIST' |
'PackageContent' |
'PopularDestination_LIST' |
'PayBtn' |
'PayDetail' |
'Picture' |
'PriceDetail' |
'PaymentDetail' |
'Promotion' |
'PromotionRateTag' |
'RatePlanBookBtn_LIST' |
'RatePlanChange' |
'RatePlanPhoto' |
'RatePlanPhoto_LIST' |
'RatePlanViewExpand' |
'RatePlanRoomDetail_LIST' |
'RateplanPackageDetail_LIST' |
'ReadReview' |
'RecommendComments' |
'RecommendHotelVoucher_LIST' |
'RefundInfo' |
'RefundPolicy' |
'RefundPolicyDetail' |
'Review' |
'Review_LIST' |
'ReviewFilter_LIST' |
'ReviewScore' |
'RoomDetail' |
'SearchBoxEdit' |
'SearchButton' |
'SearchChangeCity' |
'SearchCheckInOutDate' |
'SearchDate' |
'SearchDestination' |
'SearchGuestAndRoom' |
'SearchGuestAndRoomConfirm' |
'SearchMode' |
'SeeAvailableProperty' |
'SeeHotelFacilitiesInfo' |
'SeeHotelPolicyInfo' |
'SeeRoomFacilitiesInfo' |
'SelectDate' |
'SelectAllCities' |
'SelectRoom' |
'SelectRoomType' |
'ShareBtn' |
'SortByEdit' |
'SortByViewResult' |
'StaycationCityDropdown' |
'StaycationHotel_LIST' |
'StaycationTheme_LIST' |
'StaycationViewDetails' |
'StayEntrance' |
'StayRecommended_LIST' |
'StayRecommendedDescription' |
'SupplyTnC' |
'Theme_LIST' |
'TopListChangeCity_LIST' |
'UpcomingDealHotel_LIST' |
'UseCredits' |
'UseGiftCard' |
'ViewRatePlan' |
'View_More' |
'ViewMoreRoomFacility' |
'ViewBenefitContent' |
'ViewMorePackageRecommended' |
'HotelCard' |
'WhatToExpert' |
'HowToUseIt' |
'FreeUpgradeEntry' |
'FreeUpgradeViewDetail' |
'ConfirmFreeUpgrade' |
'RefundFreeUpgrade' |
'ShareButton' |
'ShareCopyLink' |
'CheckInInfo' |
'PromoteCodeEntry' |
'ListingBanner' |
'HotelCampaignBanner_LIST' |
'HotelName' |
'HighlightsDescription' |
'HotelFacilities' |
'Highlight'

export interface SpmExtraInfoAll extends MpTracking {
  [key: string]: string
}

export interface SpmExtraInfo extends Partial<SpmExtraInfoAll> {
}

export type CustomSpm = 'ApplyErrorMsg' |
'HotelAPI_BookingDetailLoad' |
'HotelAPI_HotelDetailLoad' |
'HotelAPI_HotelListingLoad' |
'HotelAPI_HotelVoucherDetail' |
'HotelAPI_HotelVoucherReservation.PayBtn' |
'HotelAPI_HotelVoucherReservationLoad' |
'HotelAPI_Payment.ErrorMsg' |
'HotelAPI_Payment.PayBtn' |
'HotelAPI_Payment.PayBtn_GenerateOrder' |
'HotelAPI_PaymentLoad' |
'HotelAPI_StayDetailLoad' |
'HotelAPI_StayPlusListingLoad' |
'HotelAPI_StayPlusLoad' |
'HotelAPI_StayPlusLoad' |
'Staycation_VerticalLoad';

// 转成最终的 html💰iht query的key映射
export interface IHTPropObj {
  oid?: string
  ext?: string
  len?: number
  idx?: number
  trg?: 'manual'
  _f?: '1' | string
}

export interface MpTracking {
}

export interface IHTProp {
  spm: PageSpm | ModuleSpm | CustomSpm
  oidName?: ObjectIdName,
  oidValue?: number | string,
  ext?: SpmExtraInfo
  manual?: boolean // 是否是手动触发, 对于page是pageview，对于item的点击， 对于module是曝光
  isOverlay?: boolean // 浮层pageview为了确保clickid正确要加
}
export interface ModuleIHTProp extends IHTProp {
  // module的manual表示是否是手动触发曝光
  spm: ModuleSpm
  idx?: number
  len?: number
  isVirtualItem?: boolean // 是否是虚拟item 默认true
  isClick?: boolean // 对于item是否默认有点击事件
}

export interface PageIHTProp extends IHTProp {
  spm: PageSpm
  isOverlay?: boolean // 是否是浮层，是浮层加上 _f=1
}

export interface TrackIHTProp {
  spm?: CustomSpm
  ext?: SpmExtraInfo
  force?: boolean
}

export interface UpdateIHTProp {
  oidName?: ObjectIdName,
  oidValue?: number | string,
  ext?: SpmExtraInfo
}

export interface IhtHelperTool {
  setElementSpm: (el: HTMLElement, spm: string) => string
  getPageIHT: (ihtProp: PageIHTProp) => {
    'data-spm-page': string
  }
  getModuleIHT: (ihtProp: ModuleIHTProp) => {
    'data-spm-module': string
    'data-spm-virtual-item'?: string
  }
  getPageIHTStr: (ihtProp: PageIHTProp) => string
  getModuleIHTStr: (ihtProp: ModuleIHTProp) => string
  updateBinding: (el: HTMLElement | string, ihtProp: UpdateIHTProp) => void
  track: (type: 'action' | 'pageview' | 'exposure' | 'custom', el: HTMLElement | string, attrs?: TrackIHTProp) => void
}
