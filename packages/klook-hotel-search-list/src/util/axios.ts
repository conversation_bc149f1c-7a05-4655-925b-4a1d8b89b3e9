let requestSuggestCancel: any = null
let requestFilterCancel: any = null

export const getDestinationList= async (vm: any, params = {}) => {
    const res = await vm.$axios?.$get(
      '/v2/hotelapiserv/hotelapi/destination', {
        params,
        throwError: true
      }).catch(() => {})
    return res?.result?.destination || []
  }

  export const getHotelSuggest = (vm: any, params: string | Record<string, string>) => {
    if (requestSuggestCancel) {
      requestSuggestCancel('cancel')
      requestSuggestCancel = null
    }
    if (typeof params === 'string') {
      params = { keyword: params }
    }

    return vm.$axios.$get('/v2/hotelapiserv/hotelapi/suggest', {
      params,
      cancelToken: new vm.$axios.CancelToken(function (cancelFn: any) {
        requestSuggestCancel = cancelFn
      })
    }).then((res: any) => {
      requestSuggestCancel = null
      if (res.success && Array.isArray(res.result?.suggests)) {
        return res.result
      } else {
        return { suggests: [] }
      }
    })
  }

  export const getHotelFilter =  (vm: any, params:  any) => {
    if (requestFilterCancel) {
      requestFilterCancel('cancel')
      requestFilterCancel = null
    }

    // commit('updateUuidTimestamp')
    const headers: any = {
      'K-HOTEL-API-VERSION': 1
    }

    // if (klook.platformMp) {
    //   headers['X-Platform'] = klook.platformMp
    // }

    // session_token: state.uuid,
    return vm.$axios.$post('/v1/hotelapiserv/public/hotel/filter',
      {  ...params },
      {
        throwError: true,
        headers,
        cancelToken: new vm.$axios.CancelToken(function (cancelFn: any) {
          requestFilterCancel = cancelFn
        })
      }
    ).then((res: any) => res.success && res.result
      ? res.result
      : Promise.reject(new Error(res && res.error))
    ).catch(() => ({}))
  }