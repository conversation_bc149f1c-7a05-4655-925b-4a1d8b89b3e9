export function windowScrollTop() {
  return document.documentElement.scrollTop || window.pageYOffset ||
    document.body.scrollTop
}

export const realTypeof = (obj: any) => Object.prototype.toString.call(obj).slice(8, -1).toLocaleLowerCase()

interface QueryString {
  /** @function queryString
   * 内部根据第一个参数类型为 string 或者 object(已过滤函数和数组等) 调用 str2obj 或 obj2str并传参剩余参数 否则返回第一个参数
   *  直接调用即可 例如 queryString('?aaa=123&bbb=456') queryString({aaa: 123})
   */
  (data: any, ...options: Array<boolean | undefined>): any

  /** @function queryString.str2obj
   * @param parseNum: boolean 是否解析number
   * @param deep: boolean 是否把重复出现的key组成一个数组
   * 例如 aaa=aaa&bbb=123&bbb1=456&bbb=789&ddd=123将被解析为
   * {
   *  aaa: 'aaa',
   *  bbb: [123, 789],
   *  bbb1: '456', //parseNum 为false
   *  ddd: 123 //parseNum 为true
   * }
   */
  str2obj(query: string, parseNum?: boolean, deep?: boolean): object

  /** @function queryString.obj2str
   * @param hasQuestion: boolean 是否需要返回的结果以 ？开头（false 不以&开头）
   * @param deep: boolean 是否递归深度解析
   * 例如 {
   *  aaa: 'aaa',
   *  bbb: [123, {bbb1: 456}, [789]],
   *  ccc: {
   *    ddd: 123
   *  }
   * } 将被解析为 aaa=aaa&bbb=123&bbb1=456&bbb=789&ddd=123
   */
  obj2str(params: object, hasQuestion?: boolean, deep?: boolean): string
}

export const queryString: QueryString = Object.assign((data: any, ...options: Array<boolean | undefined>) => {
  switch (realTypeof(data)) {
    case 'object':
      return queryString.obj2str(data as object, ...options)
    case 'string':
      return queryString.str2obj(data as string, ...options)
    default:
      return data
  }
}, {
  str2obj(query: string, parseNum = false, deep: boolean = false): object {
    return query.slice(query.indexOf('?') + 1).split('&').reduce((res, str: string) => {
      // eslint-disable-next-line prefer-const
      let [key, val] = str.split('=')
      if (parseNum && /^-?\d+(\.\d+)?$/.test(val)) {
        // @ts-ignore
        val = +val
      }
      if (!res[key] || !deep) {
        res[key] = val
      } else if (Array.isArray(res[key])) {
        res[key].push(val)
      } else {
        res[key] = [res[key], val]
      }
      return res
    }, ({} as any))
  },
  obj2str(params: { [key: string]: any }, hasQuestion = false, deep = false): string {
    let res = ''
    for (const key in params) {
      let val = params[key]
      if (!val && typeof val !== 'number') {
        val = ''
      }
      res += '&' + (
        deep && typeof val === 'object'
          ? Array.isArray(val)
            ? val.map((item: any) => queryString.obj2str({ [key]: item }, false, true)).join('&')
            : queryString.obj2str(val, false, true)
          : `${key}=${val.toString()}`
      )
    }
    return res.replace(/^&/, hasQuestion ? '?' : '')
  }
})

/** 截取 querstring 单个字段的值
 * @param name: string
 * @param query?: string 被解析的字符串，没有传 并且在客户端会按照location.search 解析
 */
export function parseQueryString(name: string, query?: string): string {
  if (!query) {
    if (arguments.length === 1 && typeof window === 'object') {
      query = window.location.search
    } else {
      return ''
    }
  }
  const [, res = ''] = new RegExp('[?|&]*' + name + '=' + '([^&;]+?)(&|#|;|$)').exec(query) || []
  return res && decodeURIComponent(res.replace(/\+/g, '%20'))
}


export const mockAHrefClick = (href = '') => {
  const a = document.createElement('a')
  a.href = href
  a.target = '_blank'
  Object.assign(a.style, { opacity: 0, position: 'absolute', left: '-10000px' })
  document.body.appendChild(a)
  a.click()
  setTimeout(() => document.body.removeChild(a))
}

/**
 * 千分位格式化
 *
 * example:
 * 10000 -> 10,000
 */
export function formatPriceThousands(value: number | string) {
  const price = (value || '0').toString()
  let result = ''

  if (!price.includes('.')) {
    result = price.replace(/(?=(?!(\b))(\d{3})+$)/g, ',')
  } else {
    const terms = price.split('.')
    result = `${terms[0].toString().replace(/(?=(?!(\b))(\d{3})+$)/g, ',')}.${terms[1]}`
  }

  return result
}

export const dateQueryType = (data: any): { onlyDate: boolean } => {
  const { calendar_type, check_in, check_out, flexible_day } = data
  return {
    onlyDate: (!calendar_type || (calendar_type === 1 && !flexible_day)) && !!check_in && !!check_out // 列表页固定日期是分页，其余全是无限加载
  }
}