import { DirectiveOptions } from 'vue'
/*
增加 vue 自定义指令，解决 mobile web 浮层滚动穿透问题。
 */

function fix() {
  document.body.className += ' no_scroll '
}

function noFix() {
  const className = document.body.className
  const index = className.indexOf('no_scroll')
  if (index > -1) {
    document.body.className = className.replace(' no_scroll ', '')
  }
}

const directive: DirectiveOptions = {
  bind(_el, { value }, vnode) {
    if (value) {
      fix.apply(vnode.context)
    }
  },
  update(_el, { value, oldValue }, vnode) {
    if (value !== oldValue) {
      if (value) {
        fix.apply(vnode.context)
      } else {
        noFix.apply(vnode.context)
      }
    }
  },
  unbind(_el, { value }) {
    // 在spa 模式下 切换路由造成实例销毁  body.style.position 还是fixed 无法滚动
    value && noFix()
  }
}

export default directive
