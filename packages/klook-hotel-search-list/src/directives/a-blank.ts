import { DirectiveOptions } from 'vue'
import { mockAHrefClick } from '../util/index'

function mockHref(e: any) {
  const target = e.target

  if (target && target.nodeName.toLowerCase() === 'a' && target.href && target.href !== 'javascript:;') {
    const target = e.target
    e.preventDefault()

    mockAHrefClick(target.href)
    return false
  }
}
const directive: DirectiveOptions = {
  bind(el) {
    el.addEventListener('click', mockHref)
  },
  unbind(el) {
    el.removeEventListener('click', mockHref)
  }
}

export default directive
