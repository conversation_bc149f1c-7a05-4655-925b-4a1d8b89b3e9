@mixin scrollbar {
    overflow-y: auto;
     // for firefox
     scrollbar-width: thin;
     scrollbar-color: transparent transparent;
  
     /* for others 定义滚动条高宽及背景 高宽分别对应横竖滚动条的尺寸*/
     &::-webkit-scrollbar {
      width: 5px;
      height: 0;
    }
    /*定义滚动条轨道 内阴影+圆角*/
    &::-webkit-scrollbar-track {
      border-radius: 2px;
      background-color: transparent;
    }
    /*定义滑块 内阴影+圆角*/
    &::-webkit-scrollbar-thumb {
      border-radius: 4px;
      border-right: 1px solid transparent;
      box-shadow: 4px 0 0 $color-neutral-600 inset;
      visibility: hidden;
    }
    &:hover {
      // for firefox
      scrollbar-color: $color-neutral-600 transparent;
      // for others 
      &::-webkit-scrollbar-thumb {
        visibility: initial;
      }
    }
  }

// 文本溢出隐藏
@mixin text-ellipsis($lines: 1) {
  @if $lines >1 {
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: $lines;
      -webkit-box-orient: vertical;
  }

  @else {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
  }
}