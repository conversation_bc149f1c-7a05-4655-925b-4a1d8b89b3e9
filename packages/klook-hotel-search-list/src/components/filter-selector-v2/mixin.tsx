import { Vue, Component, Model, Prop, Watch } from 'vue-property-decorator'
import { IconNext, IconCautionCircle, IconStar } from '@klook/klook-icons'
import { SubFilterItem, HotelFilter } from '../../../types/index.d'
import PriceFilter from './components/price'
import WrapTag from './components/wrap-tag'
import ListWrap from '../list-wrap'
import ModalWrap from '../modal-wrap.vue'
import PayLaterLayer from '../pay-later/layer.vue'

// price|0-1200
export const priceRegTestFn = RegExp.prototype.test.bind(/^price\|\d+-\d+\+?$/)

@Component({
  inheritAttrs: false,
  components: {
    PriceFilter,
    ListWrap,
    ModalWrap,
    WrapTag,
    IconNext,
    IconCautionCircle,
    IconStar,
    PayLaterLayer
  }
})
export default class FilterMixin extends Vue {
  @Prop({ type: Function, default: null }) getHotelFilter !: Function
  @Model('change', { type: Array, default: () => [] }) value!: string[]
  @Prop({ type: Boolean, default: false }) isOnlyDate!: boolean // 是否是精确搜索
  @Prop({ type: Object, default: null }) paramsInfo!: object

  @Prop({ type: Object, default: () => ({}) }) hotelFilter!: HotelFilter
  @Prop({ type: Object, default: () => null }) hotelFilterCur!: HotelFilter
  @Prop({ type: String, default: '' }) type!: 'area' | '' // mweb area入口打开
  @Prop({ type: Boolean, default: false }) isMobile!: boolean

  readonly PriceId = 'price|'
  readonly SubwayId = 'subway'
  readonly PopularId = 'popular'
  readonly StarId = 'star'
  readonly RatingId = 'rating'

  isCommitChange = false
  localSelectedValue: string[] = []
  priceFilterValue: Array<string|number>= []

  hotelFilterTemp: HotelFilter | null = null // mWeb浮层内和web modal搜索时候的新HotelFilter临时数据

  tabAttrs: {
    data: SubFilterItem, // web是父级临时选择, 全部数据
    subData: SubFilterItem | null // mWeb是子集实时选择
    tabIndex?: number, // web modal选中第几个
    tempSelectedValue?: string[] // mWeb没用这个变量，因为是实时变化
  } | null = null

  get priceMax() {
    return Number(this.filterList.find(({ id }) => id === this.PriceId)?.price_filter?.end) || 0
  }

  get mergeHotelFilterCur() {
    return this.isMobile ? (this.hotelFilterTemp || this.hotelFilterCur) : this.hotelFilterCur
  }

  // @ts-ignore
  get filterList() {
    const key = this.type === 'area' ? 'area_filter_list' : 'filter_list'
    const filterList = this.hotelFilter[key] || []
    if (!this.mergeHotelFilterCur) {
      return filterList
    }
    return this.mergeFilterListReduce(JSON.parse(JSON.stringify(filterList)), this.mergeHotelFilterCur[key] || [])
  }

  // web只在modal中显示
  get currentResultCount() {
    return (this.isMobile ? (this.hotelFilterTemp || this.hotelFilterCur || this.hotelFilter)?.count
      : (this.hotelFilterTemp ? this.hotelFilterTemp.count : (this.hotelFilterCur || this.hotelFilter).count)) ||
    0
  }

  get resultCountText() {
    if (!this.isOnlyDate) {
      return this.$t('hotel.vertical_to_filter')
    }
    const num = Number(this.currentResultCount)
    return num === 0 ? this.$t('118766') : (num > 1 ? this.$t('114753', { num }) : this.$t('118717'))
  }

  @Watch('value', { immediate: true })
  initLocalSelectedValue(value = this.value) {
    if (this.isCommitChange || this.localSelectedValue.toString() === value.toString()) {
      return (this.isCommitChange = false)
    }

    this.localSelectedValue = value.slice()
    this.priceFilterValue = value.find(priceRegTestFn)?.replace(this.PriceId, '').split('-') || []
  }

  get priceFilter() {
    const priceFilter = this.filterList.find(({ id }) => id === this.PriceId)
    return priceFilter?.price_filter || null
  }

  get starPoptipContent() {
    return <div class="star-poptip-content">
      <p class="star-desc">{this.$t('85881')}</p>
      { [5, 4, 3, 2, 1].map((count: number) => {
        return <div class="star-wrap">
          <span>{count + ' ' + (this.$t(count > 1 ? '80218' : '85883'))}</span>
          <div>
            { Array(count).fill(<IconStar size={16} theme="filled" style="margin-right: 2px" />) }
          </div>
        </div>
      })}
    </div>
  }

  isChecked(filterList: SubFilterItem[], filterValue = this.localSelectedValue): boolean {
    return filterValue.length > 0 &&
      filterList?.some(({ id, sub_filter_list }) => sub_filter_list?.length
        ? this.isChecked(sub_filter_list)
        : filterValue.includes(id)
      )
  }

  postFilterValue(needEmitChange = true) {
    if (this.priceMax) {
      const index = this.localSelectedValue.findIndex(priceRegTestFn)
      if (!this.priceFilterValue.length) {
        index > -1 && this.localSelectedValue.splice(index, 1)
      } else {
        const priceValue = this.PriceId + this.priceFilterValue.join('-')
        if (index < 0) {
          this.localSelectedValue.unshift(priceValue)
        } else if (priceValue !== this.localSelectedValue[index]) {
          this.localSelectedValue.splice(index, 1, priceValue)
        }
      }
    }

    if (needEmitChange) {
      this.$emit('change', this.localSelectedValue.slice())
      this.isCommitChange = true
    }
  }

  clearSelected() {
    this.localSelectedValue = []
    this.priceFilterValue = []
    this.fetchFilterList()
  }

  getIdsByFilter(subFilterItem: SubFilterItem) {
    const getSubIds = (subFilterItem: SubFilterItem[]) => {
      return subFilterItem.reduce((res: string[], item) => {
        if (item.sub_filter_list) {
          res.push(...getSubIds(item.sub_filter_list))
        } if (item.popular_facility_list) {
          res.push(...getSubIds(item.popular_facility_list))
        } else {
          res.push(item.id)
        }
        return res
      }, [])
    }

    return getSubIds(subFilterItem.sub_filter_list || []) as string[]
  }

  clearSingleFilter(subFilterItem: SubFilterItem) {
    if (subFilterItem.id === this.PriceId) {
      this.priceFilterValue = []
    } else {
      const length = this.localSelectedValue.length
      const currentFilterIds = this.getIdsByFilter(subFilterItem)
      for (let i = length - 1; i >= 0; i--) {
        if (currentFilterIds.includes(this.localSelectedValue[i])) {
          this.localSelectedValue.splice(i, 1)
        }
      }
    }
    this.fetchFilterList()
  }

  /**
   *
   * @param filterId rating目前只能单选
   */
  selectTag(filterId: string) {
    const ratingReg = RegExp.prototype.test.bind(/^rating\|\d+(\.)?(\d+)?\+?$/)
    if (ratingReg(filterId)) {
      const index = this.localSelectedValue.findIndex(ratingReg)
      if (filterId === this.localSelectedValue[index]) {
        this.localSelectedValue.splice(index, 1)
      } else {
        index > -1
          ? this.localSelectedValue.splice(index, 1, filterId)
          : this.localSelectedValue.push(filterId)
      }
    } else {
      const index = this.localSelectedValue.indexOf(filterId)
      index > -1
        ? this.localSelectedValue.splice(index, 1)
        : this.localSelectedValue.push(filterId)
    }
    this.fetchFilterList(filterId)
  }

  fetchFilterList(filterId: string = '') {
    // 如果操作的是价格筛选，则需要传递当前价格price_filter为入参
    const param = priceRegTestFn(filterId) ? {
      price_filter: this.priceFilter
    } : {}
    if (!this.isMobile) {
      this.postFilterValue()
      this.isOnlyDate && this.$emit('fetch-filter', param)
      return
    }
    if (!this.isOnlyDate) {
      return
    }
    this.getHotelFilter(Object.assign(param, {
      filter_list: this.localSelectedValue.concat((this as any).filterSelected[this.type === 'area' ? 'filter' : 'area'])
    }, this.paramsInfo))
      .then((res) => {
        this.setHotelFilterTempByData(res)
      })
  }

  renderPrice(subFilterItem: SubFilterItem) {
    const { price_filter } = subFilterItem
    if (!price_filter) {
      return null
    }
    const vNode = <price-filter
      ref="priceFilter"
      priceFilter={price_filter}
      value={this.priceFilterValue}
      onChange={this.priceChange}
      isMobile={this.isMobile} />

    return this.renderSectionWrap(subFilterItem, vNode)
  }

  priceChange(value: number[]) {
    this.priceFilterValue = value
    this.postFilterValue(false)
    this.fetchFilterList(this.PriceId + this.priceFilterValue.join('-'))
  }

  getListWrapAttrs(subFilterItem: SubFilterItem) {
    const length = subFilterItem?.sub_filter_list!.length
    const isSubway = subFilterItem.id === this.SubwayId
    const isPopular = subFilterItem.id === this.PopularId
    return isPopular ? {} : {
      splitIndex: this.isMobile ? (isSubway ? 15 : 5) : 3,
      viewMoreText: this.isMobile ? this.$t('23924') : this.$t('117939', { num: length }),
      viewLessText: this.$t('28961'),
      isCustomExpand: (!this.isMobile && length >= 30) || isSubway
    }
  }

  renderCheckBoxSingle(subFilterItem: SubFilterItem, index: number | string, {
    style,
    customChange,
    selectedValue
  }: {
    style?: any,
    customChange?: Function
    selectedValue?: string[]
  } = {}) {
    const { id, title, disabled, icon_url, pay_later_terms_conditions } = subFilterItem
    const checked = (selectedValue || this.localSelectedValue).includes(id)

    const isStayplus = id === 'stayplus|1'
    return <klk-checkbox
      style={style}
      class={['filter-tag', isStayplus && 'stayplus-icon']}
      key={`${id}-${index}`}
      disabled={checked ? false : !!disabled}
      value={checked}
      onChange={() => {
        if (customChange) {
          return customChange()
        }
        this.selectTag(id)
        !this.isMobile && this.postFilterValue()
      }}
    >
      { isStayplus && icon_url ? <img src={icon_url} /> : title }
      { pay_later_terms_conditions && <pay-later-layer data={pay_later_terms_conditions} isMobile={this.isMobile} /> }
    </klk-checkbox>
  }

  renderCheckbox(subFilterItem: SubFilterItem) {
    const { sub_filter_list } = subFilterItem
    if (!sub_filter_list) {
      return null
    }

    const listWrapAttrs = this.getListWrapAttrs(subFilterItem)

    const vNode = <list-wrap
      class="filter-wrap--checkbox"
      attrs={{ ...listWrapAttrs }}
      onExpand={() => listWrapAttrs.isCustomExpand && this.tabAdd(subFilterItem)}>{sub_filter_list.map((item: SubFilterItem, index) => this.renderCheckBoxSingle(item, index))}</list-wrap>

    return this.renderSectionWrap(subFilterItem, vNode)
  }

  tabAdd(subFilterItem: SubFilterItem, curClickItem?: SubFilterItem, tabIndex: number = 0) {
    this.tabAttrs = {
      data: subFilterItem,
      subData: curClickItem || null,
      tabIndex,
      tempSelectedValue: this.localSelectedValue.slice()
    }
  }

  // 在二级浮层选择tag
  tabSelectTag(tagId: string | boolean) {
    const isClearTags = tagId === false
    // mweb二级浮层没有clear
    if (this.isMobile) {
      this.selectTag(tagId as string)
    } else {
      let tempSelectedValue: string[] = []

      if (isClearTags) {
        const currentTypeFilterIds = this.getIdsByFilter(this.tabAttrs?.data!)
        this.localSelectedValue.forEach(item => !currentTypeFilterIds.includes(item) && tempSelectedValue.push(item))
      } else {
        tempSelectedValue = this.tabAttrs!.tempSelectedValue!
        const index = tempSelectedValue.indexOf(tagId as string)
        index > -1
          ? tempSelectedValue!.splice(index, 1)
          : tempSelectedValue!.push(tagId as string)
      }
      this.$set(this.tabAttrs!, 'tempSelectedValue', tempSelectedValue)

      if (!this.isOnlyDate) {
        return
      }

      this.getHotelFilter(Object.assign({}, {
        filter_list: tempSelectedValue
      }, this.paramsInfo))
        .then((res) => {
          this.setHotelFilterTempByData(res as any, tempSelectedValue)
        })
    }
  }

  // tabSelectTag时候才会传tabSelectedValue
  setHotelFilterTempByData(res: any, tabSelectedValue?: string[]) {
    const { filter_selected, filter_list } = res
    if (filter_list) {
      // 无数据时候用上一次数据的值, 不重新赋值
      this.hotelFilterTemp = res
    }

    if (filter_selected) {
      if (tabSelectedValue) {
        tabSelectedValue = Array.from(new Set(tabSelectedValue.concat(filter_selected)))
        this.$set(this.tabAttrs!, 'tempSelectedValue', tabSelectedValue)
      } else {
        this.localSelectedValue = Array.from(new Set(this.localSelectedValue.concat(filter_selected)))
      }
    }

    // tab下选择时候可能当前tab数据已经被更新了, 还要和hotelFilter主数据比较返回
    if (this.tabAttrs && filter_list) {
      const newData = filter_list.find((item: { id: string | undefined }) => item.id === this.tabAttrs?.data.id)
      const baseData = this.hotelFilter!.filter_list!.find((item: { id: string | undefined }) => item.id === this.tabAttrs?.data.id)
      newData && this.$set(this.tabAttrs!, 'data', (this.mergeFilterListReduce(JSON.parse(JSON.stringify([baseData])), [newData]))[0])
    }
  }

  renderTag(subFilterItem: SubFilterItem) {
    const { sub_filter_list, id } = subFilterItem
    if (!sub_filter_list) {
      return null
    }

    const vNode = this.renderTagPure(subFilterItem)
    return this.renderSectionWrap(subFilterItem, vNode, {
      titleVNode: id === this.StarId && (this as any).starPoptipVnode
    })
  }

  renderTagPure(subFilterItem: SubFilterItem, attrs: any = {}) {
    return <wrap-tag subFilterItem={subFilterItem} localSelectedValue={this.localSelectedValue} class={attrs.class || ''} isMobile={this.isMobile} onSelect={(id: string) => {
      this.selectTag(id)
      !this.isMobile && this.postFilterValue()
    }}></wrap-tag>
  }

  renderColumnInfo(subFilterItem: SubFilterItem) {
    const { sub_filter_list } = subFilterItem
    if (!sub_filter_list) {
      return null
    }

    const listWrapAttrs = this.getListWrapAttrs(subFilterItem)

    const vNode = <list-wrap
      class="filter-wrap--column-info"
      attrs={{ ...listWrapAttrs }}
      onExpand={() => listWrapAttrs.isCustomExpand && this.tabAdd(subFilterItem)}>{sub_filter_list.map((item: SubFilterItem, index) => <div class={{ 'column-info-item': true, checked: this.isChecked(item.sub_filter_list!), disabled: item.disabled }} key={index} onClick={() => this.tabAdd(subFilterItem, item, index)}>
        <span class="column-info-item--title">{ item.title}</span>
        <icon-next size={16} />
      </div>)}</list-wrap>

    return this.renderSectionWrap(subFilterItem, vNode)
  }

  renderSectionWrap(subFilterItem: SubFilterItem, vNode: JSX.Element | null, {
    titleVNode
  }: {
    titleVNode?: JSX.Element // 评星的tip
  } = {}) {
    if (!vNode) {
      return
    }

    const { id, title, popular_facility_list } = subFilterItem

    const checked = subFilterItem.id === this.PriceId
      ? !!this.priceFilterValue.length
      : this.isChecked(subFilterItem.sub_filter_list!.concat(popular_facility_list || []))

    return <div class={['filter-section', checked && 'checked', id]}>
      <div class="filter-section--title">
        <span>{title}{titleVNode}</span>
        {!this.isMobile && checked && <span class="filter-clear" onClick={() => this.clearSingleFilter(subFilterItem)}>{this.$t('clear')}</span>}
      </div>

      { !!popular_facility_list?.length && this.renderTagPure({ id: '', sub_filter_list: popular_facility_list }, { class: popular_facility_list?.length >= 3 ? 'multiple' : '' }) }

      {vNode}
    </div>
  }

  renderMainContent() {
    return <div
      class={['filter-container', this.isMobile ? 'mobile' : 'desktop']}
    >
      {
        this.filterList?.map(
          (filterItem) => {
            const id = filterItem?.id
            const uiType = filterItem?.ui_type

            if (id === this.PriceId) {
              return this.renderPrice(filterItem)
            } else if (id === this.StarId) {
              return this.renderTag(filterItem)
            } else if (uiType) {
              const uiRender = ['Tag', 'Tag', 'ColumnInfo']
              return (this as any)[`render${uiRender[uiType - 1]}`](filterItem)
            }
            return this.renderCheckbox(filterItem)
          }
        )
      }
    </div>
  }

  mergeFilterListReduce(filterList: SubFilterItem[], curFilterList: SubFilterItem[]) {
    return filterList.reduce((res: SubFilterItem[], baseFilter: SubFilterItem) => {
      const cur = curFilterList.find(item => item.id === baseFilter.id) as SubFilterItem

      if (this.StarId === baseFilter.id) {
        res.push({
          ...cur,
          sub_filter_list: this.disabledSubFilterList(cur.sub_filter_list, (itemFilter: SubFilterItem) => itemFilter.sub_title?.match(/(\d+)/)?.[1] === '0')
        })
        return res
      } else if ([this.PriceId, this.RatingId].includes(baseFilter.id)) {
        cur && res.push(cur)
        return res
      } else if (!cur) {
        res.push({
          disabled: true,
          ...baseFilter,
          popular_facility_list: this.disabledSubFilterList(baseFilter.popular_facility_list || [], () => true),
          sub_filter_list: this.disabledSubFilterList(baseFilter.sub_filter_list, () => true)
        })
        return res
      }

      if (baseFilter.sub_filter_list) {
        baseFilter.sub_filter_list = baseFilter.sub_filter_list.reduce((subRes: SubFilterItem[], subBaseFilter: SubFilterItem) => {
          const subCur = (cur.sub_filter_list as SubFilterItem[]).find(item => item.id === subBaseFilter.id)

          // 地铁二级等
          if (subBaseFilter.sub_filter_list?.length) {
            subBaseFilter.sub_filter_list = this.mergeFilterListReduce(subBaseFilter.sub_filter_list, subCur?.sub_filter_list || [])
          }

          subBaseFilter.disabled = !subCur
          subRes.push(subBaseFilter)
          return subRes
        }, [])
      }

      if (baseFilter.popular_facility_list) {
        baseFilter.popular_facility_list = this.disabledSubFilterList(baseFilter.popular_facility_list, (itemFilter: SubFilterItem) => {
          return cur.popular_facility_list && cur.popular_facility_list?.length > 0 ? !(cur.popular_facility_list || []).find(item => item.id === itemFilter.id) : true
        })
      }
      res.push(baseFilter)

      return res
    }, [])
  }

  disabledSubFilterList(filterList: SubFilterItem[] | null, fn: (item: SubFilterItem) => Boolean): SubFilterItem[] {
    return (filterList || []).reduce((res: SubFilterItem[], curFilter: SubFilterItem) => {
      res.push({
        ...curFilter,
        disabled: !!fn(curFilter)
      })
      return res
    }, [])
  }

  isTabSingleAllChecked() {
    if (!this.tabAttrs) {
      return {
        value: false,
        indeterminate: false
      }
    }
    let currentTabFilterIds = []
    let checkedNum = 0

    if (this.isMobile) {
      currentTabFilterIds = this.getIdsByFilter(this.tabAttrs!.subData as SubFilterItem)
      checkedNum = currentTabFilterIds.filter(id => this.localSelectedValue.includes(id)).length
    } else {
      currentTabFilterIds = this.getIdsByFilter((this.tabAttrs!.data!.sub_filter_list as SubFilterItem[])[this.tabAttrs!.tabIndex!] as SubFilterItem)
      checkedNum = currentTabFilterIds.filter(id => (this.tabAttrs!.tempSelectedValue || []).includes(id)).length
    }

    return {
      value: checkedNum === currentTabFilterIds.length,
      indeterminate: checkedNum > 0 && checkedNum < currentTabFilterIds.length
    }
  }

  handleTabCheckAllChange(value: boolean) {
    if (this.isMobile) {
      if (value) {
        const currentTabFilterIds = this.getIdsByFilter(this.tabAttrs!.subData as SubFilterItem)
        this.localSelectedValue = Array.from(new Set([...this.localSelectedValue, ...currentTabFilterIds]))
        this.fetchFilterList()
      } else {
        this.clearSingleFilter(this.tabAttrs!.subData as SubFilterItem)
      }
    } else {
      const currentTabFilterIds = this.getIdsByFilter((this.tabAttrs!.data!.sub_filter_list as SubFilterItem[])[this.tabAttrs!.tabIndex!] as SubFilterItem)
      let tempSelectedValue: string[] = []

      if (value) {
        tempSelectedValue = Array.from(new Set([...this.tabAttrs?.tempSelectedValue || [], ...currentTabFilterIds]))
      } else {
        tempSelectedValue = (this.tabAttrs?.tempSelectedValue || [])!.filter((id: string) => !currentTabFilterIds.includes(id))
      }
      this.$set(this.tabAttrs!, 'tempSelectedValue', tempSelectedValue)
      if (!this.isOnlyDate) {
        return
      }

      this.getHotelFilter(Object.assign({}, {
        filter_list: tempSelectedValue
      }, this.paramsInfo))
        .then((res) => {
          this.setHotelFilterTempByData(res as any, tempSelectedValue)
        })
    }
  }
}
