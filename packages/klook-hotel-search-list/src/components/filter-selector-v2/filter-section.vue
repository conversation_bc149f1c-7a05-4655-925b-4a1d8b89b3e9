<script lang="tsx">
import { Component, Prop } from 'vue-property-decorator'
import { SubFilterItem } from '../../../types/index.d'
import FilterMixin from './mixin'

@Component({
  components: {
  }
})
export default class FilterSection extends FilterMixin {
  @Prop({ default: '', type: String }) page!: 'filterMap' | '' // 普通还是通过map打开的
  @Prop({ default: false, type: Boolean }) filterPin!: boolean

  @Prop({ type: Object, default: () => ({}) }) filterMap!: {
    filter_list: SubFilterItem[]
    area_filter_list: SubFilterItem[]
  }

  get starPoptipVnode() {
    return <klk-poptip
      width={390}
      maxHeight={500}
      zIndex={2002}
      offset={[-10, 10]}
      placement={this.page === 'filterMap' ? 'right-start' : 'top-start'}
      append-to-body
    >
      <icon-caution-circle size="16" style="cursor: pointer;margin-left: 4px;color: #757575;" />
      <div slot="content" class="star-poptip-content">
        {this.starPoptipContent}
      </div>
    </klk-poptip>
  }

  mounted() {

  }

  beforeDestroy() {
  }

  render() {
    return <div class={{ 'filter-container-outer': true, 'filter-pin': this.filterPin, 'has-selected': this.value.length > 0 }}>
      <div class="filter-container-wrap">
        {this.renderMainContent()}
        {this.renderFilterTagList()}
      </div>
      {this.renderTabs()}
    </div>
  }

  renderFilterTagList() {
    return this.value.length > 0 && <div class="filter-tag-list">
      <div class="filter-tag--title">
        <span>{this.$t('125031')}<span class="filter-tag--count">&nbsp;&nbsp;( {this.value.length} )</span></span>
        <span class="filter-clear" onClick={this.clearSelected}>{this.$t('clear')}</span>
      </div>
      {this.$slots.default}
    </div>
  }

  // 渲染tab（浮层和切换和web的modal）
  renderTabs() {
    if (!this.tabAttrs) {
      return
    }
    const { data: { sub_filter_list, title, id } } = this.tabAttrs!
    const noTabs = !sub_filter_list?.[0].sub_filter_list?.length

    return <klk-modal
      title={title}
      width={750}
      zIndex={3000}
      open={!!this.tabAttrs}
      closable={true}
      overlay-closable={true}
      scrollable={noTabs}
      modal-class={`list-filter-modal ${noTabs ? 'no-tabs' : 'with-tabs'}`}
      ref={`modal-${id}`}
      onClose={this.onTabClose}
      onOn-Close={this.onTabClose}
    >
      {
        noTabs ? sub_filter_list!.map((item: SubFilterItem, index) => this.renderCheckBoxSingle(item, index, {
          selectedValue: this.tabAttrs!.tempSelectedValue,
          customChange: () => this.tabSelectTag(item.id)
        })) : <div class="filter-layer-tabs">
          <div class="filter-layer-nav">
            {
              sub_filter_list!.map(
                ({ title }, index) => <div class={['nav-item', this.tabAttrs!.tabIndex === index && 'active']} onClick={() => this.onTabTitleSelect(index)}>{title}</div>)
            }
          </div>
          <div class="filter-layer-content">
            <klk-checkbox
              class="filter-tag"
              attrs={this.isTabSingleAllChecked()}
              onChange={this.handleTabCheckAllChange}
            >{this.$t('389')}</klk-checkbox>
            {
              sub_filter_list!.map(
                ({ sub_filter_list }, index: number) => sub_filter_list!.map((item: SubFilterItem, index2) => this.renderCheckBoxSingle(item, `${index}-${index2}`, {
                  style: { display: index !== this.tabAttrs!.tabIndex ? 'none' : undefined },
                  selectedValue: this.tabAttrs!.tempSelectedValue,
                  customChange: () => this.tabSelectTag(item.id)
                })))
            }
          </div>
        </div>
      }
      <div slot="footer" class="footer">
        <klk-button disabled={this.tabCurrentSelected?.length === 0} class="filter-clear-btn" type="outlined" onClick={this.onTabCancel}>
          {this.$t('clear')}
        </klk-button>
        <klk-button
          class="filter-confirm-btn"
          disabled={this.currentResultCount === '0'}
          {...{ directives: [{ name: 'galileo-click-tracker', value: { spm: 'FilterViewResult' } }] }}
          attrs={this._$ihtHelper.getModuleIHT({ spm: 'FilterViewResult', manual: true })}
          onClick={this.onTabConfirm}
        >
          {this.resultCountText}
        </klk-button>
      </div>
    </klk-modal>
  }

  get tabCurrentSelected() {
    if (!this.tabAttrs) {
      return []
    }
    const currentFilterIds = this.getIdsByFilter(this.tabAttrs?.data!)
    const curTypeIds: string[] = []
    this.tabAttrs!.tempSelectedValue!.forEach(item => currentFilterIds.includes(item) && curTypeIds.push(item))
    return curTypeIds
  }

  onTabTitleSelect(index: number) {
    const tabContent = document.querySelector('.list-filter-modal .filter-layer-content') as HTMLElement
    tabContent && (tabContent.scrollTop = 0)
    this.$set(this.tabAttrs!, 'tabIndex', index)
  }

  onTabClose() {
    this.tabAttrs = null
    this.hotelFilterTemp = null
  }

  onTabCancel() {
    this.tabSelectTag(false)
  }

  onTabConfirm() {
    if (this.isOnlyDate) {
      this.$emit('set-filter', this.hotelFilterTemp)
    }
    this.localSelectedValue = this.tabAttrs!.tempSelectedValue!
    this.tabAttrs = null
    this.hotelFilterTemp = null
    this.postFilterValue()
  }
}
</script>

<style lang="scss" scoped>
@import '../../style/variables';

@import './filter.scss';

.filter-container-outer {
  width: 300px;
  border-radius: $radius-xl;
  background-color: $color-bg-widget-normal;
  @include scrollbar;

  &.map-filter {
    border: solid 1px $color-border-dim;

    .filter-container-wrap {
      display: flex;
      flex-direction: column-reverse;
    }

    &.has-selected {
      .filter-section {
        &:first-child {
          &::before {
            content: '';
            position: absolute;
            top: 0;
            left: 12px;
            width: 100%;
            height: 1px;
            background-color: $color-border-dim;
          }
        }
      }
    }

    .filter-tag-list {
      margin-top: 0;
      display: block;
      position: sticky;
      top: 0;
      z-index: 1;
      background-color: $color-bg-1;

      .filter-tag--count {
        display: inline-block;
      }

      .filter-tag--title {
        margin-bottom: 0;
      }

      .filter-clear {
        @include font-body-m-regular;
      }

      ::v-deep .filter-section-selected---tag-list {
        .filter-tag {
          @include font-caption-m-semibold;
        }
      }
    }
  }

  &.filter-pin {
    .filter-section:not(.popular) {
      display: none;
    }

    .filter-tag-list {
      display: block;
    }
  }

  .filter-clear {
    float: right;
    flex-shrink: 0;
    @include font-body-s-regular;
    text-decoration: underline;
    cursor: pointer;
    margin-right: 0 !important;
  }

  .filter-container.desktop {
    .filter-section {
      padding: 0 12px 20px;
      position: relative;

      &::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 12px;
        width: calc(100% - 24px);
        height: 1px;
        background-color: $color-border-dim;
      }

      &.section-header {
        position: sticky;
        top: 0;
        z-index: 1;
        background-color: $color-bg-1;

        .filter-section--title {
          margin-bottom: 0;
        }
      }

      &:last-child::after {
        content: none;
      }

      .filter-section--title {
        @include font-body-m-bold;
        padding: 20px 0 12px;

        >span {
          margin-right: 4px;
        }
      }

      &.checked {
        .filter-section--title {
          &>span:first-child::after {
            content: none;
          }
        }

        ::v-deep .list-wrap-view-more {
          &>span:first-child {
            &::after {
              content: '';
            }
          }
        }
      }
    }
  }

  // filter pin的tagList
  .filter-tag-list {
    display: none;
    padding: 0 12px 20px;
    margin-top: -20px;
    position: relative;
    background: #ffffff;

    .filter-tag--title {
      @include font-body-m-bold;
      padding: 20px 0 12px;

      >span {
        margin-right: 4px;
      }
    }

    .filter-tag--count {
      display: none;
    }

    ::v-deep .filter-section-selected---tag-list {
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      margin-bottom: -8px;

      .filter-tag--count {
        display: none;
      }

      .klk-link {
        display: none;
      }

      .filter-tag--delete {
        display: none;
      }

      // 这里直接copy mWeb 的外部tagList样式
      .filter-tag {
        max-width: 100%;
        margin: 0 8px 8px 0;
        @include font-body-s-regular;
        border-radius: $radius-m;
        padding: 8px 12px;
        display: flex;
        align-items: center;
        background: $color-bg-page;
        background: $color-bg-widget-normal;
        border: 1px solid $color-border-normal;

        &:last-child {
          margin-right: 0;
        }

        .filter-tag-name {
          flex: 1;
          align-items: center;
          @include text-ellipsis;
        }

        &>.filter-tag-icon {
          height: 14px;
          width: auto;
          flex: none;
        }

        >.i-icon-icon-star {
          margin-top: -2px;
          flex: none;
          margin-left: 4px;
          color: $color-accent-9;
        }

        .i-icon-icon-close {
          cursor: pointer;
          margin-top: -1px;

          svg {
            width: 14px;
            height: 14px;
          }
        }
      }
    }
  }
}
</style>

<style lang="scss">
.list-filter-modal {
  .klk-modal-title {
    margin-bottom: 24px;
  }

  .klk-modal-body {
    height: 45vh;
  }

  .klk-checkbox {
    .klk-checkbox-label {
      flex: 1;
    }
  }

  &.no-tabs {
    .filter-tag {
      padding: 8px;
      margin: 0;
      width: calc(50% - 6px);

      &:hover {
        border-radius: $radius-l;
        background-color: $color-bg-2;
      }
    }
  }

  &.with-tabs {
    .filter-layer-tabs {
      display: flex;
      height: 100%;

      .filter-layer-nav {
        // @damon @include scrollbar scss不生效
        overflow-y: auto;
        // for firefox
        scrollbar-width: thin;
        scrollbar-color: transparent transparent;

        /* for others 定义滚动条高宽及背景 高宽分别对应横竖滚动条的尺寸*/
        &::-webkit-scrollbar {
          width: 5px;
          height: 0;
        }

        /*定义滚动条轨道 内阴影+圆角*/
        &::-webkit-scrollbar-track {
          border-radius: 2px;
          background-color: transparent;
        }

        /*定义滑块 内阴影+圆角*/
        &::-webkit-scrollbar-thumb {
          border-radius: 4px;
          border-right: 1px solid transparent;
          box-shadow: 4px 0 0 $color-neutral-600 inset;
          visibility: hidden;
        }

        &:hover {
          // for firefox
          scrollbar-color: $color-neutral-600 transparent;

          // for others 
          &::-webkit-scrollbar-thumb {
            visibility: initial;
          }
        }

        overflow-x: hidden;
        overflow-y: auto;

        .nav-item {
          cursor: pointer;
          @include font-body-m-regular;
          width: 180px;
          padding: 12px;

          &:hover {
            background-color: $color-bg-2;
            color: $color-brand-primary;
          }

          &.active {
            background-color: $color-bg-2;
            color: $color-brand-primary;
          }
        }
      }

      .filter-layer-content {
        overflow-y: auto;
        // for firefox
        scrollbar-width: thin;
        scrollbar-color: transparent transparent;

        /* for others 定义滚动条高宽及背景 高宽分别对应横竖滚动条的尺寸*/
        &::-webkit-scrollbar {
          width: 5px;
          height: 0;
        }

        /*定义滚动条轨道 内阴影+圆角*/
        &::-webkit-scrollbar-track {
          border-radius: 2px;
          background-color: transparent;
        }

        /*定义滑块 内阴影+圆角*/
        &::-webkit-scrollbar-thumb {
          border-radius: 4px;
          border-right: 1px solid transparent;
          box-shadow: 4px 0 0 $color-neutral-600 inset;
          visibility: hidden;
        }

        &:hover {
          // for firefox
          scrollbar-color: $color-neutral-600 transparent;

          // for others 
          &::-webkit-scrollbar-thumb {
            visibility: initial;
          }
        }

        overflow-x: hidden;
        overflow-y: auto;
        padding: 0 16px;
        display: flex;
        flex-wrap: wrap;
        align-content: flex-start;
        background-color: $color-bg-2;
        flex: 1;

        .filter-tag {
          margin: 0;
          padding: 12px 0;
          width: calc(50% - 6px);

          &:hover {
            color: $color-brand-primary;
          }

          &:nth-child(2n + 1) {
            margin-right: 12px;
          }
        }
      }
    }
  }

  .klk-modal-footer {
    .footer {
      display: flex;
      justify-content: flex-end;
      padding-top: 20px;

      .filter-clear-btn {
        margin-right: 16px;
      }
    }
  }
}
</style>
