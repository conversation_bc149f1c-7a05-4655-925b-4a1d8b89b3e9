import { Vue, Component, Prop, Watch } from 'vue-property-decorator'
import { SubFilterItem } from '../../../../types/index.d'

import {
  formatPriceThousands
} from '../../../util'

@Component({
  components: {
  }
})
export default class PriceFilter extends Vue {
    @Prop({ type: Object, default: null }) priceFilter!: SubFilterItem['price_filter']
    @Prop({ type: Boolean, default: false }) isMobile!: boolean
    @Prop({ type: Boolean, default: true }) changeListener!: boolean // web 直接修改，mWeb不监听
    @Prop({ type: Array, default: () => [] }) value!: Array<string|number>

    priceFilterValue: string[] = []
    error: {
      state: boolean,
      index: 0 | 1 // 哪个位置错误
    } | null = null

    changeTimer: any = null
    hackPriceSliderTimer: any = null // 修复bug：超出price-slide范围时候，会再次渲染触发slide的input，又修正priceFilterValue为 2000+; 而不是用户真正触发的input
    isSliderReachMax = false // 滑动条滑到满，显示+
    isInputExceed = false // 输入开始值大于end, 或结束值小于start, 滑块重合且到一端的尽头

    @Watch('priceFilter', { immediate: true })
    @Watch('value', { immediate: true })
    setPriceValue() {
      const priceValue = this.value
      if (!this.priceFilter) {
        return
      }
      if (priceValue.length) {
        this.isSliderReachMax = this.isEndOfPlus(priceValue[1])
        this.priceFilterValue = priceValue.map(this.getFormatPriceNumber)
        this.hackPriceSliderChange()
      } else {
        this.isSliderReachMax = true
        this.priceFilterValue = this.getPriceMaxRangeValue()
      }
    }

    get priceFilterValueShow() {
      const { end } = this.priceFilter!

      return this.priceFilterValue.map((item: string, index: number) => {
        return `${item}${index === 1 && this.isSliderReachMax && this.getNumber(item) === +end && !this.isEndOfPlus(item) ? '+' : ''}`
      })
    }

    getNumber(value: string | number) {
      return +('' + value).replace(/[^\d]/g, '')
    }

    getFormatPriceNumber(value: string | number) {
      if (!this.priceFilter) {
        return ''
      }

      return `${formatPriceThousands(value.toString().replace('+', ''))}`
    }

    /**
     * 获取PriceSlider的number值
     * 初始默认情况，或超出超出最大最小值范围时候slide的number取值
     * 否则显示为priceFilterValue的值
     */
    getPriceSliderNumber() {
      const { start, end } = this.priceFilter!
      return this.priceFilterValue.map((item, index) => {
        const value = this.getNumber(item)
        return index === 0 ? Math.min(Math.max(value, +start), +end) : Math.min(value, +end)
      })
    }

    // 获取slide原始的最大最小范围格式化的值（含有+）
    getPriceMaxRangeValue() {
      if (!this.priceFilter) {
        return []
      }
      const { start, end } = this.priceFilter
      return [start, end].map((item, index) => {
        return formatPriceThousands(item) + `${index === 1 ? '+' : ''}`
      })
    }

    // priceFilterValue是否当前正好是slide原始范围值（含有+）
    isPriceMaxRange() {
      if (!this.priceFilter) {
        return
      }
      return this.getPriceMaxRangeValue().toString() === this.priceFilterValueShow.toString()
    }

    // price input输入值为空
    isPriceInputEmpty(value: string = '') {
      return value.trim() === ''
    }

    render() {
      const { start, end, step, currency_symbol } = this.priceFilter!
      const attrs = {
        max: +end,
        min: +start,
        range: true,
        step: +step
      }

      // 默认最大值或超出最大最小值范围时候
      const value = this.getPriceSliderNumber()

      const listeners: Record<string, Function> = {
        input: (priceArr: number[]) => {
          // 初始值或者input值大于max时候，会再次渲染触发slider的input， 阻止掉
          if (this.isInputExceed && priceArr[0] === priceArr[1]) {
            return
          } else {
            this.isInputExceed = false
          }

          if (this.hackPriceSliderTimer) {
            return
          }
          const isMax = priceArr.join('') === start + end

          this.isSliderReachMax = priceArr[1] === +end
          if (priceArr.toString() === this.priceFilterValue.map(this.getNumber).toString()) {
            return
          }
          this.priceFilterValue = isMax
            ? this.getPriceMaxRangeValue()
            : priceArr.map(this.getFormatPriceNumber)
        }
      }

      if (this.changeListener) {
        listeners.change = () => {
          this.emitChange()
        }
      }

      return <div class="filter-price">
        { this.renderGraph() }

        <klk-slider attrs={ attrs } value={ value } on={ listeners } />

        <div class="price-input">
          { Array.from({ length: 3 }, (_v, index) => {
            const inputIndex = index === 0 ? 0 : 1
            return index === 1 ? <span>-</span> : <klk-input
              value={this.priceFilterValueShow[inputIndex]} ref={`priceInput${inputIndex === 0 ? 'Min' : 'Max'}`}
              type="tel"
              onInput={(value: any) => this.priceInput(value, inputIndex)}
              onEnter={(e: any) => e.target.blur()}
              onBlur={() => this.priceChange(inputIndex)}>
              <span slot="prepend" class="prepend-currency">{currency_symbol}</span>
            </klk-input>
          })}
        </div>
      </div>
    }

    renderGraph() {
      const { step, counts } = this.priceFilter!
      if (!counts || !counts.length) {
        return
      }
      const [min, max] = this.priceFilterValue.map(this.getNumber)
      const maxValue = Math.max(...counts)
      return <div class="price-graph">
        { counts.map((count: number, index: number) => <div class="price-graph--item" key={ index }
          style={{
            width: `calc(${100 / counts.length}%)`,
            height: maxValue ? `calc(${100 * count / maxValue}%)` : 0,
            backgroundColor: ((index + 1) * +step > min && (index + 1) * +step <= max) ? '#FFF0E5' : ''
          }}>
        </div>)}
      </div>
    }

    formatPriceThousands(value: number | string) {
      return formatPriceThousands(value)
    }

    hackPriceSliderChange() {
      this.hackPriceSliderTimer && clearTimeout(this.hackPriceSliderTimer)

      this.hackPriceSliderTimer = setTimeout(() => {
        this.hackPriceSliderTimer = null
      }, 300)
    }

    priceChange(index?: 0 | 1) {
      this.changeTimer && clearTimeout(this.changeTimer)
      // web端 max < min错误或空时候情况时候在这里修正
      if (this.error) {
        index = index || this.error!.index
        const [min, max] = ['Min', 'Max'].map(value => this.getNumber((this.$refs[`priceInput${value}`] as any).curValue))
        const ref = this.$refs[index === 0 ? 'priceInputMin' : 'priceInputMax'] as any
        if (this.isPriceInputEmpty(ref.curValue) || !/\d/.test(ref.curValue) || min > max) {
          this.fixPriceInput(index)
        }
        if (index === 1) {
          if (this.isEndOfPlus(this.value[1])) {
            this.isSliderReachMax = true
          }
        }
      }

      this.emitChange()
    }

    emitChange() {
      const value: any[] = this.isPriceMaxRange() ? [] : this.priceFilterValue.map(this.getNumber)

      if (value.length !== 0 && this.isSliderReachMax) {
        value[1] += '+'
      }
      // 和上次发生变化才进行change
      if (this.value.toString() === value.toString()) {
        return
      }
      this.$emit('change', value)
    }

    /**
     * 1、mWeb直接延迟触发，web触发blur/enter时候才是实时
     * 2、输入min max错误那一边， 或空的那一边，都重置为上一次这个位置的正确值
     */
    priceInput(value: any, index: 0 | 1) {
      this.changeTimer && clearTimeout(this.changeTimer)

      const currentInputValue = this.getNumber(value)
      let isError = false
      // 为空或者一个数字都没有时候什么都不处理, 直到用户blur或enter
      if (this.isPriceInputEmpty(value) || !/\d/.test(value)) {
        this.setInputElValue('', index)
        this.error = {
          state: true,
          index
        }
        return
      } else if (index === 0) {
        const max = this.getNumber(this.priceFilterValue[1])
        if (currentInputValue > max) {
          isError = true
        }
      } else {
        const min = this.getNumber(this.priceFilterValue[0])
        if (currentInputValue < min) {
          isError = true
        }
      }

      this.error = isError ? {
        state: isError,
        index
      } : null

      // const ref = (this.$refs[index === 0 ? 'priceInputMin' : 'priceInputMax'] as any)

      const formatValue = this.getFormatPriceNumber(currentInputValue)
      // ref.curValue = formatValue
      this.setInputElValue(formatValue, index)
      // web不直接修正是因为有min > max或空情况，需要等blur、enter修正

      if (!this.error) {
        index === 1 && (this.isSliderReachMax = false)
        this.$set(this.priceFilterValue, index, formatValue)
        // this.inputExceed(currentInputValue, index)
        this.hackPriceSliderChange()
        if (this.isMobile) {
          this.changeTimer = setTimeout(() => {
            this.emitChange()
          }, 1000)
        } else {
        }
      }
    }

    inputExceed(currentInputValue: number, index: 0 | 1) {
      const { start, end } = this.priceFilter!
      if ((index === 0 && currentInputValue > +end) || (index === 1 && currentInputValue < +start)) {
        this.isInputExceed = true
      } else {
        this.isInputExceed = false
      }
    }

    isEndOfPlus(value: string | number) {
      return (value || '').toString().charAt((value || '').toString().length - 1) === '+'
    }

    setInputElValue(value: any, index: 0 | 1) {
      const { end } = this.priceFilter!

      const ref = (this.$refs[index === 0 ? 'priceInputMin' : 'priceInputMax'] as any)
      ref.curValue = value + (this.isSliderReachMax && !this.isEndOfPlus(value) && this.getNumber(value) === +end ? '+' : '')
    }

    // 修正PriceInput 对应位置上的空值或错误值为上一次的正确值
    fixPriceInput(index: 0 | 1) {
      const fixValue = this.value.length === 0 ? this.getPriceMaxRangeValue()[index] : this.getFormatPriceNumber(this.value[index])
      this.$set(this.priceFilterValue, index, fixValue)
      this.hackPriceSliderChange()
      // ref.curValue = fixValue
      this.setInputElValue(fixValue, index)
    }
}
