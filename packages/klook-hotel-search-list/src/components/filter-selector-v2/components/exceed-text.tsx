import { Vue, Component, Prop } from 'vue-property-decorator'

@Component({
  components: {
  }
})
export default class ExceedText extends Vue {
    @Prop({ type: String }) text!: 'boolean'
    @Prop({ type: Number, default: 18 }) lineHeight!: number

    render() {
      return <div>
        { this.text }
      </div>
    }

    mounted() {
      this.exceed()
    }

    exceed() {
      const content = this.$el as HTMLElement
      const { clientHeight } = content
      if (clientHeight > this.lineHeight) {
        this.$emit('exceed')
      }
    }
}
