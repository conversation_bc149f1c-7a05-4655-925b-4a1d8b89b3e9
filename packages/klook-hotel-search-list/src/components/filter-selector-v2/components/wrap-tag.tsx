import { Vue, Component, Prop } from 'vue-property-decorator'
import { IconStar } from '@klook/klook-icons'
import { SubFilterItem } from '../../../type'
import ExceedText from './exceed-text'

@Component({
  components: {
    ExceedText,
    IconStar
  }
})
export default class WrapTag extends Vue {
    @Prop({ type: Object, default: () => {} }) subFilterItem!: SubFilterItem
    @Prop({ type: Array, default: () => [] }) localSelectedValue!: string[]
    @Prop({ type: Boolean, default: false }) isMobile!: boolean
    isTextExceed = false

    get isStar() {
      return this.subFilterItem?.id === 'star'
    }

    get isRating() {
      return this.subFilterItem?.id === 'rating'
    }

    get isPopular() {
      return this.subFilterItem?.id === 'popular'
    }

    get hideSubTitle() {
      return !this.isMobile && this.isRating
    }

    render() {
      const { ui_type } = this.subFilterItem
      return <div class={[
        'filter-wrap--rounded',
        (ui_type === 2 || this.isStar) && 'column',
        this.isTextExceed && 'exceed'
      ]}>
        {this.subFilterItem.sub_filter_list!.map((item, index) => this.renderSingle(item, index))}</div>
    }

    renderSingle({ id, title, icon_url, sub_title, selected_icon_url, disabled }: SubFilterItem, index: number) {
      const checked = this.localSelectedValue.includes(id)
      return <div
        class={['filter-tag',
          checked && 'checked',
          !!disabled && 'disabled',
          icon_url && this.isStayplusItem(id) && 'stayplus-icon'
        ]}
        key={`${id}-${index}`}
        onClick={() => (checked || !disabled) && this.$emit('select', id, this.subFilterItem)}
      >
        <div class="filter-tag-title">
          {icon_url && <img src={selected_icon_url && checked ? selected_icon_url : icon_url} />}
          { this.isStayplusItem(id) && icon_url ? '' : title }
          { this.isStar && <icon-star theme="filled" size={12} /> }
        </div>
        { !this.hideSubTitle && !!sub_title && <exceed-text class="filter-tag-sub" onExceed={this.onTextExceed} text={sub_title} /> }
      </div>
    }

    isStayplusItem(id: string) {
      return id === 'stayplus|1'
    }

    onTextExceed() {
      this.isTextExceed = true
    }
}
