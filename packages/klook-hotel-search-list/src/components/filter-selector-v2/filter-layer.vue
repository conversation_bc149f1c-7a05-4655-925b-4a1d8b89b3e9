<script lang="tsx">
import { Component, Model, Prop } from 'vue-property-decorator'
import { IconTriangleDown } from '@klook/klook-icons'
import { SubFilterItem } from '../../../types/index.d'
import FilterMixin from './mixin'
import ModalWrap from '../modal-wrap.vue'

@Component({
  components: {
    IconTriangleDown,
    ModalWrap
  }
})
export default class FilterLayer extends FilterMixin {
  @Model('change', { type: Array, default: () => [] }) value!: string[]
  @Prop({ type: String, default: 'FilterEdit?trg=manual' }) dataSpmModule!: string
  @Prop({ type: Array, default: () => [] }) curFilterList!: SubFilterItem[]
  @Prop({ type: Object, default: () => ({}) }) filterSelected!: {
    area?: string[],
    filter?: string[]
  }

  @Prop({ type: String, default: '' }) title!: string // layer title
  @Prop({ type: String, default: '' }) entranceTitle!: string // 入口title

  layerVisible = false
  bottomSheetVisible = false
  page: 'filterMap' | '' = ''

  mounted() {

  }

  toggleShow(page: 'filterMap' | '' = '') {
    this.page = page
    if (!this.filterList?.length) {
      return
    }
    if ((this.layerVisible = !this.layerVisible)) {
      this.$nextTick(() => {
        this.$inhouse?.track('pageview', (this.$refs.pageLayer as Vue).$el, {
          force: true
        })
      })
    }
  }

  render() {
    return <div
      class={['filter-layer--entrance', !this.filterList?.length ? 'disabled' : this.value.length > 0 && 'active']}
      {...{ directives: [{ name: 'galileo-click-tracker', value: { spm: this.dataSpmModule.split('?')[0] } }] }}
      data-spm-module={ this.dataSpmModule }
      data-spm-virtual-item="__virtual"
      onClick={ this.toggleShow }
    >
      {
        this.$slots.default || [
          <span>{ this.entranceTitle || this.$t('80629') }</span>,
          <IconTriangleDown theme="filled" size="12" />
        ]
      }
      <client-only>
        <modal-wrap
          visible={ this.layerVisible }
          isMobile={true}
          type={this.page === 'filterMap' ? 'layer' : ''}
          tabIndex={this.tabAttrs ? 1 : 0}
          config={{
            canPullClose: false,
            headerDivider: true,
            maskClosable: false,
            transfer: true,
            showClose: true
          }}
          title={ this.tabAttrs ? this.tabAttrs.data.title : this.title || this.$t('hotel.vertical_filter') }
          class="filter-layer"
          {...{ directives: [{ name: 'galileo-click-tracker', value: { spm: this.type === 'area' ? 'PoiHotel_AreaOverlay' : 'PoiHotel_FilterOverlay' } }] }}
          attrs={ this._$ihtHelper.getPageIHT({ spm: this.type === 'area' ? 'PoiHotel_AreaOverlay' : 'PoiHotel_FilterOverlay', manual: true })}
          ref="pageLayer"
          onClose={this.onClose}
          onBack={this.onTabBack}
        >
          { this.layerVisible && this.renderMainContent() }
          { this.renderTabs() }
          <div slot="footer" class={`filter-layer--footer ${this.tabAttrs && 'tab'}`}>
            { !this.tabAttrs && <klk-link color="#212121" disabled={ !this.localSelectedValue.length } class="filter-clear-btn" onClick={ this.clearSelected }>
              { this.$t('clear') }
            </klk-link> }
            <klk-button
              class="filter-confirm-btn"
              disabled={this.currentResultCount === '0'}
              {...{ directives: [{ name: 'galileo-click-tracker', value: { spm: this.type === 'area' ?  'AreaSelectResult': 'FilterEditResult' } }] }}
              attrs={ this._$ihtHelper.getModuleIHT({ spm: this.type === 'area' ?  'AreaSelectResult': 'FilterEditResult', manual: true })}
              onClick={ this.onConfirm }
            >
              { this.resultCountText }
            </klk-button>
          </div>
        </modal-wrap>

      </client-only>
    </div>
  }

  get starPoptipVnode() {
    return <div class="star-poptip-entrance">
      <icon-caution-circle nativeOnClick={() => { this.bottomSheetVisible = true }} size={20} style="margin-left: 8px;color: #757575;" />
      <klk-bottom-sheet header-divider transfer visible={ this.bottomSheetVisible } title={ this.$t('85880') } on={{ close: () => { this.bottomSheetVisible = false } }}>
        { this.starPoptipContent }
      </klk-bottom-sheet>
    </div>
  }

  onConfirm() {
    if (this.isOnlyDate) {
      this.$emit('set-filter', this.hotelFilterTemp)
    }
    this.postFilterValue()
    this.layerVisible = false
    this.hotelFilterTemp = null
    this.tabAttrs = null
  }

  onClose() {
    this.tabAttrs = null
    this.hotelFilterTemp = null
    this.layerVisible = false
    this.initLocalSelectedValue()
  }

  onTabBack() {
    this.tabAttrs = null
  }

  // 渲染tab（浮层和切换和web的modal）
  renderTabs() {
    if (!this.tabAttrs) {
      return
    }
    const { subData: { sub_filter_list } } = this.tabAttrs! as {
      data: SubFilterItem,
      subData: SubFilterItem
      tabIndex?: number,
      tempSelectedValue?: string[]
    }
    return <div class="layer-tab">
      <klk-checkbox
        class="filter-tag"
        attrs={this.isTabSingleAllChecked()}
        onChange={this.handleTabCheckAllChange}
      >{ this.$t('389') }</klk-checkbox>
      {
        sub_filter_list!.map((item: SubFilterItem, index: any) => this.renderCheckBoxSingle(item, index, {
          selectedValue: this.localSelectedValue,
          customChange: () => this.tabSelectTag(item.id)
        }))
      }
    </div>
  }

  beforeDestroy() {
  }
}
</script>

<style lang="scss" scoped>
@import './filter.scss';
</style>

<style lang="scss" scoped>
.filter-layer  {
  &.layer {
    z-index: 9999 !important;
  }

  .filter-layer--footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .filter-clear-btn {
      min-width: auto;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      margin-right: 12px;
      color: $color-text-primary;
      text-decoration: underline;
    }
    .filter-confirm-btn {
      flex-shrink: 0;
    }

    &.tab {
      justify-content: flex-end;
    }
  }
  .layer-tab {
    padding-top: 20px !important;
    .klk-checkbox {
      width: 100%;
      display: flex;
      margin-bottom: 20px;
      .klk-checkbox-label {
        flex: 1;
      }
    }
  }
}
</style>
