// 纵向滚动条美化
.filter-container {

    &.mobile {
        padding-top: 0 !important;
    }
    background-color: $color-bg-widget-normal;

    .checked-dot {
        position: relative;
        &::after {
            position: absolute;
            content: "";
            width: 6px;
            height: 6px;
            border-radius: 50%;
            margin-left: 1px;
            background-color: $color-brand-primary;
        }
    }

    .filter-section {
        position: relative;
        .filter-section--title {
            display: flex;
            align-items: center;
            justify-content: space-between;
            @include font-heading-xs;
            padding: 32px 0 16px;
            > span {
                margin-right: 4px;
            }
        }
  
        &.checked {
            .filter-section--title {
                & > span:first-child {
                    @extend .checked-dot;
                }
            }
            ::v-deep .list-wrap-view-more {
                & > span:first-child {
                    @extend .checked-dot;

                    &::after {
                        content: none;
                    }
                }
            }
        }
    }

    .filter-price {
  
        ::v-deep .klk-slider {
            margin: 16px 12px 28px;
        }

        ::v-deep .price-graph {
            height: 70px;
            padding-top: 8px;
            width: 100%;
            margin-left: 12px;
            width: calc(100% - 24px);
            display: flex;
            align-items: flex-end;
            margin-bottom: -16px;
            & > .price-graph--item {
                background-color: #FAFAFA;
                transition: background-color 0.3;
            }
        }
        ::v-deep .price-input {
            display: flex;
            align-items: center;
            & > span {
                margin: 0 8px;
                color: $color-text-placeholder;
            }
            & > .klk-input {
                .prepend-currency {
                    padding-left: 12px;
                }
                input {
                    padding: 0 12px 0 4px;
                }
            }
        }

    }
  
    .filter-wrap--rounded {
        margin-bottom: -12px;
        display: flex;
        flex-wrap: wrap;
        ::v-deep .filter-tag {
            @include font-body-s-regular;
            padding: 8px 12px;
            box-sizing: border-box;
            margin: 0 12px 12px 0;
            align-items: center;
            background-color: #ffffff;
            border: 1px solid $color-border-normal;
            border-radius: $radius-m;
            text-align: center;
            cursor: pointer;
  
            .filter-tag-title {
                display: flex;
                align-items: center;
                justify-content: center;
                flex-wrap: nowrap;
                .i-icon-icon-star {
                    margin-left: 4px;
                    color: #fe9c00;
                    transform: translateY(-1px);
                    flex-shrink: 0;
                }
            }
  
            .filter-tag-sub {
                @include font-caption-m-regular;
                text-align: center;
                margin-top: 4px;
                color: $color-text-secondary;
                word-break: break-word;
            }
          
  
            &:last-child {
                margin-right: 0;
            }
  
  
            &.checked {
                border-color: $color-brand-primary;
                font-weight: $fontWeight-semibold;
                color: $color-brand-primary;
                .filter-tag-title {
                    .i-icon {
                        color: $color-brand-primary;
                    }
                }
                .filter-tag-sub { 
                    color: $color-brand-primary;
                }
            }

            &.disabled:not(.checked) {
                background-color: $color-bg-2;
                .filter-tag-title {
                    color: $color-text-disabled;
                    img {
                        filter: grayscale(1);
                        opacity: 0.4;
                    }
                    .i-icon-icon-star {
                        color: $color-text-disabled;
                    }
                }
                .filter-tag-sub {
                    color: $color-text-disabled;
                }
            }

            // stay plus icon
            &.stayplus-icon {
                height: 39px;
                padding: 11px 12px;
                .filter-tag-title > img:first-child {
                    height: 16px;
                    width: auto;
                }
                &.disabled {
                    .filter-tag-title > img:first-child {
                        filter: grayscale(1);
                        opacity: 0.4;
                    }
                }
            }
        }
        ::v-deep .list-wrap-view-more {
            width: 100%;
            margin-bottom: 12px;
        }

        // 先全部放一行，如果内部文本有一个换行，一行只放2个
        &.column {
            flex-wrap: nowrap;

            ::v-deep .filter-tag {
                @include font-body-m-regular;
                line-height: 21px;
                border-radius: $radius-l;
                padding: 12px 8px;
                flex: 1;
                margin: 0 12px 12px 0;
                &:last-child {
                    margin-right: 0;
                }
            }
  
            &.exceed {
                flex-wrap: wrap;
                ::v-deep .filter-tag {
                    width: calc(50% - 6px);
                    flex: auto;
                    &:nth-child(2n) {
                        margin-right: 0;
                    }
                }
            }
        }
    }
  
    .filter-wrap--checkbox {
        ::v-deep .filter-tag {
            display: flex;
            margin: 0 0 12px 0;
            &.klk-checkbox {
                .klk-checkbox-label {
                    flex: 1;
                }

                &.stayplus-icon {
                    .klk-checkbox-label > img:first-child {
                        height: 15px;
                        width: auto;
                        vertical-align: middle;
                    }
                    &.klk-checkbox-disabled {
                        .klk-checkbox-label > img:first-child {
                            filter: grayscale(1);
                            opacity: 0.4;
                        }
                    }
                }
            }
            &:last-child {
                margin-bottom: 0;
            }
        }
  
        ::v-deep .list-wrap-view-more {
            margin-top: 16px;
        }
    }
  
    .filter-wrap--column-info {
        ::v-deep .column-info-item {
            @include font-body-s-regular;
            display: flex;
            margin-bottom: 12px;
            justify-content: space-between;
            align-items: center;
            cursor: pointer;
  
            .i-icon {
                flex-shrink: 0;
                margin-left: 8px;
            }
            &.checked {
                .column-info-item--title {
                    @extend .checked-dot;
                    &::after {
                        margin-left: 4px;
                    }
                }
            }
        }
        ::v-deep .list-wrap-view-more {
            margin-top: 4px;
        }
    }

    .star-poptip-entrance {
        .star-poptip-entrance {
            margin-left: 8px;
            color: $color-text-primary;
        }
        float: right;
        ::after {
            content: '';
            display: block;
            clear: both;
        }
    }

    ::v-deep .list-wrap-view-more {
        text-decoration: underline;
        word-wrap: break-word;
        cursor: pointer;
        & > .i-icon  {
            display: none;
        }
    }

    .filter-section {
        &.hotelAttr {
            .filter-wrap--rounded {
                    margin-bottom: 8px !important;
                    ::v-deep .filter-tag {
                        width: auto;
                        border-radius: $radius-l;
                        padding: 12px 4px;
                        @include font-body-s-regular;
                        .filter-tag-title {
                            flex-direction: column;
                            img {
                                width: 20px;
                                height: 20px;
                                margin-bottom: 4px;
                            }
                        }
                    }
        
                    &.multiple {
                        ::v-deep .filter-tag {
                            width: calc(33.33% - 8px);
            
                            &:nth-child(3n) {
                                margin-right: 0;
                            }
                        }
                    }
            }
        }
    }

    &.desktop {
        ::v-deep .list-wrap-view-more {
            margin-top: 12px;
        }
        .filter-wrap--rounded {
            margin-bottom: -8px;
            display: flex;
            flex-wrap: wrap;

            ::v-deep .filter-tag {
                &:not(.disabled):not(.checked):hover {
                    background-color: $color-bg-2;
                    border-color: $color-border-active;
                }
                &.disabled {
                    cursor: not-allowed;
                }
            } 
            // 先全部放一行，如果内部文本有一个换行，一行只放2个
            &.column {
                flex-wrap: nowrap;
                ::v-deep .filter-tag {
                    padding: 12px 8px;
                    border-radius: $radius-m;
                    margin: 0 8px 8px 0;
                    &:last-child {
                        margin-right: 0;
                    }
                }
                &.exceed {
                    flex-wrap: wrap;
                    ::v-deep .filter-tag {
                        width: calc(50% - 4px);
                    }
                }
            }
        }
      
        .filter-wrap--checkbox {
            ::v-deep .filter-tag {
                &:not(.klk-checkbox-disabled):not(.klk-checkbox-checked):hover {
                    .klk-checkbox-base {
                        border-color: $color-text-primary;
                    }
                }
                display: flex;
                margin-bottom: 0;
                padding: 4px;
            }
        }
      
        .filter-wrap--column-info {
            ::v-deep .column-info-item {
                &:hover {
                    background-color: $color-bg-2;
                    border-radius: $radius-m;
                }

                @include font-body-s-regular;

                padding: 4px;
                margin-bottom: 0;

                &.checked {
                    .column-info-item--title {
                        &::after {
                            margin-top: 2px;
                            margin-left: 1px;
                        }
                    }
                }
            }
        }
    }
}

.star-poptip-content {
    .star-desc {
        padding-top: 12px;
        @include font-body-m-regular;
        color: #000000;
        margin-bottom: 20px;
    }
    .star-wrap {
        display: flex;
        align-items: center;
        margin-bottom: 12px;
        @include font-body-m-regular;
        span {
            margin-right: 8px;
        }
        > div {
            img {
                pointer-events: none;
            }
        }
        .i-icon-icon-star {
            color: $color-brand-primary;
        }
    }
}