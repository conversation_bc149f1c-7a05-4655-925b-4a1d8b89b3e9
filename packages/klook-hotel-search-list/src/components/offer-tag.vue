<template>
  <div
    v-if="tagInfo.filter_id"
    v-galileo-click-tracker="{spm: isStayPlus ? 'StayEntrance' : 'OffersTag'}"
    :class="`hotel-offer-tag offer-tag-${isMobile ? 'mobile' : 'desktop'}`"
    :style="inlineStyle"
    v-bind="spmModuleInfo"
    @click="onClick"
  >
    <img v-if="tagInfo.icon" :src="tagInfo.icon" width="28" height="28" class="tag-icon" />
    <div class="tag-content">
      <img v-if="tagInfo.top_icon" :src="tagInfo.top_icon" height="21px" />
      <div v-else class="tag-title">{{ tagInfo.title }}</div>
      <div v-if="showdesc" class="tag-desc">{{ tagInfo.desc }}</div>
    </div>
    <klk-button :size="isMobile ? 'mini' : 'small'" type="outlined">
      {{ tagInfo.btn_text }}
    </klk-button>
  </div>
</template>

<script lang="ts">
import { Vue, Component, Prop } from 'vue-property-decorator'

// https://klook.larksuite.com/docx/YQzGdFVM8o62Toxuc7YuNQJyskd

const stayplusFilterValue = 'stayplus|1'

type OfferTagInfo = {
  title: string
  title_en: string
  desc: string
  color: string
  icon: string
  top_icon: string
  btn_text: string
  filter_id: string
}

@Component
export default class OfferTag extends Vue {
  @Prop({ type: Object, default: () => ({}) }) readonly tagInfo!: OfferTagInfo
  @Prop({ type: Boolean, default: false }) isMobile!: boolean

  get isStayPlus() {
    const { top_icon, filter_id } = this.tagInfo
    return top_icon && filter_id === stayplusFilterValue
  }

  get showdesc() {
    const { desc } = this.tagInfo
    return this.isStayPlus || (desc && !this.isMobile)
  }

  get inlineStyle() {
    return { backgroundColor: this.tagInfo.color || '#fff' }
  }

  get spmModuleInfo() {
    return this._$ihtHelper.getModuleIHT({
      spm: this.isStayPlus ? 'StayEntrance' : 'OffersTag',
      ext: {
        FilterID: this.tagInfo.filter_id,
        TitleEn: this.tagInfo.title_en
      }
    })
  }

  onClick() {
    this.$inhouse?.track('action', this.$el.querySelector('button'))
    this.$nextTick(() => {
      this.$emit('click', this.tagInfo)
    })
  }
}
</script>

<style lang="scss" scoped>
@import '../style/_variables.scss';
.hotel-offer-tag {
  display: flex;
  align-items: center;
  border-radius: $radius-m;
  padding: 12px 16px;
  margin-bottom: 16px;

  .tag-icon {
    flex: 0 0 auto;
  }

  .tag-content {
    margin: 0 12px;
    flex: 1 1 0;
  }

  .tag-title {
    color: $color-text-primary;

    @include font-body-m-bold;
  }

  .tag-desc {
    color: $color-text-primary;
    word-break: break-word;

    @include text-ellipsis(2);
    @include font-body-s-regular;
  }

  ::v-deep .klk-button {
    flex: 0 0 auto;
    white-space: nowrap;
  }

  &.offer-tag-desktop {
    border-radius: $radius-xl;
    margin: 0 0 12px 0;
    cursor: pointer;
  }

  &.offer-tag-mobile {
    border: 1px solid $color-bg-widget-normal;
  }
}
</style>
