<template>
  <div :class="`empty-view empty-view-${platform} empty-view-${type} ${position ? `empty-view-position--${position}` : ''}`">
    <klk-loading v-if="emptyItem.showDefaultLoading"></klk-loading>
    <img v-if="emptyItem.img" :src="emptyItem.img" />
    <div v-if="emptyItem.title" class="empty-view-title">{{ emptyItem.title }}</div>
    <div v-if="emptyItem.desc" class="empty-view-desc">{{ emptyItem.desc }}</div>
    <div class="empty-view-button-list">
      <klk-button
        v-if="emptyItem.buttonText && buttonProp"
        class="empty-view-button"
        v-bind="buttonProp"
        @click="onClick"
      >
        {{ emptyItem.buttonText }}
      </klk-button>
      <template v-for="(button, index) in otherButtonList">
        <span :key="'delimiter-' + index"> {{ button.delimiter }} </span>
        <component
          :is="button.com || 'klk-button'"
          :key="index"
          :class="`empty-view-button ${button.type}`"
          v-bind="button"
          @click="onClick(button.action)"
        >
          {{ button.buttonText }}
        </component>
      </template>
    </div>
    <slot></slot>
  </div>
</template>

<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator'

@Component({
  components: {},
})
export default class EmptyView extends Vue {
  @Prop({ type: String, default: '' }) position!: 'relative' | 'fixed' | 'absolute'
  @Prop({ type: String, default: '' }) type!: 'error' | 'empty' | 'filterEmpty' | 'loading' | 'error2'
  @Prop({ type: Object, default: null }) item!: object
  @Prop({ type: [Object, Error], default: null }) error!: any
  @Prop({ type: Object, default: () => ({ type: 'outlined', size: 'small' }) }) buttonProp!: any
  @Prop({ type: Array, default: () => ([]) }) otherButtonList!: any
  @Prop({ type: String, default: '' }) platform!: any

  get emptyItem() {
    return Object.assign({
      error: {
        title: this.$t('47985'),
        desc: this.$t('47986'),
        buttonText: this.$t('47987'),
        img: 'https://res.klook.com/image/upload/v1657173556/zasyvr3rbeukh1l5j5x2.png'
      },
      filterEmpty: {
        title: this.$t('47991'),
        desc: this.$t('48051'),
        buttonText: this.$t('48052'),
        img: 'https://res.klook.com/image/upload/v1657158870/pcdpb2gqttqatpagckq5.png'
      },
      empty: {
        title: this.$t('47989'),
        desc: this.$t('48049'),
        buttonText: this.$t('48050'),
        img: 'https://res.klook.com/image/upload/v1657158870/pcdpb2gqttqatpagckq5.png'
      },
      empty2: {
        title: this.$t('search_no_results_found'),
        img: 'https://res.klook.com/image/upload/v1657158870/pcdpb2gqttqatpagckq5.png'
      },
      loading: {
        desc: this.$t('hotel.search_waiting'),
        img: 'https://res.klook.com/image/upload/v1639721175/cavobu4damovch1ztlcx.gif',
        showDefaultLoading: false
      },
      // app 122707 网络错误  业务错误122711, 是否不需要展示this.error.message 具体真实信息？ @damon
      error2: { // 目前通用的首次错误, 可避免每次传递$t('48163'),
        title: this.$t('29410'),
        desc: this.error ? `${this.error.message}` : this.$t('48163'),
        buttonText: this.$t('45514'),
        img: 'https://res.klook.com/image/upload/Group_3880_b3obts.png'
      }
    }[this.type] || {}, this.item)
  }

  onClick(type = this.type) {
    // 兼容搜索列表页
    this.$emit('action', type)
    this.$emit(type)
  }
}
</script>

<style lang="scss" scoped>
.empty-view {
  display: flex;
  align-items: center;
  flex-direction: column;
  justify-content: center;

  &-title {
    @include font-body-l-bold;
    margin-bottom: 8px;
    color: $color-text-secondary;
    text-align: center;
  }

  &-desc {
    @include font-body-s-regular;

    color: $color-text-placeholder;
    text-align: center;
  }

  img {
    width: 182px;
    margin-bottom: 24px;
  }

  .empty-view-button-list {
    display: flex;
    flex-direction: column;
    align-items: center;
    & > *  {
     margin-top: 12px;
    }
    & > span {
      @include font-body-m-regular;
      margin-top: 8px;
    }
    .underline-text {
      margin-top: 12px;
      @include font-body-s-regular;
      text-decoration: underline !important;
      outline: none;
      cursor: pointer;

      &:hover {
        opacity: $opacity-hover;
      }

      &:active {
        opacity: $opacity-hover;
      }
    }
  }

  &.empty-view-position--fixed {
    position: fixed;
    width: 100%;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
  }
  &.empty-view-position--absolute {
    position: absolute;
    width: 100%;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
  }
}

.empty-view-desktop {

  .empty-view-desc {
    @include font-body-m-regular;
  }
  .empty-view-button-list {
    display: flex;
    align-items: center;
    flex-direction: row;
    & > *  {
     margin-top: 20px;
    }
    & > span {
      @include font-body-m-regular;
      margin: 20px 12px 0;
    }
  }
  &.empty-view-loading img, &.empty-view-error img {
    width: 260px;
  }
}

.empty-view-empty, .empty-view-filterEmpty {
  img {
    width: 130px;
  }
}
</style>
