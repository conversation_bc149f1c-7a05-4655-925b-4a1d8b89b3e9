<template>
  <div
    :class="[{
      'mobile': isMobile,
      'desktop': !isMobile
    }, type, 'hotel-text-tips']"
    @click="click"
  >
    <slot name="content">
      <div class="hotel-text-tips-content">
        <template v-if="type === 'normal'">
          <span v-if="title && (isMobile || !allClick)" class="title"> {{ title }} </span>
          <template v-if="isMobile">
            <component
              :is="titleIcon.includes('#') ? 'SvgIcon' : titleIcon"
              v-if="titleIcon"
              v-bind="tipIconIht"
              :name="titleIcon"
              :size="16"
              :class="`title_icon ${titleIconClass}`"
              @click.native.stop="modelVisible = true"
            />
          </template>
          <klk-poptip
            v-else
            v-bind="props"
            @show="show"
            @hide="hide"
          >
            <span v-if="title && allClick" class="title"> {{ title }} </span>

            <component :is="tipIcon.includes('#') ? 'SvgIcon' : tipIcon" :name="tipIcon" v-bind="tipIconIht" :size="16" :class="`tip_icon ${tipIconClass}`" />

            <div slot="content">
              <slot />
            </div>
          </klk-poptip>
        </template>
        <template v-else-if="type === 'dropdown'">
          <template v-if="isMobile">
            <div class="text-tips-dropdown">
              <div class="text-tips-dropdown--title" @click="modelVisible = !modelVisible">
                <span v-if="title" class=" title"> {{ title }} </span>
                <component
                  :is="titleIcon.includes('#') ? 'SvgIcon' : titleIcon"
                  v-if="titleIcon"
                  v-bind="tipIconIht"
                  :name="titleIcon"
                  :class="`title_icon ${titleIconClass}`"
                />
              </div>
              <div v-show="modelVisible">
                <slot />
              </div>
            </div>
          </template>
        </template>
      </div>
    </slot>

    <klk-bottom-sheet
      v-if="isMobile && type === 'normal'"
      class="ui-custom-kbs"
      v-bind="props"
      :visible.sync="modelVisible"
    >
      <div slot="default">
        <slot />
      </div>
    </klk-bottom-sheet>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator'
import { IconCautionCircle } from '@klook/klook-icons'

@Component({
  components: {
    IconCautionCircle
  }
})
export default class TextTips extends Vue {
  @Prop(String) readonly title!: any
  @Prop(Boolean) readonly isMobile!: boolean
  @Prop(Boolean) readonly allClick!: boolean
  @Prop({ type: String, default: 'bottom-start' }) readonly placement!: any

  @Prop({ type: String, default: 'normal' }) readonly type!:
    | 'normal' // mWeb bottomSheet, web popTip
    | 'dropdown' // mWeb 下拉

  @Prop({ type: String, default: 'hover' }) readonly trigger!: any
  // 这里不用同一个tip变量搜索因为mobile和desktop经常是一端有改变，大部分是用默认值

  @Prop({ type: String, default: 'common#icon_tips_tips_xs' })
  readonly titleIcon!: string

  @Prop({ type: String, default: '' })
  readonly titleIconClass!: string

  @Prop({ type: String, default: 'common#icon_tips_tips_xs' })
  readonly tipIcon!: string

  @Prop({ type: String, default: '' })
  readonly tipIconClass!: string

  @Prop({ type: Object, default: () => null })
  readonly tipIconIht!: object

  @Prop({ type: Object, default: () => ({}) }) readonly config!: any

  modelVisible = false

  get props() {
    const defaultPopTip: any = {}
    if (!this.isMobile && (!this.title || !this.allClick)) {
      switch (this.placement || this.config?.placement) {
        case 'bottom-start':
          defaultPopTip.offset = [-5, 15]
          break
        case 'bottom-end':
          defaultPopTip.offset = [10, 15]
      }
    }
    return this.isMobile ? {
      transfer: true,
      ...this.config
    } : {
      trigger: this.trigger,
      placement: this.placement,
      maxWidth: 400,
      maxHeight: 300,
      ...defaultPopTip,
      ...this.config
    }
  }

  click() {
    this.$emit('click')
    if (this.allClick && this.type !== 'dropdown') {
      this.modelVisible = !this.modelVisible
    }
  }

  show() {
    this.$emit('show')
  }

  hide() {
    this.$emit('hide')
  }
}
</script>

<style lang="scss" scoped>
.hotel-text-tips {
  .hotel-text-tips-content {
    display: flex;
    align-items: center;
    color: $color-text-primary;
    ::v-deep .klk-poptip-reference {
      display: flex;
      align-items: center;
    }
  }
  .klk-poptip {
    cursor: pointer;
  }

  .title {
    @include font-body-s-regular;
  }
  .title_icon {
    color: $color-text-secondary;
    margin-left: 4px;
    margin-top: 2px;
    width: 16px;
    height: 16px;
  }

  .tip_icon {
    color: $color-text-secondary;
    margin-left: 4px;
    width: 16px;
    height: 16px;
  }

  &.dropdown {
    .text-tips-dropdown {
      .text-tips-dropdown--title {
        display: flex;
        align-items: center;
        justify-content: flex-end;
      }
    }
  }

  &.inline-flex {
    display: inline-flex;
    margin-top: -2px;
    transform: translateY(2px);
  }

}
</style>
