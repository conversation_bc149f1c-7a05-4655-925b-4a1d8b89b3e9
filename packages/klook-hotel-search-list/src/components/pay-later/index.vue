<template>
  <klk-markdown v-a-blank class="pay-later-desc" :content="data" />
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator'
import ABlank from '../../directives/a-blank'

@Component({
  components: {
  },
  directives: {
    ABlank
  }
})
export default class index extends Vue {
  @Prop({ default: false }) isMobile!: boolean
  @Prop({ default: null }) data!: any
}
</script>

<style lang="scss" scoped>
.pay-later-desc {
    @include font-body-s-regular;
    white-space: pre-wrap;
    ::v-deep a {
        color: $color-text-link;
        text-decoration: underline;
    }
}
</style>
