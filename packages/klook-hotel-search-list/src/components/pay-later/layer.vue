<template>
  <text-tip v-bind="props" class="inline-flex">
    <pay-later-tip :data="data" :is-mobile="isMobile" />
  </text-tip>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator'
import PayLaterTip from './index.vue'
import TextTip from '../text-tips.vue'

@Component({
  components: {
    TextTip,
    PayLaterTip
  }
})
export default class index extends Vue {
  @Prop({
    default: '',
    type: String
  }) data!: string
  @Prop({ type: Boolean, default: false }) isMobile!: boolean


  get props() {
    const icon = 'IconCautionCircle'
    return Object.assign({
      isMobile: this.isMobile
    }, this.isMobile ? {
      titleIcon: icon,
      config: {
        title: 'pay_later',
        headerDivider: true
      }
    } : {
      tipIcon: icon,
      config: {
        offset: [-12, 12],
        flip: true,
        zIndex: 2002,
        appendToBody: true,
        placement: 'top-start',
        flipOptions: {
          fallbackPlacements: ['bottom-start'],
          rootBoundary: 'viewport',
          padding: 20
        }
      }
    })
  }
}
</script>

<style lang="scss" scoped></style>
