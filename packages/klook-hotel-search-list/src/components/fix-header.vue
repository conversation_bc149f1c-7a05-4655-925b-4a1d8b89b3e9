<template>
  <div
    :class="`hotel-fixed-header
      ${complex ? 'complex' : ''}
      ${showBack ? 'showBack' : ''}`"
  >
    <slot>
      <div class="hotel-fixed-header-default">
        <svg-icon
          v-if="showBack"
          class="back"
          :name="backIcon"
          @click.native="$emit('back')"
        />
        <div class="hotel-fixed-header-title">{{ title }}</div>

        <slot name="header-right" />
      </div>
    </slot>
  </div>
</template>

<script lang="ts">
import { Vue, Component, Prop } from 'vue-property-decorator'
import { throttle } from 'lodash'
import { windowScrollTop } from '../util'

@Component({
  components: {}
})
export default class FixHeader extends Vue {
  @Prop({ type: String, default: '' }) title!: string
  @Prop({ type: String, default: 'common#icon_nav_chevron_left_m' }) backIcon!: string
  @Prop({ type: Number, default: 48 }) mainHeight!: number // 一级header高度
  @Prop({ type: Boolean, default: true }) showBack!: boolean

  // 上滑显示header，下滑pin
  @Prop({ type: Boolean, default: true }) complex!: boolean

  scrollTop: number = 0

  get actualMainHeight() {
    return this.platformMp ? 0 : this.mainHeight
  }

  get platformMp() {
    return this.$store?.state?.klook?.platformMp
  }

  throttleScroll = throttle(() => {
    this.scroll()
  }, 150)

  scroll() {
    const scrollTop = windowScrollTop()

    const mainHeader = document.querySelector('.layout-default_header.mobile-header') as HTMLElement
    const secondHeader = this.$el as HTMLElement

    if (scrollTop < this.scrollTop) {
      mainHeader && (mainHeader.style.visibility = 'visible')
      mainHeader && (mainHeader.style.top = '0')
      secondHeader && (secondHeader.style.top = `${this.actualMainHeight}px`)
    } else if (scrollTop < this.actualMainHeight) {
      mainHeader && (mainHeader.style.top = `-${scrollTop}px`)
      secondHeader && (secondHeader.style.top = `${this.actualMainHeight - scrollTop}px`)
    } else if (scrollTop > this.scrollTop) {
      mainHeader && (mainHeader.style.visibility = 'hidden')
      secondHeader && (secondHeader.style.top = '0px')
    }
    this.scrollTop = scrollTop
  }

  mounted() {
    if (this.complex) {
      this.scrollTop = windowScrollTop()
      window.document.addEventListener('scroll', this.throttleScroll)
      this.scroll()
    }
  }

  beforeDestroy() {
    if (this.complex) {
      window.document.removeEventListener('scroll', this.throttleScroll)
    }
  }
}
</script>

<style lang="scss">
@import '../style/_variables.scss';


.hotel-fixed-header {
  position: sticky;
  top: 0;

  &::after {
    content: '';
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 1px;
    background-color:$color-divider-solid-normal;
  }

  &.complex {
    position: fixed;
  }
  z-index: 999;
  width: 100%;
  .back {
    width: 24px;
    height: 24px;
    position: absolute;
    left: 20px;
  }
  .header-right {
    position: absolute;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
  }
  .hotel-fixed-header-default {
    padding: 0 18px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #fff;
  }
  .hotel-fixed-header-title {
    font-size: 16px;
    font-weight: bold;
    line-height: 22px;
    color: #212121;
    @include text-ellipsis(2);
  }

  &.showBack {
    .hotel-fixed-header-title {
      padding: 0 25px;
    }
  }

}
</style>
