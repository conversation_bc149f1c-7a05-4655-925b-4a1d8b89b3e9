<template>
  <div v-click-outside="close" class="hotel-tag-separated">
    <div class="show-list">
      <div
        v-for="(item, index) in showList"
        :key="index"
        :class="`tag-item ${getActiveStatus(item) ? 'active' : ''}`"
        @click="selectItem(item)"
      >
        <klk-poptip
          v-if="item.is_price && priceList.length > 1"
          ref="pricePoptip"
          :arrow="false"
          placement="bottom-start"
          :width="280"
          :offset="[0, 10]"
          trigger="click"
        >
          <span class="price-item"> {{ item[nameKey] }}</span>
          <IconChevronDown class="price-icon" theme="outline" size="20" />
          <template slot="content">
            <div v-for="(sub, subindex) in priceList" :key="subindex" :class="`sub-item ${getActiveStatus(sub) ? 'active' : ''}`" @click="selectItem(sub, subindex)">
              <span>{{ sub[nameKey] }}</span>
              <IconCheck v-if="getActiveStatus(sub)" theme="outline" size="16" />
            </div>
          </template>
        </klk-poptip>
        <span v-else> {{ item[nameKey] }}</span>
      </div>
      <svg-icon
        v-if="hideList.length > 0"
        :class="`show_more_icon ${showHideList ? 'active' : ''}`"
        name="common#icon_navigation_menu_m"
        size="20"
        @click.native="show"
      ></svg-icon>
    </div>

    <div v-if="hideList.length > 0" :style="{visibility: showHideList ? 'visible' : 'hidden'}" class="hide-list">
      <div v-for="(item, index) in hideList" :key="index" class="tag-item" @click="selectHideItem(item, index)">
        <span> {{ item[nameKey] }}</span>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop, Model, Watch } from 'vue-property-decorator'
import { IconChevronDown, IconCheck } from '@klook/klook-icons'

@Component({
  components: {
    IconChevronDown,
    IconCheck
  }
})
export default class TagSeparated extends Vue {
  @Prop({ type: String, default: 'value' }) valueKey!: string
  @Prop({ type: String, default: 'name' }) nameKey!: string
  @Prop({ type: Number, default: 24 }) padding!: number // tag 左右一起的padding

  @Prop({ type: Array, default: () => [] }) list!: { [key: string]: string }[]

  @Model('input', { type: [String, Number] }) value!: string | number

  @Prop({ type: Number, default: 4 }) firstShowLength!: number // 默认展示几个，作为初始值

  showList: any[] = []
  hideList: any[] = []
  priceList: any[] = []
  currentIndex = 0

  showHideList: boolean = false

  @Watch('list', { immediate: true })
  listChange() {
    this.formatData()
  }

  @Watch('value')
  onValueChange() {
    if (/^price_*/.test(this.value as string)) {
      const index = this.priceList.findIndex((l: any) => l.id === this.value)
      index > -1 && this.updateMenu(index)
    }
  }

  formatData() {
    this.priceList = this.list.filter((item: any) => /^price_*/.test(item.id))
    // 有两个价格sort时需要过滤掉价格类，其他情况使用原数据
    const list = this.priceList.length > 1 ? this.list.filter((item: any) => !/^price_*/.test(item.id)) : this.list
    // 有两个价格sort项时 需要合并到一起并指定放在第二个位置，通过手动加 is_price 字段来判断
    if (this.priceList.length > 1) {
      this.showList = list
      this.showList.splice(1, 0, { ...this.priceList[0], is_price: true })
      this.showList = this.showList.slice(0, this.firstShowLength)
    } else {
      this.showList = this.list.slice(0, this.firstShowLength)
    }
    this.hideList = list.slice(this.firstShowLength)
  }

  // index用于价格二级sort点击后更新一级菜单
  selectItem(item: any, index?: number) {
    if (item.is_price) { return }
    this.updateMenu(index)
    const value = item[this.valueKey]
    this.$emit('change', value)
    this.$emit('input', value)
  }

  updateMenu(index?: number) {
    this.close()
    if (index !== undefined) {
      this.showList[1] = { ...this.priceList[index], is_price: this.priceList.length > 1 }
      const poptip: any = Array.isArray(this.$refs.pricePoptip)
        ? this.$refs.pricePoptip[0]
        : this.$refs.pricePoptip
      // eslint-disable-next-line no-unused-expressions
      poptip?.hide()
    }
  }

  findIndex(list?: any[]) {
    return (list || this.showList).findIndex((e: any) => e[this.valueKey] === this.value)
  }

  // 与展示的最后一个互换位置
  selectHideItem(item: any, index: number) {
    this.selectItem(item)
    this.hideList.splice(index, 1, this.showList[this.firstShowLength - 1])
    this.showList.splice(this.firstShowLength - 1, 1, item)
    this.$nextTick(() => {
      setTimeout(() => {
        this.close()
      }, 100)
    })
  }

  show() {
    this.showHideList = !this.showHideList
  }

  close() {
    this.showHideList = false
  }

  getActiveStatus(item: any) {
    return this.value === item[this.valueKey]
  }
}
</script>

<style lang="scss" scoped>
.hotel-tag-separated {
  border-radius: $radius-xl;
  margin-bottom: 12px;
  background-color: #ffffff;
  position: relative;
  .show-list {
    display: flex;
    align-items: center;
    position: relative;
    border-radius: $radius-xl;

    .tag-item {
      text-align: center;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      position: relative;
      padding: 16px 12px;
      box-sizing: border-box;
      flex-grow: 1;
      cursor: pointer;
      > div {
        width: 100%;
        ::v-deep .klk-poptip-reference {
          justify-content: center;
          padding: 12px;
          margin: -12px;
        }
      }
      @include font-body-s-bold;
      &:hover {
        background: $color-bg-2;
      }

      &:not(:last-child):after {
        content: "";
        width: 1px;
        height: 16px;
        background: $color-border-dim;
        position: absolute;
        top: 50%;
        transform: translate(-50%, -50%);
        right: -1px;
        z-index: 1;
      }

      &:first-child {
        border-radius: $radius-xl 0 0 $radius-xl;
      }
      &:last-child {
        border-radius: 0 $radius-xl $radius-xl 0;
      }

      ::v-deep .klk-poptip-popper {
        overflow: hidden;
      }

      ::v-deep .klk-poptip-reference {
        display: flex;
        .price-icon {
          flex: 0 0 auto;
          margin-left: 4px;
        }
      }

      ::v-deep .klk-poptip-popper-inner {
        padding: 0;
        overflow: hidden;
      }
    }

    .sub-item {
      padding: 12px 16px;
      display: flex;
      justify-content: space-between;
      color: $color-text-primary;
      @include font-body-s-regular;
      cursor: pointer;
      &:hover {
        background: $color-bg-2;
      }
    }

    .active {
      @include font-body-s-bold;
      color: $color-brand-primary;
    }

    .show_more_icon {
      margin-left: 20px;
      margin-right: 20px;
      cursor: pointer;
      flex: 0 0 auto;
    }
  }

  .hide-list {
    position: absolute;
    background: $color-white;
    right: 0;
    top: calc(100% + 10px);
    box-shadow: 0px 4px 20px rgba(0, 0, 0, 0.08);
    border-radius: 8px;
    z-index: 2;
    cursor: pointer;
    .tag-item {
      padding: 12px 20px;
      @include font-body-s-regular;
      color: $color-text-primary;
      &:hover {
        background: $color-bg-2;
      }
    }
  }
}
</style>
