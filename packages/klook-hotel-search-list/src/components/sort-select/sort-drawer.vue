<template>
  <div
    v-galileo-click-tracker="{spm: 'SortByEdit'}" 
    class="sort-drawer--entrance"
    :class="!sortList || !sortList.length ? 'disabled' : sortValue && sortValue !== defaultValue && 'active'"
    v-bind="_$ihtHelper.getModuleIHT({spm:'SortByEdit', manual: true })"
    @click.stop="toggleShow"
  >
    <slot>
      <icon-sort :size="16" />
      <span>{{ currentTitle }}</span>
    </slot>
    <client-only>
      <klk-bottom-sheet
        :visible.sync="sortDrawerVisible"
        :title="$t('28270')"
      >
        <div
          v-for="item in sortList"
          :key="item.id"
          v-galileo-click-tracker="{spm: 'SortByViewResult'}" 
          v-bind="_$ihtHelper.getModuleIHT({ spm: 'SortByViewResult', manual: true })"
          class="sort-drawer--item"
          :class="{ selected: sortValue === item.id }"
          @click="selectSort(item.id)"
        >
          <span>{{ item.title }}</span>
          <icon-check :size="20" />
        </div>
      </klk-bottom-sheet>
    </client-only>
  </div>
</template>

<script lang="ts">
import { Vue, Component, Prop, Model } from 'vue-property-decorator'
import { IconSort, IconCheck } from '@klook/klook-icons'

@Component({ 
  inheritAttrs: false, 
  components: { 
    IconSort,
    IconCheck
  } 
})
export default class SortDraw extends Vue {
  @Model('change', { type: String }) sortValue!: string
  @Prop({ type: String }) defaultValue!: string
  @Prop({ type: Number }) offsetTop!: number
  @Prop({ type: Array, default: () => [] }) sortList!: {
    id: string
    title: string
    title_en: string
    selected: boolean
  }[]

  sortDrawerVisible = false

  get currentTitle() {
    const curValue = this.sortList.find(item => this.sortValue === item.id)
    return curValue?.title || this.$t('28270')
  }

  toggleShow() {
    this.sortList && this.sortList.length && (this.sortDrawerVisible = !this.sortDrawerVisible)
  }

  selectSort(id: string) {
    this.$emit('change', id)
    this.sortDrawerVisible = false
  }

  mounted() {
  }
}
</script>

<style lang="scss" scoped>
  .sort-drawer--item {
    padding: 12px 0;
    display: flex;
    @include font-body-s-regular;
    justify-content: space-between;
    .i-icon-icon-check {
      margin-left: 8px;
      visibility: hidden;
    }

    &.selected {
      color: $color-brand-primary;
      .i-icon-icon-check {
        visibility: visible;
      }
    }
  }
  .sort-drawer--entrance {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    & > .i-icon-icon-sort {
      margin-right: 8px;
    }

    &.disabled {
      color: $color-text-disabled;
      & > .i-icon-icon-sort {
        color: $color-text-disabled;
      }
    }
  }
</style>
