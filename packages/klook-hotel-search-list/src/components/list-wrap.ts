import { Vue, Prop, Component } from 'vue-property-decorator'
import { IconChevronDown } from '@klook/klook-icons'

@Component({
  components: {
    IconChevronDown
  }
})
export default class ListWrap extends Vue {
  @Prop() spmModule!: {spm: string, [key: string]: any} | string
  @Prop({ default: null }) spmModuleFunc!: Function
  @Prop({ default: 5 }) count!: number // 如果不是expandAll情况下则是一次展开几个, 出现错误时候查看children两端内容是不是有空格影响了计算
  @Prop({ default: true }) expandAll!: boolean // 一次性展开, 且固定有展开收起按钮
  @Prop({ default: false }) isCustomExpand!: boolean // 自定义打开
  @Prop({ type: Number, default: Infinity }) splitIndex!: number // 传入展开的位置
  @Prop({ default: '' }) viewMoreText!: boolean
  @Prop({ default: '' }) viewLessText!: boolean

  showCount: number = (() => this.splitIndex > 0 ? 0 : this.count)()

  isExpanded: boolean = false // 在expandAll true时候展开状态

  get spm() {
   if (typeof this.spmModule === 'string') {
      return this.spmModule
    } else if (this.spmModule && this.spmModule.spm) {
      return this.spmModule.spm
    }
    return ''
  }
  get spmModuleAttrs() {
    if (this.spmModule) {
      return {
        'data-spm-module': typeof this.spmModule === 'string' ? this.spmModule : this._$ihtHelper.getModuleIHTStr({
          spm: this.spmModule.spm as any,
          ext: {
            ...(this.spmModule.ext || {}),
            Action: this.isExpanded ? 'show more' : 'show less'
          }
        }),
        'data-spm-virtual-item': '__virtual'
      }
    }
  }

  render(h: this['$createElement']) {
    const children = this.$slots.default || []
    if (children.length > 0) {
      let hiddenCount = 0

      if (this.expandAll) {
        // 如果配置是expandAll则需要展开和收起全部
        hiddenCount = this.isExpanded ? -children.length : children.length - this.splitIndex
      } else {
        hiddenCount = children.length - this.splitIndex - this.showCount
      }

      return h('div', hiddenCount > 0 || (this.expandAll && this.isExpanded)
        ? children.slice(0, -hiddenCount).concat(h('div', {
          attrs: this.spmModuleAttrs,
          directives: [
            {
              name: 'galileo-click-tracker',
              value: {
                spm: this.spm.split('?')[0],
              }
            }
          ],
          class: `list-wrap-view-more ${this.isExpanded ? 'expanded' : 'collapsed'}`,
          on: {
            click: () => {
              if (this.isCustomExpand) {
                this.$emit('expand', this.isExpanded)
                return
              }
              if (this.expandAll) {
                this.isExpanded = !this.isExpanded
                this.$emit('expand', this.isExpanded)
              } else {
                this.showCount += this.count
              }
            }
          }
        }, [
          h('span', this.text),
          h('icon-chevron-down', {
            style: {
              marginLeft: '8px'
            },
            props: {
              size: 20
            }
          })
        ]))
        : children
      )
    }
  }

  get text() {
    if (this.expandAll) {
      return this.isExpanded ? (this.viewLessText || this.$t('14642')) : this.viewMoreText
    } else {
      return this.viewMoreText || this.$t('29529')
    }
  }
}
