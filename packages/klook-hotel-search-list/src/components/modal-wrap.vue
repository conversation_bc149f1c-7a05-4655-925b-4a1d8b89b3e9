<template>
  <mobile-page-layer
    v-if="type === 'page' || type === 'layer'"
    v-bind="modalProp"
    @back="back"
    @close="close"
  >
    <div
      v-if="!!$slots['header-right']"
      slot="header-right"
      class="header-right"
    >
      <slot name="header-right" />
    </div>

    <slot />

    <div v-if="!!$slots.footer" slot="footer">
      <slot name="footer" />
    </div>
  </mobile-page-layer>
  <component
    :is="modelCom"
    v-else
    :class="{
      'modal-wrap-mweb': !com && isMobile,
      'ui-custom-right-modal': !com && !isMobile
    }"
    v-bind="modalProp"
    @hide="close"
    @close="close"
    @back="back"
    @on-cancel="close"
    @on-confirm="confirm"
  >
    <div v-if="!!$slots.header" slot="header"></div>

    <!-- 目前只有bottom-sheet -->
    <div
      v-if="isMobile && (!!$slots['header-left'] || tabIndex > 0)"
      slot="header-left"
      class="header-left"
    >
      <slot name="header-left">
        <icon-back :size="24" @click.native="$emit('back')" />
      </slot>
    </div>

    <!-- 头部右侧按钮 -->
    <div
      v-if="!!$slots['header-right']"
      :slot="isMobile ? 'header-right' : 'header-expand'"
      class="header-right"
    >
      <slot name="header-right" />
    </div>

    <div
      v-if="!!$slots['header-plus']"
      :slot="isMobile ? 'nav' : 'header-plus'"
      class="header-plus"
    >
      <slot name="header-plus" />
    </div>

    <div
      v-if="isMobile"
      class="container-content-main"
      :class="{ animate }"
      :style="
        tabIndex ? `transform: translate3d(${tabIndex * -100}%, 0, 1px)` : ''
      "
    >
      <slot />
    </div>
    <slot v-else />

    <div v-if="!!$slots.footer" slot="footer">
      <slot name="footer" />
    </div>
  </component>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator'
import { IconBack } from '@klook/klook-icons'
import MobilePageLayer from './mobile-page-layer.vue'

@Component({
  components: {
    // RightModal: () => import('~/components/common/right-modal.vue'),
    MobilePageLayer,
    IconBack
  }
})
export default class ModalWrap extends Vue {
  @Prop(Boolean) readonly isMobile!: boolean
  @Prop({ type: Object, default: () => ({}) }) readonly config!: object
  //  page 为mobile内页div全屏（相对定位, 例如申请取消）, layer为mobile的全屏layer（绝对定位），modal为两端的弹窗，
  @Prop({ type: String, default: 'modal' }) readonly type!:
    | 'modal'
    | 'page'
    | 'layer'

  // 自定义组件
  @Prop({ type: String, default: '' }) readonly com!: string
  @Prop({ type: String }) readonly title!: string

  @Prop(Boolean) readonly visible!: boolean
  @Prop({ type: Boolean, default: true }) animate!: boolean
  @Prop({ type: Number, default: 0 }) tabIndex!: number

  get modelCom() {
    if (this.com) {
      // 支持例如div外壳. 这样就支持5种形态
      return this.com
    }
    return this.isMobile ? 'klk-bottom-sheet' : 'right-modal'
  }

  get modalProp() {
    const { isMobile, visible, title, type, animate, tabIndex } = this
    return Object.assign(
      {
        isMobile,
        visible,
        title,
        type,
        animate,
        tabIndex
      },
      this.config
    )
  }

  back() {
    this.$emit('back')
  }

  close() {
    this.$emit('update:visible', false)
    this.$emit('close')
  }

  confirm() {
    this.$emit('confirm')
  }
}
</script>

<style lang="scss" >
.modal-wrap-mweb.klk-bottom-sheet  {
  .header-left {
    .i-icon-icon-back {
      color: $color-text-primary;
    }
  }

  .klk-bottom-sheet-body {
    padding: 0 !important;
    overflow: hidden;
    position: relative;
    flex: 1;
    min-height: 0;
    display: flex;
    flex-direction: column;
    & > .container-content-main {
      flex: 1;
      min-height: 0;
      height: 100%;
      display: flex;
      & > div {
        flex: 0 0 100%;
        padding: 16px 20px 64px;
        overflow-y: auto;
        position: relative;
      }
      &::v-deep > div {
        flex: 0 0 100%;
        padding: 16px 20px 64px;
        overflow-y: auto;
        position: relative;
      }
      &.animate {
        transition: 0.3s transform;
      }
    }
  }
}
</style>
