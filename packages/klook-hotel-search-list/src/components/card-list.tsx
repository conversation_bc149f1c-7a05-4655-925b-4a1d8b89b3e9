// @ts-ignore
import Card from '@klook/hotel-card'
import OfferTag from './offer-tag.vue'
import EmptyView from './empty-view.vue'

import { Component, Vue } from 'vue-property-decorator'

@Component({
  components: {
    EmptyView,
    OfferTag,
    Card
  }
})
export default class HotelCardList extends Vue {
  render() {
    const ctx = this.$parent as any
    const { listResult, isMobile, paramsInfo, selectOfferTag, isOnlyDate, isNewCard } = ctx
    const content = listResult.content || []
    const len = content.length
    const target = ctx.isMobile ? '_self' : '_blank'
    const renderCard = Card // long-card 只有在desktop 注册过的组件
    const cardAttrs = {
      type: isMobile ? undefined : 'long',
      platform: isMobile ? 'mobile' : 'desktop',
      scene: 'listHorizontal',
      skeletonMode: 'dark',
      isAb: isNewCard
    }
    let cardIndex = 0
    let headerIndex = 0
    const scopedSlots: any = {
      banner: (tagInfo: any) => <OfferTag
        tagInfo={tagInfo}
        isMobile={isMobile}
        onClick={selectOfferTag}
      />,
      _header: ({ result_count, left_icon, right_text }: Record<string, string>) => {
        if (!isOnlyDate && result_count) {
          return
        }
        const reg = /{[^}]+}/
        const countRender = result_count ? `<b>${result_count}</b>` : ''
        headerIndex++
        return <div class="content-header">
          {left_icon && <img class="content-header--img" src={left_icon} />}
          <p domProps={{
            innerHTML: reg.test(right_text)
              ? right_text.replace(reg, countRender)
              : right_text + countRender
          }} />
        </div>
      },
      card: (cardProps: Record<string, any>) => {
        return <renderCard
          class={cardProps.cardType.includes('precise') && 'precise-card'}
          {...{ directives: [{ name: 'galileo-click-tracker', value: { spm: 'ListingHotel_LIST', enforce: 'post' }}] }}
          attrs={{
            ...cardProps,
            ...ctx._$ihtHelper.getModuleIHT({
              oidName: 'hotel',
              oidValue: cardProps.cardInfo.hotel_info?.hotel_id || 'NA',
              spm: 'ListingHotel_LIST',
              idx: cardProps.index,
              len: listResult.total_count + Number(paramsInfo.stype === 'hotel'),
              // @ts-ignore
              ext: Object.assign(cardProps.cardInfo.report || {}, cardProps.cardInfo.iht_ext_info || {}, { SearchID: listResult.search_id || '', AddToWishList: cardProps.cardInfo?.wish })
            }),
            showMap: false,
            showFavorite: true,
            favoriteSpm: ctx._$ihtHelper.getModuleIHTStr({
              spm: 'HotelsWishLIST',
              manual: true,
              ext: {
                AddToWishList: cardProps.cardInfo?.wish,
                HotelID: cardProps.cardInfo.hotel_info?.hotel_id || 'NA'
              }
            }),
            ...cardAttrs
          }}
          onSaved={({ status, message, hotel_id }: { hotel_id: number, status: boolean, message?: string }, $el: HTMLElement) => {
            if (message) {
              ctx.$toast(message || ctx.$t('jrpass_no_internet'))
            } else {
              ctx._$ihtHelper.updateBinding($el, {
                ext: {
                  AddToWishList: status,
                }
              })
              const wishEl = $el.querySelector('.hotel-add2favor') as HTMLElement
              ctx._$ihtHelper.updateBinding(wishEl, {
                ext: {
                  AddToWishList: status,
                  HotelID: String(hotel_id) || 'NA'
                }
              })
              setTimeout(() => {
                ctx.$inhouse.track('action', wishEl)
              }, 10)
              // showWishSuccessToast(ctx, status) @damon
            }
          }}
          onView-map={(mapProps: any) => {
            (ctx.$refs.map as any).show(true, mapProps)
          }}
        />
      }
    }
    return !len
      ? <EmptyView
        style="margin: 60px 0"
        type={ctx.requestError ? 'error2' : 'empty'}
        on={{
          empty: ctx.emptyAction,
          error2: ctx.formatListQuery
        }}
      />
      : <div>
        {
          content.map(({ type, data } = {}) => data && type && (
            type.includes('card')
              ? scopedSlots.card({
                href: data.deep_link,
                cardInfo: data,
                cardType: type,
                index: cardIndex++
              })
              : scopedSlots[type]?.(data)
          ))
        }
      </div>


  }
}
