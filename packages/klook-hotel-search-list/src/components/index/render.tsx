import { Component, Prop, Provide, Watch, Vue } from 'vue-property-decorator'

import { ScreenPlatform } from '../../../types/index.d'
import SelectorConfig from './selector-config'
import { HotelFilter, PoiInfo, ParamsInfo, ListResult, ListParams } from '../../../types/index'
import { windowScrollTop, dateQueryType } from '../../util/index'
import throttle from 'lodash/throttle'
import { SelectorDesktop, LayerCalendar, LayerDestination, LayerGuest } from '@klook/hotel-selector'
import { IconHotel, IconChevronDown, IconNumberOfRooms, IconUser, IconNext, IconTriangleDown } from '@klook/klook-icons'
import CardList from '../card-list'
import { getHotelFilter } from '../../util/axios'
import MapEntrance from '../map-entrance/index.vue'
import FilterSection from '../filter-selector-v2/filter-section.vue'
import FilterLayer from '../filter-selector-v2/filter-layer.vue'
import TagSeparated from '../sort-select/tag-separated.vue'
import SortDrawer from '../sort-select/sort-drawer.vue'

// import '@klook/hotel-selector/lib/index.css';
import '@klook/hotel-card/dist/cjs/index.css'

@Component({
    components: {
        CardList,
        SelectorDesktop,
        LayerCalendar,
        LayerDestination,
        LayerGuest,
        IconHotel,
        IconNext,
        IconNumberOfRooms,
        IconUser,
        IconTriangleDown,
        IconChevronDown,
        FilterSection,
        FilterLayer,
        TagSeparated,
        MapEntrance,
        SortDrawer
    }
})
export default class HotelSearchList extends SelectorConfig {
    // 这里直接传入响应式的platform
    @Prop({ default: '' }) screenPlatform!: ScreenPlatform
    @Prop({ type: Object, default: null }) poiInfo!: PoiInfo
    @Prop({ type: Number, default: 0 }) top!: number
    @Prop({ type: Object, default: null }) listResult!: ListResult
    @Prop({ type: Function, default: null }) getHotelFilter !: Function

    @Provide('isMobile')
    get isMobile() {
        return this.screenPlatform !== 'desktop'
    }

    areaFilterType = ['poi', 'district', 'subway', 'airportAndTrain']

    paramsInfo: any = this.initParamsInfo()
    listParams: ListParams = {
        filter_list: [],
        limit: 20,
        current_page: 1,
        sort: ''
    }



    hotelFilter: HotelFilter = {}

    filterPin = false
    filterLoading = true
    showFilter = true
    filterSectionHeight = 0
    throttleScroll: any = throttle(() => {
        this.scroll()
    }, 200)


    get isNewCard() {
        return !!this.listResult?.is_new_card_style
    }

    get getHotelFilterFetch() {
        return this.getHotelFilter ? this.getHotelFilter : (data: any) => getHotelFilter(this, this.paramsInfo)
    }

    get isOnlyDate() {
        return dateQueryType(this.paramsInfo).onlyDate
    }

    get filterSelected() {
        const { area_filter_list } = this.hotelFilter
        const area: string[] = []
        const filter = (this.listParams.filter_list || []).slice()
        
        if (area_filter_list?.length) {
          area_filter_list.forEach(({ id, sub_filter_list }) => {
            if (id && sub_filter_list?.length) {
              for (let index = 0; index < filter.length; index++) {
                if (filter[index].includes(id)) {
                  area.push(filter.splice(index, 1)[0])
                  index--
                }
              }
            }
          })
        }
        return { area, filter }
      }

    created() {
    }

    render() {
        return <div class={[`hotel-search-list-${this.isMobile ? 'mobile' : 'desktop'}`, {
            'hotel-search-list': true,
            'filter-pin': this.filterPin
        }]}>
            {this.isMobile && this.renderMobileFilter()}
            {this.renderHeader()}
            {this.renderMain()}
            {this.renderViewMoreButton()}
        </div>
    }

    @Watch('paramsInfo', { deep: true })
    formatListQuery() {
        this.jumpListPage(Object.assign({}, this.getJumpListExtraQuery(), this.paramsInfo))
    }

    @Watch('isMobile')
    isMobileChange() {
        this.hotelFilter = this.formatHotelFilter(this.hotelFilter)
    }

    mounted() {
        this.fetchFilterList()
        const cardData =  this.listResult?.content?.filter(item => item.type.includes('card'))
        this.$emit('get-list-data', {
            cardNum: cardData?.length || 0
        })
        window.addEventListener('scroll', this.throttleScroll)
    }

    beforeDestroy() {
        window.removeEventListener('scroll', this.throttleScroll)
    }

    renderHeader() {
        return this.$scopedSlots.selector ?  this.$scopedSlots.selector({
            paramsInfo: this.paramsInfo,
            selectorOptions: this.selectorOptions,
            onSearch: this.onSearch,
            isMobile: this.isMobile
        }) : (this.isMobile ? <div class='hotel-search-list-header'>
            <div class="hotel-selector-mobile-poi">
                <layer-destination attrs={this.selectorOptions.destinationConfig}
                    options={this.paramsInfo}
                    onChange={this.onSelectorChange}
                    scopedSlots={{
                        default: ({ valueDesc }: any) => {
                            return <div class="selector-destination">
                                <icon-hotel theme="outline" size={20} class="hotel-icon" />
                                <div class="content">{valueDesc}</div>
                                {/* v-if="destinationList && destinationList.length > 1" */}
                                <icon-chevron-down class="icon-down" theme="outline" size={20} />
                            </div>
                        }
                    }} />
                <div class="hotel-selector-mobile-main">
                    <layer-calendar ref="layerCalendar" class="selector-calendar" onChange={this.onSelectorChange} scopedSlots={{
                        default: ({ valueDesc }: any) => {
                            return <div class="selector-calendar-main">
                                {valueDesc}
                            </div>
                        }
                    }} />
                    <layer-guest class="selector-guest" attrs={this.selectorOptions.calendarConfig} onChange={this.onSelectorChange} scopedSlots={{
                        default: ({ valueDesc }: any) => {
                            return <div class="selector-guest-main">
                                <div class="rooms">
                                    <icon-number-of-rooms
                                        theme="filled"
                                        size="16"
                                        class="hotel-icon"
                                    />
                                    {this.paramsInfo.room_num}
                                </div>
                                <div class="adults">
                                    <icon-user theme="filled" size="16" class="hotel-icon" />
                                    {this.paramsInfo.adult_num + this.paramsInfo.child_num}
                                </div>
                            </div>
                        }
                    }} />
                </div>
                <klk-button
                    class="hotel-selector-mobile-search"
                    block
                    size="small"
                    data-spm-module="SearchButton"
                    {...{ directives: [{ name: 'galileo-click-tracker', value: { spm: 'SearchButton' } }] }}
                    nativeOnClick={() => this.onSelectorChange()}
                >
                    {this.$t('car_rental_home_search')}
                </klk-button>
            </div>
        </div> : <div class='hotel-search-list-header'  style={{top: `${this.top || 0}px`}}>
            <selector-desktop
                ref="selector"
                paramsInfo={this.paramsInfo}
                attrs={this.selectorOptions}
                onSearch={this.onSearch}
            />
        </div>)

    }

    renderMain() {
        return this.isMobile ? <div class="hotel-search-result--content">
            {this.renderSortDrawer()}
            {this.renderCardList()}
        </div> : <div class="hotel-search-result--content">
            {this.renderFilterLoading()}
            {this.renderDesktopFilter()}
            {this.renderMainList()}
        </div>
    }

    renderMainList() {
        return <div class="right-card-list">
            <klk-skeleton v-Show={this.filterLoading} animate mode="dark">
              <klk-skeleton-block height="50" radius-size="max" style="margin: 0 0 12px 0;" />
            </klk-skeleton>
            {this.listResult?.content?.length > 0 && this.hotelFilter.sort_list && this.hotelFilter.sort_list.length > 0 && <tag-separated
                value={this.listParams.sort}
                list={this.hotelFilter.sort_list}
                onChange={this.changeSort}
                valueKey="id"
                nameKey="title"
                {...{ directives: [{ name: 'galileo-click-tracker', value: { spm: 'SortByEdit' } }] }}
                attrs={this._$ihtHelper.getModuleIHT({ spm: 'SortByEdit' })}
            />}
            <card-list />
        </div>
    }

    renderCardList() {
        return <card-list />
    }
    renderFilterLoading() {
        return <klk-skeleton v-show={this.filterLoading} animate mode="dark" class="left-filter">
            <klk-skeleton-block radiusSize="max" height={640} />
        </klk-skeleton>
    }

    renderMobileFilter() {
        return <div v-Show={this.showFilter} class="header-filter" style={{top: `${this.top || 0}px`}}>
            <filter-layer
                ref="areaLayer"
                value={this.filterSelected.area}
                class="header-filter--item"
                type="area"
                hotelFilter={this.hotelFilter}
                onChange={this.changeFilter}
                isMobile={this.isMobile}
                entranceTitle={this.$t('80130')}
                {...{ directives: [{ name: 'galileo-click-tracker', value: { spm: 'AreaSelect' } }] }}
                data-spm-module="AreaSelect?trg=manual"
            />
            <filter-layer
                ref="filterLayer"
                value={this.filterSelected.filter}
                class="header-filter--item"
                hotelFilter={this.hotelFilter}
                isMobile={this.isMobile}
                onChange={this.changeFilter}
            />
        </div>
    }
    renderDesktopFilter() {
        return <client-only>
            <div class="left-filter" v-show={!this.filterLoading && this.showFilter}>
                <map-entrance onOpen-map={() => this.jumpListPage(Object.assign({}, this.paramsInfo, this.listParams, { pageSource: 'map' }))} />
                <filter-section
                    isMobile={this.isMobile}
                    value={this.listParams.filter_list}
                    hotelFilter={this.hotelFilter}
                    class="search-result-filter-container"
                    {...{ directives: [{ name: 'galileo-click-tracker', value: { spm: 'FilterViewResult' } }] }}
                    attrs={this._$ihtHelper.getModuleIHT({ spm: 'FilterViewResult', manual: true })}
                    filterPin={this.filterPin}
                    getHotelFilter={this.getHotelFilterFetch}
                    onChange={this.changeFilter}
                /></div>
        </client-only>
    }

    renderViewMoreButton() {
        const { total_count } = this.listResult
        return +total_count >= 12 && <div class="hotel-search-list-bottom">
            <klk-button class="hotel-search-list-viewmore" type="outlined" size={this.isMobile ? 'small' : 'large'} onClick={() => this.jumpListPage(this.paramsInfo)}
            {...{ directives: [{ name: 'galileo-click-tracker', value: { spm: 'ViewMore' } }] }} data-spm-module="ViewMore" data-spm-virtual-item="__virtual">
                <div>
                    <span>{this.$t('84644')}</span><icon-next size={16} />
                </div>
            </klk-button>
        </div>
    }

    renderSortDrawer() {
        return <div class="search-list-sort-top">
            <sort-drawer
                sortValue={this.listParams.sort}
                defaultValue={this.hotelFilter.sort_selected}
                sortList={this.hotelFilter.sort_list}
                onChange={this.changeSort} />
            <span> {this.$t('18772', { result_count: this.listResult.total_count})}</span>
        </div>
    }

    initParamsInfo() {
        const { city_id, city_name, country_name, stype, svalue } = this.poiInfo || {}
        return {
            room_num: 1,
            adult_num: 2,
            child_num: 0,
            age: '',
            city_id: city_id || '',
            stype: stype || '',
            svalue: svalue || '',
            override: city_name + (country_name ? ', ' + country_name : ''),
            title: city_name,
        }
    }

    emptyAction() {
        if(this.isMobile) {
          this.$refs.layerCalendar?.show()
        } else {
            this.$refs.selector?.show('calendar')
        }
    }

    onSearch(paramsInfo: ParamsInfo) {
        this.paramsInfo = paramsInfo
    }

    scroll() {
        if (this.isMobile) {
            return
        }
        const filterSectionEl = this.$el.querySelector('.search-result-filter-container') as HTMLElement
        if (!filterSectionEl) {
            return
        }
        const rightCardList = this.$el.querySelector('.right-card-list') as HTMLElement
        if(rightCardList.scrollHeight < filterSectionEl.scrollHeight ) {
            return
        }
        const offsetTop = 100 + 124
        const scrollTop = windowScrollTop()
        if (!this.filterPin) {
            const height = filterSectionEl!.scrollHeight as number
            if (scrollTop > (height + offsetTop)) {
                this.filterSectionHeight = height
                this.filterPin = true
            }
        } else if (scrollTop < (this.filterSectionHeight + offsetTop)) {
            this.filterPin = false
        }
    }

    async fetchFilterList() {
        this.filterLoading = true
        this.getHotelFilterFetch(this.paramsInfo).then((data: HotelFilter) => {
            this.hotelFilter = this.formatHotelFilter(data)
            this.listParams.sort = this.hotelFilter.sort_selected || ''
            this.listParams.filter_list = this.hotelFilter.filter_selected || []
        }).finally(() => {
                this.filterLoading = false
                this.showFilter = this.listResult?.content?.length > 0
            })

    }

    // 由于只请求一次，需要前端抹平差异
    formatHotelFilter(originHotelFilter: HotelFilter) {
        const { filter_list, ...other } = originHotelFilter
        let filterList = filter_list?.slice() || []
        if (!filterList.length) {
            return originHotelFilter
        }
        const areaFilterType = this.areaFilterType
        const insetIndex = ['promotionTag', 'rating']
        let areaFilter = (originHotelFilter.area_filter_list || []).concat(
            filterList.filter(filter => areaFilterType.includes(filter.id)) || []
        ).slice() || []

        filterList = filterList.filter(filter => !areaFilterType.includes(filter.id))

        console.log('filter change')
        if (this.isMobile) {
            return {
                ...other,
                area_filter_list: areaFilter,
                filter_list: filterList
            }
        } else {
            const findIndex = filterList.findIndex(filter => insetIndex.includes(filter.id)) || filterList.length - 1
            filterList.splice(findIndex, 0, ...areaFilter)
            return {
                ...other,
                area_filter_list: [],
                filter_list: filterList
            }
        }
    }

    getJumpListExtraQuery() {
        const { filter_list, sort } = this.listParams
        return Object.assign({}, this.paramsInfo, {
            sort_selected: sort && sort !== this.hotelFilter.sort_selected ? sort : undefined, 
            filter_selected: filter_list.length
            ? filter_list.toString()
            : undefined
        })
    }

    changeFilter(list: ListParams['filter_list']) {
        this.listParams.filter_list = list
        this.jumpListPage(this.getJumpListExtraQuery())
    }

    changeSort(value: string) {
        this.listParams.sort = value
        this.jumpListPage(this.getJumpListExtraQuery())
    }

    selectOfferTag(tagInfo: any) {
        if (!tagInfo?.filter_id) {
            return false
        }
        if (!this.listParams.filter_list.includes(tagInfo.filter_id)) {
            this.listParams.filter_list.push(tagInfo.filter_id)
        }

        this.jumpListPage(this.getJumpListExtraQuery())
    }

    onSelectorChange(paramsInfo: any = {}) {
        this.jumpListPage(Object.assign({}, this.getJumpListExtraQuery(), paramsInfo))
    }
}

