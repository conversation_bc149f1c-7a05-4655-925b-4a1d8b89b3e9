import { Vue, Component, Prop } from 'vue-property-decorator'

import { getDestinationList, getHotelSuggest } from '../../util/axios'
import { queryString } from '../../util'

@Component({
})
export default class selectorConfig extends Vue {
  @Prop({ type: Function, default: null }) getHotelSuggest !: Function
  @Prop({ type: Function, default: null }) getDestinationList !: Function
  @Prop({ type: Number, default: 365 }) dateRange !: number

  paramsInfo: Record<string, any> = {}


  get selectorOptions() {
    return {
      needHistory: true,
      calendarConfig: {
        maxDate: this.dateRange
      },
      destinationConfig: {
        needLocation: true,
        suggest: this.getHotelSuggest || ((params: any) => getHotelSuggest(this, params)),
        getCityList: this.getDestinationList || ((params: any) => getDestinationList(this, params)),
      }
    }
  }

  jumpListPage(newQuery: Object, methods: 'push' | 'replace' = 'push') {
    if(this.$router && this.$route && ['HotelDetail', 'HotelSearchResult', 'HotelCityHub'].includes(this.$route.name)) {
      const { name, params, query } = this.$route
      this.$router[methods]({
        query: { ...query, ...newQuery },
        params,
        name: methods === 'push'
          ? 'HotelSearchResult'
          : name
      })
    } else {
      const query = {...newQuery}
      Object.keys(query).forEach((item: any) => {
        const val = query[item]
        if (![undefined, null, false].includes(val as any)) {
          query[item] = window.encodeURIComponent(val)
        }
      })
      window.location.href = this.$href('/hotels/searchresult/?') + queryString(query)
    }
  }
}
