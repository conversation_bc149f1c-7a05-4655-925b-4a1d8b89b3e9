<script lang="tsx">
import { Component, Prop, Watch } from "vue-property-decorator";
import render from "./render";

@Component({})
export default class HotelSearchList extends render { }
</script>

<style lang="scss" scoped>
@import '../../style/_variables.scss';

.hotel-search-list {
    position: relative;

    .hotel-search-list-bottom {
        display: flex;
        justify-content: center;
        margin-top: 16px;

        .hotel-search-list-viewmore {
            ::v-deep .i-icon-icon-next {
                margin-left: 8px;
                transform: translateY(1px);
            }
        }
    }

    &.hotel-search-list-mobile {
        min-height: 50vh;

        .hotel-search-list-header {
            z-index: 4;
            padding: 16px 16px 20px 16px;
            margin-bottom: 16px;
            border-radius: $radius-xl;
            background-color: $color-bg-1;
            border: solid 1px $color-brand-primary;
            
            .hotel-selector-mobile-poi {
                .hotel-icon {
                    margin-right: 8px;
                }

                ::v-deep .selector-destination {
                    display: flex;
                    align-items: center;
                    margin-bottom: 12px;
                    width: 100%;
                    .hotel-icon {
                        color: $color-brand-primary;
                    }
                    .content {
                        @include text-ellipsis;
                        @include font-body-s-bold;
                    }
                    .icon-down {
                        color: $color-text-primary;
                        transform: rotate(-90deg);
                        margin-left: 4px;
                    }
                }

                .hotel-selector-mobile-main {
                    margin-bottom: 12px;
                    display: flex;
                    border-radius: $radius-l;
                    border: 1px solid $color-border-normal;
                    padding: 16px;

                    ::v-deep .selector-calendar {
                        flex: 1;

                        .selector-calendar-main {
                            @include font-body-m-regular;
                            color: $color-text-secondary;
                        }
                    }

                    ::v-deep .selector-guest {
                        flex-shrink: 0;
                        position: relative;
                        display: flex;
                        align-items: center;
                        .selector-guest-main {
                            display: flex;
                            align-items: center;

                            .rooms {
                                margin-right: 8px;
                            }

                            .rooms,
                            .adults {
                                display: flex;
                                align-items: center;

                                .i-icon {
                                    margin-right: 4px;
                                    color: $color-text-disabled;
                                }
                            }
                        }

                        &::before {
                            content: '';
                            display: block;
                            position: absolute;
                            left: -16px;
                            height: 16px;
                            width: 1px;
                            top: 4px;
                            background-color: $color-border-dim;
                        }
                    }
                }

                .hotel-selector-mobile-search {
                    width: 100%;
                }
            }
        }

        .header-filter {
            display: flex;
            text-align: center;
            margin-bottom: 16px;
            position: sticky;
            padding-top: 12px;
            top: 0;
            background-color: $color-bg-2;
            z-index: 2;

            &--item {
                @include font-body-s-semibold;
                padding: 10px;
                flex: 1;
                display: flex;
                align-items: center;
                justify-content: center;
                margin-right: 8px;
                border-radius: $radius-xxl;
                border: 1px solid $color-border-normal;
                background-color: $color-bg-1;

                &:last-child {
                    margin-right: 0;
                }

                &:not(.map-entrance) {
                    position: relative;

                    &.disabled {
                        color: $color-text-disabled;
                    }
                    &.active {
                        color: $color-text-primary;
                        ::v-deep .i-icon::before {
                        position: absolute;
                        left: -4px;
                        top: -4px;
                        content: '';
                        width: 6px;
                        height: 6px;
                        border-radius: $radius-circle;
                        background-color: $color-brand-primary;
                        }
                    }
                }

                ::v-deep .i-icon-icon-triangle-down {
                    margin-left: 4px;
                    position: relative;
                    overflow: visible;
                }

                ::v-deep span {
                    @include text-ellipsis;
                    min-width: 0;
                    @include font-body-s-semibold;
                }
            }
        }

        .content-header {
            display: flex;
            align-items: center;
            margin-bottom: 14px;
            color: $color-text-secondary;
            @include font-body-s-regular;

            &--img {
                width: 16px;
                height: 16px;
                margin-right: 6px;
            }
        }

  
        .search-list-sort-top {
            margin-bottom: 16px;
            display: flex;
            flex-direction: row-reverse;
            justify-content: space-between;
            .sort-drawer--entrance {

            }
            & > span {
                @include font-body-s-regular;
                color: $color-text-secondary;
                margin-right: 16px;
            }
        }

        ::v-deep .card-skeleton,
        ::v-deep .hotel-card {
            margin-bottom: 16px;

            &.precise-card {
                border: 1px solid $color-brand-primary;
            }
        }

        ::v-deep .hotel-card {
            position: relative;
            border-radius: $radius-l;
            background-color: $color-bg-1;
            padding: 12px;
            box-shadow: none !important;
        }

    }

    &.hotel-search-list-desktop {
        .hotel-search-list-header {
            background-color: $color-bg-1;
            position: sticky;
            top: 0;
            z-index: 4;
            margin-bottom: 20px;

            &::after {
                content: '';
                width: 100vw;
                height: 100%;
                top: 0;
                left:calc(((1160px - 100vw)/2));
                background-color: $color-bg-1;
                z-index: -1;
                position: absolute;
            }

            .hotel-selector-desktop {
                margin: 0 auto;
            }
        }

        .hotel-search-result--content {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            width: 1160px;
            margin: 0 auto;

            .left-filter {
                width: 300px;
                flex: 0 0 300px;
                margin-right: 20px;
                z-index: 3;
                top: 90px;
                display: flex;
                flex-direction: column;
            }

            >.right-card-list {
                flex: 1;
                min-width: 0;

                .content-header {
                    display: flex;
                    align-items: center;
                    padding: 8px 0 12px;
                    @include font-body-m-bold;

                    &--img {
                        display: none;
                        width: 20px;
                        height: 20px;
                        margin: 0 6px 0 0;
                    }
                }


                ::v-deep .hotel-card {
                    position: relative;
                    margin-bottom: 12px;
                    
                    &.precise-card {
                      outline: 1px solid $color-brand-primary;
                    }

                    &+.hotel-card {
                        // padding-top: 12px;
                    }
                }
            }
        }

        .hotel-search-list-bottom {
            margin-top: 20px;
        }

        &.filter-pin {
            .left-filter {
                position: sticky;
                max-height: calc(100vh - 190px);
                top: 154px;
            }
        }
    }
}
</style>