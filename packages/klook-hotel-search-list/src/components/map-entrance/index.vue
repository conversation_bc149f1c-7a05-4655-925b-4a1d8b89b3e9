<template>
    <div v-galileo-click-tracker="{spm: 'Map'}" class="hotel-map-entrance" v-bind="_$ihtHelper.getModuleIHT({ spm: 'Map', manual: true })"
        @click="animateShowLayer">
        <svg class="map-bg" width="600" height="282" viewBox="0 0 600 282" fill="none"
            xmlns="http://www.w3.org/2000/svg">
            <g clip-path="url(#clip0_1578_146760)">
                <rect width="600" height="282" fill="white" />
                <path fill-rule="evenodd" clip-rule="evenodd"
                    d="M236.66 102.636C236.709 103.015 236.734 103.402 236.734 103.795C236.734 105.37 236.33 106.85 235.619 108.137L283.511 190.305C284.06 191.247 285.261 191.576 286.213 191.045L369.663 144.524C370.662 143.967 370.991 142.687 370.385 141.717L317.533 57.1541C316.958 56.2342 315.754 55.9403 314.82 56.4919L236.66 102.636Z"
                    fill="#F5F5F5" />
                <path
                    d="M399.776 66.0299C399.19 65.095 399.472 63.8622 400.406 63.2749L440.657 37.9593C441.593 37.3706 442.829 37.6532 443.417 38.5903L471.439 83.3076C472.024 84.2425 471.742 85.4752 470.809 86.0626L430.558 111.378C429.621 111.967 428.385 111.684 427.798 110.747L399.776 66.0299Z"
                    fill="#B2CBFF" />
                <rect x="165" y="228.125" width="104.249" height="68.9759" rx="2" transform="rotate(-30 165 228.125)"
                    fill="#B2CBFF" />
                <path
                    d="M433.552 119.69C432.971 118.755 433.253 117.526 434.185 116.94L482.294 86.67C482.735 86.3925 483.267 86.2981 483.777 86.4068L518.561 93.8274C519.627 94.0549 520.315 95.095 520.107 96.1655L496.972 215.006C496.625 216.79 494.271 217.223 493.311 215.681L433.552 119.69Z"
                    fill="#7CD8B1" />
                <rect width="386.596" height="5"
                    transform="matrix(0.497677 0.867362 -0.864687 0.502312 146.957 -33.624)" fill="#FCEAC5" />
                <rect width="256.795" height="5" transform="matrix(-0.862709 0.5057 -0.51031 -0.859991 258.092 167.497)"
                    fill="#FCEAC5" />
                <rect width="420.231" height="5" transform="matrix(-0.866025 0.5 -0.504627 -0.863337 335.453 36.3164)"
                    fill="#FCEAC5" />
                <rect width="355.389" height="5"
                    transform="matrix(-0.709902 -0.704301 0.700492 -0.71366 65.293 190.869)" fill="#FCEAC5" />
                <path opacity="0.3" d="M436 -56.5L352.5 -10.5M352.5 -10.5L368 11.5M352.5 -10.5H411" stroke="white"
                    stroke-width="2" />
                <path opacity="0.3" d="M360.25 -74.5L406.25 9M406.25 9L428.25 -6.5M406.25 9L406.25 -49.5" stroke="white"
                    stroke-width="2" />
                <rect width="421.339" height="52.069"
                    transform="matrix(0.533307 0.845922 -0.843056 0.537826 322.332 -44.4023)" fill="#FCEAC5" />
                <path fill-rule="evenodd" clip-rule="evenodd"
                    d="M100.598 59.9002C99.6414 60.4525 99.3137 61.6757 99.8659 62.6323L140.841 133.604C141.394 134.561 142.617 134.888 143.574 134.336L203.969 99.4667C204.736 99.024 205.099 98.1301 205.03 97.2474C205.011 97.0006 205.001 96.7511 205.001 96.4993C205.001 94.9277 205.382 93.4453 206.058 92.1395C206.416 91.4486 206.47 90.6187 206.081 89.9449L167.7 23.4681C167.148 22.5115 165.925 22.1838 164.968 22.7361L100.598 59.9002Z"
                    fill="#7CD8B1" />
                <circle cx="219.771" cy="100" r="7.5" fill="white" stroke="#FCEAC5" stroke-width="5" />
                <path fill-rule="evenodd" clip-rule="evenodd"
                    d="M213.118 114.071L192.444 126.008C191.485 126.561 191.158 127.789 191.715 128.746L202.498 147.278C203.052 148.23 204.273 148.555 205.227 148.004L228.592 134.514C229.551 133.96 229.878 132.733 229.321 131.776L220.071 115.88C217.541 116.17 215.081 115.476 213.118 114.071Z"
                    fill="#F5F5F5" />
                <path
                    d="M206.656 155.252C206.092 154.295 206.416 153.06 207.379 152.504L230.785 138.991C231.735 138.442 232.95 138.762 233.508 139.707L246.029 160.938C246.594 161.895 246.269 163.13 245.306 163.686L221.901 177.199C220.95 177.748 219.735 177.428 219.178 176.483L206.656 155.252Z"
                    fill="#F5F5F5" />
                <path
                    d="M161.444 146.419C160.891 145.46 161.221 144.234 162.182 143.684L185.037 130.581C185.993 130.033 187.213 130.361 187.764 131.316L206.497 163.763C207.05 164.721 206.72 165.947 205.76 166.498L182.904 179.601C181.948 180.149 180.729 179.82 180.178 178.866L161.444 146.419Z"
                    fill="#F5F5F5" />
                <path
                    d="M541.76 73.8148C540.676 73.5991 539.973 72.546 540.189 71.4627L545.556 44.5139C545.772 43.4306 546.825 42.7273 547.908 42.943L584.653 50.2608C585.736 50.4765 586.439 51.5296 586.223 52.6129L580.856 79.5618C580.641 80.6451 579.588 81.3484 578.504 81.1326L541.76 73.8148Z"
                    fill="#F5F5F5" />
                <path
                    d="M287.842 199.902C287.297 198.942 287.633 197.722 288.592 197.176L373.324 148.953C374.259 148.421 375.448 148.725 376.014 149.64L418.407 218.232C419.004 219.197 418.679 220.465 417.691 221.024L330.238 270.516C329.278 271.059 328.059 270.722 327.514 269.763L287.842 199.902Z"
                    fill="#F5F5F5" />
                <path
                    d="M45.9951 214.276C45.4447 213.326 45.7637 212.109 46.7095 211.551L127.263 164.025C128.22 163.46 129.454 163.784 130.01 164.746L148.013 195.852C148.56 196.799 148.246 198.01 147.308 198.571L125.651 211.514C124.729 212.065 124.406 213.245 124.921 214.188L132.074 227.302C132.59 228.249 132.263 229.435 131.334 229.983L76.2365 262.476C75.2797 263.04 74.0464 262.717 73.4898 261.755L45.9951 214.276Z"
                    fill="#F5F5F5" />
                <path
                    d="M332.558 277.748C332 276.783 332.34 275.549 333.312 275.004L420.866 225.916C421.793 225.396 422.964 225.692 423.533 226.589L461.362 286.207C461.979 287.179 461.649 288.471 460.641 289.028L370.217 339.049C369.263 339.577 368.062 339.243 367.517 338.299L332.558 277.748Z"
                    fill="#F5F5F5" />
                <path
                    d="M206.001 292.668C205.448 291.711 205.777 290.487 206.735 289.935L289.507 242.251C290.461 241.702 291.678 242.026 292.233 242.976L327.486 303.369C328.045 304.327 327.718 305.557 326.756 306.11L243.685 353.943C242.728 354.494 241.507 354.166 240.955 353.21L206.001 292.668Z"
                    fill="#F5F5F5" />
                <path
                    d="M516.947 208.205C515.869 207.991 515.166 206.948 515.372 205.868L528.243 138.47C528.398 137.659 529.036 137.026 529.848 136.878L605.084 123.156C606.479 122.901 607.688 124.138 607.402 125.527L587.895 220.255C587.673 221.331 586.625 222.027 585.547 221.813L516.947 208.205Z"
                    fill="#F5F5F5" />
                <rect x="130.428" y="214.506" width="91.5815" height="14.6003" rx="2"
                    transform="rotate(-30 130.428 214.506)" fill="#F5F5F5" />
                <rect x="133" y="160.152" width="26.3054" height="41.1345" rx="2" transform="rotate(-30 133 160.152)"
                    fill="#F5F5F5" />
                <path
                    d="M374.307 28.0854C373.668 27.1116 373.994 25.7999 375.013 25.2371L397.304 12.936C398.249 12.4143 399.438 12.7372 399.99 13.6654L418.493 44.806C419.052 45.7456 418.752 46.9594 417.821 47.5317L397.558 59.9835C396.637 60.5497 395.432 60.2809 394.839 59.3767L374.307 28.0854Z"
                    fill="#F5F5F5" />
                <path
                    d="M385.456 -17.0757C384.896 -18.0045 385.178 -19.2104 386.092 -19.7942L399.617 -28.4327C400.567 -29.0393 401.83 -28.7419 402.409 -27.7753L429.268 17.0431C429.856 18.024 429.503 19.2972 428.494 19.8356L414.452 27.3302C413.514 27.8311 412.347 27.5097 411.798 26.5987L385.456 -17.0757Z"
                    fill="#F5F5F5" />
                <rect x="416" y="32.1279" width="20.2562" height="13.6978" rx="2" transform="rotate(-30 416 32.1279)"
                    fill="#F5F5F5" />
                <rect x="555.713" y="-8.33203" width="21.9238" height="27.4871" rx="2"
                    transform="rotate(10.3892 555.713 -8.33203)" fill="#B2CBFF" />
                <path
                    d="M356.646 -1.65192C356.029 -2.64664 356.395 -3.95603 357.437 -4.48761L379.322 -15.6448C380.265 -16.1252 381.418 -15.7888 381.955 -14.877L394.914 7.15459C395.502 8.15282 395.121 9.43956 394.085 9.95749L372.632 20.6838C371.708 21.1461 370.583 20.8275 370.038 19.9488L356.646 -1.65192Z"
                    fill="#F5F5F5" />
                <path fill-rule="evenodd" clip-rule="evenodd"
                    d="M166.935 -15.0024C166.019 -14.4378 165.714 -13.2495 166.245 -12.3134L220.929 84.1404C221.095 84.1312 221.263 84.1266 221.431 84.1266C223.857 84.1266 226.059 85.0863 227.677 86.6468L305.233 41.5282C306.202 40.964 306.518 39.7128 305.933 38.7558L245.047 -60.7874C244.469 -61.732 243.234 -62.0274 242.291 -61.4465L166.935 -15.0024Z"
                    fill="#F5F5F5" />
                <path
                    d="M545.393 294.134L559.116 224.57C559.329 223.493 560.37 222.788 561.449 222.992L629.083 235.762C630.149 235.963 630.86 236.978 630.686 238.05L619.328 307.613C619.148 308.718 618.096 309.459 616.996 309.258L546.996 296.488C545.898 296.288 545.177 295.228 545.393 294.134Z"
                    fill="#B2CBFF" />
                <rect width="352.366" height="5" transform="matrix(0.186913 -0.982376 0.983363 0.181653 491 277.156)"
                    fill="#FCEAC5" />
                <path
                    d="M579.756 113.937C578.681 113.681 578.017 112.603 578.272 111.529L602.092 -3.51662C602.347 -4.59126 603.425 -5.25539 604.5 -5L642.279 5.16303C643.354 5.41842 644.018 6.49662 643.762 7.57126L616.594 107.9C616.339 108.974 615.26 109.639 614.186 109.383L579.756 113.937Z"
                    fill="#F5F5F5" />
                <path
                    d="M519.509 88.6017L477.338 80.1676C476.803 80.0606 476.334 79.7393 476.042 79.2785L418.19 -11.8378C417.404 -13.0759 418.174 -14.7109 419.629 -14.8942L540.788 -30.1584C542.131 -30.3275 543.25 -29.1426 543.005 -27.8119L521.868 87.0027C521.666 88.1008 520.603 88.8207 519.509 88.6017Z"
                    fill="#F5F5F5" />
                <rect width="352.366" height="5"
                    transform="matrix(-0.986823 0.161805 -0.167083 -0.985943 868.559 75.9297)" fill="#FCEAC5" />
                <path
                    d="M549.142 26.9677L547.851 34.0712C547.656 35.1426 548.354 36.173 549.421 36.3892L585.046 43.6043C586.126 43.823 587.18 43.1268 587.402 42.0474L597.005 -4.59669C597.261 -5.83738 596.313 -7 595.046 -7H586.614C585.672 -7 584.858 -6.34309 584.659 -5.42266L577.409 28.1088C577.181 29.1632 576.158 29.8469 575.096 29.6539L551.468 25.3578C550.381 25.1602 549.34 25.881 549.142 26.9677Z"
                    fill="#F5F5F5" />
                <path
                    d="M593.393 223.509L607.116 153.946C607.329 152.869 608.37 152.164 609.449 152.368L677.083 165.138C678.149 165.339 678.86 166.354 678.686 167.426L667.328 236.989C667.148 238.094 666.096 238.835 664.996 238.634L594.996 225.864C593.898 225.664 593.177 224.604 593.393 223.509Z"
                    fill="#F5F5F5" />
                <path
                    d="M551.594 221.214L516.019 213.559C514.915 213.321 513.833 214.044 513.631 215.156L509.141 239.848C509.05 240.347 509.152 240.861 509.426 241.287L535.41 281.706C535.778 282.278 536.411 282.624 537.092 282.624H540.342C541.304 282.624 542.129 281.94 542.307 280.995L553.139 223.54C553.34 222.474 552.655 221.442 551.594 221.214Z"
                    fill="#F5F5F5" />
                <path
                    d="M-49.2268 97.917C-48.9652 94.5438 -44.8944 92.9986 -42.4604 95.3487L49.1039 183.758C51.0184 185.606 50.6276 188.774 48.3213 190.102L-54.6998 249.415C-57.487 251.02 -60.9323 248.846 -60.6836 245.639L-49.2268 97.917Z"
                    fill="#B2CBFF" />
                <path
                    d="M-57.7505 275.819C-58.308 274.864 -57.9857 273.639 -57.0308 273.082L39.0095 217.099C39.9644 216.542 41.1898 216.865 41.7456 217.821L68.2494 263.39C68.7982 264.334 68.4891 265.543 67.5547 266.107L-27.5243 323.541C-28.4796 324.118 -29.7224 323.801 -30.2853 322.838L-57.7505 275.819Z"
                    fill="#F5F5F5" />
                <path
                    d="M63.6949 290.167C63.1802 289.221 63.5066 288.038 64.4333 287.489L158.906 231.602C159.862 231.037 161.096 231.359 161.654 232.319L210.567 316.56C211.124 317.52 210.793 318.75 209.83 319.301L112.239 375.094C111.265 375.651 110.025 375.299 109.489 374.314L63.6949 290.167Z"
                    fill="#7CD8B1" />
                <path
                    d="M89.9631 57.7915L38.7153 -26.6452C38.5733 -26.8792 38.3847 -27.0816 38.1613 -27.2398L-15.0594 -64.9182C-16.3405 -65.8252 -18.1194 -64.9703 -18.2116 -63.4033L-26.9435 85.0396C-26.9792 85.6466 -26.7368 86.2368 -26.2849 86.6436L1.89425 112.005C2.55059 112.596 3.51664 112.686 4.27116 112.227L89.2923 60.5382C90.2356 59.9647 90.5359 58.7353 89.9631 57.7915Z"
                    fill="#F5F5F5" />
                <path
                    d="M141.267 29.0459L162.659 17.4943C163.662 16.9526 164.01 15.6832 163.423 14.7055L153.445 -1.92488C152.911 -2.81562 151.783 -3.14893 150.85 -2.69201L129.861 7.58827C128.844 8.08645 128.443 9.32902 128.978 10.3277L138.553 28.2294C139.075 29.2061 140.292 29.5722 141.267 29.0459Z"
                    fill="#F5F5F5" />
                <path
                    d="M96.706 54.989L133.867 32.9675C134.783 32.4251 135.113 31.2613 134.619 30.3189L125.394 12.7069C124.895 11.7541 123.732 11.3667 122.761 11.83L82.4915 31.0495C81.4247 31.5587 81.0298 32.877 81.6411 33.8887L93.9745 54.3027C94.5423 55.2424 95.7615 55.5488 96.706 54.989Z"
                    fill="#F5F5F5" />
                <path
                    d="M79.6569 26.6946L148.016 -6.53566C149.079 -7.0523 149.465 -8.37288 148.847 -9.38018L114.414 -65.5105C113.89 -66.3636 112.817 -66.6985 111.901 -66.2944L43.0612 -35.9098C41.9647 -35.4258 41.5372 -34.0926 42.1478 -33.0612L77.0615 25.9147C77.5918 26.8105 78.7206 27.1497 79.6569 26.6946Z"
                    fill="#F5F5F5" />
                <path
                    d="M91.2403 65.1312L11.0874 116.658C10.0143 117.348 9.85261 118.853 10.7547 119.755L67.4082 176.408C68.0514 177.051 69.0475 177.18 69.8326 176.72L134.279 139.007C135.23 138.45 135.552 137.229 134.998 136.276L94.051 65.8088C93.4784 64.8232 92.1991 64.5149 91.2403 65.1312Z"
                    fill="#F5F5F5" />
                <path
                    d="M531.557 118.248L539.112 80.9174C539.328 79.8517 540.355 79.1535 541.426 79.3456L576.453 85.6325C577.57 85.8331 578.297 86.9226 578.052 88.0314L572.301 114.134C572.12 114.954 571.446 115.574 570.614 115.686L533.783 120.627C532.421 120.809 531.284 119.595 531.557 118.248Z"
                    fill="#F5F5F5" />
            </g>
            <defs>
                <clipPath id="clip0_1578_146760">
                    <rect width="600" height="282" fill="white" />
                </clipPath>
            </defs>
        </svg>
        <svg class="location-icon" width="56" height="62" viewBox="0 0 56 62" fill="none"
            xmlns="http://www.w3.org/2000/svg">
            <g filter="url(#filter0_d_1598_61458)">
                <path fill-rule="evenodd" clip-rule="evenodd"
                    d="M45.8375 23.9188C45.8375 34.8229 34.7757 43.2782 30.0347 46.4014C28.7396 47.2545 27.0979 47.2545 25.8028 46.4014C21.0618 43.2782 10 34.8229 10 23.9188C10 14.0225 18.0225 6 27.9188 6C37.815 6 45.8375 14.0225 45.8375 23.9188Z"
                    fill="#FF5B00" />
                <circle cx="27.9189" cy="23.6631" r="6.40909" fill="white" />
            </g>
            <defs>
                <filter id="filter0_d_1598_61458" x="0" y="0" width="55.8379" height="61.0412"
                    filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                    <feFlood flood-opacity="0" result="BackgroundImageFix" />
                    <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                        result="hardAlpha" />
                    <feOffset dy="4" />
                    <feGaussianBlur stdDeviation="5" />
                    <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.14 0" />
                    <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1598_61458" />
                    <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1598_61458" result="shape" />
                </filter>
            </defs>
        </svg>
        <klk-button class="map-btn" type="secondary" size="small" round>{{ $t('88826') }}</klk-button>
    </div>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'

@Component({
    components: { }
})
export default class MapEntrance extends Vue {
    hoverCardIndex = -1
    currentCardIndex = -1
    isSmallMap = true


    animateShowLayer(pointer: PointerEvent | null, from: 'history' | '' = '') {
        this.$emit('open-map')       
    }

    mapTypeChange() {
    }

    mounted() {

    }

    beforeDestroy() {
    }
}
</script>

<style lang="scss" scoped>

.hotel-map-entrance {
    position: relative;
    z-index: 9;
    width: 300px;
    height: 100px;
    flex: none;
    background: $color-bg-3;
    border-radius: $radius-xxl;
    cursor: pointer;
    overflow: hidden;

    .map-bg {
        width: 100%;
        height: 100%;
        transform: scale(1.5);
        animation: scale-view 2s .5s forwards;

        @keyframes scale-view {
            to {
                transform: scale(2.3);
            }
        }
    }

    .location-icon {
        position: absolute;
        top: -62px;
        left: 50%;
        margin-left: -28px;
        transform: scale(0.1);
        animation: top-in 1s 1.5s forwards cubic-bezier(.49, 2.11, .38, .52);

        @keyframes top-in {
            to {
                top: 38px;
                transform: scale(0.75) translateY(-80%);
            }
        }
    }

    .map-btn {
        position: absolute;
        bottom: 16px;
        left: 50%;
        opacity: 0;
        transform: translateX(-50%) scale(0);
        border: none;
        box-shadow: $shadow-normal-4;
        animation: fade-in 1s 1.5s forwards cubic-bezier(.32, 1.59, 1, .86);
        white-space: nowrap;

        @keyframes fade-in {
            to {
                opacity: 1;
                transform: translateX(-50%) scale(1);
            }
        }
    }
}
</style>