<template>
  <div
    v-no-scroll-class="type === 'layer' && visible"
    :class="`mobile-page-layer ${type} ${isMobile ? 'mobile' : 'desktop'} ${visible ? 'show' : 'hidden'} ${pageAnimate ? 'animate' : ''}`"
  >
    <div class="container-content">
      <fix-header
        v-if="isMobile && !platformMp && !isKlookApp"
        :title="title"
        :complex="false"
        @back="back"
      >
        <div slot="header-right">
          <slot name="header-right"> </slot>
        </div>
      </fix-header>

      <div
        class="container-content-main"
        :class="animate ? 'animate' : 'no-animate'"
        :style="
          tabIndex ? `transform: translate3d(${tabIndex * -100}%, 0, 1px)` : ''
        "
      >
        <slot />
      </div>

      <div v-if="$slots.footer" class="container-footer">
        <slot v-if="$slots.footer" name="footer" />
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch } from 'vue-property-decorator'
import FixHeader from './fix-header.vue'
import NoScrollClass from '../directives/no-scroll-class'

@Component({
  components: {
    FixHeader
  },
  directives: {
    'no-scroll-class': NoScrollClass
  }
})
export default class PageLayer extends Vue {
  @Prop({ type: Boolean, default: true }) readonly isMobile!: boolean
  @Prop(Boolean) readonly visible!: boolean
  @Prop({ type: String, default: 'layer' }) readonly type!: string
  @Prop({ type: String }) readonly title!: string
  @Prop({ type: Boolean, default: true }) pageAnimate!: boolean // 页面出现动画
  @Prop({ type: Boolean, default: false }) animate!: boolean // 是否支持多级页面切换
  @Prop({ type: Boolean, default: false }) delay!: boolean // 延迟展示，为了v-if时候的动画
  @Prop({ type: Number, default: 0 }) tabIndex!: number
  @Prop({ type: Boolean, default: false }) transfer!: boolean
  trueShow = false
  transfered= false

  @Watch('visible', { immediate: true })
  visibleChange() {
    setTimeout(() => {
      this.trueShow = this.visible
    }, 0)
  }
  get platformMp() {
    return this.$store?.state?.klook?.platformMp
  }

  get isKlookApp() {
    return this.$store?.state?.klook?.isKlookApp
  }

  back() {
    if (this.tabIndex > 0) {
      this.$emit('back')
      this.$emit('update:tab-index', this.tabIndex - 1)
    } else {
      this.$emit('close')
    }
  }

  mounted() {
    if (this.transfer && !this.transfered) {
      document.body.appendChild(this.$el)
      this.transfered = true
    }
  }
}
</script>

<style lang="scss" scoped>
.mobile-page-layer {
  background-color: #ffffff;

  ::v-deep .hotel-fixed-header {
    top: 0;
  }

  &.hidden {
    display: none;
  }

  &.page {
    .container-content {
      width: 100%;
      .container-content-main {
        min-height: calc(100vh - 109px);
      }
    }

    .container-footer {
      position: sticky;
      bottom: 0;
      padding: 8px;
      border-top: 1px solid #eeeeee;
    }
  }

  &.layer {
    &.animate {
      transition: all $motion-duration-l $motion-timing-ease;
      transform: translateY(0px);
      // visibility: visible;
      &.hidden {
        transform: translateY(100%);
        display: flex;
      }
    }

    &.mobile {
      position: fixed;
      z-index: 999;
      width: 100%;
      display: flex;
      flex-direction: column;
      left: 0;
      right: 0;
      top: 0;
      bottom: 0;
    }

    .container-content {
      bottom: 0;
      left: 0;
      position: absolute;
      width: 100%;
      min-height: 100%;
      max-height: 100%;
      overflow: hidden;
      display: flex;
      flex-direction: column;
      .container-content-main {
        flex: 1;
        // 兼容老版本直接写的情况
        &.no-animate {
          padding: 0 20px 64px;
          overflow-y: auto;
          max-height: 100%;
          -webkit-overflow-scrolling: touch;
        }

        &.animate {
          min-height: 0;
          height: 100%;
          padding: 0;
          transition: 0.5s transform;
          padding-left: 0;
          padding-right: 0;
          display: flex;
          -webkit-overflow-scrolling: touch;
          & > div {
            overflow: auto;
            flex: 0 0 100%;
            padding: 16px 20px 64px;
            overflow-y: auto;
            position: relative;
          }
          &::v-deep > div {
            flex: 0 0 100%;
            padding: 16px 20px 64px;
            overflow-y: auto;
            position: relative;
          }
        }
      }
    }

    .container-footer {
      flex: 0 0 auto;
      z-index: 1;
      padding: 8px 20px;
    }
  }
}
</style>
