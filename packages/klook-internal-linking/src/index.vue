<template>
  <div
    :class="['internal-linking-wrapper', wrapperClass]"
    v-if="moduleLinkItems && moduleLinkItems.length > 0"
  >
    <h2 :class="['internal-linking-title', titleClass]">
      {{ moduleTitle }}
    </h2>
    <div
      v-for="(InternalLinkingSection, index) in moduleLinkItems"
      :key="index"
      :class="['internal-linking-section', moduleClass]"
      :data-spm-module="
        formatSPM('InternalLinkingSection_LIST', {
          idx: index,
          len: moduleLinkItems.length,
          ext: {
            search_type: InternalLinkingSection.search_type,
            search_key: InternalLinkingSection.search_key,
            heading_ref: InternalLinkingSection.heading_ref,
            heading: InternalLinkingSection.heading,
          },
        })
      "
    >
      <SectionComponent
        @onLinkClick="handleInternalLinking"
        :section-data="InternalLinkingSection"
        :hasBorder="hasBorder"
      />
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Prop, Provide, Watch } from "vue-property-decorator";
import SectionComponent from "./section.vue";
import Base from "./base";
import { formatSPM } from "./tracking-utils";
import { ModuleData } from "../types/index";

@Component({
  name: "KlkInternalLinking",
  components: {
    SectionComponent,
  },
})
export default class InternalLinking extends Base {
  @Prop() private moduleData!: ModuleData;
  @Prop() private platform!: string;

  @Provide() providedPlatform = this.platform;

  @Prop({ default: true }) hasBorder!: boolean;

  get wrapperClass() {
    return `internal-linking-wrapper-${this.platform}`;
  }

  get titleClass() {
    return `internal-linking-title-${this.platform}`;
  }

  get moduleClass() {
    return `internal-linking-section-${this.platform}`;
  }

  created() {}

  get moduleLinkItems() {
    return this?.moduleData?.seo_static_link || [];
  }

  get moduleTitle() {
    return this?.moduleData?.title || this.$__t("71930");
  }

  formatSPM(key: string = "", trackInfo = {}, isItem = false) {
    return formatSPM(key, trackInfo, isItem);
  }

  handleInternalLinking() {
    this.$emit("onInternalLinkingClick");
  }
}
</script>

<style scoped lang="scss">
.internal-linking-wrapper-desktop {
  margin-top: 64px;
}
.internal-linking-wrapper-mobile {
  margin-top: 32px;
}
.internal-linking-title {
  color: $color-text-primary;
}
.internal-linking-title-desktop {
  @include font-heading-m();
  margin-bottom: 32px;
}
.internal-linking-title-mobile {
  @include font-heading-xs();
  margin-bottom: 16px;
}
.internal-linking-section-desktop {
  margin-bottom: 32px;
}
.internal-linking-section-mobile {
  margin-bottom: 20px;
}
</style>
