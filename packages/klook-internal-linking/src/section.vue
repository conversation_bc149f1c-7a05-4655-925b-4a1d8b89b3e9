<template>
  <div>
    <h3 :class="['section-heading', headingClass]">
      {{ sectionData.heading }}
    </h3>
    <ul
      :class="[
        listWrapperClass,
        'link-list-wrapper',
        {
          'link-wrapper-has-border': hasBorder,
          'link-wrapper-no-border': !hasBorder,
        },
      ]"
    >
      <li
        v-for="(unit, index) in sectionData.linking"
        :key="index"
        class="link-unit"
        @click="handleLinkClick"
        :data-spm-item="
          formatSPM(
            'Link_LIST',
            {
              ext: {
                index: index,
                length: sectionData.linking.length,
                keyword: unit.key_word,
                landing_url: unit.url,
              },
            },
            true
          )
        "
        v-galileo-click-tracker.stop="{ spm: 'InternalLinkingSection_LIST.Link_LIST', componentName: 'klk-internal-linking' }"
      >
        <span class="link-index" v-if="hasBorder">{{ index + 1 }}</span>
        <a class="link-unit-anchor" :href="unit.url">{{ unit.key_word }}</a>
      </li>
    </ul>
  </div>
</template>

<script lang="ts">
import { Vue, Component, Prop, Inject } from "vue-property-decorator";
import { formatSPM } from "./tracking-utils";
import { Section } from "../types/index";

@Component
export default class SectionComponent extends Vue {
  @Inject() providedPlatform!: string;

  @Prop({ default: () => ({}) }) sectionData!: Section;

  @Prop({ default: true }) hasBorder!: boolean;

  get headingClass() {
    return `section-heading-${this.providedPlatform}`;
  }

  get listWrapperClass() {
    return `link-list-wrapper-${this.providedPlatform}`;
  }

  handleLinkClick() {
    this.$emit("onLinkClick", {});
  }

  formatSPM(key: string = "", trackInfo = {}, isItem = false) {
    return formatSPM(key, trackInfo, isItem);
  }
}
</script>
<style scoped lang="scss">
.section-heading {
  color: $color-text-primary;
}
.section-heading-desktop {
  @include font-heading-xs();
  margin-bottom: 16px;
}
.section-heading-mobile {
  @include font-body-m-bold();
  margin-bottom: 12px;
}

.link-list-wrapper {
  display: flex;
  flex-wrap: wrap;
  margin-left: -12px;
  margin-right: -12px;
  margin-bottom: -12px;
  .link-unit {
    @include font-body-s-regular();
    margin-bottom: 12px;
  }
  .link-unit-anchor {
    display: inline-block;
    padding-right: 12px;
    padding-left: 12px;
  }
}
.link-wrapper-has-border {
  .link-unit {
    display: flex;
    border: 1px solid $color-accent-19;
    border-radius: $radius-s;
    margin-left: 12px;
    margin-right: 12px;
    overflow: hidden;
    align-items: center;
    background-color: $color-bg-1;
  }
  .link-index {
    padding: 2.5px 10px;
    background: $color-caution-background;
    color: $color-accent-19;
    border-right: 1px solid $color-accent-19;
    height: 100%;
  }
  .link-unit-anchor {
    color: $color-text-primary;
  }
}
.link-wrapper-no-border {
  .link-unit-anchor {
    height: 12px;
    border-right: 1px solid $color-border-normal;
    line-height: 12px;
    color: $color-text-secondary;
  }
  .link-unit:last-child {
    .link-unit-anchor {
      border-right: none;
    }
  }
  span.link-index {
    display: none;
  }
}
</style>
