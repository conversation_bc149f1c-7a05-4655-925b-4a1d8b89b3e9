import { Vue, Component, Prop } from "vue-property-decorator";
import { genText } from "./i18n/index";
import messages from "../locales/index.js";
import { Message } from "../types/index";

@Component
export default class Base extends Vue {
  $__t: any = null;

  created() {
    try {
      const typedMessages: Message = messages;
      // @ts-ignore
      const lang = this.$i18n ? this.$i18n.options.locale : "en";
      this.$__t = typedMessages[lang]
        ? genText(typedMessages[lang])
        : genText(typedMessages["en"]);
    } catch (e) {
      const typedMessages: Message = messages;
      this.$__t = genText(typedMessages["en"]);
    }
  }
}
