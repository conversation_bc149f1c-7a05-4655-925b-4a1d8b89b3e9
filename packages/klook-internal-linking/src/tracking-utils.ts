const formatSPM = (key: string = "", trackInfo = {}, isItem = false) => {
  const jsonStringify = (val: any) =>
    typeof val === "object" ? JSON.stringify(val) : val;

  const qs = Object.entries(trackInfo)
    .map(([key, val]) => {
      return `${key}=${encodeURIComponent(jsonStringify(val))}`;
    })
    .join("&");

  return [isItem ? key : `${key}`, ...(qs ? [qs] : [])].join("?");
};
export { formatSPM };
