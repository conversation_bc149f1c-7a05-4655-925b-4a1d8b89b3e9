html {
  box-sizing: border-box;
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto',
  'Droid Sans', 'Helvetica Neue', 'Helvetica', 'Arial', sans-serif;
  text-size-adjust: 100%;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  background-color: white;
  line-height: 1.15;
  font-size: 14px;
  color: #333;
}

a {
  color: inherit;
  cursor: pointer;
  text-decoration: none;
}

ul,
ol {
  margin: 0;
  padding: 0;
  list-style-type: none;
}

h1,
h2,
h3,
h4,
h5,
h6,
ul,
ol,
dl,
li,
dt,
dd,
p,
blockquote,
pre,
form,
fieldset,
table,
th,
td {
  margin: 0px;
  padding: 0px;
}

// 设置 placholder 的颜色
::placeholder {
  color: rgba($color: #000000, $alpha: .38);

  @media(min-width: 600px) {
    color: #999;
  }
}

// 去除系统默认appearance的样式,常用于IOS下移除原生样式
// 解决input的type="number"在部分手机端会出现一个小按钮
input::-webkit-inner-spin-button,
input::-webkit-outer-spin-button {
  -webkit-appearance: none;
}

// 去除 mobile 端默认的小箭头
select {
  appearance: none;
}

// 去除在 IE 上的 X
input::-ms-clear {
  display: none;
}

// 去掉选中效果
button,
input[type='button'],
input[type='checkbox'],
input[type='reset'],
input[type='submit'],
label {
  cursor: pointer;
  user-select: none;
  -ms-user-select: none;
  -moz-user-select: none;
  -webkit-user-select: none;
}
