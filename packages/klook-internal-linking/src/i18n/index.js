let showtextid = 0;
if (process.client) {
  showtextid = location.search.includes('cmstextid=1');
}

const t = (key, option = {}, localeData) => {
  let text = localeData[key] || String(key);
  if (showtextid) {
    text = key + '-' + text;
  }
  Object.entries(option).forEach(([k, v]) => {
    text = text.replace(`{${k}}`, v);
  });
  return text;
};


export const genText = (file) => {
  return (key, option) => t(key, option, file);
};
