# klk-internal-linking
内链组件
# release version
1.0.0: 建立npm包

# dev doc


# 开发者文档
多语言文案：
 获取多语言文档步骤：
 1. 获取仓库klk-f2e-cli权限，拉取代码运行（项目运行常见报错解答：https://klook.larksuite.com/wiki/wikusyaJFq30SZCFZtfFx1gNdHf）
 2. 通过多语言平台（https://admin.klook.com/mspa/platform/loc_cms/text_list?collection_id=1）获取多语言id（注意：多语言需要添加当前项目权限才能正常拉取） 
 3. 如果增加或者修改多语言请手动导入text id 运行命令：f2e tetris locale add [key]

# 设计稿
https://www.figma.com/file/YmBO458aqK2VcA5NXcQTh0/InternalLinking-%E7%BB%84%E4%BB%B6%E6%9B%B4%E6%96%B0?type=design&node-id=1-1164&t=H6PXDaXNMFGXlun5-0

# prd
https://klook.larksuite.com/docx/RprCdSlB3oHWRuxusNJuUGwtsKc

# 埋点文档
https://klook.larksuite.com/docx/RprCdSlB3oHWRuxusNJuUGwtsKc

# 技术文档
https://klook.larksuite.com/docx/NUYedhPruoBj0ox1w6zuLXtwsld
