<template>
  <div
    class="klk-empty-panel-wrapper"
    :class="{
      [`klk-empty-panel-${platform}`]: true,
      'klk-empty-panel-multi-btn': primaryBtnText && secondaryBtnText
    }"
  >
    <slot name="icon">
      <img v-if="imageSrc" :src="imageSrc" :width="iconWidth" :height="iconHeight" alt="">
    </slot>
    <slot>
      <h3 v-if="title" class="klk-empty-panel-title">
        {{ title }}
      </h3>
      <div v-if="content" :class="{
        'klk-empty-panel-content': true,
        mt24: !title
      }">
        {{ content }}
      </div>
    </slot>
    <slot name="atc">
      <div
        v-if="primaryBtnText || primaryBtnLink || secondaryBtnText || secondaryBtnLink"
        class="klk-empty-panel-atc-box"
      >
        <klk-button
          v-if="primaryBtnText"
          size="small"
          type="primary"
          class="klk-empty-panel-btn"
          v-bind="primaryBtnAttrs"
          @click="toLink(primaryBtnLink, 'primary')"
        >
          {{ primaryBtnText }}
        </klk-button>
        <klk-button
          v-if="secondaryBtnText"
          size="small"
          type="outlined"
          class="klk-empty-panel-btn"
          v-bind="secondaryBtnAttrs"
          @click="toLink(secondaryBtnLink, 'secondary')"
        >
          {{ secondaryBtnText }}
        </klk-button>
      </div>
    </slot>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from "vue-property-decorator";
import KlkButton from "@klook/klook-ui/lib/button/index.js";
import "@klook/klook-ui/lib/styles/components/button.scss";
import transformImageUrl from "@klook/klk-traveller-utils/lib/transformImageUrl";

@Component({
  name: "klkEmptyPanel",
  components: {
    KlkButton,
  },
})
export default class klkEmptyPanel extends Vue {
  @Prop({ default: "desktop", required: false }) platform?: string;
  @Prop({ default: "", required: false }) title?: string;
  @Prop({
    // default: 'https://res.klook.com/image/upload/fl_lossy.progressive,q_auto/c_fill,w_200,h_200/v1665565697/ill_spot_Default_ap9grz.png',
    default:
      "https://res.klook.com/image/upload/v1665565697/ill_spot_Default_ap9grz.png",
    required: false,
  })
  iconSrc?: string;
  @Prop({ default: undefined, required: false }) webp?: boolean;
  @Prop({ default: 200, required: false }) iconWidth?: number;
  @Prop({ default: 200, required: false }) iconHeight?: number;

  @Prop({ default: "", required: false }) content!: string;

  @Prop({ default: "", required: false }) primaryBtnText?: string;
  @Prop({ default: "", required: false }) primaryBtnLink?: string;
  @Prop({ default: () => ({}), required: false }) primaryBtnAttrs?: Record<
    string,
    any
  >;

  @Prop({ default: "", required: false }) secondaryBtnText?: string;
  @Prop({ default: "", required: false }) secondaryBtnLink?: string;
  @Prop({ default: () => ({}), required: false }) secondaryBtnAttrs?: Record<
    string,
    any
  >;

  $store: any;

  get realWebp() {
    if (this.webp !== undefined) {
      return this.webp;
    }

    return this.$store?.state?.klook?.webp || 0;
  }

  toLink(link: string = "", type: "secondary" | "primary" = "primary") {
    if (link) {
      window.location.href = link;
    }
    this.$emit(`${type}-btn-click`);
  }

  formatPicUrl(url: string) {
    // const enlarge = this.platform === 'desktop' ? 1 : 2
    const enlarge = 2;
    if (this.iconWidth && this.iconHeight) {
      return transformImageUrl(url, {
        width: this.iconWidth * enlarge,
        height: this.iconHeight * enlarge,
        webp: this.realWebp,
      });
    } else {
      return url;
    }
  }

  get imageSrc() {
    if (!this.iconSrc) {
      return "";
    }
    return this.formatPicUrl(this.iconSrc);
  }
}
</script>

<style lang="scss">
@import "./var.scss";

.klk-empty-panel-wrapper {
  display: block;
  padding: 32px 20px;
  text-align: center;
  max-width: 712px;
  margin: 0 auto;
}
.klk-empty-panel-title {
  @include font-body-l-bold;

  margin-top: 24px;
  color: $color-text-secondary;
}

.klk-empty-panel-content {
  @include font-body-s-regular;

  margin-top: 8px;
  color: $color-text-secondary;
}

.mt24 {
  margin-top: 24px;
}

.klk-empty-panel-atc-box {
  margin: 19px -5px -5px;
  font-size: 0;
}

.klk-empty-panel-btn {
  margin: 5px;
  max-width: calc(100% - 10px);
  white-space: nowrap;
  text-overflow: ellipsis;
}

.klk-empty-panel-desktop {
  .klk-empty-panel-btn {
    min-width: 160px;
  }
}

.klk-empty-panel-mobile.klk-empty-panel-multi-btn {
  .klk-empty-panel-atc-box {
    display: flex;
    flex-wrap: wrap;
  }
  .klk-empty-panel-btn {
    flex-grow: 1;
    flex-shrink: 0;
    min-width: calc(50% - 10px);
  }
}
</style>
