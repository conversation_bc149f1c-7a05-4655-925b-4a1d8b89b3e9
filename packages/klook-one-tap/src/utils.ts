export function loadScript(url: string) {
  return new Promise(resolve => {
    const head =
      document.getElementsByTagName("head")[0] || document.documentElement;
    const script: any = document.createElement("script");

    script.src = url;

    let loadFlag = false;

    script.onload = script.onreadystatechange = function() {
      if (
        !loadFlag &&
        (!this.readyState ||
          this.readyState === "loaded" ||
          this.readyState === "complete")
      ) {
        loadFlag = true;

        resolve(void 0);

        script.onload = script.onreadystatechange = null;
        if (head && script.parentNode) {
          head.removeChild(script);
        }
      }
    };

    head.insertBefore(script, head.firstChild);
  });
}
