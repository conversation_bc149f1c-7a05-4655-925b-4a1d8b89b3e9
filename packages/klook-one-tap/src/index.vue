<template>
  <div class="show-one-tap-box">
    <div id="j-google-one-tap-box"></div>
    <div v-if="showTerms" id="box-bottom">
      <div id="box-content">
        <klk-checkbox v-model="termChoosed" size="small">
          <klk-markdown :content="termsContent"></klk-markdown>
        </klk-checkbox>
      </div>
    </div>
    <slot name="footer"></slot>
  </div>
</template>
<script lang="ts">
import { Component, Vue, Prop, Watch } from "vue-property-decorator";
import { loadScript } from "./utils";

declare global {
  interface Window {
    google: any;
  }
}

@Component({
  name: "KlkOneTap"
})
export default class KlkOneTap extends Vue {
  @Prop({ default: "" }) googleClientId!: string;
  @Prop({ default: false }) showTerms?: boolean;
  @Prop({ default: "" }) termsContent?: string;
  @Prop({ default: false }) value?: boolean;

  termChoosed: boolean | undefined = false;

  loadGoogleOneTapFramework() {
    return loadScript("https://accounts.google.com/gsi/client");
  }

  googleOneTapBoxInit() {
    const that = this;
    if(window.google){
      window.google.accounts.id.initialize({
        client_id: that.googleClientId,
        prompt_parent_id: "j-google-one-tap-box",
        use_fedcm_for_prompt: false,
        cancel_on_tap_outside: false,
        callback: that.handleCredentialResponse
      });
      window.google.accounts.id.prompt((notification: any) => {
        this.$emit("handleprompt", notification);
      });
    }
  }

  handleCredentialResponse(response: any) {
    this.$emit("handleCredentialResponse", response);
  }

  async mounted() {
    this.termChoosed = this.value;
    await this.loadGoogleOneTapFramework().then(() => {
      this.googleOneTapBoxInit();
    });
  }

  @Watch("termChoosed")
  ontermChoosedChange(value: boolean) {
    this.$emit("input", value);
  }

  @Watch("value")
  onvalueChange(value: boolean) {
    this.termChoosed = value;
  }
}
</script>
<style lang="scss" scoped>
.show-one-tap-box {
  width: 100%;
  #box-bottom {
    width: 100%;
    margin-top: 6px;
    #box-content {
      background-color: #fff;
      box-sizing: border-box;
      margin: 0 16px;
      padding-bottom: 16px;
      color: #4a4a4a;
      border-bottom: 1px solid #e0e0e0;
    }
  }
}
</style>
