/*
 * Copyright By Klook
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */
import { v4 as uuidv4 } from "uuid";

enum StateEnum {
  "noReady" = "noReady",
  "experimentError" = "experimentError",
  "experimentEmpty" = "experimentEmpty",
  "countryCodeEmpty" = "countryCodeEmpty",
  "availableEmpty" = "availableEmpty",
  "availableError" = "availableError",
  "quotationEmpty" = "quotationEmpty",
  "quotationError" = "quotationError",
  "success" = "success",
}

enum RecordTypeEnum {
  "SDK" = "SDK",
}

interface SdkLoadingTimingInfo {
  // available request id
  request_id?: string;

  // SDK加载耗时
  sdk_load_time?: number;

  // 场景
  state: StateEnum;

  // 静态资源加载时间
  res_load_time?: number;

  // 静态资源加载完成后，记录组件渲染和接口加载的时间
  data_render_time?: number;

  // 活动ID
  activity_ids?: number[];
  // package ID
  package_ids?: number[];

  leaf_category_ids?: number[];

  sub_category_ids?: number[];

  platform: "desktop" | "mobile";

  is_first_load: boolean;
}

export interface ActivityIdInfo {
  activity_ids: number[];
  package_ids: number[];
  leaf_category_ids: number[];
  sub_category_ids: number[];
}

export default class SDKMonitor {
  sdkLoadStartTime?: number; // 开始加载SDK时间
  sdkLoadEndTime?: number; // SDK加载完成时间
  state: StateEnum = StateEnum.noReady; // SDK加载场景
  firstLoad: boolean = true; // 是否是主活动第一次加载SDK
  sdkLoadingTimingList: SdkLoadingTimingInfo[]; // SDK所有加载场景
  sdkStartMountingTime?: number; // SDK开始挂载时间
  type?: RecordTypeEnum = RecordTypeEnum.SDK; // 监控记录的类型
  activityIdInfo: ActivityIdInfo;
  platform: "desktop" | "mobile";
  sessionId = uuidv4();
  availableRequestId?: string;

  constructor(activityIdInfo: ActivityIdInfo, platform: "desktop" | "mobile") {
    this.activityIdInfo = activityIdInfo;
    this.platform = platform;
    this.sdkLoadingTimingList = [
      {
        request_id: this.availableRequestId,
        state: this.state,
        platform: this.platform,
        is_first_load: this.firstLoad,
        ...this.activityIdInfo,
      },
    ];
    this.init();
  }

  init(): void {
    this.handleReportData();
  }

  setAvailableRequestId(id?: string) {
    this.availableRequestId = id
  }

  setActivityIdInfo(activityIdInfo: ActivityIdInfo): void {
    this.activityIdInfo = activityIdInfo;
  }

  setSdkLoadStartTime(time: number): void {
    this.sdkLoadStartTime = time;
    this.setAvailableRequestId()
  }

  setSdkLoadEndTime(time: number): void {
    this.sdkLoadEndTime = time;
  }

  setState(state: StateEnum): void {
    this.state = state;
  }

  getState(): StateEnum | undefined {
    return this.state;
  }

  // 设置SDK是否首次加载标识
  setFirstLoad(firstLoad: boolean) {
    this.firstLoad = firstLoad;
  }

  // 获取SDK是否首次加载标识
  getFirstLoad(): boolean {
    return this.firstLoad;
  }

  // 获取SDK所有场景加载时间
  getSdkLoadingTimingList(): SdkLoadingTimingInfo[] {
    return this.sdkLoadingTimingList;
  }

  // 获取SDK所有场景加载时间
  setSdkLoadingTimingList(sdkLoadingTimingList: SdkLoadingTimingInfo[]): void {
    this.sdkLoadingTimingList = sdkLoadingTimingList;
  }

  updateSdkLoadingTimingList(): void {
    this.sdkLoadingTimingList.push({
      request_id: this.availableRequestId,
      state: this.state,
      platform: this.platform,
      is_first_load: this.firstLoad,
      sdk_load_time:
        (this.sdkLoadEndTime as number) - (this.sdkLoadStartTime as number),
      res_load_time: this.getStaticResourceLoadingTiming(),
      data_render_time: this.getSDKDataRenderTiming(),
      ...this.activityIdInfo,
    });
    // 记录是否是第一次加载
    this.setFirstLoad(false);
  }

  // 设置SDK开始挂载时间
  setSdkStartMountingTime(sdkStartMountingTime: number) {
    this.sdkStartMountingTime = sdkStartMountingTime;
  }

  // 获取SDK从开始挂载到可交互时间差
  getSDKDataRenderTiming(): number | undefined {
    if (!this.firstLoad) {
      return;
    }
    // 第一次加载可能接口返回为空或者报错导致SDK不展示，此时不记录加载成功完成时间
    if (this.sdkLoadEndTime && this.sdkStartMountingTime) {
      return this.sdkLoadEndTime - this.sdkStartMountingTime;
    }
  }

  // 获取SDK静态资源加载时间
  getStaticResourceLoadingTiming(): number | undefined {
    if (!this.firstLoad) {
      return;
    }
    if (this.sdkStartMountingTime && this.sdkLoadStartTime) {
      return this.sdkStartMountingTime - this.sdkLoadStartTime;
    }
  }

  reportData(url: string, data: object) {
    const blob = new Blob([JSON.stringify(data)]);
    navigator.sendBeacon(url, blob);
  }

  // 上报数据
  handleReportData(): void {
    document.addEventListener("visibilitychange", () => {
      if (document.visibilityState === "hidden") {
        // 只有展示 sdk 时才上报数据
        // 保证只上报一次
        const data = {
          session_id: this.sessionId,
          metric: "Timing",
          source_from: "SDK",
          type: RecordTypeEnum.SDK,
          data_list: this.getSdkLoadingTimingList(),
        };
        this.reportData(
          "/v1/insuranceapisrv/outer/monitor/performance/collect",
          data
        );
        this.setSdkLoadingTimingList([]);
      }
    });
  }
}
