<template>
  <div id="js-za-insurance"></div>
</template>

<script lang="ts">
import { Vue, Component, Prop } from "vue-property-decorator";
import { loadMicroApp, initGlobalState } from "qiankun";
import Monitor, { ActivityIdInfo } from "./util/monitor";

const MICRO_APP: any = {
  name: "mall-web-umi",
  container: "#js-za-insurance",
  instance: null,
  microAppState: null,
  setMicroAppState: null,
  changeStatus: null,
};

@Component({
  name: "KlookInsuranceZasdk",
})
export default class KlookInsuranceZasdk extends Vue {
  @Prop({ type: String, default: "" }) klook!: any;
  @Prop({ type: String, default: "" }) customPlatform!: string;
  @Prop({ type: String, default: "" }) businessType!: string;
  @Prop({ type: String, default: "" }) routerBasePath!: string;
  @Prop({ type: String, default: "" }) countryCode!: string;
  @Prop({ type: Array, default: () => [] }) channelProductList!: any;

  monitor: any = null;

  get __klook() {
    return this.klook || (window as any).__KLOOK__.state.klook;
  }

  get __inhouse() {
    return (this as any).$inhouse;
  }

  get isDesktop() {
    // 业务传入则使用业务值
    if (this.customPlatform !== "") {
      return this.customPlatform === "desktop";
    } else {
      return this.__klook.platform === "desktop";
    }
  }

  getMicroAppUrl() {
    const uatList = [
      /t1\.fat\.klook\.io/,
      /uat\d+\.fat\.klook\.io/,
      /www1-fat\.klooktest\.com/,
      /www\d+-uat\.klooktest\.com/,
    ];
    const uatEntryHost = "https://insurance-uat2.klook.io";
    const t2EntryHost = "https://insurance.za.dev.klooktest.com";
    const isUat = uatList.reduce(
      (accu, curr) => accu || curr.test(window.location.href),
      false
    );
    const devEntryHost = isUat ? uatEntryHost : t2EntryHost;
    const prodEntryHost = "https://insurance.klook.com";
    const currentEntryHost =
      process.env.APP_ENV === "production" ? prodEntryHost : devEntryHost;
    return `${currentEntryHost}/fusion/custom/`;
  }

  getOriginData() {
    // sdk 内会对整个对象转换为驼峰
    return {
      businessType: this.businessType, // 业务类型
      isPC: this.isDesktop, // 是否是web temporary
      currency: this.__klook.currency, // 货币
      currencySymbol: this.__klook.currencySymbol, // 货币符号
      language: this.__klook.language, // 语种 temporary,
      languagePath:
        this.__klook.language === "en" ? "" : `${this.__klook.language}/`,
      promotionTags: [], // 是否使用了SRV优惠券或者其他与保险互斥的业务
      countryCode: this.countryCode, // 国家区号   比如 +86
      channelProductList: this.channelProductList, // 试算因子列表，za sdk 全部透传给后端
    };
  }

  onMicroAppSateChange(state: any, _prev: any) {
    // 更新全局状态
    if (state.changeStatus && MICRO_APP.changeStatus !== state.changeStatus) {
      MICRO_APP.changeStatus = state.changeStatus || null;
    }

    // 数据变化传出给业务
    this.$emit("callback", {
      type: "onStateChange",
      data: {
        sdkSectionTitle: "",
        sdkShowState: state.sdkShowState,
        validateChecked: state.validateChecked,
        quotationList: state.quotationList,
      },
    });
  }

  calculateCallback(data: any) {
    // 传出实时计算信息
    if (data) {
      this.$emit("callback", {
        type: "calculateCallback",
        data: {
          quotationItemList: data,
        },
      });
    }
  }

  update(onlyUpdateData: boolean | undefined) {
    // 更新传入的数据
    MICRO_APP.setMicroAppState &&
      MICRO_APP.setMicroAppState({ originData: { ...this.getOriginData() } });
    if (onlyUpdateData) {
      return;
    }

    // 调用 sdk 重新渲染方法
    MICRO_APP.changeStatus && MICRO_APP.changeStatus();
    // 清空结算数据
    this.calculateCallback([]);

    // 第一次加载other info模块也会调update方法，这里只有非第一次加载才重新记录监控信息
    if (!this.monitor.getFirstLoad()) {
      // 记录开始加载SDK时间
      this.monitor.setSdkLoadStartTime(performance.now());
    }
  }

  async handleLogin() {
    if (this.$cookies && this.$store && this.$store && this.$href) {
      const { loginWithSDK } = await import("@klook/klook-traveller-login");
      loginWithSDK({
        aid: this.$cookies.get("aid"),
        isMP: this.$store.state.klook?.platformMp !== "",
        platform: this.$store.state.klook?.platform,
        language: this.$store.state.klook?.language,
        currency: this.$store.state.klook?.currency,
        market: this.$store.state.klook?.market,
        bizName: "Platform",
        purpose: "insurance_claim",
        cancel: () => {},
        success: () => {
          this.$store.dispatch("auth/getProfile");
          window.location.reload();
        },
      }).then((supportLogin: boolean) => {
        if (!supportLogin) {
          window.location.href = this.$href(
            `/signin/?signin_jump=${encodeURIComponent(window.location.href)}`
          );
        }
      });
    } else {
      console.error(
        "need this.$cookies && this.klook && this.$store && this.$href"
      );
    }
  }

  getActivityInfo(originData: any): ActivityIdInfo {
    const activity_ids: number[] = [];
    const package_ids: number[] = [];
    const leaf_category_ids: number[] = [];
    const sub_category_ids: number[] = [];
    originData.channelProductList?.forEach((item: Record<string, any>) => {
      activity_ids.push(item.activityId);
      package_ids.push(item.channelProductId);
      leaf_category_ids.push(item.productInfo.leaf_category_id);
      sub_category_ids.push(item.productInfo.sub_category_id);
    });
    return { activity_ids, package_ids, leaf_category_ids, sub_category_ids };
  }

  init() {
    const originData = this.getOriginData();
    const activityIdInfo = this.getActivityInfo(originData);

    // 初始化监控
    this.monitor = new Monitor(activityIdInfo, this.__klook.platform);

    MICRO_APP.microAppState = {
      inhouse: this.__inhouse, // TODO: 后续 exp 接入 za sdk 改成从 props 取值时废弃
      originData: this.getOriginData(),
      calculateCallback: (data: any) => this.calculateCallback(data),
      handleLogin: this.handleLogin,
      quotationList: null, // 试算列表
      validateChecked: false, // 条款是否勾选标志
      sdkShowState: false, // SDK 内部是否有保险数据展示
      changeStatus: null, // 用于绑定重新渲染 SDK 方法
      $monitor: this.monitor, // 监控
    };

    // 初始化 state 获取通讯方式
    const { onGlobalStateChange, setGlobalState } = initGlobalState({
      ...MICRO_APP.microAppState,
    });

    const langPath =
      this.__klook.language === "en" ? "" : `/${this.__klook.language}`;

    // 记录开始加载SDK时间
    this.monitor.setSdkLoadStartTime(performance.now());
    // 记录是否是第一次加载
    this.monitor.setFirstLoad(true);

    // 加载 MicroApp
    MICRO_APP.instance = loadMicroApp({
      name: MICRO_APP.name,
      container: MICRO_APP.container,
      entry: this.getMicroAppUrl(),
      props: {
        routerBase: `${langPath}/${this.routerBasePath}`,
        title: document && document.title,
        $inhouse: this.__inhouse,
        $monitor: this.monitor, // 监控
      },
    });

    // 绑定通讯方法
    onGlobalStateChange((value: any, prev: any) =>
      this.onMicroAppSateChange(value, prev)
    );
    MICRO_APP.setMicroAppState = (data: any) => setGlobalState(data);
  }

  beforeDestroy() {
    MICRO_APP.instance && MICRO_APP.instance.unmount();
  }
}
</script>

<style lang="scss"></style>
