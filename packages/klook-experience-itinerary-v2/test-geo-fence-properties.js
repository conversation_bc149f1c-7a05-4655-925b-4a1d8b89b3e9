/**
 * 测试地理围栏属性动态切换功能
 * 这个文件用于验证 initFenceData 方法是否正确根据当前卡片的 pick_up_type 选择合适的区域属性
 */

// 模拟数据
const mockAreaConfig = {
  selected_area_properties: {
    stroke: '#FF0000', // 红色边框
    fill: '#FF000033'  // 红色填充（半透明）
  },
  unselected_area_properties: {
    stroke: '#0000FF', // 蓝色边框
    fill: '#0000FF33' // 蓝色填充（半透明）
  }
}

const mockFence = [
  {
    geom: {
      type: 'Polygon',
      coordinates: [[[116.3, 39.9], [116.4, 39.9], [116.4, 40.0], [116.3, 40.0], [116.3, 39.9]]]
    }
  }
]

// 模拟当前卡片 - 地理围栏区域
const mockCurrentCardArea = {
  pick_up_type: 2,
  data: {
    using_type: 'area'
  }
}

// 模拟当前卡片 - 普通点
const mockCurrentCardPoint = {
  pick_up_type: 1,
  data: {
    using_type: 'location'
  }
}

// 模拟 POI_USING_TYPE
const POI_USING_TYPE = {
  area: 'area',
  location: 'location'
}

// 模拟 initFenceData 方法
function initFenceData(fence, areaConfig, currentCard) {
  const list = fence.reduce((acc, curr) => {
    // 如果当前卡片的 pick_up_type 是 2（地理围栏区域），使用 selected_area_properties
    // 否则使用默认的 unselected_area_properties
    const isCurrentCardArea = currentCard?.data?.using_type === POI_USING_TYPE.area || currentCard?.pick_up_type === 2
    const area_properties = isCurrentCardArea 
      ? (areaConfig?.selected_area_properties || {})
      : (areaConfig?.unselected_area_properties || {})
    
    const strokeColor = area_properties.stroke
    const fillColor = area_properties.fill
    const coordinates = curr?.geom?.coordinates || []
    
    coordinates.forEach((polygonList) => {
      acc.push({
        polygonList: curr?.geom?.type === 'Polygon' ? [polygonList] : polygonList,
        polygonConfig: {
          strokeColor,
          fillColor
        }
      })
    })
    return acc
  }, [])
  return Object.freeze(list)
}

// 测试用例
console.log('=== 地理围栏属性动态切换测试 ===\n')

// 测试1: 当前卡片是地理围栏区域
console.log('测试1: 当前卡片是地理围栏区域 (pick_up_type: 2)')
const result1 = initFenceData(mockFence, mockAreaConfig, mockCurrentCardArea)
console.log('期望: 使用 selected_area_properties (红色)')
console.log('实际结果:', result1[0].polygonConfig)
console.log('✓ 测试通过:', result1[0].polygonConfig.strokeColor === '#FF0000' ? '是' : '否')
console.log('')

// 测试2: 当前卡片是普通点
console.log('测试2: 当前卡片是普通点 (pick_up_type: 1)')
const result2 = initFenceData(mockFence, mockAreaConfig, mockCurrentCardPoint)
console.log('期望: 使用 unselected_area_properties (蓝色)')
console.log('实际结果:', result2[0].polygonConfig)
console.log('✓ 测试通过:', result2[0].polygonConfig.strokeColor === '#0000FF' ? '是' : '否')
console.log('')

// 测试3: 没有当前卡片信息
console.log('测试3: 没有当前卡片信息')
const result3 = initFenceData(mockFence, mockAreaConfig, null)
console.log('期望: 使用 unselected_area_properties (蓝色)')
console.log('实际结果:', result3[0].polygonConfig)
console.log('✓ 测试通过:', result3[0].polygonConfig.strokeColor === '#0000FF' ? '是' : '否')
console.log('')

console.log('=== 测试完成 ===')

// 在浏览器控制台中运行此文件来验证功能
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { initFenceData, mockAreaConfig, mockFence }
}
