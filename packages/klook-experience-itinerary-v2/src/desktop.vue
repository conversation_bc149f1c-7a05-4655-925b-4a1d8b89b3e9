<template>
  <div
    class="exp-itinerary v2"
    :class="platform"
  >
    <!-- <ItineraryMenus :list="days" :active="0" />  -->
    <template v-if="customFlowType === 'itinerary-details' && calcCustomDetals">
      <ItineraryDetailsDesktop
        :details="calcCustomDetals"
        :status="moreObj[isExpandSeeMoreKey]"
        :maxHeight="moreObj.maxHeight"
        :translateI18n="translateI18n"
        :platform="platform"
        @click="clickShowMore(moreObj, $event)"
        @updateData="updateDataHandler(moreObj, $event)"
        @clickImage="clickImageHandler"
      ></ItineraryDetailsDesktop>
      <ViewMap
        v-if="!isServer"
        slot="day-content-top-slot"
        ref="viewMapRef"
        :viewOnMap="viewOnMap"
        :mapApiData="mapApiData"
        :platform="platform"
        class="view-map-wrap"
        :class="platform"
      />
    </template>
    <template v-else>
      <DayList :platform="platform" :list="days" :viewOnMap="viewOnMap" :translateI18n="translateI18n" :transitionendCallback="transitionendCallback" @viewOnMapBottom="viewOnMapBottomHandler" @clickImage="clickImageHandler">
        <ViewMap
          v-if="!isServer"
          slot="day-content-top-slot"
          ref="viewMapRef"
          :viewOnMap="viewOnMap"
          :mapApiData="mapApiData"
          :platform="platform"
          class="view-map-wrap"
          :class="platform"
        />
      </DayList>
      <!-- 警告：Map组件在desktop场景下初始化依赖ViewMap -->
    </template>

    <Disclaimers :disclaimers="disclaimers" />

    <Map
      v-if="!isServer && mapVisible"
      :show.sync="mapVisible"
      v-bind="mapAtrrs"
    />

    <template v-if="!isServer && allImages && allImages.length > 0">
      <ImgPreviewDesktop
        v-bind="imageGalleryObj"
        :activity-id="0"
        :hackCloseBtn="hackCloseBtn"
        :current-poi="currentImages"
        @close="imageGalleryObj.ons.close"
        @swiperChange="swiperChange"
      >
      </ImgPreviewDesktop>
    </template>
  </div>
</template>

<script lang="ts">
import { Component } from 'vue-property-decorator'
import BaseMixins from './base-mixins'
import ItineraryDetailsDesktop from './components/day/details/desktop.vue'
import ImgPreviewDesktop from './components/pic-preview/desktop/image-gallery/index.vue'
import Disclaimers from './components/disclaimers.vue'

@Component({
  components: {
    ItineraryDetailsDesktop,
    ImgPreviewDesktop,
    Disclaimers
  }
})
export default class ExperienceItineraryDesktop extends BaseMixins {
}
</script>

<style lang="scss">
// 设置在 html 上用于禁止页面滚动
.klk-lock-body-scroll {
  body {
    width: 100%;
    position: fixed !important;
    height: 100% !important;
  }
}
</style>

<style lang="scss" scoped>
.exp-itinerary {
  position: relative;
  .view-map-wrap {
    flex: none;
    margin-left: 32px;
  }
}
</style>
