<template>
  <component :is="comp" v-bind="$attrs" v-on="$listeners" />
</template>

<script lang="ts">
import { Vue, Component } from 'vue-property-decorator'
// $$mock
// import { itineraryData } from './mock.js'
import ExperienceItineraryMobile from './mobile.vue'
import ExperienceItineraryDesktop from './desktop.vue'

@Component({
  components: {
    ExperienceItineraryMobile,
    ExperienceItineraryDesktop
    // ExperienceItineraryMobile: () => import('./mobile.vue'), // 暂时不支持懒加载，服务端会报错
    // ExperienceItineraryDesktop: () => import('./desktop.vue') // 暂时不支持懒加载，服务端会报错
  }
})
export default class Index extends Vue {

  // itineraryData = itineraryData
  get comp() {
    return this.$attrs.platform === 'desktop' ? 'ExperienceItineraryDesktop' : 'ExperienceItineraryMobile'
  }
}
</script>
