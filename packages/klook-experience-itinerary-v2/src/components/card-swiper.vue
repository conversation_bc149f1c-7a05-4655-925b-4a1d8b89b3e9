<template>
  <div class="card-swiper-wrap">
    <div
      class="card-swiper"
      @scroll.once="handleScroll"
      @touchstart.stop="handleTouchStart"
      @touchmove.stop="handleTouchMove"
      @touchend.stop="handleTouchEnd"
    >
      <slot></slot>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'

@Component
export default class CardSwiper extends Vue {

  handleScroll() {
    this.$emit('scroll-once')
  }

  handleTouchStart() {
    this.$emit('touchstart')
  }

  handleTouchMove() {
    this.$emit('touchmove')
  }

  handleTouchEnd() {
    this.$emit('touchend')
  }
}
</script>

<style lang="scss" scoped>
  .card-swiper-wrap {
    width: 100%;
    overflow: hidden;

    .card-swiper {
      width: 100%;
      display: flex;
      flex-wrap: nowrap;
      overflow-x: auto;
      overflow-y: hidden;
      padding: 0 20px 20px 20px;
      margin-bottom: -20px;
    }
  }
</style>
