<template>
  <span class="common-copy-text" :class="styleType" @click="clickCopy(text)">
    <span><slot></slot></span>
    <span v-if="src" class="copy-style">
      <img :src="src" :style="{ width: size + 'px', height: size + 'px' }" />
    </span>
    <IconCopy v-else class="copy-style" theme="outline" :size="size" />
  </span>
</template>

<script lang="ts">
import { Vue, Component, Prop, Inject } from 'vue-property-decorator'
import { IconCopy } from '@klook/klook-icons'
import KlkToast from '@klook/klook-ui/lib/toast'
import '@klook/klook-ui/lib/styles/components/toast.scss'
import { isServer } from '../../utils'
@Component({
  components: {
    IconCopy
  }
})
export default class Index extends Vue {
  @Prop({ type: String, default: '' }) src!: string
  @Prop({ type: String, default: '' }) styleType!: 'style-left-right'
  @Prop({ type: String, default: '' }) text!: number
  @Prop({ type: Number, default: 20 }) size!: number

  @Inject('translateI18n')
  translateI18n!: (...args: any[]) => string
  clickCopy(text: string) {
    if (isServer) {
      return
    }
    if (navigator?.clipboard) {
      // clipboard api 复制
      navigator.clipboard.writeText(text)
    } else {
      const textarea = document.createElement('textarea')
      document.body.appendChild(textarea)
      // 隐藏此输入框
      textarea.style.position = 'fixed'
      textarea.style.clip = 'rect(0 0 0 0)'
      textarea.style.top = '10px'
      // 赋值
      textarea.value = text
      // 选中
      textarea.select()
      // 复制
      document.execCommand('copy', true)
      // 移除输入框
      document.body.removeChild(textarea)
    }

    KlkToast(this.translateI18n('109149'))
  }
}
</script>

<style lang="scss" scoped>
.common-copy-text {
  cursor: pointer;
  &.style-left-right {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
}
.copy-style {
  flex: none;
  margin-left: 4px;
}
</style>
