
<template>
  <div class="divider-comp" :class="platform">
    <span class="divider-comp__line before" :style="calcLineStyle"></span>
    <span class="divider-comp__text" :style="calcTextStyle">{{ data.text }}</span>
    <span class="divider-comp__line after" :style="calcLineStyle"></span>
  </div>
</template>

<script lang="ts">
import { Vue, Prop, Component } from 'vue-property-decorator'
import type { CardComponent } from '../../types'

@Component({
  inheritAttrs: false
})
export default class TypeDivider extends Vue {
  @Prop({ default: 'mobile' }) platform!: string
  @Prop({ type: Object, required: true })
  readonly data!: CardComponent<'divider'>['data']

  get isDesktop() {
    return this.platform === 'desktop'
  }

  get calcLineStyle() {
    return {
      backgroundColor: this.data.line_color || '#eee'
    }
  }

  get calcTextStyle() {
    return {
      color: this.data.text_color || (this.isDesktop ? '#fff' : '#757575')
    }
  }
}
</script>

<style lang="scss" scoped>
.divider-comp {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 12px 0;

  .divider-comp__text {
    @include font-caption-m-semibold;
    padding: 0 8px;
  }

  &.desktop {
    .divider-comp__text {
      border-radius: 2px;
      background-color: #212121;
    }
  }

  &.mobile {
    .divider-comp__line {
      &.before {
        flex: 1;
      }
    }
  }

  .divider-comp__line {
    height: 1px;

    &.before {
      width: 20px;
    }

    &.after {
      flex: 1;
    }
  }
}
</style>
