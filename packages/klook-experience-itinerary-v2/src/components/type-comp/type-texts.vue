
<template>
  <div class="texts-comp" :class="platform">
    <p
      v-for="(item, index) in data.texts"
      :key="index"
      :class="['texts-comp__item', item.type]"
      :style="{ marginTop: `${getGap(item.type)}px` }"
    >
      {{ item.text }}
    </p>
  </div>
</template>

<script lang="ts">
import { Vue, Prop, Component } from 'vue-property-decorator'
import type { CardComponent } from '../../types'

@Component
export default class TypeTexts extends Vue {
  @Prop({ default: 'mobile' }) platform!: string
  @Prop({ type: Object, required: true })
  readonly data!: CardComponent<'texts'>['data']
  @Prop() gap!: number

  getGap(type: string) {
    if (typeof this.gap === 'number') {
      return this.gap
    }
    const typeMap: any = this.platform === 'desktop' ? {
      desc: 8,
      text: 12,
      dark: 12
    } : {
      desc: 4,
      text: 12,
      dark: 8
    }
    return typeMap[type] || typeMap.dark
  }
}
</script>
 
<style lang="scss" scoped>
.desktop.texts-comp {
  .texts-comp {
    &__item {
      &.dark,
      &.text {
        @include font-body-m-regular-v2;
      }
      &.desc {
        @include font-body-s-regular-v2;
      }
    }
  }
}
.texts-comp {
  &__item {
    &.dark,
    &.text {
      @include font-body-s-regular;
      color: $color-text-primary;
    }
    &.desc {
      @include font-body-xs-regular;
      color: $color-text-secondary;
    }
  }
}
</style>