
<template>
  <div class="icons-comp" :style="{ marginTop: `${calcGap}px` }">
    <div v-for="(icon, index) in calcIcons" :key="index" class="icons-comp__item">
      <img v-if="icon.icon" :src="icon.icon" class="icons-comp__icon">
      <span class="icons-comp__text">{{ icon.text }}</span>
    </div>
  </div>
</template>

<script lang="ts">
import { Vue, Prop, Component } from 'vue-property-decorator'
import type { CardComponent } from '../../types'

@Component
export default class TypeIcons extends Vue {
  @Prop({ default: 'mobile' }) platform!: string
  @Prop({ type: Object, required: true })
  readonly data!: CardComponent<'icons'>['data']
  @Prop() gap!: number

  get calcGap() {
    if (typeof this.gap === 'number') {
      return this.gap
    }
    return this.platform === 'desktop' ? 4 : 4
  }

  get calcIcons() {
    return this.data?.icons || []
  }
}
</script>

<style lang="scss" scoped>
.desktop {
  .icons-comp {
    @include font-body-m-regular-v2;
    &__icon {
      margin-right: 8px;
      width: 16px;
      height: 16px;
    }
  }
}
.icons-comp {
  display: flex;
  flex-wrap: wrap;
  @include font-body-xs-regular;
  color: $color-text-secondary;
  &__item {
    display: flex;
    align-items: center;
    &:not(:last-of-type) {
      margin-right: 12px;
    }
  }
  &__icon {
    margin-right: 4px;
    width: 12px;
    height: 12px;
  }
}
</style>
