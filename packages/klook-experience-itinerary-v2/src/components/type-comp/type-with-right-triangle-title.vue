
<template>
  <div class="with-right-triangle-title-comp" :class="{ '__enter': data.with_right_triangle }" @click="clickHandler">
    {{ data.text }}
    <klk-icon v-if="data.with_right_triangle" type="icon_navigation_chevron_right" :size="12" />
  </div>
</template>

<script lang="ts">
import { Vue, Prop, Component } from 'vue-property-decorator'
import type { CardComponent } from '../../types'

@Component({
  inheritAttrs: false
})
export default class TypeWithRightTriangleTitle extends Vue {
  @Prop({ default: 'mobile' }) platform!: string
  @Prop({ type: Object, required: true })
  readonly data!: CardComponent<'with_right_triangle_title'>['data']

  clickHandler() {
    if (!(this.data.detail_components?.length && this.data.with_right_triangle)) {
      return
    }

    this.$emit('clickSeasonalDetails')
  }
}
</script>

<style lang="scss" scoped>
.with-right-triangle-title-comp {
  @include font-body-m-bold-v2;
  margin-bottom: 20px;

  color: $color-text-primary;
  display: flex;
  align-items: center;
  gap: 4px;

  &.__enter {
    cursor: pointer;
    text-decoration: underline;
  }
}
</style>
