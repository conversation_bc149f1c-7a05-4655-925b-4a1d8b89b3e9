<template>
  <div class="reminder-box">
    <h5 class="reminder-title">{{ data.title_name }}</h5>
    <ul class="reminder-list">
      <li
        v-for="(text, index) in data.texts"
        :key="index"
        class="reminder-item"
      >
        <p>{{ text }}</p>
      </li>
    </ul>
  </div>
</template> 

<script lang="ts">
import { Vue, Prop, Component } from 'vue-property-decorator'
import type { CardComponent } from '../../types'

@Component
export default class TypeReminder extends Vue {
  @Prop({ type: Object, required: true })
  readonly data!: CardComponent<'reminder'>['data']
}
</script>

<style lang="scss" scoped>
.desktop .reminder-box {
  margin: 24px 0 0 0;
  background-color: $color-neutral-100;
}
.reminder-box {
  margin: 24px 0 0 0;
  background-color: $color-green-50;
  padding: 16px;
  border-radius: $radius-l;
  .reminder-title {
    margin-bottom: 4px;
    font-size: 16px;
    font-weight: 600;
    line-height: 20px;
    color: $color-text-primary;
  }
  .reminder-list {
    margin: 0;
    padding: 0 0 0 16px;
    list-style: disc;
    .reminder-item {
      @include font-paragraph-s-regular;
      color: $color-text-primary;
    }
  }
}
</style>
