

<template>
  <div v-if="fromType === 'mobile-itinerary-details' && calcImgs.length" :class="platform" :style="{ marginTop: `${calcGap}px` }">
    <div class="imgs-comp" :class="fromType ? fromType : 'scroll-nowrap'">
      <ShowMoreBox
        :showLinearGradient="moreObj.status === 'more'"
        :status="moreObj.status"
        @click="clickShowMore"
      >
        <div v-for="(img, index) in calcImgs" :key="index" class="imgs-comp__item" @click="viewImages(index)">
          <PrefixImg
            :src="img"
            class="imgs-comp__item-img"
            :whRate="'4:3'"
            :realHeight="fromType === 'mobile-itinerary-details' ? 234 : 72"
            :showInitImage="true"
          />
        </div>
      </ShowMoreBox>
    </div>
  </div>
  <CardSwiper v-else-if="calcImgs.length" class="imgs-comp" :class="platform" :style="{ marginTop: `${calcGap}px` }">
    <div v-for="(img, index) in calcImgs" :key="index" class="imgs-comp__item" @click="viewImages(index)">
      <PrefixImg
        :src="img"
        class="imgs-comp__item-img"
        :whRate="'4:3'"
        :realHeight="platform === 'desktop' ? 105 : 72"
        :showInitImage="true"
      />
    </div>
  </CardSwiper>
</template>

<script lang="ts">
import { Vue, Prop, Component, Watch } from 'vue-property-decorator'
import type { CardComponent } from '../../types'
import PrefixImg from '../prefix-img/index.vue'
import ShowMoreBox from '../show-more-box/index.vue'
import CardSwiper from '../card-swiper.vue'

@Component({
  components: {
    CardSwiper,
    PrefixImg,
    ShowMoreBox
  }
})
export default class TypeImgs extends Vue {
  @Prop({ default: 'mobile' }) platform!: string
  @Prop({ default: false }) showMore!: boolean
  @Prop({ type: Object, required: true })
  readonly data!: CardComponent<'imgs'>['data']
  @Prop() gap!: number
  @Prop({ type: String, default: '' }) fromType!: 'mobile-itinerary-details' | ''

  showMoreSelf = false

  created() {
    this.showMoreSelf = this.showMore && this.fromType === 'mobile-itinerary-details'
  }

  get calcGap() {
    if (typeof this.gap === 'number') {
      return this.gap
    }
    return this.platform === 'desktop' ? 12 : 8
  }

  @Watch('showMoreSelf')
  showMoreWatch(isb: boolean) {
    const { moreObj } = this
    if (isb && this.data?.imgs?.length > moreObj.len) {
      moreObj.status = 'more'
    } else {
      moreObj.status = ''
    }
  }

  get calcImgs() {
    if (this.showMoreSelf && this.moreObj.status === 'more') {
      return this.data?.imgs?.slice(0, this.moreObj.len) || []
    }
    return this.data?.imgs || []
  }

  viewImages(index: number) {
    const { poi_identify_id = '' } = this.data || {}
    this.$emit('clickImage', { list: this.calcImgs, index, poi_identify_id })
  }

  moreObj: any = {
    len: 2,
    status: ''
  }

  clickShowMore(status: string) {
    const { moreObj } = this
    moreObj.status = status
  }
}
</script>

<style lang="scss" scoped>
.desktop .imgs-comp {
  .imgs-comp__item {
    &-img {
      width: 140px;
      height: 105px;
    }
  }
}
.imgs-comp {
  &.scroll-nowrap {
    display: flex;
    flex-wrap: nowrap;
    overflow: hidden;
    overflow-x: scroll;
  }
  .imgs-comp__item {
    &:not(:last-of-type) {
      margin-right: 5px;
    }
    cursor: pointer;
    &-img {
      width: 96px;
      height: 60px;
      border-radius: $radius-s;
    }
  }
}
.mobile-itinerary-details.imgs-comp {
  .imgs-comp__item {
    display: block;
    width: 100%;
    height: 100%;
    &:not(:last-of-type) {
      margin-bottom: 12px;
      margin-right: 0;
    }
    &-img {
      width: 100%;
      height: 100%;
      border-radius: $radius-l;
    }
  }
}
</style>
