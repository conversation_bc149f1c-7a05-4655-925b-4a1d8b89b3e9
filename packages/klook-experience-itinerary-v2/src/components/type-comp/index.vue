<template>
  <div class="type-comp">
    <div
      v-for="(comp, index) in calcList"
      :key="index"
      class="type-comp__item"
      :data-type="comp.type"
    >
     <component
        :is="componentMap[comp.type]"
        :data="comp.data"
        :gap="isSetGap && index === 0 && comp.type !== 'poi' ? 0 : undefined"
        :from-type="fromType"
        :show-more="showMore"
        :platform="platform"
        :trackInfo="trackInfo"
        :refresh="refresh"
        @clickItineraryDetails="$emit('clickItineraryDetails', comp)"
        @clickSeasonalDetails="$emit('clickSeasonalDetails', comp)"
        @clickImage="$emit('clickImage', $event)"
      />
    </div>
  </div>
</template>

<script lang="ts">
import { Vue, Component, Prop } from 'vue-property-decorator'
import * as TypeComponents from './register'
import TypeWithRightTriangleTitle from './type-with-right-triangle-title.vue'
import type { CardComponent } from '../../types'


@Component({
  name: 'TypeComp'
})
export default class ComponentRenderer extends Vue {
  @Prop({ default: false }) isSetGap!: boolean
  @Prop() trackInfo!: { type: string, extra: object }
  @Prop({ default: 'mobile' }) platform!: string
  @Prop({ default: false }) showMore!: boolean
  @Prop({ type: Array, required: true })
  readonly list!: CardComponent<'spacing' | 'poi' | 'icons' | 'imgs' | 'texts' | 'title_icons' | 'reminder'>[]
  @Prop({ type: String, default: '' })
  readonly fromType!: '' | 'mobile-itinerary-details' | 'desktop-itinerary-details'
  @Prop({ default: undefined }) refresh!: string | number | undefined

  get componentMap() {
    return {
      spacing: TypeComponents.TypeSpacing,
      poi: TypeComponents.TypePoi,
      icons: TypeComponents.TypeIcons,
      imgs: TypeComponents.TypeImgs,
      texts: TypeComponents.TypeTexts,
      title_icons: TypeComponents.TypeTitleIcons,
      reminder: TypeComponents.TypeReminder,
      divider: TypeComponents.TypeDivider,
      tips: TypeComponents.TypeTips,
      color_texts: TypeComponents.TypeColorText,
      front_title_with_tag: TypeComponents.TypeFrontTitleWithTag,
      with_right_triangle_title: TypeWithRightTriangleTitle,
      session_block: TypeComponents.TypeSessionBlock
    } as any
  }

  get calcList() {
    return this.list?.filter(item => item.type) || []
  }
}
</script>
