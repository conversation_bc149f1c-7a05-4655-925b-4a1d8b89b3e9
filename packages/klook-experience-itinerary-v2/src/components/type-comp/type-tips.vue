<template>
  <div class="tips-comp" :style="{ backgroundColor: data.background_color || '#eee' }">
    <img v-if="data.icon" class="tips-comp__icon" :src="data.icon" alt="tips icon" />
    <span class="tips-comp__text">{{ data.text }}</span>
  </div>
</template>

<script lang="ts">
import { Vue, Prop, Component } from 'vue-property-decorator'
import type { CardComponent } from '../../types'

@Component
export default class TypeTips extends Vue {
  @Prop({ default: 'mobile' }) platform!: string
  @Prop({ type: Object, required: true })
  readonly data!: CardComponent<'tips'>['data']
  @Prop({ type: Boolean, default: false }) isBlock!: boolean

  get tipStyle() {
    const style: { [key: string]: string } = {}

    if (this.data && this.data.background_color) {
      style.backgroundColor = this.data.background_color
    }

    return style
  }
}
</script>

<style lang="scss" scoped>
.tips-comp {
  @include font-body-s-regular-v2;
  display: flex;
  gap: 8px;
  padding: 8px;
  align-items: flex-start;
  border-radius: $radius-l;
  color: #212121;

  .tips-comp__icon {
    width: 16px;
    height: 16px;
  }
}
</style>
