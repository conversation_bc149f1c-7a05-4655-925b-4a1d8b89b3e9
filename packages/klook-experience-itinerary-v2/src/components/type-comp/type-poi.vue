
<template>
  <!-- POI组件实现 -->
  <h4 v-if="data && data.poi_desc" class="poi-comp" :style="calcStyle" @click="clickHandler(calcIsTappable)">
    <span class="poi-comp__desc" :class="data.is_poi_tappable && 'poi-title'">
      <span>{{ data.poi_desc }}</span>
      <CopyText
        v-if="data.copy_icon"
        :src="data.copy_icon"
        :text="data.poi_desc"
        :size="16"
        @click.native.stop
      >
      </CopyText>
    </span>
    <IconNext v-if="calcIsTappable" class="poi-comp__icon" theme="outline" size="16" />
  </h4>
</template>

<script lang="ts">
import { Vue, Prop, Component, Inject } from 'vue-property-decorator'
import type { CardComponent } from '../../types'
import { IconNext } from '@klook/klook-icons'
import CopyText from '../copy-text/index.vue'

@Component({
  components: {
    CopyText,
    IconNext
  }
})
export default class TypePoi extends Vue {
  @Prop({ default: 'mobile' }) platform!: string
  @Prop({ type: Object, required: true }) data!: CardComponent<'poi'>['data']
  @Prop({ type: Number, default: 8 }) gap!: number
  @Inject("handleTitleClick") handleTitleClick!: Function;

  get isDesktop() {
    return this.platform === 'desktop'
  }

  get calcIsPoiTitle() {
    return this.isDesktop && this.data?.poi_data?.length === 1
  }

  clickHandler(isTappable: boolean) {
    // console.log('click poi', this.isDesktop, isTappable, this.calcIsPoiTitle, this.data)
    if (isTappable) {
      if (this.calcIsPoiTitle) {
        const map = this.data?.poi_data?.[0]?.map
        this.handleTitleClick({ poiMapData: map }, true)
        return
      }
      this.$emit('clickItineraryDetails')
    }
  }

  get calcIsTappable() {
    return !!this.data?.is_poi_tappable
  }

  get calcStyle() {
    if (this.platform === 'desktop') {
      return {
        marginTop: `${this.gap}px`,
        background: 'transparent',
      }
    }
    const bgColor = '#fff'
    return {
      marginTop: `${this.gap}px`,
      background: `linear-gradient(90deg, ${this.data?.background_color || bgColor} 45.76%, ${bgColor} 94.09%)`
    }
  }
}
</script>

<style lang="scss" scoped>
.desktop .poi-comp {
  padding: 0;
  @include font-body-m-regular-v2;
  color: $color-text-primary;
  cursor: inherit;
  .poi-comp__icon {
    display: none;
  }
  .poi-title {
    text-decoration: underline;
    cursor: pointer;
  }
  ::v-deep {
    .copy-style img{
      position: relative;
      top: 3px;
    }
  }
}
.poi-comp {
  display: inline-flex;
  align-items: center;
  justify-content: flex-start;
  padding: 8px 16px 8px 16px;
  @include font-body-xs-regular;
  color: $color-text-secondary;
  border-radius: $radius-m;
  cursor: pointer;
  .poi-comp__desc {
    display: flex;
    align-items: center;
  }
  .poi-comp__icon {
    margin-left: 4px;
  }
}
</style>
