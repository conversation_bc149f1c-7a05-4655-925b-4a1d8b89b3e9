
<template>
  <div class="color-texts-comp" :class="platform">
    <p
      v-for="(item, index) in data.color_texts"
      :key="index"
      :class="['color-texts-comp__item', item.type]"
    >
      <span v-html="getFormattedText(item)"></span>
    </p>
  </div>
</template>

<script lang="ts">
import { Vue, Prop, Component } from 'vue-property-decorator'
import type { CardComponent } from '../../types'

@Component
export default class TypeColorText extends Vue {
  @Prop({ default: 'mobile' }) platform!: string
  @Prop({ type: Object, required: true })
  readonly data!: CardComponent<'color_texts'>['data']

  /**
   * 根据 text_map 替换文本中的变量并应用样式
   * @param item - 包含 text 和 text_map 的对象
   * @returns 格式化后的 HTML 字符串
   */
  getFormattedText(item: any): string {
    if (!item.text || !item.text_map) {
      return item.text || ''
    }

    let formattedText = item.text

    Object.keys(item.text_map || {}).forEach(variable => {
      const config = item.text_map[variable]
      const { text, color, style } = config
      const styleString = this.generateInlineStyle(color, style)
      const styledText = `<span style="${styleString}" class="variable-text">${text}</span>`
      formattedText = formattedText.replace(new RegExp(this.escapeRegExp(variable), 'g'), styledText)
    })

    return formattedText
  }

  generateInlineStyle(color?: string, style?: string | string[]): string {
    const styles: string[] = []

    if (color) {
      styles.push(`color: ${color}`)
    }

    if (style) {
      const styleArray = Array.isArray(style) ? style : [style]

      styleArray.forEach(s => {
        switch (s.toLowerCase()) {
          case 'bold':
            styles.push('font-weight: bold')
            break
          default:
            if (s.includes(':')) {
              styles.push(s)
            }
        }
      })
    }

    return styles.join('; ')
  }

  escapeRegExp(string: string): string {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
  }
}
</script>

<style lang="scss" scoped>
.color-texts-comp__item {
  @include font-body-m-semibold;
  color: #757575;
}
</style>
