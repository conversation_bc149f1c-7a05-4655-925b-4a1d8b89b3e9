
<template>
  <div class="front-title-with-tag-comp" @click="clickHandler">
    <div class="front-title-with-tag-comp__tags">
      <template v-for="(tag, index) in data.tags">
        <div
          :key="index"
          class="front-title-with-tag-comp__tag"
          :style="{
            color: tag.text_color,
            borderColor: tag.border_color
          }"
        >
          {{ tag.text }}
        </div>
      </template>
    </div>
    <span class="front-title-with-tag-comp__title">
      {{ data.title }}
    </span>
  </div>
</template>

<script lang="ts">
import { Vue, Prop, Component } from 'vue-property-decorator'
import type { CardComponent } from '../../types'

@Component({
  inheritAttrs: false
})
export default class TypeFrontTitleWithTag extends Vue {
  @Prop({ default: 'mobile' }) platform!: string
  @Prop({ type: Object, required: true })
  readonly data!: CardComponent<'front_title_with_tag'>['data']

  clickHandler() {
    this.$emit('clickFrontTitleWithTag')
  }
}
</script>

<style lang="scss" scoped>
.front-title-with-tag-comp {
  &__tags {
    @include font-heading-xxxxs;
    margin-right: 6px;
    display: inline-flex;
    gap: 6px;
    align-items: center;
    white-space: nowrap;
  }

  &__tag {
    padding: 2px 4px;
    border-radius: 4px;
    border: 1px solid transparent;
  }

  &__title {
    @include font-heading-xs-v2;
    color: $color-text-primary;
    text-decoration: underline;
    cursor: pointer;
  }
}
</style>
