
<template>
  <div class="session-block-comp" :class="platform">
    <div class="session-block-comp__texts">
      <p
        v-for="(item, index) in data.texts"
        :key="index"
        :class="['session-block-comp__text', item.type]"
        :style="{ marginTop: `${getGap(item.type)}px` }"
      >
        {{ item.text }}
      </p>
    </div>

    <component
      :is="isDesktop && isMultiDeparture ? 'klk-poptip' : 'span'"
      dark
      flip
      placement="right"
      :max-height="218"
    >
      <SessionDetails v-if="isDesktop && isMultiDeparture" slot="content" :platform="platform" :session-table="data.session_table || []" />

      <div
        ref="departureTimesContainer"
        class="session-block-comp__departure-times"
        @click="clickDepartureTimesHandler"
      >
        <p
          v-for="(item, index) in visibleDepartureTimes"
          :key="index"
          ref="departureTimeItems"
          class="session-block-comp__departure-time"
          :class="{
            'is-multi': isMultiDeparture
          }"
        >
          {{ item }}
        </p>
        <p
          v-if="hasMoreTimes"
          class="session-block-comp__more-times"
          :class="{
            'is-multi': isMultiDeparture
          }"
        >
          +{{ remainingTimesCount }}
        </p>
        <klk-icon
          v-if="isMultiDeparture"
          type="icon_navigation_chevron_right"
          :size="12"
        />
      </div>
    </component>

    <klk-bottom-sheet
      v-if="platform === 'mobile' && bSheet.visible"
      header-divider
      class="session-block-details-bs"
      :title="data.session_table.title"
      :visible.sync="bSheet.visible"
      show-close
      transfer
      :can-pull-close="false"
      :max-height="'80%'"
    >
      <SessionDetails :platform="platform" :session-table="data.session_table || []" />
    </klk-bottom-sheet>
  </div>
</template>

<script lang="ts">
import { Vue, Prop, Component, Watch } from 'vue-property-decorator'
import type { CardComponent } from '../../types'
import SessionDetails from '../session-details.vue'

@Component({
  components: {
    SessionDetails
  }
})
export default class TypeSessionBlock extends Vue {
  @Prop({ default: 'mobile' }) platform!: string
  @Prop({ type: Object, required: true })
  readonly data!: CardComponent<'session_block'>['data']
  @Prop() gap!: number
  @Prop({ default: 40 }) defaultGap!: number
  @Prop({ default: undefined }) refresh!: string | number | undefined

  bSheet = {
    visible: false,
    title: ''
  }

  maxVisibleItems = 0
  isCalculated = false
  get isMobile() {
    return this.platform === 'mobile'
  }

  get isMultiDeparture() {
    return this.data.departure_times.length > 1
  }

  get visibleDepartureTimes() {
    if (!this.isCalculated || this.maxVisibleItems === 0) {
      return this.data.departure_times
    }
    return this.data.departure_times.slice(0, this.maxVisibleItems)
  }

  get hasMoreTimes() {
    return this.isCalculated && this.data.departure_times.length > this.maxVisibleItems
  }

  get remainingTimesCount() {
    return this.data.departure_times.length - this.maxVisibleItems
  }

  get isDesktop() {
    return this.platform === 'desktop'
  }

  mounted() {
    this.$nextTick(() => {
      this.calculateMaxVisibleItems()
    })
  }

  @Watch('refresh')
  onRefreshChange() {
    this.$nextTick(() => {
      this.calculateMaxVisibleItems()
    })
  }

  clickDepartureTimesHandler() {
    if (this.isDesktop) {
      return
    }
    this.bSheet.visible = true
  }

  calculateMaxVisibleItems() {
    const container = this.$refs.departureTimesContainer as HTMLElement
    if (!container || this.data.departure_times.length <= 1) {
      this.isCalculated = true
      return
    }

    this.isCalculated = false
    this.maxVisibleItems = this.data.departure_times.length

    this.$nextTick(() => {
      this.performWidthCalculation(container)
    })
  }

  performWidthCalculation(container: HTMLElement) {
    // if (this.isDesktop) {
    //   this.maxVisibleItems = Math.min(3, this.data.departure_times.length)
    //   this.isCalculated = true
    //   return
    // }

    const screenWidth = window.innerWidth
    const paddingTotal = this.defaultGap
    const calculatedWidth = screenWidth - paddingTotal

    let iconWidth = 0
    if (this.isMultiDeparture) {
      const iconElement = container.querySelector('.klk-icon')
      if (iconElement) {
        iconWidth = (iconElement as HTMLElement).offsetWidth + 4
      } else {
        iconWidth = 12 + 4
      }
    }

    const availableWidth = calculatedWidth - iconWidth

    const items = this.$refs.departureTimeItems as HTMLElement[]
    if (!items || items.length === 0) {
      this.isCalculated = true
      return
    }

    let totalWidth = 0
    let visibleCount = 0
    const gap = 4

    let allItemsWidth = 0
    for (let i = 0; i < items.length; i++) {
      const itemWidth = items[i].offsetWidth
      allItemsWidth += itemWidth + (i > 0 ? gap : 0)
    }

    if (allItemsWidth <= availableWidth) {
      this.maxVisibleItems = items.length
      this.isCalculated = true
      return
    }

    const tempMoreElement = document.createElement('p')
    tempMoreElement.className = items[0].className + ' session-block-comp__more-times'
    tempMoreElement.style.visibility = 'hidden'
    tempMoreElement.style.position = 'absolute'
    tempMoreElement.textContent = '+99'
    container.appendChild(tempMoreElement)
    const moreElementWidth = tempMoreElement.offsetWidth
    container.removeChild(tempMoreElement)

    for (let i = 0; i < items.length; i++) {
      const itemWidth = items[i].offsetWidth
      const currentItemWidth = totalWidth + itemWidth + (i > 0 ? gap : 0)

      if (i === items.length - 1) {
        if (currentItemWidth <= availableWidth) {
          visibleCount = i + 1
        }
        break
      }

      const withMoreWidth = currentItemWidth + gap + moreElementWidth
      if (withMoreWidth <= availableWidth) {
        totalWidth = currentItemWidth
        visibleCount = i + 1
      } else {
        break
      }
    }

    this.maxVisibleItems = Math.max(1, visibleCount)
    this.isCalculated = true
  }

  getGap(type: string) {
    if (typeof this.gap === 'number') {
      return this.gap
    }
    const typeMap: any = this.platform === 'desktop' ? {
      desc: 8,
      text: 12,
      dark: 12
    } : {
      desc: 4,
      text: 12,
      dark: 8
    }
    return typeMap[type] || typeMap.dark
  }
}
</script>

<style lang="scss" scoped>
.session-block-comp {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  background-color: $color-purple-0;
  border-radius: $radius-m;
  padding: var(--spacing-xxs, 8px);
  gap: var(--spacing-xxs, 8px);

  ::v-deep .klk-poptip {
    max-width: 100%;
  }

  &__departure-times {
    display: flex;
    gap: 4px;
    align-items: center;
    flex-wrap: nowrap;
    overflow: hidden;
    width: 100%;
    max-width: 100%;
    box-sizing: border-box;
    cursor: pointer;
  }

  &__departure-time {
    white-space: nowrap;
    flex-shrink: 0;

    &.is-multi {
      @include font-body-s-semibold;
      color: $color-text-primary;
      padding: 4px 8px;
      border-radius: $radius-s;
      background-color: $color-purple-100;
    }
  }
}
</style>
