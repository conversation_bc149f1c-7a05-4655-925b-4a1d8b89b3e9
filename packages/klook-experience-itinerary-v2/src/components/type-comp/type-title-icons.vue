
<template>
  <div v-if="calcList" class="title-icons-group" :class="[calcHasUnderline ? 'text-underline' : 'none-underline', platform]">
    <div
      v-for="(item, index) in calcList"
      :key="index"
      class="title-item"
      :class="[item.title_name && calcHasUnderline ? 'has-padding' : 'has-margin']"
      :style="getStyle(item, index)"
    >
      <component :is="seoElement" class="title-heading">
        <span class="title-heading__text">
          <template v-if="item.title_name">
            <CopyText
              v-if="item.copy_icon"
              :src="item.copy_icon"
              :text="item.title_name"
              :size="16"
              :data-spm-module="`CopyName?ext=${JSON.stringify(trackInfo || {})}`"
              data-spm-virtual-item="__virtual"
            >
              {{ item.title_name }}
            </CopyText>
            <span v-else>{{ item.title_name }}</span>
          </template>
          <span v-if="item.star > 0" class="title-heading__stars">
            <Stars
              :style-type="'stars-v2-type'"
              :size="platform === 'desktop' ? 16 : 12"
              :score="item.star"
              total="5"
              :star-show="true"
              :hide-appraise="true"
              class="custom-stars-style"
            />
          </span>
        </span>
      </component>
      <div v-if="item.icons && item.icons.length" class="title-icons">
        <TypeIcons class="title-icons-style" :data="{ icons: item.icons }" :gap="0" />
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Vue, Prop, Component } from 'vue-property-decorator'
import type { CardComponent } from '../../types'
import CopyText from '../copy-text/index.vue'
import { TypeIcons } from './register'
import Stars from '../stars.vue'

@Component({
  components: {
    Stars,
    CopyText,
    TypeIcons
  }
})
export default class TypeTitleIcons extends Vue {
  @Prop() gap!: number
  @Prop({ default: 'h4' }) seoElement!: string
  @Prop({ default: 'mobile' }) platform!: string
  @Prop() trackInfo!: { type: string, extra: object }
  @Prop({ type: Object, required: true })
  readonly data!: CardComponent<'title_icons'>['data']

  getStyle(_item: any, index: number) {
    if (this.calcHasGap && index === 0) {
      return {
        paddingTop: this.calcGap + 'px',
        marginTop: this.calcGap + 'px'
      }
    }
    return {}
  }

  get calcHasGap() {
    return typeof this.gap === 'number'
  }

  get calcGap() {
    return this.gap || 0
  }

  get calcHasUnderline() {
    return this.data.has_underline || false
  }

  get calcList() {
    return this.data.data_list || []
  }
}
</script>

<style lang="scss" scoped>
.desktop .title-icons-group {
  .title-item {
    .title-heading__stars {
      vertical-align: middle;
    }
    &.has-padding {
      padding: 12px 0;
    }
    &.has-margin {
      margin: 12px 0 0 0;
    }
    .title-heading {
      @include font-body-m-regular-v2;
    }
  }
}
.title-icons-group ::v-deep {
  .copy-style img{
    position: relative;
    top: 2px;
  }
}
.title-icons-group {
  .custom-stars-style {
    display: inline;
  }
  &.text-underline {
    .title-item {
      position: relative;
      width: 100%;
      &.border-top {
        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: -50%;
          z-index: 1;
          width: 200%;
          height: 0;
          border-top: 1px solid $color-neutral-300;
          transform: scale(0.5); // 转换为0.5像素
        }
      }
      &:last-of-type{
        &::after {
          content: '';
          position: absolute;
          bottom: 0;
          left: -50%;
          z-index: 1;
          width: 200%;
          height: 0;
          border-top: 1px solid $color-neutral-300;
          transform: scale(0.5); // 转换为0.5像素
        }
      }
    }
  }
  .title-item {
    &.has-padding {
      padding: 8px 0;
    }
    &.has-margin {
      margin: 8px 0 0 0;
    }
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    .title-icons {
      flex: none;
      margin-left: 16px;
    }
    .title-icons-style ::v-deep {
      @include font-body-m-regular-v2;
      img {
        width: 14px;
        height: 14px;
      }
    }
    .title-heading {
      @include font-heading-xxxs;
      color: $color-text-primary;
    }
    .title-heading__text {
      display: inline;
    }
    .title-heading__stars {
      flex: none;
    }
  }
}
</style>
