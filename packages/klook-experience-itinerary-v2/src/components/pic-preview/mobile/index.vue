<template>
  <klk-modal
    fullscreen 
    :open.sync="visible"
    class="image-viewer-modal"
  >
    <ActivityLayer
      class="activity-image-viewer"
      :visible.sync="visible"
      :transfer="transfer"
      :not-fixed="notFixed"
      color="black"
      transition="fade"
      @close="$emit('update:visible', false)"
    >
      <ImageGalleryViewer
        :index="index"
        :images="images"
        :title="calcCurrentPoi ? calcCurrentPoi.title : ''"
        :showImageViewerMapEntry="showImageViewerMapEntry"
        @showMap="showMap"
        @close="$emit('update:visible', false)"
        @swiperChange="swiperChange"
      >
        <template slot="description" slot-scope="props">
          <slot name="description" :image="props.image"></slot>
        </template>
      </ImageGalleryViewer>
    </ActivityLayer>
  <klk-modal>
</template>

<script lang="ts">
import { Component, Prop, Vue, Inject } from 'vue-property-decorator'
import ImageGalleryViewer from './gallery/image-viewer.vue'
import ActivityLayer from '../layer/activity-layer.vue'

@Component({
  components: {
    ActivityLayer,
    ImageGalleryViewer
  }
})
export default class ImageViewer extends Vue {
  @Prop() showImageViewerMapEntry!: boolean
  @Prop() visible!: boolean
  @Prop() transfer!: boolean
  @Prop() notFixed!: boolean
  @Prop() images!: any[]
  @Prop() title!: string
  @Prop({ type: Number }) index!: number
  @Inject({ default: () => null }) openMap2provide!: Function;

  get calcCurrentImage() {
    return this.images[this.index] || {}
  }

  get calcCurrentPoi() {
    return this.calcCurrentImage.currentPoi || {}
  }

  showMap() {
    this.openMap2provide(this.calcCurrentPoi?.poi_identify_id)
  }

  swiperChange(idx: number) {
    this.$emit('swiperChange', idx)
  }
}
</script>

<style lang="scss" scoped>
.activity-image-viewer {
  position: fixed;
  top: 0;
  left: 0;
  background: $color-common-black;
  width: 100%;
  height: 100%;
  z-index: 9999!important;
}

.image-viewer-modal {
  z-index: 2020 !important;
}
</style>
