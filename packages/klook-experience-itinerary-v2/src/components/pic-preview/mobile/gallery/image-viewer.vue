<template>
  <div v-swiper:_swiper="swiperOptions" class="activity-image-viewer_swiper_container swiper-container">
    <div class="image-viewer-top">
      <closeSvg
        class="activity-image_icon-close"
        @click.native="close"
      />
      <div ref="pagination" class="swiper-pagination activity-swiper-pagination"></div>
      <klk-button
        v-if="showImageViewerMapEntry"
        class="map-button"
        type="white-outlined"
        size="mini"
        round
        data-spm-module="SeeMap"
        data-spm-virtual-item="__virtual"
        @click="$emit('showMap')"
      >{{ buttonText }}</klk-button>
    </div>
    <div class="image-viewer-title">{{ title }}</div>
    <div class="swiper-wrapper">
      <div v-for="(image, index) in images" :key="index" class="swiper-slide">
        <div class="img-box">
          <ImageZoomer>
            <img :src="image.src" alt="activity-image" class="activity-image">
          </ImageZoomer>
        </div>
      </div>
    </div>
    <slot name="description" />
  </div>
</template>

<script lang="ts">
import { Component, Prop, Vue, Inject } from 'vue-property-decorator'
import ImageZoomer from '../image-zoomer/index.vue'
import CloseSvg from '../../../../imgs/icon_edit_close_mobile.svg'

import KlkButton from '@klook/klook-ui/lib/button'
import '@klook/klook-ui/lib/styles/components/button.scss'

@Component({
  components: {
    ImageZoomer,
    CloseSvg,
    KlkButton
  }
})
export default class ImageGalleryViewer extends Vue {
  @Prop() showImageViewerMapEntry!: boolean
  @Prop() images!: any[]
  @Prop({ type: Number, default: 0 }) index!: number
  @Prop() title!: string
  @Inject({ default: () => '' }) translateI18n!: Function
  @Inject() viewOnMap2provide!: any

  _swiper: any = null
  swiperOptions = {
    slidesPerView: 1,
    initialSlide: this.index,
    paginationClickable: true,
    spaceBetween: 16,
    pagination: {
      el: '.swiper-pagination',
      type: 'fraction'
    },
    on: {
      slideChange: () => {
        this.slideChange()
      }
    }
  }
  get buttonText() {
    return this.viewOnMap2provide?.map_title || this.translateI18n('100787')
  }

  slideChange() {
    this.$emit('swiperChange', this._swiper.realIndex, this.images)
  }

  close() {
    this.$emit('close')
  }

  mounted() {
    this._swiper.slideTo(this.index)
  }
}
</script>

<style lang="scss" scoped>
.activity-image-viewer_swiper_container {
  height: 100%;

  .image-viewer-title {
    position: absolute;
    z-index: 2;
    top: 56px;
    left: 0;
    width: 100%;
    padding: 16px 20px;
    color: $color-text-reverse;
    text-align: center;
    @include font-body-m-bold;
  }

  .swiper-wrapper {
    .swiper-slide {
      display: flex;
      align-items: center;
      overflow-y: scroll;
      scrollbar-width: none; /* firefox */
      -ms-overflow-style: none;

      &::-webkit-scrollbar {
        display: none; /* Chrome Safari */
      }

      .img-box {
        max-height: 100%;
        width: 100%;
      }

      .vue-zoomer {
        padding: 0 ;
        margin: 0;
        width: 100%;
      }
    }
  }

  .activity-image {
    width: 100%;
  }

  .image-viewer-top {
    display: flex;
    height: 56px;
    align-items: center;
    justify-content: center;
    color: $color-text-primary-onDark;
    position: absolute;
    z-index: 2;
    top: 0;
    width: 100%;
    left: 0;

    .activity-image_icon-close {
      position: absolute;
      left: 16px;
      top: 16px;
      z-index: 11;
      width: 24px;
      height: 24px;
    }

    .map-button {
      position: absolute;
      z-index: 11;
      right: 12px;
      top: 14px;
      color: #ffffff;
      background-color: transparent;
      border: 1px solid #ffffff;
    }

    .activity-swiper-pagination {
      @include font-body-m-semibold;
      position: static;
      color: $color-text-reverse;
    }
  }

  .activity-image-description {
    position: absolute;
    bottom: 0;
    padding: 8px 16px 24px;
    background: $color-bg-overlay-black-desktop;
    box-sizing: border-box;
    color: $color-text-primary-onDark;
    opacity: $opacity-overlay-desktop;
    width: 100%;

    .activity-image-description_author {
      font-size: $fontSize-body-m;
      color: $color-text-primary-onDark;
    }

    .activity-image-description_score-time {
      margin-top: 4px;
      font-size: $fontSize-caption-m;
      color: $color-text-secondary-onDark;
      display: flex;
      align-items: center;

      .activity-image-description_time {
        margin-left: 4px;
      }
    }

    .activity-image-description_package {
      font-size: $fontSize-caption-m;
      margin-bottom: 4px;
      max-height: 28px;
      overflow: hidden;
      text-overflow: ellipsis;
      -webkit-line-clamp: 2;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      color: $color-text-secondary-onDark;
    }

    .activity-image-description_comment {
      font-size: $fontSize-body-m;
      max-height: 72px;
      overflow-y: scroll;
      color: $color-text-primary-onDark;
    }
  }
}
</style>
