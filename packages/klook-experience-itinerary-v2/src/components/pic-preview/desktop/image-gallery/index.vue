<template>
  <klk-modal
    :open.sync="showGallery"
    :scrollable="false"
    :overlay-closable="false"
    :padding="0"
    :closable="hackCloseBtn ? false : true"
    :show-default-footer="false"
    :fullscreen="true"
    :overlay="false"
    class="page-activity-reviews-gallery-modal"
    @close="closeModal"
  >
    <div v-if="hackCloseBtn" class="hack-close-btn-wrap">
      <span class="hack-close-btn-wrap__close-btn" @click="closeModal">
        <svg><use xlink:href="#klk-icon-icon_navigation_close"></use></svg>
      </span>
    </div>
    <div class="reviews-container" :class="!(calcTitle || calcShowNavTag) && 'hack-hide-nav-tag'">
      <div class="button-group">
        <span class="title-text">{{ calcTitle }}</span>
      </div>
      <div v-if="calcShowNavTag" class="button-group">
        <klk-button
          class="button-group-item"
          :class="{ active: currentTab === 0 }"
          type="outlined"
          :data-spm-module="`OfficialPhoto?oid=activity_${activityId}`"
          data-spm-virtual-item="__virtual"
          @click="switchTabs(0)"
        >
          {{ translateI18n('review.merchant_photo_gallery') }} ({{ bannerList.length }})
        </klk-button>
        <klk-button
          v-if="total > 0"
          class="button-group-item"
          :class="{ active: currentTab === 1 }"
          type="outlined"
          :data-spm-module="`CustomerPhotos?oid=activity_${activityId}`"
          data-spm-virtual-item="__virtual"
          @click="switchTabs(1)"
        >
          {{ translateI18n('review.customer_photo_gallery') }} ({{ total }})
        </klk-button>
      </div>
      <div class="image-gallery-container">
        <div v-show="currentTab === 0" class="reviews-container-left banner-swiper">
          <ImageSwiper
            ref="gallery0"
            :image-list="bannerList"
            :total="bannerList.length"
            :current-index="currentIndex"
            :image-scroll="true"
            name="banner-swiper"
            class="banner-swiper__img-content"
            @bigSwiperChange="handleSwiperChange"
          />
        </div>
      </div>
    </div>
  </klk-modal>
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Inject } from 'vue-property-decorator'
import ImageSwiper from './image-swiper/index.vue'
export function urlTemplate(url: string, params: any) {
  return url.replace(/:([a-z]+)/gi, (_m, p1) => {
    if (!params[p1]) {
      throw new Error(`url parameter not find: ${url}`)
    }

    return params[p1]
  })
}

@Component({
  components: {
    ImageSwiper
  }
})
export default class ImageGalleryDesktop extends Vue {
  @Prop({ default: false }) hackCloseBtn?: boolean
  @Prop({ default: '' }) title?: string
  @Prop({ default: false }) dontGetImages?: boolean
  @Prop({ default: () => [] }) bannerList!: any[]
  @Prop() activityId!: number | string
  @Prop() showGallery!: boolean
  @Prop() defaultTag!: number
  @Prop({ default: 0 }) currentIndex!: number
  @Prop({ default: null }) overrideImages!: any[]
  @Prop({ default: null }) overrideReview!: any
  @Prop({ type: Object, default: () => ({}) }) currentPoi!: any
  @Inject({ default: () => '' }) translateI18n!: Function
  @Watch('showGallery')
  showGalleryChange(val: boolean) {
    if (val) {
      this.currentTab = this.defaultTag || 0
    }
  }

  @Watch('overrideImages', { immediate: true })
  overrideImagesChange(val: any[]) {
    if (val) {
      this.initImages()
    }
  }

  imageList: any [] = []
  reviews: any [] = []
  page: number = 1
  pageSize: number = 8
  total: number = 0
  currentReview: any = null

  mounted() {
    this.getImages(true)
  }

  get calcTitle() {
    return this.currentPoi? this.currentPoi.title : ''
  }

  get calcShowNavTag() {
    const { bannerList, total } = this
    return bannerList && bannerList.length && total
  }

  handleSwiperChange(index: number = 0) {
    this.$emit('swiperChange', index)
  }

  initImages() {
    if (this.overrideImages?.length && this.overrideReview) {
      this.currentTab = this.defaultTag
      this.imageList = this.overrideImages.map((item: any) => ({ ...item, reviewId: item.review_id }))
      this.currentReview = this.overrideReview
      this.reviews[this.imageList[0].reviewId] = this.overrideReview
      this.total = this.imageList.length
    }
  }

  handleBuyNow() {
    this.$emit('close')
    this.$nextTick(() => {
      this.$emit('buy-now')
    })
  }

  changeCommentLike(data: any) {
    const review = this.reviews[data.reviewId]
    this.$set(review, 'like_count', review.like_count + 1)
    this.$set(review, 'has_liked', true)
  }

  slideChangeTransitionEnd() {
    // this.throttleGetImages()
  }

  // throttleGetImages: any = throttle(() => {
  //   this.getImages()
  // }, 3000, { trailing: true })

  async getImages(firstLading: boolean = false) {
    // if (this.dontGetImages) {
    //   return
    // }
    // if (this.overrideImages?.length) {
    //   return
    // }

    // const images = await this.getReviewsImages({ page: this.page, pageSize: this.pageSize })
    // if (images?.imageTotalCount) {
    //   this.total = images.imageTotalCount
    //   const o = this.arrayToObj(images.reviewImagesInfo)
    //   this.reviews = Object.assign(this.reviews, o)
    //   this.imageList = this.imageList.concat(images.imageList)
    //   this.page = this.page + 1
    //   if (firstLading && this.imageList.length && this.reviews.length) {
    //     this.currentReview = this.reviews[this.imageList[0].reviewId]
    //   }
    // }
  }

  arrayToObj(array: any) {
    const o: any = {}
    array.forEach((item: any) => {
      o[item.review_id] = item
    })
    return o
  }

  // async getReviewsImages(data: { page: number, pageSize: number }) {
  //   const { activityId } = this
  //   const res = await this.$axios.$get(urlTemplate('/v1/usrcsrv/activities/:activityId/images/get', { activityId }), {
  //     regularUrl: '/v1/usrcsrv/activities/{*}/images/get',
  //     params: {
  //       page: data.page,
  //       limit: data.pageSize
  //     }
  //   })

  //   const { result } = res
  //   if (res.success && !isEmpty(result)) {
  //     const { image_total_count: imageTotalCount, review_images_info: reviewImageInfo } = result
  //     if (imageTotalCount === 0 || isEmpty(reviewImageInfo) || isEmpty(reviewImageInfo)) {
  //       return {}
  //     }

  //     /**
  //      * 评论reviews和图片image是一对多的关系
  //      * 通过图片image_id检索review_id，最后通过imageReviewMap得到review的信息
  //      * reviews的数据保存一份，因为有对数据对操作，这样可以保持所有数据的同步
  //      * @reviewImagesInfo Array 后端请求数据
  //      * @imageList Array 图片数组
  //      * @reviewsMap Object reviews和reviewId的map
  //      */

  //     const formatReviews = reduce(reviewImageInfo, (acc: any, v: any) => {
  //       acc.imageList = [...acc.imageList, ...v.images.map((img: any) => ({ ...img, reviewId: v.review_id }))]
  //       acc.reviewsMap = { ...acc.reviewsMap, [v.review_id]: v }

  //       return acc
  //     }, {
  //       imageList: [] as any[],
  //       reviewsMap: {}
  //     })

  //     return {
  //       imageTotalCount: result.image_total_count,
  //       reviewsCount: result.reviews_count,
  //       reviewImagesInfo: result.review_images_info,
  //       imageList: formatReviews.imageList,
  //       reviewsMap: formatReviews.reviewsMap
  //     }
  //   }
  // }

  BigSwiperSlideChange(idx: number) {
    const reviewId = this.imageList[idx].reviewId
    this.currentReview = this.reviews[reviewId]
  }

  closeModal() {
    this.$emit('close')
  }

  currentTab: number = 0

  switchTabs(tab: number) {
    this.currentTab = tab
    this.$nextTick(() => {
      const gallery0 = this.$refs[`gallery${tab}`] as any
      gallery0.updateSwiper()
    })
  }
}
</script>

<style lang="scss" scoped>
// 多行省略
@mixin text-ellipsis($line-clamp: 2) {
  display: -webkit-box;
  -webkit-line-clamp: $line-clamp;
  -webkit-box-orient: vertical;
  text-overflow: ellipsis;
  overflow: hidden;
}

.hack-close-btn-wrap {
  position: absolute;
  top: 46px;
  right: 46px;
  z-index: 8;
  &__close-btn{
    display: inline-block;
    width: 32px;
    height: 32px;
    font-size: 32px;
    font-weight: $fontWeight-bold;
    color: $color-text-reverse;
    cursor: pointer;
    svg {
      fill: currentColor;
      width: 100%;
      height: 100%;
    }
  }
}

.title-text {
  display: inline-block;
  box-sizing: border-box;
  max-width: 886px;
  color: #fff;
  height: 44px;
  padding-top: 20px;
  line-height: 24px;
  font-size: $fontSize-body-m;
  font-weight: $fontWeight-bold;
  @include text-ellipsis(1);
}
// .hack-hide-nav-tag {
//   padding: 44px 0;
// }
.page-activity-reviews-gallery-modal {
  ::v-deep .klk-modal {
    margin: 0;
    padding: 0;
    background-color: $color-overlay-default-1;
  }

  ::v-deep .klk-modal-close {
    position: fixed;
    top: 46px;
    right: 46px;
    font-size: 32px;
    z-index: 23;
  }

  ::v-deep .klk-modal-body {
    width: 100%;
    height: 100%;
    padding: 70px 40px;
    position: relative;
    overflow: hidden;
  }

  .reviews-container {
    width: 100%;
    max-width: 100%;
    height: 100%;
    max-height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    border-radius: $radius-l;

    .button-group {
      display: flex;
      justify-content: center;
      width: 100%;
      height: 44px;
      margin-bottom: 40px;
      .button-group-item {
        margin: 0 10px;
        border-radius: 22px;
        border-color: #fff;
        color: $color-text-reverse;
        &.active {
          background-color: $color-text-reverse;
          color: $color-text-primary;
        }
      }
    }

    .image-gallery-container {
      flex: 1 1 auto;
      display: flex;
      justify-content: center;
    }

    .reviews-container-left {
      position: relative;
      border-radius: $radius-l 0 0 $radius-l;
      &.banner-swiper {
        width: 100%;
        height: 100%;
        .banner-swiper__img-content {
          display: flex;
          align-content: flex-start;
          flex-direction: column;
          max-width: 100%;
          max-height: 100%;
        }
      }
      &.reviews-swiper {
        max-width: calc(100% - 352px - 80px);
      }
    }

    .reviews-container-right {
      flex: none;
      width: 352px;
      min-width: 352px;
      padding: 0 40px 40px 32px;
      border-radius: 0 $radius-l $radius-l 0;
    }
  }
}

.big-image-swiper {
  height: 483px;
  width: 100%;
  position: relative;

  .swiper-container {
    position: static;
    height: 100%;
    width: 636px;

    .swiper-slide {
      position: relative;
      background-size: contain;
      background-repeat: no-repeat;
      background-position: center center;
    }

    .next-prev {
      position: absolute;
      top: 0;
      height: 483px;
      width: 52px;
      cursor: pointer;
      z-index: 1;

      svg {
        position: absolute;
        top: 50%;
        right: 0;
        transform: translate(0, -50%);
        color: $color-text-placeholder-onDark;
      }

      &:hover {
        svg {
          color: $color-text-primary-onDark;
        }
      }
    }

    .swiper-next {
      right: 0;
    }

    .swiper-prev {
      left: 0;

      svg {
        left: 0;
        transform: translate(0, -50%) rotate(180deg);
      }
    }

    .swiper-pagination {
      padding: 2px;
      position: absolute;
      left: auto;
      right: 44px;
      bottom: 8px;
      width: auto;

      @include font-caption-m-regular;
      border-radius: $radius-s;
      background: $color-overlay-default-2;
      color: $color-text-reverse;
    }
  }
}

.small-image-swiper {
  position: relative;
  width: 100%;
  height: 72px;
  margin-top: 20px;

  &-wrapper {
    margin: 0 auto;
    position: relative;
    width: 646px;
    height: 100%;

    &:hover .small-next-prev {
      visibility: visible;
      pointer-events: auto;
    }
  }

  .swiper-container {
    width: 646px;
    height: 100%;
  }

  .swiper-slide {
    box-sizing: border-box;
    width: 72px;
    height: 72px;
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center center;
    cursor: pointer;
    border-radius: $radius-l;
    opacity: $opacity-overlay-desktop;
  }

  .high-light-swiper-slide {
    opacity: $opacity-solid;
    border: solid 3px $color-border-active-reverse;
  }

  .small-next-prev {
    position: absolute;
    top: 0;
    height: 72px;
    width: 20px;
    cursor: pointer;
    z-index: 1;
    outline: none;
    visibility: hidden;
    pointer-events: none;

    .gradient-wrapper {
      display: flex;
      align-items: center;
      width: 100%;
      height: 100%;
    }

    .gradient-1 {
      width: 12px;
      height: 100%;
      /* stylelint-disable-next-line */
      background: linear-gradient(90deg, rgba(0, 0, 0, 0.48) 0%, #000000 100%);
    }

    .gradient-2 {
      width: 8px;
      height: 100%;
      /* stylelint-disable-next-line */
      background: linear-gradient(90deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.48) 100%);
    }

    svg {
      position: absolute;
      top: 50%;
      right: 0px;
      transform: translate(0, -50%);
      color: $color-text-placeholder-onDark;
    }

    &:hover {
      svg {
        color: $color-text-primary-onDark;
      }
    }
  }

  .small-swiper-next {
    right: -1px;
  }

  .small-swiper-prev {
    left: -1px;

    .gradient-wrapper {
      transform: rotate(180deg);
    }

    svg {
      left: 0;
      transform: translate(0, -50%) rotate(180deg);
    }
  }
}
</style>
