<template>
  <div>
    <!-- 大图 swiper -->
    <div class="big-image-swiper">
      <div
        ref="$bigImageSwiper"
        v-swiper:$bigImageSwiper="bigImageSwiperOption"
        class="swiper-container js-swiper-container"
        :class="[name]"
      >
        <div class="swiper-wrapper">
          <div
            v-for="(item, idx) in imageList"
            :key="idx"
            v-lazy:background-image.container="item.img_url"
            ratio="1:1"
            class="swiper-slide"
            :class="highlightSwiperSlideIndex === idx && 'current-slide-visible'"
            :data-image-id="item.image_id"
          >
            <Logo :ratio="'1:1'" :width="161" />
            
            <div v-show="imageScroll && isScreened" class="swiper-slide__img-wrap">
              <img v-lazy="item.img_url" class="swiper-slide__img">
            </div>
          </div>
        </div>
        <div v-if="imageScroll && showEnlargeEntry" class="swiper-screen" @click="handleScreen">
          <IconEnlarge theme="outline" size="16" fill="#ffffff" />
          <span class="swiper-screen-text">{{ screenText }}</span>
        </div>
        <div class="swiper-pagination swiper-pagination-fraction">
          {{ highlightSwiperSlideIndex + 1 }} / {{ total }}
        </div>
        <div class="next-prev swiper-next" :class="[`${name}-swiper-next`]">
          <span class="next-prev__icon"><ArrowSvg class="svg-style" name="desktop-common#arrow-right-new" size="20"></ArrowSvg></span>
        </div>
        <div class="next-prev swiper-prev" :class="[`${name}-swiper-prev`]">
          <span class="next-prev__icon"><ArrowSvg class="svg-style" name="desktop-common#arrow-right-new" size="20"></ArrowSvg></span>
        </div>
      </div>
    </div>
    <!-- 小图 swiper -->
    <div class="small-image-swiper">
      <div class="small-image-swiper-wrapper">
        <div
          ref="$smallImageSwiper"
          v-swiper:$smallImageSwiper="smallImageSwiperOption"
          class="swiper-container"
        >
          <div class="swiper-wrapper swiper-no-swiping">
            <div
              v-for="(item, idx) in imageList"
              :key="idx"
              v-lazy:background-image.container="item.img_resize_url"
              ratio="1:1"
              class="swiper-slide swiper-no-swiping"
              :class="highlightSwiperSlideIndex === idx ? 'high-light-swiper-slide' : ''"
              :data-image-id="item.image_id"
              @click="highlightSwiperSlide(idx)"
            >
              <Logo :ratio="'1:1'" :width="24" />
            </div>
          </div>
        </div>
        <div class="small-next-prev small-swiper-next" :class="[`${name}-small-swiper-next`]">
          <span class="small-next-prev__icon"><ArrowSvg class="svg-style" name="desktop-common#arrow-right-new" size="20"></ArrowSvg></span>
        </div>
        <div class="small-next-prev small-swiper-prev" :class="[`${name}-small-swiper-prev`]">
          <span class="small-next-prev__icon"><ArrowSvg class="svg-style" name="desktop-common#arrow-right-new" size="20"></ArrowSvg></span>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Inject } from 'vue-property-decorator'
import Logo from './logo.vue'
import ArrowSvg from "../../../../../imgs/arrow-right-new.svg";
import { IconEnlarge } from '@klook/klook-icons'

@Component({
  components: {
    ArrowSvg,
    Logo,
    IconEnlarge
  }
})
export default class ImageGallery extends Vue {
  @Prop() imageList!: any[]
  @Prop() total!: number
  @Prop({ default: 0 }) currentIndex!: number
  @Prop({ default: false }) imageScroll!: boolean
  // 加上name属性，避免存在多个swiper的时候状态混乱
  // 通过name属性生成不同的类名
  @Prop({ default: '' }) name!: string
  @Inject({ default: () => '' }) translateI18n!: Function

  @Watch('currentIndex', { immediate: true })
  currentIndexChange(val: number) {
    this.$nextTick(() => {
      this.$bigImageSwiper && this.$bigImageSwiper.slideTo(val)
      this.handleImageLoad(val)
    })
  }

  showEnlargeEntry: boolean = false
  timer: any = null
  
  handleImageLoad(idx: number) {
    const _this = this
    this.showEnlargeEntry = false
  
    this.timer = setTimeout(() => {
      const image = this.imageList[idx]
      const img_url = image?.img_url ?? ''
      if (!img_url) {
        return
      }
      if (image.width && image.height) {
        this.getImgSize(image)
        return
      }      
      const img = new Image()
      img.src = img_url
      img.onload = function(){
        image.width = img.width
        image.height = img.height
        _this.getImgSize({
          width: img.width,
          height: img.height
        })
      }
    }, 350)
  }

  getImgSize(source: any) {
    if (!this.imageScroll) { return }
    const elem = this.$el
    const container = elem.querySelector('.js-swiper-container .swiper-wrapper')
    if (container) {
      const { clientWidth: boxWidth, clientHeight: boxHeight } = container

      if (source) {
        const { width: imageWidth, height: imageHeight } = source

        if (imageWidth && imageHeight) {
          const boxRatio = boxWidth / boxHeight
          const imageRatio = imageWidth / imageHeight
          this.showEnlargeEntry = imageRatio <= boxRatio
        }
      }
    }
  }

  isScreened: boolean = false

  handleScreen() {
    this.isScreened = !this.isScreened
  }

  handleSlideStart() {
    this.isScreened = false
  }

  get screenText() {
    return this.isScreened ? this.translateI18n('102478') : this.translateI18n('102477')
  }

  updateSwiper() {
    this.$smallImageSwiper.update()
    this.$bigImageSwiper.update()
  }

  $bigImageSwiper: any = null
  $smallImageSwiper: any = null
  highlightSwiperSlideIndex: number = 0
  currentReview: any = null

  bigImageSwiperOption: any = {
    navigation: {
      nextEl: '.swiper-next',
      prevEl: '.swiper-prev'
    },
    lazy: {
      loadPrevNext: true,
      loadOnTransitionStart: true
    },
    on: {
      init: () => {
        // ${name}-swiper-next
        if (this.currentIndex === 0) {
          const el = `.${this.name}-swiper-prev`
          this.initSwiper(el)
        }

        const slidesPerGroup = this.bigImageSwiperOption.slidesPerGroup || 1
        if (this.imageList.length <= slidesPerGroup) {
          this.initSwiper(`.${this.name}-swiper-next`)
        }
      },
      slideChangeTransitionStart: this.handleSlideStart,
      slideChange: () => {
        const name = this.name
        this.BigSwiperSlideChange()
        this.slideChangeTransitionEnd('$bigImageSwiper', {
          next: `.${name}-swiper-next`,
          prev: `.${name}-swiper-prev`
        })
      }
    }
  }

  smallImageSwiperOption: any = {
    slidesPerView: 'auto',
    centeredSlides: false,
    navigation: {
      nextEl: '.small-swiper-next',
      prevEl: '.small-swiper-prev'
    },
    lazy: {
      loadPrevNext: true,
      loadOnTransitionStart: true
    },
    spaceBetween: 10,
    slidesPerGroup: 8,
    noSwiping: true,
    on: {
      init: () => {
        // ${name}-small-swiper-next
        const el = `.${this.name}-small-swiper-prev`
        this.initSwiper(el)
        const slidesPerGroup = this.smallImageSwiperOption.slidesPerGroup || 1
        const maxLength = this.name === 'banner-swiper' ? 11 : slidesPerGroup
        if (this.imageList.length <= maxLength) {
          this.initSwiper(`.${this.name}-small-swiper-next`)
        }
      },
      slideChange: () => {
        const name = this.name
        this.slideChangeTransitionEnd('$smallImageSwiper', {
          next: `.${name}-small-swiper-next`,
          prev: `.${name}-small-swiper-prev`
        })
      }
    }
  }

  highlightSwiperSlide(idx: number) {
    this.$bigImageSwiper.slideTo(idx)
    this.highlightSwiperSlideIndex = idx
  }

  BigSwiperSlideChange() {
    const idx = this.$bigImageSwiper.realIndex
    this.highlightSwiperSlideIndex = idx
    this.$smallImageSwiper.slideTo(idx)
    this.$emit('bigSwiperChange', idx)
    this.handleImageLoad(idx)
  }

  slideChangeTransitionEnd(swiper: string, elem: any) {
    const selfSwiper = (this as any)[swiper]
    const $next = document.querySelector(elem.next) as any
    const $prev = document.querySelector(elem.prev) as any
    if (selfSwiper.isEnd) {
      $next.style.display = 'none'
    } else {
      $next.style.display = 'block'
    }
    if (selfSwiper.isBeginning) {
      $prev.style.display = 'none'
    } else {
      $prev.style.display = 'block'
    }
    const realIndex = selfSwiper.realIndex

    if (this.imageList.length < this.total && this.imageList.length - realIndex <= 15) {
      this.$emit('slideToEnd')
    }
  }

  initSwiper(swiper: string) {
    this.$nextTick(() => {
      const $el = document.querySelector(swiper) as any
      $el?.style && ($el.style.display = 'none')
    })
  }
}

</script>

<style lang="scss" scoped>
$calcWidth: calc((100vh - 160px - 88px - 72px) / 2 * 3);
$calcWidthArrow: calc((100vh - 160px - 88px - 72px) / 2 * 3 + 128px);
.svg-style {
  width: 20px;
  height: 20px;
}
.big-image-swiper {
  flex: 1 1 auto;
  position: relative;

  .swiper-container {
    width: $calcWidthArrow;
    padding: 0 64px;
    max-width: 100%;
    height: calc(100vh - 160px - 88px - 71px);
    max-height: 100%;
    overflow: hidden;

    .swiper-slide {
      position: relative;
      background-size: contain;
      background-repeat: no-repeat;
      background-position: center center;
      display: flex;
      align-items: center;
      visibility: hidden;

      &.current-slide-visible {
        visibility: visible;
      }

      &[lazy=loading] {
        background-color: $color-neutral-200;
      }

      &__img-wrap {
        width: 100%;
        max-height: 100%;
        overflow: auto;
        cursor: pointer;
        &::-webkit-scrollbar {
          -webkit-appearance: none;
          background-color: transparent;
          width: 7px;
        }
        &:hover::-webkit-scrollbar {
          -webkit-appearance: none;
          background: transparent;
          width: 7px;
        }
        &:hover::-webkit-scrollbar-track {
          background: transparent;
          width: 7px;
        }
        &:hover::-webkit-scrollbar-thumb {
          border-radius: 4px;
          background-color: rgba(255, 255, 255, .3);
          box-shadow: 0 0 1px rgba(255, 255, 255, .5);
        }
      }
      &__img {
        display: block;
        width: 100%;
      }
    }

    .next-prev {
      position: absolute;
      top: 0;
      height: 100%;
      width: 64px;
      cursor: pointer;
      z-index: 1;

      &__icon {
        width: 48px;
        height: 48px;
        line-height: 48px;
        border-radius: 50%;
        background-color: rgba(255, 255, 255, 0.16);
        position: absolute;
        top: 50%;
        right: 0;
        transform: translate(0, -50%);
        color: $color-text-placeholder-onDark;
        display: flex;
        align-items: center;
        justify-content: center;
        &:hover {
          border: 1px solid $color-text-reverse;
          background-color: rgba(255, 255, 255, 0.25);
        }
      }

    }

    .swiper-next {
      right: 0;
    }

    .swiper-prev {
      left: 0;
      transform: rotate(180deg);
    }

    .swiper-pagination {
      padding: 3px 4px;
      position: absolute;
      left: auto;
      right: calc(64px + 8px);
      bottom: 8px;
      width: auto;

      @include font-caption-m-regular;
      border-radius: $radius-s;
      background: $color-overlay-default-2;
      color: $color-text-reverse;
    }
  }
}
.small-image-swiper {
  position: relative;
  width: $calcWidth;
  max-width: calc(100% - 64px * 2);
  height: 72px;
  margin: 20px auto 0;

  &-wrapper {
    margin: 0 auto;
    position: relative;
    width: 100%;
    height: 100%;
    // .small-next-prev {
    //   visibility: visible !important;
    //   pointer-events: auto !important;
    // }
    &:hover .small-next-prev {
      visibility: visible;
      pointer-events: auto;
    }
  }
  .swiper-slide {
    position: relative;
    box-sizing: border-box;
    width: 72px;
    height: 72px;
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center center;
    cursor: pointer;
    border-radius: $radius-l;
    overflow: hidden;
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: $color-overlay-default-3;
    }

    &[lazy=loading] {
      background-color: $color-neutral-200;
    }

    &.high-light-swiper-slide {
      opacity: $opacity-solid;
      border: solid 3px $color-border-active-reverse;
      &::before {
        display: none;
      }
    }
  }

  .small-next-prev {
    position: absolute;
    top: 0;
    height: 72px;
    width: 48px;
    cursor: pointer;
    z-index: 1;
    outline: none;
    visibility: hidden;
    pointer-events: none;
    &.small-swiper-prev {
      left: -48px;
      transform: rotate(180deg);
    }
    &.small-swiper-next {
      right: -48px;
    }

    &__icon {
      width: 32px;
      height: 32px;
      line-height: 32px;
      border-radius: 50%;
      background-color: rgba(255, 255, 255, 0.16);
      display: flex;
      align-items: center;
      justify-content: center;
      position: absolute;
      top: 50%;
      right: 0;
      transform: translate(0, -50%);
      color: $color-text-placeholder-onDark;

      &:hover {
        border: 1px solid $color-text-reverse;
        background-color: rgba(255, 255, 255, 0.25);
      }
    }
  }

}

.swiper-screen {
  padding: 8px;
  position: absolute;
  left: 50%;
  bottom: 8px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  border-radius: $radius-m;
  color: $color-text-reverse;
  background-color: rgba(0, 0, 0, 0.45);
  transform: translateX(-50%);
  z-index: 100;
  cursor: pointer;

  &:hover {
    background-color: rgba(0, 0, 0, 0.6);
  }

  &-text {
    margin-left: 8px;
  }
}
</style>
