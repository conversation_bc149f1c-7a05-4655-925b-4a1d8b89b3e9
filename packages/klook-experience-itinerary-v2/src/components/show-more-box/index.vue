<template>
  <div class="show-more-wrap" :class="showLinearGradient && 'show-linear-gradient'">
    <div class="show-more-box">
      <slot></slot>
      <slot name="btn" :calcText="calcText" :clickBtn="clickBtn" :status="status">
        <div v-if="status" :style="moreStyle" class="common-more-box" @click="clickBtn">
          <span class="common-more-btn">
            {{ calcText }}
          </span>
          <IconNext v-if="!hideIcon && calcIconDirection" :class="calcIconDirection" theme="outline" size="16" /> 
        </div>
      </slot>
    </div>
  </div>
</template>

<script lang="ts">
import { Vue, Component, Prop, Inject } from 'vue-property-decorator'
import { IconNext } from '@klook/klook-icons'

@Component({
  components: {
    IconNext,
  }
})
export default class Index extends Vue {
  @Prop({ default: false }) showLinearGradient!: boolean 
  @Prop({ default: '' }) status!: '' | 'more' | 'less'
  @Prop({ default: '' }) moreText!: string
  @Prop({ default: '' }) lessText!: string
  @Prop({ default: '' }) moreStyle!: any
  @Prop({ default: false }) hideIcon!: boolean

  @Inject() translateI18n!: Function

  get calcIconDirection() {
    const { status } = this
    let str = ''
    if (status) {
      str = status === 'more' ? 'down' : 'up'
    }
    return str
  }

  clickBtn() {
    const { status } = this
    status && this.$emit('click', status === 'more' ? 'less' : 'more')
  }

  get calcText() {
    const { status, moreText, lessText } = this
    let text = ''
    if (status === 'more') {
      text = moreText || this.translateI18n('see_more')
    } else if (status === 'less') {
      text = lessText || this.translateI18n('17843')
    }
    return text
  }

  updated() {
    this.$emit('updateData', this.getUpdateData())
  }

  mounted() {
    this.$emit('updateData', this.getUpdateData()) 
  }

  getUpdateData() {
    return {
      height: this.$el.clientHeight
    }
  }
}
</script>

<style lang="scss" scoped>
.text-underline-style.show-more-wrap{
  .show-more-box{
    .common-more{
      &-box{
          margin-top: 8px;
          color: #000;
      }
      &-btn{
        text-decoration: underline;
      }
    }
  }
}
.show-more-wrap{
  &.show-linear-gradient{
    .common-more-box{
      position: relative;
      &::before{
        content: '';
        position: absolute;
        z-index: 3;
        left: 0;
        top: -74px;
        display: inline-block;
        width: 100%;
        height: 74px;
        background: linear-gradient(180deg, #F5F5F500, #FFFFFF 100%);
      }
    }
  }
}
.show-more-wrap{
  .show-more-box{
    display: flex;
    flex-wrap: wrap;
    .common-more{
        &-box{
            padding: 12px 0 0 0;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100%;
            @include font-body-s-bold;
            color: $color-brand-primary;
            cursor: pointer;
            background-color: white;
            .down{
              margin-left: 8px;
              transform: rotate(90deg);
            }
            .up{
              margin-left: 8px;
              transform: rotate(-90deg);
            }
        }
        &-icon{
            margin-left: 8px;
        }

    }
  }
}
.show-more-wrap{
  &.mini-style{
    .common-more-btn{
      font-size: $fontSize-caption-m;
      line-height: 16px;
    }
  }
}
</style>
