<template>
  <klk-map ref="map" v-bind="mapConfig">
    <klk-map-marker
      v-for="(marker, index) in mapMarker"
      :key="index"
      :center="marker.center"
      :options="marker.options"
      :class="{ 'marker-active': currentMarker === index }"
      :z-index="currentMarker === index ? 202 : 100"
      v-bind="getBindTracker(marker.tracker)"
    >
      <img
        v-show="currentMarker != index"
        class="icon-img"
        :src="marker.groupIcon.unselect"
        @click="markerClick(index)"
      >
      <img
        v-show="currentMarker === index"
        class="icon-img"
        :class="{ active: currentMarker === index }"
        :src="marker.groupIcon.select"
      >
    </klk-map-marker>
    <div class="klk-scroll-snap-map" @touchmove.stop>
      <klk-scroll-snap ref="scrollSnap" @change="setMarkerIndex">
        <MapCard
          v-for="(item, index) in itineraryMap"
          :key="index"
          :style="cardStyle"
          :data="item"
        />
      </klk-scroll-snap>
    </div>

    <KlkMapCircleButton
      class="close-button"
      v-bind="getBindTracker({
        type: 'module',
        spm: 'ItineraryMapLayerClose',
        exposure: false
      })"
      @click.native="$emit('close')"
    >
      <klk-icon type="icon_navigation_close_m" size="20"></klk-icon>
    </KlkMapCircleButton>

    <KlkMapUserLocation
      v-bind="getBindTracker({
        type: 'module',
        spm: 'ItineraryMapFindMyPositionButton',
        exposure: false
      })"
      class="user-location"
      :style="locationBottom"
    />
  </klk-map>
</template>

<script lang="ts">
import { Vue, Component, Prop, Watch, Inject } from 'vue-property-decorator'

import {
  KlkMap,
  KlkMapMarker,
  KlkMapUserLocation,
  KlkMapCircleButton,
  MapTypes,
  mapUtils
} from '@klook/map'
import '@klook/map/dist/esm/index.css'
import KlkScrollSnap from '@klook/klook-scroll-snap'
import '@klook/klook-scroll-snap/dist/esm/index.css'

import MapCard from '../map-card/index.vue'
import { handleIHTrack, getExt } from '../../../../utils/inHouseTrack'

import KlkIcon from '@klook/klook-ui/lib/icon'
import '@klook/klook-ui/lib/styles/components/icon.scss'

@Component({
  components: {
    KlkMap,
    KlkMapMarker,
    KlkScrollSnap,
    KlkMapUserLocation,
    KlkMapCircleButton,
    KlkIcon,
    MapCard,
  }
})
export default class Map extends Vue {
  @Prop({ type: Object, default: () => [] }) itineraryMap!: any
  @Prop({ type: String, default: 'mapbox' }) mapType!: 'mapbox' | 'google'
  @Prop() currentPoi!: any
  @Inject("language2provide") language!: string;

  @Watch('currentPoi', { deep: true, immediate: true })
  currentPoiChange(val: any) {
    const id = val?.attr_id ?? 0
    if (!val || !id) {
      return
    }
    const itineraryMap = this.itineraryMap || []
    const currentIndex = itineraryMap.findIndex((item: any) => item.attr_id === id)
    if (currentIndex >= 0) {
      this.$nextTick(() => {
        this.markerClick(currentIndex)
      })
    }
  }

  currentMarker: number = 0

  $map: MapTypes.MapTool | undefined

  scrollSnapHeight: number = 0

  get locationBottom() {
    const bottom = this.scrollSnapHeight + 12
    return {
      bottom: `${bottom}px`
    }
  }

  get cardStyle() {
    const height = this.scrollSnapHeight - 16
    const itineraryMap = this.itineraryMap
    const width = itineraryMap.length > 1 ? {} : {
      width: 'calc(100% - 40px)'
    }
    return {
      height: `${height}px`,
      ...width
    }
  }

  get mapConfig() {
    const mapType = this.mapType
    const center = this.itineraryMap?.[0]?.map?.location
    const [lat = '', lng = ''] = center?.split(',') || []
    return {
      language: this.language,
      type: mapType,
      // 本地调试打开该token，否则地图无法正常加载
      // 产线环境记得注释掉
      // $$mock
      googleConf: { token: 'AIzaSyByoaOJMATcSHo6iZ-cofp9vlHU8t64ukw' },
      height: '100%',
      interactive: 'greedy',
      zoom: 16,
      center: `${lng},${lat}`
    }
  }

  get mapMarker() {
    return this.initMarker()
  }

  mounted() {
    this.calcHeight()
    this.initMap()
  }

  initMap() {
    const map = this.$refs.map as any
    map?.$getMapTool && map.$getMapTool.then(async ($map: MapTypes.MapTool) => {
      this.$map = $map
      const points = this.mapMarker.map((item: any) => item.center)
      const box = await mapUtils.turfBbox(points, this.mapType as MapTypes.Type)
      const bounds = $map.createBounds(box.sw, box.ne, {
        formatLngLat: false
      })
      $map.fitBounds(bounds, {
        padding: {
          top: 80,
          left: 40,
          bottom: this.scrollSnapHeight,
          right: 40
        }
      })
      // 隐藏默认点marker，其他地图元素也可以用这个方法
      if (typeof this.$map?.map?.setOptions === 'function') {
        const noPoi = [
          {
            featureType: 'poi',
            elementType: 'labels.icon',
            stylers: [
              { visibility: 'off' }
            ]
          }
        ]
        this.$map.map.setOptions({ styles: noPoi })
      }
    })
  }

  initMarker() {
    const itineraryMap = this.itineraryMap || []

    return itineraryMap.map((item: any) => {
      const center = item.map.location
      const group_key = item.group_key
      const icon_url = item.group_icon || ''
      const ext = {
        marker_type: group_key
      }
      const tracker = {
        type: 'module',
        spm: 'ItineraryMapMarkerClick',
        exposure: false,
        query: {
          ext: getExt(ext)
        }
      }
      const groupIcon = item.group_icon_v2 || { select: icon_url, unselect: icon_url }
      return {
        center: mapUtils.formatLatLng(center),
        options: {
          anchor: 'bottom'
        },
        tracker,
        icon_url,
        groupIcon
      }
    })
  }

  getBindTracker(tracker: any) {
    return handleIHTrack(tracker)
  }

  calcHeight() {
    const scrollSnap = this.$refs.scrollSnap as any
    const elem = scrollSnap.$el
    this.scrollSnapHeight = elem.offsetHeight
  }

  getMarkerStyle(marker: any) {
    const background = marker?.background ?? '#fff'
    return { background }
  }

  stopChange = false

  markerClick(index: number) {
    this.stopChange = true
    const scrollSnap = this.$refs.scrollSnap as any
    scrollSnap.slideTo(index)
    this.currentMarker = index
    this.flyTo(index)
  }

  setMarkerIndex(index: number) {
    if (this.stopChange) {
      this.stopChange = false
      return
    }

    this.currentMarker = index
    this.flyTo(index)
  }

  flyTo(index: number) {
    const marker = this.mapMarker[index]
    if (!marker) {
      return
    }
    const $map = this.$map
    if (!$map) {
      return
    }
    const latLng = marker.center
    this.$map!.flyTo(latLng)
  }
}
</script>

<style lang="scss" scoped>
.icon-img {
  width: 40px;
  height: 46px;
  object-fit: contain;

  &.active {
    transform: scale(1.36);
    transform-origin: 50% bottom;
    animation: MarkerScale 0.26s ease-in 0s 1;
  }
}

.klk-scroll-snap-map {
  position: absolute;
  bottom: 0px;
  left: 0;
  right: 0;
  z-index: 222;
}

.user-location {
  bottom: 174px;
  left: unset !important;
  right: 16px;
}

.close-button {
  top: 20px;
  left: 20px;
  position: absolute;
}

.marker-item-outer {
  box-shadow: $shadow-normal-4;
  border: 1px solid $color-border-normal;
  border-radius: $radius-circle;
  position: relative;
  &.active {
    border: 2px solid $color-border-normal;
    transform: scale(1.4);
    transform-origin: 50% bottom;
    transition: transform ease-in-out .25s;
    .marker-item-outer-foot {
      border: 2px solid $color-border-normal;
    }
  }
  .marker-item-outer-foot {
    width: 12px;
    height: 12px;
    transform: translateX(-50%) rotate(45deg);
    transform-origin: center;
    position: absolute;
    bottom: -4px;
    left: 50%;
    z-index: 1;
    border: 1px solid $color-border-normal;
    border-radius: 1px;
  }
  .marker-item {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    border-radius: $radius-circle;
    z-index: 2;
  }
}

::v-deep .klkMap_marker.marker-active {
  z-index: 202 !important;
}

::v-deep .klkMap_user_location-marker {
  background-color: $color-brand-primary-light;
  height: 24px;
  width: 24px;
  border: 6px solid $color-brand-primary;
  box-shadow: $shadow-normal-4;
}

@keyframes MarkerScale {
  from {
    transform: scale(1);
  }
  to {
    transform: scale(1.4);
  }
}
</style>
