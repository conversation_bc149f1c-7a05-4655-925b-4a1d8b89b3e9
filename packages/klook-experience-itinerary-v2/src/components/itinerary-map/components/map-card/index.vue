<template>
  <div class="card-wrap">
    <img
      v-if="info.image"
      :src="info.image"
      class="image"
      v-bind="getBindTracker({
        type: 'module',
        spm: 'ItineraryMapCardImage',
        exposure: false
      })"
    >
    <div class="info-wrap">
      <div v-if="info.tag && info.tag.length" class="info-tags">
        <div
          v-for="(tagItem, index) in info.tag"
          :key="index"
          class="tag-item"
          :style="{
            color: tagItem.text_color,
            backgroundColor: tagItem.background_color,
            borderColor: tagItem.border_color,
            borderRadius: `${tagItem.radius}px`,
            opacity: tagItem.background_alpha !== undefined ? tagItem.background_alpha : 1
          }"
        >
          {{ tagItem.text }}
        </div>
      </div>
            <!-- {
  "text_color": "#0B9E65",
  "border_color": "#0B9E65",
  "text": "Attractions"
} -->
      <div
        v-if="info.title_icon && info.title_icon.text"
        class="info-title-icon"
        :style="{
          color: info.title_icon.text_color,
          borderColor: info.title_icon.border_color
        }"
      >
        {{ info.title_icon.text }}
      </div>

      <div class="info-top">
        <div class="info-top-left">
          <div v-if="info.title" class="bold-head line-clamp">{{ info.title }}</div>
          <div class="head ellipsis">
            {{ info.group_time }}
            <span v-if="info.group_time" class="dot"></span>
            {{ info.group_name }}
          </div>
          <div v-for="(item, idx) in info.icons" :key="idx" class="head">{{ item.text }}</div>
        </div>
      </div>
      <div
        v-if="info.address"
        class="sub-head"
        v-bind="getBindTracker({
          type: 'module',
          spm: 'ItineraryMapCardAddress',
          exposure: false
        })"
        @click="copyAddress"
      >
        <div class="address line-clamp">{{ info.address }}</div>
        <div v-if="!info.is_static_card" class="copy-wrap">
          <CopyIconSvg style="width: 16px; height: 16px"/>
        </div>
      </div>
      <div
        v-if="info.is_static_card"
        class="search-area"
        v-bind="getBindTracker({
          type: 'module',
          spm: 'LocationSearch',
          exposure: true
        })"
        @click.stop="$emit('search')"
      >
        <SearchGlassIcon class="search-icon" />
        <div class="search-text">{{ info.search_placeholder }}</div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Vue, Component, Prop, Inject } from 'vue-property-decorator'
import { copyToClipboard, setNewImageSize } from '../../../../utils'
import { handleIHTrack } from '../../../../utils/inHouseTrack'

import CopyIconSvg from '../../../../imgs/icon_other_copy_s.svg'
import SearchGlassIcon from '../../../../imgs/search-glass-icon.svg'

import Toast from '@klook/klook-ui/lib/toast'

@Component({
  components: {
    CopyIconSvg,
    SearchGlassIcon
  }
})
export default class MapCard extends Vue {
  @Prop() data!: any
  @Inject({ default: () => null }) translateI18n!: Function

  async copyAddress() {
    const text = this.info.address
    const result = await copyToClipboard(text)

    if (result) {
      Toast(this.translateI18n('copy_success'))
    }
  }

  getBindTracker(tracker: any) {
    return handleIHTrack(tracker)
  }

  get info() {
    const { title, icons, map, group_name, group_time, img: image, is_static_card, search_placeholder, tag, title_icon } = this.data || {}
    const location = map?.location ?? ''
    const address = map?.address_desc ?? ''
    // 只要1条数据
    const iconList = Array.isArray(icons) ? icons.slice(0, 1) : []
    return {
      title,
      icons: iconList,
      address,
      group_time,
      group_name,
      location,
      image: image && setNewImageSize(image, 'image/upload/', 240, 240, 0),
      is_static_card,
      search_placeholder,
      tag,
      title_icon
    }
  }
}

</script>

<style lang="scss" scoped>
.search-area {
  display: flex;
  padding: 8px 20px;
  align-items: center;
  border-radius: $radius-m;
  background-color: $color-bg-3;
  margin-top: 12px;

  .search-icon {
    width: 16px;
    height: 16px;
    margin-right: 3px;
  }

  .search-text {
    @include font-body-s-regular;
  }
}

.card-wrap {
  background: $color-bg-1;
  box-shadow: $shadow-normal-4;
  border-radius: $radius-xl;
  display: flex;
  padding: 12px;
  height: 150px !important;
  gap: 12px;

  .image {
    width: 70px;
    height: 70px;
    object-fit: cover;
    flex: none;
    border-radius: $radius-l;
    margin-right: 12px;
  }
  .info-wrap {
    display: flex;
    flex-direction: column;
    gap: 4px;
    flex: 1;
    color: $color-text-primary;
    .info-tags {
      display: flex;
      flex-wrap: wrap;
      gap: 4px;
      margin-bottom: 8px;

      .tag-item {
        padding: 2px 6px;
        border: 1px solid;
        border-radius: 6px;
        @include font-caption-s-regular;
        white-space: nowrap;
      }
    }
    .info-top {
      display: flex;
      &-left {
        flex: 1;
        padding-right: 12px;
        .head {
          @include font-body-s-regular;
          .dot {
            width: 3px;
            height: 3px;
            border-radius: $radius-circle;
            background: $color-text-primary;
            display: inline-block;
            vertical-align: middle;
          }
        }
        .bold-head {
          max-height: 42px;
          @include font-body-s-bold;
        }
      }
    }
    .sub-head {
      /* padding-top: 4px; */
      color: $color-text-secondary;
      display: flex;
      @include font-caption-m-regular;
      .address {
        flex: 1;
        max-height: 40px;
      }
      .copy-wrap {
        flex: none;
        align-self: flex-end;
        width: 32px;
        height: 19px;
        display: flex;
        justify-content: flex-end;
        background: linear-gradient(270deg, #FFFFFF 0%,#FFFFFF 50%, rgba(255, 255, 255, 0) 100%);
      }
    }

    .info-title-icon {
      @include font-caption-m-semibold;
      width: fit-content;
      border: 1px solid transparent;
      padding: 1px 6px;
      border-radius: 4px;
    }
  }
}
.ellipsis {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  word-break: break-all;
}

.line-clamp {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}
</style>
