<template>
  <div class="session-details-comp" :class="[platform]">
    <div class="session-details-comp__desc">{{ sessionTable.desc }}</div>
    <div class="session-details-comp__table">
      <div
        v-for="(item, index) in sessionTable.table"
        :key="index"
        class="session-details-comp__row"
        :class="{ 'table-header': index === 0 }"
      >
        <div class="session-details-comp__row-key">{{ item.values[0] }}</div>
        <div class="session-details-comp__row-value">{{ item.values[1] }}</div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Vue, Component, Prop } from 'vue-property-decorator'

@Component
export default class SessionDetails extends Vue {
  @Prop({ default: 'mobile' }) platform!: string
  @Prop({ type: Object, required: true })
  readonly sessionTable!: {
    desc: string;
    table: Array<{
      type: 'desc' | 'text';
      values: string[];
    }>;
  }
}
</script>

<style lang="scss" scoped>
.session-details-comp {
  display: flex;
  flex-direction: column;
  gap: 12px;

  &.desktop {
    height: 170px;
    @include font-paragraph-xs-regular;
    color: #fff;
  }

  &.mobile {
    gap: 16px;

    .session-details-comp__desc {
      position: sticky;
      top: 0;
      background-color: #fff;
      padding-top: 12px;
      color: $color-text-primary;
      z-index: 999999;
    }

    .session-details-comp__row {
      color: $color-text-primary;
      border-bottom-color: $color-border-normal;

      &.table-header {
        @include font-body-s-regular-v2;
        padding: 0 0 12px;
        color: $color-text-secondary;
      }
    }
  }

  &__desc {
    @include font-paragraph-s-regular;
  }

  &__table {
    overflow: auto;
    overscroll-behavior: none;
  }

  &__row {
    @include font-paragraph-s-regular;
    display: flex;
    justify-content: space-between;
    gap: 12px;
    padding: 12px 0 8px;
    border-bottom: 1px dashed #EEEEEE;

    &:last-child {
      border-bottom: none;
    }

    &.table-header {
      @include font-body-s-regular-v2;
      padding: 0 0 12px;
    }
  }
}
</style>
