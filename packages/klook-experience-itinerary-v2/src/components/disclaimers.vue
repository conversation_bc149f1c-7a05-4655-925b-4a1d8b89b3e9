<template>
  <div v-if="disclaimers && disclaimers.length" class="itinerary-disclaimers">
    <div v-for="(item, index) in disclaimers" :key="index" class="itinerary-disclaimers__item">
      <img :src="item.icon" alt="icon" class="itinerary-disclaimers__item-icon" />
      <span class="itinerary-disclaimers__item-text">{{ item.text }}</span>
    </div>
  </div>
</template>

<script lang="ts">
import { Vue, Component, Prop } from "vue-property-decorator";
import { Disclaimers } from '../types'

@Component
export default class ActivityMapLayer extends Vue {
  @Prop({ default: () => [], type: Array })
  disclaimers!: Array<Disclaimers>;
}
</script>

<style scoped lang="scss">
.itinerary-disclaimers {
  display: flex;
  gap: 8px;
  flex-direction: column;

  &__item {
      display: flex;
      gap: 8px;
      align-items: flex-start;
  }

  &__item-icon {
    width: 14px;
    height: 14px;
  }

  &__item-text {
    @include font-body-s-regular-v2;
    color: #a8a8a8;
  }
}
</style>
