<template>
  <div v-if="list.length" class="itinerary-seasonal-details" :class="platform">
    <TypeComp
      :trackInfo="trackInfo"
      :platform="platform"
      :list="list"
      v-bind="$attrs"
      @clickImage="$emit('clickImage', $event)"
    />
  </div>
</template>

<script lang="ts">
import { Vue, Component, Prop } from 'vue-property-decorator'
import TypeComp from '../../type-comp/index.vue'

@Component({
  components: {
    TypeComp
  }
})
export default class Index extends Vue {
  @Prop() trackInfo!: { type: string, extra: object }
  @Prop({ default: 'mobile' }) platform!: string
  @Prop({ type: Array, required: true }) list!: any
}
</script>

<style lang="scss" scoped>
.itinerary-seasonal-details {
  ::v-deep .type-comp {
    display: flex;
    flex-direction: column;
    gap: 8px;

    .title-icons-group .title-item.has-padding {
      padding: 0;
    }

    .title-icons-group.text-underline .title-item:last-of-type:after {
      display: none;
    }

    .card-swiper {
      flex-direction: column;
      gap: 12px;
      padding: 0;

      .imgs-comp .imgs-comp__item:not(:last-of-type) {
        margin-right: 0;
      }

      .prefix-img-contain.imgs-comp__item-img {
          width: 100% !important;
          padding-bottom: 62.5% !important;
      }

      .imgs-comp .imgs-comp__item-img {
        border-radius: $radius-l;
      }
    }
  }
}
</style>
