<template>
  <ShowMoreBox
    v-if="details && details.tabs"
    class="itinerary-details-desktop"
    :showLinearGradient="status === 'more'"
    :status="status"
    :translateI18n="translateI18n"
    v-on="$listeners" 
  >
    <div
      class="custom-tabs__list"                     
      :style="{
        maxHeight: status === 'more' ? maxHeight + 'px' : ''
      }"
    >
      <div v-for="(tab, index) in details.tabs" :key="index" :label="tab.tab_name" class="custom-tabs__item">
        <div v-if="details.tabs.length > 1" class="custom-tabs__item-title">
          {{ tab.tab_name }}
        </div>
        <ItineraryDetails :trackInfo="tab.track_info" :platform="platform" class="itinerary-details-desktop__content" :sectionList="tab.sections" @clickImage="$emit('clickImage', $event)" />
      </div>
    </div>
  </ShowMoreBox>
</template>

<script lang="ts">
import { Vue, Component, Prop } from 'vue-property-decorator'
import { ItineraryDay } from '../../../types'
import ItineraryDetails from '../details/index.vue'
import ShowMoreBox from '../../show-more-box/index.vue'

@Component({
  components: {
    ShowMoreBox,
    ItineraryDetails,
  }
})
export default class Index extends Vue {
  @Prop() details!: ItineraryDay['groups'][number]['details']
  @Prop() status!: string
  @Prop({ default: 700 }) maxHeight!: number
  @Prop({ default: 'mobile' }) platform!: string
  @Prop({ default: (str: string) => str }) translateI18n!: Function

  get calcDetailsTabs() {
    return this.details?.tabs || []
  }
}
</script>

<style lang="scss" scoped>
.itinerary-details-desktop ::v-deep{
  .show-more-box .common-more-box {
    justify-content: flex-start;
  }
}
.itinerary-details-desktop__content ::v-deep{
  padding: 0;
  .card-swiper-wrap{
    &.imgs-comp {
      .card-swiper {
        padding: 0 0 20px 0;
      }
    }
  } 
}
.custom-tabs {
  &__list {
    width: 100%;
    overflow: hidden;
  }
  &__item {
    &:not(:last-of-type) {
      margin-bottom: 24px;
    }
  }
  &__item-title {
    @include font-body-m-bold-v2;
    color: $color-text-primary;
    margin-top: 24px;
  }
}
</style>
