<template>
  <div v-if="details && details.tabs" class="itinerary-details-mobile" :class="{ 'is-bs-style': isBottomSheetStyle, 'is-one-tabs-style': calcDetailsTabs.length <= 1 }">
    <klk-tabs v-model="active" underline>
      <klk-tab-pane v-for="(tab, index) in details.tabs" :key="index" v-bind="getPageTrack(tab.track_info)" :label="tab.tab_name">
        <ItineraryDetails
          :trackInfo="tab.track_info"
          :platform="platform"
          class="itinerary-details-mobile__content"
          :class="{ 'has-poi-style': showPoiButton }"
          :sectionList="tab.sections"
          :refresh="active"
          @clickImage="$emit('clickImage', $event)"
        />
        <ViewOnMapBottom
          v-if="showPoiButton"
          v-bind="getMapTrack(tab.track_info)"
          :mapTitle="mapTitle"
          @click="$emit('viewOnMapBottom')" />
      </klk-tab-pane>
    </klk-tabs>
  </div>
</template>

<script lang="ts">
import { Vue, Component, Prop } from 'vue-property-decorator'
import { ItineraryDay } from '../../../types'
import ItineraryDetails from '../details/index.vue'
import ViewOnMapBottom from '../../view-map/mobile/view-on-map-bottom.vue'

@Component({
  components: {
    ItineraryDetails,
    ViewOnMapBottom
  }
})
export default class Index extends Vue {
  @Prop() details!: ItineraryDay['groups'][number]['details']
  @Prop({ default: 'mobile' }) platform!: string
  @Prop() mapTitle!: string
  @Prop({ default: false }) showPoiButton!: boolean
  @Prop({ default: false }) isBottomSheetStyle!: boolean

  active = this.calcDetailsTabs.length ? 0 : undefined

  get calcDetailsTabs() {
    return this.details?.tabs || []
  }

  getPageTrack(trackData: any) {
    if (!trackData.type) {
      return {}
    }
    const obj = {
      'data-spm-page': trackData.type
    }
    return obj
  }

  getMapTrack(trackData: any) {
    const obj = {
      'data-spm-module': `ItineraryMap?ext=${JSON.stringify(trackData?.extra || {})}`,
      'data-spm-virtual-item': '__virtual'
    }
    return obj
  }
}
</script>

<style lang="scss" scoped>
.itinerary-details-mobile {
  padding-top: 0px;
  &.is-bs-style ::v-deep{
    .klk-tabs-body {
      margin-top: 20px;
    }
    .klk-tabs-header {
      padding-left: 20px;
      position: sticky;
      top: 0;
      background-color: #fff;
      z-index: 999;
    }
    .itinerary-details-mobile__content {
      padding: 0 20px;
      &.has-poi-style {
        padding-bottom: 58px;
      }
    }
  }
  &.is-one-tabs-style .klk-tabs ::v-deep{
    padding-top: 20px;
    .klk-tabs-header {
      display: none;
    }
    .klk-tabs-body {
      margin-top: 0;
    }
  }
}
</style>
