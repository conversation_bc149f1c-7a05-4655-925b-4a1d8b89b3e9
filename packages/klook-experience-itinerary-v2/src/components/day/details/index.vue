<template>
  <div class="itinerary-details" :class="platform">
    <div class="section-list">
      <div v-for="(section, index) in sectionList" :key="index" class="section-list__item">
        <p v-if="section.section_name" class="itinerary-details__title" :class="platform === 'mobile' && index === 0 && 'none-margin'">
          {{ section.section_name }}
        </p>
        <TypeComp
          v-if="section.components"
          :trackInfo="trackInfo"
          :platform="platform"
          :show-more="getShowMore(section)"
          :list="section.components"
          :from-type="platform === 'mobile' ? 'mobile-itinerary-details' : 'desktop-itinerary-details'"
          :isSetGap="getIsSetGap(section, index)"
          :refresh="refresh"
          class="itinerary-details__comp-box"
          @clickImage="$emit('clickImage', $event)"
        />
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Vue, Component, Prop } from 'vue-property-decorator'
import TypeComp from '../../type-comp/index.vue'

@Component({
  components: {
    TypeComp
  }
})
export default class Index extends Vue {
  @Prop() trackInfo!: { type: string, extra: object }
  @Prop({ default: 'mobile' }) platform!: string
  @Prop() sectionList!: any
  @Prop({ default: undefined }) refresh!: string | number | undefined

  getIsSetGap(section: any, index: number) {
    if (this.platform === 'desktop') {
      return false
    }
    return index === 0 && !section.section_name // mweb bottom-sheet首个section不需要gap

  }

  getShowMore() {
    const arr = this.sectionList?.filter((section: any) => section?.section_name)
    return arr?.length > 1
  }
}
</script>

<style lang="scss" scoped>
.desktop {
  .itinerary-details {
    .section-list {
      &__item {
        &:not(:last-of-type) {
          margin-bottom: 0;
        }
      }
    }
  }
}
.itinerary-details {
  .section-list {
    &__item {
      &:not(:last-of-type) {
        margin-bottom: 0;
      }
    }
    .itinerary-details__title {
      display: inline-block;
      margin: 24px 0 0 0;
      padding: 4px 8px;
      border-radius: $radius-s;
      @include font-body-s-semibold;
      color: $color-purple-500;
      background-color: $color-purple-0;
      &.none-margin {
        margin: 0 0 0 0;
      }
    }
  }
}
.itinerary-details__comp-box ::v-deep{
  .icons-comp {
    flex-direction: column;
    flex-wrap: nowrap;
    &__item{
      &:not(:last-of-type) {
        margin-top: 4px;
        margin-right: 0;
        margin-bottom: 8px;
      }
    }
  }
}
</style>
