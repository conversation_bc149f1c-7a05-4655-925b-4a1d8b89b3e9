<template>
  <div v-if="list" class="day-list" :class="{'is-one-day': list.length === 1}">
    <div class="collapse-container-gray">
      <klk-collapse v-model="activeList" size="small" split class="klk-collapse-reset-style">
        <klk-collapse-item
          v-for="(day, dayIndex) in cacheList"
          :key="'' + dayIndex"
          :name="'' + dayIndex"
          :initial-collapsed="false"
          :data-index="dayIndex"
          :class="getItineraryMenusClass(dayIndex)"
        >
          <div slot="header" v-bind="getDayBtnTrack({ index: dayIndex, expand })" class="header-box" slot-scope="{expand, toggle}" @click="toggle">
            <div class="header-box__title">{{ day.day_name }}</div>
            <IconUpCircle v-if="expand" class="header-box__icon" theme="outline" size="16" />
            <IconDownCircle v-else class="header-box__icon" theme="outline" size="16" />
          </div>
          <div class="day-list__content">
            <div v-if="day.groups && day.groups.length" :current="0" direction="vertical" class="klk-steps-reset-style">
              <div
                v-for="(item, index) in day.groups"
                :key="index"
                class="day-list__group-item"
              >
                <CardBox v-if="platform === 'mobile'" v-bind="getGroupTrack(item, [TypeDefined.AttractionType])" :data="item" :card-type="getCardType(item)" class="hack-step-style">
                  <TitleComp
                    v-bind="getGroupTrack(item, [TypeDefined.NormalType])"
                    :seoElement="getSeoElement(item)"
                    :platform="platform"
                    :leftIcon="item.left_icon"
                    :titles="item.titles"
                    :is-title-bold="item.is_title_bold"
                    :is-details-active="getIsDetailsActive(item)"
                    @clickItineraryDetails="clickItineraryDetailsHandler(item, $event)"
                    @clickSeasonalDetails="clickSeasonalDetailsHandler(item, $event)"
                  />
                  <TypeComp
                    v-if="item.title_components && item.title_components.length"
                    :platform="platform"
                    :list="item.title_components"
                    data-source="title_components"
                    @clickItineraryDetails="clickItineraryDetailsHandler(item, $event)"
                    @clickSeasonalDetails="clickSeasonalDetailsHandler(item, $event)"
                    @clickImage="clickImageHandler(item, $event)"
                  />
                  <TypeComp
                    v-if="item.card_components && item.card_components.length"
                    slot="sub-card-box-slot"
                    v-bind="getGroupTrack(item, [TypeDefined.PoiType])"
                    :platform="platform"
                    :list="item.card_components"
                    :isSetGap="true"
                    data-source="card_components"
                    @clickItineraryDetails="clickItineraryDetailsHandler(item, $event)"
                    @clickSeasonalDetails="clickSeasonalDetailsHandler(item, $event)"
                    @clickImage="clickImageHandler(item, $event)"
                  />
                </CardBox>
                <div v-else>
                  <!-- desktop -->
                  <CardBox v-bind="getGroupTrack(item, [TypeDefined.AttractionType])" :data="item" :platform="platform" :card-type="getCardType(item)" class="hack-step-style">
                    <div class="spec-title-map-box">
                      <TitleComp
                        v-bind="getGroupTrack(item, [TypeDefined.NormalType, TypeDefined.PoiType])"
                        :seoElement="getSeoElement(item)"
                        :isPoiTitle="!!item.title_map_data"
                        :platform="platform"
                        :isExpand="getIsExpand(item)"
                        :leftIcon="item.left_icon"
                        :titles="item.titles"
                        :is-title-bold="item.is_title_bold"
                        :is-details-active="getIsDetailsActive(item)"
                        @clickTitle="handleTitleClick({ poiMapData: item.title_map_data }, true)"
                        @clickSeasonalDetails="clickSeasonalDetailsHandler(item, $event)"
                        @clickItineraryDetails="clickItineraryDetailsHandler(item, $event)"
                      />
                      <slot v-if="dayIndex === 0 && index === 0" name="day-content-top-slot"></slot>
                    </div>
                    <TypeComp
                      v-if="item.title_components && item.title_components.length"
                      :platform="platform"
                      :list="item.title_components"
                      data-source="title_components"
                      @clickItineraryDetails="clickItineraryDetailsHandler(item, $event)"
                      @clickSeasonalDetails="clickSeasonalDetailsHandler(item, $event)"
                      @clickImage="clickImageHandler(item, $event)"
                    />
                    <TypeComp
                      v-if="item.card_components && item.card_components.length && !getIsExpand(item)"
                      slot="sub-card-box-slot"
                      :platform="platform"
                      :list="item.card_components"
                      :isSetGap="false"
                      data-source="card_components"
                      @clickItineraryDetails="clickItineraryDetailsHandler(item, $event)"
                      @clickSeasonalDetails="clickSeasonalDetailsHandler(item, $event)"
                      @clickImage="clickImageHandler(item, $event)" />
                  </CardBox>
                  <ItineraryDetailsDesktop
                    v-if="item.details && item.details.tabs"
                    v-show="getIsExpand(item)"
                    :details="item.details"
                    :status="item[isExpandSeeMoreKey]"
                    :maxHeight="moreObj.maxHeight"
                    :translateI18n="translateI18n"
                    :platform="platform"
                    @click="clickShowMore(item, $event)"
                    @updateData="updateDataHandler(item, $event)"
                    @clickImage="clickImageHandler(item.details, $event)"
                  ></ItineraryDetailsDesktop>
                </div>
              </div>
            </div>
          </div>
        </klk-collapse-item>
      </klk-collapse>
    </div>
    <klk-bottom-sheet
      v-if="platform === 'mobile' && bsObj.visible"
      v-bind="bsObj"
      :visible.sync="bsObj.visible"
      :height="calcDetailsTabs.length <= 1 ? undefined : '90%'"
      :header-divider="calcDetailsTabs.length <= 1"
      class="itinerary-details-bs"
    >
      <ItinerarySeasonalDetails
        v-if="bsData.type === POI_TYPE.SEASONAL"
        class="seasonal-details-comp"
        :platform="platform"
        :list="bsData.item.components"
        :isSetGap="false"
        @clickImage="clickImageHandler(bsData.item.details, $event)"
      />
      <ItineraryDetailsMobile
        v-else-if="bsData.item && bsData.item.details"
        :details="bsData.item.details"
        :showPoiButton="calcHasPoi"
        :isBottomSheetStyle="true"
        :mapTitle="viewOnMap ? viewOnMap.map_title : ''"
        :platform="platform"
        @viewOnMapBottom="$emit('viewOnMapBottom', calcPoiIdentifyId)"
        @clickImage="clickImageHandler(bsData.item, $event)"
      ></ItineraryDetailsMobile>
    </klk-bottom-sheet>
  </div>
</template>

<script lang="ts">
import { Vue, Component, Prop, Inject, Watch } from 'vue-property-decorator'
import cloneDeep from 'lodash/cloneDeep'
import { ItineraryDay } from '../../types'
import TypeComp from '../type-comp/index.vue'
import KlkCollapse from '@klook/klook-ui/lib/collapse'
import '@klook/klook-ui/lib/styles/components/collapse.scss'
Vue.use(KlkCollapse)
import KlkBottomSheet from '@klook/klook-ui/lib/bottom-sheet'
import '@klook/klook-ui/lib/styles/components/bottom-sheet.scss'
Vue.use(KlkBottomSheet)
import KlkTabs from '@klook/klook-ui/lib/tabs'
import '@klook/klook-ui/lib/styles/components/tabs.scss'
Vue.use(KlkTabs)
import { IconDownCircle, IconUpCircle } from '@klook/klook-icons'
import TitleComp from './groups/title.vue'
import CardBox from './groups/card-box.vue'
import ItineraryDetailsMobile from './details/mobile.vue'
import ItineraryDetailsDesktop from './details/desktop.vue'
import ItinerarySeasonalDetails from './seasonal-details/index.vue'
import { getItineraryMenusClass, isExpandSeeMoreKey, isExpandDetailsKey } from '../../utils'
import { handleIHTrack, getExt } from '../../utils/inHouseTrack'

const POI_TYPE = {
  NORMAL: 'NORMAL',
  SEASONAL: 'SEASONAL'
}

const TypeDefined = {
  AttractionType: 'attraction',
  PoiType: 'departure-return-accommodation',
  NormalType: 'meal-transport'
}
@Component({
  components: {
    ItinerarySeasonalDetails,
    ItineraryDetailsDesktop,
    ItineraryDetailsMobile,
    TypeComp,
    IconDownCircle,
    IconUpCircle,
    TitleComp,
    CardBox
  }
})
export default class Index extends Vue {
  @Prop() transitionendCallback!: Function
  @Prop() viewOnMap!: any
  @Prop({ default: (str: string) => str }) translateI18n!: Function
  @Prop({ default: 'mobile' }) platform!: string
  @Prop() list!: ItineraryDay[]
  @Inject("handleTitleClick") handleTitleClick!: Function;
  @Inject("customInhouseTrack") customInhouseTrack!: Function;

  POI_TYPE = POI_TYPE
  TypeDefined = TypeDefined

  activeList: string[] = []

  moreObj: any = {
    maxHeight: 700,
    status: ''
  }

  clickShowMore(item: any, status: string) {
    this.$set(item, this.isExpandSeeMoreKey, status)
  }

  updateDataHandler(item: any, data: any) {
    const { moreObj} = this
    if (!item[this.isExpandSeeMoreKey] && data?.height > moreObj.maxHeight) {
      this.$set(item, this.isExpandSeeMoreKey, 'more')
    }
  }

  getItineraryMenusClass = getItineraryMenusClass

  isExpandDetailsKey = isExpandDetailsKey
  isExpandSeeMoreKey = isExpandSeeMoreKey
  cacheList: any = []

  @Watch('list', { immediate: true })
  listWatch(arr: any[]) {
    this.cacheList = this.getInitList(arr)
  }

  clickImageHandler(item: any, data: any) {
    this.$emit('clickImage', {
      ...(data || {}),
      showImageViewerMapEntry: this.getCardType(item) === TypeDefined.AttractionType
    })
  }

  getInitList(list: any[]) {
    const arr = cloneDeep(list)
    arr.forEach((day: any) => {
      day?.groups?.forEach((group: any) => {
        this.$set(group, this.isExpandDetailsKey, this.platform === 'desktop' && this.getIsDetailsActive(group)) // desktop端，存在details展开折叠，则默认值为true
        this.$set(group, this.isExpandSeeMoreKey, '')
      })
    })
    return arr
  }

  bsObj = {
    delayTime: 0,
    transfer: true,
    title: '',
    visible: false,
    'show-close': true,
    'mask-closable': true,
    'can-pull-close': false
  }
  bsData: any = {
    item: null,
    cardItem: null,
    type: POI_TYPE.NORMAL
  }

  getBindTracker(tracker: any) {
    return handleIHTrack(tracker)
  }

  getDayBtnTrack(item: any) {
    const extra = {
      BtnType: item?.expand ? 'expand' : 'fold',
      Day: item.index + 1
    }
    const obj = this.getBindTracker({
      type: 'module',
      exposure: false,
      action: true,
      spm: 'MultidayBtn',
      query: {
        ext: getExt(extra)
      }
    })
    return obj
  }

  getGroupTrack(item: any, typeList: string[]) {
    const cardType = this.getCardType(item)
    const { track_info } = item || {}
    const { type, extra } = track_info || {}
    if (!type || !typeList.includes(cardType)) {
      return {}
    }
    const obj = this.getBindTracker({
      type: 'module',
      exposure: true,
      action: !(cardType === TypeDefined.AttractionType),
      spm: type,
      query: {
        ext: getExt(extra || {})
      }
    })
    return obj
  }

  getSeoElement(item: any) {
    const seoMap: any = {
      'meal': 'h4',
      'transport': 'h4'
    }
    const el = seoMap[item.group_key] || 'h3'
    return el
  }

  getCardType(item: any) {
    const typeMap: any = {
      'departure': TypeDefined.PoiType,
      'return': TypeDefined.PoiType,
      'accommodation': TypeDefined.PoiType,
      'meal': TypeDefined.NormalType,
      'transport': TypeDefined.NormalType,
      'attraction': TypeDefined.AttractionType
    }
    const cardType = typeMap[item.group_key] || ''
    if (!cardType) {
      console.error('cardType is undefined!', item.group_key)
    }
    return cardType
  }


  getIsDetailsActive(item: any) {
    if (!item?.details) {
      return false
    }
    let isb = this.getCardType(item) === TypeDefined.NormalType
    if (isb) {
      return true
    }
    if (this.platform === 'desktop') {
      if (this.getCardType(item) === TypeDefined.AttractionType) {
        return false
      }
      isb = item.details?.tabs?.length > 0 // desktop和mobile接口返回的数据结构不一样！！！
      // const isPoiType = this.getCardType(item) === TypeDefined.PoiType
      // if (isPoiType) {
      //   isb = !!item?.card_components?.find((o: any) => o.type === 'poi')?.data?.is_poi_tappable
      // } else {
      //   isb = false
      // }
    }
    return isb
  }

  get calcPoiIdentifyId() {
    const card = this.bsData.item?.card_components?.find((comp: any) => comp.type === 'poi')
    const id = card?.data?.poi_identify_id || ''
    return id
  }

  get calcHasPoi() {
    return this.bsData.item?.card_components?.some((comp: any) => comp.type === 'poi')
  }

  get calcDetailsTabs() {
    return this.bsData.item?.details?.tabs || []
  }

  created() {
    this.activeList = this.list?.map((_, idx) => '' + idx) || []
  }

  getIsExpand(item: any) {
    return item?.[this.isExpandDetailsKey]
  }

  clickItineraryDetailsHandler(item: any, cardItem: any) {
    if (this.platform === 'desktop') {
      this.$set(item, this.isExpandDetailsKey, !item?.[this.isExpandDetailsKey])
      return
    }
    this.bsObj.visible = true
    this.bsObj.title = item?.details?.title || ''
    this.bsData.type = POI_TYPE.NORMAL
    this.bsData.item = item
    this.bsData.cardItem = cardItem
    this.bsData.components = []
  }

  clickSeasonalDetailsHandler(item: any, seasonItem: any) {
    if (this.platform === 'desktop') {
      return
    }

    const { text, detail_components } = seasonItem.data
    this.bsObj.visible = true
    this.bsObj.title = text || ''
    this.bsData.cardItem = null
    this.bsData.type = POI_TYPE.SEASONAL
    this.bsData.item = {
      details: item,
      components: detail_components || []
    }
  }

  transitionendCallbackHandler(event: any) {
    this.transitionendCallback()
    event.target.removeEventListener('transitionend', this.transitionendCallbackHandler)
  }

  initTransitionendHandler() {
    if (typeof this.transitionendCallback === 'function') {
      const el = document.querySelector('.day-list .klk-collapse-item-content')
      el?.addEventListener && el.addEventListener('transitionend', this.transitionendCallbackHandler)
    }
  }

  mounted() {
    this.initTransitionendHandler()
  }
}
</script>

<style lang="scss" scoped>
.desktop .day-list {
  .day-list__group-item {
    &:not(:last-of-type) {
      padding-bottom: 36px;
    }
  }
  .day-list__content {
    position: relative;
  }
  .hack-step-style.spec-bg-content ::v-deep {
    .title-comp__left-icon{
      left: -52px;
    }
  }
}
.day-list {
  &.is-one-day {
    .klk-steps-reset-style {
      margin-top: 0;
    }
    .klk-collapse ::v-deep {
      .klk-collapse-item-header {
        display: none;
      }
    }
  }
  .day-list__content {
    padding-left: 36px;
  }
  .day-list__group-item {
    position: relative;
    &:not(:last-of-type) {
      padding-bottom: 24px;
      &::before {
        content: '';
        display: inline-block;
        width: 1px;
        height: 100%;
        background-color: $color-neutral-300;
        position: absolute;
        left: -24px;
        top: 0;
      }
    }
  }
  .spec-title-map-box {
    position: relative;
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
  }
}

.desktop .klk-collapse-reset-style {
  &.klk-collapse ::v-deep {
    .klk-collapse-item {
      padding-left: 0;
      padding-right: 0;
    }
  }
}

.klk-collapse-reset-style {
  .header-box {
    display: flex;
    align-items: center;
    cursor: pointer;
    color: $color-orange-500;
  }
  .header-box__title {
    @include font-heading-xxs;
  }
  .header-box__icon {
    margin-left: 10px;
  }
  &.klk-collapse ::v-deep {
    .klk-collapse-item {
      padding: 0 0 12px 0;
    }
    .klk-collapse-item-ctrl-btn {
      display: none;
    }
  }
}
.klk-steps-reset-style {
  margin-top: 20px;
  .step-icon {
    display: inline-block;
    width: 24px;
    height: 24px;
  }
  &.klk-steps.klk-steps-vertical ::v-deep {
    .klk-step-head-inner {
      margin-right: 12px;
    }
    .klk-step-content {
      margin: 0;
    }
  }
}

.itinerary-details-bs {
  &.klk-bottom-sheet ::v-deep {
    .klk-bottom-sheet-body {
      padding: 0 0 20px 0;
    }
  }

  .seasonal-details-comp {
    padding: 20px;
  }
}

.hack-step-style ::v-deep {
  .poi-comp {
    padding-right: 0;
  }
}
.hack-step-style.spec-bg-content ::v-deep {
  .title-comp__left-icon{
    left: -48px;
  }
  .card-swiper-wrap{
    &.imgs-comp {
      &.mobile{
        width: calc(100% + 20px);
      }
      .card-swiper {
        padding: 0 0 20px 0;
      }
    }
  }
}

</style>
