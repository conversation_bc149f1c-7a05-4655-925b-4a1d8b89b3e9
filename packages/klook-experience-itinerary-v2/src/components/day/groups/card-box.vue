<template>
  <div class="card-box" :class="[calcBgColor && 'spec-bg-content']" :style="calcStyle">
    <div class="card-box__content">
      <slot />
    </div>
    <div v-if="!calcHideCardSlot" class="card-box__sub-card-box">
      <slot name="sub-card-box-slot" />
    </div>
  </div>
</template>

<script lang="ts">
import { Vue, Component, Prop } from 'vue-property-decorator'

@Component
export default class Index extends Vue {
  @Prop({ default: 'mobile' }) platform!: string
  @Prop({ type: Number, default: 0 }) gap!: number
  @Prop({ type: Object, required: true }) data!: any
  @Prop() cardType!: 'attraction' | 'departure-return-accommodation' | 'meal-transport'

  get calcHideCardSlot() {
    return !this.data?.card_components?.length
  }

  get calcBgColor() {
    return this.data?.background_color || ''
  }

  get calcStyle() {
    const bgColor = '#fff'
    if (this.calcBgColor) {
      return {
        marginTop: `${this.gap}px`,
        background: this.platform === 'desktop' ? this.calcBgColor : `linear-gradient(90deg, ${this.calcBgColor} 45.76%, ${bgColor} 94.09%)`,
        borderRadius: '8px'
      }
    }
    return {
      marginTop: `${this.gap}px`,
      background: bgColor
    }
  }
}
</script>

<style lang="scss" scoped>
.spec-bg-content.card-box {
  .card-box__content {
    padding: 12px;
  }
  .card-box__sub-card-box {
    padding: 12px 0 12px 12px;
    background: rgba(255, 255, 255, 0.5);
  }
}
.desktop {
  .spec-bg-content.card-box {
    .card-box__content {
      padding: 16px;
    }
    .card-box__sub-card-box {
      padding: 4px 0 16px 16px;
    }
  }
}
</style>
