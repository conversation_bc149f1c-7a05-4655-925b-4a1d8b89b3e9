<template>
  <div
    v-bind="calcTrack"
    class="title-comp js-spm-poi-title"
    :class="[isTitleBold && 'title-comp-bold', isDetailsActive && 'curson-pointer']"
    @click="clickHandler"
  >
    <span v-if="leftIcon" class="title-comp__left-icon">
      <img class="step-icon" :src="leftIcon" />
    </span>
    <component :is="seoElement" :class="isPoiTitle && 'poi-title'" class="seo-element" @click="clickTitleHandler"><span v-for="(item, index) in calcTitles" :key="index" class="title-comp__text">{{ item }}</span></component>
    <template v-if="platform === 'desktop'">
      <IconChevronUp v-if="isDetailsActive" class="title-comp__icon" :class="isExpand ? 'is-unexpand' : 'is-expand'" theme="outline" size="24" />
    </template>
    <template v-else>
      <IconNext v-if="isDetailsActive" class="title-comp__icon" theme="outline" size="12" />
    </template>
  </div>
</template>

<script lang="ts">
import { Vue, Component, Prop } from 'vue-property-decorator'
import { IconNext, IconChevronUp } from '@klook/klook-icons'
@Component({
  components: {
    IconChevronUp,
    IconNext
  }
})
export default class Index extends Vue {
  @Prop({ default: 'h3' }) seoElement!: string
  @Prop() isPoiTitle!: false
  @Prop({ default: false }) isExpand!: boolean
  @Prop({ default: 'mobile' }) platform!: string
  @Prop() leftIcon!: string
  @Prop() titles!: string[]
  @Prop({ type: Boolean, default: false }) isTitleBold!: boolean
  @Prop({ type: Boolean, default: false }) isDetailsActive!: boolean
  get calcTitles() {
    return this.titles || []
  }

  get calcTrack() {
    const obj = {
      'data-spm-module': 'ItineraryMap',
      'data-spm-virtual-item': '__virtual'
    }
    return this.isPoiTitle ? obj : {}
  }

  clickTitleHandler() {
    this.isPoiTitle && this.$emit('clickTitle')
  }

  clickHandler() {
    this.isDetailsActive && this.$emit('clickItineraryDetails')
  }
}
</script>

<style lang="scss" scoped>
.desktop .title-comp {
  &__icon {
    margin: 0 0 0 4px;
    &.is-expand {
      transform: rotate(180deg);
    } 
  }
  .poi-title {
    text-decoration: underline;
    cursor: pointer;
  }
  .seo-element{
    @include font-heading-xs-v2;
  }
}
.title-comp {
  position: relative;
  display: flex;
  align-items: flex-start;
  color: $color-text-primary;
  .seo-element{
    @include font-body-s-regular-v2;
  }
  &.title-comp-bold {
    .seo-element{
      font-weight: 600; // 后端可控制加粗
    }
  }
  &.curson-pointer {
    cursor: pointer;
  }
  &.event-none {
    pointer-events: none;
  }
  &__text:not(:last-of-type)::after {
    content: '';
    display: inline-block;
    margin: 0 7px;
    width: 4px;
    height: 4px;
    border-radius: 50%;
    background-color: $color-text-primary;
    vertical-align: middle;
  }
  &__icon {
    margin: 3px 0 0 4px;
  }
  &__left-icon {
    position: absolute;
    left: -36px;
    top: -2px;
    display: inline-block;
    width: 24px;
    height: 24px;
    .step-icon {
      width: 100%;
      height: 100%;
    }
  }
}
</style>

