<template>
  <div class="score-stars" :class="[`score-stars-${platform}`, styleType]">
    <div class="score-stars-content">
      <span class="score-stars-box score-stars-light" :style="{ width: width }">
        <IconStar
          v-for="(i, index) in 5"
          :key="index"
          theme="filled"
          :size="size"
        />
      </span>

      <span class="score-stars-box score-stars-dark">
        <IconStar
          v-for="(i, index) in 5"
          :key="index"
          theme="filled"
          :size="size"
        />
      </span>
    </div>
    <span v-if="starShow && starAppraise && !hideAppraise" class="star-appraise">
      {{ starAppraise() }}
    </span>
  </div>
</template>

<script lang="ts">
import { Vue, Component, Prop, Inject } from 'vue-property-decorator'
import { IconStar } from '@klook/klook-icons'
import { starAppraise } from '../utils'

@Component({
  components: {
    IconStar
  }
})
export default class KlkStars extends Vue {
  @Prop({ default: 'mobile' }) platform!: string
  @Prop({ type: String, default: '' }) styleType!: '' | 'new-stars-type' | 'stars-v2-type'
  @Prop({
    default: 5
  })
  readonly score!: number

  @Prop({
    default: 14
  })
  readonly size!: number

  @Prop({
    default: 5
  })
  readonly total!: number

  @Prop({
    default: false
  })
  readonly starShow!: boolean

  @Prop({
    default: false
  })
  readonly hideAppraise!: boolean

  @Inject({ default: () => '' }) translateI18n!: Function

  get width() {
    return (this.score / this.total) * 100 + '%'
  }

  // 这个 80 有问题，但是我不敢动，怕影响其它地方
  get starAppraise() {
    return starAppraise(this.translateI18n.bind(this), this.score, 80)
  }
}
</script>

<style lang="scss" scoped>
.score-stars.stars-v2-type {
  &.score-stars-mobile {
    height: auto;
  }
  .score-stars-content {
    height: 100%;
    .i-icon {
      &:not(:last-of-type) {
        margin-right: 4px;
      }
    }
    .score-stars-box {
      display: inline-block;
      height: 100%;
    }
  }
}
.score-stars.new-stars-type {
  &.score-stars-mobile {
    height: auto;
  }
  .score-stars-content {
    height: 100%;
    .score-stars-star-icon {
      margin-right: 0;
    }
    .score-stars-box {
      display: inline-block;
      height: 100%;
    }
  }
}
.score-stars {
  &.score-stars-mobile {
    height: 16px;
    line-height: 16px;
    overflow: hidden;
  }
  .score-stars-content {
    display: inline-block;
    position: relative;

    .score-stars-star-icon {
      margin-right: 2px;
      color: $color-brand-secondary;

      &:last-child {
        margin-right: 0;
      }
    }

    .score-stars-light {
      position: absolute;
      overflow: hidden;
      white-space: nowrap;
      color: $color-caution;
    }
    .score-stars-dark {
      color: $color-bg-5;
    }
  }

  .star-appraise {
    position: relative;
    font-weight: $fontWeight-bold;
    top: -2px;
    margin-left: 8px;
    line-height: 1;
  }
}
</style>
