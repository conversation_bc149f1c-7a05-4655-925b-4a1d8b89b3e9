<template>
  <div v-if="calcMenus && calcMenus.length > 1" class="itinerary-menus">
    <CardSwiper class="itinerary-menus__list">
      <div
        v-for="(item, index) in calcMenus"
        :key="index"
        class="itinerary-menus__item"
        :class="{ 'itinerary-menus__item--active': index === selfActive }"
      >
        <span class="itinerary-menus__item-btn" :data-spm-module="`MultidaySwitch?ext=${JSON.stringify({ Daytype: index + 1 })}`" data-spm-virtual-item="__virtual" @click="clickHandler(item, index)">
          {{ item.text }}
        </span>
      </div>
    </CardSwiper>
  </div>
</template>

<script lang="ts">
import { Vue, Component, Prop, Watch } from 'vue-property-decorator'
import { ItineraryDay } from '../../types'
import KlkButton from '@klook/klook-ui/lib/button'
import '@klook/klook-ui/lib/styles/components/button.scss'
import CardSwiper from '../card-swiper.vue'
import { getItineraryMenusClass } from '../../utils'

@Component({
  components: {
    CardSwiper,
    KlkButton
  }
})
export default class Index extends Vue {
  @Prop({ default: 0 }) active!: number
  @Prop() list!: ItineraryDay[]

  @Watch('active', { immediate: true })
  activeWatch(val: number) {
    this.selfActive = val
  }

  selfActive = -1

  get calcMenus() {
    return this.list?.map((item: any, index: number) => {
      return {
        text: item.day_name,
        className: getItineraryMenusClass(index)
      }
    })
  }
  clickHandler(item: any, index: number) {
    this.selfActive = index
    this.$emit('click', item, index)
  }
  
  created() {
    this.$emit('updateMenus', this.calcMenus)
  }
}
</script>

<style lang="scss" scoped>
.itinerary-menus {
  z-index: 6;
  &__list {
    display: flex;
    align-items: center;
  }
  &__item {
    &:not(:last-of-type) {
      margin-right: 8px;
    }
    &--active {
      .itinerary-menus__item-btn {
        background-color: $color-orange-50;
        color: $color-brand-primary;
        border-color: $color-brand-primary;
      }
    }
  }
  &__item-btn {
    display: flex;
    align-items: center;
    flex-wrap: nowrap;
    padding: 0 12px;
    height: 28px;
    border: 1px solid $color-neutral-300;
    @include font-body-s-regular;
    color: $color-text-primary;
    border-radius: 28px;
    white-space: nowrap;
  }
}
</style>
