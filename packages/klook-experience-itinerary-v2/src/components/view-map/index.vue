<template>
  <component
    ref="viewMapRef"
    :is="componentName"
    v-bind="$attrs"
    v-on="$listeners"
  />
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator'
import PickUpPointsDesktop from './desktop/modal.vue'
import PickUpPointsMobile from './mobile/index.vue'

@Component({
  components: {
    PickUpPointsDesktop,
    PickUpPointsMobile
  }
})
export default class PickUpPoints extends Vue {
  @Prop() platform!: string

  get componentName() {
    return this.platform === 'mobile' ? PickUpPointsMobile : PickUpPointsDesktop
  }

  public viewOnMapHandler() {
    this.$refs.viewMapRef?.viewOnMapHandler?.()
  }
}
</script>
