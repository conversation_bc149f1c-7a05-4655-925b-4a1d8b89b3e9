<template>
  <div
    v-if="data"
    class="view-on-map"
    :class="[platform, { 'is-success': successStatus }]"
    :style="calcStyle"
    data-spm-module="ItineraryMap"
    data-spm-virtual-item="__virtual"
    @click="$emit('click')"
  >
    <div class="view-on-map__title-box">
      <span class="view-on-map__title">{{ data.map_title }}</span>
      <img v-if="data.map_icon" class="view-on-map__icon" :src="data.map_icon" alt="">
    </div>
  </div>
</template>

<script lang="ts">
import { Vue, Component, Prop } from 'vue-property-decorator'

interface ViewOnMapIf {
  map_url: string
  map_img: string
  map_title: string
  map_icon: string
}

@Component
export default class ViewOnMap extends Vue {
  @Prop() platform!: string
  @Prop({ type: Object, default: null }) data!: ViewOnMapIf
  @Prop({ type: <PERSON><PERSON><PERSON>, default: false }) successStatus!: boolean

  get calcSimpleType() {
    return this.platform === 'desktop'
  }

  get calcStyle() {
    if (this.calcSimpleType) {
      return
    }
    const { map_img = '' } = this.data || {}
    return {
      backgroundImage: `url(${map_img})`,
      backgroundSize: 'cover',
      backgroundPosition: 'center',
      backgroundRepeat: 'no-repeat',
    }
  }
}
</script>

<style lang="scss" scoped>
.desktop.view-on-map {
  width: auto;
  height: auto;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  @include font-body-m-semibold;
  padding: 0 0 0 0;
  .view-on-map {
    &__icon {
      width: 20px;
      height: 20px;
    }
  }
}
.view-on-map {
  width: 122px;
  height: 64px;
  padding: 0 0 0 2px;
  display: flex;
  align-items: center;
  box-sizing: border-box;
  @include font-caption-m-semibold;
  color: $color-text-disabled;
  &.is-success {
    color: $color-blue-500;
    cursor: pointer;
  }
  &.is-failed {
    color: $color-text-disabled;
  }
  &__title-box {
    display: flex;
    align-items: center;
  }
  &__title {
    text-decoration: underline;
  }
  &__icon {
    width: 16px;
    height: 16px;
    margin-left: 4px;
  }
}
</style>
