<template>
  <div class="view-map-modal-wrapper">
    <ViewOnMap v-if="showEntry && viewOnMap" :data="viewOnMap" :successStatus="calcStatus" :platform="'desktop'" @click="viewOnMapHandler" />
    <klk-modal
      v-bind="modalObj"
      :title="translateI18n('161195')"
      :open.sync="modalObj.visible"
      className="view-map-modal"
    >
      <ViewMapDesktop :i18n="calcInfo.i18n" :mapInfo="mapInfo" />
    </klk-modal>
    <ViewMapDesktop
      :i18n="calcInfo.i18n"
      :mapInfo="mapInfo"
      :style="{ display: 'none' }"
      class="hack-desktop-init-map"
    />
  </div>
</template>

<script lang="ts">
import { Vue, Component, Prop, Inject } from 'vue-property-decorator'
import ViewOnMap from '../view-on-map.vue'
import ViewMapDesktop from './index.vue'

@Component({
  components: {
    ViewMapDesktop,
    ViewOnMap
  }
})
export default class Index extends Vue {
  @Prop({ type: Object, default: null }) viewOnMap!: any
  @Prop({ type: Boolean, default: true }) showEntry!: boolean
  @Prop({ type: Object, default: null }) mapApiData!: any

  @Inject('translateI18n')
  translateI18n!: (...args: any[]) => string

  modalObj = {
    visible: false,
    'show-default-footer': false,
    closable: true,
    scrollable: true,
    size: 'large'
  }

  // $$mock - del
  get calcInfo() {
    return { i18n: this.mapApiData?.result?.departure_maps_i18ns || {} }
  }

  get mapInfo() {
    const { poi_card_info, area_config } = this.mapApiData?.result ?? {}

    return {
      map: poi_card_info || [],
      areaConfig: area_config || {}
    }
  }

  get calcStatus() {
    return !!this.mapApiData?.success
  }

  public viewOnMapHandler() {
    this.modalObj.visible = true
  }
}
</script>

<style lang="scss" scoped>
.view-map-modal {
}
</style>
