<template>
  <div
    class="pick-up-desktop"
    :data-spm-module="spmModule"
  >
    <SearchKeywords
      ref="keywordsSearchRef"
      :selected.sync="selected"
      :is-scope="true"
      :map-data="locationList"
      style="margin-bottom: 16px;"
      @change="handleSearchChange"
      @clear="handleClear"
    />
    <!-- 设置最小高度，防止全屏的时候页面抖动 -->
    <div v-if="!isSimplified" class="pickup-wrapper">
      <PickUpMap
        ref="mapRef"
        :selected.sync="selected"
        :location-list="locationList"
        :map-info="mapInfo"
        :extra-point="extraPoint"
        :map-type="currentMapType || 'google'"
        @fullscreen="handleFullscreen"
      />
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop, Inject, Watch } from 'vue-property-decorator'
import SearchKeywords from './search-keywords.vue'
import PickUpMap from './map.vue'
import type { flatLocationItem, searchResultTabOption, MapInfo2Prop } from '../types/index'
import { POI_USING_TYPE } from '../../../utils'

@Component({
  components: {
    SearchKeywords,
    PickUpMap
  }
})
export default class PickUpDesktop extends Vue {
  @Prop({ type: Object, default: () => ({}) }) mapInfo!: MapInfo2Prop;
  @Prop({ type: Object, default: () => ({}) }) i18n!: any
  // @Prop({ type: Array, default: () => [] }) departureMaps!: any[]
  @Inject({ default: false }) isSimplified!: boolean
  @Inject({ default: () => null }) customInhouseTrack!: Function;
  @Inject("currentMapType") currentMapType!: string;

  selected: flatLocationItem | searchResultTabOption | null = null
  extraPoint: flatLocationItem | searchResultTabOption | null = null

  showModal:boolean = false

  handleFullscreen() {
    this.showModal = true
  }

  get spmModule() {
    const types = this.locationList.reduce((acc: string[], curr) => {
      const { data, is_customized_area } = curr
      const pick_up_type = data?.using_type === POI_USING_TYPE.location ? 1 : 2
      if (pick_up_type === 1) {
        acc.push('pick_up_fixed')
      } else {
        acc.push(is_customized_area ? 'pick_up_customized' : 'pick_up_scope')
      }
      return acc
    }, [])
    const typeStr = Array.from(new Set(types)).join(',')
    return `PickMeetUpInformation?ext=${JSON.stringify({ type: typeStr })}`
  }

  // 范围接载点
  get locationList() {
    return this.mapInfo?.map || []
  }

  @Watch('selected')
  selectedChange(newVal: any, oldVal: any) {
    if (newVal && oldVal && newVal !== oldVal) {
      this.customInhouseTrack('custom', 'body', { spm: 'SwitchPickUpPoint' })
    }
  }

  handleSearchChange(val: flatLocationItem | searchResultTabOption | null) {
    this.extraPoint = val
  }

  handleClear() {
    // 如果一样的话，就取消选中
    if (this.selected && this.extraPoint && this.selected.location === this.extraPoint.location) {
      this.selected = null
    }
    this.extraPoint = null
  }
}
</script>

<style lang="scss" scoped>
.pick-up-desktop {
  height: 480px;
}
.pickup-wrapper {
  margin-bottom: 16px;
  min-height: 300px;
}
</style>
