<template>
  <div class="location-info">
    <h3 class="location-info-title">{{ info.title }}</h3>
    <Tag
      v-if="tags.length > 0"
      :list="tags"
      :line="line"
      class="location-info-tags"
    />
    <div class="location-info-address" @click="handleCopy">
      <p class="location-info-address-text">{{ info.sub_title }}</p>
      <div
        v-if="showCopyIcon"
        class="location-info-address-copy-wrap"
      >
        <CopyIconSvg class="copy-icon"/>
      </div>
    </div>
    <NearestComp :data="nearestData" />
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop, Inject } from 'vue-property-decorator'
import Tag from "../../tag.vue";
import CopyIconSvg from '../../../imgs/icon_other_copy_s.svg'
import Toast from '@klook/klook-ui/lib/toast'
import { copyToClipboard } from '../../../utils'
import NearestComp from '../components/nearest.vue'

@Component({
  components: {
    Tag,
    Toast,
    CopyIconSvg,
    NearestComp
  }
})
export default class PickUpInfoMapCard extends Vue {
  @Prop({ type: Object, default: () => {} }) info!: any;
  @Prop({ type: Number, default: 1 }) line!: number;
  @Prop({ type: Boolean, default: false }) showCopyIcon!: boolean;
  @Inject({ default: () => null }) translateI18n!: Function

  get tags() {
    return this.info?.detail_data?.tag ?? []
  }

  get nearestData() {
    return this.info?.detail_data?.nearest
  }

  async handleCopy() {
    if (!this.showCopyIcon) {
      return
    }

    const text = this.info?.sub_title ?? ''
    const result = await copyToClipboard(text)

    if (result) {
      Toast(this.translateI18n('copy_success'))
    }
  }
}

</script>

<style lang="scss" scoped>
@mixin text-ellipsis($line-clamp: 2) {
  display: -webkit-box;
  -webkit-line-clamp: $line-clamp;
  -webkit-box-orient: vertical;
  text-overflow: ellipsis;
  overflow: hidden;
}

.location-info {
  &-title {
    @include text-ellipsis(2);
    font-size: $fontSize-body-m;
    line-height: 150%;
    font-weight: $fontWeight-regular;
  }

  &-tags {
    margin-top: 4px;
  }

  &-address {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 4px;

    &-text {
      @include text-ellipsis();
      margin-top: 4px;
      color:$color-neutral-700;
      font-size: $fontSize-body-s;
      line-height: 150%;
    }

    &-copy-wrap {
      flex: none;
      align-self: flex-end;
      width: 32px;
      height: 19px;
      display: flex;
      justify-content: flex-end;
      background: linear-gradient(270deg, #FFFFFF 0%,#FFFFFF 50%, rgba(255, 255, 255, 0) 100%);

      .copy-icon {
        width: 16px;
        height: 16px;
      }
    }
  }

  &.card-info {
    .location-info-title {
      font-weight: $fontWeight-semibold;
    }

    .location-info-address {
      &-text {
        font-size: $fontSize-caption-m;
      }
    }
  }
}
</style>

