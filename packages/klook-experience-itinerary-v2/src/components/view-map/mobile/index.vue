<template>
  <div>
    <ViewOnMap v-if="showEntry && viewOnMap" :data="viewOnMap" :successStatus="calcStatus" @click="$emit('viewOnMapEntry')" />
    <div
      class="pick-up-mobile"
      :data-spm-module="spmModule"
    >
      <FullScreenModal
        :visible.sync="visible"
        class="pick-up-mobile-full-screen-modal"
      >
        <Map
          v-if="createdMap"
          :map-info="mapInfo"
          :map-type="currentMapType || 'google'"
          :itinerary-pois="calcItineraryPois"
          :current-poi="currentPoi"
          :pickup-info="calcInfo"
          @close="visible = false"
        />
      </FullScreenModal>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch, Inject } from 'vue-property-decorator'
import Map from './map.vue'
import FullScreenModal from './full-screen-modal.vue'
import { isServer } from '../../../utils'
import ViewOnMap from '../view-on-map.vue'

@Component({
  components: {
    Map,
    FullScreenModal,
    ViewOnMap
  }
})
export default class  PickUpMobile extends Vue {
  @Prop({ type: Object, default: null }) viewOnMap!: any
  @Inject("customInhouseTrack") customInhouseTrack!: Function;
  @Inject("currentMapType") currentMapType!: string;
  @Prop({ type: Boolean, default: true }) showEntry!: boolean
  @Prop({ type: Object, default: null }) currentPoi!: any
  @Prop({ type: Object, default: null }) mapApiData!: any

  createdMap: boolean = false
  visible: boolean = false

  // $$mock - del
  get calcInfo() {
    return { i18n: this.mapApiData?.result?.departure_maps_i18ns || {} }
  }

  get calcItineraryPois() {
    return this.mapApiData?.result?.tab_list || []
  }

  get calcStatus() {
    return !!this.mapApiData?.success
  }

  public viewOnMapHandler() {
    this.handleShowMap()
  }

  get mapInfo() {
    const { poi_card_info, area_config } = this.mapApiData?.result || {}

    return {
      map: poi_card_info || [],
      areaConfig: area_config || {}
    }
  }

  get spmModule() {
    const types = (this.mapInfo?.map || []).reduce((acc: string[], curr) => {
      const { pick_up_type, is_customized_area } = curr
      if (pick_up_type === 1) {
        acc.push('pick_up_fixed')
      } else {
        acc.push(is_customized_area ? 'pick_up_customized' : 'pick_up_scope')
      }
      return acc
    }, [])
    const typeStr = Array.from(new Set(types)).join(',')
    return `PickMeetUpInformation?ext=${JSON.stringify({ type: typeStr })}`
  }

  @Watch('currentPoi', { deep: true, immediate: true })
  currentPoiChange(val: any) {
    if (val) {
      this.handleShowMap()
    }
  }

  @Watch('visible')
  onVisibleChange(val: boolean) {
    if (val) {
      !isServer && this.$nextTick(() => {
        const target = document.querySelector('.pick-up-mobile-full-screen-modal') as HTMLElement
        this.customInhouseTrack('pageview', target, { force: true })
      })
    } else {
      this.$emit('close')
    }
  }

  handleShowMap() {
    this.visible = true
    this.createdMap = true
  }
}
</script>
