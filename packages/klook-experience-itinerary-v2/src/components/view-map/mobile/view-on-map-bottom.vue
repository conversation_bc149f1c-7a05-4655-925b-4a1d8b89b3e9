<template>
  <div v-if="mapTitle" class="view-map-bottom" @click="$emit('click')">
    <div class="view-map-bottom__title">
      <IconMap class="view-map-bottom__title-icon" theme="outline" size="16" />
      <span class="view-map-bottom__title-text">{{ mapTitle }}</span>
    </div>
  </div>
</template>

<script lang="ts">
import { Vue, Component, Prop } from 'vue-property-decorator'
import { IconMap } from '@klook/klook-icons'

@Component({
  components: {
    IconMap
  }
})
export default class Index extends Vue {
  @Prop() mapTitle!: string
}
</script>

<style lang="scss" scoped>
.view-map-bottom {
  position: absolute;
  z-index: 18;
  padding: 8px 16px;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  background-color: rgba(0, 0, 0, 0.6);
  @include font-body-s-bold;
  color: white;
  border-radius: 38px;
  .view-map-bottom__title {
    display: flex;
    align-items: center;
  }
  .view-map-bottom__title-icon {
    margin-right: 8px;
  }
}
</style>

