<template>
  <klk-map
    ref="mapRef"
    v-bind="mapConfig"
    @click.native="handleMapClick"
    @bounds-change="handleMapMove"
  >
    <klk-map-geo-fence
      v-for="(conf, index) in geoFenceConf"
      :key="'geo' + index"
      :ref="'geoFenceRef_' + index"
      :geo-fence-conf="conf"
      :isWgs84="true"
    />
    <klk-map-marker
      v-for="(marker, index) in allMarkers"
      ref="marker"
      :key="'marker' + index"
      :center="marker.center"
      :options="marker.options"
      :class="{
        'marker-active': currentMarkers.includes(index),
        'is-area': isArea(marker)
      }"
      :z-index="isArea(marker) ? 200 : currentMarkers.includes(index) ? 202 : 100"
      v-bind="getBindTracker(marker.tracker)"
    >
      <template v-if="!isArea(marker)">
        <!-- 显示文案的气泡 -->
        <div
          v-if="showMarkerTooltip === index && (marker.poptip || marker.tips_text)"
          class="marker-tooltip"
          @click.stop
        >
          <div class="marker-tooltip-content">
            <div v-if="marker.tips_text" class="marker-tips">
              {{ marker.tips_text }}
            </div>
            <div v-if="marker.poptip" class="marker-address">
              {{ marker.poptip }}
            </div>
          </div>
          <div class="marker-tooltip-arrow"></div>
        </div>

        <img
          v-show="!currentMarkers.includes(index)"
          class="icon-img"
          :src="marker.groupIcon.unselect"
          @click.stop="markerClick(index)"
        />
        <div v-show="currentMarkers.includes(index)" class="icon-img-wrap">
          <div
            v-if="marker.isTemporary && marker.tips_text"
            class="pick-up-map-area-marker-wrap"
          >
            <div
              class="pick-up-map-area-marker"
              :style="marker.style"
            >
              {{ marker.tips_text }}
            </div>
            <div class="marker-after" :style="marker.style"></div>
          </div>
          <img
            class="icon-img"
            :class="{ active: currentMarkers.includes(index) }"
            :src="marker.groupIcon.select"
            @click.stop="handleMarkerIconClick(index)"
          />
        </div>
      </template>
      <div
        class="pick-up-map-area-marker-wrap"
        v-else-if="marker.poptip"
      >
        <div
          class="pick-up-map-area-marker"
          :style="marker.style"
        >
          {{ marker.poptip }}
        </div>
        <div class="marker-after" :style="marker.style"></div>
      </div>
      <div v-else></div>
    </klk-map-marker>

    <div v-if="showSelectedLocation" class="map-card-box">
      <MapCard :data="currentSelectedLocation" />
    </div>

    <div class="top-operator-box">
      <KlkMapCircleButton
        v-show="!showSelectedLocation && !visible"
        class="close-button"
        data-spm-module="CloseMap?trg=manual"
        data-spm-virtual-item="__virtual"
        @click.stop.native="handleClose"
      >
        <klk-icon type="icon_navigation_close_m" size="20"></klk-icon>
      </KlkMapCircleButton>
      <KlkMapCircleButton
        v-show="showSelectedLocation"
        @click.stop.native="handleBack"
      >
        <klk-icon type="icon_navigation_chevron_left_xs" size="18"></klk-icon>
      </KlkMapCircleButton>
    </div>

    <div v-show="!visible" class="map-search-box" :style="searchIconStyle">
      <div v-show="showSearchTips" class="map-search-tips">
        <IconLightBulbOutlined class="map-search-light-bulb" />
        <span>$$t-Search for your location here</span>

        <klk-icon class="map-search-close" type="icon_navigation_close" :size="16" @click.stop="hideSearchTips"></klk-icon>
      </div>
      <transition name="fade">
        <IconSearchOutlined class="map-search-icon" @click.stop.native="showModal" />
      </transition>
    </div>

    <div
      v-if="showRecenterButton"
      class="recenter-button"
      @click="handleRecenter"
    >Re-center</div>

    <klk-bottom-sheet
      ref="bottomSheet"
      class="search-bottom-sheet"
      :visible.sync="visible"
      v-show="!showSelectedLocation"
      :mask-closable="false"
      @close="handleModalClose"
    >
      <KeyWordsSearch
        ref="keywordsSearchRef"
        :selected-item="currentSelectedLocation"
        :is-scope="isScope"
        :mapData="locationList"
        @closeFullScreenModal="handleModalClose"
        @selected="handleSelected"
        @focusModeChanged="handleFocusModeChanged"
      />
    </klk-bottom-sheet>
   <!-- <div
    v-show="!showSelectedLocation"
    :class="[visible && 'active', 'search-full-srceen-modal']"
    @click.stop
  >
    <KeyWordsSearch
      ref="keywordsSearchRef"
      :selected-item="currentSelectedLocation"
      :is-scope="isScope"
      :mapData="locationList"
      @closeFullScreenModal="handleModalClose"
      @selected="handleSelected"
    />
   </div> -->

  <KlkMapUserLocation
    v-show="!showSelectedLocation"
    v-bind="getBindTracker({
      type: 'module',
      spm: 'ItineraryMapFindMyPositionButton',
      exposure: false
    })"
    class="user-location"
    :style="locationBottom"
  />

  <div v-show="!showSelectedLocation" class="klk-scroll-snap-map" @touchmove.stop>
    <klk-scroll-snap ref="scrollSnap" @change="setMarkerIndex">
      <SnapCard
        v-for="(item, index) in allCards"
        :key="index"
        :style="cardStyle"
        :data="item"
      />
    </klk-scroll-snap>
  </div>
  </klk-map>
</template>

<script lang="ts">
import { Component, Prop, Inject, Ref } from "vue-property-decorator";
import {
  KlkMap,
  KlkMapMarker,
  KlkMapCircleButton,
  KlkMapUserLocation,
  MapTypes,
  mapUtils,
  KlkMapGeoFence
} from "@klook/map";
import "@klook/map/dist/esm/index.css";

import KlkIcon from "@klook/klook-ui/lib/icon";
import "@klook/klook-ui/lib/styles/components/icon.scss";

import KlkButton from '@klook/klook-ui/lib/button'
import '@klook/klook-ui/lib/styles/components/button.scss'

import MapCard from "./map-card.vue";

import SearchGlassIcon from "../../../imgs/search-glass-icon.svg";
import SearchClearIcon from "../../../imgs/search-clear-icon.svg";
import KeyWordsSearch from "./keywords-search.vue"
import type { KeywordsSearchItem, searchResultTabOption } from '../types/index'
import MapBase from '../base/map'

import KlkScrollSnap from '@klook/klook-scroll-snap'
import '@klook/klook-scroll-snap/dist/esm/index.css'
import IconLightBulbOutlined from '../../../imgs/icon-light-bulb-outlined.svg'
import IconSearchOutlined from '../../../imgs/search_outlined.svg'

import SnapCard from '../../itinerary-map/components/map-card/index.vue'
import { getExt } from "../../../utils/inHouseTrack";
import { POI_USING_TYPE } from '../../../utils'

@Component({
  components: {
    KlkMap,
    KlkMapMarker,
    KlkMapCircleButton,
    KlkMapUserLocation,
    MapCard,
    SearchGlassIcon,
    SearchClearIcon,
    KeyWordsSearch,
    KlkIcon,
    KlkMapGeoFence,
    KlkButton,
    KlkScrollSnap,
    SnapCard,
    IconLightBulbOutlined,
    IconSearchOutlined
  },
})
export default class PickUpPointMap extends MapBase {
  @Inject("customInhouseTrack") customInhouseTrack!: Function;
  @Inject("language2provide") language!: string;
  @Ref() scrollSnap!: any
  @Ref() keywordsSearchRef!: any
  @Prop() currentPoi!: any

  visible: boolean = false;
  currentSelectedLocation: any = null
  searchSelectedLocation: any = null // 单独记录从搜索那边选择过来的数据
  currentMarkers: number[] = []; // marker index array
  geoFenceConf: any = []
  originalGeomList: any[] = [] // 存储原始的 geom 数据
  markerList: any[] = []
  poptipVisible = true
  showSearchTips: boolean = false
  showMarkerTooltip: number = -1 // 显示文案的 marker 索引，-1 表示不显示

  get locationBottom() {
    const bottom = this.scrollSnapHeight + 16
    return {
      bottom: `${bottom}px`
    }
  }

  get searchIconStyle() {
    const bottom = this.scrollSnapHeight + 16 + 40 + 12 // 40 location card height 12 padding
    return {
      bottom: `${bottom}px`
    }
  }

  handleBack() {
    this.currentSelectedLocation = null
  }

  handleModalClose() {
    this.visible = false
    this.currentSelectedLocation = null
    this.searchSelectedLocation = null
    this.currentMarkers = []
    this.handleClear()
  }

  get showSelectedLocation() {
    // return !!this.currentSelectedLocation
    return false
  }

  get allMarkers() {
    return this.markerList
  }

  get allCards() {
    return (this.mapInfo.map || []).map((item) => {
      return {
        ...item,
        card_id: item.attr_id
      }
    })
  }

  // 点和范围混合了，好像也没用了
  get isScope() {
    return [POI_USING_TYPE.area, POI_USING_TYPE.location].every((type: string) => {
      return !!this.mapInfo.map.find((item: any) => item.data?.using_type === type)
    })
  }

  get mapConfig() {
    let center = ''
    const locationList = this.locationList || []

    if (locationList?.length) {
      const { using_type, map, area_id_list } = locationList[0].data
      if (using_type === POI_USING_TYPE.area) {
        const area_id = area_id_list[0]
        const curr = this.mapInfo.areaConfig.area_list.find((item: any) => item.area_id === area_id)
        center = curr?.area_center_points?.[0] || ''
      } else {
        // $$mock
        center = map.location || '39.945166,116.304159'
      }
    }

    return {
      language: this.language,
      type: this.mapType,
      // 本地调试地图需要打开该token, 开发环境记得注释掉
      // $$mock
      googleConf: {
        token: "AIzaSyByoaOJMATcSHo6iZ-cofp9vlHU8t64ukw"
      },
      height: "100%",
      interactive: 'greedy',
      zoom: 16,
      center: mapUtils.formatLatLng(center),
      emitBoundsChange: {
        debounce: {
          delay: 300,
          immediate: false
        }
      }
    }
  }

  get fencePointList() {
    const list = this.geoFenceConf.reduce((accu: any, curr: any) => {
      (curr.polygonList || []).forEach((list: any[]) => {
        if (Array.isArray(list)) {
          if (Array.isArray(list[0])) {
            accu.push(...list.map((item: any) => mapUtils.formatLatLng(item.reverse().join(','))))
          } else if (list.length === 2) {
            accu.push(mapUtils.formatLatLng(list.reverse().join(',')))
          }
        }
      })
      return accu
    }, [])

    return Object.freeze(list)
  }

  scrollSnapHeight: number = 0

  get cardStyle() {
    const height = this.scrollSnapHeight - 16
    // const itineraryPois = this.itineraryPois
    const width = [].length > 1 ? {} : {
      width: 'calc(100% - 40px)'
    }
    return {
      height: `${height}px`,
      ...width
    }
  }

  calcHeight() {
    const scrollSnap = this.scrollSnap as any
    const elem = scrollSnap.$el
    this.scrollSnapHeight = elem.offsetHeight
  }

  stopChange = false

  setMarkerIndex(index: number) {
    const data = this.allCards[index]

    if (this.stopChange || !data) {
      this.stopChange = false
      return
    }


    const result = this.allMarkers.reduce((acc: any, curr: any, index: number) => {
      if (curr.attr_id !== data.attr_id) {
        return acc
      }

      return [...acc, index]
    }, [])
    this.currentMarkers = result

    // 更新地理围栏的显示属性（根据当前卡片的 pick_up_type）
    this.updateGeoFenceForCurrentCard(data)

    // 当有多个 markers 时，使用 fitMapBounds 来显示所有相关的 markers
    if (result.length > 1) {
      const markerCenters = result.map((markerIndex: number) => this.allMarkers[markerIndex].center)
      this.fitMapBoundsForMarkers(markerCenters)
    } else if (result.length === 1) {
      this.flyTo(result[0])
    }

    const marker = this.allMarkers[result[0]]
    this.currentSelectedLocation = marker?.data ?? null
  }

  handleClose() {
    this.visible = false
    this.$emit('close')
  }

  async getGeoFenceConf(currentCard?: any) {
    if (!currentCard) {
      currentCard = this.currentSelectedLocation || this.allCards[0]
    }

    const areaIdList = this.getValidAreaId(this.locationList || [])
    if (areaIdList.length) {
      const res = await this.getGeomByIdList({ area_id_list: areaIdList.join(',') })
      this.originalGeomList = res?.result?.geom_list || []
      this.geoFenceConf =  this.initFenceData(this.originalGeomList, this.mapInfo.areaConfig, currentCard)
    }
  }

  updateGeoFenceForCurrentCard(currentCard?: any) {
    // 当切换卡片时，更新地理围栏的显示属性
    if (this.originalGeomList.length) {
      // 使用原始的 geom 数据重新生成地理围栏配置
      this.geoFenceConf = this.initFenceData(this.originalGeomList, this.mapInfo.areaConfig, currentCard)
    }
  }

  async initMarkerAndFence() {
    await this.getGeoFenceConf()
    this.markerList = this.flatLocationList.map((item: any) => this.formatMarker(item))
  }

  handleCurrentPoiFlyto() {
    const val = this.currentPoi
    const id = val?.attr_id ?? 0
    if (!val || !id) {
      return
    }
    const allMarkers = this.allMarkers || []
    const currentIndex = allMarkers.findIndex((item: any) => item.id === id)

    if (currentIndex >= 0) {
      this.$nextTick(() => {
        this.markerClick(currentIndex, false)
      })
    }
  }

  initMap() {
    const map = this.$refs.mapRef as any;
    map?.$getMapTool && map.$getMapTool.then(async (map: MapTypes.MapTool) => {
      this.map = map;

      // 隐藏默认点marker，其他地图元素也可以用这个方法
      if (typeof this.map?.map?.setOptions === "function") {
        const noPoi = [
          {
            featureType: "poi",
            elementType: "labels.icon",
            stylers: [{ visibility: "off" }],
          },
        ];
        this.map.map.setOptions({ styles: noPoi });
      }

      await this.fitMapBounds()
      this.handleCurrentPoiFlyto()
    });
  }

  async fitMapBounds(center?: any) {
    if (this.map) {
      const points = center ? [center] : [...this.fencePointList, ...this.allMarkers.map((item: any) => item.center)]
      if (points.length) {
        const box = await mapUtils.turfBbox(points, this.mapType as MapTypes.Type)
        const bounds = this.map.createBounds(box.sw, box.ne, { formatLngLat: false })
        this.map.fitBounds(bounds, {
          padding: {
            top: this.allMarkers.length ? 80 : 20,
            left: 20,
            bottom: 160,
            right: 20,
          },
        })
      }
    }
  }

  async fitMapBoundsForMarkers(markerCenters: any[]) {
    if (this.map && markerCenters.length > 0) {
      const box = await mapUtils.turfBbox(markerCenters, this.mapType as MapTypes.Type)
      const bounds = this.map.createBounds(box.sw, box.ne, { formatLngLat: false })
      this.map.fitBounds(bounds, {
        padding: {
          top: 80,
          left: 20,
          bottom: 160,
          right: 20,
        },
      })
    }
  }

  formatMarker(data: any, isTemporary = false) {
    return this.initMarker(data, isTemporary, {
      tracker: {
        type: 'module',
        spm: 'ViewPickUpPoint',
        exposure: true,
        query: {
          ext: getExt({
            Type: isTemporary ? 'customed' : 'fixed'
          }),
        },
      }
    })
  }

  markerClick(index: number, changeStop = true) {
    const data = this.allMarkers[index]
    const current = this.allCards.findIndex((item: any) => item.card_id === data.id)
    const currentCard = this.allCards[current]

    // 找到同一个卡片对应的所有 markers
    const result = this.allMarkers.reduce((acc: any, curr: any, markerIndex: number) => {
      if (curr.attr_id !== data.attr_id) {
        return acc
      }
      return [...acc, markerIndex]
    }, [])

    this.currentMarkers = result;
    this.currentSelectedLocation = data?.data ?? null

    // 更新地理围栏的显示属性（根据当前卡片的 pick_up_type）
    this.updateGeoFenceForCurrentCard(currentCard)

    // 当有多个 markers 时，使用 fitMapBounds 来显示所有相关的 markers
    if (result.length > 1) {
      const markerCenters = result.map((markerIndex: number) => this.allMarkers[markerIndex].center)
      this.fitMapBoundsForMarkers(markerCenters)
    } else if (result.length === 1) {
      this.flyTo(index);
    }

    this.stopChange = changeStop
    this.visible = false
    const scrollSnap = this.scrollSnap as any
    scrollSnap && scrollSnap!.slideTo(current)
  }

  flyTo(index: number) {
    const marker = this.allMarkers[index];
    if (!marker || !this.map) {
      return;
    }
    const latLng = marker.center;
    this.map!.flyTo(latLng)
  }

  handleClear() {
    this.markerList = this.markerList.filter(marker => !marker.isTemporary)

    this.$nextTick(() => {
      this.fitMapBounds()
    })
  }

  handleMapClick(event: Event) {
    // 检查点击事件是否来自搜索弹窗内部
    const bottomSheet = this.$el.querySelector('.search-bottom-sheet')
    if (bottomSheet && event.target && bottomSheet.contains(event.target as Node)) {
      return
    }

    const fitMap = !!this.searchSelectedLocation
    this.visible = false

    if (fitMap) {
      this.currentSelectedLocation = null
      this.searchSelectedLocation = null
      this.currentMarkers = []
      this.handleClear()
    }
  }

  addMarker(data: any) {
    this.markerList = this.markerList.filter(marker => !marker.isTemporary)
    let index = this.markerList.findIndex((item: any) => item.location === data.location)

    if (index === -1) {
      const target = this.formatMarker(data, true)
      this.markerList.push(target)
      index = this.markerList.length - 1

      const nearestData = (data as searchResultTabOption)?.nearest
      if (nearestData && nearestData.location && nearestData.icon) {
        const nearestMarker = this.formatNearestMarker(nearestData)
        this.markerList.push(nearestMarker)

        this.cancelOutMoveAndSaveState = true
        this.fitMapBoundsForTwoPoints(data.location, nearestData.location)
        this.currentMarkers = [index, index + 1]
        return
      }
    }

    this.fitMapBounds(this.markerList[index]?.center ?? null)
    this.currentMarkers = [index]
    this.flyTo(index)
  }

  fitMapBoundsForTwoPoints(location1: string, location2: string) {
    if (this.map) {
      const points = [
        mapUtils.formatLatLng(location1),
        mapUtils.formatLatLng(location2)
      ]
      this.fitMapBoundsForCoordinates(points)
    }
  }

  showModal() {
    this.hideSearchTips()
    this.visible = true
    // 自动聚焦，防止页面抖动
    setTimeout(() => {
      this.keywordsSearchRef?.focusInput?.()
    }, 400)
  }

  hideSearchTips() {
    this.showSearchTips = false
    // 保存到 sessionStorage，确保在当前会话中不再显示
    sessionStorage.setItem('map-search-tips-hidden', 'true')
  }

  checkSearchTipsVisibility() {
    // 检查 sessionStorage 中是否已经隐藏过提示
    const isHidden = sessionStorage.getItem('map-search-tips-hidden')
    this.showSearchTips = !isHidden
  }

  handleSelected(item: KeywordsSearchItem) {
    this.currentSelectedLocation = item
    this.searchSelectedLocation = item
    this.addMarker(item)
  }

  handleFocusModeChanged(isFocusMode: boolean) {
    // 当聚焦态改变时，可以在这里处理相关逻辑
    // 比如调整 bottom-sheet 的高度等
    if (isFocusMode) {
      // 进入聚焦态
      this.$nextTick(() => {
        // 可以在这里添加额外的处理逻辑
      })
    } else {
      // 退出聚焦态
      this.$nextTick(() => {
        // 可以在这里添加额外的处理逻辑
      })
    }
  }

  handleMarkerIconClick(index: number) {
    // 点击 marker 图标显示/隐藏文案
    if (this.showMarkerTooltip === index) {
      this.showMarkerTooltip = -1 // 隐藏
    } else {
      this.showMarkerTooltip = index // 显示
    }

    // 3秒后自动隐藏
    setTimeout(() => {
      if (this.showMarkerTooltip === index) {
        this.showMarkerTooltip = -1
      }
    }, 3000)
  }

  handleMapMove(event: any) {
    if (this.cancelOutMoveAndSaveState) {
      this.cancelOutMoveAndSaveState = false
      this.saveCurrentMapState()
      return
    }

    if (this.savedMapState && event.map) {
      let currentCenter = event.map?.map?.getCenter()
      if (currentCenter) {
        currentCenter = {
          lat: currentCenter.lat(),
          lng: currentCenter.lng()
        }
      }
      const currentZoom = event.map?.map?.getZoom()

      if (currentCenter && currentZoom !== undefined) {
        const centerChanged = this.hasSignificantCenterChange(currentCenter, this.savedMapState.center)
        const zoomChanged = Math.abs(currentZoom - this.savedMapState.zoom) > 0.5

        this.showRecenterButton = centerChanged || zoomChanged
      }
    }
  }

  async mounted() {
    this.checkSearchTipsVisibility()
    await this.initMarkerAndFence()
    this.initMap()
    this.calcHeight()
  }
}
</script>

<style lang="scss" scoped>
@import '../scss/map.scss';

.icon-img {
  width: 38px;
  height: 44px;
  object-fit: contain;

  &.active {
    transform: scale(1.36);
    transform-origin: 50% bottom;
    animation: MarkerScale 0.26s ease-in 0s 1;
  }
}

.top-operator-box {
  top: 0;
  padding: 20px;
  width: 100%;
  position: absolute;
  display: flex;
  align-items: center;

  .close-button {
    flex: none;
  }

  .fake-input-box {
    margin-left: 16px;
    display: flex;
    align-items: center;
    padding: 0 16px;
    height: 40px;
    border-radius: 9999px;
    background: #fff;
    box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.14);
    flex: 1;
    // padding: 20 + closeicon : 40px + margin-left: 16px
    width: calc(100% - 96px);
    transition: all 0.32s ease-in-out;

    &-left {
      display: flex;
      align-items: center;
      flex: 1;
      overflow: hidden;

      .glass-icon {
        flex: none;
        width: 20px;
        height: 20px;
      }

      .search-input {
        margin-left: 8px;
        flex: 1;
        font-size: $fontSize-body-s;
        line-height: 21px;
        color: $color-neutral-600;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;

        &.is-keywords {
          color: $color-neutral-900;
        }
      }
    }

    &-right {
      display: flex;
      align-items: center;
      padding: 0 4px;
      margin-left: 8px;

      .clear-icon {
        flex: none;
        width: 16px;
        height: 16px;
      }
    }

    &.no-width {
      overflow: hidden;
      flex: none;
      width: 0 !important;
      padding: 0;
    }
  }
}

.marker-item-outer {
  box-shadow: $shadow-normal-4;
  border: 1px solid $color-border-normal;
  border-radius: $radius-circle;
  position: relative;
  &.active {
    border: 2px solid $color-border-normal;
    transform: scale(1.4);
    transform-origin: 50% bottom;
    transition: transform ease-in-out 0.25s;
    .marker-item-outer-foot {
      border: 2px solid $color-border-normal;
    }
  }
  .marker-item-outer-foot {
    width: 12px;
    height: 12px;
    transform: translateX(-50%) rotate(45deg);
    transform-origin: center;
    position: absolute;
    bottom: -4px;
    left: 50%;
    z-index: 1;
    border: 1px solid $color-border-normal;
    border-radius: 1px;
  }
  .marker-item {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    border-radius: $radius-circle;
    z-index: 2;
  }
}

::v-deep .klkMap_marker.marker-active {
  z-index: 202 !important;
}

::v-deep .klkMap_marker.is-area {
  z-index: 200 !important;
}

// 新版本搜索样式
.search-full-srceen-modal {
  position: fixed;
  width: 100vw;
  bottom: 0;
  left: 0;
  z-index: 3333;
  min-height: 40vh;
  max-height: 75vh;
  background: $color-bg-1;
  border-radius: $radius-xxl $radius-xxl 0 0;
  transition: transform 0.3s ease-in-out;
  transform: translateY(200%);

  &.active {
    transform: translateY(0);
  }
}

.klk-scroll-snap-map {
  position: absolute;
  bottom: 0px;
  left: 0;
  right: 0;
  z-index: 222;

  ::v-deep .klk-scroll-snap_scroller {
    align-items: flex-end;

    .card-wrap {
      height: fit-content !important;
    }
  }
}

.icon-img-wrap {
  position: relative;

  .pick-up-map-area-marker-wrap {
    top: -56px;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
  }
}

.user-location {
  bottom: 174px;
  left: unset !important;
  right: 16px;
}

.map-search-box {
  position: absolute;
  right: 16px;

  .map-search-tips {
    @include font-paragraph-m-regular;
    position: absolute;
    right: 50px;
    top: 50%;
    transform: translateY(-50%);
    background: #fff;
    border-radius: 8px;
    padding: 12px 16px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, .14);
    white-space: nowrap;
    display: flex;
    align-items: center;
    gap: 4px;
    margin-right: 6px;
    color: #212121;
    z-index: 10;

    &::after {
      content: '';
      position: absolute;
      right: -8px;
      top: 50%;
      transform: translateY(-50%);
      width: 0;
      height: 0;
      border-left: 8px solid #fff;
      border-top: 8px solid transparent;
      border-bottom: 8px solid transparent;
    }

    .map-search-light-bulb {
      width: 20px;
      height: 20px;
      flex: none;
    }

    .map-search-close {
      cursor: pointer;
      color: #666;

      &:hover {
        color: #212121;
      }
    }
  }

  .map-search-icon {
    display: inline-flex;
    width: 40px;
    height: 40px;
    justify-content: center;
    padding: 10px;
    color: #212121;
    background: #fff;
    text-align: center;
    line-height: 46px;
    border-radius: 999px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, .14);
    cursor: pointer;
  }
}

.search-bottom-sheet  {
  max-height: 90vh;
  top: initial;

  ::v-deep .klk-bottom-sheet-mask {
    display: none;
  }

  ::v-deep .klk-bottom-sheet-inner {
    padding-top: 4px;
    height: auto;
    min-height: auto;
    max-height: 90vh;
    .klk-bottom-sheet-body {
      padding: 0;
      height: auto;
    }
  }
}

// Marker 文案提示样式
.marker-tooltip {
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  margin-bottom: 8px;
  z-index: 1000;

  .marker-tooltip-content {
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 12px;
    line-height: 1.4;
    white-space: nowrap;
    max-width: 200px;
    word-break: break-all;

    .marker-tips {
      margin-bottom: 4px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .marker-address {
      opacity: 0.9;
    }
  }

  .marker-tooltip-arrow {
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 6px solid transparent;
    border-right: 6px solid transparent;
    border-top: 6px solid rgba(0, 0, 0, 0.8);
  }
}
</style>
