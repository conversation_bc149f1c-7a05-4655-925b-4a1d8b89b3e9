import { Component, Vue, Inject, Prop } from 'vue-property-decorator'
import type { MapInfo2Prop, flatLocationItem } from '../types/index'
import type { PoiCardInfoItem, AreaConfig, IMapDataItemTips, IAreaProperties } from '../../../types'
import { mapUtils } from "@klook/map"
import { handleIHTrack, getExt } from "../../../utils/inHouseTrack";
import { POI_USING_TYPE } from '../../../utils'
import { MapTypes } from "@klook/map";

@Component
export default class MapBase extends Vue {
  @Prop({ type: Object, default: () => ({}) }) mapInfo!: MapInfo2Prop;
  @Prop({ type: String, default: "mapbox" }) mapType!: "mapbox" | "google"
  @Inject({ default: () => null }) customAxios!: any
  @Inject({ default: () => () => '' }) translateI18n!: Function;
  @Prop({ type: Object, default: () => null }) pickupInfo!: any

  map: MapTypes.MapTool | undefined;
  // 地图状态记录和 Re-center 功能
  savedMapState: {
    center: any;
    zoom: number;
  } | null = null
  showRecenterButton = false
  recentering = false
  cancelOutMoveAndSaveState = false

  get locationList() {
    return (this.mapInfo.map || []).map((item: any) => {
      return {
        ...item
      }
    })
  }

  get flatLocationList(): flatLocationItem[] {
    return this.locationList?.reduce((acc: any, curr: any) => {
      if (curr.data.using_type === POI_USING_TYPE.area) {
        return curr.data.area_id_list.reduce((acc: any, area_id: any) => {
          const area = this.mapInfo.areaConfig.area_list.find((item: any) => item.area_id === area_id)
          if (area) {
            acc.push({
              ...curr,
              ...area,
              area_id,
              location: area.area_center_points?.[0],
            })
          }

          return acc
        }, acc)
      }

      acc.push({
        ...curr,
        area_id: 0,
      })
      return acc
    }, [])
  }

  getGeomByIdList(params: any) {
    return this.customAxios.$get('/v1/experiencesrv/area/strative_area_service/get_geom_by_id_list', {
      params
    })
  }

  initFenceData(fence: any[], areaConfig: AreaConfig, currentCard?: any) {
    const list = fence.reduce((acc: any, curr: any) => {
      // 如果当前卡片的 pick_up_type 是 2（地理围栏区域），使用 selected_area_properties
      // 否则使用默认的 unselected_area_properties
      const isCurrentCardArea = currentCard?.data?.using_type === POI_USING_TYPE.area || currentCard?.pick_up_type === 2
      let area_properties = isCurrentCardArea && currentCard?.area_id_list?.includes(curr.area_id)
        ? (areaConfig?.selected_area_properties || {} as IAreaProperties)
        : (areaConfig?.unselected_area_properties || {} as IAreaProperties)

      const strokeColor = area_properties.stroke
      const fillColor = area_properties.fill
      const coordinates = curr?.geom?.coordinates || []
      coordinates.forEach((polygonList: any) => {
        acc.push({
          area_id: curr.area_id,
          polygonList: curr?.geom?.type === 'Polygon' ? [polygonList] : polygonList,
          polygonConfig: {
            strokeColor,
            fillColor
          }
        })
      })
      return acc
    }, [])
    return list
  }

  isArea(marker: any) {
    const { pick_up_type } = marker
    return pick_up_type === 2
  }

  getUuid() {
    return parseInt(String(Math.random() * 10000000))
  }

  initMarker(data: any, isTemporary = false, options: any = {}) {
    const {
      location,
      icon,
      address_desc,
      area_id,
      tips = {},
      pick_up_type,
      group_icon_v2,
      attr_id
    } = data
    const {
      text = '',
      bg_color = '',
      border_color = '',
      font_color = '',
      border_width = ''
    } = tips as IMapDataItemTips
    const poptip = this.isArea(data) ? text : address_desc
    // const style = this.isArea(data) ? { backgroundColor: bg_color, borderColor: border_color, color: font_color } : {}
    const style = {
      backgroundColor: bg_color,
      borderColor: border_color,
      color: font_color,
      borderWidth: border_width + 'px'
    }
    const groupIcon = group_icon_v2 || { select: icon, unselect: icon }
    return {
      data,
      location,
      isTemporary,
      center: mapUtils.formatLatLng(location),
      poptip,
      tips_text: text,
      options: {
        anchor: 'bottom',
      },
      area_id,
      style,
      pick_up_type,
      groupIcon,
      ...options,
      id: attr_id,
      attr_id
    }
  }

  initCards(pois: any[] = []) {
    const cards: any[] = []
    const { points, areas } = pois.reduce((acc: any, curr: any) => {
      const { pick_up_type } = curr
      if (pick_up_type === 2) {
        acc.areas.push(curr)
      } else {
        const { location, address_desc, location_name, group_name = '', group_time = '' } = curr.data
        const { id } = curr
        acc.points.push({
          imgs: [],
          title: location_name,
          group_time,
          group_name,
          icons: [],
          map: {
            address_desc,
            location
          },
          card_id: id
        })
      }
      return acc
    }, { points: [], areas: [] })

    if (areas.length) {
      const { i18n = {} } = this.pickupInfo || {}
      const { search_title = '', search_description = '', search_text = '' } = i18n
      cards.push(
        {
          is_static_card: true,
          imgs: [],
          title: search_title,
          group_time: '',
          group_name: '',
          icons: [],
          map: {
            address_desc: search_description,
            location: ''
          },
          search_placeholder: search_text
        }
      )
    }

    return [...cards, ...points]
  }

  getBindTracker(tracker: any) {
    return handleIHTrack(tracker);
  }

  getValidAreaId(areas: PoiCardInfoItem[] = []) {
    return areas.reduce((acc: number[], item: any) => {
      const id = item.area_id
      if (!!id) {
        acc.push(id)
      }

      const area_id_list = item.area_id_list || []
      acc.push(...area_id_list)

      return acc
    }, [])
  }

  formatNearestMarker(nearestData: any) {
    const data = {
      location: nearestData.location,
      icon: nearestData.icon,
      address_desc: nearestData.title || 'Nearest Location',
      area_id: 0,
      tips: {},
      pick_up_type: 1,
      group_icon_v2: {
        select: nearestData.icon,
        unselect: nearestData.icon
      },
      attr_id: this.getUuid()
    }

    return this.initMarker(data, true, {
      isNearest: true,
      tracker: {
        type: 'module',
        spm: 'ViewNearestPoint',
        exposure: true,
        query: {
          ext: getExt({
            Type: 'nearest'
          }),
        },
      }
    })
  }

  async fitBoundsByPoints(points: any) {
    if (this.map && points.length) {
      const box = await mapUtils.turfBbox(points, this.mapType as MapTypes.Type)
      const bounds = this.map.createBounds(box.sw, box.ne, { formatLngLat: false })
      this.map.fitBounds(bounds, {
        padding: {
          // top: this.markerList.length ? 100 : 20,
          top: 20,
          left: 20,
          bottom: 20,
          right: 20,
        },
      })
    }
  }

  saveCurrentMapState() {
    if (this.map?.map?.getCenter && this.map?.map?.getZoom) {
      const center = this.map.map.getCenter()
      this.savedMapState = {
        center: this.map.map.getCenter().toJSON(),
        zoom: this.map.map.getZoom()
      }
    }
  }

  restoreDefaultMapState() {
    this.savedMapState = null
    this.showRecenterButton = false
  }

  handleRecenter() {
    if (this.savedMapState && this.map) {
      const center = this.savedMapState.center
      this.map.map.setCenter(center)
      this.map.setZoom(this.savedMapState.zoom)
      this.showRecenterButton = false
      this.recentering = true
    }
  }

  fitMapBoundsForTwoPoints(location1: string, location2: string) {
    if (this.map) {
      const points = [
        mapUtils.formatLatLng(location1),
        mapUtils.formatLatLng(location2)
      ]
      this.fitMapBoundsForCoordinates(points)
    }
  }

  hasSignificantCenterChange(current: any, saved: any): boolean {
    if (!current || !saved) return false

    // 计算两个中心点之间的距离（简单的经纬度差值检查）
    const latDiff = Math.abs((current.lat || current.latitude || 0) - (saved.lat || saved.latitude || 0))
    const lngDiff = Math.abs((current.lng || current.longitude || 0) - (saved.lng || saved.longitude || 0))

    // 如果经纬度差值超过 0.001（约100米），认为是显著变化
    return latDiff > 0.001 || lngDiff > 0.001
  }

  /**
   * 传入坐标数据数组，地图将这些坐标全部显示并尽可能居中显示
   * @param coordinates 坐标数组，格式: [{ lat: number, lng: number, title?: string }, ...]
   * 或者字符串格式: ['lat,lng', 'lat,lng', ...]
   */
  fitCoordinates(coordinates: Array<{lat: number, lng: number, title?: string} | string>) {
    if (!coordinates || coordinates.length === 0) {
      return
    }

    // 转换坐标格式
    const points = coordinates.map(coord => {
      if (typeof coord === 'string') {
        const [lat, lng] = coord.split(',').map(Number)
        return mapUtils.formatLatLng(`${lat},${lng}`)
      } else {
        return mapUtils.formatLatLng(`${coord.lat},${coord.lng}`)
      }
    })

    this.fitMapBoundsForCoordinates(points)
  }

  async fitMapBoundsForCoordinates(points: any[]) {
    if (this.map && points.length > 0) {
      if (points.length === 1) {
        // 单个坐标点，直接飞到该点
        this.map.flyTo(points[0])
        this.map.setZoom(16)
      } else {
        // 多个坐标点，计算边界并适配
        const box = await mapUtils.turfBbox(points, this.mapType as MapTypes.Type)
        const bounds = this.map.createBounds(box.sw, box.ne, { formatLngLat: false })
        this.map.fitBounds(bounds, {
          padding: {
            top: 80,
            left: 20,
            bottom: 160,
            right: 20,
          },
        })
      }
    }
  }
}
