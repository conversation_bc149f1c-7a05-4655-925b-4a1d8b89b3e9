import { Component, Vue, Inject, Prop, Watch } from 'vue-property-decorator'
import apis from '../../../utils/apis'
import { PoiCardInfoItem } from '../../../types'
import { SearchResultItem } from '../types/index'

@Component
export default class PoiSearch extends Vue {
  @Prop({ type: Array, default: () => [] }) mapData!: PoiCardInfoItem[]
  @Inject("translateI18n") translateI18n!: Function;
  @Inject("customAxios") customAxios!: any;
  @Inject("packageId") packageId!: number;
  @Inject("isMobile") isMobile!: boolean;

  cancelToken: any = null
  cancelDetailToken: any = null
  searchResultList: SearchResultItem[] = []
  keywords: string = ''
  currentLoadingIndex: number = -1
  isNotMatch: boolean = false
  activeTabType: string | undefined = undefined
  cacheActiveTabType:string | undefined = undefined
  renderList = []

  get showTab() {
    return this.searchResultList.length > 1
  }

  get tabList() {
    return this.searchResultList.map((item) => {
      const {  tab_type, tab_title } = item
      return {
        tab_title,
        tab_type,
        key: this.getTabTypeKey(item)
      }
    })
  }

  @Watch('searchResultList', { immediate: true, deep: true })
  tabListChange(data: any, oldData) {
    if (oldData?.length !== data.length) {
      this.activeTabType = data[0]?.tab_type ? this.getTabTypeKey(data[0]) : undefined
    }
    this.getRenderList()
  }

  handleTabChange() {
    this.getRenderList()
  }

  getRenderList() {
    if (this.keywords && this.activeTabType !== undefined) {
      const res = this.searchResultList.find(
        (item) => this.getTabTypeKey(item) === this.activeTabType
      )

      this.$set(this, 'renderList', res?.tab_options ?? [])
    }

    return []
  }

  getTabTypeKey(tab: any) {
    return `_${tab.tab_type}`
  }

  initResult(item: any = {}) {
    const { name: title, address_desc: sub_title, google_place_id: place_id } = item
    return {
      title,
      sub_title,
      place_id,
      is_default: true,
      detail_data: {
        ...item
      }
    }
  }

  async getSearchFun(params: any) {
    if (this.cancelToken) {
      this.cancelToken.cancel('cancel')
    }

    this.cancelToken = this.customAxios?.CancelToken?.source?.() || null

    const res = await this.customAxios.$get(apis.autocomplete_v2, {
      params,
      cancelToken: this.cancelToken?.token
    })
    const result = res?.result?.tab_list ?? []

    return result.map((item: any) => {
      return {
        ...item
      }
    })
  }

  async getPoiDetail(params: any) {
    if (this.cancelDetailToken) {
      this.cancelDetailToken.cancel('cancel')
    }

    this.cancelDetailToken = this.customAxios?.CancelToken?.source?.() || null

    const res = await this.customAxios.$get(apis.getPlaceDetail, {
      params,
      cancelToken: this.cancelDetailToken?.token
    })

    return res?.result ?? null
  }


}
