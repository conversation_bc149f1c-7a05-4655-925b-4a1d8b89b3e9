
<template>
  <div v-if="data" class="nearest-comp" :class="platform">
    <div class="nearest-comp__left">
      <span
        class="nearest-comp__title-tag"
        :style="{
          borderColor: data.title_icon.border_color,
          color: data.title_icon.text_color
        }"
      >{{ data.title_icon.text }}</span>
      <span class="nearest-comp__title">{{ data.title }}</span>
    </div>
    <div class="nearest-comp__right">
      <span class="nearest-comp__distance">{{ data.distance }}</span>
      <klk-poptip v-if="data.tips.text" :content="data.tips.text" placement="top">
        <img class="nearest-comp__tips-icon" :src="data.tips.icon" />
      </klk-poptip>
    </div>
  </div>
</template>

<script lang="ts">
import { Vue, Prop, Component } from 'vue-property-decorator'
import type { NearestData } from '../types/index'

@Component
export default class NearestComp extends Vue {
  @Prop({ default: 'mobile' }) platform!: string
  @Prop({ type: Object, required: true }) readonly data!: NearestData
}
</script>

<style lang="scss" scoped>
.nearest-comp {
  display: flex;
  gap: 12px;
  align-items: center;
  justify-content: space-between;

  .nearest-comp__title-tag {
    @include font-caption-m-semibold;
    margin-right: 4px;
    padding: 1px 6px;;
    border: 1px solid transparent;
  }

  .nearest-comp__title {
    @include font-paragraph-m-regular;
    color: $color-text-primary;
  }

  .nearest-comp__right {
    display: flex;
    align-items: center;
  }

  .nearest-comp__distance {
    @include font-paragraph-s-bold;
    color: $color-text-secondary;
    margin-right: 4px;
  }

  .nearest-comp__tips-icon {
    margin-top: 4px;
  }
}
</style>
