import { IAreaProperties, PoiCardInfoItem, AreaConfig, IMapDataItemTips, AreaInfoItem } from '../../../types'

export interface IGroupIcon {
  select: string
  unselect: string
}

export interface MapDataItem {
  location: string
  icon: string
  google_place_id: string
  location_name: string
  city_name: string
  address_desc: string
  data_type: number
  area_id: number
  tag: any[]
  pick_up_type?: number
  tips?: IMapDataItemTips
  area_properties?: IAreaProperties
  group_icon_v2?: IGroupIcon
  id?: number
  is_customized_area?: number
  itinerary_attr_value_id?: number
  attr_id: string
}

export interface flatLocationItem extends PoiCardInfoItem, AreaInfoItem {
  using_type: 'location' | 'area'
  area_id_list: number[]
  location: string
  google_place_id: string
  city_name: string
  address_desc: string
  name: string
  area_id: number
  pick_up_type: number
  data_type?: number
}

export interface MapInfo2Prop {
  map: Array<PoiCardInfoItem>,
  areaConfig: AreaConfig
}

export interface NearestData {
  title_icon: {
    text_color: string
    border_color: string
    text: string
  }
  title: string
  distance: string
  tips: {
    icon: string
    text: string
  }
  icon: string
  location: string
}

export interface searchResultTabOption {
  location: string
  icon: string
  location_name: string
  address_desc: string
  google_place_id: string
  data_type: number
  area_id: number
  tag: any[]
  pick_up_type?: number
  tips?: IMapDataItemTips
  group_icon_v2?: IGroupIcon
  id?: number
  is_customized_area?: number
  itinerary_attr_value_id?: number
  attr_id: string
  customized_config?: any
  nearest?: NearestData
}

export interface MapInfo2Prop {
  map: Array<PoiCardInfoItem>,
  areaConfig: AreaConfig
}

export interface PickUpInfoData {
  title: string
  description: string
  point_list: string[]
  point_count: number
  map_data: {
    map_type: 'string',
    pick_up_type: number, // 1:point 2:scope
    map: MapDataItem[]
  }
}

export interface KeywordsSearchItem {
  location: string,
  address_desc: string,
  location_name: string,
  place_id: string,
  map_type: number,
  amap_code: string,
  data_type: number,
  tag: any[],
  google_place_id: string
}

export interface MapData {
  location: string,
  google_place_id: string,
  address_desc: string,
  icon: string,
  area_id: number,
  administrative_level: number
}

export interface TabOption {
  place_id: string
  title: string
  sub_title: string
  is_detail_expanded: boolean
  detail_data: searchResultTabOption
}

export interface SearchResultItem {
  tab_type: string
  tab_title: string
  tab_options: TabOption[]
}
