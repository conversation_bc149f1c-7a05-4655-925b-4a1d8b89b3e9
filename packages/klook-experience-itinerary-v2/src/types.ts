// 基础类型定义
export interface MapLocation {
  location: string;
  google_place_id: string;
  city_name: string;
  address_desc: string;
  name: string;
}

export interface PoiData {
  title: string;
  title_icon: string;
  map: MapLocation;
  group_key: string;
  attr_id: number;
  poi_identify_id: string;
}

// 先定义ComponentTypeMap
type ComponentTypeMap = {
  spacing: { height: number };
  poi: {
    background_color: string;
    poi_desc: string;
    poi_data: PoiData[];
    is_poi_tappable: boolean;
    copy_icon: string;
  };
  icons: { icons: Array<{ text: string; icon: string }> };
  imgs: { imgs: string[], poi_identify_id: string };
  texts: { texts: Array<{ type: 'dark' | 'desc' | 'text'; text: string }> };
  title_icons: {
    has_underline: boolean;
    data_list: Array<{
      title_name: string;
      star: number;
      icons: Array<{ text: string; icon: string }> | null;
      copy_icon: string;
    }>;
  };
  reminder: { title_name: string; texts: string[] };
  divider: { text: String, text_color: string, line_color: string },
  with_right_triangle_title: { text: string, with_right_triangle: boolean, detail_components: Array<{ type: String, data: any }> },
  tips: { text: string, icon: string, background_color: string },
  color_texts: {
    color_texts: Array<{
      text: string
      type?: string
      text_map?: {
        [key: string]: {
          text: string
          color?: string
          style?: string | string[]
        }
      }
    }>
  },
  front_title_with_tag: { tags: Array<{ text_color: string, border_color: string, text: string }>, title: string },
  session_block: {
    texts: Array<{ type: 'desc' | 'text' | 'dark', text: string }>;
    departure_times: string[];
    session_table: {
      title: string;
      desc: string;
      table: Array<{
        type: 'desc' | 'text';
        values: string[];
      }>;
    };
  }
};

// 然后定义泛型CardComponent
export type CardComponent<T extends keyof ComponentTypeMap> = {
  type: T;
  data: ComponentTypeMap[T];
};

// 行程组类型
export interface ItineraryGroup {
  group_id: number;
  group_key: 'departure' | 'attraction' | 'meal' | 'accommodation' | 'transport' | 'return';
  left_icon: string;
  titles: string[];
  is_title_bold: boolean;
  background_color: string;
  card_components: Array<CardComponent<keyof ComponentTypeMap>>;
  track_info: {
    type: string;
    extra: any; // 根据实际业务需求细化
  };
  details: {
    title: string;
    tabs: Array<{
      tab_name: string;
      sections: Array<{
        section_name: string;
        components: Array<CardComponent<keyof ComponentTypeMap>>;
      }>;
      track_info: {
        type: string;
        extra: any;
      };
    }>;
  } | null;
}

// 每日行程
export interface ItineraryDay {
  day_name: string;
  groups: ItineraryGroup[];
}

export interface Disclaimers {
  icon: string;
  text: string;
}

// 完整行程结果类型
export interface ItineraryData {
  spu_id: number;
  disclaimers: Array<Disclaimers> | null;
  view_on_map: {
    map_url: string;
    map_img: string;
    map_title: string;
    map_icon: string;
  };
  days: ItineraryDay[];
}

export interface IAreaProperties {
  stroke: string;
  stroke_width: number;
  stroke_opacity: number;
  fill: string;
  fill_opacity: number;
}

export interface PoiCardInfoItem {
  title: string;
  title_icon: {
    text_color: string;
    border_color: string;
    text: string;
  };
  img: string;
  data: {
    using_type: 'location' | 'area';
    map: MapLocation;
    area_id_list: number[];
  };
  icon_text: Array<{
    icon: string;
    text: string;
  }>;
  group_key: string;
  attr_id: string;
  group_icon_v2: {
    select: string;
    unselect: string;
  };
  poi_identify_id: string;
  tips?: IMapDataItemTips
  area_properties?: IAreaProperties
  is_customized_area?: number
  itinerary_attr_value_id?: number
}

export interface IMapDataItemTips {
  bg_color: string
  border_color: string
  font_color: string
  text: string,
  border_width: string
}

export interface AreaInfoItem {
  area_id: number;
  customized_config: {
    extra_fee: {
      price_value: number;
      price_currency: string;
    };
    is_selected: number;
    customized_area_name: string;
  };
  is_customized_area?: number;
  area_center_points: string[];
  tips?: IMapDataItemTips;
}

export interface AreaConfig {
  area_list: Array<AreaInfoItem>;
  selected_area_properties: IAreaProperties;
  unselected_area_properties: IAreaProperties;
}

export interface ItineraryPoiInfoV2Result {
  poi_card_info: Array<PoiCardInfoItem>;
  area_config: AreaConfig
}

export interface ItineraryPoiInfoV2Resp {
  success: boolean;
  error: {
    code: string;
    message: string;
  };
  result: ItineraryPoiInfoV2Result;
}

