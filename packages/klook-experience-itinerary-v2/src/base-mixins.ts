import { Component, Vue, Prop, Provide, Watch } from 'vue-property-decorator'
import VueLazyLoad from 'vue-lazyload';
import { ItineraryData, ItineraryPoiInfoV2Resp } from './types'
import DayList from './components/day/list.vue'
import ViewMap from './components/view-map/index.vue'
import Map from './components/map.vue'
import ItineraryMenus from './components/menus/index.vue'
import { isServer, getCropDefined, isExpandSeeMoreKey, POI_USING_TYPE } from './utils'

import IconTipsSvg from './imgs/icon-tips.svg'

const lazyLoadingImage = 'https://res.klook.com/image/upload/image_logo_mx7wgd.png'

Vue.use(VueLazyLoad, {
  adapter: {
    loaded(listender: any) {
      const el = listender.el
      const ratio = el.getAttribute('ratio')

      if (!isServer && ratio) {
        const logo = el?.querySelector?.('.v-lazy-logo') || el?.parentElement?.querySelector?.('.v-lazy-logo')
        logo && (logo.style.display = 'none')
      }
    }
  },
  error: lazyLoadingImage
})

if (!isServer) {
  const VueAwesomeSwiper = require('vue-awesome-swiper/dist/ssr')
  Vue.use(VueAwesomeSwiper)
}

@Component({
  components: {
    ItineraryMenus,
    ViewMap,
    DayList,
    IconTipsSvg,
    Map,
  }
})
export default class BaseMixins extends Vue {
  @Prop() transitionendCallback!: Function
  @Prop({ default: '' }) customFlowType!: 'itinerary-details'
  @Prop({ type: Object }) customFlowData!: { details: { tabs: any[] } }
  @Prop({ default: null }) customComp!: Record<string, object>
  @Prop({ default: 'mobile' }) platform!: string
  @Prop({ default: () => '' }) getTranslateFunc!: Function // this.$t.bind(this)
  @Prop({ default: () => () => null }) getAxiosFunc!: Function // this.$t.bind(this)
  @Prop({ default: false }) disableLazyload!: boolean
  @Prop({ type: Object, default: () => ({}) }) itineraryData!: ItineraryData
  @Prop({ type: String, default: 'mapbox' }) mapType!: 'mapbox' | 'google'
  @Prop({ default: false }) hackCloseBtn?: boolean
  @Prop() grounpTitleClick!: Function
  @Prop({ default: () => '' }) inhouseTrack!: Function // 埋点
  @Prop({ default: false }) simplified!: boolean // desktop 右侧栏需要简化版本
  @Prop() language!: string | undefined
  @Prop() klook!: object // 以后替代其它散落的参数

  isServer = false

  isExpandSeeMoreKey = isExpandSeeMoreKey

  moreObj: any = {
    [isExpandSeeMoreKey]: false,
    maxHeight: 700,
    status: ''
  }

  clickShowMore(item: any, status: string) {
    this.$set(item, this.isExpandSeeMoreKey, status)
  }

  updateDataHandler(item: any, data: any) {
    const { moreObj} = this
    if (!item[this.isExpandSeeMoreKey] && data?.height > moreObj.maxHeight) {
      this.$set(item, this.isExpandSeeMoreKey, 'more')
    }
  }

  get calcCustomDetals() {
    return this.customFlowData?.details
  }
  get spuId() {
    return this.itineraryData?.spu_id
  }

  get viewOnMap() {
    return this.itineraryData?.view_on_map
  }

  get disclaimers() {
    return this.itineraryData?.disclaimers || []
  }

  getUuid() {
    return parseInt(String(Math.random() * 10000000))
  }

  async getViewOnMapData(): Promise<ItineraryPoiInfoV2Resp | undefined> {
    const { map_url = '' } = this.viewOnMap || {}
    if (map_url) {
      try {
        const res = await this.customAxios.$get(map_url)
        if (res?.success) {
          if (Array.isArray(res?.result?.poi_card_info)) {
            res.result.poi_card_info = res.result.poi_card_info.map((item: any) => {
              const pick_up_type = item.data?.using_type === POI_USING_TYPE.area ? 2 : 1

              return {
                ...item,
                ...item?.data || {},
                ...item?.data?.map || {},
                pick_up_type,
                attr_id: item?.poi_identify_id || this.getUuid(),
              }
            })
          }
        }
        return res
      } catch (error) {
        console.error('获取地图数据失败', error)
      }
    }
  }

  mapApiData: any = null

  async mounted() {
    this.mapApiData = await this.getViewOnMapData()
  }

  @Provide('klook2provide')
  get calcKlook() {
    return this.klook || {
      isBot: false,
      platform: 'mobile',
      webp: 0
    }
  }

  @Provide('openMap2provide')
  viewOnMapBottomHandler(id: string) {
    const { result } = this.mapApiData || {}
    const poiList = result?.poi_card_info || []
    const map = poiList?.find((item: any) => item.poi_identify_id === id)
    this.currentPoi = map
    this.$refs.viewMapRef?.viewOnMapHandler?.()
  }

  get days() {
    return this.itineraryData?.days || []
  }

  @Provide('viewOnMap2provide')
  get viewOnMap2provide() {
    return this.viewOnMap
  }

  currentPoi = null

  @Watch('visible')
  visibleChange(v: boolean) {
    if (v) {
      if (typeof this.inhouseTrack === 'function') {
        this.inhouseTrack('pageview', '#TNA_ItineraryPhoto_page_spm', { force: true })
      }
    }
  }

  get pageSpm() {
    return `TNA_ItineraryPhoto?oid=package_${this.packageId}&trg=manual`
  }

  @Provide('language2provide')
  get calcLanguage() {
    return this.language
  }

  @Provide('mapStyle')
  get mapStyle() {
    return this.mapType
  }

  // 好像没有用了，暂时先保留
  @Provide('isSimplified')
  get isSimplified() {
    return this.simplified
  }

  @Provide('isMobile')
  get isMobile() {
    return this.platform === 'mobile'
  }

  mapVisible: boolean = false
  mapAtrrs = {}

  @Provide('handleTitleClick')
  handleTitleClick(data: any, isPoi: boolean = false) {
    if (typeof this.grounpTitleClick === 'function') {
      this.grounpTitleClick(data)
      return
    }
    if (isPoi) {
      const { poiMapData } = data || {}
      if (this.isMobile) {
        // this.handleShowMap(poi) // 新版同APP/Mweb不可点击
      } else {
        if (poiMapData) {
          const { location: address, address_desc: addressDesc } = poiMapData ?? {}
          this.setMapShow({
            address,
            addressDesc
          })
        }
      }
    }
  }

  setMapShow(data: any) {
    this.mapVisible = true
    this.mapAtrrs = data
  }

  handleShowMap(map: any) {
    this.currentPoi = map
  }

  @Provide('handleEmit')
  handleEmit(event: string, data: any) {
    this.$emit(event, data)
  }

  @Provide('customCompObj')
  get customCompObj() {
    return this.customComp
  }

  @Provide('customInhouseTrack')
  customInhouseTrack(...args: any) {
    return this.inhouseTrack(...args)
  }

  @Provide('platform')
  get currentPlatform() {
    return ['mobile', 'desktop'].includes(this.platform) ? this.platform : 'mobile'
  }

  @Provide('translateI18n')
  translateI18n(key: string, ...values: any) {
    const $t = this.getTranslateFunc()
    return $t(key, ...values)
  }


  @Provide('customAxios')
  get customAxios() {
    return this.getAxiosFunc()
  }

  @Provide('packageId')
  get packageId() {
    return this.spuId ?? 0
  }

  // 生成pdf的时候图片不能用懒加载
  @Provide('disableLazyload')
  get shouldDisableLazyload() {
    return this.disableLazyload
  }

  @Provide('currentMapType')
  get currentMapType() {
    return this.mapType
  }

  visible: boolean = false
  currentIndex: number = 0
  currentImages: any = {}

  imageGalleryObj: any = {
    dontGetImages: true,
    showGallery: false,
    currentIndex: 0,
    bannerList: [],
    ons: {
      close: () => {
        const { imageGalleryObj } = this
        imageGalleryObj.itineraryImages = []
        imageGalleryObj.currentIndex = 0
        imageGalleryObj.showGallery = false
      }
    }
  }

  allImages: { src: string }[] = []
  showImageViewerMapEntry = false

  clickImageHandler(data: any) {
    this.showImageViewerMapEntry = !!data?.showImageViewerMapEntry
    const { list, index, poi_identify_id: id } = data || {}
    const poiMaps = this.mapApiData?.result?.poi_card_info || []
    const map = poiMaps.find((item: any) => item.attr_id === id)
    this.allImages = (list || []).map((str: any, index: number) => ({ src: str, id: index, currentPoi: map }))
    this.currentIndex = index
    if (this.isDesktop) {
      this.showImageFnHandler(index)
    } else {
      this.swiperChange(index)
      this.visible = true
    }
  }

  get calcCropDefined() {
    const { platform } = this
    return getCropDefined(platform)
  }

  getCropUrl(src = '', options?: { crop: string }) {
    // 接口返回数据：原图、带裁剪参数的图片
    const { uploadUrl, previewCrop, adminCrop } = this.calcCropDefined
    let imgCrop = options?.crop || previewCrop
    if (src.indexOf(imgCrop) !== -1) {
      // 防止重复拼接参数
      return src
    }
    let str = src
    if (src.indexOf(adminCrop) !== -1) {
      // 替换裁剪参数
      str = src.replace(adminCrop, imgCrop)
    } else {
      // 原图增加裁剪参数
      str = src.replace(uploadUrl + '/', uploadUrl + imgCrop)
    }
    return str
  }

  getOfficialImages(list: any[]) {
    const arr = list || []
    return arr.map((item, index) => {
      const { src, id } = item
      const previewImgSrc = this.getCropUrl(src)
      return {
        image_id: id,
        img_resize_url: previewImgSrc,
        img_url: previewImgSrc
      }
    })
  }

  showImageFnHandler(index: number = 0) {
    const { imageGalleryObj } = this
    imageGalleryObj.bannerList = this.allOfficialImages
    imageGalleryObj.showGallery = true
    imageGalleryObj.currentIndex = index || 0
    this.swiperChange(index)
  }

  swiperChange(index: number) {
    this.currentImages = this.allImages[index] || null
  }

  get isDesktop() {
    return this.platform === 'desktop'
  }

  get dayList() {
    return this.itineraryData?.days || []
  }

  get allOfficialImages() {
    return this.getOfficialImages(this.allImages)
  }
}
