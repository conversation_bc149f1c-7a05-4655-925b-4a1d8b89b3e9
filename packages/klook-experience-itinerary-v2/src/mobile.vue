<template>
  <div
    class="exp-itinerary v2"
    :class="platform"
  >
    <!-- <ItineraryMenus :list="days" :active="0" />  -->
    <template v-if="customFlowType === 'itinerary-details' && calcCustomDetals">
      <ItineraryDetailsMobile
        :details="calcCustomDetals"
        :showPoiButton="false"
        :isBottomSheetStyle="false"
        :mapTitle="viewOnMap ? viewOnMap.map_title : ''"
        :platform="platform"
        @clickImage="clickImageHandler"
      ></ItineraryDetailsMobile>
    </template>
    <template v-else>
      <ViewMap v-if="!isServer" ref="viewMapRef" :viewOnMap="viewOnMap" :mapApiData="mapApiData" :platform="platform" :currentPoi="currentPoi" class="view-map-wrap" :class="platform" @viewOnMapEntry="viewOnMapBottomHandler" />
      <DayList :platform="platform" :list="days" :viewOnMap="viewOnMap" :translateI18n="translateI18n" :transitionendCallback="transitionendCallback" @viewOnMapBottom="viewOnMapBottomHandler" @clickImage="clickImageHandler" />
      <!-- 警告：Map组件在desktop场景下初始化依赖ViewMap -->
      <Map
        v-if="!isServer && mapVisible"
        :show.sync="mapVisible"
        v-bind="mapAtrrs"
      />
    </template>

    <Disclaimers :disclaimers="disclaimers" />

    <template v-if="!isServer && allImages && allImages.length > 0">
      <div
        id="TNA_ItineraryPhoto_page_spm"
        :data-spm-page="pageSpm"
        :data-showImageViewerMapEntry="showImageViewerMapEntry"
      >
        <ImgPreviewMobile
          :images="allImages"
          :index="currentIndex"
          :visible.sync="visible"
          :showImageViewerMapEntry="showImageViewerMapEntry"
          @close="currentImages = null"
          @swiperChange="swiperChange"
        >
          <template slot="description">
            <div class="merchant-image-description">
              {{ currentImages.text }}
            </div>
          </template>
        </ImgPreviewMobile>
      </div>
    </template>
  </div>
</template>

<script lang="ts">
import { Component } from 'vue-property-decorator'
import BaseMixins from './base-mixins'
import ImgPreviewMobile from './components/pic-preview/mobile/index.vue'
import ItineraryDetailsMobile from './components/day/details/mobile.vue'
import Disclaimers from './components/disclaimers.vue'

@Component({
  components: {
    ItineraryDetailsMobile,
    ImgPreviewMobile,
    Disclaimers
  }
})
export default class ExperienceItineraryMobile extends BaseMixins {
}
</script>

<style lang="scss">
// 设置在 html 上用于禁止页面滚动
.klk-lock-body-scroll {
  body {
    width: 100%;
    position: fixed !important;
    height: 100% !important;
  }
}
</style>

<style lang="scss" scoped>
.exp-itinerary {
  position: relative;
  .view-map-wrap {
    position: absolute;
    top: 0;
    right: 0;
    z-index: 2;
  }
}
.merchant-image-description {
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  color: $color-text-reverse;
  padding: 16px 20px calc(env(safe-area-inset-bottom) + 16px) 20px;
  max-height: 133px;
  overflow: auto;
  z-index: 1;
  box-sizing: border-box;
  background-color: rgba(0, 0, 0, 0.8);
  border-radius: $radius-xl $radius-xl 0 0;
  @include font-body-s-regular;
}
</style>
