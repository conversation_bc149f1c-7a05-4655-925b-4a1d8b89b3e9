export const res = {
  success: true,
  error: {
    code: "",
    message: "",
  },
  result: {
    spu_id: 100662,
    disclaimers: [
      {
        "icon": "https://res.klook.com/image/upload/v1735900390/UED_new/TnA/TnA_ToursRevamp_2412/icon_travel_location_fill_xs.svg",
        "text": "我是免责说明，多写点才能换行。多写点才能换行。多写点才能换行。多写点才能换行。多写点才能换行。多写点才能换行。多写点才能换行。多写点才能换行。多写点才能换行。多写点才能换行。"
      },
      {
        "icon": "https://res.klook.com/image/upload/v1735900390/UED_new/TnA/TnA_ToursRevamp_2412/icon_travel_location_fill_xs.svg",
        "text": "我是免责说明，多写点才能换行。多写点才能换行。多写点才能换行。多写点才能换行。多写点才能换行。多写点才能换行。多写点才能换行。多写点才能换行。多写点才能换行。多写点才能换行。"
      }
    ],
    view_on_map: {
      map_url:
        "/v1/experiencesrv/product/itinerary_service/get_itinerary_poi_info?spu_id=100662&language=zh_CN&preview=0",
      map_img:
        "https://res.klook.com/image/upload/v1733733499/UED_new/TnA/TnA_ToursRevamp_2412/bg_view_on_map.png",
      map_title: "寻找出发地点",
      map_icon:
        "https://res.klook.com/image/upload/v1735900390/UED_new/TnA/TnA_ToursRevamp_2412/icon_travel_location_fill_xs.svg",
    },
    days: [
      {
        day_name: "第1天~~~",
        groups: [
          {
            group_id: 142,
            group_key: "departure",
            left_icon:
              "https://res.klook.com/image/upload/v1733467621/UED_new/TnA/TnA_ToursRevamp_2412/icon_itinerary_depature.png",
            titles: ["接送或集合"],
            title_components: [],
            is_title_bold: true,
            background_color: "",
            card_components: [
              {
                "type": "poi",
                "data": {
                  "background_color": "#F1EEFA",
                  "poi_desc": "2 pick-up and 1 meet-up points in multiple cities",
                  "poi_data": [
                    {
                      "title": "test Nickname - Hokkaido ",
                      "title_icon": "",
                      "map": {
                        "location": "43.774163,142.355386",
                        "google_place_id": "ChIJVbfn4QrmDF8Rc6OtJgEh-vU",
                        "city_name": "",
                        "address_desc": "4046-1 Tokiwakoen, Asahikawa, Hokkaido 070-0044, Japan",
                        "name": "test Nickname - Hokkaido "
                      },
                      "group_key": "",
                      "attr_id": 0,
                      "poi_data_id": "1-2"
                    },
                    {
                      "title": "Minami",
                      "title_icon": "",
                      "map": {
                        "location": "42.953919,141.219999",
                        "google_place_id": "ChIJD4c7aFzSCl8RQNVDt0cp3_U",
                        "city_name": "",
                        "address_desc": "",
                        "name": "Minami"
                      },
                      "group_key": "",
                      "attr_id": 0,
                      "poi_data_id": "1-12-195586"
                    },
                    {
                      "title": "Chuo",
                      "title_icon": "",
                      "map": {
                        "location": "43.046848,141.322114",
                        "google_place_id": "ChIJsS4_ghnWCl8R0OaA72qW7ow",
                        "city_name": "",
                        "address_desc": "",
                        "name": "Chuo"
                      },
                      "group_key": "",
                      "attr_id": 0,
                      "poi_data_id": "1-12-219205"
                    }
                  ],
                  "is_poi_tappable": true,
                  "poi_identify_id": "1-2"
                }
              },
              {
                type: "texts",
                data: {
                  texts: [
                    {
                      type: "text",
                      text: "00:08 / 00:10",
                    },
                  ],
                },
              },
              {
                type: "texts",
                data: {
                  texts: [
                    {
                      type: "text",
                      text: "多城市：2个接送地点和2个集合点",
                    },
                  ],
                },
              },
            ],
            track_info: {
              type: "ItineraryDeparture",
              extra: {
                DepartureType: "both",
              },
            },
            details: {
              "title": "###### Departure details",
              "tabs": [
                {
                  "tab_name": "Pick-up",
                  "sections": [
                    {
                      "section_name": "",
                      "components": [
                        {
                          "type": "title_icons",
                          "data": {
                            "data_list": [
                              {
                                "title_name": "Pick-up time range: 10:00 - 11:00 /15:00 - 18:58 ",
                                "star": 0,
                                "icons": [],
                                "copy_icon": ""
                              }
                            ],
                            "has_underline": false
                          }
                        }
                      ]
                    },
                    {
                      "section_name": "Free pick-up",
                      "components": [
                        {
                          "type": "title_icons",
                          "data": {
                            "data_list": [
                              {
                                "title_name": "Chuo",
                                "star": 0,
                                "icons": [],
                                "copy_icon": ""
                              }
                            ],
                            "has_underline": true
                          }
                        }
                      ]
                    },
                    {
                      "section_name": "Pick-up for a fee",
                      "components": [
                        {
                          "type": "title_icons",
                          "data": {
                            "data_list": [
                              {
                                "title_name": "Minami",
                                "star": 0,
                                "icons": [
                                  {
                                    "text": "+12 JPY ",
                                    "icon": ""
                                  }
                                ],
                                "copy_icon": ""
                              }
                            ],
                            "has_underline": true
                          }
                        },
                        {
                          "type": "texts",
                          "data": {
                            "texts": [
                              {
                                "type": "dark",
                                "text": "* Pay directly to the tour operator during the tour"
                              }
                            ]
                          }
                        }
                      ]
                    },
                    {
                      "section_name": "",
                      "components": [
                        {
                          "type": "imgs",
                          "data": {
                            "imgs": [
                              "https://res.klook.com/image/upload/v1748667070/activities/otronyywrlfddabryo6o.jpg"
                            ],
                            "poi_identify_id": ""
                          }
                        },
                        {
                          "type": "texts",
                          "data": {
                            "texts": [
                              {
                                "type": "dark",
                                "text": "test 123"
                              }
                            ]
                          }
                        }
                      ]
                    },
                    {
                      "section_name": "",
                      "components": [
                        {
                          "type": "reminder",
                          "data": {
                            "title_name": "Reminder",
                            "texts": [
                              "Please arrive at the location 3 mins before the departure time",
                              "Enter your hotel name and address at the checkout page",
                              "This is a shared transfer and pick-up could be early or late"
                            ]
                          }
                        }
                      ]
                    }
                  ],
                  "track_info": {
                    "type": "ItineraryDeparture",
                    "extra": {
                      "DepartureType": "pickup"
                    }
                  }
                },
                {
                  "tab_name": "Meet-up",
                  "sections": [
                    {
                      "section_name": "Point 1",
                      "components": [
                        {
                          "type": "title_icons",
                          "data": {
                            "data_list": [
                              {
                                "title_name": "test Nickname - Hokkaido ",
                                "star": 0,
                                "icons": null,
                                "copy_icon": "https://res.klook.com/image/upload/v1734592581/UED_new/TnA/TnA_ToursRevamp_2412/___KIcon_KIcons.icon_copy_outlined_size__16_colors__colorScheme.colorTextPrimary.svg"
                              }
                            ],
                            "has_underline": false
                          }
                        },
                        {
                          "type": "session_block",
                          "data": {
                              "title": "title session block",
                              "texts": [
                                  {
                                      "type": "desc",
                                      "text": "This point will departure at:"
                                  }
                              ],
                              "departure_times": [
                                  "8:00-17:00",
                                              "8:00",
                                              "8:00-17:00",
                                              "8:00",
                                              "8:00-17:00",
                                              "8:00",
                                              "8:00-17:00"
                              ],
                              "session_table": {
                                  "title": "session_table",
                                  "desc": "",
                                  "table": [
                                      {
                                          "type": "desc",
                                          "values": [
                                              "Session",
                                              "Meet-up time"
                                          ]
                                      },
                                      {
                                          "type": "text",
                                          "values": [
                                              "8:00-17:00",
                                              "8:00",
                                              "8:00-17:00",
                                              "8:00",
                                              "8:00-17:00",
                                              "8:00",
                                              "8:00-17:00",
                                              "8:00"
                                          ]
                                      }
                                  ]
                              }
                          }
                      },
                        {
                          "type": "texts",
                          "data": {
                            "texts": [
                              {
                                "type": "dark",
                                "text": "09:00 / 15:00"
                              }
                            ]
                          }
                        },
                        {
                          "type": "imgs",
                          "data": {
                            "imgs": [
                              "https://res.klook.com/image/upload/v1735787681/activities/gb494qzhix4rvh2zjxzo.jpg",
                              "https://res.klook.com/image/upload/v1735787681/activities/bfw3wosnw9o1mhbdloah.jpg",
                              "https://res.klook.com/image/upload/v1735787681/activities/zbsgjdpfe6eriyp7cfu9.jpg"
                            ],
                            "poi_identify_id": ""
                          }
                        },
                        {
                          "type": "texts",
                          "data": {
                            "texts": [
                              {
                                "type": "dark",
                                "text": "meet up - describe today see more test abd"
                              }
                            ]
                          }
                        }
                      ]
                    },
                    {
                      "section_name": "",
                      "components": [
                        {
                          "type": "reminder",
                          "data": {
                            "title_name": "Reminder",
                            "texts": [
                              "Please arrive at the location 3 mins before the departure time",
                              "Enter your hotel name and address at the checkout page",
                              "This is a shared transfer and pick-up could be early or late"
                            ]
                          }
                        }
                      ]
                    }
                  ],
                  "track_info": {
                    "type": "ItineraryDeparture",
                    "extra": {
                      "DepartureType": "meetup"
                    }
                  }
                }
              ]
            },
            group_desc: null,
            title_map_data: null,
          },
          {
            group_id: 161,
            group_key: "attraction",
            left_icon:
              "https://res.klook.com/image/upload/v1733467621/UED_new/TnA/TnA_ToursRevamp_2412/icon_itinerary_attraction.png",
            // titles: ["北京艺术博物馆"],
            title_components: [
              {
                  "type": "front_title_with_tag",
                  "data": {
                      "tags": [
                          {
                              "text_color": "red",
                              "border_color": "blue",
                              "text": "green"
                          }
                      ],
                      "title": "##### title_components - front_title_with_tag"
                  }
              },
              {
                type: "color_text",
                data: {
                  color_texts: [
                    {
                      text: "@ From {date}, will visit the alternative attraction.",
                      text_map: {
                        "{date}": {
                          text: "Mar 3 - Mar 15",
                          color: "#212121",
                          style: "bold",
                        },
                      },
                    },
                  ],
                },
              },
              {
                type: "icons",
                data: {
                  icons: [
                    {
                      text: "行程经过",
                      icon: "https://res.klook.com/image/upload/v1734593387/UED_new/TnA/TnA_ToursRevamp_2412/___KIcon_KIcons.icon_map_outlined_size__12_colors__colorScheme.colorTextPrimary.svg",
                    },
                  ],
                },
              },
            ],
            is_title_bold: true,
            background_color: "#F1EEFA",
            card_components: [
              {
                type: "poi",
                data: {
                  background_color: "#F1EEFA",
                  poi_desc: "Sapporo Hitsujigaoka Observation Hill",
                  poi_data: [],
                  is_poi_tappable: true,
                  poi_identify_id: "28-51",
                },
              },
              {
                type: "imgs",
                data: {
                  imgs: [""],
                  poi_identify_id: "22-41",
                },
              },
              {
                type: "divider",
                data: {
                  text: "Or",
                  text_color: "",
                  line_color: "",
                },
              },
              {
                type: "with_right_triangle_title",
                data: {
                  text: "5th Station of Mt. Fuji",
                  with_right_triangle: true,
                  detail_index: 0,
                  detail_components: [
                    {
                      type: "tips",
                      data: {
                        text: "# tttipp From Aug 26 - Nov 30 will visit the alternative attraction.",
                        icon: "https://res.klook.com/image/upload/v1734593387/UED_new/TnA/TnA_ToursRevamp_2412/___KIcon_KIcons.icon_map_outlined_size__12_colors__colorScheme.colorTextPrimary.svg",
                        background_color: "",
                      },
                    },
                    {
                      type: "title_icons",
                      data: {
                        data_list: [
                          {
                            title_name:
                              "# Gardens, Mt Fuji viewpoint, Lavender",
                            star: 0,
                            icons: [],
                            copy_icon: "",
                          },
                        ],
                        has_underline: true,
                      },
                    },
                    {
                      type: "icons",
                      data: {
                        icons: [
                          {
                            text: "4 hr(s) 3 min(s)",
                            icon: "https://res.klook.com/image/upload/v1734593387/UED_new/TnA/TnA_ToursRevamp_2412/___KIcon_KIcons.icon_map_outlined_size__12_colors__colorScheme.colorTextPrimary.svg",
                          },
                        ],
                      },
                    },
                    {
                      type: "texts",
                      data: {
                        texts: [
                          {
                            type: "desc",
                            text: "Gardens, Mt Fuji viewpoint, Lavender",
                          },
                        ],
                      },
                    },
                    {
                      type: "imgs",
                      data: {
                        imgs: [
                          "https://res.klook.com/image/upload/v1733467621/UED_new/TnA/TnA_ToursRevamp_2412/icon_itinerary_meal.png",
                          "https://res.klook.com/image/upload/v1733467621/UED_new/TnA/TnA_ToursRevamp_2412/icon_itinerary_meal.png",
                        ],
                        poi_identify_id: "22-41",
                      },
                    },
                  ],
                },
              },
              {
                type: "with_right_triangle_title",
                data: {
                  text: "5-111th Station of Mt. Fuji",
                  with_right_triangle: true,
                  detail_index: 0,
                  detail_components: [
                    {
                      type: "tips",
                      data: {
                        text: "# Tips !!! From Aug 26 - Nov 30 will visit the alternative attraction.",
                        icon: "https://res.klook.com/image/upload/v1734593387/UED_new/TnA/TnA_ToursRevamp_2412/___KIcon_KIcons.icon_map_outlined_size__12_colors__colorScheme.colorTextPrimary.svg",
                        background_color: "",
                      },
                    },
                    {
                      type: "title_icons",
                      data: {
                        data_list: [
                          {
                            title_name:
                              "# Gardens, Mt Fuji viewpoint, Lavender",
                            star: 0,
                            icons: [],
                            copy_icon: "",
                          },
                        ],
                        has_underline: true,
                      },
                    },
                    {
                      type: "icons",
                      data: {
                        icons: [
                          {
                            text: "4 hr(s) 3 min(s)",
                            icon: "https://res.klook.com/image/upload/v1734593387/UED_new/TnA/TnA_ToursRevamp_2412/___KIcon_KIcons.icon_map_outlined_size__12_colors__colorScheme.colorTextPrimary.svg",
                          },
                        ],
                      },
                    },
                    {
                      type: "texts",
                      data: {
                        texts: [
                          {
                            type: "desc",
                            text: "Gardens, Mt Fuji viewpoint, Lavender",
                          },
                        ],
                      },
                    },
                    {
                      type: "imgs",
                      data: {
                        imgs: [
                          "https://res.klook.com/image/upload/v1733467621/UED_new/TnA/TnA_ToursRevamp_2412/icon_itinerary_meal.png",
                          "https://res.klook.com/image/upload/v1733467621/UED_new/TnA/TnA_ToursRevamp_2412/icon_itinerary_meal.png",
                        ],
                        poi_identify_id: "22-41",
                      },
                    },
                  ],
                },
              },
              {
                type: "tips",
                data: {
                  text: "# From Aug 26 - Nov 30 will visit the alternative attraction.",
                  icon: "",
                  background_color: "",
                },
              },
              {
                type: "title_icons",
                data: {
                  data_list: [
                    {
                      title_name:
                        "# Gardens, Mt Fuji viewpoint, Lavender",
                      star: 0,
                      icons: [],
                      copy_icon: "",
                    },
                  ],
                  has_underline: true,
                },
              },
              {
                type: "icons",
                data: {
                  icons: [
                    {
                      text: "4 hr(s) 3 min(s)",
                      icon: "https://res.klook.com/image/upload/v1734593387/UED_new/TnA/TnA_ToursRevamp_2412/___KIcon_KIcons.icon_map_outlined_size__12_colors__colorScheme.colorTextPrimary.svg",
                    },
                  ],
                },
              },
              {
                type: "texts",
                data: {
                  texts: [
                    {
                      type: "desc",
                      text: "Gardens, Mt Fuji viewpoint, Lavender",
                    },
                  ],
                },
              },
              {
                type: "imgs",
                data: {
                  imgs: [
                    "https://res.klook.com/image/upload/v1733467621/UED_new/TnA/TnA_ToursRevamp_2412/icon_itinerary_meal.png",
                    "https://res.klook.com/image/upload/v1733467621/UED_new/TnA/TnA_ToursRevamp_2412/icon_itinerary_meal.png",
                  ],
                  poi_identify_id: "22-41",
                },
              },
              {
                type: "texts",
                data: {
                  texts: [
                    {
                      type: "desc",
                      text: "# From **Aug 26 - Nov 30** will visit the alternative attraction.",
                    },
                  ],
                },
              },
            ],
            track_info: {
              type: "ItineraryAttraction",
              extra: {
                AttractionName: "北京艺术博物馆",
              },
            },
            details: {
              title: "景点详情",
              tabs: [
                {
                  tab_name: "201642",
                  sections: [
                    {
                      section_name: "",
                      components: [
                        {
                          type: "title_icons",
                          data: {
                            data_list: [
                              {
                                title_name: "北京艺术博物馆",
                                star: 0,
                                icons: [
                                  {
                                    text: "",
                                    icon: "",
                                  },
                                ],
                                copy_icon: "",
                              },
                            ],
                            has_underline: false,
                          },
                        },
                        {
                          type: "poi",
                          data: {
                            background_color: "#F1EEFA",
                            poi_desc: "北京艺术博物馆",
                            poi_data: [
                              {
                                title: "北京艺术博物馆",
                                title_icon: "",
                                map: {
                                  location: "39.945166,116.304159",
                                  google_place_id:
                                    "ChIJaW8dUStS8DURyx2hgfiDrqI",
                                  city_name: "",
                                  address_desc:
                                    "北京艺术博物馆, 北京, 北京 (及周边地区), 中国",
                                  name: "北京艺术博物馆",
                                },
                                group_key: "",
                                attr_id: 0,
                                poi_data_id: "161-244",
                              },
                            ],
                            is_poi_tappable: true,
                            poi_identify_id: "161-244",
                          },
                        },
                      ],
                    },
                  ],
                  track_info: {
                    type: "",
                    extra: null,
                  },
                },
              ],
            },
            group_desc: [],
            title_map_data: {
              location: "39.945166,116.304159",
              google_place_id: "ChIJaW8dUStS8DURyx2hgfiDrqI",
              city_name: "",
              address_desc: "北京艺术博物馆, 北京, 北京 (及周边地区), 中国",
              name: "北京艺术博物馆",
            },
          },
          {
            group_id: 144,
            group_key: "attraction",
            left_icon:
              "https://res.klook.com/image/upload/v1733467621/UED_new/TnA/TnA_ToursRevamp_2412/icon_itinerary_attraction.png",
            titles: [
              "王府井古人类文化遗址博物馆坐落在北京东方广场地下发掘出的最早的古人类遗址，再现2万5千年前斑鹿跳跃、鸵鸟欢腾、望山听水、狩猎生息景象，展示“北京人”狩猎、烧火、制造工具的痕迹在博物馆300多平方米的展厅里，将陈列在原址发掘出土的石砧、石锤、石核、石片",
            ],
            title_components: [
              {
                type: "icons",
                data: {
                  icons: [
                    {
                      text: "1小时",
                      icon: "https://res.klook.com/image/upload/v1735968462/UED_new/TnA/TnA_ToursRevamp_2412/icon_time.svg",
                    },
                    {
                      text: "导览",
                      icon: "https://res.klook.com/image/upload/v1736143556/UED_new/TnA/TnA_ToursRevamp_2412/icon_guide.svg",
                    },
                    {
                      text: "需门票（费用包含或现场支付）",
                      icon: "https://res.klook.com/image/upload/v1733887435/UED_new/TnA/TnA_ToursRevamp_2412/icon_itinerary_ticket.png",
                    },
                  ],
                },
              },
            ],
            is_title_bold: true,
            background_color: "#F1EEFA",
            card_components: [
              {
                type: "texts",
                data: {
                  texts: [
                    {
                      type: "desc",
                      text: "传统服饰体验",
                    },
                  ],
                },
              },
              {
                type: "imgs",
                data: {
                  imgs: [
                    "https://res.klook.com/image/upload/v1735982876/activities/rsybds7ufxxgkue9xwrk.jpg",
                    "https://res.klook.com/image/upload/v1735982875/activities/gf7yi8viirw1ieuojpla.jpg",
                    "https://res.klook.com/image/upload/v1735982876/activities/cgbuxxlnbwwauqihv6cl.jpg",
                    "https://res.klook.com/image/upload/v1735982875/activities/xptoswoayywiyhqk1c4x.jpg",
                    "https://res.klook.com/image/upload/v1735982877/activities/jtcwkas3sqz9txloyoc8.jpg",
                  ],
                  poi_identify_id: "144-220",
                },
              },
            ],
            track_info: {
              type: "ItineraryAttraction",
              extra: {
                AttractionName:
                  "王府井古人类文化遗址博物馆坐落在北京东方广场地下发掘出的最早的古人类遗址，再现2万5千年前斑鹿跳跃、鸵鸟欢腾、望山听水、狩猎生息景象，展示“北京人”狩猎、烧火、制造工具的痕迹在博物馆300多平方米的展厅里，将陈列在原址发掘出土的石砧、石锤、石核、石片",
              },
            },
            details: {
              title: "景点详情",
              tabs: [
                {
                  tab_name: "201642",
                  sections: [
                    {
                      section_name: "",
                      components: [
                        {
                          type: "icons",
                          data: {
                            icons: [
                              {
                                text: "1小时",
                                icon: "https://res.klook.com/image/upload/v1735968462/UED_new/TnA/TnA_ToursRevamp_2412/icon_time.svg",
                              },
                              {
                                text: "导览",
                                icon: "https://res.klook.com/image/upload/v1736143556/UED_new/TnA/TnA_ToursRevamp_2412/icon_guide.svg",
                              },
                              {
                                text: "需门票（费用包含或现场支付）",
                                icon: "https://res.klook.com/image/upload/v1733887435/UED_new/TnA/TnA_ToursRevamp_2412/icon_itinerary_ticket.png",
                              },
                            ],
                          },
                        },
                        {
                          type: "title_icons",
                          data: {
                            data_list: [
                              {
                                title_name:
                                  "王府井古人类文化遗址博物馆坐落在北京东方广场地下发掘出的最早的古人类遗址，再现2万5千年前斑鹿跳跃、鸵鸟欢腾、望山听水、狩猎生息景象，展示“北京人”狩猎、烧火、制造工具的痕迹在博物馆300多平方米的展厅里，将陈列在原址发掘出土的石砧、石锤、石核、石片",
                                star: 0,
                                icons: [
                                  {
                                    text: "",
                                    icon: "",
                                  },
                                ],
                                copy_icon: "",
                              },
                            ],
                            has_underline: false,
                          },
                        },
                        {
                          type: "poi",
                          data: {
                            background_color: "#F1EEFA",
                            poi_desc:
                              "王府井古人类文化遗址博物馆坐落在北京东方广场地下发掘出的最早的古人类遗址，再现2万5千年前斑鹿跳跃、鸵鸟欢腾、望山听水、狩猎生息景象，展示“北京人”狩猎、烧火、制造工具的痕迹在博物馆300多平方米的展厅里，将陈列在原址发掘出土的石砧、石锤、石核、石片",
                            poi_data: [
                              {
                                title:
                                  "王府井古人类文化遗址博物馆坐落在北京东方广场地下发掘出的最早的古人类遗址，再现2万5千年前斑鹿跳跃、鸵鸟欢腾、望山听水、狩猎生息景象，展示“北京人”狩猎、烧火、制造工具的痕迹在博物馆300多平方米的展厅里，将陈列在原址发掘出土的石砧、石锤、石核、石片",
                                title_icon: "",
                                map: {
                                  location: "39.909453,116.412622",
                                  google_place_id:
                                    "ChIJs2LlcMlS8DURdyeSTlfHhVA",
                                  city_name: "",
                                  address_desc:
                                    "王府井古人类文化遗址博物馆, 北京, 北京 (及周边地区), 中国",
                                  name: "王府井古人类文化遗址博物馆坐落在北京东方广场地下发掘出的最早的古人类遗址，再现2万5千年前斑鹿跳跃、鸵鸟欢腾、望山听水、狩猎生息景象，展示“北京人”狩猎、烧火、制造工具的痕迹在博物馆300多平方米的展厅里，将陈列在原址发掘出土的石砧、石锤、石核、石片",
                                },
                                group_key: "",
                                attr_id: 0,
                                poi_data_id: "144-220",
                              },
                            ],
                            is_poi_tappable: true,
                            poi_identify_id: "144-220",
                          },
                        },
                        {
                          type: "imgs",
                          data: {
                            imgs: [
                              "https://res.klook.com/image/upload/v1735982876/activities/rsybds7ufxxgkue9xwrk.jpg",
                              "https://res.klook.com/image/upload/v1735982875/activities/gf7yi8viirw1ieuojpla.jpg",
                              "https://res.klook.com/image/upload/v1735982876/activities/cgbuxxlnbwwauqihv6cl.jpg",
                              "https://res.klook.com/image/upload/v1735982875/activities/xptoswoayywiyhqk1c4x.jpg",
                              "https://res.klook.com/image/upload/v1735982877/activities/jtcwkas3sqz9txloyoc8.jpg",
                            ],
                            poi_identify_id: "144-220",
                          },
                        },
                      ],
                    },
                  ],
                  track_info: {
                    type: "",
                    extra: null,
                  },
                },
              ],
            },
            group_desc: [
              "王府井古人类文化遗址博物馆坐落在北京东方广场地下发掘出的最早的古人类遗址，再现2万5千年前斑鹿跳跃、鸵鸟欢腾、望山听水、狩猎生息景象，展示“北京人”狩猎、烧火、制造工具的痕迹在博物馆300多平方米的展厅里，将陈列在原址发掘出土的石砧、石锤、石核、石片",
            ],
            title_map_data: {
              location: "39.909453,116.412622",
              google_place_id: "ChIJs2LlcMlS8DURdyeSTlfHhVA",
              city_name: "",
              address_desc:
                "王府井古人类文化遗址博物馆, 北京, 北京 (及周边地区), 中国",
              name: "王府井古人类文化遗址博物馆坐落在北京东方广场地下发掘出的最早的古人类遗址，再现2万5千年前斑鹿跳跃、鸵鸟欢腾、望山听水、狩猎生息景象，展示“北京人”狩猎、烧火、制造工具的痕迹在博物馆300多平方米的展厅里，将陈列在原址发掘出土的石砧、石锤、石核、石片",
            },
          },
          {
            group_id: 162,
            group_key: "attraction",
            left_icon:
              "https://res.klook.com/image/upload/v1733467621/UED_new/TnA/TnA_ToursRevamp_2412/icon_itinerary_attraction.png",
            titles: ["北京环球影城"],
            title_components: [
              {
                type: "icons",
                data: {
                  icons: [
                    {
                      text: "2小时",
                      icon: "https://res.klook.com/image/upload/v1735968462/UED_new/TnA/TnA_ToursRevamp_2412/icon_time.svg",
                    },
                    {
                      text: "需门票（费用包含或现场支付）",
                      icon: "https://res.klook.com/image/upload/v1733887435/UED_new/TnA/TnA_ToursRevamp_2412/icon_itinerary_ticket.png",
                    },
                  ],
                },
              },
            ],
            is_title_bold: true,
            background_color: "#F1EEFA",
            card_components: [
              {
                type: "imgs",
                data: {
                  imgs: [
                    "https://res.klook.com/image/upload/v1736157137/activities/hwc1cjbbuxiwzbiyukda.jpg",
                  ],
                  poi_identify_id: "162-245",
                },
              },
            ],
            track_info: {
              type: "ItineraryAttraction",
              extra: {
                AttractionName: "北京环球影城",
              },
            },
            details: {
              title: "景点详情",
              tabs: [
                {
                  tab_name: "201642",
                  sections: [
                    {
                      section_name: "",
                      components: [
                        {
                          type: "icons",
                          data: {
                            icons: [
                              {
                                text: "2小时",
                                icon: "https://res.klook.com/image/upload/v1735968462/UED_new/TnA/TnA_ToursRevamp_2412/icon_time.svg",
                              },
                              {
                                text: "需门票（费用包含或现场支付）",
                                icon: "https://res.klook.com/image/upload/v1733887435/UED_new/TnA/TnA_ToursRevamp_2412/icon_itinerary_ticket.png",
                              },
                            ],
                          },
                        },
                        {
                          type: "title_icons",
                          data: {
                            data_list: [
                              {
                                title_name: "北京环球影城",
                                star: 0,
                                icons: [
                                  {
                                    text: "",
                                    icon: "",
                                  },
                                ],
                                copy_icon: "",
                              },
                            ],
                            has_underline: false,
                          },
                        },
                        {
                          type: "poi",
                          data: {
                            background_color: "#F1EEFA",
                            poi_desc: "北京环球影城",
                            poi_data: [
                              {
                                title: "北京环球影城",
                                title_icon: "",
                                map: {
                                  location: "39.855117,116.678625",
                                  google_place_id:
                                    "ChIJKTlVaYem8TURd0RL30n-uy4",
                                  city_name: "",
                                  address_desc:
                                    "北京环球影城, 通州, 北京 (及周边地区), 中国",
                                  name: "北京环球影城",
                                },
                                group_key: "",
                                attr_id: 0,
                                poi_data_id: "162-245",
                              },
                            ],
                            is_poi_tappable: true,
                            poi_identify_id: "162-245",
                          },
                        },
                        {
                          type: "imgs",
                          data: {
                            imgs: [
                              "https://res.klook.com/image/upload/v1736157137/activities/hwc1cjbbuxiwzbiyukda.jpg",
                            ],
                            poi_identify_id: "162-245",
                          },
                        },
                      ],
                    },
                  ],
                  track_info: {
                    type: "",
                    extra: null,
                  },
                },
              ],
            },
            group_desc: ["北京环球影城"],
            title_map_data: {
              location: "39.855117,116.678625",
              google_place_id: "ChIJKTlVaYem8TURd0RL30n-uy4",
              city_name: "",
              address_desc: "北京环球影城, 通州, 北京 (及周边地区), 中国",
              name: "北京环球影城",
            },
          },
          {
            group_id: 151,
            group_key: "attraction",
            left_icon:
              "https://res.klook.com/image/upload/v1733467621/UED_new/TnA/TnA_ToursRevamp_2412/icon_itinerary_attraction.png",
            titles: ["颐和宫等"],
            title_components: [
              {
                type: "icons",
                data: {
                  icons: [
                    {
                      text: "行程经过",
                      icon: "https://res.klook.com/image/upload/v1734593387/UED_new/TnA/TnA_ToursRevamp_2412/___KIcon_KIcons.icon_map_outlined_size__12_colors__colorScheme.colorTextPrimary.svg",
                    },
                  ],
                },
              },
            ],
            is_title_bold: true,
            background_color: "#F1EEFA",
            card_components: [
              {
                type: "poi",
                data: {
                  background_color: "#F1EEFA",
                  poi_desc: "颐和宫",
                  poi_data: [
                    {
                      title: "颐和宫",
                      title_icon: "",
                      map: {
                        location: "39.989845,116.414499",
                        google_place_id: "ChIJyT6dzs1U8DURBTC7BFQ5scI",
                        city_name: "",
                        address_desc: "",
                        name: "颐和宫",
                      },
                      group_key: "",
                      attr_id: 0,
                      poi_data_id: "151-228",
                    },
                  ],
                  is_poi_tappable: true,
                  poi_identify_id: "151-228",
                },
              },
              {
                type: "texts",
                data: {
                  texts: [
                    {
                      type: "desc",
                      text: "航行体验",
                    },
                  ],
                },
              },
              {
                type: "poi",
                data: {
                  background_color: "#F1EEFA",
                  poi_desc: "故宫博物院",
                  poi_data: [
                    {
                      title: "故宫博物院",
                      title_icon: "",
                      map: {
                        location: "39.916308,116.397109",
                        google_place_id: "ChIJq4HNm91S8DURZGAQm-3qQ94",
                        city_name: "",
                        address_desc:
                          "故宫博物院, 北京, 北京 (及周边地区), 中国",
                        name: "故宫博物院",
                      },
                      group_key: "",
                      attr_id: 0,
                      poi_data_id: "151-229",
                    },
                  ],
                  is_poi_tappable: true,
                  poi_identify_id: "151-229",
                },
              },
              {
                type: "texts",
                data: {
                  texts: [
                    {
                      type: "desc",
                      text: "冲沙",
                    },
                  ],
                },
              },
            ],
            track_info: {
              type: "ItineraryAttraction",
              extra: null,
            },
            details: {
              title: "景点详情",
              tabs: [
                {
                  tab_name: "201642",
                  sections: [
                    {
                      section_name: "",
                      components: [
                        {
                          type: "title_icons",
                          data: {
                            data_list: [
                              {
                                title_name: "颐和宫",
                                star: 0,
                                icons: [
                                  {
                                    text: "",
                                    icon: "",
                                  },
                                ],
                                copy_icon: "",
                              },
                            ],
                            has_underline: false,
                          },
                        },
                        {
                          type: "poi",
                          data: {
                            background_color: "#F1EEFA",
                            poi_desc: "颐和宫",
                            poi_data: [
                              {
                                title: "颐和宫",
                                title_icon: "",
                                map: {
                                  location: "39.989845,116.414499",
                                  google_place_id:
                                    "ChIJyT6dzs1U8DURBTC7BFQ5scI",
                                  city_name: "",
                                  address_desc: "",
                                  name: "颐和宫",
                                },
                                group_key: "",
                                attr_id: 0,
                                poi_data_id: "151-228",
                              },
                            ],
                            is_poi_tappable: true,
                            poi_identify_id: "151-228",
                          },
                        },
                        {
                          type: "title_icons",
                          data: {
                            data_list: [
                              {
                                title_name: "故宫博物院",
                                star: 0,
                                icons: [
                                  {
                                    text: "",
                                    icon: "",
                                  },
                                ],
                                copy_icon: "",
                              },
                            ],
                            has_underline: false,
                          },
                        },
                        {
                          type: "poi",
                          data: {
                            background_color: "#F1EEFA",
                            poi_desc: "故宫博物院",
                            poi_data: [
                              {
                                title: "故宫博物院",
                                title_icon: "",
                                map: {
                                  location: "39.916308,116.397109",
                                  google_place_id:
                                    "ChIJq4HNm91S8DURZGAQm-3qQ94",
                                  city_name: "",
                                  address_desc:
                                    "故宫博物院, 北京, 北京 (及周边地区), 中国",
                                  name: "故宫博物院",
                                },
                                group_key: "",
                                attr_id: 0,
                                poi_data_id: "151-229",
                              },
                            ],
                            is_poi_tappable: true,
                            poi_identify_id: "151-229",
                          },
                        },
                      ],
                    },
                  ],
                  track_info: {
                    type: "",
                    extra: null,
                  },
                },
              ],
            },
            group_desc: [],
            title_map_data: null,
          },
          {
            group_id: 163,
            group_key: "attraction",
            left_icon:
              "https://res.klook.com/image/upload/v1733467621/UED_new/TnA/TnA_ToursRevamp_2412/icon_itinerary_attraction.png",
            titles: [
              "北京大观园位于北京市西城区南菜园街12号，为一个再现中国古典文学名著《红楼梦》中“大观园”景观的仿古园林，是以影视拍摄服务为主，兼具观光旅游、文化娱乐、休闲度假等功能的综合性旅游区。 其占地面积11.06万平方米，建筑面积2.54万平方米，水域面积1.6万平方米等",
            ],
            title_components: [
              {
                type: "icons",
                data: {
                  icons: [
                    {
                      text: "2小时",
                      icon: "https://res.klook.com/image/upload/v1735968462/UED_new/TnA/TnA_ToursRevamp_2412/icon_time.svg",
                    },
                    {
                      text: "包含门票",
                      icon: "https://res.klook.com/image/upload/v1733887435/UED_new/TnA/TnA_ToursRevamp_2412/icon_itinerary_ticket.png",
                    },
                  ],
                },
              },
            ],
            is_title_bold: true,
            background_color: "#F1EEFA",
            card_components: [
              {
                type: "poi",
                data: {
                  background_color: "#F1EEFA",
                  poi_desc:
                    "北京大观园位于北京市西城区南菜园街12号，为一个再现中国古典文学名著《红楼梦》中“大观园”景观的仿古园林，是以影视拍摄服务为主，兼具观光旅游、文化娱乐、休闲度假等功能的综合性旅游区。 其占地面积11.06万平方米，建筑面积2.54万平方米，水域面积1.6万平方米",
                  poi_data: [
                    {
                      title:
                        "北京大观园位于北京市西城区南菜园街12号，为一个再现中国古典文学名著《红楼梦》中“大观园”景观的仿古园林，是以影视拍摄服务为主，兼具观光旅游、文化娱乐、休闲度假等功能的综合性旅游区。 其占地面积11.06万平方米，建筑面积2.54万平方米，水域面积1.6万平方米",
                      title_icon: "",
                      map: {
                        location: "39.871101,116.356195",
                        google_place_id: "ChIJIWeaNrdN8DUR4dZzquclVFk",
                        city_name: "",
                        address_desc:
                          "中国北京市西城区南菜园街12号 邮政编码: 100054",
                        name: "北京大观园位于北京市西城区南菜园街12号，为一个再现中国古典文学名著《红楼梦》中“大观园”景观的仿古园林，是以影视拍摄服务为主，兼具观光旅游、文化娱乐、休闲度假等功能的综合性旅游区。 其占地面积11.06万平方米，建筑面积2.54万平方米，水域面积1.6万平方米",
                      },
                      group_key: "",
                      attr_id: 0,
                      poi_data_id: "163-246",
                    },
                  ],
                  is_poi_tappable: true,
                  poi_identify_id: "163-246",
                },
              },
              {
                type: "texts",
                data: {
                  texts: [
                    {
                      type: "desc",
                      text: "陶艺",
                    },
                  ],
                },
              },
              {
                type: "imgs",
                data: {
                  imgs: [
                    "https://res.klook.com/image/upload/v1736157226/activities/juqniznmhfjwdsk8aoun.jpg",
                    "https://res.klook.com/image/upload/v1736157227/activities/d5slzztgfp4q0vi22lct.jpg",
                    "https://res.klook.com/image/upload/v1736157228/activities/a7uiiaqjcpj55ght1xp0.jpg",
                    "https://res.klook.com/image/upload/v1736157231/activities/frhnzihwdxed3qasdltp.jpg",
                    "https://res.klook.com/image/upload/v1736157229/activities/fdey36cpebduiznf6nxg.jpg",
                  ],
                  poi_identify_id: "163-246",
                },
              },
              {
                type: "poi",
                data: {
                  background_color: "#F1EEFA",
                  poi_desc: "北京世界公园",
                  poi_data: [
                    {
                      title: "北京世界公园",
                      title_icon: "",
                      map: {
                        location: "39.810743,116.28744",
                        google_place_id: "ChIJJ6QzBQVJ8DURn8CC50EdEBY",
                        city_name: "",
                        address_desc: "中国北京市丰台区 邮政编码: 100070",
                        name: "北京世界公园",
                      },
                      group_key: "",
                      attr_id: 0,
                      poi_data_id: "163-247",
                    },
                  ],
                  is_poi_tappable: true,
                  poi_identify_id: "163-247",
                },
              },
              {
                type: "imgs",
                data: {
                  imgs: [
                    "https://res.klook.com/image/upload/v1736157279/activities/bennivztcwzjiyrcchtu.jpg",
                  ],
                  poi_identify_id: "163-247",
                },
              },
              {
                type: "poi",
                data: {
                  background_color: "#F1EEFA",
                  poi_desc: "北京鲁迅博物馆",
                  poi_data: [
                    {
                      title: "北京鲁迅博物馆",
                      title_icon: "",
                      map: {
                        location: "39.925672,116.35876",
                        google_place_id: "ChIJcSxh6ERS8DURFdDRR0ueVX0",
                        city_name: "",
                        address_desc:
                          "鲁迅纪念馆, 北京, 北京 (及周边地区), 中国",
                        name: "北京鲁迅博物馆",
                      },
                      group_key: "",
                      attr_id: 0,
                      poi_data_id: "163-248",
                    },
                  ],
                  is_poi_tappable: true,
                  poi_identify_id: "163-248",
                },
              },
              {
                type: "texts",
                data: {
                  texts: [
                    {
                      type: "desc",
                      text: "航行体验",
                    },
                  ],
                },
              },
              {
                type: "imgs",
                data: {
                  imgs: [
                    "https://res.klook.com/image/upload/v1736157300/activities/czjzsgb2xul5jtppo3pu.jpg",
                  ],
                  poi_identify_id: "163-248",
                },
              },
            ],
            track_info: {
              type: "ItineraryAttraction",
              extra: null,
            },
            details: {
              title: "景点详情",
              tabs: [
                {
                  tab_name: "201642",
                  sections: [
                    {
                      section_name: "",
                      components: [
                        {
                          type: "icons",
                          data: {
                            icons: [
                              {
                                text: "2小时",
                                icon: "https://res.klook.com/image/upload/v1735968462/UED_new/TnA/TnA_ToursRevamp_2412/icon_time.svg",
                              },
                              {
                                text: "包含门票",
                                icon: "https://res.klook.com/image/upload/v1733887435/UED_new/TnA/TnA_ToursRevamp_2412/icon_itinerary_ticket.png",
                              },
                            ],
                          },
                        },
                        {
                          type: "title_icons",
                          data: {
                            data_list: [
                              {
                                title_name:
                                  "北京大观园位于北京市西城区南菜园街12号，为一个再现中国古典文学名著《红楼梦》中“大观园”景观的仿古园林，是以影视拍摄服务为主，兼具观光旅游、文化娱乐、休闲度假等功能的综合性旅游区。 其占地面积11.06万平方米，建筑面积2.54万平方米，水域面积1.6万平方米",
                                star: 0,
                                icons: [
                                  {
                                    text: "",
                                    icon: "",
                                  },
                                ],
                                copy_icon: "",
                              },
                            ],
                            has_underline: false,
                          },
                        },
                        {
                          type: "poi",
                          data: {
                            background_color: "#F1EEFA",
                            poi_desc:
                              "北京大观园位于北京市西城区南菜园街12号，为一个再现中国古典文学名著《红楼梦》中“大观园”景观的仿古园林，是以影视拍摄服务为主，兼具观光旅游、文化娱乐、休闲度假等功能的综合性旅游区。 其占地面积11.06万平方米，建筑面积2.54万平方米，水域面积1.6万平方米",
                            poi_data: [
                              {
                                title:
                                  "北京大观园位于北京市西城区南菜园街12号，为一个再现中国古典文学名著《红楼梦》中“大观园”景观的仿古园林，是以影视拍摄服务为主，兼具观光旅游、文化娱乐、休闲度假等功能的综合性旅游区。 其占地面积11.06万平方米，建筑面积2.54万平方米，水域面积1.6万平方米",
                                title_icon: "",
                                map: {
                                  location: "39.871101,116.356195",
                                  google_place_id:
                                    "ChIJIWeaNrdN8DUR4dZzquclVFk",
                                  city_name: "",
                                  address_desc:
                                    "中国北京市西城区南菜园街12号 邮政编码: 100054",
                                  name: "北京大观园位于北京市西城区南菜园街12号，为一个再现中国古典文学名著《红楼梦》中“大观园”景观的仿古园林，是以影视拍摄服务为主，兼具观光旅游、文化娱乐、休闲度假等功能的综合性旅游区。 其占地面积11.06万平方米，建筑面积2.54万平方米，水域面积1.6万平方米",
                                },
                                group_key: "",
                                attr_id: 0,
                                poi_data_id: "163-246",
                              },
                            ],
                            is_poi_tappable: true,
                            poi_identify_id: "163-246",
                          },
                        },
                        {
                          type: "imgs",
                          data: {
                            imgs: [
                              "https://res.klook.com/image/upload/v1736157226/activities/juqniznmhfjwdsk8aoun.jpg",
                              "https://res.klook.com/image/upload/v1736157227/activities/d5slzztgfp4q0vi22lct.jpg",
                              "https://res.klook.com/image/upload/v1736157228/activities/a7uiiaqjcpj55ght1xp0.jpg",
                              "https://res.klook.com/image/upload/v1736157231/activities/frhnzihwdxed3qasdltp.jpg",
                              "https://res.klook.com/image/upload/v1736157229/activities/fdey36cpebduiznf6nxg.jpg",
                            ],
                            poi_identify_id: "163-246",
                          },
                        },
                        {
                          type: "title_icons",
                          data: {
                            data_list: [
                              {
                                title_name: "北京世界公园",
                                star: 0,
                                icons: [
                                  {
                                    text: "",
                                    icon: "",
                                  },
                                ],
                                copy_icon: "",
                              },
                            ],
                            has_underline: false,
                          },
                        },
                        {
                          type: "poi",
                          data: {
                            background_color: "#F1EEFA",
                            poi_desc: "北京世界公园",
                            poi_data: [
                              {
                                title: "北京世界公园",
                                title_icon: "",
                                map: {
                                  location: "39.810743,116.28744",
                                  google_place_id:
                                    "ChIJJ6QzBQVJ8DURn8CC50EdEBY",
                                  city_name: "",
                                  address_desc:
                                    "中国北京市丰台区 邮政编码: 100070",
                                  name: "北京世界公园",
                                },
                                group_key: "",
                                attr_id: 0,
                                poi_data_id: "163-247",
                              },
                            ],
                            is_poi_tappable: true,
                            poi_identify_id: "163-247",
                          },
                        },
                        {
                          type: "imgs",
                          data: {
                            imgs: [
                              "https://res.klook.com/image/upload/v1736157279/activities/bennivztcwzjiyrcchtu.jpg",
                            ],
                            poi_identify_id: "163-247",
                          },
                        },
                        {
                          type: "title_icons",
                          data: {
                            data_list: [
                              {
                                title_name: "北京鲁迅博物馆",
                                star: 0,
                                icons: [
                                  {
                                    text: "",
                                    icon: "",
                                  },
                                ],
                                copy_icon: "",
                              },
                            ],
                            has_underline: false,
                          },
                        },
                        {
                          type: "poi",
                          data: {
                            background_color: "#F1EEFA",
                            poi_desc: "北京鲁迅博物馆",
                            poi_data: [
                              {
                                title: "北京鲁迅博物馆",
                                title_icon: "",
                                map: {
                                  location: "39.925672,116.35876",
                                  google_place_id:
                                    "ChIJcSxh6ERS8DURFdDRR0ueVX0",
                                  city_name: "",
                                  address_desc:
                                    "鲁迅纪念馆, 北京, 北京 (及周边地区), 中国",
                                  name: "北京鲁迅博物馆",
                                },
                                group_key: "",
                                attr_id: 0,
                                poi_data_id: "163-248",
                              },
                            ],
                            is_poi_tappable: true,
                            poi_identify_id: "163-248",
                          },
                        },
                        {
                          type: "imgs",
                          data: {
                            imgs: [
                              "https://res.klook.com/image/upload/v1736157300/activities/czjzsgb2xul5jtppo3pu.jpg",
                            ],
                            poi_identify_id: "163-248",
                          },
                        },
                      ],
                    },
                  ],
                  track_info: {
                    type: "",
                    extra: null,
                  },
                },
              ],
            },
            group_desc: [],
            title_map_data: null,
          },
          {
            group_id: 165,
            group_key: "meal",
            left_icon:
              "https://res.klook.com/image/upload/v1733467621/UED_new/TnA/TnA_ToursRevamp_2412/icon_itinerary_meal.png",
            titles: ["午餐", "日本料理"],
            title_components: [],
            is_title_bold: false,
            background_color: "",
            card_components: [
              {
                type: "texts",
                data: {
                  texts: [
                    {
                      type: "desc",
                      text: "可加购（根据需求）",
                    },
                  ],
                },
              },
              {
                type: "icons",
                data: {
                  icons: [
                    {
                      text: "1小时",
                      icon: "https://res.klook.com/image/upload/v1735968462/UED_new/TnA/TnA_ToursRevamp_2412/icon_time.svg",
                    },
                  ],
                },
              },
            ],
            track_info: {
              type: "ItineraryMeal",
              extra: null,
            },
            details: null,
            group_desc: null,
            title_map_data: null,
          },
          {
            group_id: 628,
            group_key: "meal",
            left_icon:
              "https://res.klook.com/image/upload/v1733467621/UED_new/TnA/TnA_ToursRevamp_2412/icon_itinerary_meal.png",
            titles: ["晚餐", "融合料理"],
            title_components: [],
            is_title_bold: false,
            background_color: "",
            card_components: [
              {
                type: "icons",
                data: {
                  icons: [
                    {
                      text: "1小时1分钟",
                      icon: "https://res.klook.com/image/upload/v1735968462/UED_new/TnA/TnA_ToursRevamp_2412/icon_time.svg",
                    },
                  ],
                },
              },
            ],
            track_info: {
              type: "ItineraryMeal",
              extra: null,
            },
            details: {
              title: "餐饮介绍",
              tabs: [
                {
                  tab_name: "餐饮介绍",
                  sections: [
                    {
                      section_name: "",
                      components: [
                        {
                          type: "poi",
                          data: {
                            background_color: "#F1EEFA",
                            poi_desc: "Beijing Road",
                            poi_data: [
                              {
                                title: "Beijing Road",
                                title_icon: "",
                                map: {
                                  location: "37.46934,121.04317",
                                  google_place_id:
                                    "EjxCZWkgSmluZyBMdSwgUWkgWGlhIFNoaSwgWWFuIFRhaSBTaGksIFNoYW4gRG9uZyBTaGVuZywgQ2hpbmEiLiosChQKEglP_0gJBEvHNRFXgZ6W6eYgoBIUChIJUauGvj_gkDURKvrmHCEUHpw",
                                  city_name: "",
                                  address_desc:
                                    "Bei Jing Lu, Qi Xia Shi, Yan Tai Shi, Shan Dong Sheng, China",
                                  name: "Beijing Road",
                                },
                                group_key: "",
                                attr_id: 0,
                                poi_data_id: "628-748",
                              },
                            ],
                            is_poi_tappable: true,
                            poi_identify_id: "628-748",
                          },
                        },
                        {
                          type: "texts",
                          data: {
                            texts: [
                              {
                                type: "text",
                                text: "融合料理",
                              },
                            ],
                          },
                        },
                        {
                          type: "icons",
                          data: {
                            icons: [
                              {
                                text: "1小时1分钟",
                                icon: "https://res.klook.com/image/upload/v1735968462/UED_new/TnA/TnA_ToursRevamp_2412/icon_time.svg",
                              },
                            ],
                          },
                        },
                      ],
                    },
                  ],
                  track_info: {
                    type: "",
                    extra: null,
                  },
                },
              ],
            },
            group_desc: null,
            title_map_data: null,
          },
        ],
      },
      {
        day_name: "第2天",
        groups: [
          {
            group_id: 154,
            group_key: "accommodation",
            left_icon:
              "https://res.klook.com/image/upload/v1733467621/UED_new/TnA/TnA_ToursRevamp_2412/icon_itinerary_accommodation.png",
            titles: ["住宿", "酒店"],
            title_components: [],
            is_title_bold: true,
            background_color: "",
            card_components: [],
            track_info: {
              type: "ItineraryAccomodation",
              extra: null,
            },
            details: {
              title: "",
              tabs: [],
            },
            group_desc: null,
            title_map_data: null,
          },
          {
            group_id: 149,
            group_key: "accommodation",
            left_icon:
              "https://res.klook.com/image/upload/v1733467621/UED_new/TnA/TnA_ToursRevamp_2412/icon_itinerary_accommodation.png",
            titles: ["住宿", "民宿"],
            title_components: [],
            is_title_bold: true,
            background_color: "",
            card_components: [
              {
                type: "poi",
                data: {
                  background_color: "#F1EEFA",
                  poi_desc: "北京古代钱币博物馆 -交易所",
                  poi_data: [
                    {
                      title: "北京古代钱币博物馆 -交易所",
                      title_icon: "",
                      map: {
                        location: "39.91095899999999,116.411341",
                        google_place_id: "ChIJpaF2MslS8DURRskvKooTIKk",
                        city_name: "",
                        address_desc: "",
                        name: "北京古代钱币博物馆 -交易所",
                      },
                      group_key: "",
                      attr_id: 0,
                      poi_data_id: "149-226",
                    },
                  ],
                  is_poi_tappable: true,
                  poi_identify_id: "149-226",
                },
              },
            ],
            track_info: {
              type: "ItineraryAccomodation",
              extra: null,
            },
            details: {
              title: "",
              tabs: [],
            },
            group_desc: null,
            title_map_data: null,
          },
          {
            group_id: 153,
            group_key: "accommodation",
            left_icon:
              "https://res.klook.com/image/upload/v1733467621/UED_new/TnA/TnA_ToursRevamp_2412/icon_itinerary_accommodation.png",
            titles: ["住宿", "公寓"],
            title_components: [],
            is_title_bold: true,
            background_color: "",
            card_components: [
              {
                type: "poi",
                data: {
                  background_color: "#F1EEFA",
                  poi_desc: "北京兆龙青年旅舍",
                  poi_data: [
                    {
                      title: "北京兆龙青年旅舍",
                      title_icon: "",
                      map: {
                        location: "39.932657,116.445718",
                        google_place_id: "ChIJV7ikNeqs8TURGwPXNiKsUGM",
                        city_name: "",
                        address_desc: "",
                        name: "北京兆龙青年旅舍",
                      },
                      group_key: "",
                      attr_id: 0,
                      poi_data_id: "153-232",
                    },
                  ],
                  is_poi_tappable: true,
                  poi_identify_id: "153-232",
                },
              },
            ],
            track_info: {
              type: "ItineraryAccomodation",
              extra: null,
            },
            details: {
              title: "",
              tabs: [],
            },
            group_desc: null,
            title_map_data: null,
          },
          {
            group_id: 155,
            group_key: "accommodation",
            left_icon:
              "https://res.klook.com/image/upload/v1733467621/UED_new/TnA/TnA_ToursRevamp_2412/icon_itinerary_accommodation.png",
            titles: ["住宿", "度假村"],
            title_components: [],
            is_title_bold: true,
            background_color: "",
            card_components: [
              {
                type: "poi",
                data: {
                  background_color: "#F1EEFA",
                  poi_desc: "北京南山滑雪滑水度假村",
                  poi_data: [
                    {
                      title: "北京南山滑雪滑水度假村",
                      title_icon: "",
                      map: {
                        location: "40.336456,116.861855",
                        google_place_id: "ChIJ_c_sg-5G8TURYuiBQrK79H8",
                        city_name: "",
                        address_desc: "中国北京市密云区 邮政编码: 101509",
                        name: "北京南山滑雪滑水度假村",
                      },
                      group_key: "",
                      attr_id: 0,
                      poi_data_id: "155-234",
                    },
                  ],
                  is_poi_tappable: true,
                  poi_identify_id: "155-234",
                },
              },
            ],
            track_info: {
              type: "ItineraryAccomodation",
              extra: null,
            },
            details: {
              title: "",
              tabs: [
                {
                  tab_name: "住宿",
                  sections: [
                    {
                      section_name: "度假村",
                      components: [
                        {
                          type: "poi",
                          data: {
                            background_color: "#F1EEFA",
                            poi_desc: "北京南山滑雪滑水度假村",
                            poi_data: [
                              {
                                title: "北京南山滑雪滑水度假村",
                                title_icon: "",
                                map: {
                                  location: "40.336456,116.861855",
                                  google_place_id:
                                    "ChIJ_c_sg-5G8TURYuiBQrK79H8",
                                  city_name: "",
                                  address_desc:
                                    "中国北京市密云区 邮政编码: 101509",
                                  name: "北京南山滑雪滑水度假村",
                                },
                                group_key: "",
                                attr_id: 0,
                                poi_data_id: "155-234",
                              },
                            ],
                            is_poi_tappable: true,
                            poi_identify_id: "155-234",
                          },
                        },
                        {
                          type: "title_icons",
                          data: {
                            data_list: [
                              {
                                title_name: "",
                                star: 5,
                                icons: null,
                                copy_icon: "",
                              },
                            ],
                            has_underline: false,
                          },
                        },
                        {
                          type: "icons",
                          data: {
                            icons: [
                              {
                                text: " 加大双人床 2人 / 房",
                                icon: "https://res.klook.com/image/upload/v1733803592/UED_new/TnA/TnA_ToursRevamp_2412/icon_itinerary_accommodation_bed.png",
                              },
                              {
                                text: "独立卫浴",
                                icon: "https://res.klook.com/image/upload/v1733803592/UED_new/TnA/TnA_ToursRevamp_2412/icon_itinerary_accommodation_bathroom.png",
                              },
                            ],
                          },
                        },
                        {
                          type: "imgs",
                          data: {
                            imgs: [
                              "https://res.klook.com/image/upload/v1736155523/activities/ror9uhilw8bn4wdfrsnr.jpg",
                              "https://res.klook.com/image/upload/v1736155523/activities/qcnke3phewkztyeqdp2b.jpg",
                              "https://res.klook.com/image/upload/v1736155523/activities/lleubxfcfxrj8ikavrlz.jpg",
                              "https://res.klook.com/image/upload/v1736155523/activities/vrtcoytgal0msh7s61zd.jpg",
                            ],
                            poi_identify_id: "",
                          },
                        },
                      ],
                    },
                  ],
                  track_info: {
                    type: "",
                    extra: null,
                  },
                },
              ],
            },
            group_desc: null,
            title_map_data: null,
          },
          {
            group_id: 156,
            group_key: "accommodation",
            left_icon:
              "https://res.klook.com/image/upload/v1733467621/UED_new/TnA/TnA_ToursRevamp_2412/icon_itinerary_accommodation.png",
            titles: ["住宿", "青年旅舍等"],
            title_components: [],
            is_title_bold: true,
            background_color: "",
            card_components: [
              {
                type: "texts",
                data: {
                  texts: [
                    {
                      type: "desc",
                      text: "2种住宿选择",
                    },
                  ],
                },
              },
            ],
            track_info: {
              type: "ItineraryAccomodation",
              extra: null,
            },
            details: {
              title: "",
              tabs: [
                {
                  tab_name: "住宿",
                  sections: [
                    {
                      section_name: "青年旅舍",
                      components: [
                        {
                          type: "poi",
                          data: {
                            background_color: "#F1EEFA",
                            poi_desc: "青年胶囊公寓",
                            poi_data: [
                              {
                                title: "青年胶囊公寓",
                                title_icon: "",
                                map: {
                                  location: "40.006828,116.408391",
                                  google_place_id:
                                    "ChIJm15buyhV8DUR1w46n4PX0rk",
                                  city_name: "",
                                  address_desc: "",
                                  name: "青年胶囊公寓",
                                },
                                group_key: "",
                                attr_id: 0,
                                poi_data_id: "156-235",
                              },
                            ],
                            is_poi_tappable: true,
                            poi_identify_id: "156-235",
                          },
                        },
                        {
                          type: "title_icons",
                          data: {
                            data_list: [
                              {
                                title_name: "",
                                star: -1,
                                icons: null,
                                copy_icon: "",
                              },
                            ],
                            has_underline: false,
                          },
                        },
                      ],
                    },
                    {
                      section_name: "胶囊旅馆",
                      components: [
                        {
                          type: "poi",
                          data: {
                            background_color: "#F1EEFA",
                            poi_desc: "青年公寓",
                            poi_data: [
                              {
                                title: "青年公寓",
                                title_icon: "",
                                map: {
                                  location: "39.76446200000001,116.511023",
                                  google_place_id:
                                    "ChIJkaMiMCC08TURUehLMqgWNxk",
                                  city_name: "",
                                  address_desc: "",
                                  name: "青年公寓",
                                },
                                group_key: "",
                                attr_id: 0,
                                poi_data_id: "156-236",
                              },
                            ],
                            is_poi_tappable: true,
                            poi_identify_id: "156-236",
                          },
                        },
                        {
                          type: "title_icons",
                          data: {
                            data_list: [
                              {
                                title_name: "",
                                star: 3,
                                icons: null,
                                copy_icon: "",
                              },
                            ],
                            has_underline: false,
                          },
                        },
                        {
                          type: "icons",
                          data: {
                            icons: [
                              {
                                text: " 3张单人床 3人 / 房",
                                icon: "https://res.klook.com/image/upload/v1733803592/UED_new/TnA/TnA_ToursRevamp_2412/icon_itinerary_accommodation_bed.png",
                              },
                              {
                                text: "独立卫浴",
                                icon: "https://res.klook.com/image/upload/v1733803592/UED_new/TnA/TnA_ToursRevamp_2412/icon_itinerary_accommodation_bathroom.png",
                              },
                            ],
                          },
                        },
                        {
                          type: "imgs",
                          data: {
                            imgs: [
                              "https://res.klook.com/image/upload/v1744015227/activities/efr1gn47m6zmqr0qufml.jpg",
                              "https://res.klook.com/image/upload/v1744015227/activities/d7mzaa3yey6tikuelrat.jpg",
                            ],
                            poi_identify_id: "",
                          },
                        },
                      ],
                    },
                  ],
                  track_info: {
                    type: "",
                    extra: null,
                  },
                },
              ],
            },
            group_desc: null,
            title_map_data: null,
          },
          {
            group_id: 168,
            group_key: "accommodation",
            left_icon:
              "https://res.klook.com/image/upload/v1733467621/UED_new/TnA/TnA_ToursRevamp_2412/icon_itinerary_accommodation.png",
            titles: ["住宿", "酒店"],
            title_components: [],
            is_title_bold: true,
            background_color: "",
            card_components: [],
            track_info: {
              type: "ItineraryAccomodation",
              extra: null,
            },
            details: {
              title: "",
              tabs: [
                {
                  tab_name: "住宿",
                  sections: [
                    {
                      section_name: "酒店",
                      components: [
                        {
                          type: "imgs",
                          data: {
                            imgs: [
                              "https://res.klook.com/image/upload/v1736252467/activities/ikxbzbejvpgikzcme8qq.jpg",
                              "https://res.klook.com/image/upload/v1736252467/activities/wkazau6nflpt78pwnyq2.jpg",
                            ],
                            poi_identify_id: "",
                          },
                        },
                      ],
                    },
                  ],
                  track_info: {
                    type: "",
                    extra: null,
                  },
                },
              ],
            },
            group_desc: null,
            title_map_data: null,
          },
          {
            group_id: 169,
            group_key: "accommodation",
            left_icon:
              "https://res.klook.com/image/upload/v1733467621/UED_new/TnA/TnA_ToursRevamp_2412/icon_itinerary_accommodation.png",
            titles: ["住宿", "青年旅舍"],
            title_components: [],
            is_title_bold: true,
            background_color: "",
            card_components: [],
            track_info: {
              type: "ItineraryAccomodation",
              extra: null,
            },
            details: {
              title: "",
              tabs: [],
            },
            group_desc: null,
            title_map_data: null,
          },
          {
            group_id: 171,
            group_key: "accommodation",
            left_icon:
              "https://res.klook.com/image/upload/v1733467621/UED_new/TnA/TnA_ToursRevamp_2412/icon_itinerary_accommodation.png",
            titles: ["住宿", "酒店等"],
            title_components: [],
            is_title_bold: true,
            background_color: "",
            card_components: [
              {
                type: "poi",
                data: {
                  background_color: "#F1EEFA",
                  poi_desc: "北京环球影城大酒店",
                  poi_data: [
                    {
                      title: "北京环球影城大酒店",
                      title_icon: "",
                      map: {
                        location: "39.855117,116.678625",
                        google_place_id: "ChIJKTlVaYem8TURd0RL30n-uy4",
                        city_name: "",
                        address_desc: "中国北京市通州区梨园镇",
                        name: "北京环球影城大酒店",
                      },
                      group_key: "",
                      attr_id: 0,
                      poi_data_id: "171-262",
                    },
                  ],
                  is_poi_tappable: true,
                  poi_identify_id: "171-262",
                },
              },
            ],
            track_info: {
              type: "ItineraryAccomodation",
              extra: null,
            },
            details: {
              title: "",
              tabs: [
                {
                  tab_name: "住宿",
                  sections: [
                    {
                      section_name: "酒店",
                      components: [
                        {
                          type: "poi",
                          data: {
                            background_color: "#F1EEFA",
                            poi_desc: "北京环球影城大酒店",
                            poi_data: [
                              {
                                title: "北京环球影城大酒店",
                                title_icon: "",
                                map: {
                                  location: "39.855117,116.678625",
                                  google_place_id:
                                    "ChIJKTlVaYem8TURd0RL30n-uy4",
                                  city_name: "",
                                  address_desc: "中国北京市通州区梨园镇",
                                  name: "北京环球影城大酒店",
                                },
                                group_key: "",
                                attr_id: 0,
                                poi_data_id: "171-262",
                              },
                            ],
                            is_poi_tappable: true,
                            poi_identify_id: "171-262",
                          },
                        },
                        {
                          type: "title_icons",
                          data: {
                            data_list: [
                              {
                                title_name: "",
                                star: -1,
                                icons: null,
                                copy_icon: "",
                              },
                            ],
                            has_underline: false,
                          },
                        },
                        {
                          type: "imgs",
                          data: {
                            imgs: [
                              "https://res.klook.com/image/upload/v1736334156/activities/kiawr2fdgejycfcw2wne.jpg",
                            ],
                            poi_identify_id: "",
                          },
                        },
                      ],
                    },
                    {
                      section_name: "民宿",
                      components: [],
                    },
                    {
                      section_name: "公寓",
                      components: [],
                    },
                    {
                      section_name: "青年旅舍",
                      components: [],
                    },
                    {
                      section_name: "度假村",
                      components: [],
                    },
                    {
                      section_name: "胶囊旅馆",
                      components: [],
                    },
                  ],
                  track_info: {
                    type: "",
                    extra: null,
                  },
                },
              ],
            },
            group_desc: null,
            title_map_data: null,
          },
          {
            group_id: 172,
            group_key: "accommodation",
            left_icon:
              "https://res.klook.com/image/upload/v1733467621/UED_new/TnA/TnA_ToursRevamp_2412/icon_itinerary_accommodation.png",
            titles: ["住宿", "酒店"],
            title_components: [],
            is_title_bold: true,
            background_color: "",
            card_components: [],
            track_info: {
              type: "ItineraryAccomodation",
              extra: null,
            },
            details: {
              title: "",
              tabs: [],
            },
            group_desc: null,
            title_map_data: null,
          },
          {
            group_id: 173,
            group_key: "accommodation",
            left_icon:
              "https://res.klook.com/image/upload/v1733467621/UED_new/TnA/TnA_ToursRevamp_2412/icon_itinerary_accommodation.png",
            titles: ["住宿", "民宿"],
            title_components: [],
            is_title_bold: true,
            background_color: "",
            card_components: [],
            track_info: {
              type: "ItineraryAccomodation",
              extra: null,
            },
            details: {
              title: "",
              tabs: [],
            },
            group_desc: null,
            title_map_data: null,
          },
          {
            group_id: 174,
            group_key: "accommodation",
            left_icon:
              "https://res.klook.com/image/upload/v1733467621/UED_new/TnA/TnA_ToursRevamp_2412/icon_itinerary_accommodation.png",
            titles: ["住宿", "公寓"],
            title_components: [],
            is_title_bold: true,
            background_color: "",
            card_components: [],
            track_info: {
              type: "ItineraryAccomodation",
              extra: null,
            },
            details: {
              title: "",
              tabs: [],
            },
            group_desc: null,
            title_map_data: null,
          },
          {
            group_id: 175,
            group_key: "accommodation",
            left_icon:
              "https://res.klook.com/image/upload/v1733467621/UED_new/TnA/TnA_ToursRevamp_2412/icon_itinerary_accommodation.png",
            titles: ["住宿", "青年旅舍"],
            title_components: [],
            is_title_bold: true,
            background_color: "",
            card_components: [],
            track_info: {
              type: "ItineraryAccomodation",
              extra: null,
            },
            details: {
              title: "",
              tabs: [],
            },
            group_desc: null,
            title_map_data: null,
          },
          {
            group_id: 176,
            group_key: "accommodation",
            left_icon:
              "https://res.klook.com/image/upload/v1733467621/UED_new/TnA/TnA_ToursRevamp_2412/icon_itinerary_accommodation.png",
            titles: ["住宿", "度假村"],
            title_components: [],
            is_title_bold: true,
            background_color: "",
            card_components: [],
            track_info: {
              type: "ItineraryAccomodation",
              extra: null,
            },
            details: {
              title: "",
              tabs: [],
            },
            group_desc: null,
            title_map_data: null,
          },
          {
            group_id: 177,
            group_key: "accommodation",
            left_icon:
              "https://res.klook.com/image/upload/v1733467621/UED_new/TnA/TnA_ToursRevamp_2412/icon_itinerary_accommodation.png",
            titles: ["住宿", "胶囊旅馆"],
            title_components: [],
            is_title_bold: true,
            background_color: "",
            card_components: [],
            track_info: {
              type: "ItineraryAccomodation",
              extra: null,
            },
            details: {
              title: "",
              tabs: [],
            },
            group_desc: null,
            title_map_data: null,
          },
        ],
      },
      {
        day_name: "第3天",
        groups: [
          {
            group_id: 148,
            group_key: "meal",
            left_icon:
              "https://res.klook.com/image/upload/v1733467621/UED_new/TnA/TnA_ToursRevamp_2412/icon_itinerary_meal.png",
            titles: ["早午餐", "融合料理等"],
            title_components: [],
            is_title_bold: false,
            background_color: "",
            card_components: [
              {
                type: "icons",
                data: {
                  icons: [
                    {
                      text: "1小时1分钟",
                      icon: "https://res.klook.com/image/upload/v1735968462/UED_new/TnA/TnA_ToursRevamp_2412/icon_time.svg",
                    },
                  ],
                },
              },
            ],
            track_info: {
              type: "ItineraryMeal",
              extra: null,
            },
            details: {
              title: "餐饮介绍",
              tabs: [
                {
                  tab_name: "餐饮介绍",
                  sections: [
                    {
                      section_name: "",
                      components: [
                        {
                          type: "poi",
                          data: {
                            background_color: "#F1EEFA",
                            poi_desc: " -交易所",
                            poi_data: [
                              {
                                title: " -交易所",
                                title_icon: "",
                                map: {
                                  location: "39.91095899999999,116.411341",
                                  google_place_id:
                                    "ChIJpaF2MslS8DURRskvKooTIKk",
                                  city_name: "",
                                  address_desc: "",
                                  name: " -交易所",
                                },
                                group_key: "",
                                attr_id: 0,
                                poi_data_id: "148-225",
                              },
                            ],
                            is_poi_tappable: true,
                            poi_identify_id: "148-225",
                          },
                        },
                        {
                          type: "texts",
                          data: {
                            texts: [
                              {
                                type: "text",
                                text: "融合料理, 多种料理选择, 越南料理",
                              },
                            ],
                          },
                        },
                        {
                          type: "icons",
                          data: {
                            icons: [
                              {
                                text: "1小时1分钟",
                                icon: "https://res.klook.com/image/upload/v1735968462/UED_new/TnA/TnA_ToursRevamp_2412/icon_time.svg",
                              },
                            ],
                          },
                        },
                        {
                          type: "imgs",
                          data: {
                            imgs: [
                              "https://res.klook.com/image/upload/v1736155853/activities/rk6mq0cqfy7rnjw7vwa2.jpg",
                              "https://res.klook.com/image/upload/v1736155852/activities/hliznulnlzu7vdppldb8.jpg",
                              "https://res.klook.com/image/upload/v1736155852/activities/oikuqvkrgywredmm56w0.jpg",
                            ],
                            poi_identify_id: "",
                          },
                        },
                      ],
                    },
                  ],
                  track_info: {
                    type: "",
                    extra: null,
                  },
                },
              ],
            },
            group_desc: null,
            title_map_data: null,
          },
          {
            group_id: 152,
            group_key: "meal",
            left_icon:
              "https://res.klook.com/image/upload/v1733467621/UED_new/TnA/TnA_ToursRevamp_2412/icon_itinerary_meal.png",
            titles: ["晚餐", "融合料理"],
            title_components: [],
            is_title_bold: false,
            background_color: "",
            card_components: [
              {
                type: "icons",
                data: {
                  icons: [
                    {
                      text: "1小时6分钟",
                      icon: "https://res.klook.com/image/upload/v1735968462/UED_new/TnA/TnA_ToursRevamp_2412/icon_time.svg",
                    },
                  ],
                },
              },
            ],
            track_info: {
              type: "ItineraryMeal",
              extra: null,
            },
            details: null,
            group_desc: null,
            title_map_data: null,
          },
          {
            group_id: 166,
            group_key: "meal",
            left_icon:
              "https://res.klook.com/image/upload/v1733467621/UED_new/TnA/TnA_ToursRevamp_2412/icon_itinerary_meal.png",
            titles: ["下午茶", "融合料理等"],
            title_components: [],
            is_title_bold: false,
            background_color: "",
            card_components: [
              {
                type: "icons",
                data: {
                  icons: [
                    {
                      text: "1小时",
                      icon: "https://res.klook.com/image/upload/v1735968462/UED_new/TnA/TnA_ToursRevamp_2412/icon_time.svg",
                    },
                  ],
                },
              },
            ],
            track_info: {
              type: "ItineraryMeal",
              extra: null,
            },
            details: {
              title: "餐饮介绍",
              tabs: [
                {
                  tab_name: "餐饮介绍",
                  sections: [
                    {
                      section_name: "",
                      components: [
                        {
                          type: "poi",
                          data: {
                            background_color: "#F1EEFA",
                            poi_desc:
                              "融合料理 & 多种料理选择 & 埃塞俄比亚料理",
                            poi_data: [
                              {
                                title:
                                  "融合料理 & 多种料理选择 & 埃塞俄比亚料理",
                                title_icon: "",
                                map: {
                                  location: "",
                                  google_place_id: "",
                                  city_name: "",
                                  address_desc: "",
                                  name: "融合料理 & 多种料理选择 & 埃塞俄比亚料理",
                                },
                                group_key: "",
                                attr_id: 0,
                                poi_data_id: "166-253",
                              },
                            ],
                            is_poi_tappable: true,
                            poi_identify_id: "166-253",
                          },
                        },
                        {
                          type: "texts",
                          data: {
                            texts: [
                              {
                                type: "text",
                                text: "融合料理, 多种料理选择, 埃塞俄比亚料理",
                              },
                            ],
                          },
                        },
                        {
                          type: "icons",
                          data: {
                            icons: [
                              {
                                text: "1小时",
                                icon: "https://res.klook.com/image/upload/v1735968462/UED_new/TnA/TnA_ToursRevamp_2412/icon_time.svg",
                              },
                            ],
                          },
                        },
                      ],
                    },
                  ],
                  track_info: {
                    type: "",
                    extra: null,
                  },
                },
              ],
            },
            group_desc: null,
            title_map_data: null,
          },
          {
            group_id: 614,
            group_key: "meal",
            left_icon:
              "https://res.klook.com/image/upload/v1733467621/UED_new/TnA/TnA_ToursRevamp_2412/icon_itinerary_meal.png",
            titles: ["早餐", "多种料理选择等"],
            title_components: [],
            is_title_bold: false,
            background_color: "",
            card_components: [
              {
                type: "icons",
                data: {
                  icons: [
                    {
                      text: "3小时",
                      icon: "https://res.klook.com/image/upload/v1735968462/UED_new/TnA/TnA_ToursRevamp_2412/icon_time.svg",
                    },
                  ],
                },
              },
            ],
            track_info: {
              type: "ItineraryMeal",
              extra: null,
            },
            details: {
              title: "餐饮介绍",
              tabs: [
                {
                  tab_name: "餐饮介绍",
                  sections: [
                    {
                      section_name: "",
                      components: [
                        {
                          type: "poi",
                          data: {
                            background_color: "#F1EEFA",
                            poi_desc: "多种料理选择 & 越南料理 & 素食选择",
                            poi_data: [
                              {
                                title: "多种料理选择 & 越南料理 & 素食选择",
                                title_icon: "",
                                map: {
                                  location: "",
                                  google_place_id: "",
                                  city_name: "",
                                  address_desc: "",
                                  name: "多种料理选择 & 越南料理 & 素食选择",
                                },
                                group_key: "",
                                attr_id: 0,
                                poi_data_id: "614-726",
                              },
                            ],
                            is_poi_tappable: true,
                            poi_identify_id: "614-726",
                          },
                        },
                        {
                          type: "texts",
                          data: {
                            texts: [
                              {
                                type: "text",
                                text: "多种料理选择, 越南料理, 素食选择",
                              },
                            ],
                          },
                        },
                        {
                          type: "icons",
                          data: {
                            icons: [
                              {
                                text: "3小时",
                                icon: "https://res.klook.com/image/upload/v1735968462/UED_new/TnA/TnA_ToursRevamp_2412/icon_time.svg",
                              },
                            ],
                          },
                        },
                      ],
                    },
                  ],
                  track_info: {
                    type: "",
                    extra: null,
                  },
                },
              ],
            },
            group_desc: null,
            title_map_data: null,
          },
          {
            group_id: 615,
            group_key: "meal",
            left_icon:
              "https://res.klook.com/image/upload/v1733467621/UED_new/TnA/TnA_ToursRevamp_2412/icon_itinerary_meal.png",
            titles: ["早午餐", "西式料理等"],
            title_components: [],
            is_title_bold: false,
            background_color: "",
            card_components: [
              {
                type: "texts",
                data: {
                  texts: [
                    {
                      type: "desc",
                      text: "可加购（根据需求）",
                    },
                  ],
                },
              },
              {
                type: "icons",
                data: {
                  icons: [
                    {
                      text: "3小时",
                      icon: "https://res.klook.com/image/upload/v1735968462/UED_new/TnA/TnA_ToursRevamp_2412/icon_time.svg",
                    },
                  ],
                },
              },
            ],
            track_info: {
              type: "ItineraryMeal",
              extra: null,
            },
            details: {
              title: "餐饮介绍",
              tabs: [
                {
                  tab_name: "餐饮介绍",
                  sections: [
                    {
                      section_name: "",
                      components: [
                        {
                          type: "poi",
                          data: {
                            background_color: "#F1EEFA",
                            poi_desc: "西式料理 & 多种料理选择",
                            poi_data: [
                              {
                                title: "西式料理 & 多种料理选择",
                                title_icon: "",
                                map: {
                                  location: "",
                                  google_place_id: "",
                                  city_name: "",
                                  address_desc: "",
                                  name: "西式料理 & 多种料理选择",
                                },
                                group_key: "",
                                attr_id: 0,
                                poi_data_id: "615-727",
                              },
                            ],
                            is_poi_tappable: true,
                            poi_identify_id: "615-727",
                          },
                        },
                        {
                          type: "texts",
                          data: {
                            texts: [
                              {
                                type: "text",
                                text: "西式料理, 多种料理选择",
                              },
                            ],
                          },
                        },
                        {
                          type: "icons",
                          data: {
                            icons: [
                              {
                                text: "3小时",
                                icon: "https://res.klook.com/image/upload/v1735968462/UED_new/TnA/TnA_ToursRevamp_2412/icon_time.svg",
                              },
                            ],
                          },
                        },
                      ],
                    },
                  ],
                  track_info: {
                    type: "",
                    extra: null,
                  },
                },
              ],
            },
            group_desc: null,
            title_map_data: null,
          },
          {
            group_id: 616,
            group_key: "meal",
            left_icon:
              "https://res.klook.com/image/upload/v1733467621/UED_new/TnA/TnA_ToursRevamp_2412/icon_itinerary_meal.png",
            titles: ["饮料", "融合料理等"],
            title_components: [],
            is_title_bold: false,
            background_color: "",
            card_components: [
              {
                type: "texts",
                data: {
                  texts: [
                    {
                      type: "desc",
                      text: "可加购（根据需求）",
                    },
                  ],
                },
              },
              {
                type: "icons",
                data: {
                  icons: [
                    {
                      text: "3小时1分钟",
                      icon: "https://res.klook.com/image/upload/v1735968462/UED_new/TnA/TnA_ToursRevamp_2412/icon_time.svg",
                    },
                  ],
                },
              },
            ],
            track_info: {
              type: "ItineraryMeal",
              extra: null,
            },
            details: {
              title: "餐饮介绍",
              tabs: [
                {
                  tab_name: "餐饮介绍",
                  sections: [
                    {
                      section_name: "",
                      components: [
                        {
                          type: "poi",
                          data: {
                            background_color: "#F1EEFA",
                            poi_desc: "唐会- 沟通所",
                            poi_data: [
                              {
                                title: "唐会- 沟通所",
                                title_icon: "",
                                map: {
                                  location: "39.928909,116.44404",
                                  google_place_id:
                                    "ChIJ80kYN-is8TUR3JEoTz5j4ws",
                                  city_name: "",
                                  address_desc: "",
                                  name: "唐会- 沟通所",
                                },
                                group_key: "",
                                attr_id: 0,
                                poi_data_id: "616-728",
                              },
                            ],
                            is_poi_tappable: true,
                            poi_identify_id: "616-728",
                          },
                        },
                        {
                          type: "texts",
                          data: {
                            texts: [
                              {
                                type: "text",
                                text: "融合料理, 自助餐",
                              },
                            ],
                          },
                        },
                        {
                          type: "icons",
                          data: {
                            icons: [
                              {
                                text: "3小时1分钟",
                                icon: "https://res.klook.com/image/upload/v1735968462/UED_new/TnA/TnA_ToursRevamp_2412/icon_time.svg",
                              },
                            ],
                          },
                        },
                        {
                          type: "imgs",
                          data: {
                            imgs: [
                              "https://res.klook.com/image/upload/v1743648812/activities/vx7qfzos7zramzosmjc4.jpg",
                              "https://res.klook.com/image/upload/v1743648812/activities/z4t5bhyb144guqt3nbuv.jpg",
                            ],
                            poi_identify_id: "",
                          },
                        },
                      ],
                    },
                  ],
                  track_info: {
                    type: "",
                    extra: null,
                  },
                },
              ],
            },
            group_desc: null,
            title_map_data: null,
          },
          {
            group_id: 617,
            group_key: "meal",
            left_icon:
              "https://res.klook.com/image/upload/v1733467621/UED_new/TnA/TnA_ToursRevamp_2412/icon_itinerary_meal.png",
            titles: ["夜宵", "烧烤"],
            title_components: [],
            is_title_bold: false,
            background_color: "",
            card_components: [
              {
                type: "texts",
                data: {
                  texts: [
                    {
                      type: "desc",
                      text: "可加购（根据需求）",
                    },
                  ],
                },
              },
              {
                type: "icons",
                data: {
                  icons: [
                    {
                      text: "2小时",
                      icon: "https://res.klook.com/image/upload/v1735968462/UED_new/TnA/TnA_ToursRevamp_2412/icon_time.svg",
                    },
                  ],
                },
              },
            ],
            track_info: {
              type: "ItineraryMeal",
              extra: null,
            },
            details: {
              title: "餐饮介绍",
              tabs: [
                {
                  tab_name: "餐饮介绍",
                  sections: [
                    {
                      section_name: "",
                      components: [
                        {
                          type: "poi",
                          data: {
                            background_color: "#F1EEFA",
                            poi_desc: "烧烤",
                            poi_data: [
                              {
                                title: "烧烤",
                                title_icon: "",
                                map: {
                                  location: "",
                                  google_place_id: "",
                                  city_name: "",
                                  address_desc: "",
                                  name: "烧烤",
                                },
                                group_key: "",
                                attr_id: 0,
                                poi_data_id: "617-729",
                              },
                            ],
                            is_poi_tappable: true,
                            poi_identify_id: "617-729",
                          },
                        },
                        {
                          type: "texts",
                          data: {
                            texts: [
                              {
                                type: "text",
                                text: "烧烤",
                              },
                            ],
                          },
                        },
                        {
                          type: "icons",
                          data: {
                            icons: [
                              {
                                text: "2小时",
                                icon: "https://res.klook.com/image/upload/v1735968462/UED_new/TnA/TnA_ToursRevamp_2412/icon_time.svg",
                              },
                            ],
                          },
                        },
                        {
                          type: "imgs",
                          data: {
                            imgs: [
                              "https://res.klook.com/image/upload/v1743998037/activities/uisor1rxlxnyi2k9ft1a.jpg",
                            ],
                            poi_identify_id: "",
                          },
                        },
                      ],
                    },
                  ],
                  track_info: {
                    type: "",
                    extra: null,
                  },
                },
              ],
            },
            group_desc: null,
            title_map_data: null,
          },
        ],
      },
      {
        day_name: "第4天",
        groups: [
          {
            group_id: 159,
            group_key: "transport",
            left_icon:
              "https://res.klook.com/image/upload/v1733467622/UED_new/TnA/TnA_ToursRevamp_2412/icon_itinerary_transport.png",
            titles: ["双层巴士 / 嘟嘟车等"],
            title_components: [],
            is_title_bold: true,
            background_color: "",
            card_components: [
              {
                type: "texts",
                data: {
                  texts: [
                    {
                      type: "desc",
                      text: "3种车辆可选",
                    },
                  ],
                },
              },
            ],
            track_info: {
              type: "ItineraryTrans",
              extra: null,
            },
            details: {
              title: "交通",
              tabs: [
                {
                  tab_name: "交通",
                  sections: [
                    {
                      section_name: "选择1",
                      components: [
                        {
                          type: "texts",
                          data: {
                            texts: [
                              {
                                type: "text",
                                text: "双层巴士",
                              },
                            ],
                          },
                        },
                        {
                          type: "icons",
                          data: {
                            icons: [
                              {
                                text: "1小时",
                                icon: "https://res.klook.com/image/upload/v1735968462/UED_new/TnA/TnA_ToursRevamp_2412/icon_time.svg",
                              },
                            ],
                          },
                        },
                      ],
                    },
                    {
                      section_name: "选择2",
                      components: [
                        {
                          type: "texts",
                          data: {
                            texts: [
                              {
                                type: "text",
                                text: "嘟嘟车",
                              },
                            ],
                          },
                        },
                        {
                          type: "icons",
                          data: {
                            icons: [
                              {
                                text: "3小时",
                                icon: "https://res.klook.com/image/upload/v1735968462/UED_new/TnA/TnA_ToursRevamp_2412/icon_time.svg",
                              },
                            ],
                          },
                        },
                      ],
                    },
                    {
                      section_name: "选择3",
                      components: [
                        {
                          type: "texts",
                          data: {
                            texts: [
                              {
                                type: "text",
                                text: "渡轮",
                              },
                            ],
                          },
                        },
                        {
                          type: "icons",
                          data: {
                            icons: [
                              {
                                text: "1小时",
                                icon: "https://res.klook.com/image/upload/v1735968462/UED_new/TnA/TnA_ToursRevamp_2412/icon_time.svg",
                              },
                            ],
                          },
                        },
                      ],
                    },
                  ],
                  track_info: {
                    type: "",
                    extra: null,
                  },
                },
              ],
            },
            group_desc: null,
            title_map_data: null,
          },
          {
            group_id: 160,
            group_key: "transport",
            left_icon:
              "https://res.klook.com/image/upload/v1733467622/UED_new/TnA/TnA_ToursRevamp_2412/icon_itinerary_transport.png",
            titles: ["子弹头列车 / 渡轮"],
            title_components: [],
            is_title_bold: true,
            background_color: "",
            card_components: [
              {
                type: "texts",
                data: {
                  texts: [
                    {
                      type: "desc",
                      text: "2种车辆可选",
                    },
                  ],
                },
              },
            ],
            track_info: {
              type: "ItineraryTrans",
              extra: null,
            },
            details: {
              title: "交通",
              tabs: [
                {
                  tab_name: "交通",
                  sections: [
                    {
                      section_name: "选择1",
                      components: [
                        {
                          type: "texts",
                          data: {
                            texts: [
                              {
                                type: "text",
                                text: "子弹头列车",
                              },
                            ],
                          },
                        },
                        {
                          type: "icons",
                          data: {
                            icons: [
                              {
                                text: "28分钟",
                                icon: "https://res.klook.com/image/upload/v1735968462/UED_new/TnA/TnA_ToursRevamp_2412/icon_time.svg",
                              },
                            ],
                          },
                        },
                        {
                          type: "imgs",
                          data: {
                            imgs: [
                              "https://res.klook.com/image/upload/v1736156804/activities/htpwfwh0hvrvaug89ofy.jpg",
                              "https://res.klook.com/image/upload/v1736156805/activities/xrfllvycxrhjyplzgwt5.jpg",
                            ],
                            poi_identify_id: "",
                          },
                        },
                      ],
                    },
                    {
                      section_name: "选择2",
                      components: [
                        {
                          type: "texts",
                          data: {
                            texts: [
                              {
                                type: "text",
                                text: "渡轮",
                              },
                            ],
                          },
                        },
                        {
                          type: "icons",
                          data: {
                            icons: [
                              {
                                text: "3小时",
                                icon: "https://res.klook.com/image/upload/v1735968462/UED_new/TnA/TnA_ToursRevamp_2412/icon_time.svg",
                              },
                            ],
                          },
                        },
                      ],
                    },
                  ],
                  track_info: {
                    type: "",
                    extra: null,
                  },
                },
              ],
            },
            group_desc: null,
            title_map_data: null,
          },
          {
            group_id: 143,
            group_key: "return",
            left_icon:
              "https://res.klook.com/image/upload/v1733467621/UED_new/TnA/TnA_ToursRevamp_2412/icon_itinerary_depature.png",
            titles: ["多种回程选择"],
            title_components: [],
            is_title_bold: true,
            background_color: "",
            card_components: [
              {
                type: "texts",
                data: {
                  texts: [
                    {
                      type: "text",
                      text: "21:04 / 23:04 / 23:04 / 23:18 / 23:45 / 23:59 / 23:59 / 23:59",
                    },
                  ],
                },
              },
              {
                type: "poi",
                data: {
                  background_color: "#F1EEFA",
                  poi_desc: "  - 交易所",
                  poi_data: [
                    {
                      title: " - 交易所",
                      title_icon: "",
                      map: {
                        location: "39.915853,116.359632",
                        google_place_id: "ChIJyTh9lGhS8DURI9MqUQAFXUY",
                        city_name: "",
                        address_desc:
                          "中国北京市西城区金融大街金融大街甲17 邮政编码: 100032",
                        name: " - 交易所",
                      },
                      group_key: "",
                      attr_id: 0,
                      poi_data_id: "143-1151",
                    },
                  ],
                  is_poi_tappable: true,
                  poi_identify_id: "143-1151",
                },
              },
            ],
            track_info: {
              type: "ItineraryReturn",
              extra: {
                ReturnType: "dropoff,disband,hotel",
              },
            },
            details: {
              title: "回程详情",
              tabs: [
                {
                  tab_name: "前往下车点",
                  sections: [
                    {
                      section_name: "下车点1",
                      components: [
                        {
                          type: "texts",
                          data: {
                            texts: [
                              {
                                type: "text",
                                text: "23:59 / 23:59",
                              },
                            ],
                          },
                        },
                        {
                          type: "poi",
                          data: {
                            background_color: "#F1EEFA",
                            poi_desc: " - 交易所",
                            poi_data: [
                              {
                                title: " - 交易所",
                                title_icon: "",
                                map: {
                                  location: "39.915853,116.359632",
                                  google_place_id:
                                    "ChIJyTh9lGhS8DURI9MqUQAFXUY",
                                  city_name: "",
                                  address_desc:
                                    "中国北京市西城区金融大街金融大街甲17 邮政编码: 100032",
                                  name: " - 交易所",
                                },
                                group_key: "",
                                attr_id: 0,
                                poi_data_id: "143-1151",
                              },
                            ],
                            is_poi_tappable: true,
                            poi_identify_id: "143-1151",
                          },
                        },
                      ],
                    },
                  ],
                  track_info: {
                    type: "ItineraryReturn",
                    extra: {
                      ReturnType: "dropoff",
                    },
                  },
                },
                {
                  tab_name: "与司机协安排下车地点",
                  sections: [
                    {
                      section_name: "",
                      components: [
                        {
                          type: "texts",
                          data: {
                            texts: [
                              {
                                type: "text",
                                text: "23:04 / 23:18",
                              },
                            ],
                          },
                        },
                        {
                          type: "texts",
                          data: {
                            texts: [
                              {
                                type: "text",
                                text: "与司机协安排下车地点",
                              },
                            ],
                          },
                        },
                        {
                          type: "texts",
                          data: {
                            texts: [
                              {
                                type: "text",
                                text: "测试一下",
                              },
                            ],
                          },
                        },
                      ],
                    },
                  ],
                  track_info: {
                    type: "ItineraryReturn",
                    extra: {
                      ReturnType: "hotel",
                    },
                  },
                },
              ],
            },
            group_desc: null,
            title_map_data: null,
          },
        ],
      },
    ],
  },
};

export const itineraryData = res.result;
