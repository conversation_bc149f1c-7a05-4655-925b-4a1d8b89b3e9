export const isServer = typeof window === 'undefined'


/**
 * @Author: mingming.chen
 * @Date: 2020-02-04
 * @description: 根据星级增加描述
 * @function: starDesc
 * @param {Function} $t 翻译函数
 * @param {Number} rating
 * @param {boolean} ratingHide 评分低于ratingHide 不展示描述
 * @return:描述
 */
export function starAppraise($t: Function, rating: number, ratingHide: number) {
  const starAppraiseObj: any = {
    1: 'review_not_recommended',
    2: 'review_unsatisfactory',
    3: 'review_average',
    4: 'review_good',
    5: 'review_highly_recommended'
  }
  if (rating >= ratingHide) {
    const t = $t(starAppraiseObj[rating / 20])
    return t
  }
}

export const getCropDefined = (platform: string = 'desktop') => {
  const cropData: any = {
    desktop: {
      previewCrop: '/fl_lossy.progressive,q_65/w_1080/'
    },
    mobile: {
      previewCrop: '/fl_lossy.progressive,q_65/'
    }
  }
  const obj = {
    cardCrop: '/fl_lossy.progressive,q_auto/c_fill,w_480/', // C端展示使用
    adminCrop: '/fl_lossy.progressive,q_auto/q_80,c_fill,w_400/', // admin后端下发crop，本地上传为原图crop
    uploadUrl: 'https://res.klook.com/image/upload',
    ...(cropData[platform || 'desktop'])
  }
  return obj
}

const imgTypeMap: any = {
  1: 3 / 4,  // 4 : 3
  2: 1,      // 1: 1
  3: 9 / 16  // 16 : 9
}

interface ImgAttrs {
  type: string
  width: number
  src: string
}

export function replacedImgSrc(attrs: ImgAttrs) {
  const { type, width, src } = attrs
  const w = Math.floor(width)
  const h = Math.floor(w * imgTypeMap[type])
  const widthAndHeight = `w_${2 * w},h_${2 * h}`

  // 后端返回了尺寸
  if (src.includes('c_fill')) {
    return src
  }

  return src.replace('image/upload/', `image/upload/q_85/c_fill,${widthAndHeight}/`)
}

export const copyToClipboard = (value: string): Promise<any> => {
  return new Promise((resolve) => {
    const el = document.createElement('textarea') // temporary element
    el.value = value

    el.style.position = 'absolute'
    el.style.left = '-9999px'
    el.readOnly = true // avoid iOs keyboard opening
    el.contentEditable = 'true'

    document.body.appendChild(el)

    selectText(el, 0, value.length)

    if (document.execCommand('copy')) {
      document.body.removeChild(el)
      resolve(true)
    } else {
      resolve(false)
    }
  })
}

type EditableInput = HTMLTextAreaElement | HTMLInputElement

const selectText = (
  editableEl: EditableInput,
  selectionStart: number,
  selectionEnd: number
) => {
  const isIOS = navigator.userAgent.match(/ipad|iPod|iphone/i)
  if (isIOS) {
    const range = document.createRange()
    range.selectNodeContents(editableEl)

    const selection = window.getSelection() as Selection // current text selection
    selection.removeAllRanges()
    selection.addRange(range)
    editableEl.setSelectionRange(selectionStart, selectionEnd)
  } else {
    editableEl.select()
  }
}

/**
 * @param originUrl 图片url
 * @param targetUrl 替换url
 * @param width 图片宽度
 * @param height 高度
 * @param webp 是否支持 webp
 */
export function setNewImageSize(originUrl: string, targetUrl: string, width: number, height: number, webp: number, isFill: boolean = true) {
  let newImgUrl = originUrl || ''

  if (targetUrl && originUrl) {
    // 设置清晰度
    const temp = targetUrl + 'fl_lossy.progressive,q_85/'
    const newHeight = height > 0 ? ',h_' + height + '/' : '/'

    newImgUrl = newImgUrl.replace(targetUrl, temp)
    newImgUrl = newImgUrl.replace(temp, temp + (isFill ? 'c_fill,' : '') + 'w_' + width + newHeight)

    // Apply .webp
    if (webp && newImgUrl.indexOf('res.klook.com') > 0) {
      newImgUrl = newImgUrl.replace(/.(jpg|png|jpeg|gif)$/, '.webp')
    }
  }

  return newImgUrl
}

/**
 * 图片 webp 格式
 * @param url 图片 url
 * @param webp 是否支持 webp
 */
export function setImageFormat(url: string, webp: number) {
  let destUrl = url
  if (webp && destUrl && destUrl.includes('res.klook.com')) {
    destUrl = destUrl.replace(/.(jpg|png|jpeg|gif)$/, '.webp')
  }

  return destUrl
}

export function getItineraryMenusClass(index: number) {
  const el = `js-itinerary-day-${index + 1}`
  return el
}

export const isExpandSeeMoreKey = 'isExpandSeeMoreFlag'
export const isExpandDetailsKey = 'isExpandDetailsFlag'

export const IS_PICK_UP_KEY = '_2'

export const POI_USING_TYPE = {
  location: 'location',
  area: 'area'
}
