# 多个 Marker 地图显示优化 & 地理围栏动态属性

## 问题描述

1. **多个 Marker 显示问题**: 原来的 `flyTo` 方法只适用于单个 marker 的情况。当当前生效的卡片存在多个 markers 时（`currentMarker.length > 1`），需要让地图同时显示所有相关的 markers 并适用合适的缩放。

2. **地理围栏属性问题**: 当前卡片 `pick_up_type` 是 2（地理围栏区域）时，需要使用 `selected_area_properties` 作为地理围栏的属性。反之如果非当前卡片，则默认用 `unselected_area_properties`。

## 解决方案

### 1. 修改 `setMarkerIndex` 方法

在 `packages/klook-experience-itinerary-v2/src/components/view-map/mobile/map.vue` 中：

- **原逻辑**: 无论有多少个 markers，都只调用 `flyTo(result[0])` 飞到第一个 marker
- **新逻辑**:
  - 当 `result.length > 1` 时，使用 `fitMapBoundsForMarkers` 方法显示所有相关 markers
  - 当 `result.length === 1` 时，继续使用 `flyTo` 方法

### 2. 修改 `markerClick` 方法

保持与 `setMarkerIndex` 方法的逻辑一致：

- 找到同一个卡片对应的所有 markers（通过 `attr_id` 匹配）
- 根据 markers 数量选择合适的地图显示方式

### 3. 新增 `fitMapBoundsForMarkers` 方法

专门用于处理多个 markers 的地图适配：

```typescript
async fitMapBoundsForMarkers(markerCenters: any[]) {
  if (this.$map && markerCenters.length > 0) {
    const box = await mapUtils.turfBbox(markerCenters, this.mapType as MapTypes.Type)
    const bounds = this.$map.createBounds(box.sw, box.ne, { formatLngLat: false })
    this.$map.fitBounds(bounds, {
      padding: {
        top: 80,
        left: 20,
        bottom: 160,
        right: 20,
      },
    })
  }
}
```

### 4. 修改 `initFenceData` 方法

在 `packages/klook-experience-itinerary-v2/src/components/view-map/base/map.ts` 中：

- **新增参数**: 添加 `currentCard` 参数来传递当前卡片信息
- **动态属性选择**: 根据当前卡片的 `pick_up_type` 或 `using_type` 选择合适的区域属性

```typescript
initFenceData(fence: any[], areaConfig: AreaConfig, currentCard?: any) {
  const list = fence.reduce((acc: any, curr: any) => {
    // 如果当前卡片的 pick_up_type 是 2（地理围栏区域），使用 selected_area_properties
    // 否则使用默认的 unselected_area_properties
    const isCurrentCardArea = currentCard?.data?.using_type === POI_USING_TYPE.area || currentCard?.pick_up_type === 2
    const area_properties = isCurrentCardArea
      ? (areaConfig?.selected_area_properties || {} as IAreaProperties)
      : (areaConfig?.unselected_area_properties || {} as IAreaProperties)

    // ... 其余逻辑保持不变
  }, [])
  return Object.freeze(list)
}
```

### 5. 新增地理围栏动态更新功能

- **新增 `originalGeomList` 属性**: 存储原始的 geom 数据
- **新增 `updateGeoFenceForCurrentCard` 方法**: 当切换卡片时动态更新地理围栏属性
- **修改 `getGeoFenceConf` 方法**: 支持传递当前卡片信息

## 技术细节

### 使用的地图工具方法

- `mapUtils.turfBbox()`: 计算多个点的边界框
- `$map.createBounds()`: 创建地图边界对象
- `$map.fitBounds()`: 调整地图视图以适配边界

### 地图显示策略

- **单个 marker**: 使用 `flyTo` 方法，平滑飞到指定位置
- **多个 markers**: 使用 `fitBounds` 方法，调整缩放级别以显示所有 markers

### Padding 设置

考虑到移动端界面元素的位置，设置了合适的 padding：
- top: 80px (为顶部操作栏留空间)
- left/right: 20px (左右边距)
- bottom: 160px (为底部卡片滚动区域留空间)

## 影响范围

### 文件修改
- `packages/klook-experience-itinerary-v2/src/components/view-map/base/map.ts`
  - 修改 `initFenceData` 方法，新增 `currentCard` 参数
- `packages/klook-experience-itinerary-v2/src/components/view-map/mobile/map.vue`
  - 修改 `setMarkerIndex`, `markerClick` 方法
  - 新增 `fitMapBoundsForMarkers`, `updateGeoFenceForCurrentCard` 方法
  - 新增 `originalGeomList` 属性

### 功能影响
- 地图 marker 显示和缩放逻辑
- 地理围栏动态属性切换
- 卡片切换时的视觉反馈

## 测试建议

### 多个 Marker 功能测试
1. 测试单个 marker 的卡片切换，确保 `flyTo` 功能正常
2. 测试多个 markers 的卡片切换，确保所有 markers 都能在地图上可见
3. 测试直接点击 marker 的交互，确保逻辑一致
4. 测试不同屏幕尺寸下的 padding 效果

### 地理围栏属性测试
1. 测试 `pick_up_type` 为 2 的卡片，确保地理围栏使用 `selected_area_properties`
2. 测试 `pick_up_type` 非 2 的卡片，确保地理围栏使用 `unselected_area_properties`
3. 测试卡片切换时地理围栏颜色的动态变化
4. 测试混合场景（既有点又有区域）的正确显示
