type HotelTag = {
  id: string | null
  type: string
  icon: string | null
  name: string
  name_en: string
  desc: string | null
  text_color: string | null
  bg_color: string | null
  border_color: string | null
  time_left: string | null
  [key: string]: string | null
}

type Discount = {
  sign: string
  currency_symbol: string
  price: string
  discount_tags: Array<{ price_desc: string, tag: HotelTag }>
}

interface Promotion {
  title?: string
  desc?: string
  currency_symbol?: string
  original_per_night_price?: string
  original_price_desc?: string
  per_night_price?: string
  price_desc?: string
  discount: Discount
  gift?: {
    gift_list: HotelTag[]
  },
  tip?: HotelTag
}


interface PriceItemData {
  type: string
  title: string
  content: string
  content_color: string,
  description?: string
  per_night_tax_price: string // 税后价格
  per_night_tax_price_desc?: string // 税后价格描述
  item_list: {
    title: string
    content: string
    desc: string
  }[]
}
interface PriceDetail {
  title: string
  currency_symbol: string
  price_items: PriceItemData[]
  pay_online: {
    title: string
    total_price: string
    total_price_desc: string
    per_night_price: string
    per_night_price_desc: string
    taxes_desc: string
  }
  gift: {
    title: String
    gift_list: HotelTag[]
  }
}

export {
  HotelTag,
  Promotion,
  Discount,
  PriceDetail,
  PriceItemData
}
