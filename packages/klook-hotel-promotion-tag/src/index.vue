<template>
  <div v-if="forceShow || tag.name" v-galileo-click-tracker="galileoSpm" :data-spm-module="spmModule"  data-spm-virtual-item="__virtual" :class="`klk-hotel-promotion-tag ${forceShow && !tag.name ? 'force-show' : '' }`" :style="tagStyle">
    <span>{{ tag.name || '' }}</span>
    <nav-down v-if="showIcon" class="arrow-icon" :style="iconStyle"/>
  </div>
</template>

<script lang="ts">
import { Vue, Component, Prop } from 'vue-property-decorator'
import { HotelTag } from './types/index'
import NavDown from './svg/nav_down.vue'

@Component({
  components: {
    NavDown
  }
})
export default class HotelPromotionTag extends Vue {
  @Prop({ type: Object, default: () => ({})}) tag!: HotelTag
  @Prop({ type: Boolean, default: false }) forceShow!: boolean // 没有name也展示，强制兼容没有tag也可以展示明细
  @Prop({ type: Boolean, default: true }) showIcon!: boolean
  @Prop({ type: String, default: 'desktop' }) platform!: string
  @Prop({ type: String }) oid!: string  // 这个 oid 存在 hotel_id 和 rateplane_id   需要带上前缀
  @Prop({ type: String, default: '' }) spm!: string

  get tagStyle() {
    const { text_color, bg_color, border_color } = this.tag
    return {
      color: text_color || '#ffffff',
      backgroundColor: bg_color || '#FF5B00',
      border: border_color ? `1px solid ${border_color}` : 'none',
      padding: this.showIcon ? '1px 4px 1px 6px' : '1px 6px'
    }
  }

  get iconStyle() {
    const { text_color } = this.tag
    return {
      width: '12px',
      height: '12px',
      color: text_color || 'white'
    } 
  }

  get galileoSpm() {
    return {
      spm: this.spmModule.split('?')[0],
    }
  }

  get spmModule() {
    return this.spm || `PromotionRateTag${this.oid ? `?oid=${this.oid}` : ''}`
  }
}
</script>

<style lang="scss" scoped>
.klk-hotel-promotion-tag {
  display: inline-flex;
  align-items: center;
  border-radius: 6px;
  font-weight: 600;
  font-size: 12px;
  line-height: 1.5;
  cursor: pointer;
  max-width: 100%;
  > .arrow-icon {
    flex-shrink: 0;
    margin-top: -1px;
    transform: rotate(-90deg);
  }
  > span {
    max-width: 300px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  &.force-show {
    display: none;
  }
}
</style>
