<template>
  <div>
    <div class="demo-wrapper">
      <klk-form class="demo-form">
        <klk-form-item label="标签文案">
          <klk-input v-model="tagInfo.desc" />
        </klk-form-item>
        <klk-form-item label="是否展示右侧箭头">
          <klk-switch v-model="showIcon"></klk-switch>
        </klk-form-item>
      </klk-form>
    </div>
    <div style="display: flex">
      <HotelPromotionTag :tag="tagInfo" :showIcon="showIcon" />
      <div style="width: 100px;padding-left:10px">默认</div>
      <HotelPoptipTag
        :tag="tagInfo"
        :showIcon="showIcon"
        platform="desktop"
        @show="show"
        @hide="hide"
        :promotion="promotion_info.promotion"
      />

      <div style="width: 100px;padding-left:10px">PoptipTag</div>
      <HotelBottomsheetTag
        :tag="tagInfo"
        :showIcon="showIcon"
        :oid="456"
        @show="show"
        @confirm="hide"
        platform="mobile"
        :promotion="promotion_info.promotion"
      >
      </HotelBottomsheetTag>
      <div style="width: 150px;padding-left:10px">BottomsheetTag</div>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue } from "vue-property-decorator";
import HotelPromotionTag from "../index.vue";
import HotelPoptipTag from "../components/poptip-tag.vue";
import HotelBottomsheetTag from "../components/bottomsheet-tag.vue";

@Component({
  name: "Demo",
  components: {
    HotelPromotionTag,
    HotelPoptipTag,
    HotelBottomsheetTag,
  },
})
export default class HotelPromotionTagDemo extends Vue {
  tagInfo = {
    name: "Save -¥200",
    text_color: '#FF5B00',
    bg_color: "#FFF0E5"
  };

  promotion_info = {
    icon: "",
    name: "Save -¥200",
    text_color: "#FF5B00",
    bg_color: "#FFF0E5",
    border_color: "",
    promotion: {
      check_in: "2023-05-12",
      check_out: "2023-05-15",
      room_num: 1,
      title: "Offer Description",
      desc: "May 1st - May 4th, 3 nights in total",
      currency_symbols: "HK$",
      original_per_night_price: "870",
      original_price_desc: "3 nights total,HK$2,610",
      per_night_price: "850",
      price_desc: "3 nights total,HK$ 2,550",
      discount: {
        sign: "-",
        currency_symbols: "HK$",
        price: "120",
        per_night_price: "72.8",
        price_desc: "入住1间房3晚, ¥145.6",
        discount_tags: [
          {
            price_desc: "-HK$80",
            tag: {
              type: 0,
              id: "",
              icon: "",
              name: "50% off for a limited time",
              desc: "First order for new users only",
              text_color: "#FF5B00",
              bg_color: "rgba(255, 240, 229, 0.6)",
              border_color: "#FF5B00",
              end_time: "",
            },
          },
          {
            price_desc: "-HK$40",
            tag: {
              type: 0,
              id: "",
              icon: "",
              name: "50% off for a limited time",
              desc: "First order for new users only",
              text_color: "#FF5B00",
              bg_color: "rgba(255, 240, 229, 0.6)",
              border_color: "#FF5B00",
              end_time: "",
            },
          },
          {
            price_desc: "-HK$40",
            tag: {
              type: 0,
              id: "",
              icon: "",
              name: "KLOOK Member Price",
              desc: "Available for a limited time until this Friday",
              text_color: "#FF5B00",
              bg_color: "rgba(255, 240, 229, 0.6)",
              border_color: "#FF5B00",
              end_time: "",
            },
          },
          {
            price_desc: "-HK$40",
            tag: {
              type: 0,
              id: "",
              icon: "",
              name: "50% off for a limited time",
              desc: "First order for new users only",
              text_color: "#FF5B00",
              bg_color: "rgba(255, 240, 229, 0.6)",
              border_color: "#FF5B00",
              end_time: "",
            },
          },
        ],
      },
      gift: {
        gift_list: [
          {
            type: 0,
            id: "",
            icon: "https://res.klook.com/image/upload/local_tour_gddq0v.png",
            name: "住3晚送1张住宿券",
            desc: "",
            text_color: "#FF5B00",
            bg_color: "",
            border_color: "",
            end_time: "",
          },
          {
            type: 0,
            id: "",
            icon: "https://res.klook.com/image/upload/v1639320783/UED%20Team%EF%BC%88for%20DE%20only%EF%BC%89/System%20Icon/Hotel/Benifit/icon_hotel_parking_xs.png",
            name: "住3晚送1张住宿券",
            desc: "",
            text_color: "#FF5B00",
            bg_color: "",
            border_color: "",
            end_time: "",
          },
        ],
      },
      tip: {
        type: 0,
        id: "",
        icon: "",
        name: "Only 2 rooms left!",
        desc: "",
        text_color: "#FF5B00",
        bg_color: "",
        border_color: "",
        end_time: "",
      },
    },
  };

  showIcon = true;

  get platform() {
    return navigator.userAgent.match(/iPhone|iPad|iPod|Mobile/i)
      ? "mobile"
      : "desktop";
  }

  show() {
    console.log('show')
  }

  hide() {
    console.log('hide')
  }
}
</script>

<style lang="scss">
.markdown-body img {
  background-color: initial;
}
.demo-wrapper {
  display: flex;
  align-items: flex-start;

  .demo-hotel-card {
    flex-shrink: 0;
  }
}
.demo-form {
  flex: 1 1 0;

  .klk-form-item {
    flex-direction: row;
    align-items: center;

    label {
      margin-right: 12px;
      width: 100px;
    }
  }

  .klk-form-item-content {
    min-width: 300px;
  }

  .tag-form-item {
    flex-direction: column;
    align-items: flex-start;
  }

  .tag-wrapper {
    display: flex;
    align-items: center;
    flex-wrap: wrap;

    &-item {
      display: flex;
      align-items: center;
      padding: 4px 10px;

      svg {
        margin-left: 6px;
        cursor: pointer;
      }
    }
  }
}
</style>
