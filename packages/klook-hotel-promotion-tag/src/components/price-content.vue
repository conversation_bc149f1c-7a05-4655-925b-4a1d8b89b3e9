<template>
  <div
    :class="`hotel-price-content hotel-price-content-${platform}`"
    @click.stop.prevent
  >
    <!-- title -->
    <div v-if="showTitle && priceDetail.title" class="top-title-wrap">
      <b>{{ priceDetail.title }}</b>
    </div>
    <!-- price detail -->
    <template v-if="showOriginPrice">
      <price-wrap
        v-for="(item, index) in priceDetail.price_items"
        :key="index"
        :price-item="item"
        :platform="platform"
        :type="type"
        :is-show-detail="isShowDetail(item)"
      />
    </template>
    <!-- Sell sell price -->
    <div v-if="showSellPrice && pay_online" class="sell-price">
      <div class="price-item-title">
        <b>{{ pay_online.title }}</b>
        <p>
          <i>{{ pay_online.total_price_desc }}</i>
          <b>{{ pay_online.total_price }}</b>
        </p>
      </div>
      <div class="tip-wrap">
        <div v-if="pay_online.per_night_price">
          <i>{{ pay_online.per_night_price_desc }}</i> 
          <b>{{ pay_online.per_night_price }} </b>
        </div>
        <div>{{ pay_online.taxes_desc }}</div>
      </div>
    </div>
    <!-- Gift -->
    <dl v-if="showGift && isShowGift" class="gift-list">
      <dt>{{ priceDetail.gift.title }}</dt>
      <dd
        class="gift-item"
        v-for="(item, index) in priceDetail.gift.gift_list"
        :key="index"
      >
        <div>
          <img v-if="item.icon" :src="item.icon" alt="" />
          <span :style="getTagStyle(item)">{{ item.name }}</span>
        </div>
        <div class="gift-desc" v-if="item.desc">{{ item.desc }}</div>
      </dd>
    </dl>
  </div>
</template>

<script lang="ts">
import { Vue, Component, Prop } from "vue-property-decorator";
import { PriceDetail, HotelTag } from "../types/index";
import PriceWrap from "./price/price-wrap.vue";

@Component({
  components: {
    PriceWrap
  },
})
export default class PriceContent extends Vue {
  @Prop({ type: Object, default: () => ({}) }) priceDetail!: PriceDetail;
  @Prop({ type: String, default: "desktop" }) platform!: string;
  @Prop({ type: Boolean, default: true }) showTitle!: boolean;
  @Prop({ type: Boolean, default: true }) showOriginPrice!: boolean;
  @Prop({ type: Boolean, default: true }) showDiscount!: boolean;
  @Prop({ type: Boolean, default: true }) showSellPrice!: boolean;
  @Prop({ type: Boolean, default: true }) showGift!: boolean;
  @Prop({ type: String, default: 'content' }) type!: 'collapse' | 'content' | 'payment' | 'normal'; /**
 * 1、collapse：popPip 是变成了normal全部展开， bottomSheet 内容都是一样的, 都展开. 是collapse
   2、content是默认模式，是默认展开discount，新版已经没有discount，变成全部展开
 * 3、下单页支付明细部分mWeb只展开优惠券部分；web都不展开, 但是优惠券部分是平铺
 * 4、normal 全部展开，全部div布局，全部无箭头(voucher下单页支付明细)
 */

  $t!: Function;

  getTagStyle(tag: HotelTag) {
    const { bg_color, border_color, text_color } = tag;
    return {
      color: text_color || "#FF5B00",
      border: border_color ? `1px solid ${border_color}` : "none",
      background: bg_color || "none",
    };
  }

  get isShowGift() {
    const { gift } = this.priceDetail;
    return gift && gift.gift_list && gift.gift_list.length;
  }

  get currency_symbol() {
    return this.priceDetail.currency_symbol;
  }

  get pay_online() {
    return this.priceDetail.pay_online;
  }
  
  /**
    "pretax_price",
    "taxes_and_fee",
    "upgrades",
    "discount",
    "credit_and_gift"
   */
  isShowDetail(item: any) {
    if (this.type === 'collapse') {
      return true
    } else if(this.type === 'payment') {
      return item.type === 'credit_and_gift'
    } else if (this.type === 'content') {
      return true
    } else {
      return true
    }
  }
}
</script>

<style lang="scss" scoped>
.hotel-price-content {
  color: $color-text-primary;
  .top-title-wrap {
    padding: 16px 0;
    text-align: center;
    > b {
      @include font-heading-xs-v2;
    }
  }
  .sell-price {
    .price-item-title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      color: $color-text-primary;
      > b {
        margin: 0;
        @include font-body-m-bold;
      }
      > p {
        margin: 0;
        text-align: right;
        @include font-body-s-regular;
        > i {
          margin: 0 4px;
          font-style: normal;
          @include font-caption-m-regular;
          color: $color-text-secondary;
        }
        > b {
          font-style: normal;
          @include font-heading-xs;
        }
      }
    }

    overflow: hidden;
    .tip-wrap {
      text-align: right;
      color: $color-text-secondary;
      & > div {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        @include font-body-xs-regular;
        i {
          font-style: normal;
          margin-right: 4px;
        }
        b {
          @include font-body-s-regular-v2;
          color: $color-text-primary;
          font-weight: normal;
        }
      }
    }
  }
  .gift-list {
    margin-top: 16px;
    padding: 12px 10px;
    background: #fff5f5;
    border-radius: $radius-l;
    text-align: left;
    > dt {
      color: $color-text-primary;
      margin: 0 0 8px;
      font-style: normal;
      @include font-body-s-bold;
    }
    .gift-item {
      overflow: hidden;
      margin-bottom: 12px;
      padding: 0;
      &:last-child {
        margin-bottom: 0;
      }
      & > div:first-child {
        display: flex;
        align-items: center;

        > img {
          width: 14px;
          height: 14px;
          flex-shrink: 0;
          margin-right: 4px;
        }
        > span {
          @include font-body-s-regular;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
        }
      }
      .gift-desc {
        @include font-body-s-regular;
        margin-top: 2px;
        color: $color-text-secondary;
      }
    }
  }
  &.hotel-price-content-desktop {
    .top-title-wrap {
      padding-top: 0;
      text-align: center;
      padding-bottom: 16px;
    }
  }
}
</style>
