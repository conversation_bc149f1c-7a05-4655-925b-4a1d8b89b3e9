<template>
  <klk-poptip
    v-model="showPopTip"
    v-bind="popTipProps"
    :class="{'poptip-show': showPopTip}"
    @show="$emit('show')"
    @hide="$emit('hide')"
    @click.stop.prevent.native
    class="klk-hotel-price-poptip-tag"
  >
    <slot name="tag">
      <HotelPromotionTag :spm="spmModule" :class="{'is-poptip-show': showPopTip}" :tag="tag" :showIcon="showIcon" :platform="platform" :force-show="forceShow" />
    </slot>
    <slot slot="content">
      <slot name="header"></slot>
      <slot name="content">
        <PriceContent :style="{margin: '-16px -20px', padding: '16px 20px'}" :platform="platform" :price-detail="priceDetail" type="collapse" />
      </slot>
      <slot name="footer"></slot>
    </slot>
  </klk-poptip>
</template>

<script lang="ts">
import { Vue, Component, Prop } from 'vue-property-decorator'
import { HotelTag, Promotion } from '../types/index'
import HotelPromotionTag from '../index.vue'
import PriceContent from './price-content.vue'

const defaultProps = {
  zIndex: 9999,
  placement: 'top-end',
  offset: [0, 12],
  width: 375,
  'max-width': 375,
  'max-height': 600,
  preventOverflow: true,
  flip: true,
  appendToBody: false
}
@Component({
  components: {
    HotelPromotionTag,
    PriceContent
  }
})
export default class HotelPoptipPrice extends Vue {
  @Prop({ type: Object, default: () => ({})}) poptipProps!: any
  @Prop({ type: Object, default: () => ({})}) tag!: HotelTag
  @Prop({ type: Object, default: () => ({})}) priceDetail!: Promotion
  @Prop({ type: Boolean, default: true }) showIcon!: boolean
  @Prop({ type: String, default: 'desktop' }) platform!: string
  @Prop({ type: String }) oid!: string  // 这个 oid 存在 hotel_id 和 rateplane_id   需要带上前缀
  @Prop({ type: Boolean, default: false }) customOpen!: boolean // 自定义出现
  @Prop({ type: Boolean, default: false }) forceShow!: boolean // 没有name也展示，强制兼容没有tag也可以展示明细
  @Prop({ type: Boolean, default: false }) appendToBody	!: boolean 

  showPopTip = false

  get spmModule() {
    if( this.oid ) {
      let { discount_tags } = this.priceDetail.discount || {}
      let ext = ''
      if(Array.isArray(discount_tags)) {
        const PromotionRate = discount_tags.map(item => item?.tag?.name_en).filter(Boolean)
        if (PromotionRate.length) {
          ext = '&ext=' + encodeURIComponent(JSON.stringify({ PromotionRate }))
        }
      }
      return `PromotionRateTag?oid=${this.oid}${ext}`
    }
  }

  open(visible = true) {
    this.showPopTip = visible
  }

  get popTipProps() {
    return Object.assign(defaultProps, {
      trigger:  this.customOpen ? 'none' : 'hover',
      flip: !this.customOpen,
      appendToBody: this.appendToBody,
    }, this.poptipProps || {})
  }
}
</script>

<style lang="scss" scoped>
.klk-hotel-price-poptip-tag {
  ::v-deep .is-poptip-show.klk-hotel-promotion-tag {
    > .arrow-icon {
      transform: rotate(-180deg);
    }
  }
  ::v-deep .klk-poptip-popper-inner {
    padding: 16px 20px;
    margin: 0;
  }

  &.poptip-show {
    ::v-deep .klk-hotel-promotion-tag {
      > .arrow-icon {
        transform: rotate(-180deg);
      }
    }
  }

}
</style>
