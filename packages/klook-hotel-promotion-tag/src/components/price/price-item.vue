<template>
  <!-- onClick={ item.action?.bind(this) || (() => {}) } -->
  <div :class="['price-item', isMobile ? 'mobile' : 'desktop']">
    <div class="price-item-top">
      <div class="label_section" @click="labelClick">
        <span class="label" >
          <span v-if="item.title" class="label_text"> {{ item.title }}</span>

          <nav-down v-if="leftIcon" class="label_icon" />
        </span>
        <span v-if="item.desc" class="label_desc"> {{ item.desc }} </span>
      </div>

      <span class="value_section">
        <span
          v-if="item.content"
          :style="{ color: item.content_color || item.color || undefined }"
          class="value_text"
        >
          {{ item.content }}
        </span>

        <nav-down v-if="rightIcon" class="value_icon" />
      </span>
    </div>
    <slot></slot> 
  </div>
</template>

<script lang="ts">
import { Vue, Component, Prop } from "vue-property-decorator";
import NavDown from "../../svg/nav_down.vue";

@Component({
  components: {
    NavDown,
  },
})
export default class PriceItemCom extends Vue {
  @Prop({ type: Boolean, default: false }) isMobile!: boolean;
  @Prop({ type: String, default: "desktop" }) platform!: string;
  @Prop({ type: Boolean, default: false }) leftIcon!: string;
  @Prop({ type: Boolean, default: false }) rightIcon!: string;

  @Prop({ type: Object, default: () => ({}) }) item!: any;

  labelClick(e: Event) {
    e.stopPropagation();
    this.$emit("label-click");
  }
}
</script>

<style lang="scss" scoped>
.price-item {
  display: block;
  padding: 0 0 12px 0;

  &:last-child {
    padding-bottom: 0;
  }

  .price-item-top {
    display: flex;
    width: 100%;
    justify-content: space-between;
    align-items: flex-start;
    justify-content: space-between;
    color: $color-text-primary;

    .label_section {
      cursor: pointer;
      @include font-body-s-regular-v2;

      min-width: 100px;

      .label_text {
        text-align: left;
      }

      .label_icon {
        margin-left: 4px;
        width: 14px;
        height: 14px;
      }

      .label {
        display: flex;
        align-items: center;
      }

      .label_desc {
        color: $color-text-secondary;

        @include font-body-xs-regular;
        text-align: left;

        display: block;
        margin-top: 0;
      }
    }
    .value_section {
      @include font-body-s-regular-v2;

      margin-left: 12px;
      color: $color-text-primary;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      flex: 1 1 25%;
      min-width: 25%;

      .value_icon {
        margin-left: 4px;
        color: $color-text-primary;
        width: 16px;
        height: 16px;
      }

      .value_text {
        text-align: right;
      }
    }
  }



  &.desktop {
    .price-item-top {
      .label_section {
        .label_icon {
          width: 16px;
          height: 16px;
        }
      }
    }
  }
}
</style>