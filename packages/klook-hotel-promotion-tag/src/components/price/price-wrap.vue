<template>
  <component
    :is="contentWrap"
    v-model="showDetail"
    v-bind="contentWrapProps"
    :class="`price-item-wrap ${isMobile ? 'mobile' : 'desktop'}  ${
      showDetail ? 'is-show-detail' : ''
    } ${contentWrap}`"
  >
    
    <price-item
      class="price-title"
      :item="priceItem"
      @label-click="labelClick"
      :left-icon="showLeftIcon"
      :right-icon="!!(contentWrap === 'klk-poptip' && priceItem.item_list)"
    >
       <div class="price-tax-price" v-if="priceItem.per_night_tax_price">
          <span class="price-tax-price-desc" v-if="priceItem.per_night_tax_price_desc"> {{ priceItem.per_night_tax_price_desc}}</span>
          <span v-if="priceItem.per_night_tax_price"> {{ priceItem.per_night_tax_price}}</span>
       </div>
    </price-item>

    <!-- <transition name="expand"> -->
    <!--  slot="content" -->
    <component v-if="priceItem.item_list" :is="itemWrap" v-bind="itemWrapProps">
      <div v-show="showDetail" class="price-item-wrap-popper">
        <!-- <price-item
          v-if="!isMobile && type === 'payment'"
          class="popper-title"
          :item="priceItem"
        /> -->

        <div class="price-item-wrap-popper-desc" v-if="priceItem.description">
          {{ priceItem.description }}
        </div>
    
        <slot name="popper-content">
          <div class="popper-content">
            <template v-for="(itemPrice, idx) in priceItem.item_list">
              <price-item
                class="popper-content--child"
                :key="idx"
                :item="itemPrice"
              />
            </template>
          </div>
        </slot>
      </div>
    </component>
  </component>
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch } from "vue-property-decorator";
import PriceItem from "./price-item.vue";
import { PriceItemData } from "../../types/index";

@Component({
  components: {
    PriceItem,
  },
})
export default class PriceWrap extends Vue {
  @Prop({ type: String, default: "desktop" }) platform!: string;
  @Prop({ type: Boolean, default: true }) isShowDetail!: boolean;
  @Prop({ type: String, default: "content" }) type!:
    | "collapse"
    | "content"
    | "payment"
    | 'normal';

  @Prop({ type: Object, default: () => ({}) }) priceItem!: PriceItemData;

  showDetail = false;

  @Watch("isShowDetail", { immediate: true })
  isShowDetailChange(val: boolean) {
    this.showDetail =  this.type === 'normal' || val;
  }

  get isMobile() {
    return this.platform === "mobile";
  }

  get contentWrap() {
    return "div";
  }

  get contentWrapProps() {
    return this.contentWrap === 'div'
      ? {}
      : {
          width: 390,
          maxWidth: 390,
          placement: "top-end",
          trigger: "hover",
          maxHeight: 500,
          zIndex: 1001,
          offset: [10, 10],
        };
  }

  get showLeftIcon() {
    if (this.type === 'normal') {
      return false
    }
    return this.contentWrap === "div" &&
      this.priceItem.item_list &&
      (this.type !== "collapse" || this.isMobile)
  }


  get itemWrap() {
    return this.contentWrap === "div" ? "transition" : "div";
  }

  get itemWrapProps() {
    return this.itemWrap === "transition"
      ? {
          name: "expand",
        }
      : {
          slot: "content",
        };
  }

  labelClick() {
    if (!this.showLeftIcon) {
      return;
    }
    this.showDetail = !this.showDetail;
  }
}
</script>

<style lang="scss" scoped>
.price-item-wrap {
  margin-bottom: 12px;

  &.is-show-detail {
    ::v-deep .price-item-top {
      .value_icon,
      .label_icon {
        transform: rotate(-180deg);
      }
    }
  }

  ::v-deep .price-item.price-title {
    // title 部分
    padding-bottom: 0;
    .label_section {
      @include font-body-m-bold-v2;
    }
    .value_section {
      @include font-body-m-bold-v2;
    }
    .price-tax-price {
      margin-top: 2px;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      color: $color-text-primary;
      @include font-body-s-regular-v2;
      .price-tax-price-desc {
        margin-right: 4px;
        color: $color-text-secondary;
        @include font-body-xs-regular;
      } 
    }
  }
  .price-item-wrap-popper-desc {
    padding-top: 4px;
    color: $color-text-secondary;
    @include font-paragraph-s-regular;
  }
  //内容部分
  .popper-content {
    background-color: $color-bg-3;
    border-radius: $radius-l;
    padding: 12px;
    margin-top: 10px;
    position: relative;
    &::after {
      position: absolute;
      content: "";
      width: 0;
      height: 0;
      top: -6px;
      right: 24px;
      border-style: solid;
      border-width: 0 7px 6px;
      border-color: transparent transparent #F5F5F5;
    }
    .price-item.popper-content--child {
      .price-item-top {
        .label_section {
          .label_text {
            @include font-body-s-regular;
          }
        }
        .value_section {
          .value_text {
            @include font-body-s-regular;
          }
        }
      }
      .label_desc {
        @include font-body-s-regular;
      }
    }
  }

  &.klk-poptip {
    display: block;
    margin-bottom: 8px;


    ::v-deep .price-item.price-title {
       // title 部分
      .label_section {
        @include font-body-m-regular;
      }
      .value_section {
        @include font-body-m-regular;
      }
    }

    // poptip

    .price-item-wrap-popper {
      & > div {
        &.popper-title {
          padding: 0;
          @include font-body-m-bold;
          &.price-item ::v-deep {
            .label_section, .value_section {
              @include font-body-m-bold;
            }
          }
        }
      }
    }

    // .klk-poptip-reference {
    //   cursor: pointer;
    //   & > .price-item {
    //     padding: 0 0 8px 0;
    //   }
    // }
  }

  &.desktop {
    .price-item-wrap-popper-desc {
      padding-bottom: 4px;
    }
  }
}

.expand-enter,
.expand-leave-to {
  opacity: 0;
}

// 向上淡入显示
.expand-enter-active,
.expand-leave-active {
  transition: all 0.1s ease-in-out;
}
</style>
