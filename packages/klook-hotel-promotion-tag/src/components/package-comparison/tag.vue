<template>
  <div v-if="savePrice || forceShow" v-galileo-click-tracker="{ spm: 'PackageComparison'}"  :class="`comparison-tag ${forceShow && !savePrice ? 'force-show' : '' }`" v-bind="attrs">
    <span>{{ $t('83425') }}</span>
    <svg-icon name="mobile-hotel#icon-vs" size="20" />
    <span>{{ $t('83426') }}</span>
    <b>{{ savePrice }}</b>
    <svg-icon v-if="showIcon" :name="`common#icon_navigation_chevron_${arrowDirection}_xxs`" size="12" />
  </div>
</template>

<script lang="ts">
import { Vue, Component, Prop } from 'vue-property-decorator'

@Component
export default class ComparisonTag extends Vue {
  @Prop({ type: String, default: '' }) savePrice!: string | null
  @Prop({ type: Boolean, default: true }) showIcon!: boolean
  @Prop({ type: String, default: 'right' }) arrowDirection!: boolean
  @Prop({ type: Boolean, default: false }) forceShow!: boolean
  @Prop({ type: String, default: '' }) parentSpm!: string

  get attrs() {
    const item = 'PackageComparison'
    return this.parentSpm ? {
      'data-spm-item': item
    } : {
      'data-spm-module': item,
      'data-spm-virtual-item': '__virtual'
    }
  }
}
</script>

<style lang="scss" scoped>
.comparison-tag {
  display: inline-flex;
  align-items: center;
  cursor: pointer;
  line-height: 20px;
  font-size: $fontSize-caption-m;
  padding: 0 6px;
  border-radius: $radius-s;
  background-color: $color-brand-primary-light;
  .vs-icon {
    margin: 0 4px;
    flex-shrink: 0;
  }

  span {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    &:first-child {
      flex-shrink: 1;
    }
  }
  .vs-icon + span {
    flex-shrink: 2;
  }

  b,
  .svg-icon {
    margin-left: 4px;
    color: $color-brand-primary;
    flex-shrink: 0;
  }

  &.force-show {
    display: none;
  }
}
</style>
