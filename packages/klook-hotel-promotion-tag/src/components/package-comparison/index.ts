import { Vue, Component } from 'vue-property-decorator'
import PackageComparison from './package.vue'
import ComparisonTag from './tag.vue'
import PoptipPackageComparison from './poptip'

// export default PackageComparison
export { PackageComparison, ComparisonTag, PoptipPackageComparison }

@Component({
  inheritAttrs: false
})
export class PtPackageComparison extends Vue {
  arrowDirection = 'right'
  delayTimmer: any = null
  packageInstance: PackageComparison | null = null
  visible = false

  createPackageInstance() {
    const parityInfo = (this.$attrs as Record<string, any>)['parity-info']

    const instance: any = Object.assign(
      new PackageComparison({
        propsData: { parityInfo }
      }),
      {
        $t: (this as any).$t.bind(this),
        // $store: this.$store
      }
    ).$mount()

    const { left, right, bottom } = this.$el.getBoundingClientRect()
    const { clientWidth } = document.documentElement
    Object.assign((instance.$el as HTMLElement).style, {
      display: 'none',
      padding: '20px',
      width: '710px',
      position: 'absolute',
      'z-index': 9,
      'border-radius': '12px',
      'box-shadow': '0px 4px 20px 0px rgb(0, 0, 0, 0.08)',
      top: bottom + window.scrollY + 5 + 'px',
      left: (clientWidth - right < left ? right - 710 : left) + window.scrollX + 'px'
    })

    ;['mouseenter', 'mouseleave'].forEach(action => instance.$el.addEventListener(action, this[action as 'mouseleave']))

    document.body.appendChild(instance.$el)

    return instance as PackageComparison
  }

  openPopper() {
    if (this.visible) {
      return
    }
    this.arrowDirection = 'down'
    this.packageInstance = this.packageInstance || this.createPackageInstance()

    (this as any).$inhouse.track('action', this.$el)
    ;(this.packageInstance.$el as HTMLElement).style.display = 'block'
    this.visible = true
  }

  mouseenter() {
    clearTimeout(this.delayTimmer)
    this.delayTimmer = setTimeout(this.openPopper, 300)
  }

  mouseleave() {
    clearTimeout(this.delayTimmer)
    if (this.packageInstance) {
      // 3秒删除实例
      this.delayTimmer = setTimeout(() => {
        ;(this.packageInstance!.$el as HTMLElement).style.display = 'none'
        this.visible = false
        this.arrowDirection = 'right'
        this.delayTimmer = setTimeout(this.removePopper, 3000)
      }, 300)
    }
  }

  removePopper() {
    ;['mouseenter', 'mouseleave'].forEach(action => this.packageInstance!.$el.removeEventListener(action, this[action as 'mouseleave']))
    document.body.removeChild(this.packageInstance!.$el)
    this.packageInstance!.$destroy()
    this.packageInstance = this.delayTimmer = null
  }

  render(h: Vue['$createElement']) {
    const parityInfo = (this.$attrs as Record<string, any>)['parity-info']

    return parityInfo?.discount && h(ComparisonTag, {
      style: 'position: relative;',
      props: {
        arrowDirection: this.arrowDirection,
        savePrice: parityInfo.discount,
        parentSpm: (this.$attrs as Record<string, any>)['parent-spm']
      },
      nativeOn: {
        click(e: Event) {
          e.stopImmediatePropagation()
          return e.preventDefault()
        },
        mouseenter: this.mouseenter,
        mouseleave: this.mouseleave
      }
    })
  }
}

@Component({
  inheritAttrs: false
})
export class BsPackageComparison extends Vue {
  open(e: Event) {
    if (this.$attrs['custom-open']) {
      return
    }

    const parityInfo = (this.$attrs as Record<string, any>)['parity-info']
    const vm = this

    let pc: null | Vue = Object.assign(new PackageComparison({
      propsData: { parityInfo }
    }), {
      $t: (vm as any).$t.bind(vm),
      // $store: vm.$store
    })

    let instance: any = new Vue({
      data() {
        return {
          visible: true
        }
      },
      render(this: Vue & { visible: boolean }, h: Vue['$createElement']) {
        return h('klk-bottom-sheet', {
          props: { visible: this.visible, transfer: true },
          on: {
            close: () => {
              document.body.removeChild(this.$el)
              instance.$destroy()
              instance = pc = null
            }
          }
        }, [
          // @ts-ignore
          pc._render(),
          vm.$slots.footer
            ? h('template', { slot: 'footer' }, vm.$slots.footer)
            : h('klk-button', {
              slot: 'footer',
              props: { block: true },
              nativeOn: {
                click: () => {
                  vm.$emit('confirm')
                  this.visible = false
                }
              }
            }, [
              vm.$attrs.buttonText || (vm as any).$t('74401-查看酒店详情')
            ])
        ])
      }
    }).$mount();

    (vm as any).$inhouse.track('action', this.$el)

    e && e.stopImmediatePropagation()
    return e && e.preventDefault()
  }

  render(
    h: Vue['$createElement']
  ) {
    const parityInfo = (this.$attrs as Record<string, any>)['parity-info']
    return parityInfo?.discount && h(ComparisonTag, {
      props: {
        savePrice: parityInfo.discount,
        parentSpm: (this.$attrs as Record<string, any>)['parent-spm']
      },
      nativeOn: {
        click: this.open
      }
    })
  }
}
