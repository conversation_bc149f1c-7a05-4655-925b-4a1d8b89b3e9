import { Vue, Component, Prop } from 'vue-property-decorator'
import HotelPackageComparison from './package.vue'
import ComparisonTag from './tag.vue'
import { ParityInfo } from '../../types'

@Component({
  components: {
    HotelPackageComparison,
    ComparisonTag
  }
})
export default class PoptipPackageComparison extends Vue {
    @Prop({ type: Object, default: null }) parityInfo!: ParityInfo
    @Prop({ type: Object, default: null }) poptipProps!: object
    @Prop({ type: Boolean, default: false }) customOpen!: boolean // 自定义出现
    @Prop({ type: Boolean, default: false }) forceShow!: boolean
    showPopTip = false

    open(state: boolean) {
      this.showPopTip = state
    }

    get arrowDirection() {
      return this.showPopTip ? 'up' : 'right'
    }

    render() {
      const parityInfo = this.parityInfo

      const attrs = Object.assign({}, {
        value: this.showPopTip,
        maxWidth: 580,
        maxHeight: 700,
        placement: 'top-start',
        trigger: this.customOpen ? 'none' : 'hover'
      }, this.poptipProps)

      return (parityInfo?.discount || this.forceShow) && <klk-poptip attrs={attrs} onShow={() => this.open(true)} onHide={() => this.open(false)}>
        <comparison-tag arrowDirection={this.arrowDirection} savePrice={parityInfo.discount } forceShow={this.forceShow} />

        <hotel-package-comparison slot="content" parityInfo={parityInfo}/>
      </klk-poptip>
    }
}
