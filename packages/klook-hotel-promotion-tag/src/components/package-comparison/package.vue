<template>
  <dl v-if="canRender" class="package-comparison" :class="'platform-' + platform">
    <slot v-if="showTitle" name="header">
      <dt v-if="parityInfo.title" class="package-comparison-title">{{ parityInfo.title }}</dt>
    </slot>
    <dd v-if="parityInfo.save_discount" class="package-comparison-item">
      <div class="current-header font-xl">
        {{ parityInfo.save }}
        <p>{{ parityInfo.save_discount }}</p>
      </div>
    </dd>
    <dd class="package-comparison-item" :class="{ 'no-discount': !parityInfo.save_discount }">
      <div v-if="parityInfo.current_rate.price_info" class="price-item current-item">
        <b class="font-xl">{{ $t('83459') }}</b>
        <p class="price-info" v-html="formatPrice(parityInfo.current_rate.price_info)" />
        <p class="c-gray">{{ parityInfo.current_rate.price_info.per_night_desc }}</p>
      </div>
      <div v-if="parityInfo.only_rate.price_info" class="price-item only-item">
        <b class="font-xl">{{ $t('83460') }}</b>
        <p class="price-info" v-html="formatPrice(parityInfo.only_rate.price_info, 'xl')" />
        <p class="c-gray">{{ parityInfo.only_rate.price_info.per_night_desc }}</p>
      </div>
    </dd>
    <dd v-for="index in parityInfo.current_rate.rate_content_list.length" :key="index" class="package-comparison-item">
      <div class="flex-item current-item">
        <img :src="parityInfo.current_rate.rate_content_list[index - 1].icon_url" width="16" height="16" />
        <p>{{ parityInfo.current_rate.rate_content_list[index - 1].name }}</p>
      </div>
      <div v-if="parityInfo.only_rate.rate_content_list[index - 1]" class="flex-item only-item">
        <img :src="parityInfo.only_rate.rate_content_list[index - 1].icon_url" width="16" height="16" />
        <p>
          <b>{{ parityInfo.only_rate.rate_content_list[index - 1].prefix }}</b>
          <span class="c-gray">{{ parityInfo.only_rate.rate_content_list[index - 1].name }}</span>
        </p>
      </div>
    </dd>
    <dd v-if="parityInfo.tax_desc" class="package-comparison-tax-desc"> {{ parityInfo.tax_desc }}</dd>
  </dl>
</template>

<script lang="ts">
import { Vue, Component, Prop, Getter } from 'vue-property-decorator'
import { ParityInfo } from '~/pages/hotel/common/components/stay-card/card-info.d'

@Component
export default class PackageComparisonV2 extends Vue {
  @Prop({ type: Object, default: null }) parityInfo!: ParityInfo
  @Prop({ type: Boolean, default: true }) showTitle!: boolean
  @Getter platform!: string

  get canRender() {
    if (this.parityInfo) {
      const { only_rate, current_rate } = this.parityInfo
      return current_rate?.rate_content_list?.length && only_rate?.rate_content_list?.length
    }
  }

  formatPrice({ currency_symbols, selling_price_format }: Record<string, string>, font = 'xxl') {
    if (!selling_price_format) {
      return ''
    }

    // eslint-disable-next-line prefer-const
    let [price, smallPrice = ''] = String(selling_price_format).split('.')
    if (smallPrice) {
      smallPrice = '.' + smallPrice
    }
    return `
      ${currency_symbols}
      <b class="font-${font}">${price}</b>${smallPrice}
    `
  }
}
</script>

<style lang="scss" scoped>
.package-comparison {
  background-color: $color-bg-1;
  padding-top: 16px;

  &.platform-desktop {
    @include font-body-s-regular;
    ::v-deep .font-xl {
      @include font-heading-xs;
    }

    ::v-deep .font-xxl {
      @include font-heading-s;
    }

    .package-comparison-item > div {
      flex: 0 0 50%;
      padding-left: 20px;
      padding-right: 20px;
    }

    .current-header {
      padding-top: 12px;
      padding-bottom: 12px;
    }
  }

  &.platform-mobile {
    @include font-caption-m-regular;
    ::v-deep .font-xl {
      @include font-body-m-bold;
    }

    ::v-deep .font-xxl {
      @include font-heading-xs;
    }

    .package-comparison-item > div {
      flex: 0 0 50%;
      padding-left: 12px;
      padding-right: 12px;
    }

    .current-header {
      padding-top: 8px;
      padding-bottom: 8px;
    }
  }

  &-title {
    text-align: center;
    @include font-heading-xs;
    margin-bottom: 32px;
  }

  &-item {
    display: flex;
    text-align: left;
    &:nth-last-child(2) {
      align-items: flex-start;
      .current-item {
        border-bottom: 4px solid $color-brand-primary;
        border-bottom-left-radius: 16px;
        border-bottom-right-radius: 16px;
        padding-bottom: 60px;
      }

      .only-item {
        padding-bottom: 20px;
        border-bottom: 1px solid $color-border-normal;
        border-bottom-right-radius: 16px;
      }
    }

    &.no-discount {
      align-items: flex-end;
      .current-item {
        border-top: 4px solid $color-brand-primary;
        border-top-left-radius: 16px;
        border-top-right-radius: 16px;
        padding-top: 44px;
      }
    }

    .c-gray {
      color: $color-text-placeholder;
    }

    .flex-item {
      display: flex;
      padding-bottom: 8px;
      > img {
        margin-top: 2px;
      }
      > p {
        flex: 1;
        margin-left: 4px;
      }
    }

    .price-item {
      padding: 20px 12px;

      .price-info {
        margin: 12px 0 4px;
      }

      &.current-item {
        color: $color-brand-primary;
      }

      &.only-item {
        border-top: 1px solid $color-border-normal;
        border-top-right-radius: 16px;
      }
    }

    .current-header {
      padding-top: 8px;
      background-color: $color-brand-primary;
      color: $color-text-reverse;
      border-top-left-radius: 16px;
      border-top-right-radius: 16px;
    }

    .current-item {
      border-left: 4px solid $color-brand-primary;
      border-right: 4px solid $color-brand-primary;
      background-color: $color-brand-primary-light;
    }

    .only-item {
      border-right: 1px solid $color-border-normal;
    }
  }

  &-tax-desc {
    margin-top: 12px;
    margin-bottom: 12px;
    text-align: left;
    @include font-caption-m-regular;
    color: $color-text-placeholder;
  }
}
</style>
