<template>
  <div class="hotel-price-bottom-sheet">
    <div @click="showContent">
      <slot name="tag">
        <HotelPromotionTag :spm="spmModule" :tag="tag" :show-icon="showIcon" :force-show="forceShow" :platform="platform" />
      </slot>
    </div>
    <client-only>
      <klk-bottom-sheet class="hotel-price-bottom-sheet" :visible.sync="visible" v-bind="bottomSheetProps">

        <template slot="footer">
          <slot name="bottomsheetFooter">
            <klk-button block @click="confirm">{{ footerBottonText || $t('74401-See hotel details') }}</klk-button>
          </slot>
        </template>

        <slot>
          <slot name="header"></slot>
          <slot v-if="$scopedSlots.content" name="content"></slot>
          <PriceContent v-else :platform="platform" :priceDetail="priceDetail" type="collapse" />
          <slot name="footer"></slot>
        </slot>
      </klk-bottom-sheet>
    </client-only>
  </div>
</template>

<script lang="ts">
import { Vue, Component, Prop } from 'vue-property-decorator'
import { HotelTag, Promotion } from '../types/index'
import HotelPromotionTag from '../index.vue'
import PriceContent from './price-content.vue'

const defaultProps = {
  'can-pull-close': false,
  transfer: true
}
@Component({
  components: {
    HotelPromotionTag,
    PriceContent
  }
})
export default class HotelBottomsheetTag extends Vue {
  @Prop({ type: Object, default: () => ({})}) bottomsheetProps!: any
  @Prop({ type: Object, default: () => ({})}) tag!: HotelTag
  @Prop({ type: Object, default: () => ({})}) priceDetail!: Promotion
  @Prop({ type: Boolean, default: true }) showIcon!: boolean
  @Prop({ type: String, default: 'desktop' }) platform!: string
  @Prop({ type: String }) footerBottonText!: string
  @Prop({ type: String }) oid!: string  // 这个 oid 存在 hotel_id 和 rateplane_id   需要带上前缀
  @Prop({ type: Boolean, default: false }) customOpen!: boolean // 自定义出现
  @Prop({ type: Boolean, default: false }) forceShow!: boolean // 没有name也展示，强制兼容没有tag也可以展示明细

  visible = false

  $t!: Function

  get bottomSheetProps() {
    return Object.assign(defaultProps, this.bottomsheetProps || {})
  }

  get spmModule() {
    if( this.oid ) {
      let { discount_tags } = this.priceDetail.discount || {}
      let ext = ''
      if(Array.isArray(discount_tags)) {
        const PromotionRate = discount_tags.map(item => item?.tag?.name_en).filter(Boolean)
        if (PromotionRate.length) {
          ext = '&ext=' + encodeURIComponent(JSON.stringify({ PromotionRate }))
        }
      }
      return `PromotionRateTag?oid=${this.oid}${ext}`
    }
  }

  confirm() {
    this.$emit('confirm')
    setTimeout(() => {
      this.visible = false
    }, 200)
  }

  open(visible = true) {
    this.visible = visible
  }

  showContent(e: Event) {
    this.$emit('show')
    if (this.customOpen) {
      return
    }
    e.stopPropagation()
    e.preventDefault()
    this.visible = true
  }
}
</script>

<style lang="scss" scoped>
.hotel-price-bottom-sheet {
  ::v-deep .klk-bottom-sheet-body {
    padding: 0 20px 20px;
  }
  ::v-deep .klk-bottom-sheet-footer {
    margin: 0;
    padding: 8px 20px;
    border-top: 1px solid #E0E0E0;
  }
  ::v-deep .klk-bottom-sheet-inner {
    padding: 20px 0 0;
    &::before {
      content: '';
      display: block;
      position: absolute;
      left: 50%;
      top: 8px;
      margin-left: -16px;
      width: 32px;
      height: 4px;
      border-radius: 100px;
      background: #e6e6e6;
    }
  }
}
</style>
