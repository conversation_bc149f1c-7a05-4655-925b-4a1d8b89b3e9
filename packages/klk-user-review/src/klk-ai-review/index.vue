<template>
  <div
    class="ai-review-summary"
    :class="{
      'ai-review-summary--mobile': realPlatform === 'mobile'
    }"
  >
    <div>
      <div class="ai-review-summary__title">
        <img class="ai-review-summary__title-icon" :src="aiReviewInfo.icon">
        <span class="ai-review-summary__title-text">{{ aiReviewInfo.title }}</span>
      </div>
      <div class="ai-review-summary__mini-info">
        {{ aiReviewInfo.mini_info.tips }}
        <klk-poptip
          v-if="realPlatform !== 'mobile'"
          placement="bottom-start"
          trigger="hover"
          dark
          :title="aiReviewInfo.mini_info.pop_info.title"
          :max-height="400"
        >
          <p slot="content">{{ aiReviewInfo.mini_info.pop_info.content }}</p>
          <IconInformation class="ai-review-summary__mini-info-show" theme="outline" size="16" />
        </klk-poptip>
        <span v-else @click="showMiniInfo">
          <IconInformation class="ai-review-summary__mini-info-show" theme="outline" size="12" />
        </span>
      </div>
    </div>

    <div class="ai-review-summary__cards">
      <card-swiper :controller-offset="10">
        <card-swiper-item
          v-for="(summary, index) in aiReviewInfo.summary_list"
          :key="index"
          :class="cardColumnClass"
          :data-spm-module="`ReviewOverviewAIReviewSummary_LIST?idx=${index}&len=${aiReviewInfo.summary_list.length}`"
        >
          <klkAIReviewCard
            :helpful-api="helpfulApi"
            :hide-helpful-action="aiReviewInfo.hide_helpful_action"
            :summary="summary"
          ></klkAIReviewCard>
        </card-swiper-item>
      </card-swiper>
    </div>

    <klk-bottom-sheet
      v-if=" realPlatform === 'mobile' "
      show-close
      :visible.sync="showBottomSheet"
      :mask-closable="true"
      height="40%"
    >
      <div class="ai-review-summary__mini-info-bottom-sheet-title">{{ aiReviewInfo.mini_info.pop_info.title }}</div>
      <klk-markdown :content="aiReviewInfo.mini_info.pop_info.content"></klk-markdown>
    </klk-bottom-sheet>
  </div>
</template>
<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator'
import IconInformation from '@klook/klook-icons/lib/IconInformation'
import KlkBottomSheet from '@klook/klook-ui/lib/bottom-sheet'
import KlkPoptip from '@klook/klook-ui/lib/poptip'
import KlkMarkdown from '@klook/klook-ui/lib/markdown'
import { CardSwiper, CardSwiperItem } from '../klk-user-review-common/components/klook-responsive/index'
import { AiReviewData } from '../types/ai-review'
import klkAIReviewCard from './components/ai-review-card.vue'

@Component({
  name: 'AiReview',
  components: {
    IconInformation,
    KlkBottomSheet,
    KlkPoptip,
    KlkMarkdown,
    CardSwiper,
    CardSwiperItem,
    klkAIReviewCard
  }
})
export default class AiReview extends Vue {
  @Prop({ required: true }) aiReviewInfo!: AiReviewData
  @Prop({ required: false }) platform?: string
  @Prop({ required: false, default: '/v1/usrcsrv/review/summary/helpful' }) helpfulApi!: string

  get realPlatform() {
    return this.platform || this?.$store?.state?.klook?.platform || 'desktop'
  }

  showBottomSheet = false


  get isSingleCard() {
    return this.aiReviewInfo.summary_list.length === 1
  }

  get cardColumnClass() {
    return this.isSingleCard
      ? 'klk-col-md-1 klk-col-lg-1 klk-col-xl-1 klk-col-sm-1'
      : 'klk-col-md-2 klk-col-lg-2 klk-col-xl-2 klk-col-sm-1-2'
  }

  showMiniInfo() {
    this.showBottomSheet = true
  }
}
</script>
<style lang="scss" scoped>
.ai-review-summary {
  display: flex;
  width: 100%;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  background: $color-bg-1;
  //border: #55ac7d 1px solid;
  .ai-review-summary__title{
    display: flex;
    align-items: center;
  }
  .ai-review-summary__title-icon{
    width: 24px;
    height: 24px;
    margin: 0 6px 0 0;
  }
  .ai-review-summary__title-text{
    text-align: left;
    color: transparent;
    background-image: linear-gradient(90deg, #CB5EFF, #31C1FF);
    background-clip: text;
    @include font-paragraph-m-bold;
  }
  .ai-review-summary__mini-info{
    text-align: left;
    color: $color-text-secondary;
    @include font-body-xs-regular;
    display: flex;
    margin: 4px 2px 16px 0;
  }
  .ai-review-summary__mini-info-show {
    margin: 0 0 0 2px;
    color: $color-text-placeholder;
    vertical-align: sub;
  }
  .ai-review-summary__cards {
    display: flex;
    align-items: center;
    align-self: stretch;
  }
  .ai-review-summary__mini-info-bottom-sheet-title {
    text-align: center;
    color: $color-text-primary;
    @include font-heading-xs-v2;
    padding-bottom: 16px;
  }
}
.ai-review-summary--mobile {
  margin: 0;
  width: 100%;
  .ai-review-summary__title-text{
    @include font-paragraph-m-bold;
  }
  .ai-review-summary__mini-info{
    @include font-paragraph-xs-regular;
    margin: 2px 0 16px;
  }
  .ai-review-summary__mini-info-show {
    margin: 0 0 0 4px;
  }
}

.ai-review-summary {
  ::v-deep {
    .responsive-card-swiper-wrap {
      padding: 0;
      margin: 0;
      width: 100%;
    }
    .klk-poptip-popper {
      margin-left: -13px !important;
    }
    @media (min-width: 768px) {
      .ai-review-summary__cards .responsive-card-swiper-wrap .klk-card-swiper-next-btn {
        right: -16px !important;
      }
      .ai-review-summary__cards .responsive-card-swiper-wrap .klk-card-swiper-prev-btn {
        left: -16px !important;
      }
      .responsive-card-swiper-wrap .klk-card-swiper-next-btn {
        width: 32px;
        height: 32px;
      }
      .responsive-card-swiper-wrap .klk-card-swiper-prev-btn {
        width: 32px;
        height: 32px;
      }
    }
  }
}

</style>
