<template>
  <div class="base-sticky" :style="{ 'top': top }">
    <slot></slot>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator'
import { isClient } from '../../../utils/utils'

@Component
export default class BaseSticky extends Vue {
  @Prop({ default: '-1px' }) top!: string
  elementObserver: any = null

  mounted() {
    if (isClient) {
      this.createObserver()
    }
  }

  beforeDestroy() {
    this.destroyObserver()
  }

  createObserver() {
    if ('IntersectionObserver' in window) {
      this.elementObserver = new IntersectionObserver(
        entries => entries.forEach((e) => {
          e.target.classList.toggle('is-pinned', e.intersectionRatio < 1)
          this.$emit('change', e.intersectionRatio < 1)
        }),
        { threshold: [1] }
      )
      this.elementObserver.observe(this.$el)
    }
  }

  destroyObserver() {
    this.elementObserver && this.$el && this.elementObserver.unobserve(this.$el)
  }
}
</script>

<style lang="scss" scoped>
.base-sticky {
  position: sticky;
  top: -1px;
  z-index: 10;
}
</style>
