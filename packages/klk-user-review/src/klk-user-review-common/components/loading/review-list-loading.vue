<template>
  <div ref="skeletonContainer" class="review-list__skeleton klk-skeleton-data">
    <klk-skeleton v-for="card in cardNum" :key="card" animate style="margin-bottom: 40px;">
      <div class="flex" style="margin-bottom: 12px; align-items: center;">
        <klk-skeleton-circle class="flex-none" style="margin-right: 12px; margin-bottom: 0" :size="44" />
        <klk-skeleton-block inline width="123" height="24"></klk-skeleton-block>
      </div>
      <klk-skeleton-block v-for="i in 3" :key="i" width="100%" height="24" style="margin-bottom: 12px;"></klk-skeleton-block>
      <div class="flex">
        <klk-skeleton-block
          v-for="i in (skeletonLayout === 'large' ? 6 : 3)"
          :key="i"
          inline
          class="flex-block review-list__skeleton-image"
          height="102"
        />
      </div>
    </klk-skeleton>
  </div>
</template>

<script lang="ts">
import { Vue, Component, Prop } from 'vue-property-decorator'
import KlkSkeleton from '@klook/klook-ui/lib/skeleton'

@Component({
  name: 'ReviewListLoading',
  components: {
    KlkSkeleton
  }
})
export default class ReviewListLoading extends Vue {
  @Prop({ default: 3 }) cardNum?: number // 卡片数量
  skeletonLayout: 'large' | 'small' = 'large' // 根据容器宽度设置布局模式，一行6张 / 一行3张

  containerWidth: number = 0 // 容器宽度

  initSkeletonLayout() {
    const skeletonContainer = this.$refs.skeletonContainer as HTMLElement
    this.containerWidth = skeletonContainer?.offsetWidth || 0
    if (skeletonContainer?.offsetWidth > 600) {
      this.skeletonLayout = 'large'
    } else {
      this.skeletonLayout = 'small'
    }
  }

  mounted() {
    this.$nextTick(() => {
      this.initSkeletonLayout()
    })
  }
}
</script>

<style lang="scss" scoped>
.review-list {
  &__skeleton {
    width: 100%;
    height: 100%;
    overflow: hidden;
  }
  ::v-deep &__skeleton-image {
    &:not(:last-of-type) {
      margin-right: 9px;
    }
  }
}
</style>
