<template>
  <div ref="skeletonContainer" class="review-detail__skeleton klk-skeleton-data">
    <klk-skeleton animate style="margin-bottom: 40px;margin-top: 40px;">
      <klk-skeleton-block width="184" height="24"></klk-skeleton-block>
      <klk-skeleton-block height="180"></klk-skeleton-block>
    </klk-skeleton>
    <review-list-loading />
  </div>
</template>

<script lang="ts">
import { Vue, Component } from 'vue-property-decorator'
import KlkSkeleton from '@klook/klook-ui/lib/skeleton'
import ReviewListLoading from './review-list-loading.vue'

@Component({
  name: 'ReviewDetailLoading',
  components: {
    ReviewListLoading,
    KlkSkeleton
  }
})
export default class ReviewDetailLoading extends Vue {
}
</script>

<style lang="scss">
.review-detail {
  &__skeleton {
    width: 100%;
    height: 100%;
    overflow: hidden;
  }
}
</style>
