<template>
  <div class="review-overview__skeleton klk-skeleton-data" ref="skeletonContainer">
    <klk-skeleton animate style="margin-bottom: 40px;margin-top: 0;">
      <div class="flex" style="margin-bottom: 12px; align-items: center;">
        <klk-skeleton-block v-for="i in cardNum" :key="i" width="100%" height="180" class="flex-block review-overview__skeleton-top"></klk-skeleton-block>
      </div>
    </klk-skeleton>
    <review-list-loading :card-num="1" />
  </div>
</template>

<script lang="ts">
import { Vue, Component, Inject } from 'vue-property-decorator'
import KlkSkeleton from '@klook/klook-ui/lib/skeleton'
import ReviewListLoading from './review-list-loading.vue'

@Component({
  name: 'ReviewOverviewLoading',
  components: {
    ReviewListLoading,
    KlkSkeleton
  }
})
export default class ReviewOverviewLoading extends Vue {
  @Inject('realPlatform') realPlatform?: string

  get cardNum() {
    return this.realPlatform === 'mobile' ? 1 : 2 // 根据平台调整卡片数量
  }
}
</script>

<style lang="scss" scoped>
.review-overview {
  &__skeleton {
    width: 100%;
    height: 100%;
    overflow: hidden;
  }
  ::v-deep &__skeleton-top {
    margin-bottom: 0 !important;
    &:not(:last-of-type) {
      margin-right: 9px;
    }
  }
}
</style>
