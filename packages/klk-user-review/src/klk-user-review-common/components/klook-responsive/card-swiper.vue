<template>
  <div class="responsive-card-swiper-wrap">
    <div v-show="!prevDisabled" :class="prevClass" :style="prevBtnStyle" @click="showPrev">
      <IconBack theme="outline" size="16" fill="#212121" />
      <!--      <klk-icon type="icon_navigation_chevron_left_xs"></klk-icon>-->
    </div>
    <div ref="swiperFixedContainer" class="responsive-swiper-fixed-container">
      <div
        ref="cardSlider"
        class="responsive-card-slider"
        :style="itemsStyle"
        @scroll.once="handleScroll"
      >
        <slot></slot>
      </div>
    </div>
    <div v-show="!nextDisabled" :class="nextClass" :style="nextBtnStyle" @click="showNext">
      <IconNext theme="outline" size="16" fill="#212121" />
      <!--      <klk-icon type="icon_navigation_chevron_right_xs"></klk-icon>-->
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator'
import debounce from 'lodash/debounce'
import { IconBack, IconNext } from '@klook/klook-icons'

@Component({
  components: { IconNext, IconBack }
})
export default class CardSwiper extends Vue {
  // 箭头按钮的偏移量，单位 px，用来计算箭头按钮的位置
  @Prop({ default: 10, type: [Number, String] }) controllerOffset!: number | string

  // 默认的箭头按钮的偏移量，单位 px，用来计算箭头按钮的位置，箭头按钮的位置可以通过 btnOffset 来调整
  defaultControllerOffset = 10
  // 默认的箭头按钮的宽度，单位 px，用来计算箭头按钮的位置，箭头按钮的位置可以通过 btnOffset 来调整
  controllerBtnWidth = 40

  // 卡片的 transform x 值
  // 窗口变化要重新计算
  sliderOffsetDistance = 0

  // 屏幕左边隐藏的卡片个数
  // 1. 用来判断是否禁用上一页，下一页按钮
  // 2. 用来计算滚动的距离
  itemsOffset = 0

  debouncedHandleResize = debounce(this.calculateSliderOffsetDistance, 200)

  isDomReady = false

  // 滚动触发事件
  handleScroll() {
    this.$emit('scroll-once')
  }

  // 禁用上一页按钮
  get prevDisabled() {
    if (this.$isServer) {
      return false
    }
    return this.itemsOffset <= 0
  }

  // 禁用下一页按钮
  get nextDisabled() {
    if (this.$isServer) {
      return false
    }
    if (this.isDomReady) {
      return this.itemsOffset + this.generateDisplayedItemCount() >= this.generateItemsCount()
    }
  }

  // 上一页按钮的样式
  get prevClass() {
    return {
      'klk-card-swiper-prev-btn': true,
      'klk-card-swiper-prev-btn-disabled': this.prevDisabled
    }
  }

  // 下一页按钮的样式
  get nextClass() {
    return {
      'klk-card-swiper-next-btn': true,
      'klk-card-swiper-next-btn-disabled': this.nextDisabled
    }
  }

  // 上一页按钮的样式，目前只支持调整按钮的偏移距离
  get prevBtnStyle() {
    if (this.$isServer) {
      return {}
    }
    let offset = this.controllerOffset || this.defaultControllerOffset
    let leftValue = 0
    if (typeof this.controllerOffset !== 'number' && typeof this.controllerOffset !== 'string') {
      offset = this.defaultControllerOffset
    }
    if (typeof this.controllerOffset === 'string') {
      offset = parseInt(this.controllerOffset, 10)
    }
    leftValue = (offset as number) + this.controllerBtnWidth
    return { left: `-${leftValue}px` }
  }

  // 下一页按钮的样式，目前只支持调整按钮的偏移距离
  get nextBtnStyle() {
    if (this.$isServer) {
      return {}
    }
    let offset = this.controllerOffset || this.defaultControllerOffset
    let rightValue = 0
    if (typeof this.controllerOffset !== 'number' && typeof this.controllerOffset !== 'string') {
      offset = this.defaultControllerOffset
    }
    if (typeof this.controllerOffset === 'string') {
      offset = parseInt(this.controllerOffset, 10)
    }
    rightValue = (offset as number) + this.controllerBtnWidth
    return { right: `-${rightValue}px` }
  }

  // 卡片的样式
  get itemsStyle() {
    return {
      transform: `translateX(${this.sliderOffsetDistance}px)`
    }
  }

  // 卡片的总数
  // 1. 用来判断是否禁用下一页按钮
  // 2. 用来计算滚动的距离
  generateItemsCount() {
    const itemsCount = this.$slots.default ? this.$slots.default.length : 0
    return itemsCount
  }

  // swiper 容器的固定宽度
  // 1. 用来计算一屏显示的卡片个数
  generateSwiperFixedWidth() {
    if (this.$isServer) {
      return 0
    }
    if (!this.$refs.swiperFixedContainer) {
      return 0
    }
    const containerWidth = (this.$refs.swiperFixedContainer as HTMLElement).getBoundingClientRect()
      .width
    return containerWidth
  }

  // 生成单个卡片的宽度，
  // 1. 用来计算一屏显示的卡片个数
  // 2. 用来计算滚动的距离
  generateSingleItemWidth() {
    if (this.$isServer) {
      return 0
    }
    const firstItem = this.$slots.default ? this.$slots.default[0].elm : null
    if (firstItem) {
      const itemWidth = (firstItem as HTMLElement).getBoundingClientRect().width
      return itemWidth
    } else {
      return 0
    }
  }

  // 生成一屏显示的卡片个数
  // 1. 用来计算滚动的距离
  // 2. 用来判断是否禁用下一页按钮
  generateDisplayedItemCount() {
    const swiperFixedWidth = this.generateSwiperFixedWidth()
    const singleItemWidth = this.generateSingleItemWidth()
    const displayedItemCount = Math.ceil(swiperFixedWidth / singleItemWidth)
    return displayedItemCount
  }

  showPrev() {
    if (this.prevDisabled) {
      return
    }
    const singleItemWidth = this.generateSingleItemWidth()
    const displayedItemCount = this.generateDisplayedItemCount()

    const leftInvisibleItemsCount = this.itemsOffset

    // 如果待显示的卡片个数大于等于一屏的卡片个数
    if (leftInvisibleItemsCount >= displayedItemCount) {
      this.sliderOffsetDistance += displayedItemCount * singleItemWidth
      this.itemsOffset -= displayedItemCount
    } else {
      // 如果待显示的卡片个数小于一屏的卡片个数
      this.sliderOffsetDistance += leftInvisibleItemsCount * singleItemWidth
      this.itemsOffset -= leftInvisibleItemsCount
    }
  }

  showNext() {
    if (this.nextDisabled) {
      return
    }
    const totalItemsCount = this.generateItemsCount()
    const singleItemWidth = this.generateSingleItemWidth()
    const displayedItemCount = this.generateDisplayedItemCount()

    const rightInvisibleItemsCount = totalItemsCount - displayedItemCount - this.itemsOffset

    // 如果待显示的卡片个数大于等于一屏的卡片个数
    if (rightInvisibleItemsCount >= displayedItemCount) {
      this.sliderOffsetDistance -= displayedItemCount * singleItemWidth
      this.itemsOffset += displayedItemCount
    } else {
      // 如果待显示的卡片个数小于一屏的卡片个数
      this.sliderOffsetDistance -= rightInvisibleItemsCount * singleItemWidth
      this.itemsOffset += rightInvisibleItemsCount
    }
  }

  calculateSliderOffsetDistance() {
    this.sliderOffsetDistance = 0
    this.itemsOffset = 0
  }

  mounted() {
    this.debouncedHandleResize()
    window.addEventListener('resize', this.debouncedHandleResize)
    this.$nextTick(() => {
      this.isDomReady = true
    })
  }

  beforeDestroy() {
    window.removeEventListener('resize', this.debouncedHandleResize)
  }
}
</script>

<style lang="scss" scoped>
@import './styles/index.scss';
$card-gap: 20px;
$half-card-gap: calc($card-gap / 2);
$container-padding: 20px;

$mobile-card-gap: 12px;
$mobile-half-card-gap: calc($mobile-card-gap / 2);
$mobile-container-padding: 12px;

.responsive-card-swiper-wrap {
  position: relative;
  margin-left: -$container-padding;
  margin-right: -$container-padding;
  padding: 0 0 0 $container-padding;

  @include screen-size('md', 'lg', 'xl') {
    margin-left: -$mobile-container-padding;
    margin-right: -$mobile-container-padding;
    padding: 0 0 0 $mobile-container-padding;
  }

  @include screen-size('md', 'lg', 'xl') {
    margin: 0;
    padding: 0;
  }

  .responsive-swiper-fixed-container {
    width: 100%;
    overflow: hidden;
  }

  .responsive-card-slider {
    display: flex;
    flex-wrap: nowrap;
    overflow-x: auto;
    overflow-y: hidden;
    padding-bottom: 20px;
    margin-left: -$mobile-half-card-gap;
    margin-right: -$mobile-half-card-gap;
    margin-bottom: -20px;

    @include screen-size('md', 'lg', 'xl') {
      transition: all 0.8s ease 0s;
      overflow: unset;
      margin-left: -$half-card-gap;
      margin-right: -$half-card-gap;
    }
  }

  .klk-card-swiper-prev-btn,
  .klk-card-swiper-next-btn {
    display: none;
  }

  @include screen-size('md', 'lg', 'xl') {
    .klk-card-swiper-prev-btn,
    .klk-card-swiper-next-btn {
      display: flex;
      width: 40px;
      height: 40px;
    }
  }
}
</style>
