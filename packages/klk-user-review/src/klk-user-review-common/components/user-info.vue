<template>
  <div class="review-user-info">
    <div class="user">
      <div class="user-avatar" v-lazy:background-image="userInfo.user_avatar" />
      <div class="user-info">
        <div class="user-info__name">{{ userInfo.user_name }}</div>
        <div class="user-info__rating-time">{{ reviewDate }}</div>
      </div>
    </div>
    <div v-if="!hideRating" class="user-rating">
      <span class="user-rating__desc" v-if="userInfo.rating_desc">{{ userInfo.rating_desc }}</span>
      <span class="user-rating__rating">{{ userInfo.user_rating }}</span>
    </div>
  </div>
</template>

<script lang="ts">
import { Vue, Component, Prop } from 'vue-property-decorator'
import { getNewDateFormat } from '@klook/klk-traveller-utils/lib/datetime'
import { IUserInfo } from '@/types/review'

@Component({
  name: 'UserInfo'
})
export default class UserInfo extends Vue {
  @Prop({ type: Object, default: () => null }) userInfo!: IUserInfo
  @Prop({ required: false }) language?: string
  @Prop({ required: false }) hideRating?: boolean

  get reviewDate() {
    return getNewDateFormat(this.language || this?.$store?.state?.klook?.language || 'en', this.userInfo.rating_time, 'YYYY/MM/DD')
  }
}
</script>

<style lang="scss" scoped>
@import "../../style/index.scss";

.review-user-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  .user {
    display: flex;
    align-items: center;
    .user-avatar {
      @include lazy-load-bg();
      width: 44px;
      height: 44px;
      border-radius: 44px;
      background-size: cover;
      background-position: center center;
      flex-shrink: 0;
    }
    .user-info {
      margin-left: 8px;
      .user-info__name {
        @include font-body-s-bold-v2();
        color: $color-text-primary;
      }
      .user-info__rating-time {
        @include font-caption-m-regular();
        color: $color-text-secondary;
      }
    }
  }
  .user-rating {
    flex-shrink: 0;
    .user-rating__desc {
      @include font-body-s-regular();
      color: $color-accent-1;
    }
    .user-rating__rating {
      @include font-caption-1();
      display: inline-block;
      background-color: $color-accent-1;
      border-radius: 8px 2px;
      padding: 0 4px;
      color: $color-text-reverse;
      margin-left: 4px;
    }
  }
}
</style>