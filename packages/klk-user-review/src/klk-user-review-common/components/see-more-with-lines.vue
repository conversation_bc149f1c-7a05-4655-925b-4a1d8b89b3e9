<template>
  <div class="see-more-with-lines">
    <p
      ref="contentRef"
      class="content"
      :class="{ 'not-truncated': isShowMore }"
      :style="{ '-webkit-line-clamp': maxLines }"
    >
      {{ content }}
    </p>
    <div v-show="isShowMoreBtn && !hideMoreButton" class="more-btn-wrap">
      <span
        class="more-btn"
        @click="showMore()"
      >
        <span
          v-if="!isShowMore"
          v-galileo-click-tracker="{
            spm,
            componentName: 'klk-user-review',
            enable: spm !== undefined && spm !== '',
            autoTrackSpm: true
          }"
          v-bind="trackInfo"
        >
          {{ $t('see_more') }}
        </span>
        <span v-else>
          {{ $t('6307') }}
        </span>
      </span>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Prop, Ref, Vue, Watch } from 'vue-property-decorator'
import { isClient } from '../../utils/utils'

@Component
export default class SeeMoreWithLines extends Vue {
  @Prop({ default: 2 }) maxLines!: number
  @Prop() content!: string[]
  @Prop({ default: false }) hideLess!: boolean
  @Prop({ default: () => '' }) spm?: string
  // @Prop({ default: false }) visible!: boolean // 慎重使用
  @Ref() contentRef!: any

  isShowMore = false
  isShowMoreBtn = false

  @Watch('content', { immediate: true, deep: true })
  handleContentChange() {
    this.$nextTick(() => {
      this.checkHeight()
    })
  }

  get trackInfo() {
    return this.spm ? { 'data-spm-item': this.spm } : {}
  }

  // 支持展开后隐藏收起按钮
  get hideMoreButton() {
    return this.isShowMore && this.hideLess
  }

  mounted() {
    this.$nextTick(() => {
      this.checkHeight()
    })
  }

  showMore() {
    if (!this.hideMoreButton) {
      this.isShowMore = !this.isShowMore
    }
  }

  checkHeight() {
    if (isClient) {
      const contentDom = this.contentRef as any
      this.isShowMoreBtn = false
      this.isShowMore = false
      // 延迟计算
      this.$nextTick(() => {
        if (contentDom && contentDom.scrollHeight > contentDom.offsetHeight) {
          this.isShowMoreBtn = true
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.content {
  @include font-body-s-regular;

  display: -webkit-box;
  color: $color-text-primary;
  -webkit-box-orient: vertical;
  -moz-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;

  &.not-truncated {
    -webkit-line-clamp: unset !important;
  }
}

.more-btn-wrap {
  margin-top: 4px;
  display: flex;
  justify-content: flex-start;
}

.more-btn {
  @include font-body-s-regular;

  display: flex;
  align-items: center;
  cursor: pointer;
  text-decoration: underline;

  &-icon {
    display: flex;
    align-items: center;
  }
}
</style>
