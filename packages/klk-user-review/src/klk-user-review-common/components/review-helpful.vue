<template>
  <div
    class="review-helpful"
    @click="clickLike"
    data-spm-item="ReviewHelpful"
    v-galileo-click-tracker="{
      spm: 'ReviewHelpful',
      componentName: 'klk-user-review',
      autoTrackSpm: true
    }"
  >
    <IconThumbsUp
      :class="['review-like-icon']"
      size="16"
      :theme="hasLiked ? 'filled' : 'outline'"
      :fill="hasLiked ? colors.colorIconFilled : colors.colorTextSecondary"
    />
    <template v-if="Number(likeCount) > 0 || hasLiked">
      <div :class="['review-helpful-like', hasLiked ? 'review-liked' : 'review-like']">
        <div v-html="$t('my_reviews.helpful_with_num', [likeCount])"></div>
      </div>
    </template>
    <div v-else>
      <div class="review-helpful-btn">
        {{ $t('my_reviews.helpful_text') }}
      </div>
    </div>
  </div>
</template>
<script lang="ts">
import { Component, Prop, Watch, Inject } from 'vue-property-decorator'
import IconThumbsUp from '@klook/klook-icons/lib/IconThumbsUp'
import { $colorTextSecondary, $colorBrandPrimary } from '@klook/klook-ui/lib/utils/design-token-esm'
import Base from '../../klk-user-review-common/mixins/base'
import { IReviewItem } from '@/types/review'
import { ILoginOptions } from '@/types/review-detail'

@Component({
  components: {
    IconThumbsUp
  }
})
export default class ReviewHelpful extends Base {
  @Prop({ default: () => {} }) reviewInfo!: IReviewItem

  @Inject('loginOptions') loginOptions?: ILoginOptions

  @Inject('lang') lang!: String

  @Watch('reviewInfo', { deep: true })
  onreviewInfoChange() {
    this.init()
  }

  hasLiked: boolean = false
  likeCount: number = 0

  get colors() {
    return {
      colorTextSecondary: $colorTextSecondary,
      colorIconFilled: $colorBrandPrimary
    }
  }

  get isLogin() {
    return this.$store?.state?.auth?.isLoggedIn
  }

  clickLike() {
    if (!this.isLogin) {
      if (this.loginOptions) {
        const that = this
        this.loginOptions.onLogin(() => {
          that.handleLike()
        })
      } else {
        window.location.href = `${this.lang !== 'en' ? `/${this.lang}` : ''}/signin?signin_jump=${encodeURIComponent(window.location.href)}`
      }
    } else {
      this.handleLike()
    }
  }

  handleLike() {
    const reviewId = this.reviewInfo.review_id

    if (!this._axios) {
      return
    }

    if (this.hasLiked) {
      this._axios.$post('/v1/usrcsrv/review/like/remove', {
        review_id: reviewId
      })
      this.hasLiked = false
      this.likeCount = (this.likeCount > 0) ? this.likeCount - 1 : 0
    } else {
      this._axios.$post('/v1/usrcsrv/review/like/add', `review_id=${reviewId}`)
      this.hasLiked = true
      this.likeCount = this.likeCount + 1
    }
  }

  init() {
    this.hasLiked = this.reviewInfo.has_liked || false
    this.likeCount = Number(this.reviewInfo.liked_count) || 0
  }

  mounted() {
    this.init()
  }
}
</script>
<style lang="scss">
.review-helpful {
  display: flex;
  justify-content: flex-start;
  margin-top: 12px;
  align-items: center;
  font-size: $fontSize-caption-m;
  color: $color-text-secondary;
  width: fit-content;
  cursor: pointer;

  .review-like-icon {
    margin-right: 8px;
    color: $color-text-secondary;
  }

  .review-liked {
    color: $color-brand-primary;
  }
  .review-like {
    color: $color-text-secondary;
  }
}

.review-helpful-btn {
  @include font-body-s-regular;
  display: flex;
  white-space: nowrap;
  align-items: center;
}

.review-helpful-like {
  @include font-body-s-regular;
}
</style>
