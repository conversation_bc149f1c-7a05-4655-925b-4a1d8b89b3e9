<template>
  <div class="user-review" v-if="reviewInfo">
    <div v-if="mode === 'full'">
      <SeeMoreWithLines
        v-if="textMode === 'brief'"
        :content="content"
        :max-lines="4"
        spm="ReviewSeeMore"
      />
      <div v-else>
        {{ content }}
      </div>
      <div
        v-if="reviewInfo.show_translation"
        @click="showOrigin"
        class="user-review__translation-btn"
      >
        <span
          v-if="showOriginContent"
          :data-spm-item="`ReviewSwitchLanguage?ext=${encodeURIComponent(JSON.stringify({ SwitchType: 'ShowTranslation' }))}`"
          v-galileo-click-tracker="{
            spm: 'ReviewSwitchLanguage',
            componentName: 'klk-user-review',
            autoTrackSpm: true
          }"
        >
          {{ $t('activity.v2.translate.btn') }}
        </span>
        <span
          v-else
          :data-spm-item="`ReviewSwitchLanguage?ext=${encodeURIComponent(JSON.stringify({ SwitchType: 'ShowOrigin' }))}`"
          v-galileo-click-tracker="{
            spm: 'ReviewSwitchLanguage',
            componentName: 'klk-user-review',
            autoTrackSpm: true
          }"
        >
          {{ $t('activity.v2.translate.show.original') }}
        </span>
      </div>
    </div>
    <div v-else>
      <div class="user-review__content">{{ content }}</div>
    </div>
  </div>
</template>

<script lang="ts">
import { Vue, Component, Prop } from 'vue-property-decorator'
import SeeMoreWithLines from './see-more-with-lines.vue'
import { IUserReview } from '@/types/review'

@Component({
  name: 'UserReview',
  components: {
    SeeMoreWithLines
  }
})
export default class UserReview extends Vue {
  @Prop({ type: Object, default: () => null }) reviewInfo!: IUserReview
  @Prop({ type: String, default: 'full' }) mode!: 'brief' | 'full'
  @Prop({ type: String, default: 'brief' }) textMode!: 'brief' | 'full' // 仅在mode=full时有效，控制文本是不是展示完全
  showOriginContent = false

  get content() {
    return this.showOriginContent ? this.reviewInfo.review_content : (this.reviewInfo.translate_content || this.reviewInfo.review_content)
  }

  showOrigin() {
    this.showOriginContent = !this.showOriginContent
    this.$emit('translationChange', this.showOriginContent)
  }
}
</script>

<style lang="scss" scoped>
@import '../../style/index.scss';
.user-review {
  &__translation-btn {
    @include font-body-s-regular-v2;
    margin-top: 16px;
    color: $color-text-primary;
    cursor: pointer;
    text-decoration: underline;
  }
  &__content {
    @include text-ellipsis(4);
  }
}
</style>
