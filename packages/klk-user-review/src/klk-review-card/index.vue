<template>
  <div v-if="reviewInfo" class="review-card">
    <div>
      <!--user-info-->
      <div class="review-card__user-info">
        <user-info :user-info="reviewInfo.user_info" />
      </div>
      <!-- product-info -->
      <div
        v-if="type === 'full' && reviewInfo.product_info && reviewInfo.product_info.product_name"
        v-galileo-click-tracker="{
          spm: 'ReviewBookNow',
          componentName: 'klk-user-review',
          enable: reviewInfo.product_info.is_booking_now
        }"
        :class="['review-card__product-info', reviewInfo.product_info.is_booking_now && 'clickable']"
        v-bind="reviewInfo.product_info.is_booking_now && {
          'data-spm-module': 'ReviewBookNow',
          'data-spm-virtual-item': '__virtual'
        }"
        @click="goBooking"
      >
        <div class="review-card__product-info-name">
          {{ $t('reviews.review_for') }}&nbsp;{{ reviewInfo.product_info.product_name }}
        </div>
        <div v-if="reviewInfo.product_info.is_booking_now" class="review-card__product-info-icon">
          <IconNext theme="outline" size="16" />
        </div>
      </div>

      <!-- user-review -->
      <div class="review-card__review-content">
        <user-review
          :review-info="{
            review_content: reviewInfo.review_content,
            translate_content: reviewInfo.translate_content,
            show_translation: reviewInfo.show_translation
          }"
          :mode="type"
        />
      </div>
    </div>

    <div>
      <!--image-preview-->
      <template v-if="imageList.length">
        <image-preview
          :review-image="imageList"
          :show-preview="type === 'full'"
          :preview-mode="previewMode"
          v-on="$listeners"
        />
      </template>
    </div>
  </div>
</template>
<script lang="ts">
import { Vue, Component, Prop, Provide } from 'vue-property-decorator'
import IconNext from '@klook/klook-icons/lib/IconNext'
import UserReview from '../klk-user-review-common/components/user-review.vue'
import UserInfo from '../klk-user-review-common/components/user-info.vue'
import SeeMoreWithLines from '../klk-user-review-common/components/see-more-with-lines.vue'
import ImagePreview from '../klk-review-detail/components/image-preview/index.vue'
import { IReviewItem } from '@/types/review'

@Component({
  name: 'KlkReviewStaticCard',
  components: {
    UserReview,
    UserInfo,
    SeeMoreWithLines,
    IconNext,
    ImagePreview
  }
})

export default class KlkReviewStaticCard extends Vue {
  @Prop({ type: Object, default: () => null }) reviewInfo!: IReviewItem
  @Prop({ type: String, default: () => 'modal' }) previewMode?: 'inline' | 'modal' | 'fullscreen'
  @Prop({ type: String, default: () => 'full' }) type!: 'brief' | 'full'

  @Provide('reviewCardData')
  get reviewCardData() {
    return {
      reviewInfo: this.reviewInfo || null
    }
  }

  @Provide('realPlatform')
  get realPlatform() {
    return this.$store?.state?.klook?.platform || 'desktop'
  }

  get imageList() {
    if (this.reviewInfo.review_image_list?.length) {
      return this.type === 'brief' ? this.reviewInfo.review_image_list.slice(0, 3) : this.reviewInfo.review_image_list
    }
    return []
  }

  goBooking(e) {
    e?.stopPropagation && e.stopPropagation()
    if (this.reviewInfo?.product_info?.is_booking_now) {
      this.$emit('book', this.reviewInfo.product_info)
    }
  }
}
</script>
<style lang="scss" scoped>
@import "../style/index.scss";

.review-card {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  justify-content: space-between;
  padding: 16px;
  border: 1px solid #E6E6E6;
  border-radius: 16px;

  &__user-info {
    margin-bottom: 12px;
  }
  &__product-info {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    color: $color-text-placeholder;
    &.clickable {
      cursor: pointer;
    }
    &-name {
      @include font-paragraph-s-regular;
      @include text-ellipsis(2);
    }
    &-icon {
      margin-left: 8px;
    }
  }
}
</style>
