<template>
  <div
    v-if="ratingInfo"
    :class="['review-rating', `review-rating__${size}`]"
    data-spm-module="ReviewOverviewRating"
  >
    <div class="review-rating__top">
      <rating :rating-info="ratingInfo.rating_info" :size="size" class="review-rating__rating" />
      <sub-rating
        class="review-rating__sub-rating"
        v-if="ratingInfo.sub_rating_list && ratingInfo.sub_rating_list.length"
        :sub-rating-list="ratingInfo.sub_rating_list"
        :size="size"
      />
    </div>
    <div v-if="ratingInfo.merchant_info" class="review-rating__merchant">
      <div v-if="ratingInfo.merchant_info.merchant_logo" class="review-rating__merchant-img">
        <img
          class="review-rating__merchant-logo"
          :alt="ratingInfo.merchant_info.merchant_name"
          v-lazy="ratingInfo.merchant_info.merchant_logo"
          height="20"
        />
      </div>
      <div class="review-rating__merchant-name">{{ $t('6938', ratingInfo.merchant_info.merchant_name) }}</div>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator'
import { IReviewRating } from '../types/review-rating'
import Rating from './components/rating.vue'
import SubRating from './components/sub-rating.vue'

@Component({
  name: 'ReviewRating',
  components: {
    Rating,
    SubRating
  }
})
export default class ReviewRating extends Vue {
  @Prop({ type: Object, default: () => null }) ratingInfo?: IReviewRating
  @Prop({ required: true, default: () => 'small' }) size!: string
}
</script>

<style lang="scss" scoped>
@import "../style/index.scss";
.review-rating {
  width: 100%;
  background: $color-bg-1;
  &__top {
    display: flex;
  }
  &__merchant {
    @include font-body-xs-regular;
    color: $color-text-placeholder;
    display: flex;
    align-items: flex-start;
    &-img {
      margin-right: 8px;
    }
    &-logo {
      @include lazy-load-bg();
    }
    &-name {
      line-height: 20px;
    }
  }
}
.review-rating__large {
  .review-rating {
    &__rating {
      flex-shrink: 0;
    }
    &__top {
      align-items: flex-start;
      justify-content: space-between;
    }
    &__merchant {
      margin-top: 16px;
    }
  }
}
.review-rating__small {
  .review-rating {
    &__top {
      flex-direction: column;
    }
    &__sub-rating {
      margin-top: 16px;
    }
    &__merchant {
      margin-top: 12px;
    }
  }
}
</style>
