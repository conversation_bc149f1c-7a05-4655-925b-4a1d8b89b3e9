<template>
  <div
    v-if="subRatingList && subRatingList.length"
    :class="['sub-rating-list', `sub-rating-list__${size}`]"
  >
    <div class="sub-rating-container">
      <div
        v-for="(subRating, index) in subRatingList"
        :key="index"
        class="sub-rating"
      >
        <div class="title">
          <span class="title-text">{{ subRating.sub_rating_desc }}</span>
        </div>
        <div class="value">{{ subRating.sub_rating }}</div>
      </div>
    </div>
    </div>
</template>

<script lang="ts">
import { Vue, Component, Prop } from 'vue-property-decorator'
import { ISubRating } from '../../types/review-rating'

@Component({
  name: 'SubRating'
})
export default class SubRating extends Vue {
  @Prop({ type: Array, default: () => null }) subRatingList?: ISubRating[]
  @Prop({ required: true, default: () => 'small' }) size!: string
}
</script>

<style lang="scss" scoped>
@import "../../style/index.scss";
.sub-rating-list {
  overflow-x: scroll;
  &::-webkit-scrollbar { /* for Chrome */
    display: none;
  }
  scrollbar-width: none; /* Firefox 64 */
  -ms-overflow-style: none; /* Internet Explorer 11 */

  .sub-rating-container {
    min-width: 352px;
    max-width: calc(160px * 4 + 8px * 3); /* 4 items max with spacing */
  }
  &__large {
    .sub-rating-container {
      justify-content: flex-end;
      margin-left: 16px;
    }
  }
  &__small {
    width: 100%;
  }
}
.sub-rating-container {
  display: flex;
  .sub-rating {
    min-height: 56px;
    max-height: 72px;
    min-width: 82px;
    max-width: 160px;
    padding: 8px;
    border-radius: $radius-s;
    background-color: $color-bg-3;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    flex-shrink: 0;
    &:not(:last-of-type) {
      margin-right: 8px;
    }
    .title {
      @include font-caption-m-semibold();
      color: $color-text-primary;
      margin-bottom: 8px;
      &-text {
        @include text-ellipsis(2);
        line-height: 1.32;
      }
    }
    .value {
      @include font-body-xs-regular();
      @include text-ellipsis(1);
      color: $color-text-secondary;
    }
  }
}
</style>