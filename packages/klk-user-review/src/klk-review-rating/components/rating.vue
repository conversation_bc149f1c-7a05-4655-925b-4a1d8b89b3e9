<template>
  <div v-if="ratingInfo" :class="['rating', `rating__${size}`]">
    <div class="rating-left">
      <img v-if="ratingInfo.rating_icon" class="rating-icon" :src="ratingInfo.rating_icon" width="40" />
      <div class="rating-score">
        <span class="rating-score__avg">{{ ratingInfo.avg_rating }}</span>
        <span class="rating-score__dash">/</span>
        <span>{{ ratingInfo.max_rating }}</span>
      </div>
    </div>
    <div class="rating-right">
      <div class="rating-right__desc">{{ ratingInfo.rating_desc }}</div>
      <div v-if="ratingInfo.review_count_desc">{{ ratingInfo.review_count_desc }}</div>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator'
import { IRating } from '../../types/review-rating'

@Component({
  name: 'Rating'
})
export default class Rating extends Vue {
  @Prop({ type: Object, default: () => null }) ratingInfo?: IRating
  @Prop({ required: true, default: () => 'small' }) size!: string

}
</script>

<style lang="scss" scoped>
@import "../../style/index.scss";
.rating {
  &-icon {
    margin-right: 8px;
  }
  &-score {
    @include font-body-xs-regular();
    color: $color-text-secondary;
    flex-shrink: 0;
    &__dash {
      transform: scaleY(.7);
      display: inline-block;
    }
    &__avg {
      @include font-heading-xl();
      color: $color-accent-1;
    }
  }
  &-left {
    flex-shrink: 0;
    display: flex;
    align-items: center;
    margin-right: 8px;
  }
  &-right {
    @include font-caption-m-regular();
    display: flex;
    flex-direction: column;
    color: $color-text-placeholder;
    max-width: 100%;
    &__desc {
      @include font-body-s-bold-v2();
      @include text-ellipsis(1);
      color: $color-accent-1;
      width: 100%;
    }
  }
}
.rating__small {
  &.rating {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
  }
  .rating-right {
    margin-top: 2px;
  }
}
</style>