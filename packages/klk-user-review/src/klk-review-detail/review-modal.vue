<template>
  <klk-bottom-sheet
    class="review-modal"
    show-close
    :header-divider="true"
    :visible="visible"
    :can-pull-close="false"
    :title="$t('activity.v2.mobile.activity.review')"
    :z-index="1000"
    @close="close"
  >
    <div class="review-modal__body">
      <review-detail
        title=""
        display="modal"
        :visible="visible"
        v-bind="$attrs"
        v-on="$listeners"
        @close="close"
      >
        <template v-for="(_, name) in $scopedSlots" v-slot:[name]="data">
          <slot :name="name" v-bind="data"/>
        </template>
      </review-detail>
    </div>
  </klk-bottom-sheet>
</template>

<script lang="ts">
import { Component, Mixins } from 'vue-property-decorator'
import KlkBottomSheet from '@klook/klook-ui/lib/bottom-sheet'
import Common from './common'

@Component({
  name: 'ReviewModal',
  components: {
    KlkBottomSheet
  }
})
export default class ReviewModal extends Mixins(Common) {
}
</script>

<style lang="scss" scoped>
.review-modal {
  &__body {
    overflow-y: scroll;
    height: 100%;
    overflow-x: hidden;
  }
  ::v-deep {
    > .klk-bottom-sheet-inner {
      height: 100% !important;
      max-height: 100% !important;
      border-radius: 0;
      > .klk-bottom-sheet-body {
        padding: 0;
        overflow: hidden;
      }
    }

    > .klk-bottom-sheet-mask {
      display: none;
    }
  }
}
</style>
