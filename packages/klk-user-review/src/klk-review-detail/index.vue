<template>
  <component
    :is="listComponentsMap[paginationMode]"
    :extra-data="extraData"
    :pagination-info="paginationInfo"
    :total="totalReviews"
    :loading-error="loadingError"
    :bottom-loading="bottomLoading"
    :done="isLoadingDone"
    :on-reach-bottom="throttleReachBottom"
    ref="reviewListContainer"
    v-bind="moduleTrackInfo"
    @onChangePage="handlePageChange"
  >
    <div :class="['review-detail', `review-detail__${display}`, `review-detail__${realPlatform}`]">
      <div v-if="title" class="review-detail__header">
        <component
          :is="display === 'inline' ? 'h2' : 'span'"
          class="review-detail__title"
        >
          {{ title }}
        </component>
      </div>
      <!-- 骨架屏 -->
      <div v-if="loading || overviewLoading" class="review-detail__loading">
        <review-detail-loading />
      </div>
      <template v-else-if="!loading && !overviewLoading && initDataSource">
        <!-- 顶部overall信息 -->
        <div class="review-detail__overview">
          <!--rating-->
          <div class="review-detail__rating">
            <KlkReviewRating
              :rating-info="ratingInfo"
              size="small"
            />
          </div>
          <!--ai-summary-->
          <div v-if="initDataSource.ai_summary_info" class="review-detail__aisummary">
            <klk-divider></klk-divider>
            <KlkAiReview
              :ai-review-info="initDataSource.ai_summary_info"
              :platform="realPlatform"
            />
            <!--模块divider-->
            <div v-if="display === 'inline'">
              <klk-divider></klk-divider>
            </div>
          </div>
        </div>
        <!--模块divider-->
        <div v-if="(initDataSource.ai_summary_info || ratingInfo) && display === 'modal'" class="review-detail__overview-divider">
          <div class="review-detail__divider-large" />
        </div>
        <!-- 评论列表 -->
        <div ref="review-list" class="review-detail__review-list">
          <!-- 标题仅在有ai summary和rating的时候出现 -->
          <div v-if="initDataSource.ai_summary_info && ratingInfo" class="review-detail__sub-title">
            {{ initDataSource.reviews.title || $t('208984-reviews_from_user') }}
          </div>
          <!-- filter筛选器 -->
          <div class="reviews-anchor-wrap">
            <div ref="reviews-anchor" :class="{ 'reviews-anchor-scrolltop': display === 'inline' }"></div>
          </div>
          <template v-if="initDataSource.filter">
            <template v-if="display === 'modal'">
              <base-sticky
                ref="sticky-filter"
                class="review-detail__filter-sticky"
                @change="changePin"
              >
                <component
                  :is="filterCom[realPlatform]"
                  :filter-list="initDataSource.filter.filter_list"
                  :sort-list="initDataSource.filter.sort_list"
                  :sort-by.sync="sortBy"
                  :filter-by.sync="filterBy"
                  @onChangeFilter="handleFilterChange"
                />
              </base-sticky>
            </template>
            <template v-else>
              <component
                :is="filterCom[realPlatform]"
                :filter-list="initDataSource.filter.filter_list"
                :sort-list="initDataSource.filter.sort_list"
                :sort-by.sync="sortBy"
                :filter-by.sync="filterBy"
                @onChangeFilter="handleFilterChange"
              />
            </template>
          </template>
          <klk-divider v-if="display === 'modal' && !isPinned" class="review-detail__divider"></klk-divider>

          <!-- 评论列表 -->
          <!-- 列表加载需要展示骨架屏 -->
          <div v-if="listLoading" class="review-detail__list-loading">
            <review-list-loading />
          </div>
          <template v-else-if="reviewList && reviewList.length">
            <div
              v-for="(item, index) in reviewList"
              :key="item.review_id"
              :data-spm-module="`ReviewReviewDetail_LIST?idx=${index}&len=${reviewList.length}&ext=${encodeURIComponent(JSON.stringify({ ...reviewListTrackInfo, ReviewID: item.review_id }))}`"
            >
              <review-card
                :ref="`review_${item.review_id}`"
                :review-info="item"
                :preview-mode="previewMode"
                :login-option="loginOption"
                class="review-detail__card"
                @book="goBooking"
              >
                <template v-slot:review-footer="slotProps">
                  <slot name="review-footer" :review-info="slotProps.reviewInfo"></slot>
                </template>
              </review-card>
              <klk-divider v-if="index !== reviewList.length - 1" class="review-detail__divider"></klk-divider>
            </div>
          </template>
          <!-- 空结果 -->
          <template v-else-if="reviewList && !reviewList.length && !loadingError && isNoMore">
            <div class="review-detail__empty-result">
              <img width="152" alt="empty" :src="formatPicUrl('https://res.klook.com/image/upload/v1747101943/UED_new/Platform/platform_AI_review_2409/NoReview.png', 152, 135)" />
              <div class="tips">{{ $t('hotel.vertical_no_result_found_with_filter') }}</div>
            </div>
          </template>
        </div>
      </template>
      <!-- 首屏空结果 -->
      <template v-else>
        <div class="review-detail__empty-result">
          <img width="152" alt="empty" :src="formatPicUrl('https://res.klook.com/image/upload/v1747101943/UED_new/Platform/platform_AI_review_2409/NoReview.png', 152, 135)" />
          <div class="tips">{{ $t('12691') }}</div>
          <klk-button class="button" type="primary" reverse size="small" @click="getReviewData">{{ $t('global.reload') }}</klk-button>
        </div>
      </template>
    </div>
  </component>
</template>

<script lang="ts">
import { Component, Prop, Provide, Watch } from 'vue-property-decorator'
import transformImageUrl from "@klook/klk-traveller-utils/lib/transformImageUrl";
import KlkButton from '@klook/klook-ui/lib/button'
import KlkDivider from '@klook/klook-ui/lib/divider'
import { getScrollContainer } from '@klook/klk-traveller-utils/lib/dom'
import { ldjson, throttle, isClient } from '../utils/utils'
import BaseSticky from '../klk-user-review-common/components/sticky/base-sticky.vue'
import KlkAiReview from '../klk-ai-review/index.vue'
import ReviewListLoading from '../klk-user-review-common/components/loading/review-list-loading.vue'
import ReviewDetailLoading from '../klk-user-review-common/components/loading/review-detail-loading.vue'
import Base from '../klk-user-review-common/mixins/base'
import KlkReviewRating from '../klk-review-rating/index.vue'
import ReviewFilterDesktop from './components/filter/desktop/index.vue'
import ReviewFilterMobile from './components/filter/mobile/index.vue'
import ReviewCard from './components/review-card/index.vue'
import Paginator from './components/review-list/paginator.vue'
import InfiniteScroll from './components/review-list/infinite-scroll.vue'
import { IReviewData, ILoginOptions } from '@/types/review-detail'
import { IExtraData, IReviewItem, Display, RenderMode } from '@/types/review'

@Component({
  components: {
    KlkAiReview,
    KlkReviewRating,
    ReviewFilterDesktop,
    ReviewFilterMobile,
    ReviewCard,
    BaseSticky,
    ReviewListLoading,
    ReviewDetailLoading,
    KlkButton,
    KlkDivider,
    Paginator,
    InfiniteScroll
  }
})
export default class ReviewDetail extends Base {
  @Prop({ required: false }) platform?: string
  @Prop({ required: false }) language?: string
  @Prop({ type: String, default: () => 'ssr' }) renderMode!: RenderMode
  @Prop({ type: String, default: () => 'inline' }) display!: Display // inline: 直接在页面上展示，modal: 弹窗展示
  @Prop({ type: String, default: () => '' }) title?: string
  @Prop({ type: Boolean, default: () => false }) enableReviewSchema?: boolean
  @Prop({ type: Boolean, default: () => false }) loading?: boolean
  @Prop({ type: Object, default: () => ({ aggregate_id: '', template_id: '' }) }) extraData!: IExtraData
  @Prop({ type: Object, default: () => null }) reviewData?: IReviewData
  @Prop({ type: Object, default: () => null }) loginOption?: ILoginOptions
  @Prop({ type: String, default: () => '' }) sourceReviewId?: string // 跳转的reviewid
  @Prop({ type: Boolean, default: () => true }) isInitPosition?: boolean // 是否初始化定位到reviewlist
  @Prop({ type: Boolean, default: () => true }) visible?: boolean // 是否在页面中展示，仅针对modal类型，用于控制当弹层隐藏和出现切换式触发pageview

  // 列表组件
  listComponentsMap: any = {
    paginator: 'Paginator',
    scroll: 'InfiniteScroll'
  }

  filterCom = {
    desktop: 'ReviewFilterDesktop',
    mobile: 'ReviewFilterMobile'
  }

  paginationInfo: any = {
    page: 1,
    limit: 10
  }

  totalReviews: number = 0

  // 列表加载状态
  loadingError = false
  listLoading = false // 列表筛选变更重新加载时的骨架屏loading
  bottomLoading = false // 处理触底加载的底部loading
  overviewLoading = false // 处理overview loading, 当渲染方式为csr时，组件内部的loading

  isNoMore: boolean = false // 是否没有更多数据

  initDataSource: IReviewData | null = null // overview + 首屏数据
  reviewList: IReviewItem[] = []
  sortBy: string = ''
  filterBy: any[] = []
  isPinned: boolean = false

  cancelToken: any = null // axios取消请求的token

  head() {
    if (this.enableReviewSchema && this.initDataSource) {
      const schema: Object = {
        '@context': 'https://schema.org',
        aggregateRating: {
          '@type': 'AggregateRating',
          ratingValue: String(this.initDataSource?.rating_info?.avg_rating || ''),
          bestRating: String(this.initDataSource?.rating_info?.max_rating || ''),
          worstRating: '1',
          reviewCount: String(this.initDataSource?.rating_info?.review_count || '')
        }
      }
      return {
        script: [ldjson(schema)]
      }
    }
  }

  formatPicUrl(url: string, width: number, height: number) {
    const enlarge = this.realPlatform === 'desktop' ? 2 : 1
    return transformImageUrl(url, {
      width: width * enlarge,
      height: height * enlarge,
      webp: this.realWebp,
      quantity: 100
    })
  }

  @Watch('sourceReviewId')
  handleSourceReviewIdChange() {
    if (this.sourceReviewId) {
      this.jumpToReview(this.sourceReviewId)
    }
  }

  @Watch('reviewData', { immediate: true })
  handleReviewDataChange() {
    if (this.reviewData && this.renderMode === 'ssr') {
      this.initData(this.reviewData as IReviewData)
    }
  }

  @Watch('visible', { immediate: true })
  handleVisibleChange(newVal: boolean) {
    this.$nextTick(() => {
      if (!isClient || !newVal || this.display === 'inline') {
        return
      }
      // 弹层类型每次打开需要重新获取列表数据
      this.getReviewData()
      // 仅在modal类型下，visible变更时触发pageview
      this.$inhouse?.track('pageview', (this.$refs.reviewListContainer as any)?.$el, { force: true })
    })
  }

  @Provide('loginOptions')
  get loginOptions() {
    return this.loginOption || null
  }

  @Provide('realPlatform')
  get realPlatform() {
    return this.platform || this?.$store?.state?.klook?.platform || 'desktop'
  }

  @Provide('lang')
  get lang() {
    return this.language || this?.$store?.state?.klook?.language || 'en'
  }

  get moduleTrackInfo() {
    return this.display === 'inline' ? { 'data-spm-module': `NewReviewList?${this.trackInfo}` } : { 'data-spm-page': `NewReviewList?${this.trackInfo}&trg=manual` }
  }

  get trackInfo() {
    return `ext=${encodeURIComponent(JSON.stringify({ AggregateKey: this.extraData?.aggregate_key, TemplateID: this.extraData?.template_id }))}`
  }

  get reviewListTrackInfo() {
    return this.initDataSource?.track_info?.extra || {}
  }

  get paginationMode() {
    return this.display === 'inline' ? 'paginator' : 'scroll'
  }

  get previewMode() {
    if (this.realPlatform === 'desktop') {
      return this.display === 'inline' ? 'modal' : 'inline'
    } else {
      return 'fullscreen'
    }
  }

  get isLoadingDone() {
    return this.reviewList?.length > 0 && this.isNoMore && !this.bottomLoading && !this.listLoading && !this.loading && !this.overviewLoading && !this.loadingError
  }

  get ratingInfo() {
    const { rating_info, sub_rating_list, merchant_info } = this.initDataSource || {}
    if (!rating_info && !sub_rating_list && !merchant_info) {
      return null
    }
    return { rating_info, sub_rating_list, merchant_info }
  }

  get realWebp() {
    return this.$store?.state?.klook?.webp || 0
  }

  changePin(val: any) {
    this.isPinned = val
  }

  created() {
    if (this.renderMode === 'ssr' && this.reviewData) {
      this.initData(this.reviewData as IReviewData)
    }
  }

  mounted() {
    // 如果是csr渲染方式，且display为inline，则需要获取review数据，modal类型每次打开弹窗都会获取数据
    if (this.renderMode === 'csr' && this.display === 'inline') {
      this.getReviewData()
    } else {
      // 跳转到默认位置
      this.initPosition()
    }
  }

  initData(data: IReviewData) {
    this.initDataSource = data
    this.reviewList = this.initDataSource.reviews.review_list
    this.paginationInfo = {
      ...this.paginationInfo,
      page: this.initDataSource.reviews.current_page,
      limit: this.initDataSource.reviews.each_page_count
    }
    this.totalReviews = this.initDataSource.reviews.total
    this.isNoMore = !this.initDataSource.reviews.has_next
  }

  jumpToReview(reviewId) {
    if (!isClient) {
      return
    }
    const targetReviews = this.$refs[`review_${reviewId}`] as any
    const targetReview = Array.isArray(targetReviews) ? targetReviews[0]?.$el : targetReviews?.$el
    const stickyFilters = this.$refs['sticky-filter'] as any
    const stickyFilter = Array.isArray(stickyFilters) ? stickyFilters[0]?.$el : stickyFilters?.$el
    if (targetReview) {
      setTimeout(() => {
        const targetPosition = targetReview.offsetTop || 0
        const scrollItem = getScrollContainer(targetReview)
        if (scrollItem) {
          this.$nextTick(() => {
            scrollItem.scrollTo({
              top: targetPosition - stickyFilter?.offsetHeight - 100,
              behavior: 'smooth'
            })
          })
        }
      }, 200)
    }
  }

  handleFilterChange() {
    this.paginationInfo.page = 1
    // 滚动模式切换筛选需要滚动到最上方
    if (this.paginationMode === 'scroll') {
      this.adjustScrollPosition()
    }
    this.refreshReviewList(true)
  }

  handleSortChange(val: string) {
    this.sortBy = val
    this.paginationInfo.page = 1
    // 滚动模式切换筛选需要滚动到最上方
    if (this.paginationMode === 'scroll') {
      this.adjustScrollPosition()
    }
    this.refreshReviewList(true)
  }

  // 调整滚动位置，如果pin住的时候需要滚动到最上面pin住的地方
  adjustScrollPosition() {
    if (this.isPinned) {
      const anchorDom = this.$refs['reviews-anchor'] as any
      if (anchorDom) {
        anchorDom.scrollIntoView({
          behavior: 'smooth',
          block: 'start'
        })
      }
    }
  }

  async handlePageChange(page: number) {
    // 翻页器模式翻页需要滚动到最上方
    if (this.paginationMode === 'paginator') {
      const anchorDom = this.$refs['reviews-anchor'] as any
      if (anchorDom) {
        this.$nextTick(() => {
          anchorDom.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
          })
        })
      }
    }
    await this.refreshReviewList(false, { page })
    // 翻页模式无论当页是否加载成功都更新页码
    this.paginationInfo.page = page
  }

  throttleReachBottom = throttle(() => {
    this.reachBottom()
  }, 1000)

  // 滚动模式触底加载
  async reachBottom() {
    // 如果当前正在加载中或没有更多数据，则不再请求数据
    if (this.loading || this.isNoMore || this.bottomLoading || this.overviewLoading || this.listLoading) {
      return
    }
    try {
      this.bottomLoading = true
      const result = await this.getReviewList(false)
      if (result) {
        // 触底加载仅在当前页加载成功时更新页码
        this.paginationInfo.page = result.current_page
        if (result.current_page === 1) {
          this.reviewList = result.review_list
        } else {
          this.reviewList = [...this.reviewList, ...result.review_list]
        }
      } else {
        this.loadingError = true
      }
    } catch (e) {
      this.loadingError = true
    } finally {
      this.bottomLoading = false
    }
  }

  // 翻页器翻页 / 筛选器筛选 / 排序
  async refreshReviewList(resetPage: boolean, params?: any) {
    try {
      this.listLoading = true
      const result = await this.getReviewList(resetPage, params)
      if (result) {
        this.reviewList = result.review_list
      } else {
        this.reviewList = []
      }
    } catch (e) {
      this.loadingError = true
      this.reviewList = []
    } finally {
      this.listLoading = false
    }
  }

  // 获取review数据 overview+filter+首屏list
  async getReviewData() {
    try {
      this.overviewLoading = true
      if (!this._axios) {
        return
      }
      const { success, result } = await this._axios.$get('/v1/platformbffsrv/reviewcomponent/service/get_over_review_info', {
        params: {
          ...this.extraData
        }
      })
      if (success) {
        this.initData(result)
        this.initPosition()
      }
    } catch (e) {
      console.error('get review data error')
    } finally {
      this.overviewLoading = false
    }
  }

  // 仅在独立modal模式下，进入页面时会滚动到特定位置
  initPosition() {
    if (!isClient) {
      return
    }
    if (this.display === 'modal') {
      this.$nextTick(() => {
        if (this.sourceReviewId) {
          this.jumpToReview(this.sourceReviewId)
        } else if (this.isInitPosition) {
          const reviewListEl = this.$refs['review-list'] as any
          reviewListEl?.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
          })
        } else {
          // 默认行为定位到最上方
          const reviewListEl = this.$refs['review-list'] as any
          const scrollItem = getScrollContainer(reviewListEl)
          if (scrollItem) {
            scrollItem.scrollTo({
              top: 0,
              behavior: 'smooth'
            })
          }
        }
      })
    }
  }

  async getReviewList(resetPage: boolean, extra?: any) {
    if (this.cancelToken) {
      this.cancelToken.cancel('cancel')
      this.cancelToken = null
    }
    const params = {
      // 如果extra没传入page信息，则默认获取下一页数据
      page: resetPage ? 1 : this.paginationInfo.page + 1,
      sort_key: this.sortBy,
      filter_key: this.filterBy.join(','),
      ...this.extraData,
      ...extra
    }
    this.loadingError = false
    this.cancelToken = this._axios?.CancelToken?.source?.() || null
    const { success, result } = await this._axios.$get('/v1/platformbffsrv/reviewcomponent/service/get_review_list', {
      params,
      cancelToken: this.cancelToken?.token
    })
    if (success) {
      this.totalReviews = result?.total || 0
      this.isNoMore = !result?.has_next
      return {
        total: 0,
        has_next: false,
        current_page: 1,
        review_list: [],
        ...result
      }
    } else {
      return null
    }
  }

  goBooking(productInfo) {
    if (productInfo.is_booking_now) {
      if (this.display === 'modal') {
        this.$emit('close')
      }
      this.$emit('book', productInfo)
    }
  }
}
</script>

<style lang="scss" scoped>
.review-detail {
  height: 100%;
  &__header {
    color: $color-text-primary;
  }
  &__title {
    position: relative;
    padding-left: 12px;
    &::before {
      background-color: $color-brand-primary;
      border-radius: 3px;
      content: "";
      position: absolute;
      display: inline-block;
      left: 0;
      top: 6px;
    }
  }
  &__sub-title {
    @include font-paragraph-m-bold;
    color: $color-text-primary;
    margin-bottom: 4px;
  }
  &__overview-divider {
    margin: 24px 0;
  }
  &__filter-sticky {
    background-color: $color-bg-1;
  }
  &__divider-large {
    background-color: $color-bg-3;
    height: 12px;
    width: 100%;
  }
  &__aisummary {
    margin-top: 24px;
    ::v-deep .ai-review-summary {
      margin: 24px 0;
    }
  }
  &__review-list {
    height: 100%;
  }
  &__card {
    margin: 24px 0;
    &:last-of-type {
      margin-bottom: 0;
    }
  }
  &__empty-result {
    text-align: center;
    padding: 32px 0;
    .tips {
      @include font-body-s-bold-v2;
      margin-top: 20px;
      text-align: center;
      color: $color-text-primary;
    }
    .button {
      margin-top: 20px;
    }
  }
}
// 差异化样式
.review-detail__modal {
  .review-detail__title {
    @include font-heading-xs;
    &::before {
      height: 16px;
      width: 4px;
    }
  }
  &.review-detail__desktop {
    .review-detail__header {
      padding: 24px 48px 16px;
    }
    .review-detail__review-list {
      padding: 8px 48px 0;
    }
    .review-detail__list-loading {
      margin-top: 24px;
    }
    .review-detail__loading, .review-detail__overview {
      padding: 0 48px;
    }
    .is-pinned {
      margin: 0 -48px;
      padding: 0 48px;
      border-bottom: 1px solid $color-divider-solid-normal;
    }
  }
  &.review-detail__mobile {
    .review-detail__rating {
      margin-top: 24px;
    }
    .review-detail__review-list {
      padding: 8px 20px 0;
    }
    .review-detail__list-loading {
      margin-top: 24px;
    }
    .review-detail__loading, .review-detail__overview {
      padding: 0 20px;
    }
    .is-pinned {
      margin: 0 -20px;
      padding: 12px 20px 0;
      border-bottom: 1px solid $color-divider-solid-normal;
    }
  }
  ::v-deep .review-filter {
    margin-bottom: 16px;
  }
}

.review-detail__inline {
  &.review-detail__desktop {
    .review-detail__header {
      padding-bottom: 20px;
    }
    .review-detail__title {
      @include font-heading-m;
      &::before {
        height: 24px;
        width: 6px;
      }
    }
  }
  &.review-detail__mobile {
    .review-detail__header {
      padding-bottom: 16px;
    }
    .review-detail__title {
      @include font-heading-xs;
      &::before {
        height: 16px;
        width: 4px;
      }
    }
  }
  ::v-deep .review-filter {
    margin-bottom: 32px;
    padding-top: 0;
  }
  .review-detail__review-list {
    margin-top: 32px;
  }
  .reviews-anchor-scrolltop {
    scroll-margin-top: 150px;
  }
}
</style>
