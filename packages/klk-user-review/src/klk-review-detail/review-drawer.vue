<template>
  <klk-drawer :visible="visible" direction="right" class="review-drawer" @close="close">
    <div class="review-drawer__header">
      <div class="review-drawer__icon-close" @click="close">
        <IconClose theme="outline" size="16" fill="#666666" />
      </div>
      <span class="review-drawer__text-close">{{ $t('close') }}</span>
    </div>
    <review-detail
      :title="$t('208981')"
      display="modal"
      :visible="visible"
      v-bind="$attrs"
      v-on="$listeners"
      @close="close"
    >
      <template v-for="(_, name) in $scopedSlots" v-slot:[name]="data">
        <slot :name="name" v-bind="data" />
      </template>
    </review-detail>
  </klk-drawer>
</template>

<script lang="ts">
import { Component, Mixins } from 'vue-property-decorator'
import KlkDrawer from '@klook/klook-ui/lib/drawer'
import IconClose from '@klook/klook-icons/lib/IconClose'
import Common from './common'

@Component({
  name: 'ReviewDrawer',
  components: {
    IconClose,
    KlkDrawer
  }
})
export default class ReviewDrawer extends Mixins(Common) {
}
</script>

<style lang="scss" scoped>
.review-drawer {
  z-index: 1000 !important;
  &__header {
    @include font-body-s-bold-v2;
    padding: 20px 48px;
    border-bottom: solid 1px #eee;
    color: #666666;
    display: flex;
    align-items: center;
  }
  &__icon-close {
    cursor: pointer;
    width: fit-content;
  }
  &__text-close {
    margin-left: 8px;
  }
  ::v-deep .klk-drawer-content {
    width: 680px;
  }
}
</style>
