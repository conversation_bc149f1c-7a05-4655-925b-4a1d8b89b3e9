import { Vue, Component, Prop } from 'vue-property-decorator'
import KlkBottomSheet from '@klook/klook-ui/lib/bottom-sheet'
import KlkIcon from '@klook/klook-ui/lib/icon'
import KlkDropdown from '@klook/klook-ui/lib/dropdown'
import CheckIcon from '../../../klk-user-review-common/components/icon/check.vue'
import { ISortItem, IFilterItem } from '@/types/review-detail'

import '@klook/klook-ui/lib/bottom-sheet/style'
import '@klook/klook-ui/lib/dropdown/style'

@Component({
  name: 'ReviewFilterCommon',
  components: {
    CheckIcon,
    KlkBottomSheet,
    KlkIcon,
    KlkDropdown
  }
})
export default class ReviewFilterCommon extends Vue {
  @Prop({ type: Array, default: () => [] }) sortList!: ISortItem[]
  @Prop({ type: Array, default: () => [] }) filterList!: IFilterItem[]
  @Prop({ type: String, default: () => '' }) sortBy!: string
  @Prop({ type: Array, default: () => [] }) filterBy!: string[]

  get currentSortDesc() {
    return this.sortList.find(item => item.key === this.sortBy)?.desc || ''
  }

  mounted() {
    // 初始化选中项
    const defaultSelected = this.filterList.filter(item => item.default_selected) || []
    // 查看选中项中是否有单选
    const singleSelect = defaultSelected?.find(item => item.is_only_single_select) || null
    if (singleSelect) {
      // 如果有单选，其他的都不选中
      this.$emit('update:filter-by', [singleSelect.filter_key])
    } else {
      this.$emit('update:filter-by', defaultSelected ? defaultSelected.map(item => item.filter_key) : [])
    }
    this.$emit('update:sort-by', this.sortList.find(item => item.default_selected)?.key || this.sortList[0]?.key)
  }

  handleFilterButtonClick(val: string) {
    const originalSelectedList = [...this.filterBy]

    if (originalSelectedList.includes(val)) {
      originalSelectedList.splice(originalSelectedList.indexOf(val), 1)
      // 取消选中
      this.$emit('update:filter-by', originalSelectedList)
      // 如果全部反选了，就自动选上第一个选项
      if (originalSelectedList.length === 0) {
        this.$emit('update:filter-by', this.filterList[0] ? [this.filterList[0].filter_key] : [])
      }
    } else {
      // 查看当前项是否为单选
      const isSingleSelect = this.filterList.find(item => item.filter_key === val)?.is_only_single_select
      if (isSingleSelect) {
        // 如果是单选，其他的都不选中
        this.$emit('update:filter-by', [val])
      } else {
        // 检查当前选择列表是否包含单选项
        const isIncludeSingleSelect = this.filterBy.length === 1 ? this.filterList.find(item => item.filter_key === this.filterBy[0])?.is_only_single_select : false
        if (isIncludeSingleSelect) {
          this.$emit('update:filter-by', [val])
        } else {
          this.$emit('update:filter-by', this.filterBy.concat(val))
        }
      }
    }

    this.$emit('onChangeFilter')
  }

  hanldeSortChange(val: string) {
    this.$emit('update:sort-by', val)
    this.$emit('onChangeFilter')
  }
}
