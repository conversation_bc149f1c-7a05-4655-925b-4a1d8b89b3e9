<template>
  <div class="review-filter">
    <div class="review-filter__sort">
      <span class="review-filter__label">{{ $t('208986-sort_by') }}</span>
      <div class="review-filter__sort-container">
        <div class="review-filter__sort-button" @click="open">
          <div class="review-filter__sort-btn">
            <span class="review-filter__sort-btn-text">{{ currentSortDesc }}</span><klk-icon type="icon_navigation_chevron_down_xs"></klk-icon>
          </div>
        </div>

        <klk-bottom-sheet
          v-if="visible"
          show-close
          :title="$t('28270')"
          :visible.sync="visible"
          class="review-filter__sort-modal"
          data-spm-page="ReviewSortBy"
        >
          <div
            v-for="(item, index) in sortList"
            :key="item.desc"
            :class="['review-filter__sort-modal-item', {selected: sortBy === item.key}]"
            @click="handleSelectSort(item.key)"
            :data-spm-module="`SortBy_LIST?idx=${index}&len=${sortList.length}&ext=${encodeURIComponent(JSON.stringify({
              SortType: item.desc_en
            }))}`"
            data-spm-virtual-item="__virtual"
            v-galileo-click-tracker="{
              spm: 'SortBy_LIST',
              componentName: 'klk-user-review'
            }"
          >
            <span>
              {{ item.desc }}
            </span>
            <CheckIcon v-if="sortBy === item.key"></CheckIcon>
          </div>
        </klk-bottom-sheet>
      </div>
    </div>
    <div class="review-filter__filter">
      <span class="review-filter__label">{{ $t('208987-filter_by') }}</span>
      <div class="review-filter__button-container">
        <div class="review-filter__button">
          <span
            v-for="(item, index) in filterList"
            :key="item.filter_key"
            :class="{ 'on': filterBy.includes(item.filter_key) }"
            class="review-filter__button-item"
            @click="handleFilterButtonClick(item.filter_key)"
            :data-spm-module="`ReviewFilter_LIST?idx=${index}&len=${filterList.length}&ext=${encodeURIComponent(JSON.stringify({
              FilterType: item.filter_desc_en
            }))}`"
            data-spm-virtual-item="__virtual"
            v-galileo-click-tracker="{
              spm: 'ReviewFilter_LIST',
              componentName: 'klk-user-review'
            }"
          >
            {{ item.filter_desc }}
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Mixins } from 'vue-property-decorator'
import ReviewFilterMixins from '../common'

@Component({
  name: 'ReviewFilter'
})
export default class ReviewFilter extends Mixins(ReviewFilterMixins) {
  visible = false

  open() {
    this.visible = true
  }

  handleSelectSort(val: string) {
    this.visible = false
    this.hanldeSortChange(val)
  }
}
</script>

<style lang="scss" scoped>
.review-filter {
  padding-top: 12px;
  background-color: $color-bg-1;
  &__sort {
    margin-bottom: 12px;
    display: flex;
    align-items: center;

    &-btn {
      padding: 8px 12px;
      border: 1px solid $color-brand-primary;
      color: $color-brand-primary;
      background-color: $color-brand-primary-light;
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 100px;
      height: 32px;
      &-text {
        @include font-caption-m-semibold;
        margin-right: 4px;
      }
    }

    &-modal {
      &-item {
        padding: 10px 0;
        display: flex;
        justify-content: space-between;
        align-items: center;
        position: relative;
        @include font-body-m-regular;
        color: $color-text-primary;

        &.selected {
          @include font-body-m-semibold;
          color: $color-brand-primary;
        }
      }

      ::v-deep  .klk-bottom-sheet-mask {
        background-color: $color-overlay-default-2;
      }

      ::v-deep  .klk-bottom-sheet-inner {
        min-height: 40%;
      }
    }
  }
  &__filter {
    display: flex;
    align-items: center;
    width: calc(100% + 20px);
  }
  &__label {
    @include font-body-s-regular-v2;
    color: $color-text-primary;
    margin-right: 8px;
    flex-shrink: 0;
  }
  &__button-container {
    flex-grow: 1;
    overflow-y: scroll;
    scrollbar-width: none; /* Firefox 64 */
    -ms-overflow-style: none; /* Internet Explorer 11 */
    &::-webkit-scrollbar { /** WebKit */
      display: none;
    }
  }
  &__button {
    display: flex;
    height: 32px;
    gap: 0 8px;
    &-item {
      @include font-caption-m-semibold;
      padding: 8px 12px;
      line-height: 1.32;
      color: $color-text-primary;
      border: 1px solid $color-border-normal;
      border-radius: 100px;
      min-width: 48px;
      text-align: center;
      flex-shrink: 0;
      cursor: pointer;

      &:hover {
        border-color: $color-border-normal;
      }

      &.on {
        border-color: $color-brand-primary;
        color: $color-brand-primary;
        background-color: $color-brand-primary-light;
      }
    }
  }
}
</style>
