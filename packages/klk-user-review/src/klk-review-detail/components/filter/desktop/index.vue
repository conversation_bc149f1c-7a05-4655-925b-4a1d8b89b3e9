<template>
  <div class="review-filter">
    <div class="review-filter__sort">
      <span class="review-filter__label">{{ $t('208986-sort_by') }}</span>
      <klk-dropdown v-model="sortBy" :checkable="true" @click="hanldeSortChange">
        <div class="review-filter__sort-btn">
          <span class="review-filter__sort-btn-text">{{ currentSortDesc }}</span><klk-icon type="icon_navigation_chevron_down_xs"></klk-icon>
        </div>
        <klk-dropdown-menu slot="list">
          <klk-dropdown-item
            v-for="(item, index) in sortList"
            :key="item.key"
            :name="item.key"
            :data-spm-module="`SortBy_LIST?idx=${index}&len=${sortList.length}&ext=${encodeURIComponent(JSON.stringify({
              SortType: item.desc_en
            }))}`"
            data-spm-virtual-item="__virtual"
            v-galileo-click-tracker="{
              spm: 'SortBy_LIST',
              componentName: 'klk-user-review'
            }"
          >
            {{ item.desc }}
            <div slot="append">
              <CheckIcon v-if="sortBy === item.key"></CheckIcon>
            </div>
          </klk-dropdown-item>
        </klk-dropdown-menu>
      </klk-dropdown>
    </div>
    <div class="review-filter__filter">
      <span class="review-filter__label">{{ $t('208987-filter_by') }}</span>
      <div class="review-filter__button">
        <span
          v-for="(item, index) in filterList"
          :key="item.filter_key"
          :class="{ 'on': filterBy.includes(item.filter_key) }"
          class="review-filter__button-item"
          :data-spm-module="`ReviewFilter_LIST?idx=${index}&len=${filterList.length}&ext=${encodeURIComponent(JSON.stringify({
            FilterType: item.filter_desc_en
          }))}`"
          data-spm-virtual-item="__virtual"
          v-galileo-click-tracker="{
            spm: 'ReviewFilter_LIST',
            componentName: 'klk-user-review'
          }"
          @click="handleFilterButtonClick(item.filter_key)"
        >
          {{ item.filter_desc }}
        </span>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Mixins } from 'vue-property-decorator'
import ReviewFilterMixins from '../common'

@Component({
  name: 'ReviewFilter'
})
export default class ReviewFilter extends Mixins(ReviewFilterMixins) {
}
</script>

<style lang="scss" scoped>
.review-filter {
  padding-top: 12px;
  background-color: $color-bg-1;
  &__sort {
    margin-bottom: 12px;
    display: flex;
    align-items: center;
    &-btn {
      padding: 8px 12px;
      border: 1px solid $color-brand-primary;
      color: $color-brand-primary;
      background-color: $color-brand-primary-light;
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 100px;
      &-text {
        @include font-caption-m-semibold;
        margin-right: 4px;
      }
    }
  }
  &__filter {
    display: flex;
  }
  &__label {
    @include font-body-s-regular-v2;
    color: $color-text-primary;
    margin-right: 8px;
    padding: 8px 0;
    flex-shrink: 0;
  }
  &__button {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;

    &-item {
      @include font-caption-m-semibold;
      line-height: 1.32;
      padding: 8px 12px;
      color: $color-text-primary;
      border: 1px solid $color-border-normal;
      border-radius: 9999px;
      min-width: 48px;
      text-align: center;
      cursor: pointer;

      &:hover {
        border-color: $color-border-normal;
      }
      
      &.on {
        border-color: $color-brand-primary;
        color: $color-brand-primary;
        background-color: $color-brand-primary-light;
      }
    }
  }
}
</style>
