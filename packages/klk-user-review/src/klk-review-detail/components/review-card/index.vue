<template>
  <div v-if="reviewInfo" class="review-card">
    <div class="review-card__user">
      <user-info
        :user-info="reviewInfo.user_info"
      />
    </div>
    <!-- product-info -->
    <div
      v-if="reviewInfo.product_info && reviewInfo.product_info.product_name"
      v-galileo-click-tracker="{
        spm: 'ReviewBookNow',
        componentName: 'klk-user-review',
        enable: reviewInfo.product_info.is_booking_now
      }"
      :class="['review-card__product-info', showBook && 'clickable']"
      v-bind="showBook && {
        'data-spm-module': 'ReviewBookNow',
        'data-spm-virtual-item': '__virtual'
      }"
      @click="goBooking"
    >
      <div class="review-card__product-info-name">{{ $t('reviews.review_for') }}&nbsp;{{ reviewInfo.product_info.product_name }}</div>
      <div class="review-card__product-info-icon">
        <IconNext v-if="showBook" theme="outline" size="16" />
      </div>
    </div>
    <div class="review-card__review-content">
      <user-review
        :review-info="{
          review_content: reviewInfo.review_content,
          translate_content: reviewInfo.translate_content,
          show_translation: reviewInfo.show_translation
        }"
        @translationChange="(showOrigin) => showOriginContent = showOrigin"
      />
    </div>
    <!-- image -->
    <image-preview
      v-if="reviewInfo.review_image_list && reviewInfo.review_image_list.length"
      :preview-mode="previewMode"
      :review-image="reviewInfo.review_image_list"
      @book="goBooking"
    />
    <div v-if="$scopedSlots['review-footer']" class="review-card__footer">
      <slot name="review-footer" :review-info="reviewInfo">
      </slot>
    </div>
    <!-- reply -->
    <div v-if="reviewInfo.reply" class="review-card__reply">
      <div class="review-card__reply-from">{{ reviewInfo.reply.reply_from || $t('ptp_review_klook_reply') }}</div>
      <SeeMoreWithLines
        :content="showOriginContent ? reviewInfo.reply.content : (reviewInfo.reply.translate_content || reviewInfo.reply.content)"
      />
    </div>
    <!-- like -->
    <review-helpful
      :review-info="reviewInfo"
    />
  </div>
</template>

<script lang="ts">
import { Vue, Component, Prop, Provide } from 'vue-property-decorator'
import IconNext from '@klook/klook-icons/lib/IconNext'
import ImagePreview from '../image-preview/index.vue'
import UserReview from '../../../klk-user-review-common/components/user-review.vue'
import UserInfo from '../../../klk-user-review-common/components/user-info.vue'
import ReviewHelpful from '../../../klk-user-review-common/components/review-helpful.vue'
import SeeMoreWithLines from '../../../klk-user-review-common/components/see-more-with-lines.vue'
import { IReviewItem } from '@/types/review'

@Component({
  name: 'ReviewCard',
  components: {
    UserReview,
    UserInfo,
    SeeMoreWithLines,
    IconNext,
    ImagePreview,
    ReviewHelpful
  }
})
export default class ReviewCard extends Vue {
  @Prop({ type: Object, default: () => null }) reviewInfo!: IReviewItem
  @Prop({ type: String, default: () => 'modal' }) previewMode!: 'inline' | 'modal' | 'fullscreen'

  showOriginContent: boolean = false

  // 传递给底层的image preview modal使用
  @Provide('reviewCardData')
  get reviewCardData() {
    return {
      reviewInfo: this.reviewInfo || null
    }
  }

  get showBook() {
    return this.reviewInfo?.product_info?.is_booking_now
  }

  goBooking(e?: Event) {
    e?.stopPropagation && e.stopPropagation()
    if (this.showBook) {
      this.$emit('book', this.reviewInfo.product_info)
    }
  }
}
</script>

<style lang="scss" scoped>
@import "../../../style/index.scss";
.review-card {
  &__user {
    margin-bottom: 12px;
  }
  &__product-info {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    color: $color-text-placeholder;
    &.clickable {
      cursor: pointer;
    }
    &-name {
      @include font-paragraph-s-regular;
      @include text-ellipsis(2);
    }
    &-icon {
      margin-left: 8px;
    }
  }
  &__footer {
    margin-top: 16px;
  }
  &__reply {
    margin-top: 16px;
    background-color: $color-bg-3;
    border-radius: $radius-m;
    padding: 12px 16px;
    &-from {
      @include font-body-s-bold;
      color: $color-text-primary;
      margin-bottom: 4px;
    }
  }
}
</style>
