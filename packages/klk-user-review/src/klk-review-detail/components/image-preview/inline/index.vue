<template>
  <div class="review-img-container">
    <div v-if="visible" class="review-img-swiper-box" :style="{ width: `${width}px`, height: `${width * (5/8)}px` }">
      <klk-carousel
        :transition="transition"
        :cycle="false"
        :active="index"
        :loop="false"
        :autoplay="false"
        hide-indicators
        :controller-position="btnPosition"
        @change="swiperChange"
      >
        <klk-carousel-item
          v-for="(image, index) in images"
          :key="index"
        >
          <div class="review-big-img-item" :style="{ height: `${width * (5/8)}px` }">
            <img v-lazy="image.url" @click="hidePreview" />
          </div>
        </klk-carousel-item>
      </klk-carousel>
    </div>
  </div>
</template>

<script lang="ts">
import { Vue, Component, Prop, Watch } from 'vue-property-decorator'
import KlkCarousel from '@klook/klook-ui/lib/carousel'

type Transition = 'fade' | 'slide';

@Component({
  name: 'InlineImagePreview',
  components: {
    KlkCarousel
  }
})
export default class InlineImagePreview extends Vue {
  @Prop({ default: () => [] }) images!: any
  @Prop() visible!: boolean
  @Prop({ type: Number, default: 0 }) index!: number
  @Prop({ type: Number, default: 0 }) width!: number
  transition: Transition = 'slide'

  @Prop({
    default: 'outside'
  }) btnPosition!: string

  swiperChange(index: number) {
    this.$emit('update:index', index)
  }

  @Watch('images')
  imagesChange() {
    this.hidePreview()
  }

  hidePreview() {
    this.$emit('close')
  }
}
</script>

<style lang="scss" scoped>
@import '../../../../style//index.scss';

.review-img-container {
  .review-small-imgs {
    position: relative;
    font-size: 0;
    display: flex;
    margin-top: 12px;

    .review-small-img-item {
      position: relative;
      width: 100px;
      height: 100px;
      border-radius: $radius-xl;
      overflow: hidden;
      background-color: $color-bg-widget-darker-3;
      margin-right: 16px;
      cursor: zoom-in;
      background-repeat: no-repeat;
      background-size: cover;
      background-position: center center;

      &:last-child {
        margin-right: 0;
      }

      &.active {
        opacity: $opacity-solid;
      }

      &.deactive {
        opacity: $opacity-overlay-mobile;
      }

      .review-small-img-item-loading {
        position: absolute;
        top: 38px;
        left: 6px;
        color: $color-text-disabled;
      }
    }
  }

  .review-img-swiper-box {
    margin-top: 16px;
    background-color: $color-common-black;

    .review-big-img-item {
      position: relative;
      text-align: center;

      img {
        @include lazy-load-bg();
        cursor: zoom-out;
        position: absolute;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;
        max-width: 100%;
        max-height: 100%;
        margin: auto;
      }
    }

    ::v-deep .klk-carousel-prev, ::v-deep .klk-carousel-next {
      z-index: 1 !important;
    }
  }
}
</style>
