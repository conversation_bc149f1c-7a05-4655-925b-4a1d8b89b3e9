<template>
  <transition name="fade">
    <div v-if="visible" class="review-image-viewer__layer">
      <transition name="fade">
        <div v-swiper:$swiper="swiperOptions" class="review-image-viewer__swiper-container swiper-container">
          <div class="review-image-viewer__top">
            <IconClose theme="outline" size="20" fill="#b1b1b1" @click.native="close" />
            <div ref="pagination" class="swiper-pagination review-swiper-pagination"></div>
          </div>

          <div class="swiper-wrapper">
            <div v-for="image in images" :key="image.id" class="swiper-slide">
              <div class="review-image-wrapper">
                <div class="review-image-content">
                  <ImageZoomer>
                    <img :src="image.url" alt="review-image" class="review-image" />
                  </ImageZoomer>
                </div>
              </div>
            </div>
          </div>
        </div>
      </transition>
    </div>
  </transition>
</template>

<script lang="ts">
import { Component, Prop, Vue, Watch } from 'vue-property-decorator'
import IconClose from '@klook/klook-icons/lib/IconClose'
import ImageZoomer from './image-zoomer/index.vue'

const isServer = typeof window === 'undefined'
if (!isServer) {
  const VueAwesomeSwiper = require('vue-awesome-swiper/dist/ssr')
  Vue.use(VueAwesomeSwiper)
}

@Component({
  components: {
    ImageZoomer,
    IconClose
  }
})
export default class ImageViewer extends Vue {
  @Prop() visible!: boolean
  @Prop() images!: any[]
  @Prop({ type: Number, default: 0 }) index!: number

  @Watch('visible')
  indexChange(val: boolean) {
    if (val) {
      this.swiperOptions.initialSlide = this.index
    }
  }

  swiperOptions = {
    slidesPerView: 1,
    initialSlide: this.index,
    paginationClickable: true,
    spaceBetween: 16,
    pagination: {
      el: '.swiper-pagination',
      type: 'fraction'
    }
  }

  close() {
    this.$emit('close')
  }
}
</script>

<style lang="scss">
.review-image-viewer {

  &__layer {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 999;
    background-color: $color-common-black;
  }

  &__top {
    display: flex;
    align-items: flex-end;
    justify-content: center;
    color: $color-text-primary-onDark;
    position: absolute;
    z-index: 2;
    top: 0;
    width: 100%;
    left: 0;
    padding: 16px 20px;

    .review-image_icon-close {
      position: absolute;
      left: 20px;
      top: 16px;
      z-index: 11;
    }

    .review-swiper-pagination {
      position: static;
    }
  }

  &__swiper-container {
    height: 100%;

    .swiper-wrapper {
      .swiper-slide {
        height: 100%;
        display: flex;
        align-items: center;

        .vue-zoomer {
          padding: 400px 0;
          margin: -400px 0;
          width: 100%;
        }
      }
    }

    .review-image {
      width: 100%;

      &-wrapper {
        padding: 400px 0;
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100%;
        width: 100%;
        overflow-y: auto;
        background-color: $color-common-black;
      }

      &-content {
        max-height: 100%;
      }
    }
  }
}
</style>
