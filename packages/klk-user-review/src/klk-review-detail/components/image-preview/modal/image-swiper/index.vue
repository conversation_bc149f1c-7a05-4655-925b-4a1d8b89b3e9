<template>
  <div class="review-img-swiper">
    <div class="review-img-swiper__container">
      <div class="review-img-swiper__container-body">
        <div
          ref="$bigImageSwiper"
          v-swiper:$bigImageSwiper="bigImageSwiperOption"
          class="swiper-container js-swiper-container"
        >
          <div class="swiper-wrapper">
            <div
              v-for="(item, idx) in imageList"
              :key="idx"
              v-lazy:background-image.container="item.url"
              ratio="1:1"
              class="swiper-slide"
              :class="highlightSwiperSlideIndex === idx && 'current-slide-visible'"
            >
            </div>
          </div>
          <div class="swiper-pagination swiper-pagination-fraction">
            {{ highlightSwiperSlideIndex + 1 }} / {{ imageList.length }}
          </div>
          <div class="next-prev swiper-next">
            <span class="next-prev__icon">
              <IconNext class="icon" theme="outline" size="20" :fill="colors.colorTextReverse" />
            </span>
          </div>
          <div class="next-prev swiper-prev">
            <span class="next-prev__icon">
              <IconBack class="icon" theme="outline" size="20" :fill="colors.colorTextReverse" />
            </span>
          </div>
        </div>
      </div>
    </div>
    <div class="small-swiper__container">
      <div
        ref="$smallImageSwiper"
        v-swiper:$smallImageSwiper="smallImageSwiperOption"
        class="swiper-container"
      >
        <div class="swiper-wrapper swiper-no-swiping">
          <div
            v-for="(item, idx) in imageList"
            :key="idx"
            v-lazy:background-image.container="item.resize_url"
            ratio="1:1"
            class="swiper-slide swiper-no-swiping"
            :class="highlightSwiperSlideIndex === idx ? 'high-light-swiper-slide': ''"
            @click="highlightSwiperSlide(idx)"
          >
          </div>
        </div>
      </div>
      <div class="small-control small-swiper-next">
        <span class="next-prev__icon">
          <IconNext class="icon" theme="outline" size="20" :fill="colors.colorTextReverse" />
        </span>
      </div>
      <div class="small-control small-swiper-prev">
        <span class="next-prev__icon">
          <IconBack class="icon" theme="outline" size="20" :fill="colors.colorTextReverse" />
        </span>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Prop, Watch, Vue } from 'vue-property-decorator'
import IconBack from '@klook/klook-icons/lib/IconBack'
import IconNext from '@klook/klook-icons/lib/IconNext'
import { $colorTextReverse, $colorBg1 } from '@klook/klook-ui/lib/utils/design-token-esm'

const isServer = typeof window === 'undefined'
if (!isServer) {
  const VueAwesomeSwiper = require('vue-awesome-swiper/dist/ssr')
  Vue.use(VueAwesomeSwiper)
}

@Component({
  components: {
    IconBack,
    IconNext
  }
})
export default class DesktopImagesSwiper extends Vue {
  @Prop({ default: () => [] }) imageList!: any[]
  @Prop({ type: Number, default: 0 }) currentIndex!: number

  $smallImageSwiper: any = null;
  $bigImageSwiper: any = null;
  highlightSwiperSlideIndex: number = 0;

  get colors() {
    return {
      colorTextReverse: $colorTextReverse,
      colorBg1: $colorBg1
    }
  }

  @Watch('currentIndex', { immediate: true })
  currentIndexChange(val: number) {
    this.$nextTick(() => {
      this.$bigImageSwiper && this.$bigImageSwiper.slideTo(val)
    })
  }

  smallImageSwiperOption: object = {
    slidesPerView: "auto",
    centeredSlides: false,
    navigation: {
      nextEl: ".small-swiper-next",
      prevEl: ".small-swiper-prev",
    },
    lazy: {
      loadPrevNext: true,
      loadOnTransitionStart: true,
    },
    spaceBetween: 10,
    slidesPerGroup: 5,
    noSwiping: true,
    on: {
      init: () => {
        this.initSwiper("$smallImageSwiper");
      },
      slideChange: () => {
        this.slideChangeTransitionEnd("$smallImageSwiper");
      },
    },
  };

  bigImageSwiperOption: object = {
    navigation: {
      nextEl: ".swiper-next",
      prevEl: ".swiper-prev",
    },
    lazy: {
      loadPrevNext: true,
      loadOnTransitionStart: true,
    },
    on: {
      init: () => {
        this.initSwiper("$bigImageSwiper");
      },
      slideChange: () => {
        this.bigSwiperSlideChange();
        this.slideChangeTransitionEnd("$bigImageSwiper");
      }
    },
  }

  slideChangeTransitionEnd(swiper: string) {
    const selfSwiper = (this as any)[swiper];
    if (selfSwiper.isEnd) {
      selfSwiper.navigation.$nextEl.css("display", "none");
    } else {
      selfSwiper.navigation.$nextEl.css("display", "flex");
    }
    if (selfSwiper.isBeginning) {
      selfSwiper.navigation.$prevEl.css("display", "none");
    } else {
      selfSwiper.navigation.$prevEl.css("display", "flex");
    }

  }

  initSwiper(swiper: string) {
    this.$nextTick(() => {
      const selfSwiper = (this as any)[swiper];
      if (swiper === "$bigImageSwiper") {
        if (selfSwiper.slides.length <= 1) {
          selfSwiper.navigation.$nextEl.css("display", "none");
        } else {
          selfSwiper.navigation.$nextEl.css("display", "flex");
        }
      }
      if (swiper === "$smallImageSwiper") {
        if (selfSwiper.slides.length < 5) {
          selfSwiper.navigation.$nextEl.css("display", "none");
        } else {
          selfSwiper.navigation.$nextEl.css("display", "flex");
        }
      }
      if (selfSwiper.isBeginning) {
        selfSwiper.navigation.$prevEl.css("display", "none")
      } else {
        selfSwiper.navigation.$prevEl.css("display", "flex")
      }
      if (selfSwiper.isEnd) {
        selfSwiper.navigation.$nextEl.css("display", "none")
      } else {
        selfSwiper.navigation.$nextEl.css("display", "flex")
      }
    })
  }

  bigSwiperSlideChange() {
    const idx = this.$bigImageSwiper.realIndex
    this.highlightSwiperSlideIndex = idx
    this.$smallImageSwiper.slideTo(idx)
  }

  highlightSwiperSlide(idx: number) {
    this.$bigImageSwiper.slideTo(idx);
    this.highlightSwiperSlideIndex = idx;
  }
}
</script>

<style lang="scss" scoped>
@import '../../../../../style//index.scss';

.review-img-swiper {
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;

  .review-img-swiper {

    &__container {
      display: flex;
      flex: 1;
      max-height: calc(100% - 160px);

      &-body {
        flex: 1;
      }
    }
  }

  .small-swiper__container {
    position: relative;
    width: calc(100% - 128px);
    height: 72px;
    margin: 20px auto 0;

    .swiper-slide {
      @include lazy-load-bg();
      box-sizing: border-box;
      width: 72px;
      height: 72px;
      background-size: cover;
      background-repeat: no-repeat;
      background-position: center center;
      cursor: pointer;
      border-radius: $radius-m;
      opacity: 0.6;

      &.high-light-swiper-slide {
        opacity: 1;
        border: 3px solid $color-text-reverse;
      }
    }

    .small-control {
      position: absolute;
      top: 50%;
      cursor: pointer;
      z-index: 10;
      outline: none;
      visibility: visible;
      width: 32px;
      height: 32px;
      border-radius: 50%;
      background-color: hsla(0,0%,100%,.16);
      display: flex;
      align-items: center;
      justify-content: center;
      transform: translateY(-50%);

      &:hover {
        border: 1px solid $color-text-reverse;
      }
    }

    .small-swiper-next {
      right: -48px;
    }

    .small-swiper-prev {
      left: -48px;
    }

    .next-prev__icon .icon {
      display: flex;
    }
  }

  .review-img-swiper__container-body {
    flex: 1;
    position: relative;
    max-width: 100%;

    .swiper-container {
      padding: 36px 64px;
      max-width: 100%;
      height: calc(100vh - 248px);
      width: calc((100vh - 160px - 88px - 72px) / 2 * 3 + 128px);
      max-height: 100%;
      overflow: hidden;

      .swiper-slide {
        @include lazy-load-bg();
        position: relative;
        background-size: contain;
        background-repeat: no-repeat;
        background-position: center center;
        display: flex;
        align-items: center;
        visibility: hidden;

        &.current-slide-visible {
          visibility: visible;
        }
      }

      .next-prev {
        position: absolute;
        top: 0;
        height: 100%;
        width: 64px;
        cursor: pointer;
        z-index: 1;

        &__icon {
          width: 48px;
          height: 48px;
          line-height: 48px;
          border-radius: 50%;
          background-color: rgba(255, 255, 255, 0.16);
          position: absolute;
          top: 50%;
          right: 0;
          transform: translate(0, -50%);
          display: flex;
          align-items: center;
          justify-content: center;

          &:hover {
            border: 1px solid $color-text-reverse;
            background-color: rgba(255, 255, 255, 0.25);
          }
          .icon {
            display: flex;
          }
        }

      }

      .swiper-next {
        right: 0;
      }

      .swiper-prev {
        left: 0;
      }

      .swiper-pagination {
        @include font-caption-m-regular;
        padding: 3px 4px;
        position: absolute;
        left: auto;
        right: calc(64px + 8px);
        bottom: 8px;
        width: auto;
        color: $color-text-reverse;
        border-radius: $radius-s;
        background: $color-overlay-default-2;
        color: $color-text-reverse;
      }
    }
  }
}
</style>
