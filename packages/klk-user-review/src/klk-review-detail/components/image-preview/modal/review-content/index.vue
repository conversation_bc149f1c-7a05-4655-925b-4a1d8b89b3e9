<template>
  <div v-if="reviewInfo" class="review-content">
    <div class="review-content__user-info">
      <user-info
        :user-info="reviewInfo.user_info"
        :hide-rating="true"
      />
    </div>
    <div class="review-content__info">
      <!-- product-info -->
      <div v-if="reviewInfo.product_info" :class="['review-content__product-info', showBook && 'clickable']" @click="goBooking">
        <div class="review-content__product-info-name">{{ $t('reviews.review_for') }}&nbsp;{{ reviewInfo.product_info.product_name }}</div>
        <div class="review-content__product-info-icon">
          <IconNext v-if="showBook" theme="outline" size="16" />
        </div>
      </div>
      <div class="review-content__content">
        <user-review
          text-mode="full"
          :review-info="{
            review_content: reviewInfo.review_content,
            translate_content: reviewInfo.translate_content,
            show_translation: false
          }"
        />
      </div>
      <review-helpful
        :review-info="reviewInfo"
      />
    </div>
  </div>
</template>
<script lang="ts">
import { Vue, Component, Inject } from 'vue-property-decorator'
import IconNext from '@klook/klook-icons/lib/IconNext'
import ReviewHelpful from '../../../../../klk-user-review-common/components/review-helpful.vue'
import UserInfo from '../../../../../klk-user-review-common/components/user-info.vue'
import UserReview from '../../../../../klk-user-review-common/components/user-review.vue'
import { IReviewItem } from '@/types/review'

@Component({
  components: {
    ReviewHelpful,
    UserInfo,
    UserReview,
    IconNext
  }
})
export default class ActivityGalleryCommentItem extends Vue {

  @Inject('reviewCardData') reviewCardData!: {
    reviewInfo: IReviewItem
  }

  showOriginContent = false

  get content() {
    return this.showOriginContent ? this.reviewInfo.review_content : (this.reviewInfo.translate_content || this.reviewInfo.review_content)
  }

  get reviewInfo() {
    return this.reviewCardData.reviewInfo
  }

  get showBook() {
    return this.reviewInfo?.product_info?.is_booking_now
  }

  goBooking(e) {
    e?.stopPropagation && e.stopPropagation()
    if (this.showBook) {
      this.$emit('book', this.reviewInfo.product_info)
    }
  }
}
</script>
<style lang="scss" scoped>
@import "../../../../../style/index.scss";
.review-content {
  color: $color-text-secondary;

  &__user-info {
    margin-bottom: 8px;
  }

  ::v-deep .user-info__name {
    color: $color-text-reverse !important;
  }

  ::v-deep .user-info__rating-time {
    color: $color-text-disabled !important;
  }

  &__product-info {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 8px;
    color: $color-text-disabled;
    &.clickable {
      cursor: pointer;
    }
    &-name {
      @include font-paragraph-s-regular;
      @include text-ellipsis(2);
    }
    &-icon {
      margin-left: 8px;
    }
  }

  &__content {
    max-height: calc(100vh - 410px);
    overflow-y: auto;
    word-break: break-word;
    color: $color-text-reverse;
    // for firefox
    scrollbar-width: thin;
    scrollbar-color: transparent transparent;

    /* for others 定义滚动条高宽及背景 高宽分别对应横竖滚动条的尺寸*/
    &::-webkit-scrollbar {
      width: 5px;
      height: 0;
    }

    /*定义滚动条轨道 内阴影+圆角*/
    &::-webkit-scrollbar-track {
      border-radius: 2px;
      background-color: transparent;
    }

    /*定义滑块 内阴影+圆角*/
    &::-webkit-scrollbar-thumb {
      border-radius: 4px;
      border-right: 1px solid transparent;
      box-shadow: 4px 0 0 $color-neutral-600 inset;
      visibility: hidden;
    }

    &:hover {
      // for firefox
      scrollbar-color: $color-neutral-600 transparent;

      // for others
      &::-webkit-scrollbar-thumb {
        visibility: initial;
      }
    }

    ::v-deep p {
      word-break: break-word;
      color: $color-text-reverse !important;
    }

    ::v-deep .more-btn {
      @include font-body-m-bold;
      color: $color-text-reverse;
    }
  }
}
</style>
