<template>
  <klk-modal
    :open="visible"
    :scrollable="false"
    :overlay-closable="false"
    :padding="0"
    :closable="true"
    :show-default-footer="false"
    :fullscreen="true"
    :overlay="false"
    class="reviews-gallery-modal"
    @close="closeModal"
  >
    <div class="reviews-container">
      <div class="image-gallery-container">
        <div class="reviews-container-left reviews-swiper">
          <ImageSwiper
            :image-list="images"
            :current-index="index"
          />
        </div>
        <div class="reviews-container-right">
          <review-content
            @book="handleBook"
          ></review-content>
        </div>
      </div>
    </div>
  </klk-modal>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator'
import ImageSwiper from './image-swiper/index.vue'
import ReviewContent from './review-content/index.vue'

@Component({
  components: {
    ReviewContent,
    ImageSwiper
  }
})
export default class ImageGallery extends Vue {
  @Prop({ default: [] }) images!: any
  @Prop() visible!: boolean
  @Prop({ type: Number, default: 0 }) index!: number

  handleBook() {
    this.$emit('close')
    this.$nextTick(() => {
      this.$emit('book')
    })
  }

  closeModal() {
    this.$emit('close')
  }
}
</script>

<style lang="scss" scoped>
@import '../../../../style/index.scss';
.reviews-gallery-modal {
  ::v-deep .klk-modal {
    margin: 0;
    padding: 0;
    background-color: rgba(0, 0, 0, 0.9);
  }

  ::v-deep .klk-modal-close {
    position: fixed;
    top: 46px;
    right: 46px;
    font-size: 32px;
    z-index: 23;
  }

  ::v-deep .klk-modal-body {
    width: 100%;
    height: 100%;
    padding: 70px 40px;
    position: relative;
    overflow: hidden;
  }

  .reviews-container {
    width: 100%;
    max-width: 100%;
    height: 100%;
    max-height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    border-radius: $radius-l;
    padding: 44px 0;

    .image-gallery-container {
      flex: 1 1 auto;
      display: flex;
      justify-content: center;
    }

    .reviews-container-left {
      position: relative;
      border-radius: $radius-l 0 0 $radius-l;
      &.banner-swiper {
        width: 100%;
        height: 100%;
        .banner-swiper__img-content {
          display: flex;
          align-content: flex-start;
          flex-direction: column;
          max-width: 100%;
          max-height: 100%;
        }
      }
      &.reviews-swiper {
        max-width: calc(100% - 352px - 80px);
      }
    }

    .reviews-container-right {
      flex: none;
      width: 352px;
      min-width: 352px;
      padding: 0 40px 40px 32px;
      border-radius: 0 $radius-l $radius-l 0;
    }
  }
}
</style>
