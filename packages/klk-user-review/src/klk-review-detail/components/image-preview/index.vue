<template>
  <div v-if="reviewImage && reviewImage.length" ref="imagesContainer" :class="['reviews-item-images', `reviews-item-images__${imagesLayout}`]">
    <div
      v-for="(image, index) in reviewImage"
      :key="image.id"
      v-lazy:background-image="image.resize_url"
      :class="highLightImg(index)"
      class="reviews-item-images-item"
      :style="{ cursor: showPreview ? 'zoom-in' : 'default' }"
      @click="showPreview ? showImageViewer(Number(index)) : null"
    >
    </div>
    <component
      :is="componentMap[previewMode]"
      v-if="showPreview"
      :visible="imageViewerVisible"
      :images="reviewImage"
      :index.sync="index"
      :width="containerWidth"
      v-bind="$attrs"
      btn-position="intersect"
      v-on="$listeners"
      @close="hideImageViewer"
    />
  </div>
</template>

<script lang="ts">
import { Vue, Component, Prop, Inject } from 'vue-property-decorator'
import ImagePreviewInline from './inline/index.vue'
import ImagePreviewFullScreen from './fullscreen/index.vue'
import ImagePreviewModal from './modal/index.vue'

@Component({
  name: 'ImagePreview',
  components: {
    ImagePreviewInline,
    ImagePreviewFullScreen,
    ImagePreviewModal
  }
})
export default class ImagePreview extends Vue {
  @Prop({ type: Array, default: () => [] }) reviewImage!: any[]
  @Prop({ type: String, default: () => '' }) previewMode!: string
  @Prop({ type: Boolean, default: () => true }) showPreview!: boolean // 新增预览开关

  @Inject('realPlatform') realPlatform?: string

  componentMap: any = {
    inline: 'ImagePreviewInline',
    fullscreen: 'ImagePreviewFullScreen',
    modal: 'ImagePreviewModal'
  }

  containerWidth: number = 0 // 容器宽度

  imagesLayout: 'large' | 'small' = 'large' // 根据容器宽度设置布局模式，一行6张 / 一行3张

  index: number = 0
  imageViewerVisible: boolean = false

  highLightImg(index: number) {
    return [this.imageViewerVisible && this.index === index ? 'active' : this.imageViewerVisible ? 'deactive' : '']
  }

  showImageViewer(index: number) {
    this.index = index
    this.imageViewerVisible = true
  }

  hideImageViewer() {
    this.imageViewerVisible = false
  }

  initImagesLayout() {
    const imagesContainer = this.$refs.imagesContainer as HTMLElement
    this.containerWidth = imagesContainer?.offsetWidth || 600
    if (imagesContainer?.offsetWidth > 600) {
      this.imagesLayout = 'large'
    } else {
      this.imagesLayout = 'small'
    }
  }

  mounted() {
    if (this.reviewImage.length) {
      this.$nextTick(() => {
        this.initImagesLayout()
      })
    }
  }
}
</script>

<style lang="scss" scoped>
@import "../../../style/index.scss";

.reviews-item {
  &-images {
    display: flex;
    flex-wrap: wrap;
    position: relative;
    margin-top: 8px;
    &-item {
      @include lazy-load-bg();
      border-radius: $radius-m;
      margin-top: 8px;
      background-repeat: no-repeat;
      background-size: cover;
      background-position: center center;
      overflow: hidden;
    }
    .active {
      opacity: $opacity-solid;
    }

    .deactive {
      opacity: $opacity-overlay-mobile;
    }
  }
}
.reviews-item-images__small {
  .reviews-item {
    &-images {
      &-item {
        width: calc((100% - 16px) / 3);
        padding-top: calc(((100% - 16px) / 3) * (5 / 8)); // 宽高 8:5

        &:not(:nth-child(3n)) {
          margin-right: 8px;
        }
      }
    }
  }
}
.reviews-item-images__large {
  .reviews-item {
    &-images {
      &-item {
        width: calc((100% - 40px) / 6);
        padding-top: calc(((100% - 40px) / 6) * (5 / 8)); // 宽高 8:5
        &:not(:nth-child(6n)) {
          margin-right: 8px;
        }
      }
    }
  }
}
</style>
