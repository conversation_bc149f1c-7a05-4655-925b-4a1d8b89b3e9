<template>
  <div
    :class="['review-list__scroll', `review-list__scroll-${realPlatform}`]"
  >
    <klk-infinite-scroll
      v-bind="currentScrollProps"
    >
      <slot></slot>
      <div
        v-if="loadingError"
        class="review-list__loading-error"
      >
        <klk-button size="mini" type="outlined" @click="refreshList">{{ $t('global.reload') }}</klk-button>
      </div>
      <template v-else slot="loading">
        <div class="review-list__loading">
          <div class="review-list__loading-wrapper">
            <klk-loading />
          </div>
          <span class="review-list__loading-text">
            {{ $t('car_rental_home_loading') }}
          </span>
        </div>
      </template>
    </klk-infinite-scroll>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop, Inject } from 'vue-property-decorator'
import KlkLoading from '@klook/klook-ui/lib/loading'
import KlkButton from '@klook/klook-ui/lib/button'
import KlkInfiniteScroll from '@klook/klook-ui/lib/infinite-scroll'

/**
 * 触底加载模式的应用场景为内嵌到modal弹层
 */
@Component({
  name: 'ReviewList',
  components: {
    KlkInfiniteScroll,
    KlkButton,
    KlkLoading
  }
})
export default class ReviewList extends Vue {
  @Prop({ default: false }) bottomLoading!: boolean
  @Prop({ default: false }) loadingError!: boolean
  @Prop({ default: false }) done!: boolean
  @Prop({ default: null }) onReachBottom!: any

  @Inject('realPlatform') realPlatform?: string

  get currentScrollProps() {
    return {
      doneText: this.$t('14258'),
      loadingError: this.loadingError,
      loading: this.bottomLoading,
      onReachBottom: this.onReachBottom,
      done: this.done
    }
  }

  refreshList() {
    this.onReachBottom && this.onReachBottom()
  }
}
</script>

<style lang="scss" scoped>
@import '../../../style/index.scss';
.review-list {
  &__scroll-desktop {
    height: calc(100% - 70px);
  }

  &__scroll-mobile {
    height: 100%;
  }

  ::v-deep .klk-bottom-sheet-body {
    padding: 0;
    overflow: hidden;
  }

  ::v-deep .klk-infinite-scroll-bottom {
    display: block;

    .klk-infinite-scroll-done-text {
      padding: 14px 0;
      @include font-body-s-regular;
      color: $color-text-secondary;
      text-align: center;
    }
  }

  &__loading-error {
    margin: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 40px;

    span {
      @include text-ellipsis(2);
      @include font-body-s-regular;
      line-height: 20px;
      color: $color-text-secondary;
    }
  }

  &__loading {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 40px;

    &-wrapper {
      position: relative;
      width: 16px;
      height: 16px;

      .klk-loading svg {
        width: 16px;
        height: 16px
      }
    }

    &-text {
      margin-left: 8px;
      @include font-body-s-regular;
      color: $color-text-secondary;
    }
  }
}
</style>
