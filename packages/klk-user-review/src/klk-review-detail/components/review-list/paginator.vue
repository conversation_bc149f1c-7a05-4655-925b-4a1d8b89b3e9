<template>
  <div
    class="review-list"
  >
    <div>
      <slot></slot>
    </div>
    <div v-if="loadingError" class="review-list__loading-error">
      <img width="152" alt="empty" :src="formatPicUrl('https://res.klook.com/image/upload/v1747101943/UED_new/Platform/platform_AI_review_2409/NoReview.png', 152, 135)" />
      <div class="tips">{{ $t('12346') }}</div>
    </div>
    <klk-pagination
      v-if="total > paginationInfo.limit"
      class="review-list__pagination"
      :hide-page-num="true"
      :total="total"
      :page-size="paginationInfo.limit"
      :current="paginationInfo.page"
      @change="onPageChange"
    />
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator'
import transformImageUrl from "@klook/klk-traveller-utils/lib/transformImageUrl";
import KlkPaination from '@klook/klook-ui/lib/pagination'
import KlkButton from '@klook/klook-ui/lib/button'
import ReviewListLoading from '../../../klk-user-review-common/components/loading/review-list-loading.vue'

/**
 * 触底加载模式的应用场景为页面中的独立楼层
 */
@Component({
  name: 'ReviewList',
  components: {
    ReviewListLoading,
    KlkPaination,
    KlkButton
  }
})
export default class ReviewList extends Vue {
  @Prop({ default: { page: 1, limit: 10 } }) paginationInfo!: any
  @Prop({ default: 0 }) total!: number
  @Prop({ default: false }) loading!: boolean
  @Prop({ default: false }) listLoading!: boolean
  @Prop({ default: false }) loadingError!: boolean
  @Prop({ default: 'desktop' }) platform!: string

  get realWebp() {
    return this.$store?.state?.klook?.webp || 0
  }

  onPageChange(page: number) {
    // 上报翻页埋点
    this.$inhouse?.track('custom', '[data-spm-page]', {
      spm: page > this.paginationInfo.page ? 'ReviewNextPage' : 'ReviewPreviousPage'
    })
    this.$emit('onChangePage', page)
  }

  formatPicUrl(url: string, width: number, height: number) {
    const enlarge = this.platform === 'desktop' ? 2 : 1
    return transformImageUrl(url, {
      width: width * enlarge,
      height: height * enlarge,
      webp: this.realWebp,
      quantity: 100
    })
  }
}
</script>

<style lang="scss" scoped>
.review-list {
  &__pagination {
    justify-content: center;
    margin: 8px 0;
  }

  &__loading-error {
    margin: 32px auto;
    text-align: center;
    .tips {
      @include font-body-s-bold-v2;
      margin-top: 20px;
      text-align: center;
      color: $color-text-primary;
    }
  }
}
</style>
