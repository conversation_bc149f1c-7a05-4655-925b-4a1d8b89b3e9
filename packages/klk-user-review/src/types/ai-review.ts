export interface AiReviewSummaryItem {
  id: number,
  type: string,
  title: string,
  content: string,
  helpful_status: number
}
export interface AiReviewMiniInfoData {
  tips: string,
  pop_info: AiReviewPopInfo,
}
interface AiReviewPopInfo {
  title: string,
  content: string
}
export interface AiReviewData {
  icon: string,
  title: string,
  mini_info: AiReviewMiniInfoData,
  hide_helpful_action?: Boolean,
  summary_list: AiReviewSummaryItem[],
  platform?: string,
  helpful_api?: string
}
