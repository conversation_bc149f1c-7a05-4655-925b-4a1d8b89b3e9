import { IReviewImage } from '@/types/review-image'

export type Theme = 'light' | 'dark'

export type Direction = 'left' | 'right'

export type Display = 'inline' | 'modal'

export type RenderMode = 'csr' | 'ssr'

export interface IUserInfo {
  user_name: string,
  user_avatar: string,
  user_rating: number,
  rating_desc: string,
  rating_time: string,
}

export interface IUserReview {
  review_content: string,
  translate_content: string,
  show_translation: boolean,
}

export interface IExtraData {
  template_id: string,
  aggregate_id: string,
  [key: string]: any
}

export interface IReviewItem {
  review_id: string,
  user_info: IUserInfo, // 原来是any
  review_content: string,
  translate_content: string,
  show_translation?: boolean,
  has_liked?: boolean,
  liked_count?: number,
  product_info?: {
    product_name: string,
    id: string,
    is_booking_now?: boolean,
  },
  review_image_list?: IReviewImage[], // 原来是any
  reply?: {
    reply_from: string,
    content: string,
    translate_content: string
  }
}
