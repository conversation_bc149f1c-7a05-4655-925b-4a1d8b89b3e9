import { IReviewRating } from './review-rating'
import { AiReviewData } from './ai-review'
import { IReviewItem } from './review'

export interface IFilterItem {
  filter_desc: string,
  filter_key: string,
  filter_value: any,
  default_selected: boolean,
  filter_desc_en: string,
  is_only_single_select: boolean // 是否单选
  value: string // 前端重新组合value为 filter_key|filter_value格式
}

export interface ISortItem {
  desc: string,
  key: string,
  sort_value: any,
  default_selected: boolean,
  desc_en: string
}

export interface IReviewData extends IReviewRating {
  ai_summary_info: AiReviewData,
  filter: {
    filter_list: IFilterItem[],
    sort_list: ISortItem[]
  },
  first_page_review_limit: number,
  reviews: {
    title: string,
    total: number,
    current_page: number,
    has_next: boolean,
    each_page_count: number,
    review_list: IReviewItem[]
  },
  track_info: {
    extra: {
      aggregate_id: string,
      aggregate_key: string
    }
  }
}

export interface ILoginOptions {
  onLogin: (next: () => void) => void
}
