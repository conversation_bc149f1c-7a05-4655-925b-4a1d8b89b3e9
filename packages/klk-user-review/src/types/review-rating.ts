export interface IRating {
  avg_rating: number,
  max_rating: number,
  rating_desc?: string,
  rating_icon: string,
  review_count_desc: string
  review_count: number
}
export interface IRatingInfo {
  rating_info: IRating
}

export interface ISubRating {
  sub_rating_desc: string,
  sub_rating: number
}

export interface IReviewRating extends IRatingInfo {
  sub_rating_list: ISubRating[],
  merchant_info?: {
    merchant_logo: string,
    merchant_name: string
  }
}