<template>
  <div v-if="ratingInfo && ratingInfo.rating_info" >
    <div :class="['klk-mini-review', `klk-mini-review__${direction}`, clickable && 'klk-mini-review__clickable']" @click="handleShowMore">
      <div :class="['review-rating', `review-rating__${theme}`]">
        <span class="review-rating__avg">{{ ratingInfo.rating_info.avg_rating }}</span>
        <span class="review-rating__dash">/</span>
        <span>{{ ratingInfo.rating_info.max_rating }}</span>
      </div>
      <div
        v-if="ratingInfo.rating_info.review_count_desc"
        :class="['review-count', clickable && 'review-count__clickable']"
        data-spm-module="MiniReview"
        data-spm-virtual-item="__virtual"
        v-galileo-click-tracker="{
          spm: 'MiniReview',
          componentName: 'klk-user-review'
        }"
      >
        <span>{{ ratingInfo.rating_info.review_count_desc }}</span>
      </div>
    </div>
    <component
      :is="detailComponent"
      name="mini-overview-detail"
      :platform="realPlatform"
      :login-option="loginOption"
      :visible.sync="detailVisible"
      :is-init-position="false"
      :extra-data="extraData"
      render-mode="csr"
      @close="closeDetail"
      @book="goBooking"
    >
      <template v-for="(_, name) in $scopedSlots" v-slot:[name]="data">
        <slot :name="name" v-bind="data" />
      </template>
    </component>
  </div>
</template>

<script lang="ts">
import { Component, Prop, Vue, Provide } from 'vue-property-decorator'
import { ldjson } from '../utils/utils'
import { IRatingInfo } from '@/types/review-rating'
import { ILoginOptions } from '@/types/review-detail'
import { IExtraData, Theme, Direction } from '@/types/review'

@Component({
  name: 'KlkMiniReview'
})
export default class MiniReview extends Vue {
  @Prop({ required: false }) platform?: string
  @Prop({ required: false }) language?: string
  @Prop({ type: String, default: () => 'dark' }) theme?: Theme
  @Prop({ type: String, default: () => 'left' }) direction?: Direction
  @Prop({ type: Boolean, default: () => false }) showDetail?: boolean
  @Prop({ type: Object, default: () => null }) ratingInfo?: IRatingInfo
  @Prop({ type: Boolean, default: () => false }) clickable?: boolean
  @Prop({ type: Boolean, default: () => false }) enableReviewSchema?: boolean
  @Prop({ type: Object, default: () => ({ aggregate_id: '', template_id: '' }) }) extraData!: IExtraData
  @Prop({ type: Object, default: () => null }) loginOption?: ILoginOptions

  detailVisible: boolean = false

  detailComponent: any = null

  head() {
    if (this.enableReviewSchema) {
      const schema: Object = {
        '@context': 'https://schema.org',
        aggregateRating: {
          '@type': 'AggregateRating',
          ratingValue: String(this.ratingInfo?.rating_info.avg_rating || ''),
          bestRating: String(this.ratingInfo?.rating_info.max_rating || ''),
          worstRating: '1',
          reviewCount: String(this.ratingInfo?.rating_info.review_count || '')
        }
      }
      return {
        script: [ldjson(schema)]
      }
    }
  }

  @Provide('realPlatform')
  get realPlatform() {
    return this.platform || this?.$store?.state?.klook?.platform || 'desktop'
  }

  @Provide('lang')
  get lang() {
    return this.language || this?.$store?.state?.klook?.language || 'en'
  }

  handleShowMore(e) {
    e?.stopPropagation()
    if (!this.showDetail) {
      this.$emit('showMore')
      return
    }
    // 显示详情
    this.detailVisible = true
    if (!this.detailComponent) {
      if (this.realPlatform === 'mobile') {
        import('../klk-review-detail/review-modal.vue').then((module) => {
          this.detailComponent = module.default || module
        })
      } else {
        import('../klk-review-detail/review-drawer.vue').then((module) => {
          this.detailComponent = module.default || module
        })
      }
    }
  }

  closeDetail() {
    this.detailVisible = false
  }

  goBooking(productInfo) {
    this.$emit('book', productInfo)
  }
}
</script>

<style lang="scss" scoped>
.klk-mini-review {
  display: flex;
  align-items: center;

  &__left {
    .review-count {
      margin-left: 6px;
    }
  }

  &__right {
    flex-direction: row-reverse;
    .review-count {
      margin-right: 6px;
    }
  }

  &__clickable {
    cursor: pointer;
  }

  .review-rating {
    @include font-caption-m-regular();
    border-radius: 8px 3px;
    display: flex;
    align-items: center;
    padding: 2px 4px;

    &__dash {
      transform: scaleY(.7);
    }

    &__avg {
      @include font-caption-1();
    }
  }

  .review-rating__dark {
    background-color: $color-accent-1;
    color: $color-text-reverse;
  }

  .review-rating__light {
    background-color: #F1EEFA;
    color: $color-accent-1;
  }

  .review-count {
    @include font-paragraph-xs-regular();
    color: $color-text-secondary;

    &__clickable {
      cursor: pointer;
      text-decoration: underline;
    }
  }
}
</style>
