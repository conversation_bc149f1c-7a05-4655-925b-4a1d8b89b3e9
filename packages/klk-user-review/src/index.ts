import KlkMiniReview from './klk-mini-review/index.vue'
import KlkAIReviewCard from './klk-ai-review/index.vue'
import KlkReviewRating from './klk-review-rating/index.vue'
import KlkReviewCard from './klk-review-card/index.vue'
import KlkReviewDetail from './klk-review-detail/index.vue'
import KlkReview from './klk-review/index.vue'

// @ts-ignore
KlkMiniReview.install = function (Vue) {
  Vue.component(KlkMiniReview.name, KlkMiniReview)
}
// @ts-ignore
KlkAIReviewCard.install = function (Vue) {
  Vue.component(KlkAIReviewCard.name, KlkAIReviewCard)
}
// @ts-ignore
KlkReviewRating.install = function (Vue) {
  Vue.component(KlkReviewRating.name, KlkReviewRating)
}
// @ts-ignore
KlkReviewCard.install = function (Vue) {
  Vue.component(KlkReviewCard.name, KlkReviewCard)
}
// @ts-ignore
KlkReviewDetail.install = function (Vue) {
  Vue.component(KlkReviewDetail.name, KlkReviewDetail)
}
// @ts-ignore
KlkReview.install = function (Vue) {
  Vue.component(KlkReview.name, KlkReview)
}

export * from './types/review'
export * from './types/review-rating'
export * from './types/ai-review'
export * from './types/review-detail'

export {
  KlkMiniReview,
  KlkAIReviewCard,
  KlkReviewRating,
  KlkReviewCard,
  KlkReviewDetail,
  KlkReview
}
