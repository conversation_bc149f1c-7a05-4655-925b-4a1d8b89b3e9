export const isClient = typeof window !== 'undefined'

/**
 * seo Setting script.ld+json of document
 */
export function ldjson(json: any) {
  return {
    type: 'application/ld+json',
    json
  }
}

export function throttle(func, wait, ops?) {
  let context
  let args
  let result
  let timeout: any = null
  let previous = 0
  const options = Object.assign({}, ops)

  const later = () => {
    previous = options.leading === false ? 0 : Date.now()
    timeout = null
    result = func.apply(context, args)
    if (!timeout) {
      context = null
      args = null
    }
  }

  return function fn(this: any, ...reArgs) {
    const now = Date.now()
    if (!previous && options.leading === false) { previous = now }
    const remaining = wait - (now - previous)
    context = this
    args = reArgs
    if (remaining <= 0 || remaining > wait) {
      if (timeout) {
        clearTimeout(timeout)
        timeout = null
      }
      previous = now
      result = func.apply(context, args)
      if (!timeout) {
        context = null
        args = null
      }
    } else if (!timeout) {
      timeout = setTimeout(later, remaining)
    }
    return result
  }
}