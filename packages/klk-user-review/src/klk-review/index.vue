<template>
  <div :class="['klk-review', realPlatform === 'mobile' ? 'klk-review--mobile' : 'klk-review--desktop']">
    <!--title-->
    <div v-if="title" class="klk-review__header">
      <h2 class="klk-review__title">{{ title }}</h2>
    </div>
    <!-- 当loading为true时显示骨架屏 -->
    <template v-if="loading">
      <ReviewOverviewLoading />
    </template>
    <template v-else>
      <!--rating-->
      <div class="klk-review__rating" v-if="ratingInfo">
        <KlkReviewRating
          :rating-info="ratingInfo"
          size="small"
        />
      </div>
      <!--divider-->
      <div v-if="ratingInfo && reviewData.ai_summary_info" class="klk-review__divider"><klk-divider /></div>
      <!--ai-summary-->
      <div v-if="reviewData.ai_summary_info" class="klk-review__aisummary">
        <KlkAiReview
          :ai-review-info="reviewData.ai_summary_info"
          :platform="realPlatform"
        />
      </div>
      <!--cards-->
      <div class="klk-review__review-card" v-if="reviewStaticList && reviewStaticList.length">
        <div v-if="reviewData.ai_summary_info || ratingInfo" class="klk-review__review-card-title">
          <!-- 仅有评论列表就不展示标题，仅有ai或者rating info就预留间距 -->
          <span v-if="reviewData.ai_summary_info && ratingInfo" class="klk-review__review-card-title-text">
            {{ (reviewData.reviews && reviewData.reviews.title) || $t('208984-reviews_from_user') }}
          </span>
        </div>
        <div class="klk-review__review-card-list">
          <card-swiper :controller-offset="10">
            <card-swiper-item
              v-for="(item, index) in reviewStaticList"
              :key="item.review_id+'-'+index"
              :class="cardColumnClass"
            >
              <div
                v-galileo-click-tracker="{
                  spm: 'ReviewReviewPreview_LIST',
                  componentName: 'klk-user-review'
                }"
                class="klk-review__review-card-item"
                :data-spm-module="`ReviewReviewPreview_LIST?idx=${index}&len=${reviewStaticList.length}&ext=${encodeURIComponent(JSON.stringify({
                  ReviewID: item.review_id
                }))}`"
                data-spm-virtual-item="__virtual"
                @click="jumpToReview(item.review_id)"
              >
                <KlkReviewCard
                  :class="{
                    'klk-review__review-card-item--multiple': !isSingleCard
                  }"
                  :review-info="item"
                  type="brief"
                />
              </div>
            </card-swiper-item>
          </card-swiper>
        </div>
        <div class="klk-review__review-card-button" v-if="!hideShowMore">
          <klk-button
            v-galileo-click-tracker="{
              spm: 'ReviewOverviewSeeAll',
              componentName: 'klk-user-review'
            }"
            :data-spm-module="`ReviewOverviewSeeAll?${trackInfo}&trg=manual`"
            data-spm-virtual-item="__virtual"
            type="outlined"
            block
            @click.native="handleMoreClicked"
          >
            {{ $t('208985') }}
          </klk-button>
        </div>
      </div>
      <component
        :is="detailComponent"
        name="overview-detail"
        :platform="realPlatform"
        :review-data="reviewData"
        :source-review-id="sourceReviewId"
        :login-option="loginOption"
        :visible.sync="detailVisible"
        :extra-data="extraData"
        :is-init-position="true"
        render-mode="csr"
        @close="closeDetail"
        @book="goBooking"
      >
        <template v-for="(_, name) in $scopedSlots" v-slot:[name]="data">
          <slot :name="name" v-bind="data" />
        </template>
      </component>
    </template>
  </div>
</template>
<script lang="ts">
import { Component, Prop, Provide, Vue } from 'vue-property-decorator'
import KlkButton from '@klook/klook-ui/lib/button'
import KlkDivider from '@klook/klook-ui/lib/divider'
import ReviewOverviewLoading from '../klk-user-review-common/components/loading/review-overview-loading.vue'
import KlkReviewRating from '../klk-review-rating/index.vue'
import KlkAiReview from '../klk-ai-review/index.vue'
import { ldjson } from '../utils/utils'
import KlkReviewCard from '../klk-review-card/index.vue'
import { CardSwiper, CardSwiperItem } from '../klk-user-review-common/components/klook-responsive/index'
import { IExtraData } from '@/types/review'
import { ILoginOptions, IReviewData } from '@/types/review-detail'

@Component({
  name: 'KlkReview',
  components: {
    KlkReviewRating,
    KlkAiReview,
    KlkReviewCard,
    CardSwiper,
    CardSwiperItem,
    ReviewOverviewLoading,
    KlkButton,
    KlkDivider
  }
})
export default class ReviewOverall extends Vue {
  @Prop({ required: false }) platform?: string
  @Prop({ required: false }) language?: string
  @Prop({ type: String, default: () => '' }) title?: string
  @Prop({ type: Boolean, default: () => false }) loading?: boolean
  @Prop({ type: Object, default: () => ({ aggregate_id: '', template_id: '' }) }) extraData!: IExtraData
  @Prop({ type: Object, default: () => ({}) }) reviewData!: IReviewData
  @Prop({ type: Boolean, default: () => false }) enableReviewSchema?: boolean
  @Prop({ type: Object, default: () => null }) loginOption?: ILoginOptions // 添加 loginOption 属性
  @Prop({ type: Boolean, default: () => true }) showDetail?: boolean
  @Prop({ type: Boolean, default: () => false }) hideShowMore?: boolean // 是否隐藏查看更多按钮

  sourceReviewId: string = ''

  head() {
    if (this.enableReviewSchema) {
      const schema: Object = {
        '@context': 'https://schema.org',
        aggregateRating: {
          '@type': 'AggregateRating',
          ratingValue: String(this.reviewData?.rating_info?.avg_rating || ''),
          bestRating: String(this.reviewData?.rating_info?.max_rating || ''),
          worstRating: '1',
          reviewCount: String(this.reviewData?.rating_info?.review_count || '')
        }
      }
      return {
        script: [ldjson(schema)]
      }
    }
  }

  get trackInfo() {
    return `ext=${encodeURIComponent(JSON.stringify({ ...this.reviewData?.track_info?.extra }))}`
  }

  @Provide('realPlatform')
  get realPlatform() {
    return this.platform || this?.$store?.state?.klook?.platform || 'desktop'
  }

  @Provide('lang')
  get lang() {
    return this.language || this?.$store?.state?.klook?.language || 'en'
  }

  detailVisible: boolean = false
  detailComponent: any = null

  get ratingInfo() {
    const { rating_info, sub_rating_list, merchant_info } = this.reviewData || {}
    if (!rating_info && !sub_rating_list && !merchant_info) {
      return null
    }
    return { rating_info, sub_rating_list, merchant_info }
  }

  get reviewStaticList() {
    const { reviews, first_page_review_limit } = this.reviewData || {}
    return reviews?.review_list.slice(0, first_page_review_limit) || []
  }

  get isSingleCard() {
    return this.reviewStaticList.length === 1
  }

  get cardColumnClass() {
    return this.isSingleCard
      ? 'klk-col-md-1 klk-col-lg-1 klk-col-xl-1 klk-col-sm-1'
      : 'klk-col-md-2 klk-col-lg-2 klk-col-xl-2 klk-col-sm-1-2'
  }

  handleMoreClicked(e) {
    e?.stopPropagation()
    this.sourceReviewId = ''
    this.handleShowMore()
  }

  jumpToReview(reviewId: string) {
    this.sourceReviewId = reviewId + ''
    this.handleShowMore()
  }

  handleShowMore() {
    if (!this.showDetail) {
      this.$emit('showMore', {
        reviewId: this.sourceReviewId
      })
      return
    }
    // 显示详情
    this.detailVisible = true
    // 如果组件尚未加载，则根据平台动态加载
    if (!this.detailComponent) {
      if (this.realPlatform === 'mobile') {
        import('../klk-review-detail/review-modal.vue').then((module) => {
          this.detailComponent = module.default || module
        })
      } else {
        import('../klk-review-detail/review-drawer.vue').then((module) => {
          this.detailComponent = module.default || module
        })
      }
    }
  }

  goBooking(productInfo) {
    this.$emit('book', productInfo)
  }

  closeDetail() {
    this.detailVisible = false
  }
}
</script>
<style lang="scss" scoped>
.klk-review {
  width: 100%;
  display: flex;
  justify-content: center;
  flex-direction: column;
}

.klk-review__title {
  position: relative;
  padding-left: 12px;
  margin-bottom: 8px;
  margin-top: 0;
  &::before {
    background-color: $color-brand-primary;
    border-radius: 3px;
    content: "";
    display: inline-block;
    height: 16px;
    left: 0;
    position: absolute;
    top: 9px;
    width: 4px;
  }
}
.klk-review__rating {
  height: auto;
}
.klk-review__divider {
  margin: 24px 0;
}
.klk-review__aisummary {
  margin: 0 0 12px;
}
.klk-review__review-card {
  align-items: center;
  align-self: stretch;
  &-item {
    height: 100%;
    cursor: pointer;
  }
  &-item--multiple {
    width: calc(100% - 4px);
  }
}
.klk-review__review-card-title {
  display: flex;
  align-items: center;
  padding-top: 20px;
  &-text {
    @include font-paragraph-m-bold;
    padding: 4px 0 12px;
    text-align: left;
    background-clip: text;
  }
}
.klk-review__review-card-list {
  display: flex;
  align-items: center;
  align-self: stretch;
}
.klk-review__review-card-button {
  padding: 16px 0 0 0;
  text-align: center;
}

.klk-review--mobile {
  width: 100%;
  display: flex;
  justify-content: center;
  flex-direction: column;

  .klk-review__header {
    padding-bottom: 16px;
  }
}

.klk-review--desktop {
  .klk-review__header {
    padding-bottom: 24px;
  }
}

.klk-review {
  ::v-deep {
    .responsive-card-swiper-wrap {
      padding: 0;
      margin: 0;
      width: 100%;
    }
    @media (max-width: 767px) {
      .responsive-card-swiper-wrap {
        padding: 0;
        margin: 0;
        width: 100%;
      }
    }
    @media (min-width: 768px) {
      .klk-review__review-card-list .responsive-card-swiper-wrap .klk-card-swiper-next-btn {
        right: -16px !important;
      }
      .klk-review__review-card-list .responsive-card-swiper-wrap .klk-card-swiper-prev-btn {
        left: -16px !important;
      }
      .responsive-card-swiper-wrap .klk-card-swiper-next-btn {
        width: 32px;
        height: 32px;
      }
      .responsive-card-swiper-wrap .klk-card-swiper-prev-btn {
        width: 32px;
        height: 32px;
      }
    }
  }
}
</style>
