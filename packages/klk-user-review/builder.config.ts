import { definedConfig } from '@klook/klook-builder/lib/builder/helper'
import pkg from './package.json'

export default definedConfig({
  entry: './src/index.ts',
  type: 'rollup',
  exports: 'named',

  // 如果你需要将打包后的文件放到其他地方，可以通过 outputPath 指定
  // outputPath: '/Users/<USER>/Desktop/dev/ssr-platform/node_modules/@klook/klk-user-review/dist',
  // outputPath: '/Users/<USER>/WebstormProjects/ssr-platform/node_modules/@klook/klk-user-review/dist',

  external(id) {
    const externalList = Object.keys(pkg.peerDependencies)
    return externalList.some((externalItem) => {
      const regStr = new RegExp(`(^${externalItem}$)|(^${externalItem}/.*)`)
      return regStr.test(id)
    })
  },

  rollupConfig: (config) => {
    config.output.forEach((conf) => {
      conf.interop = 'auto'
    })
  },

  // 通过对象形式可以覆盖公共配置
  format: {
    esm: {},
    commonjs: {}
    // umd: {
    //   name: 'TestAIReviewUmd'
    // }
  }
})
