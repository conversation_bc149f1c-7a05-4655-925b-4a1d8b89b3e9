import Vue from 'vue'

export declare class Klk<PERSON>iniReview extends Vue {
  theme: string
  direction: string
  showDetail: boolean
  ratingInfo: object
  clickable: boolean
  enableReviewSchema?: boolean
  loading?: boolean
  extraData?: object
  reviewData?: object
  platfrom?: string
}
export declare class KlkAIReviewCard extends Vue {
  aiReviewInfo: object
  platform?: string
  helpfulApi?: string
}
export declare class KlkReviewRating extends Vue {
  ratingInfo: object
  platfrom?: string
}
export declare class KlkReviewDetail extends Vue {
  title?: string
  renderMode: string
  display?: string
  enableReviewSchema?: boolean
  loading?: boolean
  extraData: object
  reviewData: object
  isInitPosition?: boolean
  sourceReviewId?: string
  platfrom?: string
}
export declare class KlkReviewCard extends Vue {
  reviewInfo: object
  previewMode: string
  type: string
}
export declare class KlkReview extends Vue {
  title?: string
  loading?: boolean
  extraData?: object
  reviewData: object
  enableReviewSchema?: boolean
  loginOption?: object
  platform?: string
  language?: string
  hideShowMore?: boolean
  showDetail?: boolean
}
