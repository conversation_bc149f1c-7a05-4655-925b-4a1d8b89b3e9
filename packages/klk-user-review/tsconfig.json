{"include": ["src/**/*.ts", "src/**/*.vue", "shims-index.d.ts"], "exclude": ["node_modules", "unpackage"], "compilerOptions": {"module": "esnext", "target": "esnext", "lib": ["dom", "esnext", "esnext.asynciterable"], "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "importHelpers": true, "declaration": true, "sourceMap": true, "rootDir": "src", "strict": true, "noUnusedLocals": true, "noUnusedParameters": true, "noImplicitReturns": false, "noFallthroughCasesInSwitch": true, "moduleResolution": "node", "baseUrl": "./", "types": ["./shims-index"], "paths": {"@/*": ["./src/*"], "~": ["/"]}, "jsx": "preserve", "esModuleInterop": true, "allowJs": true, "noImplicitAny": false, "resolveJsonModule": true}}