{"name": "@klook/klk-user-review", "version": "0.0.2", "description": "A Common Component based on Vue of Klook", "author": "<EMAIL>", "homepage": "https://design.klook.io", "main": "dist/commonjs/index.js", "module": "dist/esm/index.js", "typings": "types/index.d.ts", "files": ["dist", "lib", "types"], "license": "UNLICENSED", "publishConfig": {"registry": "https://knpm.klook.io", "access": "public"}, "scripts": {"build": "klk-builder build", "watch": "klk-builder build -w", "lint": "NODE_ENV=production eslint --ext .js,.vue src", "test": "NODE_ENV=test jest -i --updateSnapshot", "test:coverage": "NODE_ENV=test jest -i --coverage --updateSnapshot", "prepush": "yarn run lint", "prepublishOnly": "bash prepublishOnly.sh", "commit": "npx git-cz", "commitmsg": "commitlint -E GIT_PARAMS"}, "devDependencies": {"@babel/core": "^7.17.9", "@klook/klook-builder": "1.1.1", "@klook/klook-ui": "^1.38.14", "vue-property-decorator": "^8.3.0", "@klook/site-config": "^1.10.0", "@klook/klk-traveller-utils": "^1.8.17", "@klook/klook-icons": "^0.18.0", "swiper": "^4.0.7", "vue-awesome-swiper": "^3.1.3", "@klook/galileo-vue": "^1.2.11", "vue-lazyload": "^1.3.3"}, "peerDependencies": {"@klook/klook-icons": "^0.18.0", "@klook/klook-ui": "^1.38.14", "vue": "2.x", "vue-property-decorator": "^8.3.0", "@klook/site-config": "^1.10.0", "@klook/klk-traveller-utils": "^1.8.17", "swiper": "^4.0.7", "vue-awesome-swiper": "^3.1.3", "@klook/galileo-vue": "^1.2.11", "vue-lazyload": "^1.3.3"}}