{"root": true, "extends": ["@nuxtjs/eslint-config-typescript"], "rules": {"camelcase": "off", "vue/no-v-html": "off", "nuxt/no-cjs-in-config": "off", "vue/html-self-closing": "off", "vue/space-infix-ops": "error", "vue/mustache-interpolation-spacing": "error", "vue/singleline-html-element-content-newline": "off", "@typescript-eslint/type-annotation-spacing": "error", "import/newline-after-import": "error", "space-before-function-paren": ["error", {"anonymous": "always", "named": "never", "asyncArrow": "always"}]}}