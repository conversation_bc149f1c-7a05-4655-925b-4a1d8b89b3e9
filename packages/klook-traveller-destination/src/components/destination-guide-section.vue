<template>
  <div v-if="data" class="destination-guide">
    <DestinationTitle
      :title="data.klook_area_name"
      :subTitle="getI18NConfig(13632)"
      :subTitleUrl="data.klook_area_url"
      :inhouse-data="{'country': data.country_id}"
    />

    <div class="guide-card-group">
      <a :href="item.klook_area_url" v-for="(item, index) in destinationGuideCard" class="guide-card" :key="item.klook_id">
        <KlkAtomicAspectRatio
          :img-ratio="169 / 113"
          :img-src="item.image_url"
          :crop-width="400"
          :crop-height="300"
          :data-spm-module="`DestinationList::::Card_LIST?oid=city_${item.city_id}&ext=${encodeURIComponent(JSON.stringify({ CardType: 'Bigcard' }))}&idx=${index}&len=${data.leaf_items.length}`"
          data-spm-virtual-item="__virtual"
          v-galileo-click-tracker="{ spm: 'DestinationList::::Card_LIST', componentName: 'klook-traveller-destination' }"
        >
          <span v-if="item.klook_area_name" class="guide-card-tag">
            {{ item.klook_area_name }}
          </span>
        </KlkAtomicAspectRatio>
      </a>
    </div>

    <div class="guide-btn-group-content" :class="{'max-height-135': showMore}">
      <div ref="guideBtnGroup" class="guide-btn-group">
        <a
          v-for="(item, index) in destinationGuideBtn"
          class="guide-btn"
          :href="item.klook_area_url"
          :key="item.klook_id"
          :data-spm-module="`DestinationList::::Card_LIST?oid=city_${item.city_id}&ext=${encodeURIComponent(JSON.stringify({ CardType: 'Small Card' }))}&idx=${index + 3}&len=${data.leaf_items.length}`"
          data-spm-virtual-item="__virtual"
          v-galileo-click-tracker="{ spm: 'DestinationList::::Card_LIST', componentName: 'klook-traveller-destination' }"
        >
          {{ item.klook_area_name }}
        </a>
      </div>
      <div
        v-if="showMore"
        class="show-more"
        @click="showMore = false"
        :data-spm-module="`DestinationList::::ViewAll?oid=country_${data.country_id}`"
        data-spm-virtual-item="__virtual"
        v-galileo-click-tracker="{ spm: 'DestinationList::::ViewAll', componentName: 'klook-traveller-destination' }"
      >
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16">
          <path fill-opacity=".87" d="M12.42 5.02c.292-.293.767-.293 1.06 0 .293.293.293.767 0 1.06l-4.95 4.95c-.293.293-.768.293-1.06 0L2.52 6.08c-.293-.293-.293-.767 0-1.06.293-.293.767-.293 1.06 0L8 9.44l4.42-4.42z"/>
        </svg>
      </div>
    </div>
  </div>
</template>

<script>
import DestinationTitle from "./destination-title.vue";
import { KlkAtomicAspectRatio } from "@klook/klook-card";

import "@klook/klook-card/dist/esm/index.css";

export default {
  props: {
    data: { type: Object, default: () => null },
    i18nConfig: { type: Object, default: () => null }
  },
  components: {
    DestinationTitle,
    KlkAtomicAspectRatio,
  },
  data() {
    return {
      showMore: true
    };
  },
  computed: {
    destinationGuideCard() {
      if(this.data && this.data.leaf_items) {
        return this.data.leaf_items.slice(0, 3)
      }
      return []
    },

    destinationGuideBtn() {
      if(this.data && this.data.leaf_items) {
        return this.data.leaf_items.slice(3, this.data.leaf_items.length)
      }
      return []
    }
  },
  methods: {
    getI18NConfig(key) {
      return this.i18nConfig[key] || ''
    },
  },

  mounted() {
    this.showMore = this.$refs.guideBtnGroup.offsetHeight > 137
  }
};
</script>

<style lang="scss" scoped>
.destination-guide {
  .guide-card-group {
    display: flex;

    .guide-card {
      width: calc((100% - 24px) / 3);
      border-radius: $radius-xl;
      overflow: hidden;
      margin-right: 12px;

      &:last-of-type {
        margin-right: 0;
      }

      .guide-card-tag {
        @include font-body-s-bold();

        position: absolute;
        bottom: 0;
        left: 0;
        display: flex;
        flex-direction: column-reverse;
        width: 100%;
        height: 100%;
        color: $color-text-reverse;
        background: linear-gradient(180deg, rgba(0, 0, 0, 0) 25.21%, rgba(0, 0, 0, 0.48) 100%);
        padding: 0 8px 8px 8px;
      }
    }
  }
  .guide-btn-group-content {
    position: relative;

    .guide-btn-group {
      display: flex;
      flex-wrap: wrap;

      .guide-btn {
        @include font-body-s-regular();

        color: $color-text-primary;
        margin: 10px 8px 0 0;
        padding: 6px 12px;
        border: 1px solid $color-border-normal;
        border-radius: $radius-m;
      }
    }
    .show-more {
      position: absolute;
      right: 0;
      bottom: 0;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      width: 50px;
      height: 35px;
      background: linear-gradient(to right, rgb(255, 255, 255, 0), rgba(255, 255, 255) 40%);
    }
  }
  .max-height-135 {
    max-height: 135px;
    overflow: hidden;
  }
}
</style>
