<template>
  <div class="destination-title">
    <div class="title">{{ title }}</div>
    <a
      v-if="subTitle && subTitleUrl"
      class="sub-title"
      :href="subTitleUrl"
      :data-spm-module="`DestinationList::::ExploreButton?oid=country_${inhouseData.country}`"
      data-spm-virtual-item="__virtual"
      v-galileo-click-tracker="{ spm: 'DestinationList::::ExploreButton', componentName: 'klook-traveller-destination' }"
    >
      {{ subTitle }}
    </a>
    <div
      v-else-if="subTitle"
      class="sub-title"
      :data-spm-module="`PopularDestination::::SeeMoreButton?ext=${encodeURIComponent(JSON.stringify(inhouseData))}`"
      data-spm-virtual-item="__virtual"
      v-galileo-click-tracker="{ spm: 'PopularDestination::::SeeMoreButton', componentName: 'klook-traveller-destination' }"
      @click="$emit('handleClick')">
      {{ subTitle }}
    </div>
  </div>
</template>

<script>
export default {
  props: {
    title: { type: String, default: () => "" },
    subTitle: { type: String, default: () => "" },
    subTitleUrl: { type: String, default: () => "" },
    inhouseData: { type: Object, default: () => null }
  },
  components: {},
  data() {
    return {};
  },
};
</script>

<style lang="scss" scoped>
.destination-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: $color-text-primary;
  padding: 32px 0 16px 0;

  .title {
    @include font-heading-xs();
  }

  .sub-title {
    @include font-body-s-regular();

    text-decoration: underline;
    color: $color-text-primary;
  }
}
</style>
