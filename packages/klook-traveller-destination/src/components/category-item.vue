<template>
  <div v-if="categoryItem" class="category-item">
    <span
      class="category-item-a"
      @click="gotoPage(categoryItem.deep_link)"
      :key="categoryItem.id"
      :data-spm-module="`Category_LIST?ext=${encodeURIComponent(JSON.stringify({ BusinessName: `${categoryItem.id}_${categoryItem.content}` }))}&idx=${inhouse.index}&len=${inhouse.dataLength}`"
      data-spm-virtual-item="__virtual"
      v-galileo-click-tracker="{ spm: 'Category_LIST', componentName: 'klook-traveller-destination' }"
    >
      <div
        class="icon-image"
        v-lazy:background-image.container="formatPicUrl(categoryItem.icons[0])"
      ></div>
      {{ categoryItem.content }}
      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16">
        <path fill-rule="evenodd" d="M8,9.29289322 L11.6464466,5.64644661 C11.8417088,5.45118446 12.1582912,5.45118446 12.3535534,5.64644661 C12.5488155,5.84170876 12.5488155,6.15829124 12.3535534,6.35355339 L8.35355339,10.3535534 C8.25592232,10.4511845 8.12796116,10.5 8,10.5 C7.87203884,10.5 7.74407768,10.4511845 7.64644661,10.3535534 L3.64644661,6.35355339 C3.45118446,6.15829124 3.45118446,5.84170876 3.64644661,5.64644661 C3.84170876,5.45118446 4.15829124,5.45118446 4.35355339,5.64644661 L8,9.29289322 Z" transform="rotate(-90 8 8)"/>
      </svg>
    </span>
    <div class="category-child-group" v-if="categoryItem.children && categoryItem.children.length">
      <span
        class="category-child-btn"
        @click="gotoPage(item.deep_link)"
        v-for="(item, index) in categoryItem.children"
        :key="item.id"
        :data-spm-module="`Category_LIST?ext=${encodeURIComponent(JSON.stringify({ BusinessName: `${item.id}_${item.content}` }))}&idx=${index}&len=${categoryItem.children.length}`"
        data-spm-virtual-item="__virtual"
        v-galileo-click-tracker="{ spm: 'Category_LIST', componentName: 'klook-traveller-destination' }"
      >
        {{ item.content }}
    </span>
    </div>
  </div>
</template>

<script>
  import transformImageUrl from '@klook/klk-traveller-utils/lib/transformImageUrl'

  export default {
    props: {
      categoryItem: { type: Object, default: () => null },
      inhouse: {type: Object, default: () => null },
      webp: {type: Number, default: () => undefined }
    },
    components: {},
    data() {
      return {};
    },
    computed: {
      realWebp() {
        if (this.webp !== undefined) {
          return this.webp
        }
        return this.$store?.state?.klook?.webp || 0
      }
    },
    methods: {
      formatPicUrl(url) {
        return transformImageUrl(url, { width: 48, height: 48, webp: this.realWebp })
      },
      gotoPage(url) {
        if (!url) {
          return
        }
        window.location.href = url
      }
    }
  };
</script>

<style lang="scss" scoped>
.category-item {
  padding: 13px 20px;
  border-bottom: 1px solid rgb(238, 238, 238);

  .category-item-a {
    @include font-body-m-regular();

    display: flex;
    align-items: center;
    color: $color-text-primary;
    cursor: pointer;
    .icon-image {
      width: 24px;
      height: 24px;
      background-repeat: no-repeat;
      background-size: 100% 100%;
      background-position: center;
      margin-right: 16px;
    }

    svg {
      margin-left: 8px;
      flex-shrink: 0;
    }
  }

  .category-child-group {
    display: flex;
    flex-wrap: wrap;

    .category-child-btn {
      @include font-body-s-regular();

      color: $color-text-primary;
      padding: 2px 8px;
      border-radius: $radius-m;
      border: 1px solid $color-text-primary;
      margin: 8px 8px 0 0;
      cursor: pointer;
    }
  }
}
</style>
