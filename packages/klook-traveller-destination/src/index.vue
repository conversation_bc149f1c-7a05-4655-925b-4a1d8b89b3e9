<template>
<div class="destination-wrapper">
  <klk-tabs
    v-if="destinationData && allCategoryList.length"
    class="tab-content"
    underline
    v-model="activeTab"
    @change="onChange"
  >
    <klk-tab-pane
      name="destinations"
      width="50%"
    >
      <template slot="label">
          <span
            :data-spm-module="`TopTab_LIST?ext=${encodeURIComponent(JSON.stringify({ TabName: 'Destination' }))}`"
            data-spm-virtual-item="__virtual"
            v-galileo-click-tracker="{ spm: 'TopTab_LIST', componentName: 'klook-traveller-destination' }"
          >
            {{ getI18NConfig(13472) }}
          </span>
      </template>
    </klk-tab-pane>
    <klk-tab-pane
      name="categories"
      width="50%"
    >
      <template slot="label">
          <span
            :data-spm-module="`TopTab_LIST?ext=${encodeURIComponent(JSON.stringify({ TabName: 'All categories' }))}`"
            data-spm-virtual-item="__virtual"
            v-galileo-click-tracker="{ spm: 'TopTab_LIST', componentName: 'klook-traveller-destination' }"
          >
            {{ getI18NConfig(13892) }}
          </span>
      </template>
    </klk-tab-pane>
  </klk-tabs>
  <div v-if="destinationData" class="destination-content" v-show="activeTab === 'destinations'">
    <div
      class="destination-search-content"
      data-spm-module="TopSearchBar"
      data-spm-virtual-item="__virtual"
      v-galileo-click-tracker="{ spm: 'TopSearchBar', componentName: 'klook-traveller-destination' }"
    >
      <klk-input
        v-model="searchInputVal"
        class="destination-search"
        size="small"
        :placeholder="destinationData.basic_info.search_place_holder"
        style-type="filled"
        clearable
        @input="searchInputChange"
      >
        <svg
          slot="prepend"
          class="search-icon"
          xmlns="http://www.w3.org/2000/svg"
          width="20"
          height="20"
          fill="currentColor"
          viewBox="0 0 20 20"
        >
          <path
            fill-rule="evenodd"
            d="M1.667 8.75a7.083 7.083 0 1112.458 4.614.7.7 0 01.196.134l3.084 3a.7.7 0 11-.977 1.004l-3.083-3a.698.698 0 01-.157-.23A7.083 7.083 0 011.667 8.75zm7.083 5.683a5.683 5.683 0 110-11.366 5.683 5.683 0 010 11.366z"
            clip-rule="evenodd"
          />
        </svg>
      </klk-input>
    </div>
    <div class="destination-content-main">
      <!--search-result层级-->
      <div v-show="searchInputVal" class="search-suggest-floor padding-20">
        <!--搜索结果数据展示-->
        <div v-show="searchSuggestList.length" class="suggest-content">
          <div class="suggest-title">{{ getI18NConfig(15821) }}</div>
          <a
            class="suggest-item"
            v-for="(item, index) in searchSuggestList"
            :href="item.deep_link"
            :key="item.id"
            :data-spm-module="`Destination_LIST?oid=city_${item.id}&idx=${index}&len=${searchSuggestList.length}`"
            data-spm-virtual-item="__virtual"
            v-galileo-click-tracker="{ spm: 'Destination_LIST', componentName: 'klook-traveller-destination' }"
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 16 16">
              <path fill="currentColor" fill-rule="evenodd" d="M8 14c-3.333-2.826-5-5.16-5-7a5 5 0 1 1 10 0c0 1.84-1.667 4.174-5 7zm4-7a4 4 0 1 0-8 0c0 1.354 1.31 3.286 4 5.676 2.69-2.39 4-4.322 4-5.676zm-6-.25c0-1.103.897-2 2-2s2 .897 2 2-.897 2-2 2-2-.897-2-2zm1 0c0 .55.45 1 1 1s1-.45 1-1-.45-1-1-1-1 .45-1 1z"/>
            </svg>
            <div class="item-content">
              <div class="suggest-item-title">{{ item.city_name ? item.city_name : item.country_name }}</div>
              <div v-if="item.city_name && item.country_name" class="suggest-item-subtitle">{{ item.country_name }}</div>
            </div>
          </a>
        </div>
        <!--搜索状态展示-->
        <div
          v-show="!searchSuggestList.length"
          class="search-state"
          v-loading="searchLoading"
          data-klk-loading-show-loading-bg="false"
          data-klk-loading-show-overlay="false"
        >
          <!--搜索无数据-->
          <div class="suggest-title" v-show="isSearchResult">{{ getI18NConfig(15951) }}</div>
        </div>
      </div>
      <div v-show="!searchInputVal" class="destination-floor">
        <!--popular-destination层级-->
        <div v-if="popularDestination && popularDestination.length" class="popular-destination-floor padding-20">
          <DestinationTitle
            :title="getI18NConfig(5061)"
            :subTitle="popularSubTitle"
            :inhouse-data="{'ClickType': popularClickType}"
            @handleClick="popularMoreClick"
          />
          <div class="popular-destination-content">
            <a :href="item.klook_area_url" class="popular-card" v-for="(item, index) in popularDestination" :key="item.city_id">
              <KlkAtomicAspectRatio
                :img-ratio="169/113"
                :img-src="item.image_url"
                :crop-width="400"
                :crop-height="300"
                :data-spm-module="`PopularDestination::::Card_LIST?oid=city_${item.city_id}&idx=${index}&len=${popularDestination.length}`"
                data-spm-virtual-item="__virtual"
                v-galileo-click-tracker="{ spm: 'PopularDestination::::Card_LIST', componentName: 'klook-traveller-destination' }"
              >
                  <span
                    v-if="item.klook_area_name"
                    class="popular-card-tag"
                  >
                    {{ item.klook_area_name }}
                  </span>
              </KlkAtomicAspectRatio>
            </a>
          </div>
        </div>
        <!--吸顶滚动条-->
        <ScrollNav
          class="destination-scroll-nav"
          :nav-items="navItems"
          :offset="47"
        />
        <!--destination-guide层级-->
        <div class="destination-guide-floor padding-20">
          <div :id="`s-${navId}${index}`" class="destination-guide-content" v-for="(item, index) in destinationData.destination_guide" :key="index">
            <DestinationGuideSection :i18n-config="i18nConfig" v-for="subItem in item.sub_items" :key="subItem.klook_id" :data="subItem" />
          </div>
        </div>
      </div>
    </div>
  </div>
  <div v-if="allCategoryList.length" class="category-content" v-show="activeTab === 'categories'">
    <CategoryItem v-for="(item, index) in categoriesList" :key="item.id" :category-item="item" :inhouse="{index: index, dataLength: categoriesList.length}" />
  </div>

</div>
</template>

<script>
import { Tabs, TabPane } from "@klook/klook-ui/lib/tabs";
import Input from "@klook/klook-ui/lib/input";
import Loading from '@klook/klook-ui/lib/loading'
import { KlkAtomicAspectRatio } from "@klook/klook-card";
import ScrollNav from "@klook/scroll-nav"

import "@klook/klook-card/dist/esm/index.css";
import "@klook/scroll-nav/dist/esm/index.css"

import DestinationTitle from "./components/destination-title.vue";
import DestinationGuideSection from "./components/destination-guide-section.vue"
import CategoryItem from "./components/category-item.vue"

// import mockData from "./mock.js";
import debounce from "./utils/debounce.js"

export default {
  props: {
    i18nConfig: { type: Object, default: () => null }, // 见mock文件注释
    destinationData: { type: Object, default: () => null },
    allCategoryList: { type: Array, default: () => [] },
    tabActive: { type:String, default: 'destinations'},
    navId: { type:String, default: ''}
  },
  components: {
    klkTabs: Tabs,
    klkTabPane: TabPane,
    klkInput: Input,
    DestinationTitle,
    KlkAtomicAspectRatio,
    DestinationGuideSection,
    CategoryItem,
    ScrollNav,
    Loading
  },
  data() {
    return {
      activeTab: "destinations",
      popularDestinationShowMore: false,
      searchInputVal: '',
      searchSuggestList: [],
      popularClickType: 'Fold',
      searchLoading: false,
      isSearchResult: false
    };
  },
  computed: {
    navItems() {
      return (this.destinationData.destination_guide || []).map((item, index) => ({
        id: `#s-${this.navId}${index}`,
        text: item.title,
        name: item.title
      }))
    },

    categoriesList() {
      let list = []
      this.allCategoryList.forEach((item) => {
        list = [...list, ...item.children]
      })
      return list
    },

    popularDestination() {
      if(this.popularDestinationShowMore) {
        return this.destinationData.popular_destinations || []
      }
      return this.destinationData.popular_destinations && this.destinationData.popular_destinations.slice(0, 4)
    },

    popularSubTitle() {
      return this.popularDestinationShowMore ? this.getI18NConfig(14089) : this.getI18NConfig(13126)
    }
  },

  methods: {
    onChange(type) {
      this.$emit('tabChange', type)
    },

    getI18NConfig(key) {
      return this.i18nConfig[key] || key
    },

    popularMoreClick() {
      this.popularDestinationShowMore = !this.popularDestinationShowMore
      this.popularClickType = this.popularDestinationShowMore ? 'Open' : 'Fold'
    },

    searchInputChange() {
      this.isSearchResult = false
      this.searchSuggestList = []
      this.searchInputDebounce()
    },

    searchInputDebounce: debounce(function() {
      this.searchInputVal && this.getSearchSuggest(this.searchInputVal)
    },300),

    async getSearchSuggest(query) {
      this.searchLoading = true
      const { success, result } = await this._axios.$get('/v2/usrcsrv/search/destination', {
        params: {
          query
        }
      })
      if (success && result) {
        this.searchLoading = false
        this.isSearchResult = true
        this.searchSuggestList = result.destination_list || []
      } else {
        this.searchLoading = false
      }
    }
  },

  beforeMount() {
    this._axios = this.$attrs.axios || window.$axios
    this.activeTab = this.tabActive
  }
};
</script>

<style lang="scss" scoped>
.destination-wrapper {
  height: 100%;
  display: flex;
  flex-direction: column;

  .padding-20 {
    padding: 0 20px;
  }

  .tab-content {
    flex-shrink: 0;
  }
  .destination-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    .destination-search-content {
      flex-shrink: 0;

      .destination-search {
        padding: 8px 20px;
        border-bottom: 1px solid $color-bg-3;
        background: $color-bg-1;

        ::v-deep .klk-input-inner {
          border-radius: $radius-pill;
        }

        .search-icon {
          margin-left: 12px;
          color: $color-text-placeholder;
        }
      }
    }
    .destination-content-main {
      flex: 1;
      overflow-y: scroll;
      position: relative;
    }
    .search-suggest-floor {
      height: 100%;
      overflow-y: scroll;

      .suggest-title {
        @include font-body-s-regular();

        color: $color-text-secondary;
        padding: 28px 0 12px 0;
      }
      .suggest-content {
        display: flex;
        flex-direction: column;

        .suggest-item {
          display: flex;
          align-items: center;
          padding: 16px 0;

          svg {
            color: $color-text-secondary;
            margin-right: 16px;
            flex-shrink: 0;
          }

          .item-content {
            .suggest-item-title {
              @include font-body-m-regular();

              color: $color-text-primary;
            }
            .suggest-item-subtitle {
              @include font-body-s-regular();

              color: $color-text-secondary;
            }
          }
        }
      }
      .search-state {
        position: relative;
        height: 100%;
      }
    }
    .destination-floor {
      position: relative;
      height: 100%;
      overflow-y: scroll;
    }
    .popular-destination-floor {
      padding-bottom: 12px;
    }
    .popular-destination-content {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
    }
    .popular-card {
      width: calc((100% - 12px) / 2);
      border-radius: $radius-xl;
      overflow: hidden;

      &:not(:nth-last-child(-n+2)) {
        margin-bottom: 12px;
      }
    }
    .popular-card-tag {
      @include font-body-m-bold();

      position: absolute;
      bottom: 0;
      left: 0;
      display: flex;
      flex-direction: column-reverse;
      width: 100%;
      height: 100%;
      color: $color-text-reverse;
      background: linear-gradient(180deg, rgba(0, 0, 0, 0) 40.01%, rgba(0, 0, 0, 0.48) 100%);
      padding: 0 12px 12px 12px ;
    }
    .destination-scroll-nav {
      position: sticky;
      top: -1px;
      box-shadow: none;
      margin: 0 -20px;
    }
    .destination-guide-floor {
      padding-bottom: 16px;
    }
  }
  .category-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow-y: scroll;
  }

  ::v-deep .klk-tabs-body {
    margin-top: 0;
  }

  ::v-deep .klk-tab-pane {
    width: 100%;
  }
}
</style>
