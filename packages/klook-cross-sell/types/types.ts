export interface TransferItemData {
  title?: string
  sub_title?: string
  icon_url?: string
  deep_link?: string
  cover_url?: string
  hotel_star?: string
  desc?: string
  data?: any
  neary_by: {
    title?: string
    deep_link?: string
    track_info: any
  },
  track_info: any
  type?: 'entrance' | 'card'
}

export interface TextData {
  end: string
}
export interface TransferData {
  rich_text: {
    content?: string
    highlight_text?: string
    deep_link?: string
    track_info: any
  }
  transfer_list: TransferItemData[]
}

export interface CrossSellData {
  title?: string
  total_count?: number
  items?: any
  entrance?: TransferData
  page_url: string
}

export interface SpmOptions {
  idx?: number | string
  len?: number | string
  oid?: string
  ext?: Record<string, any>
  trg?: 'manual'
  [key: string]: any
}

// 瀑布流
export interface CardPosition {
  right?: number
  left?: number
  top?: string
  type?: string
}
