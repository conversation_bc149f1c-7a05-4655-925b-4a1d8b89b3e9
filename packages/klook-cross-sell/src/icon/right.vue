<template>
  <svg
    width="8"
    height="14"
    viewBox="0 0 8 14"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M0.505806 0.765893C0.779173 0.492526 1.22239 0.492526 1.49576 0.765893L7.28231 6.55245C7.55568 6.82581 7.55568 7.26903 7.28231 7.5424L1.49576 13.329C1.22239 13.6023 0.779173 13.6023 0.505806 13.329C0.23244 13.0556 0.23244 12.6124 0.505806 12.339L5.79739 7.04742L0.505806 1.75584C0.23244 1.48248 0.23244 1.03926 0.505806 0.765893Z"
      :fill="color"
    />
  </svg>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator'

@Component({
  components: {}
})
export default class RightIcon extends Vue {
  @Prop({ default: '#757575' }) color!: string
}
</script>
