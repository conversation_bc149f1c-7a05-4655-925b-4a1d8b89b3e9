<template>
  <a
    :href="cardData.deep_link"
    class="cross-sell-item"
  >
    <div class="icon">
      <img :src="cardData.icon_url" />
    </div>
    <span>{{ cardData.title }}</span>
  </a>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator'
import { TransferItemData } from '../types/types'
import RightIcon from './icon/right.vue'

@Component({
  components: {
    RightIcon
  }
})
export default class BusinessCardSmall extends Vue {
  @Prop() cardData!: TransferItemData;
}
</script>

<style lang="scss" scoped>
@mixin text-ellipsis($line-clamp: 2) {
  display: -webkit-box;
  -webkit-line-clamp: $line-clamp;
  -webkit-box-orient: vertical;
  text-overflow: ellipsis;
  overflow: hidden;
}

.cross-sell-item {
  display: flex;
  background-color: $color-bg-1;
  border-radius: $radius-m;
  margin-bottom: 8px;
  padding: 8px;
  align-items: center;

  .icon {
    flex: 0 0 24px;
    height: 24px;
    margin-right: 8px;
    justify-content: center;
    display: flex;
    align-items: center;


    img {
      display: block;
      max-width: 100%;
      max-height: 100%;
    }
  }

  span {
    @include font-body-s-regular;
    @include text-ellipsis(2);

    color: $color-text-primary;
  }
}
</style>
