<template>
  <a
    v-if="!isCard"
    :href="cardData.deep_link"
    class="cross-sell-card card"
    target="_blank"
  >
    <div class="icon">
      <img :src="cardData.icon_url" />
    </div>
    <div class="desc">
      <div class="title">
        <span>{{ cardData.title }}</span> <RightIcon />
      </div>
      <div class="sub-title">{{ cardData.sub_title }}</div>
    </div>
  </a>

  <div v-else class="cross-sell-card">
    <a
      v-if="hotelCardData.neary_by"
      :data-spm-module="`RecommendHotelCard_More?oid=${
        nearyByTrackInfo.object_id
      }&ext=${JSON.stringify(nearyByTrackInfo.extra)}`"
      data-spm-virtual-item="__virtual"
      v-galileo-click-tracker="{ spm: 'RecommendHotelCard_More', componentName: 'klook-cross-sell' }"
      :href="
        (hotelCardData.neary_by && hotelCardData.neary_by.deep_link) ||
        'javascript:;'
      "
      class="title"
      :class="{
        'not-deep-link': !(
          hotelCardData.neary_by && hotelCardData.neary_by.deep_link
        ),
      }"
      target="_blank"
    >
      <span>{{ hotelCardData.neary_by && hotelCardData.neary_by.title }}</span>
      <RightIcon
        v-if="hotelCardData.neary_by && hotelCardData.neary_by.deep_link"
      />
    </a>
    <a
      :data-spm-module="`RecommendHotelCard_Card?oid=${
        hotelCardTrackInfo.object_id
      }&ext=${JSON.stringify(hotelCardTrackInfo.extra)}`"
      data-spm-virtual-item="__virtual"
      v-galileo-click-tracker="{ spm: 'RecommendHotelCard_Card', componentName: 'klook-cross-sell' }"
      :href="hotelCardData.deep_link"
      class="card-info"
      target="_blank"
    >
      <div
        class="card-img"
        :style="{ backgroundImage: `url(${hotelCardData.cover_url})` }"
      />
      <div class="info">
        <div class="info-title">{{ hotelCardData.title }}</div>
        <span class="info-tip">{{ hotelCardData.hotel_star }}</span>
        <div class="info-sub-title">{{ hotelCardData.desc }}</div>
      </div>
    </a>
  </div>
</template>
<script lang="ts">
import { Component, Vue, Prop } from "vue-property-decorator";
import { TransferItemData } from "../types/types";
import RightIcon from "./icon/right.vue";

@Component({
  components: {
    RightIcon,
  },
})
export default class BusinessCardBig extends Vue {
  @Prop({ default: false }) isCard!: boolean;
  @Prop() cardData!: TransferItemData;

  get hotelCardTrackInfo() {
    return this.cardData.track_info?.Card || {};
  }

  get hotelCardData() {
    return this.cardData.data || {};
  }

  get nearyByTrackInfo() {
    return this.cardData.track_info?.More || {};
  }
}
</script>

<style lang="scss" scoped>
@mixin text-ellipsis($line-clamp: 2) {
  display: -webkit-box;
  -webkit-line-clamp: $line-clamp;
  -webkit-box-orient: vertical;
  text-overflow: ellipsis;
  overflow: hidden;
}

.cross-sell-card {
  display: flex;
  flex: 1;
  padding: 12px 16px;
  background: $color-bg-1;
  border-radius: $radius-xl;
  width: 100%;
  // max-width: 50%;
  &:not(.card) {
    flex-direction: column;
  }
  .desc{
    width: 100%;
  }

  .title {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 4px;

    &.not-deep-link {
      cursor: auto;
    }

    span {
      @include font-body-m-bold;
      @include text-ellipsis(1);

      color: $color-text-primary;
    }

    svg {
      margin-left: auto;
      flex: 0 0 16px;
    }
  }

  .sub-title {
    @include font-body-s-regular;
    @include text-ellipsis(2);

    color: $color-text-secondary;
    margin: 4px 0;
  }

  .icon {
    flex: 0 0 40px;
    margin-right: 12px;
    img {
      width: 100%;
      display: block;
    }
  }

  .card-info {
    display: flex;
    margin-top: 8px;

    .card-img {
      flex: 0 0 40px;
      margin-right: 8px;
      height: 40px;
      border-radius: $radius-m;
      background-size: cover;
      background-position: center;
    }

    .info {
      .info-tip {
        @include font-caption-m-regular;
        @include text-ellipsis(1);

        color: $color-brand-primary;
        margin-top: 2px;
      }

      .info-title {
        @include font-body-s-semibold;
        @include text-ellipsis(2);

        color: $color-text-primary;
      }

      .info-sub-title {
        @include font-caption-m-regular;
        @include text-ellipsis(1);

        color: $color-text-secondary;
      }
    }
  }
}
</style>
