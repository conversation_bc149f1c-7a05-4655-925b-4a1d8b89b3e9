<template>
  <div
    v-if="initCrossSellData && initCrossSellData.title"
    class="cross-sell"
    :class="'cross-sell-' + platformName"
  >
    <div class="title">
      <div class="title-text" v-if="initCrossSellData.title">
        <span>{{ initCrossSellData.title }}</span>
      </div>
      <a
        v-if="isDesktop && transferData.rich_text && !entranceCoupon"
        target="_blank"
        :href="transferData.rich_text.deep_link"
        :data-spm-module="`RecommendShortcutCard_Explore?oid=${
          transferData.rich_text.track_info.object_id
        }&ext=${JSON.stringify(transferData.rich_text.track_info.extra)}`"
        data-spm-virtual-item="__virtual"
        v-galileo-click-tracker="{ spm: 'RecommendShortcutCard_Explore', componentName: 'klook-cross-sell' }"
      >
        <klk-button type="outlined" size="small">
          <span
            v-html="
              transferData.rich_text.content.replace(
                '{City_ID}',
                transferData.rich_text.highlight_text
              )
            "
          />
        </klk-button>
      </a>

      <a
        v-if="entranceCoupon"
        :data-spm-module="`RecommendCouponBar?oid=${
          entranceCoupon.track_info && entranceCoupon.track_info.object_id
        }&ext=${JSON.stringify(
          entranceCoupon.track_info && entranceCoupon.track_info.extra
        )}`"
        data-spm-virtual-item="__virtual"
        v-galileo-click-tracker="{ spm: 'RecommendCouponBar', componentName: 'klook-cross-sell' }"
        target="_blank"
        :href="entranceCoupon.deep_link"
        class="coupon-entry"
      >
        <img class="icon" :src="entranceCoupon.icon_url" />
        <span class="content">{{ entranceCoupon.text }}</span>
        <div class="tag">
          <span>{{ entranceCoupon.coupon_text }}</span>
        </div>
        <img
          class="deep-icon"
          src="https://res.klook.com/image/upload/v1699932927/UED_new/Platform/Trip%20tab_2308/icon_right_arrow_3x.png"
        />
      </a>
    </div>
    <div
      class="cross-sell-card-list"
      :style="{ height: isDesktop ? 'auto' : boxHeight + 'px' }"
    >
      <div
        v-if="
          isDesktop &&
          transferData.transfer_list &&
          transferData.transfer_list.length
        "
        class="business-card"
        :style="{ order: isNew ? order : 0 }"
      >
        <div
          class="business-card-list"
          :class="[
            transferData.transfer_list.length === 1 && 'only-one',
            'business-card-list-' + platformName,
          ]"
        >
          <div
            v-for="(item, index) in transferData.transfer_list"
            :key="index"
            class="business-card-web"
          >
            <BusinessCardWeb
              v-if="item.card_name === 'app_cross_sell_hotel_01'"
              :card-data="item"
              :is-card="true"
            />

            <BusinessCardWeb
              v-else
              :card-data="item"
              :is-card="false"
              :data-spm-module="`${transferCardSpmNameList[index].spm}?oid=${
                item.track_info && item.track_info.object_id
              }&ext=${JSON.stringify(
                item.track_info && item.track_info.extra
              )}`"
              data-spm-virtual-item="__virtual"
              v-galileo-click-tracker="{ spm: transferCardSpmNameList[index].spm, componentName: 'klook-cross-sell' }"
            />
          </div>
        </div>
      </div>
      <div
        v-for="(card, i) in cardList"
        :key="i"
        class="item-card j-item-card"
        :style="
          !isDesktop && cardPositionList[i]
            ? {
                opacity: 1,
                visibility: 'visible',
                marginLeft: cardPositionList[i].type === 'left' ? 0 : '12px',
                transform: `translate(${
                  cardPositionList[i].type === 'left' ? 0 : '100%'
                },${cardPositionList[i].top})`,
              }
            : {
                order: i,
              }
        "
      >
        <div
          v-if="card.card_name === 'app_cross_sell_transfer_card_01'"
          class="business-card-list item"
          :class="'business-card-list-' + platformName"
        >
          <BusinessCardMweb
            v-for="(item, index) in card.data.transfer_list"
            :key="index"
            :card-data="item"
            :data-spm-module="`${transferCardSpmNameList[index].spm}?oid=${
              card.track_info &&
              card.track_info[transferCardSpmNameList[index].name] &&
              card.track_info[transferCardSpmNameList[index].name].object_id
            }&ext=${JSON.stringify(
              card.track_info &&
                card.track_info[transferCardSpmNameList[index].name] &&
                card.track_info[transferCardSpmNameList[index].name].extra
            )}`"
            data-spm-virtual-item="__virtual"
            v-galileo-click-tracker="{ spm: transferCardSpmNameList[index].spm, componentName: 'klook-cross-sell' }"
          />
          <a
            v-if="card.data.rich_text"
            :href="card.data.rich_text.deep_link"
            :data-spm-module="`RecommendShortcutCard_Explore?oid=${
              card.track_info &&
              card.track_info['Explore'] &&
              card.track_info['Explore'].object_id
            }&ext=${JSON.stringify(
              card.track_info &&
                card.track_info['Explore'] &&
                card.track_info['Explore'].extra
            )}`"
            data-spm-virtual-item="__virtual"
            v-galileo-click-tracker="{ spm: 'RecommendShortcutCard_Explore', componentName: 'klook-cross-sell' }"
            class="entry"
          >
            <span
              v-if="card.data.rich_text.highlight_text"
              v-html="
                card.data.rich_text.content.replace(
                  '{City_ID}',
                  `<span class='bold'>${card.data.rich_text.highlight_text}</span>`
                )
              "
            />
            <RightIcon :color="'#FFFFFF'" />
          </a>
        </div>
        <div
          v-else-if="card.card_name === 'app_cross_sell_hotel_01' && !isDesktop"
          class="item"
        >
          <klk-vertical-card
            :key="i"
            :img-ratio="1 / 1"
            :crop-width="270"
            style-type="modern"
            :size="'small'"
            :card-data="
              card.data
                ? {
                    deep_link: card.data.deep_link,
                    cover_url: card.data.cover_url,
                    description: card.data.desc,
                    title: card.data.title,
                    price: {
                      ...card.data.price,
                      market_price: null,
                    },
                    promotion_tag: card.data.promotion_tag,
                  }
                : {}
            "
            :data-spm-module="
              formatSPM('RecommendHotelCard_Card', {
                oid:
                  (card.track_info &&
                    card.track_info.Card &&
                    card.track_info.Card.object_id) ||
                  '',
                idx: i,
                len: cardList.length,
                ext:
                  (card.track_info &&
                    card.track_info.Card &&
                    card.track_info.Card.extra) ||
                  {},
              })
            "
            data-spm-virtual-item="__virtual"
            v-galileo-click-tracker="{ spm: 'RecommendHotelCard_Card', componentName: 'klook-cross-sell' }"
          >
            <div
              v-if="card.data.hotel_star"
              class="hotel_star"
              slot="content-product-slot"
            >
              {{ card.data.hotel_star }}
            </div>
          </klk-vertical-card>
          <a
            v-if="card.data.neary_by"
            :href="card.data.neary_by.deep_link"
            class="neary_by"
            :data-spm-module="`RecommendHotelCard_More?oid=${
              card.track_info &&
              card.track_info.More &&
              card.track_info.More.object_id
            }&ext=${JSON.stringify(
              card.track_info &&
                card.track_info.More &&
                card.track_info.More.extra
            )}`"
            data-spm-virtual-item="__virtual"
            v-galileo-click-tracker="{ spm: 'RecommendHotelCard_More', componentName: 'klook-cross-sell' }"
          >
            <span>{{ card.data.neary_by.title }}</span>
            <RightIcon :color="'#FF5B00'" />
          </a>
        </div>
        <klk-vertical-card
          class="card"
          v-else
          :key="i"
          :crop-width="270"
          :img-ratio="3 / 2"
          :style-type="isDesktop ? 'default' : 'modern'"
          :size="isDesktop ? 'medium' : 'small'"
          :is-show-review-booked="false"
          :card-data="
            card.data
              ? {
                  price_tag_list: card.data.price_tag_list,
                  deep_link: card.data.deep_link,
                  cover_url: card.data.cover_url,
                  description: card.data.desc,
                  title: card.data.title,
                  price: {
                    ...card.data.price,
                    market_price: null,
                  },
                  review: card.data.review,
                }
              : {}
          "
          :data-spm-module="
            card.track_info
              ? formatSPM(card.track_info.spm, {
                  oid: card.track_info.object_id || '',
                  idx: i,
                  len: cardList.length,
                  ext: card.track_info.extra,
                })
              : {}
          "
          data-spm-virtual-item="__virtual"
          v-galileo-click-tracker="{ spm: card.track_info ? card.track_info.spm : '', componentName: 'klook-cross-sell' }"
        />
      </div>
    </div>

    <div v-if="isInfiniteLoad" class="loading_box">
      <p
        v-if="
          (initCrossSellData &&
            initCrossSellData.items &&
            initCrossSellData.total_count <= initCrossSellData.items.length) ||
          loadError
        "
        v-html="textData.end"
      />
      <Loading v-else @onLoadData="loadDataHandler" />
    </div>
  </div>
</template>

<script lang="ts">
import debounce from "lodash/debounce";
import { Component, Vue, Prop, Watch } from "vue-property-decorator";
import { KlkVerticalCard } from "@klook/klook-card";
import "@klook/klook-card/dist/esm/index.css";
import {
  CrossSellData,
  SpmOptions,
  CardPosition,
  TextData,
} from "../types/types";
import BusinessCardWeb from "./business-card-web.vue";
import BusinessCardMweb from "./business-card-mweb.vue";
import RightIcon from "./icon/right.vue";
import Loading from "./loading.vue";

@Component({
  components: {
    RightIcon,
    BusinessCardWeb,
    BusinessCardMweb,
    KlkVerticalCard,
    Loading,
  },
})
export default class CrossSell extends Vue {
  @Prop() platform!: string;
  @Prop({
    type: Object,
    default: () => ({
      page_size: 20,
    }),
  })
  params!: any;

  @Prop({ type: Object, default: () => ({}) }) textData!: TextData; // 多语言
  @Prop({ type: String, default: "" }) dataUrl!: string;
  @Prop({ type: Number, default: 8 }) order!: number;
  @Prop({ default: true }) isResize!: boolean;
  @Prop({ type: String, default: "inside" }) dataOrigin!:
    | "inside" // 通过传入的dataUrl在组件内部获取
    | "outside" // 组件外传入数据
    | "mix"; // 首屏组件外传入，滚到底部 isInfiniteLoad = true 时 通过dataUrl获取

  @Prop() crossSellData!: CrossSellData;

  transferCardSpmNameList = [
    {
      spm: "RecommendShortcutCard_FirstCard",
      name: "FirstCard",
    },
    {
      spm: "RecommendShortcutCard_SecondCard",
      name: "SecondCard",
    },
    {
      spm: "RecommendShortcutCard_ThirdCard",
      name: "ThirdCard",
    },
  ];

  _axios!: any;
  isLoadingEnd: Boolean = false;
  loadError = false;

  // mweb 计算瀑布流
  leftHeight: number = 0;
  rightHeight: number = 0;
  boxHeight: number = 0;
  cardPositionList: CardPosition[] = [];
  currentLastCard: number = 0;
  pageNum: number = 1;
  isNew: Boolean = false;

  initCrossSellData: CrossSellData | any = {};

  get realPlatform() {
    return (
      this.platform ||
      (window as any)?.__KLOOK__?.state?.klook?.platform ||
      "desktop"
    );
  }

  get isDesktop() {
    return this.realPlatform === "desktop";
  }

  get platformName() {
    return this.isDesktop ? "web" : "mweb";
  }

  get cardList() {
    return this.initCrossSellData?.items || [];
  }

  get transferData() {
    return this.initCrossSellData?.entrance || {};
  }

  get entranceCoupon() {
    return this.initCrossSellData.entrance_coupon;
  }

  get isInfiniteLoad() {
    return this.dataOrigin !== "outside" && this.dataUrl;
  }

  loadDataHandler() {
    if (this.isLoadingEnd) {
      this.isLoadingEnd = false;
      // 存下当前卡片个数
      this.currentLastCard =
        (this.initCrossSellData &&
          this.initCrossSellData?.items &&
          this.initCrossSellData?.items.length) ||
        0;

      this.$emit("onUnlimitedLoad");
      this.getData();
    }
  }

  // 组件外传入数据
  @Watch("crossSellData", { immediate: true, deep: true })
  crossSellDataChange() {
    if (this.crossSellData && this.dataOrigin !== "inside") {
      this.initCrossSellData = JSON.parse(JSON.stringify(this.crossSellData));
      this.isNew = this.initCrossSellData.is_new;
      this.$nextTick(() => {
        if (!this.isDesktop) {
          this.getCardPosition();
          return;
        }
        this.isLoadingEnd = true;
      });
    }
  }

  mounted() {
    // 通过传入的dataUrl在组件内部获取
    if (this.dataOrigin === "inside") {
      this.getData();
    } else {
      this.pageNum += 1;
    }
    if (!this.isDesktop && this.isResize) {
      window.addEventListener("resize", this.handleResize);
    }
  }

  beforeDestroy() {
    if (!this.isDesktop && this.isResize) {
      window.removeEventListener("resize", this.handleResize);
    }
  }

  handleResize = debounce(() => {
    this.resize();
  }, 200);

  resize() {
    this.rightHeight = 0;
    this.leftHeight = 0;
    this.cardPositionList = [];
    this.currentLastCard = 0;
    this.getCardPosition();
  }

  beforeMount() {
    this._axios = this.$attrs.axios || window.$axios;
  }

  getData() {
    this._axios &&
      this._axios
        .$get(this.dataUrl, {
          params: { ...this.params, page_num: this.pageNum },
        })
        .then((res: any) => {
          if (res.success && res.result) {
            this.pageNum += 1;
            if (
              this.initCrossSellData?.items &&
              this.initCrossSellData?.items.length
            ) {
              this.initCrossSellData.items.push(...res.result.items);
            } else {
              this.initCrossSellData = res.result || {};
              this.isNew = this.initCrossSellData.is_new;
            }
            this.$nextTick(() => {
              if (!this.isDesktop) {
                this.getCardPosition();
                return;
              }
              this.isLoadingEnd = true;
            });
          } else {
            this.loadError = true;
          }
          this.$emit("onDataStatus", res.success && res.result);
        })
        .catch(() => {
          this.loadError = true;
        });
  }

  // 设置瀑布流布局定位
  getCardPosition() {
    if (!this.$el || !this.$el.getElementsByClassName) {
      return;
    }
    const cardListEl = this.$el.getElementsByClassName("j-item-card") as any;
    for (let i = this.currentLastCard; i < cardListEl.length; i++) {
      const item = cardListEl[i] as HTMLElement;
      const height = item.offsetHeight + 12;
      if (this.rightHeight < this.leftHeight) {
        this.cardPositionList.push({
          right: 0,
          type: "right",
          top: this.rightHeight + "px",
        });
        this.rightHeight += height;
      } else {
        this.cardPositionList.push({
          left: 0,
          type: "left",
          top: this.leftHeight + "px",
        });
        this.leftHeight += height;
      }
    }
    this.boxHeight =
      this.leftHeight > this.rightHeight ? this.leftHeight : this.rightHeight;
    this.isLoadingEnd = true;
  }

  formatSPM(key: string = "", trackInfo: SpmOptions = {}, isItem = false) {
    const jsonStringify = (val: any) =>
      typeof val === "object" ? JSON.stringify(val) : val;
    const qs = Object.entries(trackInfo)
      .map(([key, val]) => {
        return `${key}=${encodeURIComponent(jsonStringify(val))}`;
      })
      .join("&");

    return [isItem ? key : `${key}`, ...(qs ? [qs] : [])].join("?");
  }
}
</script>

<style lang="scss" scoped>
@mixin text-ellipsis($line-clamp: 2) {
  display: -webkit-box;
  -webkit-line-clamp: $line-clamp;
  -webkit-box-orient: vertical;
  text-overflow: ellipsis;
  overflow: hidden;
}

.cross-sell {
  margin: 0 auto;
  position: relative;
  z-index: 1;

  .coupon-entry {
    @include font-body-m-regular;

    display: flex;
    min-height: 48px;
    padding: 12px 16px;
    align-items: center;
    border-radius: $radius-l;
    background-color: $color-brand-primary-light-2;

    .content {
      @include text-ellipsis(2);
      margin: 0 16px 0 8px;
    }

    .icon {
      width: 20px;
    }

    .tag {
      @include font-caption-m-semibold;
      color: $color-text-reverse;
      display: flex;
      height: 20px;
      padding: 0px 6px;
      align-items: center;
      gap: 4px;
      max-width: 100px;
      border-radius: $radius-s;
      background: $color-brand-primary;
      margin-right: 8px;
      padding: 0 6px;
      margin-left: auto;

      span {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }

    .deep-icon {
      width: 16px;
    }
  }

  .hotel_star {
    @include font-caption-m-regular;
    color: $color-brand-primary;
  }

  .title {
    display: flex;
    gap: 10px;

    .title-text {
      display: flex;
      align-items: center;

      span {
        @include text-ellipsis(1);
      }

      flex: 1;
    }

    color: $color-text-primary;
  }

  .loading_box {
    height: 30px;
    position: relative;
    text-align: center;
  }

  &-web {
    .coupon-entry {
      width: 400px;
      background-color: $color-brand-primary-light;

      .content {
        @include text-ellipsis(1);
      }

      .tag {
        max-width: 120px;
      }
    }

    .title {
      .title-text {
        @include font-heading-m;
      }

      margin-bottom: 24px;
      justify-content: space-between;
    }

    .cross-sell-card-list {
      display: flex;
      flex-wrap: wrap;
      gap: 20px;
      align-items: stretch;

      .card {
        height: 100%;
      }

      .item-card {
        width: 275px;
      }
    }

    .loading_box {
      margin-top: 20px;
    }

    .business-card-web {
      display: flex;
      flex-direction: column;
      flex: 1;
      width: 100%;
    }
  }

  &-mweb {
    .title {
      .title-text {
        @include font-heading-xs;
      }

      margin-bottom: 12px;
      flex-wrap: wrap;
    }

    .coupon-entry {
      margin-top: 6px;
      width: 100%;
      height: auto;

      .icon,
      .tag {
        margin-bottom: auto;
        margin-top: 2px;
      }

      .deep-icon {
        margin-bottom: auto;
        margin-top: 4px;
      }
    }

    .cross-sell-card-list {
      // column-count: 2;
      // column-gap: 12px;
      position: relative;
    }

    .neary_by {
      @include font-body-s-regular;

      display: flex;
      justify-content: space-between;
      align-items: center;
      gap: 12px;
      color: $color-brand-primary;
      padding: 8px 12px;
      border-radius: $radius-l;
      background: $color-brand-primary-light;

      svg {
        flex: 0 0 6px;
      }
    }

    .item-card {
      position: absolute;
      width: calc(50% - 6px);
      opacity: 0;
      top: 0;
      left: 0;
      visibility: hidden;
    }
  }

  .business-card {
    width: 100%;
  }

  .business-card-list {
    background: $color-accent-5;
    border-radius: $radius-xl;

    &-web {
      display: flex;
      flex-direction: row;
      padding: 20px;
      gap: 20px;
      width: 100%;

      &.only-one {
        width: 50%;
      }
    }

    &-mweb {
      padding: 12px;

      .entry {
        @include font-body-s-regular;

        display: flex;
        gap: 4px;
        justify-content: space-between;
        align-items: center;
        padding-top: 4px;
        color: $color-text-reverse;

        ::v-deep.bold {
          font-weight: bold;
        }
      }
    }
  }
}
</style>
