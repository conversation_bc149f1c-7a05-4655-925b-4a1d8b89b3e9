<template>
  <klk-loading />
</template>

<script>
export default {
  name: 'Loading',
  mounted() {
    this.createObserver(this.$el, () => {
      this.$emit('onLoadData')
    })
  },
  methods: {
    createObserver(element, cb) {
      if (element && 'IntersectionObserver' in window) {
        const elementObserver = new IntersectionObserver((entries) => {
          entries.forEach((entry) => {
            // 如果元素可见
            if (entry.intersectionRatio > 0) {
              cb && cb()
            }
          })
        })

        elementObserver.observe(element)

        this.$once('hook:beforeDestroy', () => {
          elementObserver.disconnect()
        })
      } else {
        this.$emit('onLoadData')
      }
    }
  }
}
</script>

<style scoped>

</style>
