import replace from 'rollup-plugin-replace'
import typescript from 'rollup-plugin-typescript2'
import vue from 'rollup-plugin-vue'
import postcss from 'rollup-plugin-postcss'
import nodeResolve from 'rollup-plugin-node-resolve'
import clear from 'rollup-plugin-clear'
import pkg from './package.json'

const commonjs = require('rollup-plugin-commonjs')
const babel = require('rollup-plugin-babel')

// 删除dist
clear({ targets: ['dist'] })

const {
  name,
  version,
  author
} = require('./package.json')

const banner =
  `/**
  * v${version}
  * (c) ${new Date().getFullYear()} ${author}
  */`
const configs = {
  esm: {
    dir: 'dist/esm/',
    output: 'dist/esm/index.js',
    format: 'esm',
    target: 'es5',
    env: 'production',
    genDts: true,
    isSSR: false
  },
  cjs: {
    dir: 'dist/cjs/',
    output: 'dist/cjs/index.js',
    inlineDynamicImports: true,
    format: 'cjs',
    target: 'es5',
    isSSR: true
  }
}

const genTsPlugin = configOpts => typescript({
  useTsconfigDeclarationDir: true,
  tsconfigOverride: {
    compilerOptions: {
      target: configOpts.target,
      declaration: configOpts.genDts
    },
    exclude: ['**/__tests__', 'test-dts']
  },
  abortOnError: false
})

const genPlugins = (configOpts) => {
  const plugins = []
  if (configOpts.env) {
    plugins.push(replace({
      'process.env.NODE_ENV': JSON.stringify(configOpts.env)
    }))
  }
  plugins.push(nodeResolve({
    extensions: ['.mjs', '.js', '.jsx', '.vue']
  }))
  plugins.push(commonjs({
    include: /node_modules/
  }))
  plugins.push(replace({
    'process.env.MODULE_FORMAT': JSON.stringify(configOpts.format)
  }))
  if (configOpts.plugins && configOpts.plugins.pre) {
    plugins.push(...configOpts.plugins.pre)
  }
  plugins.push(genTsPlugin(configOpts))

  plugins.push(vue({
    css: false,
    normalizer: '~vue-runtime-helpers/dist/normalize-component.js',
    template: {
      isProduction: true,
      optimizeSSR: configOpts.isSSR,
    },
    style: {
      postcssPlugins: [
        require('autoprefixer')()
      ],
      preprocessStyles: true,
      preprocessOptions: {
        scss: {
          data: '@import "../klook-ui/src/styles/token/index.scss";'
        }
      }
    }
  }))

  plugins.push(babel({
    include: ['src/**', 'node_modules/**'],
    extensions: ['.js', '.vue','.ts','.jsx','.tsx']
  }))

  // 不必提取css
  plugins.push(postcss({
    extract: true,
    plugins: [
      require('autoprefixer')()
    ]
  }))

  if (configOpts.plugins && configOpts.plugins.post) {
    plugins.push(...configOpts.plugins.post)
  }
  return plugins
}

const genConfig = configOpts => ({
  input: 'src/index.js',
  inlineDynamicImports: configOpts.inlineDynamicImports,
  output: {
    banner,
    dir: configOpts.dir,
    format: configOpts.format,
    name,
    sourcemap: false,
    exports: 'named',
    globals: {
      vue: 'Vue'
    }
  },
  external(id) {
    if(configOpts.format === 'cjs' && /\.(css|scss)$/.test(id)){
      return false
    }else {
      return Object.keys(pkg.peerDependencies).includes(id.split('/')[0]) || id.includes('@klook/');
    }
  },
  plugins: genPlugins(configOpts)
})

const genAllConfigs = configs => (Object.keys(configs).map(key => genConfig(configs[key])))

export default genAllConfigs(configs)
