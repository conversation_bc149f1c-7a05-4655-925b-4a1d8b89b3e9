<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator'
import Nav from './lib'
import {CreateElement} from "vue";
import ExpandMore from './expand-more.vue'

@Component({
  name: "KlookScrollNav"
})
export default class ScrollNav extends Vue {
  @Prop({ type: Array, default: () => [] }) navItems!: any[]
  @Prop({ type: String, default: 'current' }) currentClass!: string
  @Prop({ type: Number, default: 80 }) offset!: number
  @Prop({ type: Boolean, default: true }) useSwiper!: number

  @Prop({ type: Number, default: 0 }) visiblePosition!: number // 滚动多少高度后显示
  @Prop({ type: Boolean, default: false }) visibleByFirstItem!: boolean // 滚动条高度超过第一个元素显示，优先级高于 visiblePosition
  @Prop({ type: Boolean, default: false }) isExpandBtnShow!: boolean

  defaultVisible = false
  currentIndex = 0
  $swiper: any = null

  get swiperOption() {
    return {
      init: true,
      slidesPerView: 'auto',
      freeMode: true,
      observer: true,
      observeParents: true
    }
  }

  get visible() {
    if (this.visiblePosition > 0 || this.visibleByFirstItem) {
      return this.defaultVisible
    }

    return true
  }

  get canUseSwiper() {
    return this.useSwiper && this.$options?.directives?.swiper
  }

  firstItemVisiblePosition = 0

  mounted() {
    this.$nextTick(() => {
      this.init()
    })
  }

  nav:Nav|null = null

  init() {
    if (this.navItems.length) {
      this.nav = new Nav(this.$refs.navSwiper, {
        expandMoreWrapDom: this.$el.querySelector('.js_expandMoreWrap'),
        offset: this.offset,
        onNavChange: ({ index }: { index: number }) => {
          this.currentIndex = index
        },
        ...this.canUseSwiper ? {
          scrollToNav: (offsetPixel: number) => {
            if (this.$swiper) {
              this.$swiper.setTransition(350)
              this.$swiper.setTranslate(0 - offsetPixel)
            }
          }
        } : {},
        // 需要处理默认滚动显示
        ...(this.visiblePosition > 0 || this.visibleByFirstItem) ? {
          onScroll: (top: number) => {
            this.defaultVisible = top > (this.firstItemVisiblePosition || this.visiblePosition)
          }
        } : {},

        // 使用第一个元素高度做定位
        ...this.visibleByFirstItem ? {
          updateFirstItemTop: (val: number) => {
            this.firstItemVisiblePosition = val - this.offset
          }
        } : {}
      })

      const unwatch = this.$watch('navItems', ()=>{
        this.nav?.destroy()
        unwatch()
        this.init()
      },{ deep:true })
    }
  }

  beforeDestroy() {
    this.nav?.destroy()
  }

  render(_c: CreateElement) {
    return _c('div', {
      staticClass: 'klk-scroll-nav-content',
      class: {
        'scroll-nav-hidden': !this.visible
      }
    }, [
      _c('div', {
        directives: this.canUseSwiper ? [{
          name: 'swiper',
          value: this.swiperOption,
          expression: 'swiperOption',
          arg: '$swiper'
        }] : [],
        ref: 'navSwiper',
        staticClass: 'klk-scroll-nav-wrapper swiper-container'
      }, [
        _c('ul', {
          staticClass: "scroll-nav-list swiper-wrapper",
        }, this.navItems.map((item, index) => {
          return _c('li', {
            key: index,
            staticClass: "scroll-nav-item swiper-slide",
            attrs: {
              'data-spm-module': `Location::::SectionTab_LIST?&idx=${index}&len=${this.navItems.length}&ext=${encodeURIComponent(JSON.stringify({SectionName: item.name}))}`,
              'data-spm-virtual-item': '__virtual'
            }
          }, [
            _c('a', {
              staticClass: "scroll-nav-link js_navItem",
              class: {
                [this.currentClass]: index === this.currentIndex,
                'pd-right-20': this.isExpandBtnShow && index === this.navItems.length - 1
              },
              attrs: {
                href: item.id
              }
            }, item.text)
          ])
        })),
      ]),
      _c(ExpandMore, {
        directives: [{
          name: 'show',
          value: this.isExpandBtnShow,
          expression: 'isExpandBtnShow',
        }],
        staticClass: 'js_expandMoreWrap',
        props: {
          navItems: this.navItems,
          currentIndex: this.currentIndex,
          offset: this.offset
        },
      })
    ])
  }
}

</script>

<style scoped lang="scss">
.pd-right-20 {
  padding: 10px 44px 10px 20px!important;
}

.scroll-nav-hidden {
  visibility: hidden;
  opacity: $opacity-transparent;
  transition: 0.2s ease opacity;
}

.klk-scroll-nav-content {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 19;
  background: $color-bg-widget-normal;
  box-shadow: $shadow-normal-2;
}

.klk-scroll-nav-wrapper {
  text-align: center;
  overflow: auto;

  &::-webkit-scrollbar {
    width: 0;
    height: 0;
  }
}

.scroll-nav-list {
  list-style: none;
  font-size: 0;
  display: inline-block;
  vertical-align: top;
  white-space: nowrap;
  width: auto;
}

.scroll-nav-item {
  display: inline-block;
  vertical-align: top;
  width: auto;
}

.scroll-nav-link {
  display: flex;
  position: relative;
  align-items: center;
  padding: 10px 24px;
  font-size: $fontSize-body-s;
  text-decoration: none;
  height: 48px;
  box-sizing: border-box;
  color: inherit;

  &.current {
    color: $color-brand-primary;
  }
}
</style>
