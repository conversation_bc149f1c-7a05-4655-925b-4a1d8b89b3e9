<template>
  <div ref="expandMore" class="expand-more">
    <ul v-show="isExpandContentShow" class="expand-content">
      <li
        v-for="(item, index) in navItems"
        class="scroll-nav-item"
        :key="index"
        @click="showExpandContent"
      >
        <a
          :href="item.id" class="scroll-nav-link js_navItemExpand"
          :data-index="index"
          :class="{current: index === currentIndex}"
        >{{ item.text }}</a>
      </li>
      <li class="expand-up-btn" @click="showExpandContent">
        <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" fill="currentColor" viewBox="0 0 12 12">
          <path d="M6.583 9.121L10.4 5.576c.571-.53.196-1.485-.583-1.485H2.183c-.78 0-1.155.955-.584 1.485l3.818 3.545a.857.857 0 001.166 0z"/>
        </svg>
      </li>
    </ul>
    <div v-show="!isExpandContentShow" class="expand-down-btn" @click="showExpandContent">
      <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" fill="currentColor" viewBox="0 0 12 12">
        <path d="M6.583 9.121L10.4 5.576c.571-.53.196-1.485-.583-1.485H2.183c-.78 0-1.155.955-.584 1.485l3.818 3.545a.857.857 0 001.166 0z"/>
      </svg>
    </div>
  </div>
</template>

<script>

export default {
  props: {
    navItems: {type: Array, default: () => []},
    currentIndex: {type: Number, default: 0},
    offset: {type: Number, default: 80},
  },
  components: {},
  data() {
    return {
      isInit: false,
      isExpandContentShow: false
    };
  },
  computed: {
  },
  methods: {
    showExpandContent() {
      this.isExpandContentShow = !this.isExpandContentShow
    }
  }
};
</script>

<style lang="scss" scoped>
.expand-more {
  position: absolute;
  left: 0;
  width: 100vw;
  background: $color-bg-1;
  box-shadow: 0 1px 6px 0 rgb(0 0 0 / 10%);

  .expand-down-btn {
    position: absolute;
    right: 0;
    top: -48px;
    z-index: 9;
    width: 44px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(270deg, #FFFFFF 70.07%, rgba(255, 255, 255, 0.8) 87.27%, rgba(255, 255, 255, 0.1) 100%);
    cursor: pointer;
  }

  .expand-content {
    .scroll-nav-link {
      display: flex;
      padding: 12px 24px;
    }

    .current {
      color: $color-brand-primary;
    }

    .expand-up-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 12px 0;
      cursor: pointer;

      svg {
        transform: rotate(180deg);
      }
    }
  }
}
</style>
