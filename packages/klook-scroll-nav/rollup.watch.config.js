import path from 'path'
import clear from 'rollup-plugin-clear'
import buildConf from './rollup.config.js'

const resolve = p => path.resolve(__dirname, './', p)

const esmConf = buildConf.find(item => item.output.format === 'esm')

esmConf.plugins.unshift(clear({ targets: [esmConf.output.dir] }))

const watchConf = {
  // watch 只构建esm
  ...esmConf,
  watch: {
    buildDelay: 300,
    include: resolve('src/**'),
    exclude: resolve('node_modules/**'),
    clearScreen: true
  }
}

export default watchConf
