<template>
  <div class="klook-order-refund-entry" @click="$emit('click', refundStatus)"
    :data-spm-module="`RefundAccess?ext=${JSON.stringify({ RefundStatus: refundStatus.status })}`"
    data-spm-virtual-item="__virtual">
    <div class="klook-order-refund-entry-main">
      <div class="klook-order-refund-entry-main_status" :style="{ 'color': refundStatus.text_color }">{{ refundStatus.text
      }}</div>
      <IconNext theme="outline" size="16" fill="#212121" />
    </div>

    <div v-if="refundStatus.desc" class="klook-order-refund-entry-addon">
      {{ refundStatus.desc }}
    </div>
  </div>
</template>

<script lang="ts">
import {
  Vue,
  Component,
  Prop,
  Provide,
} from 'vue-property-decorator'
import { IconNext } from '@klook/klook-icons';
import { genText } from "./locale/index";
import messages from "../locales/index.js";
import KlkPoptip from '@klook/klook-ui/lib/poptip'

interface RefundStatus {
  text: string;
  text_color: string;
  desc: string;
  icon: string;
  status: string;
}

@Component({
  name: "OrderRefundEntry",
  components: {
    IconNext,
    KlkPoptip
  }
})
export default class OrderRefundEntry extends Vue {
  // start from here
  @Prop({
    default: () => { }
  })
  refundStatus!: RefundStatus;
  @Provide() __t: any = this.getTranslate()

  getTranslate() {
    return this.__t;
  }

  beforeCreate(this: any) {
    const locales = messages as any;
    const lang = this.$attrs.language || 'en';
    this.__t = locales[lang]
      ? genText(locales[lang])
      : genText(locales["en"]);
  }
}
</script>

<style lang="scss">
.klook-order-refund-entry {
  cursor: pointer;
  background-color: #fff;
  padding: 16px;
  border-radius: 16px;

  &-main {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 16px;
    font-style: normal;
    font-weight: 600;
  }

  &-addon {
    font-size: 14px;
    color: #757575;
    margin-top: 4px;
  }
}
</style>
