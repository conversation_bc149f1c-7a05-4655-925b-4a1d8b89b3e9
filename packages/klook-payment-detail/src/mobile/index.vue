<template>
  <div>
    <klk-bottom-sheet :data-spm-page="`CmptPaymentDetail?oid=booking_${bookingRefNo}`" class="klook-payment-detail-wrap"
      :title="__t('109020')" :visible="visible" @close="onVisibleChange(false)" @open="onVisibleChange(true)">
      <klk-icon @click="onVisibleChange(false)" slot="header-left" type="icon_navigation_close_m" size="24" color="#212121"></klk-icon>

      <payment-info v-bind="$attrs" v-on="$listeners">
        <template v-for="(item, key) in $slots" v-slot:[key]>
          <slot :name="key"></slot>
        </template>
      </payment-info>
    </klk-bottom-sheet>
  </div>
</template>

<script lang="ts">
import {
  Vue,
  Component,
  Prop,
  Watch
} from 'vue-property-decorator'
import { Icon as KlkIcon } from '@klook/klook-ui';
import { BottomSheet as KlkBottomSheet } from '@klook/klook-ui';
import PaymentInfo from '../components/payment-info.vue';
import Base from '../base';

@Component({
  name: "KlookPaymentDetailMobile",
  components: {
    KlkIcon,
    KlkBottomSheet,
    PaymentInfo,
  }
})
export default class KlookPaymentDetailMobile extends Base {
  @Prop({
    default: false
  }) visible!: boolean;

  @Prop()
  bookingRefNo!: string;

  close() {
    this.$emit('update:visible', false);
  }

  onVisibleChange(val: boolean) {
    this.$emit('visibleChange', val);
  }
}
</script>

<style lang="scss"></style>
