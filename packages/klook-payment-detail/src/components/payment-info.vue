<template>
  <div class="payment-info">
    <div class="payment-info-subtotal form-item">
      <div class="label">{{ paymentDetails.sub_total_text }}</div>
      <div class="info">
        <div class="content">{{ paymentDetails.sub_total_amount }}</div>
      </div>
    </div>

    <div v-if="$slots['price-detail']" class="payment-detail-price-detail">
      <slot name="price-detail"></slot>
    </div>

    <div class="price-details" v-if="paymentDetails.price_details && paymentDetails.price_details.length">
      <div class="form-item" v-for="(item, index) in paymentDetails.price_details" :key="index">
        <div class="label">{{ item.title_text }}</div>
        <div class="info">
          <div class="content">{{ item.content_text }}</div>
        </div>
      </div>
    </div>

    <div class="payment-detail">
      <div class="form-item">
        <div class="label">{{ paymentDetails.total_after_discount_text }}</div>
        <div class="info">
          <div class="content">{{ paymentDetails.total_after_discount_value }}</div>
        </div>
      </div>

      <div class="form-item" v-for="item in paymentDetails.extra_pay_amount">
        <div class="label">{{ item.title_text }}</div>
        <div class="info">
          <div class="content">{{ item.content_text }}</div>
        </div>
      </div>

      <div
        v-if="paymentDetails.actual_payment_amount_text || paymentDetails.actual_payment_amount_value"
        class="form-item form-item-bold"
      >
        <div class="label">{{ paymentDetails.actual_payment_amount_text }}</div>
        <div class="info">
          <div class="content">{{ paymentDetails.actual_payment_amount_value }}</div>
        </div>
      </div>

      <klk-markdown
        v-if="paymentDetails.paylater_desc"
        class="payment-desc-text"
        :content="paymentDetails.paylater_desc"
      />

      <div class="payment-desc-text" v-if="paymentDetails.payment_desc_text">
        {{ paymentDetails.payment_desc_text }}
      </div>

      <div class="form-item" v-if="paymentDetails.payment_details_buttons && paymentDetails.payment_details_buttons.length">
        <div class="button-group">
          <klk-button @click="customButtonClick(item, index)" type="outlined" v-for="(item, index) in paymentDetails.payment_details_buttons"
            :key="index">{{ item.button_text }}</klk-button>
        </div>
      </div>
    </div>

    <div v-if="$slots['bottom-component']" class="payment-detail-bottom-child">
      <slot name="bottom-component"></slot>
    </div>
  </div>
</template>

<script lang="ts">
import {
  Vue,
  Component,
  Prop,
  Watch
} from 'vue-property-decorator'
import KlkIcon from '@klook/klook-ui/lib/icon';
import KlkMarkdown from '@klook/klook-ui/lib/markdown';
import Base from '../base';

@Component({
  name: "KlookRefundDetailList",
  components: {
    KlkIcon,
    KlkMarkdown
  }
})
export default class KlookRefundDetailList extends Base {
  @Prop()
  paymentDetails!: Record<string, any>

  @Prop({
    default: false
  }) isKlookApp: boolean;

  customButtonClick(item: any, index: number) {
    if (item.button_link) {
      if (this.isKlookApp) {
        window.location.href = item.button_link;
      } else {
        window.open(item.button_link);
      }
    }
    this.$emit('custom-button-click', item, index)
  }

  copy(text: string) {
    if (navigator.clipboard) {
      // clipboard api 复制
      navigator.clipboard.writeText(text);
    } else {
      var textarea = document.createElement('textarea');
      document.body.appendChild(textarea);
      // 隐藏此输入框
      textarea.style.position = 'fixed';
      textarea.style.clip = 'rect(0 0 0 0)';
      textarea.style.top = '10px';
      // 赋值
      textarea.value = text;
      // 选中
      textarea.select();
      // 复制
      document.execCommand('copy', true);
      // 移除输入框
      document.body.removeChild(textarea);
    }
  }
}
</script>

<style lang="scss">
.payment-info {
  .price-details {
    padding: 8px 0;
  }

  .payment-desc-text {
    font-size: 14px;
    font-weight: 400;
    color: #757575;
  }

  .form-item {
    display: flex;
    justify-content: space-between;
    padding: 8px 0;

    &.form-item-bold {
      font-weight: 600;

      .label, .content {
        color: #212121;
        font-size: 20px;
        font-weight: 600;
      }

    }

    .label {
      max-width: 50%;
      font-size: 16px;
      color: #757575;
    }

    .info {
      max-width: 50%;
      text-align: right;
    }

    .content {
      color: #212121;
      // margin-bottom: 4px;
      font-size: 16px;

      svg {
        cursor: pointer;
        vertical-align: middle;
      }
    }
  }

  .payment-detail {
    .label {
      color: #212121;
    }
  }

  &-subtotal {
    font-weight: 600;

    &.form-item .label {
      color: #212121;
    }
  }

  .payment-detail-price-detail {
    background-color: #f5f5f5;
    border-radius: 16px;
    padding: 16px;
    margin-bottom: 8px;
    position: relative;
    z-index: 10;

    &::before {
      content: '';
      position: absolute;
      border-left: 7px solid transparent;
      border-right: 7px solid transparent;
      top: -7px;
      border-bottom: 7px solid #f5f5f5;
      right: 20px;
      margin-left: -7px;
    }
  }

  .payment-detail {
    padding-top: 16px;
    margin-top: 16px;
    border-top: 1px solid #eee;
  }

  .button-group {
    font-size: 0;

    .klk-button {
      margin-right: 12px;
      margin-bottom: 12px;
    }
  }
}
</style>
