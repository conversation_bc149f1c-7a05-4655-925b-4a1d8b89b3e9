<template>
  <div>
    <klk-drawer :data-spm-page="`CmptPaymentDetail?oid=booking_${bookingRefNo}`" class="klook-payment-detail-wrap"
      direction="right" :visible="visible"  @close="onVisibleChange(false)" @open="onVisibleChange(true)">
      <div class="payment-header">
        <klk-icon class="header-btn" @click="close" type="icon_navigation_close_m" color="#212121" size="24"></klk-icon>

        <div class="title">{{ __t('109020') }}</div>

        <template v-if="popTipTitle || popTipContent">
          <klk-poptip slot="header-right" :title="popTipTitle" :content="popTipContent" placement="bottom-end"
            trigger="click">
            <klk-icon class="header-btn" type="icon_tips_tips_xs" size="24" color="#212121"></klk-icon>
          </klk-poptip>
        </template>
      </div>

      <payment-info v-bind="$attrs" v-on="$listeners">
        <template v-for="(item, key) in $slots" v-slot:[key]>
          <slot :name="key"></slot>
        </template>
      </payment-info>
    </klk-drawer>
  </div>
</template>

<script lang="ts">
import {
  Vue,
  Component,
  Prop,
  Watch
} from 'vue-property-decorator'
import { Icon as KlkIcon } from '@klook/klook-ui';
import { Drawer as KlkDrawer } from '@klook/klook-ui';
import PaymentInfo from '../components/payment-info.vue';
import Base from '../base';

@Component({
  name: "KlookPaymentDetailDesktop",
  components: {
    KlkIcon,
    KlkDrawer,
    PaymentInfo,
  }
})
export default class KlookPaymentDetailDesktop extends Base {
  @Prop({
    default: false
  }) visible!: boolean;

  @Prop()
  bookingRefNo!: string;

  @Prop()
  title!: string;

  @Prop()
  popTipTitle?: string;

  @Prop()
  popTipContent?: string;

  @Prop()
  refundDetailList!: Array<any>

  close() {
    this.$emit('update:visible', false);
  }

  onVisibleChange(val: boolean) {
    this.$emit('visibleChange', val);
  }
}
</script>

<style lang="scss">
.klook-payment-detail-wrap {

  .klk-drawer-content {
    padding: 0 32px;
    width: 40%;
    min-width: 420px;
    max-width: 750px;
  }

  .payment-header {
    position: sticky;
    z-index: 99999;
    top: 0;
    display: flex;
    align-items: center;
    background-color: #fff;
    padding: 20px 0;

    .title {
      font-size: 20px;
      font-weight: 600;
      color: #212121;
      flex: 1;
      text-align: center;
    }

    .header-btn {
      cursor: pointer;
    }
  }
}
</style>
