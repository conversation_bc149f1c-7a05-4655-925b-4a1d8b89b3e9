let showtextid = 0;

// load i18n file
// async function loadLanguageAsync(locale) {
//   const filename = `${locale}.json`;
//   let messages = null;
//   try {
//     const result = await import(
//       `../../locales/${filename}` /* webpackChunkName: "lang-[request]" */
//     );
//     messages = result.default || result;
//   } catch (error) {
//     // eslint-disable-next-line no-console
//     console.error("Load locale error:", error);
//     return messages;
//   }

//   return messages;
// }

// 处理 {}
const t = (key, option = {}, localeData) => {
  let text = localeData[key] || String(key);
  if (showtextid) {
    text = key + '-' + text;
  }
  Object.entries(option).forEach(([k, v]) => {
    // console.log(text, text.replaceAll)
    text = text.replace(`{${k}}`, v);
  });
  return text;
};


export const genText = (file) => {
  return (key, option) => t(key, option, file);
};
