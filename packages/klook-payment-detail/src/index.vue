<template>
  <component :class="[`klook-payment-detail-${platform}`]" :is="contactUsComp" v-bind="$attrs" v-on="$listeners"
    :visible="visible" @visibleChange="onVisibleChange" :platform="platform">

    <!-- 透传slot -->
    <template v-for="(item, key) in $slots" v-slot:[key]>
      <slot :name="key"></slot>
    </template>
  </component>
</template>

<script lang="ts">
import {
  Vue,
  Component,
  Prop,
  Watch,
  Provide
} from 'vue-property-decorator'
import KlkIcon from '@klook/klook-ui/lib/icon';

import mobileComp from './mobile/index.vue';
import desktopComp from './desktop/index.vue';
import { genText } from "./locale/index";
import messages from "../locales/index.js";

@Component({
  name: "KlookRefundDetail",
  components: {
    KlkIcon,
    mobileComp,
    desktopComp
  }
})
export default class KlookRefundDetail extends Vue {
  @Prop({
    default: false
  }) visible!: boolean;

  @Prop({
    default: 'mobile'
  }) platform!: 'mobile' | 'desktop';

  get contactUsComp() {
    return this.platform === 'mobile' ? 'mobileComp' : 'desktopComp'
  }

  onVisibleChange(val: boolean) {
    this.$emit('update:visible', val);
  }

  @Provide() __t: any = this.getTranslate()

  getTranslate() {
    return this.__t;
  }

  beforeCreate(this: any) {
    const locales = messages as any;
    const lang = this.$attrs.language || 'en';
    this.__t = locales[lang]
      ? genText(locales[lang])
      : genText(locales["en"]);
  }
}
</script>

<style lang="scss">
@import '~@klook/klook-ui/dist/klook-ui.css';
$primaryColor: #F5F5F5;

.klook-payment-detail {

  .payment-info-subtotal {
    &.form-item {

      .label,
      .content {
        font-size: 16px;
      }
    }
  }

  .form-item-bold {
    font-size: 20px;

    &.form-item {
      .label, .content {
        font-size: 20px;
      }
    }
  }

  &-desktop {
    .payment-info-subtotal {
      &.form-item {

        .label,
        .content {
          font-size: 20px;
        }
      }
    }

    .form-item-bold {
    font-size: 24px;

    &.form-item {
      .label, .content {
        font-size: 24px;
      }
    }
  }
  }

  .klk-bottom-sheet-inner,
  .klk-bottom-sheet-header,
  .klk-drawer-content {
    background: $primaryColor;
  }

  .klk-drawer-content {
    padding: 0px 16px 0;
    width: 40%;
    min-width: 420px;
    max-width: 750px;

    .close-btn {
      cursor: pointer;
      margin: 20px 0;
    }
  }
}
</style>
