<template>
  <div :class="cClass">
    <div ref="scroll" class="klk-scroll-snap_scroller">
      <slot />
    </div>
    <slot name="action"> </slot>
  </div>
</template>

<script lang="ts">
import { Vue, Component, Prop } from "vue-property-decorator";

@Component({
  name: "KlkScrollSnap",
})
export default class KlkScrollSnap extends Vue {
  @Prop({ type: Boolean, default: true }) withShadow!: boolean;
  @Prop({ type: String, default: "horizontal" }) direction!:
    | "horizontal"
    | "vertical";
  @Prop({ type: String, default: "center" }) itemAlign!:
    | "center"
    | "start"
    | "end";
  @Prop({ type: Number, default: 0 }) delta!: number;
  @Prop({ type: String, default: 'normal' }) size!: 'normal' | '';

  timer = undefined;

  get cClass() {
    return [
      {
        "klk-scroll-snap": true,
        "klk-scroll-snap-shadow": this.withShadow,
      },
      `klk-scroll-snap--size-${this.size}`,
      `klk-scroll-snap--${this.direction}`,
      `klk-scroll-snap-align--${this.itemAlign}`,
    ];
  }

  // 兼容服务端
  get scrollContainer() {
    return this.$refs.scroll as HTMLElement;
  }

  mounted() {
    this.scrollContainer.addEventListener("scroll", this.scrollThrottle);
  }

  beforeDestroy() {
    this.scrollContainer?.removeEventListener("scroll", this.scrollThrottle);
  }

  scrollThrottle() {
    clearTimeout(this.timer);
    this.timer = setTimeout(() => {
      this.countItemIndex();
    }, 100) as any;
  }

  getStyle(dom: any, attr: any) {
    return dom.currentStyle
      ? dom.currentStyle[attr]
      : getComputedStyle(dom, null)[attr];
  }

  countItemIndex() {
    let index = 0;
    const length = this.scrollContainer.children.length;
    if (length === 0) {
      return;
    }
    const attr = this.direction === "horizontal" ? "Left" : "Top";
    const firstChildMargin = Number(
      this.getStyle(
        this.scrollContainer.children[0] as HTMLElement,
        `margin${attr}`
      ).replace(/auto|px/g, "")
    );
    const scrollDistance = this.scrollContainer[`scroll${attr}`];
    const scrollDistancePadding = Number(
      this.getStyle(
        this.scrollContainer as HTMLElement,
        `scrollPadding${attr}`
      ).replace(/auto|px/g, "")
    );

    for (let i = 0; i < length; i++) {
      const item = this.scrollContainer.children[i] as HTMLElement;
      const offset = item[`offset${attr}`];
      // (this.itemAlign === "start" ? 0 : firstChildMargin)
      if (
        offset + this.delta - firstChildMargin >=
        scrollDistance + scrollDistancePadding
      ) {
        index = i;
        break;
      }
      /**
      const attrWidth = this.direction === "horizontal" ? "Width" : "Height";
      if(scrollDistance + this.scrollContainer[`offset${attrWidth}`] === this.scrollContainer[`scroll${attrWidth}`] ) {
        index = length - 1;
        break;
      }
      */
    }

    this.$emit("change", index);
  }

  slideTo(index: number) {
    const attr = this.direction === "horizontal" ? "Left" : "Top";
    const scrollAttr: any = {};
    const el = this.$el.querySelector(
        `.klk-scroll-snap_scroller > *:nth-child(${index + 1})`
      ) as HTMLElement

    if(!el) {
      return;
    }
    scrollAttr[attr.toLowerCase()] = el[`offset${attr}`];

    this.scrollContainer.scrollTo({
      ...scrollAttr,
      behavior: "smooth",
    });
  }
}
</script>

<style lang="scss" scoped>
.klk-scroll-snap {
  position: relative;
  &.klk-scroll-snap--horizontal {
    .klk-scroll-snap_scroller {
      scroll-snap-type: x mandatory;
      overflow-x: scroll;
      display: flex;
      box-sizing: border-box;
      scrollbar-width: none;
      &::-webkit-scrollbar {
        display: none;
      }
      /* Enable Safari touch scrolling physics which is needed for scroll snap */
      -webkit-overflow-scrolling: touch;

      & > * {
        scroll-snap-align: center;
        margin-right: 12px;
        position: relative;
        border-radius: 16px;
        scroll-snap-stop: always;
        flex-shrink: 0;

        &:first-child {
          margin-left: 20px;
        }
        &:last-child {
          margin-right: 20px;
          position: relative;
          // hack 最后一个元素边距
          &::after {
            content: "";
            position: absolute;
            right: -20px;
            top: 0;
            display: block;
            width: 20px;
            height: 20px;
          }
        }
      }
    }

    &.klk-scroll-snap--size-normal {
      .klk-scroll-snap_scroller {
        & > * {
          width: calc(100% - 52px)
        }
      }
    }

    &.klk-scroll-snap-align--start {
      .klk-scroll-snap_scroller {
        & > * {
          scroll-snap-align: start;

          &:first-child {
            margin-left: 0;
          }
          &:last-child {
            margin-right: 0;
            position: relative;
            // hack 最后一个元素边距
            &::after {
              content: none;
            }
          }
        }
      }
    }

    &.klk-scroll-snap-align--end {
      .klk-scroll-snap_scroller {
        & > * {
          scroll-snap-align: end;

          &:first-child {
            margin-left: 0;
          }
          &:last-child {
            margin-right: 0;
            &::after {
              content: none;
            }
          }
        }
      }
    }

    &.klk-scroll-snap-shadow {
      .klk-scroll-snap_scroller {
        padding-top: 4px;
        padding-bottom: 12px;
        & > * {
          box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.14);
        }
      }
      &.klk-scroll-snap-align--start {
        .klk-scroll-snap_scroller {
          scroll-padding-left: 2px; // 为阴影预约位置
          & > * {
            &:first-child {
              margin-left: 2px;
            }
            &:last-child {
              margin-right: 2px;
              &::after {
                right: -2px;
                top: 0;
                display: block;
                width: 2px;
                height: 2px;
              }
            }
          }
        }
      }

      &.klk-scroll-snap-align--end {
        .klk-scroll-snap_scroller {
          scroll-padding-right: 2px;
          & > * {
            &:first-child {
              margin-left: 2px;
            }
            &:last-child {
              margin-right: 2px;
              &::after {
                right: -2px;
                top: 0;
                display: block;
                width: 2px;
                height: 20px;
              }
            }
          }
        }
      }
    }
  }

  &.klk-scroll-snap--vertical {
    .klk-scroll-snap_scroller {
      height: 100%;
      scroll-snap-type: y mandatory;

      overflow-y: scroll;
      scrollbar-width: none;
      &::-webkit-scrollbar {
        display: none;
      }
      flex-direction: column;

      width: 100%;
      box-sizing: border-box;

      /* Enable Safari touch scrolling physics which is needed for scroll snap */
      -webkit-overflow-scrolling: touch;

      & > * {
        scroll-snap-align: center;
        margin-top: 12px;
        position: relative;
        width: 100%;
        border-radius: 16px;
        scroll-snap-stop: always;
        flex-shrink: 0;

        &:first-child {
          margin-top: 20px;
        }
        &:last-child {
          margin-bottom: 20px;
        }
      }
    }

    &.klk-scroll-snap-align--start {
      .klk-scroll-snap_scroller {
        & > * {
          scroll-snap-align: start;
          &:first-child {
            margin-top: 0;
          }
          &:last-child {
            margin-bottom: 0;
          }
        }
      }
    }

    &.klk-scroll-snap-align--end {
      .klk-scroll-snap_scroller {
        & > * {
          scroll-snap-align: end;
          &:first-child {
            margin-top: 0;
          }
          &:last-child {
            margin-bottom: 0;
          }
        }
      }
    }

    &.klk-scroll-snap-shadow {
      .klk-scroll-snap_scroller {
        padding-left: 4px;
        padding-right: 4px;
        & > * {
          box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.14);
        }
      }
      &.klk-scroll-snap-align--start {
        .klk-scroll-snap_scroller {
          scroll-padding-top: 2px;
          & > * {
            &:first-child {
              margin-top: 2px;
            }
            &:last-child {
              margin-bottom: 2px;
            }
          }
        }
      }

      &.klk-scroll-snap-align--end {
        .klk-scroll-snap_scroller {
          scroll-padding-bottom: 2px;
          & > * {
            &:first-child {
              margin-top: 2px;
            }
            &:last-child {
              margin-bottom: 2px;
            }
          }
        }
      }
    }
  }
}
</style>
