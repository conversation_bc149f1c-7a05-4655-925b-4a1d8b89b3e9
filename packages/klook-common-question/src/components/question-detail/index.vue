<template>
  <component
    :is="contactUsComp"
    v-bind="$attrs"
    :visible="visible"
    @visibleChange="onVisibleChange"
    :platform="platform"></component>
</template>

<script lang="ts">
import {
  Vue,
  Component,
  Prop,
  Watch
} from 'vue-property-decorator'
import KlkIcon from '@klook/klook-ui/lib/icon';

import mobileComp from './mobile.vue';
import desktopComp from './desktop.vue';

@Component({
  name: "KlookRefundDetail",
  components: {
    KlkIcon,
    mobileComp,
    desktopComp
  }
})
export default class KlookRefundDetail extends Vue {
  @Prop({
    default: false
  }) visible!: boolean;

  @Prop({
    default: 'mobile'
  }) platform!: 'mobile' | 'desktop';

  get contactUsComp() {
    return this.platform === 'mobile' ? 'mobileComp' : 'desktopComp'
  }

  onVisibleChange(val: boolean) {
    this.$emit('update:visible', val);
  }
}
</script>

<style lang="scss"></style>
