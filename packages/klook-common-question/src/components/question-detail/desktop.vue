<template>
  <div>
    <klk-drawer class="common-question-wrap" direction="right" :visible="visible" @close="onVisibleChange(false)"
      @open="onVisibleChange(true)">
      <div class="header">
        <klk-icon class="header-btn" @click="onVisibleChange(false)" type="icon_navigation_close_m" color="#212121" size="24"></klk-icon>
      </div>

      <div class="question">
        {{ questionItem.question }}
      </div>

      <div class="answer">
        {{ questionItem.answer }}
      </div>

    </klk-drawer>
  </div>
</template>

<script lang="ts">
import {
  Vue,
  Component,
  Prop,
  Watch
} from 'vue-property-decorator'
import { Icon as KlkIcon } from '@klook/klook-ui';
import { Drawer as KlkDrawer } from '@klook/klook-ui';

@Component({
  name: "KlookRefundDetailDesktop",
  components: {
    KlkIcon,
    KlkDrawer,
  }
})
export default class KlookRefundDetailDesktop extends Vue {
  @Prop({
    default: false
  }) visible!: boolean;

  @Prop({
    default: {}
  })
  questionItem!: any;

  onVisibleChange(val: boolean) {
    this.$emit('visibleChange', val);
  }
}
</script>

<style lang="scss">
.common-question-wrap {

  .klk-drawer-content {
    width: 40%;
    padding: 0 32px;
    min-width: 420px;
    max-width: 750px;
  }

  .header {
    position: sticky;
    top: 0;
    display: flex;
    align-items: center;
    background-color: #fff;
    padding: 20px 0;

    .header-btn {
      cursor: pointer;
    }
  }

  .question {
    font-size: 20px;
    font-weight: 600;
    color: #212121;
    margin-bottom: 16px;
  }
}
</style>
