<template>
  <div>
    <klk-bottom-sheet class="klook-question-wrap" :visible.sync="visible" @close="onVisibleChange(false)"
      @open="onVisibleChange(true)">
      <klk-icon @click="onVisibleChange(false)" slot="header-left" type="icon_navigation_close_m" size="24"
        color="#212121"></klk-icon>

      <div class="question">
        {{ questionItem.question }}
      </div>

      <div class="answer">
        {{ questionItem.answer }}
      </div>
    </klk-bottom-sheet>
  </div>
</template>

<script lang="ts">
import {
  Vue,
  Component,
  Prop,
  Watch
} from 'vue-property-decorator'
import { Icon as KlkIcon } from '@klook/klook-ui';
import { BottomSheet as KlkBottomSheet } from '@klook/klook-ui';

@Component({
  name: "KlookCommonQuestionMobile",
  components: {
    KlkIcon,
    KlkBottomSheet,
  }
})
export default class KlookCommonQuestionMobile extends Vue {
  @Prop({
    default: false
  }) visible!: boolean;

  @Prop({
    default: {}
  })
  questionItem!: any;

  onVisibleChange(val: boolean) {
    this.$emit('visibleChange', val);
  }
}
</script>

<style lang="scss">
.klook-question-wrap {

  .question {
    font-size: 20px;
    font-weight: 600;
    color: #212121;
    margin: 16px 0 12px 0;
  }

  .answer {
    font-size: 16px;
    font-weight: 400;
    color: #212121;
  }
}
</style>
