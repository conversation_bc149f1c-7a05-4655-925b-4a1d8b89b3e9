<template>
  <div :data-spm-module="`CmptCommonQuestn`">
    <div :class="['klook-common-question', `klook-common-question-${platform}`]">
      <div class="klook-common-question-title">{{ __t('109039') }}</div>
      <div class="common-question-container"
        v-galileo-click-tracker="{ spm: 'CmptCommonQuestn.Question', componentName: 'klook-common-question' }"
        :data-spm-item="`Question?ext=${JSON.stringify({ FAQid: item.faq_id })}`"
        :key="index"
        @click="showQuestionDetail(item, index)"
        v-for="(item, index) in questionList"
      >
        <div class="common-question-main">
          <div class="common-question-title">{{ item.question }}</div>
        </div>
        <div class="common-question-right">
          <IconNext theme="outline" size="16" fill="#212121" />
        </div>
      </div>
    </div>

    <question-detail :question-item="question" :platform="platform"
      :visible.sync="questionDetailVisible"></question-detail>
  </div>
</template>

<script lang="ts">
import {
  Vue,
  Component,
  Prop,
  Provide
} from 'vue-property-decorator'
import { Icon as KlkIcon } from '@klook/klook-ui'
import questionDetail from './components/question-detail/index.vue';
import { genText } from "./locale/index";
import messages from "../locales/index.js";
import { IconNext } from '@klook/klook-icons';

@Component({
  name: "KlookCommonQuestion",
  components: {
    KlkIcon,
    questionDetail,
    IconNext
  }
})
export default class KlookCommonQuestion extends Vue {
  // start from here
  @Prop()
  questionList!: Array<any>

  @Prop({
    default: 'mobile'
  })
  platform!: 'mobile' | 'desktop';

  question = {};
  questionDetailVisible = false;


  @Provide() __t: any = this.getTranslate()

  getTranslate() {
    return this.__t;
  }

  beforeCreate(this: any) {
    const locales = messages as any;
    const lang = this.$attrs.language || 'en';
    this.__t = locales[lang]
      ? genText(locales[lang])
      : genText(locales["en"]);
  }

  showQuestionDetail(question: any, index: number) {
    if (question.deeplink) {
      window.open(question.deeplink);
      return
    } else if (question.answer) {
      this.questionDetailVisible = true;
      this.question = question;
    } else {
      this.$emit('question-click', question, index)
    }
  }
}
</script>

<style lang="scss">
@import '~@klook/klook-ui/dist/klook-ui.css';

.klook-common-question {
  border: 1px solid #eee;
  border-radius: 16px;
  padding: 16px;

  &-desktop {
    padding: 20px;

    &.klook-common-question .klook-common-question-title {
      font-size: 24px;
    }
  }

  .common-question-main {
    font-size: 16px;
  }

  .common-question-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    cursor: pointer;
    padding: 12px 0;

    .common-question-right {
      .klk-icon {
        vertical-align: middle;
      }
    }
  }

  .klook-common-question-title {
    font-size: 20px;
    font-weight: 600;
    color: #212121;
  }
}
</style>
