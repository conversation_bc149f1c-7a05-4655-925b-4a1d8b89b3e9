import { Vue } from 'vue-property-decorator'
import wishListPopTip from '../components/wishListPopTip.vue'
import LocalStorageManager from "@klook/klk-traveller-utils/lib/localStorage"

const apis = {
  getFavoriteListData: '/v1/usrcsrv/wishlist/ref_id/list',  // GET 获取收藏列表
  addFavorite: '/v1/usrcsrv/wishlist/favorites/joint/put',  // POST 添加收藏
  cancelFavorite: '/v1/usrcsrv/wishlist/favorites/joint/remove', // POST 取消收藏
}

export default new Vue({
  data() {
    return {
      favoritePopTipShow: false,
      favoriteRes: null,
      favoriteMap: {},
      loading: false
    }
  },
  methods: {
    processArray(arr) {
      const result = {}
      arr.forEach((obj) => {
        const key = `${obj.object_ref_id}`
        result[key] = true
      })
      this.favoriteMap = result
    },

    ajaxPostJSON(url, data) {
      if(window.$axios && window.$axios.$post){
        const post = (window.$axios.$post || window.$axios.post).bind(window.$axios);
        return post(url, data);
      } else {
        return console.error("window.$axios is required!!!");
      }
    },

    ajaxGet(url, data) {
      if(window.$axios && window.$axios.$get){
        const get = (window.$axios.$get || window.$axios.get).bind(window.$axios);
        return get(url, data);
      } else {
        return console.error("window.$axios is required!!!");
      }
    },

    getFavoriteList() {
      if (!this.favoriteRes) {
        this.favoriteRes = new Promise((resolve, reject) => {
          this.ajaxGet(apis.getFavoriteListData)
            .then((res) => {
              this.processArray(res.result?.object_list || [])
              resolve(this.favoriteMap)
            })
            .catch((error) => {
              reject(error);
            });
        });
      }
      return this.favoriteRes
    },

    toggleFavorite(options) {
      // options: {
      //   isFavorite, 是否收藏（当前状态）
      //   favoriteKey,  key
      //   params, 接口入参
      //   wishlistTranslation, 多语言
      //   platform, 平台
      //   relPoptipDom 弹窗吸附节点
      // }
      if(this.loading) { return false }
      const favoriteToggleApi = !options.isFavorite ? apis.addFavorite : apis.cancelFavorite
      this.loading = true
      return this.ajaxPostJSON(favoriteToggleApi, {...options.params})
        .then((result) => {
          this.loading = false
          if(result.success) {
            this.$set(this.favoriteMap, options.favoriteKey, !options.isFavorite)
            if(LocalStorageManager.getItem('klk_wishListEnterClicked') !== 'true' && !this.favoritePopTipShow && !options.isFavorite) {
              this.initPopTip(options)
              this.favoritePopTipShow = true
            }
            this.$emit('favoriteClick', { result, isFavorite: !options.isFavorite, status: 'success' })
          } else {
            if(['05102', '05103'].includes(result.error?.code)) {
              this.$set(this.favoriteMap, options.favoriteKey, !options.isFavorite)
              this.$toast(options.wishlistTranslation.errorCode)
              this.$emit('favoriteClick', { result, isFavorite: !options.isFavorite, status: 'success' })
            } else {
              this.$toast(result.error?.message)
              this.$emit('favoriteClick', { result, isFavorite: options.isFavorite, status: 'fail' })
            }
          }
          return result
        })
        .catch((error) => {
          this.loading = false
          this.$toast(error.message)
          return error
        })
    },

    initPopTip(options) {
      if(!options.relPoptipDom) { return false }
      const favoritePopTipCtor = Vue.extend({
        components:{
          wishListPopTip
        },
        name: 'FavoritePopTipComponent',
        data() {
          return {}
        },
        methods: {
          show() {
            this.$refs.pop.show()
          },
          hide() {
            this.$refs.pop.hide()
          }
        },
        render(h) {
          return h('wishListPopTip', {
            props:{
              ...options
            },
            ref: 'pop'
          })
        }
      })
      const popTipInstance = (new favoritePopTipCtor()).$mount()
      document.body.appendChild(popTipInstance.$el)

      popTipInstance.$nextTick(() => {
        popTipInstance.show()
      })
    }
  }
})
