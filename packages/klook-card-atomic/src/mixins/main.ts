import { Vue, Component, Prop, Provide, Watch } from 'vue-property-decorator'

import KlkSkeleton from '@klook/klook-ui/lib/skeleton/index.js'
import AtomicAspectRatio from '../components/atomicAspectRatio.vue'
import AtomicBasicText from '../components/atomicBasicText.vue'
import AtomicBasicTag from '../components/atomicBasicTag.vue'
import AtomicPriceInfo from '../components/atomicPriceInfo.vue'
import AtomicMinimalRating from '../components/atomicMinimalRating.vue'
import AtomicFavorite from "../components/atomicFavorite.vue"
import {AtomicCardDataTypes, wishlistTranslationTypes} from '../../types/atomicComponents'
import { CardProp } from '../../types/data'
import Favorite from "../utils/favorite";
import openPage from '@klook/klk-traveller-utils/lib/openPage.js'

const cardStyleData: any = {
  // horizontal && vertical 共同
  backgroundColor: {
    modern: 'transparent',
    default: '#ffffff'
  },
  isFlowLayout: {
    modern: 1,
    default: 0
  },
  hoverStyle: {
    modern: 'imageZoomIn',
    default: 'shadow'
  },
  border: {
    modern: 'none',
    default: 'solid 1px #e0e0e0'
  },
  // horizontal 区分
  horizontal: {
    boxRadius: {
      modern: '',
      default: 'radius-xl'
    },
    imgRadius: {
      modern: 'radius-l',
      default: 'radius-l'
    }
  },
  // vertical 区分
  vertical: {
    boxRadius: {
      modern: 'radius-xl',
      default: 'radius-xl'
    },
    imgRadius: {
      modern: 'radius-xl',
      default: null
    }
  }
}

@Component({
  inject: [], // https://github.com/kaorun343/vue-property-decorator/issues/277
  components: {
    KlkSkeleton,
    AtomicAspectRatio,
    AtomicBasicText,
    AtomicBasicTag,
    AtomicPriceInfo,
    AtomicMinimalRating,
    AtomicFavorite
  }
})
export default class Card extends Vue {
  @Prop({ type: String }) size?: CardProp['size']
  @Prop({ type: Object, default: () => {}}) cardData!: AtomicCardDataTypes
  @Prop({ type: Boolean, default: () => true }) isLazy!: boolean
  @Prop({ type: Boolean, default: () => false }) priceTagShow!: boolean
  @Prop({ type: Boolean, default: () => true }) isCrop!: boolean
  @Prop({ type: Boolean, default: () => true }) isShowReviewNumber!: boolean
  @Prop({ type: Boolean, default: () => true }) isShowReviewBooked!: boolean
  @Prop({ type: Boolean, default: () => false }) isLoading!: boolean
  @Prop({ type: Number }) isFlowLayout!: number
  @Prop({ type: Number, default: () => undefined }) webp!: number
  @Prop({ type: [Number, String] }) rank!: number | string
  @Prop({ type: String, default: () => 'default' }) styleType!: CardProp['styleType']
  @Prop({ type: String, default: () => '_blank' }) target!: string
  @Prop({ type: String }) hoverStyle!: CardProp['hoverStyle']
  @Prop({ type: Number, default: () => 1 }) subTextLine!: number
  @Prop({ type: Number, default: () => 2 }) titleLine!: number
  @Prop({ type: String, default: () => 'div' }) titleTag!: string
  @Prop({ type: Number, default: () => 2 }) descLine!: number
  @Prop({ type: String, default: () => '#757575' }) subTextColor!: string
  @Prop({ type: String }) backgroundColor!: string
  @Prop({ type: String }) imgRadius!: CardProp['radius']
  @Prop({ type: Boolean, default: () => false }) isFavoriteShow!: boolean
  @Prop({ type: Boolean, default: () => false }) isFavorite!: boolean
  @Prop({ type: String, default: () => '' }) dataSpmModule!: string
  @Prop({ type: String, default: () => ''}) poptipDom!: string
  @Prop({ type: Object, default: () => null}) wishlistTranslation!: wishlistTranslationTypes
  @Prop({ type: Boolean, default: () => false }) isSeoCard!: boolean // 是否要对卡片按照seo标准
  @Prop({ type: Object, default: ()=>({}) }) imgProps!: any 

  @Provide() cardSize = this.size

  @Watch('size')
  sizeHandle(value: CardProp['size']) {
    this.cardSize = value
  }

  get realDataSpmModule() {
    if(this.dataSpmModule === '') {
      return ''
    } else if(this.isFavoriteShow) {
      const spmArr = this.dataSpmModule.split('?')
      const spmData = new URLSearchParams(spmArr[1])
      let _ext = JSON.parse(spmData.get('ext') as string) || {}
      _ext.favoriteStatus = !!(Favorite.favoriteMap as any)[`act-${this.cardData?.vertical_type}:${this.cardData?.vertical_id}`] || false
      spmData.set('ext', JSON.stringify(_ext))
      return `${spmArr[0]}?${spmData.toString()}`
    } else {
      return this.dataSpmModule
    }
  }

  get cardTag() {
    return this.isSeoCard ? 'div' : 'a' // 对于seo标准卡片，需要去掉卡片最外层的a标签，在卡片的title用a标签包裹
  }

  get cardAttrs() {
    if (!this.isSeoCard) { // 对于非seo标准卡片，a标签卡片需要加上href和target属性
      return {
        href: this.cardData?.deep_link,
        target: this.target
      }
    }

    return {}
  }

  // 根据cardStyle区分style
  getStyleType(s: string, type?: CardProp['cardType']) {
    if (this.$props[s] !== undefined) { return this.$props[s] }
    const data = type ? cardStyleData[type] : cardStyleData[s]
    return type ? data[s] && data[s][this.styleType] : data[this.styleType]
  }

  favoriteClick(e: object) {
    this.$emit('favoriteClick', e)
  }

  cardClick() {
    if (this.isSeoCard && this.cardData?.deep_link) { // 对于seo标准卡片，加上点击事件
      openPage({
        url: this.cardData.deep_link,
        target: this.target
      })
    } 
  }

  get realSize() {
    return this.size || 'medium'
  }
}
