<template>
  <klk-skeleton
    v-if="isLoading"
    :class="[
      'vertical-card',
      `vertical-card-${realSize}`,
      styleType,
      getStyleType('boxRadius', 'vertical'),
    ]"
    :style="{
      backgroundColor: getStyleType('backgroundColor'),
      border: getStyleType('border')
    }"
    animate
  >
    <div
      class="card-img card-img-skeleton"
      :class="getStyleType('imgRadius', 'vertical')"
    >
      <klk-skeleton-block
        :height="0"
        :style="{ paddingBottom: `${(1 / imgRatio) * 100}%` }"
      ></klk-skeleton-block>
    </div>
    <div
      class="card-info"
      :class="[!getStyleType('isFlowLayout') && 'notFlowLayout']"
    >
      <div class="card-info-top">
        <klk-skeleton-block
          class="card-subText"
          :height="18 * subTextLine"
        ></klk-skeleton-block>
        <klk-skeleton-block
          class="card-title"
          :height="20 * titleLine"
        ></klk-skeleton-block>
        <klk-skeleton-block
          class="review"
          style="width: 80%"
          :height="16"
        ></klk-skeleton-block>
        <div class="general-tag">
          <klk-skeleton-block
            class="tag"
            inline
            :width="72"
          ></klk-skeleton-block>
          <klk-skeleton-block
            class="tag"
            inline
            :width="72"
          ></klk-skeleton-block>
          <klk-skeleton-block
            class="tag"
            inline
            :width="72"
          ></klk-skeleton-block>
        </div>
      </div>
      <div class="card-info-bottom">
        <klk-skeleton-block style="width: 70%"></klk-skeleton-block>
      </div>
    </div>
  </klk-skeleton>
  <component
    v-else-if="cardData && !isLoading"
    :is="cardTag"
    :class="[
      'vertical-card',
      `vertical-card-${realSize}`,
      styleType,
      getStyleType('boxRadius', 'vertical'),
      getStyleType('hoverStyle'),
    ]"
    :style="{
      backgroundColor: getStyleType('backgroundColor'),
      border: getStyleType('border')
    }"
    :data-spm-module="realDataSpmModule"
    v-bind="cardAttrs"
    @click="cardClick"
  >
    <div v-if="cardData.cover_url" class="card-img">
      <AtomicAspectRatio
        class="card-image-view"
        :is-lazy="isLazy"
        :webp="webp"
        :img-src="cardData.cover_url"
        :img-ratio="imgRatio"
        :crop-width="cropWidth * 2"
        :crop-height="setCropH * 2"
        :class="getStyleType('imgRadius', 'vertical')"
        :disabled="cardData.sold_out"
        class-name="img"
        :is-img-tag="isSeoCard"
        :img-props="{
          alt: cardData.title,
          ...imgProps
        }"
      >
        <slot>
          <AtomicFavorite
            v-if="isFavoriteShow"
            key="favorite-tag"
            class="favorite-tag"
            purpose="ActivityCard_AddtoWishlist"
            :poptip-dom="poptipDom"
            :favorite-data="{
              activityId: cardData.vertical_id + '',
              activityType: cardData.vertical_type + ''
            }"
            :wishlist-translation="wishlistTranslation"
            @favoriteClick="favoriteClick"
          />
          <div v-if="!isFavoriteShow && cardData.showVideo" key="video" class="video">
            <img
              src="https://res.klook.com/image/upload/v1658980469/bwnro6n3m505nfcvnwht.png"
              alt="video"
            />
          </div>
          <div
            v-if="cardData.promotion_tag && cardData.promotion_tag.length"
            key="promotion_tag"
            class="promotion-tag"
          >
            <AtomicBasicTag
              v-for="(item, index) in cardData.promotion_tag || []"
              :key="index"
              :data="item"
              class="tag"
              size="small"
            />
          </div>
          <klk-corner-label v-if="rank" class="rank" key="video" type="rank">{{
            rank
          }}</klk-corner-label>
          <div class="image-bottom-slot">
            <slot name="image-bottom-slot"></slot>
          </div>
        </slot>
      </AtomicAspectRatio>
    </div>
    <div
      class="card-info"
      :class="[!getStyleType('isFlowLayout') && 'notFlowLayout']"
    >
      <div class="card-info-top">
        <AtomicBasicText
          :class-name="['card-subText']"
          :text="cardData.sub_title || ''"
          :line="subTextLine"
          :style="{ color: subTextColor }"
        />
        <AtomicBasicText
          :class-name="['card-title']"
          :text="cardData.title || ''"
          :webp="webp"
          :icon="cardData.title_image"
          :icon-height="18"
          :line="titleLine"
          :tag="titleTag"
          :href-link="isSeoCard ? cardData.deep_link : ''"
        />
        <div
          v-if="cardData.general_tag && cardData.general_tag.length"
          class="general-tag"
        >
          <AtomicBasicTag
            v-for="(item, index) in cardData.general_tag || []"
            :key="index"
            :data="item"
            class="tag"
            size="small"
          />
        </div>
        <AtomicMinimalRating
          class="review"
          :data="cardData.review"
          :show-number="isShowReviewNumber"
          :show-booked="isShowReviewBooked"
        />
        <div v-if="cardData.description" class="description">
          <AtomicBasicText :text="cardData.description || ''" :line="descLine" />
        </div>
        <slot name="content-product-slot"></slot>
      </div>
      <div class="card-info-bottom">
        <AtomicPriceInfo :data="cardData.price" :price-tag-show="priceTagShow" />
        <div
          v-if="!cardData.sold_out && cardData.promo_tag_list && cardData.promo_tag_list.length > 0"
          class="card-price-tag-list"
        >
          <AtomicBasicTag
            v-for="(item, index) in cardData.promo_tag_list || []"
            :key="index"
            :data="item"
            class="tag"
            size="small"
          />
        </div>
        <slot name="content-price-slot"></slot>
      </div>
    </div>
  </component>
</template>

<script lang="ts">
import { Component, Mixins, Prop } from 'vue-property-decorator'
import Main from '../mixins/main'

@Component
export default class VerticalCard extends Mixins(Main) {
  @Prop({ type: Number, default: () => 3 / 2 }) imgRatio!: number
  @Prop({ type: Number }) cropWidth!: number

  get setCropH() {
    if (!this.isCrop) { return null }
    const num = Math.round(this.cropWidth / this.imgRatio)
    return (num + '') as string
  }
}
</script>

<style lang="scss" scoped>
.imageZoomIn {
  &:hover {
    .card-img {
      ::v-deep.img {
        transform: scale(1.2);
      }
    }
  }
}
@media (min-width: 600px) {
  .shadow {
    transition: box-shadow $motion-duration-m $motion-timing-ease,
    transform $motion-duration-m $motion-timing-ease;

    &:hover {
      transform: translateY(-4px);
      box-shadow: $shadow-hover-1;
      z-index: 2;
    }
  }
}

.radius-xl {
  border-radius: $radius-xl;
}

.vertical-card {
  overflow: hidden;
  display: flex;
  flex-wrap: wrap;
  flex-direction: column;
  -webkit-backface-visibility: hidden;
  -webkit-transform: translate3d(0, 0, 0);
  -webkit-mask-image: -webkit-radial-gradient(white, black);
  cursor: pointer;

  .rank {
    position: absolute;
    top: 0;
    left: 0;
  }

  .card-img {
    ::v-deep.img {
      transition: transform $motion-duration-l $motion-timing-ease;
    }
  }

  .favorite-tag {
    position: absolute;
    z-index: 99;
    width: 24px;
    height: 24px;
  }

  .video {
    position: absolute;
    right: 16px;
    top: 12px;
    width: 24px;
    img {
      background: none;
    }
  }

  .image-bottom-slot {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
  }

  &:hover {
    text-decoration: none;
  }

  .card-img {
    flex-shrink: 0;
    width: 100%;
    overflow: hidden;

    &-skeleton {
      background-color: $color-border-normal;
    }

    .card-image-view {
      transform: rotate(0deg);
      -webkit-transform: rotate(0deg);
      overflow: hidden;
    }
  }

  .card-info {
    display: flex;
    flex-direction: column;
    width: 100%;
    &.notFlowLayout {
      flex: 1;
      justify-content: space-between;
    }
    .card-subText {
      word-break: break-word;
      color: $color-text-secondary;
    }

    .card-title {
      word-break: break-word;
      color: $color-text-primary;
    }

    .description {
      word-break: break-word;
      padding: 4px;
      margin-top: 6px;
      color: $color-text-secondary;
      background-color: $color-bg-3;
      border-radius: 2px;
    }

    .card-price-tag-list {
      box-sizing: content-box;
      display: flex;
      margin-top: 2px;
      max-height: 20px;
      overflow: hidden;
      flex-wrap: wrap;

      .tag:not(:last-child ) {
        margin-right: 4px;
      }
    }
  }

  .review {
    margin-bottom: 4px;
  }

  .general-tag {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 4px;
    margin-top: 2px;
    overflow: hidden;
    height: 20px;
    font-size: 0;
    .tag {
      margin-right: 4px;
    }
  }

  .promotion-tag {
    display: flex;
    flex-wrap: wrap;
    overflow: hidden;
    height: 20px;
    position: absolute;
    width: 100%;
    top: 12px;
    padding: 0 48px 0 12px;

    .tag {
      margin-right: 8px;
    }
  }

  &-medium {
    .card-info {
      padding: 12px 0 16px 0;
    }
    &.default {
      .card-info {
        padding: 8px 16px 16px 16px;
      }
      .card-info-bottom {
        margin-top: 8px;
      }
    }

    .favorite-tag {
      top: 12px;
      right: 12px;
    }

    &.modern {
      .card-info-bottom {
        margin-top: 4px;
      }
    }

    .card-title {
      @include font-body-m-bold();
      line-height: 21px;
      margin-bottom: 4px;
    }
    .card-subText {
      @include font-body-s-regular();
      line-height: 18px;
      margin-bottom: 4px;
    }

    .description {
      @include font-caption-m-regular;
      line-height: 16px;
    }

    .card-price-tag-list {
      margin-bottom: -4px;
      max-height: 48px !important;
      .tag {
        margin-bottom: 4px;
      }
    }
  }

  &-small {
    .card-info {
      padding: 8px 0 12px 0;
    }
    &.default {
      .card-info {
        padding: 8px 12px 12px 12px;
      }
      .card-info-bottom {
        margin-top: 8px;
      }
    }

    .favorite-tag {
      top: 12px;
      right: 12px;
    }

    &.modern {
      .card-info-bottom {
        margin-top: 4px;
      }
    }
    .card-title {
      @include font-body-s-bold();
      margin-bottom: 4px;
      line-height: 18px;
    }
    .card-subText {
      @include font-caption-m-regular();
      line-height: 16px;
      margin-bottom: 4px;
    }
    .description {
      @include font-caption-m-regular();
      line-height: 16px;
    }
    .promotion-tag {
      padding-left: 12px;
    }
  }
}
</style>
