<template>
  <klk-skeleton
    v-if="isLoading"
    :class="[
      'horizontal-card',
      `horizontal-card-${realSize}`,
      styleType,
      `hz-${hzPosition}`,
      getStyleType('boxRadius', 'horizontal'),
    ]"
    :style="{
      backgroundColor: getStyleType('backgroundColor'),
      border: getStyleType('border')
    }"
    animate
  >
    <div
      class="card-img"
      :class="getStyleType('imgRadius', 'horizontal')"
      :style="{ width: getImageWidth + 'px' }"
    >
      <klk-skeleton-block
        :height="0"
        :style="{ paddingBottom: `${(1 / imgRatio) * 100}%` }"
      ></klk-skeleton-block>
    </div>
    <div
      class="card-info"
      :class="[!getStyleType('isFlowLayout') && 'notFlowLayout']"
    >
      <div class="card-info-top">
        <klk-skeleton-block
          class="card-subText"
          :height="18 * subTextLine"
        ></klk-skeleton-block>
        <klk-skeleton-block
          class="card-title"
          :height="20 * titleLine"
        ></klk-skeleton-block>
        <klk-skeleton-block
          class="review"
          style="width: 80%"
          :height="16"
        ></klk-skeleton-block>
        <div class="general-tag">
          <klk-skeleton-block
            class="tag"
            inline
            :width="72"
          ></klk-skeleton-block>
          <klk-skeleton-block
            class="tag"
            inline
            :width="72"
          ></klk-skeleton-block>
          <klk-skeleton-block
            class="tag"
            inline
            :width="72"
          ></klk-skeleton-block>
        </div>
      </div>
      <div class="card-price-bottom">
        <klk-skeleton-block style="width: 70%"></klk-skeleton-block>
      </div>
    </div>
  </klk-skeleton>
  <component
    v-else-if="cardData && !isLoading"
    :is="cardTag"
    :class="[
      'horizontal-card',
      `horizontal-card-${realSize}`,
      styleType,
      `hz-${hzPosition}`,
      getStyleType('boxRadius', 'horizontal'),
      getStyleType('hoverStyle'),
    ]"
    :style="{
      backgroundColor: getStyleType('backgroundColor'),
      border: getStyleType('border')
    }"
    :data-spm-module="realDataSpmModule"
    v-bind="cardAttrs"
    @click="cardClick"
  >
    <div
      v-if="cardData.cover_url"
      class="card-img"
      :style="{ width: getImageWidth + 'px' }"
    >
      <AtomicAspectRatio
        :class="getStyleType('imgRadius', 'horizontal')"
        class="card-image-view"
        :is-lazy="isLazy"
        :webp="webp"
        :img-src="cardData.cover_url"
        :img-ratio="imgRatio"
        :crop-width="setCropW"
        :crop-height="setCropH"
        :disabled="cardData.sold_out"
        class-name="img"
        :is-img-tag="isSeoCard"
        :img-props="{
          alt: cardData.title,
          ...imgProps
        }"
      >
        <slot>
          <AtomicFavorite
            v-if="isFavoriteShow"
            key="favorite-tag"
            class="favorite-tag"
            :favorite-data="{
              activityId: cardData.vertical_id + '',
              activityType: cardData.vertical_type + ''
            }"
            :poptip-dom="poptipDom"
            :wishlist-translation="wishlistTranslation"
            purpose="ActivityCard_AddtoWishlist"
            @favoriteClick="favoriteClick"
          />
          <div v-else-if="cardData.showVideo" class="video">
            <img
              src="https://res.klook.com/image/upload/v1658980469/bwnro6n3m505nfcvnwht.png"
              alt="video"
            />
          </div>
          <klk-corner-label v-if="rank" class="rank" type="rank">{{
            rank
          }}</klk-corner-label>
          <div class="image-bottom-slot">
            <slot name="image-bottom-slot"></slot>
          </div>
        </slot>
      </AtomicAspectRatio>
    </div>
    <div
      class="card-info"
      :class="[!getStyleType('isFlowLayout') && 'notFlowLayout']"
    >
      <div class="card-info-top">
        <div
          v-if="cardData.promotion_tag && cardData.promotion_tag.length"
          class="promotion-tag"
        >
          <AtomicBasicTag
            v-for="(item, index) in cardData.promotion_tag || []"
            :key="index"
            :data="item"
            class="tag"
            size="small"
          />
        </div>
        <AtomicBasicText
          :class-name="['card-subText']"
          :text="cardData.sub_title || ''"
          :line="subTextLine"
          :style="{ color: subTextColor }"
        />
        <AtomicBasicText
          :class-name="['card-title']"
          :text="cardData.title || ''"
          :webp="webp"
          :icon="cardData.title_image"
          :icon-height="18"
          :line="titleLine"
          :tag="titleTag"
          :href-link="isSeoCard ? cardData.deep_link : ''"
        />
        <div
          v-if="cardData.general_tag && cardData.general_tag.length"
          class="general-tag"
        >
          <AtomicBasicTag
            v-for="(item, index) in cardData.general_tag || []"
            :key="index"
            :data="item"
            class="tag"
            size="small"
          />
        </div>
        <AtomicMinimalRating
          class="review"
          :data="cardData.review"
          :show-number="isShowReviewNumber"
          :show-booked="isShowReviewBooked"
        />
        <div v-if="cardData.description" class="description">
          <AtomicBasicText :text="cardData.description || ''" :line="descLine" />
        </div>
        <slot name="content-product-slot"></slot>
      </div>
      <div class="card-price-bottom">
        <div class="card-price">
          <div class="card-price-content">
            <AtomicPriceInfo :data="cardData.price" :price-tag-show="priceTagShow" />
            <div
              v-if="!cardData.sold_out && cardData.promo_tag_list && cardData.promo_tag_list.length > 0"
              class="card-price-tag-list"
            >
              <AtomicBasicTag
                v-for="(item, index) in cardData.promo_tag_list || []"
                :key="index"
                :data="item"
                class="tag"
                size="small"
              />
            </div>
          </div>
          <slot name="content-price-right-slot"></slot>
        </div>

        <slot name="content-price-slot"></slot>
      </div>
    </div>
  </component>
</template>

<script lang="ts">
import { Component, Prop } from 'vue-property-decorator'
import Main from '../mixins/main'
import { CardProp } from '../../types/data'

@Component
export default class HorizontalCard extends Main {
  @Prop({ type: Number, default: () => 1 }) imgRatio!: number
  @Prop({ type: Number }) imageWidth!: number
  @Prop({ type: Number }) cropWidth!: number
  @Prop({ type: String, default: () => 'default' }) hzPosition!: CardProp['hzPosition']

  get getImageWidth() {
    if (this.imageWidth) { return this.imageWidth }
    if (this.realSize === 'small') {
      return '96'
    }
    return '164'
  }

  get setCropW() {
    if (!this.isCrop) { return null }
    if (!this.cropWidth) {
      if (this.realSize === 'small') {
        return 200
      }
      return 300
    }
    return this.cropWidth * 2
  }

  get setCropH() {
    if (!this.setCropW) { return null }
    return Math.round(this.setCropW / this.imgRatio)
  }
}
</script>

<style lang="scss" scoped>
.imageZoomIn {
  &:hover {
    .card-img {
      ::v-deep.img {
        transform: scale(1.2);
      }
    }
  }
}

@media (min-width: 600px) {
  .shadow {
    transition: box-shadow $motion-duration-m $motion-timing-ease,

    transform $motion-duration-m $motion-timing-ease;

    &:hover {
      transform: translateY(-4px);
      box-shadow: $shadow-hover-1;
      z-index: 2;
    }
  }
}
.radius-l {
  border-radius: $radius-l;
}

.radius-xl {
  border-radius: $radius-xl;
}

.horizontal-card {
  display: flex;
  overflow: hidden;
  -webkit-backface-visibility: hidden;
  -webkit-transform: translate3d(0, 0, 0);
  -webkit-mask-image: -webkit-radial-gradient(white, black);
  cursor: pointer;

  .card-price-bottom {
    margin-top: 8px;
  }

  .rank {
    position: absolute;
    top: 0;
    left: 0;
  }
  .card-img {
    ::v-deep.img {
      transition: transform $motion-duration-l $motion-timing-ease;
    }
  }

  .favorite-tag {
    position: absolute;
    z-index: 99;
    width: 24px;
    height: 24px;
  }

  .video {
    position: absolute;
    right: 16px;
    top: 12px;
    width: 24px;
    img {
      background: none;
    }
  }

  .image-bottom-slot {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
  }

  &.default {
    padding: 16px;

    &.horizontal-card-small {
      padding: 12px;
    }
  }

  &:hover {
    text-decoration: none;
  }

  .card-img {
    flex-shrink: 0;
    overflow: hidden;
    .card-image-view {
      overflow: hidden;
    }
  }

  .card-info {
    display: flex;
    flex-direction: column;
    flex: 1;
    min-width: 0;

    &.notFlowLayout {
      justify-content: space-between;
    }

    .card-title {
      word-break: break-word;
      color: $color-text-primary;
      margin-bottom: 4px;
    }
    .card-subText {
      word-break: break-word;
      color: $color-text-secondary;
      margin-bottom: 4px;
    }

    .description {
      word-break: break-word;
      padding: 4px;
      margin-top: 6px;
      color: $color-text-secondary;
      background-color: $color-bg-3;
      border-radius: 2px;
    }
  }

  .review {
    margin-bottom: 4px;
  }
  .general-tag {
    display: flex;
    flex-wrap: wrap;
    height: 20px;
    margin-bottom: 4px;
    margin-top: 2px;
    overflow: hidden;
    font-size: 0;
    .tag {
      display: inline-block;

      margin-right: 4px;
    }
  }
  .promotion-tag {
    display: flex;
    flex-wrap: wrap;
    width: 100%;
    height: 20px;
    overflow: hidden;
    margin-bottom: 8px;

    .tag {
      margin-right: 8px;
    }
  }

  .card-price {
    display: flex;

    &-content {
      width: 100%;
    }
  }

  .card-price-tag-list {
    box-sizing: content-box;
    display: flex;
    margin-top: 2px;
    max-height: 20px;
    overflow: hidden;
    flex-wrap: wrap;

    .tag:not(:last-child) {
      margin-right: 4px;
    }
  }

  &-medium {
    &.hz-default {
      .card-img {
        margin-right: 16px;
        margin-left: 0;
      }
    }

    &.hz-flipped {
      .card-img {
        margin-left: 20px;
        margin-right: 0;
      }
    }

    .favorite-tag {
      top: 12px;
      right: 12px;
    }

    .card-title {
      @include font-body-m-bold();
      line-height: 21px;
    }
    .card-subText {
      @include font-body-s-regular();
      line-height: 18px;
    }

    .description {
      @include font-caption-m-regular;
      line-height: 16px;
    }

    .card-price-tag-list {
      margin-bottom: -4px;
      max-height: 48px !important;
      .tag {
        margin-bottom: 4px;
      }
    }
  }

  &-small {
    &.hz-default {
      .card-img {
        margin-right: 12px;
        margin-left: 0;
      }
    }

    .favorite-tag {
      top: 8px;
      right: 8px;
    }

    &.hz-flipped {
      .card-img {
        margin-left: 12px;
        margin-right: 0;
      }
    }

    .card-title {
      @include font-body-s-bold();
      line-height: 18px;
    }
    .card-subText {
      @include font-caption-m-regular();
      line-height: 16px
    }

    .description {
      @include font-caption-m-regular();
      line-height: 16px;
    }
  }

  &.modern {
      .card-price-bottom {
        margin-top: 4px;
      }
    }

  &.hz-flipped {
    .card-img {
      order: 1;
    }
  }
}
</style>
