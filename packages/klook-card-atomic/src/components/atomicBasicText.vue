<template>
  <component v-if="text" :is="tag" :style="textStyle" :class="className">
    <img
      v-if="icon"
      :style="`height: ${iconHeight}px; margin-right: 2px; vertical-align: middle;`"
      :src="transformImageUrl(icon)"
      :alt="icon"
    />
    <!-- 对于seo标准卡片，需要对卡片的title用a标签包裹 -->
    <a v-if="hrefLink" :href="hrefLink" @click.prevent>{{ text }}</a>
    <template v-else>{{ text }}</template>
  </component>
</template>

<script lang="ts">
  import { Vue, Prop, Component } from 'vue-property-decorator'
  import transformImageUrl from '@klook/klk-traveller-utils/lib/transformImageUrl';

  @Component
  export default class AtomicBasicText extends Vue {
    @Prop({ type: String, default: () => '', required: true }) text!: string
    @Prop({ type: String, default: () => '' }) icon!: string
    @Prop({ type: [String, Number], default: 24 }) iconHeight!: Number
    @Prop({ type: [Number, String], default: () => 0 }) line!: number | string
    @Prop({ type: String, default: () => 'div' }) tag!: string
    @Prop({ type: Array, default: () => [] }) className!: string[]
    @Prop({ type: Number, default: undefined }) webp!: number
    @Prop({ type: String, default: () => '' }) hrefLink!: string
    declare $store: any;

    get realWebp() {
      if (this.webp !== undefined) {
        return this.webp
      }

      return this.$store?.state?.klook?.webp || 0
    }

    transformImageUrl(url: string){
      return transformImageUrl(url, { webp: !!this.realWebp }) || ''
    }

    get textStyle(){
      return `overflow: hidden; text-overflow: ellipsis; display: -webkit-box; -webkit-box-orient: vertical; -webkit-box-align: start; -webkit-line-clamp: ${this.line};`;
    }
  }
</script>
