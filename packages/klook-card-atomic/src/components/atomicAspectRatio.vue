<template>
  <div
    v-if="imgSrc"
    class="image-view"
    :class="{
      'image-view-hydrated': hydrated,
      'is-img-tag': isImgTag,
    }"
    :style="{
      width: imgWidth,
      height: imgHeight,
      paddingBottom: isImgTag ? aspectRatio : 'unset',
    }"
  >
    <template v-if="isImgTag">
      <img
        v-if="isLazy"
        class="original-tag-image"
        :class="{
          'original-tag-image-lazy': isLazy,
        }"
        v-lazy="formatPicUrl(imgSrc)"
        v-bind="{
          ...imgProps,
        }"
      />
      <img
        v-else
        class="original-tag-image"
        :src="formatPicUrl(imgSrc)"
        v-bind="{
          ...imgProps,
        }"
      />
    </template>
    <template v-else>
      <div
        v-if="isLazy"
        v-lazy:background-image.container="formatPicUrl(imgSrc)"
        class="image-box-lazy"
        :class="className"
        :style="{
          'padding-bottom': aspectRatio,
          backgroundSize: bgSize,
        }"
      />
      <div
        v-else
        class="image-box"
        :class="className"
        :style="{
          'padding-bottom': aspectRatio,
          backgroundSize: bgSize,
          backgroundImage: `url(${formatPicUrl(imgSrc)})`,
        }"
      />
    </template>
    <slot class="slot-section"></slot>
    <div v-if="disabled" class="mask"></div>
  </div>
</template>

<script lang="ts">
import { Vue, Prop, Component } from "vue-property-decorator";
import transformImageUrl from "@klook/klk-traveller-utils/lib/transformImageUrl";

@Component
export default class AtomicAspectRatio extends Vue {
  @Prop({ type: Boolean, default: () => true }) isLazy!: boolean;
  @Prop({ type: String, default: () => "", required: true }) imgSrc!: string;
  @Prop({ type: Number, default: undefined }) webp!: number;
  @Prop({ type: String, default: () => "100%" }) imgWidth!: string;
  @Prop({ type: String, default: () => "auto" }) imgHeight!: string;
  @Prop({ type: Number, default: 0 }) imgRatio!: number;
  @Prop({ type: String, default: "cover" }) bgSize!: string;
  @Prop({ type: Number, default: 0 }) cropWidth!: number;
  @Prop({ type: Number, default: 0 }) cropHeight!: number;
  @Prop({ type: String, default: "" }) className!: string;
  @Prop({ type: Boolean, default: false }) disabled!: boolean;
  @Prop({ type: Boolean, default: false }) isImgTag!: boolean;
  @Prop({ type: Object, default: () => ({}) }) imgProps!: any;
  $store: any;

  hydrated = false;

  mounted() {
    this.hydrated = true;
  }

  get realWebp() {
    if (this.webp !== undefined) {
      return this.webp;
    }

    return this.$store?.state?.klook?.webp || 0;
  }

  get aspectRatio() {
    if (this.imgRatio === 0) {
      return "unset";
    }
    return (1 / Number(this.imgRatio)) * 100 + "%";
  }

  formatPicUrl(url: string) {
    if (this.cropWidth && this.cropHeight) {
      return transformImageUrl(url, {
        width: this.cropWidth,
        height: this.cropHeight,
        webp: this.realWebp,
      });
    } else {
      return url;
    }
  }
}
</script>

<style lang="scss" scoped>
$loadingImage: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAQSURBVHgBAQUA+v8A9fX1/wmhA9+DscNoAAAAAElFTkSuQmCC";

.image-view {
  position: relative;

  .original-tag-image {
    position: absolute;
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .original-tag-image-lazy {
    visibility: hidden;
  }

  .image-box {
    width: 100%;
    height: 100%;
    background-position: center center;
    background-repeat: no-repeat;
  }

  .slot-section {
    position: absolute;
    z-index: 1;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
  }

  .mask {
    position: absolute;
    z-index: 2;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: $color-bg-widget-normal;
    opacity: 0.6;
  }
}

.is-img-tag {
  background: url($loadingImage);

  &.image-view-hydrated {
    .original-tag-image {
      visibility: visible;
    }
  }
}
</style>
