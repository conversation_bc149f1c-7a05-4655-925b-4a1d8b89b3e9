<template>
  <div
    v-if="data"
    class="atomic-price-info"
    :class="`atomic-price-info-size-${realSize}`"
  >
    <div
      v-if="data.selling_price"
      class="price-content"
    >
      <div
        class="sell-price"
        v-html="getPrice(data.selling_price, data.selling_price_format)"
      />
      <div
        v-if="data.save_price"
        class="save-price"
      >
        {{ data.save_price }}
      </div>
      <div
        v-else-if="data.market_price"
        class="market-price"
        :class="{'market-strike': !data.market_price_no_strike }"
      >
        {{ data.market_price }}
      </div>
    </div>
    <AtomicBasicTag
      v-if="data.discount_tag && priceTagShow"
      :data="data.discount_tag"
      class="discount-tag-content"
      size="small"
    />
  </div>
</template>

<script lang="ts">
import { Prop, Component } from 'vue-property-decorator'
  import SizeMixin from "../mixins/size"
  import AtomicBasicTag from './atomicBasicTag.vue'

  import { AtomicPriceInfoTypes } from '../../types/atomicComponents'

  @Component({
    components: {
      AtomicBasicTag
    }
  })
  export default class AtomicPriceInfo extends SizeMixin {
    @Prop({ type: Object, default: () => null }) data!: AtomicPriceInfoTypes
    @Prop({ type: Boolean, default: () => false }) highLightPrice!: boolean
    @Prop({ type: Boolean, default: () => true }) priceTagShow!: boolean

    getPrice(priceNumber: string, priceText: string) {
      if (!(priceNumber && priceText)) { return '' }
      let price = ''
      const priceMatch = priceText.match(/\{[^}]+\}/)
      if (priceMatch && priceMatch[0]) {
        price = priceText.replace(
          priceMatch[0],
          `<span class="price-number ${this.highLightPrice ? 'high-light-price' : ''}">
            ${priceNumber}
          </span>`
        )
      }

      return price
    }
  }
</script>

<style lang="scss">
  .high-light-price {
    color: $color-brand-primary;
  }

  /*size=small 样式*/
  .atomic-price-info-size-small {
    .market-price {
      @include font-body-s-regular();
    }

    .save-price {
      @include font-caption-m-semibold();
    }

    .sell-price {
      margin-right: 4px;
      @include font-body-s-bold();
      .price-number {
        word-break: break-word;
        font-weight: $fontWeight-bold;
      }
    }
  }

  /*size=medium 样式*/
  .atomic-price-info-size-medium {
    .market-price {
      @include font-body-m-regular();
    }

    .save-price {
      @include font-body-s-semibold();
    }

    .sell-price {
      margin-right: 4px;
      @include font-body-m-bold();
      font-weight: $fontWeight-bold;

      .price-number {
        word-break: break-word;
        font-weight: $fontWeight-bold;
      }
    }
  }

  /*size=large 样式*/
  .atomic-price-info-size-large {
    .market-price {
      @include font-body-m-regular();
    }

    .save-price {
      @include font-body-m-semibold();
    }

    .sell-price {
      margin-right: 8px;
      @include font-heading-s ;
      .price-number {
        word-break: break-word;
        font-weight: $fontWeight-bold;
      }
    }
  }
</style>

<style lang="scss" scoped>
  /*size=medium 样式*/
  .atomic-price-info {
    display: inline-block;

    .price-content {
      display: flex;
      flex-wrap: wrap;
      align-items: baseline;

      .sell-price {
        color: $color-text-primary;
      }

      .market-price {
        color: $color-text-disabled;
      }

      .save-price {
        color: $color-brand-primary;
      }

      .market-strike {
        text-decoration: line-through;
      }
    }
    .discount-tag-content {
      display: inline-block;
      margin-top: 4px;
    }
  }
</style>
