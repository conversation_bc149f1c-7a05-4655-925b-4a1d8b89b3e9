<template>
  <div
    v-if="data"
    class="atomic-minimal-rating"
    :class="`atomic-minimal-rating-size-${realSize}`"
  >
    <span class="review-star">{{ data.star }}</span>
    <span
      v-if="showNumber && data.number"
      class="review-number"
      :class="{'review-number-src': showUnderLine}"
      :data-spm-item="propSpmItem"
      v-galileo-click-tracker="{ spm: propSpmItem, componentName: 'klook-card', autoTrackSpm: true }"
      @click.stop="handleClick"
    >
      {{ data.number }}
    </span>
    <span
      v-if="showBooked && data.booked"
      class="review-booked"
    >
      {{ data.dot }}{{ data.booked }}
    </span>
  </div>
</template>

<script lang="ts">
  import {Prop, Component} from 'vue-property-decorator'
  import SizeMixin from "../mixins/size"
  import {AtomicMinimalRatingTypes} from '../../types/atomicComponents'

  @Component({})
  export default class AtomicMinimalRating extends SizeMixin {
    @Prop({ type: Object, default: () => null }) data!: AtomicMinimalRatingTypes
    @Prop({ type: Boolean, default: () => true }) showNumber?: boolean
    @Prop({ type: Boolean, default: () => true }) showBooked?: boolean
    @Prop({ type: Boolean, default: () => false }) showUnderLine?: boolean
    @Prop({ type: String, default: () => 'MoreReviews' }) propSpmItem?: string
    @Prop({ type: Function, default: () => null }) handlePropClick?: Function


    handleClick() {
      if(this.handlePropClick) {
        this.handlePropClick()
      } else if(this.data.deep_link) {
        window.location.href = this.data.deep_link
      }
    }
  }
</script>

<style lang="scss" scoped>
  .atomic-minimal-rating {
    @include font-body-s-regular();

    color: $color-text-secondary;

    .review-star {
      @include font-body-s-bold();

      color: $color-accent-9;
    }

    .review-number-src {
      text-decoration: underline;

      &:hover {
        cursor: pointer;
      }
    }
  }

  /*size=small 样式*/
  .atomic-minimal-rating-size-small {
    @include font-caption-m-regular();

    .review-star {
      @include font-caption-1();
    }
  }

  /*size=large 样式*/
  .atomic-minimal-rating-size-large {
    @include font-body-m-regular();

    .review-star {
      @include font-body-m-semibold();
    }
  }
</style>
