<template v-if="data && data.text">
  <!--有特殊tag请统一加在atomicSpecialTag内-->
  <AtomicSpecialTag
    v-if="isSpecialTag(data.type)"
    :data="data"
    :size="realSize"
  />

  <div
    v-else
    class="atomic-basic-tag"
    :style="setStyle"
  >
    <img class="tag-icon" v-if="data.icon_src" :src="data.icon_src" alt="">
    <div class="tag-content">{{ data.text }}</div>
  </div>
</template>

<script lang="ts">
  import { Prop, Component } from 'vue-property-decorator'
  import { AtomicBasicTagTypes } from "../../types/atomicComponents"
  import SizeMixin from "../mixins/size"
  import AtomicSpecialTag from './atomicSpecialTag.vue'

  @Component({
    components: {
      AtomicSpecialTag
    }
  })
  export default class AtomicBasicTag extends SizeMixin {
    @Prop({ type: Object, default: () => null }) data!: AtomicBasicTagTypes;

    isSpecialTag(type?: string) {
      return type && ['reward', 'platform_promo_code_tag', 'vertical_promo_tag', 'manual_tag'].includes(type)
    }

    get setStyle() {
      const height = this.realSize === 'medium' ? '24px' : '20px'
      const { background_color_left, background_color_right, bg_color, line_color, text_color } = this.data
      return {
        height,
        lineHeight: height,
        border: line_color ? `1px solid ${line_color}` : 'none',
        color: text_color,
        fontSize: this.realSize === 'medium' ? '14px' : '12px',
        background: background_color_left && background_color_right &&
          background_color_left !== background_color_right
            ? `linear-gradient(to right, ${background_color_left}, ${background_color_right})`
            : bg_color || background_color_left,
      }
    }
  }
</script>

<style lang="scss" scoped>
.atomic-basic-tag {
  display: inline-flex;
  align-items: center;
  border-radius: 2px;
  padding: 0 4px;
  max-width: 100%;

  .tag-icon {
    height: calc(1em + 2px);
    margin-right: 4px;
    background: none;
  }

  .tag-content {
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    text-overflow: ellipsis;
    overflow: hidden;
  }
}
</style>
