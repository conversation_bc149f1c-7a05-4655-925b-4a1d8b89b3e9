<template>
  <div
    v-if="favoriteData"
    :class="`favorite-tag-${realSize}`"
    :data-spm-item="`AddToFavorite?mod=stop&ext=${JSON.stringify({'ClickType': isFavorite ? 'cut' : 'add'})}`"
    v-galileo-click-tracker="{ spm: 'AddToFavorite', componentName: 'klook-card', autoTrackSpm: true }"
    @click.prevent.stop="handleFavoriteClick"
  >
    <svg
      v-show="!isFavorite"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M11.9979 21.5C6.83695 18.5141 1.56054 14.3057 1.50051 8.7407C1.50017 8.7146 1.5 8.6884 1.5 8.6622L1.50001 8.6551L1.5 8.6448C1.50912 5.52565 3.96624 3 6.9959 3C9.21955 3 11.1348 4.36055 12 6.31775C12.8652 4.36055 14.7805 3 17.0041 3C20.0394 3 22.5 5.53505 22.5 8.6622C22.5 8.756 22.4978 8.8492 22.4934 8.94185H22.4959C22.4959 14.4555 17.1884 18.5894 11.9979 21.5Z"
        fill="black"
        fill-opacity="0.38"
        stroke="white"
        stroke-width="1.8"
        stroke-linejoin="round"
      />
    </svg>
    <svg
      v-show="isFavorite"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M11.9979 21.5C6.83695 18.5141 1.56054 14.3057 1.50051 8.7407C1.50017 8.7146 1.5 8.6884 1.5 8.6622L1.50001 8.6551L1.5 8.6448C1.50912 5.52565 3.96624 3 6.9959 3C9.21955 3 11.1348 4.36055 12 6.31775C12.8652 4.36055 14.7805 3 17.0041 3C20.0394 3 22.5 5.53505 22.5 8.6622C22.5 8.756 22.4978 8.8492 22.4934 8.94185H22.4959C22.4959 14.4555 17.1884 18.5894 11.9979 21.5Z"
        fill="#FF5B00"
        stroke="white"
        stroke-width="1.8"
        stroke-linejoin="round"
      />
    </svg>
  </div>
</template>

<script lang="ts">
import {Prop, Component, Vue} from 'vue-property-decorator'
import SizeMixin from "../mixins/size"
import {AtomicFavoriteTypes, wishlistTranslationTypes} from '../../types/atomicComponents'
import Favorite from "../utils/favorite";


@Component({})
export default class AtomicFavorite extends SizeMixin {
  @Prop({ type: Object, default: () => null}) favoriteData!: AtomicFavoriteTypes
  @Prop({ type: String, default: () => '', required: true}) purpose!: string
  @Prop({ type: String, default: () => ''}) bizName!: string
  @Prop({ type: String, default: () => ''}) poptipDom!: string
  @Prop({ type: Object, default: () => null, required: true}) wishlistTranslation!: wishlistTranslationTypes
  clickFavoriteBtnToLogin: boolean = false

  get platform() {
    return this.$tetris?.runtime?.platform || this.$store?.state?.klook?.platform
  }

  get isLoggedIn() {
    if(this.$tetris) {
      return this.$tetris?.store?.state?.isLogin
    } else {
      return this.$store?.state?.auth?.isLoggedIn
    }
  }

  get favoriteKey() {
    return `act-${this.favoriteData?.activityType}:${this.favoriteData?.activityId}` || ''
  }

  get isFavorite() {
    return !!(Favorite.favoriteMap as any)[this.favoriteKey] || false
  }

  async login(purpose: string, bizName?: string) {
    if(this.$cookies && this.$store && this.$store && this.$href) {
      const { loginWithSDK } = await import('@klook/klook-traveller-login')

      loginWithSDK({
        aid: this.$cookies.get('aid'),
        isMP: this.$store.state.klook?.platformMp !== '',
        platform: this.$store.state.klook?.platform,
        language: this.$store.state.klook?.language,
        currency: this.$store.state.klook?.currency,
        market: this.$store.state.klook?.market,
        bizName: bizName || 'Platform',
        purpose,
        cancel: () => {},
        success: () => {
          this.$store.dispatch('auth/getProfile')
        }
      }).then((supportLogin: boolean) => {
        if (!supportLogin) {
          window.location.href = this.$href(
            `/signin/?signin_jump=${encodeURIComponent(window.location.href)}`
          )
        }
      })
    } else {
      console.error('need this.$cookies && this.klook && this.$store && this.$href')
    }
  }

  async tetrisLogin(purpose: string, bizName?: string) {
    this.$tetris.util.$login({purpose, bizName})
  }

  toggleFavorite() {
    Favorite.toggleFavorite({
      isFavorite: this.isFavorite,
      favoriteKey: this.favoriteKey,
      params: {
        object_type: 'act-' + this.favoriteData.activityType, // 先写死act,后续有其他类型了由后端下发
        object_id: this.favoriteData.activityId
      },
      wishlistTranslation: this.wishlistTranslation,
      platform: this.platform,
      relPoptipDom: this.poptipDom ? document.querySelector(this.poptipDom) : null
    })
  }

  async handleFavoriteClick() {
    if(!this.isLoggedIn) {
      this.clickFavoriteBtnToLogin = true
      this.$tetris ? await this.tetrisLogin(this.purpose, this.bizName) : await this.login(this.purpose, this.bizName)
    } else {
      this.toggleFavorite()
    }
  }

  async mounted() {
    const that = this
    if(this.isLoggedIn) {
      Favorite.getFavoriteList()
    } else {
      this.$watch(
        () => this.isLoggedIn,
        (newValue, oldValue) => {
          // 在值发生变化时执行的回调函数
          if(newValue) {
            const favoriteList = Favorite.getFavoriteList()
            newValue && favoriteList &&  (favoriteList as Promise<void>).then((res) => {
              if(that.clickFavoriteBtnToLogin && !that.isFavorite) {
                that.toggleFavorite()
              }
            })
          }

        }
      );
    }
  }
}
</script>

<style lang="scss" scoped>
.wishlist-poptip {
  .title {
    display: flex;
    align-items: center;
  }

  .btn {
    text-align: right;
    margin-top: 12px;

    button:last-child {
      margin-left: 12px;
    }
  }
}
</style>
