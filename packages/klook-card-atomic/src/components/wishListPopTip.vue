<template>
  <klk-poptip
    ref="wishListPoptip"
    trigger="none"
    :reference-el="relPoptipDom"
    :offset="platform === 'mobile' ? [7, 10] : [0, 10]"
    placement="bottom-end"
    dark
    :fixed="true"
    :z-index="9999"
    :append-to-body="true"
  >
    <div slot="content" class="wishlist-poptip">
      <div class="title"><IconCheckCircle theme="outline" size="24" :fill="$colorTextReverse" style="margin-right: 12px" />{{ wishlistTranslation.poptipTitle }}</div>
      <div class="btn">
        <klk-button size="mini" type="white-filled" @click="wishListPoptipGot">{{ wishlistTranslation.poptipGotItBtn }}</klk-button>
        <klk-button size="mini" type="white-outlined" @click="wishListPoptipJump">{{ wishlistTranslation.poptipJumpBtn }}</klk-button>
      </div>
    </div>
  </klk-poptip>
</template>
<script>
import { $colorTextReverse } from '@klook/klook-ui/es/utils/design-token-esm'
import IconCheckCircle from '@klook/klook-icons/lib/IconCheckCircle'

import LocalStorageManager from "@klook/klk-traveller-utils/lib/localStorage"
import openPage from '@klook/klk-traveller-utils/lib/openPage'
export default {
  data(){
    return {
      $colorTextReverse
    }
  },
  components:{
    IconCheckCircle
  },
  props:{
    wishlistTranslation:{
      type: Object,
      default:()=>({
        poptipGotItBtn:'',
        poptipJumpBtn:'',
        poptipTitle:''
      })
    },
    platform:'',
    relPoptipDom: null
  },
  computed: {
    formatUrl() {
      const language = window.$tetris?.runtime?.language || window.__KLOOK__?.state?.klook?.language || 'en'
      return language === 'en' ? `/wishlist/` : `/${language}/wishlist/`
    },
  },
  methods: {
    show() {
      this.$refs.wishListPoptip.show()
    },
    hide() {
      this.$refs.wishListPoptip.hide()
    },
    wishListPoptipGot() {
      this.$refs.wishListPoptip.hide()
      LocalStorageManager.setItem('klk_wishListEnterClicked', 'true')
    },
    wishListPoptipJump() {
      const that = this
      this.wishListPoptipGot()
      debugger
      openPage({url: that.formatUrl, target: '_blank'})
    }
  }
}
</script>
<style lang="scss">
.wishlist-poptip {
  .title {
    display: flex;
    align-items: center;
  }

  .btn {
    text-align: right;
    margin-top: 12px;

    button:first-child {
      border: 1px solid #ffffff; // 兼容tetris问题
    }

    button:last-child {
      margin-left: 12px;
    }
  }
}
</style>
