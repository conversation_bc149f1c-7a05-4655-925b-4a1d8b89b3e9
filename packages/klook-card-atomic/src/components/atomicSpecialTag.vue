<template>
  <div v-if="data" :class="`atomic-special-tag  atomic-special-tag-${size} special-${data.type.replace(/_/g, '-')}`"  :style="tagStyle">
    <span v-if="data.type === 'vertical_promo_tag' && data.tag_text" class="vertical-promo-tag-text" v-html="formatPercent(data.tag_text)" />
    <img v-else-if="iconSrc" class="tag-icon" :src="iconSrc" />
    <div class="tag-content" v-html="formatPercent(data.text)" />
  </div>
</template>

<script lang="ts">
  import { Vue, Prop, Component } from 'vue-property-decorator'
  import { AtomicBasicTagTypes } from "../../types/atomicComponents"

  const percentSvg = '<svg class="svg-percent" width="11" height="9" viewBox="0 0 11 9" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M8 1L3 8" stroke="currentColor" stroke-linecap="square" stroke-linejoin="round"/><circle cx="2.5" cy="2.5" r="2" stroke="currentColor"/><circle cx="8.5" cy="6.5" r="2" stroke="currentColor"/></svg>'


  @Component
  export default class AtomicSpecialTag extends Vue {
    @Prop({ type: Object, default: () => null }) data!: AtomicBasicTagTypes
    @Prop({ type: String, default: () => 'small' }) size!: string

    formatPercent(text?: string) {
      return text?.replace(/%/g, percentSvg) || ''
    }

    get tagStyle() {
      const { type, tag_color: color, background_image_src: bgImage } = this.data
      if (type === 'reward') {
        return {
          background: bgImage ? `url(${bgImage}) no-repeat center` : 'linear-gradient(270deg, #F38504 0%, #F9A938 100%)',
        }
      }

      return type === 'manual_tag' && color
        ? {
          color,
          'box-shadow': `inset 0 0 0 1px rgba(
            ${parseInt('0x' + color.slice(1, 3)) },
            ${parseInt('0x' + color.slice(3, 5)) },
            ${parseInt('0x' + color.slice(5, 7)) },
            0.4
          )`
        }
        : null
    }

    get iconSrc() {
      const { type, icon_src } = this.data
      switch (type) {
        case 'manual_tag':
          return icon_src
        case 'platform_promo_code_tag':
          return 'https://res.klook.com/image/upload/v1712803939/UED_new/Attraction/Attraction_tag_2404/Subtract.png'
        case 'reward':
          return icon_src || 'https://res.klook.com/image/upload/v1661398434/qvekdumb9fv5vzfglegl.png'
      }
    }
  }
</script>


<style lang="scss" scoped>
.atomic-special-tag {
  background: $color-bg-widget-normal;
  display: inline-block;
  border-radius: 2px;
  overflow: hidden;
  max-width: 100%;
  word-break: break-all;
  padding: 1px;
  box-sizing: content-box;

  .tag-icon {
    float: left;
    background: none;
  }

  &-small {
    font-size: $fontSize-caption-m;
    line-height: 18px;
    height: 18px;

    .tag-icon {
      padding: 3px 4px;
    }
  }

  &-medium {
    font-size: $fontSize-body-s;
    line-height: 22px;
    height: 22px;

    .tag-icon {
      padding: 4px;
    }
  }

  ::v-deep .svg-percent {
    width: 1em;
    height: 1em;
    vertical-align: middle;
    margin-left: 1px;
    margin-top: -2px;
  }


  .tag-content {
    padding: 0 4px;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
  }

  &.special-reward {
    // background: linear-gradient(270deg, #F38504 0%, #F9A938 100%);
    color: $color-text-reverse;
    .tag-icon {
      padding: 0;
      margin-top: -1px;
      margin-left: -1px;
      height: calc(100% + 2px);

      & + .tag-content {
        padding-left: 2px;
      }
    }
  }

  &.special-platform-promo-code-tag,
  &.special-manual-tag,
  &.special-vertical-promo-tag {
    color: $color-error;
    box-shadow: inset 0 0 0 1px #********;

    .tag-icon {
      height: 1em;
      background: none;
      & + .tag-content {
        padding-left: 0;
      }
    }

    .vertical-promo-tag-text {
      padding-right: 4px;
      padding-left: 1.42em;
      background-color: #********;
      margin-left: -3px;
      float: right;
      box-sizing: border-box;
      max-width: 100%;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
      background-image: 
        radial-gradient(circle at 0.9em center, $color-white 0.22em, transparent 0.22em),
        linear-gradient(125deg, $color-white 0.68em, transparent 0.68em),
        linear-gradient(55deg, $color-white 0.68em, transparent 0.68em);
    }
  }
}
</style>
