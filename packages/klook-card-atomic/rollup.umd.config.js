import path from 'path';
import replace from 'rollup-plugin-replace'
import typescript from 'rollup-plugin-typescript2'
import vue from 'rollup-plugin-vue';
import json from '@rollup/plugin-json';
import nodeResolve from 'rollup-plugin-node-resolve'
import commonjs from 'rollup-plugin-commonjs';
import image from '@rollup/plugin-image';
import alias from "rollup-plugin-alias";
import postcss from 'rollup-plugin-postcss'
import dynamicImportVars from '@rollup/plugin-dynamic-import-vars'
const babel = require('rollup-plugin-babel');
const terser = require('rollup-plugin-terser').terser;
const pkg = require('./package.json')

// const nodePolyfills = require('rollup-plugin-polyfill-node');

const resolve = p => path.join(__dirname, './', p);

// clear({ targets: ['dist'] })

const banner =
  `/**
  * v${pkg.version}
  * (c) ${new Date().getFullYear()} ${pkg.author}
  */`
const configs = {
  // script引入
  umd: {
    inlineDynamicImports: true,
    dir: 'dist/umd',
    // output: 'dist/umd/index.js',
    name: 'KlkCard',
    format: 'umd',
    target: 'es5',
    env: 'production',
  }
}

const genTsPlugin = (configOpts) => typescript({
  useTsconfigDeclarationDir: true,
  tsconfigOverride: {
    compilerOptions: {
      target: configOpts.target,
      declaration: configOpts.genDts
    }
  },
  abortOnError: false
})

const genPlugins = (configOpts) => {
  const plugins = [image()]
  if (configOpts.env) {
    plugins.push(replace({
      'process.env.NODE_ENV': JSON.stringify(configOpts.env)
    }))
  }
  plugins.push(replace({
    'process.env.MODULE_FORMAT': JSON.stringify(configOpts.format)
  }))

  // plugins.push(nodePolyfills())

  plugins.push(nodeResolve({
    extensions: ['.mjs', '.js', '.jsx', '.ts', '.vue'],
    preferBuiltins: false,
    browser: true
  }))

  plugins.push(commonjs({
    include: /node_modules/,
  }))

  if (configOpts.plugins && configOpts.plugins.pre) {
    plugins.push(...configOpts.plugins.pre)
  }

  plugins.push(genTsPlugin(configOpts))

  plugins.push(alias({
    resolve: [".ts", ".js", ".vue"],
    entries: [{
      find: '@',
      replacement: resolve('./src')
    }]
  }))

  plugins.push(json())

  plugins.push(vue({
    css: false,
    template: {
      isProduction: true,
    },
    style: {
      postcssPlugins: [
        require('autoprefixer')()
      ],
      preprocessStyles: true,
      preprocessOptions: {
        scss: {
          data: '@import "../klook-ui/src/styles/token/index.scss";'
        }
      }
    }
  }))

  plugins.push(babel({
    include: ['src/**', 'node_modules/**'],
    extensions: ['.js', '.vue', '.ts', '.jsx', '.tsx'],
    runtimeHelpers: true
  }));

  plugins.push(terser({
    output: {
      comments(node, comment) {
        const text = comment.value;
        const type = comment.type;
        // multiline comment
        return type === 'comment2' && /@preserve|@license|@cc_on/i.test(text);
      },
    },
    compress: {
      drop_console: true,
    }
  }))

  plugins.push(dynamicImportVars({
    exclude: [/\.css/]
  }))

  plugins.push(postcss({
    extract: true,
    minimize: true,
    plugins: [
      require('autoprefixer')()
    ]
  }))

  if (configOpts.plugins && configOpts.plugins.post) {
    plugins.push(...configOpts.plugins.post)
  }
  return plugins
}

const genConfig = (configOpts) => ({
  input: 'src/index.ts',
  external: [
    '@klook/klook-traveller-login',
    'vue',
    '@klook/klook-ui',
    'vue-property-decorator',
    'vue-class-component',
    '@klook/klook-ui/dist/klook-ui.css',
    "@klook/klook-ui/es/alert/index.js",
    "@klook/klook-ui/es/breadcrumb/index.js",
    "@klook/klook-ui/es/carousel/index.js",
    "@klook/klook-ui/es/checkbox/index.js",
    "@klook/klook-ui/es/collapse/index.js",
    "@klook/klook-ui/es/button/index.js",
    "@klook/klook-ui/es/announcement/index.js",
    "@klook/klook-ui/es/counter/index.js",
    "@klook/klook-ui/es/badge/index.js",
    "@klook/klook-ui/es/divider/index.js",
    "@klook/klook-ui/es/drawer/index.js",
    "@klook/klook-ui/es/dropdown/index.js",
    "@klook/klook-ui/es/form/index.js",
    "@klook/klook-ui/es/grid/index.js",
    "@klook/klook-ui/es/helpers/index.js",
    "@klook/klook-ui/es/icon/index.js",
    "@klook/klook-ui/es/input/index.js",
    "@klook/klook-ui/es/label/index.js",
    "@klook/klook-ui/es/link/index.js",
    "@klook/klook-ui/es/loading/index.js",
    "@klook/klook-ui/es/markdown/index.js",
    "@klook/klook-ui/es/message/index.js",
    "@klook/klook-ui/es/locale/index.js",
    "@klook/klook-ui/es/modal/index.js",
    "@klook/klook-ui/es/notification/index.js",
    "@klook/klook-ui/es/pagination/index.js",
    "@klook/klook-ui/es/picker/index.js",
    "@klook/klook-ui/es/poptip/index.js",
    "@klook/klook-ui/es/progress/index.js",
    "@klook/klook-ui/es/radio/index.js",
    "@klook/klook-ui/es/select/index.js",
    "@klook/klook-ui/es/skeleton/index.js",
    "@klook/klook-ui/es/slider/index.js",
    "@klook/klook-ui/es/switch/index.js",
    "@klook/klook-ui/es/steps/index.js",
    "@klook/klook-ui/es/tabs/index.js",
    "@klook/klook-ui/es/table/index.js",
    "@klook/klook-ui/es/tree/index.js",
    "@klook/klook-ui/es/upload/index.js",
    "@klook/klook-ui/es/tag/index.js",
    "@klook/klook-ui/es/toast/index.js",
    "@klook/klook-ui/es/utils/index.js",
    "@klook/klook-ui/lib/alert/index.js",
    "@klook/klook-ui/lib/button/index.js",
    "@klook/klook-ui/lib/carousel/index.js",
    "@klook/klook-ui/lib/breadcrumb/index.js",
    "@klook/klook-ui/lib/collapse/index.js",
    "@klook/klook-ui/lib/counter/index.js",
    "@klook/klook-ui/lib/divider/index.js",
    "@klook/klook-ui/lib/drawer/index.js",
    "@klook/klook-ui/lib/dropdown/index.js",
    "@klook/klook-ui/lib/form/index.js",
    "@klook/klook-ui/lib/grid/index.js",
    "@klook/klook-ui/lib/helpers/index.js",
    "@klook/klook-ui/lib/icon/index.js",
    "@klook/klook-ui/lib/input/index.js",
    "@klook/klook-ui/lib/label/index.js",
    "@klook/klook-ui/lib/link/index.js",
    "@klook/klook-ui/lib/loading/index.js",
    "@klook/klook-ui/lib/locale/index.js",
    "@klook/klook-ui/lib/markdown/index.js",
    "@klook/klook-ui/lib/badge/index.js",
    "@klook/klook-ui/lib/announcement/index.js",
    "@klook/klook-ui/lib/notification/index.js",
    "@klook/klook-ui/lib/pagination/index.js",
    "@klook/klook-ui/lib/picker/index.js",
    "@klook/klook-ui/lib/poptip/index.js",
    "@klook/klook-ui/lib/progress/index.js",
    "@klook/klook-ui/lib/modal/index.js",
    "@klook/klook-ui/lib/message/index.js",
    "@klook/klook-ui/lib/checkbox/index.js",
    "@klook/klook-ui/lib/slider/index.js",
    "@klook/klook-ui/lib/steps/index.js",
    "@klook/klook-ui/lib/switch/index.js",
    "@klook/klook-ui/lib/table/index.js",
    "@klook/klook-ui/lib/tabs/index.js",
    "@klook/klook-ui/lib/tag/index.js",
    "@klook/klook-ui/lib/toast/index.js",
    "@klook/klook-ui/lib/radio/index.js",
    "@klook/klook-ui/lib/upload/index.js",
    "@klook/klook-ui/lib/skeleton/index.js",
    "@klook/klook-ui/lib/utils/index.js",
    "@klook/klook-ui/lib/tree/index.js",
    "@klook/klook-ui/lib/select/index.js"
  ],
  plugins: genPlugins(configOpts),
  output: {
    banner,
    file: configOpts.output,
    format: configOpts.format,
    name: configOpts.name || pkg.name,
    sourcemap: false,
    exports: 'named',
    // globals: configOpts.globals,
    extend: true,
    inlineDynamicImports: configOpts.inlineDynamicImports,
    dir: configOpts.dir,
    chunkFileNames: 'chunks/[name]-[hash].js',
    entryFileNames: '[name].js',
    esModule: true,
    generatedCode: {
      symbols: true
    },
    globals: {
      vue: 'Vue',
      "@klook/klook-traveller-login": "KlookTravellerLogin",
      '@klook/klook-ui': 'KlookUI',
      'vue-class-component': 'VueClassComponent',
      'vue-property-decorator': 'VuePropertyDecorator',
      '@klook/klook-ui/dist/klook-ui.css': '',
      '@klook/klook-ui/es/alert/index.js': 'KlookUI.Alert',
      '@klook/klook-ui/es/breadcrumb/index.js': 'KlookUI.Breadcrumb',
      '@klook/klook-ui/es/carousel/index.js': 'KlookUI.Carousel',
      '@klook/klook-ui/es/checkbox/index.js': 'KlookUI.Checkbox',
      '@klook/klook-ui/es/collapse/index.js': 'KlookUI.Collapse',
      '@klook/klook-ui/es/button/index.js': 'KlookUI.Button',
      '@klook/klook-ui/es/announcement/index.js': 'KlookUI.Announcement',
      '@klook/klook-ui/es/counter/index.js': 'KlookUI.Counter',
      '@klook/klook-ui/es/badge/index.js': 'KlookUI.Badge',
      '@klook/klook-ui/es/divider/index.js': 'KlookUI.Divider',
      '@klook/klook-ui/es/drawer/index.js': 'KlookUI.Drawer',
      '@klook/klook-ui/es/dropdown/index.js': 'KlookUI.Dropdown',
      '@klook/klook-ui/es/form/index.js': 'KlookUI.Form',
      '@klook/klook-ui/es/grid/index.js': 'KlookUI.Grid',
      '@klook/klook-ui/es/helpers/index.js': 'KlookUI.Helpers',
      '@klook/klook-ui/es/icon/index.js': 'KlookUI.Icon',
      '@klook/klook-ui/es/input/index.js': 'KlookUI.Input',
      '@klook/klook-ui/es/label/index.js': 'KlookUI.Label',
      '@klook/klook-ui/es/link/index.js': 'KlookUI.Link',
      '@klook/klook-ui/es/loading/index.js': 'KlookUI.Loading',
      '@klook/klook-ui/es/markdown/index.js': 'KlookUI.Markdown',
      '@klook/klook-ui/es/message/index.js': 'KlookUI.Message',
      '@klook/klook-ui/es/locale/index.js': 'KlookUI.Locale',
      '@klook/klook-ui/es/modal/index.js': 'KlookUI.Modal',
      '@klook/klook-ui/es/notification/index.js': 'KlookUI.Notification',
      '@klook/klook-ui/es/pagination/index.js': 'KlookUI.Pagination',
      '@klook/klook-ui/es/picker/index.js': 'KlookUI.Picker',
      '@klook/klook-ui/es/poptip/index.js': 'KlookUI.Poptip',
      '@klook/klook-ui/es/progress/index.js': 'KlookUI.Progress',
      '@klook/klook-ui/es/radio/index.js': 'KlookUI.Radio',
      '@klook/klook-ui/es/select/index.js': 'KlookUI.Select',
      '@klook/klook-ui/es/skeleton/index.js': 'KlookUI.Skeleton',
      '@klook/klook-ui/es/slider/index.js': 'KlookUI.Slider',
      '@klook/klook-ui/es/switch/index.js': 'KlookUI.Switch',
      '@klook/klook-ui/es/steps/index.js': 'KlookUI.Steps',
      '@klook/klook-ui/es/tabs/index.js': 'KlookUI.Tabs',
      '@klook/klook-ui/es/table/index.js': 'KlookUI.Table',
      '@klook/klook-ui/es/tree/index.js': 'KlookUI.Tree',
      '@klook/klook-ui/es/upload/index.js': 'KlookUI.Upload',
      '@klook/klook-ui/es/tag/index.js': 'KlookUI.Tag',
      '@klook/klook-ui/es/toast/index.js': 'KlookUI.Toast',
      '@klook/klook-ui/es/utils/index.js': 'KlookUI.Utils',
      '@klook/klook-ui/lib/alert/index.js': 'KlookUI.Alert',
      '@klook/klook-ui/lib/button/index.js': 'KlookUI.Button',
      '@klook/klook-ui/lib/carousel/index.js': 'KlookUI.Carousel',
      '@klook/klook-ui/lib/breadcrumb/index.js': 'KlookUI.Breadcrumb',
      '@klook/klook-ui/lib/collapse/index.js': 'KlookUI.Collapse',
      '@klook/klook-ui/lib/counter/index.js': 'KlookUI.Counter',
      '@klook/klook-ui/lib/divider/index.js': 'KlookUI.Divider',
      '@klook/klook-ui/lib/drawer/index.js': 'KlookUI.Drawer',
      '@klook/klook-ui/lib/dropdown/index.js': 'KlookUI.Dropdown',
      '@klook/klook-ui/lib/form/index.js': 'KlookUI.Form',
      '@klook/klook-ui/lib/grid/index.js': 'KlookUI.Grid',
      '@klook/klook-ui/lib/helpers/index.js': 'KlookUI.Helpers',
      '@klook/klook-ui/lib/icon/index.js': 'KlookUI.Icon',
      '@klook/klook-ui/lib/input/index.js': 'KlookUI.Input',
      '@klook/klook-ui/lib/label/index.js': 'KlookUI.Label',
      '@klook/klook-ui/lib/link/index.js': 'KlookUI.Link',
      '@klook/klook-ui/lib/loading/index.js': 'KlookUI.Loading',
      '@klook/klook-ui/lib/locale/index.js': 'KlookUI.Locale',
      '@klook/klook-ui/lib/markdown/index.js': 'KlookUI.Markdown',
      '@klook/klook-ui/lib/badge/index.js': 'KlookUI.Badge',
      '@klook/klook-ui/lib/announcement/index.js': 'KlookUI.Announcement',
      '@klook/klook-ui/lib/notification/index.js': 'KlookUI.Notification',
      '@klook/klook-ui/lib/pagination/index.js': 'KlookUI.Pagination',
      '@klook/klook-ui/lib/picker/index.js': 'KlookUI.Picker',
      '@klook/klook-ui/lib/poptip/index.js': 'KlookUI.Poptip',
      '@klook/klook-ui/lib/progress/index.js': 'KlookUI.Progress',
      '@klook/klook-ui/lib/modal/index.js': 'KlookUI.Modal',
      '@klook/klook-ui/lib/message/index.js': 'KlookUI.Message',
      '@klook/klook-ui/lib/checkbox/index.js': 'KlookUI.Checkbox',
      '@klook/klook-ui/lib/slider/index.js': 'KlookUI.Slider',
      '@klook/klook-ui/lib/steps/index.js': 'KlookUI.Steps',
      '@klook/klook-ui/lib/switch/index.js': 'KlookUI.Switch',
      '@klook/klook-ui/lib/table/index.js': 'KlookUI.Table',
      '@klook/klook-ui/lib/tabs/index.js': 'KlookUI.Tabs',
      '@klook/klook-ui/lib/tag/index.js': 'KlookUI.Tag',
      '@klook/klook-ui/lib/toast/index.js': 'KlookUI.Toast',
      '@klook/klook-ui/lib/radio/index.js': 'KlookUI.Radio',
      '@klook/klook-ui/lib/upload/index.js': 'KlookUI.Upload',
      '@klook/klook-ui/lib/skeleton/index.js': 'KlookUI.Skeleton',
      '@klook/klook-ui/lib/utils/index.js': 'KlookUI.Utils',
      '@klook/klook-ui/lib/tree/index.js': 'KlookUI.Tree',
      '@klook/klook-ui/lib/select/index.js': 'KlookUI.Select'
    }
  }
})

const genAllConfigs = (configs) => (Object.keys(configs).map(key => genConfig(configs[key])))

export default genAllConfigs(configs)
