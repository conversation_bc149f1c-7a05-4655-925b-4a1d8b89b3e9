import Vue from "vue";
import { CardProp } from "./data";
import { AtomicCardDataTypes, AtomicPriceInfoTypes, AtomicBasicTagTypes, AtomicMinimalRatingTypes } from "./atomicComponents";

export class BaseCard extends Vue {
  size: CardProp["size"];
  cardData: AtomicCardDataTypes;
  isLazy: boolean;
  isCrop: boolean;
  isShowReviewNumber: boolean;
  isShowReviewBooked: boolean;
  isLoading: boolean;
  isFlowLayout: boolean;
  webp: number;
  rank: number | string;
  target: string;
  styleType: CardProp["styleType"];
  subTextLine: number;
  titleLine: number;
  titleTag: string;
  descLine: number;
  imgRatio: number;
  subTextColor: string;
  backgroundColor: string;
  imgRadius: CardProp["radius"];
  hoverStyle: CardProp["hoverStyle"];
}

export declare class KlkHorizontalCard extends BaseCard {
  imageWidth: number;
  hzPosition: CardProp["hzPosition"];
  cropWidth: string;
}

export declare class KlkVerticalCard extends BaseCard {
  cropWidth: string;
}

export declare class KlkAtomicAspectRatio extends Vue{
  isLazy: boolean;
  imgSrc: string;
  webp: number;
  imgWidth: string;
  imgHeight: string;
  imgRatio: number;
  bgSize: string;
  cropWidth: string;
  className: string;
  cropHeight: string;
  disabled: boolean
}

export declare class KlkAtomicBasicTag extends Vue{
  data: AtomicBasicTagTypes;
}

export declare class KlkAtomicBasicText extends Vue{
  text: string;
  line: number | string;
  tag: string;
  className: string[]
}

export declare class KlkAtomicMinimalRating extends Vue{
  data: AtomicMinimalRatingTypes;
  showNumber: boolean;
  showBooked: boolean
}

export declare class KlkAtomicPriceInfo extends Vue{
  data: AtomicPriceInfoTypes;
  highLightPrice: boolean
}
