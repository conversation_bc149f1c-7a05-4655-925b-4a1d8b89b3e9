// 标准卡片数据
export interface AtomicCardDataTypes {
  title_image?: string;
  title: string; // title
  price: AtomicPriceInfoTypes; // 价格
  vertical_id: number;
  vertical_type: number;
  cover_url: string; // 图片地址
  description?: string; // 描述
  showVideo?: Boolean; // 是否展示播放按钮
  general_tag?: AtomicBasicTagTypes[]; // general_tag
  promotion_tag?: AtomicBasicTagTypes[]; // promotion_tag
  promo_tag_list?: AtomicBasicTagTypes[]; // promotion_tag
  deep_link?: string; // 跳转链接
  sub_title?: string; // 次级文本
  review?: AtomicMinimalRatingTypes; // 评论
  sold_out?: boolean
}

// 标准卡片原子组件——价格信息
export interface AtomicPriceInfoTypes {
  selling_price: string; // 销售价
  selling_price_format: string; // 销售价格式
  discount_tag?: AtomicBasicTagTypes; // 优惠tag
  market_price?: string; // 市场价
  market_price_no_strike?: boolean; // 市场价是否划线
}

// 标准卡片原子组件——tag
export interface AtomicBasicTagTypes {
  type: 'reward' | 'platform_promo_code_tag' | 'vertical_promo_tag' | 'manual_tag'  // manual_tag 为可配置tag
  tag_text?: string; // vertical_promo_tag 类型 独有
  tag_color?: string; // manual_tag 类型 独有
  text: string; // tag文案
  text_color: string; // tag文案颜色
  bg_color?: string; // 背景色
  line_color?: string; // 线框颜色
  icon_src?: string; // tag icon
  background_image_src?: string; // 背景图
  background_color_left?: string; // 背景色左色
  background_color_right?: string; // 背景色右色
}

// 标准卡片原子组件——评分
export interface AtomicMinimalRatingTypes {
  star: string; // 评分星级（必传）
  number?: string; // 评分人数
  deep_link?: string; // 评分跳转链接
  booked?: string; // 已订阅人数
  dot?: string; // 分隔符
}

// 标准卡片原子组件——收藏
export interface AtomicFavoriteTypes {
  activityId: string
  activityType: string
}

export interface wishlistTranslationTypes {
  poptipTitle: string
  poptipGotItBtn: string
  poptipJumpBtn: string
  errorCode: string
}
