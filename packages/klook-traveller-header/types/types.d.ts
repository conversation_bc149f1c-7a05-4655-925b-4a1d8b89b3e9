import { SiteConfigTypes } from '@klook/site-config'

export {}

declare global {
  namespace Data {
    type Language = SiteConfigTypes.Language

    interface IUser {
      globalId: string
      avatar: string
      unreview: number
      user_residence: string
      membership_level: number // 0: 普通用户 1: Gold 2: 更高等级
      membership_style?: any
      display_membership_entry?: boolean
    }
  }
}
