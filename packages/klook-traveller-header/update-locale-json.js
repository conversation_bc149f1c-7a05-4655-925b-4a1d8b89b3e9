const fs = require('fs')
const path = require('path')
const axios = require('axios').default

// const isDev = !!process.argv.splice(2).length
const GETMAXCOUNTER = 50

const GET_BATCH_TEXT_API = 'https://www.klook.com/v1/locpublishinnerserv/text/batch'
const PLATFORM_CMS_TOKEN = '63gEEsMSq392FV7veEo9I1rrZw9XH26g42b'
const COLOR_TOKEN_MAPS = {
  'color-blue': '#0091FF',
  'color-green': '#36B37E',
  'color-yellow': '#FFAB00',
  'color-red': '#FF5630',
  'color-black-85': '#262626',
  'color-black-45': '#8C8C8C',
  'color-black-25': '#BFBFBF',
  'color-white': '#FFFFFF'
}

const LANG_MAPS = {
  'de_DE':'de',
  'en_AU':'en-AU',
  'en_CA':'en-CA',
  'en_GB':'en-GB',
  'en_HK':'en-HK',
  'en_IN':'en-IN',
  'en_MY':'en-MY',
  'en_NZ':'en-NZ',
  'en_PH':'en-PH',
  'en_SG':'en-SG',
  'en_US':'en-US',
  'en_BS':'en',
  'es_ES':'es',
  'fr_FR':'fr',
  'id_ID':'id',
  'it_IT': 'it',
  'ja_JP':'ja',
  'ko_KR':'ko',
  'ru_RU':'ru',
  'th_TH':'th',
  'vi_VN':'vi',
  'zh_HK':'zh-HK',
  zh_CN: 'zh-CN',
  zh_TW: 'zh-TW',
  ms_MY: 'ms-MY',
}

// 获取翻译资源
const getLocalesRes = (key) =>
  axios
    .get(GET_BATCH_TEXT_API, {
      params: {
        key
      },
      headers: {
        Version: '5.3',
        Accept: 'application/json, text/plain, */*',
        'X-Requested-With': 'XMLHttpRequest',
        token: PLATFORM_CMS_TOKEN
      }
    })
    .then((response) => response.data)

// 处理文案原始内容, 替换颜色token, 换行
const processTextContent = (content, type) => {
  const colorTokenRegex = new RegExp(
    `color:(${Object.keys(COLOR_TOKEN_MAPS).join('|')})`,
    'g'
  )

  if (typeof content !== 'string' || !content) {
    return content
  }

  if (type !== 'normal') {
    return { content: JSON.parse(content), template_type: type }
  }

  return content
    .replace(colorTokenRegex, (_match, p1) => `color:${COLOR_TOKEN_MAPS[p1]}`)
    .replace('\n', '<br/>')
}

/**
 * 更新语言资源文件
 * @param {*} lang
 * @param {*} langRes
 */
const updateLocaleForLang = async (lang, langRes, mfilePath) => {
  if (!LANG_MAPS[lang]) {
    console.log(
      `[Error] ${lang} not in support lang arary: ${Object.values(
        LANG_MAPS
      ).join(',')}`
    )
    return
  }
  const filePath = path.join(
    __dirname,
    `./locales/${LANG_MAPS[lang]}.json`
  )
  const oldContentBuf = fs.readFileSync(filePath)
  const oldJsonRes = JSON.parse(oldContentBuf.toString() || '{}')
  let updated = [],
    deleted = [],
    added = []
  Object.keys(langRes).forEach((key) => {
    if (!oldJsonRes[key]) {
      added.push(key)
    } else if (oldJsonRes[key] !== langRes[key]) {
      updated.push(key)
    }
  })
  Object.keys(oldJsonRes).forEach((key) => {
    if (!langRes[key]) {
      deleted.push(key)
    }
  })
  fs.writeFileSync(filePath, JSON.stringify(langRes, null, 2), {
    encoding: 'utf-8'
  })

  // console.log(
  //   `[Update locale] ${LANG_MAPS[lang]}, updated ${updated.length}, added ${
  //     added.length
  //   }, deleted ${deleted.length}`
  // )
  updated.length && console.log(`[Updated] ${LANG_MAPS[lang]}.json ${updated.join(',')}`)
  added.length && console.log(`[Added] ${LANG_MAPS[lang]}.json ${added.join(',')}`)
  deleted.length && console.log(`[Deleted] ${LANG_MAPS[lang]}.json ${deleted.join(',')}`)

  return LANG_MAPS[lang]
}
// const getDefaultLocalesKey = function(){
//   const filePath = path.join(
//     __dirname,
//     `./locales/en.json`
//   )
//   const oldContentBuf = fs.readFileSync(filePath)
//   const oldJsonRes = JSON.parse(oldContentBuf.toString() || '{}')
//   return Object.keys(oldJsonRes)
// }
function getAllFileKeys(){
  let keyArr = []
  Object.values(LANG_MAPS).forEach(lang=>{
    const filePath = path.join(
      __dirname,
      `./locales/${lang}.json`
    )
    const oldContentBuf = fs.readFileSync(filePath)
    const oldJsonRes = JSON.parse(oldContentBuf.toString() || '{}')
    keyArr = keyArr.concat(Object.keys(oldJsonRes))
  })
  console.log('keys count: ',keyArr.length)
  return Array.from(new Set(keyArr))
}
const getKeyArr = function() {
  const defaultTextIdList = getAllFileKeys()
  console.log('update keys: ',defaultTextIdList)
  const keyArr = [[]]
  defaultTextIdList.forEach(textId=>{
    const lastInd = keyArr.length - 1
    if (keyArr[lastInd].length < GETMAXCOUNTER) {
      keyArr[lastInd].push(textId)
    } else {
      keyArr.push([textId])
    }
  })
  return keyArr
}
/**
 * 主运行入口
 */
const run = async () => {
    const reqList = getKeyArr().map(keys =>getLocalesRes(keys.toString()))
    const resList = await Promise.all(reqList)
    let texts = []
    resList.forEach(resObj=>{
      texts = texts.concat(resObj.result)
    })
    console.log(
      '======================== build locales ========================'
    )
    console.log(
      `[Get locales]length: ${texts && texts.length}`
    )

    if (!Array.isArray(texts) || !texts.length) {
      return
    }

    const result = {}
    texts.forEach((item) => {
      const { content, key, locale, text_id, template_type } = item || {}

      const newContent = processTextContent(content, template_type)
      const textKey = text_id || key

      if (!result[locale]) {
        result[locale] = {}
      }
      result[locale][textKey] = newContent
    })
    await Promise.all(
      Object.keys(result).map((langKey) =>
        updateLocaleForLang(langKey, result[langKey])
      )
    )
    console.log(
      '======================== end build locales ========================'
    )
}

run().catch((error) => {
  console.log(error)
  console.log(
    '======================== end build locales ========================'
  )
  process.exit(1)
})
