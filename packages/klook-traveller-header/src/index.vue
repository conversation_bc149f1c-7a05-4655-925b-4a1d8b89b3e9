<template>
  <component
    ref="header"
    v-bind="$attrs"
    :is="componentName"
    :platform="platform"
    v-on="$listeners"
  />
</template>

<script lang="ts">
import { Vue, Component, Prop, Provide } from 'vue-property-decorator';
import href from '@klook/klk-traveller-utils/lib/href';
import { translate } from './locale';

@Component({
  components: {
    MobileHeader: () => import('./mobile/index.vue'),
    DesktopHeader: () => import('./desktop/index.vue')
  }
})
export default class KlkTravellerHeader extends Vue {
  @Prop() platform!: string
  @Prop() userInfo!: Data.IUser

  @Prop() aid!:number
  @Prop() ismp!:boolean
  @Prop() loginSuccess!:(data:any) => void
  @Prop() loginCancel!:() => void
  @Prop() useLoginSdk!: boolean

  @Provide() getAid = () => this.aid
  @Provide() getIsMP = () => this.ismp ?? false
  @Provide() getUseLoginSdk = () => this.useLoginSdk
  @Provide() getCurrency = () => this.$attrs.currency
  @Provide() loginSuccessCB = this.loginSuccess
  @Provide() loginCancelCB = this.loginCancel

  @Provide() language = this.getLang()
  @Provide() __t = this.getTranslate()
  @Provide() __href = this.getHref()
  @Provide() getUser = () => this.userInfo

  getLang() {
    return this.$attrs.language;
  }

  getTranslate() {
    return this.__t;
  }

  getHref() {
    return this.__href;
  }

  beforeCreate(this: any) {
    const language = this.$attrs.language;

    this.__t = translate(this.$attrs.locales);

    const getHref = (language: Data.Language) => (pathname: string, lang: Data.Language, baseLink?: string) => {
      lang = lang || language;
      return href(pathname, lang as string, baseLink);
    };
    this.__href = getHref(language);
  }

  updateShoppingCart() {
    const header  = this.$refs.header as any
    header && header.updateShoppingCart && header.updateShoppingCart()
  }

  get componentName() {
    if (this.platform === 'mobile') {
      return 'MobileHeader';
    }

    return 'DesktopHeader';
  }
}
</script>
