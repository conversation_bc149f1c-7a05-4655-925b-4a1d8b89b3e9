$color-gray3: #f5f5f5;
$color-main: #ff5722;
$color-white: white;
$color-gray2: #e0e0e0;

$zindex-trigger-layer: 200; // 提示气泡 Poptip，下拉菜单 Dropdown ，级联选择器 Cascader Select， 选择器 Select， 局部加载 Loading

// 多行省略
@mixin text-ellipsis($line-clamp: 2) {
  display: -webkit-box;
  -webkit-line-clamp: $line-clamp;
  -webkit-box-orient: vertical;
  text-overflow: ellipsis;
  overflow: hidden;
}

@mixin retina-sprite($url, $url2, $width, $height) {
  background-image: url(#{$url});
  width: $width;
  height: $height;

  @media (-webkit-min-device-pixel-ratio: 2),
  (min-resolution: 192dpi) {
    background-image: url(#{$url2});
    background-size: $width $height;
  }
}
