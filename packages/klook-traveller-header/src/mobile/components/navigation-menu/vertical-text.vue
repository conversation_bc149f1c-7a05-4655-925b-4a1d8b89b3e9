<template>
  <div>
    <div v-for="(child, childIndex) in L2MenuData" :key="childIndex" class="text-type-item">
      <div class="vertical-menu-2" @click="jumpToUrl(child.url)">
        <a
          class="text-type-item-a"
          :href="child.url"
          @click.prevent
          :data-spm-module="buildSpmModule(child, L2MenuData.length, childIndex, itemTitle)"
          data-spm-virtual-item="__virtual"
          v-galileo-click-tracker="{ spm: 'CategoryBar_L2_LIST', componentName: 'klk-traveller-header' }"
        >
          <span>
            {{ child.title }}
          </span>
        </a>
        <IconNext theme="outline" size="16" />
      </div>
      <template v-if="child.children && child.children.length">
        <div
          v-for="(child2, child2Index) in child.children"
          :key="child2Index"
          class="vertical-menu-3"
          @click="jumpToUrl(child.url)"
        >
          <a
            class="vertical-menu-3-link"
            :href="child2.url"
            @click.prevent
            :data-spm-module="buildSpmModule(child2, child.children.length, child2Index, itemTitle)"
            data-spm-virtual-item="__virtual"
            v-galileo-click-tracker="{ spm: 'CategoryBar_L2_LIST', componentName: 'klk-traveller-header' }"
          >
            <span v-if="child2.title" class="vertical-menu-3-text">{{ child2.title }}</span>
          </a>
          <IconNext theme="outline" size="16" />
        </div>
      </template>
    </div>
  </div>
</template>
<script>
import { IconNext } from "@klook/klook-icons";
import { buildLinkDescriptionFn, buildSpmModuleFn, jumpToUrlFn } from "./utils";

export default {
  name: "NavigationVerticalItemText",

  components: {
    IconNext,
  },

  props: {
    L2MenuData: {
      type: Array,
      default: () => [],
    },

    itemTitle: {
      type: String,
      default: "",
    },
  },

  methods: {
    buildLinkDescription(menu) {
      return buildLinkDescriptionFn(menu);
    },
    jumpToUrl(url) {
      jumpToUrlFn(url);
    },
    buildSpmModule(item, length, index, itemTitle) {
      return buildSpmModuleFn(item, length, index, itemTitle);
    },
  },
};
</script>
<style scoped lang="scss">
@mixin clamp-lines($lines: 2) {
  display: -webkit-box;
  -webkit-line-clamp: $lines;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;

  white-space: normal;
  word-break: break-word;
  overflow-wrap: break-word;
}
.text-type-item {
  cursor: pointer;
}
.text-type-item-a,
.vertical-menu-3-link {
  padding: 13px 0px;
  @include font-body-m-regular();
  color: $color-text-primary;

  svg {
    margin-left: 8px;
    flex-shrink: 0;
  }

  &:hover {
    text-decoration: none;
  }
}
.vertical-menu-2:link .text-type-item-a {
  color: $color-text-primary;
}
.vertical-menu-2:visited .text-type-item-a {
  color: $color-text-primary;
}
.vertical-menu-2:hover .text-type-item-a {
  color: $color-text-primary;
}
.vertical-menu-2:active .text-type-item-a {
  color: $color-brand-primary;
}

.vertical-menu-3:link span.vertical-menu-3-text {
  color: $color-text-primary;
}
.vertical-menu-3:visited span.vertical-menu-3-text {
  color: $color-text-primary;
}
.vertical-menu-3:hover span.vertical-menu-3-text {
  color: $color-text-primary;
}
.vertical-menu-3:active span.vertical-menu-3-text {
  color: $color-brand-primary;
}

.vertical-menu-2,
.vertical-menu-3 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid rgb(238, 238, 238);
}
.vertical-menu-3 {
  background-color: $color-bg-2;
  left: 0;
  right: 0;
  padding: 0 20px 0 48px;
}
.vertical-menu-2 {
  padding: 0 20px;
}

.vertical-menu-3-text,
.text-type-item-a span {
  @include clamp-lines(2);
}
</style>
