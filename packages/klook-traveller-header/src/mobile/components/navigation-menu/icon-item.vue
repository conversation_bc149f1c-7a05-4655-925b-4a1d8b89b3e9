<template>
  <div class="menu-l2-node-outter">
    <div
      class="menu-l2-node-inner"
    >
      <div
        v-for="(menu, menuIndex) in itemData"
        :key="menuIndex"
        class="menu-l2-node-item"
        :data-spm-module="
          buildSpmModule(menu, itemData.length, menuIndex, itemTitle)
        "
        data-spm-virtual-item="__virtual"
        @click="jumpToUrl(menu.url)"
        v-galileo-click-tracker="menuTrackerInfo"
      >
        <span class="menu-icon"
          ><img
            v-lazy="formatPicUrl(menu.image, 96, 96)"
            :alt="buildLinkDescription(menu)"
            width="48"
            height="48"
        /></span>
        <a
          class="menu-content"
          :href="menu.url"
          @click.prevent
          :title="buildLinkDescription(menu)"
        >
          <span v-if="menu.sub_title" class="menu-text">{{
            menu.sub_title
          }}</span>
          <span v-if="menu.title" class="menu-place">{{ menu.title }}</span>
          <span v-if="menu.bottom_caption" class="menu-text">{{
            menu.bottom_caption
          }}</span>
        </a>
      </div>
    </div>
  </div>
</template>

<script>
import transformImageUrl from "@klook/klk-traveller-utils/lib/transformImageUrl";

import { buildLinkDescriptionFn, buildSpmModuleFn, jumpToUrlFn } from "./utils";
import { GalileoVuePlugin } from '@klook/galileo-vue'
import Vue from 'vue'

Vue.use(GalileoVuePlugin)
const UniversalClickEventData = 'com.klook.galileo.UniversalClick:1.0.0'

export default {
  name: "NavigationIconItem",

  inject: ["__t"],

  props: {
    itemData: {
      type: Array,
      required: true,
      default: () => [],
    },
    itemTitle: {
      type: String,
      required: true,
    },
  },
  computed: {
    menuTrackerInfo() {
      return {
        spm: 'CategoryBar_L2_LIST',
        binding: true,
        schema: {
          version: UniversalClickEventData
        }
      }
    },
  },
  methods: {
    formatPicUrl(url, width, height) {
      const _url =
        url ||
        "https://res.klook.com/image/upload/v1611304744/ued/Other/Group_871.jpg";
      return transformImageUrl(_url, { width, height, webp: true });
    },

    buildLinkDescription(menu) {
      return buildLinkDescriptionFn(menu);
    },

    jumpToUrl(url, spmModule) {
      jumpToUrlFn(url, spmModule);
    },

    buildSpmModule(menu, menuLength, menuIndex, menuName) {
      return buildSpmModuleFn(menu, menuLength, menuIndex, menuName);
    },
  },
};
</script>

<style lang="scss" scoped>
$single-item-height: 60px;
$single-item-margin: 16px;
$initial-row: 3;
$initial-height: $single-item-height * $initial-row + $single-item-margin *
  ($initial-row - 1);

.menu-l2-node-inner {
  .menu-l2-node-item {
    display: flex;
    margin-bottom: 16px;
    align-items: center;
    height: 60px;
    cursor: pointer;
    .menu-icon {
      width: 48px;
      height: 48px;
      margin-right: 10px;
      background: $color-bg-3;
      display: inline-block;
      border-radius: 25px;
      img {
        border-radius: 25px;
      }
    }

    .menu-content {
      display: flex;
      flex-direction: column;
      padding: 4.5px 0;
      justify-content: space-around;
      flex: 1;

      &:hover {
        text-decoration: none;
      }

      &:link .menu-place   { color: $color-text-primary; }
      &:visited .menu-place { color: $color-text-primary; }
      &:hover .menu-place   { color: $color-text-primary; }
      &:active .menu-place  { color: $color-brand-primary; }

      .menu-text {
        @include font-body-xs-regular;
        color: $color-text-disabled;
      }

      .menu-place {
        @include font-body-s-regular;
        color: $color-text-primary;
        overflow: hidden;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        line-clamp: 2;
        text-overflow: ellipsis;
        white-space: normal;
      }
    }
  }
  .menu-l2-node-item:last-child {
    margin-bottom: 0;
  }
}
</style>
