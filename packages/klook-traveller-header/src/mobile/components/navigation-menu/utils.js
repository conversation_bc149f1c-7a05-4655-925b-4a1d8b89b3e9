export function buildObjectId(menu) {
  try {
    return menu.track_info.object_id;
  } catch (e) {
    return "";
  }
}

export function buildLinkDescriptionFn(menu) {
  try {
    return menu.title;
  } catch (e) {
    return "";
  }
}

export function buildSpmModuleFn(menu, menuLength, menuIndex, menuName) {
  return `CategoryBar_L2_LIST?oid=${buildObjectId(menu)}&idx=${menuIndex}&len=${menuLength
    }&ext=${encodeURIComponent(
      JSON.stringify({
        L1NodeName: menuName || '',
        HyperLink: menu.url || ''
      })
    )}`
}

export function jumpToUrlFn(url) {
  window.location.assign(url)
}
