<template>
  <div class="navi-wrapper">
    <klk-tabs class="tab-content" underline v-model="activeTab" @change="onChange" :scrollable="true">
      <klk-tab-pane name="iconTab" v-if="iconTypeData.length">
        <template slot="label">
          <span> {{ __t("201738") }} </span>
        </template>
      </klk-tab-pane>
      <klk-tab-pane name="textTab" v-if="textTypeData.length">
        <template slot="label">
          <span> {{ __t("201739") }} </span>
        </template>
      </klk-tab-pane>
      <klk-tab-pane name="verticalTab" v-if="verticalTypeData.length">
        <template slot="label">
          <span> {{ __t("201739") }} </span>
        </template>
      </klk-tab-pane>
    </klk-tabs>
    <div class="navi-menu-wrapper" v-show="activeTab === 'iconTab'">
      <scroll-nav :items="iconTypeData">
        <template slot-scope="{ item, index }">
          <div v-if="item.menu_data && item.menu_data.length" class="menu-title">
            {{ item.menu_name }}
          </div>
          <icon-item
            v-if="item.menu_data && item.menu_data.length"
            :initial-row="3"
            :item-data="item.menu_data"
            :item-title="item.menu_name"
          ></icon-item>
        </template>
      </scroll-nav>
    </div>
    <div v-show="activeTab === 'textTab'">
      <div v-for="(item, index) in textTypeData" :key="index">
        <text-item
          v-for="(menu, menuIndex) in item.menu_data"
          :key="menuIndex"
          :item-data="menu"
          :item-title="item.menu_name"
          :index="menuIndex"
          :length="item.menu_data.length"
        />
      </div>
    </div>
    <div class="navi-menu-wrapper" v-show="activeTab === 'verticalTab'" v-if="verticalTypeData.length">
      <vertical-item
        :menu-data="verticalTypeData[0].menu_data"
        :menu-name="verticalTypeData[0].menu_name"
        :menu-style="verticalTypeData[0].menu_style"
      />
    </div>
  </div>
</template>

<script>
import { Tabs, TabPane } from "@klook/klook-ui/lib/tabs";

import TextItem from "./text-item.vue";
import IconItem from "./icon-item.vue";
import VerticalItem from "./vertical-item.vue";

import ScrollNav from "./scroll-nav.vue";

export default {
  name: "MwebNavigationComponent",

  inject: ["__t"],

  components: {
    klkTabs: Tabs,
    klkTabPane: TabPane,
    TextItem,
    IconItem,
    VerticalItem,
    ScrollNav,
  },

  props: {
    navigationMenuData: {
      type: Array,
      default: () => [],
    },
  },

  data() {
    return {
      activeTab: "iconTab",
    };
  },

  computed: {
    iconTypeData() {
      try {
        return this.navigationMenuData.filter(item => item.menu_style === "icon" && item.menu_data?.length);
      } catch (e) {
        return [];
      }
    },

    textTypeData() {
      try {
        return this.navigationMenuData.filter(item => item.menu_style === "text" && item.menu_data?.length);
      } catch (e) {
        return [];
      }
    },

    verticalTypeData() {
      try {
        return this.navigationMenuData.filter(
          item => item.menu_style === "menu_vertical" && item.menu_data?.length,
        );
      } catch (e) {
        return [];
      }
    },
  },

  methods: {
    onChange(type) {
      this.$emit("tabChange", type);
    },
  },

  beforeMount() {},
};
</script>

<style lang="scss" scoped>
.navi-wrapper {
  height: 100%;
  display: flex;
  flex-direction: column;
  ::v-deep .klk-tabs {
    background: #fff;
    z-index: 1;
    height: 49px;
  }

  ::v-deep .klk-tabs-body {
    margin-top: 0;
  }

  ::v-deep .klk-tab-pane {
    width: 100%;
  }

  ::v-deep .klk-tabs-item {
    flex: 1;
  }

  .navi-menu-wrapper {
    flex: 1;
    padding: 0 0;
    overflow: hidden;
    position: relative;
    overflow-x: visible;
    .menu-title {
      @include font-heading-xxs;
      color: $color-text-primary;
      margin: 0 0 16px;
      padding: 0 20px;
    }
  }

  ::v-deep .menu-l2-node-item {
    padding: 0 20px;
  }
}
</style>
