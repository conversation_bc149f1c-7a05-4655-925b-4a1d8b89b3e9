<template>
  <div class="menu-scroll-nav-wrapper">
    <div class="scroll-title-wrapper">
      <div class="scroll-nav-item-names">
        <span
          v-for="(item, index) in items"
          :key="index"
          class="scroll-nav-item-name"
          :class="{ active: activeIndex === index, 'last-child': index === items.length - 1 }"
          ref="namesRefs"
          @click="scrollToContent(index)"
        >
          <img
            v-if="item.image"
            :src="formatPicUrl(item.image, 40, 40)"
            :alt="item.title"
            width="20"
            height="20"
            class="vertical-menu-1-image"
          />
          <span>{{ item.menu_name || item.title }}</span>
        </span>
      </div>
    </div>

    <div class="scroll-nav-down-wrapper">
      <div class="scroll-nav-item-contents" ref="contentsRefs">
        <div v-for="(item, index) in items" :key="index" ref="itemRefs" class="scroll-nav-item-content">
          <slot :item="item" :index="index"></slot>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import transformImageUrl from "@klook/klk-traveller-utils/lib/transformImageUrl";
import debounce from "lodash/debounce";
export default {
  name: "ScrollNav",

  props: {
    items: {
      type: Array,
      required: true,
    },
  },
  data() {
    return {
      activeIndex: 0,
      isClickTab: false,
    };
  },
  mounted() {
    const container = this.$el.querySelector(".scroll-nav-down-wrapper");
    if (container) {
      container.addEventListener("scroll", debounce(this.handleScroll, 200));
    }
  },
  beforeDestroy() {
    const container = this.$el.querySelector(".scroll-nav-down-wrapper");
    if (container) {
      container.removeEventListener("scroll", debounce(this.handleScroll, 200));
    }
  },
  methods: {
    scrollToContent(index) {
      this.isClickTab = true;
      const containerOffsetTop = 62;
      const target = this.$refs.itemRefs[index];
      const container = this.$el.querySelector(".scroll-nav-down-wrapper");
      if (target && container) {
        const targetOffsetTop = target.offsetTop;
        this.$refs.namesRefs[index].scrollIntoView({
          behavior: "smooth",
        });

        container.scrollTo({
          top: targetOffsetTop - containerOffsetTop,
          behavior: "smooth",
        });
      }
      this.activeIndex = index;
      setTimeout(() => {
        this.isClickTab = false;
      }, 300);
    },
    formatPicUrl(url, width, height) {
      const _url = url || "https://res.klook.com/image/upload/v1611304744/ued/Other/Group_871.jpg";
      return transformImageUrl(_url, { width, height, webp: true });
    },
    handleScroll() {
      if (this.isClickTab) return;
      const containerOffsetTop = 0;
      const container = this.$el.querySelector(".scroll-nav-down-wrapper");
      if (!container) return;

      this.$refs.itemRefs.forEach((item, index) => {
        const itemTop = item.getBoundingClientRect().top;
        const containerTop = container.getBoundingClientRect().top + containerOffsetTop;

        if (itemTop <= containerTop && itemTop + item.offsetHeight > containerTop) {
          if(this.activeIndex !== index) {
            this.$refs.namesRefs[index].scrollIntoView({
              behavior: "smooth",
            });
          }
          this.activeIndex = index;
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.menu-scroll-nav-wrapper {
  height: 100%;
  -webkit-overflow-scrolling: touch; 
}

// 顶部区域
.scroll-title-wrapper {
  overflow: auto;
  height: 64px;
  scrollbar-width: none; 
  -ms-overflow-style: none;
  &::-webkit-scrollbar { 
    display: none;
  }
}

.scroll-nav-item-names {
  display: inline-flex; 
  white-space: nowrap;
  min-width: max-content; 
  padding: 20px 20px 8px;
  background-color: #fff;
  height: 65px;
}

.scroll-nav-item-name {
  cursor: pointer;
  padding: 8px 12px;
  border-radius: $radius-pill;
  background-color: $color-bg-3;
  border: 1px solid $color-bg-3;
  height: 36px;
  display: inline-flex;
  margin-right: 8px;
  align-items: center;
  flex: 0 0 auto;
}

.scroll-nav-item-name.last-child {
  margin-right: 0;
}

.scroll-nav-item-name > img {
  margin-right: 4px;
}

.scroll-nav-item-name > span {
  @include font-body-xs-bold;
  color: $color-text-primary;
  white-space: nowrap;
  word-break: break-word;
  overflow-wrap: break-word;
}

.scroll-nav-item-name.active {
  background-color: $color-brand-primary-light;
  border: 1px solid $color-brand-primary;
}

.scroll-nav-item-name.active span {
  color: $color-brand-primary;
}

// 下方内容区域
.scroll-nav-down-wrapper {
  height: calc(100% - 64px);
  overflow-y: auto;
  scrollbar-width: none; 
  -ms-overflow-style: none; 
  &::-webkit-scrollbar { 
    display: none;
  }
}
.scroll-nav-item-contents {
  overflow-y: auto;
  flex: 1;
  scrollbar-width: none; 
  -ms-overflow-style: none; 
  &::-webkit-scrollbar {
    display: none;
  }
}
.scroll-nav-item-content {
  padding-top: 32px;
}

</style>
