<template>
  <div
    v-if="itemData"
    class="text-type-item"
    :data-spm-module="buildSpmModule(itemData, length, index, itemTitle)"
    data-spm-virtual-item="__virtual"
    v-galileo-click-tracker="{ spm: 'CategoryBar_L2_LIST', componentName: 'klk-traveller-header' }"
    @click="jumpToUrl(itemData.url)"
  >
    <a
      class="text-type-item-a"
      :href="itemData.url"
      @click.prevent
      :title="buildLinkDescription(itemData)"
    >
      {{ itemData.title }}
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="16"
        height="16"
        viewBox="0 0 16 16"
      >
        <path
          fill-rule="evenodd"
          d="M8,9.29289322 L11.6464466,5.64644661 C11.8417088,5.45118446 12.1582912,5.45118446 12.3535534,5.64644661 C12.5488155,5.84170876 12.5488155,6.15829124 12.3535534,6.35355339 L8.35355339,10.3535534 C8.25592232,10.4511845 8.12796116,10.5 8,10.5 C7.87203884,10.5 7.74407768,10.4511845 7.64644661,10.3535534 L3.64644661,6.35355339 C3.45118446,6.15829124 3.45118446,5.84170876 3.64644661,5.64644661 C3.84170876,5.45118446 4.15829124,5.45118446 4.35355339,5.64644661 L8,9.29289322 Z"
          transform="rotate(-90 8 8)"
        />
      </svg>
    </a>
  </div>
</template>

<script>
import { buildLinkDescriptionFn, buildSpmModuleFn, jumpToUrlFn } from "./utils";

export default {
  name: "NavigationTextItem",

  props: {
    itemData: { type: Object, default: () => {} },
    itemTitle: {
      type: String,
      required: true,
    },

    index: {
      type: Number,
      required: true,
    },

    length: {
      type: Number,
      required: true,
    },
  },

  methods: {
    buildLinkDescription(menu) {
      return buildLinkDescriptionFn(menu);
    },

    jumpToUrl(url, spmModule) {
      jumpToUrlFn(url, spmModule);
    },

    buildSpmModule(menu, menuLength, menuIndex, menuName) {
      return buildSpmModuleFn(menu, menuLength, menuIndex, menuName);
    },
  },
};
</script>

<style lang="scss" scoped>
.text-type-item {
  padding: 13px 20px;
  border-bottom: 1px solid rgb(238, 238, 238);
  cursor: pointer;

  .text-type-item-a {
    @include font-body-m-regular();

    display: flex;
    justify-content: space-between;
    align-items: center;
    color: $color-text-primary;

    svg {
      margin-left: 8px;
      flex-shrink: 0;
    }

    &:hover {
      text-decoration: none;
    }
  }
}
</style>
