<template>
  <div class="menu-l2-node-wrapper menu-type-vertical">
    <div class="menu-l2-node-inner">
      <scroll-nav :items="menuData">
        <div class="vertical-menu-1-inner" slot-scope="{ item: menu, index: menuIndex }">
          <div class="vertical-menu-1-title">
            <img
              loading="lazy"
              :src="formatPicUrl(menu.image, 96, 96)"
              :alt="menu.title"
              width="20"
              height="20"
              class="vertical-menu-1-image"
            />
            <span>{{ menu.title }}</span>
          </div>
          <vertical-text :l2-menu-data="menu.children" :item-title="menuName" />
        </div>
      </scroll-nav>
    </div>
  </div>
</template>

<script>
import transformImageUrl from "@klook/klk-traveller-utils/lib/transformImageUrl";
import ScrollNav from "./scroll-nav.vue";
import VerticalText from "./vertical-text.vue";

export default {
  name: "NavigationVerticalItem",
  components: {
    VerticalText,
    ScrollNav,
  },
  props: {
    menuData: {
      type: Array,
      default: () => [],
    },
    menuType: {
      type: String,
      default: "menu_vertical",
    },
    menuName: {
      type: String,
      default: "Popular regions",
    },
  },

  methods: {
    buildObjectId(menu) {
      try {
        return menu.track_info.object_id;
      } catch (e) {
        return "";
      }
    },

    buildSpmModule(menu, menuIndex, menuName) {
      return `CategoryBar_L2_LIST?oid=${this.buildObjectId(menu)}&idx=${menuIndex}&len=${
        this.menuData.length
      }&ext=${encodeURIComponent(
        JSON.stringify({
          L1NodeName: menuName || "",
          HyperLink: menu.url || "",
        }),
      )}`;
    },

    formatPicUrl(url, width, height) {
      // const { webp } = this.klook;
      const _url = url || "https://res.klook.com/image/upload/v1611304744/ued/Other/Group_871.jpg";
      return transformImageUrl(_url, { width, height, webp: true });
    },

    jumpToUrl(url) {
      window.location.assign(url);
    },
  },
};
</script>

<style lang="scss" scoped>
.menu-l2-node-wrapper {
  height: 100%;
  .menu-l2-node-inner {
    height: 100%;
  }

  .vertical-menu-1 {
    padding: 16px 0 16px;
  }

  .vertical-menu-1-inner {
    flex: 1;
  }

  .vertical-menu-1-title {
    @include font-heading-xxxs;
    margin: 0px 0 16px;
    padding: 0 20px;
    display: flex;
    align-items: center;
    img {
      margin-right: 4px;
    }
  }

  .menu-type-vertical {
    margin-left: -20px;
    margin-right: -20px;
  }
}
</style>
