<template>
  <transition name="fade">
    <div v-if="visible" class="cookie-agreement">
      <div class="cookie-agreement-content" v-html="desc" />

      <IconClose
        class="icon-close"
        theme="outline"
        size="16"
        :fill="$colorTextPrimary"
        @click.native="close"
      />
    </div>
  </transition>
</template>

<script lang="ts">
import { Component } from 'vue-property-decorator';
import localStorage from '@klook/klk-traveller-utils/lib/localStorage';
import { $colorTextPrimary } from '@klook/klook-ui/lib/utils/design-token-esm'
import IconClose from '@klook/klook-icons/lib/IconClose';
import isEUArea from '@klook/klk-traveller-utils/lib/isEUArea';
import Base from '../../../common/base';

@Component({
  components: {
    IconClose
  }
})
export default class CookieAgreement extends Base {
  scrollY = 0
  visible = false
  timer: any = 0

  get $colorTextPrimary() {
    return $colorTextPrimary
  }

  get desc() {
    const languagePath = this.language === 'en' ? '' : `${this.language}/`;

    return this.__t('4132', [languagePath]);
  }

  get isEUArea() {
    return isEUArea(this.language);
  }

  handleVisible() {
    const scrollTop = document.documentElement.scrollTop || document.body.scrollTop;
    this.visible = scrollTop < 5;
  }

  debounce(fn: Function, gap: number) {
    return (...params: any) => {
      clearTimeout(this.timer);
      this.timer = setTimeout(() => {
        fn(...params);
      }, gap);
    };
  }

  handleScroll = this.debounce(this.handleVisible, 300)

  mounted() {
    const hasVisited = localStorage.getItem('has_visited') || (window.Cookies && window.Cookies.get('has_visited'));
    // 欧盟地区不使用这个弹窗
    if (!this.isEUArea && !hasVisited) {
      localStorage.setItem('has_visited', 'true');
      this.handleVisible();
      document.addEventListener('scroll', this.handleScroll);
    } else {
      document.removeEventListener('scroll', this.handleScroll);
    }
  }

  beforeDestroy() {
    document.removeEventListener('scroll', this.handleScroll);
  }

  close() {
    this.visible = false;
    document.removeEventListener('scroll', this.handleScroll);
  }
}
</script>

<style lang="scss" scoped>
  .fade-enter-active, .fade-leave-active {
    transition: opacity .2s;
  }
  .fade-enter, .fade-leave-to {
    opacity: 0;
  }

  .cookie-agreement {
    position: fixed;
    bottom: 0;
    left: 0;
    z-index: 1000;
    background: rgba(0,0,0,0.66);
    color: #ffffff;
    padding: 18px 2px 18px 18px;
    width: 100vw;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
  }

  .cookie-agreement-content {
    font-size: 12px;
    line-height: 14px;
    color: white;
  }

  .icon-close {
    flex: none;
    margin: 0 16px;
  }
</style>
