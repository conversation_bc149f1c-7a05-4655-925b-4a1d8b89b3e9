<template>
  <div class="default-header">
    <div class="default-header_left default-header_left_mobile">
      <a
        v-if="mwebShowHamburger"
        data-spm-module="TopNavigationCategories?trg=manual"
        data-spm-virtual-item="__virtual"
        v-galileo-click-tracker="{ spm: 'TopNavigationCategories', componentName: 'klk-traveller-header' }"
        @click="handleClickHamburger"
      >
        <IconMenu class="default-header_icon" theme="outline" size="24" :fill="$colorTextPrimary" />
      </a>
      <div class="mweb-logo" data-spm-module="TopNavigation">
        <logo
          v-if="isLogoVisible"
          :logo="logo"
          :is-white="isWhite"
          platform="mobile"
          data-spm-item="BackHome"
          v-galileo-click-tracker="{ spm: 'TopNavigation.BackHome', componentName: 'klk-traveller-header' }"
        />
      </div>
      <a
        v-if="anniversaryIconSrc"
        :href="__href(anniversaryLink)"
        :data-spm-module="anniversarySPMData"
        data-spm-virtual-item="__virtual"
        v-galileo-click-tracker="{ spm: anniversarySPMData, componentName: 'klk-traveller-header' }"
      >
        <img class="anniversary-icon" :src="anniversaryIconSrc" />
      </a>
    </div>

    <div v-if="!isMiniStyle" class="default-header_right default-header_right_mobile">
      <a
        v-if="showSearchEntry"
        href="javascript:;"
        data-spm-module="TopNavigationSearch?trg=manual"
        data-spm-virtual-item="__virtual"
        v-galileo-click-tracker="{ spm: 'TopNavigationSearch', componentName: 'klk-traveller-header' }"
        @click="toLinkAndTrack(__href('/search/'), 'Search Box|Search Button Clicked')"
      >
        <ClientOnly>
          <IconSearch
            class="default-header_icon"
            theme="outline"
            size="24"
            :fill="$colorTextPrimary"
          />
        </ClientOnly>
      </a>
      <template v-if="isUserVisible">
        <ClientOnly>
          <template v-if="isShoppingCartVisible">
            <a
              v-if="isLoggedIn"
              href="javascript:;"
              class="shopcart"
              data-spm-module="TopNavigationShoppingCart?trg=manual"
              data-spm-virtual-item="__virtual"
              v-galileo-click-tracker="{ spm: 'TopNavigationShoppingCart', componentName: 'klk-traveller-header' }"
              @click="toLinkAndTrack(__href('/shoppingcart/'), `Shopping Cart Page|Shopping Cart Icon Clicked|${currentPage}`)"
            >
              <IconShoppingCart
                class="default-header_icon"
                theme="outline"
                size="24"
                :fill="$colorTextPrimary"
              />
              <output v-if="shoppingCartCount">{{ shoppingCartCount }}</output>
            </a>
            <a
              v-else
              href="javascript:;"
              data-spm-module="TopNavigationShoppingCart?trg=manual"
              data-spm-virtual-item="__virtual"
              v-galileo-click-tracker="{ spm: 'TopNavigationShoppingCart', componentName: 'klk-traveller-header' }"
              @click="sdkLogin(__href('/shoppingcart'),`Shopping Cart Page|Shopping Cart Icon Clicked|${currentPage}`, 'Cart')"
            >
              <IconShoppingCart
                class="default-header_icon"
                theme="outline"
                size="24"
                :fill="$colorTextPrimary"
              />
            </a>
          </template>
        </ClientOnly>
        <a
          href="javascript:;"
          data-spm-module="TopNavigationAccount?trg=manual"
          data-spm-virtual-item="__virtual"
          v-galileo-click-tracker="{ spm: 'TopNavigationAccount', componentName: 'klk-traveller-header' }"
          @click="sdkLogin(__href('/account/'),'Account Screen|My Account Page Viewed', 'Navigator')"
        >
          <ClientOnly>
            <IconUser
              class="default-header_icon"
              theme="outline"
              size="24"
              :fill="$colorTextPrimary"
            />
          </ClientOnly>
        </a>
      </template>
      <a
        v-if="isGuestBookingVisible"
        href="javascript:;"
        @click="toLinkAndTrack(__href('/guest_booking/'), 'Account Screen|My Account Page Viewed')"
      >
        <ClientOnly>
          <IconBookings
            class="default-header_icon"
            theme="outline"
            size="24"
            :fill="$colorTextPrimary"
          />
        </ClientOnly>
      </a>
    </div>

    <BottomSheet
      v-if="mwebShowHamburger"
      height="90vh"
      class="destination-bottom-sheet"
      :visible.sync="navigationMenuVisible"
      :transfer="isTransfer"
      :title="__t('202548')"
    >
      <klk-loading v-if="isLoading" />
      <navigation-menu :navigation-menu-data="displayMenuData" />
    </BottomSheet>
  </div>
</template>

<script lang="ts">
import { Component, Inject, Prop, Watch } from "vue-property-decorator";
import setNewImageSize from "@klook/klk-traveller-utils/lib/setNewImageSize";

import { $colorTextPrimary } from "@klook/klook-ui/lib/utils/design-token-esm";
import IconSearch from "@klook/klook-icons/lib/IconSearch";
import IconMenu from "@klook/klook-icons/lib/IconMenu";
import IconShoppingCart from "@klook/klook-icons/lib/IconShoppingCart";
import IconUser from "@klook/klook-icons/lib/IconUser";
import IconBookings from "@klook/klook-icons/lib/IconBookings";

import ClientOnly from "vue-client-only";
import Logo from "../common/logo.vue";
import Anniversary from "../common/anniversary";

import BottomSheet from "@klook/klook-ui/lib/bottom-sheet";

import NavigationMenu from "./components/navigation-menu/index.vue";

import KlkLoading from "@klook/klook-ui/lib/loading";

import {
  WithGalileoPopupTracker,
  PopupStyleEnum
} from '@klook/galileo-vue'

const NewBottomSheet = WithGalileoPopupTracker(BottomSheet, {
  spm: 'CategoryPopup',
  popupType: 'web',
  popupStyle: PopupStyleEnum.BottomSheet,
  watchKey: ['visible']
})


@Component({
  components: {
    Logo,
    ClientOnly,
    IconMenu,
    IconSearch,
    IconShoppingCart,
    IconUser,
    IconBookings,
    BottomSheet: NewBottomSheet,
    NavigationMenu,
    KlkLoading
  }
})
export default class DefaultHeader extends Anniversary {
  @Prop() webp!: boolean;

  @Prop({ default: true }) isLogoVisible!: boolean;
  // navigation revamp: 新增navigationMenuData属性
  @Prop({ type: Array, default: () => [] }) navigationMenuData!: any;

  // navigation revamp: 新增
  @Prop({ default: false }) mwebShowHamburger!: boolean;

  @Prop({ default: true }) isSearchVisible!: boolean;
  @Prop({ default: true }) isUserVisible!: boolean;
  @Prop() isGuestBookingVisible!: boolean;
  @Prop() isShoppingCartVisible!: boolean;
  @Prop({ default: false }) isWhite!: boolean;
  @Prop({ default: false }) isMiniStyle!: boolean;
  // navigation revamp: isDestinationListVisible为了兼容旧版本保留，但是在组件中已经没有使用
  @Prop({ default: false }) isDestinationListVisible!: boolean;
  // navigation revamp: serverDestinationData为了兼容旧版本保留，但是在组件中已经没有使用
  @Prop({ default: null }) serverDestinationData!: any;
  // navigation revamp: serverAllCategoryList为了兼容旧版本保留，但是在组件中已经没有使用
  @Prop({ type: Array, default: () => [] }) serverAllCategoryList!: any;
  @Prop({
    default: () => ({
      link: "",
      url: "",
      height: 0
    })
  })
  logo!: {
    link?: string;
    url?: string;
    height?: number;
  };
  @Prop({ default: "global" }) market!: "global" | "cn";
  @Inject() getCurrency!: () => string;
  @Inject() getAid!: () => number;
  @Inject() getIsMP!: () => boolean;
  @Inject() getUseLoginSdk!: () => boolean;
  @Inject() loginSuccessCB!: (data: any) => void;
  @Inject() loginCancelCB!: () => void;
  @Inject() __t!: Function

  shoppingCartCount: number = 0; // 购物车里的商品个数
  currentPage = "";
  navigationMenuVisible: boolean = false;

  isTransfer: boolean = true;

  isLoading: boolean = false;

  innerMenuData: any = [];

  get $colorTextPrimary() {
    return $colorTextPrimary;
  }

  created() {
    if (this.navigationMenuData && this.navigationMenuData.length) {
      this.isTransfer = false;
    }
  }

  mounted() {
    this.getShoppingCartCount();
    this.getCurrentPage();
  }

  get showSearchEntry() {
    const excludeLanguage = !["de", "it", "fr", "ru", "es"].includes(
      this.language
    );
    return excludeLanguage && this.isSearchVisible;
  }

  getCurrentPage() {
    // 获取当前页面page 包括链接上有语言和没有语言的情况
    let page = window.location.pathname;
    const { language } = this;
    page = page.replace("/" + language, "");
    const index = page.indexOf("/", 1);
    this.currentPage =
      index === -1 ? page.substring(0) : page.substring(0, index);
  }

  updateShoppingCart() {
    this.getShoppingCartCount();
  }

  getShoppingCartCount() {
    if (this.isLoggedIn) {
      this._axios &&
        this._axios.$get("/v3/order/shoppingcart/count").then((res: any) => {
          if (res.success && res.result > 0) {
            this.shoppingCartCount = res.result;
          }
        });
    }
  }

  formatCoBrandUrl(url: string) {
    const { webp } = this;
    return setNewImageSize(url, "image/upload/", 600, -1, Number(webp), false);
  }

  sendGTM(tag: string) {
    window.tracker && window.tracker.gtm.sendGTMCustomEvent(tag);
  }

  toLinkAndTrack(href: string, tag: string) {
    if (tag) {
      this.sendGTM(tag);
    }

    window.location.href = href;
  }

  async sdkLogin(href: string, tag: string, purpose: string) {
    // 不使用sdk登录
    if (!this.getUseLoginSdk()) {
      this.toLinkAndTrack(href, tag);
      return;
    }

    if (tag) {
      this.sendGTM(tag);
    }

    if (this.isLoggedIn) {
      window.location.href = href;
      return;
    }

    const { loginWithSDK } = await import("@klook/klook-traveller-login");
    const supportLogin = await loginWithSDK({
      aid: this.getAid(),
      isMP: this.getIsMP(),
      platform: "mobile",
      language: this.language,
      currency: this.getCurrency(),
      bizName: "Platform",
      purpose,
      market: this.market,
      success: (data: any) => {
        this.loginSuccessCB && this.loginSuccessCB(data);
        if (href) {
          window.location.href = href;
        }
      },
      cancel: () => {
        this.loginCancelCB && this.loginCancelCB();
      }
    });

    if (!supportLogin) {
      window.location.href = `${this.__href("/login/")}?signin_jump=${encodeURI(
        href ?? ""
      )}`;
    }
  }

  async handleClickHamburger() {
    this.navigationMenuVisible = true;

    if (!this.displayMenuData.length) {
      this.isLoading = true;
      try {
        await this.getHamburgerData();
      } finally {
        this.$nextTick(() => {
          this.isLoading = false;
        });
      }
    }
  }

  getHamburgerData() {
    return this._axios &&
      this._axios.$get("/v1/seosrv/public/query/navigation/basic/info").then((res: any) => {
        if (res.success) {
          this.innerMenuData = res.result;
        }
      });
  }

  get displayMenuData() {
    if (this.navigationMenuData && this.navigationMenuData.length) {
      return this.navigationMenuData;
    }
    if (this.innerMenuData && this.innerMenuData.length) {
      return this.innerMenuData;
    }
    return [];
  }
}
</script>

<style lang="scss">
@import "../style/variables";
$prefix: ".default-header";

#{$prefix} {
  display: flex;
  justify-content: space-between;
  height: 48px;
  padding: 0 16px;
  background-color: #fff;

  &_left_mobile,
  &_right_mobile {
    display: flex;
    align-items: center;
    height: 100%;

    &_icon {
      fill: $color-icon-normal;
    }

    .shopcart {
      position: relative;

      output {
        display: block;
        position: absolute;
        top: -6px;
        right: -8px;
        width: 16px;
        height: 16px;
        background-color: $color-main;
        border: 1px solid #fff;
        border-radius: 50%;
        color: #fff;
        font-size: 12px;
        text-align: center;
        line-height: 14px;
      }
    }

    > a {
      line-height: 1;
    }

    a + a {
      margin-left: 16px;
    }
  }

  &_logo {
    img {
      width: auto;
      height: auto;
      max-width: 100%;
      max-height: 18px;
    }
  }
}

.mweb-logo {
  margin-left: 16px;
}

.anniversary-icon {
  height: 22px;
  width: auto;
  margin-left: 8px;
}

.destination-bottom-sheet {
  .klk-bottom-sheet-header {
    z-index: 1;
  }

  .klk-bottom-sheet-body {
    padding: 0 !important;
  }

  .destination-bottom-sheet-content {
    position: relative;
    height: 100%;
  }
}
</style>
