import dayjs from 'dayjs';
import 'dayjs/locale/de'
import 'dayjs/locale/en'
import 'dayjs/locale/es'
import 'dayjs/locale/fr'
import 'dayjs/locale/id'
import 'dayjs/locale/it'
import 'dayjs/locale/ja'
import 'dayjs/locale/ko'
import 'dayjs/locale/ru'
import 'dayjs/locale/th'
import 'dayjs/locale/vi'
import 'dayjs/locale/ms-my'
import 'dayjs/locale/zh-cn'

import { languageConfig } from '@klook/site-config'

export function formatPicUrl(url: string, webp: boolean) {
  if (/^https?:\/\//.test(url)) {
    return url;
  }

  const prefix = `https://res.klook.com/image/upload/fl_lossy.progressive,q_65/c_fill,w_160,h_160/activities/`;
  let fullUrl = `${prefix}${url}`;

  if (webp && fullUrl.indexOf('res.klook.com') > 0) {
    fullUrl = fullUrl.replace(/.(jpg|png|jpeg|gif)$/, '.webp');
  }

  return fullUrl;
}

export const currencyTextMap = {
  HKD: '29',
  USD: '30',
  TWD: '31',
  SGD: '32',
  CNY: '33',
  AUD: '34',
  GBP: '35',
  LAK: '36',
  EUR: '37',
  THB: '38',
  NZD: '39',
  MYR: '40',
  JPY: '41',
  AED: '42',
  PHP: '43',
  KRW: '44',
  VND: '45',
  IDR: '46',
  SEK: '1888',
  NOK: '1887',
  DKK: '1885',
  CHF: '1889',
  RUB: '1886',
  TRY: '12440',
  ISK: '1884',
  INR: '1891',
  KHR: '2037',
  MMK: '2038',
  MOP: '3216',
  QAR: '12442',
  OMR: '2040',
  JOD: '2041',
  LBP: '2042',
  FJD: '2496',
  CAD: '2497',
  MUR: '2498',
  MXN: '2499',
  MAD: '3575',
  EGP: '3576',
  ZAR: '3577',
  MGA: '3578',
  ILS: '74681'
};

type LanguageDayjsLocaleMap = Partial<Record<Data.Language, string>>
export const languageDayjsLocaleMap: LanguageDayjsLocaleMap = languageConfig.languageDayjsLocaleMap;

type TimeType = 'h:mma' | 'h:mm A' | 'A h:mm' | 'HH:mm'
type TimeFormatLanguageMap = Record<TimeType, Data.Language[]>
export const timeFormatLanguageMap: TimeFormatLanguageMap = {
  'h:mma': [
    'en',
    'en-AU',
    'en-CA',
    'en-GB',
    'en-HK',
    'en-IN',
    'en-MY',
    'en-NZ',
    'en-PH',
    'en-SG',
    'en-US'
  ],
  'h:mm A': ['zh-CN', 'zh-TW', 'zh-HK'],
  'A h:mm': ['ko'],
  'HH:mm': ['th', 'vi', 'id', 'ja', 'ms-MY']
};

/**
 * 获取标准日期区间格式
 * e.g.
 * '2020-08-08 08:08||18:08' -> '2020年08月08日 上午 08:08 - 下午 06:08'
 * '2020-08-08 2020-08-08' -> '2020年08月08日'
 */
export function getStandardDateRangeFormat(
  range: string,
  $t: Function,
  language: Data.Language
) {
  const terms = range.split(' ');
  const firstTerm = terms[0];
  const lastTerm = terms[1] || '';

  if (!firstTerm) {
    return '';
  }

  if (firstTerm === lastTerm) {
    return getStandardDateFormat(firstTerm, $t, language);
  }

  if (lastTerm.includes('||')) {
    const [startTime, endTime] = lastTerm.split('||');
    const startDatetime = `${firstTerm} ${startTime}`;
    const endDatetime = `${firstTerm} ${endTime}`;

    return (
      `${
        getStandardDateFormat(startDatetime, $t, language, 5)
      } - ${
        getStandardDateFormat(endDatetime, $t, language, 4)
      }`
    );
  }

  return getStandardDateFormat(firstTerm, $t, language, 5);
}

export type DateFormatType = 1 | 2 | 3 | 4 | 5 | 6

/**
 * 获取标准日期格式
 *
 * type:
 * 1: 2018年08月08日 (default),
 * 2: 2018年08月08日 18:08,
 * 3: 08月08日,
 * 4: 下午 06:08,
 * 5: 2018年08月08日 下午 06:08
 */
export function getStandardDateFormat(
  date: string | Date,
  $t: any,
  language: Data.Language,
  type: DateFormatType = 1,
  week = '' // d || dd || ddd || dddd 星期几的展示  默认不带
) {
  let datetime = date;

  dayjs.locale(getDayjsLocale(language));

  // Fix safari
  if (typeof datetime === 'string') {
    datetime = new Date(datetime.replace(/-/g, '/'));
  }

  // type 1, YYYY-MM-DD
  let formatStr = $t('1339');

  if (type === 2) {
    // Not 00:00
    if (datetime.getHours() > 0 || datetime.getMinutes() > 0) {
      formatStr += ' HH:mm';
    }
  }

  if (type === 3) {
    formatStr = $t('1340');
  }

  if (type === 4) {
    formatStr = getTimeFormatByLanguage(language);
  }

  if (type === 5) {
    formatStr += ` ${getTimeFormatByLanguage(language)}`;
  }

  if (type === 6) {
    formatStr = $t('1339');
  }
  // 增加星期几
  if (week && /d+/.test(week)) {
    formatStr += week;
  }

  if (type === 6) {
    // support 00:00
    if (datetime.getHours() >= 0 || datetime.getMinutes() >= 0) {
      formatStr += ' HH:mm';
    }
  }
  return dayjs(datetime).format(formatStr);
}

/**
 * 通过语言获取时间格式
 */
function getTimeFormatByLanguage(langauge: Data.Language) {
  const keys = Object.keys(timeFormatLanguageMap) as TimeType[];

  for (let index = 0; index < keys.length; index += 1) {
    const key = keys[index];
    const langauges = timeFormatLanguageMap[key];

    if (langauges.includes(langauge)) {
      return key;
    }
  }

  return '';
}

export function formatActLocalDate(
  date: string,
  $t: Function,
  language: Data.Language
) {
  const reg = /([-+Z][01][0-9]:[0-5][0-9]|[Zz])/; // 时区
  const dateStr = date.replace(reg, '').split('T').join(' ');
  return getStandardDateFormat(dateStr, $t, language, 2);
}

/**
 * 获取 Dayjs 的语言
 */
function getDayjsLocale(language: Data.Language) {
  return languageDayjsLocaleMap[language] || language;
}

/**
 * 根据文案占位符，格式化日期
 *      participation_date_desc: {start_time}到{end_time}之间可以用
 *      participation_date_map:{
 *      start_time: 2021-01-31 14:12:12,
 *      end_time: 2021-03-31 14:12:12
 *      }
 */
export function formatDateDesc(
  dateDesc: string,
  dateMap: any,
  $t: Function,
  language: Data.Language
) {
  if (!Object.keys(dateMap || {}).length) {
    return dateDesc;
  }

  for (const key in dateMap) {
    dateDesc = dateDesc.replace(key, formatActLocalDate(dateMap[key], $t, language));
  }

  return dateDesc;
}
