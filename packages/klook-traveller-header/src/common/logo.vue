<template>
  <a
    :href="logo.link || __href('/')"
    class="default-header_logo-wrapper"
    @click="handleClick"
  >
    <img
      v-if="isImageType"
      class="default-header_logo"
      :src="logo.url"
      :style="{ height: `${logo.height}px`}"
    />
    <svg
      v-else
      xmlns="http://www.w3.org/2000/svg"
      :width="size.width"
      :height="size.height"
      :viewBox="size.viewBox"
    >
      <g id="logo-icon">
        <path d="M2.54763 8.36536C3.06015 8.61137 3.59026 8.82129 4.13396 8.99281C5.30016 9.3607 5.46157 9.20603 4.95716 8.16962C4.74383 7.73129 4.50605 7.30996 4.24603 6.9069C3.73787 6.11922 3.39541 5.82966 2.58517 6.51148C2.42393 6.64717 2.27315 6.79368 2.13362 6.94961C1.64432 7.49646 1.65049 7.93473 2.54763 8.36536Z" :fill="isWhite ? '#FFF' : '#FF5B00'" />
        <path d="M14.1387 30.6133C13.9381 30.042 13.697 29.4846 13.4164 28.9461C12.8147 27.7909 12.5758 27.7861 12.1718 28.948C12.001 29.4393 11.8614 29.9363 11.7525 30.4362C11.5397 31.4131 11.579 31.8893 12.7068 31.9844C12.9312 32.0033 13.1559 32.0062 13.3793 31.9935C14.1627 31.9489 14.4898 31.6134 14.1387 30.6133Z" :fill="isWhite ? '#FFF' : '#4D40CA' " />
        <path d="M29.9921 22.1916C29.5357 21.6588 29.1166 21.0934 28.739 20.4994C27.9292 19.2255 28.0621 18.9832 29.4582 19.2528C30.0487 19.3668 30.6266 19.5154 31.1899 19.6965C32.2907 20.0503 32.7449 20.3662 32.1901 21.552C32.0797 21.788 31.9531 22.015 31.8116 22.2316C31.3156 22.991 30.7912 23.1243 29.9921 22.1916Z" :fill="isWhite ? '#FFF' : '#00CBD0' " />
        <path d="M22.4408 1.98794C22.0369 2.33178 21.6084 2.64744 21.1582 2.93187C20.1926 3.54195 20.009 3.44178 20.2133 2.39013C20.2997 1.94537 20.4124 1.50999 20.5496 1.08569C20.8178 0.2565 21.0572 -0.0856586 21.956 0.332238C22.1349 0.4154 22.3069 0.510793 22.4711 0.617347C23.0467 0.991023 23.1477 1.38605 22.4408 1.98794Z" :fill="isWhite ? '#FFF' : '#FFC200' " />
        <path d="M5.42233 21.2638C0.93364 23.938 -0.367496 20.5653 0.0845712 16.146C0.792365 9.2268 5.42233 9.10795 7.59725 14.0515C8.91167 17.0392 8.41049 19.4836 5.42233 21.2638Z" :fill="isWhite ? '#FFF' : '#00CBD0'" />
        <path d="M10.6485 26.0616C10.6485 26.811 10.6177 27.5533 10.5574 28.2872C10.4335 29.7926 9.45576 31.049 7.46783 29.7926C6.65317 29.2777 5.88662 28.6937 5.17642 28.0488C3.53171 26.5552 3.11245 25.1077 5.17642 23.3024C5.6975 22.8466 6.23639 22.4107 6.79193 21.9958C9.37625 20.0658 10.3777 21.3717 10.5759 24.0743C10.624 24.7305 10.6485 25.3932 10.6485 26.0616Z" :fill="isWhite ? '#FFF' : '#FF5B00' " />
        <path d="M25.6517 24.6001C24.0483 23.2415 22.3469 21.9943 20.5596 20.8704C16.726 18.4598 15.9969 18.8556 16.8081 23.0109C17.1512 24.7683 17.5985 26.4886 18.1434 28.1651C19.2081 31.4414 20.1587 32.7933 23.7271 31.1421C24.4373 30.8135 25.1203 30.4366 25.772 30.0156C28.0574 28.5391 28.4584 26.9783 25.6517 24.6001Z" :fill="isWhite ? '#FFF' : '#FFC200'" />
        <path d="M23.7952 7.67218C23.3049 8.31326 22.8329 8.96882 22.3797 9.6381C19.6944 13.6037 19.9603 13.7517 23.7952 15.4257C25.1767 16.0289 26.5971 16.5603 28.0514 17.0154C33.3695 18.6794 34.1829 16.8212 32.6334 12.2089C32.1916 10.8938 31.5799 9.65613 30.8244 8.52163C28.4711 4.98797 26.5415 4.08062 23.7952 7.67218Z" :fill="isWhite ? '#FFF' : '#FF5B00' " />
        <path d="M15.4106 0.0412101C14.0773 0.169808 12.7917 0.461057 11.574 0.894807C8.25614 2.07667 8.74808 3.46615 10.576 6.12C10.9533 6.66782 11.3431 7.20642 11.7451 7.73538C13.8749 10.5385 14.4369 11.1585 16.165 7.18391C16.7289 5.88706 17.23 4.55679 17.6646 3.19692C18.4003 0.894806 18.2869 -0.236201 15.4106 0.0412101Z" :fill="isWhite ? '#FFF' : '#4D40CA'" />
      </g>
      <g id="logo-en">
        <path :class="['logo-en-fill',{ 'logo-en-fill--white':isWhite }]" xmlns="http://www.w3.org/2000/svg" d="M110.033 11.2082L105.034 16.6937L105.033 5.86887C105.033 5.49526 104.658 5.23652 104.308 5.36831L101.74 6.33195C101.531 6.41054 101.393 6.61003 101.393 6.8325V24.8478C101.393 25.144 101.634 25.3834 101.929 25.3834H104.497C104.794 25.3834 105.034 25.1428 105.034 24.8478V19.1155L110.366 25.1996C110.468 25.3157 110.614 25.3822 110.77 25.3822H114.126C114.589 25.3822 114.834 24.8369 114.527 24.4923L108.583 17.8146L114.057 11.9324C114.376 11.5902 114.132 11.0328 113.665 11.0328H110.429C110.279 11.0328 110.135 11.0969 110.033 11.2082Z" />
        <path :class="['logo-en-fill',{ 'logo-en-fill--white':isWhite }]" d="M91.1668 25.8527C87.0009 25.8527 83.6133 22.3317 83.6133 18.0026C83.6133 13.908 86.8803 10.7007 91.0499 10.7007C95.4265 10.7007 98.6034 13.8862 98.6034 18.2773C98.6034 22.6684 95.4765 25.8527 91.1668 25.8527ZM91.2801 22.463C93.452 22.463 95.0282 20.7493 95.0282 18.3903C95.0282 15.8818 93.4861 14.1304 91.2801 14.1304C88.9852 14.1304 87.3833 15.821 87.3833 18.242C87.3833 20.6485 89.0595 22.463 91.2801 22.463Z" />
        <path :class="['logo-en-fill',{ 'logo-en-fill--white':isWhite }]" d="M73.9871 25.8527C69.8224 25.8527 66.4336 22.3317 66.4336 18.0026C66.4336 13.908 69.7006 10.7007 73.8702 10.7007C78.2481 10.7007 81.4249 13.8862 81.4249 18.2773C81.4249 22.6684 78.2968 25.8527 73.9871 25.8527ZM74.1004 22.463C76.2723 22.463 77.8485 20.7493 77.8485 18.3903C77.8485 15.8818 76.3064 14.1304 74.1004 14.1304C71.8055 14.1304 70.2037 15.821 70.2037 18.242C70.2037 20.6874 71.842 22.463 74.1004 22.463Z" />
        <path :class="['logo-en-fill',{ 'logo-en-fill--white':isWhite }]" d="M60.0078 24.9472L60.0285 6.82125C60.0285 6.59762 60.1674 6.39709 60.3781 6.31809L62.9045 5.37132C63.2577 5.23884 63.6341 5.50015 63.6329 5.87569L63.6049 24.9484C63.6049 25.245 63.3637 25.4856 63.0653 25.4856H60.5462C60.249 25.4856 60.0078 25.2438 60.0078 24.9472Z" />
        <path :class="['logo-en-fill',{ 'logo-en-fill--white':isWhite }]" d="M52.9732 11.2082L47.9745 16.6937L47.9733 5.86887C47.9733 5.49526 47.5988 5.23652 47.2486 5.36831L44.6808 6.33195C44.4712 6.41054 44.333 6.61003 44.333 6.8325V24.8478C44.333 25.144 44.5742 25.3834 44.8698 25.3834H47.4377C47.7346 25.3834 47.9745 25.1428 47.9745 24.8478V19.1155L53.3065 25.1996C53.4082 25.3157 53.5549 25.3822 53.71 25.3822H57.0667C57.5296 25.3822 57.7744 24.8369 57.4678 24.4923L51.5239 17.8146L56.9976 11.9324C57.3163 11.5902 57.0728 11.0328 56.605 11.0328H53.3695C53.2192 11.0328 53.075 11.0969 52.9732 11.2082Z" />
      </g>
      <g
        v-if="isCNType"
        id="logo-cn"
      >
        <path :class="['logo-cn-fill',{ 'logo-cn-fill--white':isWhite }]" fill-rule="evenodd" clip-rule="evenodd" d="M149.211 17.0486H154.626C155.592 17.0486 156.376 17.8314 156.376 18.7935V24.1388C156.376 25.1008 155.592 25.8837 154.626 25.8837H149.211C148.247 25.8837 147.461 25.1008 147.461 24.1388V18.7935C147.461 17.8314 148.247 17.0486 149.211 17.0486ZM149.716 23.9728H154.123V18.9594H149.716V23.9728Z" />
        <path :class="['logo-cn-fill',{ 'logo-cn-fill--white':isWhite }]" fill-rule="evenodd" clip-rule="evenodd" d="M146.776 23.6121L146.456 22.329C146.432 22.2303 146.37 22.1486 146.283 22.0957C146.197 22.044 146.095 22.0283 145.995 22.0524L143.581 22.6536V19.0304H145.339C145.548 19.0304 145.718 18.8608 145.718 18.6516V17.33C145.718 17.1207 145.548 16.95 145.339 16.95H143.581V14.7H144.074C144.907 14.7 145.584 14.0242 145.584 13.1932V8.8737C145.584 8.04395 144.908 7.36812 144.074 7.36812H139.746C138.915 7.36812 138.236 8.04275 138.236 8.8737V13.1932C138.236 14.023 138.913 14.7 139.746 14.7H141.496V23.1707L140.151 23.505V17.0702C140.151 16.861 139.981 16.6914 139.771 16.6914H138.447C138.237 16.6914 138.067 16.861 138.067 17.0702V24.0245L137.736 24.1063C137.532 24.1556 137.408 24.3625 137.459 24.5645L137.779 25.8476C137.823 26.0196 137.978 26.1362 138.148 26.1362C138.178 26.1362 138.208 26.1326 138.24 26.1242L146.499 24.0726C146.598 24.0486 146.681 23.9873 146.734 23.9007C146.786 23.8129 146.802 23.7107 146.776 23.6121ZM143.499 12.7904H140.322V9.28016H143.499V12.7904Z" />
        <path :class="['logo-cn-fill',{ 'logo-cn-fill--white':isWhite }]" d="M157.189 14.7181C157.391 14.7253 157.551 14.8912 157.553 15.0945L157.56 16.4112C157.56 16.5171 157.52 16.6133 157.445 16.6866C157.372 16.7576 157.279 16.7949 157.179 16.7949H157.167C155.064 16.7299 153.241 15.9386 151.769 14.9514C150.308 15.9326 148.506 16.7179 146.425 16.7924C146.421 16.7936 146.415 16.7936 146.411 16.7936C146.311 16.7936 146.217 16.7552 146.146 16.6854C146.073 16.6145 146.03 16.5171 146.03 16.4136V15.0909C146.03 14.8876 146.19 14.7217 146.393 14.7133C147.631 14.6615 148.853 14.2839 150.045 13.5901C149.293 12.8986 148.701 12.224 148.289 11.7081C148.835 11.4315 149.473 10.924 149.915 10.4094C150.321 10.924 150.95 11.6432 151.765 12.3466C152.94 11.3281 153.736 10.2687 154.037 9.83574V9.27776H150.093C150.088 9.28608 150.082 9.29433 150.077 9.30252C150.06 9.32748 150.043 9.35192 150.026 9.37637L150.025 9.37686C150.003 9.40917 149.98 9.44147 149.958 9.47377C149.894 9.56517 149.825 9.65536 149.753 9.74434C149.731 9.77441 149.708 9.80327 149.685 9.82973C149.649 9.87542 149.611 9.91992 149.574 9.962C149.016 10.6102 148.453 11.0058 147.952 11.2716C147.48 11.5205 146.974 11.6889 146.45 11.7454L146.446 11.7457C146.433 11.7468 146.421 11.7478 146.409 11.7478C146.315 11.7478 146.224 11.7129 146.156 11.6504C146.075 11.5782 146.029 11.476 146.029 11.3678V10.0318C146.029 9.85017 146.158 9.69624 146.341 9.65896C147.365 9.45333 148.736 8.14737 148.905 6.83539C148.93 6.649 149.092 6.5083 149.282 6.5083H150.612C150.719 6.5083 150.823 6.55279 150.894 6.63216C150.966 6.71153 151.002 6.81736 150.991 6.92438C150.977 7.07109 150.953 7.22021 150.921 7.36932H154.613C155.447 7.36932 156.123 8.04515 156.123 8.87491V10.0823C156.123 10.3312 156.051 10.5717 155.915 10.7797C155.739 11.0527 154.889 12.301 153.489 13.5901C154.699 14.2936 155.936 14.6724 157.189 14.7181Z" />
        <path :class="['logo-cn-fill',{ 'logo-cn-fill--white':isWhite }]" d="M120.386 11.2456C120.386 11.4549 120.216 11.6244 120.006 11.6244H118.511C118.302 11.6244 118.132 11.4549 118.132 11.2456V8.87661C118.132 8.04685 118.809 7.37102 119.641 7.37102H126.085L126.631 6.00253C126.708 5.80892 126.93 5.71392 127.124 5.79088L128.355 6.27911C128.449 6.31639 128.524 6.38855 128.563 6.48114C128.603 6.57374 128.604 6.67716 128.567 6.77095L128.328 7.37102H134.72C135.551 7.37102 136.229 8.04685 136.229 8.87661V11.2456C136.229 11.4549 136.059 11.6244 135.849 11.6244H134.355C134.145 11.6244 133.975 11.4549 133.975 11.2456V9.28186H120.386V11.2456Z" />
        <path :class="['logo-cn-fill',{ 'logo-cn-fill--white':isWhite }]" fill-rule="evenodd" clip-rule="evenodd" d="M122.034 18.9984H132.497C133.462 18.9984 134.246 19.7813 134.246 20.7445V24.1381C134.246 25.1001 133.462 25.883 132.497 25.883H122.034C121.07 25.883 120.286 25.1001 120.286 24.1381V20.7445C120.286 19.7813 121.07 18.9984 122.034 18.9984ZM122.539 23.9733H131.992V20.9093H122.539V23.9733Z" />
        <path :class="['logo-cn-fill',{ 'logo-cn-fill--white':isWhite }]" d="M135.861 16.4767C133.906 16.4526 132.104 16.1893 130.505 15.8153C131.803 15.2188 132.688 14.6981 133.047 14.4756C133.427 14.2412 133.653 13.8347 133.653 13.3873V12.4061C133.653 11.5763 132.977 10.8993 132.144 10.8993H124.634C124.692 10.6961 124.734 10.4928 124.757 10.2908C124.768 10.1862 124.734 10.0791 124.663 9.99978C124.592 9.91921 124.487 9.87352 124.379 9.87352H123.035C122.855 9.87352 122.702 9.99858 122.665 10.1778C122.489 11.0087 121.574 12.3784 119.864 12.5119C119.671 12.5263 119.518 12.6923 119.518 12.8895V14.2135C119.518 14.3181 119.563 14.4191 119.638 14.4913C119.708 14.5574 119.799 14.5935 119.894 14.5935C119.902 14.5935 119.908 14.5935 119.917 14.5923C120.425 14.5622 120.882 14.4672 121.292 14.3301C121.303 14.3263 121.314 14.3226 121.324 14.3189C121.341 14.3134 121.357 14.3078 121.374 14.3013L121.374 14.3012C121.428 14.282 121.483 14.2628 121.534 14.2424C122.136 14.0067 122.823 13.587 123.405 12.9857C123.442 12.9484 123.48 12.9087 123.513 12.8691C123.531 12.8498 123.548 12.8318 123.565 12.8125H131.569V12.9364C130.886 13.3308 129.399 14.1257 127.342 14.8617C125.811 14.2905 124.628 13.6868 123.88 13.2635C123.517 13.7289 122.691 14.2893 122.053 14.5959C122.452 14.8532 123.181 15.2934 124.194 15.79C122.523 16.1809 120.648 16.4538 118.638 16.4791C118.43 16.4815 118.261 16.651 118.261 16.8579V18.1807C118.261 18.2829 118.302 18.3791 118.374 18.4501C118.445 18.5198 118.539 18.5595 118.639 18.5595H118.644C121.961 18.521 124.927 17.8596 127.305 17.0755C129.599 17.8596 132.506 18.5198 135.855 18.5595H135.859C135.959 18.5595 136.053 18.521 136.124 18.4501C136.198 18.3791 136.237 18.2829 136.237 18.1807V16.8579C136.237 16.6498 136.069 16.4791 135.861 16.4767Z" />
      </g>
    </svg>
  </a>
</template>

<script lang="ts">
import { Component, Prop } from 'vue-property-decorator';
import Base from './base';

type LanguageLogoMap = Partial<Record<Data.Language, string>>
const languageLogoMap: LanguageLogoMap = {
  en: 'en',
  'zh-CN': 'cn'
};

@Component
export default class Logo extends Base {
  @Prop({ default: "desktop" }) platform!: string;
  @Prop({ default: false }) isWhite!: boolean;
  @Prop({ default: () => ({ link: "", url: "", height: 0 }) }) logo!: {
    url: string;
    height: number;
    link: string;
  };

  get type() {
    return languageLogoMap[this.language] || languageLogoMap.en;
  }

  get isImageType() {
    return this.logo && this.logo.url;
  }

  get isCNType() {
    return this.type === "cn";
  }

  handleClick() {
    window.tracker && window.tracker.gtm.sendGTMCustomEvent('Others|Logo Clicked');
  }

  get size() {
    // cn
    const config: Record<string, any> = {
      cn: {
        viewBox: '0 0 158 32',
        mobile: {
          width: 138,
          height: 28
        },
        desktop: {
          width: 158,
          height: 32
        }
      },
      en: {
        viewBox: '0 0 115 32',
        mobile: {
          width: 101,
          height: 28
        },
        desktop: {
          width: 115,
          height: 32
        }
      }
    };

    const curConf = config[this.type!] || config.en;

    return {
      viewBox: curConf.viewBox,
      ...(curConf[this.platform] || curConf.desktop)
    };
  }
}
</script>

<style lang="scss">
.default-header_logo-wrapper {
  font-size: 0;
}

.logo-en-fill {
  fill: $color-brand-primary;

  &--white {
    fill: $color-common-white
  }
}

.logo-cn-fill {
  fill: $color-brand-primary;

  &--white {
    fill: $color-common-white
  }
}
</style>
