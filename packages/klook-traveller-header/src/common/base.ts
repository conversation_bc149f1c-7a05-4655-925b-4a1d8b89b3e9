import { Vue, Component, Inject } from 'vue-property-decorator';

@Component
export default class Base extends Vue {
  @Inject() __t!: Function
  @Inject() __href!: Function
  @Inject() language!: Data.Language
  @Inject() getUser!: () => Data.IUser

  _axios!: any

  get displayRewardsEntry(){
    if(this.user){
      return this.user.display_membership_entry
    }else {
      return false
    }
  }

  get userResidence(){
    if(this.user){
      return this.user.user_residence
    }else {
      return ''
    }
  }

  get userMembershipLevel(){
    if(this.user){
      return this.user.membership_level
    }else {
      return 0
    }
  }

  get user() {
    return this.getUser()
  }

  get isLoggedIn() {
    return !!this.user;
  }

  beforeMount() {
    this._axios = this.$attrs.axios || window.$axios;
  }
}
