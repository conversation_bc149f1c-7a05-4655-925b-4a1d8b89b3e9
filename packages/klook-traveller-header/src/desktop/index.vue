<template>
  <nav class="default-header" data-spm-module="TopNavigation">
    <div class="default-header_left">
      <Logo v-if="isLogoVisible" data-spm-item="BackHome" v-galileo-click-tracker="{ spm: 'TopNavigation.BackHome', componentName: 'klk-traveller-header' }" :logo="logo" :is-white="isWhite" />
      <a
        v-if="anniversaryIconSrc"
        :href="__href(anniversaryLink)"
        :data-spm-item="anniversarySPMData"
        v-galileo-click-tracker="{ spm: anniversarySPMData, componentName: 'klk-traveller-header' }"
      >
        <img
          v-if="anniversaryIconSrc"
          class="anniversary-icon"
          :src="anniversaryIconSrc"
        />
      </a>
      <ClientOnly>
        <SearchBox
        v-if="isSearchVisible && !isMiniStyle"
        :is-simple="false"
        :language="language"
        class="default-header_search-box"
        :is-recommendation-visible="$props.isRecommendationVisible"
        @update:isRecommendationVisible="$listeners['update:isRecommendationVisible']"
      />
      </ClientOnly>
    </div>

    <ClientOnly>
      <div v-if="!isMiniStyle" class="default-header_right">
        <template v-if="market === 'global'">
          <LanguageSelector
            :render-languages="renderLanguages"
            :is-change-site-tip-visible="isChangeSiteTipVisible"
          />

          <CurrencySelector
            :currency="currency"
            :support-currencies="supportCurrencies"
            :is-change-site-tip-visible="isChangeSiteTipVisible"
          />
        </template>
        <DownloadApp v-if="isDownloadAppVisible" />

        <Wechat v-if="isWeChatVisible" />

        <HelpCenter />

        <RecentlyReview :webp="webp" />

        <GuestBooking v-if="isOrderVisible" />

        <template v-else>
          <ShoppingCart
            ref="shoppingCart"
            v-if="user && isShoppingCartVisible"
            :currency="currency"
            :webp="webp"
          />

          <PanelUser :webp="webp" :market="market" />
        </template>

      </div>
    </ClientOnly>
  </nav>
</template>

<script lang="ts">
import { Component, Prop } from 'vue-property-decorator';
import ClientOnly from 'vue-client-only';
import Logo from '../common/logo.vue';
import Anniversary from '../common/anniversary';
import SearchBox from './components/search-box.vue';
import LanguageSelector from './components/language/index.vue';
import CurrencySelector from './components/currency/index.vue';
import RecentlyReview from './components/recently-review/index.vue';
import ShoppingCart from './components/shopping-cart/index.vue';
import PanelUser from './components/user/index.vue';
import DownloadApp from './components/download-app/index.vue';
import Wechat from './components/wechat/index.vue';
import HelpCenter from './components/help-center/index.vue';
import GuestBooking from './components/guest-booking/index.vue';

interface ICurrencyLists {
  suggestCurrencies: string[]
  otherCurrencies: string[]
}

@Component({
  components: {
    Wechat,
    PanelUser,
    HelpCenter,
    SearchBox,
    ClientOnly,
    Logo,
    LanguageSelector,
    CurrencySelector,
    RecentlyReview,
    ShoppingCart,
    DownloadApp,
    GuestBooking
  }
})
export default class KlkTravellerHeader extends Anniversary {
  @Prop({ default: true }) isSearchVisible!: boolean
  @Prop({ default: true }) isLogoVisible!: boolean
  @Prop() isSimpleSearch!: boolean
  @Prop() renderLanguages!: any

  @Prop({
    default: () => ({
      suggestCurrencies: [],
      otherCurrencies: []
    })
  })
  supportCurrencies!: ICurrencyLists

  @Prop() isChangeSiteTipVisible!: boolean
  @Prop() currency!: string
  @Prop() isOrderVisible!: boolean
  @Prop({ default: true }) isDownloadAppVisible!: boolean
  @Prop() webp!: boolean
  @Prop({ default: true }) isShoppingCartVisible!: boolean
  @Prop({ default: () => ({ url: '', height: 0 }) }) logo!: { url: string, height: number }
  @Prop({ default: false }) isWhite!: boolean
  @Prop({ default: false }) isRecommendationVisible!: boolean
  @Prop({ default: 'global' }) market!: 'global'|'cn'
  @Prop({ default: false }) isMiniStyle!: boolean

  hideMenuText: boolean = false // 控制右边菜单文案是否展示

  get isWeChatVisible() {
    return this.language && this.language.includes('zh-CN') && this.currency && this.currency.includes('CNY');
  }

  updateShoppingCart() {
    const shoppingCart  = this.$refs.shoppingCart as any
    if (shoppingCart) {
      shoppingCart.updateShoppingCart()
    }
  }
}
</script>

<style lang="scss" scoped>
.default-header {
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 1160px;
  height: 60px;
  margin: 0 auto;
  z-index: 10;

  .default-header_search-box {
    margin-left: 16px;
  }
}

.default-header_left {
  display: flex;
  align-items: center;

  > li + li {
    margin-left: 16px;
  }
}

.default-header_right {
  display: flex;
  align-items: center;
  padding: 0;
  margin: 0;
  list-style: none;
}
</style>

<style lang="scss">
@import "../style/base";

.default-header {
  ul {
    list-style-type: none;
  }

  span {
    overflow-wrap: normal;
  }

  svg {
    flex-shrink: 0;
  }

  a {
    text-decoration: none;
  }
}
.anniversary-icon{
  height: 24px;
  width: auto;
  margin-left: 8px;
  &.has-link{
    cursor: pointer;
  }
}
</style>
