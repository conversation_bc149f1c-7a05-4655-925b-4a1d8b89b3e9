<template>
  <Panel
    class="default-header_download-app"
    :link="__href('/tetris/appdownload/default/?ref-source=NavigationBar')"
    :content="__t('12998')"
    data-spm-item="LandingpageAppBtn"
    @click="handleClick"
  />
</template>

<script lang="ts">
import { Component } from 'vue-property-decorator';
import Base from '../../../common/base';
import Panel from '../panel.vue';

@Component({
  components: {
    Panel
  }
})
export default class DownloadApp extends Base {
  showOTA = false

  handleClick() {
    window.tracker && window.tracker.gtm.sendGTMCustomEvent('Download App|Download App Button Clicked on Header');
  }

  show() {
    this.showOTA = true
  }

}
</script>

<style lang="scss">
.default-header_download-app {
  .klk-poptip-popper[data-popper-placement^="bottom"]::after {
    left: calc(50% + 110px);
  }
}
</style>
