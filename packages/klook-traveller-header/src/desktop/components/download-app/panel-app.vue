<template>
  <div class="panel-app">
    <div class="panel-app_top">
      <div class="panel-app_icon" />
      <div class="panel-app_text">
        <b>{{ __t('1592') }}</b>
        <span v-if="coupon">
          {{ __t('12478', [coupon.code, coupon.amount]) }}
        </span>
      </div>
    </div>
    <div class="panel-app_bottom">
      <div class="panel-app_qrcode" />
      <div class="panel-app_market">
        <a class="panel-app_ios" :href="__t('12')" />
        <a class="panel-app_android" @click="handleDownloadApk" />
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Component } from 'vue-property-decorator';
import Base from '../../../common/base';

@Component
export default class PanelApp extends Base {
  coupon = null

  getCoupon() {
    this._axios && this._axios.$get('/v1/usrcsrv/refer/amount').then((res) => {
      this.coupon = res.result;
    });
  }

  handleDownloadApk() {
    this._axios && this._axios.$get(
      '/web2/download.action',
      { baseURL: '/' }
    ).then((res: any) => {
      if (res.success && res.result) {
        window.location.href = res.result.downland_url;
      }
    });
  }

  mounted() {
    this.getCoupon();
  }
}
</script>

<style lang="scss">
@import "../../../style/_variables.scss";

.panel-app {
  padding: 24px 20px;

  &_top {
    display: flex;
    padding-bottom: 16px;
    margin-bottom: 16px;
    border-bottom: 1px solid $color-border-dim;
  }

  &_icon {
    @include retina-sprite('https://res.klook.com/image/upload/v1646305445/wcwk08wt3phsbcdee5m4.png ',
      'https://res.klook.com/image/upload/v1646305445/pjouhb5razi9tedkqhgi.png ',
      40px, 40px
    );
  }

  &_text {
    flex: 1;
    margin-left: 12px;

    b {
      display: block;
      font-size: $fontSize-body-m;
    }

    span {
      display: block;
      margin-top: 6px;
      color: $color-text-secondary;
      font-size: $fontSize-caption-m;
    }
  }

  &_bottom {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

&_qrcode {
    @include retina-sprite('https://res.klook.com/image/upload/v1646305446/kos0bcfiq1yjbranwpdr.png',
      'https://res.klook.com/image/upload/v1646305445/t7gpi8qqqfx811ugitod.png',
      100px, 99px
    );
  }

  &_market {
    a {
      display: block;
    }
  }

  &_ios {
    &:lang(zh-Hans-CN) {
      @include retina-sprite('https://res.klook.com/image/upload/v1631183203/mx3vdiccxpgpfosurw1r.png',
        'https://res.klook.com/image/upload/v1631183256/sctemdlgoab3vcjdsbti.png',
        140px, 42px
      );
    }

    &:lang(zh-Hant-HK),
    &:lang(zh-Hant-TW),
    &:lang(zh-Hant) {
      @include retina-sprite('https://res.klook.com/image/upload/v1631183368/kqpjjsfrazrqljpjxkbd.png',
        'https://res.klook.com/image/upload/v1631183377/opxd0943osow8fjj2ftq.png',
        140px, 42px
      );
    }

    @include retina-sprite(
      'https://res.klook.com/image/upload/v1631183398/pmie1mibumhqvjwiwt8g.png',
      'https://res.klook.com/image/upload/v1631183393/vrluzlwqitke02fanpog.png',
      140px, 42px
    );
  }

  &_android {
    margin-top: 8px;

    &:lang(zh-Hans-CN) {
      @include retina-sprite('https://res.klook.com/image/upload/v1631183411/neinlw1vczqayukkel0q.png',
        'https://res.klook.com/image/upload/v1631183417/wqaiyhygry33hpy5mhy6.png',
        140px, 42px
      );
    }

    &:lang(zh-Hant-HK),
    &:lang(zh-Hant-TW),
    &:lang(zh-Hant) {
      @include retina-sprite('https://res.klook.com/image/upload/v1631183435/jbllf6uqc9ijseqxgsuh.png',
        'https://res.klook.com/image/upload/v1631183430/q2irg5481o5em03sutbb.png',
        140px, 42px
      );
    }

    @include retina-sprite('https://res.klook.com/image/upload/v1631183446/fgk9gvcdbikofipt9hkq.png',
      'https://res.klook.com/image/upload/v1631183451/bnebjw18ei9tcjmpl1c6.png',
      140px, 42px
    );
  }
}
</style>
