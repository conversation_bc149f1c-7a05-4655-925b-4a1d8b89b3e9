<template>
  <ClientOnly>
    <div
      v-if="otaData && otaData.type"
      class="ota"
      :class="['app-download', otaData.position]"
    >
      <app-download-mobile-top
        v-if="isTopFixed"
        v-bind="otaData"
        class="download"
      >
        <div v-if="otaData.downButtonImage" slot="download-button">
          <img :src="otaData.downButtonImage" alt="" />
        </div>
      </app-download-mobile-top>

      <app-download v-else v-bind="otaData" class="download" :style="otaStyle">
        <div v-if="otaData.downButtonImage" slot="download-button">
          <img :src="otaData.downButtonImage" alt="" />
        </div>
      </app-download>
    </div>
    <div v-else></div>
  </ClientOnly>
</template>

<script lang="ts">
import { Component, Prop } from "vue-property-decorator";
import ClientOnly from "vue-client-only";
import "@klook/klk-app-download/dist/cjs/index.css";
import Base from "../../../common/base";

interface OtaInfoOption {
  style?: {
    show_close: Boolean;
    scale: "small" | "medium" | "large" | "bottomFixed" | "topFixed";
    position: // web
    | "header"
      | "rightBottomFixed"
      | "staticBanner"
      | "bottomFixed"
      // mweb
      | "small"
      | "topFixed";
  };
  time?: {
    has_countdown: Boolean; // 是否有倒计时
    from_time?: any;
    to_time?: any;
  };
  onelink?: {
    qr_desc?: string;
    link?: string; // 生成二维码链接
  };
  banner?: {
    img?: string; // banner_img
    title?: string;
    desc?: any[];
  };
  download_button?: {
    img?: string;
    desc?: string;
  };
  bg?: {
    img?: string;
    color: string;
  };
  tracking?:
    | {
        download?: {
          spm?: string;
          extra?: {
            [key: string]: any;
          };
        };
        close?: {
          spm?: string;
          extra?: {
            [key: string]: any;
          };
        };
        module?: {
          spm?: string;
          extra?: {
            [key: string]: any;
          };
        };
      }
    | any;
}

@Component({
  components: {
    ClientOnly,
    // @ts-ignore
    AppDownloadMobileTop: () => import("@klook/klk-app-download/dist/esm/top"),
    // @ts-ignore
    AppDownload: () => import("@klook/klk-app-download/dist/esm/download")
  }
})
export default class OTA extends Base {
  mo!: MutationObserver;

  @Prop({ default: "navigation", type: String }) location!: string;

  otaStyle = {};

  get isTopFixed() {
    return (
      this.otaData.type === "topFixed" && this.otaData.position === "topFixed"
    );
  }

  infoOtaData: OtaInfoOption = {};

  get spm() {
    const tracking = this.infoOtaData.tracking || {};
    const spm = {} as any;
    const spmName = {
      download: "download",
      close: "closeBtn",
      module: "module"
    } as any;
    for (const item in tracking) {
      const i = tracking[item];
      if (i) {
        spm[spmName[item]] = i.spm;
        if (i.extra) {
          spm[spmName[item]] = `${i.spm}?ext=${encodeURIComponent(
            JSON.stringify(i.extra)
          )}`;
        }
      }
    }
    return spm;
  }

  get otaData() {
    const { style, bg, banner, onelink, time, download_button } =
      this.infoOtaData || {};
    return {
      platform: style?.position === "topFixed" ? "mobile" : "desktop",
      language: this.language,
      bgImg: bg?.img,
      bgColor: bg?.color,
      bannerImg: banner?.img,
      qrCodeTip: onelink?.qr_desc, // 二维码上的提示文案
      timeLeft: time?.has_countdown ? time?.to_time - +new Date() / 1000 : 0, // 倒计时，剩余时间，单位秒
      title: banner?.title,
      desc: banner?.desc,
      link: onelink?.link,
      downButtonImage: download_button?.img, // 下载按钮图片
      downloadText: download_button?.desc,
      spm: this.spm,

      showClose: style?.show_close, // 是否展示close
      type: style?.scale, // small、medium、large、bottomFixed、topFixed,
      position: style?.position // web：header、rightBottomFixed、staticBanner、bottomFixed，mweb:small、topFixed
    };
  }

  get position() {
    return this.infoOtaData?.style?.position;
  }

  async getOTAData() {
    await (
      this._axios &&
      this._axios
        .$get(`/v1/usrcsrv/ota/config?location=${this.location}`)
        .then((res: any) => {
          if (res.success) {
            this.infoOtaData = res.result || {};
          }
        })
    ).catch(() => {
      this.infoOtaData = {};
    });
  }

  mounted() {
    this.getOTAData();
  }
}
</script>
