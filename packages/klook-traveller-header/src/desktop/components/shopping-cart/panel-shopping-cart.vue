<template>
  <div class="wrapper">
    <div v-if="count" class="content">
      <ul class="list" :style="{minHeight: loading?'163px':'auto'}">
        <klk-loading v-if="loading" :show-loading-bg="false" />
        <template v-else>
          <li
            v-for="(item, index) in items"
            :key="item.shoppingcart_id_str || item.shoppingcart_id"
            :data-spm-module="`ShoppingCartBox_Activity_LIST?oid=activity_${item.activity_id}&idx=${index}&len=${items.length}`"
          >
            <ShoppingItem
              :item="item"
              :webp="webp"
              :currency="currency"
            />
          </li>
        </template>
      </ul>
      <div class="preview">
        <div class="output">
          <template v-if="!loading">
            <span>{{ __t('3410', [items.length]) }}</span>
            <output>
              {{ totalPriceText }}
            </output>
          </template>
        </div>
        <div data-spm-module="ShoppingCartBox_GoTo" class="entry">
          <a
            :href="__href('/shoppingcart/')"
            data-spm-item="__default?typ=entry"
            v-galileo-click-tracker="{ spm: 'ShoppingCartBox_GoTo', componentName: 'klk-traveller-header' }"
            @click="handleClick"
          >{{
            __t('119')
          }}</a>
        </div>
      </div>
    </div>
    <div v-else class="empty">
      <IconShoppingCart theme="outline" size="24" :fill="$colorTextPrimary"/>
      <p>{{ __t('518') }}</p>
      <a :href="__href('/shoppingcart/')" class="gocart">
        <klk-button
          type="primary"
          size="small"
        >
          {{ __t('119') }}
        </klk-button>
      </a>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Prop } from 'vue-property-decorator';
import { add } from '@klook/accurate';
import { $colorTextPrimary } from '@klook/klook-ui/lib/utils/design-token-esm'
import IconShoppingCart from '@klook/klook-icons/lib/IconShoppingCart';

import formatThousands from '@klook/klk-traveller-utils/lib/formatThousands';
import KlkButton from '@klook/klook-ui/lib/button';
import KlkLoading from '@klook/klook-ui/lib/loading';
import getCurrencySymbol from '@klook/klk-traveller-utils/lib/getCurrencySymbol';
import Base from '../../../common/base';
import ShoppingItem from './shopping-item.vue';

@Component({
  components: {
    IconShoppingCart,
    ShoppingItem,
    KlkButton,
    KlkLoading
  }
})
export default class PanelShoppingCart extends Base {
  @Prop({ default: 0 }) count!: number
  @Prop() currency!: string
  @Prop() webp!: boolean
  @Prop({ default: () => [] }) items!: any[]
  @Prop({default: ''}) latestTotalPriceText!: string
  @Prop() loading!: boolean

  get $colorTextPrimary() {
    return $colorTextPrimary
  }

  get totalPrice() {
    let amount = 0;

    this.items.forEach((item: any) => {
      amount = add(amount, item.ticket_sell_price);
    });

    return amount;
  }
  get totalPriceText() {
    const latestAmount = this.latestTotalPriceText
    if (latestAmount){
      return latestAmount
    }
    return `${this.currencySymbol} ${this.formatThousands(this.totalPrice)}`
  }

  get currencySymbol() {
    return getCurrencySymbol(this.currency);
  }

  formatThousands(value: number | string) {
    return formatThousands(value);
  }

  handleClick() {
    window.tracker && window.tracker.gtm.sendGTMCustomEvent(`Shopping Cart|Go To Shopping Cart Button Clicked in Pop Up Cart Box|${this.count}`);
  }
}
</script>

<style lang="scss" scoped>
@import "../../../style/_variables.scss";

.list {
  overflow: auto;
  max-height: 340px;

  > li {
    padding: 20px 0;
    margin: 0 20px;
  }

  > li + li {
    border-top: 1px solid $color-gray2;
  }
}

.preview {
  display: flex;
  height: 60px;
  border-top: 1px solid $color-gray2;
}

.output {
  flex: 1;
  padding: 10px 24px;
  line-height: 1.4;

  span,
  output {
    display: block;
  }

  output {
    color: $color-main;
    font-size: 16px;
    font-weight: 500;
  }
}

.entry {
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 1;
  margin-top: -1px;
  background-color: $color-main;
  border-radius: 0 0 $radius-xl 0;
  color: #fff;
  font-size: 16px;
  font-weight: 500;

  > a {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.empty {
  margin: 48px 0;
  line-height: 30px;
  text-align: center;
}

.gocart{
  margin-top: 14px;
  display: block;
}
</style>
