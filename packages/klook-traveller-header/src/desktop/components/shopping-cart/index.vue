<template>
  <Panel
    v-model="visible"
    :class="{ 'has-item': hasItem }"
    class="default-header_shopping-cart"
    :width="400"
    placement="bottom-end"
    :content="__t('15093')"
    :popover="true"
    data-spm-item="ShoppingCart"
    @click="handleClick"
    @show="onShowHandle"
  >
    <PanelShoppingCart
      v-if="visible"
      slot="pop-content"
      :loading="!inited"
      :count="shoppingCartCount"
      :items="shoppingCartList"
      :latestTotalPriceText="latestTotalPriceText"
      :currency="currency"
      :webp="webp"
    />
  </Panel>
</template>

<script lang="ts">
import { Component, Prop } from "vue-property-decorator";
import Base from "../../../common/base";
import Panel from "../panel.vue";

@Component({
  components: {
    Panel,
    PanelShoppingCart: () => import("./panel-shopping-cart.vue")
  }
})
export default class ShoppingCart extends Base {
  @Prop() currency!: string;
  @Prop() webp!: boolean;

  shoppingCartList: any[] = []; // 购物车里的商品列表
  shoppingCartCount: number = 0; // 购物车里的商品个数
  visible = false;
  inited = false;
  latestTotalPriceText = ""; // 空表示没有获取最新总价失败

  get hasItem() {
    return this.shoppingCartCount > 0;
  }
  onShowHandle() {
    if (!this.inited && this.shoppingCartCount) {
      this.getShoppingCartList()
    }
  }

  async getShoppingCartList() {
    
    if (this._axios) {
      let res: any = null
      try {
        res = await this._axios.$get("/v3/order/shoppingcart");
      } catch (e){
        this.inited = true;
      }
      
      if (res.success && res.result) {
        const group = res.result.group.find((item: any) => item.type === 0);
        const items = group ? group.items : [];
        //获取最新的总价
        try {
          const sku_arrangement_list = items
            .filter((item: any) => {
              const { price_items } = item;
              return price_items && price_items.length;
            })
            .map((item: any) => {
              const {
                shoppingcart_id,
                shoppingcart_id_str,
                package_id,
                selected_time,
                item_selected,
                price_items = []
              } = item;
              return {
                is_selected: item_selected, // 1:勾选 0:未勾选，
                shopping_cart_id: shoppingcart_id,
                shopping_cart_id_str: shoppingcart_id_str || String(shoppingcart_id),
                package_id: package_id,
                start_time: selected_time, // 用户当前选购sku的出行时间. selected_time字段.
                sku_list: price_items.map((sku: any) => {
                  const { sku_id, count } = sku;
                  return {
                    sku_id,
                    quantity: count
                  };
                })
              };
            });
          const latestFormData = {
            page_from: 3,
            sku_arrangement_list
          };
          if (sku_arrangement_list.length == 0) {
            throw Error();
          }
          const totalRes = await this._axios.$post(
            "/v1/experiencesrv/order/settlement_service/shopping_cart_pre_settlement",
            latestFormData
          );
          if (totalRes.success && totalRes.result) {
            const {total_price, sku_arrangement_list} = totalRes.result
            this.latestTotalPriceText = total_price;
            items.forEach((item:any)=>{
              const item_shoppingcart_id = item.shoppingcart_id_str || item.shoppingcart_id;
              const preItem = sku_arrangement_list.find((sku:any)=>(sku.shopping_cart_id_str || sku.shopping_cart_id) == item_shoppingcart_id)
              if(preItem){
                item.pre_total_price = preItem.total_price
              }
            })
          }
        } catch (e) {
          this.latestTotalPriceText = "";
        } finally {
          this.shoppingCartList = items;
          this.inited = true;
        }
      }
    }
  }

  async getShoppingCartCount() {
    await (this._axios &&
      this._axios.$get("/v3/order/shoppingcart/count").then((res: any) => {
        if (res.success) {
          this.shoppingCartCount = res.result || 0;
        }
      }));
  }

  async mounted() {
    await this.getShoppingCartCount();
  }

  async updateShoppingCart() {
    await this.getShoppingCartCount();

    if (this.shoppingCartCount) {
      this.getShoppingCartList();
    }
  }

  handleClick() {
    window.tracker &&
      window.tracker.gtm.sendGTMCustomEvent(
        "Shopping Cart|Header Cart Button Clicked"
      );
    window.tracker &&
      window.tracker.sendMixpanel({
        name: "Signup / Login Page Revised",
        props: { "Entrance Path": "Nav Bar Shopping Cart" }
      });
  }
}
</script>

<style lang="scss">
@import "../../../style/variables";

.default-header_shopping-cart {
  &.has-item .default-header_entry {
    position: relative;

    &::before {
      display: block;
      content: "";
      width: 5px;
      height: 5px;
      background: $color-main;
      border-radius: $radius-circle;
      position: absolute;
      top: -5px;
      right: -5px;
    }
  }

  .klk-poptip-popper-inner {
    overflow-y: unset;
  }
}
</style>
