<template>
  <a
    class="wrapper"
    :href="__href(`/activity/${activity.activity_id}/`)"
    data-spm-item="__default?typ=entry"
    v-galileo-click-tracker="{ spm: 'ShoppingCartBox_Activity_LIST', componentName: 'klk-traveller-header' }"
    @click="handleClick"
  >
    <div class="image">
      <img :src="formatPicUrl(activity.activity_image_url)" alt>
    </div>
    <div class="info">
      <h4 class="title">{{ activity.activity_name }}</h4>
      <template v-if="activity.templateAssert.isWifiOrSim">
        <template v-if="activity.templateAssert.isWifi">
          <p
            v-if="activity.booking_days"
            class="days"
            v-html="__t('wifi_charge_for_days', [activity.booking_days])"
          />
          <p v-if="activity.pickup_time_new" class="time">
            <span>{{ __t('wifi_sim.pick_up_new') }}:</span>
            <output>{{ formatDateRange(activity.pickup_time_new) }}</output>
          </p>
          <p v-if="activity.return_time_new" class="time">
            <span>{{ __t('wifi_sim.return_new') }}:</span>
            <output>{{ formatDateRange(activity.return_time_new) }}</output>
          </p>
        </template>
        <template v-if="activity.templateAssert.isSim">
          <p v-if="activity.pickup_time_new" class="time">
            <span>{{ __t('wifi_sim.pick_up_new') }}:</span>
            <output>{{ formatDateRange(activity.pickup_time_new) }}</output>
          </p>
        </template>
        <template v-if="activity.templateAssert.isYsim">
          <p v-if="activity.pickup_time_new" class="time">
            <span>{{ __t('wifi_pick_up_date') }}:</span>
            <output>{{ formatDateRange(activity.pickup_time_new) }}</output>
          </p>
        </template>
        <p v-if="activity.package_specs.length" class="more">
          {{ convertToString(activity.package_specs) }}{{ preOtherInfo }}
        </p>
      </template>
      <template v-else>
        <p class="pkgName">{{ activity.package_name }}</p>
        <p class="time">{{ formatDateStr(activity.participation_date_desc, activity.participation_date_map) }}</p>
        <p class="unit">
          <template v-for="price in activity.price_items">
            {{ price.count }}&nbsp;{{ price.name }}&nbsp;&nbsp;&nbsp;
          </template>
        </p>
      </template>
      <p class="price">
        {{ activity.totalPriceText }}
      </p>
    </div>
  </a>
</template>

<script lang="ts">
import { Component, Prop } from 'vue-property-decorator';
import formatThousands from '@klook/klk-traveller-utils/lib/formatThousands';
import assertActivityTemplate from '@klook/klk-traveller-utils/lib/assertActivityTemplate';
import getCurrencySymbol from '@klook/klk-traveller-utils/lib/getCurrencySymbol';
import {
  getStandardDateRangeFormat,
  formatDateDesc,
  formatPicUrl
} from '../../../common/util';
import Base from '../../../common/base';

@Component
export default class ShoppingItem extends Base {
  @Prop({ default: () => ({}) }) item!: any
  @Prop() currency!: string
  @Prop() webp!: boolean

  formatThousands = formatThousands
  assertActivityTemplate = assertActivityTemplate

  get currencySymbol() {
    return getCurrencySymbol(this.currency);
  }

  get activity() {
    const item = { ...this.item };
    const id = item.activity_template_id;

    const templateAssert = this.assertActivityTemplate(id);
    item.templateAssert = templateAssert;

    if (templateAssert.isWifiOrSim) {
      return this.convertTime(item);
    }
    let totalPriceText = `${this.currencySymbol} ${this.formatThousands(item.ticket_sell_price)}`
    if(item.pre_total_price){
      totalPriceText = item.pre_total_price
    }

    return {...item,totalPriceText};
  }

  get preOtherInfo() {
    const content = this.getPreOtherContent(this.item.pre_other_info);

    if (content) {
      return ` · ${content}`;
    }

    return content;
  }

  formatPicUrl(url: string) {
    return formatPicUrl(url, this.webp);
  }

  formatDateStr(dateDesc: any, dateMap: any) {
    const { language } = this;
    return formatDateDesc(dateDesc, dateMap, this.__t.bind(this), language);
  }

  formatDateRange(dateRange: string) {
    const { language } = this;

    return getStandardDateRangeFormat(
      dateRange,
      this.__t.bind(this),
      language
    );
  }

  getPreOtherContent(info: any) {
    if (!info) {
      return '';
    }
    // 之前pre_other_info处理成object导致不展示数据 ，现在添加数组处理逻辑如果不是数组还是走之前的逻辑
    let content2 = '';
    if (Array.isArray(info)) {
      info.forEach((item: any) => {
        const { type_hint: hint, content, position } = item;
        if (Number.parseInt(position, 10) === 1) {
          const hintArray = hint.split(',');
          content2 += hintArray[Number.parseInt(content, 10) + 1];
        } else {
          content2 += content;
        }
      });
      return content2;
    }
    const { type_hint: hint, content, position } = info;
    if (Number.parseInt(position, 10) === 1) {
      const hintArray = hint.split(',');
      return hintArray[Number.parseInt(content, 10) + 1];
    }
    return content;
  }

  convertToString(spaces: any[] | null) {
    if (!spaces) {
      return '';
    }

    return spaces.map(item => item.attr_name).join(' · ');
  }

  convertTime(item: any) {
    const itemCopy = { ...item };

    if (itemCopy.selected_time) {
      const [time] = itemCopy.selected_time.split(' ');
      itemCopy.pickup_time_new = time;
      if (itemCopy.pickup_time) {
        itemCopy.pickup_time_new += ` ${itemCopy.pickup_time}`;
      }
    }

    if (itemCopy.schedule_end_time) {
      const [time] = itemCopy.schedule_end_time.split(' ');
      itemCopy.return_time_new = time;
      if (itemCopy.return_time) {
        itemCopy.return_time_new += ` ${itemCopy.return_time}`;
      }
    }

    return itemCopy;
  }

  handleClick() {
    window.tracker && window.tracker.gtm.sendGTMCustomEvent(`Shopping Cart|Activity Clicked On The Shopping Cart Window|${this.activity.activity_id}`);
  }
}
</script>

<style lang="scss" scoped>
@import "../../../style/variables";

.wrapper {
  display: flex;
}

.image {
  flex-shrink: 0;
  width: 60px;
  height: 60px;
  margin-right: 20px;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.info {
  flex: 1;
  min-width: 0; // Hack text-overflow: ellipsis

  > p {
    margin-top: 4px;
    line-height: 1.2;
  }
}

.title {
  margin-top: 0;
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 700;
  line-height: 1.3;
  @include text-ellipsis(2);
}

.pkgName,
.unit {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.days,
.time,
.unit,
.more {
  color: #888;
}

.days {
  :global(.t_green) {
    color: #009685;
  }
}

.price {
  color: $color-main;
  font-weight: 500;
}
</style>
