<template>
  <component
    v-bind="$attrs"
    :is="comp"
    class="default-header_right-panel"
    :offset="popOffset"
    v-on="$listeners"
  >
    <a class="default-header_entry" :href="link" :data-spm-item="dataSpmItem" v-galileo-click-tracker="{ spm: 'TopNavigation.' + dataSpmItem, componentName: 'klk-traveller-header' }" @click="handleClick">
      <slot name="content">
        <span>{{ content }}</span>
      </slot>
    </a>

    <div slot="content">
      <slot name="pop-content" />
    </div>
  </component>
</template>

<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator';
import Popover from './popover.vue';

@Component({
  components: {
    Popover
  }
})
export default class Panel extends Vue {
  @Prop({ default: 'javascript:;' }) link!: string
  @Prop({ default: '' }) content!: string
  @Prop() popover!: boolean
  @Prop() offset!: number
  @Prop({ default: "" }) dataSpmItem?: string;

  get comp() {
    // pop-content是异步组件时取不到slots
    if (this.popover) {
      return 'Popover';
    }

    const isTypePopover = this.$slots['pop-content'];

    return isTypePopover ? 'Popover' : 'div';
  }

  get popOffset() {
    return [this.offset || 10, 20];
  }

  handleClick() {
    this.$emit('click');
    // window.tracker && window.tracker.gtm.sendGTMCustomEvent('Help Center|Help Button Clicked on Header');
  }
}
</script>

<style lang="scss" scoped>
@import "../../style/variables";

.default-header_right-panel {
  height: 36px;
  display: flex;
  align-items: center;
  border-radius: $radius-pill;

  &:hover:not(.not-hover){
    background: $color-gray3;
  }

  .default-header_entry {
    display: flex;
    align-items: center;
    flex-wrap: nowrap;
    white-space: nowrap;
    margin: 0 12px;

    > span {
      flex: 1;
      font-weight: $fontWeight-semibold;
      font-size: $fontSize-caption-m;
    }
  }
}
</style>
