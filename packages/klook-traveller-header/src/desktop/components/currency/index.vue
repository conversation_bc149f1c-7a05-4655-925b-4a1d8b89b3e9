<template>
  <Panel
    v-model="visible"
    class="default-header_currency"
    :popover="true"
    :offset="-110"
    data-spm-item="Currency"
  >
    <template slot="content">
      <span class="default-header_currency-text">{{ currency }}</span>
      <IconTriangleDown class="drop-down" theme="filled" size="12" :fill="$colorTextSecondary"/>
    </template>

    <PanelCurrency
      v-if="visible"
      slot="pop-content"
      :currency="currency"
      :is-change-site-tip-visible="isChangeSiteTipVisible"
      :suggest-currencies="supportCurrencies.suggestCurrencies"
      :other-currencies="supportCurrencies.otherCurrencies"
      @get-width="getWidth"
    />
  </Panel>
</template>

<script lang="ts">
import { Component, Prop } from 'vue-property-decorator';
import { $colorTextSecondary } from '@klook/klook-ui/lib/utils/design-token-esm'
import IconTriangleDown from '@klook/klook-icons/lib/IconTriangleDown';
import Base from '../../../common/base';
import Panel from '../panel.vue';

interface ICurrencyLists {
  suggestCurrencies: string[]
  otherCurrencies: string[]
}

@Component({
  components: {
    Panel,
    IconTriangleDown,
    PanelCurrency: () => import('./panel-currency.vue')
  }
})
export default class CurrencySelector extends Base {
  @Prop() currency!: string
  @Prop() isChangeSiteTipVisible!: boolean

  @Prop({
    default: () => ({
      suggestCurrencies: [],
      otherCurrencies: []
    })
  })
  supportCurrencies!: ICurrencyLists

  width = 0
  visible = false

  get $colorTextSecondary(){
    return $colorTextSecondary
  }

  getWidth(width: number) {
    this.width = width;
  }

}
</script>

<style lang="scss" scoped>
.default-header_currency {
  .default-header_currency-text {
    font-weight: 500;
    font-size: 12px;
  }

  .drop-down {
    margin-left: 4px;
  }
}
</style>

<style lang="scss">
.default-header_currency {
  .klk-poptip-popper[data-popper-placement^="bottom"]::after {
    left: calc(50% + 120px);
  }
}
</style>
