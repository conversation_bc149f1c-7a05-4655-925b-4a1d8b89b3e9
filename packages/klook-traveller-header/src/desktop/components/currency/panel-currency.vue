<template>
  <div class="wrapper">
    <div class="title">
      <span>{{ __t('82') }}</span>
    </div>
    <ul class="list">
      <li v-for="item in suggestCurrencies" :key="item">
        <a
          :class="{ selected: currency === item }"
          href="javascript:;"
          @click="handleItemClick(item)"
          :data-spm-item="`SelectCurrency?ext=${JSON.stringify({Currency: currency})}`"
          v-galileo-click-tracker="{ spm: 'TopNavigation.SelectCurrency', componentName: 'klk-traveller-header' }"
        >
          <span>{{ item }}</span>
          <span>{{ __t(currencyTextMap[item]) }}</span>
        </a>
      </li>
    </ul>
    <template v-if="otherCurrencies.length">
      <div :class="['title', 'mt10']">
        <span>{{ __t('83') }}</span>
        <i />
      </div>
      <ul class="list">
        <li v-for="item in otherCurrencies" :key="item">
          <a
            :class="{ selected: currency === item }"
            href="javascript:;"
            @click="handleItemClick(item)"
            :data-spm-item="`SelectCurrency?ext=${JSON.stringify({Currency: currency})}`"
            v-galileo-click-tracker="{ spm: 'TopNavigation.SelectCurrency', componentName: 'klk-traveller-header' }"
          >
            <span>{{ item }}</span>
            <span>{{ __t(currencyTextMap[item]) }}</span>
          </a>
        </li>
      </ul>
    </template>

    <SiteChange v-if="isChangeSiteTipVisible" :title="__t('16638')" />
  </div>
</template>

<script lang="ts">
import { Component, Prop } from "vue-property-decorator";
import getCookieConfig from "@klook/klk-traveller-utils/lib/getCookieConfig";
import replaceQueryParams from "@klook/klk-traveller-utils/lib/replaceQueryParams";
import SiteChange from "../site/index.vue";
import Base from "../../../common/base";
import { currencyTextMap } from "../../../common/util";

@Component({
  components: {
    SiteChange
  }
})
export default class PanelCurrency extends Base {
  @Prop({ default: "" }) currency!: string;
  @Prop() isChangeSiteTipVisible!: boolean;
  @Prop({ default: () => [] }) suggestCurrencies!: string[];
  @Prop({ default: () => [] }) otherCurrencies!: string[];

  get currencyTextMap() {
    return currencyTextMap;
  }

  /**
   * @TODO: 此处的请求应当封装起来，通过 config 来指定数据类型
   * 但是，由于 axios 0.19.0 版本不支持自定义 config 了
   * 所以，暂且在这里组装 querystring 和指定 Content-Type
   *
   * Issue: https://github.com/axios/axios/issues/2295
   */
  syncCurrency(currency: string) {
    if (!this.isLoggedIn) {
      return Promise.resolve();
    }

    const qs = `currency=${currency}`;
    const headers = {
      "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8"
    };

    return this.$axios
      ? this.$axios.$post(
          "/v3/userserv/user/profile_service/change_currency",
          qs,
          { headers }
        )
      : Promise.resolve();
  }

  reload(currency: string) {
    const cookie = getCookieConfig("currency");
    const href = window.location.href.split("?")[0];

    window.Cookies &&
      window.Cookies.set(cookie.key, currency, {
        path: "/",
        expires: new Date(Date.now() + cookie.lifetime)
      });
    window.location.replace(href + replaceQueryParams("_currency"));
    // window.location.reload();
  }

  handleItemClick(currency: string) {
    this.syncCurrency(currency)
      .then(() => {
        window.tracker &&
          window.tracker.gtm.sendGTMCustomEvent(
            `Currency Setting|Currency Switched|${currency}`
          );
      })
      .finally(() => {
        this.reload(currency);
      });
  }

  mounted() {
    this.$emit("get-width", this.$el.offsetWidth);
  }
}
</script>

<style lang="scss" scoped>
.wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 28px 22px 10px;
  max-width: 740px;
  min-width: 260px;
  width: 740px;
}

.title {
  display: flex;
  align-items: center;
  height: 20px;
  flex-shrink: 0;
  font-weight: $fontWeight-bold;

  span {
    padding: 0 20px 0 8px;
  }
}

.list {
  display: flex;
  flex-wrap: wrap;
  padding: 0;
  margin: 16px 0 0;
  list-style: none;

  li {
    flex-shrink: 0;
    width: 25%;
    margin-bottom: 4px;
    min-width: 174px;
  }

  a {
    display: flex;
    align-items: center;
    width: 100%;
    height: 28px;
    padding: 0 10px;
    font-size: $fontSize-caption-m;
    color: $color-text-primary;

    &:hover {
      background-color: $color-bg-page;
    }

    span:first-child {
      display: inline-block;
      width: 30px;
      margin-right: 8px;
      color: $color-text-secondary;
    }

    &.selected {
      color: $color-brand-primary;
      font-weight: $fontWeight-semibold;

      span {
        color: inherit;
      }
    }
  }
}

.mt10 {
  margin-top: 10px;
}
</style>
