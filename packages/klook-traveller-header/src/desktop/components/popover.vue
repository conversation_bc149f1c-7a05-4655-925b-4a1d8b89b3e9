<template>
  <Poptip
    v-bind="$attrs"
    trigger="click"
    :max-width="1280"
    :max-height="1280"
    class="common-header_poptip"
    :placement="placement"
    v-on="$listeners"
  >
    <slot />

    <template slot="content">
      <slot name="content" />
    </template>
  </Poptip>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import Poptip from '@klook/klook-ui/lib/poptip';

@Component({
  components: {
    Poptip
  }
})
export default class Popover extends Vue {
  @Prop({ default: 'bottom' }) placement!: string
}
</script>

<style lang="scss">
  .common-header_poptip {
    .klk-poptip-popper {
      overflow: hidden;
    }
    .klk-poptip-popper-inner {
      margin: 0;
      padding: 0;
    }
  }
</style>
