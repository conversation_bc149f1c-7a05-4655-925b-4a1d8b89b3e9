<template>
  <div class="wrapper" :style="styles">
    <div class="title">
      <span>{{ __t('15399') }}</span>
    </div>
    <ul class="list">
      <li v-for="item in langs" :key="item.title">
        <div
          v-if="Array.isArray(item.children)"
          :class="{ selected: item.children.some(i => i.value === language) }"
        >
          <SvgLang language="zh-HK" width="16" height="16" />

          <span>{{ item.title }}</span>
          <div class="subitem">
            <template v-for="subitem in item.children">
              <a
                v-if="checkIsSupportedLanguage(subitem.value)"
                :key="subitem.title"
                :class="{ selected: subitem.value === language }"
                href="javascript:;"
                :data-spm-item="`SelectLanguage?ext=${JSON.stringify({Language: language})}`"
                v-galileo-click-tracker="{ spm: 'TopNavigation.SelectLanguage', componentName: 'klk-traveller-header' }"
                @click="changeLanguage(subitem.value)"
              >{{ subitem.title }}</a>
            </template>
          </div>
        </div>
        <a
          v-else
          href="javascript:;"
          :class="{ selected: language === item.value }"
          :data-spm-item="`SelectLanguage?ext=${JSON.stringify({Language: language})}`"
          v-galileo-click-tracker="{ spm: 'TopNavigation.SelectLanguage', componentName: 'klk-traveller-header' }"
          @click="changeLanguage(item.value)"
        >
          <SvgLang :language="item.value" :current-language="language" width="16" height="16" />
          <span>{{ item.title }}</span>
        </a>
      </li>
    </ul>

    <SiteChange v-if="isChangeSiteTipVisible" :title="__t('16637')" />
  </div>
</template>

<script lang="ts">
import { Component, Prop } from "vue-property-decorator";
import convertToBackendLanguage from "@klook/klk-traveller-utils/lib/convertToBackendLanguage";
import getCookieConfig from "@klook/klk-traveller-utils/lib/getCookieConfig";
import SiteChange from "../site/index.vue";
import Base from "../../../common/base";
import SvgLang from "./svg-lang.vue";

@Component({
  components: {
    SvgLang,
    SiteChange
  }
})
export default class PanelLanguage extends Base {
  @Prop({ default: "" }) isChangeSiteTipVisible!: boolean;
  @Prop({ default: () => [] }) langs!: string[];

  get styles() {
    const colWidth = 210 + 6;
    const paddingWith = 20 * 2;
    const contentWidth = Math.ceil(this.langs.length / 8) * colWidth;

    return {
      width: `${paddingWith + contentWidth}px`
    };
  }

  checkIsSupportedLanguage(language: Data.Language) {
    return !!convertToBackendLanguage(language);
  }

  syncLanguage(language: Data.Language) {
    if (!this.isLoggedIn) {
      return Promise.resolve();
    }

    const qs = `language=${convertToBackendLanguage(language)}`;
    const headers = {
      "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8"
    };
    return this._axios
      ? this._axios.$post(
          "/v3/userserv/user/profile_service/change_language",
          qs,
          { headers, loading: true }
        )
      : Promise.resolve();
  }

  redirect(language: Data.Language) {
    const originalPathName = window.location.href.replace(
      window.location.origin,
      ""
    );
    const currentLanguage = this.language;
    let originalPath = originalPathName;
    const repalceReg = new RegExp(
      `^/${currentLanguage}/|^/${currentLanguage}$`
    );
    if (originalPathName.match(repalceReg)) {
      originalPath = originalPathName.replace(`/${currentLanguage}`, "");
    }

    const cookie = getCookieConfig("language");
    window.Cookies &&
      window.Cookies.set(cookie.key, language, {
        path: "/",
        expires: new Date(Date.now() + cookie.lifetime)
      });

    if (language === "en") {
      window.location.href = originalPath;
    } else {
      window.location.href = `/${language}${originalPath}`;
    }
  }

  changeLanguage(language: Data.Language) {
    this.syncLanguage(language)
      .then(() => {
        window.tracker &&
          window.tracker.gtm.sendGTMCustomEvent(
            `Language Setting|Language Switched|${language}`
          );
      })
      .finally(() => {
        this.redirect(language);
      });
  }
}
</script>

<style lang="scss" scoped>
.wrapper {
  display: flex;
  flex-direction: column;
  min-width: 255px;
  height: 100%;
  padding: 30px 20px 20px;
}

.title {
  display: flex;
  align-items: center;
  height: 20px;
  flex-shrink: 0;
  font-weight: $fontWeight-bold;
  font-size: 14px;

  span {
    padding: 0 20px 0 8px;
  }
}

.list {
  display: flex;
  // flex-direction: column;
  flex-wrap: wrap;
  // align-content: space-between;
  // height: calc(100% - 36px);
  padding: 0;
  margin: 16px 0 0;
  list-style: none;

  li {
    flex-shrink: 0;
    height: 28px;
    margin-bottom: 4px;
    width: 33%;
    min-width: 210px;

    &:hover {
      background-color: $color-bg-page;

      .subitem {
        display: block;
      }
    }

    > a,
    > div {
      display: flex;
      align-items: center;
      width: 210px;
      height: 28px;
      padding: 0 10px;
      font-size: $fontSize-caption-m;
      color: $color-text-primary;

      &.selected {
        color: $color-brand-primary;
      }
    }

    > div {
      display: inline-flex;
      width: auto;
      position: relative;
    }
  }

  .subitem {
    display: none;
    position: absolute;
    top: 50%;
    left: calc(100% - 10px);
    padding: 6px;
    white-space: nowrap;
    background-color: $color-bg-widget-normal;
    margin-left: 12px;
    margin-top: -21px;
    border-radius: $radius-s;
    /* stylelint-disable */
    box-shadow: 0 1px 6px 0 rgba(#000, 0.2);
    /* stylelint-enable */
    color: $color-text-primary;

    &::before {
      content: "";
      width: 10px;
      height: 10px;
      display: block;
      background-color: $color-bg-widget-normal;
      transform: translate(-50%, -50%) rotate(45deg);
      position: absolute;
      left: 0;
      top: 50%;
      /* stylelint-disable */
      box-shadow: 0 1px 6px 0 rgba(0, 0, 0, 0.2);
      /* stylelint-enable */
    }

    &::after {
      content: "";
      width: 10px;
      height: 70%;
      display: block;
      background-color: $color-bg-widget-normal;
      position: absolute;
      left: 0;
      top: 15%;
    }

    a {
      display: inline-block;
      position: relative;
      height: 30px;
      line-height: 30px;
      padding: 0 6px;

      &.selected,
      &:hover {
        color: $color-brand-primary;
      }
    }

    a + a {
      margin-left: 11px;

      &::before {
        content: "";
        display: block;
        position: absolute;
        left: -6px;
        top: 50%;
        width: 1px;
        height: 10px;
        margin-top: -5px;
        background-color: $color-bg-overlay-black-mobile;
      }
    }
  }

  svg {
    margin-right: 10px;
  }
}
</style>
