<template>
  <component v-bind="$attrs" :is="i18nIcon" width="22" height="22" />
</template>

<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator';
const Langde = () => import('../../../svg/lang-de.svg' /* webpackChunkName: 'flog-Langde' */);
const Langen = () => import('../../../svg/lang-en.svg' /* webpackChunkName: 'flog-Langen' */);
const LangenAU = () => import('../../../svg/lang-en-AU.svg' /* webpackChunkName: 'flog-LangenAU' */);
const LangenCA = () => import('../../../svg/lang-en-CA.svg' /* webpackChunkName: 'flog-LangenCA' */);
const LangenGB = () => import('../../../svg/lang-en-GB.svg' /* webpackChunkName: 'flog-LangenGB' */);
const LangenHK = () => import('../../../svg/lang-en-HK.svg' /* webpackChunkName: 'flog-LangenHK' */);
const LangenIN = () => import('../../../svg/lang-en-IN.svg' /* webpackChunkName: 'flog-LangenIN' */);
const LangenMY = () => import('../../../svg/lang-en-MY.svg' /* webpackChunkName: 'flog-LangenMY' */);
const LangmsMY = () => import('../../../svg/lang-ms-MY.svg' /* webpackChunkName: 'flog-LangmsMY' */);
const LangenNZ = () => import('../../../svg/lang-en-NZ.svg' /* webpackChunkName: 'flog-LangenNZ' */);
const LangenPH = () => import('../../../svg/lang-en-PH.svg' /* webpackChunkName: 'flog-LangenPH' */);
const LangenSG = () => import('../../../svg/lang-en-SG.svg' /* webpackChunkName: 'flog-LangenSG' */);
const LangenUS = () => import('../../../svg/lang-en-US.svg' /* webpackChunkName: 'flog-LangenUS' */);
const Langes = () => import('../../../svg/lang-es.svg' /* webpackChunkName: 'flog-Langes' */);
const Langfr = () => import('../../../svg/lang-fr.svg' /* webpackChunkName: 'flog-Langfr' */);
const Langid = () => import('../../../svg/lang-id.svg' /* webpackChunkName: 'flog-Langid' */);
const Langit = () => import('../../../svg/lang-it.svg' /* webpackChunkName: 'flog-Langit' */);
const Langja = () => import('../../../svg/lang-ja.svg' /* webpackChunkName: 'flog-Langja' */);
const Langko = () => import('../../../svg/lang-ko.svg' /* webpackChunkName: 'flog-Langko' */);
const Langru = () => import('../../../svg/lang-ru.svg' /* webpackChunkName: 'flog-Langru' */);
const Langth = () => import('../../../svg/lang-th.svg' /* webpackChunkName: 'flog-Langth' */);
const Langvi = () => import('../../../svg/lang-vi.svg' /* webpackChunkName: 'flog-Langvi' */);
const LangzhCN = () => import('../../../svg/lang-zh-CN.svg' /* webpackChunkName: 'flog-LangzhCN' */);
// const LangzhCNTW = () => import('../../../svg/lang-zh-CN-TW.svg' /* webpackChunkName: 'flog-LangzhCN' */);
const LangzhHK = () => import('../../../svg/lang-zh-HK.svg' /* webpackChunkName: 'flog-LangzhHK' */);
const LangzhTW = () => import('../../../svg/lang-zh-TW.svg' /* webpackChunkName: 'flog-LangzhTW' */);
const LangzhTWCN = () => import('../../../svg/lang-zh-TW-CN.svg' /* webpackChunkName: 'flog-LangzhTWCN' */);

@Component({
  components: {
    Langde,
    Langen,
    LangenAU,
    LangenCA,
    LangenGB,
    LangenHK,
    LangenIN,
    LangenMY,
    LangenNZ,
    LangenPH,
    LangenSG,
    LangenUS,
    Langes,
    Langfr,
    Langid,
    Langit,
    Langja,
    Langko,
    Langru,
    Langth,
    Langvi,
    LangzhCN,
    // LangzhCNTW,
    LangzhHK,
    LangzhTW,
    LangzhTWCN,
    LangmsMY
  }
})
export default class SvgLang extends Vue {
  @Prop() language!: string
  @Prop() currentLanguage!: string

  get i18nIcon() {
    if (!this.language) { return; }

    let icon = this.language;
    if (this.language === 'zh-HK' || this.language === 'zh-TW') {
      icon = 'zh-TW-CN';
    }
    // if(this.currentLanguage === 'zh-TW' && this.language === 'zh-CN'){
    //   icon = 'zh-CN-TW';
    // }

    return `Lang${icon.split('-').join('')}`;
  }
}
</script>

<style lang="scss">
</style>
