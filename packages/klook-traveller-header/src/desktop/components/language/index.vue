<template>
  <Panel v-model="visible" :popover="true" class="default-header_language" data-spm-item="Language">
    <template slot="content">
      <SvgLang :language="language" width="22" height="22" />
      <IconTriangleDown  class="drop-down" theme="filled" size="12" :fill="$colorTextSecondary"/>
    </template>

    <PanelLanguage
      v-if="visible"
      slot="pop-content"
      :is-change-site-tip-visible="isChangeSiteTipVisible"
      :langs="renderLanguages"
    />
  </Panel>
</template>

<script lang="ts">
import { Component, Prop } from 'vue-property-decorator';
import { $colorTextSecondary } from '@klook/klook-ui/lib/utils/design-token-esm'
import IconTriangleDown from '@klook/klook-icons/lib/IconTriangleDown';
import Base from '../../../common/base';
import Panel from '../panel.vue';
import SvgLang from './svg-lang.vue';

@Component({
  components: {
    PanelLanguage: () => import('./panel-language.vue'),
    Panel,
    IconTriangleDown,
    SvgLang
  }
})
export default class LanguageSelector extends Base {
  @Prop() renderLanguages!: any
  @Prop() isChangeSiteTipVisible!: boolean

  visible = false
  
  get $colorTextSecondary() {
    return $colorTextSecondary
  }
}
</script>

<style lang="scss" scoped>
.default-header_language {
  font-size: 0;

  .drop-down {
    margin-left: 4px;
  }
}
</style>
