<template>
  <a
    v-if="view"
    class="wrapper"
    :href="removeUrlHost(view.deep_link)"
    target="_blank"
    @click="sendData(view)"
  >
    <img
      :src="formatPicUrl(view.image_src)"
      class="image"
      data-img-type="headerRecentlyView"
    >
    <div class="info">
      <h3 class="title">{{ view.title }}</h3>
      <ul class="price">
        <template v-if="view.free_text">
          <span>{{ view.free_text }}</span>
        </template>
        <template v-else>
          <li v-if="sellPrice !== 0" class="priceMarket">
            <del v-if="marketPrice > sellPrice">
              {{ priceSymbol }}
              {{ marketPrice }}
            </del>
          </li>
          <li>
            <span v-if="sellPrice > 0">
              {{ priceSymbol }}
              {{ sellPrice }}
            </span>
            <IconLightning v-if="sellPrice > -1 && view.can_immediately" theme="filled" size="16" :fill="$colorBrandPrimary"/>
          </li>
        </template>
      </ul>
    </div>
  </a>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import removeUrlHost from '@klook/klk-traveller-utils/lib/removeHost';
import { $colorBrandPrimary } from '@klook/klook-ui/lib/utils/design-token-esm'
import IconLightning from '@klook/klook-icons/lib/IconLightning';
import { formatPicUrl } from '../../../common/util';

@Component({
  components: {
    IconLightning
  }
})
export default class RecentlyViewsActivity extends Vue {
  @Prop({ default: null }) view!: any
  @Prop() webp!: boolean
  @Prop() currency!: string

  removeUrlHost: Function = removeUrlHost

  get $colorBrandPrimary() {
    return $colorBrandPrimary
  }

  formatPicUrl(url: string) {
    return formatPicUrl(url, this.webp);
  }

  toNumber(num: string) {
    return Number(num);
  }

  get marketPrice() {
    return (this.view.market_price && this.view.market_price.amount_display) || 0;
  }

  get sellPrice() {
    return (this.view.sell_price && this.view.sell_price.amount_display) || 0;
  }

  get priceSymbol() {
    return (this.view.sell_price && this.view.sell_price.symbol) || '';
  }

  sendData(activity: any) {
    window.tracker && window.tracker.gtm.sendGTMCustomEvent(`Others|Nav Bar Recently Viewed Activity Clicked|Activity_${activity.id}`);
    window.tracker && window.tracker.sendMixpanel({
      name: 'Activity Page',
      props: {
        'Activity ID': activity.id,
        'City ID': activity.city_id,
        'Country ID': activity.country_id,
        'Category ID': activity.template_id,
        'Activity Card Feature': activity.sold_out ? ['Sold Out'] : [],
        Activity: activity.url_seo,
        'Landing Page': false,
        'Entrance Path': 'Nav Bar Recently Viewed Activity'
      }
    });
  }
}
</script>

<style lang="scss" scoped>
@import "../../../style/_variables.scss";

.wrapper {
  display: flex;
  padding: 12px 0;
}

.image {
  width: 80px;
  height: 60px;
  margin-right: 12px;
  border-radius: $radius-s;
  object-fit: cover;
}

.info {
  flex: 1;
}

.title {
  margin-top: 0;
  margin-bottom: 4px;
  font-size: $fontSize-body-s;
  font-weight: $fontWeight-bold;
  line-height: 1.29;

  @include text-ellipsis(2);
}

.meta {
  display: flex;
  align-items: baseline;
  font-size: $fontSize-caption-m;

  li + li {
    margin-left: 4px;
  }
}

.score {
  color: $color-brand-secondary;
  font-size: $fontSize-body-s;
}

.comments {
  color: $color-text-secondary;
}

.join {
  color: $color-text-secondary;
}

.price {
  margin-top: 8px;
  font-size: $fontSize-body-s;

  li {
  }

  svg {
    fill: $color-brand-primary;
  }
}

.priceMarket {
  color: $color-text-secondary;
  font-size: $fontSize-caption-m;
}

.tip {
  color: $color-text-secondary;
  font-size: $fontSize-caption-m;
}

.recently {
  color: $color-success;
}
</style>
