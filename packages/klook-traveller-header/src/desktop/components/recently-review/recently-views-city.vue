<template>
  <a class="recently-views-city" :href="view.deep_link" @click="track">
    <div
      v-lazy:background-image.container="view.background_src"
      class="city-image"
    />
    <div class="city-name">
      <IconDestination theme="filled" size="16" :fill="$colorTextPrimary"/>
      <span>{{ view.title }}</span>
    </div>
  </a>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import { $colorTextPrimary } from '@klook/klook-ui/lib/utils/design-token-esm'
import IconDestination from '@klook/klook-icons/lib/IconDestination';

@Component({
  components: {
    IconDestination
  }
})
export default class RecentlyViewsCity extends Vue {
  @Prop() view!: any
  
  get $colorTextPrimary() {
    return $colorTextPrimary
  }

  track() {
    window.tracker && window.tracker.gtm.sendGTMCustomEvent('Others|Nav Bar Recently Viewed Activity Clicked|' + 'DestinationCityID_' + this.view.city_id);
  }

  formatImage(url: string) {
    return url.replace(/\/c_scale,w_\d+\//, 'c_scale,w_160');
  }
}
</script>

<style lang="scss" scoped>
.recently-views-city {
  display: flex;
  padding: 12px 0;
  justify-content: flex-start;
  align-items: center;

  .city-image {
    background-size: cover;
    background-position: center;
    margin-right: 12px;
    width: 80px;
    height: 60px;
    border-radius: $radius-s;
    flex-shrink: 0;
  }

  .city-name {
    height: 100%;
    font-size: $fontSize-body-s;
    line-height: 18px;
    font-weight: $fontWeight-bold;
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;

    svg {
      margin-right: 6px;
      margin-top: 2px;
      flex-shrink: 0;
    }

    span {
      overflow: hidden;
      text-overflow: ellipsis;
      -webkit-line-clamp: 2;
      display: -webkit-box;
      -webkit-box-orient: vertical;
    }
  }
}
</style>
