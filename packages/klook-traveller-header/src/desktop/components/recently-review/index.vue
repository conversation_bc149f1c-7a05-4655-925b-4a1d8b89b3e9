<template>
  <Panel
    v-model="visible"
    class="default-header_recently-reviews"
    :width="400"
    :content="__t('12915')"
    dataSpmItem="RecentlyViewed"
    @show="handleRecentlyViewEnter"
    :popover="true"
  >
    <RecentlyViewsList
      v-if="visible"
      slot="pop-content"
      :webp="webp"
      :views="recentlyViewList"
      :loading="loading"
    />
    <div ></div>
  </Panel>
</template>

<script lang="ts">
import { Component, Prop } from 'vue-property-decorator';
import Base from '../../../common/base';
import Panel from '../panel.vue';

@Component({
  components: {
    Panel,
    RecentlyViewsList: () => import('./recently-view-list.vue')
  }
})
export default class RecentlyReview extends Base {
  @Prop() webp!: boolean

  recentlyViewList: any[] = []
  visible = false
  loading = false

  handleRecentlyViewEnter() {
    this.getRecentlyViews();
    window.tracker && window.tracker.gtm.sendGTMCustomEvent('Others|Nav Bar Recently Viewed Activity Section Hovered');
  }

  async getRecentlyViews() {
    if (this._axios) {
      this.loading = true
      try {
        const res = await this._axios.$get('/v1/usrcsrv/home/<USER>/viewed')
        if (res.success && res.result) {
          this.recentlyViewList = res.result.items || [];
        }
      } finally {
        this.loading = false
      }
    }

  }

  mounted() {
  }
}
</script>

<style lang="scss">
</style>
