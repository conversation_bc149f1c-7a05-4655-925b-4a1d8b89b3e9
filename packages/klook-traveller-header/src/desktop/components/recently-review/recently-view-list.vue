<template>
  <ul class="wrapper recently-view-container">
    <klk-loading v-if="loading" :show-loading-bg="false" />
    <template v-else>
      <template v-if="views.length">
        <li v-for="view in views" :key="view.id">
          <RecentlyViewsActivity
            v-if="view.type === 'activity'"
            :webp="webp"
            :view="view.extra_info"
          />
          <RecentlyViewsCity
            v-if="view.type === 'city'"
            :view="view.extra_info"
          />
        </li>
      </template>
      <template v-else>
        <KlkEmptyPanel :iconWidth="100" :iconHeight="100" :content="__t('47976')" />
      </template>
    </template>
  </ul>
</template>

<script lang="ts">
import { Component, Prop } from 'vue-property-decorator';
import KlkLoading from '@klook/klook-ui/lib/loading';
import KlkEmptyPanel from '@klook/empty-panel'
import '@klook/empty-panel/dist/esm/index.css'
import RecentlyViewsActivity from './recently-views-activity.vue';
import RecentlyViewsCity from './recently-views-city.vue';

import Base from '../../../common/base';

@Component({
  components: {
    RecentlyViewsActivity,
    RecentlyViewsCity,
    KlkLoading,
    KlkEmptyPanel
  }
})
export default class RecentlyViewList extends Base {
  @Prop({ default: () => [] }) views!: any[]
  @Prop() webp!: boolean
  @Prop() loading!: boolean
}
</script>

<style lang="scss" scoped>
@import "../../../style/_variables.scss";

.recently-view-container{
  min-height: 199px;
  position:relative;
}
.wrapper {
  max-height: 400px;
  overflow: auto;

  > li {
    margin: 0 20px;
  }

  > li + li {
    border-top: 1px solid $color-gray2;
  }
}
</style>
