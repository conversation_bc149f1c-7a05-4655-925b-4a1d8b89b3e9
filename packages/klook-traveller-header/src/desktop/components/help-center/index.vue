<template>
  <Panel
    class="default-header_help-center"
    :link="__href('/faq/?ref_source=NavigationBar')"
    :content="__t('99')"
    data-spm-item="HelpCenter"
    @click="handleClick"
  />
</template>

<script lang="ts">
import { Component } from 'vue-property-decorator';
import Base from '../../../common/base';
import Panel from '../panel.vue';

@Component({
  components: {
    Panel
  }
})
export default class HelpCenter extends Base {
  handleClick() {
    window.tracker && window.tracker.gtm.sendGTMCustomEvent('Help Center|Help Button Clicked on Header');
  }
}
</script>
