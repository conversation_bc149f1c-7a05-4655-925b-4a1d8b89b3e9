<template>
  <Panel
    class="default-header_we-chat"
    :width="160"
    :height="184"
    :content="__t('12130')"
    @mouseenter.native="handleMouseenter"
  >
    <div slot="pop-content" class="qr-code" />
  </Panel>
</template>

<script lang="ts">
import { Component } from 'vue-property-decorator';
import Base from '../../../common/base';
import Panel from '../panel.vue';

@Component({
  components: {
    Panel
  }
})
export default class Wechat extends Base {
  handleMouseenter() {
    window.tracker && window.tracker.gtm.sendGTMCustomEvent('Navigation Bar|WeChat Public Account Click');
  }
}
</script>

<style lang="scss" scoped>
@import "../../../style/variables";

.default-header_we-chat {
  .qr-code {
    width: 136px;
    height: 136px;
    margin: 12px;
    box-sizing: border-box;
    background-repeat: no-repeat;
    background-size: cover;

    @include retina-sprite('https://res.klook.com/image/upload/v1646711444/kijcemjafeggyc7v0vw3.png',
      'https://res.klook.com/image/upload/v1646711444/rgfvf222fbaytwofoinn.png',
      136px, 136px
    );
  }
}
</style>
