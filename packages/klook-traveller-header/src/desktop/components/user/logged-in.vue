<template>
  <div class="default-header_logged-in">
    <Popover
      v-model="visible"
      placement="bottom-end"
    >
      <a 
        class="default-header_entry2 box" 
        href="javascript:;" 
        :style="{borderColor: memberShipColor}"
        v-galileo-click-tracker="{ spm: 'TopNavigation.Account', componentName: 'klk-traveller-header' }"
      >
        <img class="default-header_avatar" :src="formatAvatarUrl(user.avatar)">
        <div v-if="memberShipMedal" class="default-header_reward-icon" :style="{backgroundImage: `url('${memberShipMedal}')`}"></div>
      </a>
      <PanelSetting v-if="visible" slot="content" />
    </Popover>
  </div>
</template>

<script lang="ts">
import { Component, Prop } from 'vue-property-decorator';
import formatAvatarUrl from '@klook/klk-traveller-utils/lib/formatAvatar';
import Popover from '../../components/popover.vue';
import Base from '../../../common/base';
import {
  formatPicUrl
} from '../../../common/util';

@Component({
  components: {
    Popover,
    PanelSetting: () => import('./panel-setting.vue')
  }
})
export default class LoggedIn extends Base {
  @Prop({ default: false }) webp!: boolean

  visible = false

  get iconVisible(){
    return ['HK','TW','SG'].includes(this.userResidence) && this.userMembershipLevel >= 1
  }

  get memberShipColor() {
    return this?.user?.membership_style?.color || 'transparent'
  }

  get memberShipMedal() {
    return this.formatPicUrl(this?.user?.membership_style?.medal)
  }
  
  formatAvatarUrl = formatAvatarUrl
  
  formatPicUrl(url: string) {
    if (!url) {
      return ''
    }
    return formatPicUrl(url, this.webp);
  }
}
</script>

<style lang="scss" scoped>
.default-header_logged-in {
  .box{
    position: relative;
    width: 38px;
    height: 38px;
    border-radius: $radius-circle;
    margin-right: 0;
    border: 1.9px solid transparent;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  &:hover .box{
    border: 1.9px solid $color-border-dim;
  }
}
.default-header_avatar {
  width: 30px;
  height: 30px;
  border-radius: $radius-circle;
  object-fit: cover;
}
.default-header_reward-icon {
  position: absolute;
  width: 20px;
  height: 20px;
  top: 19px;
  right: -5px;
  background-image: url("https://res.klook.com/image/upload/q_85/v1677031966/yr7e1gyh98embgva9a8s.png");
  background-repeat: no-repeat;
  background-size: cover;
}
</style>
