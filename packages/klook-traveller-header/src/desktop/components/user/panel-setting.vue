<template>
  <ul class="wrapper">
    <li
      v-for="(item,index) in settingList"
      :key="index"
      :data-spm-item="item.spmItemName"
      v-galileo-click-tracker="{ spm: 'TopNavigation.' + item.spmItemName, componentName: 'klk-traveller-header' }"
      @click.stop="removeDot(item);sendMixpanel(item)"
    >
      <a :href="__href(item.path)">
        <component :is="item.icon" theme="outline" size="24" :fill="$colorTextPrimary" />
        <span>{{ __t(item.txtPath) }}</span>
      </a>
    </li>
    <li data-spm-item="LogOut" v-galileo-click-tracker="{ spm: 'TopNavigation.LogOut', componentName: 'klk-traveller-header' }">
      <a href="javascript:;" @click="logout">
        <span>{{ __t('14536') }}</span>
      </a>
    </li>
  </ul>
</template>

<script lang="ts">
import { Component, Prop } from 'vue-property-decorator';
import Base from '../../../common/base';
import { $colorTextPrimary } from '@klook/klook-ui/lib/utils/design-token-esm'

import { getSettingList } from './config';

@Component
export default class PanelSetting extends Base {
  isUnreviewDot: boolean = false

  get $colorTextPrimary() {
    return $colorTextPrimary
  }

  get settingList() {
    const settingList = getSettingList(this.language, this.displayRewardsEntry);
    return settingList.filter(item => item.isDisplay);
  }

  logout() {
    this._axios && this._axios.$post('/v3/usrcsrv/user/logout').then(() => {
      window.Cookies && window.Cookies.remove('_pt', { path: '/' });
      window.location.href = this.__href('/');
    });
  }

  // 移除红点
  removeDot(item: any) {
    if (item.path !== '/my_reviews/') {
      return;
    }
    if (this.user) {
      const reddot = localStorage.getItem(`${this.user.globalId}reddot`);
      if (reddot && (reddot === 'true')) {
        this.isUnreviewDot = false;
        localStorage.setItem(`${this.user.globalId}reddot`, 'false');
      }
    }
    window.tracker && window.tracker.sendMixpanel({
      name: 'Review page',
      props: { 'Entrance Path': 'Account Centre Hover Bar' }
    });
  }

  // 设置红点
  addDot() {
    if (this.user) {
      try {
        const reddot = localStorage.getItem(`${this.user.globalId}reddot`);
        if (this.user.unreview && !reddot && (reddot !== 'false')) {
          this.isUnreviewDot = true;
          localStorage.setItem(`${this.user.globalId}reddot`, 'true');
        } else if (reddot === 'true') {
          this.isUnreviewDot = true;
        }
      } catch (error) {
        // console.log('localStorage设置无效')
      }
    }
  }

  mounted() {
    this.addDot();
  }

  sendMixpanel(item: any) {
    if (item.path !== '/klook-gift-card/') {
      return;
    }
    window.tracker && window.tracker.sendMixpanel({
      name: 'Gift Card Page',
      props: {
        'Entrance Path': 'Navigation Bar'
      }
    });
  }
}
</script>

<style lang="scss" scoped>
.wrapper {
  padding-top: 5px;

  li:last-child {
    margin-top: 5px;
    border-top: 1px solid $color-border-normal;

    a {
      justify-content: center;
      border-radius: $radius-none $radius-none $radius-xl $radius-xl;
    }
  }

  a {
    display: flex;
    align-items: center;
    height: 40px;
    padding-left: 24px;
    padding-right: 24px;
    white-space: nowrap;
    position: relative;

    &:hover {
      background-color: $color-bg-page;
    }

    i.dot {
      width: 8px;
      height: 8px;
      position: absolute;
      right: 10px;
      top: 50%;
      transform: translateY(-50%);
      background: $color-brand-primary;
      border-radius: $radius-circle;
    }
  }

  svg {
    width: 24px;
    height: 24px;
    margin-right: 15px;
  }

  .i-icon {
    margin-right: 15px;
    display: flex;
  }
}

.logout {
  justify-content: center!important;
  padding-right: 24px!important;
}
</style>
