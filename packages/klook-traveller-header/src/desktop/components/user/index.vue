<template>
  <div class="not-hover default-header_avatar-box">
    <LoggedIn v-if="user" :webp="webp"/>

    <Logout v-else :market="market" />
  </div>
</template>

<script lang="ts">
import { Component, Prop } from 'vue-property-decorator';
import Base from '../../../common/base';

@Component({
  components: {
    LoggedIn: () => import('./logged-in.vue'),
    Logout: () => import('./Logout.vue')
  }
})
export default class PanelUser extends Base {
  @Prop() webp!: boolean
  @Prop({ default: 'global' }) market!: 'global'|'cn'
}
</script>

<style lang="scss">
</style>
