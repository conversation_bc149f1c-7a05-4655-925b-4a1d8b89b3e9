<template>
  <div class="logout">
    <div style="display: flex;">
      <a
        class="default-header_signup"
        :href="__href('/signup/')"
        data-spm-item="Signup"
        v-galileo-click-tracker="{ spm: 'TopNavigation.Signup', componentName: 'klk-traveller-header' }"
        :class="{'loading': isSignupLoading }"
        v-loading="isSignupLoading"
        data-klk-loading-show-loading-bg="false"
        data-klk-loading-show-overlay="false"
        @click.prevent="registerAction(__href('/signup/'))"
      >{{ __t('15990') }}</a>
      <a
        class="default-header_signin"
        :href="__href('/signin/')"
        data-spm-item="Login"
        v-galileo-click-tracker="{ spm: 'TopNavigation.Login', componentName: 'klk-traveller-header' }"
        :class="{'loading': isLoginLoading }"
        v-loading="isLoginLoading"
        data-klk-loading-show-loading-bg="false"
        data-klk-loading-show-overlay="false"
        @click.prevent="loginAction(__href('/signin/'))"
      >{{ __t('14228') }}</a>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Inject, Prop } from 'vue-property-decorator';
import Base from '../../../common/base';

@Component
export default class Logout extends Base {
  @Inject() getCurrency!: ()=>string
  @Inject() getAid!: ()=>number
  @Inject() getIsMP!: ()=>boolean
  @Inject() getUseLoginSdk!: ()=>boolean
  @Inject() loginSuccessCB!: (data:any)=>void
  @Inject() loginCancelCB!: ()=>void
  @Prop({ default: 'global' }) market!: 'global'|'cn'

  loginPage(eventConfig: string, setPropertyData: any) {
    window.tracker && window.tracker.sendMixpanel({
      name: eventConfig,
      props: setPropertyData
    });
  }

  isSignupLoading: boolean = false;
  isLoginLoading: boolean = false;
  timmer: any = null

  async sdkLogin(href: string) {
    if (!this.getUseLoginSdk()) {
      window.location.href = href
      return
    }

    const { loginWithSDK } = await import('@klook/klook-traveller-login')
    const supportLogin = await loginWithSDK({
      aid: this.getAid(),
      isMP: this.getIsMP(),
      platform: 'desktop',
      language: this.language,
      currency: this.getCurrency(),
      bizName: "Platform",
      purpose: "Navigator",
      market: this.market,
      success: (data: any) => {
        this.loginSuccessCB && this.loginSuccessCB(data)
      },
      cancel: () => {
        this.loginCancelCB && this.loginCancelCB()
      }
    })
    if (!supportLogin) {
      window.location.href = href
    }
  }

  async loginAction(href: string) {
    clearTimeout(this.timmer)
    this.timmer = setTimeout(()=>{
      this.isLoginLoading = true
    },200)
    window.tracker && window.tracker.gtm.sendGTMCustomEvent('Sign Up & Login|Sign In Button Clicked|Direct Clicked');

    window.tracker && window.tracker.sendMixpanel({
      name: 'Signup / Login Page',
      props: {
        Type: 'Login',
        'Entrance Path': 'Navigation Bar'
      }
    });
    this.loginPage('Signup / Login Page Revised', {
      Type: 'Login',
      'Entrance Path': 'Nav Bar Log In'
    });
    window.tracker && window.tracker.gtm.sendGTMCustomEvent({ event: 'Login' });

    await this.sdkLogin(href)
    clearTimeout(this.timmer)
    this.timmer = null
    this.isLoginLoading = false;
  }

  async registerAction(href: string) {
    clearTimeout(this.timmer)
    this.timmer = setTimeout(()=>{
      this.isSignupLoading = true
    },200)
    window.tracker && window.tracker.gtm.sendGTMCustomEvent('Sign Up & Login|Sign Up Button Clicked|Direct Sign Up');

    window.tracker && window.tracker.sendMixpanel({
      name: 'Signup / Login Page',
      props: {
        Type: 'Signup',
        'Entrance Path': 'Navigation Bar'
      }
    });
    this.loginPage('Signup / Login Page Revised', {
      Type: 'Signup',
      'Entrance Path': 'Nav Bar Sign Up'
    });
    window.tracker && window.tracker.gtm.sendGTMCustomEvent({ event: 'SignUp' });

    await this.sdkLogin(href)
    clearTimeout(this.timmer)
    this.timmer = null
    this.isSignupLoading = false;
  }
}
</script>

<style lang="scss" scoped>
@import "../../../style/_variables.scss";

$prefix: '.default-header';

.logout {
  white-space: nowrap;
}
#{$prefix}_signin,
#{$prefix}_signup {
  display: inline-block;
  font-size: $fontSize-caption-m;
  font-weight: $fontWeight-bold;
  white-space: nowrap;
  height: 36px;
  line-height: 36px;
  position: relative;

  &.loading {
    color: transparent;
  }

  ::v-deep .klk-loading-spin-icon{
    width: 20px;
  }
}

#{$prefix}_signin {
  display: inline-block;
  padding: 0 12px;
  /* stylelint-disable */
  border-radius: 120px;
  /* stylelint-enable */
  background: $color-brand-primary;
  color: $color-text-primary-onDark;

  &:hover {
    background: $color-brand-primary;
  }

  &:active {
    background: $color-brand-primary;
  }
}
#{$prefix}_signup {
  padding: 0 12px;
  border-radius: $radius-pill;
  margin-right: 8px;

  &:hover {
    background: $color-gray3;
  }
}
</style>
