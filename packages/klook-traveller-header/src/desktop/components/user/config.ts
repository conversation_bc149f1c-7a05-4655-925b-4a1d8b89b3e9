import isGiftcardEntranceVisible from '@klook/klk-traveller-utils/lib/isGiftcardEntranceVisible';
import IconSettings from '@klook/klook-icons/lib/IconSettings';
import IconBookings from '@klook/klook-icons/lib/IconBookings';
import IconCoupon from '@klook/klook-icons/lib/IconCoupon';
import IconCredit from '@klook/klook-icons/lib/IconCredit';
import IconGift from '@klook/klook-icons/lib/IconGift';
import IconHeart from '@klook/klook-icons/lib/IconHeart';
import IconComment from '@klook/klook-icons/lib/IconComment';
import IconEarnRewards from '@klook/klook-icons/lib/IconEarnRewards';
import IconKlookRewardsCircle from '@klook/klook-icons/lib/IconKlookRewardsCircle';

export const getSettingList = (language: string, displayRewardsEntry: boolean) => [{
  path: '/bookings/',
  icon: IconBookings,
  txtPath: '14834',
  isDisplay: true,
  spmItemName: 'Booking'
},{
  path: '/rewards/',
  icon: IconKlookRewardsCircle,
  txtPath: '74786',
  isDisplay: displayRewardsEntry,
  spmItemName: 'KlookReward'
},{
  path: '/coupons/',
  icon: IconCoupon,
  txtPath: '1897',
  isDisplay: true,
  spmItemName: 'PromoCode'
}, {
  path: '/klookcash/',
  icon: IconCredit,
  txtPath: '194829',
  isDisplay: true,
  spmItemName: 'Credits'
}, {
  path: '/klook-gift-card/',
  icon: IconGift,
  txtPath: '15966',
  isDisplay: isGiftcardEntranceVisible(language),
  spmItemName: 'GiftCard'
}, {
  path: '/wishlist/',
  icon: IconHeart,
  txtPath: '103859',
  isDisplay: true,
  spmItemName: 'Wish'
}, {
  path: '/my_reviews/',
  icon: IconComment,
  txtPath: '14475',
  isDisplay: true,
  spmItemName: 'MyReview'
}, {
  path: '/invite/',
  icon: IconEarnRewards,
  txtPath: '14823',
  isDisplay: true,
  spmItemName: 'Invite'
}, {
  path: '/account_setting/',
  icon: IconSettings,
  txtPath: '13483',
  isDisplay: true,
  spmItemName: 'Setting'
}];
