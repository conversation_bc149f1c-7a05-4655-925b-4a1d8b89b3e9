<template>
  <div
    id="js-header-search-box"
    ref="headerSearchBox"
    data-spm-module="TopNavigation?trg=manual"
    :class="{
      'search-box': true,
      'search-box--simple': isSimpleSearch,
      'search-box--default': !isSimpleSearch
    }"
  >
    <input
      ref="input"
      v-model.trim="keyword"
      class="search-box_input"
      autocomplete="off"
      name="klkHeadSearch"
      type="text"
      data-spm-item="SearchInput?trg=manual&typ=entry"
      v-galileo-click-tracker="{ spm: 'TopNavigation.SearchInput', componentName: 'klk-traveller-header' }"
      :placeholder="placeholder ? placeholder : __t('2416')"
      @focus="handleInputFocus"
      @keypress.enter="handleEnterKey"
      @click="handleInputClick"
    >
    <button
      class="search-box_btn-search"
      type="button"
      data-spm-item="SearchBtn?trg=manual&typ=entry"
      v-galileo-click-tracker="{ spm: 'TopNavigation.SearchBtn', componentName: 'klk-traveller-header' }"
      @mousedown.prevent="handleBtnSearchClick"
      @click="handleBtnClick"
    >
      <IconSearch class="search-box_icon-search" theme="outline" size="20" :fill="iconColor" />
    </button>

    <div class="search-box_popover">
      <KlkTravellerSearchRecommendations
        v-if="loadPopover"
        :axios="_axios"
        :language="language"
        :visible="showPopover"
        :keyword="keyword"
        @delete-history="deleteHistory"
      />
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Ref, Prop, Watch } from 'vue-property-decorator';
import SearchHistory from '@klook/klk-traveller-utils/lib/searchHistoryFunc'
import { $colorTextPrimary, $colorBrandPrimary } from '@klook/klook-ui/lib/utils/design-token-esm'
import IconSearch from '@klook/klook-icons/lib/IconSearch';
import Base from '../../common/base';

@Component({
  components: {
    KlkTravellerSearchRecommendations: () => import('@klook/klk-traveller-search-recommendations'),
    IconSearch
  }
})
export default class SearchBox extends Base {
  @Ref() readonly input!: HTMLInputElement
  @Prop() isSimple!: boolean
  @Prop() placeholder!: string
  @Prop() isRecommendationVisible!: boolean

  showPopover: boolean = false
  loadPopover: boolean = false

  keyword: string = ''
  searchHistoryArr: any[] = []
  isSimpleSearch = false

  get iconColor() {
    return this.keyword ? $colorBrandPrimary : $colorTextPrimary;
  }

  @Watch('isRecommendationVisible')
  isRecommendationVisibleChange(val: boolean) {
    this[val ? 'handleShowPopover' : 'handleHidePopover'].call(this)
  }

  created() {
    this.isSimpleSearch = this.isSimple;
  }

  handleEnterKey() {
    const el = this.$el.querySelector('.search-box_input');
    window.tracker && window.tracker.inhouse.updateBinding(el, {
      ext: {
        trigger: 'keyboard_search',
        input_word: this.keyword || ''
      }
    });
    setTimeout(() => {
      this.handleSearch();
      window.tracker && window.tracker.inhouse.track('action', '.search-box_input');
    }, 0);
  }

  handleBtnClick() {
    const el = document.querySelector('.search-box_btn-search');

    window.tracker && window.tracker.inhouse.updateBinding(el, {
      ext: {
        query: this.keyword
      }
    });
    setTimeout(() => {
      window.tracker && window.tracker.inhouse.track('action', '.search-box_btn-search');
    }, 0);
  }

  handleInputClick() {
    const el = this.$el.querySelector('.search-box_input');
    window.tracker && window.tracker.inhouse.updateBinding(el, {
      ext: {
        trigger: 'default'
      }
    });
    setTimeout(() => {
      this.handleInputFocus();

      window.tracker && window.tracker.inhouse.track('action', '.search-box_input');
    }, 0);
  }

  handleBtnSearchClick() {
    if (this.isSimpleSearch) {
      this.isSimpleSearch = false;
      this.$nextTick(() => {
        this.input.focus();
      });
      return;
    }

    if (this.keyword) {
      this.handleSearch();
    } else {
      this.input.focus();
    }
  }

  handleSearchBoxOutsideClick() {
    document.addEventListener('click', (e: any) => {
      // 判断用户的点击行为是否在 input 框和弹层上
      // 若不是，则收起弹层
      if (this.$refs.headerSearchBox) {
        const clickInPopoverAndInput = (this.$refs.headerSearchBox as HTMLElement).contains(e.target);
        if (!clickInPopoverAndInput) {
          this.hidePopover();
        }
      }
    });
  }

  handleShowPopover() {
    this.loadPopover = true;
    this.showPopover = true;
  }

  handleHidePopover() {
    this.showPopover = false;
  }

  handleInputFocus() {
    if (this.showPopover) {
      return;
    }

    this.handleShowPopover()
    this.$emit('update:isRecommendationVisible', true);
    window.tracker && window.tracker.gtm.sendGTMCustomEvent('Navigation Bar Search Box|Search Keyword Clicked');
  }

  hidePopover() {
    this.handleHidePopover()
    this.$emit('update:isRecommendationVisible', false);
  }

  handleSearch() {
    window.tracker && window.tracker.sendMixpanel({
      name: 'Search Start',
      props: {
        Keyword: this.keyword
      }
    });

    if (!this.keyword) { return; }

    SearchHistory.setSearchHistoryWord({ id: 0, type: 0, title: this.keyword, url: '' })
    const query = window.searchQuery ? `&${window.searchQuery}` : '';
    this.goToSearchPage(this.keyword, query);
  }

  goToSearchPage(keyword: string, query: string) {
    const path = `/search/result/?query=${encodeURIComponent(keyword)}${query}`;
    window.location.href = this.__href(path);
  }

  deleteHistory() {
    this.searchHistoryArr = [];
  }

  mounted() {
    this.handleSearchBoxOutsideClick();
  }
}
</script>

<style lang="scss">
@import "~@klook/klk-traveller-search-recommendations/dist/index.css";
.search-box {
  position: relative;
  height: 40px;
  transition: all 0.25s ease-in-out;
  margin-right: 24px;

  .i-icon {
    display: flex;
  }

  &--default {
    width: 280px;
  }

  &--simple {
    width: 40px;

    .search-box_input {
      display: none;
    }
  }

  &_input {
    width: 100%;
    height: 100%;
    background-color: $color-bg-page;
    border: 1px solid $color-white;
    font-size: $fontSize-body-s;
    padding-left: 12px;
    padding-right: 38px;
    caret-color: $color-brand-primary;
    box-sizing: border-box;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    border-radius: $radius-pill;
    color: $color-text-primary;

    &:focus,
    &:hover {
      border: 1px solid $color-brand-primary;
      outline-style: none;
      background: $color-bg-widget-normal;
    }

    &::placeholder {
      color: $color-text-secondary;
      opacity: $opacity-solid;
    }
  }

  &_btn-search {
    position: absolute;
    top: 0;
    right: 0;
    height: 40px;
    margin-right: 14px;
    padding: 0;
    background: transparent;
    border: none;
    outline: none;
    color: $color-text-secondary;
    display: flex;
    align-items: center;
    cursor: pointer;
  }

  &_popover {
    position: absolute;
    top: 46px;
    left: 0;
    width: 800px;
    background-color: $color-bg-widget-normal;
    border-radius: $radius-l;
    /* stylelint-disable */
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);
    /* stylelint-enable */
    overflow: hidden;
  }
}
</style>
