<template>
  <div class="site-change">
    <div class="changeWebsiteSection">
      <div class="changeTip">
        {{ title }}
      </div>
      <div class="changeBtn" @click="settingWebsite">
        {{ __t('16635') }}
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Prop } from 'vue-property-decorator';
import Base from '../../../common/base';

@Component
export default class SiteChange extends Base {
  @Prop() title!: string

  settingWebsite() {
    window.location.href = this.__href('/edit_profile?residence=show')
  }
}
</script>

<style lang="scss" scoped>
.changeWebsiteSection {
  font-size: $fontSize-caption-m;
  padding: 16px 0;
  border-top: 1px solid $color-border-normal;
  margin: 12px 10px 0;

  .changeTip {
    color: $color-text-secondary;
    line-height: 16px;
  }

  .changeBtn {
    color: $color-info;
    margin-top: 8px;
    cursor: pointer;
    font-weight: $fontWeight-bold;
  }
}
</style>
