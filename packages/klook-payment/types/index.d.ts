
import Vue from 'vue'

export const DialogTips: Vue.Component;
export const PaymentTerms: Vue.Component;

type CheckoutParams = Record<string, any>
type Headers = { 'X-Klook-Request-Id': string }


type TypeKlookPayment = Vue.Component & {
  install (vue: typeof Vue): void;
  getCheckoutParams(selectedCouponList?: { finance_type: string, code: string }[]): {
    payment_type: string
    headers: Headers,
    checkout_params: CheckoutParams
  }
  submit(params: object): Promise<void>
  verify(): Promise<void>
}

export const KlookPayment: TypeKlookPayment

export default KlookPayment


export const transformPayModel: (checkoutDetail: { payment_info: Record<string, any> }) => ({
  paymentType: string
  payLaterDescForPay: string
  currencyChangeTips: string
})

export const initCheckout: (this: Vue, draft_order_id: any) => Promise<void | {
  headers: Headers,
  checkout_params: CheckoutParams
}>

export const payemtMiddleware: (ctx: any) => Promise<void>
