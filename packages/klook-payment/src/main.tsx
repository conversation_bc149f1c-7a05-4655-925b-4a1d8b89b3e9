import { Component, Ref, Vue } from 'vue-property-decorator'
import { CreateElement, FunctionalComponentOptions, VueConstructor } from 'vue'
// @ts-ignore
import { Dialog } from '@klook/klook-ui/lib/modal'
// @ts-ignore
import Radio, { RadioGroup } from '@klook/klook-ui/lib/radio'
// @ts-ignore
import Input from '@klook/klook-ui/lib/input'
// @ts-ignore
import Button from '@klook/klook-ui/lib/button'
// @ts-ignore
import IconProtect from '@klook/klook-icons/lib/IconProtect'
import { iconSgLogo } from './assets/base64-svg-icons'
import { getGuestReturnUrl, jumpOrReload, getLangPath } from './payment-sdk/core/utils'
import type { IEvent, Method } from './types.d'
import { HEADERS } from './utils'
import NativeSdk from './components/native-sdk.vue'
import { RadioWrapper, DialogTips, MarkdownTerms } from './components/method-item/modules'
import { DesktopMethodList, MobileMethodList } from './components/method-list'
import CashierTop from './components/cashier-top'
import Mixins from './mixins'
import './index.scss'
import { paymentLogQuery, getErrorInfo } from './payment-sdk/core/utils/logQuery'

const dialogOptions = {
  scrollable: true,
  overlayClosable: false,
  closable: true,
  showDefaultFooter: false
}

@Component({
  inheritAttrs: false,
  name: 'klk-payment'
})
export default class KlookPayment extends Mixins {
  @Ref('mobileMethodList') mobileMethodList?: MobileMethodList

  get ISMOBILE() {
    return this.isMobile()
  }

  get footerAttrs() {
    return {
      okLabel: this.$t('global.tips.okTxt'),
      cancelLabel: this.$t('global.tips.cancelTxt')
    }
  }

  render() {
    if (!this.paymentInfo || !this.creditcardOptions.cardTypeRules.length) {
      return
    }

    const { special_scenes, payment_type, methods, default_method_key, supported_cards, payment_support_type } = this.paymentInfo
    const hasMethods = methods?.length > 0
    let methodListVnode!: JSX.Element | JSX.Element[]

    // t特殊情况  没有支付方式， 包含0元购 和 无法转货币
    const isSpecialScenes = special_scenes?.type && ['non_need', 'non_usable'].includes(special_scenes.type)

    if (isSpecialScenes) {
      methodListVnode = <p
        class={`text-${this.nonUsablePaymentError && payment_type === 'paylater' ? 'error' : 'secondary' }`}
      >
        { special_scenes.content }
      </p>
    } else if (hasMethods) {
      const MethodListType = this.ISMOBILE
        ? MobileMethodList
        : DesktopMethodList

      methodListVnode = [
        // @ts-ignore
        <NativeSdk />,
        <MethodListType
          ref={`${this.platform}MethodList`}
          methodList={ methods }
          verifyMethod={ this.verifyMethod }
          currentKey={ default_method_key }
          onCommit-current-method={ (method: Method) => { this.currentMethod = method } }
        >
          {
            // @ts-ignore
            this.cashierTop && <CashierTop
              slot="cashier-top"
              attrs={ this.cashierTop }
            />
          }

          {
            !this.ISMOBILE && supported_cards.types && supported_cards.types.length > 0 && <div
              slot="supported-cards"
              class="supported-cards"
            >
              {
                supported_cards.types.map(({ icon, type }) => icon && <img
                  key={ type + icon }
                  class="creditcard-icon"
                  src={ icon }
                  height="30"
                />)
              }
            </div>
          }
        </MethodListType>
      ]
    }

    return <div
      data-spm-module
      class={['klook-payment', !this.ISMOBILE && 'platform-desktop']}
    >
      {
        payment_support_type?.length > 1
          ? payment_support_type.map(({ key, tips, content }) => {
            const isSelected = key === payment_type
            const isPaylater = key === 'paylater'
            return <RadioWrapper
              key={ key }
              isSelected={ isSelected }
              reverse={ this.ISMOBILE }
              ihtAttrs={{
                'data-spm-virtual-item': '__virtual',
                'data-spm-module': `${isPaylater ? 'PayLater' : 'PayNow'}_Selection`
              }}
              class="payment-item"
              onSelect={ () => this.changePaymentType(key) }
            >
              <template slot="label">
                <b>{ content }</b>
                {
                  tips && isPaylater &&
                    <span
                      data-spm-module="PayLater_Icon"
                      data-spm-virtual-item={`__virtual${this.ISMOBILE ? '' : '?evt=mouseenter'}`}
                    >
                      {/* @ts-ignore */}
                      <DialogTips
                        markdown
                        botoomSheet
                        dialog={{ message: tips.content, title: tips.title }}
                        options={{ maxHeight: 400 }}
                      />
                    </span>
                }
              </template>
              {
                isSelected && [
                  !isSpecialScenes && isPaylater && this.paylaterChooseDesc && <div
                    class="paylater-alert"
                    data-spm-module="PayLater_TnC_1"
                    data-spm-virtual-item="__virtual"
                  >
                    <IconProtect theme="filled" size="22" fill="#08B371" />
                    {/* @ts-ignore */}
                    <MarkdownTerms content={this.paylaterChooseDesc} />
                  </div>,
                  methodListVnode
                ]
              }
            </RadioWrapper>
          })
          : [
            <p class="klook-payment--section-title">{ this.$t('index.payment.channel') }</p>,
            methodListVnode
          ]
      }
    </div>
  }

  verifyMethod(method = this.currentMethod) {
    const { method_key, action, sub_options } = method || {}

    if (!method_key || ( // 目前发现naverpay 是一级的，但是下面包含个和它method_key 一样的子支付方式，所以需要排除掉
      action !== 'select' && !sub_options?.some(subMethod =>
        subMethod && subMethod.method_key === method_key && subMethod.action === 'select')
    )) {
      this.$toast(this.$t('global.payment.type.tip'))
      return Promise.reject(new Error('METHOD_KEY_INVALID'))
    }

    return this.paramsManager.verifyForm(method_key)
  }

  async verify() {
    try {
      if (!this.paymentInfo || !this.creditcardOptions.cardTypeRules.length) {
        this.$toast(this.$t('global.payment.error'))
        throw new Error('LOSE_PAYMENT_DEPENDENCIES')
      }

      if (!this.isZeroPayment) {
        this.nonUsablePaymentError = this.paymentInfo.special_scenes?.type === 'non_usable'
        if (this.nonUsablePaymentError) {
          throw new Error('non usable payment method')
        }

        await this.verifyMethod()
        await this.verifyPhoneNumber() // DBS 手机号
      }

      await this.verifySgTerm()
      return { success: true }
    // @ts-ignore
    } catch (error: any) {
      const WhiteListCodes = ['LOSE_PAYMENT_DEPENDENCIES', 'PCI_PREFIX', 'DBS_PHONE_VERIFY_CANCEL', 'SG_TERM_VERIFY_CANCEL']
      // 白名单的错误code这里不需要处理。不需要滚动到支付组件附近
      if (error.message && !WhiteListCodes.includes(error.message)) {
        // eslint-disable-next-line no-unused-expressions
        this.ISMOBILE
          ? this.mobileMethodList?.onOpenLayer()
          : this.$el.scrollIntoView({
            behavior: 'smooth'
          })
      }

      this.captureSentryEvent({
        message: '[validate] - cashier-verify | verify-fail',
        level: 'warning',
        fingerprint: ['klook-payment | ' + this.businessLine, 'validate', 'main.tsx@verify'],
        tags: {
          module: 'cashier-verify',
          action: 'ui',
          path: 'main.tsx@verify',
          message: '提交支付时校验参数失败，错误代码是：' + error.message
        }
      })

      return { success: false, error }
    }
  }

  async verifySgTerm() {
    const { type, link } = this.paymentInfo?.terms || {}
    if (type === 'singapore' && !this.paramsManager.terms_accepted) {
      const { result } = await Dialog({
        ...dialogOptions,
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        content: (h: CreateElement) => <div class="klk-payment--sg-term">
          <div class="sg-term-header">
            <div class="sg-term-title">
              <img src={iconSgLogo} height="26" />
              <p><b>{this.$t('pay.sg_term.title')}</b></p>
            </div>
            <p class="text-secondary">
              {this.$t('pay.sg_term.title_desc')}
            </p>
          </div>
          <p><b>{this.$t('pay.sg_term.question')}</b></p>
          <RadioGroup
            value={this.paramsManager.terms_accepted}
            onChange={(val: string) => { this.paramsManager.terms_accepted = val }}
          >
            {
              [{
                value: 'accepted',
                title: this.$t('pay.sg_term.option_yes'),
                content: this.$t(
                  'pay.sg_term.option_content_yes',
                  [link || 'https://www.stb.gov.sg/industries/travel-agents/Documents/TInsurers.pdf']
                )
              }, {
                value: 'refused',
                title: this.$t('pay.sg_term.option_no'),
                content: this.$t('pay.sg_term.option_content_no')
              }].map(({ value, title, content }) => <Radio groupValue={value}>
                <b>{title}</b>
                <p class="text-secondary" domPropsInnerHTML={content} />
              </Radio>)
            }
          </RadioGroup>
        </div>,
        //  @ts-ignore
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        footer: (h: CreateElement, closeHandler: Function) => <DialogButtonFooter
          confirmDisabled={!this.paramsManager.terms_accepted}
          attrs={this.footerAttrs}
          onCancel={closeHandler}
          buttonALign={ this.ISMOBILE && 'block' }
        />
      })

      if (!result) {
        // 还原用户取消前的选项
        this.paramsManager.terms_accepted = ''
        return Promise.reject(new Error('SG_TERM_VERIFY_CANCEL')) // verifySgTerm: user cancel 不需要弹message
      }
    }

    return this.paramsManager.terms_accepted
  }

  async verifyPhoneNumber() {
    // 0元单 currentMethod 会为null
    if (this.currentMethod?.method_key === 'paylah' && !this.currentMethod?.token && !this.paramsManager.phone_number) {
      const verifyTips = Vue.observable({ visible: false })
      const { result } = await Dialog({
        title: this.$t('79399'),
        ...dialogOptions,
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        content: (h: CreateElement) => <div class="klk-payment--dbs-phone">
          <div class="dbs-phone-input">
            <span>+ 65</span>
            <Input
              value={this.paramsManager.phone_number}
              class="phone-number-warp-input"
              placeholder={this.$t('79986')}
              type="none"
              append-icon="icon_communication_phone"
              attrs={{ oninput: "this.tagName === 'INPUT' && (this.value = this.value.replace(/\\D/g, ''))" }}
              maxlength={8}
              onInput={(val: string) => {
                if ((this.paramsManager.phone_number = val).length === 8) {
                  verifyTips.visible = false
                }
              }}
              onBlur={() => {
                verifyTips.visible = this.paramsManager.phone_number.length < 8
              }}
            />
          </div>
          <p class="dbs-phone-error-tips" style={!verifyTips.visible && 'display:none'}>
            {this.$t('79403')}
          </p>
        </div>,
        // @ts-ignore
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        footer: (h: CreateElement, closeHandler: Function) => <DialogButtonFooter
          confirmDisabled={!this.paramsManager.phone_number}
          attrs={this.footerAttrs}
          onCancel={closeHandler}
          showCanceButton={ false }
          buttonALign={ this.ISMOBILE && 'equal' }
          onConfirm={() => {
            !verifyTips.visible && closeHandler(true)
          }}
        />
      })

      this.$inhouse.track('custom', this.$el, {
        spm: 'PaymentMethod_Paylah_' + (result ? 'Confirm' : 'Cancel')
      })

      if (!result) {
        // 还原用户取消前的选项
        this.paramsManager.phone_number = ''
        return Promise.reject(new Error('DBS_PHONE_VERIFY_CANCEL')) // verifyPhoneNumber: user cancel 不需要弹message
      }
    }

    return this.paramsManager.phone_number
  }

  // submit 跟业务线约定的是在调用完verify 后调用， 可认为已校验通过
  async submit(params: {
    payment_asset_no?: string,
    order_no?: string,
    merchant_id?: string
    country_code?: string
  } = {}) {
    if (!params?.order_no) {
      throw new Error('params error! order_no is null')
    }

    if (!this.paymentInfo || (!this.isZeroPayment && !this.currentMethod)) {
      throw new Error('Validation paymentDetail failure')
    }

    const payStartTime = Date.now()

    const extData = {
      PaymentMethod: this.currentMethod?.method_key || 'NA',
      OrderNo: params.order_no
    }

    // eslint-disable-next-line no-unused-expressions
    this.$inhouse?.track('custom', this.$el, {
      spm: 'PlaceOrder',
      ext: extData
    })

    this.subSentry?.addBreadcrumb({
      message: '[create order] - success',
      data: extData
    })

    const { common_voucher = [] } = this.paymentInfo.asset_voucher_info.list || {}
    const assetVoucherList = common_voucher.filter(coupon => coupon.selected)

    const checkoutParams: Record<string, any> = {
      ...params,
      ...this.paramsManager.getSubmitParams(this.isZeroPayment ? undefined : this.currentMethod),
      coupon_code: this.getCurrentCouponCode(),
      checkoutXKlookReqId: HEADERS['X-Klook-Request-Id'],
      user_total_pay_price: this.paymentInfo.price_info.pay_price.amount,
      user_pay_currency: this.paymentInfo.price_info.pay_price.currency,
      asset_voucher_code: assetVoucherList[0]?.code,
      asset_voucher_code_list: assetVoucherList.map(coupon => coupon.code)
    }

    if (['cap.klook.com', 'klook.cn'].includes(window.location.host)) {
      checkoutParams.return_url = getGuestReturnUrl.call(this, params.order_no, this.currentMethod?.method_key)
    }

    const { default: kPay } = await import('./payment-sdk/core/jsapi')

    return new Promise((resolve) => {
      this.$on('success', () => {
        if (this.subSentry) {
          this.subSentry.reportPerf('operation_time')
          // 支付失败，再点击提交，需要重新填写 DBS 手机号
          this.subSentry.reportPerf('pay_time', ((Date.now() - payStartTime) / 1000).toFixed(1), 'second')
          this.subSentry.setPageTransactionStatus('ok')
        }
        resolve({ success: true, result: null })
      })

      this.$on('cancel', (_error?: Error, logError?: IEvent['tags']) => {
        const resp = this.handlerPayError(
          checkoutParams,
          new Error('cancel'),
          Object.assign({ error_type: 'user-cancel', level: 'warning' }, logError)
        )
        resolve(resp)
      })

      this.$on('error', (error?: null | Error & { code?: string }, logError?: IEvent['tags']) => {
        const resp = this.handlerPayError(
          checkoutParams,
          error || new Error(this.$t('173929')),
          logError
        )
        resolve(resp)
      })

      try {
        kPay.call(this, checkoutParams)
      } catch (err) {
        const resp = this.handlerPayError(
          checkoutParams,
          new Error(this.$t('173929')),
          {
            module: 'submit-kPay',
            action: 'ui',
            path: 'main.tsx@submit-catch',
            message: 'submit-kPay兜底捕获的错误， errMessage：' + getErrorInfo(err)
          })
        resolve(resp)
      }
    }).finally(() => {
      ;['error', 'cancel', 'success'].forEach((event) => {
        this.$off(event)
      })
    })
  }

  /**
   * @function handlerPayError 
   * @params error 用户可以看到的错误原因
   * @params logError sentry 和 日志上报的错误
   * @returns Promise<errorResult> reject 代表业务线需要继续处理错误
   */

  async handlerPayError(checkoutParams: any, error: Error & { code?: string }, logError?: IEvent['tags']) {
    // 支付失败，再点击提交，需要重新填写 DBS 手机号
    this.paramsManager.phone_number = ''

    // 上报 sdk、api、data 类型的错误
    if (typeof logError === 'object') {
      const errorType: 'sdk-error' | 'request-fail' | 'runtime-error' = logError.error_type || (
        logError.action === 'api'
          ? 'request-fail'
          : 'runtime-error'
      )

      this.captureSentryEvent({
        message: `[${logError.action}] - ${logError.module} | ${errorType}`,
        level: logError.level as 'warning' || (logError.type === 'api' ? 'error' : 'fatal'),
        fingerprint: ['klook-payment | ' + this.businessLine, logError.action, logError.path],
        tags: logError
      })
    }

    if (error.message !== 'cancel') {
      paymentLogQuery({
        action: logError?.action
          ? {
            ui: 3,
            api: 2,
            sdk: 4,
            default: 5
          }[logError.action]
          : 3,
        error_msg: getErrorInfo(error) || logError?.message,
        file_name: logError?.path || 'main.tsx',
        message: `订单 ${checkoutParams.order_no} 支付失败，submitReq：${JSON.stringify(checkoutParams || {})}`
      })

      if (error.message) {
        await this.$alert(error.message, { zIndex: 4000 })
      }

      if (error.code) {
        const errorResult = { success: false, error }

        if (error.code === '0222E0005') {
          const { method_key = '', payment_coupon_code: coupon_code } = this.currentMethod || {}
          this.updateSettlement({
            method_key,
            coupon_code,
            auto_select_coupon: !coupon_code
          })
          return errorResult
        }

        if (['0220B10111', '0220B10112', '0220B10113', 'KLSC002', 'KLSC004'].includes(error.code)) {
          jumpOrReload()
          return errorResult
        }

        if (error.code === '4001') {
          jumpOrReload(getLangPath.call(this, 'login'))
          return errorResult
        }

        if ([
          '02005001000',
          '02005001001',
          '02005001002',
          '02005001004',
          '02004001007',
          '02005001005',
          '999988',
          'KLSC006',
          'KLSC003',
          'KLSC005'
        ].includes(error.code)) {
          jumpOrReload(getLangPath.call(this, 'bookings'))
          return errorResult
        }
      }
    }

    return Promise.reject(error)
  }

  static install(Vue: VueConstructor) {
    // @ts-ignore
    Vue.component(this.options.name, this)
  }
}

// 这里自定义footer是为了实现确认按钮有禁用的逻辑
const DialogButtonFooter: FunctionalComponentOptions<{
  confirmDisabled: boolean,
  okLabel: string,
  cancelLabel: string,
  showCanceButton: boolean
  buttonALign: string
}> = {
  functional: true,
  render(_h, { props: { okLabel, confirmDisabled, cancelLabel, buttonALign, showCanceButton }, listeners }) {
    const { cancel, confirm } = listeners as Record<string, Function>
    return <div
      class={[
        'klk-modal-button-group',
        buttonALign && `klk-modal-button-group-${buttonALign}`
      ]}
    >
      {
        showCanceButton && <Button
          type="outlined"
          onClick={cancel}
        >
          {cancelLabel}
        </Button>
      }
      <Button
        type="primary"
        disabled={confirmDisabled}
        onClick={confirm || cancel.bind(null, true)}
      >
        {okLabel}
      </Button>
    </div>
  }
}
