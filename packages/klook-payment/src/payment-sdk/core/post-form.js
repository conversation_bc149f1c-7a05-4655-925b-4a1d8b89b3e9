/* eslint-disable */
import { offbeforeunload } from './utils'

/**
 * @description 表单提交的公共处理类
 * @param resData
 */
export const postForm = async (resData, ctx) => {
  const redirect = resData.action.action_details.redirect
  const { data, href, method } = redirect
  const form = await createForm(data, href, method)
  offbeforeunload()
  ctx.$emit('success')
  form.submit()
}

/**
 * @description 创建一个表单，并将数据等在表单内处理完毕
 * @param data
 * @param url
 * @param method
 */
const createForm = async (data, url, method) => {
  const form = document.createElement('form')
  form.method = method
  form.action = url
  for (const key in data) {
    const input = document.createElement('input')
    input.name = key
    input.value = data[key]
    form.appendChild(input)
  }
  document.body.appendChild(form)
  return form
}
