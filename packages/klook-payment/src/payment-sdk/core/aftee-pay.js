/* eslint-disable */
import assign from 'lodash/assign'
import {
  offbeforeunload,
  enterExecuteSuccessPage,
  execute,
  getExecuteBaseData
} from './utils'
import { paymentLogQuery } from './utils/logQuery'

const Aftee = window.Aftee
let reqData = {}

/**
 * @description Aftee 回调结果 post 到 execute 接口
 * @param paymentDetails Aftee 返回的信息
 * @param ctx KlkPayment 实例
 * @caller 方便根据错误日志定位错误
 */
const executePayment = (paymentDetails, ctx, caller) => {
  reqData = assign(reqData, {
    payment_details: paymentDetails,
    modulePath: 'at <aftee-pay.js>: Aftee.config-' + caller
  })
  execute(ctx, reqData, (result) => {
    ctx.$emit('success')
    offbeforeunload()
    enterExecuteSuccessPage(result.order_no, (reqData.return_url && reqData.return_url.success), result.payment_asset_no)
  })
}

/**
 * 是否有重复回调
 */
function isRepeatCallback(errors) {
  if (!Array.isArray(errors)) { return }
  let isExistRepeatCallback = false
  for (let i = 0; i < errors.length; i++) {
    const code = errors[i].code
    if (code === 'EATN1101') {
      isExistRepeatCallback = true
    }
  }

  return isExistRepeatCallback
}


/**
 * @description 调用 Aftee 支付
 */
export const afteePay = (submitResult, ctx) => {
  reqData = getExecuteBaseData(submitResult)
  const redirectData = submitResult.action.action_details.redirect.data
  const pub_key = redirectData.pub_key
  // payment 不能有 pub_key
  delete redirectData.pub_key

  Aftee.config({
    pub_key,
    payment: redirectData,
    authenticated(authentication_token, user_no) {},
    cancelled() {
      executePayment({ pay_result: 'cancelled' }, ctx, 'cancelled')
    },
    failed(response) {
      executePayment({ pay_result: 'failed', redirect: response }, ctx, 'failed')
    },
    succeeded(response) {
      executePayment({ pay_result: 'succeeded', redirect: response }, ctx, 'succeeded')
    },
    error(name, message, errors) {
      isRepeatCallback(errors)
        ? paymentLogQuery({
          action: 4,
          order_no: submitResult.order_no,
          file_name: 'aftee-pay.js',
          message: `重复订单错误信息：${message}，errors：${JSON.stringify(errors)}`
        })
        : executePayment({
            pay_result: 'error',
            redirect: { name, message, errors }
          }, ctx, 'error')
    }
  }, function () {
    Aftee.merge({
      payment: redirectData
    }),
    Aftee.sync(),
    Aftee.start()
  })
}
