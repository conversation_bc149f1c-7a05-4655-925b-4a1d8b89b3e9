import { offbeforeunload, execute, enterExecuteSuccessPage, getExecuteBaseData } from './utils'

const getExecuteData = (data) => {
  const reqData = getExecuteBaseData(data)
  const paymentDetails = {
    modulePath: 'signed-pay.js',
    payment_details: {
      token: data.token
    }
  }

  return Object.assign(reqData, paymentDetails)
}

export const signedPay = (submitResult, ctx) => {
  execute(ctx, getExecuteData(submitResult), (executeResult) => {
    offbeforeunload()
    enterExecuteSuccessPage(executeResult.order_no, submitResult.return_url && submitResult.return_url.success)
  })
}
