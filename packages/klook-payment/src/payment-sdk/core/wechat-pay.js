/* eslint-disable */
import { redirectPay } from './redirect-pay'
import { offbeforeunload, isPrdEnv, urlParam, getLangPath, isWechatBrowser } from './utils'
const emitError = function(message, error_type) {
  this.$emit('error', null, {
    module: 'wechat-pay',
    action: 'ui',
    error_type,
    path: 'wechat-pay.js',
    message
  })
}

  /** 
 * @baoyi.cui 2025-5-13: 进入此流程一定是mobile 端， deskop 端都走了 二维码支付
 *  mobile 只走wechat 直连， 如果在微信浏览器中就走微信SDK支付
 *  desktop 在com 站走 ntt 网关， 在cn 站走wechat 直连，如果在微信浏览器中，不会下发native， 即不走微信SDK支付， 走二维码支付
 */ 

export const wechatPay = (submitResult, ctx) => {
  const orderNo = submitResult.order_no
  // 如果submit 的时候没有提交微信 weixin_open_id , 后端不会返回submitResult.action?.action_details?.native
  if(!submitResult.action || !submitResult.action.action_details) {
    return emitError.call(ctx, '微信支付异常: submitResult数据缺submitResult.action.action_details')
  }

  const _host = (ctx?.$store || window.__KLOOK__)?.state?.klook.host || window.location.host

  if (submitResult.action.action_details.native && isWechatBrowser(window.navigator.userAgent)) {
    const { invoice_guid, invoice_submission_guid } = submitResult
    const { appId, timeStamp, nonceStr, paySign, signType, package: packageStr } = submitResult.action.action_details.native

    if (!isPrdEnv) {
      /*
            目前公众号上的支付目录配的是 https://www.klook.com/ （只能配置备案过的域名）,只能进行转发
            https://pay.weixin.qq.com/wiki/doc/api/jsapi.php?chapter=7_3
             */
      let redirectUrl = `${urlParam('wechat_browser', '1', location.href)}&invoice_guid=${invoice_guid}&invoice_submission_guid=${invoice_submission_guid}`
      // 如果URL内包含code，先行剔除code
      if (redirectUrl.includes('&code=')) {
        redirectUrl = redirectUrl.split('&code=')[0]
      }
      const searchStr = `?redirect_url=${encodeURIComponent(redirectUrl)}&app_id=${appId}&time_stamp=${timeStamp}&nonce_str=${nonceStr}` +
                `&package_str=${packageStr}&pay_sign=${paySign}&sign_type=${signType}`
      window.location.href = `https://${_host}/web3/wechatbrowser_pay_helper/${searchStr}`
      ctx.$emit('success')
    } else {
      const onBridgeReady = () => {
        window.WeixinJSBridge.invoke(
          'getBrandWCPayRequest', {
            appId,
            timeStamp,
            nonceStr,
            package: packageStr,
            signType,
            paySign
          },
          function (res) {
            const errMsg = res.err_msg
            if (errMsg === 'get_brand_wcpay_request:ok') {
              offbeforeunload()
              window.location.href = getLangPath.call(ctx, `web3/order-payment/wechatbrowserpay/?invoice_guid=${invoice_guid}&invoice_submission_guid=${invoice_submission_guid}`)
              ctx.$emit('success')
            } else {
              emitError.call(ctx, '微信SDK 支付失败，错误信息：' +  errMsg, 'sdk-error')
            }
          }
        )
      }

      if (typeof window.WeixinJSBridge === 'object') {
        onBridgeReady()
      } else if (document.addEventListener) {
        document.addEventListener('WeixinJSBridgeReady', onBridgeReady, false)
      } else if (document.attachEvent) {
        document.attachEvent('WeixinJSBridgeReady', onBridgeReady)
        document.attachEvent('onWeixinJSBridgeReady', onBridgeReady)
      }
    }
  } else if (!isPrdEnv) {
    const invoiceGuid = submitResult.invoice_guid
    const href = submitResult.action.action_details.redirect.href
    const middle_url = `https://${_host}/web3/wechatpay_resp?redirect_url=`
    const wait_url = `${location.protocol}//${location.host}${getLangPath.call(ctx)}web3/order-payment/wechatpay?invoice_guid=${invoiceGuid}&order_no=${orderNo}`
    const redirect_url = middle_url + encodeURIComponent(wait_url)
    location.href = middle_url + encodeURIComponent(`${href.slice(0, href.indexOf('&redirect_url='))}&redirect_url=${encodeURIComponent(redirect_url)}`)
    ctx.$emit('success')
  } else {
    redirectPay(submitResult, {
      onSuccess: ctx.$emit.bind(ctx, 'success'),
      onError: ctx.$emit.bind(ctx, 'error')
    })
  }
}
