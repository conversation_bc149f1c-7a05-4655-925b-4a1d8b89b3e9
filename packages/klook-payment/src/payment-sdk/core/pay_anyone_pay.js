export const payAanonePay = (submitResult, ctx) => {
  try {
    const href = submitResult.action.action_details.redirect.href
    window.top.location.href = href
    ctx.$emit('success')
  } catch (error) {
    ctx.$emit('error', null, {
      module: 'payAnyonePay',
      action: 'ui',
      path: 'pay_anyone_pay.js',
      message: '读取submitResult.action.action_details.redirect.href 失败'
    })
  }
}
