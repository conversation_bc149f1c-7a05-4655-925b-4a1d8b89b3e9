import drawPaymeQrcode from './draw-payme-qrcode'

export default (type: string, containerEl: HTMLElement, codeData: string) => {
  if (!containerEl) {
    throw new Error('at <gen-qrcode.ts>: expect params<containerEl> as HTMLElement or params<codeData> as string, but is null')
  }

  if (!codeData) {
    throw new Error('at <gen-qrcode.ts>: expect params<codeData> as string, but is null')
  }

  switch (type) {
    case 'iframe': {
      const iframe = document.createElement('iframe')
      iframe.setAttribute('scrolling', 'no')
      iframe.setAttribute('src', codeData)
      containerEl.appendChild(iframe)
      return loadAsset(iframe)
    }
    case 'url': {
      const img = document.createElement('img')
      img.setAttribute('src', codeData)
      containerEl.appendChild(img)
      return loadAsset(img)
    }
    default: {
      const canvas = document.createElement('canvas')
      containerEl.appendChild(canvas)
      return (type === 'payme' ? drawPaymeQrcode : genCanvasQrcode)(
        canvas,
        codeData
      )
    }
  }
}

const genCanvasQrcode = (canvasEl: HTMLCanvasElement, codeData: string) =>
  new Promise<void>((resolve, reject) => {
    // @ts-ignore
    import('qrcode/lib/browser.js')
      .then((QRCode) => {
        QRCode.toCanvas(
          canvasEl,
          codeData,
          {
            width: 240,
            margin: 0,
            errorCorrectionLevel: 'H'
          },
          // @ts-ignore
          error => (error ? reject(error) : resolve())
        )
      })
      .catch(reject)
  })

const loadAsset = (el: HTMLIFrameElement | HTMLImageElement) =>
  new Promise((resolve, reject) => {
    // iframe 监听不到 500错误
    el.onload = resolve
    el.onerror = reject
  }).finally(() => {
    el.onload = el.onerror = null
  })
