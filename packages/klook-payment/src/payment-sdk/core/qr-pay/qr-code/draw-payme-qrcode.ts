import { asyncLoadScript } from '../../utils'

declare global {
  interface Window {
    qrcode: any
  }
}
export default async (
  canvasEl: HTMLCanvasElement,
  codeData: string
) => {
  if (!window.qrcode) {
    await new Promise((resolve, reject) => {
      try {
        asyncLoadScript('https://cdn.klook.com/pay/pay-me-qrcode.js', resolve)
      } catch (error) {
        reject(error)
      }
    })
    window.qrcode.stringToBytes = window.qrcode.stringToBytesFuncs['UTF-8']
  }
  const qr = window.qrcode(suggestTypeNumber(codeData), 'Q')
  qr.addData(codeData, 'Byte')
  qr.make()
  return drawQrCode(qr, canvasEl, 7)
}

const drawQrCode = async (
  qrcode: any,
  canvasEl: HTMLCanvasElement,
  margin: number
) => {
  const width = (canvasEl.width = canvasEl.height = 240)

  const ctx = canvasEl.getContext('2d')!
  ctx.setTransform(1, 0, 0, 1, 0, 0)
  ctx.clearRect(0, 0, width, width)
  const cellCount = qrcode.getModuleCount()

  const cellRadius = (width - margin * 2) / cellCount / 2
  const offset = cellRadius + margin

  const iconPercent = 32 / 260
  const iconClipPercent = 35 / 260

  const logoPercent = 67 / 260
  const logoClipPercent = 72 / 260

  // white background
  ctx.fillStyle = '#ffffff'
  ctx.fillRect(0, 0, width, width)

  // PM4B red
  ctx.fillStyle = '#c92a23'

  // top left eye
  drawEye(ctx, margin, cellRadius, 0, 0)
  // top right eye
  drawEye(ctx, margin, cellRadius, 2 * (cellCount - 7) * cellRadius, 0)
  // bottom left eye
  drawEye(ctx, margin, cellRadius, 0, 2 * (cellCount - 7) * cellRadius)

  const iconWidth = (width - 2 * margin) * iconPercent
  const logoWidth = (width - 2 * margin) * logoPercent

  // PayMe icon in bottom right
  await drawIcon(ctx, iconWidth, width, margin)
  // business logo in the middle
  await drawLogo(ctx, logoWidth, width)

  const iconClip =
    width - cellRadius - margin - (width - 2 * margin) * iconClipPercent
  const logoLeftClip = width / 2 - ((width - 2 * margin) * logoClipPercent) / 2
  const logoRightClip = width / 2 + ((width - 2 * margin) * logoClipPercent) / 2

  for (let r = 0; r < cellCount; r += 1) {
    for (let c = 0; c < cellCount; c += 1) {
      const x = c * cellRadius * 2 + offset
      const y = r * cellRadius * 2 + offset

      if (
        (r < 7 && (c < 7 || c > cellCount - 8)) ||
        (r > cellCount - 8 && c < 7)
      ) {
        // don't draw cells over the "eyes"
        continue
      } else if (x >= iconClip && y >= iconClip) {
        // don't draw cells over the PM4B icon
        continue
      } else if (
        x >= logoLeftClip &&
        x < logoRightClip &&
        y >= logoLeftClip &&
        y < logoRightClip
      ) {
        // don't draw cells over the logo
        continue
      }

      if (qrcode.isDark(r, c)) {
        ctx.beginPath()
        ctx.arc(x, y, cellRadius, 0, 2 * Math.PI)
        ctx.fill()
      }
    }
  }
}

function drawEye(
  ctx: CanvasRenderingContext2D,
  margin: number,
  cellRadius: number,
  xOffset: number,
  yOffset: number
) {
  // outer edge
  ctx.beginPath()
  ctx.moveTo(
    xOffset + 11 * cellRadius + margin,
    yOffset + 0 * cellRadius + margin
  )
  ctx.arcTo(
    xOffset + 14 * cellRadius + margin,
    yOffset + 0 * cellRadius + margin,
    xOffset + 14 * cellRadius + margin,
    yOffset + 3 * cellRadius + margin,
    3 * cellRadius
  )

  ctx.arcTo(
    xOffset + 14 * cellRadius + margin,
    yOffset + 14 * cellRadius + margin,
    xOffset + 12 * cellRadius + margin,
    yOffset + 14 * cellRadius + margin,
    3 * cellRadius
  )

  ctx.arcTo(
    xOffset + 0 * cellRadius + margin,
    yOffset + 14 * cellRadius + margin,
    xOffset + 0 * cellRadius + margin,
    yOffset + 11 * cellRadius + margin,
    3 * cellRadius
  )

  ctx.arcTo(
    xOffset + 0 * cellRadius + margin,
    yOffset + 0 * cellRadius + margin,
    xOffset + 3 * cellRadius + margin,
    yOffset + 0 * cellRadius + margin,
    3 * cellRadius
  )
  ctx.lineTo(
    xOffset + 11 * cellRadius + margin,
    yOffset + 0 * cellRadius + margin
  )

  // inner edge
  ctx.moveTo(
    xOffset + 4 * cellRadius + margin,
    yOffset + 2 * cellRadius + margin
  )
  ctx.arcTo(
    xOffset + 2 * cellRadius + margin,
    yOffset + 2 * cellRadius + margin,
    xOffset + 2 * cellRadius + margin,
    yOffset + 4 * cellRadius + margin,
    2 * cellRadius
  )

  ctx.arcTo(
    xOffset + 2 * cellRadius + margin,
    yOffset + 12 * cellRadius + margin,
    xOffset + 4 * cellRadius + margin,
    yOffset + 12 * cellRadius + margin,
    2 * cellRadius
  )

  ctx.arcTo(
    xOffset + 12 * cellRadius + margin,
    yOffset + 12 * cellRadius + margin,
    xOffset + 12 * cellRadius + margin,
    yOffset + 10 * cellRadius + margin,
    2 * cellRadius
  )

  ctx.arcTo(
    xOffset + 12 * cellRadius + margin,
    yOffset + 2 * cellRadius + margin,
    xOffset + 10 * cellRadius + margin,
    yOffset + 2 * cellRadius + margin,
    2 * cellRadius
  )
  ctx.lineTo(
    xOffset + 4 * cellRadius + margin,
    yOffset + 2 * cellRadius + margin
  )

  ctx.closePath()
  ctx.fill()

  // central rect
  ctx.beginPath()
  ctx.moveTo(
    xOffset + 8 * cellRadius + margin,
    yOffset + 4 * cellRadius + margin
  )
  ctx.arcTo(
    xOffset + 10 * cellRadius + margin,
    yOffset + 4 * cellRadius + margin,
    xOffset + 10 * cellRadius + margin,
    yOffset + 6 * cellRadius + margin,
    cellRadius
  )

  ctx.arcTo(
    xOffset + 10 * cellRadius + margin,
    yOffset + 10 * cellRadius + margin,
    xOffset + 8 * cellRadius + margin,
    yOffset + 10 * cellRadius + margin,
    cellRadius
  )

  ctx.arcTo(
    xOffset + 4 * cellRadius + margin,
    yOffset + 10 * cellRadius + margin,
    xOffset + 4 * cellRadius + margin,
    yOffset + 8 * cellRadius + margin,
    cellRadius
  )

  ctx.arcTo(
    xOffset + 4 * cellRadius + margin,
    yOffset + 4 * cellRadius + margin,
    xOffset + 6 * cellRadius + margin,
    yOffset + 4 * cellRadius + margin,
    cellRadius
  )
  ctx.lineTo(
    xOffset + 8 * cellRadius + margin,
    yOffset + 4 * cellRadius + margin
  )
  ctx.closePath()
  ctx.fill()
}

function drawIcon(
  ctx: CanvasRenderingContext2D,
  iconWidth: number,
  width: number,
  margin: number
) {
  const xOffset = width - margin - iconWidth
  const yOffset = width - margin - iconWidth

  // Apple design guidelines state that corner radius is 80px for a 512px icon
  const cornerRadius = (iconWidth * 80) / 512
  const edgeLength = iconWidth - cornerRadius

  const icon = new Image()
  icon.src =
    'data:image/png;base64,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'
  return new Promise((resolve) => {
    icon.onload = function() {
      ctx.save()
      ctx.beginPath()

      ctx.moveTo(xOffset + edgeLength, yOffset)
      ctx.arcTo(
        xOffset + edgeLength + cornerRadius,
        yOffset,
        xOffset + edgeLength + cornerRadius,
        yOffset + cornerRadius,
        cornerRadius
      )

      ctx.arcTo(
        xOffset + edgeLength + cornerRadius,
        yOffset + edgeLength + cornerRadius,
        xOffset + edgeLength,
        yOffset + edgeLength + cornerRadius,
        cornerRadius
      )

      ctx.arcTo(
        xOffset,
        yOffset + edgeLength + cornerRadius,
        xOffset,
        yOffset + edgeLength,
        cornerRadius
      )

      ctx.arcTo(xOffset, yOffset, xOffset + cornerRadius, yOffset, cornerRadius)
      ctx.lineTo(xOffset + edgeLength, yOffset)
      ctx.closePath()

      ctx.clip()
      ctx.drawImage(icon, xOffset, yOffset, iconWidth, iconWidth)

      ctx.strokeStyle = '#767676'
      ctx.lineWidth = 0.5
      ctx.stroke()

      ctx.restore()
      resolve(undefined)
    }
  })
}

function drawLogo(
  ctx: CanvasRenderingContext2D,
  logoWidth: number,
  width: number
) {
  ctx.save()
  ctx.beginPath()
  ctx.arc(width / 2, width / 2, logoWidth / 2, 0, 2 * Math.PI)
  ctx.closePath()
  ctx.clip()

  const img = new Image()

  img.src =
    'data:image/png;base64,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'

  return new Promise((resolve) => {
    img.onload = () => {
      ctx.drawImage(
        img,
        (width - logoWidth) / 2,
        (width - logoWidth) / 2,
        logoWidth,
        logoWidth
      )
      // match PM4B app
      ctx.strokeStyle = '#e9e9e9'
      ctx.lineWidth = width * 0.015
      ctx.stroke()
      ctx.restore()
      resolve(undefined)
    }
  })
}

/**
 * @description 获取密度建议
 * @returns {number}
 */
const suggestTypeNumber = (codeUrl: string) => {
  const length = codeUrl.length
  if (length <= 32) {
    return 3
  } else if (length <= 46) {
    return 4
  } else if (length <= 60) {
    return 5
  } else if (length <= 74) {
    return 6
  } else if (length <= 86) {
    return 7
  } else if (length <= 108) {
    return 8
  } else if (length <= 130) {
    return 9
  } else if (length <= 151) {
    return 10
  } else if (length <= 177) {
    return 11
  } else if (length <= 203) {
    return 12
  } else if (length <= 241) {
    return 13
  } else if (length <= 258) {
    return 14
  } else if (length <= 292) {
    return 15
  } else {
    return 40
  }
}
