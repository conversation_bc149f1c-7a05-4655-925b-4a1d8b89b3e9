<template>
  <div class="qr-code">
    <div ref="container" class="qr-code-img" />
    <klk-loading v-if="value" overlay-color="rgba(255,255,255,0.88)" show-overlay />
  </div>
</template>

<script>
import KlkLoading from '@klook/klook-ui/lib/loading'
import genQrcode from './gen-qrcode'
import '@klook/klook-ui/lib/styles/components/loading.scss'

export default {
  components: { KlkLoading },
  props: {
    value: <PERSON>olean,
    actionData: {
      type: Object,
      default: () => ({})
    }
  },
  computed: {
    qrCodeData() {
      const { code_data, gateway_name } = this.actionData
      if (gateway_name === 'PayNow') {
        return 'data:image/png;base64,' + code_data
      }
      try {
        return window.atob(code_data)
      } catch {
        return ''
      }
    }
  },
  async mounted() {
    const { type } = this.actionData
    try {
      if (!this.qrCodeData) {
        throw new Error('no code_data')
      }
      await genQrcode(type, this.$refs.container, this.qrCodeData)
      this.$emit('input', false)
    } catch (error) {
      this.$emit('failed', {
        action: 3,
        error_msg: error?.message || '加载/生成二维码失败',
        file_name: 'at <payment-sdk/core/qr-pay/qr-code/index.vue>: genQrcode',
        message: `desktop订单${this.actionData.order_no} 扫码支付失败，原因是加载${type}类型二维码失败，qrCodeData为${this.qrCodeData}`
      })
    }
  }
}
</script>
