@import "node_modules/@klook/klook-ui/lib/styles/token/index.scss";

.payment-qr-pay {
  @include font-body-s-regular;
  text-align: center;
  padding: 16px;

  .mt6 {
    margin-top: 6px;
  }

  .mt12 {
    margin-top: 12px;
  }

  .font-body-m-regular {
    @include font-body-m-regular;
  }

  .font-body-m-bold {
    @include font-body-m-bold;
  }

  .font-body-l-regular {
    @include font-body-l-regular;
  }

  .font-body-l-bold {
    @include font-body-l-bold;
  }

  .font-heading-s {
    @include font-heading-s;;
  }

  .color-text-disabled {
    color: $color-text-disabled;
  }

  .color-text-secondary {
    color: $color-text-secondary;
  }

  .logo-img {
    height: 42px;
    display: block;
    margin-bottom: 16px;
  }

  .payment-alert {
    padding: 12px 20px;
    background-color: $color-caution-background;
    border: 1px solid rgba(255, 157, 38, 0.3);
    border-radius: $radius-s;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;

    .tips-icon {
      color: $color-caution;
      margin-right: 8px;
      vertical-align: text-bottom;
    }
  }


  .qr-code-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 12px 0;

    .qr-code-invalid {
      width: 264px;
      height: 264px;
      overflow: hidden;
      display: flex;
      justify-content: space-around;
      align-items: center;
      flex-direction: column;
      background-color: $color-bg-widget-darker-3;
      border-radius: $radius-m;
    }
    
    .qr-code {
      position: relative;
      padding: 12px;
      box-sizing: content-box;
      border: 1px solid $color-border-dim;
      border-radius: $radius-m;

      .qr-code-img {
        width: 240px;
        height: 240px;
        display: flex;
        align-items: center;
        justify-content: center;
         > img {
          width: 100%;
          height: 100%;
          object-fit: cover;
         }

         > canvas {
          position: relative;
          z-index: 3;
         }
      }
    }
  }

  .payment-qr-wechatpay {
    .i-icon-icon-scan {
      vertical-align: text-bottom;
      margin-right: 4px;
    }
    .scan-guid {
      position: relative;
      top: -25px;
      left: 25px;
      box-sizing: content-box;
      width: 150px;
      height: 150px;
      padding: 50px;
      background:#fcfcfc;
      border: 1px solid $color-border-dim;
      border-radius: 50%;
      
      &:before {
          position: absolute;
          content: "";
          bottom: 80px;
          left: -6px;
          width: 18px;
          height: 25px;
          background:#fcfcfc;
          border-width: 1px 1px 0 0;
          border-style: solid;
          border-color: #e0e0e0;
          transform: skew(-30deg) rotate(173deg);
      }

      .guid-list {
          text-align: left;
          border-left: 1px dashed #d1d1d1;
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          height: 100%;
          .guid-item {
            color: #888;
            font-size: 16px;
            display: flex;
            align-items: center;
            background:#fcfcfc;
            margin-left: -18px;

            img {
              margin-right: 12px;
            }
          }
      }
    }
  }

  .payment-qr-alipay {
    iframe {
      width: 200px;
      height: 200px;
      border: 0;
    }

    .qrcode-info {
      border-radius: $radius-l;
      overflow: hidden;
      &-item {
        display: flex;
        align-items: center;
        padding: 24px 30px;
        width: 320px;

        img, .i-icon {
          margin-right: 12px;
        }

        &:first-child {
          background-color: #b4cad5;
        }

        &:last-child {
          background-color: #c6e2fb;
        }
      }
    }
  }

  .payment-qr-common {
    .payment-steps-wrapper {
      padding: 20px 40px;
      margin-top: 20px;
      background-color: $color-bg-widget-darker-1;
      border-radius: $radius-none $radius-none $radius-m $radius-m;

      .payment-steps {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .payment-step {
          align-items: center;
          color: $color-text-secondary;
          font-weight: $fontWeight-regular;
          display: flex;
          justify-content: space-between;
          max-width: 31%;
    
          > img {
            width: 80px;
            height: 80px;
            margin-left: 16px;
            object-fit: cover;
          }
    
          &:not(:first-child)::before {
            content: '';
            width: 0;
            height: 0;
            border-top: 8px solid transparent;
            border-left: 14px solid $color-icon-disabled;
            border-bottom: 8px solid transparent;
            margin-right: 30px;
          }
        }
      }
    }
  }
}