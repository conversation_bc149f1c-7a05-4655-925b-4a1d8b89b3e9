<template>
  <div class="payment-qr-common">
    <div class="qr-code-wrapper">
      <slot />
    </div>
    <p v-if="actionData.gateway_name" class="font-body-l-bold mt6">
      {{ geAppTips('44859') }}
    </p>
    <p class="color-text-secondary mt6">{{ $t('44860') }}</p>
    <slot name="complete-tips" />
    <div v-if="actionData.gateway_name && steps.length" class="payment-steps-wrapper">
      <p class="font-body-m-bold">
        {{ geAppTips('44864') }}
      </p>
      <ul class="payment-steps color-text-secondary mt6">
        <li
          v-for="step in steps"
          :key="step.tip"
          class="payment-step"
        >
          <p>{{ step.tip }}</p>
          <img
            v-if="step.icon_url"
            :src="step.icon_url"
            class="process-step-item-img"
          />
        </li>
      </ul>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    actionData: {
      type: Object,
      default: () => ({})
    }
  },
  computed: {
    steps() {
      return Array.isArray(this.actionData.step_icons)
        ? [
          this.geAppTips('44865'),
          this.$t('44866'),
          this.$t('44867')
        ].map((tip, index) => ({
          tip,
          icon_url: this.actionData.step_icons[index]
        }))
        : []
    }
  },
  methods: {
    geAppTips(textId) {
      return this.$t(textId, { app: ' ' + this.actionData.gateway_name + ' ' })
    }
  }
}
</script>
