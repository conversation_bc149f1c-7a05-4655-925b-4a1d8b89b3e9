import Vue from 'vue'
import { camelCase } from 'lodash'
import { cashierQuery } from '../../../assets/apis'

export const gatewayList = [
  'wechatpay',
  'yunshanfucashier_unionpay',
  'wechatpay_ntt_data',
  'alipayhk',
  'paynow_paynow',
  'payme_hsbc',
  'kakaopaycashier_antfin',
  'alipayhkcashier_antfin',
  'gcashcashier_antfin',
  'tngcashier_antfin',
  'jkopay_jkopay'
]

export const isSupportDesktopQrPay = (gateway: string) => gatewayList.includes(gateway)

export async function qrPay(
  this: Vue & { $t: Function },
  submitResult: Record<string, any>,
  listenersMap: {
    onSuccess?: Function,
    onCancel?: Function,
    // runtimeError 为null 是无法让用户看到的错误原因，sentryError 是真实的错误原因
    onError?: (runtimeError: null | Error, sentryError: object) => void,
    onQuery?: (url: string, params: object) => Promise<{ success: boolean, result: any, error: Error & { code?: string } }>,
    onLogQuery?: (details: object) => void,
    onEnterBookingPage?: Function,
  } = {}
) {
  const ctx = this
  if (!(ctx instanceof Vue) || typeof ctx.$t !== 'function') {
    throw new TypeError('ctx or this must be instanceof Vue')
  }

  if (!submitResult?.action?.action_details?.redirect?.data) {
    // eslint-disable-next-line no-unused-expressions
    return listenersMap.onError?.(new Error(ctx.$t('173929')), {
      module: 'qr-pay',
      action: 'ui',
      path: 'at <payment-sdk/core/qr-pay/qr-pay.ts>: qrPay',
      message: '获取后端submit接口返回的submitResult.action.action_details.redirect.data失败'
    })
  }

  const { default: QrPayDialog } = await import('./dialog.vue')

  let dialogInstance: any = new (Vue.extend(QrPayDialog))({
    parent: ctx,
    propsData: { submitResult }
  }).$mount()

  const params = { invoice_guid: submitResult.invoice_guid }
  let pollingTimer: NodeJS.Timeout
  const pollingQuery = () => listenersMap.onQuery?.call(ctx, cashierQuery, params)
    .then((resp) => {
      if (resp?.success && resp.result) {
        const result = resp.result
        if (['Completed', 'Pending'].includes(result.invoice_status)) {
          // eslint-disable-next-line no-unused-expressions
          dialogInstance.$toast?.(dialogInstance.$t('global.pay.wait.tip'))
          dialogInstance.$emit('success')
        } else if (result.invoice_status === 'Failed') {
          dialogInstance.onQrCodeInvalid({
            action: 2,
            error_msg: 'Payment Failed',
            file_name: 'at <payment-sdk/core/qr-pay/qr-pay.ts>: pollingQuery',
            message: `desktop订单${submitResult.order_no}轮询${cashierQuery}接口结果为Failed`
          })
        } else if (!result.check_payment) {
          dialogInstance.onQrCodeInvalid({
            action: 2,
            error_msg: 'Payment Failed',
            file_name: 'at <payment-sdk/core/qr-pay/qr-pay.ts>: pollingQuery',
            message: `desktop订单${submitResult.order_no}轮询${cashierQuery}接口未返回Result.check_payment`
          })
        } else {
          pollingTimer = setTimeout(pollingQuery, result.check_interval || 2000)
        }
      } else {
        throw resp && resp.error
      }
    }).catch((error) => {
      dialogInstance.$emit('error', error, {
        module: 'cashier-query',
        action: 'api',
        code: error?.code,
        path: 'at <payment-sdk/core/qr-pay/qr-pay.ts>: pollingQuery',
        message: error?.message
      })
    })

  dialogInstance.$once('qr-code-invalid', (errorDetail: object) => {
    clearTimeout(pollingTimer)
    // eslint-disable-next-line no-unused-expressions
    listenersMap.onLogQuery?.call(ctx, errorDetail)
  })

  ;['success', 'cancel', 'error', 'enter-booking-page'].forEach((listener, _index, listenerList) => {
    dialogInstance.$once(listener, function () {
      clearTimeout(pollingTimer)
      // eslint-disable-next-line no-unused-expressions
      listenersMap[camelCase('on-' + listener) as 'onCancel']?.apply(ctx, arguments)
      listenerList.forEach(listener => dialogInstance.$off(listener))
      dialogInstance.visible = false

      setTimeout(() => {
        document.body.removeChild(dialogInstance.$el)
        dialogInstance.$destroy()
        dialogInstance = null
      }, 300)
    })
  })

  pollingTimer = setTimeout(pollingQuery, 2000)
  document.body.appendChild(dialogInstance.$el)
  dialogInstance.visible = true
}
