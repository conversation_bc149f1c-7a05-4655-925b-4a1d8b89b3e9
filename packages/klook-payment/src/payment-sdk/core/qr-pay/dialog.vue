<template>
  <klk-modal
    v-if="actionData"
    :open="visible"
    closable
    :width="1160"
    :overlay-closable="false"
    :show-default-footer="false"
    :z-index="4000"
    @on-close="$emit('cancel')"
  >
    <div class="payment-qr-pay">
      <img
        class="logo-img"
        height="42"
        :src="actionData.app_logo"
      />
      <div v-if="actionData.code_tips && actionData.code_tips.length" class="payment-alert mt12">
        <icon-caution-circle
          type="icon_feedback_warning_fill"
          :size="16"
          class="tips-icon"
        />
        {{ actionData.code_tips.join('') }}
      </div>
      <p class="font-body-l-regular mt12">
        {{ $t('44857') }}
      </p>
      <p class="font-heading-s">{{ priceFormatAmount }}</p>
      <p v-if="isCodeInvalid" class="color-text-disabled">{{ $t('44869') }}</p>
      <count-down
        v-else
        slot="count-down"
        :type="2"
        time-class="font-body-m-regular"
        :end-time="+actionData.expiry_timestamp"
        :end-call-back="onQrCodeInvalid"
      >
        <template #default="{ countDownTime }">
          {{ $t('pay.qrcode_validity') }}
          {{ [countDownTime.h, countDownTime.m, countDownTime.s].join(':') }}
        </template>
      </count-down>
      <component
        :is="qrPayType"
        :action-data="actionData"
      >
        <div v-if="isCodeInvalid" class="qr-code-invalid">
          <icon-caution-circle
            type="icon_feedback_warning_fill"
            size="60"
            class="color-text-disabled"
          />
          <klk-button type="outlined" @click="$emit('cancel')">
            {{ $t('44868') }}
          </klk-button>
        </div>
        <qr-code v-else v-model="loading" :action-data="actionData" @failed="onQrCodeInvalid" />
        <p v-if="!loading" slot="complete-tips" class="color-text-secondary">
          {{ $t('44861') }}
          <klk-link class="issue-line" @click="$emit('enter-booking-page')">
            {{ $t('44862') }}
          </klk-link>
          {{ $t('44863') }}
        </p>
      </component>
    </div>
  </klk-modal>
</template>

<script>
import KlkModal from '@klook/klook-ui/lib/modal'
import KlkLink from '@klook/klook-ui/lib/link'
import KlkButton from '@klook/klook-ui/lib/button'
import CountDown from '@klook/platform-countdown'
import IconNext from '@klook/klook-icons/lib/IconNext'
import IconCautionCircle from '@klook/klook-icons/lib/IconCautionCircle'
import '@klook/klook-ui/lib/styles/components/modal.scss'
import '@klook/klook-ui/lib/styles/components/loading.scss'
import '@klook/klook-ui/lib/styles/components/link.scss'
import '@klook/klook-ui/lib/styles/components/button.scss'
import '@klook/klook-icons/styles/index.css'

import { formatAmount } from '../../../utils/get-payment-static-data'
import Alipay from './payment-gateway/alipay.vue'
import QrCode from './qr-code/index.vue'
import Paynow from './payment-gateway/paynow.vue'
import Wechatpay from './payment-gateway/wechatpay.vue'
import Common from './payment-gateway/common.vue'

import './qr-pay.scss'

export default {
  name: 'QrCodeDialog',
  components: {
    QrCode,
    KlkButton,
    KlkModal,
    CountDown,
    IconNext,
    IconCautionCircle,
    KlkLink,
    Alipay,
    Paynow,
    Wechatpay,
    Common
  },
  props: {
    submitResult: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      actionData: this.submitResult.action.action_details.redirect.data,
      isCodeInvalid: false,
      visible: false,
      loading: true
    }
  },
  computed: {
    qrPayType() {
      return {
        wechatpay_ntt_data: 'wechatpay',
        wechatpay: 'wechatpay',
        alipayhk: 'alipay',
        paynow_paynow: 'paynow'
      }[this.submitResult.payment_gateway] || 'common'
    },
    priceFormatAmount() {
      const { user_total_pay_price, user_pay_currency } = this.submitResult
      return formatAmount(user_pay_currency, user_total_pay_price)
    }
  },

  methods: {
    onQrCodeInvalid(errorDetail) {
      this.isCodeInvalid = true
      this.$emit('qr-code-invalid', errorDetail || {
        action: 5,
        error_msg: 'timeout',
        file_name: 'at <qr-pay/dialog.vue>: countDown-endCallBack',
        message: `desktop订单${this.submitResult.order_no}支付失败，原因是超时未扫码支付`
      })
    }
  }
}
</script>
