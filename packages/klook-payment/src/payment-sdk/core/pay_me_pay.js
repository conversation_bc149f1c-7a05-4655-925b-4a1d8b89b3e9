import { offbeforeunload } from './utils'

/**
 * @description payMe支付
 * @param dataInfo
 */
export const payMePay = (dataInfo, ctx) => {
  offbeforeunload()
  const href = dataInfo.action.action_details.redirect.href
  // 如果存在code_url标识符，为PC端
  if (href.includes('code_url')) {
    const { user_pay_currency, user_total_pay_price } = dataInfo
    // 二维码有效期为10分钟
    const deadline = Date.now() + 600000
    window.location.href = `${href}&deadline=${deadline}&currency=${user_pay_currency}&amount=${user_total_pay_price}`
  } else {
    location.href = href
  }
  ctx.$emit('success')
}
