/* eslint-disable */
// import { Dialog } from '@klook/klook-ui/lib/modal'
import {
  enterExecuteSuccessPage, execute, asyncLoadScript,
  // enterLoginPage,
  // enterBookingPage,
  offbeforeunload
} from './utils'

const gcashIn = (result, ctx) => {
  const reqData = {
    modulePath: 'at <gcash.js>: gcashIn',
    payment_gateway: result.payment_gateway,
    invoice_guid: result.invoice_guid,
    invoice_submission_guid: result.invoice_submission_guid,
    payment_details: {},
    order_no: result.order_no,
    return_url: result.return_url,
    resultCode: result.resultCode
  }
  execute(ctx, reqData, (result) => {
    offbeforeunload()
    ctx.$emit('success')
    enterExecuteSuccessPage(result.order_no, reqData.return_url.success)
  })
  /*, (errorObj) => {

     Dialog.alert(errorObj.message, ctx.$t('global.tips.header'), {
      okLabel: ctx.$t('global.tips.okTxt'),
      zIndex: 4000
    }).then(() => {
      if (errorObj.code === '4001') {
      offbeforeunload()
      enterLoginPage(reqData.return_url.success)
      } else {
        offbeforeunload()
        enterBookingPage(reqData.return_url.success)
      }
    })
  } */
}

export const gcash = (submitResult, ctx) => {
  asyncLoadScript('https://appx/web-view.min.js', () => {
    my.tradePay({
      paymentUrl: submitResult.action.action_details.native && submitResult.action.action_details.native.href,
      success: (res) => {
        gcashIn({ ...submitResult, ...res }, ctx)
      },
      fail: (res) => {
        gcashIn(submitResult, ctx)
      }
    })
  })
}
