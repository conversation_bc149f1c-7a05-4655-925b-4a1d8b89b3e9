import { offbeforeunload, getLangPath } from './utils'

export const payNowPay = (submitResult, ctx) => {
  const queryData = submitResult.action.action_details.redirect.data
  const qrCode = `data:image/png;base64,${queryData.code_data}`
  localStorage.setItem('payNowCode', qrCode)
  localStorage.setItem('payNowCodeTips', JSON.stringify(queryData.code_tips))
  localStorage.setItem('payNowCodeName', queryData.activity_name)
  localStorage.setItem('payNowCodeDescription', queryData.activity_description_suffix)
  let redirectUrl = `${location.protocol}//${location.host}${getLangPath.call(ctx)}web3/order-payment/paynow?`
  redirectUrl += `amount=${queryData.amount}&currency=${queryData.currency}&order_no=${queryData.order_no}&invoice_guid=${submitResult.invoice_guid}&end_time=${queryData.expiry_timestamp}`
  offbeforeunload()
  window.location.href = redirectUrl
  ctx.$emit('success')
}
