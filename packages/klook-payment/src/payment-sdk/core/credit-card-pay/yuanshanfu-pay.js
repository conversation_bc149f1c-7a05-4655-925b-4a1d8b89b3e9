import { offbeforeunload } from '../utils'

export const yunshanfuDatapay = (submitResult, ctx) => {
  const nativeData = submitResult.action.action_details.native

  let yunshanfuForm = ''
  for (const key in nativeData.data) {
    yunshanfuForm += `<input type="hidden" id="${key}" name="${key}" value="${nativeData.data[key]}">`
  }

  const yunshanfuFormElements = document.querySelectorAll('#yunshanfuForm')
  if (!yunshanfuFormElements.length) {
    const form = document.createElement('form')
    form.id = 'yunshanfuForm'
    form.method = 'post'
    form.innerHTML = yunshanfuForm
    document.body.appendChild(form)
  }

  offbeforeunload()
  const form = document.getElementById('yunshanfuForm')
  form.innerHTML = yunshanfuForm
  form.setAttribute('action', nativeData.href)
  ctx.$emit('success')
  form.submit()
}
