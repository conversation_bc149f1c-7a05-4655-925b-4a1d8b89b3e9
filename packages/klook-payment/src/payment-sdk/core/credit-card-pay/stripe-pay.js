import assign from 'lodash/assign'
import { enterExecuteSuccessPage, execute, isCreatedStatus, offbeforeunload, getExecuteBaseData } from '../utils'
import { getErrorInfo } from '../utils/logQuery'

const getExecuteData = (data, response) => {
  const creditCardInfo = data.credit_card_info
  return assign(getExecuteBaseData(data), {
    modulePath: 'at <stripe-pay.js>: stripePayIn',
    payment_details: {
      token: data.token || response.id,
      save_card: creditCardInfo ? creditCardInfo.save_card : false,
      is_new: !!creditCardInfo,
      bin: creditCardInfo ? creditCardInfo.bin : '',
      issuer_bin: creditCardInfo ? creditCardInfo.issuer_bin : '',
      last4: creditCardInfo ? creditCardInfo.last4 : '',
      kvalue: creditCardInfo ? creditCardInfo.kvalue : ''
    }
  })
}

const stripePayIn = (ctx, result) => {
  let reqData

  try {
    reqData = getExecuteData(result)
  } catch (error) {
    return this.$emit('error', null, {
      module: 'stripe-pay',
      action: 'ui',
      path: 'at <stripe-pay.js>: stripePayIn-getExecuteData',
      message: '错误信息：getExecuteData数据合并时发生异常，中断了进程。 errorMsg：' + getErrorInfo(error) + '。submitResult：' + JSON.stringify(result)
    })
  }
  execute(ctx, reqData, (executeResult) => {
    ctx.$emit('success')
    offbeforeunload()
    if (isCreatedStatus(executeResult.invoice_status)) {
      window.location.href = executeResult.gateway_extra_info.creditcard_stripe.issuer_url
    } else {
      enterExecuteSuccessPage(executeResult.order_no, result.return_url && result.return_url.success)
    }
  })
}

export const stripePay = (submitResult, ctx) => {
  if (submitResult.token) {
    // old card
    stripePayIn(ctx, submitResult)
  } else {
    // new card
    ctx.$emit('error', null, {
      module: 'stripe-pay',
      action: 'ui',
      path: 'at <stripe-pay.js>: stripePay',
      message: '新卡不支持stripePay, stripe_token_not_exist'
    })
  }
}
