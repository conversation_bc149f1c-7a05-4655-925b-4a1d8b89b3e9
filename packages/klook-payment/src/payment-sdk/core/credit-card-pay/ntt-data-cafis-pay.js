/* eslint-disable */

import assign from 'lodash/assign'
import { getLangPath, isMobile, execute, offbeforeunload, getExecuteBaseData, getMiddleRedirectUrlPage, isCreatedStatus, enterExecuteSuccessPage, parseUrlQuery } from '../utils'
import { getErrorInfo } from '../utils/logQuery'

let reqData = {}
let __ctx = null

const getExecuteData = (data) => {
  const creditCardInfo = data.credit_card_info
  return assign(getExecuteBaseData(data), {
    payment_details: {
      token: data.token,
      save_card: creditCardInfo ? creditCardInfo.save_card : false,
      is_new: !!creditCardInfo,
      bin: creditCardInfo ? creditCardInfo.bin : '',
      issuer_bin: creditCardInfo ? creditCardInfo.issuer_bin : '',
      last4: creditCardInfo ? creditCardInfo.last4 : '',
      kvalue: creditCardInfo ? creditCardInfo.kvalue : ''
    }
  })
}

// 创建iframe， 生成短信输入页面
const createAuthIframe = (action, ctx) => {
  hideFrames()
  document.documentElement.classList.add('no_scroll')

  const IFRAME_NAME = 'authenticateFrame'

  const container = document.getElementById('authenticate3dsSimpleRedirect')
  container.style.display = 'block'

  const ISMOBILE = isMobile.call(ctx)

  // TODO 需要实时获取 因为hotel 是响应式
  if (ISMOBILE) {
    const loadingBox = container.querySelector('.loading_box')
    loadingBox.style.transform = 'translate(-50%,-50%)'
    loadingBox.style.position = 'fixed'
    loadingBox.style.top = '50%'
    loadingBox.style.left = '50%'
    container.style.backgroundColor = '#ccc'
  }
  createIframe(container, IFRAME_NAME, '100%', '100%')

  const searchString = '?' + action.split('?')[1]
  const searchData = parseUrlQuery(searchString, true)
  const authenticateFrameForm = document.forms.authenticate3dsSimpleRedirectForm
  authenticateFrameForm.action = action.split('?')[0]
  authenticateFrameForm.method = 'GET'
  for (const key in searchData) {
    const input = document.createElement('input')
    input.name = key
    input.value = searchData[key]
    input.type = 'hidden'
    authenticateFrameForm.appendChild(input)
  }
  offbeforeunload()
  authenticateFrameForm.submit()
  while (authenticateFrameForm.firstChild) {
    authenticateFrameForm.removeChild(authenticateFrameForm.firstChild)
  }
}

const createIframe = (container, name, width = '0', height = '0', callback) => {
  if (!name || name.length === 0) {
    throw new Error('Name parameter missing for iframe')
  }

  const iframe = document.createElement('iframe')

  iframe.width = width
  iframe.height = height
  iframe.name = name
  iframe.id = name
  iframe.setAttribute('frameborder', '0')
  iframe.setAttribute('border', '0')

  const noIframeElContent = document.createTextNode('<p>Your browser does not support iframes.</p>')

  iframe.appendChild(noIframeElContent)

  container.append(iframe)

  if (iframe.attachEvent) {
    // IE fallback
    iframe.attachEvent('onload', function () {
      if (callback && typeof callback === 'function') {
        callback(iframe.contentWindow)
      }
    })
  } else {
    iframe.onload = function () {
      if (callback && typeof callback === 'function') {
        callback(iframe.contentWindow)
      }
    }
  }

  return iframe
}

const hideFrames = () => {
  const authenticateFrame = document.getElementById('authenticate3dsSimpleRedirect')
  authenticateFrame.style.display = 'none'

  document.documentElement.classList.remove('no_scroll')
}

const enterBookingPage = (returnUrl) => {
  offbeforeunload()
  if (returnUrl && returnUrl.includes('middle_redirect_url')) {
    window.location.href = getMiddleRedirectUrlPage(returnUrl, '2')
    return
  }
  window.location.href = getLangPath('bookings')
}

const formatMessage = (e) => {
  if (e.origin === `${location.protocol}//${location.host}`) {
    const eventData = e.data
    reqData.payment_details.creditcard_ntt_data = {
      additional_data: {
        cardAuthentication: Object.assign({}, eventData)
      }
    }

    execute(__ctx, { modulePath: 'at <ntt-data-cafis-pay.js>: formatMessage', ...reqData }, (result) => {
      __ctx.$emit('success')
        offbeforeunload()
      enterExecuteSuccessPage(result.order_no, reqData.return_url && reqData.return_url.success)
    }, hideFrames)
  }
}

export const nttDataCafisPay = (submitResult, ctx) => {
  __ctx = ctx
  const authenticate3dsContainer = document.querySelector('#authenticate3dsSimpleRedirect')
  const authenticateCloseIcon = authenticate3dsContainer.querySelector('.klook-icon-close')
  authenticateCloseIcon.addEventListener('click', () => {
    enterBookingPage(submitResult.return_url)
  })

  try {
    reqData = getExecuteData(submitResult)
  }  catch (err) {
    // 一般不会走到这里
    hideFrames()
    return ctx.$emit('error', null, {
      module: 'ntt-data-cafis-pay',
      action: 'ui',
      path: 'at <ntt-data-cafis-pay.js>: nttDataCafisPay-getExecuteData',
      message: '错误信息：getExecuteData数据合并时发生异常，中断了进程。 errorMsg：' + getErrorInfo(err)  + '。submitResult：' + JSON.stringify(submitResult || {})
    })
  }

  execute(ctx, { modulePath: 'at <ntt-data-cafis-pay.js>: nttDataCafisPay', ...reqData}, (executeResult) => {
    if (isCreatedStatus(executeResult.invoice_status)) {
      try {
        const formUrl = executeResult.gateway_extra_info.creditcard_ntt_data['3dsForm'].url
        createAuthIframe(formUrl, ctx)
      } catch (error) {
        ctx.$emit('error', null, {
          module: 'ntt-data-cafis-pay',
          action: 'ui',
          path: 'at <ntt-data-cafis-pay.js>: nttDataCafisPay',
          message: 'nttDataCafisPay@execute 在成功回调内部报错,  errorMsg：' + getErrorInfo(error)  + '。executeResult：' + JSON.stringify(executeResult || {})
        })
        hideFrames()
      }
    } else {
      ctx.$emit('error', null, {
        module: 'ntt-data-cafis-pay',
        action: 'ui',
        path: 'at <ntt-data-cafis-pay.js>: nttDataCafisPay',
        message: 'nttDataCafisPay支付失败, 需要走3ds2 流程。executeResult：' + JSON.stringify(executeResult)
      })
      hideFrames()
    }
  })
  window.removeEventListener('message', formatMessage)
  window.addEventListener('message', formatMessage)
}
