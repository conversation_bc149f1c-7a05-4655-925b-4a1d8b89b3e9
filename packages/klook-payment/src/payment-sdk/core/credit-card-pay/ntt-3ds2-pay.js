import assign from 'lodash/assign'
import { execute, offbeforeunload, getExecuteBaseData, getMiddleRedirectUrlPage, getLangPath, isMobile } from '../utils'
import { getErrorInfo } from '../utils/logQuery'

let submitResp

export const ntt3ds2Pay = (submitResult, ctx) => {
  const initiate3dsContainer = document.querySelector('#initiate3dsSimpleRedirect')
  const initiateCloseIcon = initiate3dsContainer.querySelector('.klook-icon-close')
  initiateCloseIcon.addEventListener('click', () => {
    ctx.$emit('cancel', null, {
      module: 'ntt-3ds2-pay',
      action: 'ui',
      path: 'at <ntt-3ds2-pay.js>: initiateCloseIcon.click',
      message: '用户点击initiate3dsSimpleRedirect的关闭按钮取消ntt3ds2Pay支付'
    })
    offbeforeunload()
    window.location.reload()
  })

  const authenticate3dsContainer = document.querySelector('#authenticate3dsSimpleRedirect')
  const authenticateCloseIcon = authenticate3dsContainer.querySelector('.klook-icon-close')
  authenticateCloseIcon.addEventListener('click', () => {
    enterBookingPage(submitResult.return_url)
    ctx.$emit('cancel', null, {
      module: 'ntt-3ds2-pay',
      action: 'ui',
      path: 'at <ntt-3ds2-pay.js>: authenticateCloseIcon.click',
      message: '用户点击authenticate3dsSimpleRedirect的关闭按钮取消ntt3ds2Pay支付'
    })
  })

  try {
    firstExecute(submitResp = submitResult, ctx)
  } catch (error) {
    // 这里只能抓取error 状态
    ctx.$emit('error', null, {
      module: 'ntt-3ds2-pay',
      action: 'ui',
      path: 'at <ntt-3ds2-pay.js>: ntt3ds2Pay-firstExecute',
      message: 'firstExecute执行失败， errMsg：' + getErrorInfo(error) + '。submitResult：' + JSON.stringify(submitResult || {})
    })
  }
}

const enterBookingPage = (returnUrl) => {
  offbeforeunload()
  if (returnUrl && returnUrl.includes('middle_redirect_url')) {
    window.location.href = getMiddleRedirectUrlPage(returnUrl, '2')
    return
  }
  window.location.href = getLangPath('bookings')
}

const getExecuteData = (data, isSecondExecute, excResp) => {
  const creditCardInfo = data.credit_card_info
  let nttData = {
    userAgent: window.navigator.userAgent
  }
  if (isSecondExecute) {
    const extra = {
      subAction: 'authenticatePayer',
      nttrefid: excResp.gateway_extra_info.creditcard_ntt_data['3dsForm'].nttrefid
    }
    nttData = { ...nttData, ...extra }
  }
  return assign(getExecuteBaseData(data), {
    payment_details: {
      token: data.token,
      save_card: creditCardInfo ? creditCardInfo.save_card : false,
      is_new: !!creditCardInfo,
      bin: creditCardInfo ? creditCardInfo.bin : '',
      issuer_bin: creditCardInfo ? creditCardInfo.issuer_bin : '',
      last4: creditCardInfo ? creditCardInfo.last4 : '',
      kvalue: creditCardInfo ? creditCardInfo.kvalue : '',
      creditcard_ntt_data: nttData
    }
  })
}

// 第一次execute请求
const firstExecute = (data, ctx) => {
  let reqData
  try {
    reqData = { modulePath: 'at <ntt-3ds2-pay.js>: firstExecute', ...getExecuteData(data) }
  } catch (error) {
    hideFrames()
    ctx.$emit('error', null, {
      module: 'ntt-3ds2-pay',
      action: 'ui',
      path: 'at <ntt-3ds2-pay.js>: firstExecute-getExecuteData',
      message: 'firstExecute流程中getExecuteData失败，errMsg：' + getErrorInfo(error) + '。submitResult：' + JSON.stringify(data || {})
    })
  }
  execute(ctx, reqData, (executeResult) => {
    offbeforeunload()
    createInitiateIframe(executeResult, ctx)
  }, hideFrames)
}

const createIframe = (container, name, width = '0', height = '0', callback) => {
  if (!name || name.length === 0) {
    throw new Error('Name parameter missing for iframe')
  }

  const iframe = document.createElement('iframe')

  iframe.width = width
  iframe.height = height
  iframe.name = name
  iframe.id = name
  iframe.setAttribute('frameborder', '0')
  iframe.setAttribute('border', '0')

  const noIframeElContent = document.createTextNode('<p>Your browser does not support iframes.</p>')

  iframe.appendChild(noIframeElContent)

  container.append(iframe)

  if (iframe.attachEvent) {
    // IE fallback
    iframe.attachEvent('onload', function () {
      if (callback && typeof callback === 'function') {
        callback(iframe.contentWindow)
      }
    })
  } else {
    iframe.onload = function () {
      if (callback && typeof callback === 'function') {
        callback(iframe.contentWindow)
      }
    }
  }

  return iframe
}

// 创建iframe， 收集用户数据
const createInitiateIframe = (data, ctx) => {
  // eslint-disable-next-line prefer-const
  let { data: formData, url } = data?.gateway_extra_info?.creditcard_ntt_data?.['3dsForm'] || {}

  if (!url) {
    return ctx.$emit('error', null, {
      module: 'ntt-3ds2-pay',
      action: 'ui',
      path: 'at <ntt-3ds2-pay.js>: createInitiateIframe',
      message: '读取data.gateway_extra_info.creditcard_ntt_data.3dsForm.url 失败'
    })
  }
  document.documentElement.classList.add('no_scroll')
  const IFRAME_NAME = 'initiateFrame'

  const container = document.getElementById('initiate3dsSimpleRedirect')
  container.style.display = 'block'

  if (isMobile.call(ctx)) {
    const loadingBox = container.querySelector('.loading_box')
    loadingBox.style.transform = 'translate(-50%,-50%)'
    loadingBox.style.position = 'fixed'
    loadingBox.style.top = '50%'
    loadingBox.style.left = '50%'
  }
  const iframe = createIframe(container, IFRAME_NAME, '100%', '100%')
  iframe.style.display = 'none'

  const initiate3dsSimpleRedirectForm = document.forms.initiate3dsSimpleRedirectForm

  initiate3dsSimpleRedirectForm.action = url
  formData = formData || {}
  for (const key in formData) {
    const input = document.createElement('input')
    input.name = key
    input.value = formData[key]
    input.type = 'hidden'
    initiate3dsSimpleRedirectForm.appendChild(input)
  }
  offbeforeunload()
  initiate3dsSimpleRedirectForm.submit()

  // 延迟三秒，第二次execute请求
  setTimeout(() => {
    secondExecute(submitResp, data, ctx)
  }, 3000)
}

const secondExecute = (data, resp, ctx) => {
  let reqData
  try {
    reqData = { modulePath: 'at <ntt-3ds2-pay.js>: secondExecute', ...getExecuteData(data, true, resp) }
  } catch (error) {
    hideFrames()
    return ctx.$emit('error', null, {
      module: 'ntt-3ds2-pay',
      action: 'ui',
      path: 'at <ntt-3ds2-pay.js>: secondExecute-getExecuteData',
      message: 'secondExecute流程中getExecuteData失败，errMsg：' + getErrorInfo(error) + '。submitResult：' + JSON.stringify(data || {})
    })
  }
  execute(ctx, reqData, (executeResult) => {
    offbeforeunload()
    createAuthIframe(executeResult, ctx)
  }, hideFrames)
}

// 创建iframe， 生成密码短信输入页面
const createAuthIframe = (data, ctx) => {
  hideFrames()
  // eslint-disable-next-line prefer-const
  let { data: formData, url } = data?.gateway_extra_info?.creditcard_ntt_data?.['3dsForm'] || {}

  if (!url) {
    return ctx.$emit('error', null, {
      module: 'ntt-3ds2-pay',
      action: 'ui',
      path: 'at <ntt-3ds2-pay.js>: createAuthIframe',
      message: '读取data.gateway_extra_info?.creditcard_ntt_data.3dsForm.url 失败'
    })
  }
  document.documentElement.classList.add('no_scroll')

  const IFRAME_NAME = 'authenticateFrame'

  const container = document.getElementById('authenticate3dsSimpleRedirect')
  container.style.display = 'block'

  if (isMobile.call(ctx)) {
    const loadingBox = container.querySelector('.loading_box')
    loadingBox.style.transform = 'translate(-50%,-50%)'
    loadingBox.style.position = 'fixed'
    loadingBox.style.top = '50%'
    loadingBox.style.left = '50%'

    container.style.backgroundColor = '#ccc'
  }
  createIframe(container, IFRAME_NAME, '100%', '100%')

  const authenticate3dsSimpleRedirectForm = document.forms.authenticate3dsSimpleRedirectForm
  authenticate3dsSimpleRedirectForm.action = url
  const input = document.createElement('input')
  input.name = 'creq'
  input.value = (formData && formData.threeDSMethodData) || ''
  input.type = 'hidden'
  authenticate3dsSimpleRedirectForm.appendChild(input)
  offbeforeunload()

  authenticate3dsSimpleRedirectForm.submit()
}

const hideFrames = () => {
  const initiateFrame = document.getElementById('initiate3dsSimpleRedirect')
  initiateFrame.style.display = 'none'

  const authenticateFrame = document.getElementById('authenticate3dsSimpleRedirect')
  authenticateFrame.style.display = 'none'

  document.documentElement.classList.remove('no_scroll')
}
