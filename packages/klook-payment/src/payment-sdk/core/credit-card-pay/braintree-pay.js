/* eslint-disable */
import assign from 'lodash/assign'
import { offbeforeunload, enterExecuteSuccessPage, isCreatedStatus, execute, getExecuteBaseData } from '../utils'
import { getErrorInfo } from '../utils/logQuery'

const getExecuteData = (data) => {
  const creditCardInfo = data.credit_card_info
  return assign(getExecuteBaseData(data), {
    modulePath: 'at <braintree-pay.js>: braintreePayIn',
    payment_details: {
      token: data.token,
      save_card: creditCardInfo ? creditCardInfo.save_card : false,
      is_new: !!creditCardInfo,
      bin: creditCardInfo ? creditCardInfo.bin : '',
      issuer_bin: creditCardInfo ? creditCardInfo.issuer_bin : '',
      last4: creditCardInfo ? creditCardInfo.last4 : '',
      kvalue: creditCardInfo ? creditCardInfo.kvalue : ''
    }
  })
}

const braintree3dsPay = (client, data, nonce, ctx) => {
  client.verify3DS({
    amount: data.user_total_pay_price,
    creditCard: nonce,
    onUserClose() {
      ctx.$emit('cancel', null, {
        module: 'braintree-pay',
        action: 'ui',
        path: 'at <braintree-pay.js>: braintree3dsPay-onUserClose',
        message: '用户取消braintree3dsPay支付'
      })
    }
  }, function (error, response) {
    if (!error) {
      const nonce = response.nonce
      braintreePay(assign(data, {
        token: nonce
      }))
    } else {
      const errorType = typeof error
      const message = errorType === 'string'
        ? error
        : errorType === 'object' && error.message
          ? error.message
          : undefined

      ctx.$emit('error', message && new Error(message), {
        module: 'braintree-pay',
        action: 'ui',
        path: 'at <braintree-pay.js>: braintree3dsPay-client.verify3DS',
        error_type: 'sdk-error',
        message: 'braintree3dsPay 支付失败：' + message || 'NA'
      })
    }
  })
}

const braintreePayIn = (submitResult, ctx) => {
  let reqData
  try {
    reqData = getExecuteData(submitResult)
  } catch (error) {
    return ctx.$emit('error', null, {
      module: 'wallet_agreement',
      action: 'ui',
      path: 'at <wallet_agreement.js>: getExecuteBaseData',
      message: '错误信息：getExecuteData数据合并时发生异常， errorMsg：' + getErrorInfo(error) + '。submitResult：' + JSON.stringify(submitResult || {})
    })
  }
  execute(ctx, reqData, (executeResult) => {
    if (isCreatedStatus(executeResult.invoice_status)) {
      const nonce = executeResult.gateway_extra_info.creditcard_braintree.nonce
      const clientToken = executeResult.gateway_extra_info.creditcard_braintree.client_token
      const client = new braintree.api.Client({ clientToken })

      braintree3dsPay(client, submitResult, nonce, ctx)
    } else {
      ctx.$emit('success')
      offbeforeunload()
      enterExecuteSuccessPage(executeResult.order_no, submitResult.return_url && submitResult.return_url.success)
    }
  })
}

export const braintreePay = (submitResult, ctx) => {
  if (submitResult && submitResult.token) {
    braintreePayIn(submitResult, ctx)
  } else {
    ctx.$emit('error', null, {
      module: 'braintree-pay',
      action: 'ui',
      path: 'at <braintree-pay.js>: braintreePay',
      message: '数据异常缺失submitResult.token：' + JSON.stringify(submitResult)
    })
  }
}
