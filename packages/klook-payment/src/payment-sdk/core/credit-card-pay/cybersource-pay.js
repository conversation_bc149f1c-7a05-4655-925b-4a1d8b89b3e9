import assign from 'lodash/assign'
import { isPrdEnv, offbeforeunload, enterExecuteSuccessPage, isCreatedStatus, execute, getExecuteBaseData } from '../utils'
import { postCreditCardIframeMessage } from '../utils/pci'
import { getErrorInfo } from '../utils/logQuery'

const getExecuteData = (data, cybersourceInfo) => {
  const creditCardInfo = data.credit_card_info

  return assign(getExecuteBaseData(data), {
    modulePath: 'at <cybersource-pay.js>: cybersourcePay-signParams',
    payment_details: {
      token: data.token || creditCardInfo.bin,
      save_card: creditCardInfo ? creditCardInfo.save_card : false,
      is_new: !!creditCardInfo,
      bin: creditCardInfo ? creditCardInfo.bin : '',
      issuer_bin: creditCardInfo ? creditCardInfo.issuer_bin : '',
      last4: creditCardInfo ? creditCardInfo.last4 : '',
      kvalue: creditCardInfo ? creditCardInfo.kvalue : '',
      creditcard_cybersource: cybersourceInfo
    }
  })
}

const signParams = (submitResult, cybersourcePayFromData, cb, ctx) => {
  const signedFieldNames = cybersourcePayFromData.signed_field_names.split(',')
  const dataToSign = {}

  signedFieldNames.forEach(function (item) {
    dataToSign[item] = cybersourcePayFromData[item] || ''
  })

  execute(ctx, getExecuteData(submitResult, dataToSign), (executeResult) => {
    if (isCreatedStatus(executeResult.invoice_status)) {
      const gatewayExtraInfo = executeResult.gateway_extra_info
      const { reference_number, transaction_uuid, sign_result, signed_date_time, payment_token } = gatewayExtraInfo.creditcard_cybersource

      cybersourcePayFromData = Object.assign(cybersourcePayFromData, { reference_number, transaction_uuid, signature: sign_result, signed_date_time, payment_token })

      typeof cb === 'function' && cb()
    } else {
      ctx.$emit('success')
      offbeforeunload()
      enterExecuteSuccessPage(executeResult.order_no, submitResult.return_url && submitResult.return_url.success)
    }
  })
}

export const cybersourcePay = (submitResult, ctx) => {
  try {
    const native = submitResult.action.action_details.native
    const cybersourcePayFromData = { ...native }
    cybersourcePayFromData.transaction_uuid = submitResult.order_no
    cybersourcePayFromData.reference_number = submitResult.reference_number
    cybersourcePayFromData.bill_to_forename = submitResult.first_name
    cybersourcePayFromData.bill_to_surname = submitResult.family_name
    cybersourcePayFromData.bill_to_email = submitResult.traveller_email
    cybersourcePayFromData.amount = submitResult.user_total_pay_price
    cybersourcePayFromData.currency = submitResult.user_pay_currency
    cybersourcePayFromData.signed_field_names = 'access_key,profile_id,merchant_id,transaction_uuid,signed_field_names,unsigned_field_names,signed_date_time,locale,transaction_type,reference_number,amount,currency,bill_to_forename,bill_to_surname,bill_to_email,bill_to_address_line1,bill_to_address_city,bill_to_address_country,merchant_defined_data5'
    cybersourcePayFromData.locale = 'en'
    cybersourcePayFromData.merchant_defined_data5 = 'web'
    cybersourcePayFromData.bill_to_address_line1 = '1295 Charleston Rd'
    cybersourcePayFromData.bill_to_address_city = 'Mountain View'
    cybersourcePayFromData.bill_to_address_country = 'US'
    cybersourcePayFromData.bill_to_address_state = 'CA'
    cybersourcePayFromData.bill_to_address_postal_code = '94043'

    if (!submitResult.token) {
      cybersourcePayFromData.signed_field_names = `${cybersourcePayFromData.signed_field_names},payment_method,bill_to_address_state,bill_to_address_postal_code`
      cybersourcePayFromData.payment_method = 'card'
      cybersourcePayFromData.unsigned_field_names = 'card_type,card_number,card_expiry_date,card_cvn'
    } else {
      cybersourcePayFromData.signed_field_names = `${cybersourcePayFromData.signed_field_names},payment_token`
    }

    const cybersourcePayFromAction = isPrdEnv
      ? 'https://secureacceptance.cybersource.com/silent/embedded/pay'
      : 'https://testsecureacceptance.cybersource.com/silent/embedded/pay'
    signParams(submitResult, cybersourcePayFromData, function () {
      offbeforeunload()
      if (!submitResult.token) {
        postCreditCardIframeMessage({
          type: 'cybersourceSubmit',
          props: {
            cybersourcePayFromData: { data: cybersourcePayFromData, action: cybersourcePayFromAction }
          }
        })
      } else {
        const cybersourcePayFrom = document.querySelector('#sybersource_old_card_payment_form')
        for (const [key, value] of Object.entries(cybersourcePayFromData)) {
          if (cybersourcePayFrom.querySelector(`.${key}`)) {
            cybersourcePayFrom.querySelector(`.${key}`).value = value
          }
        }
        cybersourcePayFrom.action = cybersourcePayFromAction
        ctx.$emit('success')
        cybersourcePayFrom.submit()
      }
    }, ctx)
  } catch (error) {
    ctx.$emit('error', null, {
      module: 'cybersource-pay',
      action: 'ui',
      path: 'at <cybersource-pay.js>: cybersourcePay',
      message: 'cybersourcePay 支付失败， 数据合并时发生异常，errorMsg：' + getErrorInfo(error) + '。submitResult：' + JSON.stringify(submitResult || {})
    })
  }
}
