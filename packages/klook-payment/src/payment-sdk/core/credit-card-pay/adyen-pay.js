/* eslint-disable */
import assign from 'lodash/assign'
import {
  isMobile,
  offbeforeunload,
  enterExecuteSuccessPage,
  isCreatedStatus,
  execute,
  getExecuteBaseData,
} from '../utils'
import { paymentLogQuery, getErrorInfo } from '../utils/logQuery'
import dfSet from './adyen_fingerprint.js'


const encodeBase64URL = (dataStr) => {
  const base64 = window.btoa(dataStr)
  let base64url = base64.split('=')[0] // Remove any trailing '='s

  base64url = base64url.replace(/\+/g, '-') // 62nd char of encoding
  base64url = base64url.replace(/\//g, '_') // 63rd char of encoding

  return base64url
}


const decodeBase64URL = (str) => {
  let base64 = str
  base64 = base64.replace(/-/g, '+') // 62nd char of encoding
  base64 = base64.replace(/_/g, '/') // 63rd char of encoding
  switch (base64.length % 4) // Pad with trailing '='s
  {
    case 0:
      break // No pad chars in this case
    case 2:
      base64 += '=='; break // Two pad chars
    case 3:
      base64 += '='; break // One pad char
    default:
      if (window.console && window.console.log) {
        window.console.log('### base64url::decodeBase64URL::  Illegal base64url string!')
      }
  }

  try {
    return window.atob(base64)
  } catch (error) {
    const orderNo = urlParam('order_no')
    const paymentAssetNo = urlParam('payment_asset_no')
    paymentLogQuery({
      action: 3,
      order_no: orderNo,
      payment_asset_no: paymentAssetNo,
      file_name: 'utils.js',
      message: `错误消息：${JSON.stringify(error)}`
    })
  }
}

dfSet(document.getElementById('fingerprint'))

let __ctx = null
let currentTime = Date.now()
// let threeDS2Token = '';
let additional_data = {}
let reqData = {}
const guestHost = 'https://www.klook.com'
let identifyShopperTime = null

const generateAdyenPayData = (submitResult) => {
  if (!submitResult.token) {
    throw new Error('no submitResult.token')
  }

  const creditCardInfo = submitResult.credit_card_info
  const fingerprint = document.getElementById('fingerprint').value

  let notificationURL = `${location.protocol}//${location.host}/web3/adyen_3ds2?type=challengeShopper`
  // if (isGuestCheckout()) {
  //   notificationURL = `${guestHost}/web3/adyen_3ds2_adyen?type=challengeShopper`
  // }

  return assign(getExecuteBaseData(submitResult), {
    payment_details: {
      token: submitResult.token,
      save_card: creditCardInfo ? creditCardInfo.save_card : false,
      is_new: !!creditCardInfo,
      bin: creditCardInfo ? creditCardInfo.bin : '',
      issuer_bin: creditCardInfo ? creditCardInfo.issuer_bin : '',
      last4: creditCardInfo ? creditCardInfo.last4 : '',
      kvalue: creditCardInfo ? creditCardInfo.kvalue : '',
      telephone_number: creditCardInfo ? creditCardInfo.telephone_number : '',
      postal_code: creditCardInfo ? creditCardInfo.postal_code : '',
      creditcard_adyen: {
        deviceFingerprint: fingerprint,
        browserInfo: {
          userAgent: window && window.navigator ? window.navigator.userAgent : '',
          acceptHeader: 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
          language: window && window.navigator ? window.navigator.language : '',
          colorDepth: window && window.screen ? window.screen.colorDepth : '',
          screenHeight: window && window.screen ? window.screen.height : '',
          screenWidth: window && window.screen ? window.screen.width : '',
          timeZoneOffset: new Date().getTimezoneOffset(),
          javaEnabled: window && window.navigator ? navigator.javaEnabled() : false
        },
        threeDS2RequestData: {
          deviceChannel: 'browser',
          notificationURL
        }
      }
    }
  })
}

const createIframe = (container, name, width = '0', height = '0', callback) => {
  if (!name || name.length === 0) {
    throw new Error('Name parameter missing for iframe')
  }

  const iframe = document.createElement('iframe')

  iframe.classList.add(name + 'Class')

  iframe.width = width
  iframe.height = height
  iframe.name = name
  iframe.style.position = 'fixed'
  iframe.style.background = '#fff'
  iframe.setAttribute('frameborder', '0')
  iframe.setAttribute('border', '0')

  const noIframeElContent = document.createTextNode('<p>Your browser does not support iframes.</p>')

  iframe.appendChild(noIframeElContent)

  container.append(iframe)

  if (iframe.attachEvent) {
    // IE fallback
    iframe.attachEvent('onload', function () {
      if (callback && typeof callback === 'function') {
        callback(iframe.contentWindow)
      }
    })
  } else {
    iframe.onload = function () {
      if (callback && typeof callback === 'function') {
        callback(iframe.contentWindow)
      }
    }
  }

  return iframe
}

const createForm = (name, action, target, inputName, inputValue) => {
  if (!name || !action || !target || !inputName || !inputValue) {
    throw new Error('Not all required parameters provided for form creation')
  }

  if (name.length === 0 || action.length === 0 || target.length === 0 || inputName.length === 0 || inputValue.length === 0) {
    throw new Error('Not all required parameters have suitable values')
  }

  const form = document.createElement('form')
  form.style.display = 'none'
  form.name = name
  form.action = action
  form.method = 'POST'
  form.target = target
  const input = document.createElement('input')
  input.name = inputName
  input.value = inputValue
  form.appendChild(input)
  return form
}

const open3dsSms = async (data) => {
  let unionPaySms = null
  if (isMobile.call(__ctx)) {
    unionPaySms =
            await import(/* webpackChunkName: "UnionPaySms" */ '../../mobile/union_pay_sms')
  } else {
    unionPaySms =
            await import(/* webpackChunkName: "UnionPaySms" */ '../../desktop/phoneVerification/union_pay_sms')
  }

  const { md, telephone_number } = data.creditcard_adyen

  unionPaySms.open3dsSms({
    phone: telephone_number,
    onConfirm(code) {
      const param = assign(getExecuteBaseData(data), {
        order_no: data.order_no,
        modulePath: 'at <adyen-pay.js>: onConfirm',
        payment_details: {
          creditcard_adyen: {
            threeDSResult: {
              md,
              paResponse: code
            }
          }
        }
      })

      execute(
        __ctx,
        param,
        (result) => {
          __ctx.$emit('success')
          offbeforeunload()
          enterExecuteSuccessPage(result.order_no, param.return_url && param.return_url.success)
       })
    },
    onClose() {
      __ctx.$emit('cancel', null, {
        module: 'adyen-pay',
        action: 'ui',
        path: 'at <adyen-pay.js>: unionPaySms.open3dsSms-onClose',
        message: 'adyen-pay支付失败，用户取消支付'
      })
    }
  }, __ctx)
}

const open3dsWin = (data) => {
  const { pa_request, md, issuer_url, return_url } = data.creditcard_adyen
  // 注：后端这里返回的 return_url 是传给他们的 return_url.success
  const res_url = return_url
  document.getElementById('adyen_PaReq').value = pa_request
  document.getElementById('adyen_MD').value = md
  document.getElementById('adyen_TermUrl').value = res_url
  offbeforeunload()
  const form = document.getElementById('adyen_pay_form')
  form.setAttribute('action', issuer_url)
  __ctx.$emit('success')
  form.submit()
}

const handleResultCode = {
  RedirectShopper: (result) => {
    // 3ds1
    if (result.creditcard_adyen.pa_request === 'CUPSecurePlus-CollectSMSVerificationCode') {
      // union pay 二期
      open3dsSms(result)
    } else {
      open3dsWin(result)
    }
  },
  IdentifyShopper: (result) => {
    const additionalData = result.creditcard_adyen.additional_data
    let threeDSMethodNotificationURL = `${location.protocol}//${location.host}/web3/adyen_3ds2?type=identifyShopper`
    // if (isGuestCheckout()) {
    //   threeDSMethodNotificationURL = `${guestHost}/web3/adyen_3ds2_adyen?type=identifyShopper`
    // }
    const dataObj = {
      threeDSServerTransID: additionalData['threeds2.threeDSServerTransID'],
      threeDSMethodNotificationURL
    }
    // threeDS2Token = additionalData['threeds2.threeDS2Token']
    additional_data = additionalData
    const encodedJSON = encodeBase64URL(JSON.stringify(dataObj))

    const IFRAME_NAME = 'threeDSMethodIframe'

    createIframe(document.body, IFRAME_NAME)

    const form = createForm('threedsMethodForm', additionalData['threeds2.threeDSMethodURL'], IFRAME_NAME, 'threeDSMethodData', encodedJSON)

    document.body.appendChild(form)

    setTimeout(function () {
      document.body.removeChild(form)
    }, 1000)

    form.submit()

    currentTime = Date.now()

    clearTimeout(identifyShopperTime)
    identifyShopperTime = setTimeout(() => {
      const eventData = {}
      eventData.additional_data = additional_data
      reqData.payment_details.creditcard_adyen.threeDS2RequestData.threeDSCompInd = 'N'
      reqData.payment_details.creditcard_adyen = assign(reqData.payment_details.creditcard_adyen, eventData)
      document.querySelectorAll('.threeDSMethodIframeClass').forEach(e => e.remove())
      clearTimeout(identifyShopperTime)
      executePayment(reqData)
    }, 10000)
  },
  ChallengeShopper: async (result) => {
    const additionalData = result.creditcard_adyen.additional_data
    const cReqData = {
      threeDSServerTransID: additionalData['threeds2.threeDS2ResponseData.threeDSServerTransID'],
      acsTransID: additionalData['threeds2.threeDS2ResponseData.acsTransID'],
      messageVersion: additionalData['threeds2.threeDS2ResponseData.messageVersion'],
      challengeWindowSize: '05',
      messageType: 'CReq'
    }
    // threeDS2Token = additionalData['threeds2.threeDS2Token']
    additional_data = additionalData

    const encodedJSON = encodeBase64URL(JSON.stringify(cReqData))

    const IFRAME_NAME = 'threeDSChallengeIframe'

    const container = document.getElementById('adyen_3ds_challenge')
    container.style.display = 'block'

    if (isMobile.call(__ctx)) {
      container.style.backgroundColor = 'rgba(0,0,0,0.3)'
      const loadingBox = container.querySelector('.loading_box')
      loadingBox.style.transform = 'translate(-50%,-50%)'
      loadingBox.style.position = 'fixed'
      loadingBox.style.top = '50%'
      loadingBox.style.left = '50%'
    }
    // Create iframe with challenge window dimensions
    createIframe(container, IFRAME_NAME, '100%', '100%')

    // Create a form that will use the iframe to POST data to the acsURL
    const form = createForm('cReqForm', additionalData['threeds2.threeDS2ResponseData.acsURL'], IFRAME_NAME, 'creq', encodedJSON)

    document.body.appendChild(form)

    setTimeout(function () {
      document.body.removeChild(form)
    }, 1000)

    form.submit()

    currentTime = Date.now()
  }
}

const adyen3DS2Pay = (reqData) => {
  execute(__ctx, { modulePath: 'at <adyen-pay.js>: adyen3DS2Pay', ...reqData }, (result) => {
    if (isCreatedStatus(result.invoice_status)) {
      const gateWayExtraInfo = result.gateway_extra_info
      // 3ds
      try {
        handleResultCode[gateWayExtraInfo.creditcard_adyen.result_code](
          assign(gateWayExtraInfo, {
            payment_gateway: reqData.payment_gateway,
            invoice_guid: reqData.invoice_guid,
            invoice_submission_guid: reqData.invoice_submission_guid,
            return_url: reqData.return_url,
            order_no: reqData.order_no
          })
        )
      } catch (err) {
        __ctx.$emit('error', null, {
          module: 'adyen-pay',
          action: 'ui',
          path: 'at <adyen-pay.js>: adyen3DS2Pay-handleResultCode',
          message: 'gateWayExtraInfo数据异常：' + JSON.stringify(gateWayExtraInfo)
        })
      }
    } else {
      // 非 3ds
      __ctx.$emit('success')
      offbeforeunload()
      enterExecuteSuccessPage(result.order_no, reqData.return_url && reqData.return_url.success)
    }
  })
}

/**
 * @description 处理消息体
 * @param e
 */
const formatMessage = (e) => {
  const eventData = e.data
  /**
   * 修复下面 `eventData.additional_data = additional_data sentry` 报错: 
    TypeError Cannot create property 'additional_data' on string 'dispatchCoroutine'
   */
  if (eventData?.type && (e.origin === `${location.protocol}//${location.host}` || e.origin === guestHost)) {
    eventData.additional_data = additional_data
    if (eventData.type === 'identifyShopper') {
      let threeDSCompInd = (Date.now() - currentTime) > 10000 ? 'N' : 'Y'
      if (eventData.threeDSMethodData) {
        const threeDSMethodData = JSON.parse(decodeBase64URL(eventData.threeDSMethodData))
        if (threeDSMethodData && threeDSMethodData.threeDSCompInd) {
          threeDSCompInd = threeDSMethodData.threeDSCompInd
        }
      }

      reqData.payment_details.creditcard_adyen.threeDS2RequestData.threeDSCompInd = threeDSCompInd
      document.querySelectorAll('.threeDSMethodIframeClass').forEach(e => e.remove())
      clearTimeout(identifyShopperTime)
      reqData.payment_details.creditcard_adyen = assign(reqData.payment_details.creditcard_adyen, eventData)
      executePayment(reqData)
    } else if (eventData.type === 'challengeShopper') {
      document.querySelectorAll('.threeDSChallengeIframeClass').forEach(e => e.remove())
      let transStatus = (Date.now() - currentTime) > 600000 ? 'U' : 'Y'
      if (eventData.cres) {
        const cresData = JSON.parse(decodeBase64URL(eventData.cres))
        if (cresData && cresData.transStatus) {
          transStatus = cresData.transStatus
        }
      }

      reqData.payment_details.creditcard_adyen = {
        additional_data,
        threeDS2Result: {
          transStatus
        }
      }
      executePayment(reqData)
    }
    sendInhouseData(eventData)
  }
}

const executePayment = (reqData) => {
  execute(__ctx, { modulePath: 'at <adyen-pay.js>: executePayment', ...reqData }, (result) => {
    if (isCreatedStatus(result.invoice_status)) {
      const gateWayExtraInfo = result.gateway_extra_info
      try {
        handleResultCode[gateWayExtraInfo.creditcard_adyen.result_code](gateWayExtraInfo)
      } catch (err) {
        __ctx.$emit('error', null, {
          module: 'adyen-pay',
          action: 'ui',
          path: 'at <adyen-pay.js>: executePayment-handleResultCode',
          message: '数据异常gateWayExtraInfo：' + JSON.stringify(gateWayExtraInfo)
        })
      }
    } else {
      __ctx.$emit('success')
      offbeforeunload()
      enterExecuteSuccessPage(result.order_no, reqData.return_url && reqData.return_url.success)
    }
  })
}

const sendInhouseData = (data) => {
  window.__in_house && window.__in_house.track('custom', document.body, {
    type: data.type,
    Gateway: reqData.payment_gateway,
    orderNo: reqData.order_no,
    spm: 'Payment_Processing.Payment_Processing_3DS'
  })
}

export const adyenPay = (submitResult, ctx) => {
  __ctx = ctx
  window.removeEventListener('message', formatMessage)
  try {
    reqData = generateAdyenPayData(submitResult)
  } catch (error) {
    return ctx.$emit('error', new Error(ctx.$t('pay.card.error.tip1')), {
      module: 'adyen-pay',
      action: 'ui',
      path: 'at <adyen-pay.js>: adyenPay-generateAdyenPayData',
      message: '错误信息：数据合并时发生异常，中断了进程，errMsg：' + getErrorInfo(error) + '。submitResult：' + JSON.stringify(submitResult || {})
    })
  }
  
  adyen3DS2Pay(reqData)
  const container = document.getElementById('adyen_3ds_challenge')
  const closeIcon = container.querySelector('.klook-icon-close')
  closeIcon.addEventListener('click', () => {
    clearTimeout(identifyShopperTime)
    offbeforeunload()
    window.location.reload()
    __ctx.$emit('cancel', null, {
      module: 'adyen-pay',
      action: 'ui',
      path: 'at <adyen-pay.js>: adyenPay',
      message: '用户取消adyenPay支付'
    })
  })
  window.addEventListener('message', formatMessage)
}
