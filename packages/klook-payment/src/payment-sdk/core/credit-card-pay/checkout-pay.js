import assign from 'lodash/assign'
import {
  enterExecuteSuccessPage,
  execute,
  isCreatedStatus,
  offbeforeunload,
  getExecuteBaseData
} from '../utils'

const getExecuteData = (data) => {
  const creditCardInfo = data.credit_card_info
  return assign(getExecuteBaseData(data), {
    payment_details: {
      token: data.token,
      save_card: creditCardInfo ? creditCardInfo.save_card : false,
      is_new: !!creditCardInfo,
      bin: creditCardInfo ? creditCardInfo.bin : '',
      last4: creditCardInfo ? creditCardInfo.last4 : '',
      kvalue: creditCardInfo ? creditCardInfo.kvalue : ''
    }
  })
}

const checkoutPayIn = (submitResult, ctx) => {
  let reqData
  try {
    reqData = getExecuteData(submitResult)
  } catch {
    return ctx.$emit('error', null, {
      module: 'checkout-pay',
      action: 'ui',
      path: 'at <checkout-pay.js>: checkoutPayIn-getExecuteData',
      message: `错误信息：getExecuteData数据合并时发生异常，中断了进程，数据源：${JSON.stringify(submitResult || {})}`
    })
  }
  execute(ctx, reqData, (executeResult) => {
    offbeforeunload()
    // 3ds
    if (isCreatedStatus(executeResult.invoice_status)) {
      const jumpUrl = executeResult.gateway_extra_info?.creditcard_checkout?.redirect?.url
      if (!jumpUrl) {
        return ctx.$emit('error', null, {
          module: 'checkout-pay',
          action: 'ui',
          path: 'at <checkout-pay.js>: checkoutPayIn-execute',
          message: '错误信息：获取executeResult.gateway_extra_info.creditcard_checkout.redirect.url 失败，中断checkout 3ds支付。executeResult：' + JSON.stringify(executeResult || {})
        })
      }
      window.location.href = jumpUrl
    } else {
      // 结束
      enterExecuteSuccessPage(
        executeResult.order_no,
        executeResult.return_url && executeResult.return_url.success
      )
    }
    ctx.$emit('success')
  })
}
export const checkoutPay = (submitResult, ctx) => {
  if (submitResult.token) {
    checkoutPayIn(submitResult, ctx)
  } else {
    ctx.$emit('error', null, {
      module: 'checkout-pay',
      action: 'ui',
      path: 'at <checkout-pay.js>: checkoutPay',
      message: 'checkoutPay 卡token缺失导致支付失败， submitResult：' + JSON.stringify(submitResult || {})
    })
  }
}
