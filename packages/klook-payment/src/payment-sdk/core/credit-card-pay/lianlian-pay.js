/* eslint-disable */
import { getRiskDeepKnowIds } from '@klook/risk-deepknow-sdk'
import { offbeforeunload, enterExecuteSuccessPage, isCreatedStatus, isPrdEnv, urlParam, isMobile } from '../utils'
import { changeXHosts } from '../utils/changeXHosts'
import { paymentLogQuery } from '../utils/logQuery'
import { bffExecute, bffSubmit } from '../../../assets/apis'


export const lianlianPay = async (res, ctx) => {
  let smsDialog = null
  if (isMobile.call(ctx)) {
    smsDialog = await import(/* webpackChunkName: "codeValidate" */ '../../mobile/codeValidate')
  } else {
    smsDialog = await import(/* webpackChunkName: "codeValidate" */ '../../desktop/codeValidate')
  }
  let phone = null
  try {
    phone = res.credit_card_info.telephone_number
  } catch {
    phone = res.action.action_details.native.telephone_number
  }
  smsDialog.openDialog({
    phone,
    disabled: true,
    showCountTime: true,
    // 执行支付->点击验证按钮触发
    onExecute: async (code) => {
      smsDialog.toggleBtnStatus(true)
      const params = {
        payment_gateway: res.payment_gateway,
        invoice_guid: res.invoice_guid,
        invoice_submission_guid: res.invoice_submission_guid,
        payment_details: { ...res.credit_card_info, ...{ verify_code: code } }
      }
      const headers = await changeXHosts({
        'X-Klook-Request-Id': res.checkoutXKlookReqId
      })
      const response = await ctx.$axios.$post(bffExecute, params, { headers, tagName: res.payment_gateway }).catch((error) => ({ error }))
      smsDialog.toggleBtnStatus(false)
      if (response && response.success) {
        await smsDialog.closeDialog()
        offbeforeunload()
        if (isCreatedStatus(response.result && response.result.invoice_status)) {
          ctx.$emit('error', {
            code: '999988',
            message: ctx.$t('44874')
          }, {
            module: 'execute',
            action: 'api',
            level: 'warning',
            path: 'at <lianlian-pay.js>: onExecute',
            code: '999988',
            message: 'payment order isCreatedStatus'
          })
          return
        }
        ctx.$emit('success')
        enterExecuteSuccessPage(res.order_no)
      } else {
        if (response.error && response.error.status) {
          const orderNo = res.order_no || urlParam('order_no')
          const paymentAssetNo = res.payment_asset_no || urlParam('payment_asset_no')
          paymentLogQuery({
            action: 2,
            api_name: 'execute',
            order_no: orderNo,
            payment_asset_no: paymentAssetNo,
            req_data: params,
            file_name: 'lianlian-pay.js',
            requestId: res.checkoutXKlookReqId,
            message: `错误信息：${JSON.stringify(response.error)}`
          })
        }
        const msg = response.error.message
        smsDialog.showToast(msg)
        if (response.error.code === '0222E1001') {
          smsDialog.cleanCode()
        } else {
          ctx.$emit('error', response.error, {
            module: 'execute',
            action: 'api',
            path: 'at <lianlian-pay.js>: onExecute',
            code: response?.error?.code,
            message: response?.error?.message
          })
          
          await smsDialog.cleanCode()
          await smsDialog.closeDialog()
        }
      }
    },
    // 重新发送->等于重创订单，需要清理部分参数信息
    onSubmit: async () => {
      smsDialog.toggleBtnStatus(true)
      const klook = (ctx?.$store || window.__KLOOK__)?.state?.klook || {}

      const {
        forterToken,
        kountSessionId
      } = getRiskDeepKnowIds({
        isPrdEnv,
        platform: klook.platformMp || klook.platform
      })
      const params = {
        method_key: res.method_key,
        token: res.token,
        credit_card_info: res.credit_card_info,
        order_no: res.order_no,
        pay_price: res.price_info.pay_price,
        merchant_id: res.merchant_id || null,
        // promo_code: res.promo_code || null,
        return_url: res.return_url || null,
        terms_accepted: res.terms_accepted || '',
        customized_data: {
          forter_token: forterToken || '',
          kount_session_id: kountSessionId || ''
        }
      }
      if (params.credit_card_info) {
        params.credit_card_info.is_new = true
      }
      if (params.credit_card_info && params.credit_card_info.phoneNumber !== undefined) {
        delete params.credit_card_info.phoneNumber
      }
      if (params.credit_card_info && params.credit_card_info.countryCode !== undefined) {
        delete params.credit_card_info.countryCode
      }
      if (params.pay_price && params.pay_price.value) {
        delete params.pay_price.value
      }
      const headers = await changeXHosts({
        'X-Klook-Request-Id': res.checkoutXKlookReqId
      })
      const response = await ctx.$axios.$post(bffSubmit, params , { headers, tagName: res.method_key }).catch((error) => ({ error }))
      if (response && response.success) {
        smsDialog.setToastLabel('sign_verification_has_been_sent')
      } else {
        if (response.error && response.error.status) {
          const orderNo = res.order_no || urlParam('order_no')
          const paymentAssetNo = res.payment_asset_no || urlParam('payment_asset_no')
          paymentLogQuery({
            action: 2,
            api_name: 'submit',
            req_data: params,
            order_no: orderNo,
            payment_asset_no: paymentAssetNo,
            file_name: 'lianlian-pay.js',
            requestId: res.checkoutXKlookReqId,
            message: `错误信息：${JSON.stringify(response.error)}`
          })
        }
        await smsDialog.cleanCode()
        await smsDialog.closeDialog()
        ctx.$emit('error', response.error, {
            module: 'submit',
            action: 'api',
            path: 'at <lianlian-pay.js>: onSubmit',
            code: response?.error?.code,
            message: response?.error?.message
          })
      }
    },
    // 点击关闭按钮，执行弹窗关闭&广播通知，更改支付按钮状态
    onCancel() {
      ctx.$emit('cancel', null, {
        module: 'lianlian-pay',
        path: 'at <lianlian-pay.js>: onCancel',
        message: 'lianlianPay 支付失败：用户点击关闭按钮取消支付',
        action: 'ui'
      })
      smsDialog.closeDialog()
    }
  }, ctx)
}
