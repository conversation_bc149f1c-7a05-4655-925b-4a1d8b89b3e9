import { offbeforeunload } from '../utils'
import { getErrorInfo } from '../utils/logQuery'

export const nttDatapay = (submitResult, ctx) => {
  try {
    const nativeData = submitResult.action.action_details.native
    if (!nativeData.card_token) {
      nativeData.data.carddata = submitResult.token
    } else if (nativeData.channel_type !== 'cafis') { /// cafis 支付，需要把cardCvv 字段去掉，不能传空
      nativeData.data.cardCvv = submitResult.cvv
    }

    const nttpayForm = document.createElement('form')
    nttpayForm.method = 'POST'
    nttpayForm.action = nativeData.href
    for (const key in nativeData.data) {
      const input = document.createElement('input')
      input.name = key
      input.value = nativeData.data[key]
      nttpayForm.appendChild(input)
    }
    document.body.appendChild(nttpayForm)
    offbeforeunload()
    ctx.$emit('success')
    nttpayForm.submit()
  } catch (error) {
    // 这里只能抓取error 状态
    ctx.$emit('error', null, {
      module: 'ntt-data-pay',
      action: 'ui',
      path: 'ntt-data-pay.js',
      message: 'nttDataCafisPay支付失败, 数据处理时发生错误。errorMsg：' + getErrorInfo(error)
    })
  }
}
