import Vue from 'vue'

let VM = null

export const payLahPay = async (submitResult = {}, ctx) => {
  if (!VM) {
    const { default: PaylahQueryModal } = await import('./dialog.vue')
    const DialogConstructor = Vue.extend(PaylahQueryModal)
    const el = document.createElement('div')
    document.body.appendChild(el)
    VM = new DialogConstructor({
      parent: ctx
    }).$mount(el)
    ;['error', 'cancel', 'success'].forEach((event) => {
      VM.$on(event, ctx.$emit.bind(ctx, event))
    })
  }
  const { phone_number } = submitResult
  VM.visible = true
  VM.phoneNumber = phone_number
  VM.submitResult = submitResult
}
