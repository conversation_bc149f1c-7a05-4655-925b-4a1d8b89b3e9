<template>
  <klk-modal
    title-align="center"
    width="480px"
    :title="getModalTitle"
    :closable="true"
    :open.sync="visible"
    :show-default-footer="false"
    :esc-press-closable="true"
    :lock-scroll="true"
    :overlay="true"
    :overlay-opacity="0.8"
    :z-index="4000"
    @on-close="onClose"
  >
    <div class="dbs-modal">
      <template v-if="isShowErorrTips">
        <div class="klk-modal-dbs-paylah-content">
          {{ errorMessage }}
        </div>
        <klk-button class="klk-modal-dbs-paylah-button" @click="onClose">
          {{ okTxt }}
        </klk-button>
      </template>
      <template v-else>
        <div class="klk-modal-dbs-paylah-phone">
          {{ getPhoneNumber }}
        </div>
        <div class="klk-modal-dbs-paylah-content">
          {{ tips }}
        </div>
      </template>
    </div>
  </klk-modal>
</template>

<script>

import KlkModal from '@klook/klook-ui/lib/modal'
import KlkButton from '@klook/klook-ui/lib/button'
import { enterExecuteSuccessPage, enterBookingPage, offbeforeunload } from '../utils'
import { paymentLogQuery } from '../utils/logQuery'

import { cashierQuery } from '../../../assets/apis'

export default {
  name: 'PaylahQueryModal',
  components: { KlkModal, KlkButton },
  data() {
    return {
      visible: false,
      phoneNumber: '',
      pollingTime: 2000,
      queryTime: '',
      submitResult: {},
      isShowErorrTips: false,
      errorMessage: '',
      okTxt: this.$t('global.tips.okTxt'),
      tips: this.$t('79407')
    }
  },
  computed: {
    getPhoneNumber() {
      let num = this.phoneNumber
      if (this.phoneNumber && this.phoneNumber.length > 4) {
        num = this.phoneNumber.substring(4)
      }
      return '+65 ****'.concat(num)
    },
    getModalTitle() {
      return this.isShowErorrTips ? this.$t('alert_title_error') : this.$t('79406')
    }
  },
  mounted() {
    if (this.queryTime) {
      clearInterval(this.queryTime)
    }
    this.queryTime = setInterval(() => {
      this.queryResult()
    }, this.pollingTime)
  },
  beforeDestroy() {
    clearInterval(this.queryTime)
  },
  methods: {
    onClose() {
      this.visible = false
      clearInterval(this.queryTime)
      const url = this.submitResult.return_url
      const returnUrl = url && url.success
      this.$emit('cancel', null, {
        module: 'paylah-pay',
        action: 'ui',
        path: 'paylah-pay/dialog.vue@onClose',
        message: '用户取消paylahPay支付' + (
          this.errorMessage
            ? '，取消支付时遇到的错误信息：' + this.errorMessage
            : ''
        )
      })
      offbeforeunload()
      enterBookingPage(returnUrl)
    },
    async queryResult() {
      const { order_no, invoice_guid, return_url } = this.submitResult
      const returnUrl = return_url && return_url.success
      const resp = await this.$axios.$get(cashierQuery, {
        params: { invoice_guid }
      }).catch(error => ({ error }))

      if (resp.success) {
        this.isShowErorrTips = false
        const result = resp.result
        this.pollingTime = result.check_interval
        if (['Completed', 'Pending'].includes(result.invoice_status)) {
          clearInterval(this.queryTime)
          offbeforeunload()
          enterExecuteSuccessPage(order_no, returnUrl)
          this.$emit('success')
        } else if (!result.check_payment) {
          clearInterval(this.queryTime)
        }
      } else {
        // 在轮询请求接口时 返回错误不代表真正的支付失败， 还可以继续流程
        paymentLogQuery({
          action: 2,
          api_name: 'query',
          order_no,
          req_data: invoice_guid,
          file_name: 'paylah-pay/dialog.vue@queryResult',
          message: `在轮询支付结果时请求失败，错误信息：${JSON.stringify(resp.error)}`
        })
        this.isShowErorrTips = true
        this.errorMessage = resp.error && resp.error.message
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.dbs-modal {
    display: flex;
    justify-content: center;
    flex-direction: column;
    align-items: center;

    .klk-modal-dbs-paylah-button {
        margin-top: 26px;
    }
    .klk-modal-dbs-paylah-phone {
        margin-top: 8px;
        text-align: center;
        padding-bottom: 4px;
        color: $color-red-500;
        font-size: $fontSize-body-s;
    }
    .klk-modal-dbs-paylah-content {
        line-height: 21px;
        text-align: center;
        color: $color-neutral-900;
        font-size: $fontSize-body-s;
    }
}
</style>
