/* eslint-disable */
import { offbeforeunload, isMobile } from './utils'
import { getErrorInfo } from './utils/logQuery'

export const redirectPaymentGatewayList = [
  /**
     * 'adyen' 类，如 'momowallet_adyen','paymayawallet_adyen',dokuwallet_adyen','sofort_adyen',
     * 'ideal_adyen','alfamart_adyen', 'atmpermata_adyen','indomaret_adyen',
     */
  'adyen',
  'onlinebanking', // 'onlinebanking' 类
  'dragonpay',
  'alipaycncashier',
  'paypal',
  'gcash',
  'grabpay',
  'antfin',
  'unionpay',
  'linepay',
  'tendopay',
  'payme',
  'paypay',
  'mobile_banking',
  'atm_by_momo',
  'payco',
  'ntt_data',
  'afterpay',
  'truemoney_2c2p',
  'shopeepay_2c2p',
  'th_card_installment_2c2p',
  'atome',
  'jkopay',
  'payanyone',
  'atome',
  'zip',
  'kakaopaycashier',
  'alipayhkcashier',
  'gcashcashier',
  'tngcashier',
  'naverpay_point',
  'naverpay_card',
  'gopay',
  'culturepass'
]

export const isInRedirectWhiteList = paymentGateway => redirectPaymentGatewayList.some(x => paymentGateway.includes(x))

export function redirectPay(submitResult, listenersMap = {}) {
  try {
    const href = submitResult.action.action_details.redirect.href
    const paymentGateway = submitResult.payment_gateway
    offbeforeunload()
    // 为了临时解决香港微信支付二维码无效的BUG
    // 主要是下面的if 使用的includes ，wechatpay_ntt_data mobile 包含 ntt_data,不会走微信那套流程，所以加的一个判断

   if (
    (paymentGateway !== 'wechatpay_ntt_data' && isInRedirectWhiteList(paymentGateway)) ||
    paymentGateway == 'alipay' ||
    ['wechatpay', 'alipayhk'].includes(paymentGateway) && isMobile()
  ) {
      // 第三方提供的页面
      window.location.href = href
    } else {
      const payment_deadline = submitResult.payment_dead_time && new Date(submitResult.payment_dead_time).getTime() || 0
    // wechat desktop 生成二维码，用户扫码支付
      window.location.href = `${href}` +
                `&order_no=${submitResult.order_no}&payment_deadline=${payment_deadline}` +
                `&user_pay_currency=${submitResult.user_pay_currency}&user_total_pay_price=${submitResult.user_total_pay_price}`
    }

    typeof listenersMap.onSuccess === 'function' && listenersMap.onSuccess()
  } catch (error) {
    typeof listenersMap.onError === 'function' && listenersMap.onError(null, {
      module: 'redirect-pay',
      action: 'ui',
      path: 'redirect-pay.js',
      message: 'redirectPay支付失败，errorMsg: ' + getErrorInfo(error) + '。submitResult：' + JSON.stringify(submitResult || {})
    })
  }
}
