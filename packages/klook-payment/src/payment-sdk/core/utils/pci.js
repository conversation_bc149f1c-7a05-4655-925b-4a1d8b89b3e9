import { paymentLogQuery } from './logQuery'
import { isPrdEnv } from './'

const KLK_ENV = isPrdEnv ? 'prd' : 'test'
const originMap = {
  // 'dev':'http://localhost:8080',
  test: 'https://pay.test.klooktest.com',
  prd: 'https://pay.klook.com'
}
// const creditCardIframeOrigin = originMap[KLK_ENV] || 'http://localhost:8080'
const creditCardIframeOrigin = originMap[KLK_ENV]

export const getCreditCardIframeSrc = function () {
  const pciVersion = '1.6.2'
  const srcMap = {
    // 'dev':'http://localhost:8080',
    // 'test': `https://klook-pci-staticfile-dev.s3-ap-southeast-1.amazonaws.com/dist/${pciVersion}/index.html`,
    test: `https://pay.test.klooktest.com/dist/${pciVersion}/index.html`,
    prd: `https://pay.klook.com/${pciVersion}/index.html` // [querystring whitelist cache policy](https://docs.aws.amazon.com/AmazonCloudFront/latest/DeveloperGuide/QueryStringParameters.html)
  }
  // return srcMap[KLK_ENV] || 'http://localhost:8080';
  return srcMap[KLK_ENV]
}
export const getCreditCardIframeElement = function (selector) {
  return document.querySelector(selector || '.j-iframe_credit_card')
}

export const checkValidOrigin = function (origin) {
  return [creditCardIframeOrigin].includes(origin)
}

export const postCreditCardIframeMessage = function (data, iframeElement) {
  iframeElement = iframeElement || getCreditCardIframeElement()
  if (iframeElement && iframeElement.contentWindow) {
    iframeElement.contentWindow.postMessage(data, creditCardIframeOrigin)
  }
}

export const getLocalization = function () {
  return {
    'pay.number.card_error': this.$t('48181'),
    'pay.number.card_bin_not_support': this.$t('48182'),
    'pay.number.order_not_support': this.$t('48183'),
    'pay.id.number': this.$t('27080'),
    'pay.id.number.tips': this.$t('27081'),
    'pay.hold.name': this.$t('27078'),
    'pay.hold.name.tips': this.$t('27079'),
    'pay.phone.number': this.$t('27075'),
    'pay.phone.number.tips': this.$t('27076'),
    'pay.card.number.tips': this.$t('27069'),
    'pay.card.number.fourKey': this.$t('27068'),
    'pay.card.number': this.$t('pay.card.number'),
    'pay.credicard.tooltips': this.$t('pay.credicard.tooltips'),
    'pay.card.expiryDate': this.$t('pay.card.expiryDate'),
    'pay.select.palceholder.month': this.$t('global.mm'),
    'pay.select.palceholder.year': this.$t('global.yyyy'),
    'pay.card.SecurityCode': this.$t('pay.card.SecurityCode'),
    'pay.contact.information': this.$t('pay.contact.information'),
    'pay.card.save.credit': this.$t('pay.card.save.credit'),
    'pay.card.error.tip1': this.$t('pay.card.error.tip1'),
    'payment.certain.credit.card': this.$t('payment.certain.credit.card'),
    'global.payment.cvv.need_be_number': this.$t('global.payment.cvv.need_be_number'),
    'global.select.empty_error': this.$t('global.select.empty_error'),
    'global.error.cant_be_empty': this.$t('global.error.cant_be_empty'),
    'global.select.palceholder': this.$t('global.select.palceholder'),
    'pay.validateTxt': this.$t('pay.validateTxt'),
    'payment.mobile.credit_card_tip_title': this.$t(
      'payment.mobile.credit_card_tip_title'
    ),
    'global.payment.cardnumber.empty': this.$t(
      'global.payment.cardnumber.empty'
    ),
    'global.payment.expiryDate.empty': this.$t(
      'global.payment.expiryDate.empty'
    ),
    'global.payment.cvv.empty': this.$t('global.payment.cvv.empty'),
    'global.payment1.cardnumber.empty': this.$t(
      'global.payment1.cardnumber.empty'
    ),
    'pay.card.error.ntt.tip2': this.$t('pay.card.error.ntt.tip2'),
    'pay.card.error.ntt.tip3': this.$t('pay.card.error.ntt.tip3'),
    'global.payment.month.need_be_number': this.$t('pay.card.error.expiration'),
    'global.payment.year.need_be_number': this.$t('pay.card.error.expiration'),
    'pay.card.ZipCode': this.$t('171407'),
    'global.payment.zipCode.invalid': this.$t('179287'),
    'global.payment.zipCode.empty': this.$t('179791'),
    cardholder_name: this.$t('204719'),
    cardholder_name_empty: this.$t('204722'),
    cardholder_name_short: this.$t('204723'),
    cardholder_name_invalid: this.$t('204724')
  }
}

export const getCardErrorTips = function () {
  return {
    checkoutCardErrorTips: { 'pay.card.error.tip4': this.$t('26209') },
    stripeCardErrorTips: {
      'pay.card.error.tip1': this.$t('pay.card.error.tip1'),
      'pay.card.error.tip2': this.$t('pay.card.error.tip2'),
      'pay.card.error.tip3': this.$t('pay.card.error.tip3')
    }
  }
}

export const handleCreditCardIframeError = function (iframeElement) {
  iframeElement.onerror = function (msg) {
    paymentLogQuery({
      action: 1,
      file_name: 'payment-sdk/core/utils/pci.js',
      message: `页面渲染失败，具体原因:PCI挂载异常，错误信息:${JSON.stringify(
        msg
      )}`
    })
  }
}

export const handleCreditCardNoIframeError = function (msg) {
  paymentLogQuery({
    action: 1,
    file_name: 'payment-sdk/core/utils/pci.js',
    message: msg || '页面渲染失败，具体原因:PCI对象节点不存在'
  })
}

export const getResEnLang = lang =>
  [
    'en',
    'en-AU',
    'en-CA',
    'en-GB',
    'en-HK',
    'en-IN',
    'en-MY',
    'en-NZ',
    'en-PH',
    'en-SG',
    'en-US'
  ].includes(lang)
    ? 'en'
    : lang
