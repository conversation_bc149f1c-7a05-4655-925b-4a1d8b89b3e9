/* eslint-disable */
import { bffExecute } from '../../../assets/apis'

export function getLangPath(path = '', ctx = this) {
  const { language = 'en' } = (ctx?.$store || window.__KLOOK__)?.state?.klook || {}
  const KLK_LANG_PATH = language !== 'en' ? `${language}/` : ''
  return '/' + KLK_LANG_PATH + path
}

export function isCnSite(ctx = this) {
  const { market, host } = (ctx?.$store || window.__KLOOK__)?.state?.klook || {}
  
  return market === 'cn' || [host, location.host].includes('klook.cn')
}

export function isMobile(ctx = this) {
  const platform = (ctx?.$store || window.__KLOOK__)?.state?.klook?.platform
  return platform
    ? platform !== 'desktop'
    : !!(/(iphone)|(android)|(ios)|(ipad)|(ipod)|(blackberry)|(mobile)|(phone)/i.exec(navigator.userAgent))
}

export function urlParam(name, value, url) {
  if (typeof value === 'undefined') {
    return decodeURIComponent((new RegExp('[?|&]' + name + '=' + '([^&;]+?)(&|#|;|$)').exec(url || location.search) || [undefined, ''])[1].replace(/\+/g, '%20')) || null
  } else {
    url = url || window.location.href
    name = name.toString()
    value = encodeURIComponent(value.toString())
    const r = new RegExp('(^|\\W)' + name + '=[^&]*', 'g')
    const vUrl = url.split('#')
    vUrl[0] = (vUrl[0].match(r)) ? vUrl[0].replace(r, '$1' + name + '=' + value) : vUrl[0] + (!vUrl[0].includes('?') ? '?' : '&') + name + '=' + value
    return vUrl.join('#')
  }
}

export const isWechatBrowser = (ua) => {
  ua = (ua || '').toLowerCase()

  return ua && ua.includes('micromessenger') && !ua.includes('miniprogram')
}

export const isPrdEnv = process.env.APP_ENV === 'production'

export const parseUrlQuery = (searchString, unDecode) => {
  const url = searchString || window.location.search
  const theRequest = {}
  if (url.includes('?')) {
    const str = url.split('?')[1]
    const strs = str.split('&')
    for (let i = 0; i < strs.length; i++) {
      if (unDecode) {
        theRequest[strs[i].split('=')[0]] = strs[i].split('=')[1]
      } else {
        theRequest[decodeURIComponent(strs[i].split('=')[0])] = decodeURIComponent(strs[i].split('=')[1])
      }
    }
  }
  return theRequest
}

export const offbeforeunload = () => {
  window.onbeforeunload = null
}

export const jumpOrReload = (href) => {
  if (typeof window === 'undefined') {
    return
  }
  offbeforeunload()
  
  href
    ? window.location.href = href
    : window.location.reload()
}

export function enterPaymentResultPage(guid, ctx = this) {
  const entranceParam = urlParam('entrance') ? `&entrance=${urlParam('entrance')}` : ''
  const upgrade = urlParam('upgrade') ? `&upgrade=${urlParam('upgrade')}` : ''
  window.location.href = getLangPath.call(ctx, `payment/success?cm=${guid}${entranceParam}${upgrade}`)
}

export const getMiddleRedirectUrlPage = (url, actionType) => {
  const parsedQuery = parseUrlQuery(url)
  const baseUrl = decodeURIComponent(window.atob(parsedQuery.middle_redirect_url || ''))
  const entranceParam = urlParam('entrance') ? `&entrance=${urlParam('entrance')}` : ''
  const upgrade = urlParam('upgrade') ? `&upgrade=${urlParam('upgrade')}` : ''
  return `${baseUrl}&action_type=${actionType}${entranceParam}${upgrade}`
}

export function enterExecuteSuccessPage(orderNo, returnUrl, paymentAssetNo) {
  if (returnUrl && returnUrl.includes('middle_redirect_url')) {
    window.location.href = getMiddleRedirectUrlPage(returnUrl, '1')
    return
  }

  enterPaymentResultPage.call(this, orderNo)
}

export function enterBookingPage(returnUrl) {
  if (returnUrl && returnUrl.includes('middle_redirect_url')) {
    window.location.href = getMiddleRedirectUrlPage(returnUrl, '2')
    return
  }
  window.location.href = getLangPath.call(this, 'bookings')
}

export function enterLoginPage(returnUrl, ctx = this) {
  if (returnUrl && returnUrl.includes('middle_redirect_url')) {
    window.location.href = getMiddleRedirectUrlPage(returnUrl, '3')
    return
  }

  window.location.href = getLangPath.call(ctx, 'login')
}

export const isCreatedStatus = (status) => {
  return status === 'Created'
}

export const getExecuteBaseData = (submitResult) => {
  return {
    order_no: submitResult.order_no,
    checkoutXKlookReqId: submitResult.checkoutXKlookReqId,
    payment_gateway: submitResult.payment_gateway,
    ...(submitResult.transaction_no ? {
      transaction_no: submitResult.transaction_no
    } : {
      invoice_guid: submitResult.invoice_guid,
      invoice_submission_guid: submitResult.invoice_submission_guid
    }),
    return_url: submitResult.return_url
  }
}

export const execute = async (ctx, reqData, successCb, errorCb, commCb) => {
  // 绑卡流程需要替换接口url
  let url = bffExecute
  // 调用execute 需要reqData 中添加modulePath 字段，需要记录具体支付方式文件和具体调用位置， 方便通过日志排查问题
  const path = reqData.modulePath || 'utils/index.js'

  delete reqData.modulePath

  if (reqData.apiUrl) {
    url = reqData.apiUrl
    delete reqData.apiUrl
  }

  const resp = await ctx.$axios.$post(url, reqData, {
    headers: { 'X-Klook-Request-Id': reqData.checkoutXKlookReqId },
    tagName: reqData.payment_gateway
  }).catch((error) => ({ error }))
  const orderNo = reqData.order_no || urlParam('order_no')
  if (resp.success) {
    const result = resp.result
    if (isCreatedStatus(result.invoice_status || result.status) && !result.gateway_extra_info) {
      // 后端返回异常，需要上报日志

      ctx.$emit('error', {
        code: '999988',
        message: ctx.$t('44874')
      }, {
          module: 'execute',
          action: 'api',
          level: 'warning',
          path,
          code: '999988',
          message: 'payment order isCreatedStatus'
      })

    // paylater 新增result.invoice_status:"Authorized" 前端等同于 Completed 的状态，跳支付结果页
    } else if (result.invoice_status === 'Authorized') {
      enterExecuteSuccessPage(orderNo, result.return_url && result.return_url.success)
      ctx.$emit('success')
    } else {
      typeof successCb === 'function'
        ? successCb(resp.result)
        : ctx.$emit('success')
    }
  } else {
    // 当时adyen3ds报错时，移除加载提示
    if (reqData.payment_gateway === 'creditcard_adyen') {
      const element = document.getElementById('adyen_3ds_challenge')
      element && (element.style.display = 'none')
    }

    ctx.$emit('error', resp.error, {
      module: 'cashier-execute',
      action: 'api',
      path,
      code: resp?.error?.code,
      message: resp?.error?.message
    })
    typeof errorCb === 'function' && errorCb(resp.error)
  }

  typeof commCb === 'function' && commCb(resp)
}

export const getGuestReturnUrl = function(orderNo, paymentGateway, ctx = this) {
  const payPath = /antfin/.test(paymentGateway) ? 'order-payment-web' : 'order-payment'

  const obj = {
    success: `${location.protocol}//${location.host}${getLangPath.call(ctx)}web3/${payPath}?order_no=${orderNo}`,
    cancel: window.location.href
  }
  obj.failure = obj.success

  return obj
}

export function asyncLoadScript(url, callback) {
  const script = document.createElement('script')
  script.type = 'text/javascript'
  if (script.readyState) { // IE
    script.onreadystatechange = function () {
      // IE状态码变成complete或者loaded，表示该元素加载完
      if (script.readyState == 'complete' || script.readyState == 'loaded') {
        callback()
      }
    }
  } else {
    // 非IE
    script.onload = function () {
      callback()
    }
  }
  script.src = url
  document.head.appendChild(script)
}



// export function isGuestCheckout() {
// import Cookie from 'cookie'
//   // 当前阶段不支持 guest 下单
//   // white label 配置
//   const whiteLabelUtilConfig = KLK_PAGE_DATA._util_config.whiteLabelUtilConfig
  
//   const guest = isPrdEnv
//     ? window.location.host === 'guest.klook.com'
//     : (Cookie.parse(document.cookie) || {}).util_type == 'guest_checkout'
//   return guest || (
//     whiteLabelUtilConfig &&
//     whiteLabelUtilConfig.login_ways &&
//     whiteLabelUtilConfig.login_ways.includes('guest')
//   )
// }

// cap.klook.com，guest.klook.com等换了产线域名的需要替换接口返回host，跳转链接host等，防止跳到主站

  // 对于域名不是www.klook.com的 要跳转到当前的host
