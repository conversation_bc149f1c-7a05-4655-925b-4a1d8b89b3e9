/* eslint-disable */
import Logquery from '@klook/logquery'

/**
 * @description 日志上报 - 支付团队统一处理
 * @param params
 */
export const paymentLogQuery = (params) => {
  try {
    const { platform, keplerId } = window.__KLOOK__?.state?.klook || {}
    const logquery = new Logquery({
      url: process.env.LOGQUERY_URL,
      headers: { 'X-Platform': platform },
      queue: { interval: 5000, size: 15 }
    })

    if (typeof params === 'object') {
      const actionList = ['页面渲染', '接口异常', '数据异常', 'SDK网关异常', '日常日志']
      const messageObj = {
        action: params.action,
        userId: window.__KLOOK__?.state?.auth?.user?.globalId,
        message: params.message,
        kepler_id: keplerId,
        request_id: params.requestId || undefined,
        api_name: params.api_name || undefined,
        action_desc: actionList[params.action - 1],
        order_no: params.order_no || undefined,
        file_name: params.file_name || undefined,
        error_msg: params.error_msg || undefined,
        url: window.location.href || undefined
      }
      // 若存在请求数据源，需要对请求数据源进行格式化处理
      // if (params.req_data) {
      // 因为有token等敏感信息, 先注释不上传
      // messageObj['req_data'] = typeof params.req_data === 'object' ? JSON.stringify(params.req_data) : params.req_data
      // }
      // 消息体
      const messageInfo = {
        Klook_payment: messageObj
      }
      logquery.service({
        isMasked: true,
        timestamp: Date.now(),
        level: (params.action === 5) ? 'I' : (params.level || 'E'),
        message: JSON.stringify(messageInfo)
      })
    }
  } catch (error) {
    const errorMsg = getErrorInfo(error)
    logquery.service({
      isMasked: true,
      timestamp: Date.now(),
      level: params.level || 'E',
      message: JSON.stringify({
        action: 3,
        error_msg: errorMsg,
        message: `日志上报内部处理时发生异常，传递的参数信息:${JSON.stringify(params)}`,
        action_desc: '数据异常'
      })
    })
  }
}

/**
 * 获取错误类型
 * @param {*} error
 * @returns
 */
export const getErrorInfo = (error) => {
  const errorType = Object.prototype.toString.call(error).slice(8, -1).toLocaleLowerCase()
  return errorType === 'error'
    ? error.stack
    : errorType === 'object'
      ? JSON.stringify(error)
      : error
        ? error.toString()
        : ''
}
