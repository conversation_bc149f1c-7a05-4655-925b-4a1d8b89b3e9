/* eslint-disable */
import assign from 'lodash/assign'
import { getRiskDeepKnowIds, initRiskDeepKnow } from '@klook/risk-deepknow-sdk'
import { changeXHosts } from './utils/changeXHosts'
import { isPrdEnv, isMobile, isCnSite, getLangPath, jumpOrReload, enterExecuteSuccessPage, offbeforeunload } from './utils'
import { bffSubmit } from '../../assets/apis'
import {
  adyenPay,
  braintreePay,
  cybersourcePay,
  stripePay,
  nttDatapay,
  yunshanfuDatapay,
  lianlianPay,
  ntt3ds2Pay,
  checkoutPay,
  nttDataCafisPay
} from './credit-card-pay/index'
import { redirectPay } from './redirect-pay'
import { wechatPay } from './wechat-pay'
import { afteePay } from './aftee-pay'
import { applePay } from './apple-pay'
import { walletAgreementPay } from './wallet_agreement'
import { straightPay } from './straight-pay'
import { signedPay } from './signed-pay'
import { postForm } from './post-form'
import {
  postCreditCardIframeMessage,
  checkValidOrigin,
  getCardErrorTips,
  getCreditCardIframeElement
} from './utils/pci'
import { paymentLogQuery } from './utils/logQuery'
import { payMePay } from './pay_me_pay'
import { isSupportDesktopQrPay, qrPay } from './qr-pay/qr-pay'
import { payNowPay } from './paynow-pay'
import { payAanonePay } from './pay_anyone_pay'
import { payLahPay } from './paylah-pay'
import { gcash } from './gcash'

let isInitRiskDeepKnow = false

const requestNewCardToken = (submitResult, ctx) => {
  const sdkType = submitResult.action.action_details.native && submitResult.action.action_details.native.sdk_type
  const channelType = submitResult.action.action_details.native && submitResult.action.action_details.native.channel_type

  const targetSelector = channelType === 'unionpay' ? '.j-iframe_union_card' : '.j-iframe_credit_card'
  const iframeElement = getCreditCardIframeElement(targetSelector)
  if (!iframeElement) {
    throw new Error('no pci iframe element')
  }

  postCreditCardIframeMessage({
    type: 'requestNewCardToken',
    props: {
      sdkType,
      publicKey: submitResult.action.action_details.native.public_key,
      clientToken: submitResult.action.action_details.native.client_token,
      native: submitResult.action.action_details.native,
      generationTime: window.iso_time,
      isProdEnv: isPrdEnv,
      ...getCardErrorTips.call(ctx)
    }
  }, iframeElement)
}

const creditCardPayConfig = {
  braintree: braintreePay,
  cybersource: cybersourcePay, // 没有新卡
  stripe: stripePay,
  checkout: checkoutPay,
  adyen: adyenPay,
  requestNewCardToken,
  ntt_data: nttDatapay,
  ntt_3ds2: ntt3ds2Pay,
  ntt_data_cafis: nttDataCafisPay
}

const creditCardContinuePayConfig = {
  braintree: braintreePay,
  stripe: stripePay,
  adyen: adyenPay,
  checkout: checkoutPay,
  ntt_data: nttDatapay,
  ntt_3ds2: ntt3ds2Pay,
  ntt_data_cafis: nttDataCafisPay,
  cybersource: cybersourcePay,
  creditcard_lianlianpay: lianlianPay
}


const generalPayConfig = {
  applepay_adyen: applePay,
  applepay_ntt_data: applePay,
  applepay_checkout: applePay,
  wechat: wechatPay,
  wallet_agreement: walletAgreementPay,
  ntt_data: nttDatapay,
  ntt_3ds2: ntt3ds2Pay,
  yunshanfu: yunshanfuDatapay,
  postForm,
  payme_hsbc: payMePay,
  aftee: afteePay,
  payNow: payNowPay,
  payAanonePay,
  gcash,
  payLahPay,
  creditcard(type) {
    return creditCardPayConfig[type]
  },
  redirect(submitResult, ctx) {
    redirectPay(submitResult, {
      onSuccess: ctx.$emit.bind(ctx, 'success'),
      onError: ctx.$emit.bind(ctx, 'error'),
    })
  },

  qr_pay(submitResult, ctx) {
    qrPay.call(ctx, submitResult, {
      onCancel: ctx.$emit.bind(ctx, 'cancel'),
      onError: ctx.$emit.bind(ctx, 'error'),
      onQuery: (url, params) => ctx.$axios.$get(url, { params }), 
      onLogQuery: paymentLogQuery,
      onEnterBookingPage() {
        ctx.$emit('success')
        jumpOrReload(getLangPath.call(ctx, 'bookings'))
      },
      onSuccess() {
        ctx.$emit('success')
        offbeforeunload()
        enterExecuteSuccessPage.call(ctx, submitResult.order_no)
      },
    })
  }
}

let receiveNewCardTokenMessage

/**
 * @description 获取具体支付方法
 * @returns 返回具体支付方法
 * @param submitResult
 */
const getPaymentMethod = (submitResult, ctx) => {
  const { payment_gateway: paymentGateway, action, token } = submitResult
  let sdkType = action.action_details.native && action.action_details.native.sdk_type
  console.log('sdkType', sdkType)
  console.log('paymentGateway', paymentGateway)
  // 检测是否为 POST
  let isMethodPost
  // 检查是否json类型数据
  let isApplicationJson
  try {
    isMethodPost = action.action_details.redirect.method.toUpperCase() === 'POST'
    isApplicationJson = action.action_details.redirect.content_type === 'application/json'
  } catch {
    isMethodPost = false
    isApplicationJson = false
  }

  if (sdkType === 'gcash_miniprogram') {
    return generalPayConfig.gcash
  }
  // 如果是 POST && From 提交，直接到表单处理工具类,兼容后台字段content_type为空,默认也是表单提交
  if (isMethodPost && !isApplicationJson) {
    return generalPayConfig.postForm
  }
  if (action.type === 'straight') {
    return straightPay
  }
  if (action.type === 'signed') {
    return signedPay
  }
  if (sdkType === 'creditcard_lianlianpay') {
    return creditCardContinuePayConfig[sdkType]
  }

  if (!isMobile.call(ctx) && isSupportDesktopQrPay(paymentGateway)) {
    return generalPayConfig.qr_pay
  }

  // desktop 的 wechatpay 和 wechatpay_ntt_data 走 的是qr_pay
  if (paymentGateway === 'wechatpay' || paymentGateway === 'wechatpay_ntt_data') {
    return generalPayConfig.wechat
  }
  if (paymentGateway === 'aftee_aftee') {
    return generalPayConfig.aftee
  }
  if (paymentGateway === 'paynow_paynow') {
    return generalPayConfig.payNow
  }

  if (paymentGateway === 'payme_hsbc') {
    return generalPayConfig.payme_hsbc
  }

  if (paymentGateway === 'payanyone_paynow') {
    return generalPayConfig.payAanonePay
  }

  if (paymentGateway === 'paylah_dbs') {
    return generalPayConfig.payLahPay
  }

  if (sdkType) {
    const isCreditcard = (paymentGateway) => {
      const creditcardList = ['creditcard_ntt_data', 'ntt_data']
      return creditcardList.includes(paymentGateway)
    }
    if (['creditcard'].some(x => paymentGateway.includes(x)) || isCreditcard(paymentGateway)) {
      if (!token) {
        if (receiveNewCardTokenMessage) {
          window.removeEventListener('message', receiveNewCardTokenMessage, false)
        }
  
        receiveNewCardTokenMessage = (event) => {
          // 我们能信任信息来源吗？
          if (!checkValidOrigin(event.origin)) {
            return
          }
        
          const data = event.data
        
          if (data.type === 'generateNewCardToken') {
            let { sdkType, tokenResp } = data.props
            if (sdkType === 'ntt_data') {
              if (submitResult.action.action_details.native.auth_type === '2') {
                sdkType = 'ntt_3ds2'
              } else if (submitResult.action.action_details.native.auth_type === '3') {
                sdkType = 'ntt_data_cafis'
              }
            }
        
            if (tokenResp.success) {
              window.removeEventListener('message', receiveNewCardTokenMessage, false)
              const { token } = tokenResp.result
              creditCardContinuePayConfig[sdkType](assign(submitResult, { token }), ctx)
            } else {
              const { code, message } = tokenResp.result
              ctx.$emit('error', {
                code,
                message
              }, {
                module: 'pci-generate-newcard-token',
                action: 'ui',
                path: 'at <jsapi.js>: receiveNewCardTokenMessage',
                error_type: 'sdk-error',
                code,
                message: '请求PCI获取token 失败， errMessage：' + message
              })
              window.removeEventListener('message', receiveNewCardTokenMessage, false)
            }
          }
        }
        window.addEventListener('message', receiveNewCardTokenMessage, false)

        console.log('method!!!: new card')
        return generalPayConfig.creditcard('requestNewCardToken')
      }

      if (sdkType === 'ntt_data') {
        if (submitResult.action.action_details.native.auth_type === '2') {
          sdkType = 'ntt_3ds2'
        } else if (submitResult.action.action_details.native.auth_type === '3') {
          sdkType = 'ntt_data_cafis'
        }
      }
      console.log('method!!!: creditcard')
      return generalPayConfig.creditcard(sdkType)
    } else {
      console.log('method!!!: sdktype', sdkType)
      return generalPayConfig[sdkType]
    }
  }

  /*
    payment_sdk/utils redirectPaymentGatewayList
     */
  console.log('method!!!: redirect')
  return generalPayConfig.redirect
}

const getSubmitData = (data, platform) => {
    const {
      method_key, token, order_no, credit_card_info, terms_accepted, return_url,
      merchant_id, user_pay_currency, user_total_pay_price, asset_voucher_code, save_token, coupon_code, phone_number, payment_asset_no, install_payment_plan, asset_voucher_code_list
    } = data
    const {
      forterToken = '',
      kountSessionId = ''
    } =  isInitRiskDeepKnow
      ? getRiskDeepKnowIds({
        isPrdEnv,
        platform
      })
      : {}

    return {
      method_key,
      token,
      order_no,
      payment_asset_no,
      credit_card_info: credit_card_info ? {
        kvalue: credit_card_info.kvalue,
        bin: credit_card_info.bin,
        issuer_bin: credit_card_info.issuer_bin,
        last4: credit_card_info.last4,
        is_new: true,
        save_card: credit_card_info.save_card,
        telephone_number: credit_card_info.telephone_number || undefined,
        postal_code: credit_card_info.postal_code
      } : null,
      terms_accepted,
      return_url: return_url || null,
      merchant_id: merchant_id || null,
      pay_price: {
        currency: user_pay_currency,
        amount: user_total_pay_price
      },
      // 废弃了
      // promo_code: promo_code || null,
      asset_voucher_code: asset_voucher_code || null,
      save_token: save_token || null,
      customized_data: {
        forter_token: forterToken || '',
        kount_session_id: kountSessionId || ''
      },
      coupon_code: coupon_code || null,
      phone_number: phone_number || null,
      install_payment_plan: install_payment_plan || null,
      asset_voucher_code_list: asset_voucher_code_list || []
    }
}

/*
reqData {
{
    "checkoutXKlookReqId": "", 请求头自定义字段，方便追踪问题
    "method_key": "xxx", //支付方式,笼统的概念
    "token":"xxx", //旧信用卡 token,
    "order_no": "03203494", //订单号,string
    "credit_card_info":{ //新信用卡相关信息
      "kvalue":"", //加密卡号
      "bin":"",//信用卡号前六
      "last4":""//信用卡号后四
        "number": //完整信用卡号, pci 不传
        "cvv": //信用卡 cvv， pci 不传
        "expirationMonth": //截止有效期月份, pci 不传
        "expirationYear"： //截止有效期年份， pci 不传
        "save_card": //是否保存卡片
        telephone_number: '+85211112222'// union pay 二期需要传
    },
    user_total_pay_price: //支付金额
    user_pay_currency: //支付货币
    payment_deadline: //支付截止  废弃
    first_name: //名
    family_name: //姓
    traveller_email: //邮件地址
    country_code: //travel_country //mobile apple pay 需要国家码 'US'
    "terms_accepted": "accepted" //是否同意条款,字段命名待确定accepted,refused
    return_url: { //自定义中转页面链接
    success: 'https://',
    cancel: '',
    failure: '',
    }
}
}
 */
/**
 * 
 * @param reqData 提交的参数
 * @param ctx 当前klkPayment Vue 实例， 后续所有支付流程都会传递到
 */
export default async function submit (reqData, ctx = this) {
  if (!ctx || !ctx.$axios) {
    throw new Error('jsapi.js-submit运行时错误，没有ctx 或 ctx没有axios实例')
  }

  const klook = (ctx?.$store || window.__KLOOK__)?.state?.klook || {}
  const platform = klook.platformMp || klook.platform

  // klook.cn 不需要风控
  if (!isInitRiskDeepKnow && !isCnSite(ctx)) {
    try {
      isInitRiskDeepKnow = await initRiskDeepKnow({ isPrdEnv, platform })
    } catch(err) {
      isInitRiskDeepKnow = false
      paymentLogQuery({
        action: 2,
        api_name: 'initRiskDeepKnow',
        order_no: orderNo,
        file_name: 'at <jsapi.js>: submit-initRiskDeepKnow',
        requestId: reqData.checkoutXKlookReqId,
        message: `风控SDK初始化失败，错误信息：${JSON.stringify(err)}`
      })
      // 风控SDK初始化失败，不影响支付流程
    }
  }

  let submitReq

  try {
    submitReq = getSubmitData(reqData, platform)
  } catch {
    return ctx.$emit('error', null, {
      module: 'getSubmitData',
      action: 'ui',
      path: 'at <jsapi.js>: getSubmitData',
      message: '处理submit请求参数发生异常'
    })
  }


  const resp = await ctx.$axios.$post(
    bffSubmit,
    submitReq,
    {
      tagName: submitReq.method_key,
      headers: changeXHosts({ 'X-Klook-Request-Id': reqData.checkoutXKlookReqId })
    }
  ).catch((error) => ({ error }))

  if (resp.success) {
    let result = resp.result
    result = assign(result, reqData)
    const paymentMethod = getPaymentMethod(result, ctx)

    paymentMethod(result, ctx)
  } else {
    ctx.$emit('error', resp.error, {
      module: 'cashier-submit',
      action: 'api',
      path: 'at <jsapi.js>: submit',
      code: resp.error?.code,
      message: resp.error?.message
    })
  }
}
