import axios from 'axios'
import { Dialog } from '@klook/klook-ui/lib/modal'
import assign from 'lodash/assign'
import { getErrorInfo } from './utils/logQuery'

import {
  isPrdEnv,
  enterExecuteSuccessPage,
  execute,
  offbeforeunload,
  getExecuteBaseData,
  isCreatedStatus
} from './utils'

/**
  applepay javascript 官方文档 https://developer.apple.com/documentation/apple_pay_on_the_web/applepaysession
  Apple pay 证书替换  https://klook.larksuite.com/wiki/Kf8rwiIL7ilNNYkBENSuLJbRsEf
*/

const getExecuteData = (data) => {
  return assign(getExecuteBaseData(data), {
    modulePath: 'at <apple-pay.js>: session.onpaymentauthorized-execute',
    payment_details: {
      token: data.token
    }
  })
}

export const applePay = async (submitResult, ctx) => {
  await Dialog.alert(ctx.$t('wechat.continue_btn'), {
    okLabel: ctx.$t('global.tips.okTxt'),
    buttonAlign: 'block',
    zIndex: 4000,
    beforeClose(result, _instance, done) {
      result && done()
    }
  })

  // 走到这里一定是支持applePay 的， submitResult.action_details.native已在submit阶段判断过，一定存在
  const {
    country_code: countryCode = 'HK',
    sdk_type,
    public_key
  } = submitResult.action.action_details.native

  let session
  try {
    session = new window.ApplePaySession(3, {
      countryCode,
      currencyCode: submitResult.user_pay_currency,
      supportedNetworks: ['visa', 'masterCard', 'amex', 'discover'],
      merchantCapabilities: ['supports3DS'],
      total: { label: 'Klook Travel', amount: submitResult.user_total_pay_price }
    })
    session.begin()
  } catch (error) {
    return ctx.$emit('error', null, {
      module: 'apple-pay',
      action: 'sdk',
      path: 'at <applepay.js>: new window.ApplePaySession',
      message: '错误信息：' + getErrorInfo(error)
    })
  }

  session.oncancel = () => {
    ctx.$emit('cancel', null, {
      module: 'apple-pay',
      action: 'ui',
      path: 'at <apple-pay.js>: session.oncancel',
      message: 'apple-pay支付失败，用户取消支付'
    })
    offbeforeunload()
    window.location.reload()
  }

  session.onvalidatemerchant = async (event) => {
    const resp = await ctx.$axios
      .$post('/v1/gatewayserv/applepay/web/validation', {
        validation_url: event.validationURL,
        sdk_type
      })
      .catch(error => ({ error }))

    if (!resp || !resp.success || !resp.result) {
      // 关闭applepay 界面
      session.abort()
      const { message = 'NA', code = 'NA' } = resp?.error || {}
      return ctx.$emit('error', resp.error, {
        module: 'gateway-validation',
        action: 'api',
        path: 'at <apple-pay.js>: session.onvalidatemerchant-post<validation>',
        code,
        message: '验证applePay网关失败，errMessage：' + message + '，submitResult：' + JSON.stringify(submitResult || {})
      })
    }

    session.completeMerchantValidation(JSON.parse(resp.result))
  }

  session.onpaymentauthorized = async (evt) => {
    try {
      const { paymentData } = evt.payment.token
      if (sdk_type === 'applepay_checkout') {
        // checkout 网关需要把applepay 的token 兑换成 checkout 的token
        const response = await axios.post(
          `https://api.${isPrdEnv ? '' : 'sandbox.'}checkout.com/tokens`,
          {
            type: 'applepay',
            token_data: paymentData
          },
          {
            headers: {
              Authorization: public_key,
              'Content-Type': 'application/json'
            }
          }
        )
        submitResult.token = response && response.data && response.data.token
      } else {
        submitResult.token = btoa(JSON.stringify(paymentData))
      }
    } catch {}

    if (!submitResult.token) {
      // 关闭applepay 界面
      session.abort()
      return ctx.$emit('error', null, {
        module: 'apple-pay',
        action: 'ui',
        path: 'at <apple-pay.js>: session.onpaymentauthorized',
        message: `apple-pay 获取 ${sdk_type} 的token 失败`
      })
    }

    execute(ctx, getExecuteData(submitResult), null, null, (resp) => {
      // execute finally callback
      if (resp.success && resp.result && !resp.result.gateway_extra_info) {
        session.completePayment(window.ApplePaySession.STATUS_SUCCESS)
        ctx.$emit('success')
        if (!isCreatedStatus(resp.result.invoice_status)) {
          offbeforeunload()
          enterExecuteSuccessPage(
            resp.result.order_no,
            submitResult.return_url && submitResult.return_url.success
          )
        } // else {} 异步支付结果，在execute内部会抛出999988错误码， 之后会引导用户到订单列表页
      } else {
        // 关闭applepay 界面, 错误状态已经在execute内部上报了
        session.abort()
      }
    })
  }
}
