import { offbeforeunload, execute, enterExecuteSuccessPage, getExecuteBaseData } from './utils'

const getExecuteData = (data) => {
  return Object.assign({ modulePath: 'at <straight-pay.js>: straightPay' }, getExecuteBaseData(data))
}

export const straightPay = (submitResult, ctx) => {
  execute(ctx, getExecuteData(submitResult), (executeResult) => {
    offbeforeunload()
    enterExecuteSuccessPage(executeResult.order_no, submitResult.return_url && submitResult.return_url.success)
  })
}
