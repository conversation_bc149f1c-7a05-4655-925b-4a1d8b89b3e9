import assign from 'lodash/assign'
import { getErrorInfo } from './utils/logQuery'
import { offbeforeunload, enterExecuteSuccessPage, execute, getExecuteBaseData } from './utils'

export const walletAgreementPay = (submitResult, ctx) => {
  let reqData
  try {
    reqData = assign(getExecuteBaseData(submitResult), {
      modulePath: 'wallet_agreement.js',
      payment_details: {
        token: submitResult.action.action_details.native.agreement_token
      }
    })
  } catch (error) {
    const errorMsg = getErrorInfo(error)
    return ctx.$emit('error', null, {
      module: 'wallet_agreement',
      action: 'ui',
      path: 'at <wallet_agreement.js>: getExecuteBaseData',
      message: '错误信息：getExecuteData数据合并时发生异常，中断了进程。 errorMsg：' + errorMsg + '。submitResult：' + JSON.stringify(submitResult || {})
    })
  }

  execute(ctx, reqData, (executeResult) => {
    offbeforeunload()
    ctx.$emit('success')
    enterExecuteSuccessPage(executeResult.order_no, submitResult.return_url && submitResult.return_url.success)
  })
}
