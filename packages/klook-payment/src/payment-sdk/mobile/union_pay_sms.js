import Vue from 'vue'
import treeDsSms from './3dsSms.vue'

const TreeDsSmsConstructor = Vue.extend(treeDsSms)

export const open3dsSms = (options, ctx) => {
  let treeDsSmsInstance = new TreeDsSmsConstructor({
    parent: ctx
  }).$mount()

  document.body.appendChild(treeDsSmsInstance.$el)

  const { phone, onConfirm, onClose } = options

  treeDsSmsInstance.phone = phone
  treeDsSmsInstance.visible = true

  treeDsSmsInstance.$on('confirm', (code) => {
    treeDsSmsInstance.$emit('close')
    typeof onConfirm === 'function' && onConfirm(code)
    treeDsSmsInstance.code = ''
  })

  treeDsSmsInstance.$on('close', () => {
    treeDsSmsInstance.visible = false
    treeDsSmsInstance.code = ''
    typeof onClose === 'function' && onClose()

    document.body.removeChild(treeDsSmsInstance.$el)
    treeDsSmsInstance.$destroy()
    treeDsSmsInstance = null
  })
}
