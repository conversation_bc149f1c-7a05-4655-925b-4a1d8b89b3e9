<template>
  <klk-modal
    width="400px"
    :title="$t('msgValidate_title1')"
    :overlay-closable="false"
    title-align="center"
    closable
    button-align="center"
    :show-default-footer="false"
    :open="visible"
    @on-close="$emit('close')"
  >
    <p style="font-size: 16px" v-html="msgValidate_desc"></p>
    <div class="klk-payment--modal-validate-content">
      <klk-input
        v-model.trim="code"
        type="text"
        :placeholder="$t('msgValidate_code')"
        class="g_row1 m_radius_box t18 t_mid"
        :maxlength="6"
        @input="resetBtnText"
      />
      <div v-if="showToast" class="credit-card-tips-toast">
        {{ toastMsg }}
      </div>
      <klk-button
        v-show="confirmBtnText"
        :loading="loading"
        :disabled="disabled"
        @click="validateCodeInfo"
      >
        <b v-html="confirmBtnText"></b>
      </klk-button>
    </div>
  </klk-modal>
</template>

<script>
import KlkModal from '@klook/klook-ui/lib/modal'
import KlkInput from '@klook/klook-ui/lib/input'
import KlkButton from '@klook/klook-ui/lib/button'

export default {
  name: 'VerificationDialog',
  components: {
    KlkModal,
    KlkInput,
    KlkButton
  },
  data() {
    return {
      testForm: null,
      disabled: true,
      code: '',
      visible: false,
      phone: '',
      msg: '',
      dataInfo: '',
      lessTime: 60,
      loading: false,
      interval: null,
      confirmBtnText: this.$t('sign_resend_code'),
      toastMsg: '',
      showToast: false,
      showCountTime: false
    }
  },
  computed: {
    msgValidate_desc() {
      return this.phone
      // eslint-disable-next-line quotes
        ? this.$t('sms_verify_code_subtitle').replace(`<span class='f18'><b class='j_msgValidate_tel'></b></span>`, `<b style="font-size:18px">${this.phone}</b>`)
        : ''
    }
  },
  watch: {
    code() {
      this.checkCodeLength()
      this.resetBtnText()
    },
    showCountTime(val) {
      if (val) {
        this.init()
      }
    }
  },
  methods: {
    /**
     * @description 初始化执行文本替换和倒计时处理
     */
    init() {
      this.confirmBtnText = this.$t('msgValidate_resend')
      setTimeout(() => {
        const elements = document.querySelectorAll('.t_white #v_countDown')
        elements.forEach((element) => {
          element.textContent = `(${this.lessTime})`
        })
      }, 10)
      this.countLessTime()
    },
    /**
     * @description 点击验证|重新发送验证码，触发回传
     */
    validateCodeInfo() {
      if (this.disabled) { return }
      this.loading = true
      if (this.code.trim().length > 0) {
        this.$emit('execute', this.code.trim())
      } else {
        this.$emit('submit')
        this.lessTime = 60
        this.disabled = true
        this.countLessTime()
      }
    },
    /**
     * @description 执行倒计时
     */
    countLessTime() {
      if (this.interval) {
        return
      }
      this.interval = setInterval(() => {
        if (this.lessTime === 1) {
          this.lessTime = 60
          this.disabled = false
          clearInterval(this.interval)
          this.interval = null
        }
        this.resetBtnText()
        this.lessTime--
      }, 1000)
    },
    /**
     * @description 重置按钮文本信息
     */
    resetBtnText() {
      if (this.code.trim().length > 0) {
        this.confirmBtnText = this.$t('sign_verify')
        this.disabled = false
      } else if (this.interval) {
        this.disabled = true
        this.confirmBtnText = this.$t('msgValidate_resend')
        const elements = document.querySelectorAll('.t_white #v_countDown')
        elements.forEach((element) => {
          element.textContent = `(${this.lessTime})`
        })
      } else {
        this.disabled = false
        this.confirmBtnText = this.$t('sign_resend_code')
      }
    },
    /**
     * @description 显示提示信息弹窗
     * @param msg
     */
    showMsgInfo(msg) {
      this.showToast = true
      this.toastMsg = msg
      setTimeout(() => {
        this.showToast = false
        this.toastMsg = ''
      }, 2000)
    },
    /**
     * @description 关闭弹窗
     */
    async closeDialog() {
      await clearInterval(this.interval)
      this.$emit('cancel')
    },
    /**
     * @description 检测验证码长度，并做处理
     */
    checkCodeLength() {
      if (this.code.trim().length > 6) {
        this.code = this.code.trim().slice(0, this.code.length - 1)
      }
    },
    /**
     * @description 设置Toast文本，并展示Toast
     * @param label
     */
    setToastLabel(label) {
      this.showMsgInfo(this.$t(`${label}`) + this.phone)
    }
  }
}
</script>
