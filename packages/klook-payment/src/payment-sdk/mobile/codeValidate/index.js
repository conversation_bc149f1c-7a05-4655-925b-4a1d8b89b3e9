import Vue from 'vue'
import codeValidateDialog from './dialog.vue'

let VM = null

export const openDialog = (options = {}, ctx) => {
  if (!VM) {
    const DialogConstructor = Vue.extend(codeValidateDialog)
    const el = document.createElement('div')
    document.body.appendChild(el)
    VM = new DialogConstructor({
      parent: ctx
    }).$mount(el)
  }
  const { phone, onExecute, onSubmit, onCancel, showCountTime, disabled } = options
  VM.visible = true
  VM.lessTime = 60
  VM.phone = phone
  VM.showCountTime = showCountTime
  VM.disabled = disabled
  VM.$off('execute')
  VM.$off('submit')
  VM.$off('cancel')
  // 点击验证，将验证码回传到业务，进行验证码校验及执行支付
  VM.$on('execute', (code) => {
    typeof onExecute === 'function' && onExecute(code)
    VM.loading = false
  })
  // 重新发送验证码，回传事件到业务方，进行重新提交订单
  VM.$on('submit', () => {
    typeof onSubmit === 'function' && onSubmit()
    VM.loading = false
  })
  // 点击关闭弹窗按钮，将事件回传到业务方
  VM.$on('cancel', () => {
    typeof onCancel === 'function' && onCancel()
  })
}
/**
 * @description 清除验证码
 */
export const cleanCode = () => {
  VM.code = ''
}
/**
 * @description 展示Toast提示
 * @param msg
 */
export const showToast = (msg) => {
  VM.showMsgInfo(msg)
}
/**
 * @description 关闭弹窗
 */
export const closeDialog = () => {
  VM.code = ''
  VM.interval = null
  VM.showCountTime = false
  VM.visible = false
}
/**
 * @description 切换按钮形态
 * @param disabled
 */
export const toggleBtnStatus = (disabled) => {
  VM.disabled = disabled
}
/**
 * @description 设置弹窗文本
 * @param label
 */
export const setToastLabel = (label) => {
  VM.setToastLabel(label)
}
