<template>
  <klk-modal
    width="400px"
    :title="$t('msgValidate_title1')"
    :overlay-closable="false"
    title-align="center"
    closable
    button-align="center"
    :show-default-footer="false"
    :open="visible"
    @on-close="$emit('close')"
  >
    <p style="font-size:16px" v-html="msgValidate_desc"></p>

    <div class="klk-payment--modal-validate-content">
      <klk-input
        v-model.trim="code"
        type="tel"
        :placeholder="$t('msgValidate_code')"
      />
      <klk-button
        :disabled="disabled"
        @click="emit('confirm', code)"
      >
        {{ $t('global.tips.okTxt') }}
      </klk-button>
    </div>
  </klk-modal>
</template>

<script>
import KlkModal from '@klook/klook-ui/lib/modal'
import KlkInput from '@klook/klook-ui/lib/input'
import KlkButton from '@klook/klook-ui/lib/button'

export default {
  name: 'TreeDsSms',
  components: {
    KlkModal,
    KlkInput,
    KlkButton
  },
  data() {
    return {
      disabled: true,
      phone: '',
      code: '',
      visible: false
    }
  },
  computed: {
    msgValidate_desc() {
      return this.phone
      // eslint-disable-next-line quotes
        ? this.$t('sms_verify_code_subtitle').replace(`<span class='f18'><b class='j_msgValidate_tel'></b></span>`, `<b style="font-size:18px">${this.phone}</b>`)
        : ''
    }
  },
  watch: {
    visible() {
      this.loading = false
      this.$nextTick(() => {
        const elements = document.querySelectorAll('.threeDsSms .j_msgValidate_tel')
        elements.forEach((element) => {
          element.textContent = this.phone
        })
      })
    },
    code(val = '') {
      this.disabled = !val.length
    }
  },
  mounted() {
  },
  methods: {
    confirm() {
      this.loading = true
      this.$emit('confirm', this.code)
    }
  }
}
</script>
