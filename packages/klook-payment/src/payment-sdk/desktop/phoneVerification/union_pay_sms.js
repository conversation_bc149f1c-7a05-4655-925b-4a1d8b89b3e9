import Vue from 'vue'
import VerificationDialog from './VerificationDialog.vue'

let verificationVm = null

export const open3dsSms = (options = {}, ctx) => {
  if (!verificationVm) {
    const DialogConstructor = Vue.extend(VerificationDialog)
    const el = document.createElement('div')
    document.body.appendChild(el)
    verificationVm = new DialogConstructor({
      parent: ctx
    }).$mount(el)
  }

  const { phone, onConfirm, onClose } = options

  verificationVm.visible = true
  verificationVm.phone = phone

  verificationVm.$off('on-ok')
  verificationVm.$off('on-close')

  verificationVm.$on('on-ok', (verificationCode) => {
    verificationVm.verificationCode = ''
    typeof onConfirm === 'function' && onConfirm(verificationCode)
  })

  verificationVm.$on('on-close', () => {
    verificationVm.verificationCode = ''
    typeof onClose === 'function' && onClose()
  })
}
