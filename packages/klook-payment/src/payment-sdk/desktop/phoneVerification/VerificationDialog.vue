<template>
  <klk-modal
    :title="$t('msgValidate_title1')"
    title-align="center"
    button-align="block"
    :z-index="4000"
    :closable="true"
    :show-default-footer="false"
    :open.sync="visible"
    @close="handleCancel"
  >
    <div class="verification-body">
      <div class="verification-body_description">
        <p v-html="msgValidate_desc"></p>
      </div>
      <klk-input v-model.trim="verificationCode" class="verification-body_input"></klk-input>
      <div :class="['verification-body_btn', {disabled}]" @click="handleOk">
        <span>{{ $t('global.tips.okTxt') }}</span>
      </div>
    </div>
  </klk-modal>
</template>

<script>
import KlkModal from '@klook/klook-ui/lib/modal'
import KlkInput from '@klook/klook-ui/lib/input'

export default {
  name: 'VerificationDialog',
  components: {
    KlkModal,
    KlkInput
  },
  data() {
    return {
      verificationCode: '',
      visible: false,
      phone: ''
    }
  },
  computed: {
    disabled() {
      return !this.verificationCode
    },
    msgValidate_desc() {
      return this.phone
      // eslint-disable-next-line quotes
        ? this.$t('sms_verify_code_subtitle').replace(`<span class='f18'><b class='j_msgValidate_tel'></b></span>`, `<b style="font-size:18px">${this.phone}</b>`)
        : ''
    }
  },
  methods: {
    handleCancel() {
      this.visible = false
      this.$emit('on-close')
    },
    handleOk() {
      if (this.disabled) { return }

      this.$emit('on-ok', this.verificationCode)
      this.visible = false
    }
  }
}
</script>

<style lang="scss">
.verification-body_description {
  margin-bottom: 8px;
  line-height: 24px;
  text-align: center;
}

.verification-body_input {
  margin-top: 16px;
}

.verification-body_btn {
  background-color: $color-brand-primary;
  cursor: pointer;
  opacity: $opacity-hover;
  height: 40px;
  margin-top: 10px;
  width: 100%;
  line-height: 40px;
  text-align: center;
  border-radius: $radius-m;
  color: $color-text-primary-onDark;
  font-weight: $fontWeight-bold;
  font-size: $fontSize-body-l;

  &.disabled {
    background-color: rgb(204, 204, 204);
    cursor: no-drop;
    opacity: $opacity-hover;
  }
}
</style>
