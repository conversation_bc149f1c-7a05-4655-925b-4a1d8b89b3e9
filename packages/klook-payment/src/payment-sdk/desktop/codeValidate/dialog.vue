<template>
  <klk-modal
    :title="$t('msgValidate_title1')"
    title-align="center"
    button-align="block"
    :closable="true"
    :show-default-footer="false"
    :open.sync="visible"
    :z-index="4000"
    @close="closeDialog"
  >
    <div class="verification-body">
      <div class="verification-body_description">
        <p v-html="msgValidate_desc" />
      </div>
      <klk-input v-model.trim="code" :maxlength="6"></klk-input>
      <div :class="['verification-body_btn', { disabled }]" @click="validateCodeInfo">
        <span v-html="confirmBtnText"></span>
      </div>
      <div v-if="showToast" class="credit-card-tips-toast">
        {{ toastMsg }}
      </div>
    </div>
  </klk-modal>
</template>

<script>
import KlkModal from '@klook/klook-ui/lib/modal'
import KlkInput from '@klook/klook-ui/lib/input'

export default {
  name: 'VerificationDialog',
  components: {
    KlkModal,
    KlkInput
  },
  data() {
    return {
      disabled: true,
      code: '',
      visible: false,
      phone: '',
      msg: '',
      dataInfo: '',
      lessTime: 60,
      interval: null,
      confirmBtnText: '',
      toastMsg: '',
      showToast: false,
      showCountTime: false
    }
  },
  computed: {
    msgValidate_desc() {
      return this.phone
      // eslint-disable-next-line quotes
        ? this.$t('sms_verify_code_subtitle').replace(`<span class='f18'><b class='j_msgValidate_tel'></b></span>`, `<b style="font-size:18px">${this.phone}</b>`)
        : ''
    }
  },
  watch: {
    code() {
      this.checkCodeLength()
      this.resetBtnText()
    },
    showCountTime(val) {
      if (val) {
        this.init()
      }
    }
  },
  methods: {
    /**
     * @description 初始化执行文本替换和倒计时处理
     */
    init() {
      this.confirmBtnText = this.$t('msgValidate_resend')
      setTimeout(() => {
        const verificationBtn = document.querySelector('.verification-body_btn')
        const countDownElement = verificationBtn.querySelector('#v_countDown')
        countDownElement.textContent = `(${this.lessTime})`
      }, 10)
      this.countLessTime()
    },
    /**
     * @description 点击验证|重新发送验证码，触发回传
     */
    validateCodeInfo() {
      if (this.disabled) { return }
      if (this.code.trim().length > 0) {
        this.$emit('execute', this.code.trim())
      } else {
        this.$emit('submit')
        this.lessTime = 60
        this.disabled = true
        this.countLessTime()
      }
    },
    /**
     * @description 执行倒计时
     */
    countLessTime() {
      if (this.interval !== null) {
        return
      }
      this.interval = setInterval(() => {
        if (this.lessTime === 1) {
          this.lessTime = 60
          this.disabled = false
          clearInterval(this.interval)
          this.interval = null
        }
        this.resetBtnText()
        this.lessTime--
      }, 1000)
    },
    /**
     * @description 重置按钮文本信息
     */
    resetBtnText() {
      if (this.code.trim().length > 0) {
        this.confirmBtnText = this.$t('sign_verify')
        this.disabled = false
      } else if (this.interval) {
        this.disabled = true
        this.confirmBtnText = this.$t('msgValidate_resend')
        const verificationBtn = document.querySelector('.verification-body_btn')
        const countDownElement = verificationBtn.querySelector('#v_countDown')
        countDownElement.textContent = `(${this.lessTime})`
      } else {
        this.disabled = false
        this.confirmBtnText = this.$t('sign_resend_code')
      }
    },
    /**
     * @description Toast展示提示信息
     * @param msg
     */
    showMsgInfo(msg) {
      this.showToast = true
      this.toastMsg = msg
      setTimeout(() => {
        this.showToast = false
        this.toastMsg = ''
      }, 2000)
    },
    /**
     * @description 关闭弹窗
     * @param msg
     */
    async closeDialog() {
      await clearInterval(this.interval)
      this.$emit('cancel')
    },
    /**
     * @description 检测验证码长度，并做处理
     */
    checkCodeLength() {
      let num = this.code.trim()
      if (num.length > 6) {
        num = num.slice(0, num.length - 1)
      }
      setTimeout(() => {
        this.$set(this, 'code', num)
      }, 1)
    },
    /**
     * @description 设置Toast文本，并展示Toast
     * @param label
     */
    setToastLabel(label) {
      this.showMsgInfo(this.$t(`${label}`) + this.phone)
    }
  }
}
</script>

<style lang="scss">
.verification-body {
  .credit-card-tips-toast {
    position: absolute;
    color: $color-text-primary-onDark;
    font-size: $fontSize-body-s;
    line-height: 14px;
    padding: 10px 5px 10px;
    border-radius: $radius-s $radius-s $radius-s;
    z-index: 8888;
    background: $color-bg-overlay-black-desktop;
    top: 30%;
    left: 50%;
    transform: translate(-50%, 0);
    white-space: nowrap;
  }

  .verification-body_description {
    margin-bottom: 8px;
    line-height: 24px;
    text-align: center;
  }

  .verification-body_btn {
    background-color: $color-brand-primary;
    cursor: pointer;
    opacity: $opacity-hover;
    height: 40px;
    margin-top: 10px;
    width: 100%;
    line-height: 40px;
    text-align: center;
    border-radius: $radius-m;
    color: $color-text-primary-onDark;
    font-weight: $fontWeight-bold;
    font-size: $fontSize-body-l;

    &.disabled {
      background-color: $color-bg-widget-darker-3;
      cursor: no-drop;
      opacity: $opacity-hover;
    }
  }
}
</style>
