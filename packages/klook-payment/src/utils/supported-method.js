import { isPrdEnv } from '../payment-sdk/core/utils'
import { HEADERS } from './index'

// 防止dragonpay 突然出现导致异常
export const filterNonsupportMethodKes = method => method && method.method_key !== 'dragonpay'

const merchantIdMap = isPrdEnv
  ? {
    applepay_ntt_data: 'merchant.com.ntt.klooktravel',
    applepay_adyen: 'merchant.com.adyen.klooktravel',
    applepay_checkout: 'merchant.com.checkout.klooktravel'
  }
  : {
    applepay_ntt_data: 'merchant.com.ntt.klooktravel.sandbox',
    applepay_adyen: 'merchant.com.klooktravel.test',
    applepay_checkout: 'merchant.com.checkout.klooktravel.sandbox'
  }

export const initMerchantIdentifier = (sdk_type = 'applepay_ntt_data') => {
  // 校验用户设备是否支持Apple Pay，支持其中一个网关必然支持其他网关， 在测试环境目前只有checkout 网关能通过applepay校验
  try {
    return window.ApplePaySession.canMakePaymentsWithActiveCard(
      merchantIdMap[sdk_type]
    )
  } catch {
    return false
  }
}

export const getInitCheckoutParams = async () => ({
  headers: HEADERS,
  checkout_params: {
    auto_select_coupon: true,
    supported_method_list: await supportedMethodList.getSupportedList()
  }
})

export const supportedMethodList = {
  isInit: true,
  value: [],
  async getSupportedList() {
    if (this.isInit) {
      const userAgent = window.navigator.userAgent.toLowerCase()
      const isIOS = userAgent.includes('iphone') || userAgent.includes('ipad')
      const version = isIOS && userAgent.match(/cpu iphone os (\d+)_(\d+)/i)
      const versionMatch = version && version.length && version[1] + version[2] > '111'

      if (isIOS && window.ApplePaySession && versionMatch && (await initMerchantIdentifier())) {
        this.value.push('applepay')
      }
      this.isInit = false
    }
    return this.value
  }
}
