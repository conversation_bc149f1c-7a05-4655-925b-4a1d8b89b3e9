import Cookie from 'cookie'
import type { Method } from '../types.d'

export const Creditcard = 'creditcard'
export const CreditcardNew = Creditcard + '-new'

/**
 * @class ParamsManager
 *  - 管理参数
 *  - 绑定子组件的参数校验函数
 * - 字段命名为驼峰风格是参与计算和交互的自定义字段， 下划线风格是给后端提交的字段
 */

export default class ParamsManager {
  [key: string]: any /* {
    install_payment_plan?: null | number // 只有到submit 时才会用到 没有响应式
    save_token?: boolean // 是否保存token 只有到submit 时才会用到 没有响应式
  } */
  // 旧卡动态添加的字段
  ['creditcard-old']?: { cvv: string | null, verify?: () => Promise<void> }
  // 特殊参数
  terms_accepted = '' // 是否同意条款, 值是accepted,refused
  phone_number = '' // DBS 手机号

  // UnionPay 不需要提交任何参数 这里只做字段收集 & UI交互使用
  UnionPay?: { last4?: string | null }

  // 这里只做参数收集 传参是使用的是下面的 get [CreditcardNew]()
  CreditcardNew = {
    countryCode: '',
    phoneNumber: '',
    save_card: false,
    token: undefined, // lianlian 专用token 支付 需要

    credit_card_info: null as null | {
      bin?: string, // 前六
      issuer_bin?: string,
      last4?: string, // 后四
      is_new?: boolean,
      kvalue?: string,
      telephone_number?: string
      postal_code?: string // AVS US LOCAL PAY
    }
  }

  get [CreditcardNew]() {
    const { credit_card_info } = this.CreditcardNew
    return credit_card_info && {
      credit_card_info
    }
  }

  verifyForm(methodKey: string) {
    const key = methodKey === CreditcardNew
      ? 'CreditcardNew'
      : methodKey === 'unionpay'
        ? 'UnionPay'
        : methodKey
    return this[key as 'creditcard-old']?.verify?.()
  }

  getSubmitParams(method?: Method) { // 0元单会传null
    const formData: Record<string, any> = {
      method_key: '',
      terms_accepted: this.terms_accepted,
      phone_number: this.phone_number
    }

    if (method) {
      const { method_key, token } = method
      formData.method_key = method_key.includes(Creditcard)
        ? Creditcard
        : method_key

      if (method_key === CreditcardNew) {
        const { phoneNumber, token: lianlianToken, countryCode, save_card, credit_card_info } = this.CreditcardNew
        Object.assign(formData, {
          token: lianlianToken,
          credit_card_info: {
            save_card,
            ...credit_card_info,
            is_new: true
          }
        })

        if (!formData.credit_card_info.phone_number && countryCode && phoneNumber) {
          formData.credit_card_info.telephone_number = `+${countryCode}${phoneNumber}`
        }
      } else {
        formData.token = method_key === 'wechatpay'
          ? Cookie.parse(document.cookie)?.weixin_open_id
          : token
        Object.assign(formData, this[method_key])
      }
    }
    return formData
  }
}
