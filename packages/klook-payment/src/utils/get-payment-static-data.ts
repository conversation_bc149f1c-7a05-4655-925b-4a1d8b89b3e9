
import formatThousands from '@klook/klk-traveller-utils/lib/formatThousands'
import { currencyConfig } from '@klook/site-config'
import type { CheckoutDetail, SettlementExtraInfo, PaymentInfo } from '../types.d'

export const formatAmount = (
  currency: string,
  amount: string | number
) => currency && amount
  ? `${currencyConfig.currencySymbolMap[currency as keyof typeof currencyConfig['currencySymbolMap']] || currency} ${formatThousands(amount)}`
  : ''

const getCurrencyChangeTips = (priceInfo: PaymentInfo['price_info']) => {
  if (!priceInfo?.tips) {
    return null
  }

  const {
    tips: { message, content, dialog },
    pay_price: { currency, amount }
  } = priceInfo || {}

  return {
    message,
    content,
    dialog,
    placeholder: '{USD}',
    price: formatAmount(currency, amount)
  }
}

export default ({ payment_info } = {} as CheckoutDetail, { tip } = {} as SettlementExtraInfo) => {
  return payment_info && {
    paymentType: payment_info.payment_type,
    payLaterDescForPay: tip?.find(({ key }) => key === 'paylater_desc_for_pay')?.text,
    currencyChangeTips: getCurrencyChangeTips(payment_info.price_info)
  }
}
