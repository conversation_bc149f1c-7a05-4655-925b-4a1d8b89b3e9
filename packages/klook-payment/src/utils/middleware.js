// import qs from 'qs'
import { isPrdEnv, isWechatBrowser } from '../payment-sdk/core/utils'

/**
 *
 * 设置CSP 相关header
 *
 */
const BaseSecurityContents = [
  {
    key: 'script-src',
    value: [
      "'self'", "'unsafe-inline'", "'unsafe-hashes'", "'unsafe-eval'", 'blob:', 'https://*.klook.com',
      'https://images.uc.cn',
      'https://*.alicdn.com',
      'https://analytics.tiktok.com',
      'https://connect.facebook.net',
      'https://www.googletagmanager.com', 'https://*.google-analytics.com',
      'https://js.datadome.co',
      'https://*.ads-twitter.com', 'https://*.twitter.com',
      'https://*.dable.io', 'https://s.yimg.jp', 'https://bat.bing.com', 'https://wcs.naver.net',
      'https://*.cdn4.forter.com', 'https://js.braintreegateway.com',
      'https://auth.aftee.tw', 'https://www.paypalobjects.com', 'https://*.cloudfront.net',
      'https://c.paypal.com',
      'https://gcaptcha4.geetest.com',
      'https://static.geetest.com',
      'https://ssl.pstatic.net'
    ]
  },
  {
    key: 'worker-src',
    value: [
      "'self'", 'https:', 'blob:'
      // 'https://*.klook.com', 'https://*.stage.klook.io',
    ]
  }
]

/**
 * 获取路由的 query 参数
 */
export function getQueryKey(key, query) {
  if (!query || !key) { return '' }
  const value = query[key] || ''

  if (Array.isArray(value) && value.length) {
    return value[0]
  }

  return value
}

const isUsefulArr = arr => Array.isArray(arr) && arr.length > 0

/**
 * 获取weixin openID
 */
export default async function (ctx, appendSecurityContents) {
  // 非服务端 没有 ctx.req, guest_checkout 不需要设置安全header,
  // TODO 当结算页支持gues 时应当跟着修改这部分逻辑
  if (!ctx.req || ctx.req.utilConfig?.is_guest_checkout) { return }

  const mergedSecurityContents = isUsefulArr(appendSecurityContents)
    ? appendSecurityContents.reduce((mergedArr, item) => {
      // eslint-disable-next-line prefer-const
      let { key, value } = item || {}
      if (key && isUsefulArr(value)) {
        const index = mergedArr.findIndex(n => n.key === key)
        if (index > -1) {
          value = Array.from(new Set(mergedArr[index].value.concat(value)))
          mergedArr.splice(index, 1, {
            key,
            value
          })
        } else {
          mergedArr.push(item)
        }
      }
      return mergedArr
    }, BaseSecurityContents.slice())
    : BaseSecurityContents

  // eslint-disable-next-line no-unused-expressions
  ctx.res?.setHeader('Content-Security-Policy-Report-Only', `${mergedSecurityContents.map(n => `${n.key} ${n.value.join(' ')}`).join(';')};report-uri /v1/webbffapi/csp/violation-report;`)

  // 如果需要获取openId，才执行下面代码, 目前只有CNY 支持微信内置浏览器支付
  if (
    isWechatBrowser(ctx.req.headers?.['user-agent']) &&
    [getQueryKey('currency', ctx.query), ctx.app.$cookies.get('klk_currency')].includes('CNY') &&
    !ctx.app.$cookies.get('weixin_open_id')
  ) {
    // 对应CNY货币的appId
    const appId = 'wx544ee7451574d5d8'
    // 地址栏不存在code时，需要先行获取code
    const code = getQueryKey('code', ctx.query)
    if (!code) {
      // 链接跳转，获取code
      // let targetUrl = fromGuest
      //   ? `https://www.klook.com/${ctx.req.language}/order-checkout-jsapi-redirect?${qs.stringify(ctx.query)}`
      //   : `https://${ctx.req.headers.host}${ctx.route.fullPath}`
      let targetUrl = `https://${ctx.req.headers.host}${ctx.route.fullPath}`
      if (!isPrdEnv) {
        targetUrl = `https://www.klook.com/web3/wechatbrowser_helper/?redirect_url=${encodeURIComponent(targetUrl)}`
      }
      ctx.redirect(`https://open.weixin.qq.com/connect/oauth2/authorize?appid=${appId}&redirect_uri=${encodeURIComponent(targetUrl)}&response_type=code&scope=snsapi_base&state=123#wechat_redirect`)
    } else {
      const resp = await ctx.app.$axios.$post('http://paygatewayserv.klook-pay:8080/v1/gatewayserv/wechat/open_id', {
        app_id: appId,
        auth_code: code
      }).catch(() => ({}))

      if (resp.success && resp.result) {
        const openId = resp.result.open_id
        // 根据货币标识符，设置不同的key值
        openId && ctx.app.$cookies.set('weixin_open_id', openId, {
          path: '/',
          httpOnly: false,
          expires: new Date(Date.now() + 30 * 24 * 3600 * 1000)
        })
      }
    }
  }
}
