import { FunctionalComponentOptions, CreateElement, VNode } from 'vue'
// @ts-ignore
import IconNext from '@klook/klook-icons/lib/IconNext'

import type { CheckoutDetail } from '../types.d'
import showAfteeTerms from '../components/aftee-terms'
import showBottoSheet from './show-bottom-sheet'

const PaymenTerms: FunctionalComponentOptions<{
  checkoutDetail: CheckoutDetail
  isMobile?: boolean
}> = {
  functional: true,
  // @ts-ignore
  render(_h, { props: { checkoutDetail, isMobile }, parent: ctx }) {
    const termsTips = checkoutDetail?.payment_info?.terms_tips
    if (!termsTips) {
      return ''
    }
    const { terms_items, content } = termsTips!
    const keywordMap: Record<string, VNode> = {}
    const spliter = '###'

    const children: (string | VNode)[] = (terms_items || []).reduce((str, item) => {
      if (item.text) {
        str = str.replace(item.text, spliter + item.text + spliter)
        const textVnode = <span style="">{item.text}</span>

        if (item.style === 'orange') {
          textVnode.data!.style += 'cursor:pointer; color: #ff5722'
        }

        if (item.type === 'redirect' && item.items?.length) {
          textVnode.data!.on = {
            click: showBottoSheet.bind(
              ctx,
              { title: ctx.$t('89780') },
              {
                // eslint-disable-next-line @typescript-eslint/no-unused-vars
                default: (h: CreateElement) => item.items.map(({ link, title }, index) => <a
                  class="payment-terms--toss-term-item"
                  target="_blank"
                  key={index}
                  href={link}
                >
                  <img width="26" src="https://res.klook.com/image/upload/v1684117546/yxropklokz2vudyp3g8x.png" />
                  <span class="term-item--content">{ title }</span>
                  <IconNext size="18" color="#333" />
                </a>)
              }
            )
          }
          keywordMap[item.text] = textVnode
        } else if (item.type === 'modal') {
          textVnode.data!.on = {
            click: showAfteeTerms.bind(ctx, isMobile || false)
          }
          keywordMap[item.text] = textVnode
        } else {
          keywordMap[item.text] = <a
            href={item.link}
            target="_blank"
            style="text-decoration: underline"
          >
            { textVnode }
          </a>
        }
      }
      return str
    }, content).split(/#{3,}/g)

    Object.entries(keywordMap).forEach(([text, vNode]) => {
      children.splice(children.indexOf(text), 1, vNode)
    })

    return <div class="payment-terms" data-spm-module="Payment_Policy" data-spm-virtual-item="__virtual">{ children }</div>
  }
}

export default PaymenTerms
