import Vue, { CreateElement } from 'vue'
// @ts-ignore
import BottomSheet from '@klook/klook-ui/lib/bottom-sheet'

type DefaulteSlotRender = (h: CreateElement) => string | JSX.Element | JSX.Element[]

export default function showBottoSheet(
  this: Vue,
  attrs: Record<string, any> = {},
  slots: string | DefaulteSlotRender | {
    default: string | JSX.Element | JSX.Element[] | DefaulteSlotRender,
    footer?: string | JSX.Element | JSX.Element[] | DefaulteSlotRender
  },
  parent?: Vue
) {
  parent = parent || this

  const BottomSheetLayer = Vue.extend({
    render(h: CreateElement) {
      return <BottomSheet
        visible={ true }
        attrs={attrs}
        on={{
          'update:visible': (val: boolean) => {
            !val && setTimeout(() => {
              document.body.removeChild(instance!.$el)
              instance!.$destroy()
              instance = null
            })
          }
        }}
      >
        {
          typeof slots === 'function'
            ? slots(h)
            : Object.entries(slots).map(([slot, content]) => content && <template slot={slot}>
              {
                typeof content === 'function'
                  ? content(h)
                  : content
              }
            </template>)
        }
      </BottomSheet>
    }
  })

  let instance: Vue | null = new BottomSheetLayer({
    parent: parent instanceof Vue ? parent : undefined
  }).$mount()

  document.body.appendChild(instance.$el)
}
