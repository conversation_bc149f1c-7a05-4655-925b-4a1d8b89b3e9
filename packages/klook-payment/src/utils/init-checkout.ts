
import Vue from 'vue'
import { bffQuery } from '../assets/apis'
import { jumpOrReload, urlParam, getLangPath } from '../payment-sdk/core/utils'
import { getInitCheckoutParams } from './supported-method'
import allLocales from './locales'

async function initDecision(
  this: Vue,
  draft_order_id_str: any,
  choose_pay_status?: 'success' | 'failed'
): Promise<ReturnType<typeof getInitCheckoutParams> | void> {
  if (!draft_order_id_str) {
    return getInitCheckoutParams()
  }

  if (typeof draft_order_id_str !== 'string') {
    draft_order_id_str = draft_order_id_str.toString()
  }

  const { result, success, error } = await this.$axios.$post(bffQuery, {
    draft_order_id_str,
    choose_pay_status
  })

  if (!success || !result) {
    throw error
  }

  let { action } = result
  switch (action) {
    case 'failed_pup_and_continue':
      await this.$alert('173929')
      // 弹窗提示支付错误后  状态变成 continue
      action = 'continue'
    // eslint-disable-next-line no-fallthrough
    case 'continue':
      return getInitCheckoutParams()
    case 'order_list_jump':
      return jumpOrReload(getLangPath('bookings'))
    case 'pay_result_jump':
      return jumpOrReload(getLangPath(`payment/success/?cm=${result.related_order_no}`))
    case 'ask_pup':
      // 重复询问
      return choose_pay_status
        ? jumpOrReload(getLangPath('bookings/'))
        : initDecision.call(
          this,
          draft_order_id_str,
          (await this.$confirm(this.$t('173931'), {
            okLabel: this.$t('173932'),
            cancelLabel: this.$t('173933')
          })).result ? 'success' : 'failed'
        )
  }
}

export default async function initCheckout(
  this: Vue,
  draft_order_id?: any
) {
  if (typeof window !== 'object') {
    return
  }

  if (!(this instanceof Vue)) {
    throw new TypeError('function "initCheckout" must call by Vue instance !')
  }

  if (!this.$axios || !this.$confirm || !this.$t || !this.$i18n) {
    throw new TypeError('Runtime error, The Vue instance has no method "$axios" or "$confirm" or "$t"')
  }

  // @ts-ignore
  const lang = (this.$store || window.__KLOOK__)?.state?.klook.language || 'en'
  this.$i18n.mergeLocaleMessage(lang, allLocales[lang])

  // 测试环境 微信webview 回调
  if (urlParam('wechat_browser') === '1') {
    const err_msg = urlParam('err_msg')
    // const err_msg = klook.urlParam('err_msg');
    // const invoice_guid = klook.urlParam('invoice_guid');
    // const invoice_submission_guid = klook.urlParam('invoice_submission_guid');

    if (err_msg === 'get_brand_wcpay_request:ok') {
      const invoice_guid = urlParam('invoice_guid')
      const invoice_submission_guid = urlParam('invoice_submission_guid')
      return jumpOrReload(getLangPath.call(this, `web3/order-payment/wechatbrowserpay/?invoice_guid=${invoice_guid}&invoice_submission_guid=${invoice_submission_guid}`))
    } else {
      await this.$alert(this.$t('173929'))
      window.history.replaceState({}, '', window.location.href.replace('wechat_browser=1', ''))
    }
  }

  return initDecision.call(this, draft_order_id)
}
