export type ModeItem = {
  action?: string;
  bottom_sheet?: Dialog & { credit_card_title: string }
  display_model?: string
  type?: string
  title?: string
  content?: string
  icon?: string
  selected_coupon_code?: string // 手动添加去的
  multi_lines?: string
  items?: Record<string, any>[]
  input?: {
    validate_type: string
    hint: string
  }
  delete?: {
    confirm_dialog: Dialog
  }
  dialog?: Dialog
  banner?: {
    type: string;
    image_url: string;
    url: string;
  }
}

export interface Method {
  // 手动添加的字段，记录用到PCI(createcard-new & unionpay)的支付方式原始的排序位置，执行逻辑在mixin Watch('checkoutDetail')中
  manualOrder?: number
  // 手动添加的字段， 用于新卡支付方式需要的数据。执行逻辑在mixin Watch('checkoutDetail')中
  newCardManualOptions?: {
    // 是否可以在选中新卡支付方式情况下自动获得焦点并使页面滚动到支付组件的位置
    autoFocusable: boolean
    supportedCards: PaymentInfo['supported_cards']
    publicKey?: string
    cardInfoExtend?: PaymentInfo['card_info_extend']
    isScop?: boolean // isSimpleCheckout 后端接口所需参数
  }
  promotionname?: string
  action: string
  method_key: string
  token?: string
  icons: string[]
  card_type?: string
  method_input?: string
  method_banner?: string
  code?: string
  name: string
  payment_coupon_code?: string // 默认选中的支付优惠券code // paylater项目新增
  free_install_payment_plan?: number[]
  right_tips: Pick<ModeItem, 'type' | 'dialog'>
  model_list: ModeItem[]
  sub_options: Method[]
}

export type SettlementParams = {
  coupon_code?: string
  method_key?: string
  auto_select_coupon?: boolean
  install_payment_plan?: number | null
  save_token?: boolean
}

export interface Dialog {
  title: string
  message: string
  positive: string
  negative: string
  customized_top?: any
  items: any[]
}

export interface CouponItem {
  coupon_use_info: {
    currency_symbol: string
    coupon_discount_dec: string
  }
  payment_discount_desc?: string
  finance_type: string
  code: string
  desc: string
  external_code: string
  status: string
  method_name: string
  payment_channel: number
  gateway_name: string
  amount: string
  available_amount: string
  currency: string
  valid_date_utc: string
  user_id: number
  selected: boolean
}

export interface PaymentInfo {
  default_method_key: string
  methods_hide: boolean
  auto_submit: boolean // 废弃
  other_tip: string
  terms: { type: string; link: string }
  new_card_info: Record<string, string>
  new_card_method_tips?: {
    content: string
    code: string
  }
  card_info_extend?: {
    need_postal_code?: boolean
    postal_code_length?: number,
    need_cardholder_name?: boolean,
    cardholder_name_max_length?: number,
    cardholder_name_min_length?: number
  }
  support_new_payment: boolean
  is_agent_channel: boolean
  disable_payment_coupon: boolean
  terms_tips?: {
    content: string
    terms_items?: {
      type: string
      style: string
      text: string
      link: string
      items: {
        link: string
        title: string
      }[]
    }[]
  }
  global_tips: {
    type: string
    content: string
    items: {
      text?: string
      style?: string
      action?: string
      dialog?: ModeItem['dialog']
    }[]
  }[]
  asset_voucher_info: {
    list: {
      payment_coupon: CouponItem[]
      common_voucher: CouponItem[]
    }
  }
  supported_cards: {
    types?: {
      type: string
      icon: string
      provider: string
      disable: boolean
    }[]
  }
  methods: Method[]
  price_info: {
    tips: {
      message: string
      content: string
      dialog: Dialog
    }
    price: {
      currency: string
    }
    pay_price: {
      currency: string
      amount: string
      value: number
    }
  }
  payment_support_type: {
    content: string
    key: 'paylater' | 'paynow'
    tips: {
      title: string
      content: string
    }
  }[]
  payment_type: 'paylater' | 'paynow'
  special_scenes: {
    // 新增两个场景，类型：non_need:现金部分0元，无需选择支付方式 / non_usable:无可用支付方式
    type: 'non_need' | 'non_usable'
    content: string // 'no payment method need'
  }
}

export interface CheckoutDetail {
  config_version_code: number
  payment_info: PaymentInfo
  order_info: {
    deadline: string
  }
}

export type SettlementExtraInfo = {
  request_unique_id: string
  tip: {
    key: 'paylater_desc_for_choose' | 'paylater_desc_for_pay'
    text: string
  }[]
  coupon_list: {
    code: string
    finance_type: 'payment_coupon' | 'common_coupon'
  }
}

export type ModelTypeAttrs = ModeItem & { isSelected: boolean, method: Method }


export interface IEvent {
  message: string,
  level: 'fatal' | 'error' | 'warning' | 'log' | 'info' | 'debug',
  fingerprint: string[], // 设置事件指纹信息，同一指纹的事件会聚合成同一个issue
  tags?: {
    module: string;
    action: 'ui' | 'api' | 'default';
    code?: string;
    path: string
    message: string
    level?: 'fatal' | 'error' | 'warning' | 'log' | 'info' | 'debug'
    [key: string]: any;
  },
  extra?: Record<string, any>
}
