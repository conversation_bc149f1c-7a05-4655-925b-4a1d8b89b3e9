[{"id": 4088, "name": "阿富汗", "name_en": "Afghanistan", "type": 2, "country_code": "AF", "country_number": "93", "value": "93", "countryCode": "阿富汗", "country": "AF"}, {"id": 1082, "name": "阿尔巴尼亚", "name_en": "Albania", "type": 2, "country_code": "AL", "country_number": "355", "value": "355", "countryCode": "阿尔巴尼亚", "country": "AL"}, {"id": 4233, "name": "阿尔及利亚", "name_en": "Algeria", "type": 2, "country_code": "DZ", "country_number": "213", "value": "213", "countryCode": "阿尔及利亚", "country": "DZ"}, {"id": 4187, "name": "安道尔", "name_en": "Andorra", "type": 2, "country_code": "AD", "country_number": "376", "value": "376", "countryCode": "安道尔", "country": "AD"}, {"id": 4190, "name": "安哥拉", "name_en": "Angola", "type": 2, "country_code": "AO", "country_number": "244", "value": "244", "countryCode": "安哥拉", "country": "AO"}, {"id": 4145, "name": "安圭拉", "name_en": "<PERSON><PERSON><PERSON>", "type": 2, "country_code": "AI", "country_number": "1264", "value": "1264", "countryCode": "安圭拉", "country": "AI"}, {"id": 4089, "name": "安提瓜和巴布达", "name_en": "Antigua and Barbuda", "type": 2, "country_code": "AG", "country_number": "1268", "value": "1268", "countryCode": "安提瓜和巴布达", "country": "AG"}, {"id": 4183, "name": "阿根廷", "name_en": "Argentina", "type": 2, "country_code": "AR", "country_number": "54", "value": "54", "countryCode": "阿根廷", "country": "AR"}, {"id": 1094, "name": "亚美尼亚", "name_en": "Armenia", "type": 2, "country_code": "AM", "country_number": "374", "value": "374", "countryCode": "亚美尼亚", "country": "AM"}, {"id": 5945, "name": "阿鲁巴岛", "name_en": "Aruba", "type": 2, "country_code": "AW", "country_number": "297", "value": "297", "countryCode": "阿鲁巴岛", "country": "AW"}, {"id": 1022, "name": "澳大利亚", "name_en": "Australia", "type": 2, "country_code": "AU", "country_number": "61", "value": "61", "countryCode": "澳大利亚", "country": "AU"}, {"id": 1026, "name": "奥地利", "name_en": "Austria", "type": 2, "country_code": "AT", "country_number": "43", "value": "43", "countryCode": "奥地利", "country": "AT"}, {"id": 1067, "name": "阿塞拜疆", "name_en": "Azerbaijan", "type": 2, "country_code": "AZ", "country_number": "994", "value": "994", "countryCode": "阿塞拜疆", "country": "AZ"}, {"id": 4125, "name": "巴林", "name_en": "Bahrain", "type": 2, "country_code": "BH", "country_number": "973", "value": "973", "countryCode": "巴林", "country": "BH"}, {"id": 1081, "name": "孟加拉国", "name_en": "Bangladesh", "type": 2, "country_code": "BD", "country_number": "880", "value": "880", "countryCode": "孟加拉国", "country": "BD"}, {"id": 4180, "name": "巴巴多斯", "name_en": "Barbados", "type": 2, "country_code": "BB", "country_number": "1246", "value": "1246", "countryCode": "巴巴多斯", "country": "BB"}, {"id": 1086, "name": "白俄罗斯", "name_en": "Belarus", "type": 2, "country_code": "BY", "country_number": "375", "value": "375", "countryCode": "白俄罗斯", "country": "BY"}, {"id": 1061, "name": "比利时", "name_en": "Belgium", "type": 2, "country_code": "BE", "country_number": "32", "value": "32", "countryCode": "比利时", "country": "BE"}, {"id": 4101, "name": "伯利兹", "name_en": "Belize", "type": 2, "country_code": "BZ", "country_number": "501", "value": "501", "countryCode": "伯利兹", "country": "BZ"}, {"id": 4032, "name": "贝宁", "name_en": "Benin", "type": 2, "country_code": "BJ", "country_number": "229", "value": "229", "countryCode": "贝宁", "country": "BJ"}, {"id": 4239, "name": "百慕大", "name_en": "Bermuda", "type": 2, "country_code": "BM", "country_number": "1441", "value": "1441", "countryCode": "百慕大", "country": "BM"}, {"id": 1021, "name": "不丹", "name_en": "Bhutan", "type": 2, "country_code": "BT", "country_number": "975", "value": "975", "countryCode": "不丹", "country": "BT"}, {"id": 4220, "name": "玻利维亚", "name_en": "Bolivia", "type": 2, "country_code": "BO", "country_number": "591", "value": "591", "countryCode": "玻利维亚", "country": "BO"}, {"id": 1076, "name": "波斯尼亚和黑塞哥维那", "name_en": "Bosnia and Herzegovina", "type": 2, "country_code": "BA", "country_number": "387", "value": "387", "countryCode": "波斯尼亚和黑塞哥维那", "country": "BA"}, {"id": 4081, "name": "博茨瓦纳", "name_en": "Botswana", "type": 2, "country_code": "BW", "country_number": "267", "value": "267", "countryCode": "博茨瓦纳", "country": "BW"}, {"id": 4098, "name": "巴西", "name_en": "Brazil", "type": 2, "country_code": "BR", "country_number": "55", "value": "55", "countryCode": "巴西", "country": "BR"}, {"id": 4148, "name": "英属印度洋领地", "name_en": "British Indian Ocean Territory", "type": 2, "country_code": "IO", "country_number": "246", "value": "246", "countryCode": "英属印度洋领地", "country": "IO"}, {"id": 4099, "name": "文莱", "name_en": "Brunei", "type": 2, "country_code": "BN", "country_number": "673", "value": "673", "countryCode": "文莱", "country": "BN"}, {"id": 1070, "name": "保加利亚", "name_en": "Bulgaria", "type": 2, "country_code": "BG", "country_number": "359", "value": "359", "countryCode": "保加利亚", "country": "BG"}, {"id": 4030, "name": "布基纳法索", "name_en": "Burkina Faso", "type": 2, "country_code": "BF", "country_number": "226", "value": "226", "countryCode": "布基纳法索", "country": "BF"}, {"id": 4196, "name": "布隆迪", "name_en": "Burundi", "type": 2, "country_code": "BI", "country_number": "257", "value": "257", "countryCode": "布隆迪", "country": "BI"}, {"id": 1008, "name": "柬埔寨", "name_en": "Cambodia", "type": 2, "country_code": "KH", "country_number": "855", "value": "855", "countryCode": "柬埔寨", "country": "KH"}, {"id": 4083, "name": "喀麦隆", "name_en": "Cameroon", "type": 2, "country_code": "CM", "country_number": "237", "value": "237", "countryCode": "喀麦隆", "country": "CM"}, {"id": 1092, "name": "加拿大", "name_en": "Canada", "type": 2, "country_code": "CA", "country_number": "1", "value": "1", "countryCode": "加拿大", "country": "CA"}, {"id": 4158, "name": "开曼群岛", "name_en": "Cayman Islands", "type": 2, "country_code": "KY", "country_number": "1345", "value": "1345", "countryCode": "开曼群岛", "country": "KY"}, {"id": 4041, "name": "中非共和国", "name_en": "Central African Republic", "type": 2, "country_code": "CF", "country_number": "236", "value": "236", "countryCode": "中非共和国", "country": "CF"}, {"id": 4209, "name": "乍得", "name_en": "Chad", "type": 2, "country_code": "TD", "country_number": "235", "value": "235", "countryCode": "乍得", "country": "TD"}, {"id": 4050, "name": "智利", "name_en": "Chile", "type": 2, "country_code": "CL", "country_number": "56", "value": "56", "countryCode": "智利", "country": "CL"}, {"id": 4213, "name": "哥伦比亚", "name_en": "Colombia", "type": 2, "country_code": "CO", "country_number": "57", "value": "57", "countryCode": "哥伦比亚", "country": "CO"}, {"id": 4225, "name": "科摩罗", "name_en": "Comoros", "type": 2, "country_code": "KM", "country_number": "269", "value": "269", "countryCode": "科摩罗", "country": "KM"}, {"id": 4044, "name": "刚果共和国", "name_en": "Congo-Brazzaville", "type": 2, "country_code": "CG", "country_number": "242", "value": "242", "countryCode": "刚果共和国", "country": "CG"}, {"id": 4064, "name": "库克群岛", "name_en": "Cook Islands", "type": 2, "country_code": "CK", "country_number": "682", "value": "682", "countryCode": "库克群岛", "country": "CK"}, {"id": 4176, "name": "哥斯达黎加", "name_en": "Costa Rica", "type": 2, "country_code": "CR", "country_number": "506", "value": "506", "countryCode": "哥斯达黎加", "country": "CR"}, {"id": 1075, "name": "克罗地亚", "name_en": "Croatia", "type": 2, "country_code": "HR", "country_number": "385", "value": "385", "countryCode": "克罗地亚", "country": "HR"}, {"id": 4135, "name": "古巴", "name_en": "Cuba", "type": 2, "country_code": "CU", "country_number": "53", "value": "53", "countryCode": "古巴", "country": "CU"}, {"id": 6815, "name": "库拉索", "name_en": "Curacao", "type": 2, "country_code": "CW", "country_number": "599", "value": "599", "countryCode": "库拉索", "country": "CW"}, {"id": 1087, "name": "塞浦路斯", "name_en": "Cyprus", "type": 2, "country_code": "CY", "country_number": "357", "value": "357", "countryCode": "塞浦路斯", "country": "CY"}, {"id": 1066, "name": "捷克", "name_en": "Czechia", "type": 2, "country_code": "CZ", "country_number": "420", "value": "420", "countryCode": "捷克", "country": "CZ"}, {"id": 4023, "name": "科特迪瓦", "name_en": "Côte d'Ivoire", "type": 2, "country_code": "CI", "country_number": "225", "value": "225", "countryCode": "科特迪瓦", "country": "CI"}, {"id": 4046, "name": "刚果民主共和国", "name_en": "Democratic Republic of the Congo", "type": 2, "country_code": "CD", "country_number": "243", "value": "243", "countryCode": "刚果民主共和国", "country": "CD"}, {"id": 1042, "name": "丹麦", "name_en": "Denmark", "type": 2, "country_code": "DK", "country_number": "45", "value": "45", "countryCode": "丹麦", "country": "DK"}, {"id": 4057, "name": "吉布提", "name_en": "Djibouti", "type": 2, "country_code": "DJ", "country_number": "253", "value": "253", "countryCode": "吉布提", "country": "DJ"}, {"id": 4128, "name": "多米尼克", "name_en": "Dominica", "type": 2, "country_code": "DM", "country_number": "1767", "value": "1767", "countryCode": "多米尼克", "country": "DM"}, {"id": 4132, "name": "多米尼加共和国", "name_en": "Dominican Republic", "type": 2, "country_code": "DO", "country_number": "1", "value": "1", "countryCode": "多米尼加共和国", "country": "DO"}, {"id": 4236, "name": "东帝汶", "name_en": "East Timor", "type": 2, "country_code": "TL", "country_number": "670", "value": "670", "countryCode": "东帝汶", "country": "TL"}, {"id": 4079, "name": "厄瓜多尔", "name_en": "Ecuador", "type": 2, "country_code": "EC", "country_number": "593", "value": "593", "countryCode": "厄瓜多尔", "country": "EC"}, {"id": 1059, "name": "埃及", "name_en": "Egypt", "type": 2, "country_code": "EG", "country_number": "20", "value": "20", "countryCode": "埃及", "country": "EG"}, {"id": 4218, "name": "萨尔瓦多", "name_en": "El Salvador", "type": 2, "country_code": "SV", "country_number": "503", "value": "503", "countryCode": "萨尔瓦多", "country": "SV"}, {"id": 4042, "name": "赤道几内亚", "name_en": "Equatorial Guinea", "type": 2, "country_code": "GQ", "country_number": "240", "value": "240", "countryCode": "赤道几内亚", "country": "GQ"}, {"id": 4011, "name": "厄立特里亚", "name_en": "Eritrea", "type": 2, "country_code": "ER", "country_number": "291", "value": "291", "countryCode": "厄立特里亚", "country": "ER"}, {"id": 1080, "name": "爱沙尼亚", "name_en": "Estonia", "type": 2, "country_code": "EE", "country_number": "372", "value": "372", "countryCode": "爱沙尼亚", "country": "EE"}, {"id": 4155, "name": "斯威士兰", "name_en": "<PERSON><PERSON><PERSON><PERSON>", "type": 2, "country_code": "SZ", "country_number": "268", "value": "268", "countryCode": "斯威士兰", "country": "SZ"}, {"id": 4054, "name": "埃塞俄比亚", "name_en": "Ethiopia", "type": 2, "country_code": "ET", "country_number": "251", "value": "251", "countryCode": "埃塞俄比亚", "country": "ET"}, {"id": 4168, "name": "福克兰群岛", "name_en": "Falkland Islands", "type": 2, "country_code": "FK", "country_number": "500", "value": "500", "countryCode": "福克兰群岛", "country": "FK"}, {"id": 4204, "name": "法罗群岛", "name_en": "Faroe Islands", "type": 2, "country_code": "FO", "country_number": "298", "value": "298", "countryCode": "法罗群岛", "country": "FO"}, {"id": 4162, "name": "密克罗尼西亚联邦", "name_en": "Federated States of Micronesia", "type": 2, "country_code": "FM", "country_number": "691", "value": "691", "countryCode": "密克罗尼西亚联邦", "country": "FM"}, {"id": 1051, "name": "斐济", "name_en": "Fiji", "type": 2, "country_code": "FJ", "country_number": "679", "value": "679", "countryCode": "斐济", "country": "FJ"}, {"id": 1037, "name": "芬兰", "name_en": "Finland", "type": 2, "country_code": "FI", "country_number": "358", "value": "358", "countryCode": "芬兰", "country": "FI"}, {"id": 1033, "name": "法国", "name_en": "France", "type": 2, "country_code": "FR", "country_number": "33", "value": "33", "countryCode": "法国", "country": "FR"}, {"id": 1077, "name": "法属波利尼西亚", "name_en": "French Polynesia", "type": 2, "country_code": "PF", "country_number": "689", "value": "689", "countryCode": "法属波利尼西亚", "country": "PF"}, {"id": 6279, "name": "法属南部领地", "name_en": "French Southern and Antarctic Lands", "type": 2, "country_code": "TF", "country_number": "262", "value": "262", "countryCode": "法属南部领地", "country": "TF"}, {"id": 4043, "name": "加蓬", "name_en": "Gabon", "type": 2, "country_code": "GA", "country_number": "241", "value": "241", "countryCode": "加蓬", "country": "GA"}, {"id": 1084, "name": "格鲁吉亚", "name_en": "Georgia", "type": 2, "country_code": "GE", "country_number": "995", "value": "995", "countryCode": "格鲁吉亚", "country": "GE"}, {"id": 1031, "name": "德国", "name_en": "Germany", "type": 2, "country_code": "DE", "country_number": "49", "value": "49", "countryCode": "德国", "country": "DE"}, {"id": 4026, "name": "加纳", "name_en": "Ghana", "type": 2, "country_code": "GH", "country_number": "233", "value": "233", "countryCode": "加纳", "country": "GH"}, {"id": 4031, "name": "直布罗陀", "name_en": "Gibraltar", "type": 2, "country_code": "GI", "country_number": "350", "value": "350", "countryCode": "直布罗陀", "country": "GI"}, {"id": 1047, "name": "希腊", "name_en": "Greece", "type": 2, "country_code": "GR", "country_number": "30", "value": "30", "countryCode": "希腊", "country": "GR"}, {"id": 4147, "name": "格陵兰", "name_en": "Greenland", "type": 2, "country_code": "GL", "country_number": "299", "value": "299", "countryCode": "格陵兰", "country": "GL"}, {"id": 4091, "name": "格林纳达", "name_en": "Grenada", "type": 2, "country_code": "GD", "country_number": "1473", "value": "1473", "countryCode": "格林纳达", "country": "GD"}, {"id": 4077, "name": "危地马拉", "name_en": "Guatemala", "type": 2, "country_code": "GT", "country_number": "502", "value": "502", "countryCode": "危地马拉", "country": "GT"}, {"id": 4182, "name": "根西岛", "name_en": "Guernsey", "type": 2, "country_code": "GG", "country_number": "441481", "value": "441481", "countryCode": "根西岛", "country": "GG"}, {"id": 4021, "name": "几内亚", "name_en": "Guinea", "type": 2, "country_code": "GN", "country_number": "224", "value": "224", "countryCode": "几内亚", "country": "GN"}, {"id": 4017, "name": "几内亚比绍", "name_en": "Guinea-Bissau", "type": 2, "country_code": "GW", "country_number": "245", "value": "245", "countryCode": "几内亚比绍", "country": "GW"}, {"id": 4122, "name": "圭亚那", "name_en": "Guyana", "type": 2, "country_code": "GY", "country_number": "592", "value": "592", "countryCode": "圭亚那", "country": "GY"}, {"id": 4134, "name": "海地", "name_en": "Haiti", "type": 2, "country_code": "HT", "country_number": "509", "value": "509", "countryCode": "海地", "country": "HT"}, {"id": 4179, "name": "洪都拉斯", "name_en": "Honduras", "type": 2, "country_code": "HN", "country_number": "504", "value": "504", "countryCode": "洪都拉斯", "country": "HN"}, {"id": 364901, "name": "香港", "name_en": "Hong Kong", "type": 2, "country_code": "HK", "country_number": "852", "value": "852", "countryCode": "香港", "country": "HK"}, {"id": 1062, "name": "匈牙利", "name_en": "Hungary", "type": 2, "country_code": "HU", "country_number": "36", "value": "36", "countryCode": "匈牙利", "country": "HU"}, {"id": 1035, "name": "冰岛", "name_en": "Iceland", "type": 2, "country_code": "IS", "country_number": "354", "value": "354", "countryCode": "冰岛", "country": "IS"}, {"id": 1039, "name": "印度", "name_en": "India", "type": 2, "country_code": "IN", "country_number": "91", "value": "91", "countryCode": "印度", "country": "IN"}, {"id": 1006, "name": "印度尼西亚", "name_en": "Indonesia", "type": 2, "country_code": "ID", "country_number": "62", "value": "62", "countryCode": "印度尼西亚", "country": "ID"}, {"id": 4065, "name": "伊朗", "name_en": "Iran", "type": 2, "country_code": "IR", "country_number": "98", "value": "98", "countryCode": "伊朗", "country": "IR"}, {"id": 4061, "name": "伊拉克", "name_en": "Iraq", "type": 2, "country_code": "IQ", "country_number": "964", "value": "964", "countryCode": "伊拉克", "country": "IQ"}, {"id": 1058, "name": "爱尔兰", "name_en": "Ireland", "type": 2, "country_code": "IE", "country_number": "353", "value": "353", "countryCode": "爱尔兰", "country": "IE"}, {"id": 4082, "name": "马恩岛", "name_en": "Isle of Man", "type": 2, "country_code": "IM", "country_number": "441624", "value": "441624", "countryCode": "马恩岛", "country": "IM"}, {"id": 1052, "name": "以色列", "name_en": "Israel", "type": 2, "country_code": "IL", "country_number": "972", "value": "972", "countryCode": "以色列", "country": "IL"}, {"id": 1027, "name": "意大利", "name_en": "Italy", "type": 2, "country_code": "IT", "country_number": "39", "value": "39", "countryCode": "意大利", "country": "IT"}, {"id": 4019, "name": "牙买加", "name_en": "Jamaica", "type": 2, "country_code": "JM", "country_number": "1876", "value": "1876", "countryCode": "牙买加", "country": "JM"}, {"id": 1012, "name": "日本", "name_en": "Japan", "type": 2, "country_code": "JP", "country_number": "81", "value": "81", "countryCode": "日本", "country": "JP"}, {"id": 4131, "name": "泽西岛", "name_en": "Jersey", "type": 2, "country_code": "JE", "country_number": "441534", "value": "441534", "countryCode": "泽西岛", "country": "JE"}, {"id": 1088, "name": "约旦", "name_en": "Jordan", "type": 2, "country_code": "JO", "country_number": "962", "value": "962", "countryCode": "约旦", "country": "JO"}, {"id": 4146, "name": "哈萨克斯坦", "name_en": "Kazakhstan", "type": 2, "country_code": "KZ", "country_number": "7", "value": "7", "countryCode": "哈萨克斯坦", "country": "KZ"}, {"id": 4052, "name": "肯尼亚", "name_en": "Kenya", "type": 2, "country_code": "KE", "country_number": "254", "value": "254", "countryCode": "肯尼亚", "country": "KE"}, {"id": 4066, "name": "基里巴斯", "name_en": "Kiribati", "type": 2, "country_code": "KI", "country_number": "686", "value": "686", "countryCode": "基里巴斯", "country": "KI"}, {"id": 4051, "name": "科索沃", "name_en": "Kosovo", "type": 2, "country_code": "XK", "country_number": "383", "value": "383", "countryCode": "科索沃", "country": "XK"}, {"id": 4203, "name": "科威特", "name_en": "Kuwait", "type": 2, "country_code": "KW", "country_number": "965", "value": "965", "countryCode": "科威特", "country": "KW"}, {"id": 4104, "name": "吉尔吉斯斯坦", "name_en": "Kyrgyzstan", "type": 2, "country_code": "KG", "country_number": "996", "value": "996", "countryCode": "吉尔吉斯斯坦", "country": "KG"}, {"id": 1038, "name": "老挝", "name_en": "Laos", "type": 2, "country_code": "LA", "country_number": "856", "value": "856", "countryCode": "老挝", "country": "LA"}, {"id": 1083, "name": "拉脱维亚", "name_en": "Latvia", "type": 2, "country_code": "LV", "country_number": "371", "value": "371", "countryCode": "拉脱维亚", "country": "LV"}, {"id": 1089, "name": "黎巴嫩", "name_en": "Lebanon", "type": 2, "country_code": "LB", "country_number": "961", "value": "961", "countryCode": "黎巴嫩", "country": "LB"}, {"id": 4175, "name": "莱索托", "name_en": "Lesotho", "type": 2, "country_code": "LS", "country_number": "266", "value": "266", "countryCode": "莱索托", "country": "LS"}, {"id": 4024, "name": "利比里亚", "name_en": "Liberia", "type": 2, "country_code": "LR", "country_number": "231", "value": "231", "countryCode": "利比里亚", "country": "LR"}, {"id": 4237, "name": "利比亚", "name_en": "Libya", "type": 2, "country_code": "LY", "country_number": "218", "value": "218", "countryCode": "利比亚", "country": "LY"}, {"id": 4129, "name": "列支敦士登", "name_en": "Liechtenstein", "type": 2, "country_code": "LI", "country_number": "423", "value": "423", "countryCode": "列支敦士登", "country": "LI"}, {"id": 1079, "name": "立陶宛", "name_en": "Lithuania", "type": 2, "country_code": "LT", "country_number": "370", "value": "370", "countryCode": "立陶宛", "country": "LT"}, {"id": 4208, "name": "卢森堡", "name_en": "Luxembourg", "type": 2, "country_code": "LU", "country_number": "352", "value": "352", "countryCode": "卢森堡", "country": "LU"}, {"id": 364902, "name": "澳门", "name_en": "Macau", "type": 2, "country_code": "MO", "country_number": "853", "value": "853", "countryCode": "澳门", "country": "MO"}, {"id": 1064, "name": "马达加斯加", "name_en": "Madagascar", "type": 2, "country_code": "MG", "country_number": "261", "value": "261", "countryCode": "马达加斯加", "country": "MG"}, {"id": 1020, "name": "中国内地", "name_en": "Mainland China", "type": 2, "country_code": "CN", "country_number": "86", "value": "86", "countryCode": "中国内地", "country": "CN"}, {"id": 4212, "name": "马拉维", "name_en": "Malawi", "type": 2, "country_code": "MW", "country_number": "265", "value": "265", "countryCode": "马拉维", "country": "MW"}, {"id": 1019, "name": "马来西亚", "name_en": "Malaysia", "type": 2, "country_code": "MY", "country_number": "60", "value": "60", "countryCode": "马来西亚", "country": "MY"}, {"id": 1091, "name": "马尔代夫", "name_en": "Maldives", "type": 2, "country_code": "MV", "country_number": "960", "value": "960", "countryCode": "马尔代夫", "country": "MV"}, {"id": 4033, "name": "马里", "name_en": "Mali", "type": 2, "country_code": "ML", "country_number": "223", "value": "223", "countryCode": "马里", "country": "ML"}, {"id": 1085, "name": "马耳他", "name_en": "Malta", "type": 2, "country_code": "MT", "country_number": "356", "value": "356", "countryCode": "马耳他", "country": "MT"}, {"id": 4136, "name": "马绍尔群岛", "name_en": "Marshall Islands", "type": 2, "country_code": "MH", "country_number": "692", "value": "692", "countryCode": "马绍尔群岛", "country": "MH"}, {"id": 4242, "name": "毛里塔尼亚", "name_en": "Mauritania", "type": 2, "country_code": "MR", "country_number": "222", "value": "222", "countryCode": "毛里塔尼亚", "country": "MR"}, {"id": 1011, "name": "毛里求斯", "name_en": "Mauritius", "type": 2, "country_code": "MU", "country_number": "230", "value": "230", "countryCode": "毛里求斯", "country": "MU"}, {"id": 1093, "name": "墨西哥", "name_en": "Mexico", "type": 2, "country_code": "MX", "country_number": "52", "value": "52", "countryCode": "墨西哥", "country": "MX"}, {"id": 4108, "name": "摩尔多瓦", "name_en": "Moldova", "type": 2, "country_code": "MD", "country_number": "373", "value": "373", "countryCode": "摩尔多瓦", "country": "MD"}, {"id": 4198, "name": "摩纳哥", "name_en": "Monaco", "type": 2, "country_code": "MC", "country_number": "377", "value": "377", "countryCode": "摩纳哥", "country": "MC"}, {"id": 4018, "name": "蒙古国", "name_en": "Mongolia", "type": 2, "country_code": "MN", "country_number": "976", "value": "976", "countryCode": "蒙古国", "country": "MN"}, {"id": 4070, "name": "黑山共和国", "name_en": "Montenegro", "type": 2, "country_code": "ME", "country_number": "382", "value": "382", "countryCode": "黑山共和国", "country": "ME"}, {"id": 4171, "name": "蒙特塞拉特", "name_en": "Montserrat", "type": 2, "country_code": "MS", "country_number": "1664", "value": "1664", "countryCode": "蒙特塞拉特", "country": "MS"}, {"id": 1060, "name": "摩洛哥", "name_en": "Morocco", "type": 2, "country_code": "MA", "country_number": "212", "value": "212", "countryCode": "摩洛哥", "country": "MA"}, {"id": 4202, "name": "莫桑比克", "name_en": "Mozambique", "type": 2, "country_code": "MZ", "country_number": "258", "value": "258", "countryCode": "莫桑比克", "country": "MZ"}, {"id": 4188, "name": "纳米比亚", "name_en": "Namibia", "type": 2, "country_code": "NA", "country_number": "264", "value": "264", "countryCode": "纳米比亚", "country": "NA"}, {"id": 4027, "name": "英属维尔京群岛", "name_en": "NancyBritish Virgin Islands", "type": 2, "country_code": "NBB2", "country_number": "1284", "value": "1284", "countryCode": "英属维尔京群岛", "country": "NBB2"}, {"id": 4165, "name": "瑙鲁", "name_en": "Nauru", "type": 2, "country_code": "NR", "country_number": "674", "value": "674", "countryCode": "瑙鲁", "country": "NR"}, {"id": 1007, "name": "尼泊尔", "name_en": "Nepal", "type": 2, "country_code": "NP", "country_number": "977", "value": "977", "countryCode": "尼泊尔", "country": "NP"}, {"id": 1025, "name": "荷兰", "name_en": "Netherlands", "type": 2, "country_code": "NL", "country_number": "31", "value": "31", "countryCode": "荷兰", "country": "NL"}, {"id": 4580, "name": "新喀里多尼亚", "name_en": "New Caledonia", "type": 2, "country_code": "NC", "country_number": "687", "value": "687", "countryCode": "新喀里多尼亚", "country": "NC"}, {"id": 1024, "name": "新西兰", "name_en": "New Zealand", "type": 2, "country_code": "NZ", "country_number": "64", "value": "64", "countryCode": "新西兰", "country": "NZ"}, {"id": 4174, "name": "尼加拉瓜", "name_en": "Nicaragua", "type": 2, "country_code": "NI", "country_number": "505", "value": "505", "countryCode": "尼加拉瓜", "country": "NI"}, {"id": 4034, "name": "尼日尔", "name_en": "Niger", "type": 2, "country_code": "NE", "country_number": "227", "value": "227", "countryCode": "尼日尔", "country": "NE"}, {"id": 4037, "name": "尼日利亚", "name_en": "Nigeria", "type": 2, "country_code": "NG", "country_number": "234", "value": "234", "countryCode": "尼日利亚", "country": "NG"}, {"id": 4047, "name": "纽埃", "name_en": "Niue", "type": 2, "country_code": "NU", "country_number": "683", "value": "683", "countryCode": "纽埃", "country": "NU"}, {"id": 4216, "name": "朝鲜", "name_en": "North Korea", "type": 2, "country_code": "KP", "country_number": "850", "value": "850", "countryCode": "朝鲜", "country": "KP"}, {"id": 1090, "name": "北马其顿", "name_en": "North Macedonia", "type": 2, "country_code": "MK", "country_number": "389", "value": "389", "countryCode": "北马其顿", "country": "MK"}, {"id": 1041, "name": "挪威", "name_en": "Norway", "type": 2, "country_code": "NO", "country_number": "47", "value": "47", "countryCode": "挪威", "country": "NO"}, {"id": 1049, "name": "阿曼", "name_en": "Oman", "type": 2, "country_code": "OM", "country_number": "968", "value": "968", "countryCode": "阿曼", "country": "OM"}, {"id": 4133, "name": "巴基斯坦", "name_en": "Pakistan", "type": 2, "country_code": "PK", "country_number": "92", "value": "92", "countryCode": "巴基斯坦", "country": "PK"}, {"id": 1057, "name": "帕劳", "name_en": "<PERSON><PERSON>", "type": 2, "country_code": "PW", "country_number": "680", "value": "680", "countryCode": "帕劳", "country": "PW"}, {"id": 4144, "name": "巴勒斯坦领土", "name_en": "Palestinian Territories", "type": 2, "country_code": "PS", "country_number": "970", "value": "970", "countryCode": "巴勒斯坦领土", "country": "PS"}, {"id": 4178, "name": "巴拿马", "name_en": "Panama", "type": 2, "country_code": "PA", "country_number": "507", "value": "507", "countryCode": "巴拿马", "country": "PA"}, {"id": 4161, "name": "巴布亚新几内亚", "name_en": "Papua New Guinea", "type": 2, "country_code": "PG", "country_number": "675", "value": "675", "countryCode": "巴布亚新几内亚", "country": "PG"}, {"id": 4114, "name": "巴拉圭", "name_en": "Paraguay", "type": 2, "country_code": "PY", "country_number": "595", "value": "595", "countryCode": "巴拉圭", "country": "PY"}, {"id": 4238, "name": "秘鲁", "name_en": "Peru", "type": 2, "country_code": "PE", "country_number": "51", "value": "51", "countryCode": "秘鲁", "country": "PE"}, {"id": 1016, "name": "菲律宾", "name_en": "Philippines", "type": 2, "country_code": "PH", "country_number": "63", "value": "63", "countryCode": "菲律宾", "country": "PH"}, {"id": 4169, "name": "皮特凯恩群岛", "name_en": "Pitcairn Islands", "type": 2, "country_code": "PN", "country_number": "64", "value": "64", "countryCode": "皮特凯恩群岛", "country": "PN"}, {"id": 1069, "name": "波兰", "name_en": "Poland", "type": 2, "country_code": "PL", "country_number": "48", "value": "48", "countryCode": "波兰", "country": "PL"}, {"id": 1063, "name": "葡萄牙", "name_en": "Portugal", "type": 2, "country_code": "PT", "country_number": "351", "value": "351", "countryCode": "葡萄牙", "country": "PT"}, {"id": 1048, "name": "卡塔尔", "name_en": "Qatar", "type": 2, "country_code": "QA", "country_number": "974", "value": "974", "countryCode": "卡塔尔", "country": "QA"}, {"id": 1068, "name": "罗马尼亚", "name_en": "Romania", "type": 2, "country_code": "RO", "country_number": "40", "value": "40", "countryCode": "罗马尼亚", "country": "RO"}, {"id": 1055, "name": "俄罗斯", "name_en": "Russia", "type": 2, "country_code": "RU", "country_number": "7", "value": "7", "countryCode": "俄罗斯", "country": "RU"}, {"id": 4223, "name": "卢旺达", "name_en": "Rwanda", "type": 2, "country_code": "RW", "country_number": "250", "value": "250", "countryCode": "卢旺达", "country": "RW"}, {"id": 4228, "name": "圣赫勒拿、阿森松和特里斯坦达库尼亚", "name_en": "Saint Helena, Ascension and Tristan <PERSON>ha", "type": 2, "country_code": "SH", "country_number": "290", "value": "290", "countryCode": "圣赫勒拿、阿森松和特里斯坦达库尼亚", "country": "SH"}, {"id": 4087, "name": "圣基茨和尼维斯", "name_en": "Saint Kitts and Nevis", "type": 2, "country_code": "KN", "country_number": "1869", "value": "1869", "countryCode": "圣基茨和尼维斯", "country": "KN"}, {"id": 4093, "name": "圣卢西亚", "name_en": "Saint Lucia", "type": 2, "country_code": "LC", "country_number": "1758", "value": "1758", "countryCode": "圣卢西亚", "country": "LC"}, {"id": 7102, "name": "圣皮埃尔和密克隆群岛", "name_en": "Saint Pierre and Miquelon", "type": 2, "country_code": "PM", "country_number": "508", "value": "508", "countryCode": "圣皮埃尔和密克隆群岛", "country": "PM"}, {"id": 4090, "name": "圣文森特和格林纳丁斯", "name_en": "Saint Vincent and the Grenadines", "type": 2, "country_code": "VC", "country_number": "1784", "value": "1784", "countryCode": "圣文森特和格林纳丁斯", "country": "VC"}, {"id": 4056, "name": "萨摩亚", "name_en": "Samoa", "type": 2, "country_code": "WS", "country_number": "685", "value": "685", "countryCode": "萨摩亚", "country": "WS"}, {"id": 4110, "name": "圣马力诺", "name_en": "San Marino", "type": 2, "country_code": "SM", "country_number": "378", "value": "378", "countryCode": "圣马力诺", "country": "SM"}, {"id": 4142, "name": "沙特阿拉伯", "name_en": "Saudi Arabia", "type": 2, "country_code": "SA", "country_number": "966", "value": "966", "countryCode": "沙特阿拉伯", "country": "SA"}, {"id": 4015, "name": "塞内加尔", "name_en": "Senegal", "type": 2, "country_code": "SN", "country_number": "221", "value": "221", "countryCode": "塞内加尔", "country": "SN"}, {"id": 1045, "name": "塞尔维亚", "name_en": "Serbia", "type": 2, "country_code": "RS", "country_number": "381", "value": "381", "countryCode": "塞尔维亚", "country": "RS"}, {"id": 4186, "name": "塞舌尔", "name_en": "Seychelles", "type": 2, "country_code": "SC", "country_number": "248", "value": "248", "countryCode": "塞舌尔", "country": "SC"}, {"id": 4020, "name": "塞拉利昂", "name_en": "Sierra Leone", "type": 2, "country_code": "SL", "country_number": "232", "value": "232", "countryCode": "塞拉利昂", "country": "SL"}, {"id": 1015, "name": "新加坡", "name_en": "Singapore", "type": 2, "country_code": "SG", "country_number": "65", "value": "65", "countryCode": "新加坡", "country": "SG"}, {"id": 1073, "name": "斯洛伐克", "name_en": "Slovakia", "type": 2, "country_code": "SK", "country_number": "421", "value": "421", "countryCode": "斯洛伐克", "country": "SK"}, {"id": 1071, "name": "斯洛文尼亚", "name_en": "Slovenia", "type": 2, "country_code": "SI", "country_number": "386", "value": "386", "countryCode": "斯洛文尼亚", "country": "SI"}, {"id": 4164, "name": "所罗门群岛", "name_en": "Solomon Islands", "type": 2, "country_code": "SB", "country_number": "677", "value": "677", "countryCode": "所罗门群岛", "country": "SB"}, {"id": 4053, "name": "索马里", "name_en": "Somalia", "type": 2, "country_code": "SO", "country_number": "252", "value": "252", "countryCode": "索马里", "country": "SO"}, {"id": 1056, "name": "南非", "name_en": "South Africa", "type": 2, "country_code": "ZA", "country_number": "27", "value": "27", "countryCode": "南非", "country": "ZA"}, {"id": 4149, "name": "南乔治亚和南桑威奇群岛", "name_en": "South Georgia and the South Sandwich Islands", "type": 2, "country_code": "GS", "country_number": "500", "value": "500", "countryCode": "南乔治亚和南桑威奇群岛", "country": "GS"}, {"id": 1010, "name": "韩国", "name_en": "South Korea", "type": 2, "country_code": "KR", "country_number": "82", "value": "82", "countryCode": "韩国", "country": "KR"}, {"id": 4116, "name": "南苏丹", "name_en": "South Sudan", "type": 2, "country_code": "SS", "country_number": "211", "value": "211", "countryCode": "南苏丹", "country": "SS"}, {"id": 1034, "name": "西班牙", "name_en": "Spain", "type": 2, "country_code": "ES", "country_number": "34", "value": "34", "countryCode": "西班牙", "country": "ES"}, {"id": 1053, "name": "斯里兰卡", "name_en": "Sri Lanka", "type": 2, "country_code": "LK", "country_number": "94", "value": "94", "countryCode": "斯里兰卡", "country": "LK"}, {"id": 4040, "name": "苏丹", "name_en": "Sudan", "type": 2, "country_code": "SD", "country_number": "249", "value": "249", "countryCode": "苏丹", "country": "SD"}, {"id": 4121, "name": "苏里南", "name_en": "Suriname", "type": 2, "country_code": "SR", "country_number": "597", "value": "597", "countryCode": "苏里南", "country": "SR"}, {"id": 1043, "name": "瑞典", "name_en": "Sweden", "type": 2, "country_code": "SE", "country_number": "46", "value": "46", "countryCode": "瑞典", "country": "SE"}, {"id": 1044, "name": "瑞士", "name_en": "Switzerland", "type": 2, "country_code": "CH", "country_number": "41", "value": "41", "countryCode": "瑞士", "country": "CH"}, {"id": 4016, "name": "叙利亚", "name_en": "Syria", "type": 2, "country_code": "SY", "country_number": "963", "value": "963", "countryCode": "叙利亚", "country": "SY"}, {"id": 4092, "name": "圣多美和普林西比", "name_en": "São Tomé and Príncipe", "type": 2, "country_code": "ST", "country_number": "239", "value": "239", "countryCode": "圣多美和普林西比", "country": "ST"}, {"id": 1014, "name": "台湾", "name_en": "Taiwan", "type": 2, "country_code": "TW", "country_number": "886", "value": "886", "countryCode": "台湾", "country": "TW"}, {"id": 4111, "name": "塔吉克斯坦", "name_en": "Tajikistan", "type": 2, "country_code": "TJ", "country_number": "992", "value": "992", "countryCode": "塔吉克斯坦", "country": "TJ"}, {"id": 1065, "name": "坦桑尼亚", "name_en": "Tanzania", "type": 2, "country_code": "TZ", "country_number": "255", "value": "255", "countryCode": "坦桑尼亚", "country": "TZ"}, {"id": 1004, "name": "泰国", "name_en": "Thailand", "type": 2, "country_code": "TH", "country_number": "66", "value": "66", "countryCode": "泰国", "country": "TH"}, {"id": 4150, "name": "巴哈马", "name_en": "The Bahamas", "type": 2, "country_code": "BS", "country_number": "1242", "value": "1242", "countryCode": "巴哈马", "country": "BS"}, {"id": 4014, "name": "冈比亚", "name_en": "The Gambia", "type": 2, "country_code": "GM", "country_number": "220", "value": "220", "countryCode": "冈比亚", "country": "GM"}, {"id": 4029, "name": "多哥", "name_en": "Togo", "type": 2, "country_code": "TG", "country_number": "228", "value": "228", "countryCode": "多哥", "country": "TG"}, {"id": 4119, "name": "托克劳", "name_en": "Tokelau", "type": 2, "country_code": "TK", "country_number": "690", "value": "690", "countryCode": "托克劳", "country": "TK"}, {"id": 4172, "name": "汤加", "name_en": "Tonga", "type": 2, "country_code": "TO", "country_number": "676", "value": "676", "countryCode": "汤加", "country": "TO"}, {"id": 4194, "name": "特立尼达和多巴哥", "name_en": "Trinidad and Tobago", "type": 2, "country_code": "TT", "country_number": "1868", "value": "1868", "countryCode": "特立尼达和多巴哥", "country": "TT"}, {"id": 4235, "name": "突尼斯", "name_en": "Tunisia", "type": 2, "country_code": "TN", "country_number": "216", "value": "216", "countryCode": "突尼斯", "country": "TN"}, {"id": 1050, "name": "土耳其", "name_en": "Turkey", "type": 2, "country_code": "TR", "country_number": "90", "value": "90", "countryCode": "土耳其", "country": "TR"}, {"id": 4071, "name": "土库曼斯坦", "name_en": "Turkmenistan", "type": 2, "country_code": "TM", "country_number": "993", "value": "993", "countryCode": "土库曼斯坦", "country": "TM"}, {"id": 4159, "name": "特克斯和凯科斯群岛", "name_en": "Turks and Caicos Islands", "type": 2, "country_code": "TC", "country_number": "1649", "value": "1649", "countryCode": "特克斯和凯科斯群岛", "country": "TC"}, {"id": 4229, "name": "图瓦卢", "name_en": "Tuvalu", "type": 2, "country_code": "TV", "country_number": "688", "value": "688", "countryCode": "图瓦卢", "country": "TV"}, {"id": 4048, "name": "乌干达", "name_en": "Uganda", "type": 2, "country_code": "UG", "country_number": "256", "value": "256", "countryCode": "乌干达", "country": "UG"}, {"id": 1072, "name": "乌克兰", "name_en": "Ukraine", "type": 2, "country_code": "UA", "country_number": "380", "value": "380", "countryCode": "乌克兰", "country": "UA"}, {"id": 1023, "name": "阿拉伯联合酋长国", "name_en": "United Arab Emirates", "type": 2, "country_code": "AE", "country_number": "971", "value": "971", "countryCode": "阿拉伯联合酋长国", "country": "AE"}, {"id": 1032, "name": "英国", "name_en": "United Kingdom", "type": 2, "country_code": "GB", "country_number": "44", "value": "44", "countryCode": "英国", "country": "GB"}, {"id": 1028, "name": "美国", "name_en": "United States", "type": 2, "country_code": "US", "country_number": "1", "value": "1", "countryCode": "美国", "country": "US"}, {"id": 4109, "name": "乌拉圭", "name_en": "Uruguay", "type": 2, "country_code": "UY", "country_number": "598", "value": "598", "countryCode": "乌拉圭", "country": "UY"}, {"id": 4152, "name": "乌兹别克斯坦", "name_en": "Uzbekistan", "type": 2, "country_code": "UZ", "country_number": "998", "value": "998", "countryCode": "乌兹别克斯坦", "country": "UZ"}, {"id": 4215, "name": "瓦努阿图", "name_en": "Vanuatu", "type": 2, "country_code": "VU", "country_number": "678", "value": "678", "countryCode": "瓦努阿图", "country": "VU"}, {"id": 4138, "name": "梵蒂冈", "name_en": "Vatican City", "type": 2, "country_code": "VA", "country_number": "379", "value": "379", "countryCode": "梵蒂冈", "country": "VA"}, {"id": 4013, "name": "委内瑞拉", "name_en": "Venezuela", "type": 2, "country_code": "VE", "country_number": "58", "value": "58", "countryCode": "委内瑞拉", "country": "VE"}, {"id": 1013, "name": "越南", "name_en": "Vietnam", "type": 2, "country_code": "VN", "country_number": "84", "value": "84", "countryCode": "越南", "country": "VN"}, {"id": 4191, "name": "也门", "name_en": "Yemen", "type": 2, "country_code": "YE", "country_number": "967", "value": "967", "countryCode": "也门", "country": "YE"}, {"id": 4200, "name": "赞比亚", "name_en": "Zambia", "type": 2, "country_code": "ZM", "country_number": "260", "value": "260", "countryCode": "赞比亚", "country": "ZM"}, {"id": 4201, "name": "津巴布韦", "name_en": "Zimbabwe", "type": 2, "country_code": "ZW", "country_number": "263", "value": "263", "countryCode": "津巴布韦", "country": "ZW"}, {"id": 703703, "name": "joe-国家名-Simplified Chinese", "name_en": "joe-国家名-American English", "type": 2, "country_code": "JCC2", "country_number": "", "value": "", "countryCode": "joe-国家名-Simplified Chinese", "country": "JCC2"}, {"id": 703705, "name": "joe-新增标准行政区域00133", "name_en": "joe-新增标准行政区域00133", "type": 2, "country_code": "joe0001", "country_number": "", "value": "", "countryCode": "joe-新增标准行政区域00133", "country": "joe0001"}, {"id": 702392, "name": "国家测试", "name_en": "国家测试", "type": 2, "country_code": "WE", "country_number": "", "value": "", "countryCode": "国家测试", "country": "WE"}, {"id": 702394, "name": "国家测试2", "name_en": "国家测试2", "type": 2, "country_code": "RE", "country_number": "", "value": "", "countryCode": "国家测试2", "country": "RE"}]