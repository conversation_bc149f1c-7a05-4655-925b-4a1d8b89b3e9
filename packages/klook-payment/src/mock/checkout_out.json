{"error": {"code": "", "message": ""}, "result": {"config_version_code": 5, "order_info": {"deadline": "2024-05-27T07:33:17Z"}, "payment_info": {"price_info": {"price": {"currency": "KRW", "amount": "56129", "value": 56129}, "pay_price": {"currency": "KRW", "amount": "56129", "value": 56129}, "pay_price_select_currency": {"currency": "KRW", "amount": "56129", "value": 56129}, "original_pay_price_select_currency": {"currency": "KRW", "amount": "56129", "value": 56129}, "original_price": {"currency": "KRW", "amount": "56129", "value": 56129}, "original_pay_price": {"currency": "KRW", "amount": "56129", "value": 56129}, "total_saving": {"currency": "KRW", "amount": "0", "value": 0}, "order_price": {"currency": "KRW", "amount": "56129", "value": 56129}, "payment_amount": {"currency": "KRW", "amount": "56129", "value": 56129}, "list": []}, "default_method_key": "creditcard-4447NjlwR2U3dUQ4NFZSZ0FaQnwZ7Pa8eW/9GhswkmjBtLc=", "methods": [{"name": "<PERSON><PERSON><PERSON> (Ali<PERSON>y+™ partner)", "action": "select", "method_key": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "token": "", "icons": ["https://res.klook.com/image/upload/v1638515728/kakao%20pay/kakao_pay_logo_24.png?width=48&height=48"], "card_type": "", "method_input": null, "method_banner": null, "model_list": [{"type": "", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}], "code": "", "disable": false}, {"name": "Toss Pay", "action": "select", "method_key": "tosswallet", "token": "", "icons": ["https://res.klook.com/image/upload/v1683699516/UED_new/Foundation/Payment/KR%20bank%20icon/48/Payment_KR_bank_logo_Toss_Bank_48.png?width=48&height=48"], "card_type": "", "method_input": null, "method_banner": null, "model_list": [{"type": "", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}], "code": "", "disable": false}, {"name": "Korean domestic card", "action": "drop_down", "method_key": "tosslocal", "token": "", "icons": ["https://res.klook.com/image/upload/v1576232859/%E6%94%AF%E4%BB%98%E6%96%B9%E5%BC%8FMobile/Online%20Banking/icon_payment_online_online_banking.png?width=48&height=48"], "sub_options": [{"name": "Toss Bank", "action": "select", "method_key": "toss_bank", "token": "", "icons": ["https://res.klook.com/image/upload/v1683699516/UED_new/Foundation/Payment/KR%20bank%20icon/48/Payment_KR_bank_logo_Toss_Bank_48.png?width=48&height=48"], "card_type": "", "method_input": null, "method_banner": null, "model_list": [{"type": "install_payment", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}, {"type": "", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}], "code": "", "disable": false}, {"name": "KB Kookmin Card", "action": "select", "method_key": "kb_kookmin_card", "token": "", "icons": ["https://res.klook.com/image/upload/v1683699517/UED_new/Foundation/Payment/KR%20bank%20icon/48/Payment_KR_bank_logo_K<PERSON>_<PERSON>okmin_Card_48.png?width=48&height=48"], "card_type": "", "method_input": null, "method_banner": null, "model_list": [{"type": "install_payment", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}, {"type": "", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}], "code": "", "disable": false, "free_install_payment_plan": [2, 3, 4, 5]}, {"name": "<PERSON>han Card", "action": "select", "method_key": "shinhan_card", "token": "", "icons": ["https://res.klook.com/image/upload/v1683699517/UED_new/Foundation/Payment/KR%20bank%20icon/48/Payment_KR_bank_logo_<PERSON>han_Card_48.png?width=48&height=48"], "card_type": "", "method_input": null, "method_banner": null, "model_list": [{"type": "install_payment", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}, {"type": "", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}], "code": "", "disable": false, "free_install_payment_plan": [2, 3, 4, 5, 6]}, {"name": "Hyundai Card", "action": "select", "method_key": "hyundai_card", "token": "", "icons": ["https://res.klook.com/image/upload/v1683699518/UED_new/Foundation/Payment/KR%20bank%20icon/48/Payment_KR_bank_logo_Hyundai_Card_48.png?width=48&height=48"], "card_type": "", "method_input": null, "method_banner": null, "model_list": [{"type": "install_payment", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}, {"type": "", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}], "code": "", "disable": false}, {"name": "<PERSON><PERSON>", "action": "select", "method_key": "hana_card", "token": "", "icons": ["https://res.klook.com/image/upload/v1683699516/UED_new/Foundation/Payment/KR%20bank%20icon/48/Payment_KR_bank_logo_Hana_Card_48.png?width=48&height=48"], "card_type": "", "method_input": null, "method_banner": null, "model_list": [{"type": "install_payment", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}, {"type": "", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}], "code": "", "disable": false}, {"name": "Samsung Card", "action": "select", "method_key": "samsung_card", "token": "", "icons": ["https://res.klook.com/image/upload/v1683858388/UED_new/Foundation/Payment/KR%20bank%20icon/48/Payment_KR_bank_logo_Samsung_Card_48.png?width=48&height=48"], "card_type": "", "method_input": null, "method_banner": null, "model_list": [{"type": "install_payment", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}, {"type": "", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}], "code": "", "disable": false}, {"name": "<PERSON><PERSON><PERSON>", "action": "select", "method_key": "woori_card", "token": "", "icons": ["https://res.klook.com/image/upload/v1683709124/UED_new/Foundation/Payment/KR%20bank%20icon/48/Payment_KR_bank_logo_Woori_BC_Card_BC_purchase__48.png?width=48&height=48"], "card_type": "", "method_input": null, "method_banner": null, "model_list": [{"type": "install_payment", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}, {"type": "", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}], "code": "", "disable": false, "free_install_payment_plan": [2, 3, 4, 5, 6]}, {"name": "NH Nonghyup Card", "action": "select", "method_key": "nh_nonghyup_card", "token": "", "icons": ["https://res.klook.com/image/upload/v1683699517/UED_new/Foundation/Payment/KR%20bank%20icon/48/Payment_KR_bank_logo_NH_Nonghyup_Card_48.png?width=48&height=48"], "card_type": "", "method_input": null, "method_banner": null, "model_list": [{"type": "install_payment", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}, {"type": "", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}], "code": "", "disable": false, "free_install_payment_plan": [2, 3, 4, 5, 6]}, {"name": "BC Card (ISP)", "action": "select", "method_key": "bc_card", "token": "", "icons": ["https://res.klook.com/image/upload/v1683699517/UED_new/Foundation/Payment/KR%20bank%20icon/48/Payment_KR_bank_logo_BC_Card_48.png?width=48&height=48"], "card_type": "", "method_input": null, "method_banner": null, "model_list": [{"type": "install_payment", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}, {"type": "", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}], "code": "", "disable": false, "free_install_payment_plan": [2, 3, 4, 5, 6]}, {"name": "Lotte Card", "action": "select", "method_key": "lotte_card", "token": "", "icons": ["https://res.klook.com/image/upload/v1683699519/UED_new/Foundation/Payment/KR%20bank%20icon/48/Payment_KR_bank_logo_Lotte_Card_48.png?width=48&height=48"], "card_type": "", "method_input": null, "method_banner": null, "model_list": [{"type": "install_payment", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}, {"type": "", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}], "code": "", "disable": false, "free_install_payment_plan": [2, 3, 4]}, {"name": "Kakao Bank", "action": "select", "method_key": "kakao_bank", "token": "", "icons": ["https://res.klook.com/image/upload/v1683699517/UED_new/Foundation/Payment/KR%20bank%20icon/48/Payment_KR_bank_logo_Kakao_Bank_48.png?width=48&height=48"], "card_type": "", "method_input": null, "method_banner": null, "model_list": [{"type": "install_payment", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}, {"type": "", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}], "code": "", "disable": false}], "card_type": "", "method_input": null, "method_banner": null, "model_list": [{"type": "", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}], "code": "", "disable": false}, {"name": "Naver Pay", "action": "drop_down", "method_key": "<PERSON><PERSON>ay", "token": "", "icons": ["https://res.klook.com/image/upload/v1700705296/UED_new/Foundation/Payment/Naver/Naver_48_48.png?width=48&height=48"], "sub_options": [{"name": "Naver Pay", "action": "select", "method_key": "<PERSON><PERSON>ay", "token": "", "icons": ["https://res.klook.com/image/upload/v1700705296/UED_new/Foundation/Payment/Naver/Naver_48_48.png?width=48&height=48"], "card_type": "", "method_input": null, "method_banner": null, "model_list": [{"type": "", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}], "code": "", "disable": false}, {"name": "Naver Pay Point", "action": "select", "method_key": "naverpay_point", "token": "", "icons": [], "card_type": "", "method_input": null, "method_banner": null, "model_list": [{"type": "", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}], "code": "", "disable": false}], "card_type": "", "method_input": null, "method_banner": null, "model_list": [{"type": "", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}], "code": "", "disable": false}, {"name": "PAYCO", "action": "select", "method_key": "payco", "token": "", "icons": ["https://res.klook.com/image/upload/v1607506121/%E6%94%AF%E4%BB%98%E6%96%B9%E5%BC%8FMobile/General/payment_methed_payco_mobile.png?width=48&height=48"], "card_type": "", "method_input": null, "method_banner": null, "model_list": [{"type": "", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}], "code": "", "disable": false}, {"name": "信用卡/借记卡", "action": "expand", "method_key": "creditcard", "token": "", "icons": ["https://res.klook.com/image/upload/v1577178193/%E6%94%AF%E4%BB%98%E6%96%B9%E5%BC%8FMobile/Credit%20Card/icon_payment_credit_card_manage_56.png?width=112&height=112"], "sub_options": [{"name": "****4440", "action": "select", "method_key": "creditcard-4440NjlwR2U3dUQ4NFZSZ0FaQnwZ7Pa8eW/9GhswkmjBtsLc=", "token": "NjlwR2U3dUQ4NFZSZ0FaQnwZ7Pa8eW/9GhswkmjBtLc=", "icons": ["https://res.klook.com/image/upload/v1657795549/ued/Business%20Services/icon_payment_credit_card_master_112x112.png?width=112&height=112"], "right_tips": {"type": "notice", "dialog": {"title": "", "message": "虽然Klook客路不会收取任何列明金额以外的费用，但你的发卡银行可能会收取海外交易手续费。更多详情请咨询你的发卡银行。", "positive": "", "negative": "", "customized_top": null}}, "card_type": "", "method_input": null, "method_banner": null, "model_list": [{"type": "warning", "title": "", "content": "", "icon": "", "multi_lines": false, "dialog": {"title": "", "message": "虽然Klook客路不会收取任何列明金额以外的费用，但你的发卡银行可能会收取海外交易手续费。更多详情请咨询你的发卡银行。", "positive": "确定", "negative": "", "customized_top": null}, "items": null}, {"type": "", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}], "code": "", "disable": false}, {"name": "****4441", "action": "select", "method_key": "creditcard-4441NjlwR2U3dUQ4NFZSZ0FaQnwZ7Pa8eW/9GhswkmjBtLc=", "token": "NjlwR2U3dUQ4NFZSZ0FaQnwZ7Pa8eW/9GhswkmjBtLc=", "icons": ["https://res.klook.com/image/upload/v1657795549/ued/Business%20Services/icon_payment_credit_card_master_112x112.png?width=112&height=112"], "right_tips": {"type": "notice", "dialog": {"title": "", "message": "虽然Klook客路不会收取任何列明金额以外的费用，但你的发卡银行可能会收取海外交易手续费。更多详情请咨询你的发卡银行。", "positive": "", "negative": "", "customized_top": null}}, "card_type": "", "method_input": null, "method_banner": null, "model_list": [{"type": "warning", "title": "", "content": "", "icon": "", "multi_lines": false, "dialog": {"title": "", "message": "虽然Klook客路不会收取任何列明金额以外的费用，但你的发卡银行可能会收取海外交易手续费。更多详情请咨询你的发卡银行。", "positive": "确定", "negative": "", "customized_top": null}, "items": null}, {"type": "", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}], "code": "", "disable": false}, {"name": "****4442", "action": "select", "method_key": "creditcard-4442NjlwR2U3dUQ4NFZSZ0FaQnwZ7Pa8eW/9GhswkmjBtLc=", "token": "NjlwR2U3dUQ4NFZSZ0FaQnwZ7Pa8eW/9GhswkmjBtLc=", "icons": ["https://res.klook.com/image/upload/v1657795549/ued/Business%20Services/icon_payment_credit_card_master_112x112.png?width=112&height=112"], "right_tips": {"type": "notice", "dialog": {"title": "", "message": "虽然Klook客路不会收取任何列明金额以外的费用，但你的发卡银行可能会收取海外交易手续费。更多详情请咨询你的发卡银行。", "positive": "", "negative": "", "customized_top": null}}, "card_type": "", "method_input": null, "method_banner": null, "model_list": [{"type": "warning", "title": "", "content": "", "icon": "", "multi_lines": false, "dialog": {"title": "", "message": "虽然Klook客路不会收取任何列明金额以外的费用，但你的发卡银行可能会收取海外交易手续费。更多详情请咨询你的发卡银行。", "positive": "确定", "negative": "", "customized_top": null}, "items": null}, {"type": "", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}], "code": "", "disable": false}, {"name": "****4443", "action": "select", "method_key": "creditcard-4443NjlwR2U3dUQ4NFZSZ0FaQnwZ7Pa8eW/9GhswkmjBtLc=", "token": "NjlwR2U3dUQ4NFZSZ0FaQnwZ7Pa8eW/9GhswkmjBtLc=", "icons": ["https://res.klook.com/image/upload/v1657795549/ued/Business%20Services/icon_payment_credit_card_master_112x112.png?width=112&height=112"], "right_tips": {"type": "notice", "dialog": {"title": "", "message": "虽然Klook客路不会收取任何列明金额以外的费用，但你的发卡银行可能会收取海外交易手续费。更多详情请咨询你的发卡银行。", "positive": "", "negative": "", "customized_top": null}}, "card_type": "", "method_input": null, "method_banner": null, "model_list": [{"type": "warning", "title": "", "content": "", "icon": "", "multi_lines": false, "dialog": {"title": "", "message": "虽然Klook客路不会收取任何列明金额以外的费用，但你的发卡银行可能会收取海外交易手续费。更多详情请咨询你的发卡银行。", "positive": "确定", "negative": "", "customized_top": null}, "items": null}, {"type": "", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}], "code": "", "disable": false}, {"name": "****4444", "action": "select", "method_key": "creditcard-NjlwR2U3dUQ4NFZSZ0FaQnwZ7Pa8eW/9GhswkmjBtLc=", "token": "NjlwR2U3dUQ4NFZSZ0FaQnwZ7Pa8eW/9GhswkmjBtLc=", "icons": ["https://res.klook.com/image/upload/v1657795549/ued/Business%20Services/icon_payment_credit_card_master_112x112.png?width=112&height=112"], "right_tips": {"type": "notice", "dialog": {"title": "", "message": "虽然Klook客路不会收取任何列明金额以外的费用，但你的发卡银行可能会收取海外交易手续费。更多详情请咨询你的发卡银行。", "positive": "", "negative": "", "customized_top": null}}, "card_type": "", "method_input": null, "method_banner": null, "model_list": [{"type": "warning", "title": "", "content": "", "icon": "", "multi_lines": false, "dialog": {"title": "", "message": "虽然Klook客路不会收取任何列明金额以外的费用，但你的发卡银行可能会收取海外交易手续费。更多详情请咨询你的发卡银行。", "positive": "确定", "negative": "", "customized_top": null}, "items": null}, {"type": "", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}], "code": "", "disable": false}, {"name": "****4445", "action": "select", "method_key": "creditcard-4445NjlwR2U3dUQ4NFZSZ0FaQnwZ7Pa8eW/9GhswkmjBtLc=", "token": "NjlwR2U3dUQ4NFZSZ0FaQnwZ7Pa8eW/9GhswkmjBtLc=", "icons": ["https://res.klook.com/image/upload/v1657795549/ued/Business%20Services/icon_payment_credit_card_master_112x112.png?width=112&height=112"], "right_tips": {"type": "notice", "dialog": {"title": "", "message": "虽然Klook客路不会收取任何列明金额以外的费用，但你的发卡银行可能会收取海外交易手续费。更多详情请咨询你的发卡银行。", "positive": "", "negative": "", "customized_top": null}}, "card_type": "", "method_input": null, "method_banner": null, "model_list": [{"type": "warning", "title": "", "content": "", "icon": "", "multi_lines": false, "dialog": {"title": "", "message": "虽然Klook客路不会收取任何列明金额以外的费用，但你的发卡银行可能会收取海外交易手续费。更多详情请咨询你的发卡银行。", "positive": "确定", "negative": "", "customized_top": null}, "items": null}, {"type": "", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}], "code": "", "disable": false}, {"name": "****4446", "action": "select", "method_key": "creditcard-4446NjlwR2U3dUQ4NFZSZ0FaQnwZ7Pa8eW/9GhswkmjBtLc=", "token": "NjlwR2U3dUQ4NFZSZ0FaQnwZ7Pa8eW/9GhswkmjBtLc=", "icons": ["https://res.klook.com/image/upload/v1657795549/ued/Business%20Services/icon_payment_credit_card_master_112x112.png?width=112&height=112"], "right_tips": {"type": "notice", "dialog": {"title": "", "message": "虽然Klook客路不会收取任何列明金额以外的费用，但你的发卡银行可能会收取海外交易手续费。更多详情请咨询你的发卡银行。", "positive": "", "negative": "", "customized_top": null}}, "card_type": "", "method_input": null, "method_banner": null, "model_list": [{"type": "warning", "title": "", "content": "", "icon": "", "multi_lines": false, "dialog": {"title": "", "message": "虽然Klook客路不会收取任何列明金额以外的费用，但你的发卡银行可能会收取海外交易手续费。更多详情请咨询你的发卡银行。", "positive": "确定", "negative": "", "customized_top": null}, "items": null}, {"type": "", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}], "code": "", "disable": false}, {"name": "****4447", "action": "select", "method_key": "creditcard-4447NjlwR2U3dUQ4NFZSZ0FaQnwZ7Pa8eW/9GhswkmjBtLc=", "token": "NjlwR2U3dUQ4NFZSZ0FaQnwZ7Pa8eW/9GhswkmjBtLc=", "icons": ["https://res.klook.com/image/upload/v1657795549/ued/Business%20Services/icon_payment_credit_card_master_112x112.png?width=112&height=112"], "right_tips": {"type": "notice", "dialog": {"title": "", "message": "虽然Klook客路不会收取任何列明金额以外的费用，但你的发卡银行可能会收取海外交易手续费。更多详情请咨询你的发卡银行。", "positive": "", "negative": "", "customized_top": null}}, "card_type": "", "method_input": null, "method_banner": null, "model_list": [{"type": "warning", "title": "", "content": "", "icon": "", "multi_lines": false, "dialog": {"title": "", "message": "虽然Klook客路不会收取任何列明金额以外的费用，但你的发卡银行可能会收取海外交易手续费。更多详情请咨询你的发卡银行。", "positive": "确定", "negative": "", "customized_top": null}, "items": null}, {"type": "", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}], "code": "", "disable": false}, {"name": "****4448", "action": "select", "method_key": "creditcard-4448NjlwR2U3dUQ4NFZSZ0FaQnwZ7Pa8eW/9GhswkmjBtLc=", "token": "NjlwR2U3dUQ4NFZSZ0FaQnwZ7Pa8eW/9GhswkmjBtLc=", "icons": ["https://res.klook.com/image/upload/v1657795549/ued/Business%20Services/icon_payment_credit_card_master_112x112.png?width=112&height=112"], "right_tips": {"type": "notice", "dialog": {"title": "", "message": "虽然Klook客路不会收取任何列明金额以外的费用，但你的发卡银行可能会收取海外交易手续费。更多详情请咨询你的发卡银行。", "positive": "", "negative": "", "customized_top": null}}, "card_type": "", "method_input": null, "method_banner": null, "model_list": [{"type": "warning", "title": "", "content": "", "icon": "", "multi_lines": false, "dialog": {"title": "", "message": "虽然Klook客路不会收取任何列明金额以外的费用，但你的发卡银行可能会收取海外交易手续费。更多详情请咨询你的发卡银行。", "positive": "确定", "negative": "", "customized_top": null}, "items": null}, {"type": "", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}], "code": "", "disable": false}], "card_type": "", "method_input": null, "method_banner": null, "model_list": [{"type": "", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}], "code": "", "disable": false}, {"name": "<PERSON><PERSON><PERSON> (Ali<PERSON>y+™ partner)", "action": "select", "method_key": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "token": "", "icons": ["https://res.klook.com/image/upload/v1638515728/kakao%20pay/kakao_pay_logo_24.png?width=48&height=48"], "card_type": "", "method_input": null, "method_banner": null, "model_list": [{"type": "", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}], "code": "", "disable": false}], "methods_hide": [], "supported_cards": {"types": [{"type": "Visa", "icon": "https://res.klook.com/image/upload/v1698376399/UED_new/Foundation/Payment/Visa/Payment_Visa_112.png?width=112&height=112", "provider": "adyen", "disable": false}, {"type": "MasterCard", "icon": "https://res.klook.com/image/upload/v1657795549/ued/Business%20Services/icon_payment_credit_card_master_112x112.png?width=112&height=112", "provider": "adyen", "disable": false}, {"type": "American Express", "icon": "https://res.klook.com/image/upload/v1624503441/ued/Payment%20Method/methed-Amex_Mweb.png?width=48&height=48", "provider": "adyen", "disable": false}, {"type": "JCB", "icon": "https://res.klook.com/image/upload/v1576233398/%E6%94%AF%E4%BB%98%E6%96%B9%E5%BC%8FMobile/Credit%20Card/icon_payment_credit_card_jcb_56.png?width=112&height=112", "provider": "adyen", "disable": false}, {"type": "UnionPay", "icon": "https://res.klook.com/image/upload/v1576233398/%E6%94%AF%E4%BB%98%E6%96%B9%E5%BC%8FMobile/Credit%20Card/icon_payment_credit_card_union_pay_56.png?width=112&height=112", "provider": "adyen", "disable": false}, {"type": "Korean Local Card", "provider": "adyen", "disable": false}]}, "terms": null, "new_card_info": {}, "support_new_payment": false, "terms_tips": null, "asset_voucher_info": {"selected_type": "", "selected_types": [], "available_types": [], "list": {"common_voucher": [], "payment_coupon": []}}, "auto_submit": false, "global_tips": [], "is_agent_channel": false, "disable_payment_coupon": false}}, "success": true}