{"config_version_code": 5, "payment_info": {"price_info": {"original_price": {"amount": "82.6", "currency": "CNY", "value": 82.6}, "pay_price": {"amount": "82.6", "currency": "CNY", "value": 82.6}, "original_pay_price": {"amount": "82.6", "currency": "CNY", "value": 82.6}, "original_pay_price_select_currency": {"amount": "82.6", "currency": "CNY", "value": 82.6}, "pay_price_select_currency": {"amount": "82.6", "currency": "CNY", "value": 82.6}, "price": {"amount": "82.6", "currency": "CNY", "value": 82.6}, "payment_amount": {"amount": "82.6", "currency": "CNY", "value": 82.6}, "list": [{"symbol": "", "amount": "9.3", "currency": "CNY", "type": "Discount Amount", "value": 0, "content": "优惠抵扣"}], "total_saving": {"amount": "9.3", "currency": "CNY", "value": 9.3}}, "auto_submit": false, "global_tips": [], "methods_hide": [], "methods": [{"model_list": [{"icon": "", "multi_lines": false, "title": "", "type": "", "content": ""}, {"type": "saved", "title": "Save for future purchases", "content": "No re-verification required"}, {"type": "deleted", "title": "Account on file", "content": "Delete", "icon": "https://res.klook.com/image/upload/v1576232855/%E6%94%AF%E4%BB%98%E6%96%B9%E5%BC%8FMobile/General/icon_payment_alipay.png?width=112&height=112", "dialog": {"title": "小 i 的内容", "message": "", "positive": "ok", "negative": "cancel"}, "delete": {"confirm_dialog": {"title": "二次确认弹层", "message": "二次确认弹层内容", "positive": "ok", "negative": "cancel"}}}], "code": "", "disable": false, "name": "银联支付", "payment_coupon_code": "", "action": "select", "card_type": "", "icons": ["https://res.klook.com/image/upload/v1576233398/%E6%94%AF%E4%BB%98%E6%96%B9%E5%BC%8FMobile/Credit%20Card/icon_payment_credit_card_union_pay_56.png?width=112&height=112"], "method_key": "unionpay", "token": "456"}, {"model_list": [{"icon": "", "multi_lines": false, "title": "", "type": "", "content": ""}], "sub_options": [{"name": "****4444", "action": "select", "method_key": "creditcard-NjlwR2U3dUQ4NFZSZ0FaQnwZ7Pa8eW/9GhswkmjBtLc=", "token": "NjlwR2U3dUQ4NFZSZ0FaQnwZ7Pa8eW/9GhswkmjBtLc=", "icons": ["https://res.klook.com/image/upload/v1657795549/ued/Business%20Services/icon_payment_credit_card_master_112x112.png?width=112&height=112"], "right_tips": {"type": "notice", "dialog": {"title": "", "message": "虽然Klook客路不会收取任何列明金额以外的费用，但你的发卡银行可能会收取海外交易手续费。更多详情请咨询你的发卡银行。", "positive": "", "negative": "", "customized_top": null}}, "card_type": "", "method_input": null, "method_banner": null, "model_list": [{"type": "warning", "title": "", "content": "", "icon": "", "multi_lines": false, "dialog": {"title": "", "message": "虽然Klook客路不会收取任何列明金额以外的费用，但你的发卡银行可能会收取海外交易手续费。更多详情请咨询你的发卡银行。", "positive": "确定", "negative": "", "customized_top": null}, "items": null}, {"type": "input", "title": "请输入卡号/请输入 CVV", "input": {"validate_type": "card_cvv", "hint": "未输入状态的提示"}}, {"type": "", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}], "code": "", "disable": false}], "code": "", "disable": false, "name": "信用卡/借记卡", "payment_coupon_code": "", "action": "expand", "card_type": "", "icons": ["https://res.klook.com/image/upload/v1577178193/%E6%94%AF%E4%BB%98%E6%96%B9%E5%BC%8FMobile/Credit%20Card/icon_payment_credit_card_manage_56.png?width=112&height=112"], "method_key": "creditcard", "token": ""}], "payment_support_type": [{"content": "Pay later", "key": "paylater", "tips": {"title": "Pay later", "content": "Pay later lets you make a reservation without paying upfront!\n\nNo deposit is required, but you'll need to add an authorized credit/debit card when making your reservation.\n\nPayment will be deducted automatically from your authorized credit/debit card before check-in.\n\nThe payment date will depend on your selected product, and will be made known to you while making your reservation.\n\nSee these [Terms and Conditions](https://www.klook.com/en-US/conditions/) for more details."}}, {"content": "Pay now", "key": "paynow"}], "is_agent_channel": false, "asset_voucher_info": {"selected_types": [], "list": {"common_voucher": [], "payment_coupon": []}, "available_types": [], "selected_type": ""}, "new_card_info": {}, "default_method_key": "creditcard-NjlwR2U3dUQ4NFZSZ0FaQnwZ7Pa8eW/9GhswkmjBtLc=", "disable_payment_coupon": false, "supported_cards": {"types": [{"provider": "stripe", "disable": false, "icon": "https://res.klook.com/image/upload/v1657795549/ued/Business%20Services/icon_payment_credit_card_master_112x112.png?width=112&height=112", "type": "MasterCard"}, {"provider": "adyen", "disable": false, "icon": "https://res.klook.com/image/upload/v1624503441/ued/Payment%20Method/methed-Amex_Mweb.png?width=48&height=48", "type": "American Express"}, {"provider": "adyen", "disable": false, "icon": "https://res.klook.com/image/upload/v1576233398/%E6%94%AF%E4%BB%98%E6%96%B9%E5%BC%8FMobile/Credit%20Card/icon_payment_credit_card_jcb_56.png?width=112&height=112", "type": "JCB"}]}, "payment_type": "paynow", "support_new_payment": false}, "order_info": {"deadline": "0001-01-01T00:00:00Z"}}