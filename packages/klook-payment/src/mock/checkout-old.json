{"error": {"code": "", "message": ""}, "result": {"config_version_code": 5, "order_info": {"deadline": "2024-05-17T03:30:59Z"}, "payment_info": {"payment_support_type": [{"content": "Pay later", "key": "paylater", "tips": {"title": "Pay later", "content": "Pay later lets you make a reservation without paying upfront!\n\nNo deposit is required, but you'll need to add an authorized credit/debit card when making your reservation.\n\nPayment will be deducted automatically from your authorized credit/debit card before check-in.\n\nThe payment date will depend on your selected product, and will be made known to you while making your reservation.\n\nSee these [Terms and Conditions](https://www.klook.com/en-US/conditions/) for more details."}}, {"content": "Pay now", "key": "paynow"}], "payment_type": "paylater", "price_info": {"price": {"currency": "CNY", "amount": "1722.5", "value": 1722.5}, "pay_price": {"currency": "CNY", "amount": "1722.5", "value": 1722.5}, "pay_price_select_currency": {"currency": "CNY", "amount": "1722.5", "value": 1722.5}, "original_pay_price_select_currency": {"currency": "CNY", "amount": "1722.5", "value": 1722.5}, "original_price": {"currency": "CNY", "amount": "1722.5", "value": 1722.5}, "original_pay_price": {"currency": "CNY", "amount": "1722.5", "value": 1722.5}, "total_saving": {"currency": "CNY", "amount": "0", "value": 0}, "order_price": {"currency": "CNY", "amount": "1722.5", "value": 1722.5}, "payment_amount": {"currency": "CNY", "amount": "1722.5", "value": 1722.5}, "list": []}, "default_method_key": "tosslocal", "__supported_methods": [{"type": "pay_now", "default_method_key": "unionpay", "methods": [{"name": "<PERSON><PERSON><PERSON> (Ali<PERSON>y+™ partner)", "action": "select", "method_key": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "token": "", "icons": ["https://res.klook.com/image/upload/v1638515728/kakao%20pay/kakao_pay_logo_24.png?width=48&height=48"], "card_type": "", "method_input": null, "method_banner": null, "model_list": [{"type": "", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}], "code": "", "disable": false}, {"name": "Dragonpay", "action": "drop_down", "method_key": "dragonpay", "token": "", "icons": ["https://res.klook.com/image/upload/v1583830589/%E6%94%AF%E4%BB%98%E6%96%B9%E5%BC%8FWeb/%E4%B8%80%E7%BA%A7%E6%94%AF%E4%BB%98%E6%96%B9%E5%BC%8F/methed-dragonpay-new.png?width=70&height=42"], "bottom_tips": {"type": "info", "content": "你所選的付款方式不支援退款"}, "sub_options": [{"name": "Online Transactions", "action": "expand", "method_key": "dragonpay_online", "token": "", "icons": [], "sub_options": [{"name": "E-Banking", "action": "select", "method_key": "dragonpay_online-e_banking", "token": "", "icons": [], "card_type": "", "method_input": null, "method_banner": null, "model_list": [{"type": "", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}], "code": "", "disable": false}], "card_type": "", "method_input": null, "method_banner": null, "model_list": [{"type": "", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}], "code": "", "disable": false}], "card_type": "", "method_input": null, "method_banner": null, "model_list": [{"type": "text_below", "title": "", "content": "你所選的付款方式不支援退款", "icon": "", "multi_lines": true, "items": null}, {"type": "", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}, {"type": "", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}], "code": "", "disable": false}, {"name": "Toss Pay", "action": "select", "method_key": "tosswallet", "token": "", "icons": ["https://res.klook.com/image/upload/v1683699516/UED_new/Foundation/Payment/KR%20bank%20icon/48/Payment_KR_bank_logo_Toss_Bank_48.png?width=48&height=48"], "card_type": "", "method_input": null, "method_banner": null, "model_list": [{"type": "", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}], "code": "", "disable": false}, {"name": "Korean domestic card", "action": "drop_down", "method_key": "tosslocal", "token": "", "icons": ["https://res.klook.com/image/upload/v1576232859/%E6%94%AF%E4%BB%98%E6%96%B9%E5%BC%8FMobile/Online%20Banking/icon_payment_online_online_banking.png?width=48&height=48"], "sub_options": [{"name": "Toss Bank", "action": "select", "method_key": "toss_bank", "token": "", "icons": ["https://res.klook.com/image/upload/v1683699516/UED_new/Foundation/Payment/KR%20bank%20icon/48/Payment_KR_bank_logo_Toss_Bank_48.png?width=48&height=48"], "card_type": "", "method_input": null, "method_banner": null, "model_list": [{"type": "install_payment", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}, {"type": "", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}], "code": "", "disable": false}, {"name": "KB Kookmin Card", "action": "select", "method_key": "kb_kookmin_card", "token": "", "icons": ["https://res.klook.com/image/upload/v1683699517/UED_new/Foundation/Payment/KR%20bank%20icon/48/Payment_KR_bank_logo_K<PERSON>_<PERSON>okmin_Card_48.png?width=48&height=48"], "card_type": "", "method_input": null, "method_banner": null, "model_list": [{"type": "install_payment", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}, {"type": "", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}], "code": "", "disable": false, "free_install_payment_plan": [2, 3, 4, 5]}, {"name": "<PERSON>han Card", "action": "select", "method_key": "shinhan_card", "token": "", "icons": ["https://res.klook.com/image/upload/v1683699517/UED_new/Foundation/Payment/KR%20bank%20icon/48/Payment_KR_bank_logo_<PERSON>han_Card_48.png?width=48&height=48"], "card_type": "", "method_input": null, "method_banner": null, "model_list": [{"type": "install_payment", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}, {"type": "", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}], "code": "", "disable": false, "free_install_payment_plan": [2, 3, 4, 5, 6]}, {"name": "Hyundai Card", "action": "select", "method_key": "hyundai_card", "token": "", "icons": ["https://res.klook.com/image/upload/v1683699518/UED_new/Foundation/Payment/KR%20bank%20icon/48/Payment_KR_bank_logo_Hyundai_Card_48.png?width=48&height=48"], "card_type": "", "method_input": null, "method_banner": null, "model_list": [{"type": "install_payment", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}, {"type": "", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}], "code": "", "disable": false}, {"name": "<PERSON><PERSON>", "action": "select", "method_key": "hana_card", "token": "", "icons": ["https://res.klook.com/image/upload/v1683699516/UED_new/Foundation/Payment/KR%20bank%20icon/48/Payment_KR_bank_logo_Hana_Card_48.png?width=48&height=48"], "card_type": "", "method_input": null, "method_banner": null, "model_list": [{"type": "install_payment", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}, {"type": "", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}], "code": "", "disable": false}, {"name": "Samsung Card", "action": "select", "method_key": "samsung_card", "token": "", "icons": ["https://res.klook.com/image/upload/v1683858388/UED_new/Foundation/Payment/KR%20bank%20icon/48/Payment_KR_bank_logo_Samsung_Card_48.png?width=48&height=48"], "card_type": "", "method_input": null, "method_banner": null, "model_list": [{"type": "install_payment", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}, {"type": "", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}], "code": "", "disable": false}, {"name": "<PERSON><PERSON><PERSON>", "action": "select", "method_key": "woori_card", "token": "", "icons": ["https://res.klook.com/image/upload/v1683709124/UED_new/Foundation/Payment/KR%20bank%20icon/48/Payment_KR_bank_logo_Woori_BC_Card_BC_purchase__48.png?width=48&height=48"], "card_type": "", "method_input": null, "method_banner": null, "model_list": [{"type": "install_payment", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}, {"type": "", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}], "code": "", "disable": false, "free_install_payment_plan": [2, 3, 4, 5, 6]}, {"name": "NH Nonghyup Card", "action": "select", "method_key": "nh_nonghyup_card", "token": "", "icons": ["https://res.klook.com/image/upload/v1683699517/UED_new/Foundation/Payment/KR%20bank%20icon/48/Payment_KR_bank_logo_NH_Nonghyup_Card_48.png?width=48&height=48"], "card_type": "", "method_input": null, "method_banner": null, "model_list": [{"type": "install_payment", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}, {"type": "", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}], "code": "", "disable": false, "free_install_payment_plan": [2, 3, 4, 5, 6]}, {"name": "BC Card (ISP)", "action": "select", "method_key": "bc_card", "token": "", "icons": ["https://res.klook.com/image/upload/v1683699517/UED_new/Foundation/Payment/KR%20bank%20icon/48/Payment_KR_bank_logo_BC_Card_48.png?width=48&height=48"], "card_type": "", "method_input": null, "method_banner": null, "model_list": [{"type": "install_payment", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}, {"type": "", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}], "code": "", "disable": false, "free_install_payment_plan": [2, 3, 4, 5, 6]}, {"name": "Lotte Card", "action": "select", "method_key": "lotte_card", "token": "", "icons": ["https://res.klook.com/image/upload/v1683699519/UED_new/Foundation/Payment/KR%20bank%20icon/48/Payment_KR_bank_logo_Lotte_Card_48.png?width=48&height=48"], "card_type": "", "method_input": null, "method_banner": null, "model_list": [{"type": "install_payment", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}, {"type": "", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}], "code": "", "disable": false, "free_install_payment_plan": [2, 3, 4]}, {"name": "Kakao Bank", "action": "select", "method_key": "kakao_bank", "token": "", "icons": ["https://res.klook.com/image/upload/v1683699517/UED_new/Foundation/Payment/KR%20bank%20icon/48/Payment_KR_bank_logo_Kakao_Bank_48.png?width=48&height=48"], "card_type": "", "method_input": null, "method_banner": null, "model_list": [{"type": "install_payment", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}, {"type": "", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}], "code": "", "disable": false}], "card_type": "", "method_input": null, "method_banner": null, "model_list": [{"type": "", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}], "code": "", "disable": false}, {"name": "Naver Pay", "action": "drop_down", "method_key": "<PERSON><PERSON>ay", "token": "", "icons": ["https://res.klook.com/image/upload/v1700705296/UED_new/Foundation/Payment/Naver/Naver_48_48.png?width=48&height=48"], "sub_options": [{"name": "Naver Pay", "action": "select", "method_key": "<PERSON><PERSON>ay", "token": "", "icons": ["https://res.klook.com/image/upload/v1700705296/UED_new/Foundation/Payment/Naver/Naver_48_48.png?width=48&height=48"], "card_type": "", "method_input": null, "method_banner": null, "model_list": [{"type": "", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}], "code": "", "disable": false}, {"name": "Naver Pay Point", "action": "select", "method_key": "naverpay_point", "token": "", "icons": [], "card_type": "", "method_input": null, "method_banner": null, "model_list": [{"type": "", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}], "code": "", "disable": false}], "card_type": "", "method_input": null, "method_banner": null, "model_list": [{"type": "", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}], "code": "", "disable": false}, {"name": "PAYCO", "action": "select", "method_key": "payco", "token": "", "icons": ["https://res.klook.com/image/upload/v1607506121/%E6%94%AF%E4%BB%98%E6%96%B9%E5%BC%8FMobile/General/payment_methed_payco_mobile.png?width=48&height=48"], "card_type": "", "method_input": null, "method_banner": null, "payment_coupon_code": "56LQYM9C", "model_list": [{"type": "text_right_coupon", "title": "", "content": "立减7.3元", "icon": "", "multi_lines": false, "items": null}], "code": "", "disable": false}, {"name": "信用卡/借记卡", "action": "expand", "method_key": "creditcard", "token": "", "icons": ["https://res.klook.com/image/upload/v1577178193/%E6%94%AF%E4%BB%98%E6%96%B9%E5%BC%8FMobile/Credit%20Card/icon_payment_credit_card_manage_56.png?width=112&height=112"], "sub_options": [{"name": "****4444", "action": "select", "method_key": "creditcard-NjlwR2U3dUQ4NFZSZ0FaQnwZ7Pa8eW/9GhswkmjBtLc=", "token": "NjlwR2U3dUQ4NFZSZ0FaQnwZ7Pa8eW/9GhswkmjBtLc=", "icons": ["https://res.klook.com/image/upload/v1657795549/ued/Business%20Services/icon_payment_credit_card_master_112x112.png?width=112&height=112"], "right_tips": {"type": "notice", "dialog": {"title": "", "message": "虽然Klook客路不会收取任何列明金额以外的费用，但你的发卡银行可能会收取海外交易手续费。更多详情请咨询你的发卡银行。", "positive": "", "negative": "", "customized_top": null}}, "card_type": "", "method_input": null, "method_banner": null, "model_list": [{"type": "warning", "title": "", "content": "", "icon": "", "multi_lines": false, "dialog": {"title": "", "message": "虽然Klook客路不会收取任何列明金额以外的费用，但你的发卡银行可能会收取海外交易手续费。更多详情请咨询你的发卡银行。", "positive": "确定", "negative": "", "customized_top": null}, "items": null}, {"type": "input", "title": "请输入卡号/请输入 CVV", "input": {"validate_type": "card_cvv", "hint": "未输入状态的提示"}}, {"type": "", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}], "code": "", "disable": false}], "card_type": "", "method_input": null, "method_banner": null, "model_list": [{"type": "", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}, {"type": "text_below_stroke", "title": "", "content": "", "icon": "", "multi_lines": false, "items": [{"type": "", "style": "default", "text": "立减¥68.8", "link": ""}]}], "code": "", "disable": false}]}, {"type": "pay_later", "default_method_key": "unionpay", "methods": [{"name": "银联支付", "action": "select", "method_key": "unionpay", "token": "123123", "icons": ["https://res.klook.com/image/upload/v1575878925/%E6%94%AF%E4%BB%98%E6%96%B9%E5%BC%8FWeb/credit-union.png?width=70&height=42"], "card_type": "", "method_input": null, "method_banner": null, "model_list": [{"type": "warning", "icon": "", "title": "", "content": "", "multi_lines": "true", "dialog": {"title": "小 i 的内容", "message": "小 i 的内容", "positive": "ok", "negative": "cancel", "items": []}}, {"type": "text_below", "content": "xxxx", "multi_lines": "true", "dialog": {"title": "小 i 的内容", "message": "小 i 的内容", "positive": "ok", "negative": "cancel", "items": []}}, {"type": "text_bubble", "content": "Credit Card Payment Offer", "dialog": {"title": "小 i 的内容", "message": "小 i 的内容", "positive": "ok", "negative": "cancel", "items": [{"icon": "https://res.klook.com/image/upload/v1698376460/UED_new/Foundation/Payment/Visa/Payment_Visa_70_png.png?width=70&height=42", "title": "item-title item-title item-title item-title item-title", "content": " item-content item-content item-content item-content item-content item-content item-content item-content item-content item-content"}, {"icon": "https://res.klook.com/image/upload/v1698376460/UED_new/Foundation/Payment/Visa/Payment_Visa_70_png.png?width=70&height=42", "title": "item-title item-title item-title item-title item-title", "content": " item-content item-content item-content item-content item-content item-content item-content item-content item-content item-content"}, {"icon": "https://res.klook.com/image/upload/v1698376460/UED_new/Foundation/Payment/Visa/Payment_Visa_70_png.png?width=70&height=42", "title": "item-title item-title item-title item-title item-title", "content": " item-content item-content item-content item-content item-content item-content item-content item-content item-content item-content"}, {"icon": "https://res.klook.com/image/upload/v1698376460/UED_new/Foundation/Payment/Visa/Payment_Visa_70_png.png?width=70&height=42", "title": "item-title item-title item-title item-title item-title", "content": " item-content item-content item-content item-content item-content item-content item-content item-content item-content item-content"}, {"icon": "https://res.klook.com/image/upload/v1698376460/UED_new/Foundation/Payment/Visa/Payment_Visa_70_png.png?width=70&height=42", "title": "item-title item-title item-title item-title item-title", "content": " item-content item-content item-content item-content item-content item-content item-content item-content item-content item-content"}, {"icon": "https://res.klook.com/image/upload/v1698376460/UED_new/Foundation/Payment/Visa/Payment_Visa_70_png.png?width=70&height=42", "title": "item-title item-title item-title item-title item-title", "content": " item-content item-content item-content item-content item-content item-content item-content item-content item-content item-content"}, {"icon": "https://res.klook.com/image/upload/v1698376460/UED_new/Foundation/Payment/Visa/Payment_Visa_70_png.png?width=70&height=42", "title": "item-title item-title item-title item-title item-title", "content": " item-content item-content item-content item-content item-content item-content item-content item-content item-content item-content"}, {"icon": "https://res.klook.com/image/upload/v1698376460/UED_new/Foundation/Payment/Visa/Payment_Visa_70_png.png?width=70&height=42", "title": "item-title item-title item-title item-title item-title", "content": " item-content item-content item-content item-content item-content item-content item-content item-content item-content item-content"}, {"icon": "https://res.klook.com/image/upload/v1698376460/UED_new/Foundation/Payment/Visa/Payment_Visa_70_png.png?width=70&height=42", "title": "item-title item-title item-title item-title item-title", "content": " item-content item-content item-content item-content item-content item-content item-content item-content item-content item-content"}, {"icon": "https://res.klook.com/image/upload/v1698376460/UED_new/Foundation/Payment/Visa/Payment_Visa_70_png.png?width=70&height=42", "title": "item-title item-title item-title item-title item-title", "content": " item-content item-content item-content item-content item-content item-content item-content item-content item-content item-content"}, {"icon": "https://res.klook.com/image/upload/v1698376460/UED_new/Foundation/Payment/Visa/Payment_Visa_70_png.png?width=70&height=42", "title": "item-title item-title item-title item-title item-title", "content": " item-content item-content item-content item-content item-content item-content item-content item-content item-content item-content"}, {"icon": "https://res.klook.com/image/upload/v1698376460/UED_new/Foundation/Payment/Visa/Payment_Visa_70_png.png?width=70&height=42", "title": "item-title item-title item-title item-title item-title", "content": " item-content item-content item-content item-content item-content item-content item-content item-content item-content item-content"}]}}, {"type": "text_below_stroke", "content": "Credit Card Payment Offer"}, {"type": "input", "title": "请输入卡号/请输入 CVV", "input": {"validate_type": "card_cvv", "hint": "未输入状态的提示"}}, {"type": "banner", "banner": {"type": "link", "image_url": "https://res.klook.com/image/upload/v1706156633/hotel/psij9ulb8qb4sfbnndvt.webp", "url": "https://www.baidu.com"}}, {"type": "saved", "title": "Save for future purchases", "content": "No re-verification required"}, {"type": "deleted", "title": "Account on file", "content": "Delete", "icon": "https://res.klook.com/image/upload/v1576232855/%E6%94%AF%E4%BB%98%E6%96%B9%E5%BC%8FMobile/General/icon_payment_alipay.png?width=112&height=112", "dialog": {"title": "小 i 的内容", "message": "", "positive": "ok", "negative": "cancel"}, "delete": {"confirm_dialog": {"title": "二次确认弹层", "message": "二次确认弹层内容", "positive": "ok", "negative": "cancel"}}}, {"type": "delete_old", "content": "删除按钮的文案", "dialog": {"title": "二次确认", "message": "二次确认", "positive": "ok", "negative": "cancel", "items": []}}], "code": "", "disable": false}, {"name": "FPX Online Banking", "action": "drop_down", "method_key": "onlinebanking_myr_adyen", "token": "", "icons": ["https://res.klook.com/image/upload/v1583830798/%E6%94%AF%E4%BB%98%E6%96%B9%E5%BC%8FWeb/%E4%B8%80%E7%BA%A7%E6%94%AF%E4%BB%98%E6%96%B9%E5%BC%8F/methed-onlinebanking-new.png?width=70&height=42"], "sub_options": [{"name": "Affin Bank", "action": "select", "method_key": "onlinebanking-affin", "token": "", "icons": ["https://res.klook.com/image/upload/v1642149763/FPX%20MY/icon_payment_online_affin_bank.png?width=32&height=34"], "card_type": "", "method_input": null, "method_banner": null, "model_list": [{"type": "", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}], "code": "", "disable": false}, {"name": "Agro Bank", "action": "select", "method_key": "onlinebanking-agro", "token": "", "icons": ["https://res.klook.com/image/upload/v1642149763/FPX%20MY/icon_payment_online_agrobank.png?width=32&height=34"], "card_type": "", "method_input": null, "method_banner": null, "model_list": [{"type": "", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}], "code": "", "disable": false}, {"name": "Alliance Bank", "action": "select", "method_key": "onlinebanking-alliance", "token": "", "icons": ["https://res.klook.com/image/upload/v1583829420/%E6%94%AF%E4%BB%98%E6%96%B9%E5%BC%8FWeb/online%20banking/online-abmb-new.png?width=32&height=34"], "card_type": "", "method_input": null, "method_banner": null, "model_list": [{"type": "", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}], "code": "", "disable": false}, {"name": "AmBank", "action": "select", "method_key": "onlinebanking-am", "token": "", "icons": ["https://res.klook.com/image/upload/v1583829424/%E6%94%AF%E4%BB%98%E6%96%B9%E5%BC%8FWeb/online%20banking/online-ambank-new.png?width=32&height=34"], "card_type": "", "method_input": null, "method_banner": null, "model_list": [{"type": "", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}], "code": "", "disable": false}, {"name": "Bank Islam", "action": "select", "method_key": "onlinebanking-islam", "token": "", "icons": ["https://res.klook.com/image/upload/v1583829399/%E6%94%AF%E4%BB%98%E6%96%B9%E5%BC%8FWeb/online%20banking/online-islam-new.png?width=32&height=34"], "card_type": "", "method_input": null, "method_banner": null, "model_list": [{"type": "", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}], "code": "", "disable": false}, {"name": "Bank Muamalat", "action": "select", "method_key": "onlinebanking-muamalat", "token": "", "icons": ["https://res.klook.com/image/upload/v1642149762/FPX%20MY/icon_payment_online_bankmuamalat.png?width=32&height=34"], "card_type": "", "method_input": null, "method_banner": null, "model_list": [{"type": "", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}], "code": "", "disable": false}, {"name": "Bank Rakyat", "action": "select", "method_key": "onlinebanking-rakyat", "token": "", "icons": ["https://res.klook.com/image/upload/v1583829422/%E6%94%AF%E4%BB%98%E6%96%B9%E5%BC%8FWeb/online%20banking/online-rakyat-new.png?width=32&height=34"], "card_type": "", "method_input": null, "method_banner": null, "model_list": [{"type": "", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}], "code": "", "disable": false}, {"name": "Bank Simpanan Nasional", "action": "select", "method_key": "onlinebanking-simpanan_nasional", "token": "", "icons": ["https://res.klook.com/image/upload/v1642149763/FPX%20MY/icon_payment_online_bsn.png?width=32&height=34"], "card_type": "", "method_input": null, "method_banner": null, "model_list": [{"type": "", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}], "code": "", "disable": false}, {"name": "CIMB Bank", "action": "select", "method_key": "onlinebanking-cimb_clicks", "token": "", "icons": ["https://res.klook.com/image/upload/v1583829424/%E6%94%AF%E4%BB%98%E6%96%B9%E5%BC%8FWeb/online%20banking/online-cimb-new.png?width=32&height=34"], "card_type": "", "method_input": null, "method_banner": null, "model_list": [{"type": "", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}], "code": "", "disable": false}, {"name": "Hong Leong Bank", "action": "select", "method_key": "onlinebanking-hong_leong_connect", "token": "", "icons": ["https://res.klook.com/image/upload/v1583829423/%E6%94%AF%E4%BB%98%E6%96%B9%E5%BC%8FWeb/online%20banking/online-hongleongbank-new.png?width=32&height=34"], "card_type": "", "method_input": null, "method_banner": null, "model_list": [{"type": "", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}], "code": "", "disable": false}, {"name": "HSBC Bank", "action": "select", "method_key": "onlinebanking-hsbc", "token": "", "icons": ["https://res.klook.com/image/upload/v1642149798/FPX%20MY/icon_payment_online_hsbc.png?width=32&height=34"], "card_type": "", "method_input": null, "method_banner": null, "model_list": [{"type": "", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}], "code": "", "disable": false}, {"name": "Kuwait Finance House", "action": "select", "method_key": "onlinebanking-kuwait_finance_house", "token": "", "icons": ["https://res.klook.com/image/upload/v1583829415/%E6%94%AF%E4%BB%98%E6%96%B9%E5%BC%8FWeb/online%20banking/online-kfh-new.png?width=32&height=34"], "card_type": "", "method_input": null, "method_banner": null, "model_list": [{"type": "", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}], "code": "", "disable": false}, {"name": "Maybank", "action": "select", "method_key": "onlinebanking-maybank2u", "token": "", "icons": ["https://res.klook.com/image/upload/v1583829447/%E6%94%AF%E4%BB%98%E6%96%B9%E5%BC%8FWeb/online%20banking/online-maybank-new.png?width=32&height=34"], "card_type": "", "method_input": null, "method_banner": null, "model_list": [{"type": "", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}], "code": "", "disable": false}, {"name": "OCBC Bank", "action": "select", "method_key": "onlinebanking-ocbc", "token": "", "icons": ["https://res.klook.com/image/upload/v1583829424/%E6%94%AF%E4%BB%98%E6%96%B9%E5%BC%8FWeb/online%20banking/Online-ocbc.png?width=32&height=34"], "card_type": "", "method_input": null, "method_banner": null, "model_list": [{"type": "", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}], "code": "", "disable": false}, {"name": "Public Bank", "action": "select", "method_key": "onlinebanking-public", "token": "", "icons": ["https://res.klook.com/image/upload/v1583829420/%E6%94%AF%E4%BB%98%E6%96%B9%E5%BC%8FWeb/online%20banking/online-public-new.png?width=32&height=34"], "card_type": "", "method_input": null, "method_banner": null, "model_list": [{"type": "", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}], "code": "", "disable": false}, {"name": "RHB Bank", "action": "select", "method_key": "onlinebanking-rhb_row", "token": "", "icons": ["https://res.klook.com/image/upload/v1583829425/%E6%94%AF%E4%BB%98%E6%96%B9%E5%BC%8FWeb/online%20banking/online-rhb-new.png?width=32&height=34"], "card_type": "", "method_input": null, "method_banner": null, "model_list": [{"type": "", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}], "code": "", "disable": false}, {"name": "Standard Chartered Bank", "action": "select", "method_key": "onlinebanking-standard_chartered", "token": "", "icons": ["https://res.klook.com/image/upload/v1583829425/%E6%94%AF%E4%BB%98%E6%96%B9%E5%BC%8FWeb/online%20banking/online-standardchartered-new.png?width=32&height=34"], "card_type": "", "method_input": null, "method_banner": null, "model_list": [{"type": "", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}], "code": "", "disable": false}, {"name": "UOB Bank", "action": "select", "method_key": "onlinebanking-uob", "token": "", "icons": ["https://res.klook.com/image/upload/v1642149763/FPX%20MY/icon_payment_online_uob.png?width=32&height=34"], "card_type": "", "method_input": null, "method_banner": null, "model_list": [{"type": "", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}], "code": "", "disable": false}], "card_type": "", "method_input": null, "method_banner": null, "model_list": [{"type": "", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}], "code": "", "disable": false}, {"name": "信用卡/借记卡", "action": "expand", "method_key": "creditcard", "token": "", "icons": ["https://res.klook.com/image/upload/v1576662807/%E6%94%AF%E4%BB%98%E6%96%B9%E5%BC%8FWeb/payment_default_70x42.png?width=70&height=42"], "sub_options": [{"name": "****0505", "action": "select", "method_key": "creditcard-NjlwR2U3dUQ4NFZSZ0FaQqNa1wruOaOb12MlUpx++BM=", "token": "NjlwR2U3dUQ4NFZSZ0FaQqNa1wruOaOb12MlUpx++BM=", "icons": ["https://res.klook.com/image/upload/v1575878925/%E6%94%AF%E4%BB%98%E6%96%B9%E5%BC%8FWeb/credit-jcb.png?width=70&height=42"], "right_tips": {"type": "notice", "dialog": {"title": "", "message": "虽然Klook客路不会收取任何列明金额以外的费用，但你的发卡银行可能会收取海外交易手续费。更多详情请咨询你的发卡银行。", "positive": "", "negative": "", "customized_top": null}}, "card_type": "", "method_input": null, "method_banner": null, "model_list": [{"type": "saved", "title": "Save for future purchases", "content": "No re-verification required"}, {"type": "input", "title": "请输入卡号/请输入 CVV", "input": {"validate_type": "card_cvv", "hint": "未输入状态的提示"}}, {"type": "warning", "title": "", "content": "", "icon": "", "multi_lines": false, "dialog": {"title": "", "message": "虽然Klook客路不会收取任何列明金额以外的费用，但你的发卡银行可能会收取海外交易手续费。更多详情请咨询你的发卡银行。", "positive": "确定", "negative": "", "customized_top": null}, "items": null}, {"type": "", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}, {"type": "text_below_stroke", "title": "", "content": "", "icon": "", "multi_lines": false, "items": [{"type": "", "style": "default", "text": "立减¥68.8", "link": ""}]}], "code": "", "disable": false}, {"name": "****1111", "action": "select", "method_key": "creditcard-NjlwR2U3dUQ4NFZSZ0FaQr2TSL7ADu+O33cR37eDA7Q=", "token": "NjlwR2U3dUQ4NFZSZ0FaQr2TSL7ADu+O33cR37eDA7Q=", "icons": ["https://res.klook.com/image/upload/v1698376460/UED_new/Foundation/Payment/Visa/Payment_Visa_70_png.png?width=70&height=42"], "right_tips": {"type": "notice", "dialog": {"title": "", "message": "虽然Klook客路不会收取任何列明金额以外的费用，但你的发卡银行可能会收取海外交易手续费。更多详情请咨询你的发卡银行。", "positive": "", "negative": "", "customized_top": null}}, "card_type": "", "method_input": null, "method_banner": null, "model_list": [{"type": "warning", "title": "", "content": "", "icon": "", "multi_lines": false, "dialog": {"title": "", "message": "虽然Klook客路不会收取任何列明金额以外的费用，但你的发卡银行可能会收取海外交易手续费。更多详情请咨询你的发卡银行。", "positive": "确定", "negative": "", "customized_top": null}, "items": null}, {"type": "", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}], "code": "", "disable": false}], "card_type": "", "method_input": null, "method_banner": null, "model_list": [{"type": "", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}, {"type": "text_below_stroke", "title": "", "content": "", "icon": "", "multi_lines": false, "items": [{"type": "", "style": "default", "text": "立减¥68.8", "link": ""}]}], "code": "", "disable": false}]}], "methods": [{"name": "银联支付", "action": "select", "method_key": "unionpay", "token": "123123", "icons": ["https://res.klook.com/image/upload/v1575878925/%E6%94%AF%E4%BB%98%E6%96%B9%E5%BC%8FWeb/credit-union.png?width=70&height=42"], "card_type": "", "method_input": null, "method_banner": null, "model_list": [{"type": "warning", "icon": "", "title": "", "content": "", "multi_lines": "true", "dialog": {"title": "小 i 的内容", "message": "小 i 的内容", "positive": "ok", "negative": "cancel", "items": []}}, {"type": "text_below", "content": "xxxx", "multi_lines": "true", "dialog": {"title": "小 i 的内容", "message": "小 i 的内容", "positive": "ok", "negative": "cancel", "items": []}}, {"type": "text_bubble", "content": "Credit Card Payment Offer", "dialog": {"title": "小 i 的内容", "message": "小 i 的内容", "positive": "ok", "negative": "cancel", "items": [{"icon": "https://res.klook.com/image/upload/v1698376460/UED_new/Foundation/Payment/Visa/Payment_Visa_70_png.png?width=70&height=42", "title": "item-title item-title item-title item-title item-title", "content": " item-content item-content item-content item-content item-content item-content item-content item-content item-content item-content"}, {"icon": "https://res.klook.com/image/upload/v1698376460/UED_new/Foundation/Payment/Visa/Payment_Visa_70_png.png?width=70&height=42", "title": "item-title item-title item-title item-title item-title", "content": " item-content item-content item-content item-content item-content item-content item-content item-content item-content item-content"}, {"icon": "https://res.klook.com/image/upload/v1698376460/UED_new/Foundation/Payment/Visa/Payment_Visa_70_png.png?width=70&height=42", "title": "item-title item-title item-title item-title item-title", "content": " item-content item-content item-content item-content item-content item-content item-content item-content item-content item-content"}, {"icon": "https://res.klook.com/image/upload/v1698376460/UED_new/Foundation/Payment/Visa/Payment_Visa_70_png.png?width=70&height=42", "title": "item-title item-title item-title item-title item-title", "content": " item-content item-content item-content item-content item-content item-content item-content item-content item-content item-content"}, {"icon": "https://res.klook.com/image/upload/v1698376460/UED_new/Foundation/Payment/Visa/Payment_Visa_70_png.png?width=70&height=42", "title": "item-title item-title item-title item-title item-title", "content": " item-content item-content item-content item-content item-content item-content item-content item-content item-content item-content"}, {"icon": "https://res.klook.com/image/upload/v1698376460/UED_new/Foundation/Payment/Visa/Payment_Visa_70_png.png?width=70&height=42", "title": "item-title item-title item-title item-title item-title", "content": " item-content item-content item-content item-content item-content item-content item-content item-content item-content item-content"}, {"icon": "https://res.klook.com/image/upload/v1698376460/UED_new/Foundation/Payment/Visa/Payment_Visa_70_png.png?width=70&height=42", "title": "item-title item-title item-title item-title item-title", "content": " item-content item-content item-content item-content item-content item-content item-content item-content item-content item-content"}, {"icon": "https://res.klook.com/image/upload/v1698376460/UED_new/Foundation/Payment/Visa/Payment_Visa_70_png.png?width=70&height=42", "title": "item-title item-title item-title item-title item-title", "content": " item-content item-content item-content item-content item-content item-content item-content item-content item-content item-content"}, {"icon": "https://res.klook.com/image/upload/v1698376460/UED_new/Foundation/Payment/Visa/Payment_Visa_70_png.png?width=70&height=42", "title": "item-title item-title item-title item-title item-title", "content": " item-content item-content item-content item-content item-content item-content item-content item-content item-content item-content"}, {"icon": "https://res.klook.com/image/upload/v1698376460/UED_new/Foundation/Payment/Visa/Payment_Visa_70_png.png?width=70&height=42", "title": "item-title item-title item-title item-title item-title", "content": " item-content item-content item-content item-content item-content item-content item-content item-content item-content item-content"}, {"icon": "https://res.klook.com/image/upload/v1698376460/UED_new/Foundation/Payment/Visa/Payment_Visa_70_png.png?width=70&height=42", "title": "item-title item-title item-title item-title item-title", "content": " item-content item-content item-content item-content item-content item-content item-content item-content item-content item-content"}, {"icon": "https://res.klook.com/image/upload/v1698376460/UED_new/Foundation/Payment/Visa/Payment_Visa_70_png.png?width=70&height=42", "title": "item-title item-title item-title item-title item-title", "content": " item-content item-content item-content item-content item-content item-content item-content item-content item-content item-content"}]}}, {"type": "text_below_stroke", "content": "Credit Card Payment Offer"}, {"type": "input", "title": "请输入卡号/请输入 CVV", "input": {"validate_type": "card_cvv", "hint": "未输入状态的提示"}}, {"type": "banner", "banner": {"type": "link", "image_url": "https://res.klook.com/image/upload/v1706156633/hotel/psij9ulb8qb4sfbnndvt.webp", "url": "https://www.baidu.com"}}, {"type": "saved", "title": "Save for future purchases", "content": "No re-verification required"}, {"type": "deleted", "title": "Account on file", "content": "Delete", "icon": "https://res.klook.com/image/upload/v1576232855/%E6%94%AF%E4%BB%98%E6%96%B9%E5%BC%8FMobile/General/icon_payment_alipay.png?width=112&height=112", "dialog": {"title": "小 i 的内容", "message": "", "positive": "ok", "negative": "cancel"}, "delete": {"confirm_dialog": {"title": "二次确认弹层", "message": "二次确认弹层内容", "positive": "ok", "negative": "cancel"}}}, {"type": "delete_old", "content": "删除按钮的文案", "dialog": {"title": "二次确认", "message": "二次确认", "positive": "ok", "negative": "cancel", "items": []}}], "code": "", "disable": false}, {"name": "<PERSON><PERSON><PERSON> (Ali<PERSON>y+™ partner)", "action": "select", "method_key": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "token": "", "icons": ["https://res.klook.com/image/upload/v1638515728/kakao%20pay/kakao_pay_logo_24.png?width=48&height=48"], "card_type": "", "method_input": null, "method_banner": null, "model_list": [{"type": "", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}], "code": "", "disable": false}, {"name": "Dragonpay", "action": "drop_down", "method_key": "dragonpay", "token": "", "icons": ["https://res.klook.com/image/upload/v1583830589/%E6%94%AF%E4%BB%98%E6%96%B9%E5%BC%8FWeb/%E4%B8%80%E7%BA%A7%E6%94%AF%E4%BB%98%E6%96%B9%E5%BC%8F/methed-dragonpay-new.png?width=70&height=42"], "bottom_tips": {"type": "info", "content": "你所選的付款方式不支援退款"}, "sub_options": [{"name": "Online Transactions", "action": "expand", "method_key": "dragonpay_online", "token": "", "icons": [], "sub_options": [{"name": "E-Banking", "action": "select", "method_key": "dragonpay_online-e_banking", "token": "", "icons": [], "card_type": "", "method_input": null, "method_banner": null, "model_list": [{"type": "", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}], "code": "", "disable": false}], "card_type": "", "method_input": null, "method_banner": null, "model_list": [{"type": "", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}], "code": "", "disable": false}], "card_type": "", "method_input": null, "method_banner": null, "model_list": [{"type": "text_below", "title": "", "content": "你所選的付款方式不支援退款", "icon": "", "multi_lines": true, "items": null}, {"type": "", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}, {"type": "", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}], "code": "", "disable": false}, {"name": "Toss Pay", "action": "select", "method_key": "tosswallet", "token": "", "icons": ["https://res.klook.com/image/upload/v1683699516/UED_new/Foundation/Payment/KR%20bank%20icon/48/Payment_KR_bank_logo_Toss_Bank_48.png?width=48&height=48"], "card_type": "", "method_input": null, "method_banner": null, "model_list": [{"type": "", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}], "code": "", "disable": false}, {"name": "Korean domestic card", "action": "drop_down", "method_key": "tosslocal", "token": "", "icons": ["https://res.klook.com/image/upload/v1576232859/%E6%94%AF%E4%BB%98%E6%96%B9%E5%BC%8FMobile/Online%20Banking/icon_payment_online_online_banking.png?width=48&height=48"], "sub_options": [{"name": "Toss Bank", "action": "select", "method_key": "toss_bank", "token": "", "icons": ["https://res.klook.com/image/upload/v1683699516/UED_new/Foundation/Payment/KR%20bank%20icon/48/Payment_KR_bank_logo_Toss_Bank_48.png?width=48&height=48"], "card_type": "", "method_input": null, "method_banner": null, "model_list": [{"type": "install_payment", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}, {"type": "", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}], "code": "", "disable": false}, {"name": "KB Kookmin Card", "action": "select", "method_key": "kb_kookmin_card", "token": "", "icons": ["https://res.klook.com/image/upload/v1683699517/UED_new/Foundation/Payment/KR%20bank%20icon/48/Payment_KR_bank_logo_K<PERSON>_<PERSON>okmin_Card_48.png?width=48&height=48"], "card_type": "", "method_input": null, "method_banner": null, "model_list": [{"type": "install_payment", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}, {"type": "", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}], "code": "", "disable": false, "free_install_payment_plan": [2, 3, 4, 5]}, {"name": "<PERSON>han Card", "action": "select", "method_key": "shinhan_card", "token": "", "icons": ["https://res.klook.com/image/upload/v1683699517/UED_new/Foundation/Payment/KR%20bank%20icon/48/Payment_KR_bank_logo_<PERSON>han_Card_48.png?width=48&height=48"], "card_type": "", "method_input": null, "method_banner": null, "model_list": [{"type": "install_payment", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}, {"type": "", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}], "code": "", "disable": false, "free_install_payment_plan": [2, 3, 4, 5, 6]}, {"name": "Hyundai Card", "action": "select", "method_key": "hyundai_card", "token": "", "icons": ["https://res.klook.com/image/upload/v1683699518/UED_new/Foundation/Payment/KR%20bank%20icon/48/Payment_KR_bank_logo_Hyundai_Card_48.png?width=48&height=48"], "card_type": "", "method_input": null, "method_banner": null, "model_list": [{"type": "install_payment", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}, {"type": "", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}], "code": "", "disable": false}, {"name": "<PERSON><PERSON>", "action": "select", "method_key": "hana_card", "token": "", "icons": ["https://res.klook.com/image/upload/v1683699516/UED_new/Foundation/Payment/KR%20bank%20icon/48/Payment_KR_bank_logo_Hana_Card_48.png?width=48&height=48"], "card_type": "", "method_input": null, "method_banner": null, "model_list": [{"type": "install_payment", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}, {"type": "", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}], "code": "", "disable": false}, {"name": "Samsung Card", "action": "select", "method_key": "samsung_card", "token": "", "icons": ["https://res.klook.com/image/upload/v1683858388/UED_new/Foundation/Payment/KR%20bank%20icon/48/Payment_KR_bank_logo_Samsung_Card_48.png?width=48&height=48"], "card_type": "", "method_input": null, "method_banner": null, "model_list": [{"type": "install_payment", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}, {"type": "", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}], "code": "", "disable": false}, {"name": "<PERSON><PERSON><PERSON>", "action": "select", "method_key": "woori_card", "token": "", "icons": ["https://res.klook.com/image/upload/v1683709124/UED_new/Foundation/Payment/KR%20bank%20icon/48/Payment_KR_bank_logo_Woori_BC_Card_BC_purchase__48.png?width=48&height=48"], "card_type": "", "method_input": null, "method_banner": null, "model_list": [{"type": "install_payment", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}, {"type": "", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}], "code": "", "disable": false, "free_install_payment_plan": [2, 3, 4, 5, 6]}, {"name": "NH Nonghyup Card", "action": "select", "method_key": "nh_nonghyup_card", "token": "", "icons": ["https://res.klook.com/image/upload/v1683699517/UED_new/Foundation/Payment/KR%20bank%20icon/48/Payment_KR_bank_logo_NH_Nonghyup_Card_48.png?width=48&height=48"], "card_type": "", "method_input": null, "method_banner": null, "model_list": [{"type": "install_payment", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}, {"type": "", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}], "code": "", "disable": false, "free_install_payment_plan": [2, 3, 4, 5, 6]}, {"name": "BC Card (ISP)", "action": "select", "method_key": "bc_card", "token": "", "icons": ["https://res.klook.com/image/upload/v1683699517/UED_new/Foundation/Payment/KR%20bank%20icon/48/Payment_KR_bank_logo_BC_Card_48.png?width=48&height=48"], "card_type": "", "method_input": null, "method_banner": null, "model_list": [{"type": "install_payment", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}, {"type": "", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}], "code": "", "disable": false, "free_install_payment_plan": [2, 3, 4, 5, 6]}, {"name": "Lotte Card", "action": "select", "method_key": "lotte_card", "token": "", "icons": ["https://res.klook.com/image/upload/v1683699519/UED_new/Foundation/Payment/KR%20bank%20icon/48/Payment_KR_bank_logo_Lotte_Card_48.png?width=48&height=48"], "card_type": "", "method_input": null, "method_banner": null, "model_list": [{"type": "install_payment", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}, {"type": "", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}], "code": "", "disable": false, "free_install_payment_plan": [2, 3, 4]}, {"name": "Kakao Bank", "action": "select", "method_key": "kakao_bank", "token": "", "icons": ["https://res.klook.com/image/upload/v1683699517/UED_new/Foundation/Payment/KR%20bank%20icon/48/Payment_KR_bank_logo_Kakao_Bank_48.png?width=48&height=48"], "card_type": "", "method_input": null, "method_banner": null, "model_list": [{"type": "install_payment", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}, {"type": "", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}], "code": "", "disable": false}], "card_type": "", "method_input": null, "method_banner": null, "model_list": [{"type": "", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}], "code": "", "disable": false}, {"name": "Naver Pay", "action": "drop_down", "method_key": "<PERSON><PERSON>ay", "token": "", "icons": ["https://res.klook.com/image/upload/v1700705296/UED_new/Foundation/Payment/Naver/Naver_48_48.png?width=48&height=48"], "sub_options": [{"name": "Naver Pay", "action": "select", "method_key": "<PERSON><PERSON>ay", "token": "", "icons": ["https://res.klook.com/image/upload/v1700705296/UED_new/Foundation/Payment/Naver/Naver_48_48.png?width=48&height=48"], "card_type": "", "method_input": null, "method_banner": null, "model_list": [{"type": "", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}], "code": "", "disable": false}, {"name": "Naver Pay Point", "action": "select", "method_key": "naverpay_point", "token": "", "icons": [], "card_type": "", "method_input": null, "method_banner": null, "model_list": [{"type": "", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}], "code": "", "disable": false}], "card_type": "", "method_input": null, "method_banner": null, "model_list": [{"type": "", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}], "code": "", "disable": false}, {"name": "PAYCO", "action": "select", "method_key": "payco", "payment_coupon_code": "56LQYM9C", "token": "", "icons": ["https://res.klook.com/image/upload/v1607506121/%E6%94%AF%E4%BB%98%E6%96%B9%E5%BC%8FMobile/General/payment_methed_payco_mobile.png?width=48&height=48"], "card_type": "", "method_input": null, "method_banner": null, "model_list": [{"type": "text_right_coupon", "title": "", "content": "立减7.3元", "icon": "", "multi_lines": false, "items": null}], "code": "", "disable": false}, {"name": "信用卡/借记卡", "action": "expand", "method_key": "creditcard", "token": "", "icons": ["https://res.klook.com/image/upload/v1577178193/%E6%94%AF%E4%BB%98%E6%96%B9%E5%BC%8FMobile/Credit%20Card/icon_payment_credit_card_manage_56.png?width=112&height=112"], "sub_options": [{"name": "****4444", "action": "select", "method_key": "creditcard-NjlwR2U3dUQ4NFZSZ0FaQnwZ7Pa8eW/9GhswkmjBtLc=", "token": "NjlwR2U3dUQ4NFZSZ0FaQnwZ7Pa8eW/9GhswkmjBtLc=", "icons": ["https://res.klook.com/image/upload/v1657795549/ued/Business%20Services/icon_payment_credit_card_master_112x112.png?width=112&height=112"], "right_tips": {"type": "notice", "dialog": {"title": "", "message": "虽然Klook客路不会收取任何列明金额以外的费用，但你的发卡银行可能会收取海外交易手续费。更多详情请咨询你的发卡银行。", "positive": "", "negative": "", "customized_top": null}}, "card_type": "", "method_input": null, "method_banner": null, "model_list": [{"type": "warning", "title": "", "content": "", "icon": "", "multi_lines": false, "dialog": {"title": "", "message": "虽然Klook客路不会收取任何列明金额以外的费用，但你的发卡银行可能会收取海外交易手续费。更多详情请咨询你的发卡银行。", "positive": "确定", "negative": "", "customized_top": null}, "items": null}, {"type": "input", "title": "请输入卡号/请输入 CVV", "input": {"validate_type": "card_cvv", "hint": "未输入状态的提示"}}, {"type": "", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}], "code": "", "disable": false}], "card_type": "", "method_input": null, "method_banner": null, "model_list": [{"type": "", "title": "", "content": "", "icon": "", "multi_lines": false, "items": null}, {"type": "text_below_stroke", "title": "", "content": "", "icon": "", "multi_lines": false, "items": [{"type": "", "style": "default", "text": "立减¥68.8", "link": ""}]}], "code": "", "disable": false}], "methods_hide": [], "supported_cards": {"types": [{"type": "Visa", "icon": "https://res.klook.com/image/upload/v1698376460/UED_new/Foundation/Payment/Visa/Payment_Visa_70_png.png?width=70&height=42", "provider": "adyen", "disable": false}, {"type": "MasterCard", "icon": "https://res.klook.com/image/upload/v1575878925/%E6%94%AF%E4%BB%98%E6%96%B9%E5%BC%8FWeb/credit-mastercard.png?width=70&height=42", "provider": "adyen", "disable": false}, {"type": "American Express", "icon": "https://res.klook.com/image/upload/v1624503441/ued/Payment%20Method/methed-Amex_Web.png?width=140&height=84", "provider": "adyen", "disable": false}, {"type": "JCB", "icon": "https://res.klook.com/image/upload/v1575878925/%E6%94%AF%E4%BB%98%E6%96%B9%E5%BC%8FWeb/credit-jcb.png?width=70&height=42", "provider": "adyen", "disable": false}, {"type": "UnionPay", "icon": "https://res.klook.com/image/upload/v1575878925/%E6%94%AF%E4%BB%98%E6%96%B9%E5%BC%8FWeb/credit-union.png?width=70&height=42", "provider": "adyen", "disable": false}, {"type": "Discover", "icon": "https://res.klook.com/image/upload/v1624503441/ued/Payment%20Method/methed-Discover_Web.png?width=140&height=84", "provider": "adyen", "disable": false}, {"type": "Diners Club", "icon": "https://res.klook.com/image/upload/v1624503441/ued/Payment%20Method/methed-Diners_Web.png?width=140&height=84", "provider": "adyen", "disable": false}]}, "terms": null, "new_card_info": {}, "support_new_payment": false, "terms_tips": null, "asset_voucher_info": {"selected_type": "", "selected_types": ["payment_coupon"], "available_types": [{"name": "payment_coupon", "usable": true, "reason": "已选****4444优惠", "text": "", "icon": ""}], "list": {"common_voucher": [], "payment_coupon": [{"code": "56LQYM9C", "external_code": "", "status": "", "method_name": "payment_coupon", "payment_channel": 0, "gateway_name": "", "amount": "7.4", "available_amount": "", "currency": "CNY", "valid_date_utc": "", "user_id": 100012341, "selected": true}]}}, "auto_submit": false, "global_tips": [{"type": "cashier_top", "content": "所选优惠码需使用Visa, MasterCard, American Express付款。 查看详情。", "items": [{"text": "查看详情。", "style": "default", "action": "dialog", "dialog": {"title": "", "message": "所选优惠码仅可用于特定的支付方式。若现在无法完成付款，稍后优惠码将退还至您的账户中。", "positive": "知道了", "negative": "", "customized_top": null}}, {"text": "Visa, MasterCard, American Express", "style": "orange", "action": "", "dialog": null}, {"text": "Visa, MasterCard, American Express", "style": "bold", "action": "", "dialog": null}]}], "is_agent_channel": false, "disable_payment_coupon": false}}, "success": true}