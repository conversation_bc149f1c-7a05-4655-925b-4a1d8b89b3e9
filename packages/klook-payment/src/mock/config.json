{"error": {"code": "", "message": ""}, "result": {"version": 5, "card_type_rules": [{"with_phone_number": false, "type": "Visa", "pattern": ["4"], "icon": "https://res.klook.com/image/upload/v1698376460/UED_new/Foundation/Payment/Visa/Payment_Visa_70_png.png?width=140&height=84", "gaps": [4, 8, 12], "lengths": [16, 18, 19], "code": {"name": "CVV", "size": 3}, "issuer_bin_lengths": [18, 19]}, {"with_phone_number": false, "type": "MasterCard", "pattern": ["51-55", "2221-2229", "223-229", "23-26", "270-271", "2720", "5", "700013", "700125", "6060530", "604823", "606045", "606052", "636078", "636093-636095", "636189", "639339", "639578", "639650"], "icon": "https://res.klook.com/image/upload/v1657795549/ued/Business%20Services/icon_payment_credit_card_master_140x84.png?width=140&height=84", "gaps": [4, 8, 12], "lengths": [16], "code": {"name": "CVC", "size": 3}, "issuer_bin_lengths": [18, 19]}, {"with_phone_number": false, "type": "American Express", "pattern": ["34", "37"], "icon": "https://res.klook.com/image/upload/v1624503441/ued/Payment%20Method/methed-Amex_Web.png?width=140&height=84", "gaps": [4, 10], "lengths": [15], "code": {"name": "CID", "size": 4}, "issuer_bin_lengths": null}, {"with_phone_number": false, "type": "Diners Club", "pattern": ["300-305", "36", "38", "39", "30"], "icon": "https://res.klook.com/image/upload/v1624503441/ued/Payment%20Method/methed-Diners_Web.png?width=140&height=84", "gaps": [4, 10], "lengths": [14, 16, 19], "code": {"name": "CVV", "size": 3}, "issuer_bin_lengths": null}, {"with_phone_number": false, "type": "Discover", "pattern": ["6011", "644-649", "65"], "icon": "https://res.klook.com/image/upload/v1624503441/ued/Payment%20Method/methed-Discover_Web.png?width=140&height=84", "gaps": [4, 8, 12], "lengths": [16, 19], "code": {"name": "CVV", "size": 3}, "issuer_bin_lengths": null}, {"with_phone_number": false, "type": "JCB", "pattern": ["2131", "1800", "35"], "icon": "https://res.klook.com/image/upload/v1594275563/%E6%94%AF%E4%BB%98%E6%96%B9%E5%BC%8FWeb/%E4%BF%A1%E7%94%A8%E5%8D%A1%E6%94%AF%E4%BB%98%E6%96%B9%E5%BC%8F/credit-jcb-new.png?width=140&height=84", "gaps": [4, 8, 12], "lengths": [16, 17, 18, 19], "code": {"name": "CVV", "size": 3}, "issuer_bin_lengths": null}, {"with_phone_number": true, "type": "UnionPay", "pattern": ["620", "624-626", "62100-62182", "62184-62187", "62185-62197", "62200-62205", "622010-622999", "622018", "622019-622999", "62207-62209", "622126-622925", "623-626", "6270", "6272", "6276", "627700-627779", "627781-627799", "6282-6289", "6291", "6292", "810", "8110-8131", "8132-8151", "8152-8163", "8164-8171", "811-816", "621-622", "628"], "icon": "https://res.klook.com/image/upload/v1594275563/%E6%94%AF%E4%BB%98%E6%96%B9%E5%BC%8FWeb/%E4%BF%A1%E7%94%A8%E5%8D%A1%E6%94%AF%E4%BB%98%E6%96%B9%E5%BC%8F/credit-union-new.png?width=140&height=84", "gaps": [4, 8, 12], "lengths": [14, 15, 16, 17, 18, 19], "code": {"name": "CVN", "size": 3}, "issuer_bin_lengths": [18, 19]}, {"with_phone_number": false, "type": "Korean Local Card", "pattern": ["020002", "209304", "605615", "83", "87", "90-95", "970-972", "99", "01", "0353", "040-047", "049", "960161", "960167", "960171-960172"], "icon": "https://res.klook.com/image/upload/v1594353292/%E6%94%AF%E4%BB%98%E6%96%B9%E5%BC%8FWeb/%E4%BF%A1%E7%94%A8%E5%8D%A1%E6%94%AF%E4%BB%98%E6%96%B9%E5%BC%8F/default-card-web-new.png?width=140&height=84", "gaps": [4, 8, 12], "lengths": [16], "code": {"name": "CVC", "size": 3}, "issuer_bin_lengths": null}]}, "success": true}