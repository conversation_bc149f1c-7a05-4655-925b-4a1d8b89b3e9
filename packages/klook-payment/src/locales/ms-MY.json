{"11872": "Sila pilih negara/rantau", "12346": "Gagal memuatkan. Sila segar semula untuk mencuba lagi.", "17826": "<PERSON><PERSON> k<PERSON>ng", "26209": "Sorry, try again later", "27068": "Nombor kad", "27069": "Sila masukkan nombor kad yang sah", "27075": "Nombor telefon", "27076": "Sila masukkan nombor telefon yang sah", "27078": "<PERSON><PERSON> pem<PERSON>g kad", "27079": "<PERSON>la masukkan nama pemegang kad yang sah", "27080": "Nombor kad ID", "27081": "Sila masukkan nombor kad ID yang sah", "30125": "Gunakan kod promosi", "30782": "Scan with your bank app to pay", "44857": "<PERSON><PERSON><PERSON>", "44859": "<PERSON><PERSON>s kod QR ini dengan {app}", "44860": "<PERSON>ikan halaman ini dibuka se<PERSON>ga anda menye<PERSON>kan pembayaran.", "44861": "<PERSON><PERSON> anda tidak dihalakan semula selepas menye<PERSON> pembayaran, sila", "44862": "pergi sini", "44863": ".", "44864": "<PERSON> memba<PERSON> dengan {app}", "44865": "1. <PERSON><PERSON> {app}", "44866": "2. <PERSON><PERSON><PERSON> kod QR menggunakan aplikasi", "44867": "3. Lengkapkan pembayaran dalam aplikasi dan tunggu pengesahan", "44868": "Ke<PERSON>li untuk segar semula", "44869": "Kod QR telah tamat tempoh", "44874": "Sila pergi ke halaman Tempahan untuk menyemak status tempahan", "48181": "Sila masukkan nombor kad yang sah mengikut kad yang ditetapkan", "48182": "Sila masukkan nombor kad mengikut kad yang ditetapkan", "48183": "<PERSON><PERSON>, kad ini tidak boleh digunakan untuk pesanan ini. <PERSON><PERSON> guna kad lain.", "73205": "<PERSON><PERSON>", "73234": "Anda hanya boleh menggunakan kod promosi ini apabila membayar dengan kad yang ditetapkan. Cuba kad lain atau tukar cara pembayaran?", "73235": "Teruskan", "73236": "Kembali", "79399": "Enter your phone number that's registered with DBS PayLah!", "79403": "Please enter a valid phone number", "79406": "Login to verify your DBS PayLah!", "79407": "You'll receive a payment request from DBS PayLah! with this phone number", "79986": "Phone number", "89760": "Pay in full", "89764": "Sorry, only orders of at least KRW 50,000 can be paid in instalments", "89768": "{0} months", "89770": "Select instalment plan", "89771": "Interest free", "89773": "Paying in instalments might incur interest. Make sure to confirm any interest rates with your card issuer before agreeing.", "89776": "Months of instalments: {months}", "89780": "Terms & Conditions", "111121": "<PERSON><PERSON><PERSON><PERSON> yuran kecil mungkin dikenakan pada kad anda untuk menges<PERSON>. <PERSON><PERSON><PERSON> tersebut akan dibayar balik secara automatik setelah disahkan.", "111123": "Sesuatu telah berlaku", "171407": "Poskod", "171709": "Cuba lagi", "173899": "<PERSON><PERSON> semua kaedah pem<PERSON>", "173909": "Tambah kad kredit atau debit baharu untuk membayar", "173925": "Tidak digunakan", "173929": "<PERSON><PERSON><PERSON><PERSON> anda tidak berjaya. Sila cuba lagi.", "173931": "<PERSON><PERSON>h anda telah melengkapkan pembayaran?", "173932": "Ya", "173933": "<PERSON><PERSON>", "176496": "<PERSON><PERSON> kad baharu", "176511": "<PERSON><PERSON><PERSON> cara pembay<PERSON>", "176512": "<PERSON><PERSON><PERSON>", "176515": "Masukkan CVV/CVC", "176516": "Masukkan nombor kad", "179287": "<PERSON>la masukkan kod poskod yang betul", "179791": "<PERSON><PERSON> masukkan poskod", "180106": "<PERSON>a akan melengkapkan transaksi di laman web rakan kongsi pembayaran kami yang dipercayai. <PERSON>la semak arahan pembayaran dan syarat ansuran di sana.", "180107": "Pelan ansuran 4 bulan tersedia", "180108": "Bayaran secara ansuran mungkin dikenakan faedah bergantung kepada pengeluar. Pastikan anda mengesahkan sebarang kadar faedah dengan pengeluar kad anda sebelum meneruskan. Klook tidak akan mengenakan sebarang faedah kepada anda.", "180109": "<PERSON><PERSON><PERSON><PERSON> kad yang diterima", "180111": "Bangkok Bank (BBL)", "180113": "Krungsri Consumer (Krungsri)", "180114": "Card X", "180115": "Kasikornbank (KBank)", "180116": "Kad K<PERSON>it KTC (KTC)", "180117": "TMB Thanachart (TTB)", "180118": "United Overseas Bank (UOB)", "204719": "<PERSON><PERSON> pem<PERSON>g kad", "204722": "<PERSON><PERSON> masukkan nama", "204723": "Sila masukkan sekurang-kurangnya 2 aksara", "204724": "<PERSON>a tidak boleh mengandungi aksara khas atau nombor.", "pay.add.cards": "Tambah kad bayaran", "global.payment.error": "<PERSON><PERSON><PERSON> gagal, sila cuba lagi", "index.payment.channel": "<PERSON><PERSON><PERSON>", "payment.mobile.save_card": "Simpan kad ini untuk lain kali", "pay.credicard.tooltips": "Maklumat pembayaran anda disulitkan dengan selamat oleh sistem pembayaran yang boleh dipercayai", "payment.mobile.credit_card_tip_title": "<PERSON><PERSON><PERSON> butiran kad kredit saya selamat?", "profile.mobile.edit.country_code": "Kod negara/rantau", "country.otherCountriesOrDistricts": "Lebih banyak negara/rantau", "rail_phone_number": "Nombor Telefon", "rail_please_enter_your_phone_number": "<PERSON>la masukkan nombor telefon anda", "global.error.cant_be_empty": "<PERSON><PERSON> yang perlu diisi", "global.select.empty_error": "<PERSON><PERSON> pilih", "global.payment.type.tip": "<PERSON><PERSON> pilih jenis p<PERSON>", "global.tips.okTxt": "OK", "global.tips.cancelTxt": "<PERSON><PERSON>", "pay.sg_term.title": "<PERSON><PERSON><PERSON>", "pay.sg_term.title_desc": "Seperti yang di<PERSON><PERSON><PERSON> o<PERSON>h <PERSON> Pelancongan Singapura, pelancong dari Singapura dinasihatkan untuk membeli insurans perjalanan bagi melindungi diri mereka daripada ketakmampuan bayar mana-mana ejen pelancongan.", "pay.sg_term.question": "<PERSON><PERSON><PERSON> anda ingin membeli insurans tersebut?", "pay.sg_term.option_content_yes": "Saya ingin membeli insurans tersebut daripada <b>pemberi insurans pihak ketiga</b> seperti yang tersenarai <a href=\"{0}\" target=\"_blank\">di sini</a> dan saya akan menghantar butiran melalui e-mel kepada <NAME_EMAIL>.", "pay.sg_term.option_content_no": "<PERSON>a tidak mahu membeli insurans tersebut dan mengakui risiko tidak membuat pembelian tersebut.", "pay.view_more_type": "<PERSON><PERSON><PERSON>", "see_more": "<PERSON><PERSON> lagi", "pay.card.number": "Nombor kad", "pay.card.expiryDate": "<PERSON><PERSON><PERSON> luput", "global.mm": "BB", "global.yyyy": "TTTT", "pay.card.SecurityCode": "<PERSON>d k<PERSON>n", "pay.contact.information": "Kod negara/rantau", "pay.card.save.credit": "<PERSON>mp<PERSON> butiran kad", "pay.card.error.tip1": "Sesetengah maklumat yang anda isi adalah tidak sah. Sila semak maklumat adalah seperti yang tertera pada kad anda.", "payment.certain.credit.card": "Kod promosi hanya terpakai untuk pengeluar kad terpilih. Sila guna kad kredit yang sah untuk menggunakan kod promosi ini", "global.payment.cvv.need_be_number": "Kod CVV tidak sah. Sila sahkan semula input anda.", "global.select.palceholder": "<PERSON><PERSON> pilih", "pay.validateTxt": "<PERSON><PERSON><PERSON><PERSON>", "global.payment.cardnumber.empty": "kad kredit <PERSON>an", "global.payment.cvv.empty": "kod keselamatan diperlukan", "global.payment1.cardnumber.empty": "Terdapat masalah dengan nombor kad anda", "pay.card.error.ntt.tip2": "Format itu tidak betul. Sila semak dan cuba lagi", "pay.card.error.ntt.tip3": "Sila masukkan nombor kad anda", "pay.card.error.expiration": "<PERSON><PERSON><PERSON>n tarikh yang sah", "pay.card.error.tip2": "Sesuatu telah berlaku. Sila cuba lagi kemudian.", "pay.card.error.tip3": "Bank pengeluar kad anda telah menolak transaksi ini. Untuk maklumat lanjut, sila hubungi bank anda.", "pay.change_type": "<PERSON><PERSON> kaedah pem<PERSON>", "global.payment.expiryDate.empty": "<PERSON>la pilih tarikh tamat tempoh kad kredit", "global.pay.wait.tip": "Pembayaran sedang diproses, sila tunggu...", "wechat.continue_btn": "Tempah sekarang", "global.tips.header": "Perhatian!", "alert_title_error": "Notis!", "msgValidate_title1": "<PERSON><PERSON><PERSON><PERSON>", "sms_verify_code_subtitle": "Untuk keselamatan pembayaran anda, kami mesti mengesahkan nombor telefon anda. Satu kod telah dihantar ke <span class='f18'><b class='j_msgValidate_tel'></b></span>", "msgValidate_resend": "<PERSON><PERSON> semula <PERSON> <span id='v_countDown'></span>", "sign_verify": "<PERSON><PERSON><PERSON>", "sign_resend_code": "<PERSON><PERSON> semula kod", "msgValidate_code": "<PERSON><PERSON><PERSON><PERSON>", "pay.sg_term.option_yes": "Ya", "pay.sg_term.option_no": "Tidak", "pay.qrcode_validity": "Tempoh Sah Kod QR:", "pay.scan_qrcode": "Sila guna WeChat Scan untuk melengkapkan pembayaran anda", "scan_alipayhk_qr_code": "Imbas kod QR dengan AlipayHK untuk melengkapkan pembayaran anda", "download_alipay_hk": "Muat turun AlipayHK", "alipayhk_encounter_problem": "<PERSON><PERSON> anda men<PERSON> sebarang ma<PERSON>, sila hubungi talian penting perkhidmatan AlipayHK"}