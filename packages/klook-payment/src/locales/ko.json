{"11872": "국가/지역을 선택하세요.", "12346": "로딩에 실패했습니다 페이지 새로고침 후 다시 시도해주세요.", "17826": "숨기기", "26209": "시스템 오류", "27068": "카드번호", "27069": "유효한 카드번호를 입력하세요.", "27075": "전화번호", "27076": "유효한 전화번호를 입력하세요.", "27078": "카드 소유자 이름", "27079": "유효한 카드 소유자 이름을 입력하세요.", "27080": "신분증 번호", "27081": "유효한 신분증 번호를 입력하세요.", "30125": "쿠폰 코드 사용하기", "30782": "Scan with your bank app to pay", "44857": "총 금액", "44859": "QR코드는 {app} 앱으로 스캔해주세요.", "44860": "결제를 완료하실 때까지 페이지를 닫지 말아주세요.", "44861": "결제를 완료하신 후 페이지가 자동으로 넘어가지 않는다면", "44862": "여기", "44863": "를 클릭해주세요.", "44864": "{app} 앱으로 결제하는 방법", "44865": "1. {app} 앱을 실행하세요.", "44866": "2. 앱을 사용하여 QR코드를 스캔하세요.", "44867": "3. 앱에서 결제를 마친 후 확인이 완료될 때까지 기다려 주세요.", "44868": "뒤로 돌아가서 새로고침하기", "44869": "QR코드가 만료되었어요.", "44874": "예약 상태는 예약내역 페이지에서 확인해주세요.", "48181": "명시된 카드에 알맞는 유효한 카드번호를 입력하세요.", "48182": "결제 수단에 명시된 카드번호를 입력하세요.", "48183": "죄송합니다. 해당 카드는 본 주문에 사용하실 수 없습니다. 다른 카드로 결제해주세요.", "73205": "적용하기", "73234": "본 쿠폰 코드는 지정된 카드로 결제시에만 적용이 가능합니다. 다른 카드 또는 결제 수단을 사용하시겠어요?", "73235": "계속하기", "73236": "돌아가기", "79399": "Enter your phone number that's registered with DBS PayLah!", "79403": "Please enter a valid phone number", "79406": "Login to verify your DBS PayLah!", "79407": "You'll receive a payment request from DBS PayLah! with this phone number", "79986": "Phone number", "89760": "일시불 결제", "89764": "할부 결제는 5만원 이상 결제시에만 가능합니다.", "89768": "{0}개월", "89770": "할부 개월수", "89771": "무이자", "89773": "할부 결제시 할부 수수료가 발생할 수 있습니다. 적용되는 할부 수수료율은 카드마다 상이하므로, 카드 발급사 홈페이지 등을 통해 확인해주시기 바랍니다.", "89776": "할부 개월 수: {months}개월", "89780": "약관", "111121": "유효성 확인을 위해 약간의 수수료가 카드에 청구될 수 있습니다. 카드 유효성이 확인되면 해당 금액은 자동으로 환불됩니다.", "111123": "오류가 발생했습니다.", "171407": "우편번호", "171709": "다시 시도하기", "173899": "모든 결제수단 보기", "173909": "새 신용카드 또는 체크카드 추가하기", "173925": "쿠폰 선택 안함", "173929": "결제가 완료되지 않았어요. 다시 시도해주세요.", "173931": "결제를 완료하셨나요?", "173932": "네", "173933": "아니오", "176496": "새 카드 추가하기", "176511": "결제수단 선택", "176512": "확인", "176515": "CVV/CVC 입력", "176516": "카드 번호 입력", "179287": "올바른 우편번호를 입력하세요", "179791": "우편번호를 입력하세요.", "180106": "신뢰할 수 있는 결제 파트너사의 웹사이트에서 거래 결제가 완료될 예정입니다. 해당 웹사이트에서 결제 안내사항과 할부 이용약관을 확인해주세요.", "180107": "4개월 할부 가능", "180108": "할부 결제를 하시면 카드 발급사에 따라 할부 이자가 발생할 수 있습니다. 결제를 진행하기 전에 카드사에 할부 이자율을 꼭 확인해주세요. 클룩은 이자를 부과하지 않습니다.", "180109": "이용 가능한 카드 발급사", "180111": "방콕은행(Bangkok Bank)", "180113": "크룽스리은행(Krungsri Consumer)", "180114": "카드 X(Card X)", "180115": "카시콘은행(Kasikornbank)", "180116": "크룽타이카드(Krungthai Card)", "180117": "TMB 타나차트(TMB Thanachart)", "180118": "대화은행(United Overseas Bank / UOB)", "204719": "카드 소지자 성명", "204722": "성명을 입력해주세요.", "204723": "최소 2자 이상 입력해주세요.", "204724": "성명에는 특수문자나 숫자를 입력할 수 없어요.", "pay.add.cards": "카드 추가/등록", "global.payment.error": "결제에 실패했습니다. 다시 시도해주세요.", "index.payment.channel": "결제 수단", "payment.mobile.save_card": "해당 카드 다음에도 사용", "pay.credicard.tooltips": "고객님의 모든 결제 정보는 암호화되어 철저하게 보호됩니다.", "payment.mobile.credit_card_tip_title": "신용카드 정보는 안전한가요? ", "profile.mobile.edit.country_code": "국가 코드", "country.otherCountriesOrDistricts": "기타 국가 / 지역", "rail_phone_number": "핸드폰 번호", "rail_please_enter_your_phone_number": "전화번호를 입력하세요.", "global.error.cant_be_empty": "내용을 입력하세요", "global.select.empty_error": "선택하세요", "global.payment.type.tip": "결제방식을 선택하세요", "global.tips.okTxt": "확인", "global.tips.cancelTxt": "취소", "pay.sg_term.title": "보험 가입 권고 공지", "pay.sg_term.title_desc": "싱가포르 관광청의 권고 시행에 따라, 클룩은 해외여행을 하시는 싱가포르 여행객에게 여행사의 채무불이행으로 발생할 수 있는 피해 구제를 위해 여행 보험에 가입하실 것을 권장합니다. ", "pay.sg_term.question": "여행 보험에 가입하시겠습니까? ", "pay.sg_term.option_content_yes": "<a href=\"{0}\">여기</a> 있는 <b>제3자 보험사</b> 명단의 보험사를 통해 여행 보험에 가입하고자 합니다. 제 상세한 보험 정보는 **********************으로 이메일을 보내 클룩에 전달하겠습니다.", "pay.sg_term.option_content_no": "여행 보험에 가입하지 않겠습니다. 이로 인해 피해가 발생할 수 있음을 인지하고 있습니다. ", "pay.view_more_type": "결제 수단 더 보기", "see_more": "더 보기", "pay.card.number": "카드번호", "pay.card.expiryDate": "유효기간", "global.mm": "MM", "global.yyyy": "YYYY", "pay.card.SecurityCode": "CVV", "pay.contact.information": "국가 코드", "pay.card.save.credit": "카드 세부정보 저장", "pay.card.error.tip1": "입력하신 정보 중 일부는 유효하지 않은 정보입니다. 카드에 기재된 정보를 정확하게 입력해 주세요.", "payment.certain.credit.card": "해당 쿠폰 코드는 특정 신용카드 사용시에만 적용 가능합니다. 쿠폰 코드를 적용할 수 있는 유효한 신용카드를 사용해주세요.", "global.payment.cvv.need_be_number": "CVV(보안코드)가 유효하지 않습니다. 확인 후 다시 입력해주세요.", "global.select.palceholder": "선택하세요", "pay.validateTxt": "인증완료", "global.payment.cardnumber.empty": "신용카드 정보 필수", "global.payment.cvv.empty": "보안코드 (CVV) 필수", "global.payment1.cardnumber.empty": "카드번호를 올바르게 입력해주세요.", "pay.card.error.ntt.tip2": "잘못된 형식입니다. 확인 후 다시 시도해주세요.", "pay.card.error.ntt.tip3": "카드번호를 입력하세요.", "pay.card.error.expiration": "유효한 날짜를 입력하세요.", "pay.card.error.tip2": "네트워크 문제가 발생하였습니다. 잠시후 다시 시도하세요.", "pay.card.error.tip3": "신용카드 발급 기관에서 결제를 거부하였습니다. 자세한 내용은 카드 발급 은행에 문의하세요.", "pay.change_type": "결제 수단 변경", "global.payment.expiryDate.empty": "신용카드 유효기간을 선택하세요", "global.pay.wait.tip": "결제 진행중입니다..", "wechat.continue_btn": "바로 구매", "global.tips.header": "알림", "alert_title_error": "주의!", "msgValidate_title1": "인증코드 입력", "sms_verify_code_subtitle": "안전한 결제를 위해 핸드폰 인증이 필요합니다. <br>인증번호가 <span class='f18'><b class='j_msgValidate_tel'></b></span>  으로 발송 되었습니다.", "msgValidate_resend": "재발송 <span id='v_countDown'></span>", "sign_verify": "인증하기", "sign_resend_code": "인증코드 재발송", "msgValidate_code": "입력하기", "pay.sg_term.option_yes": "네", "pay.sg_term.option_no": "아니요", "pay.qrcode_validity": "QR 코드 유효기간:", "pay.scan_qrcode": "WeChat 스캔으로 결제를 완료하세요.", "scan_alipayhk_qr_code": "AlipayHK로 QR 코드를 스캔하여 결제를 완료해주세요.", "download_alipay_hk": "AlipayHK를 다운로드하십시오", "alipayhk_encounter_problem": "문제가 발생하는 경우, AlipayHK 고객센터로 전화해 주시기 바랍니다."}