{"11872": "国・地域を選択してください", "12346": "［読み込み失敗］再読み込みしてください。", "17826": "隠す", "26209": "Sorry, try again later", "27068": "カード番号", "27069": "有効なカード番号を入力してください", "27075": "電話番号（携帯）", "27076": "有効な電話番号を入力してください", "27078": "カード名義人氏名", "27079": "有効なカード名義人氏名を入力してください", "27080": "身分証明書番号", "27081": "有効な身分証明書番号を入力してください", "30125": "クーポンコードを使う", "30782": "Scan with your bank app to pay", "44857": "合計料金", "44859": "{app}でQRコードをスキャン", "44860": "お支払いが完了するまでこのページは閉じないでください。", "44861": "お支払いが完了してもページがリダイレクトされない場合は", "44862": "こちらをタップ", "44863": "してください。", "44864": "{app}でのお支払い方法", "44865": "1. {app}アプリを開く", "44866": "2. アプリでQRコードをスキャン", "44867": "3. アプリ内で支払いを完了させ、確定を待つ", "44868": "戻って再読み込み", "44869": "QRコードの有効期限が切れました", "44874": "予約ページに戻って予約状況を確認", "48181": "指定カードの有効なカード番号を入力してください", "48182": "指定カードのカード番号を入力してください", "48183": "この種類のカードは今回の注文に使用できません。別のカードをご使用ください。", "73205": "適用", "73234": "このクーポンコードは指定のカードでの支払い時にのみ使用できます。別のカードや決済方法で支払いますか?", "73235": "OK", "73236": "戻る", "79399": "Enter your phone number that's registered with DBS PayLah!", "79403": "Please enter a valid phone number", "79406": "Login to verify your DBS PayLah!", "79407": "You'll receive a payment request from DBS PayLah! with this phone number", "79986": "Phone number", "89760": "Pay in full", "89764": "Sorry, only orders of at least KRW 50,000 can be paid in instalments", "89768": "{0} months", "89770": "Select instalment plan", "89771": "Interest free", "89773": "Paying in instalments might incur interest. Make sure to confirm any interest rates with your card issuer before agreeing.", "89776": "Months of instalments: {months}", "89780": "Terms & Conditions", "111121": "カードの有効性を確認するため、少額の手数料が請求される場合があります。この金額は有効性が確認でき次第、自動的に返金されます。", "111123": "エラー発生", "171407": "郵便番号", "171709": "もう一度試す", "173899": "すべてのお支払い方法を表示", "173909": "新しいクレジットまたはデビットカードを追加", "173925": "クーポン未選択", "173929": "お支払いが完了しませんでした。もう一度お試しください", "173931": "お支払いは完了しましたか?", "173932": "はい", "173933": "いいえ", "176496": "新しいカードを追加", "176511": "お支払い方法の選択", "176512": "確定", "176515": "CVV/CVCを入力", "176516": "カード番号を入力", "179287": "正しい郵便番号を入力してください", "179791": "郵便番号を入力してください", "180106": "取引は信頼できる決済パートナーのウェブサイトで完了します。当該ウェブサイトで決済方法と分割払いに関する利用規約をご確認ください。", "180107": "4ヶ月分割払いプランあり", "180108": "分割払いの場合、カード発行会社によっては手数料（利息）が発生する場合があります。お支払いを続行する前に、必ずカード発行会社の手数料（利息）をご確認ください。Klookが手数料（利息）を請求することはありません。", "180109": "利用可能なカード発行会社", "180111": "バンコク銀行（BBL）", "180113": "クルンシイコンスーマー（Krungsri）", "180114": "Card X", "180115": "カシコン銀行（KBank）", "180116": "クルンタイカード（KTC）", "180117": "TMBタナチャート（TTB）", "180118": "ユナイテッド・オーバーシーズ銀行（UOB）", "204719": "カード名義人の名前", "204722": "名前を入力してください", "204723": "少なくとも2文字以上入力してください", "204724": "名前に特殊文字や数字を含めることはできません", "pay.add.cards": "カード情報を登録", "global.payment.error": "［支払い失敗］もう一度お試しください。", "index.payment.channel": "［お支払い方法］", "payment.mobile.save_card": "カード情報を保存", "pay.credicard.tooltips": "お支払い情報はすべて暗号化され、サーバーに安全に送信されます。", "payment.mobile.credit_card_tip_title": "カード情報の安全性について", "profile.mobile.edit.country_code": "国番号", "country.otherCountriesOrDistricts": "その他の国・地域", "rail_phone_number": "電話番号", "rail_please_enter_your_phone_number": "電話番号を入力してください", "global.error.cant_be_empty": "入力必須です", "global.select.empty_error": "選択してください", "global.payment.type.tip": "※お支払い方法を選択してください", "global.tips.okTxt": "OK", "global.tips.cancelTxt": "キャンセル", "pay.sg_term.title": "海外旅行保険加入推奨のお知らせ", "pay.sg_term.title_desc": "シンガポール政府観光局からの要請により、シンガポール国籍の方には、旅行会社倒産のリスクに備えるため、海外旅行保険への加入を推奨しています。", "pay.sg_term.question": "海外旅行保険へ加入されますか? ", "pay.sg_term.option_content_yes": "私は<a href=\"{0}\" target=\"_black\">一覧</a>にある保険会社から、<b>第三者の保険会社</b>が提供する旅行保険への加入を希望します。また、加入に必要な情報をKlookまでメール（<EMAIL>）にて提供します。", "pay.sg_term.option_content_no": "海外旅行保険に加入しない場合は、それに伴うリスクを理解したものと見なされます。", "pay.view_more_type": "他の支払い方法を表示", "see_more": "さらに表示", "pay.card.number": "カード番号", "pay.card.expiryDate": "有効期限", "global.mm": "MM（月）", "global.yyyy": "YYYY（年）", "pay.card.SecurityCode": "セキュリティコード（CVV）", "pay.contact.information": "国番号", "pay.card.save.credit": "カード情報を保存する", "pay.card.error.tip1": "一部の入力内容に誤りがあります。カード情報をもう一度確認してください。", "payment.certain.credit.card": "クーポンコードは一部のクレジットカードが対象です。対象のクレジットカードのみクーポンコードが適用されます。", "global.payment.cvv.need_be_number": "※セキュリティコード（CVV）が無効です。もう一度入力してください。", "global.select.palceholder": "選択してください", "pay.validateTxt": "認証完了", "global.payment.cardnumber.empty": "※クレジットカード番号を入力してください", "global.payment.cvv.empty": "※セキュリティコードを入力してください", "global.payment1.cardnumber.empty": "カード番号エラー", "pay.card.error.ntt.tip2": "フォーマットが正しくありません。ご確認のうえ、もう一度お試しください。", "pay.card.error.ntt.tip3": "カード番号を入力してください。", "pay.card.error.expiration": "有効期限を正しく入力してください", "pay.card.error.tip2": "ネットワークの問題が発生しました。あとでもう一度お試しください。", "pay.card.error.tip3": "ご利用のカード会社が決済を拒否しました。詳細については、ご利用の銀行にお問い合わせください。", "pay.change_type": "支払い方法を変更する", "global.payment.expiryDate.empty": "※クレジットカードの有効期限を選択してください", "global.pay.wait.tip": "お支払い手続き中...しばらくお待ちください", "wechat.continue_btn": "支払い手続きへ", "global.tips.header": "［注意］", "alert_title_error": "［エラー］", "msgValidate_title1": "認証コードを入力してください", "sms_verify_code_subtitle": "お支払いのセキュリティを確保するために、電話番号認証が必要です。「<span class='f18'><b class='j_msgValidate_tel'></b></span>」に認証コードが送信されました。", "msgValidate_resend": "コードを再送する<span id='v_countDown'></span>", "sign_verify": "認証", "sign_resend_code": "コードを再送", "msgValidate_code": "コードを入力", "pay.sg_term.option_yes": "加入する", "pay.sg_term.option_no": "加入しない", "pay.qrcode_validity": " QRコードの有効期限", "pay.scan_qrcode": "WeChatスキャンを使ってお支払いを完了してください。", "scan_alipayhk_qr_code": "Scan QR code with AlipayHK to complete your payment", "download_alipay_hk": "Download AlipayHK", "alipayhk_encounter_problem": "If you encounter any problems, please contact the AlipayHK service hotline"}