{"11872": "请选择国家 / 地区", "12346": "加载失败。请刷新以重试。", "17826": "收起", "26209": "系统处理发生异常", "27068": "卡号", "27069": "请填写有效卡号", "27075": "手机号码", "27076": "请输入有效手机号码", "27078": "持卡人姓名", "27079": "请填写有效持卡人姓名", "27080": "身份证号码", "27081": "请填写有效身份证号码", "30125": "使用优惠码", "30782": "请打开银行App扫一扫继续付款", "44857": "支付总金额", "44859": "扫描{app}二维码", "44860": "支付完成前请不要关闭此页面", "44861": "支付完成后如未跳转页面，请", "44862": "点此查看", "44863": "。", "44864": "如何使用{app}支付", "44865": "1. 打开{app}app", "44866": "2. 扫描二维码", "44867": "3. 完成支付，等待确认", "44868": "刷新重试", "44869": "二维码已失效", "44874": "前往订单页面查单订单状态", "48181": "请输入指定卡的有效卡号", "48182": "请输入指定卡的卡号", "48183": "抱歉，订单无法使用此卡付款，请使用其他卡再试。", "73205": "确认", "73234": "此优惠券仅限特定卡使用，建议更换其他支付方式", "73235": "继续", "73236": "返回", "79399": "请输入注册DBS PayLah!使用的手机号码", "79403": "请输入有效的手机号码", "79406": "请登录并验证DBS PayLah!账号", "79407": "此电话号码将收到来自DBS PayLah!的付款请求", "79986": "手机号码", "89760": "Pay in full", "89764": "Sorry, only orders of at least KRW 50,000 can be paid in instalments", "89768": "{0} months", "89770": "Select instalment plan", "89771": "Interest free", "89773": "Paying in instalments might incur interest. Make sure to confirm any interest rates with your card issuer before agreeing.", "89776": "Months of instalments: {months}", "89780": "Terms & Conditions", "111121": "为验证卡片有效期，可能会收取卡内小额费用。通过验证后，此费用将自动退还", "111123": "哎呀~ 出错了", "171407": "邮政编码", "171709": "重试", "173899": "查看所有付款方式", "173909": "添加付款新信用卡或借记卡", "173925": "未使用", "173929": "付款未成功，请重试", "173931": "已完成付款？", "173932": "是", "173933": "还没有", "176496": "新增卡片", "176511": "选择付款方式", "176512": "确认", "176515": "填写CVV／CVC码", "176516": "填写卡号", "179287": "请输入正确的邮政编码", "179791": "请填写邮政编码", "180106": "将在可信赖的第三方网站上完成付款交易，请于网站查看付款说明和分期付款", "180107": "提供4个月分期付款计划", "180108": "分期付款可能会产生利息，具体取决于发卡行。请于交易前向发卡行确认是否收取利息，Klook不收取任何利息", "180109": "适用发卡行", "180111": "泰国盘谷银行（BBL）", "180113": "Krungsri Consumer（大城银行）", "180114": "Card X", "180115": "开泰银行（KBank）", "180116": "泰京卡（KTC）", "180117": "TMB Thanachart (TTB)", "180118": "大华银行（UOB）", "204719": "持卡人姓名", "204722": "请填写姓名", "204723": "请填写至少2个字符", "204724": "姓名不能包含特殊字符或数字", "pay.add.cards": "添加新卡", "global.payment.error": "支付失败，请重试", "index.payment.channel": "支付方式", "payment.mobile.save_card": "保存卡信息", "pay.credicard.tooltips": "您的付款信息将由信誉良好的支付系统公司进行安全加密和保护。", "payment.mobile.credit_card_tip_title": "信用卡安全", "profile.mobile.edit.country_code": "国家 / 地区代码", "country.otherCountriesOrDistricts": "其他国家/地区", "rail_phone_number": "电话号码", "rail_please_enter_your_phone_number": "请填写手机号码", "global.error.cant_be_empty": "请填写内容", "global.select.empty_error": "请选择", "global.payment.type.tip": "请选择支付方式", "global.tips.okTxt": "确定", "global.tips.cancelTxt": "取消", "pay.sg_term.title": "官方声明", "pay.sg_term.title_desc": "依新加坡旅游局规定，从新加坡出发的游客应购买旅游保险，保护自身权益，以免旅行社破产而求偿无门。", "pay.sg_term.question": "你是否想购买保险？", "pay.sg_term.option_content_yes": "我想购买<a href=\"{0}\" target=\"_blank\">此处</a>所列的<b>第三方保险</b>，我会寄出详细资料至Klook客路团队 <EMAIL>。", "pay.sg_term.option_content_no": "我不想买保险，我了解不买保险的风险。", "pay.view_more_type": "查看更多支付方式", "see_more": "查看更多", "pay.card.number": "卡号", "pay.card.expiryDate": "有效日期", "global.mm": "MM", "global.yyyy": "YYYY", "pay.card.SecurityCode": "安全码", "pay.contact.information": "国家 / 地区代码", "pay.card.save.credit": "保存卡信息到我的账户，便于下次预订", "pay.card.error.tip1": "您输入的信用卡信息有误，请核对您的输入是否正确。", "payment.certain.credit.card": "此优惠码仅适用于指定信用卡，请使用符合要求的信用卡以享受优惠", "global.payment.cvv.need_be_number": "信用卡安全码(CVV)有误，请确认您的安全码输入正确", "global.select.palceholder": "请选择", "pay.validateTxt": "已验证", "global.payment.cardnumber.empty": "信用卡不能为空", "global.payment.cvv.empty": "安全码不能为空", "global.payment1.cardnumber.empty": "卡号错误", "pay.card.error.ntt.tip2": "卡号格式错误，请检查并重新输入", "pay.card.error.ntt.tip3": "请输入卡号", "pay.card.error.expiration": "请输入有效日期", "pay.card.error.tip2": "发生网络错误。请稍后重试。", "pay.card.error.tip3": "您的发卡银行无法核准本次交易。如需了解详情，请联络您的银行。", "pay.change_type": "更改支付方式", "global.payment.expiryDate.empty": "请选择信用卡有效期", "global.pay.wait.tip": "正在支付中,请耐心等待...", "wechat.continue_btn": "继续支付", "global.tips.header": "提醒", "alert_title_error": "提示", "msgValidate_title1": "输入短信验证码", "sms_verify_code_subtitle": "为了您的支付安全，需验证手机号码<br>验证码已发送至 <span class='f18'><b class='j_msgValidate_tel'></b></span>", "msgValidate_resend": "重新发送 <span id='v_countDown'></span>", "sign_verify": "验证", "sign_resend_code": "重新发送", "msgValidate_code": "输入验证码", "pay.sg_term.option_yes": "是", "pay.sg_term.option_no": "不", "pay.qrcode_validity": "二维码有效时长：", "pay.scan_qrcode": "打开手机微信扫一扫继续付款", "scan_alipayhk_qr_code": "使用支付宝扫描二维码进行付款", "download_alipay_hk": "立即下载支付宝香港", "alipayhk_encounter_problem": "如有疑问，请联系支付宝香港客户服务"}