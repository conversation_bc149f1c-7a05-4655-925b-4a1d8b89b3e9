{"11872": "<PERSON><PERSON> lòng chọn <PERSON> gia/<PERSON><PERSON>ng", "12346": "<PERSON><PERSON><PERSON><PERSON> thể tải trang. <PERSON><PERSON> thử lại.", "17826": "<PERSON><PERSON>", "26209": " Lỗi hệ thống ", "27068": "Số thẻ", "27069": "<PERSON><PERSON> lòng điền số thẻ hợp lệ", "27075": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "27076": "<PERSON><PERSON> lòng điền số điện tho<PERSON>i hợp lệ", "27078": "Tên chủ thẻ", "27079": "<PERSON><PERSON> lòng điền tên chủ thẻ hợp lệ", "27080": "Số thẻ", "27081": "<PERSON><PERSON> lòng điền số thẻ hợp lệ", "30125": "Sử dụng mã ưu đãi", "30782": "Scan with your bank app to pay", "44857": "<PERSON><PERSON><PERSON> cộng", "44859": "Quét mã QR này bằng {app}", "44860": "<PERSON><PERSON><PERSON> bảo trang này luôn bật cho đến khi bạn hoàn tất thanh toán.", "44861": "<PERSON><PERSON><PERSON> bạn không đư<PERSON><PERSON> chuyển hướng sau khi hoàn tất, vui lòng", "44862": "bấm vào đây", "44863": ".", "44864": "<PERSON><PERSON><PERSON> thanh toán bằng {app}", "44865": "1. Mở ứng dụng {app}", "44866": "2. <PERSON><PERSON><PERSON> mã QR bằng ứng dụng", "44867": "3. <PERSON><PERSON><PERSON> tất thanh toán trong ứng dụng và chờ xác nhận", "44868": "Quay lại và làm mới trang", "44869": "Mã QR đã hết hạn", "44874": "<PERSON><PERSON> đến trang Đơn hàng để kiểm tra trạng thái đơn hàng", "48181": "<PERSON><PERSON> lòng điền số thẻ hợp lệ", "48182": "<PERSON><PERSON> lòng điền số thẻ hợp lệ", "48183": "<PERSON><PERSON> lỗi, không thể sử dụng loại thẻ này cho đơn hàng. <PERSON><PERSON> lòng sử dụng thẻ khác.", "73205": "Sử dụng", "73234": "Mã ưu đãi này chỉ áp dụng cho một số thẻ nhất định. Bạn có muốn dùng thẻ hay phương thức thanh toán khác?", "73235": "<PERSON><PERSON><PERSON><PERSON>", "73236": "Quay lại", "79399": "Enter your phone number that's registered with DBS PayLah!", "79403": "Please enter a valid phone number", "79406": "Login to verify your DBS PayLah!", "79407": "You'll receive a payment request from DBS PayLah! with this phone number", "79986": "Phone number", "89760": "Pay in full", "89764": "Sorry, only orders of at least KRW 50,000 can be paid in instalments", "89768": "{0} months", "89770": "Select instalment plan", "89771": "Interest free", "89773": "Paying in instalments might incur interest. Make sure to confirm any interest rates with your card issuer before agreeing.", "89776": "Months of instalments: {months}", "89780": "Terms & Conditions", "111121": "<PERSON>ột khoản phí nhỏ có thể được tính vào thẻ của bạn để xác nhận tính hợp lệ. Số tiền sẽ được tự động hoàn trả sau khi xác minh.", "111123": "Đã có sự cố xảy ra", "171407": "<PERSON><PERSON> b<PERSON><PERSON>", "171709": "<PERSON><PERSON><PERSON> lại", "173899": "<PERSON><PERSON> tất cả ph<PERSON><PERSON><PERSON> thức thanh toán", "173909": "Thê<PERSON> thẻ tín dụng hoặc thẻ ghi nợ mới để thanh toán", "173925": "Không sử dụng", "173929": "<PERSON><PERSON> to<PERSON> không thành công. <PERSON><PERSON> lòng thử lại.", "173931": "Bạn đã hoàn tất thanh toán chưa?", "173932": "<PERSON><PERSON><PERSON> t<PERSON>t", "173933": "Chưa", "176496": "Thêm thẻ mới", "176511": "<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON><PERSON> thức thanh toán", "176512": "<PERSON><PERSON><PERSON>", "176515": "Nhập CVV/CVC", "176516": "<PERSON><PERSON><PERSON><PERSON> số thẻ", "179287": "<PERSON><PERSON> lòng nhập mã bưu điện ch<PERSON>h xác", "179791": "<PERSON><PERSON> lòng điền mã bưu ch<PERSON> (zip code)", "180106": "Bạn sẽ hoàn tất giao dịch tại trang web đối tác uy tín của chúng tôi. Vui lòng kiểm tra hướng dẫn thanh toán và các điều khoản trả góp tại đó.", "180107": "Gói trả góp 4 tháng", "180108": "<PERSON><PERSON> toán theo đợt có thể phải chịu lãi suất tùy thuộc vào đơn vị phát hành thẻ. Vui lòng xác nhận kỹ mức lãi suất với đơn vị phát hành thẻ của bạn trước khi tiếp tục. Klook sẽ không tính lãi cho bạn.", "180109": "Đơn vị phát hành thẻ đ<PERSON><PERSON><PERSON> chấp nhận", "180111": "Bangkok Bank (BBL)", "180113": "Krungsri Consumer (Krungsri)", "180114": "Card X", "180115": "Kasikornbank (KBank)", "180116": "KTC credit card (KTC)", "180117": "TMB Thanachart (TTB)", "180118": "United Overseas Bank (UOB)", "204719": "Tên chủ thẻ", "204722": "<PERSON><PERSON> lòng nhập tên", "204723": "<PERSON><PERSON> lòng nhập ít nhất 2 ký tự", "204724": "<PERSON><PERSON><PERSON> không thể chứa ký tự đặc biệt hoặc số", "pay.add.cards": "Thêm thẻ mới", "global.payment.error": "<PERSON><PERSON><PERSON> d<PERSON><PERSON> không thành công, vui lòng thử lại", "index.payment.channel": "KÊNH THANH TOÁN", "payment.mobile.save_card": "<PERSON><PERSON><PERSON> thẻ này để dùng cho các đơn hàng sau", "pay.credicard.tooltips": "Thông tin giao dịch của bạn được mã hóa an toàn bởi các hệ thống thanh toán uy tín.", "payment.mobile.credit_card_tip_title": "Thông tin thẻ của tôi có an toán?", "profile.mobile.edit.country_code": "Mã quốc gia", "country.otherCountriesOrDistricts": "Nước k<PERSON>", "rail_phone_number": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "rail_please_enter_your_phone_number": "<PERSON><PERSON><PERSON><PERSON> số điện thoại", "global.error.cant_be_empty": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON><PERSON><PERSON> để trống ", "global.select.empty_error": "<PERSON><PERSON>", "global.payment.type.tip": "<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON><PERSON> thức thanh toán", "global.tips.okTxt": "OK", "global.tips.cancelTxt": "Huỷ", "pay.sg_term.title": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON> ch<PERSON>h thức", "pay.sg_term.title_desc": "<PERSON> y<PERSON> cầu của Tổ<PERSON> cục Du lịch Singapore, du khách đến từ Singapore nên mua bảo hiểm du lịch để bảo vệ bản thân trước nguy cơ gặp vấn đề tài chính của bất kỳ đại lý du lịch nào.\n", "pay.sg_term.question": "Bạn có muốn mua bảo hiểm như trên?", "pay.sg_term.option_content_yes": "Tôi muốn mua bảo hiểm tương tự từ các <b>công ty bảo hiểm bên thứ ba</b> nh<PERSON> đư<PERSON><PERSON> li<PERSON> kê <a href=\"{0}\" target=\"_blank\">tại đây</a> và tôi sẽ gửi thông tin chi tiết cho Klook <NAME_EMAIL>", "pay.sg_term.option_content_no": "<PERSON><PERSON><PERSON> không muốn mua bảo hiểm tương tự và hoàn toàn hiểu rõ các rủi ro ", "pay.view_more_type": "<PERSON><PERSON><PERSON><PERSON> thức thanh to<PERSON> k<PERSON>c", "see_more": "<PERSON><PERSON>", "pay.card.number": "Số thẻ", "pay.card.expiryDate": "<PERSON><PERSON><PERSON> h<PERSON> hạn ", "global.mm": "MM", "global.yyyy": "YYYY", "pay.card.SecurityCode": "<PERSON><PERSON> b<PERSON>o mật ", "pay.contact.information": "Mã quốc gia", "pay.card.save.credit": "<PERSON><PERSON><PERSON> thông tin thẻ", "pay.card.error.tip1": "Thông tin bạn điền không xác thực. <PERSON><PERSON> lòng xem lại thông tin lưu hiển thị trên thẻ", "payment.certain.credit.card": "Mã ưu đãi chỉ áp dụng cho một số loại thẻ xác định. Vui lòng chọn loại thẻ tín dụng hợp lệ để sử dụng mã ưu đãi này", "global.payment.cvv.need_be_number": "Mã CVV không đúng. Vui lòng kiểm tra lại", "global.select.palceholder": "<PERSON><PERSON> lòng ch<PERSON>n", "pay.validateTxt": "Đ<PERSON> x<PERSON>c <PERSON>h", "global.payment.cardnumber.empty": "<PERSON><PERSON><PERSON> cầu credit card", "global.payment.cvv.empty": "<PERSON><PERSON><PERSON> c<PERSON>u mã b<PERSON>o mật ", "global.payment1.cardnumber.empty": "<PERSON><PERSON> sự cố với số thẻ của bạn", "pay.card.error.ntt.tip2": "<PERSON><PERSON><PERSON> dạng không đúng. <PERSON><PERSON><PERSON> kiểm tra và thử lại", "pay.card.error.ntt.tip3": "<PERSON><PERSON> lòng điền số thẻ của bạn", "pay.card.error.expiration": "<PERSON><PERSON><PERSON><PERSON> ng<PERSON><PERSON> h<PERSON> l<PERSON>", "pay.card.error.tip2": "Lỗi kết nối. <PERSON><PERSON> lòng thử lại", "pay.card.error.tip3": "<PERSON>ân hàng phát hành thẻ của bạn đã từ chối giao dịch này. <PERSON><PERSON> biết thêm thông tin, vui lòng liên hệ với ngân hàng của bạn.", "pay.change_type": "<PERSON>hay đ<PERSON>i ph<PERSON><PERSON><PERSON> thức thanh toán ", "global.payment.expiryDate.empty": "<PERSON><PERSON> lòng chọn ngày hết hạn của thẻ credit card", "global.pay.wait.tip": "<PERSON><PERSON> tiến hành <PERSON> to<PERSON>, vui lòng chờ...", "wechat.continue_btn": "<PERSON><PERSON><PERSON><PERSON> hành <PERSON>h toán", "global.tips.header": "<PERSON><PERSON><PERSON>", "alert_title_error": "<PERSON><PERSON>u <PERSON>!", "msgValidate_title1": "<PERSON><PERSON><PERSON><PERSON> mã x<PERSON>c minh", "sms_verify_code_subtitle": "<PERSON><PERSON><PERSON> với bảo mật thanh toán của bạn, chúng tôi phải xác minh số điện thoại của bạn, mã đã đư<PERSON><PERSON> gửi đến <span class='f18'><b class='j_msgValidate_tel'></b></span>", "msgValidate_resend": "G<PERSON>i lại mã  <span id='v_countDown'></span>", "sign_verify": "<PERSON><PERSON><PERSON>", "sign_resend_code": "<PERSON><PERSON><PERSON> lại mã x<PERSON>c n<PERSON>n", "msgValidate_code": "<PERSON>h<PERSON><PERSON> mã ", "pay.sg_term.option_yes": "<PERSON><PERSON>", "pay.sg_term.option_no": "K<PERSON>ô<PERSON>", "pay.qrcode_validity": "Hạn sử dụng của QR Code:", "pay.scan_qrcode": "Xin sử dụng WeChat Scan để hoàn tất thanh toán của bạn ", "scan_alipayhk_qr_code": "Scan mã QR code với AlipayHK để hoàn tất thanh toán của bạn ", "download_alipay_hk": "Tải AlipayHK", "alipayhk_encounter_problem": "<PERSON><PERSON><PERSON> bạn gặp bất kỳ vấn đề nào, xin liên hệ với số hotline của AlipayHK "}