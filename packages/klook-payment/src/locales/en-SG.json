{"11872": "Please select country/region", "12346": "Loading failed. Please refresh to try again.", "17826": "See less", "26209": "Sorry, try again later", "27068": "Card number", "27069": "Please enter a valid card number", "27075": "Mobile number", "27076": "Please enter a valid phone number", "27078": "Card holder name", "27079": "Please enter a valid card holder name", "27080": "ID card number", "27081": "Please enter a valid ID card number", "30125": "Use promo code", "30782": "Scan with your bank app to pay", "44857": "Total", "44859": "Scan this QR code with {app}", "44860": "Make sure to keep this page open until you've completed the payment.", "44861": "If you're not redirected after completing the payment, please", "44862": "go here", "44863": ".", "44864": "How to pay with {app}", "44865": "1. Open the {app} app", "44866": "2. <PERSON>an the QR code using the app", "44867": "3. Complete the payment in the app and wait for confirmation", "44868": "Go back to refresh", "44869": "The QR code expired", "44874": "Head to the Bookings page to check the booking status", "48181": "Please enter a valid card number from a card specified", "48182": "Please enter a card number from a card specified", "48183": "Sorry, this card type can't be used for this order. Please use another card.", "73205": "Use", "73234": "You can use this promo code only when you're paying with the designated card. Try using a different card or payment method?", "73235": "Continue", "73236": "Go back", "79399": "Enter your phone number that's registered with DBS PayLah!", "79403": "Please enter a valid phone number", "79406": "Login to verify your DBS PayLah!", "79407": "You'll receive a payment request from DBS PayLah! with this phone number", "79986": "Phone number", "89760": "Pay in full", "89764": "Sorry, only orders of at least KRW 50,000 can be paid in instalments", "89768": "{0} months", "89770": "Select instalment plan", "89771": "Interest free", "89773": "Paying in instalments might incur interest. Make sure to confirm any interest rates with your card issuer before agreeing.", "89776": "Months of instalments: {months}", "89780": "Terms & Conditions", "111121": "A small fee may be charged to your card to confirm validity. The amount will be automatically refunded once it's verified.", "111123": "Something went wrong", "171407": "Zip code", "171709": "Try again", "173899": "See all payment methods", "173909": "Add a new credit or debit card to pay", "173925": "Not in use", "173929": "Your payment didn't go through. Please try again.", "173931": "Have you completed payment?", "173932": "Yes", "173933": "Not yet", "176496": "Add a new card", "176511": "Select payment method", "176512": "Confirm", "176515": "Enter CVV/CVC", "176516": "Enter card number", "179287": "Please enter the correct zip code", "179791": "Please enter a zip code", "180106": "You'll complete the transaction at our trusted payment partner’s website. Please check the payment instructions and installment terms there.", "180107": "4 months installment plan available", "180108": "Paying in installments might incur interest depending on the issuer. Make sure to confirm any interest rates with your card issuer before proceeding. Klook won't charge you any interest.", "180109": "Accepted card issuers", "180111": "Bangkok Bank (BBL)", "180113": "Krungsri Consumer (Krungsri)", "180114": "CardX / SCB", "180115": "Kasikornbank (KBank)", "180116": "KTC credit card (KTC)", "180117": "TMB Thanachart (TTB)", "180118": "United Overseas Bank (UOB)", "204719": "Cardholder's name", "204722": "Please enter a name", "204723": "Please enter at least 2 characters", "204724": "Name cannot have special characters or numbers", "pay.add.cards": "Add payment card", "global.payment.error": "Transaction failed, please try again", "index.payment.channel": "Payment channels", "payment.mobile.save_card": "Save this card for next time", "pay.credicard.tooltips": "Your payment information is securely encrypted by reliable payment systems", "payment.mobile.credit_card_tip_title": "Are my credit card details safe?", "profile.mobile.edit.country_code": "Country/region code", "country.otherCountriesOrDistricts": "More countries/regions", "rail_phone_number": "Phone Number", "rail_please_enter_your_phone_number": "Please enter your phone number", "global.error.cant_be_empty": "Required field", "global.select.empty_error": "Please select", "global.payment.type.tip": "Please select payment type", "global.tips.okTxt": "OK", "global.tips.cancelTxt": "Cancel", "pay.sg_term.title": "Official Statement", "pay.sg_term.title_desc": "As required by Singapore Tourism Board, travelers from Singapore are advised to purchase travel insurance to protect themselves against insolvency of any travel agents. ", "pay.sg_term.question": "Would you like to purchase such insurance? ", "pay.sg_term.option_content_yes": "I wish to purchase such insurance from <b>the third party insurers</b> as listed <a href=\"{0}\" target=\"_blank\">here</a> and I will email the details to K<PERSON> at <EMAIL>.", "pay.sg_term.option_content_no": "I do not wish to purchase such insurance and acknowledge the risk of not making such purchase.", "pay.view_more_type": "More Payment Methods", "see_more": "See more", "pay.card.number": "Card number", "pay.card.expiryDate": "Expiration date", "global.mm": "MM", "global.yyyy": "YYYY", "pay.card.SecurityCode": "Security code", "pay.contact.information": "Country/region code", "pay.card.save.credit": "Save card details", "pay.card.error.tip1": "Some of the information you filled in is invalid. Please check the information as shown on your card.", "payment.certain.credit.card": "Promo code is only applicable for select card issuers. Please use a valid credit card to avail this promo code", "global.payment.cvv.need_be_number": "CVV code is invalid. Please double confirm your input.", "global.select.palceholder": "Please select", "pay.validateTxt": "Verified", "global.payment.cardnumber.empty": "credit card required", "global.payment.cvv.empty": "security code required", "global.payment1.cardnumber.empty": "There's a problem with your card number", "pay.card.error.ntt.tip2": "That format doesn't look right. Please check and try again", "pay.card.error.ntt.tip3": "Please enter your card number", "pay.card.error.expiration": "Enter valid date", "pay.card.error.tip2": "Something went wrong. Please try again later.", "pay.card.error.tip3": "Your card issuing bank has rejected this transaction. For more information, please contact your bank.", "pay.change_type": "Change payment method", "global.payment.expiryDate.empty": "Please select credit card expiration date", "global.pay.wait.tip": "Payment processing, please wait...", "wechat.continue_btn": "Book now", "global.tips.header": "Heads up!", "alert_title_error": "Notice!", "msgValidate_title1": "Enter Verification Code", "sms_verify_code_subtitle": "For your payment security, we must verify your phone number, a code has been sent to <span class='f18'><b class='j_msgValidate_tel'></b></span>", "msgValidate_resend": "Resend Code <span id='v_countDown'></span>", "sign_verify": "Verify", "sign_resend_code": "Resend code", "msgValidate_code": "Enter Code", "pay.sg_term.option_yes": "Yes", "pay.sg_term.option_no": "No", "pay.qrcode_validity": "QR Code Validity:", "pay.scan_qrcode": "Please use WeChat <PERSON>an to complete your payment", "scan_alipayhk_qr_code": "Scan QR code with AlipayHK to complete your payment", "download_alipay_hk": "Download AlipayHK", "alipayhk_encounter_problem": "If you encounter any problems, please contact the AlipayHK service hotline"}