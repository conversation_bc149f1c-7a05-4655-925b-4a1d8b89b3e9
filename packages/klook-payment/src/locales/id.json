{"11872": "<PERSON><PERSON><PERSON> pilih", "12346": "Gagal memuat. Silakan coba lagi.", "17826": "Sembunyikan", "26209": "system error", "27068": "Nomor kartu", "27069": "<PERSON><PERSON> isi dengan nomor yang benar", "27075": "Nomor ponsel", "27076": "<PERSON>hon isi dengan nomor telepon yang benar", "27078": "<PERSON>a pemegang kartu", "27079": "<PERSON><PERSON> isi dengan nama yang benar", "27080": "Nomor kartu", "27081": "<PERSON><PERSON> isi dengan nomor yang benar", "30125": "<PERSON>aka<PERSON> kode promo", "30782": "Scan with your bank app to pay", "44857": "Total", "44859": "Scan QR Code ini dengan {app}", "44860": "Pastikan untuk menjaga halaman ini tetap terbuka sampai kamu menyelesaikan pembayaran.", "44861": "<PERSON>ka kamu halaman tidak dialihkan otomatis setelah menye<PERSON> pem<PERSON>, silakan", "44862": "<PERSON>k ke sini", "44863": ".", "44864": "<PERSON> memba<PERSON> dengan {app}", "44865": "1. <PERSON><PERSON> {app}", "44866": "2. Scan QR Code menggunakan aplikasi", "44867": "3. <PERSON><PERSON>ai<PERSON> pembayaran di aplikasi dan tunggu konfirmasi", "44868": "<PERSON><PERSON><PERSON> untuk refresh halaman", "44869": "QR Code kedaluwarsa", "44874": "<PERSON><PERSON> halaman <PERSON>an untuk cek status pemesanan", "48181": "Mohon isi kartu nomor yang valid", "48182": "<PERSON><PERSON> masukkan nomor kartu dari kartu yang telah ditentukan", "48183": "<PERSON><PERSON>, jenis kartu ini tidak dapat digunakan untuk pesanan ini. <PERSON><PERSON><PERSON> gunakan kartu lain.", "73205": "<PERSON><PERSON>", "73234": "Kamu hanya bisa menggunakan kode promo saat kamu membayar dengan kartu yang ditentukan. Cobalah menggunakan kartu atau metode pembayaran lain.", "73235": "Lanjutkan", "73236": "Kembali", "79399": "Enter your phone number that's registered with DBS PayLah!", "79403": "Please enter a valid phone number", "79406": "Login to verify your DBS PayLah!", "79407": "You'll receive a payment request from DBS PayLah! with this phone number", "79986": "Phone number", "89760": "Pay in full", "89764": "Sorry, only orders of at least KRW 50,000 can be paid in instalments", "89768": "{0} months", "89770": "Select instalment plan", "89771": "Interest free", "89773": "Paying in instalments might incur interest. Make sure to confirm any interest rates with your card issuer before agreeing.", "89776": "Months of instalments: {months}", "89780": "Terms & Conditions", "111121": "Akan dikenakan sedikit biaya ke kartu kamu untuk mengonfirmasi validitas. Ju<PERSON>lah tersebut akan dikembalikan secara otomatis setelah diverifikasi.", "111123": "<PERSON><PERSON><PERSON><PERSON>", "171407": "<PERSON><PERSON> pos", "171709": "Coba lagi", "173899": "<PERSON><PERSON> semua metode pem<PERSON>", "173909": "Menambah kartu kredit atau debit baru untuk membayar", "173925": "Tidak terpakai", "173929": "Pembayaran kamu tidak berhasil. Silakan coba lagi.", "173931": "Apakah kamu sudah menye<PERSON>kan pembayaran?", "173932": "Ya", "173933": "<PERSON><PERSON>", "176496": "Tambah kartu baru", "176511": "<PERSON><PERSON><PERSON> metode pembayaran", "176512": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "176515": "Masukkan CVV/CVC", "176516": "Masukkan nomor kartu", "179287": "Silahkan masukkan kode pos yang benar", "179791": "Silakan isi kode pos/zip code", "180106": "<PERSON><PERSON> akan menyelesaikan transaksi di website mitra pembayaran tepercaya kami. Silakan cek petunjuk pembayaran dan ketentuan cicilan di sana.", "180107": "Tersedia cicilan 4 bulan", "180108": "Me<PERSON><PERSON> secara mencicil mungkin dikenakan bunga tergantung pada penerbitnya. Pastikan untuk mengonfirmasi suku bunga apa pun dengan penerbit kartu kamu sebelum melanjutkan. Klook tidak akan membebankan bunga apa pun kepada kamu.", "180109": "Penerbit kartu yang diterima", "180111": "Bangkok Bank (BBL)", "180113": "Krungsri Consumer (Krungsri)", "180114": "Card X", "180115": "Kasikornbank (KBank)", "180116": "Kartu kredit KTC (KTC)", "180117": "TMB Thanachart (TTB)", "180118": "United Overseas Bank (UOB)", "204719": "<PERSON>a pemegang kartu", "204722": "<PERSON><PERSON><PERSON> isi nama", "204723": "<PERSON><PERSON>an isi setidaknya 2 karakter", "204724": "<PERSON>a tidak dapat terdiri dari karakter khusus atau angka", "pay.add.cards": "Tambah Kartu", "global.payment.error": "<PERSON><PERSON><PERSON> gag<PERSON>, silakan coba lagi", "index.payment.channel": "METODE PEMBAYARAN", "payment.mobile.save_card": "Simpan kartu ini untuk pemesanan selanjutnya", "pay.credicard.tooltips": "Informasi tentang pembayaran anda telah dienskripsi dengan aman oleh sistem pembayaran yang terkemuka.", "payment.mobile.credit_card_tip_title": "Apakah info kartu kredit saya aman?", "profile.mobile.edit.country_code": "Kode negara", "country.otherCountriesOrDistricts": "<PERSON>eg<PERSON> / distrik lain", "rail_phone_number": "Nomor telepon", "rail_please_enter_your_phone_number": "Ma<PERSON>kkan nomor telepon anda", "global.error.cant_be_empty": "<PERSON><PERSON><PERSON>", "global.select.empty_error": "<PERSON><PERSON><PERSON> pilih", "global.payment.type.tip": "<PERSON><PERSON><PERSON>", "global.tips.okTxt": "OK", "global.tips.cancelTxt": "Cancel", "pay.sg_term.title": "<PERSON><PERSON><PERSON>", "pay.sg_term.title_desc": "Sesuai ketentuan Singapore Tourism Board, wisatawan asal Singapura dianjurkan membeli asuransi perjalanan untuk melindungi diri bila agen perjalanan gagal memberikan layanan. \n\n", "pay.sg_term.question": "<PERSON><PERSON><PERSON><PERSON> anda ingin membeli asuransi tersebut?", "pay.sg_term.option_content_yes": "<PERSON>a ingin me<PERSON> <b>asuransi dari pihak ketiga</b> seperti yang tertera <a href=\"{0}\" target=\"_blank\">di sini</a> dan saya akan mengirimkan detailnya <NAME_EMAIL> kepada Klook.", "pay.sg_term.option_content_no": "<PERSON>a tidak ingin membeli asuransi dan sudah memahami risikonya", "pay.view_more_type": "<PERSON><PERSON>", "see_more": "<PERSON><PERSON>a", "pay.card.number": "<PERSON><PERSON>", "pay.card.expiryDate": "Tanggal Expiry", "global.mm": "MM", "global.yyyy": "YYYY", "pay.card.SecurityCode": "<PERSON><PERSON>", "pay.contact.information": "Kode negara", "pay.card.save.credit": "Simpan informasi kartu", "pay.card.error.tip1": "Beberapa informasi yang anda isi tidak valid. Silakan cek lagi informasi sesuai kartu anda.", "payment.certain.credit.card": "Kode promo hanya berlaku untuk jenis kartu kredit tertentu. Harap menggunakan kartu kredit yang berlaku untuk menggunakan kode promo", "global.payment.cvv.need_be_number": "Kode CVV tidak valid. Mohon cek ulang.", "global.select.palceholder": "<PERSON><PERSON><PERSON> pilih", "pay.validateTxt": "<PERSON><PERSON>", "global.payment.cardnumber.empty": "kartu kredit wajib diisi", "global.payment.cvv.empty": "<PERSON><PERSON> keamanan kartu wajib diisi", "global.payment1.cardnumber.empty": "Nomor kartu anda bermasalah", "pay.card.error.ntt.tip2": "Format salah. Mohon periksa kembali dan coba lagi", "pay.card.error.ntt.tip3": "<PERSON><PERSON>an isi nomor kartu anda", "pay.card.error.expiration": "<PERSON>i dengan tanggal yang valid", "pay.card.error.tip2": "<PERSON> kes<PERSON>han jar<PERSON>n. <PERSON><PERSON>an coba lagi nanti.", "pay.card.error.tip3": "Bank penerbit kartu anda telah menolak transaksi ini. Untuk informasi lebih lanjut, silakan hubungi bank anda.", "pay.change_type": "Ubah metode pembayaran", "global.payment.expiryDate.empty": "mohon pilih tanggal expiry kartu kredit", "global.pay.wait.tip": "Pembayaran sedang diproses, mohon tunggu...", "wechat.continue_btn": "Lanjut untuk Bayar", "global.tips.header": "Pemberitahuan", "alert_title_error": "<PERSON>gumuman!", "msgValidate_title1": "Masukkan Ko<PERSON> Verifikasi", "sms_verify_code_subtitle": "<PERSON><PERSON><PERSON> kema<PERSON>n p<PERSON>, kami perlu memverifikasi nomor telepon anda, kode verikasi telah dikirim ke <span class='f18'><b class='j_msgValidate_tel'></b></span>", "msgValidate_resend": "<PERSON><PERSON>de <span id='v_countDown'></span>", "sign_verify": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sign_resend_code": "<PERSON><PERSON> kode", "msgValidate_code": "<PERSON><PERSON><PERSON><PERSON>", "pay.sg_term.option_yes": "Ya", "pay.sg_term.option_no": "Tidak", "pay.qrcode_validity": "Masa berlaku QR code:", "pay.scan_qrcode": "Mohon scan menggunakan Wechat dan selesaikan pembayaran anda", "scan_alipayhk_qr_code": "Pindai QR code dengan AlipayHK untuk menyelesaikan pemesanan anda", "download_alipay_hk": "Download AlipayHK", "alipayhk_encounter_problem": "<PERSON><PERSON> anda men<PERSON>n ma<PERSON>ah, silakan hubungi layanan pelanggan AlipayHK secara langsung"}