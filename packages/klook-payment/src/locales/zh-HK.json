{"11872": "請選擇國家／地區", "12346": "載入失敗，請重新整理頁面再試一次。", "17826": "查看較少", "26209": "系統處理發生異常", "27068": "卡號碼", "27069": "請輸入有效付款卡號碼", "27075": "電話號碼", "27076": "請輸入有效電話號碼", "27078": "持卡人姓名", "27079": "請輸入有效的持卡人姓名", "27080": "身份證號碼", "27081": "請輸入有效的身份證號碼", "30125": "使用優惠碼", "30782": "Scan with your bank app to pay", "44857": "總額", "44859": "請使用{app}掃瞄此QR Code", "44860": "完成付款前請勿關閉此頁面。", "44861": "如果完成付款後畫面未有跳轉，請", "44862": "按此", "44863": "。", "44864": "如何以{app}付款", "44865": "1. 打開 {app} App", "44866": "2. 使用App掃瞄QR Code", "44867": "3. 透過App完成付款並等待確認", "44868": "返回上頁以更新", "44869": "此QR Code有效期已過", "44874": "前往訂單頁面查看預訂狀態", "48181": "請輸入指定付款卡的有效卡號碼", "48182": "請輸入指定付款卡的卡號碼", "48183": "很抱歉，此付款卡類型不適用於此訂單，請使用另一張卡付款", "73205": "使用", "73234": "此優惠碼僅限指定支付卡使用，請更換支付卡或選擇其他付款方式", "73235": "繼續", "73236": "返回", "79399": "Enter your phone number that's registered with DBS PayLah!", "79403": "Please enter a valid phone number", "79406": "Login to verify your DBS PayLah!", "79407": "You'll receive a payment request from DBS PayLah! with this phone number", "79986": "Phone number", "89760": "Pay in full", "89764": "Sorry, only orders of at least KRW 50,000 can be paid in instalments", "89768": "{0} months", "89770": "Select instalment plan", "89771": "Interest free", "89773": "Paying in instalments might incur interest. Make sure to confirm any interest rates with your card issuer before agreeing.", "89776": "Months of instalments: {months}", "89780": "Terms & Conditions", "111121": "為驗證支付卡有效期，將酌扣該卡少量費用。支付卡通過驗證後，該費用將自動退還", "111123": "發生了點錯誤", "171407": "郵遞區號", "171709": "重試一次", "173899": "查看全部付款方式", "173909": "新增付款信用卡／簽帳金融卡", "173925": "未使用", "173929": "付款未成功，請重新付款", "173931": "已完成付款？", "173932": "是", "173933": "否", "176496": "新增卡片", "176511": "選擇付款方式", "176512": "確認", "176515": "填寫 CVV／CVC 碼", "176516": "填寫卡片號碼", "179287": "請輸入正確的郵政編碼", "179791": "請輸入郵政編碼", "180106": "刷卡交易將於可信賴的第三方付款網站完成，請於該網站查看付款說明與分期條款", "180107": "提供4個月分期付款方案", "180108": "視發卡銀行而定，分期付款可能產生利息。請於交易前向發卡銀行確認是否收取利息，Klook 不收取任何利息", "180109": "適用發卡銀行", "180111": "泰國盤谷銀行（BBL）", "180113": "Krungsri Consumer（大城銀行）", "180114": "Card X", "180115": "開泰銀行（KBank）", "180116": "泰京卡（KTC）", "180117": "TMB Thanachart (TTB)", "180118": "大華銀行（UOB）", "204719": "持卡人姓名", "204722": "請填寫姓名", "204723": "請至少填寫2個字元", "204724": "姓名不能含有特殊字元或數字", "pay.add.cards": "添加新卡", "global.payment.error": "付款失敗，請再試一次", "index.payment.channel": "支付方式", "payment.mobile.save_card": "保存卡資料", "pay.credicard.tooltips": "你的付款資訊，將由信譽良好的支付系統公司進行安全加密和保護。", "payment.mobile.credit_card_tip_title": "信用卡安全", "profile.mobile.edit.country_code": "國家/地區代碼", "country.otherCountriesOrDistricts": "其他國家/地區", "rail_phone_number": "電話號碼", "rail_please_enter_your_phone_number": "請填寫手機號碼", "global.error.cant_be_empty": "請填寫內容", "global.select.empty_error": "請選擇", "global.payment.type.tip": "請選擇付款方式", "global.tips.okTxt": "確定", "global.tips.cancelTxt": "取消", "pay.sg_term.title": "官方聲明", "pay.sg_term.title_desc": "依新加坡旅遊局規定，從新加坡出發的遊客應購買旅遊保險，保護自身權益，以免旅行社破產而索償無門。", "pay.sg_term.question": "你是否想購買保險？", "pay.sg_term.option_content_yes": "我想購買<a href=\"{0}\" target=\"_blank\">此處</a>所列之<b>第三方保險</b>，我會寄出詳細資訊至*****************予Klook團隊。", "pay.sg_term.option_content_no": "我不想買保險，我瞭解不買保險的風險。", "pay.view_more_type": "更多付款方式", "see_more": "查看更多", "pay.card.number": "卡號", "pay.card.expiryDate": "有效日期", "global.mm": "MM", "global.yyyy": "YYYY", "pay.card.SecurityCode": "安全碼", "pay.contact.information": "國家/地區代碼", "pay.card.save.credit": "保存卡資料到我的帳户，方便下次預訂", "pay.card.error.tip1": "您輸入的卡資料有誤。請核對您是否已輸入正確資料。", "payment.certain.credit.card": "此優惠碼只適用指定信用卡，如欲享用優惠，請使用合資格信用卡付款", "global.payment.cvv.need_be_number": "信用卡檢查碼(CVV)有誤，請確認您已輸入正確的檢查碼", "global.select.palceholder": "請選擇", "pay.validateTxt": "已驗證", "global.payment.cardnumber.empty": "信用卡不能為空", "global.payment.cvv.empty": "安全碼不能為空", "global.payment1.cardnumber.empty": "你輸入的卡號碼有點問題", "pay.card.error.ntt.tip2": "卡號碼格式似乎不正確，請檢查後再試一次", "pay.card.error.ntt.tip3": "請輸入你的卡號碼", "pay.card.error.expiration": "輸入有效日期", "pay.card.error.tip2": "發生連接錯誤。請稍後重試。", "pay.card.error.tip3": "您的發卡銀行無法批核本次交易。如需了解詳細資料，請聯絡您的發卡銀行。", "pay.change_type": "更改付款方式", "global.payment.expiryDate.empty": "請選擇信用卡有效期", "global.pay.wait.tip": "正在付款中,請耐心等待...", "wechat.continue_btn": "繼續支付", "global.tips.header": "提醒", "alert_title_error": "提示", "msgValidate_title1": "輸入短信驗證碼", "sms_verify_code_subtitle": "為了您的支付安全，需驗證手機號碼<br>驗證碼已發送至 <span class='f18'><b class='j_msgValidate_tel'></b></span>", "msgValidate_resend": "重新發送 <span id='v_countDown'></span>", "sign_verify": "驗證", "sign_resend_code": "重新發送", "msgValidate_code": "輸入驗證碼", "pay.sg_term.option_yes": "是", "pay.sg_term.option_no": "不", "pay.qrcode_validity": "QR Code有效時間：", "pay.scan_qrcode": "請使用微信掃描以完成付款", "scan_alipayhk_qr_code": "使用Alipay掃瞄QR Code以完成付款", "download_alipay_hk": "立即下載AlipayHK", "alipayhk_encounter_problem": "如有疑問，請聯絡AlipayHK客戶服務"}