@import "node_modules/@klook/klook-ui/lib/styles/token/index.scss";
@import '@klook/klook-card/dist/esm/index.css';
@import '@klook/klook-icons/styles/index.css';

.klook-payment {
  border-radius: $radius-xl;
  background: $color-bg-1;
  word-break: break-word;

  iframe {
    border: none;
  }

  img {
    object-fit: cover;
    background: none;
  }

  .paylater-alert {
    background-color: $color-success-background;
    display: flex;
    padding: 8px 16px;
    border-radius: $radius-l;
    position: relative;
    margin-bottom: 16px;

    .i-icon-icon-protect {
      margin-right: 12px;
    }

    &::before {
      content: '';
      position: absolute;
      top: -11px;
      left: 28px;
      border-left: 8px solid transparent;
      border-right: 8px solid transparent;
      border-bottom: 12px solid $color-success-background;
    }

    .klk-markdown p {
      @include font-body-s-regular-v2;
      margin: 0;
    }
  }

  .cashier-top {
    margin-bottom: 8px;

    .bold {
      font-weight: $fontWeight-semibold;
    }

    .orange {
      color: $color-caution;
    }

    .underline {
      text-decoration: underline;
    }
  }

  .supported-cards {
    .creditcard-icon {
      vertical-align: middle;
      border-radius: $radius-s;
      border: 1px solid $color-border-normal;
  
      &:not(:last-child) {
        margin-right: 8px;
      }
    }
  }

  .method-list {
    display: flex;
    flex-direction: column;

    .view-all-switch {
      order: 999999999999;
    }
  }

  .view-all-switch {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 16px 0 ;
  
    .payment-type-icon {
      margin-right: 8px;
    }
  
    .i-icon-icon-chevron-down {
      vertical-align: middle;
      transition: 0.3s transform;
    }
  
    &.is-view-all {
      .payment-type-icon {
        display: none;
      }
  
      .i-icon-icon-chevron-down {
        transform: rotateZ(-180deg);
      }
    }
  }

  &--section-title {
    @include font-body-m-bold;
    margin-bottom: 16px;
  }

  &.platform-desktop .klook-payment--section-title {
    @include font-heading-xs-v2;
  }

  .text-error {
    color: $color-brand-primary;
  }

  .text-secondary {
    color: $color-text-secondary;
    &.caption-12 {
      font-size: $fontSize-caption-m;
    }
  }

  .creditcard-new-snapshot {
    padding: 24px 20px;
    border: 1px dashed $color-text-disabled;
    border-radius: $radius-xl;
    text-align: center;

    .creditcard-new-title {
      color: $color-brand-primary;
      margin-bottom: 8px;
      @include font-body-m-bold;

      .i-icon-icon-add-circle {
        vertical-align: bottom;
        margin-right: 8px;
      }
    }
  }

  .method-label {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .left-content {
      flex: 1;
      min-width: 0;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .label-main {
        flex: 1;
        min-width: 0;

        &:first-child {
          margin-right: 12px;
        }
        &:nth-child(2) {
          margin-left: 12px;
        }
      }
    }

    .right-content {
      margin-left: 16px;
      display: flex;
      align-items: center;
    }
  }

  .current-method-snapshot {
    padding: 12px 16px;
    border-radius: $radius-l;
    @include font-body-m-regular;
    background: $color-bg-3;

    .model-type--text_below {
      margin-top: 12px;
    }
  }
}

.payment-terms--toss-term-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  @include font-body-m-regular();
  margin-bottom: 24px;

  .term-item--content {
    margin: 0 24px 0 16px;
  }
}

// dbs 弹窗样式
.klk-payment--dbs-phone {
  .dbs-phone-input {
    display: flex;
    align-items: center;
    > span {
      margin-right: 8px;
      flex: 0;
      white-space: nowrap;
    }
  }

  .dbs-phone-error-tips {
    margin-top: 8px;
    color: $color-error;
    text-align: right;
    @include font-caption-m-regular;
  }
}

// 新加坡条款 弹窗样式
.klk-payment--sg-term {
  text-align: left !important;
  b {
    @include font-body-m-bold;
  }

  .sg-term-header {
    padding: 16px;
    background-color: $color-bg-2;
    margin-bottom: 16px;
    border-radius: $radius-m;

    .sg-term-title {
      display: flex;
      align-items: center;
      color: #E52624;

      > img {
        margin-right: 12px;
      }
    }
  }

  .text-secondary {
    margin-top: 8px;
    color: $color-text-secondary;
    @include font-caption-m-regular;

    > b {
      @include font-caption-m-regular;
      color: $color-text-primary;
    }
  }

  .klk-radio {
    margin-right: 0;
    align-items: flex-start !important;
    margin: 16px 0 0;
  }
}

.klk-payment--modal-validate-content {
  display: flex;
  align-items: center;
  margin-top: 16px;

  .klk-input {
    flex: 1;
    margin-right: 12px;
  }
}

