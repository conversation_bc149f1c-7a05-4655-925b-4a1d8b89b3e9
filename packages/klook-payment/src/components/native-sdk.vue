<template>
  <div class="native-sdk-common">
    <!--支付 native sdk 相关-->
    <form id="adyen_pay_form" method="POST">
      <input id="adyen_PaReq" type="hidden" name="PaReq" />
      <input id="adyen_MD" type="hidden" name="MD" />
      <input id="adyen_TermUrl" type="hidden" name="TermUrl" />
      <input id="fingerprint" type="hidden" name="fingerprint" />
    </form>

    <div id="adyen_3ds_challenge">
      <div
        class="loading_box m_radius_box m_bg_white g_v_c_mid g_pa_lr_20"
        style="width: 310px; height: 140px"
      >
        <div class="g_v_c_mid t_mid g_row1">
          <div class="uil-ring-css" style="transform: scale(0.8)">
            <div></div>
          </div>
          <p class="t18 g_pa_lr_20" style="margin-top: 20px">
            {{ $t("global.pay.wait.tip") }}
          </p>
        </div>
      </div>
      <icon-close class="klook-icon-close" theme="outline" size="24" fill="#A8A8A8" />
    </div>

    <div id="initiate3dsSimpleRedirect">
      <div
        class="loading_box m_radius_box m_bg_white g_v_c_mid g_pa_lr_20"
        style="width: 310px; height: 140px"
      >
        <div class="g_v_c_mid t_mid g_row1">
          <div class="uil-ring-css" style="transform: scale(0.8)">
            <div></div>
          </div>
          <p class="t18 g_pa_lr_20" style="margin-top: 20px">
            {{ $t("global.pay.wait.tip") }}
          </p>
        </div>
      </div>
      <form
        id="initiate3dsSimpleRedirectForm"
        name="initiate3dsSimpleRedirectForm"
        method="POST"
        target="initiateFrame"
      ></form>
      <icon-close class="klook-icon-close" theme="outline" size="24" fill="#A8A8A8" />
    </div>

    <div id="authenticate3dsSimpleRedirect">
      <div
        class="loading_box m_radius_box m_bg_white g_v_c_mid g_pa_lr_20"
        style="width: 310px; height: 140px"
      >
        <div class="g_v_c_mid t_mid g_row1">
          <div class="uil-ring-css" style="transform: scale(0.8)">
            <div></div>
          </div>
          <p class="t18 g_pa_lr_20" style="margin-top: 20px">
            {{ $t("global.pay.wait.tip") }}
          </p>
        </div>
      </div>
      <form
        id="authenticate3dsSimpleRedirectForm"
        name="authenticate3dsSimpleRedirectForm"
        method="POST"
        target="authenticateFrame"
      ></form>
      <icon-close class="klook-icon-close" theme="outline" size="24" fill="#A8A8A8" />
    </div>
    <form id="braintreePayForm"></form>

    <form
      id="sybersource_new_card_payment_form"
      :action="cybersourceSubmitUrl"
      method="post"
    >
      <input type="hidden" class="access_key" name="access_key" value="" />
      <input type="hidden" class="profile_id" name="profile_id" value="" />
      <input type="hidden" class="merchant_id" name="merchant_id" value="" />
      <input
        type="hidden"
        class="transaction_uuid"
        name="transaction_uuid"
        value=""
      />
      <input
        type="hidden"
        class="signed_field_names"
        name="signed_field_names"
        value="access_key,profile_id,merchant_id,transaction_uuid,signed_field_names,unsigned_field_names,signed_date_time,locale,transaction_type,reference_number,amount,currency,payment_method,bill_to_forename,bill_to_surname,bill_to_email,bill_to_address_line1,bill_to_address_city,bill_to_address_country,bill_to_address_state,bill_to_address_postal_code,merchant_defined_data5"
      />
      <input
        type="hidden"
        class="unsigned_field_names"
        name="unsigned_field_names"
        value="card_type,card_number,card_expiry_date,card_cvn"
      />
      <input
        type="hidden"
        class="signed_date_time"
        name="signed_date_time"
        value=""
      />
      <input type="hidden" class="signature" name="signature" value="" />
      <input type="hidden" class="locale" name="locale" value="en" />
      <input
        type="hidden"
        class="transaction_type"
        name="transaction_type"
        value=""
      />
      <input
        type="hidden"
        class="reference_number"
        name="reference_number"
        value=""
      />
      <input type="hidden" class="amount" name="amount" value="" />
      <input type="hidden" class="currency" name="currency" value="" />

      <input
        type="hidden"
        class="payment_method"
        name="payment_method"
        value="card"
      />
      <input type="hidden" class="card_type" name="card_type" value="" />
      <input type="hidden" class="card_number" name="card_number" value="" />
      <input
        type="hidden"
        class="card_expiry_date"
        name="card_expiry_date"
        value=""
      />
      <input type="hidden" class="card_cvn" name="card_cvn" value="" />
      <input
        type="hidden"
        class="bill_to_forename"
        name="bill_to_forename"
        value=""
      />
      <input
        type="hidden"
        class="bill_to_surname"
        name="bill_to_surname"
        value=""
      />
      <input
        type="hidden"
        class="bill_to_email"
        name="bill_to_email"
        value=""
      />
      <input
        type="hidden"
        class="merchant_defined_data5"
        name="merchant_defined_data5"
        value="web"
      />

      <!-- 此为假数据 -->
      <input
        type="hidden"
        class="bill_to_address_line1"
        name="bill_to_address_line1"
        value="1295 Charleston Rd"
      />
      <input
        type="hidden"
        class="bill_to_address_city"
        name="bill_to_address_city"
        value="Mountain View"
      />
      <input
        type="hidden"
        class="bill_to_address_country"
        name="bill_to_address_country"
        value="US"
      />
      <input
        type="hidden"
        class="bill_to_address_state"
        name="bill_to_address_state"
        value="CA"
      />
      <input
        type="hidden"
        class="bill_to_address_postal_code"
        name="bill_to_address_postal_code"
        value="94043"
      />
    </form>

    <form
      id="sybersource_old_card_payment_form"
      :action="cybersourceSubmitUrl"
      method="post"
    >
      <input type="hidden" class="access_key" name="access_key" value="" />
      <input type="hidden" class="profile_id" name="profile_id" value="" />
      <input type="hidden" class="merchant_id" name="merchant_id" value="" />
      <input
        type="hidden"
        class="transaction_uuid"
        name="transaction_uuid"
        value=""
      />
      <input
        type="hidden"
        class="signed_field_names"
        name="signed_field_names"
        value="access_key,profile_id,merchant_id,transaction_uuid,signed_field_names,unsigned_field_names,signed_date_time,locale,transaction_type,reference_number,amount,currency,payment_token,bill_to_address_line1,bill_to_address_city,bill_to_address_country,merchant_defined_data5,bill_to_forename,bill_to_surname,bill_to_email"
      />
      <input
        type="hidden"
        class="unsigned_field_names"
        name="unsigned_field_names"
        value=""
      />
      <input
        type="hidden"
        class="signed_date_time"
        name="signed_date_time"
        value=""
      />
      <input type="hidden" class="signature" name="signature" value="" />
      <input type="hidden" class="locale" name="locale" value="en" />
      <input
        type="hidden"
        class="transaction_type"
        name="transaction_type"
        value="authorization"
      />
      <input
        type="hidden"
        class="reference_number"
        name="reference_number"
        value=""
      />
      <input type="hidden" class="amount" name="amount" value="" />
      <input type="hidden" class="currency" name="currency" value="" />
      <input
        type="hidden"
        class="payment_token"
        name="payment_token"
        value=""
      />
      <input
        type="hidden"
        class="bill_to_forename"
        name="bill_to_forename"
        value=""
      />
      <input
        type="hidden"
        class="bill_to_surname"
        name="bill_to_surname"
        value=""
      />
      <input
        type="hidden"
        class="bill_to_email"
        name="bill_to_email"
        value=""
      />
      <input
        type="hidden"
        class="merchant_defined_data5"
        name="merchant_defined_data5"
        value="web"
      />

      <!-- 此为假数据 -->
      <input
        type="hidden"
        class="bill_to_address_line1"
        name="bill_to_address_line1"
        value="1295 Charleston Rd"
      />
      <input
        type="hidden"
        class="bill_to_address_city"
        name="bill_to_address_city"
        value="Mountain View"
      />
      <input
        type="hidden"
        class="bill_to_address_country"
        name="bill_to_address_country"
        value="US"
      />
    </form>
  </div>
</template>

<script>
// @ts-ignore
import IconClose from '@klook/klook-icons/lib/IconClose'
import { isPrdEnv } from '../payment-sdk/core/utils'

export default {
  components: { IconClose },
  data() {
    return {
      scripts: [
        // 包含window的braintree, aftee变量
        'https://js.braintreegateway.com/v2/braintree.js',
        'https://js.braintreegateway.com/js/braintree-2.27.0.min.js',
        'https://js.braintreegateway.com/v1/braintree-data.js'
      ],
      cybersourceSubmitUrl: ''
    }
  },
  mounted() {
    if (isPrdEnv) {
      this.scripts.push('https://auth.aftee.tw/v1/aftee.js')
      this.cybersourceSubmitUrl =
        'https://secureacceptance.cybersource.com/silent/pay'
    } else {
      this.scripts.push('https://ct-auth.np-pay.com/v1/aftee.js')
      this.cybersourceSubmitUrl =
        'https://testsecureacceptance.cybersource.com/silent/pay'
    }
    this.initBrainTree()
    window.iso_time = new Date().toISOString()
    this.loadScripts()
  },
  methods: {
    loadScripts() {
      Promise.all(
        this.scripts.map((src) => {
          return new Promise((resolve) => {
            const newScript = document.createElement('script')
            const uniqueID = 'SCRIPT_' + new Date().getTime()
            newScript.setAttribute('src', src)
            newScript.setAttribute('type', 'text/javascript')
            newScript.setAttribute('id', uniqueID)
            document.head.appendChild(newScript)
            resolve(newScript)
          })
        })
      )
    },
    initBrainTree() {
      const braintree_environment = isPrdEnv ? 'production' : 'sandbox'
      const braintree_braintreeMerchantId = isPrdEnv
        ? '48wz35xtr96nfh7c'
        : 't5ktznnyybdpr7vd'
      window.onBraintreeDataLoad = () => {
        const env = window.BraintreeData.environments[braintree_environment].withId('601090')
        window.BraintreeData.setup(
          braintree_braintreeMerchantId,
          'braintreePayForm',
          env
        )
      }
    }
  }
}
</script>

<style lang="scss">
#adyen_3ds_challenge,
#initiate3dsSimpleRedirect,
#authenticate3dsSimpleRedirect {
  position: fixed;
  background: $color-overlay-default-3;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  display: none;
  z-index: 4000;
  background: #fff;

  .g_v_c_mid, .loading_box  {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) !important;
  }

  .loading_box {
    background-color: $color-bg-widget-normal;
    border-radius: $radius-s;
    text-align: center;
  }

  .i-icon-icon-close {
    position: absolute;
    z-index: 4;
    top: 24px;
    right: 24px;
    cursor: pointer;
  }

  iframe {
    position: absolute;
    z-index: 2;
  }
}

.uil-ring-css > div {
  margin-right: auto;
  margin-left: auto;
  font-size: 10px;
  position: relative;
  text-indent: -9999em;
  border-top: 3px solid #E0E0E0;
  border-right: 3px solid #E0E0E0;
  border-bottom: 3px solid #E0E0E0;
  border-left: 3px solid #ff5722;
  -webkit-animation: animatename 1.1s infinite linear;
  animation: animatename 1.1s infinite linear;
}

.uil-ring-css > div,
.uil-ring-css > div:after {
  border-radius: 50%;
  width: 30px;
  height: 30px;
}

@-webkit-keyframes animatename {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
  }
  @keyframes animatename {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
</style>
