/* eslint-disable @typescript-eslint/no-unused-vars */
import type { FunctionalComponentOptions, CreateElement } from 'vue'
import { DialogTips } from '../modules'
import type { ModelTypeAttrs } from '../../../types.d'
import ThInstallmentTips from './th-installment-tips.vue'

/***************************************************************************
{
  "type": "warning",
  "action":"dialog/bottom_sheet",
  // 2024-8-22 新增 Th 卡分期 bottom_sheet
  "bottom_sheet": {
      "link": "https://www.klook.com" // 仅APP使用 后期提供
      "title": "",
      "credit_card_title": "",
      "items": [
        {
            "icon": "xxx",
            "content": "**********",
        }
    ]
  },
  "dialog": {
      "title": "小 i 的内容",
      "message": "小 i 的内容",
      "positive": "ok",
      "negative": "cancel",
      "items": [{
          "icon": "",
          "title": "",
          "content": ""
      }]
  }
}
*/

// 2024-12-22 合并结算页支持th分期，二次包装DialogTips
const warning: FunctionalComponentOptions<ModelTypeAttrs> = {
  functional: true,
  // @ts-ignore
  render(_h, {
    props,
    parent: ctx
  }
  ) {
    const { action, bottom_sheet, method } = props
    const { method_key: PaymentMethod = 'NA' } = method || {}
    // @ts-ignore
    return <DialogTips
      attrs={
        action === 'bottom_sheet' && bottom_sheet?.title
          ? {
            botoomSheet: true,
            ihtAttrs: {
              'data-spm-module': `PaymentMethodTips?ext=${JSON.stringify({ PaymentMethod })}`,
              'data-spm-virtual-item': '__virtual'
            },
            dialog: {
              title: bottom_sheet.title,
              // @ts-ignore
              message: (h: CreateElement = ctx.$createElement) => <ThInstallmentTips
                paymentMethod={ PaymentMethod } // 用来上报埋点
                creditCardTitle={ bottom_sheet.credit_card_title }
                creditCardList={ bottom_sheet.items }
              />
            },
            options: {
              maxWidth: 680,
              maxHeight: 600
            }
          }
          : props
      }
    />
  }
}

export default warning
