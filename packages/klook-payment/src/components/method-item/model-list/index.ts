import type { Method } from '../../../types.d'
import text_below from './text_below'
import warning from './warning'
import text_bubble from './text_bubble'
import text_below_stroke from './text_below_stroke'
import payment_coupon from './payment_coupon'
import banner from './banner'
import input from './input'
import saved from './saved'
import { deleted, delete_old } from './delete'

const modeTypesMap = {
  banner,
  warning,
  text_below,
  text_bubble,
  text_below_stroke,
  payment_coupon,
  input,
  saved,
  deleted,
  delete_old
}

export default function getModelVnodeMap(
  this: Vue,
  method: Method,
  isSelected: boolean,
  filterModeTypeList?: string[]
) {
  const h = this.$createElement
  let modelList = method.model_list || []

  if (filterModeTypeList?.length && modelList.length) {
    modelList = modelList.filter(({ type }) => filterModeTypeList.includes(type))
  }

  return modelList.reduce((res: Record<string, JSX.Element>, modeOption) => {
    if (modeOption?.type) {
      const ModelType = (modeTypesMap as any)[modeOption?.type]

      if (ModelType) {
        res[modeOption.type] = h(ModelType, { attrs: { ...modeOption, method, isSelected } })
      }
    }
    return res
  }, {})
}

export {
  banner,
  warning,
  text_below,
  text_bubble,
  text_below_stroke,
  payment_coupon,
  input,
  saved,
  deleted,
  delete_old
}
