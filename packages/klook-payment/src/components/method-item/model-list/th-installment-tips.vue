<template>
  <div
    class="payment-type-tips"
    :class="ISMOBILE ? 'tips-mobile' : 'tips-desktop'"
    :data-spm-module="`PaymentMethodTipsDetail?ext=${JSON.stringify({PaymentMethod: paymentMethod})}`"
  >
    <ul class="tips-list">
      <li v-for="(item, index) in tipsConfig.tips" :key="index" class="tips-list-item">
        {{ item }}
      </li>
    </ul>
    <div class="bank-list">
      <div class="list-title">{{ $t('180109') }}</div>
      <div class="list-content">
        <div v-for="(item, index) in tipsConfig.banks" :key="index" class="list-item">
          <icon-check-circle size="16" class="success-icon" />
          {{ item }}
        </div>
      </div>
    </div>
    <div v-if="creditCardList.length" class="credit-list">
      <div class="list-title">{{ creditCardTitle }}</div>
      <div class="list-content">
        <div v-for="(item, index) in creditCardList" :key="index" class="list-item">
          <div v-if="item.icon" class="icon-bg" />
          <icon-check-circle size="16" class="success-icon-green" />
          {{ item.content }}
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop, Inject } from 'vue-property-decorator'
// @ts-ignore
import IconCheckCircle from '@klook/klook-icons/lib/IconCheckCircle'

@Component({
  inheritAttrs: false,
  components: { IconCheckCircle }
})
export default class ThInstallmentTips extends Vue {
  @Inject() isMobile!: () => boolean
  @Prop({ type: String, default: '' }) paymentMethod!: string
  @Prop({ type: String, default: '' }) creditCardTitle!: string
  @Prop({ type: Array, default: () => [] }) creditCardList!: any[]

  get tipsConfig() {
    return {
      tips: [
        this.$t('180106'),
        this.$t('180107'),
        this.$t('180108')
      ],
      banks: [
        this.$t('180111'),
        this.$t('180114'),
        this.$t('180115'),
        this.$t('180116'),
        this.$t('180113'),
        this.$t('180117'),
        this.$t('180118')
      ]
    }
  }

  get ISMOBILE() {
    return this.isMobile()
  }
}
</script>

<style lang="scss" scoped>
.payment-type-tips {
  color: $color-text-primary;

  .tips-list {
    @include font-body-s-regular();
  }

  .tips-list-item {
    margin-bottom: 8px;
    margin-left: 14px;
    list-style-type: disc;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .bank-list, .credit-list {
    margin-top: 24px;
    margin-bottom: -8px;
  }

  .list-title {
    @include font-body-m-bold();

    margin-bottom: 8px;
  }

  .list-content {
    @include font-body-s-regular();

    display: flex;
    flex-wrap: wrap;
  }

  .list-item {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
  }

  .success-icon {
    flex-shrink: 0;
    margin-right: 8px;
    color: $color-text-primary;
  }

  .icon-bg {
    flex-shrink: 0;
    width: 16px;
    height: 16px;
    margin-right: 8px;
    background-size: content;
    background-position: center;
    background-repeat: no-repeat;
  }

  .success-icon-green {
    flex-shrink: 0;
    margin-right: 8px;
    color: #16AA77;
  }
}

.tips-desktop {
  min-width: 480px;

  .list-item {
    width: 50%;
  }
}

.tips-mobile {
  .list-item {
    width: 100%;
  }
}
</style>
