import type { FunctionalComponentOptions } from 'vue'
// @ts-ignore
import Checkbox from '@klook/klook-ui/lib/checkbox'
import type { ModelTypeAttrs } from '../../../types.d'
import type MethodItem from '../'

/***************************************************************************
 只有提交的时候才需要用到， 所以先保存到根组件
{
  "type": "saved",
  "title": "Save for future purchases",
  "content": "No re-verification required"
}
*/

export const saved: FunctionalComponentOptions<ModelTypeAttrs> = {
  functional: true,
  inject: ['getMethodForm'],
  render(_h, {
    props: { isSelected, display_model, title, content, method },
    injections: { getMethodForm },
    parent
  }
  ) {
    const formModel = getMethodForm(method.method_key, { save_token: false })
    const ctx = parent as MethodItem
    return <div
      vShow={isSelected || display_model === 'always'}
      class={['model-type--saved', { 'is-mobile': ctx.ISMOBILE }]}
    >
      <Checkbox
        value={formModel.save_token}
        onChange={(save_token: boolean) => {
          formModel.save_token = save_token
          // 没有选中该支付方式时，修改了 save_token 则选中该支付方式提升用户体验
          !isSelected && ctx.commitSettlement()
        }}
      >
        {title}
      </Checkbox>
      <p class="text-secondary caption-12" style="margin-left: 32px;">
        {content}
      </p>
    </div>
  }
}

export default saved
