import type { FunctionalComponentOptions } from 'vue'
// @ts-ignore
import { $colorCaution } from '@klook/klook-ui/es/utils/design-token-esm'
import type { ModelTypeAttrs } from '../../../types.d'
import { DialogTips } from '../modules'

/* {
  "type": "text_below",
  "content": "xxxx",
  "multi_lines": "true",
  "dialog": {
      "title": "小 i 的内容",
      "message": "小 i 的内容",
      "positive": "ok"
  },
  // 2024-12-22 新增对 content 内容设置样式结构
  "items": [ // routeItems
        {
            "type": "", 
            "style": "default/orange",
            "text": "曼谷银行",
            "text_position_name": "bangkok_bank",
        }
    ]
}
*/

const text_below: FunctionalComponentOptions<ModelTypeAttrs> = {
  functional: true,
  // @ts-ignore
  render(_h, { props: { display_model, content, multi_lines, dialog, items, isSelected } = {} }) {
    return content && <div vShow={isSelected || display_model === 'always'} class="model-type--text_below">
      <p
        class={{ 'multi-lines': multi_lines }}
        domPropsInnerHTML={
          (items || []).reduce((res, item) => res.replace(
            new RegExp(`{${item.text_position_name}}`, 'g'),
            item.style === 'orange'
              ? `<span style="color: ${$colorCaution}">${item.text}</span>`
              : item.text
          ), content)
        }
      />
      {/* @ts-ignore */}
      <DialogTips dialog={dialog} />
    </div>
  }
}

export default text_below
