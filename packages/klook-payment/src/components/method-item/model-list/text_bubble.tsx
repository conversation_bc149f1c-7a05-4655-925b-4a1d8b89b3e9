import type { FunctionalComponentOptions } from 'vue'
import type { ModelTypeAttrs } from '../../../types.d'
import type MethodItem from '../'
import { DialogTips } from '../modules'
import TextBubbleDialogContent from './text-bubble-dialog-content.vue'

/***************************************************************************
{
        "type": "text_bubble",
        "content": "Credit Card Payment Offer",
        "dialog": {
            "title": "小 i 的内容",
            "message": "小 i 的内容",
            "positive": "ok",
            "negative": "cancel",
            "items": [{
                "icon": "",
                "title": "",
                "content": ""
            }]
        }
    }
*/

const text_bubble: FunctionalComponentOptions<ModelTypeAttrs> = {
  functional: true,
  render(_h, { props: { display_model, content, dialog, isSelected } = {}, parent: ctx }) {
    if (!content) {
      return null as any
    }
    const { ISMOBILE } = ctx as MethodItem
    const dialogItems = dialog?.items?.filter(({ title, content } = {} as Record<string, string>) => title || content)

    return <div vShow={isSelected || display_model === 'always'} class="model-type--text_bubble">
      {content}
      {
        // @ts-ignore
        dialogItems?.length > 0 && <DialogTips
          options={{
            maxHeight: 300,
            scrollable: true
          }}
          dialog={{
            title: dialog!.title,
            // @ts-ignore
            // eslint-disable-next-line @typescript-eslint/no-unused-vars
            message: (h: CreateElement) => <TextBubbleDialogContent
              dialogItems={dialogItems}
              isMobile={ISMOBILE}
            />
          }}
        />
      }
    </div>
  }
}

export default text_bubble
