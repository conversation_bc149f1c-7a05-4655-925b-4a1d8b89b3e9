import type { FunctionalComponentOptions } from 'vue'
// @ts-ignore
import Link from '@klook/klook-ui/lib/link'
import type { ModelTypeAttrs, ModeItem } from '../../../types.d'
import type MethodItem from '../'
import { cashierDelete } from '../../../assets/apis'
import { DialogTips } from '../modules'
import { paymentLogQuery } from '../../../payment-sdk/core/utils/logQuery'

/***************************************************************************
 删除toekn, 和上面保存toekn 互斥
{
  "type": "deleted",
  "title": "Account on file",
  "content": "Delete",
  "icon": "https://res.klook.com/image",
  "dialog": {
      "title": "小 i 的内容",
      "message": "",
      "positive": "ok",
      "negative": "cancel"
  },
  "delete": {"confirm_dialog": {
      "title": "二次确认弹层",
      "message": "",
      "positive": "ok",
      "negative": "cancel"
  }}
}
*/

const Delete: FunctionalComponentOptions<{
  btnText: string
  token: string
  dialog: ModeItem['dialog']
}> = {
  functional: true,
  // @ts-ignore
  render(_h, { props: { btnText, token, dialog }, parent }) {
    return <Link underline={false} onClick={async () => {
      const ctx = parent as MethodItem
      if (
        dialog && !(await ctx.$confirm(
          dialog.message!,
          dialog.title,
          {
            okLabel: dialog.positive,
            cancelLabel: dialog.negative
          })).result
      ) { return }

      ctx.$showLoading({ overlayColor: 'rgba(255, 255, 255, 0.6)' })
      try {
        const { success, result, error } = await ctx.$axios.$post(cashierDelete, { token })
        if (success) {
          result.success_message && ctx.$toast(result.success_message)
        } else {
          throw error
        }
      // @ts-ignore
      } catch (err: any) {
        err?.message && ctx.$toast(err.message)
        paymentLogQuery({
          action: 2,
          api_name: 'del',
          file_name: '/components/method-item/model-list/delete.tsx',
          req_data: token,
          message: `错误信息：model_list 解除绑定出错。${JSON.stringify(err)}`
        })
      }
      ctx.$hideLoading()

      ctx.commitSettlement(undefined, true)
    }}>
      { btnText }
    </Link>
  }
}

export const delete_old: FunctionalComponentOptions<ModelTypeAttrs> = {
  functional: true,
  // @ts-ignore
  render(_h, { props: { isSelected, display_model, content, dialog, method: { token } = {} } }) {
    return token && <div class="model-type--delete_old">
      {/* @ts-ignore */}
      <Delete
        vShow={isSelected || display_model === 'always'}
        btnText={content}
        dialog={dialog}
        token={ token }
      />
    </div>
  }
}

export const deleted: FunctionalComponentOptions<ModelTypeAttrs> = {
  functional: true,
  // @ts-ignore
  render(_h, {
    props: { isSelected, display_model, icon, title, content, dialog, delete: { confirm_dialog = {} } = {}, method: { token } = {} },
    parent
  }
  ) {
    const ctx = parent as MethodItem
    return token && <div vShow={isSelected || display_model === 'always'} class={['model-type--deleted', { 'is-mobile': ctx.ISMOBILE }]}>
      <div class="model-type--deleted-main" style={!icon && 'margin-right: 16px;'}>
        {icon && <img src={icon} width="20" height="20px" style="vertical-align: middle;margin-right: 12px; " />}
        <span>{title}</span>
        {/* @ts-ignore */}
        <DialogTips dialog={dialog} />
      </div>
      {/* @ts-ignore */}
      <Delete
        vShow={isSelected || display_model === 'always'}
        style={icon && 'margin-left: 32px;'}
        btnText={content}
        dialog={confirm_dialog}
        token={ token }
      />
    </div>
  }
}
