<template functional>
  <ul class="text-bubble-dialog-list" :class="{ 'is-mobile': props.isMobile }">
    <li
      v-for="(item, index) in props.dialogItems"
      :key="item.title + index"
      class="text-bubble-dialog-item"
    >
      <img v-if="item.icon" width="32" :src="item.icon" />
      <div class="item-detail">
        <p class="title">{{ item.title }}</p>
        <p class="content">{{ item.content }}</p>
      </div>
    </li>
  </ul>
</template>
<style lang="scss" scoped>

.text-bubble-dialog-list {
  .text-bubble-dialog-item {
    display: flex;
    padding: 12px 0;
    align-items: flex-start;

    &:not(:last-child) {
      box-shadow: inset 0 -1px 0 #eee;
      box-shadow: $shadow-none;
    }

    > img {
      margin-right: 12px;
    }

    .item-detail {
      .title {
        font-size: $fontSize-body-s;
        line-height: 20px;
        color: $color-text-primary;
      }
      .content {
        font-size: $fontSize-caption-m;
        line-height: 16px;
        color: $color-brand-primary;
      }
    }
  }
}
</style>
