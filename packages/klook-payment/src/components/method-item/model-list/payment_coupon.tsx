import type { FunctionalComponentOptions } from 'vue'
import { KlkAtomicBasicTag } from '@klook/klook-card'
import type MethodItem from '../'
import type { ModelTypeAttrs } from '../../../types.d'
import type { MobileMethodList } from '../../method-list'

/***************************************************************************
 * paylater 项目后端新增  支付方式推荐的支付优惠券
{
  "type": "payment_coupon",
  "content": "-￥5"
}
*/
export const CouponTag: FunctionalComponentOptions<{
  content: string,
  grayscale: boolean,
}> = {
  functional: true,
  render(_h, { props: { content: text, grayscale = false } = {}, listeners }) {
    if (!text) {
      return null as any
    }

    const tagOptions: Record<string, any> = {
      style: grayscale ? 'filter: grayscale(100%)' : '',
      attrs: {
        size: 'small',
        data: { text, type: 'platform_promo_code_tag' }
      }
    }

    if (listeners?.click) {
      Object.assign(tagOptions.attrs, {
        'data-spm-module': 'PaymentMethod_PromoTag',
        'data-spm-virtual-item': '__virtual'
      })
      tagOptions.style += ';cursor:pointer;'
      // 只有MethodItem 的children 才支持点击事件
      tagOptions.nativeOn = { click: listeners.click }
      tagOptions.attrs.data.text += `<svg style="margin-top: 4px;vertical-align: top" width="12" height="12" viewBox="0 0 48 48" fill="none">
        <path d="M17 8L35 24L26 32L17 40" stroke="currentColor" stroke-width="3.6" stroke-linecap="round" stroke-linejoin="round" />
      </svg>`
    } else {
      tagOptions.attrs['data-spm-module'] = 'PromoTag'
    }

    return <KlkAtomicBasicTag { ...tagOptions } />
  }
}

const payment_coupon: FunctionalComponentOptions<ModelTypeAttrs> = {
  functional: true,
  inject: ['getCurrentCouponCode'],
  // @ts-ignore
  render(_h, { props: { content, isSelected, method: { payment_coupon_code, method_key } = {} }, parent, injections: { getCurrentCouponCode } }) {
    if (!content) {
      return
    }

    // 使用这个组件的Vue实例 除了MethodItem 还有 MobileMethodList, 但是MobileMethodList没有点击交互
    const ctx = parent as MethodItem | MobileMethodList

    const { originalCurrentMethodKey } = ctx as MethodItem

    // 有优惠券不用的标签是 只有真实选中的支付方式才会判断， 浮层临时选中的需要排除

    const isRealSelected = originalCurrentMethodKey
      ? originalCurrentMethodKey === method_key
      : isSelected

    if (isRealSelected) {
      payment_coupon_code = getCurrentCouponCode?.() || '' // @main.vue  @Provide() getCurrentCouponCode
    }

    // @ts-ignore
    return <CouponTag
      grayscale={!payment_coupon_code}
      content={ !payment_coupon_code ? ctx.$t('173925') : content }
      on={
        ctx.$options.name === 'method-item'
          ? {
            click: (e: Event) => {
              (ctx as MethodItem).handlerPaymentCoupon(payment_coupon_code)
              return e.stopPropagation()
            }
          }
          : undefined
      }
    />
  }
}

export default payment_coupon
