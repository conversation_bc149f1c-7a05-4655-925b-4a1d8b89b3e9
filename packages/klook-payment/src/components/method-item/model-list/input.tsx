import type { FunctionalComponentOptions } from 'vue'
import type { ModelTypeAttrs } from '../../../types.d'
import CvvInput from './cvv-input.vue'

/***************************************************************************
{
  "type": "input",
  "title": "请输入卡号/请输入 CVV",
  "input": {
      "validate_type": "card_number/card_cvv",
      "hint": "未输入状态的提示"
  }
}
*/

export const input: FunctionalComponentOptions<ModelTypeAttrs> = {
  functional: true,
  // @ts-ignore
  render(_h, { props: { isSelected, display_model, input, method: { method_key, card_type } = {} } = {} }) {
    return method_key && input?.validate_type === 'card_cvv' && <CvvInput
      vShow={isSelected || display_model === 'always'}
      ref="inputForm"
      placeholder={input.hint}
      methodKey={method_key}
      cardType={ card_type }
    />
  }
}

export default input
