<template>
  <klk-form
    ref="cvvForm"
    :model="formModel"
    :rules="rules"
  >
    <klk-form-item prop="cvv" :style="!ISMOBILE && 'width:320px'">
      <klk-input
        v-model="formModel.cvv"
        :placeholder="placeholder"
        :maxlength="ruleSize"
        :minlength="ruleSize"
        type="tel"
        class="model-type--input"
        oninput="this.tagName === 'INPUT' && (this.value = this.value.replace(/\D/g, ''))"
      />
    </klk-form-item>
  </klk-form>
</template>

<script lang="ts">
import { Vue, Component, Inject, Prop } from 'vue-property-decorator'
// @ts-ignore
import KlkInput from '@klook/klook-ui/lib/input'
// @ts-ignore
import KlkForm, { FormItem as KlkFormItem } from '@klook/klook-ui/lib/form'

@Component({
  components: {
    KlkInput,
    KlkForm,
    KlkFormItem
  }
})
export default class CvvForm extends Vue {
  @Inject() getMethodForm!: (methodKey: string, model: object, verify: Function) => { cvv: string }
  @Inject() isMobile!: () => boolean
  @Inject() creditcardOptions!: { cardTypeRules: any[] }
  @Prop({ type: String }) methodKey!: string
  @Prop({ type: String }) placeholder!: string
  @Prop({ type: String }) cardType!: string

  get ISMOBILE() {
    return this.isMobile()
  }

  formModel = this.getMethodForm(
    this.methodKey,
    { cvv: null },
    () => {
      const form = this.$refs.cvvForm as Vue & { validate: () => Promise<any> }
      return form?.validate().catch(() => {
        const input = form.$el.querySelector('.model-type--input input[type="tel"]') as HTMLInputElement

        if (input) {
          this.ISMOBILE // mobile 需要等待打开浮层
            ? setTimeout(() => { input.focus() }, 400)
            : input.focus()
        }
        return Promise.reject(new Error('INPUT'))
      })
    }
  )

  get ruleSize() {
    return (
      this.cardType &&
      this.creditcardOptions?.cardTypeRules?.length &&
      this.creditcardOptions?.cardTypeRules.find((rule) => {
        return rule.type === this.cardType
      })?.code?.size
    ) || 3
  }

  get rules() {
    return {
      cvv: [
        {
          required: true,
          trigger: 'blur',
          validator: (_: any, value: string, callback: Function) => {
            if (!value) {
              callback(new Error(this.$t('global.error.cant_be_empty')))
              return
            }

            if (value.length !== this.ruleSize) {
              callback(new Error(this.$t('global.payment.cvv.need_be_number')))
              return
            }

            callback()
          }
        }
      ]
    }
  }
}
</script>
