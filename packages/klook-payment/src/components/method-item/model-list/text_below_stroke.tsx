import type { FunctionalComponentOptions } from 'vue'
import { KlkAtomicBasicTag } from '@klook/klook-card'
// @ts-ignore
import { $colorSuccessBackground, $colorSuccess, $colorBrandPrimaryBackground, $colorBrandPrimary } from '@klook/klook-ui/es/utils/design-token-esm'

import type { ModelTypeAttrs } from '../../../types.d'

/***************************************************************************
{
  "type": "text_below_stroke",
  "items": [
      {
          "type": "",
          "style": "default",
          "text": "立减¥68.8",
          "link": ""
      }
  ]
}
*/

const tagThemes = {
  green: {
    bg_color: $colorSuccessBackground,
    line_color: $colorSuccess,
    text_color: $colorSuccess
  },
  default: {
    bg_color: $colorBrandPrimaryBackground,
    line_color: $colorBrandPrimary,
    text_color: $colorBrandPrimary
  }
}

type TagItem = { style?: 'default' | 'green', text?: string }

const text_below_stroke: FunctionalComponentOptions<ModelTypeAttrs> = {
  functional: true,
  // @ts-ignore
  render(_h, { props: { items, display_model, isSelected } = {} }) {
    return items && items[0] && <div
      vShow={isSelected || display_model === 'always'}
      class="model-type--text_below_stroke"
    >
      {
        items.map(({ text, style }: TagItem = {}) => text && <KlkAtomicBasicTag
          data={{ text, ...(tagThemes[style as 'default'] || tagThemes.default) }}
        />)
      }
    </div>
  }
}

export default text_below_stroke
