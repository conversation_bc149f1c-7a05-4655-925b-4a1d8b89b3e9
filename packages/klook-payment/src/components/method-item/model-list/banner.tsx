import type { FunctionalComponentOptions } from 'vue'
import type { ModelTypeAttrs } from '../../../types.d'
import type MethodItem from '../'
import showAfteeTerms from '../../aftee-terms'

/***************************************************************************
{
 "type": "banner",
  "banner": {
      "type": "info/link",
      "image_url": "banner 图片 url",
      "url": "点击图片，浮层加载的 url"
  }
}
*/

const banner: FunctionalComponentOptions<ModelTypeAttrs> = {
  functional: true,
  // @ts-ignore
  render(_h, { props: { isSelected, display_model, banner } = {}, parent }) {
    const ctx = parent as MethodItem
    return banner && <img
      vShow={isSelected || display_model === 'always'}
      class="model-type--banner"
      data-spm-module= "PaymentMethod_PaymentBanner"
      data-spm-virtual-item={
        banner.type !== 'link'
          ? undefined
          : '__virtual'
      }
      width="100%"
      style={banner.type === 'link' && 'cursor:pointer'}
      src={banner.image_url}
      on={
        banner.type !== 'link'
          ? undefined
          : {
            click: showAfteeTerms.bind(ctx, ctx.ISMOBILE)
          }
      }
    />
  }
}

export default banner
