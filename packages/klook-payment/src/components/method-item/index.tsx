import { Vue, Component, Prop, Inject } from 'vue-property-decorator'
// @ts-ignore
import { Dialog } from '@klook/klook-ui/lib/modal'
import type { Method, CouponItem, SettlementParams } from '../../types.d'
import ParamsManager, { Creditcard, CreditcardNew } from '../../utils/params-manager'
import type KlkPayment from '../../main'
import type { MobileMethodList } from '../method-list'
import { paymentLogQuery } from '../../payment-sdk/core/utils/logQuery'
import specialMethodContentMap from './special-method-content'
import { ToggleViewAll, RadioWrapper, MethodLabel } from './modules'
import getModelVnodeMap from './model-list'

// css部分 在 './components/radio-wrapper.vue'

// 在没有选中的时候需要缓存的支付方式，原因是这些支付方式和其子支付方式都包含有有用户输入的交互
const NeedCachePaymentTypeList = ['unionpay', Creditcard]

@Component({
  inheritAttrs: false,
  name: 'method-item'
})
export default class MethodItem extends Vue {
  @Inject() isMobile!: () => boolean
  @Inject() updateSettlement!: MobileMethodList['updateSettlement']
  @Inject() getKlkCouponOptions!: KlkPayment['getKlkCouponOptions']
  @Inject() getMethodForm!: (methodKey: string) => ParamsManager['CreditcardNew']
  @Prop({ type: Object, default: null }) method!: Method
  @Prop({ type: String }) currentMethodKey?: string // 当前选中 method_key   在mweb 中可能是临时选中的method_key
  @Prop({ type: String }) originalCurrentMethodKey!: string
  @Prop({ type: Boolean }) isSub!: boolean
  @Prop({ type: Object, default: () => ({}) }) ihtProps!: { idx: number, len: number }

  get modelVnodeMap() {
    return getModelVnodeMap.call(this, this.method, this.isSelected)
  }

  async handlerPaymentCoupon(coupon_code?: string) {
    const KlkOrderCoupon = 'klk-order-coupon'
    const { lang, redeemBasicCoupon, getBasicCouponList, ...props } = this.getKlkCouponOptions()

    if (!Vue.component(KlkOrderCoupon)) {
      // @ts-ignore
      Vue.use((await import('@klook/klk-order-coupon')).default, lang)
    }

    const { result } = await Dialog.call(this, {
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      content: (h: Vue.CreateElement, handlerClose: Function) => <KlkOrderCoupon
        attrs={{
          disabled: false,
          pageType: 'cashRegister',
          isMergedPaymentPage: true,
          visibleBestCouponSuggestNotice: false,
          couponInfo: coupon_code
            ? {
              coupon_discount: '',
              details: [{
                code: coupon_code,
                discount: '',
                finance_type: 'payment_coupon'
              }]
            }
            : undefined,
          ...props,
          redeemBasicCoupon: (params: any) => {
            try {
              return redeemBasicCoupon(params)
            } catch (error) {
              paymentLogQuery({
                action: 2,
                api_name: 'CouponRedeem',
                file_name: '/components/method-item/index.tsx',
                req_data: params,
                message: `错误信息：${JSON.stringify(error || '')}`
              })
            }
          },
          getBasicCouponList: () => {
            const selected_payment_method = { method_key: this.method.method_key }
            if (this.method.method_key === CreditcardNew) { // 新增结构
              // getMethodForm('CreditcardNew') => ParamsManager['CreditcardNew'] 是非空的
              const { credit_card_info } = this.getMethodForm('CreditcardNew')

              if (credit_card_info) {
                const { bin: card_bin, issuer_bin: card_issuer_bin } = credit_card_info
                Object.assign(selected_payment_method, credit_card_info && { card_bin, card_issuer_bin })
              }
            }

            const params = {
              custom_info: {
                // best_coupon_strategy: 1, // 最佳优惠券策略, 0: 返回推荐列表, 1: 只返回一个最佳组合
                finance_type: 'payment_coupon',
                selected_payment_method
              }
            }

            try {
              return getBasicCouponList(params)
            } catch (error) {
              paymentLogQuery({
                action: 2,
                api_name: 'CouponList',
                file_name: '/components/method-item/index.tsx',
                req_data: params,
                message: `错误信息：${JSON.stringify(error || '')}`
              })
            }
          }
        }}
        onMobileClickBack={ () => {
          handlerClose(false)
        } }
        onConfirmUse={() => {
          handlerClose(true)
        }}
        onChangeCoupon={(couponItem?: CouponItem) => {
          coupon_code = couponItem?.code
        } }
      />,
      ...(
        this.ISMOBILE
          ? { transition: 'slide-bottom', fullscreen: true, lockScroll: true }
          : { lockScroll: true, width: '55%', showCancelButton: false, showDefaultFooter: true, title: this.$t('30125'), okLabel: this.$t('73205') }
      )
    })

    if (result) {
      this.commitSettlement({ coupon_code, auto_select_coupon: false }, true)
    } else if (!this.isSelected) {
      this.commitSettlement()
    }
  }

  get isSelected() {
    return Boolean(this.currentMethodKey && (!this.isSub || this.currentMethodKey === this.method.method_key))
  }

  get ISMOBILE() {
    return this.isMobile()
  }

  commitSettlement(params?: SettlementParams, rightnowCommit?: boolean) {
    this.updateSettlement({
      auto_select_coupon: true,
      method_key: this.method.method_key,
      ...params
    }, rightnowCommit)
  }

  render() {
    const { method_key, action, sub_options } = this.method || {}
    let children!: void | false | JSX.Element | JSX.Element[]

    // 没有选中和不支持缓存的子支付方式暂时不渲染  减少dom树复杂度
    if (this.isSelected || NeedCachePaymentTypeList.some(key => method_key.includes(key))) {
      const scopedSlotOptios = {
        attrs: { isSelected: this.isSelected, method: this.method },
        on: { 'commit-settlement': this.commitSettlement }
      }
      children = this.$scopedSlots.default?.(scopedSlotOptios)
      // 数据现在只支持到两层结构，暂时不支持无限的递归下去
      if (!children && !this.isSub) {
        const subMethodViewMoreDisabled = (this.ihtProps.len || 0) < 2
        const MatchedMethodContent = specialMethodContentMap[method_key]
        children = MatchedMethodContent // @ts-ignore 一级特殊支付方式的内容区， 现阶段只有银联 unionpay
          ? <MatchedMethodContent { ...scopedSlotOptios } />
          // 有sub_options的一级非特殊支付方式的内容区， 递归生成二级支付方式
          : action !== 'select' && sub_options?.length > 0 && <ToggleViewAll
            disabled={subMethodViewMoreDisabled || sub_options.length < 5}
            class="sub-method-list"
            ihtAttrs={{
              'data-spm-module': 'PaymentMethod_SeeAllSubMethod',
              'data-spm-virtual-item': '__virtual'
            }}
          >
            {
              // 填充ToggleViewAll 的 $scopedSlots.default
              () => sub_options.map((subMethod, idx) => {
                //  二级特殊支付方式的内容区, 现阶段有creditcard-new 和 tosslocal
                const MatchedSubMethodContent = specialMethodContentMap[subMethod.method_key] ||
                  specialMethodContentMap[method_key + '/sub']
                return <MethodItem
                  isSub
                  key={subMethod.method_key}
                  data-method-type={ subMethod.method_key }
                  method={subMethod}
                  ihtProps={{ idx, len: sub_options.length }}
                  currentMethodKey={this.currentMethodKey}
                  originalCurrentMethodKey={ this.originalCurrentMethodKey }
                >
                  {
                    // 填充SubMethodItem 的 $scopedSlots.default
                    MatchedSubMethodContent && (
                      // @ts-ignore 这里的options 是递归后重新生成的 scopedSlotOptios
                      (options: typeof scopedSlotOptios) => <MatchedSubMethodContent { ...options } />
                    )
                  }
                </MethodItem>
              })
            }
          </ToggleViewAll>
      }
    }

    return <RadioWrapper
      isSelected={this.isSelected}
      class={`${this.isSub ? 'sub-' : ''}method-item`}
      ihtAttrs={{
        'data-method-type': this.method.method_key,
        'data-spm-virtual-item': '__virtual',
        'data-spm-module': `Payment${this.isSub ? 'Sub' : ''}Method_LIST?ext=${encodeURIComponent(JSON.stringify({ PaymentMethod: this.method.method_key }))}&idx=${this.ihtProps.idx}&len=${this.ihtProps.len}`
      }}
      onSelect={this.commitSettlement}
    >
      {/* @ts-ignore */}
      <MethodLabel slot="label" method={this.method}>
        <template slot="left">
          {this.$slots['label-left']}
        </template>
        <template slot="right">
          { this.$slots['label-right'] }
        </template>
      </MethodLabel>
      <template slot="extra">
        {this.$slots['extra-append']}
        {this.modelVnodeMap.text_below_stroke}
        {this.modelVnodeMap.banner}
        {this.modelVnodeMap.text_below}
        {this.modelVnodeMap.text_bubble}
        {this.modelVnodeMap.saved}
        {this.modelVnodeMap.deleted}
        {this.modelVnodeMap.delete_old}
        {this.modelVnodeMap.input}
      </template>
      {children}
    </RadioWrapper>
  }
}
