<template>
  <div v-if="method" class="tosslocal-installment">
    <p v-if="instalmentDisable" class="text-secondary">
      {{ $t('89764') }}
    </p>
    <template v-else>
      <div class="text-secondary">
        {{ $t('89770') }}
        <dialog-tips :dialog="{ message: $t('89773') }">
          <icon-question theme="outline" size="16" fill="currentColor" />
        </dialog-tips>
      </div>
      <klk-tag-select
        ref="tagSelect"
        :hide-toggle="ISMOBILE"
        v-model="formModel.install_payment_plan"
        nowrap
        size="normal"
        class="installment-tag-select"
      >
        <klk-tag-select-item :name="null">
          {{ $t('89760') }}
          <p class="height-light caption-12">
            {{ $t('89771') }}
          </p>
        </klk-tag-select-item>
        <klk-tag-select-item v-for="month in 11" :key="month" :name="month + 1">
          {{ $t('89768', month + 1) }}
          <p
            v-if="
              method.free_install_payment_plan &&
                method.free_install_payment_plan.includes(month + 1)
            "
            class="height-light caption-12"
          >
            {{ $t('89771') }}
          </p>
        </klk-tag-select-item>
      </klk-tag-select>
    </template>
  </div>
</template>

<script lang="tsx">
import { Vue, Component, Prop, Inject } from 'vue-property-decorator'
// @ts-ignore
import IconQuestion from '@klook/klook-icons/lib/IconQuestion'
// @ts-ignore
import KlkTagSelect, { TagSelectItem as KlkTagSelectItem } from '@klook/klook-ui/lib/tag-select'
import type { Method } from '../../../types.d'
import { DialogTips } from '../modules'

@Component({
  inheritAttrs: false,
  components: { DialogTips, IconQuestion, KlkTagSelect, KlkTagSelectItem }
})
export default class TosslocalInstallmentContent extends Vue {
  @Inject() isMobile!: () => boolean
  @Inject() getMethodForm!: (methodKey: string, initModel: { install_payment_plan: null | number }) => { install_payment_plan: null | number }
  @Prop({ type: Object, default: null }) method!: Method

  // 本组件是二级支付方式的附加内容。只有在二级支付方式被选中后本组件才会被初始化，即表现形式为每个曾被用户选中过的韩国本地银行都会生成一份分期表单
  formModel = this.getMethodForm(this.method.method_key, { install_payment_plan: null })

  submitInstallment(install_payment_plan: number) {
    // eslint-disable-next-line no-unused-expressions
    !this.ISMOBILE && (this.$refs.tagSelect as any)?.toggleWrap(false)
    this.$emit('commit-settlement', { install_payment_plan })
  }

  get ISMOBILE() {
    return this.isMobile()
  }

  get instalmentDisable() {
    return !(this.method.model_list || []).some(item => item?.type === 'install_payment')
  }
}
</script>

<style lang="scss" scoped>
.tosslocal-installment {
  .installment-tag-select {
    margin-top: 4px;
    background: none !important;
    font-size: 0;

    .height-light {
      color: $color-caution;
    }

    ::v-deep .klk-tag-select {
      &-right {
        height: 60px;
      }

      &-mask {
        width: 20px;
        background: linear-gradient(
          to left,
          $color-bg-3,
          rgba($color-bg-3, 0)
        ) !important;
      }

      &-item {
        border-radius: $radius-l;
        padding: 8px 12px;
        background-color: #fff;
        height: 52px !important;
        min-width: 136px;
        vertical-align: middle;

        &-active {
          color: $color-brand-primary;
          background-color: $color-brand-primary-light;
          border: 1px solid $color-brand-primary;
        }
      }
    }
  }
}
</style>
