<template>
  <div
    :class="{ 'platform-mobile': ISMOBILE }"
    class="add_new_unionpay_card payment-CardNumError-tracking"
    data-spm-module="CardNumError?trg=manual"
  >
    <iframe
      ref="iframe"
      name="iframe_union_card"
      width="100%"
      :src="iframeSrc"
      class="j-iframe_union_card iframe_credit_card"
      :style="`height: ${ISMOBILE ? 80 : 150}px`"
    />
    <klk-loading v-if="!iframeMounted" />

  </div>
</template>
<script lang="ts">
import { Vue, Component, Inject, Prop } from 'vue-property-decorator'
// @ts-ignore
import KlkLoading from '@klook/klook-ui/lib/loading'

import {
  getCreditCardIframeSrc,
  postCreditCardIframeMessage,
  checkValidOrigin,
  handleCreditCardIframeError,
  handleCreditCardNoIframeError
} from '../../../payment-sdk/core/utils/pci'

@Component({
  components: { KlkLoading }
})
export default class UnionpayContent extends Vue {
  @Inject() getMethodForm!: <T extends object>(methodKey: string, model: T, verify: Function) => T
  @Inject({ default: () => () => true, from: 'layerVisible' }) isViewAll!: () => boolean
  @Inject() isMobile!: () => boolean
  @Inject({ default: () => ({}) }) creditcardOptions!: {
    cardTypeRules: any[]
    localization: Record<string, string>
    resEnLang: string
  }

  @Prop({ type: Boolean }) isSelected!: boolean

  iframeSrc = getCreditCardIframeSrc()
  iframeMounted = false

  get ISMOBILE() {
    return this.isMobile()
  }

  mounted() {
    let iframeElement = this.$refs.iframe as HTMLElement
    if (!iframeElement) { return handleCreditCardNoIframeError() }

    const type = 'UnionPay'

    const unionPayForm = this.getMethodForm(type, { last4: null }, () => new Promise((resolve, reject) => {
      const receiveMessageCB = (event: any = {}) => {
        // 我们能信任信息来源吗？
        // 我们能信任信息来源吗？
        if (!checkValidOrigin(event.origin)) {
          return
        }

        const { type, props } = event.data || {}

        if (!props || props.paymentType !== 'union') {
          return
        }

        if (type === 'generateNewCardValidateResult') {
          window.removeEventListener('message', receiveMessageCB)
          if (props.newCardValidateResp.success) {
            resolve(true)
          } else {
            // 只有在mobile 会有提示
            props.newCardValidateResp.result?.errorMessage && this.$toast(props.newCardValidateResp.result?.errorMessage)
            reject(new Error('PCI_UNION_INVALID'))
          }
        }
      }

      window.addEventListener('message', receiveMessageCB, false)
      postCreditCardIframeMessage({
        type: 'validateNewCreditCardInfo',
        props: {}
      }, iframeElement)
    }))

    handleCreditCardIframeError(iframeElement)

    let initHandler: any = () => {
      const { cardTypeRules, ...others } = this.creditcardOptions
      postCreditCardIframeMessage({
        type: 'init',
        props: {
          paymentType: 'union',
          supportedCards: { types: [{ type }] }, // 银联可以是写死的
          parentOrigin: window.location.origin,
          parentsPlatform: this.ISMOBILE ? 'mobile' : 'desktop',
          cardTypeRules: cardTypeRules.filter((item: any) => item.type === type),
          ...others
        }
      }, iframeElement)

      // 这里清空是因为重新初始化后，iframe 会重新加载，之前填写的数据没有办法同步
      // 出现在hotel 结算页响应式 platform 切换的场景
      unionPayForm.last4 = null

      if (this.iframeMounted) {
        handleCreditCardNoIframeError('合并收银台银联支付 PCI iframe 被意外刷新')
      }
    }

    iframeElement.addEventListener('load', initHandler)

    let receiveMessage: any = (event: any = {}) => {
      // 我们能信任信息来源吗？
      if (!checkValidOrigin(event.origin)) {
        return
      }

      const data = event.data

      if (!data.props || data.props.paymentType !== 'union') {
        return
      }

      switch (data.type) {
        case 'mountIframe':
          this.iframeMounted = true
          break
          // case 'autoHeight': {
          //   const { height } = data.props
          //   iframeElement.style.height = `${height ||
          //     (this.ISMOBILE ? 77 : 148)}px`
          //   break
          // }
        /**
         * 银联填好卡或者没有填好卡发出的事件都不需要响应， 不需要调后端接口和保存数据
         */
        case 'updateCheckout':
          data.props.is_new = true
          data.type = 'updateNewCardData'
        // eslint-disable-next-line no-fallthrough
        case 'updateNewCardData': {
          const { last4, is_new } = data.props
          if (is_new && unionPayForm!.last4 !== last4) {
            unionPayForm!.last4 = last4
          }
        }
          break
        case 'showMobileSimpleDialog':
          this.$alert(data.props.content)
          break
      }
    }

    window.addEventListener('message', receiveMessage, false)

    // 促使iframe 里的输入框获取焦点
    let selectedUnwatch: any = this.$watch(
      () => this.isViewAll() && this.isSelected && this.iframeMounted,
      (val) => {
        val && setTimeout(() => {
          postCreditCardIframeMessage(
            {
              type: 'reselect',
              props: {}
            },
            iframeElement
          )
        }, 350)
      },
      { immediate: true }
    )

    this.$once('hook:beforeDestroy', () => {
      selectedUnwatch()
      window.removeEventListener('message', receiveMessage)
      iframeElement.removeEventListener('load', initHandler)
      // @ts-ignore
      selectedUnwatch = iframeElement = receiveMessage = initHandler = null
    })
  }
}
</script>

<style lang="scss">
.add_new_unionpay_card {
  position: relative;
  min-height: 77px;

  .j-iframe_union_card  {
    margin-top: -10px;
  }

  &.platform-mobile {
    background: #fafafa;
    padding: 12px 16px 4px;
    border-radius: $radius-xl;
  }
}
</style>
