<template>
  <klk-form
    ref="telephoneForm"
    :model="formModel"
    v-bind="formStyleOptions"
    class="klook-payment-telephone-form"
    :class="`platform-${isMobile ? 'mobile' : 'desktop'}`"
    :rules="rules"
  >
    <klk-form-item
      :label="$t('profile.mobile.edit.country_code')"
      prop="countryCode"
      :style="!isMobile && 'margin-right: 10px; width: 320px;'"
    >
      <klk-select
        v-model="formModel.countryCode"
        :bottom-sheet="isMobile"
        bottom-sheet-height="480px"
        filterable
        :placeholder="$t('11872')"
      >
        <klk-option
          v-for="(countryOption, i) in picCountryCodeArr"
          :key="`country_option-${i}`"
          :label="countryOption.countryCode"
          :value="countryOption.country_number"
        />
        <klk-option-group
          :label="$t('country.otherCountriesOrDistricts')"
        >
          <klk-option
            v-for="(item, i) in otherArr"
            :key="`country_option-other-${i}`"
            :label="item.countryCode"
            :value="item.country"
          />
        </klk-option-group>
      </klk-select>
    </klk-form-item>
    <klk-form-item
      :label="$t('rail_phone_number')"
      prop="phoneNumber"
      :style="!isMobile && 'width: 315px; margin-right: 0'"
    >
      <klk-input
        v-model="formModel.phoneNumber"
        :placeholder="$t('rail_please_enter_your_phone_number')"
        type="tel"
        oninput="this.tagName === 'INPUT' && (this.value = this.value.replace(/\D/g, ''))"
      />
    </klk-form-item>
  </klk-form>
</template>

<script lang="ts">
import { Vue, Component, Inject, Prop } from 'vue-property-decorator'
import getAllCountryList from '@klook/klk-traveller-utils/lib/allCountryList'
import { languageConfig } from '@klook/site-config'
// @ts-ignore
import KlkForm, { FormItem as KlkFormItem } from '@klook/klook-ui/lib/form'
// @ts-ignore
import KlkSelect, { OPtion as KlkOPtion, OptionGroup as KlkOptionGroup } from '@klook/klook-ui/lib/select'
// @ts-ignore
import KlkInput from '@klook/klook-ui/lib/input'

import type ParamsManager from '../../../../utils/params-manager'

const { getPreferCountryListByLangCode: langPreferCountryCode } = languageConfig

@Component({
  components: { KlkFormItem, KlkForm, KlkInput, KlkOPtion, KlkSelect, KlkOptionGroup }
})
export default class TelephoneForm extends Vue {
  @Inject() getMethodForm!: (methodKey: 'CreditcardNew') => ParamsManager['CreditcardNew']
  @Inject() lang!: string
  @Prop({ type: Boolean }) isMobile!: boolean

  picCountryCodeArr: any[] = []
  otherArr: any[] = []

  // getMethodForm('CreditcardNew') => ParamsManager['CreditcardNew'] 是非空的
  formModel = this.getMethodForm('CreditcardNew')

  verify() {
    return (this.$refs.telephoneForm as any)?.validate()
  }

  get formStyleOptions() {
    return this.isMobile
      ? { styleType: 'lined' }
      : {
        inline: true,
        itemMargin: 12
      }
  }

  get rules() {
    return {
      phoneNumber: [
        { required: true, message: this.$t('global.error.cant_be_empty'), trigger: 'blur' }
      ],
      countryCode: [
        { required: true, message: this.$t('global.select.empty_error'), trigger: 'change' }
      ]
    }
  }

  async mounted() {
    const order = langPreferCountryCode(this.lang as 'en')
    try {
      const array = await getAllCountryList({ $axios: this.$axios, lang: this.lang })
      this.picCountryCodeArr = array.filter((item: any) => {
        item.countryCode += ` (+${item.value})`
        return order.includes(item.country) || !this.otherArr.push(item)
      }).sort((a: any, b: any) => {
        return order.indexOf(a.country) - order.indexOf(b.country)
      })
    } catch (err) {
      return []
    }
  }
}
</script>
<style lang="scss">
.klook-payment-telephone-form {
  input::-webkit-input-placeholder {
    font-weight: $fontWeight-regular;
    color: $color-text-disabled;
    font-size: $fontSize-body-s;
  }

  .klk-form-item-label::after {
    display: none;
  }

  &.platform-desktop {
    .klk-select-reference,
    .klk-input-inner {
      border-radius: 4px;
      border: 1px solid #ccc;
    }

    .klk-form-item:last-child {
      margin-right: 0;
    }
    .klk-form-item-label {
      color: $color-text-secondary;
    }
    .klk-select-highlight .klk-select-reference,
    .klk-input-is-focus .klk-input-inner {
      border-color: $color-brand-primary !important;
    }
  }

  &.platform-mobile {
    .klk-form-item-label {
      font-size: $fontSize-caption-m;
    }

    .klk-select-mask,
    .klk-select-reference,
    .klk-input-inner {
      background: none !important;
    }
  }
}
</style>
