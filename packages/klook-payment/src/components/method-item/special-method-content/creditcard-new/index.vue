<template>
  <div
    :class="{ 'platform-desktop': !ISMOBILE }"
    class="payment-CardNumError-tracking"
    data-spm-module="CardNumError?trg=manual"
  >
    <div class="iframe-wrapper">
      <iframe
        ref="iframe"
        name="iframe_credit_card"
        :src="creditCardIframeSrc"
        class="j-iframe_credit_card iframe_credit_card"
        width="100%"
        scrolling="no"
      />
    </div>

    <klk-loading v-if="!iframeMounted" />
    <template v-else>
      <telephone-form
        v-if="showPhoneInput"
        ref="telephoneForm"
        :is-mobile="ISMOBILE"
      />

      <div v-if="isPaylater" class="creditcard-alert">
        <icon-information theme="filled" size="20" fill="currentColor" />
        <p>{{ $t('111121') }}</p>
      </div>

      <div class="creditcard-controls">
        <klk-checkbox v-model="creditCardNewForm.save_card" @change="changeSaveCard">
          {{ $t('payment.mobile.save_card') }}
        </klk-checkbox>
        <dialog-tips v-if="method.newCardManualOptions && method.newCardManualOptions.publicKey" v-bind="dialogProps" />
      </div>
    </template>
  </div>
</template>
<script lang="ts">
import { Component, Vue, Prop, Inject } from 'vue-property-decorator'
import isEqual from 'lodash/isEqual'
// @ts-ignore
import KlkLoading from '@klook/klook-ui/lib/loading'
// @ts-ignore
import KlkCheckbox from '@klook/klook-ui/lib/checkbox'
// @ts-ignore
import IconInformation from '@klook/klook-icons/lib/IconInformation'
import {
  getCreditCardIframeSrc,
  postCreditCardIframeMessage,
  checkValidOrigin,
  handleCreditCardIframeError,
  handleCreditCardNoIframeError
} from '../../../../payment-sdk/core/utils/pci'
import { Method, ModeItem, PaymentInfo } from '../../../../types.d'
import type ParamsManager from '../../../../utils/params-manager'
import { DialogTips } from '../../modules'

@Component({
  inheritAttrs: false,
  components: {
    IconInformation,
    DialogTips,
    KlkLoading,
    KlkCheckbox,
    TelephoneForm: () => import('./telephone-form.vue')
  }
})
export default class CreditcardNewContent extends Vue {
  @Inject({ default: () => () => true, from: 'layerVisible' }) isVisible!: () => boolean
  @Inject() getPaymentType!: () => string
  @Inject() getMethodForm!: (
    methodKey: string,
    model: any,
    verify: Function
  ) => ParamsManager['CreditcardNew']

  @Inject() isMobile!: () => boolean
  @Inject({ default: () => ({}) }) creditcardOptions!: {
    cardTypeRules: any[]
    localization: Record<string, string>
    resEnLang: string
  }

  @Prop({ type: Boolean }) isSelected!: boolean
  @Prop({ type: Object, default: null }) method!: Method

  creditCardIframeSrc = getCreditCardIframeSrc()
  showPhoneInput = false
  iframeMounted = false
  creditCardNewForm = {} as ParamsManager['CreditcardNew']
  // 记录上次已填写完整的新卡关键数据   不需要响应式
  prevCardOptionCache?: {
    kvalue?: string
    paymentCoupon?: ModeItem
  }

  // 特殊逻辑， 只有在mobile 场景下会被添加字段和值
  mobilePostCodeCardInfoExtend?: {
    need_postal_code?: boolean
    postal_code_length?: number
  }

  get ISMOBILE() {
    return this.isMobile()
  }

  get isPaylater() {
    return this.getPaymentType() === 'paylater'
  }

  changeSaveCard(val: boolean) {
    // eslint-disable-next-line no-unused-expressions
    this.$inhouse?.track('custom', document.body, { spm: val ? 'PaymentMethod_Save_CardDetails' : 'PaymentMethod_Cancel_Save_CardDetails' })
  }

  get dialogProps() {
    return {
      isMobile: this.ISMOBILE,
      options: { placement: 'right' },
      dialog: {
        message: this.$t('pay.credicard.tooltips'),
        title: this.$t('payment.mobile.credit_card_tip_title')
      }
    }
  }

  get couponNotAvailableRemind() {
    let isConfirming = false

    return () => {
      // PCI 每次都触发两次事件
      if (isConfirming) {
        return
      }
      isConfirming = true
      return this.$confirm(this.$t('73234'), {
        okLabel: this.$t('73235'),
        cancelLabel: this.$t('73236')
      }).then(({ result }: { result: boolean }) => {
        isConfirming = false
        // 丢弃优惠券  重新刷新
        if (result && this.creditCardNewForm.credit_card_info) {
          this.creditCardNewForm.credit_card_info.is_new = true
          this.$emit('commit-settlement', undefined, true)
        }
        return result
      })
    }
  }

  async getMobileCardInfoExtend(credit_card_info: Record<string, any>, iframeElement: Element) {
    const res = await this.$axios.$post('/v1/cashier/payment/getpaymethod', {
      merchant_id: '10073',
      credit_card_info,
      scop: Number(this.method.newCardManualOptions!.isScop) || 0
    })
    if (res && res.success) {
      const { need_postal_code = false, postal_code_length = 0 } = res.result?.payment_info?.card_info_extend || {}
      this.mobilePostCodeCardInfoExtend = { need_postal_code, postal_code_length }
      this.updateCardInfoExtend(iframeElement)
    }
  }

  updateCardInfoExtend(iframeElement: Element, cardInfoExtend = this.method.newCardManualOptions!.cardInfoExtend) {
    iframeElement && postCreditCardIframeMessage({
      type: 'updateCardInfoExtend',
      props: { cardInfoExtend: { ...cardInfoExtend, ...this.mobilePostCodeCardInfoExtend } }
    }, iframeElement)
  }

  initForm(iframeElement: Element) {
    // getMethodForm('CreditcardNew') => ParamsManager['CreditcardNew'] 是非空的
    this.creditCardNewForm = this.getMethodForm('CreditcardNew', null, async () => {
      try {
        await (this.$refs.telephoneForm as any)?.verify()
        await new Promise((resolve, reject) => {
          const pciVerifyMessage = (event: any = {}) => {
            // 我们能信任信息来源吗？
            if (!checkValidOrigin(event.origin)) {
              return
            }

            const { type, props } = event.data || {}

            if (!props || props.paymentType !== 'credit') {
              return
            }

            if (type === 'generateNewCardValidateResult') {
              window.removeEventListener('message', pciVerifyMessage)

              if (props.newCardValidateResp.success) {
                return resolve(true)
              }
              // 只有在mobile 会有props.newCardValidateResp.result
              if (props.newCardValidateResp.result) {
                const { code, errorMessage } = props.newCardValidateResp.result
                if (code === 'PREFIX') {
                  this.couponNotAvailableRemind()
                  return reject(new Error('PCI_PREFIX'))
                } else {
                  !this.method.newCardManualOptions!.publicKey && errorMessage && this.$toast(errorMessage)
                }
              }
              reject(new Error('PCI_CARD_INVALID'))
            }
          }

          window.addEventListener('message', pciVerifyMessage, false)

          postCreditCardIframeMessage(
            {
              type: 'validateNewCreditCardInfo',
              props: {}
            },
            iframeElement
          )
        })
      } catch {
        throw new Error('PCI')
      }
    })
  }

  mounted() {
    let iframeElement = document.querySelector('.j-iframe_credit_card') as any

    if (!iframeElement) {
      return handleCreditCardNoIframeError()
    }

    const { cardTypeRules, ...others } = this.creditcardOptions

    const filterCardTypeRules = (supportedCardsTypes: PaymentInfo['supported_cards']['types'] = []) =>
      cardTypeRules.filter((rule: { type?: string } = {}) =>
        supportedCardsTypes.some(card => card.type === rule.type))

    handleCreditCardIframeError(iframeElement)

    let initHandler: any = () => {
      const { publicKey, supportedCards, cardInfoExtend } = this.method.newCardManualOptions!
      // 当desktop 出现postcode 后切换成mobile， 此时PCI 重新加载， 卡号被清空，不应当出现出现postcode
      postCreditCardIframeMessage({
        type: 'init',
        props: {
          themeColor: '#ff5722',
          paymentType: 'credit',
          parentsPlatform: this.ISMOBILE ? 'mobile' : 'desktop',
          cardInfoExtend: { ...cardInfoExtend, need_postal_code: false, postal_code_length: 0 },
          ...others,
          publicKey,
          supportedCards,
          provideType: publicKey && 'lianlianpay',
          cardTypeRules: filterCardTypeRules(supportedCards.types)
        }
      }, iframeElement)

      // 这里清空是因为重新初始化后，iframe 会重新加载，之前填写的数据没有办法同步
      // 场景在hotel 结算页响应式 platform 切换的场景
      this.creditCardNewForm.credit_card_info = null
      this.prevCardOptionCache = this.mobilePostCodeCardInfoExtend = undefined

      this.method.model_list?.length && this.method.model_list.splice(0, this.method.model_list.length)

      if (this.iframeMounted) {
        handleCreditCardNoIframeError('合并收银台银行卡支付 PCI iframe 被意外刷新')
      }
    }

    iframeElement.addEventListener('load', initHandler)

    this.initForm(iframeElement)
    let receiveMessage: any = (event: any = {}) => {
      // 我们能信任信息来源吗？
      if (!checkValidOrigin(event.origin)) {
        return
      }

      const data = event.data
      if (!data.props || data.props.paymentType !== 'credit') {
        return
      }

      switch (data.type) {
        case 'mountIframe':
          this.iframeMounted = true
          break
        case 'autoHeight': {
          const { height } = data.props
          iframeElement.style.height = `${height || (this.ISMOBILE ? 319 : 248)}px`
          break
        }
        case 'updatePhoneNumberVisible':
          ;({ visible: this.showPhoneInput = false } = data.props)
          break
        case 'updateCheckout':
          data.props.is_new = true
          data.type = 'updateNewCardData'
        // eslint-disable-next-line no-fallthrough
        case 'updateNewCardData': {
        // 更新卡事件，每次卡号输入框输入都会触发，只有在失去焦点的那次带有type为creditcard-new & is_new 为true
          console.log(data.type, data.props)
          const isLianlian = this.method.newCardManualOptions!.publicKey
          const { is_new, kvalue, bin, last4, issuer_bin, telephone_number, postal_code } = !isLianlian
            ? data.props
            : data.props.credit_card_info || {}

          if (!kvalue) {
            this.creditCardNewForm.credit_card_info = null
            return
          }

          const { kvalue: oldKvalue = '', postal_code: oldPostalCode = '' } = this.creditCardNewForm.credit_card_info || {}

          // 减少this.creditCardNewForm 变化以减少MobileMethodSnapshot 等有依赖关系的组件的更新
          if (kvalue !== oldKvalue) {
            this.creditCardNewForm.credit_card_info = {
              bin,
              last4,
              telephone_number,
              kvalue,
              issuer_bin,
              postal_code
            }
          } else if (postal_code !== oldPostalCode) {
            // this.creditCardNewForm.credit_card_info 一定有值不会是null, 因为只没有输入卡号就走不到这一步，输入了卡号就不为null
            this.creditCardNewForm.credit_card_info!.postal_code = postal_code
          }

          if (isLianlian) {
            // 只有在有piblicKey(lianlian 支付的场景才会有token 和 telephone_number
            // lianlian 支付的场景，is_new 为undefined 是为了不触发价格试算（参考历史逻辑）
            // lianlian 场景，PCI 吐出的数据也没有办法区分用户输入的卡是否完整
            this.creditCardNewForm.token = data.props.token
            return
          }

          if (this.prevCardOptionCache?.paymentCoupon) {
            // this.prevCardOptionCache.paymentCoupon 有值  this.method.model_list 和  this.prevCardOptionCache.kvalue 一定不为空
            const paymentCouponIndex = this.method.model_list!.findIndex(({ type }) => type === 'payment_coupon')
            if (kvalue === this.prevCardOptionCache!.kvalue) {
              // 本次输入的卡号和缓存的上次已经过价格试算的卡号一致，通过prevCardOptionCache.paymentCoupon 还原支付优惠券
              paymentCouponIndex === -1 && this.method.model_list!.push(this.prevCardOptionCache.paymentCoupon)
            } else if (paymentCouponIndex > -1) {
              // 本次输入的卡号和缓存的上次已经过价格试算的卡号不一致去掉支付优惠券
              this.method.model_list!.splice(paymentCouponIndex, 1)
            }
          }

          if (is_new) {
            this.creditCardNewForm.credit_card_info!.is_new = true
            if (this.ISMOBILE) {
              // mobile 在浮层中， 通过请求支付后端的接口更新邮编，desktop 通过价格试算接口更新
              this.getMobileCardInfoExtend(this.creditCardNewForm.credit_card_info!, iframeElement)
            }
            // 选其他支付方式，失去焦点 this.isSelected 为false
            this.$nextTick(() => {
              this.isSelected && this.$emit('commit-settlement')
            })
          }
        }
          break
        case 'updateBuried':
          // ?? TODO 埋点 只有web 端会触发
          if (data.props.newCardValidateResp?.result?.code === 'PREFIX') {
            this.couponNotAvailableRemind()
          }
          break
        case 'showMobileSimpleDialog':
          this.$alert(data.props.content)
      }
    }

    window.addEventListener('message', receiveMessage, false)

    // 促使iframe 里的输入框获取焦点
    let visibleUnwatch: any = this.$watch(
      () => this.isVisible() && this.isSelected && this.iframeMounted,
      (val) => {
        val && setTimeout(() => {
          postCreditCardIframeMessage(
            {
              // 合并结算页场景，只需要同步高度，不需要获得焦点和滚动到PCI位置
              type: this.method.newCardManualOptions!.autoFocusable ? 'reselect' : 'syncHeight',
              props: {}
            },
            iframeElement
          )
        }, 350)
      },
      { immediate: true }
    )

    let methodUnwatch: any = this.$watch('method', (method: Method, oldMethod: Method) => {
      const kvalue = this.creditCardNewForm.credit_card_info?.kvalue

      this.prevCardOptionCache = kvalue
        ? {
          kvalue,
          paymentCoupon: this.method.model_list?.[0]
        }
        : undefined
      const [[newSupportedCards, newCardInfoExtend], [oldSupportedCards, oldCardInfoExtend]] =
        [method, oldMethod].map((method) => {
          const { supportedCards, cardInfoExtend } = method.newCardManualOptions!
          return [supportedCards || { types: [] }, cardInfoExtend || undefined]
        })

      if (!isEqual(newSupportedCards, oldSupportedCards)) {
        postCreditCardIframeMessage({
          type: 'updateSupportedCards',
          props: { supportedCards: newSupportedCards }
        }, iframeElement)

        postCreditCardIframeMessage({
          type: 'updateCardTypeRules',
          props: { cardTypeRules: filterCardTypeRules(newSupportedCards.types) }
        }, iframeElement)
      }

      if (!isEqual(newCardInfoExtend, oldCardInfoExtend)) {
        this.updateCardInfoExtend(iframeElement, newCardInfoExtend)
      }
    })

    this.$once('hook:beforeDestroy', () => {
      visibleUnwatch()
      methodUnwatch()
      window.removeEventListener('message', receiveMessage)
      iframeElement.removeEventListener('load', initHandler)
      methodUnwatch = visibleUnwatch = iframeElement = receiveMessage = initHandler = null
    })
  }
}
</script>
<style lang="scss" scoped>
.payment-CardNumError-tracking {
  position: relative;
  .creditcard-alert {
    display: flex;
    align-items: center;
    margin: 16px 0;
    background-color: $color-info-background;
    padding: 12px;
    border-radius: $radius-l;
    .i-icon-icon-information {
      margin-right: 8px;
    }
  }
  &.platform-desktop {
    .iframe-wrapper {
      overflow: hidden;
      .iframe_credit_card {
        margin: -48px -24px -4px -23px;
        width: 105%;
      }
    }
  }

  .creditcard-controls {
    display: flex;
    align-items: center;
    .info-tip {
      margin-left: 8px;
      cursor: pointer;
    }
  }
}
</style>
