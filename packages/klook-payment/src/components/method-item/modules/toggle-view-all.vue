<template>
  <div
    class="toggle-view-all-wrapper"
    :class="{ 'is-view-less': !disabled && !isViewAll }"
  >
    <slot is-view-all="this.isViewAll" />

    <view-all-switch
      v-if="!disabled"
      :is-view-all="isViewAll"
      :view-all-text="viewAllText"
      :iht-attrs="$attrs.ihtAttrs"
      @click="toggleViewAll"
    >
      <slot slot="icons" name="icons" />
      <slot name="text" />
    </view-all-switch>
  </div>
</template>

<script lang="tsx">
import { Vue, Component, Prop } from 'vue-property-decorator'
import ViewAllSwitch from './view-all-switch'

@Component({
  inheritAttrs: false,
  components: { ViewAllSwitch }
})
export default class ToggleViewAll extends Vue {
  @Prop({ type: Boolean, default: false }) disabled!: boolean
  @Prop({ type: String }) viewAllText!: string

  isViewAll = false

  toggleViewAll() {
    this.isViewAll = !this.isViewAll
  }
}
</script>

<style lang="scss" scoped>
.toggle-view-all-wrapper {
  border-radius: $radius-xl;
  padding: 0 16px;

  &.method-list {
    border: 16px solid $color-bg-3;
  }

  &.sub-method-list {
    background-color: $color-bg-3;
  }

  &.is-view-less {
    > .method-item:not(.checked),
    > .sub-method-item:not(:nth-of-type(-n+4)):not(.checked),
    // 选中的sub-method-item，都只显示4个元素
    &:has(> .sub-method-item.checked:not(:nth-of-type(-n+4))) > .sub-method-item:nth-of-type(4) {
      display: none;
    }
  }
}
</style>
