import { FunctionalComponentOptions } from 'vue'
// @ts-ignore
import Link from '@klook/klook-ui/lib/link'
// @ts-ignore
import IconChevronDown from '@klook/klook-icons/lib/IconChevronDown'
// @ts-ignore
import { $colorTextPrimary } from '@klook/klook-ui/es/utils/design-token-esm'

const ViewAllSwitch: FunctionalComponentOptions<{isViewAll: boolean, viewAllText?: string, ihtAttrs?: object }> = {
  functional: true,
  render(_h, { props: { isViewAll, viewAllText, ihtAttrs }, listeners: { click }, slots, parent: ctx }) {
    const { default: content, icons } = slots()
    return <div class={['view-all-switch', { 'is-view-all': isViewAll }]}>
      { icons }
      <Link onClick={ click } color={ $colorTextPrimary } attrs={ ihtAttrs } >
        {
          content || (
            isViewAll
              ? ctx.$t('17826')
              : viewAllText || ctx.$t('see_more')
          )
        }
        <IconChevronDown theme="outline" fill="currentColor" size="20" />
      </Link>
    </div>
  }
}

export default ViewAllSwitch
