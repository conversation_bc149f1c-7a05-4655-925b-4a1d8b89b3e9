<template>
  <div
    class="payment-radio-wrapper"
    :class="{ checked: isSelected }"
  >
    <div
      class="payment-radio"
      :class="{ 'mobile-reverse': ISMOBILE } "
      v-bind="ihtAttrs"
      @click="handlerClick"
    >
      <i class="radio-circle" />
      <div class="payment-label">
        <slot name="label">
          {{ label }}
        </slot>
      </div>
    </div>
    <div v-if="$slots.extra" class="payment-extra">
      <slot name="extra" />
    </div>
    <transition name="slide-down">
      <div v-if="$slots.default" v-show="isSelected" class="payment-content">
        <slot />
      </div>
    </transition>
  </div>
</template>

<script lang="ts">
import { Vue, Component, Prop, Inject } from 'vue-property-decorator'

@Component({ inheritAttrs: false })
export default class RadioWrapper extends Vue {
  @Prop({ type: Boolean }) isSelected!: <PERSON><PERSON>an
  @Prop({ type: Object }) ihtAttrs!: object
  @Prop({ type: String }) label!: string
  @Inject() isMobile!: () => boolean

  get ISMOBILE() {
    return this.isMobile()
  }

  handlerClick() {
    !this.isSelected && this.$emit('select')
  }
}
</script>

<style lang="scss" scoped>
.payment-radio-wrapper {
  .payment-radio {
    display: flex;
    align-items: center;
    cursor: pointer;
    padding: 20px 0;
    @include font-body-m-semibold;

    .payment-label {
      flex: 1;
      min-width: 0;

      .method-icon {
        height: 32px;
      }
    }

    .radio-circle {
      position: relative;
      width: 20px;
      height: 20px;
      border-radius: $radius-circle;
      border: 1px solid $color-border-active;
      background-color: $color-bg-1;
      transition: all $motion-duration-m $motion-timing-ease;
      text-align: center;
      margin-right: 12px;

      &::after {
        transition: all $motion-duration-m $motion-timing-ease;
        opacity: $opacity-transparent;
        position: absolute;
        top: 50%;
        left: 50%;
        content: '';
        width: 12px;
        height: 12px;
        margin-left: -6px;
        margin-top: -6px;
        border-radius: $radius-circle;
        background-color: $color-brand-primary;
      }
    }

    &.mobile-reverse {
      flex-direction: row-reverse;
      padding: 16px 0;
      @include font-body-m-regular;

      .radio-circle {
        margin-right: 0;
        margin-left: 12px;
      }

      ::v-deep .method-icon {
        height: 24px;
      }
    }
  }

  .payment-extra {
    margin: -8px 0 8px;

    ::v-deep .model-type {
      &--text_below,
      &--text_bubble,
      &--saved,
      &--deleted,
      &--text_below_stroke {
        display: flex;
        align-items: center;
        margin-bottom: 8px;
      }

      &--delete_old {
        margin-bottom: 8px;
      }

      &--text_below {
        > p {
          flex: 0 1 auto;
          min-width: 0;
          white-space: wrap;

          &:not(.multi-lines) {
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
          }
        }
      }

      &--text_bubble {
        display: inline-flex;
        border-radius: 0px 10px 10px 10px;
        padding: 4px 8px;
        background-color: $color-info-background;
      }

      &--saved,
      &--deleted {
        border-radius: $radius-m;
        background-color: $color-bg-2;
        padding: 12px;
        flex-wrap: wrap;
        justify-content: space-between;
      }

      &--deleted-main {
        display: flex;
      }
    }
  }

  .payment-content {
    padding-bottom: 16px;
  }

  &.checked > .payment-radio .radio-circle {
    border-color: $color-brand-primary;
    &:hover {
      border-color: $color-brand-primary;
    }
    &::after {
      opacity: $opacity-solid;
    }
  }

  &.payment-item > .payment-radio:not(.mobile-reverse) .payment-label {
    @include font-heading-xs-v2;
  }

  &.payment-item:first-child > .payment-radio {
    padding-top: 0;
  }

  &.payment-item:last-child {
    &:not(.checked) > .payment-radio,
     > .payment-content {
      padding-bottom: 0;
    }
  }

  &.payment-item:not(:last-of-type),
  &.method-item {
    border-bottom: 1px solid $color-border-normal;
  }

  &.sub-method-item {
    background: none;
    &:not(:last-of-type) .payment-content {
      border-bottom: 1px solid $color-border-normal;
    }
  }

  .slide-down-enter-active {
    animation: slide-down 0.4s;
  }

  .slide-down-leave-active {
    animation: slide-up 0.2s;
    overflow: hidden;
  }

  @keyframes slide-down {
    0% {
      max-height: 0;
      opacity: $opacity-transparent;
    }

    100% {
      max-height: 800px;
      opacity: $opacity-solid;
    }
  }

  @keyframes slide-up {
    0% {
      max-height: 800px;
      opacity: $opacity-solid;
    }

    20% {
      max-height: 300px;
      opacity: $opacity-overlay-desktop;
    }

    100% {
      max-height: 0;
      opacity: $opacity-pressed;
    }
  }
}
</style>
