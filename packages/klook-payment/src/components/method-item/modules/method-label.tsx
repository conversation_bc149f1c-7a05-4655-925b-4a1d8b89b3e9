import { FunctionalComponentOptions } from 'vue'
import type MethodItem from '../'
import type { Method } from '../../../types.d'
import getModelVnodeMap from '../model-list'
import type { MobileMethodList } from '../../method-list'

export const MethodLabel: FunctionalComponentOptions<{ method: Method }> = {
  functional: true,
  // @ts-ignore
  render(_h, { props: { method }, scopedSlots: { left, right }, data: { class: className }, parent }) {
    if (!method) {
      return
    }
    const ctx = parent as MethodItem | MobileMethodList
    const { warning, payment_coupon } = ctx.$options.name === 'method-item'
      ? (ctx as MethodItem).modelVnodeMap // 在 MethodItem 作用域中，直接取MethodItem.modelVnodeMap
      // mweb 入口选中支付方式快照组件, 此时 ctx.$options.name 是 mobile-method-list
      : getModelVnodeMap.call(ctx, method, true, ['warning', 'payment_coupon'])
    const { name, icons } = method || {}

    const rightVnode = right
      ? right({})
      : payment_coupon

    return <div class={['method-label', className]}>
      <div class="left-content">
        {icons?.[0] && <img class="method-icon" height="24" src={icons[0]} alt={name} />}
        <div class="label-main">
          {
            left
              ? left({})
              : [
                name,
                warning
              ]
          }
        </div>
      </div>
      {
        rightVnode && <div class="right-content">
          {rightVnode}
        </div>
      }
    </div>
  }
}

export default MethodLabel
