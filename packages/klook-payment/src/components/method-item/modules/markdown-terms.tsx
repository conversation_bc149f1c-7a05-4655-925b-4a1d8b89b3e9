import { FunctionalComponentOptions } from 'vue'
// @ts-ignore
import Markdown from '@klook/klook-ui/lib/markdown'

const MarkdownTerms: FunctionalComponentOptions<{content: string}> = {
  functional: true,
  // @ts-ignore
  render(_h, { props: { content } }) {
    // markdown a 标签补target="_blank"
    return content && <Markdown
      content={content}
      options={ (marked: any) => {
        if (!marked) {
          return undefined
        }
        // 创建一个自定义的 Renderer
        const renderer = new marked.Renderer()

        renderer.link = function () {
          return marked.Renderer.prototype.link.apply(this, arguments).replace(/^<a /, '<a target="_blank"')
        }

        return { renderer }
      } }
    />
  }
}

export default MarkdownTerms
