import Vue, { FunctionalComponentOptions, CreateElement } from 'vue'
// @ts-ignore
import { $colorTextSecondary } from '@klook/klook-ui/es/utils/design-token-esm'
// @ts-ignore
import IconInformation from '@klook/klook-icons/lib/IconInformation'
// @ts-ignore
import Poptip from '@klook/klook-ui/lib/poptip'
import type { Dialog as DialogType } from '../../../types.d'
import showBottoSheet from '.././../../utils/show-bottom-sheet'
import { MarkdownTerms } from '../modules'

const DialogTips: FunctionalComponentOptions<{
  dialog: DialogType & { message: string | ((h: CreateElement) => JSX.Element) },
  iconColor?: string,
  markdown?: boolean,
  isMobile?: boolean,
  botoomSheet?: boolean
  options?: Record<string, any>
  ihtAttrs?: Record<string, any>
}> = {
  functional: true,
  render(h, { props: { dialog, botoomSheet, isMobile, options, iconColor, markdown, ihtAttrs } = {}, slots, parent }) {
    if (!dialog?.message) {
      return
    }

    const ctx = parent as Vue & { ISMOBILE: boolean, platform: string }
    const content = typeof dialog.message === 'string' && markdown
    // @ts-ignore
    // eslint-disable-next-line
      ? (h: CreateElement = ctx.$createElement) => <MarkdownTerms content={dialog.message} />
      : dialog.message

    const slotDefault = slots().default
    const ISMOBILE = isMobile || ctx.ISMOBILE || ctx.platform === 'mobile'

    if (!ISMOBILE && ihtAttrs?.['data-spm-virtual-item'] === '__virtual') {
      ihtAttrs = {
        ...ihtAttrs, // desktop 默认 evt 为 hover
        'data-spm-virtual-item': ihtAttrs['data-spm-virtual-item'] += '?evt=' + (options?.trigger || 'hover')
      }
    }

    const iconVnode = Array.isArray(slotDefault)
      ? slotDefault[0]
      : slotDefault || <IconInformation
        size="16"
        style="vertical-align: middle;margin: 0 6px;"
        fill={iconColor || $colorTextSecondary }
        attrs={ ihtAttrs }
        class="icon-tips"
      />

    iconVnode.data.style = (iconVnode.data.style || '') + 'vertical-align: middle;'

    if (ISMOBILE) {
      iconVnode.data!.on = {
        click: (e: Event) => {
          botoomSheet
            ? showBottoSheet.call(ctx, { title: dialog.title, ...options }, content)
            : ctx.$alert(content, dialog.title, {
              okLabel: dialog.positive,
              ...options
            })
          return e?.stopImmediatePropagation()
        }
      }
      return iconVnode
    }

    iconVnode.data.style += ';cursor: pointer'
    
    iconVnode.data!.on = {
      click: (e: Event) => e?.stopImmediatePropagation()
    }

    return <Poptip title={dialog.title} attrs={options}>
      {iconVnode}
      <template slot="content">
        {
          typeof content === 'function'
            ? content(h)
            : content
        }
      </template>
    </Poptip>
  }
}

export default DialogTips
