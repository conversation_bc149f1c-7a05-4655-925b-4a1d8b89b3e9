import { FunctionalComponentOptions } from 'vue'
// @ts-ignore
import Button from '@klook/klook-ui/lib/button'
// @ts-ignore
import IconAddCircle from '@klook/klook-icons/lib/IconAddCircle'
import type { Method } from '../../../types.d'

import type { MobileMethodList } from '../../method-list'
import ParamsManager, { CreditcardNew } from '../../../utils/params-manager'
import TextBelow from '../model-list/text_below'
import MethodLabel from './method-label'

// css 代码在 '../../../index.scss'

const MobileMethodSnapshot: FunctionalComponentOptions<{ method: Method }> = {
  functional: true,
  inject: ['getMethodForm'],
  // @ts-ignore
  render(_h, { props: { method }, injections: { getMethodForm }, listeners, parent }) {
    const ctx = parent as MobileMethodList

    if (!method?.method_key || ctx.$options.name !== 'mobile-method-list') {
      return
    }
    const openLayerHandler = listeners['open-layer'] as Function

    let buttonVnode!: JSX.Element
    let labelLeftText!: string | JSX.Element | Array<JSX.Element | string>

    switch (method.method_key) {
      case CreditcardNew: {
        const { credit_card_info } = getMethodForm('CreditcardNew') as ParamsManager['CreditcardNew']
        if (!credit_card_info?.last4) {
          return <div
            class="creditcard-new-snapshot"
            data-spm-module="AddNewCard"
            data-spm-virtual-item="__virtual"
            onClick={ openLayerHandler }
          >
            <div class="creditcard-new-title">
              <IconAddCircle size={24} fill="currentColor" />
              <span>{ ctx.$t('176496') }</span>
            </div>
            <p class="creditcard-new-desc text-secondary">{ctx.$t('173909')}</p>
          </div>
        }

        labelLeftText = `****${credit_card_info.last4}`
      }
        break
      case 'unionpay': {
        const unionPayForm = getMethodForm('UnionPay')
        if (unionPayForm?.last4) {
          labelLeftText = `****${unionPayForm.last4}`
        } else {
          buttonVnode = <Button
            block
            type="secondary"
            style="margin-top: 12px"
            onClick={openLayerHandler}
          >
            { ctx.$t('176516') }
          </Button>
        }
      }
        break
      default:
        if (method.action !== 'select') {
          if (method.sub_options?.length) {
            buttonVnode = <Button
              block
              type="secondary"
              style="margin-top: 12px"
              onClick={ openLayerHandler }
            >
              { ctx.$t('global.payment.type.tip') }
            </Button>
          }
        } else {
          const methodForm = getMethodForm(method.method_key) || {}
          const months = methodForm.install_payment_plan
          // 显示分期信息
          if (months !== undefined) {
            labelLeftText = [
              method.name,
              <br />,
              months > 0
                ? ctx.$t('89776', { months })
                : ctx.$t('89760')
            ]
          } else if (method.model_list?.some(({ type }) => type === 'input') && (!methodForm.cvv || methodForm.cvv.length < 3)) {
            buttonVnode = <Button
              block
              type="secondary"
              style="margin-top: 12px"
              onClick={() => {
                openLayerHandler()
                methodForm.verify?.().catch(() => { console.log('cvv input focus') })
              }}
            >
              { ctx.$t('176515') }
            </Button>
          }
        }
    }

    const modelTextBelow = method.model_list?.find(({ type }) => type === 'text_below')

    return <div class="current-method-snapshot">
      {/* @ts-ignore */}
      <MethodLabel
        method={method}
        scopedSlots={ labelLeftText && { left: () => labelLeftText } }
      />
      {
        // @ts-ignore
        modelTextBelow && <TextBelow attrs={ modelTextBelow } isSelected />
      }
      { buttonVnode }
    </div>
  }
}

export default MobileMethodSnapshot
