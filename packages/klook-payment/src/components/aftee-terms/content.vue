<template>
  <div class="aftee-terms-wrap">
    <div class="terms-step">{{ step }}</div>
    <div v-if="isMobile" class="terms-step-mobile-wrap">
      <div class="step-mobile-item">
        <div
          class="step-mobile-item-img"
          :style="`background-image: url('${stepContents[0].url}')`"
        ></div>
        <div class="step-mobile-item-semibold">
          {{ stepContents[0].contents }}
        </div>
        <div class="step-mobile-item-regular">{{ stepContents[0].desc }}</div>
      </div>
      <div class="step-mobile-item-line"></div>
      <div class="step-mobile-item">
        <div
          class="step-mobile-item-img"
          :style="`background-image: url('${stepContents[1].url}')`"
        ></div>
        <div class="step-mobile-item-semibold">
          {{ stepContents[1].contents }}
        </div>
        <div class="step-mobile-item-regular">{{ stepContents[1].desc }}</div>
      </div>
      <div class="step-mobile-item-line"></div>
      <div class="step-mobile-item">
        <div
          class="step-mobile-item-img"
          :style="`background-image: url('${stepContents[2].url}')`"
        ></div>
        <div class="step-mobile-item-semibold">
          {{ stepContents[2].contents }}
        </div>
        <div class="step-mobile-item-regular">{{ stepContents[2].desc }}</div>
      </div>
    </div>
    <div v-else class="terms-step-desktop-wrap">
      <div class="step-desktop-item">
        <div
          class="step-desktop-item-img"
          :style="`background-image: url('${stepContents[0].url}')`"
        ></div>
        <div class="step-desktop-item-content">
          <div class="step-desktop-item-semibold">
            {{ stepContents[0].contents }}
          </div>
          <div class="step-desktop-item-regular">
            {{ stepContents[0].desc }}
          </div>
        </div>
      </div>
      <div class="step-desktop-item-line"></div>
      <div class="step-desktop-item">
        <div
          class="step-desktop-item-img"
          :style="`background-image: url('${stepContents[1].url}')`"
        ></div>
        <div class="step-desktop-item-content">
          <div class="step-desktop-item-semibold">
            {{ stepContents[1].contents }}
          </div>
          <div class="step-desktop-item-regular">
            {{ stepContents[1].desc }}
          </div>
        </div>
      </div>
      <div class="step-desktop-item-line"></div>
      <div class="step-desktop-item">
        <div
          class="step-desktop-item-img"
          :style="`background-image: url('${stepContents[2].url}')`"
        ></div>
        <div class="step-desktop-item-content">
          <div class="step-desktop-item-semibold">
            {{ stepContents[2].contents }}
          </div>
          <div class="step-desktop-item-regular">
            {{ stepContents[2].desc }}
          </div>
        </div>
      </div>
    </div>
    <h3 class="terms-title">{{ title }}</h3>
    <h3 class="terms-title">{{ titleSub }}</h3>
    <div class="service-wrap">
      <div
        v-for="(item, index1) in contents"
        :key="index1"
        class="terms-content-wrap"
      >
        <p class="terms-content-wrap-title">{{ item.title }}</p>
        <p v-if="item.title_desc">{{ item.title_desc }}</p>
        <ol>
          <li
            v-for="(value, index2) in item.contents"
            :key="index2"
            v-html="value"
          ></li>
        </ol>
        <ul
          v-if="item.sub_contents && item.sub_contents.length > 0"
          class="terms-content-wrap-lower"
        >
          <li v-for="(subContents, index3) in item.sub_contents" :key="index3">
            {{ subContents }}
          </li>
        </ul>
      </div>
      <p v-html="instructions"></p>
    </div>
  </div>
</template>

<script lang="ts">
import { Vue, Component, Prop } from 'vue-property-decorator'

@Component
export default class AfteeTerms extends Vue {
  @Prop({ type: Boolean }) isMobile!: boolean

  step = '3步完成繳費'
  stepContents = [
    {
      contents: '結帳手續完成',
      desc: '無需繳費, 即可安心體驗服務',
      url:
        'https://res.klook.com/image/upload/v1637120531/aftee_illustration_step1_yrmslm.png'
    },
    {
      contents: '缴費通知簡訊',
      desc: 'AFTEE電子繳費單',
      url:
        'https://res.klook.com/image/upload/v1637120531/aftee_illustration_step2_on7iom.png'
    },
    {
      contents: '繳費期內付款',
      desc: '四大超商或銀行轉帳',
      url:
        'https://res.klook.com/image/upload/v1637120531/aftee_illustration_step3_hpljjs.png'
    }
  ]

  title =
    '選擇「超商付款/銀行轉賬」之同時，即代表您同意以下注意事項及相關規定之規範，請仔細閱讀以下內容：'

  titleSub = '關於「超商付款/銀行轉賬」（此服務由AFTEE提供）'
  instructions =
    '若購買人擬行使個資法任一權利，請聯繫恩沛科技股份有限公司（https://netprotections.freshdesk.com/support/home）。購買人得自由選擇是否提供上開個人資料，惟若拒絕提供，可能導致無法使用全部或部分「超商付款/銀行轉賬」服務。'

  contents = [
    {
      title: '繳費方式',
      contents: [
        '使用「超商付款/銀行轉賬」完成結帳。',
        '收取訂單憑證後，即可安心體驗服務。',
        "訂單確認後，即寄送繳費通知簡訊，14天內憑帳單連結於四大超商、ATM／網銀付款即可（AFTEE會員請憑App帳單完成付款）。詳情請查看<a href='https://netprotections.freshdesk.com/support/solutions/articles/70000204168-%E2%91%A0%E8%A9%B2%E5%A6%82%E4%BD%95%E4%BB%98%E6%AC%BE' target='_blank' style='color: #0000FF' >繳費方式</a>（點擊或長按）。"
      ]
    },
    {
      title: '付款限制及繳費期限',
      contents: [
        "請留意繳費期限最短為14天，如逾期，AFTEE將向您加收滯納金，詳情請查看<a href='https://netprotections.freshdesk.com/support/solutions/articles/70000204171-%E2%91%A2%E4%BB%80%E9%BA%BC%E6%98%AF%E9%80%BE%E6%9C%9F%E6%BB%AF%E7%B4%8D%E9%87%91-' target='_blank' style='color: #0000FF' >滯納金說明</a>（點擊或長按）。",
        '初次使用AFTEE時，將依認證結果及本公司審查結果，核予每人不同之上限額度，最高額度可達新台幣10,000元。'
      ]
    },
    {
      title: '注意事項',
      contents: [
        '透過由恩沛科技股份有限公司（AFTEE）提供之「 超商付款/銀行轉賬  」服務完成的交易，需依本服務之必要範圍內提供個人資料，並將交易相關給付款項請求債權轉讓予恩沛科技股份有限公司。',
        '個人資料處理事宜詳情請查看AFTEE官網（https://aftee.tw/terms/）。',
        '未成年的使用者請事先徵得法定代理人或監護人之同意方可使用AFTEE，若未經同意申辦者引起之損失，AFTEE與KLOOK不負相關責任。',
        '嚴禁一人註冊多個帳號或使用他人資訊註冊。',
        '若發現惡意使用之情形，恩沛科技股份有限公司將有權停止該用戶之使用額度並採取法律行動。'
      ]
    },
    {
      title: '「超商付款/銀行轉賬」個人資料利用說明',
      title_desc:
        '本人已詳閱下列個資告知事項，並同意為使用「超商付款/銀行轉賬」服務完成結帳，由KLOOK提供本次訂購之購買人用戶ID予恩沛科技股份有限公司（AFTEE）。AFTEE可能直接向您蒐集電話號碼及其他個人資料。',
      contents: [
        '蒐集目的：處理與提供購買人與AFTEE提供之「 超商付款/銀行轉賬 」服務間各項往來業務、帳戶或服務，以完成 KLOOK購物結帳流程。',
        '蒐集個人資料之類別：用戶ID。',
        '利用期間：購買人使用「 超商付款/銀行轉賬  」服務存續期間。',
        '利用地區：恩沛科技股份有限公司或受其委託處理事務廠商所在地。',
        '利用對象：恩沛科技股份有限公司及其國內、外分支機構或受其委託處理事務廠商。',
        '利用方式：符合個人資料相關法令之蒐集、處理、國際傳輸與利用例如使用電子文件、紙本或其他合於當時科學技術之適當方式。',
        '依據個人資料保護法第三條規定，購買人就本人之個人資料得行使下列權利：'
      ],
      sub_contents: [
        '查詢或請求閱覽。',
        '請求製給複製本。',
        '請求補充或更正。',
        '請求停止蒐集、處理或利用。',
        '請求刪除。'
      ]
    }
  ]
}
</script>

<style lang="scss">
// 设置在 html 上用于禁止页面滚动
.klk-lock-body-scroll {
  body {
    position: fixed !important;
    height: 100% !important;
    right: 0;
    left: 0;
  }
}
</style>

<style scoped lang="scss">
ol,
p,
h3 {
  padding: 0;
  margin: 0;
}

ol {
  padding-left: 16px;
}

ul {
  padding-left: 32px;
  margin: 0;
}

.aftee-terms-wrap {
  margin: 20px 8px;
  background-color: $color-bg-widget-normal;
  font-size: $fontSize-body-s;
  color: $color-text-primary;
  font-weight: $fontWeight-regular;
  line-height: 20px;

  .terms-step {
    font-weight: $fontWeight-semibold;
    font-size: $fontSize-body-s;
  }

  .terms-step-mobile-wrap {
    margin: 2px 0 22px 0;
    display: flex;
    justify-content: space-between;

    .step-mobile-item {
      width: 100px;
      color: $color-text-primary;

      .step-mobile-item-img {
        width: 64px;
        height: 60px;
        margin: 0 auto;
        background-size: cover;
      }

      .step-mobile-item-semibold {
        margin-top: 5px;
        font-weight: $fontWeight-semibold;
        font-size: $fontSize-caption-m;
        text-align: center;
        line-height: 16px;
      }

      .step-mobile-item-regular {
        margin-top: 2px;
        font-size: $fontSize-caption-s;
        text-align: center;
        line-height: 14px;
      }
    }

    .step-mobile-item-line {
      margin-top: 22px;
      width: 0;
      height: 0;
      border-top: 12px solid transparent;
      border-left: 18px solid $color-caution-background;
      border-bottom: 12px solid transparent;
    }
  }

  .terms-step-desktop-wrap {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    margin-bottom: 20px;
    margin-top: 8px;

    .step-desktop-item {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .step-desktop-item-img {
        width: 64px;
        height: 60px;
        background-size: cover;
      }

      .step-desktop-item-content {
        margin-left: 15px;
        color: $color-text-primary;

        .step-desktop-item-semibold {
          font-weight: $fontWeight-semibold;
          font-size: $fontSize-caption-m;
          line-height: 16px;
        }

        .step-desktop-item-regular {
          margin-top: 2px;
          font-size: $fontSize-caption-s;
          line-height: 14px;
        }
      }
    }

    .step-desktop-item-line {
      margin-left: 37px;
      margin-right: 43px;
      width: 0;
      height: 0;
      border-top: 12px solid transparent;
      border-left: 18px solid $color-caution-background;
      border-bottom: 12px solid transparent;
    }
  }

  ol,
  ul {
    li {
      list-style: auto;
    }

    list-style-type: decimal;
  }

  .terms-content-wrap-lower li {
    list-style-type: lower-alpha;
  }

  h3 {
    font-size: $fontSize-body-s;
    font-weight: $fontWeight-semibold;
    align-items: center;
  }

  .terms-title {
    font-weight: $fontWeight-bold;
    line-height: 20px;
  }

  .service-wrap {
    padding-top: 12px;

    .terms-content-wrap {
      margin-bottom: 20px;
      align-items: center;

      .terms-content-wrap-title {
        font-weight: $fontWeight-semibold;
      }
    }
  }
}
</style>
