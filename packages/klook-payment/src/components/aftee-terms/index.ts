import type { CreateElement } from 'vue'
import showBottoSheet from '../../utils/show-bottom-sheet'

export default async function showAfteeTerms(this: Vue, isMobile = false) {
  try {
    const { default: AfteeTerms } = await import('./content.vue')

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const content = (h: CreateElement) => h(AfteeTerms, { props: { isMobile } })
    const title = '付款說明與使用條款' // newweb 迁移过来的代码就是写死的

    isMobile
      ? showBottoSheet.call(this, { title }, { default: content })
      : this.$alert(
        content,
        title,
        {
          width: '640px',
          scrollable: true,
          lockScroll: true,
          closable: true
        }
      )
  } catch {
    this.$alert(this.$t('pay.card.error.tip2'))
  }
}
