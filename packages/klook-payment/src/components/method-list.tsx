import { Vue, Component, Prop, Inject, Provide, Watch } from 'vue-property-decorator'
// @ts-ignore
import But<PERSON> from '@klook/klook-ui/lib/button'
// @ts-ignore
import BottomSheet from '@klook/klook-ui/lib/bottom-sheet'
import type { Method, SettlementParams } from '../types.d'
import { Creditcard } from '../utils/params-manager'

import MethodItem from './method-item'
import { ToggleViewAll, ViewAllSwitch, MobileMethodSnapshot } from './method-item/modules'

// css部分 在 'src/index.scss'

const matchCurrentMethod = (method: Method, currentKey: string, isSub = false): undefined | Method => {
  if (method.method_key === currentKey) {
    return method
  } else if (!isSub && method.action !== 'select' && method.sub_options?.length) {
    for (const subMethod of method.sub_options) {
      // 这里只遍历两层， 已废弃的dragonpay 是三层数据结构
      const matchedMethod = matchCurrentMethod(subMethod, currentKey, true)

      if (matchedMethod) {
        return matchedMethod
      }
    }
  }
}

@Component({
  inheritAttrs: false,
  name: 'desktop-method-list'
})
export class DesktopMethodList extends Vue {
  @Prop({ type: Array, default: () => [] }) methodList!: Method[]
  @Prop({ type: String }) currentKey!: string
  @Inject() isMobile!: () => boolean
  @Inject() getPaymentType!: () => string

  get ihtViewAllPaymentAttrs() {
    return {
      'data-spm-module': `View_All_Payment_Method?ext=${encodeURIComponent(JSON.stringify({
        PaymentType: this.getPaymentType() === 'paylater' ? 'PayLater' : 'PayNow'
      }))}`,
      'data-spm-virtual-item': '__virtual'
    }
  }

  get ISMOBILE() {
    return this.isMobile()
  }

  get viewAllText() {
    return this.$t('173899')
  }

  get methodIconLimit() {
    // 能进入这个计算属性，  this.methodList 的length 一定是 > 0 的
    return this.ISMOBILE
      ? 3
      : this.methodList.length > 1
        ? 5
        : 0
  }

  getChildrenVnodes(currentKey = this.currentKey) {
    const iconsList: string[] = []
    // 已选中的支付方式
    let currentMethod!: ReturnType<typeof matchCurrentMethod>
    const isVirtual = currentKey !== this.currentKey

    const methodListVodes = this.methodList.map((method, idx) => {
      // 在循环中剔出被选中的支付方式(也可以是当前支付方式的子支付方式)
      let loopCurrentMethod!: typeof currentMethod

      if (!currentMethod) {
        loopCurrentMethod = matchCurrentMethod(method, currentKey)
      }

      if (loopCurrentMethod) {
        currentMethod = loopCurrentMethod
      } else if (iconsList.length < this.methodIconLimit && method.icons?.[0]) {
        // 已选中的支付方式 不能出现在 查看更多的图标集合里
        iconsList.push(method.icons[0])
      }

      return <MethodItem
        currentMethodKey={loopCurrentMethod && loopCurrentMethod.method_key}
        originalCurrentMethodKey={isVirtual ? this.currentKey : undefined}
        key={method.method_key}
        method={method}
        ihtProps={{ idx, len: this.methodList.length }}
        style={{ order: method.manualOrder || idx }}
      >
        {
          method.method_key === Creditcard && [
            <template slot="extra-append">
              {this.$slots['cashier-top']}
            </template>,
            !this.ISMOBILE && <template slot="label-right">
              {this.$slots['supported-cards']}
            </template>
          ]
        }
      </MethodItem>
    })

    if (isVirtual) {
      return { methodListVodes, currentMethod }
    } else {
      // 提交到main.vue, 单相数据流，没有响应式，只能从中取数据，不能用于render
      this.$emit('commit-current-method', currentMethod)
      // 通过一次map 整合出需要render的结果
      return {
        methodListVodes,
        currentMethod,
        iconsListVnodes: iconsList.map((src, index) => <img
          class="payment-type-icon"
          height="24"
          key={index}
          src={src}
        />)
      }
    }
  }

  render() {
    if (!this.methodList?.length) {
      return
    }
    const { methodListVodes, iconsListVnodes } = this.getChildrenVnodes()

    return <ToggleViewAll
      class="method-list"
      viewAllText={ this.viewAllText }
      disabled={!this.methodIconLimit}
      ihtAttrs={ this.ihtViewAllPaymentAttrs }
    >
      <template slot="icons">{iconsListVnodes}</template>
      {methodListVodes}
    </ToggleViewAll>
  }
}

// MobileMethodList 作用是拦截各个子支付方式提交的数据暂存在浮层组件中，点击按钮后同步到根组件
@Component({
  inheritAttrs: false,
  name: 'mobile-method-list'
})
export class MobileMethodList extends DesktopMethodList {
  @Inject({ from: 'updateSettlement' }) submitSettlement!: (params?: SettlementParams) => void
  @Prop({ type: Function }) verifyMethod!: (method: this['selectedMethod']) => Promise<void>

  @Provide()
  layerVisible() {
    return this.isViewAll
  }

  @Provide()
  updateSettlement(params: SettlementParams, rightnowCommit?: boolean) {
    this.layerSelectedOptions = params
    rightnowCommit && this.commitSettlement(false)
  }

  // 当重新请求接口获取新的methodList， 需要把临时选中的参数置空
  @Watch('methodList')
  onMethodListChange() {
    this.layerSelectedOptions = null
  }

  // 在浮层内部临时选中的支付方式和优惠券
  layerSelectedOptions: SettlementParams | null = null
  isViewAll = false
  selectedMethod?: Method

  toggleShowAll(val = !this.isViewAll) {
    this.isViewAll = val
  }

  // updateSettlement rightnowCommit 为true的调用 不校验参数， 主要是针对特殊场景，如优惠券选择和删除钱包的操作
  async commitSettlement(needVerifyMethod = true) {
    if (needVerifyMethod) {
      try {
        await this.verifyMethod(this.selectedMethod)
      } catch {
        return
      }
    }

    this.isViewAll = false
    this.layerSelectedOptions && setTimeout(this.submitSettlement, 300, this.layerSelectedOptions)
  }

  get childrenCache() {
    return this.getChildrenVnodes()
  }

  onOpenLayer() {
    this.isViewAll = true
    setTimeout(() => {
      const currentMethodEl = document.querySelector(`.payment-radio-wrapper[data-method-type="${this.currentKey}"]`)
      currentMethodEl && currentMethodEl.scrollIntoView({
        behavior: 'smooth'
      })

      // eslint-disable-next-line no-unused-expressions
      this.$inhouse?.track('pageview', (this.$refs.layer as Vue).$el, { force: true })
    }, 300)
  }

  onCloseLayer() {
    this.isViewAll = false
    // eslint-disable-next-line no-unused-expressions
    this.$inhouse?.track('custom', (this.$refs.layer as Vue).$el, {
      spm: 'PaymentMethod_SelectPaymentMethod.PaymentMethod_Close'
    })
  }

  get confirmPaymentMethodEXT() {
    const PaymentMethod = this.layerSelectedOptions?.method_key || this.currentKey || ''

    return PaymentMethod && `?ext=${encodeURIComponent(JSON.stringify({ PaymentMethod }))}`
  }

  render() {
    if (!this.methodList?.length) {
      return
    }

    // eslint-disable-next-line prefer-const
    let { methodListVodes, iconsListVnodes, currentMethod } = this.childrenCache
    this.selectedMethod = currentMethod!
    // 当浮层里的选择项目发生变化，只需更新methodListVodes
    this.layerSelectedOptions && this.layerSelectedOptions.method_key !== this.currentKey &&
      ({ currentMethod: this.selectedMethod, methodListVodes } = this.getChildrenVnodes(this.layerSelectedOptions.method_key))

    return <div class="view-more-layer">
      <BottomSheet
        height="90%"
        visible={this.isViewAll}
        showClose
        maskClosable={ false }
        canPullClose={ false }
        title={this.$t('176511')}
        ref="layer"
        data-spm-page="PaymentMethod_SelectPaymentMethod?trg=manual"
        onClose={this.onCloseLayer}
      >
        <div class="method-list">
          {methodListVodes}
        </div>
        <Button
          slot="footer"
          block
          data-spm-module={`PaymentMethod_ConfirmPaymentMethod${this.confirmPaymentMethodEXT}`}
          data-spm-virtual-item="__virtual"
          onClick={this.commitSettlement}
        >
          { this.$t('176512') }
        </Button>
      </BottomSheet>
      {
        // @ts-ignore
        currentMethod && <MobileMethodSnapshot
          method={currentMethod}
          onOpen-layer={this.onOpenLayer}
        />
      }
      {
        // @ts-ignore
        this.methodIconLimit && <ViewAllSwitch
          viewAllText={ this.viewAllText }
          onClick={this.onOpenLayer}
          ihtAttrs={ this.ihtViewAllPaymentAttrs }
        >
          <template slot="icons">{iconsListVnodes}</template>
        </ViewAllSwitch>
      }
    </div>
  }
}
