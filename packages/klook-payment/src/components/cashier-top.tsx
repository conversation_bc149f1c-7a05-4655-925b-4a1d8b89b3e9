import type { FunctionalComponentOptions, CreateElement } from 'vue'
import type { PaymentInfo, Dialog as DialogType } from '../types.d'
import { DialogTips } from './method-item/modules'

type CashierTopProps = PaymentInfo['global_tips'][0]

const CashierTop: FunctionalComponentOptions<CashierTopProps> = {
  functional: true,
  // @ts-ignore
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  render(h: CreateElement, { props: { content, items } = {} }) {
    if (!content || !items?.length) {
      return content
    }

    const optionMap: Record<string, { classList: string[], dialog: null | DialogType }> = {}
    const spliter = '###'

    const textArr: Array<string | JSX.Element> = (items || []).reduce((
      resText,
      { text, style, action, dialog } = {}
    ) => {
      if (!text || !resText.includes(text) || !(style || action)) {
        return resText
      }
      const options = optionMap[text] = optionMap[text] || { classList: [], dialog: null }

      style && style !== 'default' && options.classList.push(style)

      if (action === 'dialog' && dialog) {
        options.classList.push('underline')
        options.dialog = dialog
      }

      return resText.replace(text, spliter + text + spliter)
    }
    , content
    ).split(/#{3,}/g)

    Object.entries(optionMap).forEach(([text, { classList, dialog }]) => {
      const vNode = <span class={classList}>{text}</span>
      textArr.splice(textArr.indexOf(text), 1, !dialog
        ? vNode // @ts-ignore
        : <DialogTips dialog={dialog}>
          {vNode}
        </DialogTips>
      )
    })

    return <div class="cashier-top" data-spm-module="PaymentCouponTips">
      {textArr}
    </div>
  }
}

export default CashierTop