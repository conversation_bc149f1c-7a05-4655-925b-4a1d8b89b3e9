
import { Component, Prop, Provide, Watch, Vue } from 'vue-property-decorator'
import localStoragManager from '@klook/klk-traveller-utils/lib/localStorage'
import type { IEvent, CheckoutDetail, PaymentInfo, SettlementParams, Method, SettlementExtraInfo } from './types.d'
import { getResEnLang, getLocalization } from './payment-sdk/core/utils/pci'
import { isCnSite } from './payment-sdk/core/utils'
import ParamsManager, { CreditcardNew, Creditcard } from './utils/params-manager'
import { filterNonsupportMethodKes, supportedMethodList } from './utils/supported-method'
import { iconAddNewCard, iconCreditCard } from './assets/base64-svg-icons'
import { cashierConfig, GLOBAL_SENTRY_DSN, CN_SENTRY_DSN } from './assets/apis'
import { HEADERS, realTypeof } from './utils'
import { paymentLogQuery } from './payment-sdk/core/utils/logQuery'

import { name as PackageName, version as PackageVersion } from '../package.json'

let SUPPORTED_METHOD_LIST: string[] = []
const isObject = (obj: any) => realTypeof(obj) === 'object'

@Component
export default class PaymentMixin extends Vue {
  @Prop({ type: Object, default: () => null }) checkoutDetail!: CheckoutDetail
  @Prop({ type: Object, default: null }) settlementExtraInfo!: SettlementExtraInfo
  @Prop({ type: Function, default: () => {} }) getBasicCouponList!: Function
  @Prop({ type: Function, default: () => {} }) redeemBasicCoupon!: Function
  @Prop({ type: String, default: 'hotel' }) businessLine!: string
  @Prop({ type: Boolean, default: false }) isScop!: boolean // Simple Checkout  目前 hotel 和 CR 有接入

  @Prop({
    type: String,
    default: 'mobile',
    validator: (val: string) => ['desktop', 'mobile'].includes(val)
  }) platform!: 'desktop' | 'mobile'

  @Provide()
  @Prop({ type: String, default: 'zh-CN' }) lang!: string

  @Provide() // 因为hotel 结算页支持响应式， 所以组件支持的platform 也需要动态的变化
  isMobile() { return this.platform === 'mobile' }

  @Provide()
  getPaymentType() { return this.paymentInfo?.payment_type }

  @Provide()
  getCurrentCouponCode() {
    try {
      return this.paymentInfo?.asset_voucher_info.list.payment_coupon[0].code
    } catch {
      return ''
    }
  }

  subSentry: any

  captureSentryEvent(event: IEvent) {
    if (!this.subSentry) {
      return console.log(event)
    }
    // 当异常发生时自动将异常信息搭载面包屑上
    this.subSentry.addBreadcrumb({
      message: event.message,
      level: event.level,
      type: 'error',
      data: event.extra // 嵌套层级不支持超过三层
    })
    let status = null
    if (['error', 'fatal'].includes(event.level)) {
      status = 'internal_error'
    }
    this.subSentry.reportEvent(event.message, event.tags, status, event)
    // 当发生前端异常上报时，打入全局tag
    if (event.tags?.action !== 'api') {
      this.subSentry.configureScope({
        is_blocked: 1,
        blocked_type: event.message
      })
    }
  }

  @Provide()
  getKlkCouponOptions() {
    return {
      lang: this.lang,
      platform: this.platform, // 有可能会变化
      currency: this.paymentInfo!.price_info.price.currency,
      getBasicCouponList: this.getBasicCouponList,
      redeemBasicCoupon: this.redeemBasicCoupon
    }
  }

  @Provide() // 这是个固定数据无需动态变化
  creditcardOptions = {
    cardTypeRules: [] as { version?: number } & any[],
    localization: {},
    resEnLang: getResEnLang(this.lang) || 'en'
  }

  paymentInfo: PaymentInfo | null = null
  paramsManager = new ParamsManager()
  nonUsablePaymentError = false

  /* @beforeSettlementMethodKey 这一串逻辑是为了防止非用户选中银行卡支付的情况，后端返回的价格试算结果为选中新卡的情况下
    支付组件调用PCI 'reselect' 事件导致页面自动滚动到支付组件位置，导致了不符合预期的交互。
      beforeSettlementMethodKey 就是记录了结算前用户选择的支付方式， 在结算结果回来比较来确定是否是用户主动操作选中新卡。
      清空是为了防止支付组件之外的价格试算对判断产生的干扰
    @TODO new-web独立收银台的交互和合并结算页不同， 接支付组件需要特殊处理
  */
  beforeSettlementMethodKey = '' // 请求价格试算接口前用户主动选择的支付方式，结算结果回来后就置空
  currentMethod?: Method // 从MethodList组件 render过程中传来，不能有响应式，只能读取数据，不能参与render

  @Provide()
  getMethodForm(methodKey: string, initModel?: Record<string, string>, verify?: Function): Record<string, any> | null {
    const observerObj = Object.prototype.hasOwnProperty.call(this.paramsManager, methodKey)
      ? this.paramsManager[methodKey]
      : isObject(initModel) 
        ? this.$set(this.paramsManager, methodKey, initModel)
        : null
    // defineProperty 手动添加 get 类型的 verify 校验函数，
    if (isObject(observerObj) && typeof verify === 'function') {
      Object.defineProperty(observerObj, 'verify', {
        configurable: true,
        get() { return verify }
      })
    }

    // 根据initModel传参不用，获得的是undefined 或一个带响应式的对象，可能还包含子组件上传的verify 方法
    return observerObj
  }

  // 没有传参代表以当前支付方式清空优惠券刷新
  @Provide()
  updateSettlement(params: SettlementParams) {
    params = params || {
      method_key: this.paymentInfo?.default_method_key,
      coupon_code: '',
      auto_select_coupon: true
    }
    const checkoutParams = this.getCheckoutParams(undefined, params)
    const { credit_card_info, method_key } = checkoutParams.checkout_params

    // eslint-disable-next-line no-unused-expressions
    this.subSentry?.addBreadcrumb({
      message: `[change pay method] - ${method_key}`
    })

    // 在没有校验好卡的时候手动选中   不需要调用接口
    if (method_key === CreditcardNew && !credit_card_info?.is_new) {
      this.paymentInfo!.default_method_key = CreditcardNew
      const creditcardMethod = !this.isMobile() &&
        this.paymentInfo!.methods.find(method => method.method_key === Creditcard)
      // 手动添加的，一定不为空。开启desktop 新卡支付方式的newCardManualOptions.autoFocusable
      creditcardMethod && this.$set(
        creditcardMethod.sub_options.find(method => method.method_key === CreditcardNew)!.newCardManualOptions!,
        'autoFocusable',
        true
      )
      return
    }

    this.beforeSettlementMethodKey = method_key

    //  暴露给业务线的事件
    this.$emit('change', checkoutParams)
  }

  //  暴露给业务线的API
  getCheckoutParams(
    selectedCouponList?: { finance_type: string, code: string }[],
    selectedParams?: SettlementParams
  ) {
    selectedParams = selectedParams || {
      method_key: this.paymentInfo?.default_method_key || '',
      auto_select_coupon: true
    }

    if (Array.isArray(selectedCouponList) && (selectedCouponList.length > 0 || this.paymentInfo)) {
      Object.assign(selectedParams, {
        coupon_code: selectedCouponList.find(coupon => coupon?.finance_type === 'payment_coupon')?.code,
        auto_select_coupon: false
      })
    }

    return {
      payment_type: this.paymentInfo?.payment_type,
      headers: HEADERS,
      checkout_params: Object.assign({
        supported_method_list: SUPPORTED_METHOD_LIST
      }, selectedParams, this.paramsManager[selectedParams.method_key!])
    }
  }

  get cashierTop() {
    return (this.paymentInfo!.global_tips || []).find(item => item?.type === 'cashier_top')
  }

  get isZeroPayment() {
    return this.paymentInfo?.special_scenes?.type === 'non_need'
  }

  get paylaterChooseDesc() {
    return this.settlementExtraInfo?.tip?.find(({ key }) => key === 'paylater_desc_for_choose')?.text
  }

  changePaymentType(payment_type: PaymentInfo['payment_type']) {
    this.$emit('change', {
      payment_type,
      headers: HEADERS,
      checkout_params: {
        supported_method_list: SUPPORTED_METHOD_LIST,
        auto_select_coupon: true
      }
    })
  }

  checkConfig() {
    // 获取收银台配置
    const { length, version } = this.creditcardOptions.cardTypeRules
    const { config_version_code } = this.checkoutDetail
    if (!length) {
      const localData = localStoragManager.getItem('card_type_rules')
      let configCache = null
      if (localData) {
        try {
          configCache = JSON.parse(localData)
        } catch {}
      }
      if (configCache && configCache.card_type_rules?.length && configCache.version === config_version_code) {
        this.creditcardOptions.cardTypeRules = configCache.card_type_rules
        this.creditcardOptions.cardTypeRules.version = configCache.version
      } else {
        this.getConfig()
      }
    } else if (version !== config_version_code) {
      this.getConfig()
    }
  }

  @Watch('checkoutDetail', { immediate: true })
  initPaymentInfo(newCheckoutDetail: CheckoutDetail, oldCheckoutDetail?: CheckoutDetail) {
    if (!newCheckoutDetail?.payment_info) {
      return
    }

    this.checkConfig()

    // 深拷贝数据   防备数据存放在store 中
    const paymentInfo = JSON.parse(JSON.stringify(newCheckoutDetail.payment_info)) as PaymentInfo

    const cashierToast = (paymentInfo?.global_tips || []).find(item => item?.type === 'cashier_toast')

    if (cashierToast?.content) {
      this.$alert(cashierToast.content)
    }

    const methodList = paymentInfo.methods = (paymentInfo.methods || []).filter(filterNonsupportMethodKes) || []
    if (methodList.length) {
      // 因unionpay & creditcard使用了PCI 因而DOM 置前，以防止Dom结构变化导致的意外刷新。并手动排序还原原位置
      const [, creditcardMethod] = ['unionpay', Creditcard].map((key) => {
        const index = methodList.findIndex(method => method?.method_key === key)
        if (index > -1) {
          const payMethod = methodList[index]
          // 记录其后端返回的排序位置
          payMethod.manualOrder = index + 1
          return payMethod
        }
      }).map((payMethod) => {
        if (payMethod) {
          // 把位置转移到最前方， 这这个操作中， index 是会变化的
          methodList.splice(methodList.indexOf(payMethod), 1)
          methodList.unshift(payMethod)
          return payMethod
        }
      })

      // creditcard 需要手动处理数据
      if (creditcardMethod) {
        const creditcardSubOptions = creditcardMethod.sub_options = creditcardMethod.sub_options || []

        // desktop 的信用卡图标需要手动添加，PS: iconCreditCard 是mobile platform 下后端返回的图标
        creditcardMethod.icons = [iconCreditCard]

        const {
          supported_cards,
          new_card_method_tips,
          card_info_extend,
          new_card_info: { lianlianpay_public_key: publicKey } = {}
        } = paymentInfo

        const creditcardNewMethod = {
          method_key: CreditcardNew,
          name: this.$t('pay.add.cards'),
          action: 'select',
          icons: [iconAddNewCard],
          // WARNING: 这里的参数是手动添加进去新卡的一些配置
          newCardManualOptions: {
            supportedCards: supported_cards || { types: [] },
            autoFocusable: false,
            isScop: this.isScop,
            publicKey,
            cardInfoExtend: card_info_extend
          }
        } as Method

        if (paymentInfo.default_method_key === Creditcard) {
          paymentInfo.default_method_key = CreditcardNew
          if (this.beforeSettlementMethodKey === Creditcard) {
            this.beforeSettlementMethodKey = CreditcardNew
          }
        } else if (paymentInfo.default_method_key === CreditcardNew) { // 已经输入完成的新卡会进入此判断
          const { content } = new_card_method_tips || {}
          content && Object.assign(creditcardNewMethod, {
            payment_coupon_code: this.getCurrentCouponCode(),
            model_list: [{
              type: 'payment_coupon',
              content
            }]
          })
        }

        creditcardNewMethod.newCardManualOptions!.autoFocusable = this.isMobile() ||
          [this.beforeSettlementMethodKey, paymentInfo.default_method_key]
            .every(methodKey => methodKey === CreditcardNew)

        // 手动添加的新卡选项
        creditcardSubOptions.push(creditcardNewMethod)
      }

      const oldPaymentType = oldCheckoutDetail?.payment_info?.payment_type

      // 由于PCI 在每次切换PaymentTyp都会重置， 这里为了同步也只能重置ParamsManager
      if (oldPaymentType && paymentInfo.payment_type !== oldPaymentType) {
        this.paramsManager = new ParamsManager()
      }
    } else {
      this.paramsManager = new ParamsManager()
      paymentInfo.default_method_key = ''
    }

    this.beforeSettlementMethodKey = ''
    this.paymentInfo = paymentInfo
  }

  async getConfig() {
    // 初始化收银台接口(获取收银台配置)
    const res = await this.$axios.$get(cashierConfig, { headers: HEADERS }).catch((error: Error) => ({ error }))

    if (res.success && res.result) {
      const { result } = res

      if (result.card_type_rules?.length) {
        this.creditcardOptions.cardTypeRules = result.card_type_rules

        if (result.version !== this.checkoutDetail.config_version_code) {
          result.version = this.checkoutDetail.config_version_code
        }

        localStoragManager.setItem('card_type_rules', JSON.stringify(result))
        this.creditcardOptions.cardTypeRules.version = result.version
      }
    } else {
      this.captureSentryEvent({
        message: '[api] - cashier-config | request-error',
        level: 'error',
        fingerprint: ['klook-payment | ' + this.businessLine, 'api', 'main.tsx'],
        tags: {
          module: 'cashier-config',
          action: 'api',
          code: res.error?.code,
          path: 'mixin.ts@getConfig',
          message: res.error?.message || 'request api<cashier/config> error'
        }
      })

      paymentLogQuery({
        action: 2,
        api_name: 'config',
        file_name: 'mixin.tsx',
        requestId: HEADERS['X-Klook-Request-Id'],
        message: '请求 API<cashier/config> 失败，错误信息：' + (
          res?.error
            ? JSON.stringify(res.error)
            : 'NA'
        )
      })

      this.$alert(
        this.$t('12346'),
        this.$t('111123'),
        { okLabel: this.$t('171709') }
      ).then(this.getConfig)
    }
  }

  async beforeMount() {
    let kvConfig: { sampleRate: number, tracesSampleRate: number } | undefined

    try {
      kvConfig = JSON.parse(process.env.CLIENT_PAYMENT_SDK_CONFIG || '{}')[this.businessLine]
    } catch {}

    this.subSentry = (this.$sentry || window.$sentry)?.create({
      dsn: isCnSite.call(this) ? CN_SENTRY_DSN : GLOBAL_SENTRY_DSN,
      release: `${this.businessLine}/${PackageName}@${PackageVersion}`,
      environment: `env-${process.env.APP_ENV || 'unknow'}-${PackageVersion}`,
      sampleRate: 0.005,
      tracesSampleRate: 0.005,
      ...kvConfig
    })

    // eslint-disable-next-line no-unused-expressions
    this.subSentry?.startPageTransaction('Klook_Payment_SDK')

    if (supportedMethodList.isInit) {
      const { default: initCheckout } = await import('./utils/init-checkout')
      await initCheckout.call(this)
    }

    SUPPORTED_METHOD_LIST = supportedMethodList.value
    this.creditcardOptions.localization = getLocalization.call(this)
  }
}
