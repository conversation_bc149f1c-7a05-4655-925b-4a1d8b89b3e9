{"name": "@klook/klook-payment", "version": "0.2.3", "description": "Payment Component based on Vue of Klook", "author": "<PERSON><PERSON><PERSON>", "homepage": "https://design.klook.io", "keywords": ["vue", "component", "ui", "framework"], "main": "lib/index.js", "module": "esm/index.js", "typings": "types/index.d.ts", "files": ["lib", "esm", "types"], "license": "UNLICENSED", "publishConfig": {"registry": "https://knpm.klook.io", "access": "public"}, "scripts": {"build": "NODE_ENV=production rollup --config ./rollup.config.js", "lint": "NODE_ENV=production eslint --ext .js,.vue src", "test": "NODE_ENV=test jest -i --updateSnapshot", "test:coverage": "NODE_ENV=test jest -i --coverage --updateSnapshot", "prepush": "yarn run lint", "prepublishOnly": "bash prepublishOnly.sh", "commit": "npx git-cz", "commitmsg": "commitlint -E GIT_PARAMS"}, "devDependencies": {"@babel/core": "^7.17.9", "@babel/plugin-proposal-class-properties": "^7.16.7", "@babel/plugin-proposal-decorators": "^7.17.9", "@babel/plugin-transform-runtime": "^7.17.0", "@babel/preset-env": "^7.16.11", "@babel/preset-typescript": "^7.9.0", "@types/jest": "^26.0.0", "@types/lodash": "4.14.149", "@types/qs": "^6.9.5", "@types/uuid": "3.4.6", "@types/webpack-env": "^1.14.0", "@vue/babel-helper-vue-jsx-merge-props": "^1.2.1", "@vue/babel-preset-jsx": "^1.2.4", "@vue/cli-plugin-babel": "^4.3.1", "@vue/test-utils": "^1.0.0-beta.32", "autoprefixer": "^8.0.0", "babel-helper-vue-jsx-merge-props": "^2.0.3", "babel-plugin-syntax-jsx": "^6.18.0", "babel-plugin-transform-vue-jsx": "^4.0.1", "babel-preset-env": "^1.7.0", "cssnano": "^5.1.8", "jest": "^25.5.4", "postcss": "^8.4.13", "postcss-import": "^12.0.1", "postcss-loader": "^6.2.1", "rimraf": "^3.0.0", "rollup": "^2.70.1", "rollup-plugin-buble": "^0.19.8", "rollup-plugin-commonjs": "^10.0.2", "rollup-plugin-node-resolve": "^5.2.0", "rollup-plugin-replace": "^2.2.0", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-typescript2": "^0.31.2", "rollup-plugin-vue": "^5.1.9", "terser": "^4.1.3", "ts-jest": "^26.1.0", "ts-node": "^8.10.2", "typescript": "^4.6.3", "vue-class-component": "^7.2.6", "vue-jest": "^3.0.4", "vue-property-decorator": "^8.3.0", "vue-template-compiler": "^2.6.11", "vue-tsx-support": "^3.0.2", "@klook/klk-order-coupon": "^1.5.1", "@klook/klk-traveller-utils": "^1.8.5", "@klook/klook-card": "^0.4.10", "@klook/klook-icons": ">=0.13.1", "@klook/klook-ui": ">=1.38.7", "@klook/logquery": "^4.9.0", "@klook/platform-countdown": "^0.0.5", "@klook/risk-deepknow-sdk": "^2.0.2", "@klook/site-config": "^1.9.0", "axios": "^0.21.4", "cookie": "^0.4.0", "lodash": ">=4.17.1", "qrcode": "^1.5.1", "uuid": ">=3.3.1", "vue": "^2.6.11"}, "peerDependencies": {"@klook/klk-order-coupon": "^1.5.1", "@klook/klk-traveller-utils": "^1.8.5", "@klook/klook-card": "^0.4.10", "@klook/klook-icons": ">=0.13.1", "@klook/klook-ui": ">=1.38.7", "@klook/logquery": "^4.9.0", "@klook/platform-countdown": "^0.0.5", "@klook/risk-deepknow-sdk": "^2.0.2", "@klook/site-config": "^1.9.0", "axios": "^0.21.4", "cookie": "0.4.0", "lodash": ">=4.17.1", "qrcode": "^1.5.1", "uuid": ">=3.3.1", "vue": "^2.6.11", "vue-property-decorator": "^8.3.0"}}