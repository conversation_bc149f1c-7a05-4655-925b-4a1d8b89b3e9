import replace from 'rollup-plugin-replace'
import typescript from 'rollup-plugin-typescript2'
import vue from 'rollup-plugin-vue';
import babel from 'rollup-plugin-babel'
import nodeResolve from 'rollup-plugin-node-resolve'
import commonjs from 'rollup-plugin-commonjs'
import cssnano from 'cssnano'
import postcss from 'rollup-plugin-postcss'
import json from '@rollup/plugin-json';
const autoprefixer = require('autoprefixer')()


const input = [
  './src/utils/get-payment-static-data.ts',
  './src/main.tsx',
  './src/components/method-item/modules/dialog-tips.tsx',
  './src/payment-sdk/core/jsapi.js',
  './src/payment-sdk/core/qr-pay/qr-pay.ts',
  './src/payment-sdk/core/redirect-pay.js',
  './src/utils/init-checkout.ts',
  './src/utils/payment-terms.tsx',
  './src/utils/middleware.js',
  './src/utils/locales.ts',
  './src/index.ts',
]

import clear from 'rollup-plugin-clear'

clear({ targets: ['esm', 'lib'] })

const {
  name,
  peerDependencies,
  version,
  author
} = require('./package.json')

const banner = '/* eslint-disable */'
  /**
  * v${version}
  * (c) ${new Date().getFullYear()} ${author}
  */
const configs = {
  esm: {
    dir: 'esm',
    format: 'esm',
    target: 'es2015',
    env: 'production',
    genDts: true,
  },
  cjs: {
    dir: 'lib',
    format: 'cjs',
    target: 'es2015',
    isSSR: true
  }
}

const externals = Object.keys(peerDependencies)

const genTsPlugin = (configOpts) => typescript({
  useTsconfigDeclarationDir: true,
  tsconfigOverride: {
    exclude: ['**/__tests__', 'test-dts'],
    compilerOptions: {
      target: configOpts.target,
      declaration: configOpts.genDts
    }
  },
  abortOnError: false
})

const genPlugins = (configOpts) => {
  const plugins = [
    nodeResolve({
      extensions: ['.mjs', '.js', '.jsx', '.vue', '.ts', '.tsx']
    }),
    commonjs({
      include: /node_modules/
    }),
    genTsPlugin(configOpts),
    json(),
    babel({
      runtimeHelpers: true,
      exclude: 'node_modules/**',
      extensions: ['.js', '.jsx', '.ts', '.tsx'],
      presets: ["@vue/babel-preset-jsx"]
    }),
    replace({
      'process.env.MODULE_FORMAT': JSON.stringify(configOpts.format)
    }),
    postcss({
      extract: 'index.css',
      plugins: [
        require('postcss-import')({
          path: ['node_modules'] // 指定导入文件搜索路径
        }),
        autoprefixer,
        cssnano()
      ],
      exclude: './src/payment-sdk/core/qr-pay/qr-pay.scss' // 排除目标文件
    }),
    // 新增 PostCSS 配置：单独处理 qr-pay.scss， 不单独打包css
    postcss({
      include: './src/payment-sdk/core/qr-pay/qr-pay.scss', // 仅包含目标文件
      plugins: [
        require('postcss-import')({ path: ['node_modules'] }),
        autoprefixer,
        cssnano()
      ]
    }),
    vue({
      css: false,
      normalizer: '~vue-runtime-helpers/dist/normalize-component.js',
      template: {
        isProduction: true,
        // optimizeSSR: configOpts.isSSR
      },
      style: {
        postcssPlugins: [autoprefixer],
        preprocessOptions: {
          scss: { data: '@import "node_modules/@klook/klook-ui/lib/styles/token/index.scss";' }
        }
      }
    })
  ]
  if (configOpts.env) {
    plugins.push(replace({
      'process.env.NODE_ENV': JSON.stringify(configOpts.env)
    }))
  }
  if (configOpts.plugins && configOpts.plugins.pre) {
    plugins.push(...configOpts.plugins.pre)
  }

  if (configOpts.plugins && configOpts.plugins.post) {
    plugins.push(...configOpts.plugins.post)
  }

  return plugins
}

const genConfig = (configOpts) => ({
  input,
  output: {
    banner,
    dir: configOpts.dir,
    // file: configOpts.output,
    format: configOpts.format,
    name: name,
    sourcemap: false,
    exports: 'named',
    globals: configOpts.globals,
  },
  external: id => id.includes('design-token-esm')
    ? configOpts.format === 'esm'
    : id.indexOf('@klook/') === 0 || externals.includes(id.split('/')[0]),
  plugins: genPlugins(configOpts)
})

const genAllConfigs = (configs) => (Object.keys(configs).map(key => genConfig(configs[key])))

export default genAllConfigs(configs)
