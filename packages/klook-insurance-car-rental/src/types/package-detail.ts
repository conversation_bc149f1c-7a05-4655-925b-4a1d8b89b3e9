export interface TermsAndConditionsInfo {
  title: string;
  infolist: string[];
}

export interface InsurancePolicyWording {
  button_text: string;
  link: string;
}

export interface ClaimGuideInfo {
  button_text: string;
  list: string[];
}

export interface ExcessPriceInfoItem {
  price: string;
  role: string;
}

export interface ExcessPriceInfo {
  desc: string;
  list: ExcessPriceInfoItem[];
}

export interface InsuranceDetail {
  sub_insurance_id: number | string;
  claim_guide_info?: ClaimGuideInfo | null;
  text: string;
  sub_text: string | null;
  price_desc: string;
  is_axa?: boolean;
  insured_source: string | null;
  insured_source_icon?: string | null;
  rental_company_icon?: string | null;
  desc: string;
  excess_price_info: ExcessPriceInfo | null;
  insurance_extend_desc: Record<string, string> | null;
  main_insurance_code?: string;
  sort_id: number;
}

export interface Package {
  terms_and_conditions_info?: TermsAndConditionsInfo | null;
  insurance_policy_wording?: InsurancePolicyWording | null;
  price_value_per_day: number;
  price_per_day: string;
  protection_id?: string | null;
  package_name: string;
  guarantee_strength: number;
  reference_id: string;
  insurance_detail_list: InsuranceDetail[];
  insurance_extend_desc?: Record<string, string> | null;
  recommend_desc?: string;
  see_title?: string | null;
  see_detail?: string | null;
  insurance_package: string | null;
  insurance_company: string | null;
  insurance_plan_code: string[] | null;
}

export interface IPackageDetail {
  package_list: Package[];
  car_id: string | null;
  car_group_code: string | null;
  see_title: string | null;
  see_detail: string | null;
}
