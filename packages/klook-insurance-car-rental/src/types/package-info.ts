export interface PackageInfo {
  title: string;
  list: InsurancePackage[];
}

export interface InsurancePackage {
  is_selected: boolean;
  is_insurance_product: boolean;
  reference_id: string;
  insurance_package: string;
  insurance_package_name: string;
  sort_id: number;
  guarantee_strength: number;
  recommend_desc: string | null;
  min_price: MinPrice;
  has_more_sub_insurance: boolean;
  less_sub_insurance_list: SubInsurance[];
  package_pop: string;
  note: string | null;
  insurance_company: string | null;
  insurance_plan_code: string[] | null;
}

export interface SubInsurance {
  sub_insurance_id: number;
  sub_insurance_name: string;
  excess_price_desc: string | null;
  deposit_price_desc: string | null;
  price_desc: string;
  sort_id: number;
}

export interface MinPrice {
  price_per_day: string;
  day_desc: string;
}
