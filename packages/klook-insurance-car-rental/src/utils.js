const selectText = (editableEl, selectionStart, selectionEnd) => {
  const isIOS = navigator.userAgent.match(/ipad|iPod|iphone/i);
  if (isIOS) {
    const range = document.createRange();
    range.selectNodeContents(editableEl);

    const selection = window.getSelection(); // current text selection
    selection.removeAllRanges();
    selection.addRange(range);
    editableEl.setSelectionRange(selectionStart, selectionEnd);
  } else {
    editableEl.select();
  }
};

export const copyToClipboard = (value) => {
  return new Promise((resolve) => {
    const el = document.createElement('textarea'); // temporary element
    el.value = value;

    el.style.position = 'absolute';
    el.style.left = '-9999px';
    el.readOnly = true; // avoid iOs keyboard opening
    el.contentEditable = 'true';

    document.body.appendChild(el);

    selectText(el, 0, value.length);

    if (document.execCommand('copy')) {
      document.body.removeChild(el);
      resolve(true);
    } else {
      resolve(false);
    }
  });
};

/**
 * 获取反爬脚本配置
 */
export function getAntiSpiderHead(vm) {
  if (vm.$store.state.klook.host === 'www.klook.cn') {
    return {};
  }
  return {
    script: [
      // {
      //   hid: 'PX5J5h9CC2',
      //   innerHTML: '(function(){window._pxAppId="PX5J5h9CC2";var p=document.getElementsByTagName("script")[0],s=document.createElement("script");s.async=1;s.src="https://cdn.klook.com/s/tetris/common/1.0.1/px/int-5J5h9CC2.js";p.parentNode.insertBefore(s,p)})();',
      //   pbody: true
      // }
    ],
  };
}
