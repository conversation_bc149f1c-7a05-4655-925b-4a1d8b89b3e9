<template>
  <div>
    <CardLoading
      v-if="loadingStatus !== 'success' && !hideCard"
      :status="loadingStatus"
      type="block"
      :hide-padding="hideTitle"
      @refresh="fetchData"
    />
    <InsuranceInfoCard
      v-if="loadingStatus === 'success' && !hideCard"
      :is-mobile="isMobile"
      :data="innerData"
      :hide-title="hideTitle"
      @seeDetails="handleSeeDetails"
      @seeClaimService="handleSeeClaimService"
    >
      <div slot="tips">
        <slot name="tips" />
      </div>
    </InsuranceInfoCard>
    <klk-modal
      v-if="isMobile && detailsModalVisible"
      transition="slide-bottom"
      fullscreen
      :open.sync="detailsModalVisible"
      :show-default-footer="false"
      data-spm-module="InsurancePopUP"
      class="insurance-details-modal"
    >
      <ModuleSwitcher :init-data="switchInitData" :init-step="switchInitStep" />
    </klk-modal>
    <klk-drawer
      v-if="!isMobile && detailsModalVisible"
      :visible="detailsModalVisible"
      direction="right"
      class="insurance-details-drawer"
      :mask-closable="false"
      @close="detailsModalVisible = false"
    >
      <ModuleSwitcher :init-data="switchInitData" :init-step="switchInitStep" />
    </klk-drawer>
  </div>
</template>

<script lang="ts">
import { Vue, Component, Prop, Provide, Watch } from "vue-property-decorator";
import ModuleSwitcher from "../module-switcher.vue";
import InsuranceInfoCard from "./card.vue";
import CardLoading from "./card-loading.vue";
import ErrorPanel from "../common/error-panel.vue";

@Component({
  name: "InsuranceDetail",
  components: {
    InsuranceInfoCard,
    CardLoading,
    ModuleSwitcher,
    ErrorPanel,
  },
})
export default class InsuranceDetail extends Vue {
  @Prop({ type: String, default: "mobile" }) platform!: string;
  @Prop({ type: String, default: () => "" }) bookingNo!: string;
  @Prop({ type: Boolean, default: false }) hideTitle!: boolean;
  @Prop({ type: Boolean, default: false }) hideCard!: boolean;

  loadingStatus: string = "loading";
  innerData: any = {};
  api: string =
    "/v1/insuranceapisrv/outer/carrental/booking_detail/get_insurance_coverage_details";
  detailsModalVisible: boolean = false;
  insOrderNo: string = "";
  switchInitData: any = {};
  switchInitStep: string = "";

  get isMobile() {
    return this.platform !== "desktop";
  }

  @Provide()
  closeContainer() {
    this.detailsModalVisible = false;
  }
  @Provide()
  getPlatform() {
    return this.platform;
  }
  @Provide()
  getBookingNo() {
    return this.bookingNo;
  }
  @Provide()
  getInsOrderNo() {
    return this.insOrderNo;
  }

  @Provide()
  getIsApp() {
    return window?.__KLOOK__?.state?.klook?.isKlookApp || false;
  }

  @Provide()
  getLanguage() {
    return window?.__KLOOK__?.state?.klook?.language || "en-US";
  }

  @Provide()
  refreshCard() {
    this.fetchData()
  }

  mounted() {
    this.fetchData();
  }

  handleError(error: any) {
    if (error && error.code === "4001") {
      this.$toast(
        this.$t("2769-profile.bookings.account_logout"),
        this.$t("20-global.tips.header")
      ).then((res: any) => {
        if (res.result) {
          window.location.href = this.$href("/signin");
        }
      });
      return;
    }
    this.$toast(error && error.message);
  }

  fetchData() {
    this.loadingStatus = "loading";
    const params = {
      car_rental_booking_query_info: {
        car_rental_booking_ref_no: this.bookingNo,
      },
    };
    this.$axios.$post(this.api, params).then(
      (res) => {
        if (!res.success || !res.result) {
          this.loadingStatus = "error";
          throw new Error((res.error && res.error.message) || "");
        }
        this.loadingStatus = "success";
        this.innerData = res.result;
        this.insOrderNo = res.result?.insurance_extend_desc?.ins_order_no || "";
      },
      (err) => {
        this.loadingStatus = "error";
      }
    );
  }

  setModalBodyHeight() {
    if (!this.isMobile || this.getIsApp()) return;
    // 设置弹窗内容区域高度
    const modalBody = document.querySelector(".klk-modal-body");
    if (modalBody) {
      modalBody.setAttribute(
        "style",
        `height: ${window.innerHeight};`
      );
    }
  }

  handleSeeDetails() {
    this.switchInitStep = "packageDetails";
    this.switchInitData = {};
    this.detailsModalVisible = true;
    this.$nextTick(() => {
      this.setModalBodyHeight();
    });
  }

  handleSeeClaimService() {
    const hasDraft = this.innerData?.claim_btn_status?.exist_claim_draft;
    if (hasDraft) {
      this.switchInitStep = "claimSubmit";
      this.switchInitData = {
        liability_id_list:
          this.innerData?.claim_btn_status?.liability_id_list || [],
        scenario_id_list:
          this.innerData?.claim_btn_status?.scenario_id_list || [],
        is_draft: true,
      };
    } else {
      this.switchInitStep = "claimRecord";
      this.switchInitData = {};
    }
    this.detailsModalVisible = true;
    this.$nextTick(() => {
      this.setModalBodyHeight();
    });
  }
}
</script>

<style lang="scss" scoped>
.insurance-details-drawer {
  .insurance-detail-content {
    padding: 0 32px;
    padding: 20px 32px;
    width: 750px;
    height: calc(100% - 64px);
  }
}
.insurance-details-modal {
  ::v-deep .klk-modal {
    margin: 0px !important;
    padding: 0px !important;
    overflow: hidden;
  }
  ::v-deep .klk-modal-body {
    height: 100vh;
    // overflow-y: scroll;
    overflow-y: hidden;
  }
  ::v-deep .klk-modal-header {
    padding-left: 20px !important;
    padding-right: 20px !important;
  }
  .insurance-detail-content {
    padding: 14px 20px;
    height: calc(100% - 58px);
    overflow-y: scroll;
  }
}
</style>
