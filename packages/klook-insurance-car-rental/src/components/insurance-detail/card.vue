<template>
  <div
    v-if="data"
    :class="{
      'insurance-info': true,
      mobile: isMobile,
      'insurance-info-hideTitle': hideTitle,
    }"
    :data-spm-module="`InsuranceInfo?oid=packagecode_${data.package_code}&ext=${JSON.stringify(
      {
        PlanCode: data.goods_plan_code_list,
      }
    )}`"
  >
    <div
      v-if="!hideTitle"
      class="insurance-info__title"
    >
      {{ data.title }}
    </div>
    <slot name="tips" />
    <div
      v-galileo-click-tracker="{ spm: 'InsuranceInfo', autoTrackSpm: true }"
      class="insurance-info__sub_title"
      :data-spm-virtual-item="`__virtual?ext=${JSON.stringify({
        btnClick: 'SeeMore',
      })}`"
      @click="handleSeeDetail"
    >
      <span>{{ subTitle }}</span>
      <IconNext
        class="icon"
        theme="outline"
        size="16"
        :fill="tipsNextIconColor"
      />
    </div>
    <div class="insurance-info__list">
      <div
        v-for="(item, index) in list"
        :key="index"
        class="list_item"
      >
        <img
          :src="item.icon"
          class="item_icon"
        >
        <div class="item_left">
          <div class="item_text">
            {{ item.title }}
          </div>
          <div class="item_sub_text">
            {{ item.sub_text }}
          </div>
        </div>
      </div>
    </div>
    <div class="insurance-info__actions">
      <klk-button
        v-for="(action, actionIndex) in actions"
        :key="actionIndex"
        v-galileo-click-tracker="{ spm: action.spm, autoTrackSpm: true }"
        type="outlined"
        size="normal"
        class="insurance-info__actionsButton"
        :data-spm-module="`${action.spm}?trg=manual&oid=mainbooking_id_${data.main_booking_no}&ext=${JSON.stringify({
          BookingID: data.addon_booking_nos,
          PlanCode: data.goods_plan_code_list,
          PackageReferenceID: data.package_ref_id,
          CoveragePackage: data.package_code,
        }
        )}`"
        data-spm-virtual-item="__virtual"
        @click="handleActionsClick(action)"
      >
        {{ action.text }}
      </klk-button>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator';
import { IconNext } from '@klook/klook-icons';
import { $colorTextPrimary } from '@klook/klook-ui/lib/utils/design-token-esm';

@Component({
  components: {
    IconNext,
  },
})
export default class InsuranceInfoCard extends Vue {
  @Prop() data!: any;
  @Prop({ type: Boolean, default: false }) isMobile!: boolean;
  @Prop({ type: Boolean, default: false }) hideTitle!: boolean;

  tipsNextIconColor: string = $colorTextPrimary;

  get actions() {
    return this.data?.actions || [];
  }

  get list() {
    return this.data?.display_insurance_list?.benefit_display_info_list || [];
  }

  get subTitle() {
    return this.data?.display_insurance_list?.insurance_package_title || '';
  }

  get tipsModalContent() {
    return this.data?.package_tips?.popup?.content || [];
  }

  get tipsModalButton() {
    return this.data?.package_tips?.popup?.button || [];
  }

  handleActionsClick(action) {
    if (action.key === 'claimsService') {
      this.$emit('seeClaimService');
    } else if (action.deeplink) {
      window.open(action.deeplink, '_blank');
    }
  }

  getUrlAllParam(url: string) {
    const urlObj = new URL(url);
    const params = new URLSearchParams(urlObj.search);
    const paramsObj = {};
    for (const [key, value] of params.entries()) {
      paramsObj[key] = value;
    }
    return paramsObj;
  }

  handleSeeDetail() {
    this.$emit('seeDetails');
  }
}
</script>

<style lang="scss" scoped>
.insurance-info {
  background: $color-bg-1;
  padding: 20px;
  border-radius: $radius-xl;
  @include font-body-m-regular;
  color: $color-text-primary;

  &__tips {
    display: flex;
    padding: 12px 16px;
    align-items: flex-start;
    gap: 8px;
    align-self: stretch;
    border-radius: $radius-l;
    border: 1px solid rgba(255, 156, 0, 0.3);
    background: $color-caution-background;
    margin-top: 8px;
    cursor: pointer;

    &_left {
      height: 20px;
      width: 20px;
      margin-top: 2px;
    }

    &_right {
      display: flex;
      align-items: center;
      justify-content: space-between;
      flex: 1;

      &_text {
        @include font-paragraph-m-bold;
        color: $color-text-primary;
      }

      &_icon {
        display: block;
        height: 20px;
        width: 20px;
      }
    }
  }

  &__modal {
    &_footer {
      margin-top: 24px;
      display: flex;
      justify-content: flex-end;
      gap: 8px;
    }
  }

  &__sheet {
    &_content {
      margin-top: 20px;
    }
  }

  &__title {
    @include font-heading-s;
    color: $color-text-primary;
  }

  &__sub_title {
    @include font-body-m-regular;
    color: $color-text-primary;
    margin-top: 16px;
    display: flex;
    align-items: center;
    gap: 6px;
    cursor: pointer;

    .icon {
      margin-top: 3px;
    }
  }

  &__list {
    display: flex;

    flex-direction: row;
    flex-wrap: wrap;

    .list_item {
      display: flex;
      align-items: flex-start;
      margin-top: 8px;
      width: 50%;

      .item_icon {
        width: 20px;
        height: auto;
        margin-top: 2px;
      }

      .item_left {
        margin-left: 12px;

        .item_text {
          @include font-body-m-regular;
          color: $color-text-primary;
        }

        .item_sub_text {
          @include font-body-s-regular;
          color: $color-text-secondary;
        }
      }
    }
  }

  &__actions {
    display: flex;
    flex-wrap: nowrap;
    gap: 8px;
    margin-top: 12px;
  }

  &__actionsButton {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.mobile {
  .insurance-info {
    padding: 16px;

    &__title {
      @include font-heading-xs;
    }
  }

  .insurance-info__sub_title {
    justify-content: space-between;
  }

  .insurance-info__actionsButton {
    flex: 1;
  }

  .list_item {
    width: 100%;
  }
}

.insurance-info-hideTitle {
  padding: 0px;
}
</style>
