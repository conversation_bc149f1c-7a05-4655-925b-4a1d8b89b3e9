<template>
  <div :class="[$style.card_loading, 'card_loading', type === 'block' && $style.card_loading_block, hidePadding && $style.card_loading_hidePadding]">
    <div v-if="status === 'loading'">
      <div v-if="type === 'normal'">
        <klk-skeleton-block
          animate
          :height="100"
          width="100%"
        />
      </div>
      <div v-else>
        <klk-skeleton-block
          :height="26"
          width="100%"
        />
        <klk-skeleton-block
          :height="26"
          width="100%"
        />
        <klk-skeleton-block
          :height="26"
          width="50%"
        />
      </div>
    </div>
    <div
      v-else-if="status === 'error'"
      :class="$style.error_container"
      @click="handleRefreshClick"
    >
      <IconRefresh
        theme="outline"
        size="16"
        fill="#212121"
      />
      <div :class="$style.error_tips">
        {{ $t('17604-refresh') }}
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import { IconRefresh } from '@klook/klook-icons';

@Component({
  components: {
    IconRefresh,
  }
})
export default class CardLoading extends Vue {
  @Prop({ type: String, default: () => 'normal' }) type!: string; // 'normal' 'block'
  @Prop({ type: String, default: () => 'loading' }) status!: string; // 'loading' 'error' 'success'
  @Prop({ type: Boolean, default: false }) hidePadding!: boolean;

  handleRefreshClick() {
    this.$emit('refresh');
  }
}
</script>

<style lang="scss" module>
.card_loading {
  width: 100%;
  height: 140px;
  padding: 20px;
  background-color: $color-bg-1;
  border-radius: $radius-xl;
  .error_container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    cursor: pointer;
    .error_tips {
      @include font-body-s-regular;
      color: #212121;
      margin-left: 4px;
      text-decoration: underline;
    }
  }
}
.card_loading_block {
  height: 192px;
}
.card_loading_hidePadding {
  padding: 0;
}
</style>

<style lang="scss" scoped>
@keyframes loading-animate {
  0% {
    opacity: 0.4;
  }
  100% {
    opacity: 1;
  }
}
.card_loading {
  ::v-deep .klk-skeleton-comp {
    background-color: $color-bg-5;
    animation: .8s linear 0s infinite alternate none running loading-animate;
  }
}
</style>
