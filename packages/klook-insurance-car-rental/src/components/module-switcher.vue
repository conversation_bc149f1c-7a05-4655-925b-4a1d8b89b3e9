<template>
  <div class="insurance-module-switcher">
    <Component
      :is="modulesMap[step]"
      :init-data="innerData"
      :step-stack="stepStack"
      @setStep="handleSetStep"
      @back="handleBackLastStep"
      @setStepStack="handleSetStepStack"
    />
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop, Inject } from "vue-property-decorator";
import modulesMap from "./modules/index.js";

@Component({
  components: {},
})
export default class ModuleSwitcher extends Vue {
  @Prop({ type: Object, default: () => {} }) initData!: any;
  @Prop({ type: String, default: "" }) initStep!: string;

  @Inject() getPlatform!: () => String;
  @Inject() getIsApp!: () => String;
  @Inject() getBookingNo!: () => String;
  @Inject() closeContainer!: Function;

  step: string = "";
  stepStack: { name: string; data: any }[] = [];
  modulesMap: any = modulesMap;
  innerData: any = this.initData;

  mounted() {
    this.handleSetStep({
      newStep: this.initStep,
      data: this.initData,
    });
  }

  handleBackLastStep(arg?: any) {
    this.stepStack.pop();
    const lastStep = this.stepStack[this.stepStack.length - 1];
    if (lastStep) {
      this.step = lastStep.name;
      this.innerData = {
        ...lastStep.data,
        ...arg
      };
    }
  }

  handleSetStep({ newStep, data }: { newStep: string; data: any }) {
    const newData = {
      ...this.innerData,
      ...data,
    };
    const stepItem = {
      name: newStep,
      data: newData,
    };
    this.stepStack.push(stepItem);
    this.step = newStep;
    this.innerData = newData;
  }

  handleSetStepStack(newStack: any[]) {
    this.stepStack = newStack;
  }
}
</script>

<style lang="scss" scoped></style>
