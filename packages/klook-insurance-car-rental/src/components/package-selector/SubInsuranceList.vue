<template>
  <div class="sub-insurance-list">
    <div
      v-for="(subItem, subIndex) in subItems"
      :key="subIndex"
      class="sub-insurance-item"
    >
      <IconProtect
        theme="outline"
        size="20"
        :fill="tokens['$color-success']"
      />
      <div class="sub-insurance-content">
        <p>{{ subItem.sub_insurance_name }}</p>
        <span v-html="formatExcess(subItem)" />
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import { tokens } from '@klook/klook-ui/lib/utils/design-token';
import { IconProtect } from '@klook/klook-icons';
import { SubInsurance } from '@/types/package-info';

@Component({
  components: {
    IconProtect,
  },
})
export default class SubInsuranceList extends Vue {
  @Prop({ type: Array, required: true }) subItems!: SubInsurance[];

  tokens: any = tokens;

  /**
   * 格式化保险超额描述文本
   * @param item SubInsurance对象
   * @returns 格式化后的HTML字符串
   */
  formatExcess(item: SubInsurance): string {
    const { excess_price_desc, price_desc, deposit_price_desc } = item;

    if (!excess_price_desc) {
      return deposit_price_desc || '';
    }

    const formattedExcess = price_desc && excess_price_desc
      ? excess_price_desc.replace(price_desc, `<strong>${price_desc}</strong>`)
      : excess_price_desc;

    return deposit_price_desc
      ? `${formattedExcess} ${deposit_price_desc}`
      : formattedExcess;
  }
}
</script>

<style lang="scss" scoped>
.sub-insurance-item {
  display: flex;
  align-items: start;
  gap: 12px;
}

.sub-insurance-content {
  p {
    @include font-body-s-bold-v2;
  }

  span {
    @include font-paragraph-xs-regular;
    color: $color-text-secondary;

    ::v-deep strong {
      color: $color-brand-primary;
    }
  }
}
</style>
