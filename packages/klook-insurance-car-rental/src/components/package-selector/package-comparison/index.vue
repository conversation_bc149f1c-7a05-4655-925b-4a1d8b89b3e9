<template>
  <component
    :is="modalComponent"
    ref="modal"
    :fullscreen="platform === 'mobile'"
    :[openProp].sync="syncVisible"
    :direction="platform === 'mobile' ? undefined : 'right'"
    :show-default-footer="false"
    :data-spm-page="`Insurance_CarRentalPlanComparison?trg=manual&ext=${trackData}`"
  >
    <component :is="contentComponent">
      <div
        :class="['package-comparison', { desktop: isDesktop }]"
        :style="{
          width: width,
        }"
      >
        <div class="header">
          <div class="icon">
            <klk-icon
              v-galileo-click-tracker="{ spm: 'CloseBtn', autoTrackSpm: true }"
              type="icon_navigation_close"
              :data-spm-module="`CloseBtn?trg=manual`"
              data-spm-virtual-item="__virtual"
              @click="syncVisible = false"
            />
          </div>
          <div
            v-if="isDesktop"
            class="title"
          >
            {{ $t("205706") }}
          </div>
        </div>
        <div
          ref="content"
          v-loading="loading"
          class="content"
        >
          <ComparisonTable
            v-if="showTable"
            :package-info="packageInfo"
            :platform="platform"
            :loading="loading"
          />
        </div>
      </div>
    </component>
  </component>
</template>

<script lang="ts">
import { Component, Vue, Prop, PropSync, Watch } from 'vue-property-decorator';
import { Drawer as klkDrawer, Modal as klkModal } from '@klook/klook-ui';
import Landscape from '../../common/Landscape.vue';
import ComparisonTable from '../../common/ComparisonTable.vue';
import { Icon as klkIcon, Table as klkTable } from '@klook/klook-ui';
import { IPackageDetail } from '@/types/package-detail';
import { IconClose } from '@klook/klook-icons';
import { tokens } from '@klook/klook-ui/lib/utils/design-token';

@Component({
  components: {
    klkDrawer,
    klkModal,
    Landscape,
    ComparisonTable,
    klkIcon,
    klkTable,
    IconClose,
  },
})
export default class PackageComparison extends Vue {
  @Prop({
    default: 'desktop',
    type: String,
  })
  platform!: string;

  @PropSync('visible', {
    default: false,
    type: Boolean,
  })
  syncVisible!: boolean;

  @Prop({ type: Object, default: () => ({}) })
  packageInfo!: IPackageDetail;

  @Prop({ type: Boolean, default: true })
  loading!: boolean;

  @Prop({ type: String, default: '' })
  searchId!: string;

  tokens = tokens;

  showTable = false;

  get isReady() {
    return this.syncVisible && !this.loading;
  }

  @Watch('isReady')
  onIsReadyChange(newVal: boolean) {
    if (newVal) {
      setTimeout(() => {
        this.$inhouse.track('pageview', this.$refs.modal.$el);
        this.showTable = true;
      }, 0);
    } else {
      this.showTable = false;
    }
  }

  get trackData() {
    return JSON.stringify({
      CarrentalSearchID: this.searchId,
      PackageReferenceID: this.packageInfo?.package_list?.map(
        (item) => item.reference_id
      ),
      CarId: this.packageInfo?.car_id,
      CarGroupCode: this.packageInfo?.car_group_code,
    });
  }

  get width() {
    if (this.platform === 'mobile') {
      return '100%';
    }
    return (this.packageInfo.package_list?.length ?? 0) > 2
      ? '1240px'
      : '900px';
  }

  get isDesktop() {
    return this.platform === 'desktop';
  }
  get modalComponent() {
    return this.platform === 'mobile' ? 'klk-modal' : 'klk-drawer';
  }

  get openProp() {
    return this.platform === 'mobile' ? 'open' : 'visible';
  }
  get contentComponent() {
    return this.platform === 'mobile' ? 'Landscape' : 'div';
  }
}
</script>

<style lang="scss" scoped>
::v-deep .klk-modal {
  margin: 0;
  padding: 0;
  &-body {
    height: 100%;
  }
}

.package-comparison {
  display: flex;
  align-items: start;
  padding-left: 20px;
  gap: 12px;
  height: 100%;
  overflow: hidden;

  &.desktop {
    flex-direction: column;
    padding: 0;
    gap: 0;
    height: 100vh;

    .header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 20px 32px;
      height: 84px;
      width: 100%;
      border-bottom: 1px solid $color-border-dim;

      &::after {
        content: "";
      }

      .title {
        @include font-heading-xs;
      }
    }

    .content {
      flex: 1;
      height: 0;
      padding: 20px 32px;
      width: 100%;
    }
  }

  .header {
    .icon {
      display: flex;
      justify-content: center;
      align-items: center;
      flex: 0 0 40px;
      height: 40px;
      font-size: 24px;
      cursor: pointer;
    }
  }

  .content {
    flex: 1;
    width: 0;
    height: 100%;
  }
}
</style>
