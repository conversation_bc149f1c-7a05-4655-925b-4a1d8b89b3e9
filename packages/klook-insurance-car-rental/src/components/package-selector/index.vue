<template>
  <div
    class="insurance-package-select"
    :class="platform"
  >
    <klk-section-title
      class="insurance-package-select-header"
      :size="isMobile ? 'small' : 'normal'"
      :decorative-line="true"
    >
      <div class="header">
        <div class="title">
          {{ $t("204452") }}
        </div>
        <div
          v-if="packageList.length > 1"
          v-galileo-click-tracker="{ spm: 'Insurance_Comparison', autoTrackSpm: true }"
          class="btn"
          :data-spm-module="`Insurance_Comparison?trg=manual&ext=${getPackageTrackData()}`"
          data-spm-virtual-item="__virtual"
          @click="onShowComparison"
        >
          {{ $t("204453") }}
        </div>
      </div>
    </klk-section-title>
    <div
      v-if="showNotice"
      class="notice"
      :class="noticeConfig.type"
    >
      <IconCheck
        v-if="noticeConfig.type === 'success'"
        class="notice-icon"
        theme="outline"
        size="16"
        :fill="tokens.$colorSuccess"
      />
      <IconClose
        v-else
        class="notice-icon"
        theme="outline"
        size="16"
        :fill="tokens.$colorError"
      />
      <span class="text">{{ noticeConfig.content }}</span>
    </div>
    <div
      v-if="packageList.length > 0"
      class="package-list"
    >
      <PackageCard
        v-for="(item, index) in packageList"
        :key="index"
        :ref="`packageCard-${item.reference_id}`"
        v-galileo-click-tracker="{ spm: 'InsuranceOption_list', autoTrackSpm: true }"
        :is-selected="currentPackageReferenceId === item.reference_id"
        :data-spm-module="`InsuranceOption_list?oid=packagecode_${
          item.insurance_package
        }&idx=${index}&len=${packageList.length}&ext=${getPackageTrackData(
          item
        )}`"
        data-spm-virtual-item="__virtual"
        :package-track-data="getPackageTrackData(item)"
        :platform="platform"
        :package-info="item"
        @select-package="selectPackage"
        @show-detail="onShowDetail"
      />
    </div>
    <PackageDetail
      :loading="loadingPackageDetail"
      :initial-reference-id="detailReferenceId"
      :package-info="packageDetail"
      :search-id="comparisonParam.search_id"
      :platform="platform"
      :visible.sync="packageDetailVisible"
      @show-comparison="onShowComparison"
      @show-claim-guide="handleShowClaimGuide"
      @select-package="onSelectAndScroll"
    />
    <PackageComparison
      :loading="loadingPackageDetail"
      :platform="platform"
      :package-info="packageDetail"
      :search-id="comparisonParam.search_id"
      :visible.sync="packageComparisonVisible"
    />
    <klk-drawer
      v-if="claimGuideVisible && !isMobile"
      :visible="claimGuideVisible"
      direction="right"
      class="claim-guide-drawer"
      :block-scroll="false"
      @close="claimGuideVisible = false"
    >
      <ModuleSwitcher
        class="claim-guide-drawer-switcher"
        :init-data="switcherInitData"
        :init-step="switcherInitStep"
      />
    </klk-drawer>
    <klk-modal
      v-if="claimGuideVisible && isMobile"
      transition="slide-bottom"
      fullscreen
      :open.sync="claimGuideVisible"
      :show-default-footer="false"
      data-spm-module="InsurancePopUP"
      class="claim-guide-modal"
    >
      <ModuleSwitcher
        :init-data="switcherInitData"
        :init-step="switcherInitStep"
      />
    </klk-modal>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop, Provide } from 'vue-property-decorator';
import PackageCard from './PackageCard.vue';
import type { InsurancePackage, PackageInfo } from '@/types/package-info';
import PackageDetail from './package-detail/index.vue';
import PackageComparison from './package-comparison/index.vue';
import { IconCheck, IconClose } from '@klook/klook-icons';
import { tokens } from '@klook/klook-ui/lib/utils/design-token';
import { lockOuterScroll, unlockOuterScroll } from '@klook/klook-ui/lib/utils/dom';
import { CarInfo } from '@/types/car-info';
import ModuleSwitcher from '../module-switcher.vue';

@Component({
  components: {
    PackageCard,
    PackageDetail,
    PackageComparison,
    IconCheck,
    IconClose,
    ModuleSwitcher,
  },
})
export default class PackageSelect extends Vue {
  @Prop({ type: Object, default: {} }) packageInfo!: PackageInfo;
  @Prop({ type: Boolean, default: false }) showLoading!: boolean;
  @Prop({ type: String, default: 'desktop' }) platform!: 'desktop' | 'mobile';
  @Prop({ type: Object, default: () => ({}) }) comparisonParam!: any;
  @Prop({ type: String, default: '' }) value!: string;
  @Prop({ type: Object, default: () => ({}) }) carInfo!: CarInfo;

  @Provide()
  getIsApp() {
    return false;
  }

  @Provide()
  getBookingNo() {
    return this.$route?.query?.bookingNo;
  }

  @Provide()
  getPlatform() {
    return this.platform;
  }

  @Provide()
  getInsOrderNo() {
    return this.$route?.query?.insOrderNo;
  }

  @Provide()
  closeContainer() {
    this.claimGuideVisible = false;
  }

  @Provide()
  getLanguage() {
    return window?.__KLOOK__?.state?.klook?.language || 'en-US';
  }

  packageDetailVisible = false;
  packageComparisonVisible = false;
  packageDetail = {};
  loadingPackageDetail = true;
  detailReferenceId = '';
  packageDetailCache: Record<string, any> = {};
  packageDetailLoading: Record<string, boolean> = {};
  tokens = tokens;

  claimGuideVisible: boolean = false;
  switcherInitStep: string = '';
  switcherInitData: any = {};

  get isMobile() {
    return this.platform === 'mobile';
  }

  get detailApi() {
    const map = {
      desktop:
        '/v1/transfercarrentalapisrv/search_detail/package_compare_info_v2',
      // 'http://127.0.0.1:4523/m1/6233332-0-default/v1/transfercarrentalapisrv/search_detail/package_compare_info_v2',
      mobile:
        '/v1/transfercarrentalapisrv/search_detail/mweb_package_compare_info_v2',
      // 'http://127.0.0.1:4523/m1/6233332-0-default/v1/transfercarrentalapisrv/search_detail/mweb_package_compare_info_v2'
    };
    return map[this.platform];
  }

  get packageList() {
    return this.packageInfo.list;
  }

  get currentPackageReferenceId() {
    return this.value;
  }

  set currentPackageReferenceId(referenceId: string) {
    this.$emit('input', referenceId);
  }

  get currentPackage() {
    return this.packageList.find(
      (item) => item.reference_id === this.currentPackageReferenceId
    );
  }

  get showNotice() {
    return (
      this.platform === 'desktop' &&
      this.currentPackage &&
      // packageList 中同时包含is_insurance_product true 和 false
      this.packageList.some((item) => item.is_insurance_product) &&
      this.packageList.some((item) => !item.is_insurance_product)
    );
  }

  get noticeConfig() {
    if (this.currentPackage?.is_insurance_product) {
      return {
        type: 'success',
        content: this.$t('204463', {
          percentage: '85%',
        }),
      };
    }
    return {
      type: 'error',
      content: this.$t('204462'),
    };
  }

  handleShowClaimGuide(data: any) {
    this.switcherInitStep = 'claimProcess';
    // const info = this.currentPackage?.insurance_extend_desc?.pre_sale_claim_guide || '{}'
    this.switcherInitData = {
      target: 'dp',
      pre_sale_claim_guide: data,
    };
    this.claimGuideVisible = true;
  }

  getPackageTrackData(item?: InsurancePackage) {
    const extra = {
      CarrentalSearchID: this.comparisonParam?.search_id,
      CoveragePackage: item?.insurance_package,
      PlanCode: item?.insurance_plan_code,
      AxaInsuranceType: item?.insurance_company,
      InsuranceType: item?.insurance_company,
      PackageReferenceID: item?.reference_id,
      CarId: this.carInfo?.car_id,
      CarGroupCode: this.carInfo?.car_group_code,
    };
    return JSON.stringify(extra);
  }

  selectPackage(referenceId: string) {
    if (this.currentPackageReferenceId === referenceId) {
      return;
    }
    this.$emit('change', referenceId);
  }

  async getPackageDetail() {
    const referenceId =
      this.detailReferenceId || this.packageList[0]?.reference_id;

    // Return cached data if available
    if (this.packageDetailCache[referenceId]) {
      this.packageDetail = this.packageDetailCache[referenceId];
      return;
    }

    // Don't start new request if one is already in progress
    if (this.packageDetailLoading[referenceId]) {
      return;
    }

    try {
      this.packageDetailLoading[referenceId] = true;
      this.loadingPackageDetail = true;

      const res = await this.$axios.$post(this.detailApi, {
        ...this.comparisonParam,
        reference_id: referenceId,
      });

      if (!res.success || !res.result) {
        this.$toast(res?.error?.message);
        return;
      }

      // Cache the result
      this.packageDetailCache[referenceId] = res.result;
      this.packageDetail = res.result;
    } catch (error) {
      this.$toast(error?.message);
    } finally {
      this.loadingPackageDetail = false;
      this.packageDetailLoading[referenceId] = false;
    }
  }

  onShowDetail(referenceId: string) {
    this.detailReferenceId = referenceId;
    this.getPackageDetail();
    this.packageDetailVisible = true;
  }

  onShowComparison() {
    this.getPackageDetail();
    this.packageComparisonVisible = true;
  }

  scrollToPackage(referenceId: string) {
    const packageEl = (this.$refs[`packageCard-${referenceId}`]?.[0] as Vue)
      ?.$el as HTMLElement;
    if (packageEl) {
      this.$nextTick(() => {
        const offsetPosition =
          packageEl.offsetTop - (this.isMobile ? 100 : 160);
        setTimeout(() => {
          window.scrollTo({
            top: offsetPosition,
            behavior: 'smooth',
          });
        }, 500);
      });
    }
  }

  onSelectAndScroll(referenceId: string) {
    this.selectPackage(referenceId);
    this.scrollToPackage(referenceId);
    this.packageDetailVisible = false;
  }
}
</script>

<style lang="scss" scoped>
.insurance-package-select {
  &-header {
    padding: 20px 0;
  }

  &.desktop {
    .insurance-package-select-header {
      padding: 32px 0;
    }
  }

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .btn {
    @include font-body-m-regular;
    cursor: pointer;
    text-decoration: underline;
  }

  .package-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }

  .notice {
    padding: 12px 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    border-radius: 8px;
    margin-bottom: 24px;

    &-icon {
      height: 16px;
      line-height: 1;
    }

    &.success {
      background: $color-success-background;
      color: $color-success;
    }

    &.error {
      background: $color-error-background;
      color: $color-error;
    }
  }
}
.claim-guide-drawer {
  ::v-deep .klk-drawer-mask {
    background: transparent;
  }
  .claim-guide-drawer-switcher {
    ::v-deep .claim-process-container {
      width: 700px !important;
    }
    ::v-deep .claim-process__benefitContainer {
      height: calc(100vh - 64px);
    }
  }
}
.claim-guide-modal {
  ::v-deep .klk-modal {
    margin: 0px !important;
    padding: 0px !important;
    overflow: hidden;
  }
  ::v-deep .klk-modal-body {
    height: 100vh;
    // overflow-y: scroll;
    overflow-y: hidden;
  }
  ::v-deep .klk-modal-header {
    padding-left: 20px !important;
    padding-right: 20px !important;
  }
}
</style>
