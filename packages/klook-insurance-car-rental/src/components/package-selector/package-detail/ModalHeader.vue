<template>
  <div
    :class="[
      'header',
      platform === 'mobile' ? 'modal-header' : 'drawer-header',
    ]"
  >
    <klk-icon
      v-galileo-click-tracker="{ spm: 'CloseBtn', autoTrackSpm: true }"
      class="icon"
      :type="
        platform === 'mobile'
          ? 'icon_navigation_chevron_left_xs'
          : 'icon_navigation_close'
      "
      :data-spm-module="`CloseBtn?trg=manual&oid=packagecode_${
        insurancePackage.insurance_package
      }&ext=${packageTrackData}`"
      data-spm-virtual-item="__virtual"
      @click="$emit('close')"
    />
    <span class="title">{{ $t("204468") }}</span>
    <div>
      <div
        v-if="comparisonVisible"
        v-galileo-click-tracker="{ spm: 'Comparison' }"
        class="button"
        data-spm-module="Comparison?trg=manual"
        data-spm-virtual-item="__virtual"
        @click="$emit('show-comparison')"
      >
        {{ $t("204453") }}
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import { Icon as klkIcon } from '@klook/klook-ui';
import { CarInfo } from '@/types/car-info';
import { Package } from '@/types/package-detail';

@Component({
  components: {
    klkIcon,
  },
})
export default class ModalHeader extends Vue {
  @Prop({ default: 'desktop', type: String }) platform!: string;
  @Prop({ default: false, type: Boolean }) comparisonVisible!: boolean;
  @Prop({ default: '', type: String }) searchId!: string;
  @Prop({ default: () => ({}), type: Object }) insurancePackage!: Package;
  @Prop({ default: () => ({}), type: Object }) carInfo!: CarInfo;

  get packageTrackData() {
    const extra = {
      CarrentalSearchID: this.searchId,
      CoveragePackage: this.insurancePackage?.insurance_package,
      PlanCode: this.insurancePackage?.insurance_plan_code,
      AxaInsuranceType: this.insurancePackage?.insurance_company,
      InsuranceType: this.insurancePackage?.insurance_company,
      PackageReferenceID: this.insurancePackage?.reference_id,
      CarId: this.carInfo?.car_id,
      CarGroupCode: this.carInfo?.car_group_code,
    };
    return JSON.stringify(extra);
  }
}
</script>

<style lang="scss" scoped>
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: $color-text-primary;
  border-bottom: 1px solid $color-border-dim;

  .icon {
    font-size: 24px;
    cursor: pointer;
  }

  .button {
    cursor: pointer;
    text-decoration: underline;
  }
}

.modal-header {
  height: 44px;
  padding: 0 12px;
  .icon {
    width: 40px;
    display: flex;
    justify-content: center;
  }
  .title {
    @include font-body-m-bold;
  }
  .button {
    @include font-body-s-regular;
  }
}

.drawer-header {
  height: 66px;
  padding: 20px 32px;
  gap: 12px;
  .title {
    @include font-heading-xs;
  }
  .button {
    @include font-body-m-regular;
  }
}
</style>
