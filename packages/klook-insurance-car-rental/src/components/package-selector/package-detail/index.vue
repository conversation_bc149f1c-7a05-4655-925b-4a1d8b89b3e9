<template>
  <component
    :is="modalComponent"
    ref="modal"
    :fullscreen="platform === 'mobile'"
    :[openProp].sync="syncVisible"
    :direction="platform === 'mobile' ? undefined : 'right'"
    :show-default-footer="false"
    :data-spm-page="`CarRental_Detail_InsuranceCompare?trg=manual&oid=packagecode_${initialPackage.insurance_package}&ext=${trackData}`"
  >
    <template
      v-if="platform === 'mobile'"
      slot="header"
    >
      <ModalHeader
        :platform="platform"
        :insurance-package="currentPackage"
        :car-info="carInfo"
        :comparison-visible="packages.length > 1"
        @close="close"
        @show-comparison="showComparison"
      />
    </template>

    <div
      v-else
      class="drawer-header"
    >
      <ModalHeader
        :comparison-visible="packages.length > 1"
        :insurance-package="currentPackage"
        :car-info="carInfo"
        :platform="platform"
        @close="close"
        @show-comparison="showComparison"
      />
    </div>

    <div
      v-loading="loading"
      :class="['package-detail', { desktop: platform === 'desktop' }]"
    >
      <!-- 头部：package选择tab -->
      <div class="package-tabs">
        <!-- package tabs will be dynamically generated here -->
        <div
          v-for="(item, index) in packages"
          :key="index"
          v-galileo-click-tracker="{ spm: 'PackageSwitch_List', autoTrackSpm: true }"
          :data-spm-module="`PackageSwitch_List?idx=${index}&len=${
            packages.length
          }&oid=packagecode_${item.insurance_package}&ext=${getPackageTrackData(
            item
          )}`"
          data-spm-virtual-item="__virtual"
          :class="[
            'package-tab',
            { selected: item.reference_id === referenceId },
          ]"
          @click="referenceId = item.reference_id"
        >
          <div class="tab-name">
            {{ item.package_name }}
          </div>
          <StrengthIndicator :strength="item.guarantee_strength" />
          <div class="tab-price">
            {{ item.price_per_day }}
            <span class="per-day">{{ $t("19705") }}</span>
          </div>
        </div>
      </div>

      <!-- 内容：tip，benefit list，T&c描述 -->
      <InsuranceDetails
        class="package-content"
        :data="currentPackage"
        :platform="platform"
        :show-bottom="false"
      />

      <!-- 底部：操作行 -->
      <div class="package-footer">
        <div
          v-if="linkList.length > 0"
          class="link-list"
        >
          <a
            v-for="(item, index) in linkList"
            :key="index"
            :href="item.link"
            target="_blank"
            v-bind="item.track"
            @click="handleFooterLinkClick(item)"
          >
            {{ item.button_text }}
            <IconNext
              theme="outline"
              size="16"
              :fill="tokens['$color-text-link']"
            />
          </a>
        </div>
        <div class="confirm-button">
          <klk-button
            v-galileo-click-tracker="{ spm: 'PackageChoose', autoTrackSpm: true }"
            block
            :data-spm-module="`PackageChoose?trg=manual&oid=packagecode_${
              currentPackage.insurance_package
            }&ext=${getPackageTrackData(currentPackage)}`"
            data-spm-virtual-item="__virtual"
            @click="onConfirm"
          >
            {{ $t("204474") }}
          </klk-button>
        </div>
      </div>
    </div>
  </component>
</template>

<script lang="ts">
import { Component, Vue, Prop, PropSync, Watch } from 'vue-property-decorator';
import {
  Drawer as klkDrawer,
  Modal as klkModal,
  Icon as klkIcon,
  Button as klkButton,
} from '@klook/klook-ui';
import { IPackageDetail, Package } from '@/types/package-detail';
import ModalHeader from './ModalHeader.vue';
import StrengthIndicator from '../../common/StrengthIndicator.vue';
import { IconNext } from '@klook/klook-icons';
import { tokens } from '@klook/klook-ui/lib/utils/design-token';
import InsuranceDetails from '../../common/details.vue';

@Component({
  components: {
    klkDrawer,
    klkModal,
    klkIcon,
    klkButton,
    ModalHeader,
    StrengthIndicator,
    IconNext,
    InsuranceDetails,
  },
})
export default class PackageDetail extends Vue {
  @Prop({ default: 'desktop', type: String }) platform!: string;
  @PropSync('visible', { default: false, type: Boolean }) syncVisible!: boolean;
  @Prop({ type: Object, default: () => ({}) })
  packageInfo!: IPackageDetail;
  // prop 默认选中的 package referenceId
  @Prop({ type: String, default: '' }) initialReferenceId!: string;
  @Prop({ type: String, default: '' }) searchId!: string;
  @Prop({ type: Boolean, default: false }) loading!: boolean;

  referenceId = this.initialReferenceId;
  tokens = tokens;

  // 计算属性：当syncVisible为true且loading为false时为true
  get shouldTrackPageview() {
    return this.syncVisible && !this.loading;
  }

  get carInfo() {
    return {
      car_id: this.packageInfo?.car_id,
      car_group_code: this.packageInfo?.car_group_code,
    };
  }

  @Watch('shouldTrackPageview')
  onShouldTrackPageviewChange(newVal: boolean) {
    if (newVal) {
      setTimeout(() => {
        this.$inhouse.track('pageview', this.$refs.modal.$el);
      }, 0);
    }
  }

  @Watch('initialReferenceId', { immediate: true })
  onInitialReferenceIdChange(newVal: string) {
    this.referenceId = newVal;
  }

  get initialPackage() {
    return (
      this.packages.find(
        (item) => item.reference_id === this.initialReferenceId
      ) ?? ({} as Package)
    );
  }

  get trackData() {
    return JSON.stringify({
      CarrentalSearchID: this.searchId,
      PlanCode: this.initialPackage?.insurance_plan_code,
      PackageReferenceID: this.initialReferenceId,
      CarId: this.packageInfo?.car_id,
      CarGroupCode: this.packageInfo?.car_group_code,
    });
  }

  getPackageTrackData(item?: Package) {
    const extra = {
      CarrentalSearchID: this.searchId,
      CoveragePackage: item?.insurance_package,
      PlanCode: item?.insurance_plan_code,
      AxaInsuranceType: item?.insurance_company,
      InsuranceType: item?.insurance_company,
      PackageReferenceID: item?.reference_id,
      CarId: this.packageInfo?.car_id,
      CarGroupCode: this.packageInfo?.car_group_code,
    };
    return JSON.stringify(extra);
  }

  get currentPackage() {
    const pkg =
      this.packages.find((item) => item.reference_id === this.referenceId) ||
      this.packages[0] ||
      ({} as Package);
    return {
      ...pkg,
      see_title: this.packageInfo.see_title,
      see_detail: this.packageInfo.see_detail,
    };
  }

  get linkList() {
    let linkList: Record<string, any> = [];
    if (this.currentPackage?.insurance_policy_wording) {
      linkList.push({
        ...this.currentPackage.insurance_policy_wording,
        track: {
          'data-spm-module': `PolicyWordingBtn?trg=manual&oid=packagecode_${
            this.currentPackage.insurance_package
          }&ext=${this.getPackageTrackData(this.currentPackage)}`,
          'data-spm-virtual-item': '__virtual',
        },
      });
    }
    const claimGuideInfo = this.currentPackage?.insurance_extend_desc?.pre_sale_claim_guide;
    if (claimGuideInfo) {
      const claimGuideInfoObj = JSON.parse(claimGuideInfo);
      linkList.push({
        key: 'claimGuide',
        button_text: claimGuideInfoObj?.button_text
      });
    }
    return linkList;
  }

  get modalComponent() {
    return this.platform === 'mobile' ? 'klk-modal' : 'klk-drawer';
  }

  get openProp() {
    return this.platform === 'mobile' ? 'open' : 'visible';
  }

  get packages() {
    return this.packageInfo.package_list || [];
  }

  handleFooterLinkClick(item) {
    if (item.key === 'claimGuide') {
      const info = this.currentPackage?.insurance_extend_desc?.pre_sale_claim_guide;
      this.$emit('show-claim-guide', info);
    }
  }

  showComparison() {
    this.$emit('show-comparison');
  }

  close() {
    this.syncVisible = false;
  }

  onConfirm() {
    this.$emit('select-package', this.referenceId);
  }
}
</script>

<style lang="scss" scoped>
@mixin text-ellipsis($line-clamp: 2) {
  display: -webkit-box;
  -webkit-line-clamp: $line-clamp;
  -webkit-box-orient: vertical;
  text-overflow: ellipsis;
  overflow: hidden;
}

::v-deep .klk-modal {
  margin: 0;
  padding: 0;

  &-header {
    padding: 0;
  }

  &-body {
    height: calc(100% - 44px);
  }
}

.package-detail {
  display: flex;
  flex-direction: column;
  height: 100%;

  &.desktop {
    width: 700px; // 为 desktop 模式设置固定宽度
    height: calc(100% - 66px);

    .package-tabs {
      padding: 16px 32px;
    }

    .package-tab {
      flex: 0 0 206px;
    }

    .package-content {
      padding: 0 32px 40px;
    }
  }

  .package-tabs {
    display: flex;
    overflow: auto;
    gap: 9px;
    padding: 16px 20px;
    height: 123px;
  }

  .package-tab {
    display: flex;
    flex-direction: column;
    flex: 0 0 163px;
    width: 163px;
    height: 91px;
    border-radius: 12px;
    padding: 12px;
    gap: 4px;
    border: 1px solid var(--color-border-normal, #e6e6e6);
    cursor: pointer;

    &.selected {
      background: var(--color-brand-primary-light-2, #faf5f2);
      border: 1px solid var(--color-brand-primary, #ff5b00);
    }

    .tab-name {
      @include font-heading-xxxs;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      color: var(--color-text-primary, #212121);
    }

    .tab-price {
      @include font-heading-xxxs;
      color: var(--color-text-primary, #212121);

      .per-day {
        @include font-body-xs-regular;
        color: var(--color-text-secondary, #757575);
      }
    }
  }

  .package-content {
    flex: 1;
    overflow: auto;
    padding: 0 20px 40px;
  }

  .package-footer {
    .link-list {
      display: flex;
      justify-content: space-between;
      padding: 12px 20px;

      a {
        display: flex;
        gap: 4px;
        align-items: center;
        @include font-body-s-regular-v2;
        color: var(--color-text-link, #2073f9);
        text-decoration: none;
      }
    }

    .confirm-button {
      padding: 8px 12px;
      border-top: 1px solid var(--color-border-normal, #e6e6e6);
    }
  }
}
</style>
