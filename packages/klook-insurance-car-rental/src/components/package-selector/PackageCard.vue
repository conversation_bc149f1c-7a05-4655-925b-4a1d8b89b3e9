<template>
  <div
    :id="packageInfo.reference_id.slice(-6)"
    class="package-card"
    :class="{
      selected: isSelected,
      desktop: platform === 'desktop',
      'has-recommend': packageInfo.recommend_desc,
    }"
    @click="selectPackage"
  >
    <klk-corner-label
      v-if="packageInfo.recommend_desc"
      type="highlight"
      position="top-end"
      size="small"
      class="package-card-tag"
    >
      {{ packageInfo.recommend_desc }}
    </klk-corner-label>
    <div class="package-card-header">
      <klk-radio v-model="isSelected" />
      <div class="package-card-header-content">
        <div class="package-card-name">
          {{ packageInfo.insurance_package_name }}
        </div>
        <StrengthIndicator :strength="packageInfo.guarantee_strength" />
      </div>
      <div class="package-card-price">
        {{ packageInfo.min_price.price_per_day }}
        <span>{{ packageInfo.min_price.day_desc }}</span>
      </div>
    </div>
    <SubInsuranceList
      v-if="
        packageInfo.less_sub_insurance_list &&
          packageInfo.less_sub_insurance_list.length > 0
      "
      :sub-items="packageInfo.less_sub_insurance_list"
    />

    <!-- footer -->
    <div class="package-card-footer">
      <!-- more benefit block-->
      <div class="package-card-more-benefit">
        <template v-if="packageInfo.has_more_sub_insurance">
          <MoreBenefitIcon />
          <span>{{ $t("204461") }}</span>
        </template>
      </div>

      <!-- view more block -->
      <div
        v-galileo-click-tracker="{ spm: 'InsuranceDetailView', autoTrackSpm: true }"
        class="package-card-view-more"
        :data-spm-module="`InsuranceDetailView?trg=manual&oid=packagecode_${packageInfo.insurance_package}&ext=${packageTrackData}`"
        data-spm-virtual-item="__virtual"
        @click.stop="openInsuranceDetail"
      >
        {{ packageInfo.package_pop }}
        <IconNext
          theme="outline"
          size="16"
          :fill="tokens['$color-text-link']"
        />
      </div>
    </div>

    <div
      v-if="packageInfo.note && isSelected"
      class="package-card-note"
    >
      <span class="label">{{ $t("121307") }}: </span>{{ packageInfo.note }}
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import SubInsuranceList from './SubInsuranceList.vue';
import StrengthIndicator from '../common/StrengthIndicator.vue';
import type { InsurancePackage } from '@/types/package-info';
import { tokens } from '@klook/klook-ui/lib/utils/design-token';
import { Radio as klkRadio } from '@klook/klook-ui';
import { CornerLabel as klkCornerLabel } from '@klook/klook-ui/lib/label';
import { IconNext } from '@klook/klook-icons';
import MoreBenefitIcon from '../../assets/more-benefit-icon.vue';

@Component({
  components: {
    SubInsuranceList,
    StrengthIndicator,
    klkRadio,
    klkCornerLabel,
    IconNext,
    MoreBenefitIcon,
  },
})
export default class PackageCard extends Vue {
  @Prop({ type: Object, required: true }) packageInfo!: InsurancePackage;
  @Prop({ type: String, default: 'desktop' }) platform!: string;
  @Prop({ type: Boolean, default: false }) isSelected!: boolean;
  @Prop({ type: String, default: '' }) packageTrackData!: string;

  tokens: any = tokens;

  selectPackage() {
    if (this.isSelected) {
      return;
    }
    this.$emit('select-package', this.packageInfo.reference_id);
  }

  openInsuranceDetail() {
    this.$emit('show-detail', this.packageInfo.reference_id);
  }
}
</script>

<style lang="scss" scoped>
.package-card {
  display: flex;
  flex-direction: column;
  gap: 12px;
  border: 1px solid $color-border-normal;
  border-radius: $radius-xl;
  padding: 16px;
  position: relative;
  cursor: pointer;

  &-tag {
    position: absolute;
    top: -12px;
    right: -6px;
  }

  &.has-recommend {
    margin-top: 12px;
  }

  ::v-deep .klk-radio {
    margin: 0;

    &-label {
      padding: 0;
    }
  }

  &-header {
    display: flex;
    align-items: start;
    gap: 12px;

    &-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 4px;
    }
  }

  &-name {
    @include font-heading-xxs;
  }

  &-price {
    @include font-heading-xxs;

    span {
      @include font-body-s-regular-v2;
      color: $color-text-secondary;
    }
  }

  &-footer {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }

  &-more-benefit {
    display: flex;
    gap: 12px;
    align-items: center;
    @include font-body-s-regular-v2;
    color: $color-text-primary;
  }

  &-view-more {
    display: flex;
    gap: 4px;
    align-items: center;
    padding-top: 4px;
    @include font-body-s-regular-v2;
    color: $color-text-link;
    cursor: pointer;

    .i-icon {
      height: 16px;
    }
  }

  &-note {
    margin-top: 12px;
    padding-top: 12px;
    border-top: 1px solid $color-border-normal;
    @include font-body-s-regular-v2;
    color: $color-text-secondary;

    .label {
      color: #f09b0a;
    }
  }

  .sub-insurance-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }

  &.selected {
    border-color: $color-brand-primary;
    background-color: $color-brand-primary-light-2;

    .package-card-name {
      color: $color-brand-primary;
    }
  }

  &.desktop {
    .sub-insurance-list {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 12px;
    }

    .package-card-footer {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
    }
  }
}
</style>
