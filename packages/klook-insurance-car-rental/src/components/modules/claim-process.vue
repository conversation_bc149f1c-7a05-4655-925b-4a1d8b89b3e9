<template>
  <div
    :class="{
      'claim-process-container': true,
      'claim-process-container-mobile': isMobile,
      'claim-process-container-app': getIsApp(),
    }"
  >
    <klk-loading v-if="loadingStatus === 'loading'" />
    <div
      v-else
      :class="{
        'claim-process': true,
      }"
    >
      <DetailsHeader
        :platform="getPlatform()"
        :icon-back="true"
        :title="$t('204483-claim_process')"
        :show-bottom-border="true"
        @click="handleBackClickInner"
      >
        <div
          slot="subTitle"
          class="claim-process__subTitle"
        >
          {{ data.claim_guide_info_sub_title }}
        </div>
      </DetailsHeader>
      <ClaimProcessList
        v-if="loadingStatus === 'success'"
        :list="list"
        class="claim-process__benefitContainer"
      />
      <ErrorPanel
        v-if="loadingStatus === 'error'"
        @refresh="fetchData"
      />
    </div>
  </div>
</template>

<script lang="ts">
import { Component } from 'vue-property-decorator';
import {
  IconChevronDown,
  IconInformation,
  IconChevronUp,
  IconNext,
} from '@klook/klook-icons';
import {
  $colorTextPrimary,
  $colorTextLink,
} from '@klook/klook-ui/lib/utils/design-token-esm';
import { getAntiSpiderHead } from '../../utils';
import InsuranceDetails from '../common/details.vue';
import DetailsHeader from '../common/header.vue';
import ErrorPanel from '../common/error-panel.vue';
import ClaimProcessList from '../common/claim-process.vue';
import ModuleBase from './base.vue';

@Component({
  components: {
    ModuleBase,
    InsuranceDetails,
    DetailsHeader,
    ClaimProcessList,
    IconChevronDown,
    IconInformation,
    IconChevronUp,
    IconNext,
    ErrorPanel,
  },
})
export default class ClaimProcess extends ModuleBase {
  colorTextPrimary: string = $colorTextPrimary;
  colorTextLink: string = $colorTextLink;
  dpApi: string =
    '/v1/insuranceapisrv/outer/carrental/claim/getPreSaleClaimGuide';
  odpApi: string =
    '/v1/insuranceapisrv/outer/carrental/claim/getSufSaleClaimGuide';
  loadingStatus: string = 'loading'; // 'loading' | 'error' | 'success'
  errorMsg: string = '';

  data: any = {};
  claimProcessVisible: boolean = false;
  fetchData: any = null;

  get list() {
    return this.data?.guide_list || [];
  }

  created() {
    const target: 'dp' | 'odp' =
      this.initData?.target || 'odp';
    this.fetchData = target === 'dp' ? this.fetchDpData : this.fetchOdpData;
    this.fetchData().then(() => {
      this.$nextTick(() => {
        this.fixBrowserBottomBar('claim-process-container', 'claim-process__benefitContainer', 48)
      });
    });
  }

  handleBackClickInner() {
    const target: 'dp' | 'odp' =
      this.initData?.target || 'odp';
    if (target === 'dp') {
      this.closeContainer();
    } else {
      this.$emit('back');
    }
  }

  handleClaimGuideClick(index: number) {
    const visible = this.data?.guide_list[index].claimGuideVisible;
    this.$set(this.data?.guide_list[index], 'claimGuideVisible', !visible);
  }

  fetchDpData() {
    const params = {
      pre_sale_claim_guide:
        this.initData?.pre_sale_claim_guide ||
        this.$route?.query?.pre_sale_claim_guide,
      language: this.getLanguage && this.getLanguage(),
    };
    return this.$axios
      .$post(this.dpApi, params)
      .then((res) => {
        if (!res.success || !res.result) {
          this.loadingStatus = 'error';
          this.errorMsg = (res.error && res.error.message) || '';
          this.processRequestError(res)
          return;
        }
        this.loadingStatus = 'success';
        this.data = res.result;
        return res.result;
      })
      .catch(() => {
        this.loadingStatus = 'error';
      });
  }

  fetchOdpData() {
    return this.$axios
      .$get(this.odpApi, { params: { bookingNo: this.getBookingNo() } })
      .then((res) => {
        if (!res.success || !res.result) {
          this.loadingStatus = 'error';
          this.errorMsg = (res.error && res.error.message) || '';
          this.processRequestError(res)
          return;
        }
        this.loadingStatus = 'success';
        this.data = res.result;
        return res.result;
      })
      .catch(() => {
        this.loadingStatus = 'error';
      });
  }
}
</script>

<style lang="scss" scoped>
@import "./mixins.scss";
.claim-process-container {
  @include module-page;
  .claim-process {
    height: 100%;
    &__subTitle {
      @include font-body-s-regular-v2;
      color: $color-text-primary;
    }
    &__benefitContainer {
      padding: 20px 32px;
      @include module-content($hide-bottom: true);
    }
  }
}
.claim-process-container-mobile {
  @include module-page-mobile;
  .claim-process {
    .claim-process__subTitle {
      @include font-paragraph-xs-regular;
    }
    .claim-process__benefitContainer {
      @include module-content-mobile($hide-bottom: true);
      padding: 24px 20px;
    }
  }
}
.claim-process-container-app {
  .claim-process {
    // .claim-process__benefitContainer {
    //   @include module-content-app($hide-bottom: true);
    // }
  }
}
</style>
