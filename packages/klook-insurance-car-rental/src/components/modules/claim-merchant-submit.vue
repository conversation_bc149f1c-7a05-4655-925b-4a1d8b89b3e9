<template>
  <div
    ref="container"
    :data-spm-page="pageSpm"
    :class="{
      'claim-merchant-submit-container': true,
      'claim-merchant-submit-container-mobile': isMobile,
      'claim-merchant-submit-container-app': getIsApp(),
      'claim-merchant-submit-container-desktop': !isMobile,
    }"
  >
    <klk-loading v-if="loadingStatus === 'loading'" />
    <div
      v-else
      :class="{
        'claim-merchant-submit': true,
      }"
    >
      <DetailsHeader
        v-if="isMobile"
        :platform="getPlatform()"
        :title="$t('176286-claim_submit')"
        :show-bottom-border="true"
        :hide-icon="true"
        @click="handleBackClick"
      />
      <div
        v-if="loadingStatus === 'success'"
        :class="{
          'claim-merchant-submit__content': true,
        }"
      >
        <div>
          <div class="claim-merchant-submit__sectionTitle">
            {{ data.claim_info_title }}
          </div>
          <div class="claim-merchant-submit__sectionContent">
            <div>
              <div class="claim-merchant-submit__claimInfoLabel">
                {{ claimInfo.booking_no_title }}
              </div>
              <div class="claim-merchant-submit__claimInfoValue">
                {{ claimInfo.booking_no }}
              </div>
            </div>
            <div>
              <div class="claim-merchant-submit__claimInfoLabel">
                {{ claimInfo.driver_name_title }}
              </div>
              <div class="claim-merchant-submit__claimInfoValue">
                {{ claimInfo.driver_name }}
              </div>
            </div>
            <div>
              <div class="claim-merchant-submit__claimInfoLabel">
                {{ claimInfo.claim_benefit_title }}
              </div>
              <div class="claim-merchant-submit__claimInfoValue">
                {{ claimInfo.claim_benefit }}
              </div>
            </div>
          </div>
          <div class="claim-merchant-submit__materialTitle">
            {{ data.material_info_title }}
          </div>
          <klk-form
            ref="claimMaterialInfoForm"
            :model="claimMaterialForm"
            class="claim-merchant-submit__form"
            auto-locate-error
          >
            <div
              v-for="(categoryItem, categoryItemIndex) in materialCategoryList"
              :key="categoryItemIndex"
            >
              <div class="claim-merchant-submit__categoryTitle">
                {{ categoryItem.category_name }}
              </div>
              <div
                v-for="(
                  materialItem, materialItemIndex
                ) in categoryItem.material_list"
                :key="materialItemIndex"
                class="claim-merchant-submit__categoryContent"
              >
                <klk-form-item
                  prop="material_list"
                  :rules="
                    getClaimMaterialFormItemRules(
                      categoryItemIndex,
                      materialItemIndex
                    )
                  "
                >
                  <div
                    slot="label"
                    class="claim-merchant-submit__materialItemLabel"
                  >
                    <div>
                      <span>{{ materialItem.material_name }}</span>
                      <span
                        v-if="materialItem.required"
                        class="claim-merchant-submit__materialItemLabelRequired"
                        >*</span
                      >
                    </div>
                    <klk-link
                      v-if="materialItem.example_url"
                      color="#212121"
                      @click="handleClickExample(materialItem.example_url)"
                    >
                      {{ $t("206505") }}
                    </klk-link>
                  </div>
                  <div
                    v-if="materialItem.material_desc"
                    class="claim-merchant-submit__materialItemDesc"
                  >
                    {{ materialItem.material_desc }}
                  </div>
                  <!-- <div v-if="materialItem.can_provide_by_other">
                      <klk-checkbox
                        :value="
                          materialItem.provide_damage_report_by_car_rental
                        "
                        @change="
                          handleReportByRentalClick(
                            $event,
                            categoryItemIndex,
                            materialItemIndex
                          )
                        "
                      >
                        {{ materialItem.content }}
                      </klk-checkbox>
                    </div> -->
                  <UploadFile
                    class="claim-merchant-submit__uploader"
                    :action="UPLOAD_CONFIG.actionLink"
                    :max-file-size="UPLOAD_CONFIG.maxFileSize"
                    :limit="UPLOAD_CONFIG.maxFileCount"
                    :limit-msg="$t('210342-upload_limit_reached')"
                    :accept="UPLOAD_CONFIG.acceptType"
                    :upload-tips="materialItem.bottom_tips"
                    :files="materialItem.file_list || []"
                    :data="UPLOAD_CONFIG.data"
                    :is-mobile="isMobile"
                    :headers=" UPLOAD_CONFIG.headers"
                    name="upload_file"
                    @success="
                      handleUploadSuccess(
                        $event,
                        categoryItemIndex,
                        materialItemIndex
                      )
                    "
                    @remove="
                      handleUploadRemoved(
                        $event,
                        categoryItemIndex,
                        materialItemIndex
                      )
                    "
                    @error="handleUploadError"
                    @open-pdf="openExternalLink"
                  />
                </klk-form-item>
              </div>
            </div>
          </klk-form>
          <klk-bottom-sheet
            show-close
            header-divider
            :visible.sync="showExampleModal"
            :title="$t('206505')"
            :mask-closable="false"
          >
            <img class="claim-merchant-submit__sheetImg" :src="exampleUrl" />
          </klk-bottom-sheet>
        </div>
      </div>
      <div v-if="loadingStatus === 'success'" class="claim-merchant-submit__bottom">
        <klk-button
          type="outlined"
          reverse
          class="claim-merchant-submit__bottomBtn"
          @click="handleClickClear"
        >
          {{ $t("206579-clear") }}
        </klk-button>
        <klk-button
          :data-spm-module="`ClaimSubmissionBtn?oid=claim_id_${
            data.claim_id
          }&ext=${JSON.stringify({
            BookingID: data.addon_booking_nos,
            MainBookingID: data.main_booking_no,
            PlanCode: data.plan_codes,
            PackageReferenceID: data.package_ref_id,
            CoveragePackage: data.coverage_package,
          })}`"
          data-spm-virtual-item="__virtual"
          type="primary"
          reverse
          class="claim-merchant-submit__bottomBtn"
          @click="handleClickSubmit"
        >
          {{ $t("210503-submit") }}
        </klk-button>
      </div>
      <KlkEmptyPanel
        v-if="loadingStatus === 'expired'"
        :content="$t('209700-link_expired')"
        icon-width="160"
        icon-height="160"
        icon-src="https://res.klook.com/image/upload/ill_spot_my_reviews_is6sjn.svg"
        class="claim-list-empty"
      />
      <ErrorPanel v-if="loadingStatus === 'error'" @refresh="fetchData" />
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Watch } from "vue-property-decorator";
import lodashClonedeep from "lodash/cloneDeep";
import lodashOmit from "lodash/omit";
import lodashGet from "lodash/get";
import {
  IconChevronDown,
  IconInformation,
  IconChevronUp,
  IconNext,
} from "@klook/klook-icons";
import {
  $colorTextPrimary,
  $colorTextLink,
} from "@klook/klook-ui/lib/utils/design-token-esm";
import KlkEmptyPanel from "@klook/empty-panel";
import InsuranceDetails from "../common/details.vue";
import DetailsHeader from "../common/header.vue";
import ErrorPanel from "../common/error-panel.vue";
import ModuleBase from "./base.vue";
import UploadFile from "../common/upload.vue";

@Component({
  components: {
    ModuleBase,
    InsuranceDetails,
    DetailsHeader,
    IconChevronDown,
    IconInformation,
    IconChevronUp,
    IconNext,
    ErrorPanel,
    KlkEmptyPanel,
    UploadFile,
  },
})
export default class ClaimMerchantSubmit extends ModuleBase {
  colorTextPrimary: string = $colorTextPrimary;
  colorTextLink: string = $colorTextLink;
  api: string =
    "/v1/insuranceendpointsrv/outer/claim/carrental/getUploadFilePage";
  submitApi: string =
    "/v1/insuranceendpointsrv/outer/claim/carrental/submitDamageReport";
  loadingStatus: string = "loading"; // 'loading' | 'error' | 'success' | 'expired'
  errorMsg: string = "";

  data: any = {};
  claimMaterialForm: any = {};
  claimMaterialFormRules: any = {};
  hasMaterialFormValidate: boolean = false; // 是否有触发过 claimMaterialForm 的校验
  UPLOAD_CONFIG = {
    actionLink: "/v1/insuranceclaimapisrv/outer/merchant/claim/uploadFile",
    maxFileSize: 10240, // 上传图片大小限制
    maxFileCount: 10, // 上传图片个数限制,
    acceptType: "application/pdf, image/jpg, image/jpeg, image/png, image/heic", // heic格式图片只有Safari支持预览
    data: {
      content_type: "application/javascript",
      claim_id: this.initData.claim_id,
      token: this.initData.token,
    },
    headers: {
      "X-Klook-Host": window.__KLOOK__?.state?.klook.host || window.location.host
    }
  };
  exampleUrl: string = "";
  showExampleModal: boolean = false;

  get claimInfo() {
    return this.data?.claim_info || {};
  }

  get materialCategoryList() {
    return this.data?.material_category_list || [];
  }

  created() {
    this.fetchData().then(() => {
      this.$nextTick(() => {
        this.fixBrowserBottomBar(
          "claim-merchant-submit-container",
          "claim-merchant-submit__content",
          48 + 68
        );
      });
    });
  }

  handleUploadError(err: any) {
    this.$toast(err.toString());
  }

  handleClickClear() {
    this.loadingStatus = "loading"; // 重置加载状态
    this.hasMaterialFormValidate = false; // 重置校验标记
    this.initClaimMaterialForm();
    // 重新渲染组件，情况所有已上传的文件
    setTimeout(() => {
      this.loadingStatus = "success"; // 重置加载状态为成功
    });
  }

  handleClickSubmit() {
    this.$refs.claimMaterialInfoForm.validate((valid) => {
      if (!valid) {
        return;
      }
      this.submit().then(() => {
        this.$toast(this.$t("24998-submit_success"));
      });
    });
  }

  handleClickExample(url) {
    this.exampleUrl = url;
    this.showExampleModal = true;
  }

  initClaimMaterialForm() {
    const infoData = this.data || {};
    this.claimMaterialForm = lodashClonedeep(infoData);
    console.log(
      "initClaimMaterialForm this.claimMaterialForm:",
      this.claimMaterialForm,
      "claimMaterialFormRules:",
      this.claimMaterialFormRules
    );
  }

  sortFormData() {
    const categoryList = this.claimMaterialForm?.material_category_list || [];
    const files: any[] = [];
    categoryList.forEach((category) => {
      const materialList = category.material_list || [];
      materialList.forEach((material) => {
        const fileList = material.file_list || [];
        fileList.forEach((file) => {
          const obj: any = {
            ...file,
            ...lodashOmit(material, ["file_list"]),
          };
          files.push(obj);
        });
      });
    });
    return {
      claim_id: this.initData.claim_id,
      token: this.initData.token,
      main_booking_ref_no: this.initData.booking_no,
      files,
    };
  }

  submit() {
    const params = this.sortFormData();
    return this.$axios.$post(this.submitApi, params).then((res) => {
      if (!res.success || !res.result) {
        this.processRequestError(res);
        throw new Error(res?.error?.message);
      }
      return res.result;
    });
  }

  handleUploadSuccess(result, categoryIndex, materialIndex) {
    const fileList = result.responses || [];
    this.$set(
      this.claimMaterialForm.material_category_list[categoryIndex]
        .material_list[materialIndex],
      "file_list",
      fileList
    );
    setTimeout(() => {
      if (this.hasMaterialFormValidate) {
        // 如果触发过 claimMaterialForm 的校验，调用一次校验来决定红字是展示还是消失
        this.$refs.claimMaterialInfoForm.validate();
      }
    });
  }

  handleUploadRemoved(result, categoryIndex, materialIndex) {
    const fileList = result.responses || [];
    this.$set(
      this.claimMaterialForm.material_category_list[categoryIndex]
        .material_list[materialIndex],
      "file_list",
      fileList
    );
    setTimeout(() => {
      if (this.hasMaterialFormValidate) {
        // 如果触发过 claimMaterialForm 的校验，调用一次校验来决定红字是展示还是消失
        this.$refs.claimMaterialInfoForm.validate();
      }
    });
  }

  getClaimMaterialFormItemRules(categoryIndex, materialIndex) {
    return [
      {
        type: "array",
        trigger: "blur",
        validator: (rule: any, value: any) => {
          this.hasMaterialFormValidate = true; // 标记已经触发过校验
          const materialItem = lodashGet(
            this.claimMaterialForm,
            `material_category_list.${categoryIndex}.material_list.${materialIndex}`,
            []
          );
          if (
            materialItem.can_provide_by_other &&
            materialItem.provide_damage_report_by_car_rental
          ) {
            // 如果可以由租车公司提供报告，则不需要上传文件
            return true;
          }
          if (
            materialItem.required &&
            (!materialItem.file_list || !materialItem.file_list.length)
          ) {
            return new Error(this.$t("16186-please_enter"));
          }
          return true;
        },
      },
    ];
  }

  fetchData() {
    return this.$axios
      .$get(this.api, {
        params: {
          claim_id: this.initData.claim_id,
          token: this.initData.token,
        },
      })
      .then((res) => {
        if (!res.success) {
          this.loadingStatus =
            res?.error?.code === "20030" ? "expired" : "error";
          this.errorMsg = (res.error && res.error.message) || "";
          this.processRequestError(res);
          return;
        }
        this.data = res.result;
        this.initClaimMaterialForm();
        this.loadingStatus = "success";

        this.$nextTick(() => {
          this.$inhouse.track("pageview", this.$refs.container);
        });
        return res.result;
      })
      .catch(() => {
        this.loadingStatus = "error";
      });
  }

  get pageSpm() {
    const {
      claim_id,
      main_booking_no,
      plan_codes,
      coverage_package,
      package_ref_id,
    } = this.data || {};
    return `Insurance_Claim3rdPartyMaterialSubmission?trg=manual&oid=claim_id_${claim_id}&ext=${JSON.stringify(
      {
        MainBookingID: main_booking_no,
        PlanCode: plan_codes,
        PackageReferenceID: package_ref_id,
        CoveragePackage: coverage_package,
      }
    )}`;
  }
}
</script>

<style lang="scss" scoped>
@import "./mixins.scss";

.claim-merchant-submit-container {
  @include module-page;

  .claim-merchant-submit {
    height: 100%;

    &__content {
      @include module-content;
      padding: 20px;

      .claim-list-empty {
        margin-top: 20%;
      }
    }

    &__sectionTitle {
      @include font-heading-xs;
      color: $color-text-primary;
    }

    &__materialTitle {
      @include font-heading-xs;
      margin-top: 40px;
      color: $color-text-primary;
    }

    &__claimInfoLabel {
      @include font-body-s-regular;
      color: $color-text-placeholder;
      margin-top: 16px;
    }

    &__claimInfoValue {
      @include font-body-m-bold;
      font-weight: 400;
      color: $color-text-primary;
    }

    &__categoryTitle {
      @include font-body-m-bold;
      color: $color-text-primary;
      margin-top: 16px;
    }

    &__categoryContent {
      margin-top: 20px;
    }

    &__materialItemLabel {
      display: flex;
      gap: 10px;
      align-items: flex-start;
      justify-content: space-between;
    }

    &__materialItemLabelRequired {
      color: $color-brand-primary;
      margin-left: 4px;
    }

    &__materialItemDesc {
      @include font-body-s-regular;
      color: $color-text-secondary;
    }

    &__uploader {
      margin-top: 10px;
    }

    &__sheetImg {
      width: 100%;
    }

    .claim-merchant-submit__bottom {
      @include claim-bottom;
    }

    .claim-merchant-submit__bottomBtn {
      @include claim-bottom-btn;
    }
    .claim-list-empty {
      margin-top: 30%;
      max-width: 335px;
    }
  }
}

// 这里的样式层级结构一定要写的跟.claim-record-container一样，
// 如果少写了一层.claim-record，那么web样式的优先级会变得更高
.claim-merchant-submit-container-mobile {
  @include module-page-mobile;

  .claim-merchant-submit {
    .claim-merchant-submit__content {
      @include module-content-mobile;
      padding: 20px 16px;
    }

    .claim-merchant-submit__bottom {
      border: 0;
    }
  }
}

.claim-merchant-submit-container-app {
  .claim-merchant-submit {
    // .claim-merchant-submit__content {
    //   @include module-content-app;
    // }

    // .claim-merchant-submit__contentNoButtom {
    //   @include module-content-app($hide-bottom: true);
    // }

    // .claim-merchant-submit__bottom {
    //   padding-bottom: 32px;
    // }
  }
}

.claim-merchant-submit-container-desktop {
  width: 100%;
  background: $color-bg-3;
  padding: 50px;

  .claim-merchant-submit {
    background: #ffffff;
    max-width: 680px;
    margin-left: auto;
    margin-right: auto;
    overflow: hidden; // 解决BFC

    .claim-merchant-submit__content {
      height: calc(100vh - 68px - 1px - 100px);
    }

    ::v-deep .klk-bottom-sheet-inner {
      width: 680px;
      left: calc((100vw - 680px) / 2);
    }

    .claim-merchant-submit__sectionContent {
      display: grid;
      grid-template-columns: 1fr 1fr; /* 两列等宽 */
      gap: 10px; /* 列间距为10px */
    }
  }
}
</style>
