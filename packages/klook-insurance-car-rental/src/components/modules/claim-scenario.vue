<template>
  <div
    ref="container"
    :data-spm-page="pageSpm"
    :class="{
      'claim-scenario-container': true,
      'claim-scenario-container-desktop': !isMobile,
      'claim-scenario-container-mobile': isMobile,
      'claim-scenario-container-app': getIsApp(),
    }"
  >
    <klk-loading v-if="loadingStatus === 'loading'" />
    <div
      v-else
      :class="{
        'claim-scenario': true,
      }"
    >
      <DetailsHeader
        :platform="getPlatform()"
        :icon-back="true"
        :title="$t('176293-claim_reason')"
        :show-bottom-border="true"
        @click="handleBackClick(true)"
      />
      <div
        v-if="loadingStatus === 'success'"
        class="claim-scenario__content"
      >
        <div class="claim-scenario__tips">
          {{ data.tips }}
        </div>
        <div class="claim-scenario__select">
          <klk-tree
            ref="tree"
            :data="selectList"
            multiple
            @node-check-change="handleCheckChange"
          />
        </div>
      </div>
      <div
        v-if="loadingStatus === 'success'"
        class="claim-scenario__bottom"
      >
        <klk-button
          type="primary"
          reverse
          class="claim-scenario__bottomBtn"
          :data-spm-module="`ClaimNextStep?trg=manual&oid=mainbooking_id_${data.main_booking_no}&ext=${JSON.stringify(
            {
              BookingID: data.addon_booking_nos,
              PlanCode: data.plan_codes,
              PackageReferenceID: data.package_ref_id,
              CoveragePackage: data.coverage_package,
            }
          )}`"
          data-spm-virtual-item="__virtual"
          :disabled="submitDisabled"
          @click="handleSubmit"
        >
          {{ $t("206492-submit") }}
        </klk-button>
      </div>
      <klk-bottom-sheet
        v-if="isMobile && tipsSheetVisible"
        :visible.sync="tipsSheetVisible"
        height="50%"
        show-close
        header-divider
      >
        <div class="claim-scenario__sheetTips">
          {{ tips }}
        </div>
      </klk-bottom-sheet>
      <ErrorPanel
        v-if="loadingStatus === 'error'"
        @refresh="fetchData"
      />
    </div>
  </div>
</template>

<script lang="ts">
import { Component } from 'vue-property-decorator';
import {
  IconChevronDown,
  IconInformation,
  IconChevronUp,
  IconNext,
} from '@klook/klook-icons';
// import * as Checkbox from '@klook/klook-ui/lib/checkbox'
import Checkbox from '@klook/klook-ui/lib/checkbox';
import '@klook/klook-ui/lib/styles/components/checkbox.scss';
import {
  $colorTextPrimary,
  $colorTextLink,
} from '@klook/klook-ui/lib/utils/design-token-esm';
import { getAntiSpiderHead } from '../../utils';
import InsuranceDetails from '../common/details.vue';
import DetailsHeader from '../common/header.vue';
import ErrorPanel from '../common/error-panel.vue';
import ModuleBase from './base.vue';
import '@klook/klook-ui/lib/checkbox/style';

@Component({
  components: {
    ModuleBase,
    InsuranceDetails,
    DetailsHeader,
    IconChevronDown,
    IconInformation,
    IconChevronUp,
    IconNext,
    ErrorPanel,
  },
})
export default class ClaimScenario extends ModuleBase {
  colorTextPrimary: string = $colorTextPrimary;
  colorTextLink: string = $colorTextLink;
  api: string = '/v1/insuranceapisrv/outer/carrental/claim/getScenarioList';
  loadingStatus: string = 'loading'; // 'loading' | 'error' | 'success'
  errorMsg: string = '';

  data: any = {};
  selectList: any[] = [];
  selectedItems: any[] = [];
  submitDisabled: boolean = true;
  tipsSheetVisible: boolean = false;
  tips: string = '';

  get pageSpm() {
    const { addon_booking_nos, main_booking_no, plan_codes, coverage_package, package_ref_id } = this.data || {};
    return `Insurance_ClaimReason?trg=manual&oid=BookingID_${addon_booking_nos}&ext=${JSON.stringify(
      {
        MainBookingID: main_booking_no,
        PlanCode: plan_codes,
        PackageReferenceID: package_ref_id,
        CoveragePackage: coverage_package,
      }
    )}`;
  }

  created() {
    this.fetchData().then(() => {
      this.$nextTick(() => {
        this.fixBrowserBottomBar('claim-scenario-container', 'claim-scenario__content', 48 + 68)
      });
    });
  }

  handleClickReasonDeail(item: any) {
    this.tips = item.scenario_tip;
    this.tipsSheetVisible = true;
  }

  handleCheckChange() {
    const selectedNodes = this.$refs?.tree?.getCheckedNodes();
    this.submitDisabled = !selectedNodes.length;
  }

  handleCheckChangeV2(val: boolean, item: string) {
    if (val) {
      this.selectedItems.push(item);
    } else {
      const index = this.selectedItems.findIndex(
        (selectedItem) => selectedItem === item
      );
      if (index > -1) {
        this.selectedItems.splice(index, 1);
      }
    }
    this.submitDisabled = !this.selectedItems.length;
  }

  initSelectedItems() {
    const scenarioIds = (this.initData?.scenario_id_list || []).map(String);
    const items: any = []
    const list = this.data?.scenario_list || []
    list.forEach((item: any) => {
      if (scenarioIds.includes(String(item.id))) {
        items.push(item);
      }
      item.children?.forEach((child: any) => {
        if (scenarioIds.includes(String(child.id))) {
          items.push(child);
        }
      });
    });
    this.selectedItems = items;
    this.submitDisabled = !items.length;
  }

  handleSubmit() {
    const liabilityIds = this.selectedItems.reduce((acc: any[], item: any) => {
      return acc.concat(item.liability_id_list);
    }, []);
    const scenarioIds = this.selectedItems.map((item: any) => item.id);
    this.$emit('setStep', {
      newStep: 'claimInstruction',
      data: {
        liability_id_list: liabilityIds,
        scenario_id_list: scenarioIds,
      },
    });
  }

  isNodeChecked(id: string) {
    return this.selectedItems.some((item: any) => item.id === id);
  }

  initSelectList() {
    const list = this.data?.scenario_list || [];
    const _this = this;
    const { addon_booking_nos, main_booking_no, plan_codes, coverage_package, package_ref_id } = this.data || {};

    return list.map((item) => {
      return {
        ...item,
        expand: true,
        title: item.scenario_title,
        render(h) {
          const node = h(
            'div',
            {
              class: 'claim-scenario__selectItem',
              style: {
                display: 'flex',
                alignItems: 'flex-start',
                gap: '8px',
              },
            },
            [
              h(Checkbox, {
                style: item.children ? { display: 'none' } : {},
                on: {
                  change: (val) => {
                    _this.handleCheckChangeV2(val, item);
                  },
                },
                props: {
                  value: _this.isNodeChecked(item.id), // 设置默认勾选
                }
              }),
              h(
                'div',
                {
                  class: 'claim-scenario__selectItemTitle',
                  style: {
                    fontSize: '16px',
                    fontWeight: 500,
                    color: '#212121',
                    lineHeight: 1.5,
                  },
                },
                [item.scenario_title]
              ),
              item.scenario_content &&
              h(
                'div',
                {
                  class: 'claim-scenario__selectItemContent',
                  style: {
                    marginTop: '2px',
                    fontSize: '16px',
                    fontWeight: 400,
                  },
                },
                [item.scenario_content]
              ),
            ]
          );
          return node;
        },
        children: (item.children || []).map((child) => {
          // const isMobile = this.isMobile
          return {
            ...child,
            expand: false,
            title: child.scenario_title,
            render(h) {
              return h(
                'div',
                {
                  class: 'claim-scenario__selectChild',
                  style: {
                    marginTop: '-20px'
                  }
                },
                [
                  h(
                    'div',
                    {
                      class: 'claim-scenario__selectChildTitleContainer',
                      style: {
                        display: 'flex',
                        alignItems: 'flex-start',
                        gap: '8px',
                      },
                    },
                    [
                      h(Checkbox, {

                        attrs: {
                          'data-spm-module': `AddClaimReason?trg=manual&oid=mainbooking_id_${main_booking_no}&ext=${JSON.stringify(
                            {
                              BookingID: addon_booking_nos,
                              PlanCode: plan_codes,
                              PackageReferenceID: package_ref_id,
                              CoveragePackage: coverage_package,
                            }
                          )}`,
                          'data-spm-virtual-item': '__virtual',
                        },
                        on: {
                          change: (val) => {
                            _this.handleCheckChangeV2(val, child);
                          },
                        },
                        props: {
                          value: _this.isNodeChecked(child.id), // 设置默认勾选
                          // value: true
                        } 
                      }),
                      h(
                        'span',
                        {
                          class: 'claim-scenario__selectChildTitle',
                          style: {
                            fontSize: '16px',
                            fontWeight: 400,
                            color: '#212121',
                            lineHeight: 1.5,
                            marginTop: '-2px'
                          },
                        },
                        [child.scenario_title]
                      ),
                      _this.isMobile && h(
                        'span',
                        {
                          style: {
                            marginLeft: 'auto'
                          },
                        },
                      ),
                      child.scenario_tip &&
                      _this.isMobile &&
                      h(
                        'span',
                        {
                          class: 'claim-scenario__selectChildIcon',
                          style: {
                            cursor: 'pointer',
                            width: '16px',
                            height: '16px',
                          },
                          on: {
                            click: (e) => {
                              // 停止冒泡
                              e.stopPropagation();
                              _this.handleClickReasonDeail(child);
                            },
                          },
                        },
                        [h(IconInformation)]
                      ),
                    ]
                  ),
                  child.scenario_tip &&
                  !_this.isMobile &&
                  h(
                    'div',
                    {
                      class: 'claim-scenario__selectChildDesc',
                      style: {
                        fontSize: '16px',
                        fontWeight: 400,
                        marginTop: '4px',
                        color: '#757575',
                        marginLeft: '28px'
                      },
                    },
                    [child.scenario_tip]
                  ),
                ]
              );
            },
          };
        }),
      };
    });
  }

  fetchData() {
    return this.$axios
      .$get(this.api, { params: { booking_no: this.getBookingNo() } })
      .then((res) => {
        if (!res.success || !res.result) {
          this.loadingStatus = 'error';
          this.errorMsg = (res.error && res.error.message) || '';
          this.processRequestError(res)
          return;
        }
        this.data = res.result;
        this.initSelectedItems();
        this.selectList = this.initSelectList();
        this.loadingStatus = 'success';

        this.$nextTick(() => {
          this.$inhouse.track('pageview', this.$refs.container);
        });
        return res.result;
      })
      .catch(() => {
        this.loadingStatus = 'error';
      });
  }
}
</script>

<style lang="scss" scoped>
@import "./mixins.scss";

.claim-scenario-container {
  @include module-page;

  .claim-scenario {
    height: 100%;

    &__content {
      @include module-content;
      padding: 20px;
      background: $color-bg-4;

      ::v-deep .klk-tree-node {
        background: #ffffff;
        border-radius: 16px;
        margin-top: 12px;
      }

      ::v-deep .klk-tree-node-inner {
        border-radius: 16px;
        display: flex;
        align-items: center;
        &:hover {
          background-color: #ffffff;
        }
      }

      ::v-deep .klk-checkbox {
        margin-top: 0px;
      }
    }

    &__tips {
      @include font-body-m-bold;
      color: $color-text-primary;
    }

    &__select {
      ::v-deep .klk-tree-sub {
        .klk-tree-node-inner {
          padding-left: 16px !important;
          .klk-tree-node-title {
            padding-right: 0px !important;
            width: 100%;
          }
        }
      }
    }

    &__selectItem {
      display: flex;
      gap: 12px;
      align-items: flex-start;
    }

    &__selectItemHiddenCheckbox {
      visibility: hidden;
    }

    &__selectItemTitle {
      @include font-body-m-semibold;
      color: $color-text-primary;
    }

    &__selectItemContent {
      @include font-body-m-regular;
      color: $color-text-primary;
      margin-top: 2px;
    }

    &__selectChild {
      display: flex;
      align-items: center;
      gap: 9px;
    }

    &__selectChildTitle {
      @include font-body-m-regular;
      color: $color-text-primary;
    }

    &__selectChildDesc {
      color: $color-text-secondary;
      margin-top: 4px;
    }

    &__bottom {
      @include claim-bottom;
    }

    &__bottomBtn {
      @include claim-bottom-btn;
    }

    &__sheetTips {
      @include font-paragraph-m-regular;
      color: $color-text-primary;
      margin-top: 20px;
    }
  }
}

.claim-scenario-container-desktop {
  .claim-scenario {
    ::v-deep .klk-bottom-sheet-inner {
      width: 680px;
      left: calc(100vw - 680px);
    }
  }
}

.claim-scenario-container-mobile {
  @include module-page-mobile;

  .claim-scenario {
    .claim-scenario__content {
      @include module-content-mobile;
      padding: 20px 16px;
    }

    .claim-scenario__bottom {
      border: 0;
    }
  }
}

.claim-scenario-container-app {
  .claim-scenario {
    // .claim-scenario__content {
    //   @include module-content-app;
    // }

    // .claim-instruction__bottom {
    //   padding-bottom: 32px;
    // }
  }
}
</style>
