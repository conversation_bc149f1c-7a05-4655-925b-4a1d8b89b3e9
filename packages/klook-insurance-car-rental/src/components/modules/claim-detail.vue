<template>
  <div
    ref="container"
    :data-spm-page="pageSpm"
    :class="{
      'claim-detail-container': true,
      'claim-detail-container-mobile': isMobile,
      'claim-detail-container-app': getIsApp(),
    }"
  >
    <klk-loading v-if="loadingStatus === 'loading'" />
    <div
      v-else
      :class="{
        'claim-detail': true,
      }"
    >
      <!-- 按设计的要求，这个页面web/mweb均是展示返回按钮，不是关闭按钮 -->
      <DetailsHeader
        :platform="getPlatform()"
        :icon-back="true"
        :title="$t('176761-claim_details')"
        :show-bottom-border="true"
        @click="handleBackClick(true)"
      />
      <div
        v-if="loadingStatus === 'success'"
        class="claim-detail__content"
      >
        <div class="claim-detail__process">
          <klk-steps
            v-if="data.status_text_list && data.status_text_list.length"
            direction="vertical"
            :current="data.status_text_list.length"
          >
            <klk-step
              v-for="(item, index) in data.status_text_list"
              :key="index"
              :title="item.application_status_text"
              :content="item.application_status_ext_text"
            >
              <div
                v-if="item.application_status_text"
                slot="title"
              >
                <div class="step-title">
                  <span>{{ item.application_status_text }}</span>
                  <span
                    v-if="item.high_light_total_amount"
                    class="step-content-highlight"
                  >{{
                    item.high_light_total_amount }}</span>
                </div>
              </div>
              <div
                slot="content"
                class="step-desc"
              >
                <div>{{ item.log_time }}</div>
                <klk-markdown :content="item.application_status_ext_text" />
              </div>
              <img
                slot="icon"
                :src="statusMap[item.web_status_v2]"
                class="step-icon"
              >
            </klk-step>
          </klk-steps>
        </div>
        <div class="claim-detail__info">
          <div class="claim-detail__infoTitle">
            {{ data.claim_info_title }}
          </div>
          <div v-if="cliamInfo.claim_info_full_name" class="claim-detail__infoItem">
            <div class="claim-detail__infoItemTitle">
              {{ cliamInfo.claim_info_full_name_title }}
            </div>
            <div class="claim-detail__infoItemContent">
              {{ cliamInfo.claim_info_full_name }}
            </div>
          </div>
          <div v-if="cliamInfo.claim_info_insurance_type" class="claim-detail__infoItem">
            <div class="claim-detail__infoItemTitle">
              {{ cliamInfo.claim_info_insurance_type_title }}
            </div>
            <div class="claim-detail__infoItemContent">
              {{ cliamInfo.claim_info_insurance_type }}
            </div>
          </div>
          <div v-if="cliamInfo.claim_info_start_date" class="claim-detail__infoItem">
            <div class="claim-detail__infoItemTitle">
              {{ cliamInfo.claim_info_start_date_title }}
            </div>
            <div class="claim-detail__infoItemContent">
              {{ cliamInfo.claim_info_start_date }}
            </div>
          </div>
          <div v-if="cliamInfo.claim_info_accident_details" class="claim-detail__infoItem">
            <div class="claim-detail__infoItemTitle">
              {{ cliamInfo.claim_info_accident_details_title }}
            </div>
            <div
              class="claim-detail__infoItemContent claim-detail__accidentDetails"
              :class="{
                'claim-detail__accidentDetailsExpanded': !showSeeMore,
              }"
            >
              {{ cliamInfo.claim_info_accident_details }}
            </div>
            <div
              v-if="showSeeMore"
              class="claim-detail__button"
              @click="handleSeeMore"
            >
              {{ $t("206489") }}
            </div>
          </div>
          <div v-if="cliamInfo.claim_info_claim_amount" class="claim-detail__infoItem">
            <div class="claim-detail__infoItemTitle">
              {{ cliamInfo.claim_info_claim_amount_title }}
            </div>
            <div class="claim-detail__infoItemContent">
              {{ cliamInfo.claim_info_claim_amount }}
            </div>
          </div>
        </div>
        <div class="claim-detail__material">
          <div class="claim-detail__materialTitle">
            {{ data.claim_material_title }}
          </div>
          <div
            v-for="(materialsItem, materialsIndex) in materialInfo"
            :key="materialsIndex"
            class="claim-detail__materialCategory"
          >
            <div class="claim-detail__materialCategoryName">
              {{ materialsItem.material_category_name }}
            </div>
            <div
              v-for="(
                categoryItem, categoryIndex
              ) in materialsItem.material_category_list"
              :key="categoryIndex"
              class="claim-detail__materialCategoryContent"
            >
              <div class="claim-detail__materialFileName">
                {{ categoryItem.material_name }}
              </div>
              <div class="claim-detail__materialFileLink">
                <a
                  v-for="(filesItem, filesIndex) in categoryItem.material_files"
                  :key="filesIndex"
                  target="_blank"
                  class="claim-detail__materialFileLinkItem"
                  @click="
                    handleClickFile(
                      filesItem.material_file_url,
                      filesItem.material_type
                    )
                  "
                >
                  {{ filesItem.material_file_name }}
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
      <ErrorPanel v-if="loadingStatus === 'error'" @refresh="fetchData" />
    </div>
    <klk-image-viewer
      :open.sync="openImageViewer"
      :value="0"
      :images="images"
      :width="960"
    />
  </div>
</template>

<script lang="ts">
import { Component, Watch } from 'vue-property-decorator';
import {
  IconChevronDown,
  IconInformation,
  IconChevronUp,
  IconNext,
} from '@klook/klook-icons';
import {
  $colorTextPrimary,
  $colorTextLink,
  $colorSuccess,
} from '@klook/klook-ui/lib/utils/design-token-esm';
import InsuranceDetails from '../common/details.vue';
import DetailsHeader from '../common/header.vue';
import ErrorPanel from '../common/error-panel.vue';
import { copyToClipboard, getAntiSpiderHead } from '../../utils';
import ModuleBase from './base.vue';

@Component({
  components: {
    ModuleBase,
    InsuranceDetails,
    DetailsHeader,
    IconChevronDown,
    IconInformation,
    IconChevronUp,
    IconNext,
    ErrorPanel,
  },
})
export default class ClaimDetail extends ModuleBase {
  colorTextPrimary: string = $colorTextPrimary;
  colorTextLink: string = $colorTextLink;
  colorSuccess: string = $colorSuccess;
  api: string = '/v1/insuranceapisrv/outer/carrental/claim/getOneClaimDetail';
  loadingStatus: string = 'loading'; // 'loading' | 'error' | 'success'
  errorMsg: string = '';

  data: any = {};
  claimProcessVisible: boolean = false;
  showSeeMore: boolean = true;
  statusMap: Record<string, string> = {
    1: 'https://res.klook.com/image/upload/icon_feedback_pending_fill_l_1_qhvern.jpg', // 进行中（黄色）
    2: 'https://res.klook.com/image/upload/icon_feedback_pending_fill_l_2_nulxtp.jpg', // 进行中（蓝色）
    3: 'https://res.klook.com/image/upload/__jb9htr.jpg', // 已完成(绿色)
    4: 'https://res.klook.com/image/upload/___KIcon_KIcons.icon_clear_filled_size__32_colors__colorScheme.colorTextPrimary_oap3gm.jpg', // 拒绝
  };
  openImageViewer: boolean = false;
  images: string[] = [];
  observer: MutationObserver | null = null;

  get cliamInfo() {
    return this.data.claim_info || {};
  }

  get materialInfo() {
    return this.data.claim_materials || [];
  }

  get pageSpm() {
    const { addon_booking_nos, main_booking_no, plan_codes, coverage_package, package_ref_id } = this.data || {};

    return `Insurance_ClaimResultDetail?trg=manual&oid=BookingID_${addon_booking_nos}&ext=${JSON.stringify(
      {
        MainBookingID: main_booking_no,
        PlanCodes: plan_codes,
        PackageReferenceID: package_ref_id,
        CoveragePackage: coverage_package,
      }
    )}`;
  }

  @Watch('data.claim_info.claim_info_accident_details')
  onAccidentDetailsChange(val) {
    this.$nextTick(() => {
      this.judgeShowSeeMore();
    });
  }

  created() {
    this.fetchData().then(() => {
      this.$nextTick(() => {
        this.fixBrowserBottomBar('claim-detail-container', 'claim-detail__content', 48)
      });
    });
  }

  judgeShowSeeMore() {
    const targetNode = document.querySelector(
      '.claim-detail__accidentDetails'
    ) as HTMLElement;
    if (targetNode) {
      // 获取元素高度
      const height = targetNode.offsetHeight;
      // 根据高度判断是否显示 "See More"
      this.showSeeMore = height >= 72;
    }
  }

  openPDF(url: string) {
    this.openExternalLink(url)
  }

  handleClickFile(url, type) {
    if (!url) {
      return;
    }
    if (type === 'pdf') {
      this.openPDF(url);
    } else {
      this.openImageViewer = true;
      this.images = [url];
    }
  }

  handleSeeMore() {
    this.showSeeMore = false;
  }

  async handleClickCopy() {
    const text = this.data?.application_no;
    const result = await copyToClipboard(text);
    if (result) {
      console.info('copyInfo', result, this);
      this.$toast(this.$t('176361'));
    }
  }

  fetchData() {
    return this.$axios
      .$get(this.api, { params: { claim_id: this.initData.application_no } })
      .then((res) => {
        if (!res.success || !res.result) {
          this.loadingStatus = 'error';
          this.errorMsg = (res.error && res.error.message) || '';
          this.processRequestError(res)
          return;
        }
        this.data = res.result;
        this.loadingStatus = 'success';
        this.$nextTick(() => {
          this.$inhouse.track('pageview', this.$refs.container);
        });
        return res.result;
      })
      .catch(() => {
        this.loadingStatus = 'error';
      });
  }
}
</script>

<style lang="scss" scoped>
@import "./mixins.scss";

.claim-detail-container {
  @include module-page;
  background: $color-bg-4;

  .claim-detail {
    height: 100%;

    &__content {
      @include module-content;
      height: calc(100vh - 64px);
    }

    &__process {
      background: $color-bg-1;
      padding: 36px 22px;

      .step-title {
        @include font-body-m-bold;
        color: $color-text-primary;
        display: flex;
        align-items: center;

        .step-content-highlight {
          color: #ff5b01;
        }
      }

      .step-desc {
        @include font-body-s-regular;
        color: $color-text-secondary;
        display: flex;
        flex-direction: column;
        gap: 4px;

        ::v-deep .klk-markdown {
          @include markdown-style;
          @include font-body-s-regular;
          color: $color-text-secondary;
          a {
            @include font-body-s-regular;
          }
        }
      }

      .step-icon {
        width: 32px;
        height: auto;
        margin-left: -4px;
      }
    }

    &__info {
      background: $color-bg-1;
      margin: 20px;
      border-radius: 16px;
      // padding: 24px 20px;
      padding: 16px;
      // padding-bottom: 72px;
    }

    &__infoTitle {
      @include font-heading-xs;
      color: $color-text-primary;
      margin-bottom: 12px;
    }

    &__infoItem {
      display: flex;
      flex-direction: column;

      &:not(:first-child) {
        margin-top: 16px;
      }
    }

    &__infoItemTitle {
      @include font-body-s-regular;
      color: $color-text-placeholder;
    }

    &__infoItemContent {
      @include font-body-m-bold;
      color: $color-text-primary;
      font-weight: 400;
    }

    &__button {
      @include font-body-m-regular;
      color: $color-text-primary;
      text-decoration: underline;
      cursor: pointer;
      margin-top: 8px;
    }

    &__accidentDetails {
      display: -webkit-box;
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
      -webkit-line-clamp: 3;
      /* 限制显示的行数为3行 */
      max-height: 4.5em;
      /* 例如3行文本的高度 */
      line-height: 1.5em;
      /* 设置行高 */
      transition: max-height 0.3s ease;
    }

    &__accidentDetailsExpanded {
      -webkit-line-clamp: unset;
      /* 去除行限制 */
      max-height: none;
      overflow: visible;
    }

    &__material {
      background: $color-bg-1;
      margin: 20px;
      border-radius: 16px;
      // padding: 24px 20px;
      padding: 16px;
    }

    &__materialTitle {
      @include font-heading-xs;
      color: $color-text-primary;
      margin-bottom: 12px;
    }

    &__materialItem {
      @include font-heading-xs;
      color: $color-text-primary;
      margin-bottom: 12px;
    }

    &__materialCategory {
      padding: 20px 0px;

      &:not(:first-child):not(:nth-child(2)) {
        border-top: 1px solid $color-border-dim;
      }

      &:nth-child(2) {
        padding-top: 0px;
      }

      &:last-child {
        padding-bottom: 0px;
      }
    }

    &__materialCategoryName {
      @include font-paragraph-m-regular;
      color: $color-text-primary;
    }

    &__materialCategoryContent {
      margin-top: 8px;
    }

    &__materialFile {
      margin-top: 8px;
    }

    &__materialFileName {
      @include font-body-s-regular;
      color: $color-text-placeholder;
    }

    &__materialFileLink {
      display: flex;
      flex-direction: column;
    }

    &__materialFileLinkItem {
      @include font-body-s-regular;
      text-decoration: none;
      color: $color-text-link;
      cursor: pointer;
      word-wrap: break-word;
    }

    &__materialFileImg {
      display: flex;
      gap: 4px;
      cursor: pointer;

      >img {
        width: 69px;
        height: 69px;
        border-radius: 12px;
      }
    }
  }
}

.claim-detail-container-mobile {
  @include module-page-mobile;

  .claim-detail {
    .claim-detail__content {
      @include module-content-mobile($hide-bottom: true);
    }

    .claim-detail__info {
      margin: 12px;
    }

    .claim-detail__material {
      margin: 12px;
    }

    .claim-detail__materialCategory {
      padding: 16px 0px;
      &:last-child {
        padding-bottom: 0px;
      }
    }

    .claim-detail__materialFileLink {
      display: flex;
      flex-direction: column;
      gap: 16px;
      margin-top: 16px;
    }
  }
}

.claim-detail-container-app {
  .claim-detail {
    // .claim-detail__content {
    //   @include module-content-app;
    //   height: 100vh;
    // }
  }
}
</style>
