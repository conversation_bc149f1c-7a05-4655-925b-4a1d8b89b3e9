<script lang="ts">
import { Component, Vue, Prop, Inject } from 'vue-property-decorator';

@Component({
  components: {},
})
export default class ModuleBase extends Vue {
  @Inject() closeContainer!: Function;
  @Inject() getPlatform!: () => String;
  @Inject() getIsApp!: () => String;
  @Inject() getBookingNo!: () => String;
  @Inject() getInsOrderNo!: () => string;
  @Inject() getLanguage!: () => string;
  @Inject() refreshCard!: () => void;
  @Prop() initData!: any;
  @Prop() stepStack!: any;
  @Prop() setStepStack!: any;

  get isMobile() {
    return this.getPlatform() !== 'desktop';
  }

  get isHeaderShowBack() {
    return this.isMobile
  }

  openExternalLink(url: string) {
    if (this.getIsApp()) {
      this.$emit('setStep', {
        newStep: 'externalLink',
        data: {
          link: url,
        },
      });
      return
    }
    window.open(url, '_blank');
  }

  // 手机浏览器底部有导航栏，需要对页面高度进行调整
  // 手机浏览器100vh高度会被浏览器底部导航栏遮挡，window.innerHeight才是准确的可视区域高度
  fixBrowserBottomBar(containerName: string, contentName: string, contentIgnoreHeight: number = 0) {
    if (this.isMobile && !this.getIsApp()) {
      const container = document.querySelector(`.${containerName}`);
      const content = document.querySelector(`.${contentName}`);
      if (container) {
        container.setAttribute('style', `height: ${window.innerHeight}px;`);
      }
      if (content) {
        content.setAttribute('style', `height: calc(${window.innerHeight}px - ${contentIgnoreHeight}px);`);
      }
    }
  }

  handleBackClick(lastStep?: boolean) {
    if (lastStep && this.stepStack.length > 1) {
      this.$emit('back');
    } else {
      this.closeContainer();
    }
  }

  processRequestError(res: any) {
    if (res.error && res.error?.code === '4001') {
      this.$alert(this.$t('2769-profile.bookings.account_logout'), this.$t('20-global.tips.header')).then((res: any) => {
        if (res.result) {
          window.location.href = this.$href('/signin')
        }
      })
    } else {
      this.$toast(res.error?.message);
    }
  }
}
</script>
