<template>
  <div
    ref="container"
    :data-spm-page="pageSpm"
    :class="{
      'insurance-package-details-container': true,
      'insurance-package-details-container-mobile': isMobile,
      'insurance-package-details-container-app': getIsApp(),
    }"
  >
    <klk-loading v-if="loadingStatus === 'loading'" />
    <div
      v-else
      :class="{
        'insurance-package-details': true,
      }"
    >
      <DetailsHeader
        :platform="getPlatform()"
        :icon-back="isHeaderShowBack"
        :title="$t('205705')"
        :show-bottom-border="true"
        @click="handleBackClick"
      />
      <InsuranceDetails
        v-if="loadingStatus === 'success'"
        class="insurance-detail-content"
        :data="data"
        :is-mobile="isMobile"
      />
      <div
        class="insurance-details__bottomSection"
        v-if="loadingStatus === 'success'"
      >
        <div
          v-if="showPolicyLink || showClaimGuideLink"
          :class="{
            'insurance-details__policy': true,
          }"
        >
          <div
            v-if="showPolicyLink"
            class="insurance-details__policyItem"
            :data-spm-module="`PolicyWordingBtn?oid=packagecode_${
              data.package_code
            }&ext=${JSON.stringify({
              PlanCode: data.goods_plan_code_list,
            })}`"
            data-spm-virtual-item="__virtual"
          >
            <a
              class="insurance-details__policyLink"
              target="_blank"
              @click="handlePolicyLinkClick(data.insurance_info.link)"
            >
              {{ data.insurance_info.button_text }}
            </a>
            <IconNext
              class="insurance-details__policyIcon"
              theme="outline"
              size="16"
              :fill="colorTextLink"
            />
          </div>
          <div
            v-if="showClaimGuideLink"
            class="insurance-details__policyItem"
            :data-spm-module="`ClaimGuideBtn?oid=packagecode_${
              data.package_code
            }&ext=${JSON.stringify({
              PlanCode: data.goods_plan_code_list,
            })}`"
            data-spm-virtual-item="__virtual"
          >
            <a
              class="insurance-details__policyLink"
              target="_blank"
              @click="handleClaimGuideClick()"
            >
              {{ claimInfo.button_text }}
            </a>
            <IconNext
              class="insurance-details__policyIcon"
              theme="outline"
              size="16"
              :fill="colorTextLink"
            />
          </div>
        </div>
        <div
          v-if="claimButtonVisible || claimRecordButtonVisible"
          :class="{
            'insurance-details__bottomButton': true,
          }"
        >
          <klk-button
            v-if="claimRecordButtonVisible"
            type="outlined"
            class="insurance-details__bottomButtonItem"
            :data-spm-module="`ClaimBtn?oid=mainbooking_id_${
              data.main_booking_no
            }&ext=${JSON.stringify({
              BookingID: data.addon_booking_nos,
              PlanCode: data.goods_plan_code_list,
              PackageReferenceID: data.package_ref_id,
              CoveragePackage: data.package_code,
            })}`"
            data-spm-virtual-item="__virtual"
            @click="handleClickClaimRecord"
          >
            {{ $t("206484-claim_record") }}
          </klk-button>
          <klk-button
            v-if="claimButtonVisible"
            type="primary"
            reverse
            class="insurance-details__bottomButtonItem"
            :data-spm-module="`ClaimSubmissionBtn?oid=mainbooking_id_${
              data.main_booking_no
            }&ext=${JSON.stringify({
              BookingID: data.addon_booking_nos,
              PlanCode: data.goods_plan_code_list,
              PackageReferenceID: data.package_ref_id,
              CoveragePackage: data.package_code,
            })}`"
            data-spm-virtual-item="__virtual"
            @click="handleClickClaim"
          >
            {{ $t("176273-claim") }}
          </klk-button>
        </div>
      </div>
      <ErrorPanel v-if="loadingStatus === 'error'" @refresh="fetchData" />
    </div>
  </div>
</template>

<script lang="ts">
import { Component } from "vue-property-decorator";
import {
  IconChevronDown,
  IconInformation,
  IconChevronUp,
  IconNext,
} from "@klook/klook-icons";
import {
  $colorTextPrimary,
  $colorTextLink,
} from "@klook/klook-ui/lib/utils/design-token-esm";
import { getAntiSpiderHead } from "../../utils";
import InsuranceDetails from "../common/details.vue";
import DetailsHeader from "../common/header.vue";
import ErrorPanel from "../common/error-panel.vue";
import ModuleBase from "./base.vue";

@Component({
  components: {
    ModuleBase,
    InsuranceDetails,
    DetailsHeader,
    IconChevronDown,
    IconInformation,
    IconChevronUp,
    IconNext,
    ErrorPanel,
  },
})
export default class PackageDetails extends ModuleBase {
  colorTextPrimary: string = $colorTextPrimary;
  colorTextLink: string = $colorTextLink;
  api: string =
    "/v1/insuranceapisrv/outer/carrental/booking_detail/get_insurance_coverage_details";
  loadingStatus: string = "loading"; // 'loading' | 'error' | 'success'
  errorMsg: string = "";

  data: any = {};

  get showPolicyLink() {
    return this.data?.insurance_info?.button_text && this.data?.insurance_info?.link;
  }

  get showClaimGuideLink() {
    return this.claimInfo?.button_text
  }

  get claimInfo() {
    const info = this.data?.insurance_extend_desc?.claim_guide_info || "{}";
    const obj = JSON.parse(info);
    if (obj && typeof obj === "object") {
      return obj;
    }
    return {};
  }

  get claimButtonVisible() {
    return this.data?.claim_btn_status?.claim_btn_status !== "not visible";
  }

  get claimRecordButtonVisible() {
    return (
      this.data?.claim_btn_status?.claim_record_btn_status !== "not visible"
    );
  }

  get pageSpm() {
    return `Insurance_CarRentalPlanDetails?trg=manual&oid=packagecode_${
      this.data?.package_code
    }&ext=${JSON.stringify({
      PlanCode: this.data?.goods_plan_code_list,
    })}`;
  }

  created() {
    this.fetchData().then(() => {
      this.$nextTick(() => {
        this.fixBrowserBottomBar(
          "insurance-package-details-container",
          "insurance-detail-content",
          64
        );
      });
    });
  }

  handlePolicyLinkClick(link: string) {
    this.openExternalLink(link)
  }

  handleClickClaimRecord() {
    this.$emit("setStep", {
      newStep: "claimRecord",
      data: {
        goods_codes: this.data?.insurance_extend_desc?.goods_codes,
      },
    });
  }

  handleClickClaim() {
    const hasDraft = this.data?.claim_btn_status?.exist_claim_draft || false;
    if (hasDraft) {
      this.$emit("setStep", {
        newStep: "claimSubmit",
        data: {
          liability_id_list:
            this.data?.claim_btn_status?.liability_id_list || [],
          scenario_id_list: this.data?.claim_btn_status?.scenario_id_list || [],
          is_draft: true,
        },
      });
    } else {
      this.$emit("setStep", {
        newStep: "claimScenario",
        data: {},
      });
    }
  }

  handleClaimGuideClick() {
    this.$emit("setStep", {
      newStep: "claimProcess",
      data: {
        target: "odp",
      },
    });
  }

  fetchData() {
    const params = {
      car_rental_booking_query_info: {
        car_rental_booking_ref_no: this.getBookingNo(),
      },
    };
    return this.$axios
      .$post(this.api, params)
      .then((res) => {
        if (!res.success || !res.result) {
          this.loadingStatus = "error";
          this.errorMsg = (res.error && res.error.message) || "";
          this.processRequestError(res);
          return;
        }
        this.loadingStatus = "success";
        this.data = res.result;

        this.$nextTick(() => {
          this.$inhouse.track("pageview", this.$refs.container);
        });
        return res.result;
      })
      .catch(() => {
        this.loadingStatus = "error";
      });
  }
}
</script>

<style lang="scss" scoped>
@import "./mixins.scss";

.insurance-package-details-container {
  @include module-page;
  height: 100vh;
  overflow-y: hidden;

  .insurance-package-details {
    height: 100%;
    .insurance-detail-content {
      padding: 20px 32px;
      height: calc(100vh - 64px);
      overflow-y: scroll;
      padding-bottom: 150px;
    }

    .insurance-details__bottomSection {
      position: sticky;
      bottom: 0px;
      background: #ffffff;
      z-index: 10;
    }

    .insurance-details__policy {
      @include font-body-s-regular-v2;
      color: $color-text-link;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;
      left: 0px;
      background: #ffffff;
      z-index: 10;
      padding: 0px 32px;
      height: 40px;

      &Item {
        display: flex;
        align-items: center;
        gap: 6px;
        max-width: 48%;

        a {
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
      }

      &Icon {
        margin-top: 4px;
      }
    }

    .insurance-details__bottomButton {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;
      left: 0px;
      padding: 12px 20px;
      gap: 10px;
      background: #ffffff;
      z-index: 10;
      border-top: 1px solid $color-border-normal;
    }

    .insurance-details__bottomButtonItem {
      @include claim-bottom-btn;
    }
  }
}

.insurance-package-details-container-mobile {
  @include module-page-mobile;

  .insurance-package-details {
    .insurance-detail-content {
      padding: 14px 20px;
      height: calc(100vh - 48px);
      padding-bottom: 100px;
    }

    .insurance-details__policy {
      padding: 0px 20px;
    }

    .insurance-details__bottomButton {
      border-top: 0px;
    }
  }
}

.insurance-package-details-container-app {
  .insurance-package-details {
    // .insurance-detail-content {
    //   height: calc(100vh - 88px - 40px);
    //   padding-bottom: 100px;
    // }

    // .insurance-details__bottomButton {
    //   padding-bottom: 32px;
    // }

    // .insurance-details__policy {
    //   bottom: 88px;
    // }
  }
}
</style>
