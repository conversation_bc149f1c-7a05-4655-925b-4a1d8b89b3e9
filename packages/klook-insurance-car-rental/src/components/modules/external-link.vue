<template>
  <div
    ref="container"
    :class="{
      'external-link-container': true,
      'external-link-container-mobile': isMobile,
      'external-link-container-app': getIsApp(),
    }"
  >
    <div
      :class="{
        'external-link': true,
      }"
    >
      <DetailsHeader
        :platform="getPlatform()"
        :icon-back="true"
        :show-bottom-border="true"
        @click="handleBackClick(true)"
      />
      <div
        class="external-link__content"
      >
        <iframe
          class="external-link__iframe"
          :src="link"
          width="100%"
          height="100%"
          frameborder="0"
        ></iframe>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Watch } from 'vue-property-decorator';
import ModuleBase from './base.vue';
import DetailsHeader from '../common/header.vue';

@Component({
  components: {
    DetailsHeader
  },
})
export default class ExternalLink extends ModuleBase {
  loadingStatus: string = ''; // 'loading' | 'error' | 'success'
  errorMsg: string = '';
  link: string = '';

  @Watch('initData.link', { immediate: true, deep: true })
  onExternalLinkChange(newLink: string) {
    this.link = newLink;
  }
  
  handleRefresh() {
    const iframeNode = document.querySelector('.external-link__iframe') as HTMLIFrameElement | null;
    if (iframeNode && iframeNode.contentWindow) {
      iframeNode.contentWindow.location.reload();
    }
  }
}
</script>

<style lang="scss" scoped>
@import "./mixins.scss";

.external-link-container {
  @include module-page;
  background: $color-bg-4;

  .external-link {
    height: 100%;

    &__content {
      @include module-content;
      height: calc(100vh - 64px);
    }

  }
}

.external-link-container-mobile {
  @include module-page-mobile;

  .external-link {
    .external-link__content {
      @include module-content-mobile($hide-bottom: true);
    }
  }
}

.external-link-container-app {
  .external-link {
    // .claim-detail__content {
    //   @include module-content-app;
    //   height: 100vh;
    // }
  }
}
</style>
