<template>
  <div
    ref="container"
    :data-spm-page="pageSpm"
    :class="{
      'claim-submit-container': true,
      'claim-submit-container-desktop': !isMobile,
      'claim-submit-container-mobile': isMobile,
      'claim-submit-container-app': getIsApp(),
    }"
  >
    <klk-loading v-if="loadingStatus === 'loading'" />
    <div
      v-else
      :class="{
        'claim-submit': true,
      }"
    >
      <DetailsHeader
        :platform="getPlatform()"
        :icon-back="isHeaderShowBack"
        :title="$t('176286-claim_submit')"
        :show-bottom-border="true"
        @click="handleBackClickSelf"
      />
      <div v-if="loadingStatus === 'success'" class="claim-submit__content">
        <div class="claim-submit__step">
          <klk-steps :current="formStep">
            <klk-step
              v-for="(item, index) in stepList"
              :key="index"
              :title="item"
            >
              <!-- 填写完成 -->
              <IconCheckCircle
                v-if="getTabCompleteStatus(index) === 'completed'"
                slot="icon"
                theme="filled"
                size="32"
                :fill="colorSuccess"
              />
              <!-- 填写中 -->
              <div
                v-else-if="getTabCompleteStatus(index) === 'ongoing'"
                slot="icon"
                class="claim-submit__stepIcon"
              >
                <span>{{ index + 1 }}</span>
              </div>
              <!-- 未填写 -->
              <div v-else slot="icon" class="claim-submit__stepIconNotStart">
                <span>{{ index + 1 }}</span>
              </div>
            </klk-step>
          </klk-steps>
        </div>
        <!-- 基本信息 -->
        <div v-show="formStep === 0" class="claim-submit__tab">
          <div class="claim-submit__section">
            <klk-section-title size="small" decorative-line>
              {{ contactInfoFormTitle }}
            </klk-section-title>
            <klk-form
              ref="contactInfoForm"
              :model="contactInfoForm"
              :rules="contactInfoFormRules"
              auto-locate-error
              class="claim-submit__form"
            >
              <klk-form-item :label="$t('172120-first_name')" prop="first_name">
                <klk-input
                  v-model="contactInfoForm.first_name"
                  size="small"
                  :maxlength="90"
                  :placeholder="$t('16186-please_enter')"
                />
              </klk-form-item>
              <klk-form-item :label="$t('172124-last_name')" prop="last_name">
                <klk-input
                  v-model="contactInfoForm.last_name"
                  size="small"
                  :maxlength="90"
                  :placeholder="$t('16186-please_enter')"
                />
              </klk-form-item>
              <klk-form-item
                :label="$t('206523-id_type')"
                prop="certificate_type"
              >
                <klk-select
                  v-model="contactInfoForm.certificate_type"
                  size="small"
                  :placeholder="$t('12022-please_select')"
                >
                  <klk-option
                    v-for="(item, index) in idTypeOptions"
                    :key="index"
                    :value="item.value"
                    :label="item.label"
                  />
                </klk-select>
              </klk-form-item>
              <klk-form-item
                :label="$t('206524-id_number')"
                prop="certificate_number"
              >
                <klk-input
                  v-model="contactInfoForm.certificate_number"
                  size="small"
                  :maxlength="90"
                  :placeholder="$t('16186-please_enter')"
                />
              </klk-form-item>
              <klk-row
                type="flex"
                align-items="flex-start"
                style="margin-bottom: 20px"
              >
                <klk-col :span="10">
                  <klk-form-item
                    prop="country_code"
                    :label="$t('16181-phone_number')"
                  >
                    <klk-select
                      v-model="contactInfoForm.country_code"
                      size="small"
                      :placeholder="$t('12022-please_select')"
                    >
                      <klk-option
                        v-for="(item, index) in areaNumberOptions"
                        :key="index"
                        :value="item.value"
                        :label="item.label"
                      />
                    </klk-select>
                  </klk-form-item>
                </klk-col>
                <klk-col :span="14">
                  <klk-form-item
                    label="phone number"
                    prop="phone"
                    class="claim-submit__hiddenFormItem"
                  >
                    <klk-input
                      v-model="contactInfoForm.phone"
                      size="small"
                      :maxlength="90"
                      :placeholder="$t('16186-please_enter')"
                    />
                  </klk-form-item>
                </klk-col>
              </klk-row>
              <klk-form-item :label="$t('16182-email')" prop="email">
                <klk-input
                  v-model="contactInfoForm.email"
                  size="small"
                  :maxlength="90"
                  :placeholder="$t('16186-please_enter')"
                />
              </klk-form-item>
            </klk-form>
          </div>
        </div>
        <!-- 赔付信息 -->
        <div v-show="formStep === 1" class="claim-submit__tab">
          <div class="claim-submit__section">
            <klk-section-title size="small" decorative-line>
              {{ claimInfoFormTitle }}
            </klk-section-title>
            <klk-form
              ref="claimInfoForm"
              :model="claimInfoForm"
              :rules="claimInfoFormRules"
              auto-locate-error
              class="claim-submit__form"
            >
              <klk-form-item :label="$t('209702-name')" prop="full_name">
                <klk-input
                  v-model="claimInfoForm.full_name"
                  size="small"
                  :maxlength="90"
                  :placeholder="$t('16186-please_enter')"
                />
              </klk-form-item>
              <klk-form-item
                :label="$t('206533-accident_time')"
                prop="start_date"
                class="claim-submit__formItemDate"
              >
                <div @click="handledDatePickerFocus">
                  <klk-input
                    readonly
                    size="small"
                    :value="dateInputValue"
                    :placeholder="$t('12022-please_select')"
                  >
                    <IconCalendar
                      slot="append"
                      class="claim-submit__dateIcon"
                      theme="outline"
                      size="16"
                      :fill="colorTextPrimary"
                    />
                  </klk-input>
                </div>
                <!-- 移动端时间选择组件 -->
                <klk-datetime-picker
                  v-if="isMobile"
                  style="z-index: 5000; position: relative"
                  type="time"
                  :title="$t('15208-select_time')"
                  :transfer="true"
                  :open.sync="mobileTimerPickerOpen"
                  :value="
                    draftDate ? getDate(`${draftDate} ${draftTime}`) : undefined
                  "
                  @confirm="handleMobileTimeConfirm"
                  @cancel="mobileTimerPickerOpen = false"
                />
                <!-- 移动端日期选择组件 -->
                <klk-bottom-sheet
                  v-if="isMobile"
                  :visible.sync="showDatePicker"
                  :title="$t('12752-select_date')"
                  show-close
                  header-divider
                  @close="handleMobileDateSelectCancel"
                >
                  <!-- 滚动模式如果可选日期[mix,max]在同一个月内，那么日历面板只会显示一个月 -->
                  <!-- 需要给marign-bottom，否则显示多个月的时候会被底部按钮遮挡 -->
                  <klk-date-picker
                    width="auto"
                    :vertical-scroll="true"
                    :date="draftDate ? getDate(draftDate) : undefined"
                    :min-date="
                      claimInfoForm.effective_date_start
                        ? new Date(claimInfoForm.effective_date_start)
                        : undefined
                    "
                    :max-date="
                      claimInfoForm.effective_date_end
                        ? new Date(claimInfoForm.effective_date_end)
                        : undefined
                    "
                    style="margin-bottom: 148px"
                    @change="handleMobileDateChange"
                  />
                  <div
                    :class="{
                      'claim-submit__datePickerBtn': true,
                      'claim-submit__datePickerBtnAPP': getIsApp(),
                    }"
                  >
                    <div
                      class="claim-submit__timePickerBtn"
                      @click="handleTimePickerClick"
                    >
                      <div class="claim-submit__timePickerBtnLabel">
                        {{ $t("206533-accident_time") }}
                      </div>
                      <div class="claim-submit__timePickerBtnRight">
                        <span class="claim-submit__timePickerBtnValue">{{
                          draftTime
                        }}</span>
                        <span class="claim-submit__timePickerBtnIcon">
                          <IconChevronDown
                            theme="outline"
                            size="16"
                            :fill="colorTextPrimary"
                          />
                        </span>
                      </div>
                    </div>
                    <klk-button
                      block
                      type="primary"
                      @click="handleMobileDateConfirm"
                    >
                      {{ $t("12313-confirm") }}
                    </klk-button>
                  </div>
                </klk-bottom-sheet>
                <!-- 桌面端日期选择组件 -->
                <klk-poptip
                  v-if="!isMobile && showDatePicker"
                  v-model="showDatePicker"
                  placement="bottom-end"
                  trigger="none"
                >
                  <div class="claim-submit__datePickerPoptip">
                    <div class="claim-submit__datePickerLeft">
                      <div class="claim-submit__datePickerLeftHead">
                        {{ $t("12752-select_date") }}
                      </div>
                      <klk-date-picker
                        :date="draftDate ? getDate(draftDate) : undefined"
                        view-switchable
                        :width="isMobile ? '300' : '340'"
                        :min-date="
                          claimInfoForm.effective_date_start
                            ? new Date(claimInfoForm.effective_date_start)
                            : undefined
                        "
                        :max-date="
                          claimInfoForm.effective_date_end
                            ? new Date(claimInfoForm.effective_date_end)
                            : undefined
                        "
                        @change="handleDateChange"
                      />
                    </div>
                    <div class="claim-submit__datePickerRight">
                      <div class="claim-submit__datePickerRightHead">
                        {{ $t("15208-select_time") }}
                      </div>
                      <div class="claim-submit__datePickerRightContent">
                        <div class="claim-submit__datePickerSelect">
                          <div class="claim-submit__datePickerSelectLeft">
                            <TimeSelector
                              :list="hoursSelectOptions"
                              :default="{
                                label: draftTime.split(':')[0],
                                value: draftTime.split(':')[0],
                              }"
                              @choose="handleTimeSelectChange($event, 0)"
                            />
                          </div>
                          <div class="claim-submit__datePickerSelectPlace">
                            :
                          </div>
                          <div class="claim-submit__datePickerSelectRight">
                            <TimeSelector
                              :list="minutesSelectOptions"
                              :default="{
                                label: draftTime.split(':')[1],
                                value: draftTime.split(':')[1],
                              }"
                              @choose="handleTimeSelectChange($event, 1)"
                            />
                          </div>
                        </div>
                        <div class="claim-submit__datePickerConfirmBtn">
                          <div
                            class="claim-submit__datePickerBtnCancel"
                            @click="handleDesktopDateSelectCancel"
                          >
                            {{ $t("11975") }}
                          </div>
                          <klk-button
                            size="mini"
                            @click="handleDesktopDateSelectConfirm"
                          >
                            {{ $t("13876") }}
                          </klk-button>
                        </div>
                      </div>
                    </div>
                  </div>
                </klk-poptip>
              </klk-form-item>
              <klk-form-item
                :label="$t('211266-location')"
                prop="accident_location"
              >
                <klk-input
                  v-model="claimInfoForm.accident_location"
                  size="small"
                  :maxlength="90"
                  :placeholder="$t('16186-please_enter')"
                />
              </klk-form-item>
              <klk-form-item
                :label="$t('206534-accident_details')"
                prop="accident_details"
              >
                <klk-input
                  v-model="claimInfoForm.accident_details"
                  size="small"
                  :maxlength="500"
                  type="textarea"
                  :placeholder="$t('16186-please_enter')"
                />
              </klk-form-item>
            </klk-form>
          </div>
          <div class="claim-submit__section">
            <klk-section-title size="small" decorative-line>
              {{ claimMaterialFormTitle }}
            </klk-section-title>
            <klk-form
              ref="claimMaterialInfoForm"
              :model="claimMaterialForm"
              class="claim-submit__form"
              auto-locate-error
            >
              <div
                v-for="(
                  categoryItem, categoryItemIndex
                ) in materialCategoryList"
                :key="categoryItemIndex"
              >
                <div class="claim-submit__categoryTitle">
                  {{ categoryItem.category_name }}
                </div>
                <div
                  v-for="(
                    materialItem, materialItemIndex
                  ) in categoryItem.material_list"
                  :key="materialItemIndex"
                  class="claim-submit__categoryContent"
                >
                  <klk-form-item
                    prop="material_list"
                    :rules="
                      getClaimMaterialFormItemRules(
                        categoryItemIndex,
                        materialItemIndex
                      )
                    "
                  >
                    <div slot="label" class="claim-submit__materialItemLabel">
                      <div>
                        <span>{{ materialItem.material_name }}</span>
                        <span
                          v-if="materialItem.required"
                          class="claim-submit__materialItemLabelRequired"
                          >*</span
                        >
                      </div>
                      <klk-link
                        v-if="materialItem.example_url"
                        color="#212121"
                        @click="handleClickExample(materialItem.example_url)"
                      >
                        {{ $t("206505") }}
                      </klk-link>
                    </div>
                    <div
                      v-if="materialItem.material_desc"
                      class="claim-submit__materialItemDesc"
                    >
                      {{ materialItem.material_desc }}
                    </div>
                    <div v-if="materialItem.can_provide_by_other">
                      <klk-checkbox
                        :value="
                          materialItem.provide_damage_report_by_car_rental
                        "
                        @change="
                          handleReportByRentalClick(
                            $event,
                            categoryItemIndex,
                            materialItemIndex
                          )
                        "
                      >
                        {{ materialItem.content }}
                      </klk-checkbox>
                    </div>
                    <UploadFile
                      v-show="!materialItem.provide_damage_report_by_car_rental"
                      class="claim-submit__uploader"
                      :action="UPLOAD_CONFIG.actionLink"
                      :max-file-size="UPLOAD_CONFIG.maxFileSize"
                      :limit="UPLOAD_CONFIG.maxFileCount"
                      :limit-msg="$t('210342-upload_limit_reached')"
                      :accept="UPLOAD_CONFIG.acceptType"
                      :upload-tips="materialItem.bottom_tips"
                      :files="materialItem.file_list || []"
                      :data="UPLOAD_CONFIG.data"
                      :is-mobile="isMobile"
                      :headers=" UPLOAD_CONFIG.headers"
                      name="upload_file"
                      :data-spm-module="`AddClaimMaterial?trg=manual&oid=mainbooking_id_${
                        data.main_booking_no
                      }&ext=${JSON.stringify({
                        BookingID: data.addon_booking_nos,
                        PlanCode: data.plan_codes,
                        PackageReferenceID: data.package_ref_id,
                        CoveragePackage: data.coverage_package,
                      })}`"
                      data-spm-virtual-item="__virtual"
                      @success="
                        handleUploadSuccess(
                          $event,
                          categoryItemIndex,
                          materialItemIndex
                        )
                      "
                      @remove="
                        handleUploadRemoved(
                          $event,
                          categoryItemIndex,
                          materialItemIndex
                        )
                      "
                      @error="handleUploadError"
                      @open-pdf="handlePDFOpen"
                    />
                  </klk-form-item>
                </div>
              </div>
            </klk-form>
          </div>
          <klk-bottom-sheet
            v-if="isMobile"
            show-close
            header-divider
            :visible.sync="showExampleModal"
            :title="$t('206505')"
            :mask-closable="false"
          >
            <img class="claim-submit__sheetImg" :src="exampleUrl" />
          </klk-bottom-sheet>
        </div>
        <!-- 银行信息 -->
        <div v-show="formStep === 2" class="claim-submit__tab">
          <div class="claim-submit__section">
            <klk-section-title size="small" decorative-line>
              {{ bankInfoFormTitle }}
            </klk-section-title>
            <klk-form
              ref="bankInfoForm"
              :model="bankInfoForm"
              :rules="bankInfoFormRules"
              auto-locate-error
              class="claim-submit__form"
            >
              <klk-form-item :label="$t('209702-name')" prop="receiver_name">
                <klk-input
                  v-model="bankInfoForm.receiver_name"
                  size="small"
                  :maxlength="90"
                  :placeholder="$t('16186-please_enter')"
                />
              </klk-form-item>
              <klk-form-item :label="$t('206535-bank_name')" prop="bank_name">
                <klk-input
                  v-model="bankInfoForm.bank_name"
                  size="small"
                  :maxlength="90"
                  :placeholder="$t('16186-please_enter')"
                />
              </klk-form-item>
              <klk-form-item
                :label="$t('206536-bank_account')"
                prop="bank_account"
              >
                <klk-input
                  v-model="bankInfoForm.bank_account"
                  size="small"
                  :maxlength="90"
                  :placeholder="$t('16186-please_enter')"
                />
              </klk-form-item>
              <klk-form-item :label="$t('208644-swift_code')" prop="swift_code">
                <klk-input
                  v-model="bankInfoForm.swift_code"
                  size="small"
                  :maxlength="90"
                  :placeholder="$t('16186-please_enter')"
                />
              </klk-form-item>
              <klk-form-item
                :label="$t('208645-bank_address')"
                prop="bank_address"
              >
                <klk-input
                  v-model="bankInfoForm.bank_address"
                  size="small"
                  :maxlength="90"
                  :placeholder="$t('16186-please_enter')"
                />
              </klk-form-item>
              <klk-form-item
                :label="$t('208646-customer_address')"
                prop="customer_address"
              >
                <div class="claim-submit__customerAddressTips">
                  {{ $t("208647") }}
                </div>
                <klk-input
                  v-model="bankInfoForm.customer_address"
                  size="small"
                  :maxlength="90"
                  :placeholder="$t('16186-please_enter')"
                />
              </klk-form-item>
            </klk-form>
          </div>
          <div class="claim-submit__checkAgreed">
            <klk-checkbox v-model="checkAgreeSubmit">
              <!-- {{ $t("210105") }} -->
              <!-- <klk-markdown :content="$t('210105')" /> -->
              <klk-markdown :content="bankInfoDeclaration" />
            </klk-checkbox>
          </div>
        </div>
      </div>
      <div v-if="loadingStatus === 'success'" class="claim-submit__bottom">
        <klk-button
          type="outlined"
          reverse
          class="claim-submit__bottomBtn"
          :data-spm-module="`ClaimPreviousStep?trg=manual&oid=mainbooking_id_${
            data.main_booking_no
          }&ext=${JSON.stringify({
            BookingID: data.addon_booking_nos,
            PlanCode: data.plan_codes,
            PackageReferenceID: data.package_ref_id,
            CoveragePackage: data.coverage_package,
          })}`"
          data-spm-virtual-item="__virtual"
          style="background: #fff; border: 1px solid #212121; color: #212121"
          @click="handleClickLastStep"
        >
          {{ $t("206508-last_step") }}
        </klk-button>
        <klk-button
          type="primary"
          reverse
          class="claim-submit__bottomBtn"
          :data-spm-module="claimNextStepSpm"
          data-spm-virtual-item="__virtual"
          :disabled="clickNextDisabled"
          @click="handleClickNext"
        >
          {{ formStep === 2 ? $t("210503-submit") : $t("206503-next") }}
        </klk-button>
      </div>
      <klk-modal
        :title="$t('176282-save_draft')"
        :open.sync="showTipsModal"
        :ok-label="$t('176284-save')"
        :cancel-label="$t('176285-cancel')"
        :button-align="isMobile ? 'block' : 'right'"
        @on-cancel="handleSaveDraftCancel"
        @on-confirm="handleSaveDraftConfirm"
      />
      <klk-drawer
        v-if="!isMobile && showExampleModal"
        :visible="showExampleModal"
        direction="right"
        class="claim-submit__exampleDrawer"
        :mask-closable="false"
        @close="showExampleModal = false"
      >
        <div class="claim-submit__exampleDrawerContent">
          <DetailsHeader
          :platform="getPlatform()"
          :icon-back="true"
          :title="$t('176286-claim_submit')"
          :show-bottom-border="true"
          @click="showExampleModal = false"
        />
        <img class="claim-submit__sheetImg" :src="exampleUrl" />
        </div>
      </klk-drawer>
      <ErrorPanel v-if="loadingStatus === 'error'" @refresh="fetchData" />
    </div>
  </div>
</template>

<script lang="ts">
import { Component } from "vue-property-decorator";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import lodashSet from "lodash/set";
import lodashGet from "lodash/get";
import {
  IconChevronDown,
  IconInformation,
  IconChevronUp,
  IconNext,
  IconCheckCircle,
  IconCalendar,
} from "@klook/klook-icons";
import {
  $colorTextPrimary,
  $colorTextLink,
  $colorSuccess,
} from "@klook/klook-ui/lib/utils/design-token-esm";
import { Col as KlkCol, Row as KlkRow } from "@klook/klook-ui/lib/grid";
import KlkDatetimePicker from "@klook/klook-ui/lib/datetime-picker";
// import KlookJSBridge from '@klook/jsbridge'
// import { getAntiSpiderHead } from "../../utils";
import InsuranceDetails from "../common/details.vue";
import DetailsHeader from "../common/header.vue";
import ErrorPanel from "../common/error-panel.vue";
import UploadFile from "../common/upload.vue";
import TimeSelector from "../common/selector.vue";
import ModuleBase from "./base.vue";

@Component({
  components: {
    ModuleBase,
    InsuranceDetails,
    DetailsHeader,
    IconChevronDown,
    IconInformation,
    IconChevronUp,
    IconCheckCircle,
    IconNext,
    IconCalendar,
    ErrorPanel,
    UploadFile,
    KlkCol,
    KlkRow,
    KlkDatetimePicker,
    TimeSelector,
  },
})
export default class ClaimSubmit extends ModuleBase {
  colorTextPrimary: string = $colorTextPrimary;
  colorTextLink: string = $colorTextLink;
  colorSuccess: string = $colorSuccess;
  api: string = '/v1/insuranceapisrv/outer/carrental/claim/preSubmitClaim';
  loadingStatus: string = "loading"; // 'loading' | 'error' | 'success'
  errorMsg: string = "";
  data: any = {};

  submitClaimApi: string =
    "/v1/insuranceapisrv/outer/carrental/claim/submitClaim";
  draftClaimApi: string = "/v1/insuranceapisrv/outer/carrental/claim/saveDraft";

  formStep: 1 | 2 | 0 = 0; // 0: 基本信息, 1: 赔付信息, 2: 银行信息
  contactInfoForm: any = {};
  contactInfoFormRules: any = {};
  contactInfoFormTitle: string = "";
  claimInfoForm: any = {};
  claimInfoFormRules: any = {};
  claimInfoFormTitle: string = "";
  showDatePicker: boolean = false;
  claimMaterialForm: any = {};
  claimMaterialFormRules: any = {};
  claimMaterialFormTitle: string = "";
  bankInfoForm: any = {};
  bankInfoFormRules: any = {};
  bankInfoFormTitle: string = "";

  UPLOAD_CONFIG = {
    actionLink: "/v1/insuranceclaimapisrv/outer/file/upload",
    maxFileSize: 10240, // 上传图片大小限制
    maxFileCount: 10, // 上传图片个数限制,
    acceptType: "application/pdf, image/jpg, image/jpeg, image/png, image/heic", // heic格式图片只有Safari支持预览
    data: {
      content_type: "application/javascript",
    },
    headers: {
      "X-Klook-Host": window.__KLOOK__?.state?.klook.host || window.location.host
    }
  };
  exampleUrl: string = "";
  showExampleModal: boolean = false;
  showTipsModal: boolean = false;
  checkAgreeSubmit: boolean = false;
  hasMaterialFormValidate: boolean = false; // 是否有触发过 claimMaterialForm 的校验
  draftDate?: string = undefined; // 移动端日期组件选择的日期
  draftTime?: string = undefined; // 移动端时间组件选择的时间
  mobileTimerPickerOpen: boolean = false; // 移动端时间选择器是否打开
  defaultSelectDate: string = "";

  created() {
    // 数据均从initData取，不要从route取，因为switcher会把route数据都放进initData里
    const isDraft = this.initData?.is_draft;
    if (isDraft) {
      const liabilityid = this.initData?.liability_id_list;
      const scenarioid = this.initData?.scenario_id_list;

      const newStepStack = [
        ...this.stepStack.slice(0, this.stepStack.length - 1),
        {
          name: "claimScenario",
          data: {},
        },
        {
          name: "claimInstruction",
          data: {
            liability_id_list:
              typeof liabilityid === "string"
                ? liabilityid.split(",")
                : liabilityid,
            scenario_id_list:
              typeof scenarioid === "string"
                ? scenarioid.split(",")
                : scenarioid,
          },
        },
        this.stepStack[this.stepStack.length - 1],
      ];
      this.$emit("setStepStack", newStepStack);
    }
    this.fetchData().then(() => {
      this.$nextTick(() => {
        this.fixBrowserBottomBar('claim-submit-container', 'claim-submit__content', 48 + 68)
      });
    });
  }

  get dateInputValue() {
    if (!this.claimInfoForm.start_date && !this.claimInfoForm.start_time) {
      return "";
    }
    return `${this.claimInfoForm.start_date || ""} ${
      this.claimInfoForm.start_time || ""
    }`;
  }

  get bankInfoDeclaration() {
    return this.data?.bank_info_tab?.claim_declaration;
  }

  get pageSpm() {
    const {
      addon_booking_nos,
      main_booking_no,
      plan_codes,
      coverage_package,
      package_ref_id,
    } = this.data || {};
    // 根据 formStep 返回不同的spm
    // Insurance_ClaimSubmission_ContactInfo
    // Insurance_ClaimSubmission_ClaimMaterial
    // Insurance_ClaimSubmission_BankInfo
    const stepSpmMap = {
      0: "Insurance_ClaimSubmission_ContactInfo",
      1: "Insurance_ClaimSubmission_ClaimMaterial",
      2: "Insurance_ClaimSubmission_BankInfo",
    };
    return `${
      stepSpmMap[this.formStep]
    }?trg=manual&oid=BookingID_${addon_booking_nos}&ext=${JSON.stringify({
      MainBookingID: main_booking_no,
      PlanCode: plan_codes,
      PackageReferenceID: package_ref_id,
      CoveragePackage: coverage_package,
    })}`;
  }

  get claimNextStepSpm() {
    const spm = this.formStep === 2 ? "ClaimSubmission" : "ClaimNextStep";
    const {
      addon_booking_nos,
      main_booking_no,
      plan_codes,
      coverage_package,
      package_ref_id,
    } = this.data || {};
    return `${spm}?trg=manual&oid=mainbooking_id_${main_booking_no}&ext=${JSON.stringify(
      {
        BookingID: addon_booking_nos,
        PlanCode: plan_codes,
        PackageReferenceID: package_ref_id,
        CoveragePackage: coverage_package,
      }
    )}`;
  }

  get stepList() {
    return this.data?.tab_text_list || [];
  }

  get areaNumberOptions() {
    const idMap = this.data?.country_code_map || {};
    return Object.keys(idMap).map((key) => ({
      label: key,
      value: idMap[key],
    }));
  }

  get idTypeOptions() {
    const idMap = this.data?.certificate_type_map || {};
    return Object.keys(idMap).map((key) => ({
      label: idMap[key],
      value: key,
    }));
  }

  get currencyOptions() {
    const currencyMap = this.data?.currency_type_map || {};
    return Object.keys(currencyMap).map((key) => ({
      label: currencyMap[key],
      value: key,
    }));
  }

  get materialCategoryList() {
    return this.claimMaterialForm?.material_category_list || [];
  }

  get clickNextDisabled() {
    if (this.formStep === 0) {
    }

    if (this.formStep === 2) {
      return !this.checkAgreeSubmit;
    }
    return false;
  }

  get hoursSelectOptions() {
    const options: any[] = [];
    for (let i = 0; i < 24; i++) {
      const hour: string = i < 10 ? `0${i}` : `${i}`;
      options.push({
        label: hour,
        value: hour,
      });
    }
    return options;
  }

  get minutesSelectOptions() {
    const options: any[] = [];
    for (let i = 0; i < 60; i++) {
      const minutes: string = i < 10 ? `0${i}` : `${i}`;
      options.push({
        label: minutes,
        value: minutes,
      });
    }
    return options;
  }

  handlePDFOpen(url: string) {
    this.handleSaveDraft().finally(() => {
      // 预览PDF
      this.openExternalLink(url)
    });
  }

  handleUploadError(err: any) {
    this.$toast(err.toString());
  }

  handleMobileDateSelectCancel() {
    this.showDatePicker = false;
    this.draftDate = this.claimInfoForm?.start_date;
    this.draftTime = this.claimInfoForm?.start_time || "00:00";
  }

  handleDesktopDateSelectCancel() {
    this.showDatePicker = false;
    this.draftDate = this.claimInfoForm?.start_date;
    this.draftTime = this.claimInfoForm?.start_time || "00:00";
  }

  handleDesktopDateSelectConfirm() {
    this.$set(this.claimInfoForm, "start_date", this.draftDate);
    this.$set(this.claimInfoForm, "start_time", this.draftTime);
    this.showDatePicker = false;
  }

  handleTimeSelectChange(item: any, index: number) {
    const val = item.value;
    const time = (this.draftTime || "").split(":");
    if (index === 0) {
      time[0] = val;
    } else if (index === 1) {
      time[1] = val;
    }
    const value = time.join(":");
    this.draftTime = value;
  }

  handleMobileTimeConfirm(val) {
    const value = dayjs(val).format("HH:mm");
    this.draftTime = value;
  }

  handleTimePickerClick() {
    this.mobileTimerPickerOpen = !this.mobileTimerPickerOpen;
    if (this.mobileTimerPickerOpen) {
      setTimeout(() => {
        const sheetNode = document.querySelector('body > .klk-bottom-sheet') as HTMLElement
        // safari 时间选择组件层级展示有问题
        if (sheetNode) {
          sheetNode.style.zIndex = '2001'
        }
      }, 500)
    }
  }

  handledDatePickerFocus() {
    this.showDatePicker = !this.showDatePicker;
    if (this.isMobile) {
      this.draftDate = this.claimInfoForm.start_date;
    }
  }

  getClaimMaterialFormItemRules(categoryIndex: number, materialIndex: number) {
    return [
      {
        type: "array",
        trigger: "blur",
        validator: (rule: any, value: any) => {
          this.hasMaterialFormValidate = true; // 标记已经触发过校验
          const materialItem = lodashGet(
            this.claimMaterialForm,
            `material_category_list.${categoryIndex}.material_list.${materialIndex}`,
            []
          );
          if (
            materialItem.can_provide_by_other &&
            materialItem.provide_damage_report_by_car_rental
          ) {
            // 如果可以由租车公司提供报告，则不需要上传文件
            return true;
          }
          if (
            materialItem.required &&
            (!materialItem.file_list || !materialItem.file_list.length)
          ) {
            return new Error(this.$t("16186-please_enter"));
          }
          return true;
        },
      },
    ];
  }

  getTabCompleteStatus(index: number) {
    if (index === 0) {
      return this.data?.base_info_tab?.complete_status;
    } else if (index === 1) {
      return this.data?.claim_info_tab?.complete_status;
    } else if (index === 2) {
      return this.data?.bank_info_tab?.complete_status;
    }
    return 0;
  }

  handleSaveDraft() {
    const params = this.sortDraftFormData();
    return this.$axios
      .$post(this.draftClaimApi, params)
      .then((res) => {
        if (!res.success || !res.result) {
          this.$toast(res?.error?.message);
          return;
        }
        return res.result;
      })
      .catch((err) => {
        this.$toast(err.message);
      });
  }

  handleSaveDraftCancel() {
    this.showTipsModal = false;
    this.handleBackClick();
  }

  handleSaveDraftConfirm() {
    this.handleSaveDraft().then(() => {
      this.showTipsModal = false;
      this.handleBackClick();
      setTimeout(() => {
        this.refreshCard && this.refreshCard();
      });
    });
  }

  handleSubmitConfirm() {
    const params = this.sortFormData();
    return this.$axios.$post(this.submitClaimApi, params).then((res) => {
      if (!res.success || !res.result) {
        this.$toast(res?.error?.message);
        throw new Error(res?.error?.message);
        // return;
      }
      return res.result;
    });
  }

  handleBackClickSelf() {
    this.showTipsModal = true;
  }

  getDate(value) {
    return new Date(value);
  }

  handleMobileDateChange(date) {
    const value = dayjs(date).format("YYYY-MM-DD");
    this.draftDate = value;
  }

  handleDateChange(date) {
    const value = dayjs(date).format("YYYY-MM-DD");
    this.draftDate = value;
  }

  handleMobileDateConfirm() {
    this.$set(this.claimInfoForm, "start_date", this.draftDate);
    this.$set(this.claimInfoForm, "start_time", this.draftTime);
    this.showDatePicker = false;
  }

  handleClickExample(url) {
    this.exampleUrl = url;
    this.showExampleModal = true;
  }

  handleReportByRentalClick(value, categoryIndex, materialIndex) {
    this.$set(
      this.claimMaterialForm.material_category_list[categoryIndex]
        .material_list[materialIndex],
      "provide_damage_report_by_car_rental",
      value
    );
  }

  getDisplayFiles(categoryIndex, materialIndex) {
    const fileList =
      this.claimMaterialForm?.material_category_list[categoryIndex]
        .material_list[materialIndex]?.fileList || [];
    return fileList((file) => {
      return {
        name: file.file_name,
        url: file.file_display_url,
      };
    });
  }

  handleUploadRemoved(result, categoryIndex, materialIndex) {
    const fileList = result.responses || [];
    this.$set(
      this.claimMaterialForm.material_category_list[categoryIndex]
        .material_list[materialIndex],
      "file_list",
      fileList
    );
    setTimeout(() => {
      if (this.hasMaterialFormValidate) {
        // 如果触发过 claimMaterialForm 的校验，调用一次校验来决定红字是展示还是消失
        this.$refs.claimMaterialInfoForm.validate();
      }
    });
  }

  handleUploadSuccess(result, categoryIndex, materialIndex) {
    const fileList = result.responses || [];
    this.$set(
      this.claimMaterialForm.material_category_list[categoryIndex]
        .material_list[materialIndex],
      "file_list",
      fileList
    );
    setTimeout(() => {
      if (this.hasMaterialFormValidate) {
        // 如果触发过 claimMaterialForm 的校验，调用一次校验来决定红字是展示还是消失
        this.$refs.claimMaterialInfoForm.validate();
      }
    });
  }

  handleClickLastStep() {
    if (this.formStep === 0) {
      this.$emit("back");
    } else {
      this.formStep--;
      this.$nextTick(() => {
        this.$inhouse.track("pageview", this.$refs.container);
      });
    }
  }

  handleClickNext() {
    if (this.formStep === 0) {
      this.$refs.contactInfoForm.validate((valid: boolean) => {
        if (!valid) {
          return;
        }
        const claimInfoTabStatus =
          this.data?.claim_info_tab?.complete_status || "not_started";
        const bankInfoTabStatus =
          this.data?.bank_info_tab?.complete_status || "not_started";
        if (claimInfoTabStatus === "not_started") {
          this.claimInfoForm = {
            ...this.claimInfoForm,
            full_name: `${this.contactInfoForm.first_name} ${this.contactInfoForm.last_name}`,
          };
        }
        if (bankInfoTabStatus === "not_started") {
          this.bankInfoForm = {
            ...this.bankInfoForm,
            receiver_name: `${this.contactInfoForm.first_name} ${this.contactInfoForm.last_name}`,
          };
        }
        // 只要前端校验通过就算当前tab填写完成
        // 即使缓存接口调用失败导致当前tab数据没有保存，那么接口保存的complete_status依然是未完成状态，不会出现complete_status为已完成但是接口数据不完整的情况
        lodashSet(this.data, "base_info_tab.complete_status", "completed");
        if (
          !this.data?.claim_info_tab?.complete_status ||
          this.data?.claim_info_tab?.complete_status === "not_started"
        ) {
          lodashSet(this.data, "claim_info_tab.complete_status", "ongoing");
        }
        this.handleSaveDraft();
        this.formStep++;
        this.$nextTick(() => {
          this.$inhouse.track("pageview", this.$refs.container);
        });
      });
    } else if (this.formStep === 1) {
      Promise.all([
        this.$refs.claimInfoForm.validate(),
        this.$refs.claimMaterialInfoForm.validate(),
      ]).then((validateArr: boolean[]) => {
        if (!validateArr.every((item) => item === true)) {
          return;
        }
        lodashSet(this.data, "claim_info_tab.complete_status", "completed");
        if (
          !this.data?.bank_info_tab?.complete_status ||
          this.data?.bank_info_tab?.complete_status === "not_started"
        ) {
          lodashSet(this.data, "bank_info_tab.complete_status", "ongoing");
        }
        this.handleSaveDraft();
        this.formStep++;
        this.$nextTick(() => {
          this.$inhouse.track("pageview", this.$refs.container);
        });
      });
    } else if (this.formStep === 2) {
      this.$refs.bankInfoForm.validate((valid: boolean) => {
        if (!valid) {
          return;
        }
        lodashSet(this.data.bank_info_tab, "complete_status", "completed");
        this.handleSubmitConfirm().then(
          (res) => {
            this.$emit("setStep", {
              newStep: "claimSubmitResult",
              data: {
                ...res,
              },
            });
          },
          (err) => {
            this.$toast(err.message);
          }
        );
      });
    }
  }

  sortFormData() {
    const categoryList = this.claimMaterialForm?.material_category_list || [];
    const liabilityid = this.initData?.liability_id_list;
    const scenarioid = this.initData?.scenario_id_list;
    const params = {
      main_booking_ref_no: this.getBookingNo && this.getBookingNo(),
      liability_id_list:
        typeof liabilityid === "string" ? liabilityid.split(",") : liabilityid,
      scenario_id_list:
        typeof scenarioid === "string" ? scenarioid.split(",") : scenarioid,
      goods_category: this.data?.goods_category,
      language: this.getLanguage && this.getLanguage(),
      ...this.contactInfoForm,
      ...this.claimInfoForm,
      material_file_list: categoryList.reduce((acc, cur) => {
        return [...acc, ...cur.material_list];
      }, []),
      ...this.bankInfoForm,
    };
    return params;
  }

  sortDraftFormData() {
    lodashSet(
      this.data.base_info_tab,
      "contact_info_data",
      this.contactInfoForm
    );
    lodashSet(this.data.claim_info_tab, "claim_info_data", this.claimInfoForm);
    lodashSet(
      this.data.claim_info_tab,
      "claim_material_data",
      this.claimMaterialForm
    );
    lodashSet(this.data.bank_info_tab, "claim_info_data", this.bankInfoForm);
    return this.data;
  }

  initContactInfoForm() {
    const infoData = this.data?.base_info_tab?.contact_info_data || {};
    this.contactInfoFormTitle = infoData.title || "";
    this.contactInfoForm = {
      ...infoData,
    };
    this.contactInfoFormRules = {
      first_name: [
        {
          required: true,
          message: this.$t("16186-please_enter"),
          trigger: "blur",
        },
        {
          validator: (rule: any, value: string) => {
            if (value.length > 90) {
              return new Error(this.$t("209701"));
            }
            return true;
          },
        },
      ],
      last_name: [
        {
          required: true,
          message: this.$t("16186-please_enter"),
          trigger: "blur",
        },
        {
          trigger: "blur",
          validator: (rule: any, value: string) => {
            if (value.length > 90) {
              return new Error(this.$t("209701"));
            }
            return true;
          },
        },
      ],
      certificate_type: [
        {
          required: true,
          message: this.$t("12022-please_select"),
          trigger: "change",
        },
      ],
      certificate_number: [
        {
          required: true,
          message: this.$t("16186-please_enter"),
          trigger: "blur",
        },
        {
          validator: (rule: any, value: string) => {
            if (value.length > 90) {
              return new Error(this.$t("209701"));
            }
            return true;
          },
        },
      ],
      email: [
        {
          required: true,
          message: this.$t("16186-please_enter"),
          trigger: "blur",
        },
        {
          trigger: "blur",
          validator: (rule: any, value: string) => {
            // 如果value不是邮箱格式则抛出错误
            if (
              !/^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$/.test(value)
            ) {
              return new Error(
                this.$t("70214", { label: this.$t("16182-email") })
              );
            }
            return true;
          },
        },
      ],
      phone: [
        {
          required: true,
          message: this.$t("16186-please_enter"),
          trigger: "blur",
        },
      ],
      country_code: [
        {
          required: true,
          message: this.$t("12022-please_select"),
          trigger: "change",
        },
      ],
    };
  }

  extractTimeZone(dateString: string) {
    const regex = /([+-]\d{2}):?(\d{2})$/;
    const match = dateString.match(regex);

    if (match) {
      return match[1] + ":" + match[2];
    } else {
      return null; // 返回null表示没有匹配到时区部分
    }
  }

  initClaimInfoForm() {
    const infoData = this.data?.claim_info_tab?.claim_info_data || {};
    this.claimInfoFormTitle = infoData.title || "";
    this.defaultSelectDate = infoData?.effective_date_start || undefined;
    this.claimInfoForm = {
      ...infoData,
      claim_currency:
        infoData?.claim_currency || this.currencyOptions[0]?.value,
    };
    this.draftDate = this.claimInfoForm?.start_date;
    this.draftTime = this.claimInfoForm?.start_time || "00:00";
    this.claimInfoFormRules = {
      full_name: [
        {
          required: true,
          message: this.$t("16186-please_enter"),
          trigger: "blur",
        },
      ],
      start_date: [
        {
          required: true,
          message: this.$t("12022-please_select"),
          trigger: "blur",
        },
        {
          trigger: "blur",
          validator: (rule: any, value: string) => {
            if (!value || !this.claimInfoForm?.start_time) {
              return new Error(this.$t("16186-please_enter"));
            }
            return true;
          },
        },
        {
          trigger: "blur",
          validator: (rule: any, value: string) => {
            dayjs.extend(utc);
            const dateTime = `${value} ${this.claimInfoForm?.start_time}`;
            const infoData = this.data?.claim_info_tab?.claim_info_data || {};
            const startDateTime = infoData?.effective_date_start;
            const endDateTime = infoData?.effective_date_end;
            if (
              dayjs(dateTime).isBefore(startDateTime) ||
              dayjs(dateTime).isAfter(endDateTime)
            ) {
              return new Error(this.$t("13726-date_valid"));
            }
            return true;
          },
        },
      ],
      accident_details: [
        {
          required: true,
          message: this.$t("16186-please_enter"),
          trigger: "blur",
        },
      ],
      accident_location: [
        {
          required: true,
          message: this.$t("16186-please_enter"),
          trigger: "blur",
        },
        {
          validator: (rule: any, value: string) => {
            if (value.length > 90) {
              return new Error(this.$t("209701"));
            }
            return true;
          },
        },
      ]
    };
  }

  initClaimMaterialForm() {
    const infoData = this.data?.claim_info_tab?.claim_material_data || {};
    this.claimMaterialFormTitle = infoData.title || "";
    this.claimMaterialForm = {
      ...infoData,
    };
  }

  initBankInfoForm() {
    const infoData = this.data?.bank_info_tab?.claim_info_data || {};
    this.bankInfoFormTitle = infoData.title || "";
    this.bankInfoForm = {
      ...infoData,
    };
    this.bankInfoFormRules = {
      receiver_name: [
        {
          required: true,
          message: this.$t("16186-please_enter"),
          trigger: "blur",
        },
        {
          validator: (rule: any, value: string) => {
            if (value.length > 90) {
              return new Error(this.$t("209701"));
            }
            return true;
          },
        },
      ],
      bank_name: [
        {
          required: true,
          message: this.$t("16186-please_enter"),
          trigger: "blur",
        },
        {
          validator: (rule: any, value: string) => {
            if (value.length > 90) {
              return new Error(this.$t("209701"));
            }
            return true;
          },
        },
      ],
      bank_account: [
        {
          required: true,
          message: this.$t("16186-please_enter"),
          trigger: "blur",
        },
      ],
      swift_code: [
        {
          required: true,
          message: this.$t("16186-please_enter"),
          trigger: "blur",
        },
        {
          validator: (rule: any, value: string) => {
            console.log("webuidebug:", "校验swift_code value：", value);
            if (value.length > 90) {
              return new Error(this.$t("209701"));
            }
            return true;
          },
        },
      ],
      bank_address: [
        {
          required: true,
          message: this.$t("16186-please_enter"),
          trigger: "blur",
        },
        {
          validator: (rule: any, value: string) => {
            if (value.length > 90) {
              return new Error(this.$t("209701"));
            }
            return true;
          },
        },
      ],
      customer_address: [
        {
          required: true,
          message: this.$t("16186-please_enter"),
          trigger: "blur",
        },
        {
          validator: (rule: any, value: string) => {
            if (value.length > 90) {
              return new Error(this.$t("209701"));
            }
            return true;
          },
        },
      ],
    };
  }

  initStepAndStatus() {
    if (
      !this.data?.base_info_tab?.complete_status ||
      this.data?.base_info_tab?.complete_status === "not_started"
    ) {
      this.formStep = 0;
      lodashSet(this.data, "base_info_tab.complete_status", "ongoing");
    } else if (
      !this.data?.claim_info_tab?.complete_status ||
      this.data?.claim_info_tab?.complete_status === "not_started" ||
      this.data?.claim_info_tab?.complete_status === "ongoing"
    ) {
      this.formStep = 1;
    } else if (
      !this.data?.bank_info_tab?.complete_status ||
      this.data?.bank_info_tab?.complete_status === "not_started" ||
      this.data?.bank_info_tab?.complete_status === "ongoing"
    ) {
      this.formStep = 2;
    }
  }

  fetchData() {
    const liabilityid = this.initData?.liability_id_list;
    const scenarioid = this.initData?.scenario_id_list;
    const params = {
      booking_no: this.getBookingNo(),
      liability_id_list:
        typeof liabilityid === "string" ? liabilityid.split(",") : liabilityid,
      scenario_id_list:
        typeof scenarioid === "string" ? scenarioid.split(",") : scenarioid,
      language: this.getLanguage && this.getLanguage(),
    };
    return this.$axios
      ?.$post(this.api, params)
      .then((res) => {
        if (!res.success || !res.result) {
          this.loadingStatus = "error";
          this.errorMsg = (res.error && res.error.message) || "";
          this.processRequestError(res);
          return;
        }
        this.data = res.result;
        this.initContactInfoForm();
        this.initClaimInfoForm();
        this.initClaimMaterialForm();
        this.initBankInfoForm();
        this.initStepAndStatus();
        this.loadingStatus = "success";

        this.$nextTick(() => {
          this.$inhouse.track("pageview", this.$refs.container);
        });
        return res.result;
      })
      .catch(() => {
        this.loadingStatus = "error";
      });
  }
}
</script>

<style lang="scss" scoped>
@import "./mixins.scss";

.claim-submit-container {
  @include module-page;

  .claim-submit {
    height: 100%;

    &__content {
      @include module-content;
      background-color: #eeeeee;
    }

    &__step {
      background: #ffffff;
      padding: 8px 0px;
    }

    &__stepIcon {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      background: #08b371;
      color: #ffffff;
      @include font-body-s-bold-v2;
      align-items: center;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    &__stepIconNotStart {
      @extend .claim-submit__stepIcon;
      background: #e6e6e6;
    }

    &__section {
      padding: 16px 20px;
      background: $color-bg-1;
      margin: 12px 0px;
    }

    &__dateIcon {
      margin-right: 12px;
    }

    &__sheetImg {
      width: 100%;
    }

    &__form {
      margin-top: 16px;
    }

    &__formItemDate {
      ::v-deep .klk-form-item-content {
        position: relative;
      }

      ::v-deep .klk-poptip {
        box-shadow: 0px 4px 12px 0px #0000001a;
        border-radius: 16px;
        z-index: 10;
        position: absolute;
        right: 0px;
        top: 36px;
      }

      ::v-deep .klk-bottom-sheet-body {
        padding: 0px;
      }
    }

    &__datePickerPoptip {
      display: flex;
      background: #ffffff;
    }

    &__datePickerLeftHead,
    &__datePickerRightHead {
      font-weight: 600;
      font-size: 16px;
      text-align: center;
      background: #f5f5f5;
      padding: 12px;
    }

    &__datePickerRight {
      display: flex;
      flex-direction: column;
    }

    &__datePickerRightContent {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      flex: 1;
      border-left: 1px solid #eeeeee;
    }

    &__datePickerSelect {
      // width: 200px;
      display: flex;
      align-items: center;
      gap: 8px;
      margin-top: 16px;
      padding: 0px 15px;
    }

    &__datePickerSelectLeft,
    &__datePickerSelectRight {
      font-size: 14px;
      font-weight: 600;
      width: 80px;
      border: 1px solid #e6e6e6;
      border-radius: 6px;
      padding: 6px 10px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      cursor: pointer;
    }

    &__datePickerConfirmBtn {
      display: flex;
      align-items: center;
      padding: 20px 18px;
      width: 100%;
      justify-content: space-between;
    }

    &__datePickerBtnCancel {
      cursor: pointer;
      font-size: 12px;
      font-weight: 700;
      color: #666666;
    }

    &__datePickerSelectPlace {
      font-size: 14px;
      font-weight: 700;
      color: #333333;
    }

    &__datePickerBtn {
      width: 100%;
      padding: 8px 20px;
      position: fixed;
      bottom: 0px;
      z-index: 10;
      background: #ffffff;
      box-shadow: 0px -1px 8px 0px #0000001c;
    }

    &__timePickerBtn {
      height: 44;
      border-radius: 12px;
      background: #f5f5f5;
      margin: 20px 0px;
      padding: 12px 16px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    &__timePickerBtnLabel {
      @include font-paragraph-s-regular;
      color: $color-text-primary;
    }

    &__timePickerBtnRight {
      display: flex;
    }

    &__timePickerBtnValue {
      @include font-body-s-bold;
      color: $color-text-primary;
    }

    &__timePickerBtnIcon {
      margin-left: 10px;
    }

    &__datePickerBtnAPP {
      padding-bottom: 28px;
    }

    &__hiddenFormItem {
      ::v-deep .klk-form-item-label {
        opacity: 0;
      }
    }

    &__categoryTitle {
      @include font-body-m-bold;
      color: $color-text-primary;
      margin-top: 16px;
    }

    &__categoryContent {
      margin-top: 20px;
    }

    &__materialItemLabel {
      display: flex;
      gap: 10px;
      align-items: flex-start;
      justify-content: space-between;
    }

    &__materialItemLabelRequired {
      color: $color-brand-primary;
      margin-left: 4px;
    }

    &__materialItemDesc {
      @include font-body-s-regular;
      color: $color-text-secondary;
      margin-bottom: 8px;
    }

    &__checkAgreed {
      @include font-body-s-regular;
      color: $color-text-primary;
      padding: 12px 20px;

      ::v-deep .klk-markdown {
        @include font-body-s-regular;
        color: $color-text-primary;
        @include markdown-style;
      }
    }

    &__customerAddressTips {
      @include font-body-s-regular;
      color: $color-text-secondary;
      margin-bottom: 8px;
    }

    &__bottom {
      @include claim-bottom;
    }

    &__bottomBtn {
      @include claim-bottom-btn;
    }

    &__exampleDrawer {
      ::v-deep .klk-drawer-mask {
        background: transparent;
      }
    }
    &__exampleDrawerContent {
      width: 680px !important;
    }
  }
}

.claim-submit-container-desktop {
  .claim-submit {
    ::v-deep .klk-bottom-sheet-inner {
      width: 680px;
      left: calc(100vw - 680px);
    }
  }
}

.claim-submit-container-mobile {
  @include module-page-mobile;

  .claim-submit {
    .claim-submit__content {
      @include module-content-mobile;
    }

    .claim-submit__bottom {
      border: 0;
    }
  }
}

.claim-submit-container-app {
  .claim-submit {
    // .claim-submit__content {
    //   @include module-content-app;
    // }

    // .claim-submit__bottom {
    //   padding-bottom: 32px;
    // }
  }
}
</style>
