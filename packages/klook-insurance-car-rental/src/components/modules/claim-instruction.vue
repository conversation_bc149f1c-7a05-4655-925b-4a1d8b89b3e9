<template>
  <div
    ref="container"
    :data-spm-page="pageSpm"
    :class="{
      'claim-instruction-container': true,
      'claim-instruction-container-mobile': isMobile,
      'claim-instruction-container-app': getIsApp(),
    }"
  >
    <klk-loading v-if="loadingStatus === 'loading'" />
    <div
      v-else
      :class="{
        'claim-instruction': true,
      }"
    >
      <DetailsHeader
        :platform="getPlatform()"
        :icon-back="true"
        :title="$t('206493-claim_instruction')"
        :show-bottom-border="true"
        @click="handleClickReselectScene"
      />
      <div
        v-if="loadingStatus === 'success'"
        class="claim-instruction__content"
      >
        <div
          v-if="tips"
          class="claim-instruction__tips"
        >
          <klk-markdown :content="tips" />
        </div>
        <div
          v-if="claimList && claimList.length"
          class="claim-instruction__list"
        >
          <div
            v-for="(item, index) in claimList"
            :key="index"
            class="claim-instruction__listItem"
          >
            <div class="claim-instruction__itemTitle">
              {{ item.instruction_title }}
            </div>
            <klk-steps
              v-if="
                item.instruction_step_list && item.instruction_step_list.length
              "
              direction="vertical"
              :current="item.instruction_step_list.length"
              class="claim-instruction__itemSteps"
            >
              <klk-step
                v-for="(stepItem, stepItemIndex) in item.instruction_step_list"
                :key="stepItemIndex"
                :title="stepItem.step_text"
                :content="stepItem.step_sub_text"
              >
                <div
                  slot="icon"
                  class="claim-instruction__itemStepsIcon"
                >
                  <span>{{ stepItemIndex + 1 }}</span>
                </div>
              </klk-step>
            </klk-steps>
            <!-- 下方有内容才展示分割线 -->
            <klk-divider
              v-if="item.benefit_title"
              class="claim-instruction__divider"
            />
            <div class="claim-instruction__benefitTitle">
              {{ item.benefit_title }}
            </div>
            <div
              v-if="item.benefit_list && item.benefit_list.length"
              class="claim-instruction__benefitContent"
            >
              <div
                v-for="(benefitItem, benefitIndex) in item.benefit_list"
                :key="benefitIndex"
                class="claim-instruction__benefitItem"
              >
                <img
                  :src="benefitItem.icon"
                  class="claim-instruction__benefitItemLeft"
                >
                <div class="claim-instruction__benefitItemRight">
                  <div class="claim-instruction__benefitItemTitle">
                    {{ benefitItem.benefit_text }}
                  </div>
                  <div class="claim-instruction__benefitItemDesc">
                    <klk-markdown :content="benefitItem.benefit_sub_text" />
                  </div>
                </div>
              </div>
            </div>
            <!-- 下方有内容才展示分割线 -->
            <klk-divider
              v-if="item.material_title"
              class="claim-instruction__divider"
            />
            <div class="claim-instruction__materialTitle">
              {{ item.material_title }}
            </div>
            <div
              v-if="item.material_list && item.material_list.length"
              class="claim-instruction__materialContent"
            >
              <div
                v-for="(materialItem, materialItemIndex) in item.material_list"
                :key="materialItemIndex"
                class="claim-instruction__materialItem"
              >
                <div class="claim-instruction__categoryTitle">
                  {{ materialItem.material_category_name }}
                </div>
                <div
                  v-if="
                    materialItem.material_name_list &&
                      materialItem.material_name_list.length
                  "
                  class="claim-instruction__categoryContent"
                >
                  <div
                    v-for="(
                      materialName, materialNameIndex
                    ) in materialItem.material_name_list"
                    :key="materialNameIndex"
                    class="claim-instruction__categoryItem"
                  >
                    <img
                      src="https://res.klook.com/image/upload/___KIcon_KIcons.icon_file_outlined_size__24_colors__colorScheme.colorTextPrimary_zatyld.svg"
                      class="claim-instruction__categoryItemIcon"
                    >
                    <div class="claim-instruction__categoryItemName">
                      {{ materialName }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <!-- 下方有内容才展示分割线 -->
            <klk-divider
              v-if="item.connect_info_title"
              class="claim-instruction__divider"
            />
            <div class="claim-instruction__contactTitle">
              {{ item.connect_info_title }}
            </div>
            <a
              class="claim-instruction__contactText"
              :href="`tel:${item.connect_info_text}`"
            >{{ item.connect_info_text
            }}</a>
          </div>
        </div>
      </div>
      <div
        v-if="loadingStatus === 'success'"
        class="claim-instruction__bottom"
      >
        <klk-button
          type="outlined"
          reverse
          :data-spm-module="`ClaimPreviousStep?trg=manual&oid=mainbooking_id_${data.main_booking_no}&ext=${JSON.stringify(
            {
              BookingID: data.addon_booking_nos,
              PlanCode: data.plan_codes,
              PackageReferenceID: data.package_ref_id,
              CoveragePackage: data.coverage_package,
            }
          )}`"
          data-spm-virtual-item="__virtual"
          class="claim-instruction__bottomBtn"
          @click="handleClickReselectScene"
        >
          {{ $t("206501-reselect_scene") }}
        </klk-button>
        <klk-button
          type="primary"
          reverse
          :data-spm-module="`ClaimNextStep??trg=manual&oid=mainbooking_id_${data.main_booking_no}&ext=${JSON.stringify(
            {
              BookingID: data.addon_booking_nos,
              PlanCode: data.plan_codes,
              PackageReferenceID: data.package_ref_id,
              CoveragePackage: data.coverage_package,
            }
          )}`"
          data-spm-virtual-item="__virtual"
          class="claim-instruction__bottomBtn"
          @click="handleClickClaim"
        >
          {{ $t("206503") }}
        </klk-button>
      </div>
      <ErrorPanel
        v-if="loadingStatus === 'error'"
        @refresh="fetchData"
      />
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Watch } from 'vue-property-decorator';
import {
  IconChevronDown,
  IconInformation,
  IconChevronUp,
  IconNext,
} from '@klook/klook-icons';
import {
  $colorTextPrimary,
  $colorTextLink,
} from '@klook/klook-ui/lib/utils/design-token-esm';
import { getAntiSpiderHead } from '../../utils';
import InsuranceDetails from '../common/details.vue';
import DetailsHeader from '../common/header.vue';
import ErrorPanel from '../common/error-panel.vue';
import ModuleBase from './base.vue';

@Component({
  components: {
    ModuleBase,
    InsuranceDetails,
    DetailsHeader,
    IconChevronDown,
    IconInformation,
    IconChevronUp,
    IconNext,
    ErrorPanel,
  },
})
export default class ClaimInstruction extends ModuleBase {
  colorTextPrimary: string = $colorTextPrimary;
  colorTextLink: string = $colorTextLink;
  api: string =
    '/v1/insuranceapisrv/outer/carrental/claim/getClaimsInstructions';
  loadingStatus: string = 'loading'; // 'loading' | 'error' | 'success'
  errorMsg: string = '';

  data: any = {};

  get claimList() {
    return this.data?.instruction_list || [];
  }

  get tips() {
    return this.data?.top_tip_text || '';
  }

  created() {
    this.fetchData().then(() => {
      this.$nextTick(() => {
        this.fixBrowserBottomBar('claim-instruction-container', 'claim-instruction__content', 48 + 68)
      });
    });
  }

  handleClickReselectScene() {
    this.$emit('back', {
      liability_id_list: this.initData?.liability_id_list || [],
      scenario_id_list: this.initData?.scenario_id_list || [],
    });
  }

  handleClickClaim() {
    this.$emit('setStep', {
      newStep: 'claimSubmit',
      data: {},
    });
  }

  fetchData() {
    const params = {
      booking_no: this.getBookingNo(),
      liability_id_list: this.initData?.liability_id_list || [],
      language: this.getLanguage && this.getLanguage(),
      scenario_id_list: this.initData?.scenario_id_list || [],
    };
    return this.$axios
      .$post(this.api, params)
      .then((res) => {
        if (!res.success || !res.result) {
          this.loadingStatus = 'error';
          this.errorMsg = (res.error && res.error.message) || '';
          this.processRequestError(res)
          return;
        }
        this.data = res.result;
        this.loadingStatus = 'success';

        this.$nextTick(() => {
          this.$inhouse.track('pageview', this.$refs.container);
        });
        return res.result;
      })
      .catch(() => {
        this.loadingStatus = 'error';
      });
  }

  get pageSpm() {
    const { addon_booking_nos, main_booking_no, plan_codes, coverage_package, package_ref_id } = this.data || {};
    return `Insurance_ClaimProcess?trg=manual&oid=BookingID_${addon_booking_nos}&ext=${JSON.stringify(
      {
        MainBookingID: main_booking_no,
        PlanCode: plan_codes,
        PackageReferenceID: package_ref_id,
        CoveragePackage: coverage_package,
      }
    )}`;
  }
}
</script>

<style lang="scss" scoped>
@import "./mixins.scss";

.claim-instruction-container {
  @include module-page;

  .claim-instruction {
    height: 100%;

    &__content {
      @include module-content;
      padding: 20px;
      background-color: #eeeeee;
    }

    &__tips {
      background: $color-info-background;
      border-radius: $radius-l;
      border: 1px solid #437dff4d;
      padding: 12px 16px;
      margin-bottom: 12px;

      ::v-deep .klk-markdown {
        @include font-paragraph-m-regular;
        color: $color-text-link;
        @include markdown-style;
      }
    }

    &__list {
      display: flex;
      flex-direction: column;
      gap: 8px;
    }

    &__listItem {
      border-radius: $radius-xl;
      background-color: #fff;
      padding: 16px;
      margin-bottom: 12px;
    }

    &__itemTitle {
      // @include font-body-m-bold;
      @include font-heading-xs-v2;
      color: $color-text-primary;
    }

    &__itemSteps {
      margin-top: 8px;
      ::v-deep .klk-step-line-track {
        width: 1px;
        left: 12px;
        .klk-step-line {
          width: 1px;
        }
      }
      // ::v-eeep .klk-step-line {
      //   width: 1px;
      // }
    }

    &__itemStepsIcon {
      width: 26px;
      height: 26px;
      border-radius: 50%;
      background: #08b371;
      color: #ffffff;
      @include font-body-s-bold-v2;
      align-items: center;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    &__divider {
      margin: 16px 0;
    }

    &__benefitTitle {
      // @include font-body-m-bold;
      @include font-heading-xs-v2;
      color: $color-text-primary;
    }

    &__benefitContent {
      margin-top: 12px;
      display: flex;
      flex-direction: column;
      gap: 12px;
    }

    &__benefitItem {
      display: flex;
      align-items: flex-start;
      gap: 13px;
    }

    &__benefitItemLeft {
      width: 20px;
      margin-top: 3px;
    }

    &__benefitItemTitle {
      @include font-body-m-regular;
      color: $color-text-primary;
    }

    &__benefitItemDesc {
      ::v-deep .klk-markdown {
        @include font-body-s-regular;
        color: $color-text-secondary;
        @include markdown-style;
      }
    }

    &__materialTitle {
      // @include font-body-m-bold;
      @include font-heading-xs-v2;
      color: $color-text-primary;
    }

    &__materialContent {
      margin-top: 12px;
      display: flex;
      flex-direction: column;
      gap: 12px;
    }

    &__categoryTitle {
      @include font-body-m-bold;
      color: $color-text-primary;
    }

    &__categoryContent {
      margin-top: 8px;
      display: flex;
      flex-direction: column;
      gap: 8px;
    }

    &__categoryItem {
      display: flex;
      align-items: flex-start;
      gap: 9px;
    }

    &__categoryItemIcon {
      width: 20px;
      height: auto;
    }

    &__categoryItemName {
      @include font-body-m-regular;
      color: $color-text-primary;
    }

    &__contactTitle {
      @include font-body-m-bold;
      color: $color-text-primary;
    }

    &__contactText {
      @include font-body-m-regular;
      color: $color-text-primary;
      margin-top: 8px;
      text-decoration: underline;
    }

    &__bottom {
      @include claim-bottom;
    }

    &__bottomBtn {
      @include claim-bottom-btn;
    }
  }
}

.claim-instruction-container-mobile {
  @include module-page-mobile;

  .claim-instruction {
    .claim-instruction__content {
      @include module-content-mobile;
      padding: 20px 16px;
    }

    .claim-instruction__bottom {
      border: 0;
      // padding-bottom: 20px;
    }
  }
}

.claim-instruction-container-app {
  .claim-instruction {
    // .claim-instruction__content {
    //   @include module-content-app;
    // }

    // .claim-instruction__bottom {
    //   padding-bottom: 32px;
    // }
  }
}
</style>
