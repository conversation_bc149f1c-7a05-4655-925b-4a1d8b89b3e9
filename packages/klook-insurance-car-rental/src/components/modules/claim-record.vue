<template>
  <div
    ref="container"
    :data-spm-page="pageSpm"
    :class="{
      'claim-record-container': true,
      'claim-record-container-mobile': isMobile,
      'claim-record-container-app': getIsApp(),
    }"
  >
    <klk-loading v-if="loadingStatus === 'loading'" />
    <div
      v-else
      :class="{
        'claim-record': true,
      }"
    >
      <DetailsHeader
        :platform="getPlatform()"
        :icon-back="isHeaderShowBack"
        :title="$t('206485-claim_service')"
        :show-bottom-border="true"
        @click="handleBackClick"
      />
      <div
        v-if="loadingStatus === 'success'"
        :class="{
          'claim-record__content': true,
          'claim-record__contentNoButtom': hideClaimButton,
        }"
      >
        <div
          v-if="claimList && claimList.length"
          class="claim-list"
        >
          <div
            v-for="(item, index) in claimList"
            :key="index"
            class="claim-item"
            :data-spm-module="`ClaimCard?oid=claim_id_${
              item.claim_id
            }&ext=${JSON.stringify({
              BookingID: data.addon_booking_nos,
              MainBookingID: data.main_booking_no,
              PlanCode: data.plan_codes,
              PackageReferenceID: data.package_ref_id,
              CoveragePackage: data.coverage_package,
            })}`"
            data-spm-virtual-item="__virtual"
            @click="handleToDetail(item)"
          >
            <div class="unit">
              {{ item.sku_text }}
            </div>
            <div class="submit-time">
              {{ item.create_time_utc }}
            </div>
            <div class="claim-status">
              <klk-steps
                v-if="item.status_text_list.length > 0"
                :current="1"
                direction="vertical"
              >
                <klk-step
                  v-for="(statusItem, index) in item.status_text_list"
                  :key="index"
                  :title="statusItem.application_status_text"
                >
                  <div slot="content">
                    <div class="update-info update-time">
                      {{ statusItem.log_time }}
                    </div>
                    <!-- <div
                      class="update-info update-desc"
                      v-html="statusItem.application_status_ext_text"
                    /> -->
                    <div class="update-info update-desc" @click="handleMkClick">
                      <klk-markdown
                        :content="statusItem.application_status_ext_text"
                      />
                    </div>
                    <div class="detail-btn">
                      {{ $t("176295") }}
                    </div>
                  </div>
                  <img
                    slot="icon"
                    :src="statusMap[statusItem.web_status_v2]"
                    class="step-icon"
                  >
                </klk-step>
              </klk-steps>
            </div>
          </div>
        </div>
        <KlkEmptyPanel
          v-else
          :title="$t('206646')"
          icon-width="147"
          icon-height="121"
          icon-src="https://res.klook.com/image/upload/empty_stateAsset_31_1_xapcwg.svg"
          class="claim-list-empty"
        />
      </div>
      <div
        v-if="loadingStatus === 'success' && !hideClaimButton"
        class="claim-record__bottom"
      >
        <klk-button
          type="primary"
          reverse
          class="claim-record__bottomBtn"
          :data-spm-module="`ClaimSubmissionBtn?oid=mainbooking_id_${
            data.main_booking_no
          }&ext=${JSON.stringify({
            BookingID: data.addon_booking_nos,
            PlanCode: data.goods_plan_code_list,
            PackageReferenceID: data.package_ref_id,
            CoveragePackage: data.package_code,
          })}`"
          data-spm-virtual-item="__virtual"
          @click="handleClickClaim"
        >
          {{ $t("176273-claim") }}
        </klk-button>
      </div>
      <ErrorPanel
        v-if="loadingStatus === 'error'"
        @refresh="fetchData"
      />
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Watch } from 'vue-property-decorator';
import {
  IconChevronDown,
  IconInformation,
  IconChevronUp,
  IconNext,
} from '@klook/klook-icons';
import {
  $colorTextPrimary,
  $colorTextLink,
} from '@klook/klook-ui/lib/utils/design-token-esm';
import KlkEmptyPanel from '@klook/empty-panel';
import { getAntiSpiderHead } from '../../utils';
import InsuranceDetails from '../common/details.vue';
import DetailsHeader from '../common/header.vue';
import ErrorPanel from '../common/error-panel.vue';
import ModuleBase from './base.vue';

@Component({
  components: {
    ModuleBase,
    InsuranceDetails,
    DetailsHeader,
    IconChevronDown,
    IconInformation,
    IconChevronUp,
    IconNext,
    ErrorPanel,
    KlkEmptyPanel,
  },
})
export default class ClaimRecord extends ModuleBase {
  colorTextPrimary: string = $colorTextPrimary;
  colorTextLink: string = $colorTextLink;
  api: string = '/v1/insuranceapisrv/outer/carrental/claim/getClaimDetailList';
  loadingStatus: string = 'loading'; // 'loading' | 'error' | 'success'
  errorMsg: string = '';

  data: any = {};
  claimProcessVisible: boolean = false;
  statusMap: Record<string, string> = {
    1: 'https://res.klook.com/image/upload/icon_feedback_pending_fill_l_1_qhvern.jpg', // 进行中（黄色）
    2: 'https://res.klook.com/image/upload/icon_feedback_pending_fill_l_2_nulxtp.jpg', // 进行中（蓝色）
    3: 'https://res.klook.com/image/upload/__jb9htr.jpg', // 已完成(绿色)
    4: 'https://res.klook.com/image/upload/___KIcon_KIcons.icon_clear_filled_size__32_colors__colorScheme.colorTextPrimary_oap3gm.jpg', // 拒绝
  };

  get claimList() {
    return this.data?.claim_detail_list || [];
  }

  get hideClaimButton() {
    return this.data?.claim_btn?.claim_btn_status === 'not visible';
  }

  get pageSpm() {
    const {
      addon_booking_nos,
      main_booking_no,
      plan_codes,
      coverage_package,
      package_ref_id,
    } = this.data || {};

    return `Insurance_ClaimResultList?trg=manual&oid=BookingID_${addon_booking_nos}&ext=${JSON.stringify(
      {
        MainBookingID: main_booking_no,
        PlanCodes: plan_codes,
        PackageReferenceID: package_ref_id,
        CoveragePackage: coverage_package,
      }
    )}`;
  }

  created() {
    this.fetchData().then(() => {
      this.$nextTick(() => {
        this.fixBrowserBottomBar('claim-record-container', 'claim-record__content', 48 + 68)
      });
    });
  }

  handleMkClick(e: any) {
    // 避免点击markdown链接时触发父元素的点击事件
    if (e.target.tagName.toLowerCase() === 'a') {
      e.stopPropagation();
    }
  }

  handleClickClaim() {
    const hasDraft = this.data?.claim_btn_status?.exist_claim_draft || false;
    if (hasDraft) {
      this.$emit('setStep', {
        newStep: 'claimSubmit',
        data: {
          liability_id_list:
            this.data?.claim_btn_status?.liability_id_list || [],
          scenario_id_list: this.data?.claim_btn_status?.scenario_id_list || [],
          is_draft: true,
        },
      });
    } else {
      this.$emit('setStep', {
        newStep: 'claimScenario',
        data: {},
      });
    }
  }

  handleToDetail(detail: Record<string, any>) {
    this.$emit('setStep', {
      newStep: 'claimDetail',
      data: detail,
    });
  }

  fetchData() {
    return this.$axios
      .$get(this.api, {
        params: { booking_no: this.getBookingNo && this.getBookingNo() },
      })
      .then((res) => {
        if (!res.success || !res.result) {
          this.loadingStatus = 'error';
          this.errorMsg = (res.error && res.error.message) || '';
          this.processRequestError(res);
          return;
        }
        this.data = res.result;
        this.loadingStatus = 'success';
        this.$nextTick(() => {
          this.$inhouse.track('pageview', this.$refs.container);
        });
        return res.result;
      })
      .catch(() => {
        this.loadingStatus = 'error';
      });
  }
}
</script>

<style lang="scss" scoped>
@import "./mixins.scss";

.claim-record-container {
  @include module-page;

  .claim-record {
    height: 100%;

    .claim-record__content {
      @include module-content;
      padding: 20px;
      background-color: #eeeeee;

      .claim-list-empty {
        margin-top: 20%;
      }

      .claim-list {
        .claim-item {
          width: 100%;
          text-align: left;
          border-radius: 16px;
          background-color: #fff;
          padding: 16px;
          margin-bottom: 12px;

          .unit {
            font-size: 16px;
            font-weight: 600;
            line-height: 24px;
          }

          .submit-time {
            @include font-body-s-regular;
            color: $color-text-secondary;
            border-bottom: 1px solid #e6e6e6;
            margin-bottom: 12px;
            padding-bottom: 12px;
          }

          .claim-status {
            display: flex;
            justify-content: flex-start;
            align-items: flex-start;
            ::v-deep .klk-step-title {
              @include font-body-m-regular;
            }
            ::v-deep .klk-step-head-inner {
              margin-right: 10px;
            }
          }

          .status-icon {
            margin-right: 8px;
            font-size: 24px;
          }

          .status {
            font-size: 16px;
            font-weight: 400;
            line-height: 24px;
            height: 24px;
          }

          .step-icon {
            width: 21px;
            height: auto;
            margin-left: -4px;
            margin-top: 3px;
          }

          .update-info {
            font-size: 14px;
            font-weight: 400;
            line-height: 21px;
            margin: 4px 0;
            ::v-deep .klk-markdown {
              @include markdown-style;
              @include font-body-s-regular;
              color: $color-text-secondary;
              a {
                @include font-body-s-regular;
              }
            }
          }

          .detail-btn {
            @include font-body-s-regular;
            color: $color-text-primary;
            text-decoration: underline;
            cursor: pointer;
          }
        }
      }
    }

    .claim-record__contentNoButtom {
      @include module-content($hide-bottom: true);
    }

    .claim-record__bottom {
      @include claim-bottom;
    }

    .claim-record__bottomBtn {
      @include claim-bottom-btn;
    }
  }
}

// 这里的样式层级结构一定要写的跟.claim-record-container一样，
// 如果少写了一层.claim-record，那么web样式的优先级会变得更高
.claim-record-container-mobile {
  @include module-page-mobile;

  .claim-record {
    .claim-record__content {
      @include module-content-mobile;
      padding: 20px 16px;
    }

    .claim-record__contentNoButtom {
      @include module-content-mobile($hide-bottom: true);
    }

    .claim-record__bottom {
      border: 0;
    }
  }
}

.claim-record-container-app {
  .claim-record {
    // .claim-record__content {
    //   @include module-content-app;
    // }

    // .claim-record__contentNoButtom {
    //   @include module-content-app($hide-bottom: true);
    // }

    // .claim-record__bottom {
    //   padding-bottom: 32px;
    // }
  }
}
</style>
