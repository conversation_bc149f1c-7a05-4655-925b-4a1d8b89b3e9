@mixin claim-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: sticky;
  bottom: 0px;
  width: 100%;
  left: 0px;
  padding: 12px 20px;
  gap: 10px;
  background: #ffffff;
  z-index: 10;
  border-top: 1px solid $color-border-normal;
}

@mixin claim-bottom-btn {
  flex: 1;
  align-self:stretch; // 高度撑满容器，设计师要求按钮高度跟随最高按钮
}

@mixin module-content($hide-bottom: false) {
  height: calc(100vh - 64px - 68px - 1px);
  overflow-y: scroll;
  @if ($hide-bottom == true) {
    height: calc(100vh - 64px);
  }
}

@mixin module-content-mobile($hide-bottom: false) {
  height: calc(100vh - 48px - 68px);
  overflow-y: scroll;
  @if ($hide-bottom == true) {
    height: calc(100vh - 48px);
  }
}
@mixin module-content-app($hide-bottom: false) {
  // app底部有黑条，要再垫高
  height: calc(100vh - 68px - 20px);
  overflow-y: scroll;
  @if ($hide-bottom == true) {
    height: calc(100vh - 20px);
  }
}

@mixin module-page {
  width: 680px;
  height: 100vh;
  overflow-y: hidden;
}

@mixin module-page-mobile {
  width: 100%;
}

@mixin markdown-style {
  * {
    margin: 0;
  }
  ol {
    padding-left: 30px;
  }
  ul {
    padding-left: 30px;
  }
}