<template>
  <div
    ref="container"
    :data-spm-page="pageSpm"
    :class="{
      'claim-submit-result-container': true,
      'claim-submit-result-container-mobile': isMobile,
      'claim-submit-result-container-app': getIsApp(),
    }"
  >
    <div
      :class="{
        'claim-submit-result': true,
      }"
    >
      <DetailsHeader
        :platform="getPlatform()"
        :icon-back="isHeaderShowBack"
        :show-bottom-border="true"
        @click="handleBackClick"
      />
      <div class="claim-submit-result__content">
        <KlkEmptyPanel
          :title="initData.title"
          :content="initData.sub_title"
          icon-width="160"
          icon-height="160"
          :icon-src="initData.icon"
          :primary-btn-text="initData.button_text"
          class="claim-list-empty"
          @primary-btn-click="handleClick"
        />
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Component } from 'vue-property-decorator';
import KlkEmptyPanel from '@klook/empty-panel';
import DetailsHeader from '../common/header.vue';
import ModuleBase from './base.vue';

@Component({
  components: {
    ModuleBase,
    KlkEmptyPanel,
    DetailsHeader,
  },
})
export default class ClaimSubmitResult extends ModuleBase {

  get pageSpm() {
    const { addon_booking_nos, main_booking_no, plan_codes, coverage_package, package_ref_id } = this.initData || {};
    return `Insurance_ClaimSubmissionComplete?trg=manual&oid=BookingID_${addon_booking_nos}&ext=${JSON.stringify({
      MainBookingID: main_booking_no,
      PlanCode: plan_codes,
      PackageReferenceID: package_ref_id,
      CoveragePackage: coverage_package,
    })}`;
  }

  mounted() {
    this.fixBrowserBottomBar('claim-submit-result-container', 'claim-submit-result__content', 48)
    this.$inhouse.track('pageview', this.$refs.container);
  }

  handleClick() {
    this.closeContainer();
    setTimeout(() => {
      this.refreshCard && this.refreshCard()
    })
  }
}
</script>

<style lang="scss" scoped>
@import "./mixins.scss";

.claim-submit-result-container {
  @include module-page;

  .claim-submit-result {
    height: 100%;

    &__content {
      @include module-content($hide-bottom: true);
      // height: calc(100vh - 64px);
      padding: 20px;

      .claim-list-empty {
        margin-top: 20%;
        ::v-deep .klk-empty-panel-title {
          color: $color-text-primary;
        }
      }
    }
  }
}

.claim-submit-result-container-mobile {
  @include module-page-mobile;

  .claim-submit-result {
    .claim-submit-result__content {
      @include module-content-mobile($hide-bottom: true);
      padding: 20px 16px;
    }
  }
}

.claim-submit-result-container-app {
  .claim-submit-result {
    // .claim-submit-result__content {
    //   @include module-content-app;
    //   height: 100vh;
    // }
  }
}
</style>
