import PackageDetails from './package-details.vue';
import ClaimProcess from './claim-process.vue';
import ClaimRecord from './claim-record.vue';
import ClaimDetail from './claim-detail.vue';
import ClaimScenario from './claim-scenario.vue';
import ClaimInstruction from './claim-instruction.vue';
import ClaimSubmit from './claim-submit.vue';
import ClaimSubmitResult from './claim-submit-result.vue';
import ClaimMerchantSubmit from './claim-merchant-submit.vue';
import ExternalLink from './external-link.vue';

export default {
  packageDetails: PackageDetails,
  claimProcess: ClaimProcess,
  claimRecord: ClaimRecord,
  claimDetail: ClaimDetail,
  claimScenario: ClaimScenario,
  claimInstruction: ClaimInstruction,
  claimSubmit: ClaimSubmit,
  claimSubmitResult: ClaimSubmitResult,
  claimMerchantSubmit: ClaimMerchantSubmit,
  externalLink: ExternalLink
};