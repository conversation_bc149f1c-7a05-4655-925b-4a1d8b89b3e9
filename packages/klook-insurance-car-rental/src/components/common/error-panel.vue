<template>
  <KlkEmptyPanel
    :title="$t('122710')"
    :content="innerMsg"
    icon-width="200"
    icon-height="160"
    icon-src="https://res.klook.com/image/upload/v1712548948/ued/platform/Edge%20case%20illustrations/ill_spot_loading_failed.svg"
    :secondary-btn-text="$t('122709')"
    :class="{
      'insurance-error-panel': true,
    }"
    @secondary-btn-click="handleRefresh"
  />
</template>

<script lang="ts">
import { Vue, Component, Prop, Watch } from 'vue-property-decorator';
import KlkEmptyPanel from '@klook/empty-panel';

@Component({
  components: {
    KlkEmptyPanel,
  },
})
export default class ErrorPanel extends Vue {
  @Prop() msg!: string;

  innerMsg: string = '';

  @Watch('msg', { immediate: true })
  onMsgChange(val: string) {
    this.innerMsg = val || this.$t('122711');
  }

  handleRefresh() {
    // 刷新页面
    this.$emit('refresh');
  }
}
</script>

<style lang="scss" scoped>
.insurance-error-panel {
  margin-top: 30%;
}
</style>
