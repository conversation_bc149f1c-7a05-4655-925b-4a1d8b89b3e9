<template>
  <klk-table
    :columns="columns"
    :data="tableData"
    border
    class="comparison-table"
    :max-height="isAboveIOS16 ? tableMaxHeight : null"
  >
    <template #package-header="{ col }">
      <div class="package-header-cell">
        <div class="package-name">
          {{ col.title }}
        </div>
      </div>
    </template>

    <template #benefit="{ row }">
      <div class="benefit-cell">
        <div class="benefit-text">
          {{ row.benefit }}
        </div>
        <div class="desc-text">
          <klk-markdown :content="row.desc" />
        </div>
      </div>
    </template>

    <template
      v-for="pkg in packageList"
      #[pkg.reference_id]="{ row }"
    >
      <div
        :key="pkg.reference_id"
        class="package-cell"
      >
        <template v-if="row[pkg.reference_id]">
          <div
            class="excess"
            v-html="formatExcess(row[pkg.reference_id])"
          />

          <div
            v-if="row[pkg.reference_id].insured_source"
            class="insured"
          >
            <div
              v-if="hasInsuranceIcons(row[pkg.reference_id])"
              class="insuredIcon"
            >
              <template
                v-for="(icon, index) in getIcons(row[pkg.reference_id])"
              >
                <img
                  :key="index"
                  height="16"
                  :src="icon"
                  class="icon"
                >
                <strong
                  v-if="shouldShowPlus(row[pkg.reference_id], index)"
                  :key="index"
                >+</strong>
              </template>
            </div>
            <span>{{ row[pkg.reference_id].insured_source }}</span>
          </div>
        </template>
        <IconClose
          v-else
          theme="outline"
          size="16"
          :fill="tokens['$color-error']"
        />
      </div>
    </template>
  </klk-table>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import { IconClose } from '@klook/klook-icons';
import { Table as KlkTable } from '@klook/klook-ui';
import { tokens } from '@klook/klook-ui/lib/utils/design-token';
import {
  IPackageDetail,
  Package,
  InsuranceDetail,
} from '@/types/package-detail';

@Component({
  components: {
    IconClose,
    KlkTable,
  },
})
export default class ComparisonTable extends Vue {
  @Prop({ type: Object, required: true })
  readonly packageInfo!: IPackageDetail;

  tableMaxHeight = 0;
  tableWidth = 0;
  readonly tokens = tokens;

  mounted() {
    this.calculateTableDimensions();
    window.addEventListener('resize', this.calculateTableDimensions);
  }

  beforeDestroy() {
    window.removeEventListener('resize', this.calculateTableDimensions);
  }

  calculateTableDimensions() {
    const container = this.$el.parentElement;
    const style = window.getComputedStyle(container);

    const paddingVertical =
      parseFloat(style.paddingTop) + parseFloat(style.paddingBottom);
    const paddingHorizontal =
      parseFloat(style.paddingLeft) + parseFloat(style.paddingRight);

    const containerHeight = container.clientHeight - paddingVertical;
    const containerWidth = container.clientWidth - paddingHorizontal;

    this.tableMaxHeight = containerHeight - 51;
    this.tableWidth = containerWidth;
  }

  get packageList(): Package[] {
    return this.packageInfo?.package_list ?? [];
  }

  get columnWidth() {
    const columnCount = this.packageList.length + 1;
    return Math.max(
      240,
      Math.floor((this.tableWidth - columnCount - 1) / columnCount)
    );
  }

  getIOSVersion() {
    const ua = window.navigator.userAgent;
    const match = ua.match(/OS (\d+)_/i);
    return match ? parseInt(match[1], 10) : null;
  }

  get isAboveIOS16() {
    const iosVersion = this.getIOSVersion();
    return iosVersion === null || iosVersion > 16;
  }

  get columns() {
    const iosVersion = this.getIOSVersion();
    const isAboveIOS16 = iosVersion === null || iosVersion > 16;
    const baseColumn = {
      title: this.$t('205707'),
      dataIndex: 'benefit',
      key: 'benefit',
      width: Math.max(
        240,
        this.tableWidth - this.columnWidth * this.packageList.length
      ),
      slot: 'benefit',
      ...(isAboveIOS16 ? { fixed: 'left' } : {}),
    };

    const packageColumns = this.packageList.map((pkg) => ({
      title: pkg.package_name,
      dataIndex: pkg.reference_id,
      key: pkg.reference_id,
      slot: pkg.reference_id,
      width: this.columnWidth,
      headerSlot: 'package-header',
    }));

    return [baseColumn, ...packageColumns];
  }

  get tableData() {
    const { rows } = this.packageList.reduce(
      (acc, pkg) => {
        pkg.insurance_detail_list.forEach((detail) => {
          const { sub_insurance_id } = detail;

          if (!acc.rowsMap[sub_insurance_id]) {
            const rowData = {
              id: sub_insurance_id,
              benefit: detail.text,
              desc: detail.desc,
              sort_id: detail.sort_id,
            };
            acc.rowsMap[sub_insurance_id] = rowData;
            acc.rows.push(rowData);
          }

          acc.rowsMap[sub_insurance_id][pkg.reference_id] = detail;
        });
        return acc;
      },
      {
        rowsMap: {} as Record<string, any>,
        rows: [] as Record<string, any>[],
      }
    );

    // 按sort_id排序,sort_id越小越靠前
    return rows.sort((a, b) => a.sort_id - b.sort_id);
  }

  getIcons(detail: InsuranceDetail): string[] {
    const icons: string[] = [];
    if (detail.rental_company_icon) {
      icons.push(detail.rental_company_icon);
    }
    if (detail.insured_source_icon) {
      icons.push(detail.insured_source_icon);
    }
    return icons;
  }

  hasInsuranceIcons(detail: InsuranceDetail): boolean {
    return this.getIcons(detail).length > 0;
  }

  shouldShowPlus(detail: InsuranceDetail, index: number): boolean {
    const icons = this.getIcons(detail);
    return index < icons.length - 1;
  }

  formatExcess(item: InsuranceDetail): string {
    if (!item.sub_text) return '';

    if (item.price_desc && item.sub_text.includes(item.price_desc)) {
      return item.sub_text.replace(
        item.price_desc,
        `<strong>${item.price_desc}</strong>`
      );
    }
    return item.sub_text;
  }
}
</script>

<style lang="scss" scoped>
.comparison-table {
  ::v-deep .klk-table {
    &-wrapper
      .klk-table-common-table-head-inner
      thead
      > tr
      > th
      > .klk-table-cell {
      padding: 12px;
      @include font-heading-xs-v2;
      color: $color-text-primary;
    }

    &-left-fixed {
      left: 1px !important;
      .klk-table-common-table-body {
        overflow-y: visible !important;
      }
    }

    &-main {
      .klk-table-common-table-body {
        overflow: auto;
        overscroll-behavior: none;
        -webkit-overscroll-behavior: none;
        -webkit-overflow-scrolling: touch;
      }
    }
  }

  .benefit-cell {
    display: flex;
    flex-direction: column;
    gap: 4px;
    color: $color-text-primary;
    word-break: break-word;

    .benefit-text {
      @include font-body-s-bold-v2;
    }

    .desc-text {
      ::v-deep .klk-markdown {
        * {
          @include font-paragraph-s-regular;
          margin: 0;
        }
      }
    }
  }

  .package-header-cell {
    width: 100%;
    text-align: right;
  }

  .package-cell {
    text-align: right;
    word-break: break-word;

    .excess {
      @include font-paragraph-s-regular;
      color: $color-text-secondary;

      ::v-deep strong {
        color: $color-text-primary;
      }
    }

    .insured {
      display: flex;
      align-items: center;
      gap: 4px;
      margin-top: 20px;
      justify-content: flex-end;
      font-weight: 400;
      font-size: 12px;
      line-height: 132%;
      font-style: italic;
      color: $color-text-secondary;

      .insuredIcon {
        display: inline-flex;
        align-items: center;
        gap: 4px;
        flex-wrap: nowrap;

        .icon {
          vertical-align: middle;
        }
      }

      strong {
        margin: 0 2px;
      }
    }
  }
}
</style>
