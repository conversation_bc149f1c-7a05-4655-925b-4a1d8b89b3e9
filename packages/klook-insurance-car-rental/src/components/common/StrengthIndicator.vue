<template>
  <div class="strength-indicator">
    <StrengthIndicatorIcon />
    <div class="strength-indicator__block">
      <div
        v-for="(i, idx) in totalBlocks"
        :key="idx"
        :class="getBlockClass(i)"
        class="strength-block"
      />
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import StrengthIndicatorIcon from '../../assets/strength-indicator-icon.vue';

@Component({
  components: {
    StrengthIndicatorIcon,
  },
})
export default class StrengthIndicator extends Vue {
  @Prop({ type: Number, required: true }) strength!: number;
  @Prop({ type: Number, default: 5 }) totalBlocks!: number; // 支持自定义块数

  getBlockClass(index: number): string {
    const floor = Math.floor(this.strength);
    const ceil = Math.ceil(this.strength);

    if (index <= floor) {
      return 'strength-block--full';
    } else if (index === ceil && this.strength % 1 !== 0) {
      return 'strength-block--half';
    }
    return 'strength-block--empty';
  }
}
</script>

<style lang="scss" scoped>
.strength-indicator {
  display: flex;
  line-height: 1;
  position: relative;
  svg {
    z-index: 10;
  }
  &__block {
    position: absolute;
    top: 3px;
    left: 7px;
    padding: 4px;
    padding-left: 14px;
    border: 1px solid $color-success;
    border-radius: 100px;
    height: 14px;
    display: flex;
    align-items: center;
    gap: 2px;
  }

  .strength-block {
    width: 8px;
    height: 6px;
    border-radius: 0.5px;
  }
} /* 完全激活的块 */
.strength-block--full {
  background: linear-gradient(90deg, #08b371 0%, #08b371 100%);
} /* 半激活的块 */
.strength-block--half {
  background: linear-gradient(90deg, #08b371 50%, #d5d1d0 50%);
} /* 未激活的块 */
.strength-block--empty {
  background: linear-gradient(90deg, #d5d1d0 0%, #d5d1d0 100%);
}
</style>
