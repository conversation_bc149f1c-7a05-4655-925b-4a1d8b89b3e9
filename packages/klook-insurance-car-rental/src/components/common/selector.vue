<template>
  <div
    v-click-outside="hide"
    class="select"
    :class="{ disabled: disabled }"
    @click="showSelect"
  >
    <div class="select-container">
      <div class="label-warp">
        <span class="label-font" :style="{ 'font-size': font + 'px' }">{{
          selected.label
        }}</span>
        <!-- <div class="label-icon" :class="{ rotate: show }">
          <template v-if="disabled">
            <SvgIcon
              name="desktop-car-rental#icon_navigation_chevron_up_xs"
              width="16"
              height="16"
            />
          </template>
          <template v-else>
            <SvgIcon
              name="desktop-car-rental#icon_navigation_chevron_down_xs"
              width="16"
              height="16"
            />
          </template>
        </div> -->
        <div class="label-icon" :class="{ rotate: true }">
          <IconChevronDown :size="16"></IconChevronDown>
        </div>
      </div>
      <div v-show="show" class="list">
        <div
          v-for="(item, i) in list"
          :key="i"
          class="item"
          :class="{ active: item.label === selected.label }"
          @click.stop="chooseItem(item)"
        >
          {{ item.label }}
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Vue, Component, Prop, Watch } from "nuxt-property-decorator";
import {
  IconChevronDown,
} from "@klook/klook-icons";

@Component({
  components: {
    IconChevronDown
  },
})
export default class Selector extends Vue {
  @Prop() list!: any[];
  @Prop() default!: any;
  @Prop({ default: false }) disabled!: boolean;
  @Prop({ default: 14 }) font!: number;

  show = false;
  selected = {
    label: "",
    value: "",
  };

  @Watch("default")
  defaultChange(val: any) {
    if (val) {
      this.selected = val;
    }
  }

  mounted() {
    if (this.default) {
      this.selected = this.default;
    }
  }

  chooseItem(item: any) {
    this.selected.label = item.label;
    this.$emit("choose", item);
    this.show = false;
  }

  showSelect() {
    if (this.disabled) {
      return;
    }
    this.show = !this.show;
  }

  hide() {
    this.show = false;
  }
}
</script>

<style lang="scss" scoped>
.select {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-sizing: border-box;
  // margin-left: 6px;
  cursor: pointer;

  .label-warp {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .label-font {
      font-size: $fontSize-body-s;
      margin-right: 6px;
    }

    .label-icon {
      height: 16px;

      svg {
        color: #4a4a4a;
      }
    }

    .rotate {
      height: 16px;
      width: 16px;
      // transform: rotate(180deg);
      transform: rotate(270deg);
    }
  }
}

.disabled {
  cursor: not-allowed;
  color: $color-text-disabled;
}

.select-container {
  position: relative;
  width: 100%;

  .list {
    max-height: 203px;
    border-radius: $radius-s;
    box-shadow: 0 1px 6px 0 rgba(0, 0, 0, 0.2);
    overflow: scroll;
    position: absolute;
    top: 36px;
    left: 0;
    background: $color-bg-widget-normal;
    box-sizing: border-box;
    text-align: center;
    color: $color-text-secondary;
    z-index: 9999;

    .item {
      width: auto;
      min-width: 80px;
      height: 27px;
      display: flex;
      padding: 0 12px;
      align-items: center;
      justify-content: center;
      font-size: $fontSize-body-s;
      cursor: pointer;
      white-space: nowrap;

      &:hover {
        background: $color-bg-page;
      }

      &.active {
        background: $color-bg-widget-normal;
        color: $color-brand-primary;
      }
    }
  }
}
</style>
