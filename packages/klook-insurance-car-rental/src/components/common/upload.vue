<template>
  <div
    :class="{
      'sdk-upload': true,
      'sdk-upload-desktop': !isMobile,
    }"
  >
    <div v-if="filesResponse && filesResponse.length" class="sdk-upload__files">
      <div
        v-for="(item, itemIndex) in filesResponse"
        :key="itemIndex"
        class="sdk-upload__filesItem"
        @click="handleClickFile(item.display_file)"
      >
        <div class="sdk-upload__filesItemName">
          {{ getItemFileName(item) }}
        </div>
        <div @click.stop="handleUploadRemoved(item.display_file, item)">
          <IconClose theme="outline" size="16" fill="#212121" />
        </div>
      </div>
    </div>
    <klk-upload
      ref="klkUpload"
      class="uploader"
      :action="action"
      :max-file-size="maxFileSize"
      :limit="limit"
      :accept="accept"
      :upload-tips="uploadTips"
      :hide-file-list="true"
      :name="name"
      :data="data"
      :headers="headers"
      @success="handleUploadSuccess"
      @error="handleUploadError"
    >
      <input
        ref="input"
        type="file"
        hidden
        :accept="accept"
        :multiple="multiple"
        @change="onFileChange"
      />
      <div
        :class="{
          'sdk-upload__button': true,
          'sdk-upload__buttonDisabled': innerDisabled,
        }"
        @click="handleUpload"
      >
        <IconUpload
          theme="outline"
          size="24"
          :class="{
            'sdk-upload__buttonIcon': true,
          }"
        />
        <div :class="{ 'sdk-upload__buttonTips': true }">
          {{ $t("206506-click_to_uplpad") }}
        </div>
      </div>
    </klk-upload>
    <klk-image-viewer
      :open.sync="openImageViewer"
      :value="0"
      :images="images"
      :width="960"
    />
  </div>
</template>

<script lang="ts">
import { IconClose, IconUpload } from "@klook/klook-icons";
import { Component, Vue, Prop } from "vue-property-decorator";
import { $colorTextPrimary } from "@klook/klook-ui/lib/utils/design-token-esm";

@Component({
  components: {
    IconClose,
    IconUpload,
  },
})
export default class Header extends Vue {
  @Prop({ type: String, default: "" }) action!: string;
  @Prop({ type: String, default: "" }) name!: string;
  @Prop({ type: String, default: "" }) uploadTips!: string;
  @Prop({ type: Number, default: 1024 }) maxFileSize!: number;
  @Prop({ type: Number, default: 6 }) limit!: number;
  @Prop({ type: String, default: "" }) limitMsg!: string;
  @Prop({ type: String, default: "image/jpg, image/jpeg, image/png" })
  accept!: string;
  @Prop({ type: Boolean, default: false }) disabled!: boolean;
  @Prop({ type: Boolean, default: false }) multiple!: boolean;
  @Prop({ type: Array, default: [] }) files!: [];
  @Prop({ type: Object, default: {} }) data!: {};
  @Prop({ type: Object, default: {} }) headers!: {};
  @Prop({ type: Boolean, default: false }) isMobile!: boolean;

  colorTextPrimary: string = $colorTextPrimary;
  filesResponse: any[] = []; // 用于和后端交互
  openImageViewer: boolean = false;
  images: any[] = []; // 用于图片查看器
  // pdfUrl: string = ""; // 用于PDF预览
  // pdfModalVisible: boolean = false;

  get innerDisabled() {
    return this.disabled || this.filesResponse.length >= this.limit;
  }

  mounted() {
    this.filesResponse = this.files;
    this.$refs.klkUpload.items = this.filesResponse.map((item) => {
      if (item.display_file) {
        if (item.display_file.file) {
          return item.display_file;
        }
        return {
          ...item.display_file,
          file: {},
        };
      }
      return { file: {} };
    });
  }

  getItemFileName(item) {
    return item?.display_file?.file?.name;
  }

  openPDF(url: string) {
    this.$emit('open-pdf', url);
  }

  handleClickFile(item) {
    if (!item) {
      return;
    }
    this.$emit('clickFile', item);
    if (item?.file?.type === "application/pdf") {
      this.openPDF(item.url);
    } else {
      this.openImageViewer = true;
      this.images = [item.url];
    }
  }

  handleUpload() {
    if (this.$refs.input && !this.innerDisabled) {
      this.$refs.input.value = null;
      this.$refs.input.click();
    } else if (this.files.length >= this.limit) {
      this.$toast(this.limitMsg);
    }
  }

  handleUploadSuccess(res, file, item) {
    const data = {
      ...res.result,
      display_file: {
        ...item,
        url: res.result.location_uri,
        blobUrl: item.url,
        file: {
          name: file.name,
          type: file.type,
        },
      },
    };
    this.filesResponse.push(data);
    const obj = {
      data,
      responses: this.filesResponse,
    };
    this.$emit("success", obj);
  }

  handleUploadRemoved(file) {
    const items = this.$refs.klkUpload.items || [];
    const index = items.findIndex(
      (item) => item.url === file.blobUrl || item.url === file.url
    );
    this.$refs.klkUpload.removeItem(items[index]);
    const res = this.filesResponse.splice(index, 1);
    const obj = {
      data: res,
      responses: this.filesResponse,
    };
    this.$emit("remove", obj);
  }

  handleUploadError(err, file) {
    this.$emit("error", err, file);
  }

  onFileChange(e) {
    this.$refs.klkUpload.onFileChange(e);
  }
}
</script>

<style lang="scss" scoped>
.sdk-upload {
  ::v-deep .klk-upload-tips {
    margin-top: 10px;
  }
  &__files {
    margin: 12px 0px;
    display: flex;
    flex-direction: column;
    gap: 12px;
    &Item {
      cursor: pointer;
      display: flex;
      gap: 10px;
      background: #ffebe0;
      padding: 10px;
      border-radius: $radius-l;
      justify-content: space-between;
      align-items: center;
      &Name {
        @include font-body-s-regular;
        color: $color-text-link;
        max-width: calc(100% - 30px);
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }
  &__button {
    border-radius: $radius-l;
    // 2px的虚线
    border: 2px dashed $color-border-normal;
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 10px 0px;
    cursor: pointer;
    &Tips {
      @include font-body-s-regular;
      color: $color-text-primary;
    }
    &Icon {
      color: $color-text-primary;
    }
  }
  &__buttonDisabled {
    cursor: not-allowed;
    .sdk-upload__buttonTips {
      color: #a6a6a6;
    }
    .sdk-upload__buttonIcon {
      color: #a6a6a6;
    }
  }
}
.sdk-upload-desktop {
  .sdk-upload__files {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    gap: 10px;
  }
  .sdk-upload__filesItem {
    width: calc(50% - 5px); /* 两列等宽，减去间距 */
  }
}
</style>
