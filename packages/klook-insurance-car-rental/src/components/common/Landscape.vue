<template>
  <div
    ref="wrapper"
    class="landscape-wrapper"
  >
    <slot />
  </div>
</template>

<script lang="ts">
import { Vue, Component, Ref } from 'vue-property-decorator';

@Component({ name: 'Landscape' })
export default class Landscape extends Vue {
  @Ref() readonly wrapper!: HTMLDivElement;

  private parentElement!: HTMLElement;
  private observer!: ResizeObserver;

  mounted() {
    this.parentElement = this.wrapper.parentElement as HTMLElement;
    this.initOrientationHandler();
    this.observeParentSize();
  }

  beforeDestroy() {
    window.removeEventListener('orientationchange', this.handleOrientation);
    this.observer?.disconnect();
  }

  // 监听父容器尺寸变化
  private observeParentSize() {
    this.observer = new ResizeObserver(() => {
      this.adjustLayout();
    });
    this.observer.observe(this.parentElement);
  }

  // 初始化方向变化监听
  private initOrientationHandler() {
    window.addEventListener('orientationchange', this.handleOrientation);
    this.handleOrientation(); // 初始执行一次
  }

  // 方向变化处理
  private handleOrientation = () => {
    this.adjustLayout();
  };

  private adjustLayout() {
    const isPortrait = window.matchMedia('(orientation: portrait)').matches;
    const wrapper = this.wrapper;

    if (isPortrait) {
      const parentWidth = this.parentElement.offsetWidth;
      const parentHeight = this.parentElement.offsetHeight;

      // 竖屏时：宽高调换
      wrapper.style.width = `${parentHeight}px`;
      wrapper.style.height = `${parentWidth}px`;
      wrapper.style.transform = 'rotate(90deg)';
      wrapper.style.transformOrigin = 'left top';
      wrapper.style.left = `${parentWidth}px`; // 补偿旋转偏移
    } else {
      // 横屏时：恢复默认
      wrapper.style.width = '100%';
      wrapper.style.height = '100%';
      wrapper.style.transform = 'none';
      wrapper.style.left = '0';
    }

  }
}
</script>

<template>
  <div class="landscape-wrapper" ref="wrapper">
    <slot />
  </div>
</template>

<style scoped>
.landscape-wrapper {
  position: absolute;
  top: 0;
  left: 0;
  overflow: hidden;
  transition: transform 0.3s; /* 添加平滑过渡 */
}
</style>
