<template>
  <div
    :class="{ mobile: isMobile }"
    class="insurance-details-component"
  >
    <div
      :class="{
        'insurance-details': true,
        'insurance-details-bottom-mobile': isMobile,
        'insurance-details-bottom-desktop': !isMobile,
      }"
    >
      <div
        v-if="data.see_title"
        class="insurance-details__see"
        @click="seeVisible = true"
      >
        <klk-poptip
          dark
          placement="bottom-end"
          :prevent-overflow="true"
        >
          <div class="insurance-details__seeInner">
            <span>{{ data.see_title }}</span>
            <IconInformation
              theme="outline"
              size="16"
              :fill="colorTextPrimary"
              class="insurance-details__seeInner_icon"
            />
          </div>
          <div
            slot="content"
            class="insurance-details__seeInnerMarkdown"
            :data-spm-module="`ExcessDefine?oid=packagecode_${data.package_code}&ext=${JSON.stringify(
              {
                PlanCode: data.goods_plan_code_list,
              }
            )}`"
          >
            <klk-markdown :content="data.see_detail" />
          </div>
        </klk-poptip>
      </div>
      <div class="insurance-details__list">
        <div
          v-for="(item, itemIndex) in list"
          :key="itemIndex"
          class="insurance-details__item"
        >
          <div class="insurance-details__itemTitle">
            {{ item.text }}
          </div>
          <div
            v-if="item.sub_text"
            class="insurance-details__itemTips"
          >
            <span class="insurance-details__itemTipsCircle" />
            <klk-markdown
              :content="getTipsStr(item.sub_text, item.price_desc)"
            />
          </div>
          <div
            v-if="item.insured_source"
            class="insurance-details__itemTips"
          >
            <span class="insurance-details__itemTipsCircle" />
            <div
              v-if="item.insured_source_icon || item.rental_company_icon"
              class="insurance-details__itemInsured"
            >
              <img
                v-if="item.insured_source_icon"
                :src="item.insured_source_icon"
              >
              <strong
                v-if="item.insured_source_icon && item.rental_company_icon"
              >+</strong>
              <img
                v-if="item.rental_company_icon"
                :src="item.rental_company_icon"
              >
            </div>
            <klk-markdown :content="item.insured_source" />
          </div>
          <div
            v-if="item.desc"
            class="insurance-details__itemDesc"
          >
            <klk-markdown :content="item.desc" />
          </div>
          <div
            v-if="item.excess_price_info"
            class="insurance-details__itemExcessPrice"
          >
            <div>
              {{ item.excess_price_info.desc }}
            </div>
            <div
              v-if="
                item.excess_price_info.note_info &&
                  item.excess_price_info.note_info.content &&
                  item.excess_price_info.note_info.content.length
              "
              class="insurance-details__itemExcessNote"
            >
              <div
                v-if="item.excess_price_info.note_info.title"
                class="insurance-details__itemExcessNoteTitle"
              >
                {{ item.excess_price_info.note_info.title }}
              </div>
              <div class="insurance-details__itemExcessNoteDesc">
                <klk-markdown
                  v-for="(item, itemIndex) in item.excess_price_info.note_info
                    .content"
                  :key="itemIndex"
                  :content="item"
                />
              </div>
            </div>
            <table
              v-if="item.excess_price_info && item.excess_price_info.list"
              class="insurance-details__itemExcessTable"
            >
              <tr
                v-for="(trItem, trItemIndex) in item.excess_price_info.list"
                :key="trItemIndex"
              >
                <td class="insurance-details__itemExcessTableTd1">
                  {{ trItem.role }}
                </td>
                <td class="insurance-details__itemExcessTableTd2">
                  {{ trItem.price }}
                </td>
              </tr>
            </table>
          </div>
          <div
            v-if="item.claim_guide_info && !item.claimGuideVisible"
            class="insurance-details__itemClaimGuide"
            @click="handleClaimGuideClick(itemIndex)"
          >
            <span>{{ $t("205703-show_claim_guide") }}</span>
            <IconChevronDown
              class="icon"
              theme="outline"
              size="16"
              :fill="colorTextLink"
            />
          </div>
          <div
            v-if="item.claimGuideVisible"
            class="insurance-details__itemGuideText"
          >
            <div
              v-for="(guideItem, guideItemIndex) in item.claim_guide_info.list"
              :key="guideItemIndex"
              class="insurance-details__itemGuideTextItem"
            >
              <klk-markdown :content="guideItem" />
            </div>
          </div>
          <div
            v-if="item.claim_guide_info && item.claimGuideVisible"
            v-galileo-click-tracker="{ spm: 'ClaimGuideBtn', autoTrackSpm: true }"
            :data-spm-module="`ClaimGuideBtn?trg=manual&oid=packagecode_${data.package_code}ext=${JSON.stringify(
              {
                showType: item.claimGuideVisible ? 'show' : 'hide',
                BenefitId: item.benefit_id,
                ClaimType: item.claim_type,
                PlanCode: data.goods_plan_code_list,
              }
            )}`"
            data-spm-virtual-item="__virtual"
            class="insurance-details__itemClaimGuide"
            @click="handleClaimGuideClick(itemIndex)"
          >
            <span>{{ $t("205704-hide_claim_guide") }}</span>
            <IconChevronUp
              class="icon"
              theme="outline"
              size="16"
              :fill="colorTextLink"
            />
          </div>
        </div>
      </div>
      <div
        v-if="
          data.note_info &&
            data.note_info.content &&
            data.note_info.content.length
        "
        class="insurance-details__itemExcessNote"
      >
        <div
          v-if="data.note_info.title"
          class="insurance-details__itemExcessNoteTitle"
        >
          {{ data.note_info.title }}
        </div>
        <div class="insurance-details__itemExcessNoteDesc">
          <klk-markdown
            v-for="(item, itemIndex) in data.note_info.content"
            :key="itemIndex"
            :content="item"
          />
        </div>
      </div>
      <div
        v-if="
          data.terms_and_conditions_info &&
            data.terms_and_conditions_info.list &&
            data.terms_and_conditions_info.list.length
        "
        class="insurance-details__item"
      >
        <div class="insurance-details__itemTitle">
          {{ data.terms_and_conditions_info.title }}
        </div>
        <div>
          <div
            v-for="(precautionsItem, precautionsItemIndex) in data
              .terms_and_conditions_info.list"
            :key="precautionsItemIndex"
            class="insurance-details__TermsItemTips"
          >
            <klk-markdown :content="precautionsItem" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import {
  IconChevronDown,
  IconInformation,
  IconChevronUp,
  IconNext,
} from '@klook/klook-icons';
import {
  $colorTextPrimary,
  $colorTextLink,
} from '@klook/klook-ui/lib/utils/design-token-esm';

interface DetailsDataType {
  insurance_detail_list: any[];
  see_title: string;
  see_detail: string;
  excess_price_info: any;
  goods_plan_code_list: string[];
  package_code: string;
}

@Component({
  components: {
    IconInformation,
    IconChevronDown,
    IconChevronUp,
    IconNext,
  },
})
export default class InsuranceDetails extends Vue {
  @Prop() data!: DetailsDataType;
  @Prop({ type: Boolean, default: false }) isMobile!: boolean;

  seeVisible: boolean = false;
  claimGuideVisible: boolean = false;
  colorTextPrimary: string = $colorTextPrimary;
  colorTextLink: string = $colorTextLink;

  get list() {
    return this.data?.insurance_detail_list || [];
  }

  getTipsStr(str: string, strongStr: string) {
    return str.replace(
      strongStr,
      `<span style='color: #FF5B00'>${strongStr}</span>`
    );
  }

  handleClaimGuideClick(index: number) {
    const visible = this.data?.insurance_detail_list[index].claimGuideVisible;
    this.$set(
      this.data?.insurance_detail_list[index],
      'claimGuideVisible',
      !visible
    );
  }
}
</script>

<style lang="scss" scoped>
.insurance-details-component {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE 10+ */
  ::-webkit-scrollbar {
    display: none; /* Chrome Safari */
  }
}
.insurance-details-bottom-mobile {
  padding-bottom: 50px;
}
.insurance-details {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE 10+ */
  ::-webkit-scrollbar {
    display: none; /* Chrome Safari */
  }
  .insurance-details-inner-sheet {
    width: 750px;
    right: 0px !important;
    left: auto;
  }
  &__see {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 8px;
  }
  &__seeInner {
    cursor: pointer;
    display: flex;
    gap: 4px;
    > span {
      text-decoration: underline;
    }
  }
  &__seeInnerMarkdown {
    ::v-deep .klk-markdown {
      color: #ffffff;
      * {
        margin: 0;
      }
      ol {
        padding-left: 28px;
      }
    }
  }
  &__item {
    &:not(:first-child) {
      margin-top: 8px;
    }
  }
  &__itemTitle {
    @include font-body-s-bold-v2;
    color: $color-text-primary;
  }
  &__itemTips {
    @include font-paragraph-xs-regular;
    color: $color-text-secondary;
    display: flex;
    align-items: center;
    gap: 6px;
    ::v-deep .klk-markdown {
      @include font-paragraph-xs-regular;
      color: $color-text-secondary;
      * {
        margin: 0;
      }
      ol {
        padding-left: 30px;
      }
      ul {
        padding-left: 30px;
      }
    }
  }
  &__TermsItemTips {
    @include font-body-s-regular-v2;
    color: $color-text-secondary;
    display: flex;
    align-items: flex-start;
    gap: 6px;
    ::v-deep .klk-markdown {
      @include font-body-s-regular-v2;
      color: $color-text-secondary;
      * {
        margin: 0;
      }
      ol {
        padding-left: 30px;
      }
      ul {
        padding-left: 30px;
      }
    }
  }
  &__itemTipsCircle {
    width: 4px;
    height: 4px;
    background-color: $color-text-secondary;
    border-radius: 50%;
    display: inline-block;
    flex-shrink: 0;
  }
  &__itemInsured {
    display: flex;
    align-items: center;
    gap: 4px;
    > img {
      height: 12px;
      width: auto;
    }
  }
  &__itemExcessPrice {
    @include font-paragraph-s-regular;
    color: $color-text-primary;
    margin-top: 12px;
  }
  &__itemDesc {
    @include font-paragraph-s-regular;
    color: $color-text-primary;
    margin-top: 8px;
    ::v-deep .klk-markdown {
      @include font-paragraph-s-regular;
      color: $color-text-primary;
      * {
        margin: 0;
        @include font-paragraph-s-regular;
      }
      ol {
        padding-left: 30px;
      }
      ul {
        padding-left: 30px;
      }
      p {
        @include font-paragraph-s-regular;
      }
    }
  }
  &__itemExcessNote {
    border: 1px solid #f09b0a;
    background: $color-caution-background;
    border-radius: 12px;
    padding: 16px;
    display: flex;
    flex-direction: column;
    gap: 4px;
    margin-top: 8px;
    &Title {
      @include font-body-m-semibold;
      color: $color-text-primary;
    }
    &Desc {
      @include font-paragraph-s-regular;
      color: $color-text-primary;
      ::v-deep .klk-markdown {
        @include font-paragraph-s-regular;
        color: $color-text-primary;
        * {
          margin: 0;
        }
        ol {
          padding-left: 30px;
        }
        ul {
          padding-left: 30px;
        }
        p {
          @include font-paragraph-s-regular;
        }
      }
    }
  }
  &__itemExcessTable {
    width: 100%;
    border: 1px solid #eeeeee;
    border-collapse: collapse;
    margin-top: 12px;
    tr {
      &:not(:last-child) {
        border-bottom: 1px solid #eeeeee;
      }
      td {
        padding: 12px;
      }
    }
    &Td1 {
      width: 128px;
      border-right: 1px solid #eeeeee;
      text-align: left;
    }
    &Td2 {
      @include font-body-s-bold-v2;
      width: auto;
      text-align: right;
    }
  }
  &__itemClaimGuide {
    @include font-body-s-regular-v2;
    color: $color-text-link;
    margin-top: 12px;
    display: flex;
    align-items: center;
    cursor: pointer;
    .icon {
      margin-top: 3px;
      margin-left: 7px;
    }
  }
  &__itemGuideText {
    @include font-paragraph-s-regular;
    color: $color-text-primary;
    margin-top: 10px;
    &Item {
      ::v-deep .klk-markdown {
        @include font-paragraph-s-regular;
        color: $color-text-primary;
        * {
          margin: 0;
        }
        ol {
          padding-left: 30px;
        }
        ul {
          padding-left: 30px;
        }
      }
    }
  }
}

.insurance-details__policy {
  @include font-body-s-regular-v2;
  color: $color-text-link;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: absolute;
  bottom: 0px;
  width: 100%;
  left: 0px;
  background: #ffffff;
  z-index: 10;
  padding: 0px 32px;
  height: 49px;
  &Item {
    display: flex;
    align-items: center;
    gap: 6px;
  }
  &Icon {
    margin-top: 4px;
  }
}

.mobile {
  .insurance-details__seeInner_icon {
    margin-top: 2px;
  }
  .insurance-details__policy {
    padding: 0px 20px;
  }
  .insurance-details__itemTitle {
    @include font-heading-xs-v2;
  }
}
.insurance-details__policy_isPage {
  position: fixed;
}
</style>
