<template>
  <div class="process-list">
    <div
      v-for="(item, itemIndex) in list"
      :key="itemIndex"
      class="process-list__processContent"
    >
      <div class="process-list__processTitle">
        {{ item.title }}
      </div>
      <div class="process-list__processDesc">
        <klk-markdown :content="item.description" />
      </div>
      <klk-steps
        v-if="item.step_list && item.step_list.length"
        direction="vertical"
        :current="item.step_list.length"
        class="process-list__benefitSteps"
      >
        <klk-step
          v-for="(benefitItem, benefitIndex) in item.step_list"
          :key="benefitIndex"
          :title="benefitItem.step_title"
          :content="benefitItem.step_desc"
        >
          <div
            slot="icon"
            class="process-list__benefitItemIcon"
          >
            <span>{{ benefitIndex + 1 }}</span>
          </div>
        </klk-step>
      </klk-steps>
      <div
        v-if="item.claim_document_info && !item.claimGuideVisible"
        class="process-list__itemClaimGuide"
        @click="handleClaimGuideClick(itemIndex)"
      >
        <span>{{ $t("205703-show_claim_guide") }}</span>
        <IconChevronDown
          class="icon"
          theme="outline"
          size="16"
          :fill="colorTextLink"
        />
      </div>
      <div
        v-if="item.claim_document_info && item.claimGuideVisible"
        class="process-list__itemGuideText"
      >
        <klk-markdown :content="item.claim_document_info" />
      </div>
      <div
        v-if="item.claim_document_info && item.claimGuideVisible"
        class="process-list__itemClaimGuide"
        @click="handleClaimGuideClick(itemIndex)"
      >
        <span>{{ $t("205704-hide_claim_guide") }}</span>
        <IconChevronUp
          class="icon"
          theme="outline"
          size="16"
          :fill="colorTextLink"
        />
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Vue, Component, Prop } from 'vue-property-decorator';
import {
  IconChevronDown,
  IconInformation,
  IconChevronUp,
  IconNext,
} from '@klook/klook-icons';
import {
  $colorTextPrimary,
  $colorTextLink,
} from '@klook/klook-ui/lib/utils/design-token-esm';

@Component({
  components: {
    IconChevronDown,
    IconInformation,
    IconChevronUp,
    IconNext,
  },
})
export default class ClaimProcessList extends Vue {
  @Prop() list!: any[];

  colorTextPrimary: string = $colorTextPrimary;
  colorTextLink: string = $colorTextLink;

  handleClaimGuideClick(index: number) {
    const visible = this.list[index].claimGuideVisible;
    this.$set(this.list[index], 'claimGuideVisible', !visible);
  }
}
</script>

<style lang="scss" scoped>
.process-list {
  display: flex;
  flex-direction: column;
  gap: 24px;

  &__processTitle {
    @include font-heading-xs-v2;
    color: $color-text-primary;
  }

  &__processDesc {
    @include font-paragraph-s-regular;
    color: $color-text-primary;
    margin-top: 12px;
  }

  &__benefitSteps {
    margin-top: 12px;

    ::v-deep .klk-step-main .klk-step-title {
      // 租车项目改了step组件的字体，这里需要改回来
      font-size: 16px !important;
    }
  }

  &__benefitItemIcon {
    width: 26px;
    height: 26px;
    border-radius: 50%;
    background: #08b371;
    color: #ffffff;
    @include font-body-s-bold-v2;
    align-items: center;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &__itemClaimGuide {
    @include font-body-s-regular-v2;
    color: $color-text-link;
    margin-top: 12px;
    display: flex;
    align-items: center;
    cursor: pointer;

    .icon {
      margin-top: 3px;
      margin-left: 7px;
    }
  }

  &__itemGuideText {
    @include font-paragraph-s-regular;
    color: $color-text-primary;
    margin-top: 10px;

    &Item {
      ::v-deep .klk-markdown {
        @include font-paragraph-s-regular;
        color: $color-text-primary;

        * {
          margin: 0;
        }

        ol {
          padding-left: 24px;
        }
      }
    }
  }
}
</style>
