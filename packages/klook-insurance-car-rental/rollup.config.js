import replace from "rollup-plugin-replace";
import typescript from "rollup-plugin-typescript2";
import postcss from "rollup-plugin-postcss";
import { terser } from "rollup-plugin-terser";
import vue from "rollup-plugin-vue";
import babel from "rollup-plugin-babel";
import nodeResolve from "rollup-plugin-node-resolve";
import commonjs from "rollup-plugin-commonjs";

const { name, version, author } = require("./package.json");

const banner = `/**
  * v${version}
  * (c) ${new Date().getFullYear()} ${author}
  */`;
const configs = {
  umd: {
    output: "dist/umd/index.js",
    format: "umd",
    target: "es5",
    env: "production",
  },
  umdMin: {
    output: "dist/umd/index.min.js",
    format: "umd",
    target: "es5",
    plugins: {
      post: [terser()],
    },
    env: "production",
  },
  esm: {
    output: "dist/esm/index.js",
    format: "esm",
    target: "es2015",
    env: "production",
    genDts: true,
  },
  cjs: {
    output: "dist/cjs/index.js",
    format: "cjs",
    target: "es2015",
  },
};

const externals = [
  "vue",
  "vue-property-decorator",
  "dayjs",
  "lodash",
  "@klook/klook-icons",
];

const genTsPlugin = (configOpts) =>
  typescript({
    useTsconfigDeclarationDir: true,
    tsconfigOverride: {
      compilerOptions: {
        target: configOpts.target,
        declaration: configOpts.genDts,
      },
    },
    abortOnError: false,
  });

const genPlugins = (configOpts) => {
  const plugins = [
    nodeResolve({
      extensions: [".mjs", ".js", ".jsx", ".vue"],
    }),
    commonjs({
      include: /node_modules/,
    }),
    genTsPlugin(configOpts),
    babel({
      runtimeHelpers: true,
      exclude: "node_modules/**",
      extensions: [".js", ".jsx", ".ts", ".tsx"],
      presets: ["@vue/babel-preset-jsx"],
    }),
  ];
  if (configOpts.env) {
    plugins.push(
      replace({
        "process.env.NODE_ENV": JSON.stringify(configOpts.env),
      })
    );
  }
  plugins.push(
    replace({
      "process.env.MODULE_FORMAT": JSON.stringify(configOpts.format),
    })
  );
  if (configOpts.plugins && configOpts.plugins.pre) {
    plugins.push(...configOpts.plugins.pre);
  }

  if (configOpts.plugins && configOpts.plugins.post) {
    plugins.push(...configOpts.plugins.post);
  }

  plugins.push(
    vue({
      css: false,
      normalizer: "~vue-runtime-helpers/dist/normalize-component.js",
      template: {
        isProduction: true,
        // optimizeSSR: configOpts.isSSR,
      },
      style: {
        preprocessOptions: {
          scss: {
            data: '@import "node_modules/@klook/klook-ui/lib/styles/token/index.scss";',
          },
        },
        postcssPlugins: [require("autoprefixer")()],
      },
    })
  );

  // 不必提取css
  plugins.push(
    postcss({
      extract: false,
      plugins: [require("autoprefixer")()],
    })
  );
  return plugins;
};

const genConfig = (configOpts) => ({
  input: "src/index.ts",
  output: {
    banner,
    file: configOpts.output,
    format: configOpts.format,
    name: name,
    sourcemap: false,
    exports: "named",
    globals: configOpts.globals,
  },
  external: externals,
  plugins: genPlugins(configOpts),
});

const genAllConfigs = (configs) =>
  Object.keys(configs).map((key) => genConfig(configs[key]));

export default genAllConfigs(configs);
