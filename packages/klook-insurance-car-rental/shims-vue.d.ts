import Vue from 'vue';

declare module '*.vue' {
  import Vue from 'vue';

  export default Vue;
}

declare module 'vue/types/vue' {
  interface Vue {
    $t: Function;
    $axios: any;
    $toast: Function;
    $inhouse: any;
    $href: Function;
    $robots: any;
    klook: any;
  }
}

declare module '@klook/klook-ui';
declare module '@klook/klook-ui/*';
declare module '@klook/empty-panel';

declare global {
  interface Window {
    __KLOOK__?: {
      state: {
        klook: {
          isKlookApp?: boolean,
          language?: string
        }
      }
    };
  }
}
