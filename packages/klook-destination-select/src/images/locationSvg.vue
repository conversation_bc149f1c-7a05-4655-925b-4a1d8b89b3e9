<template>
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="20"
    height="20"
    fill="currentColor"
    viewBox="0 0 20 20"
  >
    <path
      fill-rule="evenodd"
      d="M12.51 8.935a2.507 2.507 0 11-5.014 0 2.507 2.507 0 015.013 0zm-1.4 0a1.107 1.107 0 11-2.214 0 1.107 1.107 0 012.213 0z"
      clip-rule="evenodd"
    />
    <path
      fill-rule="evenodd"
      d="M17 9c0 4.26-4.321 7.563-6.173 8.783a1.492 1.492 0 01-1.654 0C7.321 16.563 3 13.26 3 9a7 7 0 0114 0zm-1.4 0c0 1.65-.843 3.224-2.05 4.613-1.194 1.376-2.62 2.426-3.494 3a.1.1 0 01-.********** 0 01-.056-.02c-.873-.574-2.3-1.624-3.495-3C5.243 12.224 4.4 10.65 4.4 9a5.6 5.6 0 0111.2 0z"
      clip-rule="evenodd"
    />
  </svg>
</template>
<script lang="ts">
import { Component, Vue } from "vue-property-decorator";
@Component({
  name: "LocationSvg",
})
export default class LocationSvg extends Vue {}
</script>
