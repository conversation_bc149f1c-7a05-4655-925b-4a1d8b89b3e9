import { Vue, Component, Prop } from "vue-property-decorator";
import { LangData } from '../../types/types'

@Component({})
export default class SearchBox extends Vue {
  @Prop({ type: Object, default: () => {} })
  langData!: LangData;

  @Prop({ type: Object }) tracker!: any;
  _axios!: any;

  sendGTMCustomEvent(data: any){
    return this.tracker?.gtm?.sendGTMCustomEvent(data) || window?.tracker && window?.tracker?.gtm?.sendGTMCustomEvent(data)
  }

  beforeMount() {
    this._axios = this.$attrs.axios || window.$axios;
  }
}
