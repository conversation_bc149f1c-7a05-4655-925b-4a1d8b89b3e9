<template>
  <ul class="hot-destinations">
    <li v-for="(city, index) in cities" :key="`${city.klook_id}${index}`">
      <a
        v-lazy:background-image.container="city.image_url"
        class="hot-destinations_link"
        :data-spm-item="spm(city)"
        v-galileo-click-tracker="{ spm: 'ChangeDestination.Destination', componentName: 'klook-destination-select' }"
        :href="replaceUrlHost(city.klook_area_url)"
        data-img-type="hotDestinationsImg"
        @click="sendData(city.klook_id)"
      >
        <span class="hot-destinations_name">{{ city.klook_area_name }}</span>
      </a>
    </li>
  </ul>
</template>

<script lang="ts">
import { Component, Prop } from "vue-property-decorator";
import replaceUrlHost from '@klook/klk-traveller-utils/lib/replaceUrlHost';
import Base from "../mixins/base";

@Component
export default class HotDestinations extends Base {
  @Prop({ default: () => [] }) cities!: any[];

  replaceUrlHost: Function = replaceUrlHost;

  spm(city: any) {
    return `__default?typ=entry&oid=klook_id_${
      city.klook_id
    }&ext=${JSON.stringify({
      LinkURL: encodeURIComponent(city.klook_area_url),
    })}&mod=stop`;
  }

  sendData(id: number) {
    this.sendGTMCustomEvent(
      `Explore by City Navigation Box|Navigation Box Popular Destination Chosen|${id}`
    );
    this.sendGTMCustomEvent(
      "Navigation Bar Search Box|Search Bar Listed Destination Clicked"
    );
  }
}
</script>

<style lang="scss">
.hot-destinations {
  display: flex;
  flex-wrap: wrap;
  margin-left: -10px;
  margin-right: -10px;

  li {
    flex-shrink: 0;
    width: 25%;
    padding: 10px;
  }

  &_link {
    display: block;
    position: relative;
    width: 130px;
    height: 74px;
    background-color: $color-bg-widget-darker-2;
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
    border-radius: $radius-s;
    color: $color-text-primary-onDark;
    overflow: hidden;
  }

  &_name {
    display: block;
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    padding-bottom: 3px;
    /* stylelint-disable */
    background: linear-gradient(to top, rgba(0, 0, 0, 0.65), transparent 100%);
    /* stylelint-enable */
    border-radius: $radius-s;
    font-weight: $fontWeight-regular;
    text-align: center;
  }
}
</style>
