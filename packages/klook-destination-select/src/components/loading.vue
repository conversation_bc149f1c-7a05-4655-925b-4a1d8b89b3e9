<template>
  <div v-show="visible" class="search-box-loading">
    <div class="search-box-loading_spin"></div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator'

@Component
export default class SearchBoxLoading extends Vue {
  @Prop({ default: false }) visible!: boolean
}
</script>

<style lang="scss">

.search-box-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: $color-bg-widget-normal;

  &_spin {
    width: 24px;
    height: 24px;
    border-top: 3px solid $color-border-normal;
    border-right: 3px solid $color-border-normal;
    border-bottom: 3px solid $color-border-normal;
    border-left: 3px solid $color-brand-primary;
    border-radius: $radius-circle;
    animation: search-box-spin 1.1s infinite linear;
  }
}

@keyframes search-box-spin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}
</style>
