<template>
  <div class="area-selector">
    <div
      v-if="langData.select_country_region !== 'select_country_region'"
      class="area-selector_label"
    >
      {{ langData.select_country_region }}
    </div>
    <ul class="area-selector_list">
      <li
        v-for="(country, index) in countries"
        :key="`${country.klook_id}${index}`"
      >
        <b class="area-selector_country">
          <a
            :href="replaceUrlHost(country.klook_area_url)"
            :data-spm-item="spm(country)"
            v-galileo-click-tracker="{ spm: 'ChangeDestination.Destination', componentName: 'klook-destination-select' }"
            :data-country-id="country.klook_id"
            >{{ country.klook_area_name }}</a
          >
        </b>
        <div class="area-selector_cities">
          <a
            v-for="(city, index2) in country.leaf_menu_list || []"
            :key="`${city.klook_id}${index2}`"
            :data-spm-item="spm(city)"
            v-galileo-click-tracker="{ spm: 'ChangeDestination.Destination', componentName: 'klook-destination-select' }"
            :href="replaceUrlHost(city.klook_area_url)"
            :data-city-id="city.klook_id"
            @click="sendData(city.klook_id)"
            >{{ city.klook_area_name }}</a
          >
        </div>
      </li>
    </ul>
  </div>
</template>

<script lang="ts">
import { Component, Prop } from "vue-property-decorator";
import replaceUrlHost from '@klook/klk-traveller-utils/lib/replaceUrlHost';
import Base from "../mixins/base";
import { Country } from "../../types/types";

@Component
export default class AreaSelector extends Base {
  @Prop({ default: () => [] }) countries!: Country[];

  sendData(id: number) {
    this.sendGTMCustomEvent(
      `Explore by City Navigation Box | Navigation Box Destination Chosen | ${id}`
    );
    this.sendGTMCustomEvent(
      "Navigation Bar Search Box|Search Bar Listed Hot Destination Clicked"
    );
  }

  spm(city: any) {
    return `__default?typ=entry&oid=klook_id_${
      city.klook_id
    }&ext=${JSON.stringify({
      LinkURL: encodeURIComponent(city.klook_area_url),
    })}&mod=stop`;
  }

  replaceUrlHost(url: string) {
    return replaceUrlHost(url);
  }
}
</script>

<style lang="scss">
.area-selector {
  width: 100%;

  &_label {
    margin-bottom: 20px;
  }

  &_list {
    > li {
      display: flex;
      align-items: flex-start;
    }

    li + li {
      margin-top: 10px;
    }
  }

  &_ticket {
    display: flex;
    align-items: center;
    padding: 4px 8px;
    margin: 8px 0;
    border: solid 1px $color-border-active;
    border-radius: $radius-s;
    color: $color-text-secondary;
    font-size: $fontSize-body-s;

    span {
      margin-left: 8px;
    }
  }

  &_country {
    width: 26%;
    margin-right: 24px;

    &:lang(zh-Hans-CN),
    &:lang(zh-Hant-HK),
    &:lang(zh-Hant-TW),
    &:lang(ko) {
      width: 14%;
    }

    > a {
      &:hover {
        color: $color-brand-primary;
      }
    }
  }

  &_cities {
    flex: 1;

    a {
      display: inline-block;
      margin-right: 24px;
      margin-bottom: 10px;
      color: $color-text-secondary;

      &:hover {
        color: $color-brand-primary;
      }
    }
  }
}
</style>
