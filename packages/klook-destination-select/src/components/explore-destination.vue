<template>
  <div
    id="js-header-destination-box"
    class="destination-box"
    @mouseenter="handleEnter"
    @mouseleave="handleLeave"
  >
    <slot>
      <div class="destination-box_button">
        <LocationSvg />
        <span id="js-destination-box-text">{{
          langData.explore_destinations
        }}</span>
      </div>
    </slot>
    <div
      v-show="showPopover"
      id="destination-box_popover"
      class="destination-box_popover"
      @click="handleContentClick"
    >
      <Recommend
        v-if="isLoadRecommend"
        :data="recommendData"
        :lang-data="langData"
        data-spm-module="ChangeDestination.Destination"
      ></Recommend>
      <Loading :visible="loading"></Loading>
    </div>
  </div>
</template>

<script lang="ts">
import { Component } from "vue-property-decorator";
import Popover from "./popover.vue";
import Loading from "./loading.vue";
import Base from "../mixins/base";
import LocationSvg from '../images/locationSvg.vue'

@Component({
  components: {
    Recommend: () => import('./recommend.vue').then((res: any)=>{
      return res.default || res
    }),
    Loading,
    Popover,
    LocationSvg
  },
})
export default class ExploreDestination extends Base {

  showPopover: boolean = false;
  isLoadRecommend: boolean = false
  loading: boolean = false;
  private timerId: number | undefined;
  private resolvedData = false;
  recommendData: any = {
    destinations: [],
    regions: [],
  };

  getRecommendData() {
    // 如果数据可用，防止重复加载
    if (this.resolvedData) {
      return;
    }

    this.resolvedData = true;
    this.loading = true;
    this._axios
      .$get("/v1/usrcsrv/destination/guide")
      .then((res: any) => {
        if (res.success && res.result) {
          this.recommendData = {
            destinations: res.result.destination_guide_menu_list,
            hotCities: res.result.popular_area_list,
          };
          this.loading = false;
        } else {
          this.resolvedData = false;
        }
      })
      .catch(() => {
        this.resolvedData = false;
      });
  }

  handleEnter() {
    window.clearTimeout(this.timerId);
    this.timerId = window.setTimeout(() => {
      this.getRecommendData();
      this.showPopover = true;
      this.isLoadRecommend = true
      this.$emit("handle-enter");
    }, 200);
  }

  handleLeave() {
    window.clearTimeout(this.timerId);
    this.timerId = window.setTimeout(() => {
      this.showPopover = false;
    }, 200);
  }

  handleContentClick() {
    this.showPopover = false;
  }
}
</script>

<style lang="scss">
.destination-box {
  &_button {
    display: flex;
    justify-items: center;
    -webkit-box-align: center;
    align-items: center;
    border: 1px solid $color-white;
    padding: 0 10px;
    height: 40px;
    caret-color: #ff5722;
    box-sizing: border-box;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    border-radius: $radius-pill;
    cursor: pointer;
    font-size: $fontSize-body-s;
    color: $color-text-primary;
    font-weight: $fontWeight-semibold;

    &:hover {
      background-color: $color-bg-page;
    }
  }

  &_popover {
    position: absolute;
    top: 55px;
    left: 0;
    width: 860px;
    background-color: $color-bg-widget-normal;
    border-radius: $radius-l;
    /* stylelint-disable */
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);
    /* stylelint-enable */
    z-index: 800;
    overflow: hidden;

    &:lang(zh-Hans-CN),
    &:lang(zh-Hant-HK),
    &:lang(zh-Hant-TW),
    &:lang(ko) {
      width: 800px;
    }
  }
}
</style>
