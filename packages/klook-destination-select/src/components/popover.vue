<template>
  <div class="header-popover" @click="handleClick">
    <slot name="trigger"></slot>
    <div
      v-show="show"
      ref="popover"
      class="header-popover_container"
      :style="popoverStyles"
      @click.stop="handleContentClick"
    >
      <i class="header-popover_arrow" :style="arrowStyles"></i>
      <slot name="content"></slot>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch } from 'vue-property-decorator'

interface PopoverStyles {
  top?: string
  left?: string
  right?: string
  width?: string
  height?: string
  transform?: string
}

interface ArrowStyles extends Pick<PopoverStyles, 'left' | 'right'> {
  transform?: string
}

@Component
export default class HeaderPopover extends Vue {
  @Prop() width?: number
  @Prop() height?: number
  @Prop({ default: 'center' }) align!: 'left' | 'center' | 'right'
  @Prop({ default: 0 }) offset!: number
  @Prop({ default: 0 }) right!: number
  @Prop({ default: 0 }) arrowOffset!: number
  @Prop({ default: 64 }) top!: number

  show: boolean = false
  popoverStyles: PopoverStyles = {}
  arrowStyles: ArrowStyles = {}
  private timerId: number | undefined

  @Watch('show')
  onShowChange(value: boolean) {
    if (value) {
      this.$nextTick(() => {
        this.setStyles()
      })
    }

    this.$emit(value ? 'enter' : 'leave')
  }

  mounted() {
    document.addEventListener('click', () => {
      if (this.$refs.popover) {
        this.handleContentClick()
      }
    })
  }

  destroyed() {
    window.clearTimeout(this.timerId)
  }

  setStyles() {
    const halfArrowWidth = 10
    const { offset, arrowOffset } = this
    const $el = this.$el as HTMLElement
    // const triggerRect = this.$el.getBoundingClientRect()
    const triggerWidth = $el.offsetWidth
    const popoverStyles: PopoverStyles = {
      width: this.width ? `${this.width}px` : 'auto',
      height: this.height ? `${this.height}px` : 'auto',
      top: `${this.top}px`,
      transform: `translate(${-this.right}px,0)`
    }
    const arrowStyles: ArrowStyles = {
      transform: `translate(${this.right}px,0)`
    }

    if (this.align === 'left') {
      popoverStyles.left = `${$el.offsetLeft + offset}px`
      arrowStyles.left = `${triggerWidth / 2 - halfArrowWidth + arrowOffset}px`
    }

    if (this.align === 'right') {
      const width = this.width ? this.width : this.getPopoverWidth()
      popoverStyles.left = `${$el.offsetWidth + $el.offsetLeft - width + offset}px`
      arrowStyles.right = `${triggerWidth / 2 - halfArrowWidth - arrowOffset}px`
    }

    if (this.align === 'center') {
      const width = this.width ? this.width : this.getPopoverWidth()
      const halfWidth = width / 2
      const marginLeft = triggerWidth / 2
      const left = $el.offsetLeft - halfWidth + marginLeft

      popoverStyles.left = `${left + offset}px`
      arrowStyles.left = `${halfWidth - halfArrowWidth + arrowOffset}px`
    }

    this.popoverStyles = popoverStyles
    this.arrowStyles = arrowStyles
  }

  getPopoverWidth() {
    const styles = this.getStyles(this.$refs.popover as Element)
    return Number.parseFloat(styles.width || '0')
  }

  getStyles(el: Element) {
    const styles = window.getComputedStyle(el)
    return styles
  }

  handleClick() {
    window.clearTimeout(this.timerId)
    this.timerId = window.setTimeout(() => {
      this.show = !this.show
    }, 200)
  }

  handleContentClick() {
    this.show = false
  }
}
</script>

<style lang="scss">
.header-popover {
  display: inline-block;

  &_container {
    position: absolute;
    z-index: 200;
    background-color: $color-bg-widget-normal;
    border-radius: $radius-l;
    /* stylelint-disable */
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);
    /* stylelint-enable */
  }

  &_arrow {
    display: block;

    &::before,
    &::after {
      display: block;
      content: ' ';
      position: absolute;
      left: inherit;
      right: inherit;
      width: 0;
      height: 0;
      border-width: 10px;
      border-style: solid;
    }

    &::before {
      top: -20px;
      border-color: transparent transparent  rgba(0, 0, 0, 0.02) transparent;
    }

    &::after {
      top: -19px;
      border-color: transparent transparent $color-white transparent;
    }
  }

  .pop-fade-enter-active,
  .pop-fade-leave-active {
    transition: opacity 0.3s;
  }

  .pop-fade-enter,
  .pop-fade-leave-to {
    opacity: $opacity-transparent;
  }
}
</style>
