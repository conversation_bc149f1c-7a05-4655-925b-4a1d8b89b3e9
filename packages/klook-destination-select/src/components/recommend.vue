<template>
  <div class="search-box-recommend">
    <div class="search-box-recommend_left">
      <ul class="search-box-recommend_groups">
        <li
          v-for="(group, index) in groups"
          :key="group.area + index"
          :class="{ active: active === index }"
          @mouseenter="handleMouseEnter(index)"
          @mouseleave="handleMouseLeave"
        >
          <b class="search-box-recommend_area">{{ group.area }}</b>
          <div v-if="group.subareas.length" class="search-box-recommend_cities">
            <span v-for="city in group.subareas" :key="city">{{ city }}</span>
          </div>
        </li>
      </ul>
    </div>
    <div ref="box" class="search-box-recommend_right" @scroll="handleBoxScroll">
      <HotDestinations
        v-if="showHotDestination"
        v-show="active === 0"
        :cities="data.hotCities"
      ></HotDestinations>
      <AreaSelector
        v-for="(destination, index) in data.destinations"
        v-show="areaSelectorActive(index)"
        :key="index + 1"
        :lang-data="langData"
        :countries="destination.sub_menu_list"
      ></AreaSelector>
      <div v-show="hasMore" class="search-box-recommend_view-more">
        {{ langData.scroll_to_viewmore }}
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Prop, Ref } from "vue-property-decorator";
import { RecommendData } from "../../types/types";
import AreaSelector from "./area-selector.vue";
import HotDestinations from "./hot-destinations.vue";
import Base from "../mixins/base";

@Component({
  components: {
    HotDestinations,
    AreaSelector,
  },
})
export default class SearchBoxRecommend extends Base {
  @Ref("box") $box!: HTMLElement;
  @Prop({
    default: () => ({
      destinations: [],
      hotCities: [],
    }),
  })
  data!: RecommendData;

  @Prop({ default: true }) showHotDestination!: boolean;
  active: number = 0;
  hasMore: boolean = false;
  timer: number | undefined;

  /**
   * 转换数据结构
   * @returns {Array} [
   *  { area: '东南亚', subareas: ['普吉岛', '曼谷'] }
   * ]
   */
  get groups() {
    const { destinations } = this.data;
    const areaName = this.langData.desktop_index_hotdest;
    const hotDestinations = [{ area: areaName, subareas: [] }];
    const arr = this.showHotDestination ? hotDestinations : [];
    return arr.concat(
      destinations.slice(0, 6).map((destination) => ({
        area: destination.menu_name,
        subareas: Array.isArray(destination.popular_area_name_list)
          ? destination.popular_area_name_list.slice(0, 2)
          : [],
      }))
    );
  }

  areaSelectorActive(index: number) {
    return this.showHotDestination
      ? this.active === index + 1
      : this.active === index;
  }

  initViewMore() {
    const { scrollHeight, clientHeight } = this.$box;

    if (scrollHeight - clientHeight > 0) {
      this.hasMore = true;
    } else {
      this.hasMore = false;
    }
  }

  handleMouseEnter(index: number) {
    window.clearTimeout(this.timer);
    this.timer = window.setTimeout(() => {
      this.active = index;
      this.$nextTick(() => {
        this.initViewMore();
      });
    }, 100);
  }

  handleMouseLeave() {
    window.clearTimeout(this.timer);
  }

  handleBoxScroll() {
    const { scrollTop, scrollHeight, clientHeight } = this.$box;
    if (scrollHeight - scrollTop > clientHeight + 2) {
      // 距离底部2px就隐藏，避免高度四舍五入
      this.hasMore = true;
    } else {
      this.hasMore = false;
    }
  }
}
</script>

<style lang="scss">
.search-box-recommend {
  display: flex;
  height: 406px;

  &_left {
    width: calc(100% - 628px);
    border-right: 1px solid $color-border-dim;
  }

  &_right {
    flex: 1;
    padding: 14px 24px 24px;
    overflow-y: auto;
  }

  &_groups {
    > li {
      display: flex;
      flex-direction: column;
      justify-content: center;
      position: relative;
      min-height: 58px;
      padding: 6px 6px 6px 24px;
      line-height: 1.2;

      & + li {
        border-top: 1px solid $color-border-dim;
      }

      &::after {
        content: "";
        display: none;
        position: absolute;
        top: 50%;
        right: -16px;
        border-color: transparent transparent transparent $color-brand-primary;
        border-width: 8px;
        border-style: solid;
        transform: translateY(-50%);
      }

      &.active {
        background-color: $color-brand-primary;
        color: $color-text-primary-onDark;

        &::after {
          display: block;
        }

        .search-box-recommend_cities {
          color: inherit;
        }
      }
    }
  }

  &_cities {
    margin-top: 4px;
    color: $color-text-secondary;
    font-size: $fontSize-caption-m;

    span {
      display: inline-block;
      margin-right: 10px;
    }
  }

  &_view-more {
    position: absolute;
    bottom: 0;
    width: calc(100% - 170px - 48px); // 减掉 左边面板width 和 父级的左右padding
    height: 50px;
    padding-top: 18px;
    border-radius: $radius-s;
    background-image: linear-gradient(rgba(255, 255, 255, 0.5), #fff);
    color: $color-brand-primary;
    text-align: center;
  }
}
</style>
