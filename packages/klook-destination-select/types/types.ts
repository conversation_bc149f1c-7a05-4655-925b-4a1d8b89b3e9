// @TODO: add full types
export interface RecommendData {
  destinations: any[];
  hotCities: any[];
}

export interface City {
  klook_id: number;
  klook_area_name: string;
  klook_area_url: string;
}

export interface Country {
  klook_id: number;
  klook_area_name: string;
  klook_area_url: string;
  leaf_menu_list?: City[];
}

export interface LangData {
  select_country_region: string; // select_country_region
  explore_destinations: string; // explore.destinations
  scroll_to_viewmore: string; // scroll.to.viewmore
  desktop_index_hotdest: string; // desktop.index.hotdest
}