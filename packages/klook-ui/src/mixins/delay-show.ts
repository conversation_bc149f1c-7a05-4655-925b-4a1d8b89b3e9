// @ts-nocheck
export default {
    props: {
        delayTime: {
            type: Number, // 可用于优化INP (Interaction to Next Paint)；
            default: -1, // defalut slot渲染的延迟时间，设置>=0时生效：对应setTimeout的timeout值，单位毫秒。
        },
        delayRetainDom: {
            type: Boolean, // 是否保留延迟渲染的html
            defualt: false
        }
    },
    data() {
        return {
            delayShowData: {
              timer: null, // 定时器任务
              delayShow: false, // 真为显示，假为隐藏
              hasDelayShowed: false // 是否已经显示过
            }
        }
    },
    computed: {
      isDelayShow() {
        const {
          isDelayTime,
          delayRetainDom,
          delayShowData: { delayShow, hasDelayShowed }
        } = this

        if (!isDelayTime) {
          return true // 未设置延迟时间，则默认显示
        }

        if (delayRetainDom && hasDelayShowed) {
          return true // 如果设置了保留延迟渲染的 dom，并且已经显示过，则默认显示
        }

        // 设置延迟时间，则默认隐藏，延迟后赋值显示
        return delayShow
      },
      isDelayTime() {
        // 是否设置了延迟时间
        return this.delayTime >= 0
      }
    },
    methods: {
        setDelayShow(visible) {
          const { isDelayTime, delayTime, delayShowData: obj } = this
          if (!isDelayTime) {
            return // 未设置延迟时间，不处理
          }
          if (visible) {
            clearTimeout(obj.timer)
            obj.timer = setTimeout(() => {
              obj.delayShow = true;
              obj.hasDelayShowed = true; // 标记已经显示过
              this.$emit('delay', obj.delayShow);
            }, delayTime || 0);
          } else {
            obj.delayShow = false;
            this.$emit('delay', obj.delayShow);
          }
        }
    },
    beforeDestroy() {
      clearTimeout(this.delayShowData.timer)
    }
}
