<template>
  <div class="klk-image-viewer-video-item" :class="[isMobile ? 'mobile' : 'desktop']">
    <Loading v-if="isLoading" />
    <img v-if="isCrashed" :src="crashedSrc" />
    <video
      preload="metadata"
      :src="videoSrc"
      v-show="isNormal"
      :poster="poster"
      ref="video"
      :muted="muted"
      playsinline
      webkit-playsinline
      :autoplay="true"
    >
    </video>
    <div v-if="isNormal" class="video-item-mask" @click="onPlayClick">
      <Component
        v-show="!hiddenControls"
        :is="status === 0 ? 'IconPlayWhite' : 'IconPauseWhite'"
        size="56"
        theme="outline"
      />
    </div>
    <div v-if="isNormal" class="video-item-controls" :style="videoControlsStyle">
      <div class="controls-item controls-time">
        {{ formatTime(timeStart) }}
      </div>
      <Slider
        class="controls-progress"
        :value="timeStart"
        @input="onProgressInput"
        :max="timeLeft"
        ref="slider"
      />
      <div class="controls-item controls-time">
        {{ formatTime(timeLeft) }}
      </div>
      <div
        class="controls-item control-volume"
        @click="onMuteClick">
        <Component
          :is="muted ? 'IconMute' : 'IconVolume'"
          theme="outline"
          size="16"
          :fill="colorTextReverse"
        />
        <div v-show="!isMobile || (isMobile && showVolumeSlider)" class="volume-wrapper" @click.stop>
          <span class="volume-value">{{ currentVolume }}</span>
          <Slider
            :max="100"
            class="volume-slider"
            v-model="currentVolume"
            :vertical="true"
            :reverse="true"
          />
        </div>
      </div>
      <div class="controls-item" v-if="!isMobile" @click="onFulScreenClick">
        <Component
          :is="fullscreen ? 'IconExitFullScreen' : 'IconFullScreen'"
          theme="outline"
          size="16"
          :fill="colorTextReverse"
        />
      </div>
    </div>
  </div>
</template>

<script>
import {
  IconPlayWhite,
  IconPauseWhite,
  IconMute,
  IconVolume,
  IconFullScreen,
  IconExitFullScreen
} from '@klook/klook-icons'
import { $colorTextReverse } from '../../utils/design-token-esm'
import Slider from '../slider'
import { isMobile } from '../../utils/bom';
import Loading from '../loading';
import crashedImg from './crashed.svg'

export default {
  name: "VideoItem",
  components: {
    IconPlayWhite,
    IconPauseWhite,
    IconMute,
    IconFullScreen,
    IconExitFullScreen,
    Slider,
    IconVolume,
    Loading
  },
  props: {
    item: {
      type: Object,
      required: true
    },
    active: Boolean
  },
  data() {
    return {
      status: -1, // 0: Pause, 1: Playing, 2: loading 3: crashed
      timeStart: 0,
      timeLeft: 0,
      muted: true,
      currentVolume: 40,
      fullscreen: false,
      videoControlsStyle: { opacity: 1, left: 0, right: 0, bottom: 0 },
      videoSrc: '',
      crashedSrc: crashedImg,
      hiddenControls: false,

      showVolumeSlider: true
    }
  },
  computed: {
    colorTextReverse() {
      return $colorTextReverse
    },
    isMobile() {
      return isMobile
    },
    poster() {
      if (/res\.klook\.com/.test(this.item.original)) {
        return this.item.original.replace('/upload/', '/upload/so_1/').replace(/\.[^/.]+$/, '.jpg')
      }
      return this.item.thumnail
    },
    isNormal() {
      return this.active && (this.status === 0 || this.status === 1)
    },
    isLoading() {
      return this.status === -1
    },
    isCrashed() {
      return this.status === 3
    }
  },
  methods: {
    formatTime(seconds) {
      const mins = Math.floor(seconds / 60)
      const secs = Math.floor(seconds % 60)
      const formattedMins = mins < 10 ? `0${mins}` : mins
      const formattedSecs = secs < 10 ? `0${secs}` : secs
      return `${formattedMins}:${formattedSecs}`
    },
    videoPause() {
      const videoEl = this.$refs.video
      if (!videoEl || this.status === 0) {
        return
      }
      videoEl.pause()
      this.status = 0
      this.hiddenControls = false
    },
    videoPlay() {
      const videoEl = this.$refs.video
      if (!videoEl || this.status === 1) {
        return
      }
      videoEl.play()
      this.status = 1
    },
    onPlayClick() {
      this.status === 1 ? this.videoPause() : this.videoPlay()
    },
    onMuteClick() {
      const videoEl = this.$refs.video
      if (!videoEl) {
        return
      }
      this.muted = !this.muted

      if (this.muted) {
        this.showVolumeSlider = false
      } else {
        this.showVolumeSlider = true
      }
    },
    onProgressInput(value) {
      if (this.timeStart === value) {
        return
      }
      if (this.$refs.slider?.dragging) {
        this.videoPause()
      }
      const videoEl = this.$refs.video
      if (videoEl) {
        this.timeStart = value
        videoEl.currentTime = value
      }
    },
    onVideoEvent(e) {
      const videoEl = this.$refs.video
      if (!videoEl || !this.videoSrc) {
        return
      }
      switch (e.type) {
        case 'loadedmetadata':
          this.status = 0
          this.timeLeft = Math.floor(videoEl.duration)
          break
        case 'ended':
          this.status = 0
          this.hiddenControls = false
          break
        case 'timeupdate':
          !this.$refs.slider?.dragging && (this.timeStart = Math.floor(videoEl.currentTime))
          break
        case 'fullscreenchange':
          this.fullscreen = document.fullscreenElement
          break
        case 'play':
          this.status = 1
          setTimeout(() => {
            this.status === 1 && (this.hiddenControls = true)
          }, 1000)
          break
        case 'pause':
          this.status = 0
          this.hiddenControls = false
          break
        case 'error':
          this.status = 3;
          break
      }
    },
    onFulScreenClick() {
      const eventNames = this.fullscreen ? ['exitFullscreen', 'webkitExitFullscreen', 'msExitFullscreen'] : ['requestFullscreen', 'webkitRequestFullscreen', 'msRequestFullscreen']
      const targetEl = this.fullscreen ? document : this.$el
      const cb = eventNames.reduce((acc, name) => acc || targetEl[name], null)

      typeof cb === 'function' && cb.call(targetEl)
    },
    bindEvent(isRemove = false) {
      const videoEl = this.$refs.video
      if (!videoEl) {
        return
      }
      videoEl.controls = false
      const eventName = isRemove ? 'removeEventListener' : 'addEventListener'
      videoEl[eventName]('loadedmetadata', this.onVideoEvent)
      videoEl[eventName]('loadstart', this.onVideoEvent)
      videoEl[eventName]('error', this.onVideoEvent)
      videoEl[eventName]('timeupdate', this.onVideoEvent)
      videoEl[eventName]('ended', this.onVideoEvent)
      videoEl[eventName]('play', this.onVideoEvent)
      videoEl[eventName]('pause', this.onVideoEvent)

      document[eventName]('fullscreenchange', this.onVideoEvent)
    }
  },
  mounted() {
    this.bindEvent()
  },
  beforeDestroy() {
    this.bindEvent(true)
  },
  watch: {
    active: {
      handler(newValue) {
        if (newValue && !this.videoSrc) {
          this.videoSrc = this.item.original
        }

        if (newValue && this.status === 0) {
          setTimeout(this.videoPlay, 1000)
        }

        if (!newValue && this.status === 1) {
          this.videoPause()
        }
      },
      immediate: true
    },
    currentVolume(newValue) {
      const videoEl = this.$refs.video
      videoEl && (videoEl.volume = newValue / 100)
      if (this.muted) {
        this.muted = false
      }
    }
  }
}
</script>