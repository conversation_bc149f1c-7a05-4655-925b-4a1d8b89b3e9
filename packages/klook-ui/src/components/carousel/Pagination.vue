<template>
  <div :style="holderStyle">
    <div v-for="(i, dot) in dots" :key="dot" :style="getDotsStyle(dot)">
      <div :style="getDotStyle(dot)" @click="$emit('onChange',i)"></div>
    </div>
  </div>  
</template>

<script>
const range = (start, end) => {
  if ((start === 0 && end === 0) || end - start < 0) return [0];
  return Array(end - start + 1)
    .fill()
    .map((_, idx) => start + idx);
};
export default {
  name: 'carousel-dots',
  props: {
    total: {
      type: Number,
      required: true,
    },
    current: {
      type: Number,
      required: true,
    },
    size: {
      type: Number,
      default: 12,
    },
    margin: {
      type: Number,
      default: 3,
    },
    padding: {
      type: Number,
      default: 0,
    },
    visible: {
      type: Number,
      default: 3,
    },
    onChange: {
      type: Function,
      default: () => {},
    },
  },
  data() {
    return {
      translate: 0,
      visibleDots: [],
      mediumDots: [],
      bigDots: [],
      isLooping: false,
      dots: range(0, this.total - 1),
    };
  },
  computed: {
    _visible() {
      let visible = this.visible;
      if (this.total <= 3) visible = this.total;
      if (visible > 3 && visible !== this.total && this.visible % 2 === 0) {
        throw new Error("'visible' cannot be an even number");
      }
      if (visible > this.total) {
        throw new Error("'visible' cannot be more than 'total'");
      }
      return visible;
    },
    dotFullWidth() {
      return this.size + this.margin * 2;
    },
    centerIndex() {
      return Math.floor(this._visible / 2);
    },
    holderStyle() {
      return {
        display: 'flex',
        alignItems: 'center',
        overflow: 'hidden',
        transition: 'all 0.5s ease',
        height: this.size + 'px',
        paddingLeft: this.padding + 'px',
        paddingRight: this.padding + 'px',
        zIndex: 1,
        width: this._visible * this.dotFullWidth + this.padding * 2 + 'px',
      };
    },
  },
  watch: {
    current: {
      immediate: true,
      handler(newVal, oldVal) {
        this.updateDots();
        if ((oldVal === 0 && newVal === this.total - 1) || (oldVal === this.total - 1 && newVal === 0)) {
          this.isLooping = true;
          setTimeout(() => (this.isLooping = false), 300);
        }
      },
    },
  },
  created() {
    this.updateDots();
  },
  methods: {
    updateDots() {
      const centerIndex = this.centerIndex;
      if (this.total <= 5) {
        this.translate = 0;
        this.visibleDots = range(0, this.total - 1);
        this.mediumDots = range(0, this.total - 1);
        return;
      }

      if (this.current < centerIndex) {
        this.translate = 0;
        this.visibleDots = range(0, this._visible - 1);
      } else if (this.total - this._visible / 2 < this.current) {
        this.translate = (this.total - this._visible) * this.dotFullWidth;
        this.visibleDots = range(this.total - this._visible, this.total - 1);
      } else {
        this.translate = (this.current - centerIndex) * this.dotFullWidth;
        this.visibleDots = range(this.current - centerIndex, this.current + centerIndex);
      }

      if (this.current < this._visible / 2) {
        this.mediumDots = range(0, centerIndex + centerIndex - 1);
        this.bigDots = range(0, centerIndex);
      } else if (this.total - this._visible / 2 - 1 < this.current) {
        this.mediumDots = range(this.total - centerIndex - centerIndex, this.total - 1);
        this.bigDots = range(Math.floor(this.total - this._visible / 2), this.total - 1);
      } else {
        this.mediumDots = range(this.current - 2, this.current + 2);
        this.bigDots = range(this.current - 1, this.current + 1);
      }
    },
    getDotStyle(dot) {
      return {
        width: '100%',
        height: '100%',
        borderRadius: '50%',
        backgroundColor: 'rgba(255, 255, 255, 0.5)',
        flexShrink: 0,
        transition: 'transform 0.5s ease, background 0.3s ease-in-out, opacity 0.3s ease-in-out',
        ...(this.isLooping && { opacity: 1, transform: 'scale(1)' }),
        ...(this.isActive(dot) && { backgroundColor: 'white' }),
        ...(this.isVisible(dot) && { opacity: 1 }),
        ...(this.isSmall(dot) && { transform: 'scale(0.6667)' }),
        ...(this.isMedium(dot) && { transform: 'scale(0.8333)' }),
        ...(this.isBig(dot) && { transform: 'scale(1)' }),
      };
    },
    getDotsStyle(dot) {
      return {
        cursor: 'pointer',
        flexShrink: 0,
        transition: 'all 0.5s ease',
        width: this.size + 'px',
        height: this.size + 'px',
        marginRight: this.margin + 'px',
        marginLeft: this.margin + 'px',
        transform: `translateX(-${this.translate}px)`,
      };
    },
    isActive(dot) {
      return this.current === dot;
    },
    isVisible(dot) {
      return this.visibleDots.includes(dot);
    },
    isSmall(dot) {
      return !this.mediumDots.includes(dot);
    },
    isMedium(dot) {
      return this.mediumDots.includes(dot);
    },
    isBig(dot) {
      return this.bigDots.includes(dot);
    },
  },
};
</script>

<style scoped>
/* Add any additional styles here */
</style>
