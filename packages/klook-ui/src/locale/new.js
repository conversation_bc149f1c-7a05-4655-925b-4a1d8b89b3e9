const SeeFullImage = [
  { locale: "en-US", content: "See full image" },
  { locale: "zh-CN", content: "查看全图" },
  { locale: "zh-TW", content: "查看全圖" },
  { locale: "zh-HK", content: "查看全圖" },
  { locale: "ja-JP", content: "画像全体を表示" },
  { locale: "ko-KR", content: "전체 이미지 보기" },
  { locale: "th-TH", content: "ดูภาพขนาดใหญ่" },
  { locale: "id-ID", content: "Lihat gambar penuh" },
  { locale: "vi-VN", content: "Xem toàn ảnh" },
  { locale: "fr-FR", content: "Voir l’image complète" },
  { locale: "de-DE", content: "Vollständiges Bild anzeigen" },
  { locale: "it-IT", content: "Vedi l'immagine completa" },
  { locale: "es-ES", content: "Ver imagen completa" },
  { locale: "ru-RU", content: "Смотреть полностью" },
  { locale: "ms-MY", content: "Lihat imej penuh" },
];
const EnlargeImage = [
  { locale: "en-US", content: "Enlarge image" },
  { locale: "zh-CN", content: "放大图片" },
  { locale: "zh-TW", content: "放大圖片" },
  { locale: "zh-HK", content: "放大圖像" },
  { locale: "ja-JP", content: "画像を拡大" },
  { locale: "ko-KR", content: "이미지 확대하기" },
  { locale: "th-TH", content: "ขยายภาพ" },
  { locale: "id-ID", content: "Perbesar gambar" },
  { locale: "vi-VN", content: "Phóng to ảnh" },
  { locale: "fr-FR", content: "Agrandir l’image" },
  { locale: "de-DE", content: "Bild vergrößern" },
  { locale: "it-IT", content: "Ingrandisci immagine" },
  { locale: "es-ES", content: "Agrandar imagen" },
  { locale: "ru-RU", content: "Увеличить" },
  { locale: "ms-MY", content: "Besarkan imej" },
];
function getLanguage(input) {
  const languageMap = {
    "ar-SA": "en-US",
    "en-CA": "en-US",
    "en-IN": "en-US",
    "en-PH": "en-US",
    en: "en-US",
    id: "id-ID",
    ko: "ko-KR",
    th: "th-TH",
    "zh-HK": "zh-HK",
    de: "de-DE",
    "en-GB": "en-US",
    "en-MY": "en-US",
    "en-SG": "en-US",
    es: "es-ES",
    it: "it-IT",
    "ms-MY": "ms-MY",
    vi: "vi-VN",
    "zh-TW": "zh-TW",
    "en-AU": "en-US",
    "en-HK": "en-US",
    "en-NZ": "en-US",
    "en-US": "en-US",
    fr: "fr-FR",
    ja: "ja-JP",
    ru: "ru-RU",
    "zh-CN": "zh-CN",
  };

  return languageMap[input] || "en-US";
}
const fs = require("fs");
const files = fs.readdirSync("./lang");
console.log(files);
files.forEach((file) => {
  const lang = require(`./lang/${file}`);
  lang.imageViewer = lang.imageViewer || {};
  const code = getLanguage(file.split(".")[0]);
  console.log(code);
  lang.imageViewer["SeeFullImage"] = SeeFullImage.find(
    (item) => item.locale === code
  ).content;
  lang.imageViewer["EnlargeImage"] = EnlargeImage.find(
    (item) => item.locale === code
  ).content;
  fs.writeFileSync(`./lang/${file}`, JSON.stringify(lang, null, 2));
});
