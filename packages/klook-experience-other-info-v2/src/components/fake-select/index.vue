<template>
  <div class="fake-select-box">
    <div :class="['fake-select-box__body', platform]">
      <div class="value-box">
        <span :class="[!value && 'placeholder-style']">
          {{ value ? value : placeholderValue }}
        </span>
      </div>
      <klk-icon
        v-if="suffix"
        type="icon_navigation_chevron_down_xs"
        :size="16"
        class="arrow-down"
      ></klk-icon>
    </div>
  </div>
</template>

<script>

import KlkIcon from "@klook/klook-ui/lib/icon";
import "@klook/klook-ui/lib/styles/components/icon.scss";

export default {
  name: 'FakeSelect',
  components: {
    KlkIcon
  },
  props: {
    platform: { type: String, default: 'mobile' },
    value: { type: String, default: '' },
    placeholder: { type: String, default: '' },
    suffix: { type: Boolean, default: true }
  },
  computed: {
    placeholderValue() {
      return this.placeholder ? this.placeholder : this.$t('global.select.palceholder')
    }
  }
}

</script>

<style lang="scss" scoped>
$prefix: '.fake-select-box';

#{$prefix} {
  &__body {
    min-height: 44px;
    padding: 0 12px;
    color: #666;
    border-radius: 6px;
    border: 1px solid $color-neutral-300;
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;

    .value-box {
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 100%;
      white-space: nowrap;

      span {
        color: $color-neutral-900;

        &.placeholder-style {
          color: $color-text-placeholder;
        }
      }
    }

    .arrow-down {
      color: $color-neutral-500;
      margin-left: 6px;
      transform: rotate(-90deg);
      flex: none;
    }

    &.mobile {
      border: none;
      border-radius: 0;
      padding: 0;
      border-bottom: 1px solid $color-neutral-300;
    }
  }
}
</style>
