<template>
  <div class="dynamic-form-title" :class="{ 'is-no-name': !data.name }">
    <span class="dynamic-form-title-text" v-html="getTitleHtml(data.name, true)"></span>
    <span v-if="data.style.required" class="isRequired">*</span>
    <k-poptip v-if="data.hover" placement="top-start" :content="data.hover">
      <k-icon type="icon_tips_tips" :size="16" class="dynamic-form-title-icon" />
    </k-poptip>
  </div>
</template>

<script>
import Poptip from '@klook/klook-ui/lib/poptip'
import Icon from '@klook/klook-ui/lib/icon'
import '@klook/klook-ui/lib/styles/components/icon.scss'
import '@klook/klook-ui/lib/styles/components/poptip.scss'
import { getTitleHtml } from '@src/utils'

export default {
  name: 'DynamicFormTitle',
  components: {
    KPoptip: Poptip,
    KIcon: Icon,
  },
  props: {
    data: {
      type: Object,
      default: () => ({}),
    }
  },
  methods: {
    getTitleHtml
  }
}
</script>

<style lang="scss" scoped>
.dynamic-form-title {
  margin-bottom: 4px;
  font-size: $fontSize-body-s;
  line-height: 20px;
  color: $color-text-primary;

  &-text ::v-deep a {
    cursor: pointer;
    color: $color-text-link;
    text-decoration: none;
  }

  &.is-no-name {
    height: 20px;
  }

  .isRequired {
    color: $color-brand-primary;
  }

  &-icon {
    font-size: $fontSize-body-m;
    position: relative;
    vertical-align: middle;
  }
}
</style>
