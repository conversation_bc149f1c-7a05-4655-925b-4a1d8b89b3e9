<template>
  <k-form-item
    class="dynamic-form-item"
    ref="formItem"
    :rules="genRules()"
    :prop="`${prop}.${valueType}`"
    v-bind="useEditableTravellerForm ? {
      'data-spm-module': spm,
      'data-spm-virtual-item': '__virtual'
    } : {
      'data-spm-item': spm
    }"
    @validate="handleValidate"
  >
    <slot />
    <k-alert
      v-if="successHint && isValid"
      type="warning"
      class="success-hint"
      @click.stop
    >
      {{ successHint }}
    </k-alert>
  </k-form-item>
</template>

<script>
import { FormItem } from '@klook/klook-ui/lib/form'
import Alert from '@klook/klook-ui/lib/alert'
import '@klook/klook-ui/lib/styles/components/form.scss'
import '@klook/klook-ui/lib/styles/components/alert.scss'
import { formRules } from '@src/utils/form-rules'

export default {
  name: 'DynamicFormItem',
  components: {
    KFormItem: FormItem,
    KAlert: Alert,
  },
  props: {
    prop: {
      type: String,
      default: '',
    },
    data: {
      type: Object,
      default: () => ({}),
    },
    // 值在哪个字段里面，默认content，也有可能是operation
    valueType: {
      type: String,
      default: 'content',
    },
    // 适用于埋点
    spmAmount: {
      type: Number,
      default: null,
    },
    useEditableTravellerForm: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    spm() {
      const { data, valueType, spmAmount, useEditableTravellerForm } = this
      if (!useEditableTravellerForm) {
        const operationLength = data.operation ? data.operation.length : 0
        const fillIn = valueType === 'operation' ? operationLength : data[valueType]
        const amount = spmAmount !== null ? spmAmount : valueType === 'operation' ? operationLength : 0
        const { sectionType, action } = this.$attrs
        return `DetailInfoTab?ext=${JSON.stringify({
          FillIn: Boolean(fillIn),
          TabName: data.field_key,
          Amount: amount,
          PageType: sectionType,
          Clicktype: action
        })}`
      } else {
        return `ContactInfo_List?ext=${JSON.stringify({
          FillIn: Boolean(this.data?.content),
          TabName: data.field_key,
        })}`
      }
    },
    successHint() {
      return this.data?.style?.success_hint || ''
    },
  },
  data() {
    return {
      isValid: false
    }
  },
  methods: {
    genRules() {
      return formRules.call(this)
    },
    validate() {
      this.$refs?.formItem?.validate()
    },
    handleValidate(_, isValid) {
      this.isValid = isValid
      this.$emit('validate', isValid)
    },
  },
  mounted() {
    if (this.data?.content) {
      this.validate()
    }
  }
}
</script>

<style lang="scss" scoped>
.success-hint {
  margin-top: 8px;
}
</style>
