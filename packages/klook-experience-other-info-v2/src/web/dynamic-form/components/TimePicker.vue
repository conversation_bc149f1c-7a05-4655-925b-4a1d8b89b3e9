<template>
  <div
    class="dynamic-form-time-picker"
    :style="{ width: componentWidth }"
  >
    <k-select
      v-model="hour"
      class="time-select"
      :max-height="300"
      :disabled="disabled"
      @change="handleChange"
    >
      <k-option v-for="item in hourList" :key="item" :value="item" :label="item" />
    </k-select>
    <span class="time-comma">:</span>
    <k-select
      v-model="minute"
      class="time-select"
      :max-height="300"
      :disabled="disabled"
      @change="handleChange"
    >
      <k-option v-for="item in minuteList" :key="item" :value="item" :label="item" />
    </k-select>
  </div>
</template>

<script>
import { Select, Option } from '@klook/klook-ui/lib/select'
import '@klook/klook-ui/lib/styles/components/select.scss'

export default {
  name: 'DynamicFormTimePicker',
  inject: ['componentWidth'],
  components: {
    KSelect: Select,
    KOption: Option,
  },
  props: {
    value: {
      type: String,
      default: '',
    },
    data: {
      type: Object,
      default: () => ({}),
    },
    styleType: {
      type: String,
      default: 'outlined',
    },
    disabled: {
      type: <PERSON>olean,
      default: false,
    },
  },
  computed: {
    hourList() {
      let list = []
      for (let i = 0; i < 24; i++) {
        list.push(i < 10 ? `0${i}` : `${i}`)
      }
      return list
    },
    minuteList() {
      let list = []
      for (let i = 0; i < 12; i++) {
        list.push(i < 2 ? `0${i * 5}` : `${i * 5}`)
      }
      return list
    },
    hour: {
      get() {
        const timeArr = this.value ? this.value.split(':') : []
        return timeArr[0] || ''
      },
      set(val) {
        this.$emit('input', `${val}:${this.minute}`)
      },
    },
    minute: {
      get() {
        const timeArr = this.value ? this.value.split(':') : []
        return timeArr[1] || ''
      },
      set(val) {
        this.$emit('input', `${this.hour}:${val}`)
      },
    },
  },
  methods: {
    handleChange() {
      this.$emit('change', `${this.hour}:${this.minute}`)
    },
  },
}
</script>
<style lang="scss" scoped>
.dynamic-form-time-picker {
  .time-comma {
    font-size: $fontSize-body-m;
  }

  .time-select {
    width: calc(50% - 8px);
  }
}
</style>

