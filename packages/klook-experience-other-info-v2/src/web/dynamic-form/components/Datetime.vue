<template>
  <div class="dynamic-form-datetime">
    <DynamicFormDate
      v-model="date"
      :data="data"
      :disabled="disabled"
      :should-check-date-valid="shouldCheckDateValid"
      :style="{ width: componentWidth }"
      @change="val => $emit('change', val)"
    />
    <DynamicFormTimePicker
      v-model="time"
      :data="data"
      :disabled="disabled"
      :should-check-date-valid="shouldCheckDateValid"
      :style="{ width: componentWidth }"
      @change="val => $emit('change', val)"
    />
  </div>
</template>

<script>
import DynamicFormDate from './Date.vue'
import DynamicFormTimePicker from './TimePicker.vue'

export default {
  name: 'DynamicFormDatetime',
  inject: ['wrapperWidth', 'componentWidth'],
  components: {
    DynamicFormDate,
    DynamicFormTimePicker,
  },
  props: {
    value: {
      type: String,
      default: '',
    },
    data: {
      type: Object,
      default: () => ({}),
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    shouldCheckDateValid: {
      type: Boolean,
      default: true,
    },
  },
  computed: {
    date: {
      get() {
        const timeArr = this.value ? this.value.split(' ') : []
        return timeArr[0] || ''
      },
      set(val) {
        this.$emit('input', `${val} ${this.time}`)
      },
    },
    time: {
      get() {
        const timeArr = this.value ? this.value.split(' ') : []
        return timeArr[1] || ''
      },
      set(val) {
        this.$emit('input', `${this.date} ${val}`)
      },
    },
  },
}
</script>

<style lang="scss" scoped>
.dynamic-form-datetime {
  display: flex;
  justify-content: space-between;
}
</style>
