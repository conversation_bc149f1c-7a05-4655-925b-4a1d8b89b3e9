<template>
  <div class="dynamic-form-mobile">
    <DynamicFormItem
      ref="selectFormItem"
      :data="data"
      :prop="prop"
      value-type="operation"
      v-bind="$attrs"
      :style="{ width: componentWidth }"
      @validate="handleValidate"
    >
      <DynamicFormSelect
        v-model="data.operation"
        :data="data"
        :disabled="disabled"
        label-type="areaCode"
        :style="{ width: componentWidth }"
        @change="handleSelectChange"
      />
    </DynamicFormItem>

    <DynamicFormItem
      ref="inputFormItem"
      :data="data"
      :prop="prop"
      value-type="content"
      v-bind="$attrs"
      :style="{ width: componentWidth }"
      class="dynamic-form-mobile-input"
      @validate="handleValidate"
    >
      <DynamicFormInput
        v-model="data.content"
        :data="data"
        :disabled="disabled"
        :style="{ width: componentWidth }"
        @change="handleInputChange"
        @input="val => $emit('input', val)"
      />
      <p v-if="verified" class="dynamic-form-mobile-verified">
        <CheckedTipsSvg class="dynamic-form-mobile-verified-icon" size="16px" />

        {{ $t('71766') }}
      </p>
    </DynamicFormItem>
  </div>
</template>

<script>
import CheckedTipsSvg from '@src/imgs/checked-tips.svg'
import DynamicFormItem from '../DynamicFormItem.vue'
import DynamicFormInput from './Input.vue'
import DynamicFormSelect from './Select.vue'

export default {
  name: 'DynamicFormMobile',
  inject: ['componentWidth'],
  components: {
    DynamicFormItem,
    DynamicFormInput,
    DynamicFormSelect,
    CheckedTipsSvg,
  },
  props: {
    // 由于有dynamic-form-item，所以需要传prop
    prop: {
      type: String,
      default: '',
    },
    data: {
      type: Object,
      default: () => ({}),
    },
    styleType: {
      type: String,
      default: 'outlined',
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    originalPhone: {
      type: String,
      default: null,
    },
  },
  computed: {
    currentPhone() {
      const content = this.data.content || ''
      const operation = this.data.operation || []
      return content && operation.length > 0 ? `${operation[0].field_key}-${content}` : null
    },
    verified() {
      const { verify_state } = this.data.style || {}
      return !!verify_state && this.originalPhone && this.originalPhone === this.currentPhone
    }
  },
  methods: {
    handleSelectChange(val) {
      this.$emit('change', val)
    },
    handleInputChange(val) {
      this.$emit('change', val)
    },
    handleValidate(...args) {
      this.$emit('validate', ...args)
    },
  }
}
</script>

<style lang="scss" scoped>
.dynamic-form-mobile {
  display: flex;
  justify-content: space-between;

  &-input {
    position: relative;
  }

  &-verified {
    margin: 0;
    position: absolute;
    top: 50%;
    right: 12px;
    display: flex;
    align-items: center;
    font-size: $fontSize-body-s;
    line-height: 17px;
    color: $color-success;
    transform: translateY(-50%);

    &-verified-icon {
      flex: none;
      margin-right: 6px;
    }
  }
}
</style>
