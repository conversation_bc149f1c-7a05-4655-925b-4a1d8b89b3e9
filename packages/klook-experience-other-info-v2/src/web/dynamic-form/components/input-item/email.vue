<template>
  <div class="dynamic-form-input">
    <klk-guest-contact-email
      v-model="inputVal"
      :type="type"
      :placeholder="data.hint"
      :disabled="disabled"
      :min="min"
      :max="max"
      :autocomplete="autocomplete"
      :name="name"
      :clearable="clearable"
      :email-suffix-list="emailSuffixList"
      style="width: 100%"
      @change="val => $emit('change', val)"
    >
    </klk-guest-contact-email>
  </div>
</template>

<script>
import { KlkGuestContactEmail } from '@klook/guest-contact-info'
import '@klook/guest-contact-info/dist/esm/index.css'

export default {
  name: 'DynamicFormEmailSuffix',
  components: {
    KlkGuestContactEmail,
  },
  props: {
    value: {
      type: String,
      default: '',
    },
    data: {
      type: Object,
      default: () => ({}),
    },
    styleType: {
      type: String,
      default: 'outlined',
    },
    disabled: {
      type: <PERSON><PERSON><PERSON>,
      default: false,
    },
  },
  computed: {
    type() {
      const { type } = this.data.style.match_rule || {}
      return type === 2 ? 'number' : 'text'
    },
    min() {
      const { type, min_len, min_num } = this.data.style.match_rule || {}
      const minLen = [0, 3].includes(type) ? min_len : 0
      return type === 2 ? min_num : minLen
    },
    max() {
      const { type, max_len, max_num } = this.data.style.match_rule || {}
      const maxLen = [0, 3].includes(type) ? max_len : 10000
      return type === 2 ? max_num : maxLen
    },
    autocomplete() {
      return this.data.input_info?.input_autocomplete || 'off'
    },
    name() {
      return this.data.input_info?.input_name || ''
    },
    clearable() {
      return this.data.style?.clearable || false
    },
    emailSuffixList() {
      return this.data.style?.email_suffix_list || []
    },
    inputVal: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      },
    },
  },
}
</script>

<style lang="scss" scoped></style>
