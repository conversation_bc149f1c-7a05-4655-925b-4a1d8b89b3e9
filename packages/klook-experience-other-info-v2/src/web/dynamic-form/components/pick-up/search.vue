<template>
  <div :class="['search-keywords', isPopReversed && 'is-pop-reversed']">
    <div class="search-keywords-wrap">
      <k-input
        v-model.trim="keywords"
        clearable
        prepend-icon="icon_edit_search_s"
        ref="inputRef"
        :placeholder="$t('104032')"
        :class="[
          'search-keywords-input',
          isWarningError && 'is-error'
        ]"
        @input="handleInput"
        @focus="handleFocus"
        @clear="handleClear"
      />

      <div v-show="popVisible" class="search-keywords-pop">
        <div v-if="showLoading" class="search-keywords-loading">
          <k-loading />
        </div>

        <div v-else class="search-keywords-content">
          <div v-show="isNotMatch" class="search-keywords-none">
            <div class="search-keywords-none-text">
              {{ isSearchNone ? $t('104307') : $t('104307') }}
            </div>
            <!-- <div
              v-if="isSearchNone"
              class="search-keywords-none-button"
            >
              <k-button
                type="outlined"
                data-spm-module="LocationSearch_FreeText"
                data-spm-virtual-item="__virtual"
                @click="handleFreeText"
              >
                {{ $t('106269') }}
              </k-button>
            </div> -->
            <template v-if="!isSearchNone && searchResultList.length">
              <k-divider class="search-keywords-none-divider"></k-divider>
              <div
                data-spm-module="LocationSearch_NoResult"
                class="search-keywords-none-title"
              >
                {{ $t('104308') }}
              </div>
            </template>
          </div>
          <div
            v-for="(item, index) in renderList"
            :key="index"
            :class="['search-keywords-item', checkSelected(item) && 'active']"
            :data-spm-module="`LocationSearch_SelectResult?ext=${JSON.stringify({
              InScope: inScopeList[item.data_type] || ''
            })}`"
            data-spm-virtual-item="__virtual"
            @click="handleSelect(item)"
          >
            <div class="search-keywords-item-title" :title="item.title">
              {{ item.title }}
            </div>
            <CustomTag
              v-if="getTags(item).length"
              :list="getTags(item)"
              :line="1"
              class="search-keywords-item-tags"
            />
            <div class="search-keywords-item-desc" :title="item.sub_title">
              {{ item.sub_title }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <PickUpTips
      :text="warningText"
      :is-error="isWarningError"
      style="margin-top: 8px;"
    />
  </div>
</template>

<script>
import Button from '@klook/klook-ui/lib/button'
import Input from '@klook/klook-ui/lib/input'
import Loading from '@klook/klook-ui/lib/loading'
import Divider from '@klook/klook-ui/lib/divider'
import '@klook/klook-ui/lib/styles/components/button.scss'
import '@klook/klook-ui/lib/styles/components/input.scss'
import '@klook/klook-ui/lib/styles/components/loading.scss'
import '@klook/klook-ui/lib/styles/components/divider.scss'
import Tag from "@src/utils/components/tag.vue"
import PickUpTips from './tips.vue'
import PoiSearchMixin from "@src/mixins/poi-search.js";

export default {
  name: 'DynamicFormPickUpSearch',
  mixins: [PoiSearchMixin],
  components: {
    PickUpTips,
    KButton: Button,
    KInput: Input,
    KLoading: Loading,
    KDivider: Divider,
    CustomTag: Tag
  },
  props: {
    selected: {
      type: Object | String,
      default: '',
    },
    isScope: {
      type: Boolean,
      default: false
    },
    locationList: {
      type: Array,
      default: () => ([]),
    },
    modalVisible: {
      type: Boolean,
      default: false
    },
    packageId: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      searchTimer: 0,
      isWarningError: false,
      warningText: '',
      popVisible: false,
      showLoading: false,
      cancelToken: null,
      isBlank: false, // hack: 手动检测是否为空，不需要响应式检测
      inScopeList: ['', 'Available', 'Extra fee', 'Not offered'],
    }
  },
  computed: {
    shouldHidePop() {
      // scope 的情况，如果关键词为空，则不需要打开下拉框
      return this.isScope && !this.keywords
    },
    isNotOffered() {
      const { selected } = this
      // 1:free_pick_up 2:extra_fee_need 3:not offered
      return selected && selected.data_type === 3
    },
    isExtraFee() {
      const { selected } = this
      // 1:free_pick_up 2:extra_fee_need  3:not offered
      return selected && selected.data_type === 2
    },
    // 控制颜色
    isRealWarningError() {
      return this.isBlank ? true : this.isNotOffered
    },
    // 控制文案
    realWarningText() {
      if (this.isBlank) {
        return this.$t('106593')
      }

      if (this.isNotOffered) {
        return this.$t('104310')
      }

      if (this.isExtraFee) {
        return this.$t('106531')
      }

      return ''
    },
    selectedLocation() {
      return this.selected ? this.selected.location : ''
    }
  },
  // watch: {
  //   selected: {
  //     immediate: true,
  //     handler(newVal) {
  //       if (newVal) {
  //         this.keywords = newVal.location_name
  //         this.isBlank = false
  //       }
  //     }
  //   }
  // },
  mounted() {
    document.addEventListener('click', this.handleClickOutside)
  },
  beforeDestroy() {
    document.removeEventListener('click', this.handleClickOutside)
  },
  methods: {
    getTags(data) {
      return data?.detail_data?.tag ?? []
    },
    focus() {
      if (this.$refs.inputRef) {
        this.$refs.inputRef.focus()
      }
    },
    // search 报错 + confirm 报错
    checkValid() {
      this.isBlank = !this.selected

      if (this.isBlank) {
        this.validateSearch()
        this.$toast(this.$t('106593'))
        return
      }

      if (this.isRealWarningError) {
        this.$toast(this.$t('106258'))
        return false
      }

      return true
    },
    validateSearch() {
      // 更新报错状态
      this.isWarningError = this.isRealWarningError
      this.warningText = this.realWarningText
    },
    handleFocus() {
      this.handleSearch()
    },
    cancelSearch() {
      if (this.cancelToken) {
        this.cancelToken.cancel('cancel')
        this.cancelToken = null
      }
    },
    apiError() {
      this.$toast(this.$t('client_server_failure'))

      // scope 的时候需要不阻塞用户
      if (this.isScope) {
        this.searchResultList = []
      }
    },
    handleSearch() {
      if (!this.keywords) {
        if (this.defaultResult.length) {
          this.openPop()
        } else {
          this.closePop()
        }
        this.isNotMatch = false
        return
      }

      // 如果下拉框没有打开，则要显示 loading
      if (!this.popVisible) {
        this.showLoading = true
      }
      this.openPop()
      clearTimeout(this.searchTimer)
      this.searchTimer = setTimeout(this.search, 500)
    },
    async search() {
      const params = {
        input: this.keywords,
        package_id: this.packageId,
        show_meet_up: 1,
        current_google_place_id: this.selected?.google_place_id || ''
      }
      this.isNotMatch = false

      this.searchResultList = await this.getSearchFun(params)
      this.isNotMatch = !this.searchResultList.length && !!this.keywords
      // const list = await this.getSearchFun(params)
      // this.searchResultList = this.setOrder([...list])
      this.showLoading = false
    },
    handleClear() {
      this.keywords = ''
      this.$emit('clear')
    },
    handleInput() {
      // 清空报错状态
      this.isWarningError = false
      this.warningText = ''
      this.handleSearch()
    },
    checkSelected(item) {
      return this.selectedLocation && item.location === this.selectedLocation
    },
    setOrder(data = []) {
      const selectedItem = this.selected
      if (selectedItem) {
        const location = selectedItem.location
        const list = data.reduce((acc, curr) => {
          const currLoaction = curr.detail_data?.location ?? ''
          if (currLoaction === location) {
            acc.unshift(this.initResult(curr))
          } else {
            acc.push(curr)
          }
          return acc
        }, [])

        return list
      }

      return data
    },
    handleClickOutside(e) {
      // 判断用户的点击行为是否在 input 框和弹层上
      // 若不是，则收起弹层
      if (!this.popVisible || !this.$el) {
        return
      }

      const inputDom = this.$el.querySelector('.search-keywords-input')
      const popDom = this.$el.querySelector('.search-keywords-pop')
      if (
        (!inputDom || !inputDom.contains(e.target))
        && (!popDom || !popDom.contains(e.target))
      ) {
        this.cancelSearch()
        this.closePop()
        // 回退 keywords
        // const { selected } = this
        // this.keywords = (selected && selected.location_name) || ''
      }
    },
    handleFreeText() {
      const payload = {
        // 自定义的点都是 extra_fee
        pick_up_type: this.isScope ? 2 : 1,
        map_type: 1,
        data_type: 2,
        location_name: this.keywords,
        supply_api_mapping_key: this.keywords
      }

      this.$emit('update:selected', payload)
      this.$emit('change', payload)
      this.closePop()

      setTimeout(this.validateSearch, 30)
    },
    async handleSelect(item) {

      const { detail_data = null, place_id, is_default } = item || {}

      let detail = null
      if (is_default || detail_data) {
        detail = detail_data
      } else {
        item.loading = true
        detail = await this.getPoiDetail({
          package_id: this.packageId,
          place_id,
        })
        
        this.$set(item, 'detail_data', detail)
        item.loading = false
      }
      const data_type = detail?.data_type ?? -1

      if (data_type === 3) {
        this.$toast(this.$t('161196'))
        return
      }

      this.keywords = item.title

      // 需要从 locationList 里面取值
      const target = (this.locationList || []).find(item => item.location === detail.location)
      const payload = target || { ...detail, pick_up_type: this.isScope ? 2 : 1 }
      this.$emit('update:selected', payload)
      this.$emit('change', payload)
      this.closePop()

      setTimeout(this.validateSearch, 30)
    },
    openPop() {
      this.popVisible = true
    },
    closePop() {
      this.searchResultList = []
      this.popVisible = false
    },
  }
}
</script>

<style lang="scss" scoped>
// 多行省略
@mixin text-ellipsis($line-clamp: 2) {
  display: -webkit-box;
  -webkit-line-clamp: $line-clamp;
  -webkit-box-orient: vertical;
  text-overflow: ellipsis;
  overflow: hidden;
}

.search-keywords {
  &-wrap {
    position: relative;
  }

  &-input {
    width: 400px;
    max-width: 100%;

    &:hover,
    &.klk-input-is-focus {
      ::v-deep .klk-input-inner {
        border-color: $color-brand-primary;
      }
    }

    &.is-error {
      ::v-deep .klk-input-inner {
        border-color: $color-error;
      }
    }
  }

  &-warning {
    margin-top: 8px;
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;
    color: $color-text-secondary;

    &.is-error {
      color: $color-error;
    }

    &-icon {
      margin: 2px 8px 0 0;
      flex-shrink: 0;
    }

    &-text {
      @include font-body-s-regular;
    }
  }

  &-pop {
    padding: 0 8px;
    position: absolute;
    left: 0;
    bottom: -10px;
    width: 100%;
    max-height: 320px;
    border-radius: $radius-l;
    background-color: $color-bg-1;
    box-sizing: border-box;
    border: 1px solid $color-border-normal;
    overflow-y: auto;
    transform: translateY(100%);
    z-index: 500;
    min-height: 40px;
  }

  &-loading {
    min-height: 100px;
  }

  &-content {
    padding: 12px 0;
  }

  &-none {
    padding: 0 12px;

    &-text {
      @include font-body-s-regular;
      margin: 8px 0 8px;
      color: $color-text-secondary;
    }

    &-divider {
      margin: 16px 0;
    }

    &-button {
      margin: 8px 0;
    }

    &-title {
      @include font-body-s-semibold;
      margin: 20px 0 8px;
      color: $color-text-primary;
    }
  }

  &-item {
    padding: 6px 12px;
    border-radius: $radius-m;
    background-color: $color-bg-1;
    cursor: pointer;

    &:hover {
      background-color: $color-bg-2;
    }

    &.active {
      background-color: $color-brand-primary-light-2;
    }

    &-title {
      @include font-body-m-regular;
      @include text-ellipsis(1);
      margin-bottom: 4px;
      color: $color-text-primary;
    }

    &-tags {
      margin-bottom: 4px;
    }

    &-desc {
      @include font-body-s-regular;
      @include text-ellipsis(1);
      color: $color-text-secondary;
    }
  }

  &.is-pop-reversed {
    .search-keywords-pop {
      top: -10px;
      bottom: unset;
      transform: translateY(-100%);
    }
    .search-keywords-content {
      display: flex;
      flex-direction: column-reverse;
    }
    .search-keywords-none {
      display: flex;
      flex-direction: column-reverse;
    }
  }
}
</style>
