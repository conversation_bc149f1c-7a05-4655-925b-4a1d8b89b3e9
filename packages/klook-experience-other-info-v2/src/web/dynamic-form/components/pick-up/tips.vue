<template>
  <div
    v-if="text"
    :class="['pick-up-tips', isError && 'is-error']"
  >
    <k-icon
      size="16"
      type="icon_feedback_warning"
      class="pick-up-tips-icon"
    />
    <span class="pick-up-tips-text">
      {{ text }}
    </span>
  </div>
</template>

<script>
import Icon from '@klook/klook-ui/lib/icon'
import '@klook/klook-ui/lib/styles/components/icon.scss'

export default {
  name: 'DynamicFormPickUpTips',
  components: {
    KIcon: Icon,
  },
  props: {
    text: {
      type: String,
      default: '',
    },
    isError: {
      type: Boolean,
      default: false
    }
  }
}
</script>

<style lang="scss" scoped>
.pick-up-tips {
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
  color: $color-text-secondary;

  &.is-error {
    color: $color-error;
  }

  &-icon {
    margin: 2px 8px 0 0;
    flex-shrink: 0;
  }

  &-text {
    @include font-body-s-regular;
  }
}
</style>
