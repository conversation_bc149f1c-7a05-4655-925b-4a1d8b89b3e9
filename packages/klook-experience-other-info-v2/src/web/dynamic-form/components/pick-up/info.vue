<template>
  <div class="pick-up-info">
    <div class="pick-up-info-title">
      {{ info.title }}
    </div>
    <p class="pick-up-info-sentence">
      {{ info.description }}
    </p>
    <div class="pick-up-info-points">
      <div
        v-for="text in info.point_list"
        :key="text"
        class="pick-up-info-point"
      >
        {{ text }}
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'DynamicFormPickUpInfo',
  props: {
    info: {
      type: Object,
      default: () => ({}),
    },
  },
}
</script>

<style lang="scss" scoped>
// 多行省略
@mixin text-ellipsis($line-clamp: 2) {
  display: -webkit-box;
  -webkit-line-clamp: $line-clamp;
  -webkit-box-orient: vertical;
  text-overflow: ellipsis;
  overflow: hidden;
}

.pick-up-info {
  margin-top: 14px;

  &-title {
    @include font-body-m-bold;
    color: $color-text-primary;
  }

  &-sentence {
    @include font-body-s-regular;
    margin-top: 8px;
    color: $color-text-primary;
  }

  &-point {
    @include font-body-s-regular;
    @include text-ellipsis(1);
    color: $color-text-primary;

    &:before {
      content: '';
      display: inline-block;
      width: 6px;
      height: 6px;
      margin-right: 10px;
      border-radius: 50%;
      background: $color-neutral-800;
      vertical-align: 2px;
    }
  }
}
</style>
