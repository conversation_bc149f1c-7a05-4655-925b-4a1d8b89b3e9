<template>
  <component
    :is="componentName"
    :data="data"
    v-bind="$attrs"
    v-on="$listeners"
  />
</template>

<script>
import SelectNormal from './Select.vue'
import SelectMapPoint from './SelectMapPoint.vue'
import { isMapPointSelect } from '@src/utils'

/**
 * 作为 select 的适配器：
 * option_all_type 为 1、2 的是普通 select
 * option_all_type 为 3 的是地图点 select
 */
export default {
  name: 'DynamicFormSelect',
  components: {
    SelectNormal,
    SelectMapPoint
  },
  props: {
    data: {
      type: Object,
      default: () => ({}),
    },
  },
  computed: {
    componentName() {
      if (this.data && this.data.style && isMapPointSelect(this.data.style)) {
        return 'SelectMapPoint'
      }

      return 'SelectNormal'
    },
  },
}
</script>

<style lang="scss">
.dynamic-form-select {
  h1 {
    margin: 0;
  }
}
</style>
