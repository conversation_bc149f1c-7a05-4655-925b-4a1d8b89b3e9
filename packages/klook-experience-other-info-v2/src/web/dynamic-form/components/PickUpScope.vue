<template>
  <div class="dynamic-form-pick-up-select">
    <div class="input-wrap">
      <div class="input-item">
        <FakeSelect
          platform="desktop"
          :value="inputVal"
          :placeholder="$t('104032')"
          :suffix="!autoSelect"
          @click.native="handleFocus"
        />
      </div>
      <div
        v-for="(item, index) in autoGenForm"
        :key="index"
        class="input-item"
      >
        <div class="input-label">{{ item.title }}</div>
        <FakeSelect
          platform="desktop"
          :suffix="false" 
          :value="item.value"
          @click.native="handleFocus"
        />
      </div>
    </div>

    <PickUpTips
      :text="tipsText"
      style="margin-top: 8px;"
    />

    <k-modal
      v-if="isModalVisible"
      size="normal"
      width="900"
      closable
      scrollable
      append-body
      :open.sync="isModalVisible"
      :title="$t('167237')"
      :ok-label="$t('104309')"
      :show-cancel-button="false"
      class="dynamic-form-pick-up-select-modal"
      @on-confirm="handleConfirm"
      @on-close="handleClose"
    >
      <div
        ref="mapModal"
        :data-spm-page="`PickUpMap?trg=manual&ext=${encodeURIComponent(JSON.stringify({
          Type: 'pick_up_scope',
          Source: 'CheckoutPage'
        }))}`"
      >
        <PickUpInfo
          v-if="info"
          :info="info"
          style="margin-bottom: 12px;"
        />
        <PickUpSearch
          ref="searchRef"
          :selected.sync="selected"
          :is-scope="true"
          :modal-visible="isModalVisible"
          :package-id="packageId"
          :location-list="jsonDatas"
          :map-data="jsonDatas"
          style="margin-bottom: 16px;"
          @change="handleSearchChange"
          @clear="handleClear"
        />

        <!-- 设置最小高度，防止全屏的时候页面抖动 -->
        <div class="pickup-wrapper">
          <PickUpMap
            :selected.sync="selected"
            :is-scope="true"
            :location-list="jsonDatas"
            :extra-point="extraPoint"
          />
        </div>
      </div>
    </k-modal>
  </div>
</template>

<script>
import Input from '@klook/klook-ui/lib/input'
import Modal from '@klook/klook-ui/lib/modal'
import '@klook/klook-ui/lib/styles/components/input.scss'
import '@klook/klook-ui/lib/styles/components/modal.scss'
import inhouseMixin from '@src/utils/mixins/inhouse'
import PickUpInfo from './pick-up/info.vue'
import PickUpMap from './pick-up/map.vue'
import PickUpSearch from './pick-up/search.vue'
import PickUpTips from './pick-up/tips.vue'

import PickupScopeMixin from '@src/mixins/pick-up-scope.js'

export default {
  name: 'DynamicFormPickUpScope',
  mixins: [inhouseMixin, PickupScopeMixin],
  components: {
    PickUpInfo,
    PickUpMap,
    PickUpSearch,
    PickUpTips,
    KInput: Input,
    KModal: Modal
  },
  inject: {
    $formItem: {
      default: null,
    }
  },
  props: {
    value: {
      type: Array,
      default: () => [],
    },
    disabled: {
      type: Boolean,
      default: false,
    }
  },
  data() {
    return {
      isModalVisible: false,
      selected: '',
      extraPoint: null, // 搜索的点
    }
  },
  methods: {
    handleSearchChange(val) {
      this.extraPoint = val
    },
    handleClear() {
      // 如果一样的话，就取消选中
      if (this.selected && this.extraPoint && this.selected.location === this.extraPoint.location) {
        this.selected = null
      }
      this.extraPoint = null
    },
    handleFocus() {
      if (this.disabled || this.autoSelect) {
        return
      }
      // hack: 发个埋点再打开
      setTimeout(() => {
        this.selected = this.formItemValue
        this.extraPoint = this.formItemValue || null
        this.isModalVisible = true

        this.$nextTick(function () {
          this.sendDataSpm('pageview', this.$refs.mapModal, { force: true })
        })
      })

      // 如果没有选择就 focus
      // if (!this.selected) {
      //   setTimeout(() => {
      //     this.$refs.searchRef && this.$refs.searchRef.focus()
      //   }, 400)
      // }
    },
    handleClose() {
      this.$nextTick(function () {
        this.sendDataSpm('custom', 'body', { spm: 'CloseMap' })
      })
    },
    handleConfirm() {
      if (this.$refs.searchRef && this.$refs.searchRef.checkValid()) {
        this.$emit('input', JSON.stringify(this.selected))
        this.$emit('change', JSON.stringify(this.selected))
        this.isModalVisible = false

        // fix: 这里需要再校验一次
        if (this.$formItem) {
          this.$formItem.$emit('change')
        }
      }
    },
  },
  mounted() {
    if (this.autoSelect) {
      this.selected = this.jsonDatas[0]
      this.$emit('input', JSON.stringify(this.selected))
      this.$emit('change', JSON.stringify(this.selected))
    }
  }
}
</script>

<style lang="scss" scoped>
.pickup-wrapper {
  margin-bottom: 16px;
  min-height: 300px;
}

.input-wrap {
  background: $color-bg-1;
  display: flex;
  flex-wrap: wrap;

  .input-item {
    width: calc(50% - 12px);
    margin-right: 24px;
    margin-bottom: 48px;
    position: relative;

    .input-label {
      position: absolute;
      left: 0;
      top: -24px;
      z-index: 1;
      margin-bottom: 4px;
      font-size: $fontSize-body-s;
      line-height: 20px;
      color: $color-text-primary;
    }

    &:nth-of-type(2n) {
      margin-right: 0;
    }

    &:nth-last-of-type(-n+2) {
      margin-bottom: 0;
    }
  }
}
</style>
