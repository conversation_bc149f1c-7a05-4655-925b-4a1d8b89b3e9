<template>
  <div v-click-outside="close" class="dynamic-form-date">
    <k-input
      :value="dateText"
      :append-icon="arrowIcon"
      readonly
      :disabled="disabled"
      :placeholder="data.hint || ''"
      style="width: 100%;"
      @focus="handleFocus"
    />
    <k-date-picker
      v-if="showDatePicker"
      ref="datePicker"
      view-switchable
      :date="calendarDate"
      :first-day-of-week="1"
      :min-date="min"
      :max-date="max"
      @select="handleSelect"
    />
  </div>
</template>

<script>
import dayjs from 'dayjs'
import ClickOutside from 'vue-click-outside'
import Input from '@klook/klook-ui/lib/input'
import DatePicker from '@klook/klook-ui/lib/date-picker'
import locale from '@klook/klook-ui/lib/locale'

import '@klook/klook-ui/lib/styles/components/icon.scss'
import '@klook/klook-ui/lib/styles/components/input.scss'
import '@klook/klook-ui/lib/styles/components/date-picker.scss'

import { isServer, getKlkLanguage } from '@src/utils'
import { getStandardDateFormat } from '@src/utils/datetime'

if (!isServer) {
  const lang = getKlkLanguage()
  const _lang = /en/.test(lang) ? 'en' : lang
  import(`@klook/klook-ui/lib/locale/lang/${_lang}.json`).then(lang => {
    locale.use(lang)
  }).catch(err => console.log(lang, _lang, err))
}

export default {
  name: 'DynamicFormDate',
  components: {
    KInput: Input,
    kDatePicker: DatePicker,
  },
  inject: {
    $formItem: {
      default: null,
    }
  },
  directives: {
    ClickOutside
  },
  props: {
    value: {
      type: String,
      default: '',
    },
    data: {
      type: Object,
      default: () => ({}),
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    shouldCheckDateValid: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      showDatePicker: false,
    }
  },
  computed: {
    dateText() {
      return this.value
        ? getStandardDateFormat(
            dayjs(this.value).format('YYYY-MM-DD'),
            this.$t.bind(this),
            getKlkLanguage(this),
            2,
          )
        : ''
    },
    min() {
      const { type, min_date } = this.data.style.match_rule || {}
      return type === 1 && min_date ? new Date(min_date) : new Date('1000-01-01')
    },
    max() {
      const { type, max_date } = this.data.style.match_rule || {}
      return type === 1 && max_date ? new Date(max_date) : new Date('3000-01-01')
    },
    calendarDate() {
      if (this.value) {
        return dayjs(this.value).toDate()
      }

      const { default_date } = this.data.style.match_rule || {}
      if (default_date) {
        const defaultDate = this.validateDate(default_date) ? default_date : this.min
        return dayjs(defaultDate).toDate()
      }

      return null
    },
    arrowIcon() {
      return this.showDatePicker ? 'icon_navigation_chevron_up_xs' : 'icon_navigation_chevron_down_xs'
    },
  },
  watch: {
    value: {
      immediate: true,
      handler() {
        // 检查是否合法，不合法则重置为空
        // 自助修改订单不需要检查有效性，用户从面板上选择即可
        if (this.shouldCheckDateValid && this.value && !this.validateDate(this.value)) {
          this.update('')
        }
      }
    },
    calendarDate: {
      immediate: true,
      handler() {
        this.$refs.datePicker && this.$refs.datePicker.setViewDate(this.calendarDate || dayjs().toDate())
      },
    },
  },
  methods: {
    handleSelect(date) {
      if (date) {
        const dateText = dayjs(date).format('YYYY-MM-DD')
        this.update(dateText)
      }
      this.showDatePicker = false
    },
    handleFocus() {
      if (!this.disabled) {
        this.showDatePicker = true
      }
    },
    checkClassListContains(el, cls) {
      return el && el.classList && el.classList.contains(cls)
    },
    close(e) {
      // hack dom立即消失导致click-outside判断不了的问题
      // 自己：klk-date-picker-month-inner、klk-date-picker-year-inner
      // 父节点: klk-date-picker-panel-header-title
      // 父父节点: klk-date-picker-panel-header-title
      // 如果被removed，也不关闭
      const self = e && e.target
      if (
        this.checkClassListContains(self, 'klk-date-picker-month-inner')
          || this.checkClassListContains(self, 'klk-date-picker-year-inner')
      ) {
        return
      }

      const parent = e && e.target && e.target.parentNode
      if (this.checkClassListContains(parent, 'klk-date-picker-panel-header-title')) {
        return
      }

      const grandparent = e && e.target && e.target.parentNode && e.target.parentNode.parentNode
      if (this.checkClassListContains(grandparent, 'klk-date-picker-panel-header-title')) {
        return
      }

      // 有self但是没有parent或grandparent，表示被removed
      if (self && (!parent ||!grandparent)) {
        return
      }

      this.showDatePicker = false
    },
    validateDate(dateText) {
      return !dayjs(dateText).isBefore(this.min, 'day') && !dayjs(dateText).isAfter(this.max, 'day')
    },
    update(dateText) {
      this.$emit('input', dateText)
      this.$emit('change', dateText)

      // fix: date组件会在input blur的时候校验，这个时候数据还没有改变，所以这里再校验一次
      if (this.$formItem) {
        this.$formItem.$emit('change')
      }
    }
  },
}
</script>

<style lang="scss">
.dynamic-form-date {
  .klk-input-inner .klk-input-append,
  .klk-input-inner .klk-input-prepend {
    pointer-events: none;
  }

  .klk-input-inner .klk-input-append svg {
    color: $color-text-secondary;
    font-size: $fontSize-body-m;
  }

  .klk-input-disabled .klk-input-append svg {
    color: $color-text-disabled;
  }
}
</style>

<style lang="scss" scoped>
.dynamic-form-date {
  position: relative;

  .klk-date-picker {
    position: absolute;
    left: 0;
    top: 50px;
    border: 1px solid $color-border-normal;
    /* stylelint-disable */
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    z-index: 88;
  }
}
</style>
