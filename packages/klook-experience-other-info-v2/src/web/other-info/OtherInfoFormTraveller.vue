<template>
  <!--公共表单，为绑定值未拆模块-->
  <div class="other-info-traveller">
     <p  v-if="data.showCount" class="other-info-traveller-title">
      {{ data.name }}
      <span :class="[filledCount === data.count ? 'filled' : 'not-filled']">
        ({{ filledCount }}/{{ data.count }})
      </span>
    </p>

    <TravellerSelector
      ref="travellerSelector"
      v-if="showTravellerSelector"
      :traveller-id="travellerId"
      :section-type="sectionType"
      :traveller-list="travellerList"
      :count="data.count || 0"
      :name="data.name"
      :cancle-traveller-id="cancleTravellerId"
      class="other-info-traveller-selector"
      @selectTraveller="handleSelectTraveller"
      @changeItemSelectStatus="handleItemSelectedStatus"
    />

    <TravellerPanel
      v-if="isContact"
      ref="travellerPanel"
      :data="otherInfoData.data"
      :traveller-list="travellerList"
      :section-type="sectionType"
      :original-phone="originalPhone"
      :traveller-id="travellerId"
      class="other-info-traveller-panel"
      @edit="openDrawer"
    />

    <TravellerPanelNew
      v-else
      ref="travellerPanelNew"
      :data="otherInfoData.data"
      :traveller-list="travellerList"
      :section-type="sectionType"
      :original-phone="originalPhone"
      :traveller-id="travellerId"
      :origin-data="data"
      :no-need-update-data="noNeedUpdateData"
      :cancle-traveller-id="cancleTravellerId"
      class="other-info-traveller-panel-new"
      @changeItemSelectStatus="handleItemSelectedStatus"
      @edit="newOpenDrawer"
      @updateTravellerData="$emit('updateTravellerData', $event)"
    />

    <EditDrawer
      :visible.sync="drawerVisible"
      :loading="isLoading"
      :title="drawerTitle"
      :action="action"
      :section-type="sectionType"
      :data="drawerData.data || []"
      :sensitive-terms="sensitiveTerms"
      :drawer-width="'680px'"
      direction="right"
      ref="editDrawer"
      class="other-info-traveller-drawer"
      @confirm="handleConfirm"
      @close="handleClose"
    >
      <DynamicFormSkeleton v-if="showSkeleton" />
      <k-form
        v-else
        v-bind="formatFormProps"
        :style="{ width: wrapperWidth }"
        ref="form"
        class="other-info-traveller-drawer-form"
        :class="[isContact && 'is-contact']"
      >
        <DynamicForm
          v-for="(item, index) in drawerData.data || []"
          :key="`${item.field_key}-${item.id}`"
          :data="item"
          :prop="`data[${index}]`"
          :disabled="disabled"
          :original-phone="originalPhone"
          :should-check-date-valid="shouldCheckDateValid"
          :sectionType="sectionType"
          :action="action"
          :use-editable-traveller-form="useEditableTravellerForm"
        />
      </k-form>
    </EditDrawer>
  </div>
</template>

<script>
import cloneDeep from 'lodash/cloneDeep'
import { Form } from '@klook/klook-ui/lib/form'
import Checkbox from '@klook/klook-ui/lib/checkbox'
import Alert from '@klook/klook-ui/lib/alert'

import '@klook/klook-ui/lib/styles/components/icon.scss'
import '@klook/klook-ui/lib/styles/components/form.scss'
import '@klook/klook-ui/lib/styles/components/checkbox.scss'
import '@klook/klook-ui/lib/styles/components/alert.scss'

import TravellerSelector from '@src/web/components/TravellerSelector.vue'
import TravellerPanel from '@src/web/components/TravellerPanel.vue'
import TravellerPanelNew from '@src/web/components/TravellerPanelNew.vue'
import EditDrawer from '@src/web/components/EditDrawer.vue'
import DynamicForm from '@src/web/dynamic-form/index.vue'
import DynamicFormSkeleton from '@src/web/dynamic-form/DynamicFormSkeleton.vue'
import inhouseMixin from '@src/utils/mixins/inhouse'
import {
  TRAVELLER_TYPE,
  getUpdateTravellerListParams,
  CONTACT_TYPE,DRAW_TITLE_MAPS,
  cleanDynamicFormList,
  INIT_TRAVELLER_ID
} from '@src/utils'

/* other-info 出行人特殊展示和交互 */
export default {
  name: 'OtherInfoFormTraveller',
  inheritAttrs: false,
  mixins: [inhouseMixin],
  provide() {
    return {
      wrapperWidth: this.wrapperWidth,
      componentWidth: this.componentWidth,
    }
  },
  components: {
    KForm: Form,
    KCheckbox: Checkbox,
    KAlert: Alert,
    TravellerSelector,
    DynamicForm,
    TravellerPanel,
    EditDrawer,
    DynamicFormSkeleton,
    TravellerPanelNew
  },
  props: {
    travellerId: {
      type: [Number, String],
      default: -1
    },
    cancleTravellerId: {
      type: [Number, String],
      default: -1
    },
    sectionType: {
      type: Number,
      default: 1,
    },
    data: {
      type: Object,
      default: null,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    travellerList: {
      type: Array,
      default: () => [],
    },
    // 日历组件是否自动检测日期是否有效，如果无效则置空
    shouldCheckDateValid: {
      type: Boolean,
      default: true,
    },
    updateTravellerListFunc: {
      type: Function,
      default: null,
    },
    // hoc 的传值
    otherInfoData: {
      type: Object,
      default: null,
    },
    // 新增的时候需要加一些额外的表单
    addRequiredFormData: {
      type: Object,
      default: () => ({}),
    },
    // 用于新建
    otherInfoBlankForm: {
      type: Array,
      default: () => ([])
    },
     recordOtherinfoData: {
      type: Object,
      default: () => ({})
    },
    filledCount: {
      type: Number,
      default: 0
    },
    noNeedUpdateData: {
      type: Boolean,
      default: false
    },
    formProps: {
      type: Object,
      default: () => ({})
    },
    useEditableTravellerForm: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      wrapperWidth: '616px',
      componentWidth: '298px',

      isLoading: false,
      isDrawerAdd: false,
      drawerVisible: false,
      drawerData: {
        data: []
      },
      isDefaultChecked: false
    }
  },
  watch: {
    otherInfoData: {
      immediate: true,
      handler() {
        this.$nextTick(() => {
          this.validateOtherinfoData()
        })
      }
    }
  },
  computed: {
     showTravellerSelector() {
      return !this.data?.custom_info?.simple_checkout
    },
    formatFormProps(){
      const attrs = this.formProps || {}
      if (!attrs.hasOwnProperty('styleType')) {
        attrs.styleType = 'outlined'
      }

      return { ...attrs, model: this.drawerData }
    },
    action() {
      return this.isDrawerAdd  ? 'Add' : 'Edit'
    },
    isTraveller() {
      return this.sectionType === TRAVELLER_TYPE
    },
    isContact() {
      return this.sectionType === CONTACT_TYPE
    },
    drawerTitle() {
      const flag = this.isDrawerAdd ? 1 : 0
      return this.$t(DRAW_TITLE_MAPS.get(`${this.sectionType}_${flag}`))
    },
    showSkeleton() {
      return !this.drawerData.data || this.drawerData.data.length === 0
    },
    originalPhone() {
      const target = (this.data.info_items || []).find(item => item.style.type === 8)
      const content = (target || {}).content || ''
      const operation = (target || {}).operation || []
      return content && operation.length > 0 ? `${operation[0].field_key}-${content}` : null
    },
    additionalFormData() {
      const { custom_info } = this.data || {}

      if (custom_info && Object.keys(custom_info).length > 0) {
        const { add_concat_required_form = [] } = custom_info

        if (add_concat_required_form && add_concat_required_form.length) {
          return add_concat_required_form
        }

        return []
      }

      return []
    },
    sensitiveTerms() {
      const { custom_info } = this.data || {}

      return custom_info?.terms || []
    }
  },
  methods: {
    newOpenDrawer(id) {
      const isAddAction = id === INIT_TRAVELLER_ID
      !isAddAction && this.$emit('changeTravellerId', id)
      const data = (this.recordOtherinfoData[id] && this.recordOtherinfoData[id] ['value']) || null
      this.openDrawer(isAddAction, data)
    },
    openDrawer(isAdd = false, data = null) {
      this.isDrawerAdd = isAdd
      this.drawerVisible = true
      if (this.showSkeleton) {
        // 因为渲染国家选项的时候创建了很多html，会阻塞ui渲染，所以给一个延时
        setTimeout(() => {
          this.setDrawerData(data)
        }, 300)
      } else {
        this.setDrawerData(data)
      }
    },
    setDrawerData(data) {
      const formData = this.isDrawerAdd ? [...this.additionalFormData, ...cleanDynamicFormList(cloneDeep(this.otherInfoBlankForm))] : (data || this.otherInfoData.data)
      this.drawerData.data = cloneDeep(formData)
      this.$nextTick(() => {
        this.scrollDrawerToTop()
        this.$refs.form.clearValidate()
        // if (!this.isDrawerAdd) {
        //   this.validateForm()
        //   this.scrollToFormError()
        // } else {
        //   this.$refs.form.clearValidate()
        //   this.scrollToFormError(true)
        // }
      })
    },
    scrollDrawerToTop() {
      const drawerContentDom = this.$el.querySelector('.edit-drawer-content')
      drawerContentDom && (drawerContentDom.scrollTop = 0)
    },
    handleSelectTraveller(id, status) {
      this.isDefaultChecked = !!status
      // 如果是新增的情况，则直接打开弹窗
      if (id === 0) {
        this.openDrawer(true)
      } else {
        this.$emit('selectTraveller', id)
      }
    },
    handleItemSelectedStatus(id, status) {
      this.$emit("changeItemSelectStatus", id, status)
    },
    getMethodAndValidate() {
      const method = 'validate'
      const ref = this.isTraveller ? 'travellerPanelNew' : 'travellerPanel'
      return {
        method,
        ref
      }
    },
    validate() {
      let method = 'validate', ref = 'travellerPanel'
      if (this.isTraveller) {
        method = 'validateDataList'
        ref = 'travellerPanelNew'
      }
      return this.$refs[ref][method]().catch(err => {
        return false
      })
    },
    validateForm() {
      return this.$refs.form.validate().catch(err => {
        return false
      })
    },
    validateOtherinfoData() {
      if (this.travellerId !== INIT_TRAVELLER_ID) {
        const { method, ref } = this.getMethodAndValidate()
        this.$refs[ref][method]().then(res => {
          if (this.isTraveller) {
           !this.noNeedUpdateData && this.$emit('validatePass')
          }
        }).catch(_ => {
          !this.isDefaultChecked && this.openDrawer()
        })
      }
    },
    scrollToFormError(isAdd) {
      this.$nextTick(() => {
        const formEl = this.$refs.form.$el
        const dom = isAdd ? '.dynamic-form-wrap' : '.dynamic-form-item.klk-form-item-has-error'
        const errorDom = formEl && formEl.querySelector(dom)
        errorDom && errorDom.scrollIntoView({
          block: isAdd ? 'center' : 'start',
          behavior: 'smooth'
        })
      })
    },
    clearValidate() {
      const { ref } = this.getMethodAndValidate()
      this.$refs[ref].clearValidate()
    },
    async handleSave() {
      if (!this.updateTravellerListFunc) {
        return
      }
      this.isLoading = true
      const res = await this.updateTravellerListFunc({
        traveller_id: this.isDrawerAdd ? 0 : this.travellerId,
        items: getUpdateTravellerListParams(this.drawerData.data)
      })
      this.isLoading = false

      if (res.success) {
        // 这里为了性能，暂时不用深复制
        const start = this.isDrawerAdd ? this.additionalFormData.length : 0
        this.$emit('handleUpdateTravellerList', res.result, this.drawerData.data.slice(start))
        this.drawerVisible = false
        this.$refs.editDrawer && this.$refs.editDrawer.closeAllModal()
      }
    },
    drawerAlert(text) {
      const drawerElement = this.$refs.editDrawer
      if (drawerElement) {
        drawerElement.alert(text)
      }
    },
    handleClose() {
      this.$refs.form.clearValidate()
      // !this.isDrawerAdd && await this.validate()
    },
    async handleConfirm() {
      const isValid = await this.validateForm()

      if (isValid) {
        return this.handleSave()
      }

      setTimeout(() => {
        this.drawerAlert(this.$t('72688'))
        const errorDom = this.$el.querySelector('.dynamic-form-item.klk-form-item-has-error')
        errorDom &&
          errorDom.scrollIntoView({
            block: 'center',
            behavior: 'smooth',
          })
      }, 20)
    },
  },
}
</script>

<style lang="scss" scoped>
.other-info-traveller {
  margin-bottom: 20px;


  &:last-child {
    margin-bottom: 0;
  }

  &-title {
    @include font-body-s-bold;
    padding-top: 8px;
    padding-bottom: 11px;
    color: $color-text-primary;
  }

  &-selector {
    margin-bottom: 16px;
  }

  &-panel {
    margin-top: 16px;
    border: 1px solid $color-neutral-200;
    border-radius: 16px;
    padding: 16px;
  }

  &-panel-new {
    margin-top: 16px;
  }

  &-drawer-form {
    margin-top: 24px;
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;

    &:first-child {
      margin-top: 0;
    }

    &.is-contact {
      ::v-deep .dynamic-form-mobile-verified {
        display: none;
      }
    }
  }
}

.not-filled  {
  color: $color-red-500;
}

.filled {
  color: $color-neutral-900;
}
</style>
