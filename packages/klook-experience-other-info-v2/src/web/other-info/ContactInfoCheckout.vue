<template>
  <div class="contact-info-checkout">
    <template v-for="item in dataList">
      <div
        v-if="item.title && item.txt"
        :key="item.title"
        class="contact-info-checkout-section"
      >
        <p class="contact-info-checkout-title">{{ item.title }}</p>
        <p class="contact-info-checkout-txt">{{ item.txt }}</p>
      </div>
    </template>
  </div>
</template>

<script>
export default {
  name: "ContactInfoCheckOut",
  props: {
    data: {
      type: Object,
      default: () => ({})
    },
    verificationData: {
      type: Object,
      default: () => ({})
    },
  },
  data() {
    return {
      defaultKeyArr: [
        'title',
        'first_name',
        'family_name',
        'travel_country',
        'mobile',
        'traveller_email'
      ]
    }
  },
  computed: {
    titleMap() {
      return {
        title: this.$t('globa.name.title'),
        first_name: this.$t('71762'),
        family_name: this.$t('71763'),
        travel_country: this.$t('12775'),
        mobile: this.$t('71764'),
        traveller_email: this.$t('16182-email_address') + this.$t('71765'),
      }
    },
    dataList() {
      const keyArr = this.verificationData.contact_person_keys || this.defaultKeyArr
      const { countryCode, phone } = this.data

      return keyArr.map((key) => {
        let txt = this.data[key] || null

        // 对于电话，如果没有mobile字段，则由 countryCode 和 phone 拼接
        if (!txt && key === 'mobile' && countryCode && phone) {
          txt = `${countryCode}-${phone}`
        }

        return {
          txt,
          title: this.titleMap[key]
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.contact-info-checkout {
  margin-top: -16px;
  display: flex;
  flex-wrap: wrap;
  width: 100%;

  &-section {
    width: 360px;
    margin-top: 16px;

    &:nth-child(odd) {
      margin-right: 20px;
    }
  }

  &-title {
    margin-bottom: 4px;
    font-size: $fontSize-caption-m;
    line-height: 16px;
    color: $color-text-secondary;
  }

  &-txt {
    word-break: break-word;
    font-size: $fontSize-body-m;
    line-height: 20px;
    color: $color-text-primary;
  }
}
</style>
