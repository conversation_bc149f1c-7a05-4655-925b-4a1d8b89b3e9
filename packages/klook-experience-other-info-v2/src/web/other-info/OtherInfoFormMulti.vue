<template>
  <div v-if="data && data.length > 0" class="other-info-form-multi">
    <component
      v-for="(item, index) in data"
      :is="calcComponentName()"
      :key="index"
      :data="item"
      :section-type="sectionType"
      :use-new-feature="useNewFeature"
      :use-editable-traveller-form="useEditableTravellerForm"
      :origin-form-infos="originFormInfos"
      :form-props="formProps"
      :verified-mobile-code="verifiedMobileCode"
      v-bind="$attrs"
      @add="data => $emit('add', index, data)"
      @remove="$emit('remove', index)"
      @change="data => handleChange(index, data)"
      @input="$emit('input')"
      @validate="handleValidate"
      @updateTravellerData="$emit('updateTravellerData', $event)"
    />
  </div>
</template>

<script>
import OtherInfoForm from '@src/web/form'
import OtherInfoFormTraveller from '@src/web/form-traveller'
import OtherInfoFormTravellerEditable from '@src/web/form-traveller-editable'
import { TRAVELLER_TYPE , CONTACT_TYPE} from '@src/utils'

export default {
  name: 'OtherInfoFormMulti',
  inheritAttrs: false,
  components: {
    OtherInfoForm,
    OtherInfoFormTraveller,
    OtherInfoFormTravellerEditable,
  },
  props: {
    data: {
      type: Array,
      default: () => [],
    },
    sectionType: {
      type: Number,
      default: 1,
    },
    // 目前的new feature只是：新的出行人信息卡片。（在自助修改订单不使用这个new feature）
    useNewFeature: {
      type: Boolean,
      default: false,
    },
    useEditableTravellerForm: {
      type: Boolean,
      default: false,
    },
    originFormInfos: {
      type: Array,
      default: () => [],
    },
    formProps: {
      type: Object,
      default: () => ({}),
    },
    verifiedMobileCode: {
      type: Object,
      default: () => ({}),
    }
  },
  methods: {
    handleChange(index, data) {
      const formInfos = [...this.data]
      formInfos.splice(index, 1, data)
      this.$emit('change', formInfos)
    },
    handleValidate(...args) {
      this.$emit('validate', ...args)
    },
    calcComponentName() {
      return this.useNewFeature && [TRAVELLER_TYPE, CONTACT_TYPE].includes(this.sectionType) ? (this.useEditableTravellerForm ? 'OtherInfoFormTravellerEditable' : 'OtherInfoFormTraveller') : 'OtherInfoForm'
    }
  },
}
</script>
