<template>
  <!--公共表单，为绑定值未拆模块-->
  <div class="other-info-form" :style="{ width: wrapperWidth }">
    <p v-if="data.name" class="other-info-form-title">
      {{ data.name }}
    </p>
    <TravellerSelector
      v-if="!!customInfo.traveler_is_show && travellerList.length > 0"
      :traveller-id="travellerId"
      :section-type="sectionType"
      :traveller-list="travellerList"
      class="other-info-form-selector"
      @selectTraveller="handleSelectTraveller"
    />
    <k-form
      v-bind="formatFormProps"
      ref="form"
      class="other-info-form-content"
    >
      <DynamicForm
        v-for="(item, index) in (otherInfoData.data || [])"
        :key="`${item.field_key}-${item.id}`"
        :data="item"
        :prop="`data[${index}]`"
        :disabled="disabled"
        :original-phone="originalPhone"
        :should-check-date-valid="shouldCheckDateValid"
        data-need-replace-dom="true"
        data-parent-node=".other-info-form-multi"
        data-replace-node=".other-info-module-header"
        @change="handleChange"
        @input="handleInput"
      />
      <div
        v-if="checkboxContent"
        class="other-info-form-checkbox js-spm-checkbox-in-form"
        :data-spm-module="
          `${checkboxType}?ext=${JSON.stringify({
            ModuleType: sectionType,
            ClickType: true,
          })}`
        "
        data-spm-virtual-item="__virtual?trg=manual"
      >
        <k-checkbox
          :value="data.bottom_info && data.bottom_info.is_checkbox_selected"
          @change="checkBoxChange"
        >
          {{ checkboxContent }}
        </k-checkbox>
      </div>
    </k-form>

    <ShipmentModal
      v-if="isShipment && shipmentList.length > 0"
      :visible.sync="shipmentVisible"
      :shipment-list="shipmentList"
      :other-info-list="data.info_items"
      :shipment-id="shipmentId"
      @selectShipment="handleSelectShipment"
    />
  </div>
</template>

<script>
import { Form } from '@klook/klook-ui/lib/form'
import Checkbox from '@klook/klook-ui/lib/checkbox'
import Alert from '@klook/klook-ui/lib/alert'

import '@klook/klook-ui/lib/styles/components/icon.scss'
import '@klook/klook-ui/lib/styles/components/form.scss'
import '@klook/klook-ui/lib/styles/components/checkbox.scss'
import '@klook/klook-ui/lib/styles/components/alert.scss'

import { SHIPMENT_TYPE } from '@src/utils'
import ShipmentModal from '@src/web/components/ShipmentModal.vue'
import TravellerSelector from '@src/web/components/TravellerSelector.vue'
import DynamicForm from '@src/web/dynamic-form/index.vue'
import widthMixin from '@src/utils/mixins/width'
import inhouseMixin from '@src/utils/mixins/inhouse'

export default {
  name: 'OtherInfoForm',
  inheritAttrs: false,
  mixins: [widthMixin, inhouseMixin],
  components: {
    KForm: Form,
    KCheckbox: Checkbox,
    KAlert: Alert,
    TravellerSelector,
    ShipmentModal,
    DynamicForm,
  },
  props: {
    travellerId: {
      type: Number,
      default: -1
    },
    shipmentId: {
      type: Number,
      default: -1
    },
    sectionType: {
      type: Number,
      default: 1,
    },
    data: {
      type: Object,
      default: null,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    travellerList: {
      type: Array,
      default: () => [],
    },
    shipmentList: {
      type: Array,
      default: () => [],
    },
    // 日历组件是否自动检测日期是否有效，如果无效则置空
    shouldCheckDateValid: {
      type: Boolean,
      default: true,
    },
    // hoc 的传值
    otherInfoData: {
      type: Object,
      default: null
    },
    // 下面的checkbox
    checkboxType: {
      type: String,
      default: 'UpdateInfo'
    },
    checkboxContent: {
      type: String,
      default: ''
    },
    formProps: {
      type: Object,
      default: () => ({})
    },
  },
  data() {
    return {
      shipmentVisible: false,
    }
  },
  computed: {
     formatFormProps(){
      const attrs = this.formProps || {}
      if (!attrs.hasOwnProperty('styleType')) {
        attrs.styleType = 'outlined'
      }

      return { ...attrs, model: this.otherInfoData }
    },
    isShipment() {
      return this.sectionType === SHIPMENT_TYPE
    },
    customInfo() {
      return this.data.custom_info || {}
    },
    originalPhone() {
      const target = (this.data.info_items || []).find(item => item.style.type === 8)
      const content = (target || {}).content || ''
      const operation = (target || {}).operation || []
      return content && operation.length > 0 ? `${operation[0].field_key}-${content}` : null
    },
  },
  watch: {
    otherInfoData() {
      this.$nextTick(() => {
        if (this.travellerId > 0 || this.shipmentId > 0) {
          this.validate()
        } else {
          this.clearValidate()
        }
      })
    }
  },
  methods: {
    openShipment() {
      this.shipmentVisible = true
      return this.shipmentVisible
    },
    validate() {
      return this.$refs.form.validate().catch((err) => {
        console.warn(err)
        return false
      })
    },
    clearValidate() {
      this.$refs.form.clearValidate()
    },
    handleChange() {
      this.$emit('change')
    },
    handleInput() {
      this.$emit('input')
    },
    handleSelectTraveller(id) {
      this.$emit('selectTraveller', id)
    },
    handleSelectShipment(id) {
      this.$emit('selectShipment', id)
    },
    checkBoxChange(val) {
      this.$emit('updateCheckbox', !!val)
      this.updateCheckBoxSpm(val)
    },
    updateCheckBoxSpm(ClickType = true) {
      this.$nextTick(() => {
        const spm = `${this.checkboxType}?ext=${JSON.stringify({
          ModuleType: this.sectionType,
          ClickType,
        })}`
        this.updateDataSpm('data-spm-module', '.js-spm-checkbox-in-form', spm)
        this.sendDataSpm('virtualAction', '.js-spm-checkbox-in-form')
      })
    },
  },
}
</script>

<style lang="scss">
.other-info-form {
  &-checkbox {
    .klk-checkbox-base {
      box-sizing: border-box;
    }
  }
}
</style>

<style lang="scss" scoped>
.other-info-form {
  margin-bottom: 8px;

  &:last-child {
    margin-bottom: 0;
  }

  &-title {
    margin-bottom: 16px;
    font-weight: $fontWeight-bold;
    font-size: $fontSize-body-m;
    line-height: $lineHeight-relaxed;
    color: $color-text-primary;
  }

  &-selector {
    margin-bottom: 16px;
  }

  &-content {
    margin-bottom: -24px;
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
  }

  &-checkbox {
    margin-top: -4px;
    margin-bottom: 24px;
    width: 100%;
  }
}
</style>
