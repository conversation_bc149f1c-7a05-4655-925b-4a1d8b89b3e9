<template>
  <div
    v-if="data"
    class="other-info-module"
    :class="classList"
    :data-spm-module="spmModule"
  >
    <div class="other-info-module-header">
      <p class="other-info-module-header-title">
        {{ data.title }}
      </p>
      <div>
        <div
          v-if="shouldShowShipment"
          class="other-info-module-header-address"
          data-spm-module="ChooseAddress"
          data-spm-virtual-item="__virtual"
          @click="handleOpenShipment"
        >
          {{ $t('28652') }}
        </div>
      </div>
    </div>

    <p v-if="greyTips" class="other-info-module-tips">
      {{ greyTips }}
    </p>

    <k-alert class="other-info-module-warning" v-if="data.tips" type="warning" show-icon>
      {{ data.tips }}
    </k-alert>

    <OtherInfoFormMulti
      ref="otherInfoFormMulti"
      :data="data.form_infos"
      :section-type="data.section_type"
      :shipment-list="shipmentList"
      :origin-form-infos="originFormInfos"
      v-bind="$attrs"
      @change="handleChange"
      @input="$emit('input')"
      @validate="handleValidate"
      @updateTravellerData="$emit('updateTravellerData', $event)"
    />
  </div>
</template>

<script>
import Alert from '@klook/klook-ui/lib/alert'
import '@klook/klook-ui/lib/styles/components/alert.scss'

import { SHIPMENT_TYPE, TRAVELLER_TYPE, CONTACT_TYPE } from '@src/utils'
import OtherInfoFormMulti from '@src/web/form-multi'
import childEmitMixin from '@src/utils/mixins/child-emit'

export default {
  name: 'OtherInfoModule',
  inheritAttrs: false,
  mixins: [childEmitMixin],
  components: {
    KAlert: Alert,
    OtherInfoFormMulti,
  },
  props: {
    data: {
      type: Object,
      default: null,
    },
    shipmentList: {
      type: Array,
      default: () => [],
    },
    originFormInfos: {
      type: Array,
      default: () => []
    }
  },
  computed: {
    classList() {
      return {
        'is-contact': this.isContact,
        'is-shipment': this.isShipment,
        'is-other': this.isOtherInfo
      }
    },
    spmModule() {
      return `OtherInfo?ext=${JSON.stringify({ InfoType: this.data.section_type })}`
    },
    shouldShowShipment() {
      return this.isShipment && this.shipmentList.length > 0
    },
    isShipment() {
      return SHIPMENT_TYPE === this.data.section_type
    },
    isOtherInfo() {
      return 0 === this.data.section_type
    },
    isTraveller() {
      return TRAVELLER_TYPE === this.data.section_type
    },
    isContact() {
      return CONTACT_TYPE === this.data.section_type
    },
    greyTips() {
      const { data } = this

      if (this.isTraveller) {
        const arr = []

        if (data.grey_tips) {
          arr.push(data.grey_tips)
        }

        if (data.right_title) {
          arr.push(data.right_title)
        }

        return arr.join('-')
      }

      return this.data.grey_tips
    }
  },
  methods: {
    async validate() {
      let isValid = await this.$refs.otherInfoFormMulti.validate()
      return isValid
    },
    getModifiedData() {
      const formInfos = this.$refs.otherInfoFormMulti.getModifiedData()
      return this.wrapData(formInfos)
    },
    cacheData() {
      this.$refs.otherInfoFormMulti.cacheData()
    },
    clearCache() {
      this.$refs.otherInfoFormMulti.clearCache()
    },
    recoverData() {
      this.$refs.otherInfoFormMulti.recoverData()
    },
    refreshTraveller() {
      this.$refs.otherInfoFormMulti.refreshTraveller()
    },
    handleChange(formData) {
      this.$emit('change', this.wrapData(formData))
    },
    handleValidate(...args) {
      this.$emit('validate', this.data, ...args)
    },
    handleOpenShipment() {
      this.$refs.otherInfoFormMulti.openShipment()
    },
    wrapData(formInfos) {
      return {
        ...this.data,
        form_infos: formInfos
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.other-info-module {
  // padding-top: 32px;
  margin-top: 24px;
  border-top: 1px solid $color-border-normal;

  &:first-child {
    margin-top: 0;
  }

  // 出行人信息特殊处理
  &.is-traveller {
    .other-info-module-header {
      margin-bottom: 12px;
    }

    .other-info-module-tips {
      @include font-caption-m-regular;

      margin-top: -10px;
      color: $color-text-primary;
    }
  }

  &-header {
    padding-bottom: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    &-title {
      color: $color-text-primary;
      font-weight: $fontWeight-bold;
      font-size: $fontSize-body-m;
      line-height: 23px;
    }

    &-address {
      color: $color-info;
      font-weight: $fontWeight-regular;
      font-size: $fontSize-body-s;
      line-height: 22px;
      cursor: pointer;
    }
  }

  &-tips {
    margin: -10px 0 24px;
    color: $color-text-secondary;
    line-height: 21px;
  }

  &-warning {
    margin-bottom: 16px;
  }
}
</style>
