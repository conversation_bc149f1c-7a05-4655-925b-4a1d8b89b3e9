<template>
  <div
    class="traveller-selector"
    :data-spm-module="
      `FrequentInfo?ext=${JSON.stringify({
        InfoType: sectionType,
        Amount: travellerList.length,
        Position: selectedPosition,
        FillinType: selectedFillinType,
        UserType: selectedUserType,
      })}`
    "
  >
    <k-tag-select
      v-if="isContact"
      :value="selectedId"
      size="small"
      ref="travelTagSelect"
      style="width: 100%;"
      @change="handleSelect"
    >
      <k-tag-select-item
        v-for="(item, index) in travellerList"
        :key="`${item.traveller_id}-${item.name}`"
        :name="item.traveller_id"
        :data-spm-item="
          `Traveler?ext=${JSON.stringify({
            Amount: travellerList.length,
            Position: index,
            UserType: getInhouseUserType(item),
          })}`
        "
        class="traveller-selector-item"
      >
        {{ item.name }}
      </k-tag-select-item>
      <k-tag-select-item
        class="traveller-selector-add"
        :key="0"
        :name="0"
        :data-spm-item="
          `Traveler?ext=${JSON.stringify({
            Amount: travellerList.length,
            Position: travellerList.length,
            UserType: 'Add',
          })}`
        "
      >
        <div>
          <AddSvg class="traveller-selector-add-icon"></AddSvg>
          <span class="traveller-selector-add-text">{{ $t('17443') }}</span>
        </div>
      </k-tag-select-item>
    </k-tag-select>
    <k-tag-select-multiple
      v-else
      :value="selectedId"
      :count="count"
      :name="name"
      size="small"
      style="width: 100%;"
      @change="handleChangeEvent"
      @chooseChange="handleChooseChange"
      v-bind="$attrs"
    >
      <k-tag-select-item-multiple
        v-for="(item, index) in travellerList"
        :key="`${item.traveller_id}-${item.index}`"
        :class="[{ 'no-content-style': !item.name }]"
        :name="item.traveller_id"
        :data-spm-item="
          `Traveler?ext=${JSON.stringify({
            Amount: travellerList.length,
            Position: index,
            UserType: getInhouseUserType(item),
            SelectType: selectedId === item.traveller_id ? 'Select' : 'Unselect'
          })}`
        "
        class="traveller-selector-item"
      >
        {{ item.name }}
      </k-tag-select-item-multiple>
      <k-tag-select-item-multiple
        class="traveller-selector-add"
        :key="0"
        :name="0"
        :data-spm-item="
          `Traveler?ext=${JSON.stringify({
            Amount: travellerList.length,
            Position: travellerList.length,
            UserType: 'Add',
          })}`
        "
      >
        <AddSvg class="traveller-selector-add-icon"></AddSvg>
        <span class="traveller-selector-add-text">{{ $t('17443') }}</span>
      </k-tag-select-item-multiple>
    </k-tag-select-multiple>
  </div>
</template>

<script>
import { TagSelect, TagSelectItem } from '@klook/klook-ui/lib/tag-select'
import  TagSelectMutiple from "@src/utils/components/tag-select/TagSelect.vue"
import  TagSelectItemMutiple from "@src/utils/components/tag-select/TagSelectItem.vue"

import '@klook/klook-ui/lib/styles/components/icon.scss'
import '@klook/klook-ui/lib/styles/components/tag.scss'
import '@klook/klook-ui/lib/styles/components/tag-select.scss'
import AddSvg from '@src/imgs/icon_other_plus_xs.svg'

import { TRAVELLER_TYPE, CONTACT_TYPE, INIT_TRAVELLER_ID} from '@src/utils'

export default {
  name: 'TravellerSelector',
  components: {
    KTagSelect: TagSelect,
    KTagSelectItem: TagSelectItem,
    KTagSelectMultiple:TagSelectMutiple,
    KTagSelectItemMultiple: TagSelectItemMutiple,
    AddSvg
  },
  props: {
    travellerId: {
      type: [Number, String],
      default: -1
    },
    sectionType: {
      type: Number,
      default: 1,
    },
    travellerList: {
      type: Array,
      default: () => [],
    },
    count: {
      type: Number,
      default: 0
    },
    name: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      selectedId: -1, // tag-select有bug，此处hack
      isManualChecked: false,
      isDefaultChecked: false,
      defaultCheckStatus: false,
      noUpdateTraveller: false // 联系人点击新增按钮，没有更新出行人
    }
  },
  computed: {
    isTraveller() {
      return this.sectionType === TRAVELLER_TYPE
    },
    isContact() {
      return this.sectionType === CONTACT_TYPE
    },
    selectedUser() {
      if (this.selectedId === 0) {
        return { traveller_id: 0 }
      }

      const choosedUser = this.travellerList.find(item => item.traveller_id === this.selectedId)
      return choosedUser ? choosedUser : null // null是指没有选
    },
    selectedFillinType() {
      if (!this.selectedUser) {
        return 'Null'
      }

      if (this.isDefaultChecked && !this.isManualChecked) {
        return 'Default'
      }

      if (this.selectedUser.traveller_id === 0) {
        return 'Blank'
      }

      return 'User'
    },
    selectedUserType() {
      if (!this.selectedUser) {
        return 'Null'
      }

      if (this.selectedUser.traveller_id === 0) {
        return 'Add'
      }

      return this.getInhouseUserType(this.selectedUser)
    },
    selectedPosition() {
      // 没有选则为 -1
      if (!this.selectedUser) {
        return INIT_TRAVELLER_ID
      }

      // add则为最后一个
      if (this.selectedUser.traveller_id === 0) {
        return this.travellerList.length
      }

      return this.travellerList.map(item => item.traveller_id).indexOf(this.selectedUser.traveller_id)
    },
  },
  watch: {
    travellerId: {
      immediate: true,
      handler(val) {
        this.selectedId = val
      }
    },
    travellerList: {
      immediate: true,
      handler() {
        // 默认选中只发生一次
        if (this.isDefaultChecked) {
          return
        }

        const { isTraveller, isContact, travellerList } = this
        const key = isTraveller ? 'is_last_traveller_used' : isContact ? 'is_last_contact_used' : ''

        if (travellerList.length > 0 && key && !isTraveller) {
          // 判断是否设置了默认选中(只判断前三个)
          for (let i = 0; i < 3; i++) {
            if (!this.isDefaultChecked && travellerList[i] && travellerList[i]['default_traveller']) {
              this.handleDefaultSelect(travellerList[i].traveller_id, 'default')
              this.isDefaultCheckType = true // 重置为default
              this.isDefaultChecked = true
              break
            }
          }

          // 判断 is_last_traveller_used 和 is_last_contact_used(只判断前三个)
          for (let i = 0; i < 3; i++) {
            if (!this.isDefaultChecked && travellerList[i] && travellerList[i][key]) {
              this.handleDefaultSelect(travellerList[i].traveller_id,)
              this.isDefaultCheckType = true // 重置为default
              this.isDefaultChecked = true
              break
            }
          }

          // // 如果是出行人的话，就做特殊处理：如果没有默认选中，则默认选中第一个
          // if (isTraveller && !this.isDefaultChecked && travellerList.length > 0) {
          //   this.handleSelect(travellerList[0].traveller_id)
          //   this.isDefaultChecked = true
          // }
        }
      },
    },
  },
  methods: {
    getInhouseUserType(item) {
      if (item.default_traveller) {
        return 'Myself'
      }

      if (this.isTraveller && item.is_last_traveller_used) {
        return 'Default'
      }

      if (this.isContact && item.is_last_contact_used) {
        return 'Default'
      }

      return 'Other'
    },
    handleChooseChange(id, status) {
      this.$emit('changeItemSelectStatus', id, status)
    },
    handleChangeEvent(id) {
      this.$emit('selectTraveller', id)
    },
    handleSelect(id) {
      (this.$refs.travelTagSelect && this.$refs.travelTagSelect.showCurrentItem) &&  setTimeout(() => this.$refs.travelTagSelect.showCurrentItem(true), 50)
      if (this.noUpdateTraveller) {
        this.noUpdateTraveller = false
        return
      }
      // 联系人需要默认勾选上次下单人
      const status = this.defaultCheckStatus ? 'default' : null
      this.defaultCheckStatus = false
      // klk-counter有bug，这里hack一下
      if (id === 0) {
        this.selectedId = 0
        this.$nextTick(() => {
          this.selectedId = this.travellerId
          this.noUpdateTraveller = true
        })
      }
      this.$emit('selectTraveller', id, status)
    },
    handleDefaultSelect(id) {
      this.defaultCheckStatus = true
      this.$emit('selectTraveller', id, 'default')
    }
  },
}
</script>

<style lang="scss">
.traveller-selector {
  .klk-tag-select {
    padding-left: 0;
    padding-right: 36px;

    .klk-tag-select-items {
      margin-bottom: -8px;
    }

    .klk-tag-select-item {
      margin-bottom: 8px;
      margin-top: 0;
      // margin-bottom: 8px;
      padding: 8px 12px;
      /* styleling-disable-nuxt-line */
      height: 36px;
      /* styleling-disable-nuxt-line */
      line-height: 20px;
      font-weight: $fontWeight-bold;
      border-radius: $radius-s;
      border: 1px solid transparent;
      background-color: $color-bg-widget-darker-2;

      &:not(.traveller-selector-add):hover {
        background-color: $color-bg-widget-darker-3;
      }

      &.klk-tag-select-item-active,
      &.klk-tag-select-item-active:hover {
        color: $color-brand-primary;
        /* styleling-disable-nuxt-line */
        background-color: rgba(255, 91, 0, 0.12);
        border-color: $color-brand-primary;

        .traveller-selector-add-icon {
          color: $color-brand-primary;
        }
      }

      &.no-content-style {
        position: relative;
        top: -4px;
      }
    }

    .klk-tag-select-right {
      height: 36px;
    }

    .klk-tag-select-toggle-btn {
      border-right: none;

      .klk-icon {
        font-size: 16px;

        svg {
          font-size: 16px;
        }
      }
    }
  }
}
</style>

<style lang="scss" scoped>
$prefix: '.traveller-selector';

#{$prefix}  {
  width: 100%;

  ::v-deep .klk-tag-select-item > span {
    max-width: 174px;
  }

  ::v-deep #{$prefix}-add {
    position: relative;
    background: #fff;
    border: 1px solid $color-orange-500;
    color: $color-orange-500;
    max-width: 200px;
    padding: 8px 16px 8px 12px;
    font-weight: $fontWeight-semibold !important;
  }

  #{$prefix}-add-icon {
    width: 16px;
    height: 16px;
    position: absolute;
    top: 8px;
  }

  #{$prefix}-add-text {
    margin-left: 20px;
  }
}
</style>
