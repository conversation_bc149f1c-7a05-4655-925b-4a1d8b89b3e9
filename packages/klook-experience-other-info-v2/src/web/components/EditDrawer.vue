<template>
  <div>
    <k-drawer
      :visible.sync="drawerVisible"
      :mask-closable="false"
      direction="right"
      class="edit-drawer"
      v-on="$attrs"
      data-spm-module="EditPage"
    >
      <div class="edit-drawer-header">
        <h3>{{ title }}</h3>
        <div @click="closeEvent">
          <CloseSvg class="edit-drawer-close" />
        </div>
      </div>

      <div class="edit-drawer-content" :style="{ width: drawerWidth }">
        <slot></slot>

        <div v-if="sensitiveTerms.length" class="edit-drawer-terms-box">
          <p v-html="formatSensitiveTerms()"></p>
        </div>
      </div>

      <div class="edit-drawer-footer">
        <div style="display: flex">
          <k-button
            type="outlined"
            size="small"
            style="margin-right: 16px"
            @click="drawerVisible = false"
            :data-spm-item="`Cancel?ext=${JSON.stringify({
              ModuleType: sectionType,
              Clicktype: action,
            })}`"
          >
            {{ $t('71853') }}
          </k-button>
          <k-button
            type="primary"
            size="small"
            :loading="loading"
            @click="$emit('confirm')"
            :data-spm-item="`Save?ext=${JSON.stringify({
              ModuleType: sectionType,
              Clicktype: action,
              infotype: sectionType,
            })}`"
          >
            {{ $t('72653') }}
          </k-button>
        </div>
      </div>
    </k-drawer>

    <klk-modal
      :open.sync="showTipModal"
      class="tip-modal"
      :lock-scroll="false"
      data-spm-page="TTD_Payment_LeavePage?trg=manual"
    >
      <p class="modal-content">{{ $t('78448') }}</p>
      <div slot="footer" class="footer-box">
        <klk-button
          type="outlined"
          :size="'small'"
          :data-spm-module="`LeavePageAction?ext=${JSON.stringify({
            ActionType: 'Stay'
          })}`"
          data-spm-virtual-item="__virtual"
          @click="showTipModal = false"
        >
          {{ $t('78573') }}
        </klk-button>
        <klk-button
          type="primary"
          :data-spm-module="`LeavePageAction?ext=${JSON.stringify({
            ActionType: 'Leave'
          })}`"
          data-spm-virtual-item="__virtual"
          @click="closeAllModal"
          :size="'small'"
        >
          {{ $t('78572') }}
        </klk-button>
      </div>
    </klk-modal>
  </div>
</template>

<script>
import Drawer from '@klook/klook-ui/lib/drawer'
import Button from '@klook/klook-ui/lib/button'

import '@klook/klook-ui/lib/styles/components/drawer.scss'
import '@klook/klook-ui/lib/styles/components/button.scss'

import CloseSvg from '@src/imgs/close.svg'
import cloneDeep from 'lodash/cloneDeep'
import { isServer, diffObjValue } from "@src/utils"
import inhouseMixin from '@src/utils/mixins/inhouse'


export default {
  name: 'EditDrawer',
  mixins: [inhouseMixin],
  components: {
    CloseSvg,
    KButton: Button,
    KDrawer: Drawer,
  },
  props: {
    title: {
      type: String,
      default: '',
    },
    drawerWidth: {
      type: String,
      default: 'auto',
    },
    visible: {
      type: Boolean,
      default: false,
    },
    loading: {
      type: Boolean,
      default: false,
    },
    sectionType: {
      type: Number,
      default: 1,
    },
    action: {
      type: String,
      default: '',
    },
    data: {
      type: [Object, Array],
    },
    sensitiveTerms: {
      type: [Array],
      default: () => []
    }
  },
  data() {
    return {
      sheetVisible: false,
      originData: null,
      showTipModal: false,
    }
  },
  computed: {
    drawerVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      },
    },
  },
  methods: {
    alert(text) {
      const target = this.$el?.querySelector('.edit-drawer .klk-drawer-content')
      this.$toast(text, target)
    },
    closeEvent() {
      // 编辑
      if (this.action =='Edit') {
        const hasChange = diffObjValue(this.originData, this.data)
        if (hasChange) {
          // 有修改,弹出提示
          this.showTipModal = true
        } else {
          this.$emit('close')
          this.closeAllModal()
        }
      } else {
        // 新增，直接关闭
        this.closeAllModal()
      }
    },
    closeAllModal() {
      this.drawerVisible = false
      this.originData && (this.originData = null)
      this.showTipModal && (this.showTipModal = false)
    },
    formatSensitiveTerms() {
      if (this.sensitiveTerms.length) {
        return this.sensitiveTerms.reduce((p, o) => p += o, '')
      }

      return ''
    }
  },
  watch: {
    data: {
      handler(val) {
        !this.originData && (this.originData = cloneDeep(val))
      }
    },
    showTipModal(val){
      if (!isServer && val) {
        this.$nextTick(() => {
           const target = this.$el?.querySelector('.tip-modal')
           this.sendDataSpm('pageview', target, { force: true })
        })
      }
    }
  }
}
</script>

<style lang="scss">
.edit-drawer {
  .klk-drawer-content {
    overflow: hidden;
  }
  button {
    height: auto;
  }
}
</style>

<style lang="scss" scoped>
.edit-drawer {
  &-close {
    width: 20px;
    height: 20px;
    cursor: pointer;
  }

  &-header {
    padding: 0 32px;
    position: absolute;
    top: 0;
    left: 0;

    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    height: 56px;
    border-bottom: 1px solid $color-border-normal;
    background: $color-bg-widget-normal;
    z-index: 8000;

    h3 {
      font-weight: $fontWeight-bold;
      font-size: 16px;
      line-height: 23px;
    }
  }

  &-content {
    position: relative;
    // padding-bottom: 32 - 24 + 60
    padding: 80px 32px 68px;
    height: 100%;
    overflow-y: auto;
  }

  &-terms-box {
    @include font-caption-m-regular;
    color: $color-neutral-700;
  }

  &-footer {
    padding: 0 32px;
    position: absolute;
    bottom: 0;
    left: 0;

    display: flex;
    align-items: center;
    justify-content: flex-end;
    width: 100%;
    height: 60px;
    border-top: 1px solid $color-border-normal;
    background: $color-bg-widget-normal;
  }
}

.tip-modal {

  &.klk-modal-wrapper {
    background: rgba(0, 0, 0, 0.38);

    .modal-content {
      line-height: 21px;
    }

    .footer-box {
      margin-top: 20px;
      text-align: right;

      .klk-button {
        margin-right: 16px;

        &:last-of-type {
          margin-right: 0;
        }
      }
    }
  }
}
</style>
