import { mapUtils } from "@klook/map"
import { handleIHTrack } from "../utils/inHouseTrack";

export default {
  inject: {
    customAxios: { default: () => null },
    translateI18n: { default: () => null }
  },
  data() {
    return {
      searchData: []
    }
  },
  methods: {
    getGeomByIdList(params) {
      return this.customAxios.$get('/v1/experiencesrv/area/strative_area_service/get_geom_by_id_list', {
        params
      })
    },
    initMarker(data, isTemporary = false, options = {}) {
      const {
        location,
        icon,
        address_desc,
        area_id,
        tips = {},
        pick_up_type,
        group_icon_v2,
      } = data
      const {
        text = '',
        bg_color = '',
        border_color = '',
        font_color = '',
        border_width = ''
      } = tips
      const poptip = this.isArea(data) ? text : address_desc
      // const style = this.isArea(data) ? { backgroundColor: bg_color, borderColor: border_color, color: font_color } : {}
      const style = {
        backgroundColor: bg_color,
        borderColor: border_color,
        color: font_color,
        borderWidth: border_width + 'px'
      }
      const groupIcon = group_icon_v2 || { select: icon, unselect: icon }
      return {
        data,
        location,
        isTemporary,
        center: mapUtils.formatLatLng(location),
        poptip,
        tips_text: text,
        options: {
          anchor: 'bottom',
        },
        area_id,
        style,
        pick_up_type,
        groupIcon,
        tracker: {
          type: 'module',
          spm: 'ViewPickUpPoint',
          exposure: true,
          query: {
            ext: JSON.stringify({
              Type: isTemporary ? 'customed' : 'fixed'
            }),
          },
        },
        ...options
      }
    },
    isArea(marker) {
      const { pick_up_type } = marker
      return pick_up_type === 2
    },
    initFenceData(fence, location = []) {
      const list = fence.reduce((accu, curr) => {
        const area_id = curr.area_id
        const geojson = location.find((item) => item.area_id === area_id)
        const area_properties = geojson?.area_properties || {}
        const strokeColor = area_properties.stroke || ''
        const fillColor = area_properties.fill || ''
        const coordinates = curr?.geom?.coordinates || []
        coordinates.forEach((polygonList) => {
          accu.push({
            polygonList: curr?.geom?.type === 'Polygon' ? [polygonList] : polygonList,
            polygonConfig: {
              strokeColor,
              fillColor
            }
          })
        })
        return accu
      }, [])
      return Object.freeze(list)
    },
    getValidAreaId(areas = []) {
      return areas.reduce((acc, item) => {
        const id = item.area_id
        if (!!id) {
          acc.push(id)
        }
        return acc
      }, [])
    },
    getBindTracker(tracker) {
      return handleIHTrack(tracker);
    }
  }
}
