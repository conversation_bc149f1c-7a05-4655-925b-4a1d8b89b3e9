import { getPickUpScopeFormItem, isMapPointFreeText } from '@src/utils'

import FakeSelect from '@src/components/fake-select/index.vue'

export default {
  props: {
    data: {
      type: Object,
      default: () => ({}),
    },
  },
  components: {
    FakeSelect
  },
  computed: {
    // 只有一个点的时候，自动填充数据
    autoSelect() {
      const jsonDatas = this.jsonDatas
      return jsonDatas.length === 1 && jsonDatas[0].pick_up_type === 1
    },
    packageId() {
      return this.data?.package_id ?? 0
    },
    jsonDatas() {
      return this.data?.json_datas ?? []
    },
    inputVal() {
      return this.formItemValue ? this.formItemValue.location_name : ''
    },
    formItemValue() {
      return getPickUpScopeFormItem(this.value)
    },
    tipsText() {
      // 自定义的点
      if (this.formItemValue && isMapPointFreeText(this.formItemValue)) {
        return this.$t('106260')
      }
      if (this.formItemValue && this.formItemValue.data_type === 2) {
        return this.$t('161194')
      }
      return ''
    },
    autoGenForm() {
      const { jsonDatas = [], formItemValue = {} } = this
      const { area_id: id, location: lat } = formItemValue
      const data = jsonDatas.find((item) => {
        const { area_id, location } = item
        if (id) {
          return area_id === id
        }
        return location === lat
      })
      return data?.auto_gen_form?.items ?? []
    },
    info() {
      if (this.data && this.data.style && this.data.style.pop_over) {
        const data = this.data.style.pop_over
        return {
          title: data.sub_title,
          description: data.description,
          point_list: data.point_list
        }
      }

      return null
    }
  }
}