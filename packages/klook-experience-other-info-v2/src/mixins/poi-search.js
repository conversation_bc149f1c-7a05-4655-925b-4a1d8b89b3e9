import apis from '@src/utils/apis'
import cloneDeep from 'lodash/cloneDeep'

export default {
  props: {
    mapData: {
      type: Array,
      default: () => []
    }
  },
  inject: {
    customAxios: { default: () => null }
  },
  data() {
    return {
      cancelToken: null,
      cancelDetailToken: null,
      searchResultList: [],
      keywords: '',
      currentLoadingIndex: -1,
      isNotMatch: false
    }
  },
  computed: {
    defaultResult() {
      const mapData = cloneDeep(this.mapData || [])
      return mapData.reduce((acc, curr) => {
        const { pick_up_type } = curr
        if (pick_up_type === 1) {
          acc.push(this.initResult(curr))
        }
        return acc
      }, [])
    },
    renderList() {
      return this.keywords ? this.searchResultList : this.defaultResult
    },
    // isNotMatch() {
    //   return this.keywords && !this.searchResultList.length
    // },
    isSearchNone() {
      // 只有 scope 才显示 freetext
      return !this.searchResultList || !this.searchResultList.length
    }
  },
  watch: {
    keywords: {
      immediate: true,
      handler(v) {
        this.$emit('keywordsChange', v)
      }
    }
  },
  methods: {
    initResult(data) {
      const { location_name: title, address_desc: sub_title, google_place_id: place_id } = data
      return {
        title,
        sub_title,
        place_id,
        is_default: true,
        detail_data: { ...data }
      }
    },
    async getSearchFun(params) {
      if (this.cancelToken) {
        this.cancelToken.cancel('cancel')
      }

      this.cancelToken = this.customAxios?.CancelToken?.source?.() || null
  
      const res = await this.customAxios.$get(apis.autocomplete, {
        params,
        cancelToken: this.cancelToken?.token
      })

      const result = res?.result?.suggestions ?? []

      return result.map((item) => {
        return {
          ...item
        }
      })
    },
    async getPoiDetail(params) {
      if (this.cancelDetailToken) {
        this.cancelDetailToken.cancel('cancel')
      }

      this.cancelDetailToken = this.customAxios?.CancelToken?.source?.() || null
  
      const res = await this.customAxios.$get(apis.getPlaceDetail, {
        params,
        cancelToken: this.cancelDetailToken?.token
      })
    
      return res?.result ?? null
    }
  }
}