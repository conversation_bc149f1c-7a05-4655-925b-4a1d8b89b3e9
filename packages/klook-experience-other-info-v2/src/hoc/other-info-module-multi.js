import { getTrackingInfos,TRAVELLER_TYPE, CONTACT_TYPE, composeCloneData } from '@src/utils'
import childEmitMixin from '@src/utils/mixins/child-emit'


export default function otherInfoModuleMultiHoc(WrapperComponent) {
  return {
    name: 'OtherInfoModuleMultiHoc',
    inheritAttrs: false,
    mixins: [childEmitMixin],
    props: {
      data: {
        type: Array,
        default: () => [],
      },
      originFormInfos: {
        type: Array,
        default: () => []
      }
    },
    data() {
      return {
        moduleList: [],
        cloneData: []
      }
    },
    render(h) {
      const args = {
        attrs: {
          ...{ ...this.$props, data: this.cloneData },
          ...this.$attrs,
        },

        on: {
          ...this.$listeners,
          add: this.addModule,
          remove: this.removeModule
        },

        scopedSlots: this.$scopedSlots,
        ref: 'wrapped',
      }

      return h(WrapperComponent, args)
    },
    computed: {
      isTraveller() {
        return TRAVELLER_TYPE === this.data.section_type
      },
      isContact() {
        return this.data.section_type === CONTACT_TYPE
      }
    },
    watch: {
      data: {
        handler(newVal) {
          if (newVal && Array.isArray(newVal) && newVal.length > 0) {
            composeCloneData(newVal, (data) => this.cloneData = data)
          }
        },
        immediate: true
      }
    },
    methods: {
      addModule(index, data) {
        this.moduleList[index] = data
      },
      removeModule(index) {
        this.moduleList[index] = null
      },
      async validate() {
        let isValid = true
        for (const moduleItem of this.moduleList) {
          if (moduleItem && !(await moduleItem.validate())) {
            // isValid && this.handleValidateError(moduleItem)
            isValid = false
          }
        }
        return isValid
      },
      handleValidateError(moduleItem) {
        const { section_type } = moduleItem.data

        if (section_type === TRAVELLER_TYPE) return
        switch(section_type) {
          case 4: // 联系人
            this.$toast(this.$t('73646'))
          break
          default:
            this.$toast(this.$t('17440'))
        }
      },
      async getValidateKeys() {
        let keyList = []
        let isValid = true
        for (let i = 0; i < this.moduleList.length; i += 1) {
          const moduleItem = this.moduleList[i]
          const childKeyList = moduleItem ? await moduleItem.getValidateKeys() : []

          if (moduleItem && !(await moduleItem.validate())) {
            // isValid && this.handleValidateError(moduleItem)
            isValid = false
          }

          if (childKeyList.length > 0) {
            const sectionType = this.data[i].section_type || 1
            keyList = keyList.concat(childKeyList.map(childKey => `${sectionType}-${childKey}`))
          }
        }
        return keyList
      },
      getModifiedData() {
        return this.moduleList.map(moduleItem => (moduleItem ? moduleItem.getModifiedData() : null))
      },
      getTrackingInfos() {
        // 埋点需要，查找出没填和报错的other-info，以xxxx-{xxxx}的形式聚集在一起
        try {
          const modifiedData = this.getModifiedData()
          return getTrackingInfos(modifiedData)
        } catch (error) {
          console.log('other-info tracking error', error)
          return null
        }
      },
      clearValidate() {
        this.moduleList.forEach(moduleItem => moduleItem && moduleItem.clearValidate())
      },
      cacheData() {
        this.moduleList.forEach(moduleItem => moduleItem && moduleItem.cacheData())
      },
      clearCache() {
        this.moduleList.forEach(moduleItem => moduleItem && moduleItem.clearCache())
      },
      recoverData() {
        this.moduleList.forEach(moduleItem => moduleItem && moduleItem.recoverData())
      },
      refreshTraveller() {
        this.moduleList.forEach(moduleItem => moduleItem && moduleItem.refreshTraveller())
      },
      clearGlobalErrorStatus() {
        this.moduleList.forEach(moduleItem => moduleItem && moduleItem.clearGlobalErrorStatus())
      }
    },
  }
}
