import cloneDeep from 'lodash/cloneDeep'
import inhouseMixin from '@src/utils/mixins/inhouse'
import childEmitMixin from '@src/utils/mixins/child-emit'
import eventBus from '@src/utils/event-bus'
import {
  cacheTravellerInfo,
  getTravellerInfoFromCache,
  INIT_TRAVELLER_ID,
  getUpdateTravellerListParams
} from '@src/utils'

import { getNewTravellerId, getTravellerCache } from '@src/utils/traveller-cache'

import {
  SHIPMENT_TYPE,
  TRAVELLER_TYPE,
  CONTACT_TYPE,
  cleanDynamicFormList,
  copyOperation,
  replaceMobile,
  formatOtherInfoItems,
  backFillData,
  checkDynamicFormList,
  diffDynamicFormList,
} from '@src/utils'

let countryList = null


export default function otherInfoFormHoc(WrapperComponent) {
  return {
    name: 'OtherInfoFormHoc',
    inheritAttrs: false,
    mixins: [inhouseMixin, childEmitMixin],
    props: {
      originFormInfos: {
        type: Array,
        default: () => [],
      },
      sectionType: {
        type: Number,
        default: 1,
      },
      data: {
        type: Object,
        default: null,
      },
      // 只渲染表单就传这个，不传 data，如果同时传的话，会优先使用 data
      // 在使用这个字段的时候，emit出来的也是一个数组
      formData: {
        type: Array,
        default: null,
      },
      travellerList: {
        type: Array,
        default: () => [],
      },
      shipmentList: {
        type: Array,
        default: () => [],
      },
      useNewFeature: {
        type: Boolean,
        default: false,
      },
      formProps: {
        type: Object,
        default: () => {},
      },
      verifiedMobileCode: {
        type: Object,
        default: () => {},
      },
      getAxiosFunc: {
        type: Function,
        default: () => undefined
      },
      mapType: {
        type: String,
        default: 'google'
      }
    },
    provide() {
      return {
        mapType: this.mapType,
        customAxios: this.customAxios,
      }
    },
    data() {
      return {
        travellerId: -1,
        shipmentId: -1,
        cancleTravellerId: -1, // 反选travellerId
        otherInfoDataCreateCached: null, // 缓存，目前用于新建的联系人的值
        otherInfoData: {
          data: [],
        },
        // 用于数据重置
        cache: {
          isValid: false,
          travellerId: null,
          shipmentId: null,
          formData: [],
          checkboxValue: true,
        },
        // 用于新版出行人
        isTravellerInfoSaved: false, // 出行人保存了之后就不需要数据重置了
        travellerListInited: false,
        travellerListCached: [],
        recordOtherinfoData: {},
        filledCount: 0,
        noNeedUpdateData: false, // 保存数据后，是否需要更新dataListd的标识
        savePassFlag: false, // mobile购物车多个订单合并购买，是否保存成功的标识
      }
    },
    computed: {
      customAxios() {
        return this.getAxiosFunc()
      },
      isContact() {
        return this.sectionType === CONTACT_TYPE
      },
      isTraveller() {
        return this.sectionType === TRAVELLER_TYPE
      },
      isShipment() {
        return this.sectionType === SHIPMENT_TYPE
      },
      useFormData() {
        return !!this.formData && !this.data
      },
      otherInfoItems() {
        const { info_items } = this.data || {}
        const data = this.useFormData ? this.formData : info_items
        return data || []
      },
      isCheckboxCreate() {
        // 通过idInUse是否有值，来判断checkbox是否是新建的情况
        const { custom_info } = this.data || {}
        const { address_id_use = 0, traveler_id_use = 0 } = custom_info || {}
        const idUsed = this.isShipment ? address_id_use : traveler_id_use
        return !idUsed
      },
      checkboxType() {
        return this.isCheckboxCreate ? 'CreaterInfo' : 'UpdateInfo'
      },
      shouldHideCheckbox() {
        // 当邮寄信息列表超过 30 个时，不显示新建
        if (this.isShipment) {
          return this.isCheckboxCreate && this.shipmentList.length >= 30
        }

        // 当出行人信息 hide_create_checkbox 为 true，则不显示新建
        const { bottom_info } = this.data || {}
        const { hide_create_checkbox } = bottom_info || {}
        return this.isCheckboxCreate && hide_create_checkbox
      },
      checkboxContent() {
        if (this.shouldHideCheckbox) {
          return ''
        }

        const { bottom_info } = this.data || {}
        const { save_content = '', update_content = '' } = bottom_info || {}
        return this.isCheckboxCreate ? save_content : update_content
      },
      preProcessedOtherInfoItems() {
        let result = cloneDeep(this.otherInfoItems)

        // 数据预处理：有些字段为空的时候后端没有返回，但是这些字段又是v-model的值，所以要加上
        formatOtherInfoItems(result)

        // 数据预处理：对于联系人信息，mobile里面只有field_key，这里加上areaCode
        replaceMobile(result)

        // 数据预处理，把多选checkbox的options复制到operation里面去
        copyOperation(result)

        return result
      },
    },
    watch: {
      otherInfoItems: {
        immediate: true,
        handler() {
          this.generateOtherInfoData()
        },
      },
      travellerList: {
        immediate: true,
        handler(newVal) {
          if (newVal && newVal.length && !this.travellerListInited) {
            this.travellerListInited = true
            this.travellerListCached = [...newVal]
          }
        },
      },
      verifiedMobileCode: {
        immediate: true,
        handler(newVal) {
          if (newVal && Object.keys(newVal).length > 0 && this.isContact) {
            this.replaceContactMobileAndCode(newVal)
          }
        },
      },
    },
    render(h) {
      const data = this.useFormData ? { info_items: this.formData || [] } : this.data
      const args = {
        attrs: {
          ...this.$props,
          ...this.$attrs,
          data,
          otherInfoData: this.otherInfoData,
          travellerList: this.travellerListCached,
          shipmentList: this.shipmentList,
          travellerId: this.travellerId,
          shipmentId: this.shipmentId,
          checkboxType: this.checkboxType,
          checkboxContent: this.checkboxContent,
          otherInfoBlankForm: this.preProcessedOtherInfoItems,
          cancleTravellerId: this.cancleTravellerId,
          recordOtherinfoData: this.recordOtherinfoData,
          filledCount: this.filledCount,
          noNeedUpdateData: this.noNeedUpdateData,
          formProps: this.formProps,
        },
        on: {
          ...this.$listeners,
          change: this.handleChange,
          input: this.handleInput,
          validate: this.handleValidate,
          selectTraveller: this.selectTraveller,
          selectShipment: this.selectShipment,
          updateCheckbox: this.updateCheckbox,
          handleUpdateTravellerList: this.handleUpdateTravellerList,
          changeItemSelectStatus: this.changeItemSelectStatus,
          validatePass: this.validatePass,
          changeTravellerId: this.changeTravellerId,
        },
        scopedSlots: this.$scopedSlots,
        ref: 'wrapped',
      }

      return data ? h(WrapperComponent, args) : null
    },
    mounted() {
      eventBus.$on('add-traveller', this.handleAddTravellerFromBus)
    },
    beforeDestroy() {
      eventBus.$off('add-traveller')
      countryList = null
    },
    methods: {
      async replaceContactMobileAndCode({ mobile, country_number }) {
        if (!countryList) {
          const codeManger = await import('@src/utils/code')
          const { countryList: originCountryList = [] } = codeManger.default
          countryList = originCountryList
        }

        const attr = this.otherInfoData.data.find(item => item.field_key === 'mobile')

        if (attr) {
          attr.content = mobile

          const country = countryList.find(item => item.country_number === country_number)

          if (country) {
            attr.operation = [country]
          }
        }

        this.updateTravellerInfo()
      },
      async updateTravellerInfo() {
        const updateTravellerListFunc = this.$attrs['update-traveller-list-func']

        if (updateTravellerListFunc) {
          const res = await updateTravellerListFunc({
            traveller_id: this.travellerId,
            items: getUpdateTravellerListParams(this.otherInfoData.data),
          })

          if (res.success) {
            // 同步更新缓存数据
            this.handleUpdateTravellerList(res.result, this.otherInfoData.data)
          }
        }
      },
      changeTravellerId(id) {
        this.travellerId = id
        let result = cloneDeep(this.preProcessedOtherInfoItems)
        const data = getTravellerCache(id)

        if (data) {
          result = backFillData(result, data.items)
          this.recordOtherinfoData[id]['value'] = result
          this.otherInfoData = {
            data: result,
          }
        }
      },
      getRecordOtherinfoData() {
        delete this.recordOtherinfoData[INIT_TRAVELLER_ID]
        return this.recordOtherinfoData
      },
      validatePass() {
        const res = this.recordOtherinfoData[this.travellerId]
        if (res && res.status === 'init') {
          this.recordOtherinfoData[this.travellerId] = {
            value: this.otherInfoData.data,
            status: 'pass',
          }
          this.dealDataFilledCount()
        }
      },
      changeItemSelectStatus(id, status) {
        if (!status) {
          this.cancleTravellerId = id
          this.travellerId = 'NA'
          if (this.recordOtherinfoData[id]['status'] === 'pass') {
            this.dealDataFilledCount('minus')
          }
          delete this.recordOtherinfoData[id]
        } else {
          this.cancleTravellerId = 'NA'
          this.selectTraveller(id)
        }
      },
      openShipment() {
        this.$refs.wrapped.openShipment()
      },
      validate() {
        return this.$refs.wrapped.validate()
      },
      selectTraveller(id) {
        this.noNeedUpdateData = false
        this.travellerId = id
        this.updateCustomInfo('traveler_id_use', id)
        // 每次切换的时候需要把同步checkbox选中，如果hide则要取消选中
        this.updateCheckbox(!this.shouldHideCheckbox)

        // 产品说，其它模块在点击的时候也要刷缓存
        this.updateTravellerList(id)

        this.generateOtherInfoData()
      },
      selectShipment(id) {
        this.shipmentId = id
        this.updateCustomInfo('address_id_use', id)
        // 每次切换的时候需要把同步checkbox选中，如果hide则要取消选中
        this.updateCheckbox(!this.shouldHideCheckbox)
        this.generateOtherInfoData()
      },
      updateCustomInfo(field, id) {
        if (this.data && this.data.custom_info) {
          this.$set(this.data.custom_info, field, id)
        }
      },
      updateCheckbox(val) {
        // useNewFeature 没有checkbopx的选项
        if (this.data && this.data.bottom_info) {
          this.$set(this.data.bottom_info, 'is_checkbox_selected', !!val)
        }
      },
      handleChange() {
        this.$emit('change', this.getModifiedData())
      },
      handleInput() {
        this.$emit('input')
      },
      handleValidate(...args) {
        this.$emit('validate', ...args)
      },

      /**
       * other-info 处理
       */
      generateOtherInfoData() {
        // 新建的情况要使用缓存
        if (this.travellerId === 0 && !!this.otherInfoDataCreateCached) {
          this.otherInfoData = this.otherInfoDataCreateCached
          return
        }
        let result = cloneDeep(this.preProcessedOtherInfoItems)

        // 回填数据-邮寄信息
        if (this.shipmentId > 0) {
          const data = this.getSelectedShipmentData()
          result = backFillData(result, data.items)
        }

        // 回填数据-出行人列表
        const data = this.getSelectedTravellerData()
        result = backFillData(result, data.items)

        if (this.travellerId === 0) {
          if (this.isContact) {
            // 对于联系人信息，this.data里面有默认值，此时如果选中添加的话，需要清空里面的值
            cleanDynamicFormList(result)
          }

          this.otherInfoDataCreateCached = { data: result }
        }

        // 如果只有一个sku，就先清除掉所有的record数据
        if (this.data.count === 1) {
          this.recordOtherinfoData = {}
          this.dealDataFilledCount('minus')
        }
        // 这里由于klk-form表单的model不能传array，所以用data包一层
        this.otherInfoData = { data: result }

        if (!this.recordOtherinfoData[this.travellerId]) {
          // 兼容改变travellerId后触发重新passValidate事件
          this.recordOtherinfoData[this.travellerId] = { value: result, status: 'init' }
        }
        // 触发一次change事件
        this.handleChange()
      },
      getModifiedData() {
        if (this.useFormData) {
          return this.shimItems(this.otherInfoData.data)
        }

        // 提交数据的时候要处理 fakeTravellerId 的情况
        let customInfo = this.data && this.data.custom_info
        if (customInfo && customInfo.traveler_id_use < 0) {
          customInfo = {
            ...customInfo,
            traveler_id_use: 0,
          }
        }
        const returnData = {
          ...this.data,
          custom_info: customInfo,
          // 这里要把更新的内容替换掉
          info_items: this.shimItems(this.otherInfoData.data),
        }

        if (this.isTraveller) {
          returnData['real_info_items'] = this.getRealInfoItems()
          returnData['record_otherinfo_data'] = this.getRecordOtherinfoData()
        }

        return returnData
      },
      getRealInfoItems() {
        let info_items = cloneDeep(this.originFormInfos) || []
        const id = this.data.id

        if (this.isTraveller) {
          const result = []
          info_items.forEach(item => {
            if (item.id === id) {
              result.push(item)
            }
          })

          if (result.length) {
            const recordOtherinfoData = this.getRecordOtherinfoData()
            Object.keys(recordOtherinfoData).forEach((key, index) => {
              result[index] = this.shimItems(recordOtherinfoData[key]['value'])
            })
          }

          info_items = result
        }

        return info_items
      },
      shimItems(items) {
        // 修建 items，把options设置为空
        const copy = cloneDeep(items || [])
        copy.forEach(item => {
          item.options = []
          item.operation = this.shimItems(item.operation)
        })
        return copy
      },
      checkIsModified() {
        return checkDynamicFormList(this.preProcessedOtherInfoItems, this.otherInfoData.data)
      },
      getDiffItems() {
        const result = []
        diffDynamicFormList(this.preProcessedOtherInfoItems, cloneDeep(this.otherInfoData.data), result)
        return {
          ...this.data,
          info_items: result,
        }
      },
      getSelectedShipmentData() {
        const res = this.shipmentList.find(item => item.address_id === this.shipmentId)
        return res || {}
      },
      getSelectedTravellerData() {
        const res = this.travellerListCached.find(item => item.traveller_id === this.travellerId)
        return res || {}
      },
      cacheData() {
        // 如果出行人保存了之后，就不需要数据重置了
        if (this.isTravellerInfoSaved && !this.isTraveller) {
          return
        }

        // 备份数据
        // 区分出行人和邮寄信息/其他信息
        const { bottom_info } = this.data || {}
        const { is_checkbox_selected } = bottom_info || {}
        this.cache = {
          isValid: true,
          travellerId: this.travellerId,
          shipmentId: this.shipmentId,
          formData: cloneDeep(this.otherInfoData.data),
          checkboxValue: !!is_checkbox_selected,
        }

        if (this.isTraveller) {
          this.cache = {
            ...this.cache,
            data: this.$refs.wrapped?.cacheDataList(),
          }
        }
      },
      clearCache() {
        this.cache.isValid = false
        this.savePassFlag = true
      },
      clearGlobalErrorStatus() {
        this.$refs.wrapped.clearGlobalErrorStatus && this.$refs.wrapped.clearGlobalErrorStatus()
      },
      clearValidate() {
        this.$refs.wrapped.clearValidate()
      },
      recoverData() {
        // 如果出行人保存了之后，就不需要数据重置了
        if (this.isTravellerInfoSaved && !this.isTraveller) {
          return
        }

        // 恢复数据
        if (this.cache.isValid) {
          const { travellerId, shipmentId, formData, checkboxValue, data } = this.cache
          this.travellerId = travellerId
          this.shipmentId = shipmentId
          this.updateCheckbox(checkboxValue)

          if (this.isTraveller) {
            const list = data.map(item => item.key)
            const { replaceDataList, clearSelectorList, setSelectorList } = this.$refs.wrapped || {}

            replaceDataList && replaceDataList(data)
            if (!this.savePassFlag) {
              this.recordOtherinfoData = {}
              this.filledCount = this.data.filledCount = 0
              clearSelectorList && clearSelectorList()
            } else {
              setSelectorList && setSelectorList(list)
              this.filledCount = this.data.filledCount = list.length
              this.recordOtherinfoData = data.reduce((p, o) => {
                const { value, key } = o
                p[key] = {
                  value,
                  status: 'pass',
                }
                return p
              }, {})
            }
          } else {
            this.otherInfoData = {
              data: formData,
            }
          }
        }
      },
      refreshTraveller() {
        // 出行人信息模块要重刷缓存
        if (this.isTraveller) {
          this.updateTravellerList(this.travellerId)
          // this.generateOtherInfoData()
        }
      },
      updateTravellerList(id) {
        // 非新增的情况需要做2件事：
        // 1.拿sessionstorage里面的数据更新item
        // 2.更新travellerListCached
        const index = this.travellerListCached.findIndex(item => item.traveller_id === id)
        if (index >= 0) {
          const item = getTravellerInfoFromCache(this.travellerListCached[index])
          this.travellerListCached.splice(index, 1, item)
        }
      },
      handleAddTravellerFromBus(data) {
        const { traveller_id } = data
        if (traveller_id) {
          const target = this.travellerListCached.find(item => item.traveller_id === traveller_id)
          if (!target) {
            // 新加的出行人要排在默认出行人后面
            // 备注： 可能存在没有默认出行人的场景
            if (this.travellerListCached.length) {
              const index = this.travellerListCached.findIndex(item => item.default_traveller)
              if (index === -1) {
                this.travellerListCached.unshift(cloneDeep(data))
              } else {
                // 如果有默认出行人，肯定是第一位
                this.travellerListCached.splice(index + 1, 0, cloneDeep(data))
              }
            } else {
              this.travellerListCached.push(cloneDeep(data))
            }
          }
        }
      },
      handleUpdateTravellerList(resData, drawerData) {
        // 如果后端返回的traveller_id为0，则表示后端保存出错了
        // 此时前端使用一个假id
        if (resData.traveller_id === 0) {
          resData.traveller_id = getNewTravellerId()
        }

        this.isTravellerInfoSaved = true
        cacheTravellerInfo(resData)

        const { traveller_id } = resData
        const target = this.travellerListCached.find(item => item.traveller_id === traveller_id)

        if (target) {
          this.updateTravellerList(traveller_id)
          this.recordOtherinfoData[traveller_id]['value'] = drawerData
        } else {
          eventBus.$emit('add-traveller', resData)
          if (this.isContact) {
            // 选中新增的这个数据
            this.travellerId = traveller_id
            this.updateCustomInfo('traveler_id_use', traveller_id)
          } else {
            const count = this.data.count
            const keys = Object.keys(this.getRecordOtherinfoData())
            if (keys.length < count) {
              this.travellerId = traveller_id
              this.noNeedUpdateData = false
            } else {
              this.noNeedUpdateData = true
            }
          }
        }

        this.otherInfoData = {
          data: drawerData,
        }

        if (this.isContact) {
          this.handleChange()
        }
      },
      dealDataFilledCount(action = 'add') {
        let count = this.filledCount
        if (action !== 'add' && count > 0) {
          count -= 1
        } else if (action === 'add' && this.data.count > count) {
          count += 1
        }
        this.filledCount = this.data.filledCount = count
      },
    },
  }
}
