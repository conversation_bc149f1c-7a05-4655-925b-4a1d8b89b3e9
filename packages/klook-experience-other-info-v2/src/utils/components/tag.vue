<template>
  <div v-if="calcList && calcList.length" class="tagging-wrap" :class="calcTagType" :style="wrapStyle">
    <div class="tagging-box">
      <tag v-for="(item, i) in calcList" :key="i" v-bind="{ ...item, maxWidth }" :class="[item.class, getSpecClass(item)]" />
    </div>
  </div>
</template>

<script>
import tag from './tag-item.vue'

// 将hex颜色转成rgb
export const hexToRgba = (hex, opacity) => {
  const red = parseInt('0x' + hex.slice(1, 3))
  const green = parseInt('0x' + hex.slice(3, 5))
  const blue = parseInt('0x' + hex.slice(5, 7))
  const RGBA = `rgba(${red}, ${green}, ${blue}, ${opacity})`
  return RGBA
}

const dec2hex = function ({ value, item }) {
  const v = value || '' // #123456 约定为六位rgb
  let alpha = typeof item?.background_alpha === 'number' ? item?.background_alpha : 0
  alpha = parseInt((255 - alpha * 255 / 100)) // background_alpha 背景透明度, 0:不透明；100:透明
  const rgba = hexToRgba(v, alpha)
  return rgba
}

const DefStyleClass = 'middle-style'
const tagTypeMaps = [{ key: 1, value: 'middle-style' }, { key: 2, value: 'small-style' }]

const iconKeyMaps = [
  { key: 'icon', mapKey: 'src' },
  { key: 'desc', mapKey: 'desc' } // 点击后显示的文案
]
const creditsIconKeyMaps = [
  { key: 'left_icon_url', mapKey: 'src' }
]
const tagKeyMaps = [
  { key: 'tag_key', mapKey: 'tag_key' },
  { key: 'icon', mapKey: 'leftIcon', deepKeys: creditsIconKeyMaps },
  {
    key: 'type',
    mapKey: 'class',
    getterValue: ({ value }) => {
      const d = tagTypeMaps
      return d.find((o) => o.key === value)?.value || DefStyleClass
    }
  },
  { key: 'text', mapKey: 'text' },
  { key: 'text_color', mapKey: 'color' },
  { key: 'background_color', mapKey: 'backgroundColor', getterValue: dec2hex },
  // { key: 'background_alpha', mapKey: 'background_alpha' },
  { key: 'border_color', mapKey: 'borderColor' },
  { key: 'radius', mapKey: 'borderRadius', getterValue: ({ value }) => { return `${value}px` } },
  { key: 'right_action', mapKey: 'right_icon', deepKeys: iconKeyMaps }
]

export default {
  name: "TagIndex",
  props: {
    pkg: {
      type: Object,
      default: null
    },
    tagType: {
      type: String,
      default: '' // small-style -> 2 | middle-style -> 1
    },
    maxWidth: {
      type: String,
      default: 'auto'
    },
    line: {
      type: Number,
      default: 0
    },
    tagHeight: {
      type: Number,
      default: 0
    },
    list: {
      type: Array,
      default: () => []
    }
  },
  computed: {
    wrapStyle() {
      const { line, tagHeight: customHeight, typeDefined } = this
      const tagType = this.calcTagType
      const gap = tagType === 'small-style' ? 4 : 8
      if (!line) {
        return {}
      }
      const tagHeight = customHeight || typeDefined.tagHeight[tagType]
      const maxHeight = `${tagHeight * line + (line - 1) * gap}px`
      return {
        maxHeight
      }
    },
    calcTagType() {
      return this.calcList?.[0]?.class || this.tagType || DefStyleClass
    },
    calcList() {
      const arr = Array.isArray(this.list) ? this.list : []
      return arr.map((item) => {
        const obj = this.getMapsItem(item, tagKeyMaps)
        return obj
      })
    },
    calcTrack() {
      const def = {
        type: 'item',
        spm: 'Savings_Detail'
      }
      return def
    }
  },
  components: {
    tag
  },
  data() {
    return {
      typeDefined: { tagHeight: { 'small-style': 20, 'middle-style': 24 } }
    }
  },
  methods: {
    getSpecClass(item) {
      const { tag_key, leftIcon } = item || {}
      return (tag_key === 'credits' && leftIcon?.src) ? 'credits-style' : ''
    },
    getMapsItem(item, maps) {
      return maps.reduce((o, o2) => {
        const { key, mapKey, deepKeys, getterValue } = o2
        if (deepKeys && typeof item[key] === 'object') {
          o[mapKey] = this.getMapsItem(item[key], deepKeys)
        } else {
          const value = item[key]
          o[mapKey] = getterValue ? getterValue({ value, item }) : value
        }
        return o
      }, {})
    }
  }
}

export const getTaggingExtraTrackObj = (list) => {
  const arr = list || []
  const extra = arr.reduce((o, o2) => {
    o.TagIDList.push(o2?.track?.tag_id || '')
    o.TagKeyList.push(o2?.track?.tag_key || '')
    return o
  }, { TagIDList: [], TagKeyList: [] })
  return extra
}

export const getTestTags = (num = 9) => {
  const item = {
    type: 1, // tag icon 样式类型, 具体到一个 模块中是写死的。1:middle; 2:small
    text: 'Display tag',
    text_color: '#212121',
    background_color: '#eeeeee',
    background_alpha: 0, // 背景透明度, 0:不透明；100:透明
    border_color: '#eeeeee', // 边框颜色
    radius: 4, // 圆角值
    right_action: {
      icon: '',
      desc: ''
    },
    tag_key: '', // 辅助字段，tag key
    group_keys: ['product_tag', 'promotion_tag'] // tag_key 的父级 key 列表
  }
  const arr = new Array(num).fill(0).map((_, i) => {
    return {
      ...item,
      tag_key: `key${i}`
    }
  })

  return arr
}
</script>

<style lang="scss" scoped>
.tagging-wrap{
  // max-height: 48px;
  overflow: hidden;

  &.ta-right{
    .tagging-box{
      justify-content: flex-end;
    }
  }
  .tagging-box {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: -8px;
    // gap: 8px 8px;
    .tagging-tag{
      margin: 0 8px 8px 0;
      &:last-of-type{
        margin-right: 0;
      }
    }
  }
}
.small-style.tagging-wrap{
  .tagging-box {
    // gap: 4px 4px;
    margin-bottom: -4px;
    .tagging-tag{
      margin: 0 4px 4px 0;
      &:last-of-type{
        margin-right: 0;
      }
    }
  }
}
.one-tag.tagging-wrap{
  .tagging-box {
    margin-bottom: 0;
    ::v-deep .tagging-tag{
      margin: 0;
    }
  }
}
</style>
