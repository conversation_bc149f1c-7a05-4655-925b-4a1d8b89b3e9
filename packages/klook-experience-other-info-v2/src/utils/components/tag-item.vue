<template>
  <div class="tagging-tag">
    <div class="tagging-tag_custom" :style="customStyle">
      <span v-if="leftIcon && leftIcon.src" class="tagging-tag_tips">
        <img class="tagging-tag_icon left-icon" v-bind="leftIcon" />
      </span>
      <span class="tagging-tag_text">
        <span>{{ $attrs.text }}</span>
      </span>
      <template v-if="rightIcon && rightIcon.src">
        <KlkPoptip v-if="platform === 'desktop'" class="tagging-tag_tips" :title="rightIcon.desc" dark>
          <img class="tagging-tag_icon right-icon" v-bind="rightIcon" />
        </KlkPoptip>
        <span v-else class="tagging-tag_tips">
          <img class="tagging-tag_icon right-icon" v-bind="rightIcon" @click="clickRightIcon(rightIcon.desc)" />
        </span>
      </template>
    </div>
  </div>
</template>

<script>
import Vue from 'vue'
import KlkPoptip from '@klook/klook-ui/lib/poptip'
import KlkModal from '@klook/klook-ui/lib/modal'
import '@klook/klook-ui/lib/styles/components/poptip.scss'
import '@klook/klook-ui/lib/styles/components/modal.scss'

Vue.use(KlkModal)

export default {
  name: 'TagItem',
  components: {
    KlkPoptip,
  },
  computed: {
    customStyle() {
      const { class: myClass, maxWidth, color, borderColor, backgroundColor, borderRadius } = this.$attrs
      return { class: myClass, maxWidth, color, borderColor, backgroundColor, borderRadius }
    },
    leftIcon() {
      const obj = this.$attrs?.leftIcon
      return obj
    },
    rightIcon() {
      const obj = this.$attrs?.right_icon || {}
      return obj
    }
  },
  methods: {
    rightIcon() {
      const obj = this.$attrs?.right_icon || {}
      return obj
    }
  }
}
</script>

<style lang="scss" scoped>
.tagging-tag{
  &.middle-style.credits-style,
  &.credits-style{
    .tagging-tag_custom{
      position: relative;
      padding-left: 29px;
    }
    img.left-icon{
      position: absolute;
      top: 0;
      left: 0;
      width: 27px;
      height: 100%;
      margin: 0;
    }
  }
}
.tagging-tag {
  display: inline-block;

  &_custom{
    box-sizing: border-box;
    display: flex;
    height: 20px;
    line-height: 18px;
    border: 1px solid #FF5B00;
    font-size: $fontSize-caption-m;
    color: $color-text-primary-onDark;
    background-color: #FF5B00;
    border-radius: $radius-s;
    font-weight: $fontWeight-semibold;
    overflow: hidden;
    padding: 0 6px;
  }
  &_icon{
    width: 16px;
    height: 16px;
    cursor: pointer;
    &.left-icon{
      margin-right: 4px;
    }
    &.right-icon{
      margin-left: 4px;
    }
  }
  &_text{
    box-sizing: border-box;
    // flex: 1 1 auto;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: pre-wrap;
    word-break: break-all;
  }
  &_tips{
    display: flex;
    align-items: center;
    ::v-deep .klk-poptip-reference{
      height: 16px;
    }
  }
}

.middle-style.tagging-tag{
  .tagging-tag{
    &_custom{
      height: 24px;
      line-height: 22px;
      font-size: $fontSize-body-s;
      padding: 0 8px;
    }
  }
}
</style>
