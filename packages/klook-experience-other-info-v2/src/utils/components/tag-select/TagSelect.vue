<template>
  <div :class="cClass" :style="cStyle">
    <div ref="items" class="klk-tag-select-items">
      <slot></slot>
    </div>
    <div class="klk-tag-select-right">
      <span v-if="isOverflow" class="klk-tag-select-mask" :style="maskStyle"></span>
      <span v-if="isOverflow && !hideToggle" class="klk-tag-select-toggle-btn" @click="toggleWrap()">
        <Icon :type="wrap ? 'icon_navigation_chevron_up' : 'icon_navigation_chevron_down'"></Icon>
      </span>
    </div>
  </div>
</template>

<script>
import Icon from '@klook/klook-ui/lib//icon'
import { convertHexToRGB } from '@klook/klook-ui/lib/utils/colorManipulator'
import { addResizeListener, removeResizeListener } from '@klook/klook-ui/lib/utils/resize-event';

export default {
  name: 'klk-tag-select',
  components: {
    Icon
  },
  props: {
    value: [Number, String],
    hideToggle: {
      type: Boolean,
      default: false,
    },
    nowrap: {
      type: Boolean,
      default: true,
    },
    size: {
      type: String,
      default: 'small',
      validator (size) {
        return ['small', 'normal', 'large'].includes(size);
      }
    },
    backgroundColor: {
      type: String,
      default: '#ffffff'
    },
    count: {
      type: Number,
      default: 0 //如果为0 则不限制
    },
    name: {
      type: String,
      default: '' //如果为0 则不限制
    },
    cancleTravellerId: {
      type: [Number, String],
      default: -1
    }
  },
  data () {
    return {
      items: [],
      wrap: !this.nowrap,
      isOverflow: false,
      current: this.value,
      idList: []
    };
  },
  computed: {
    cClass () {
      return {
        'klk-tag-select': true,
        'klk-tag-select-no-wrap': !this.wrap,
        'klk-tag-select-is-overflow': this.isOverflow,
        'klk-tag-select-hide-toggle': this.hideToggle,
        [`klk-tag-select-${this.size}`]: !!this.size,
      };
    },
    cStyle () {
      return {
        background: this.backgroundColor,
      };
    },
    maskStyle () {
      const bgColor = this.backgroundColor;
      return {
        background: `linear-gradient(to left, ${bgColor} 20%, ${convertHexToRGB(bgColor, 0)})`
      };
    },
  },
  watch: {
    value (val) {
      if (val === 'NA') return
      this.current = val;
      this.$nextTick(() => {
        if (val !== 0) {
          if (this.idList.length < this.count) {
            const item = this.items.find(item => item.name === val)
            if (!item) {
              return
            }
            item?.setItemActive && item.setItemActive(true)
            this.showCurrentItem(true)
            if (!this.idList.includes(item.name)) {
              this.idList.push(item.name)
            }
          } else if (this.count === 1) {
            this.showCurrentItem(true)
          }
        }
      })
    },
    current (val) {
      if (this.current !== this.cancleTravellerId) {
        this.$emit('input', val);
        this.$nextTick(() => {
          this.$emit('change', val);
        });
      }
    },
    wrap (val) {
      if (!val) {
        const curItem = this.items.find(item => item.active);
        if (curItem && this.$refs.items) {
          this.$nextTick(() => {
            this.showItem(curItem.length > 1 ? curItem[0] : curItem);
          });
        }
      }
    },
    nowrap (val) {
      this.wrap = !val;
    },
    cancleTravellerId(val) {
      if (val === 'NA') return
      if (val !== -1) {
        const item = this.items.find(item => item.name === val)
        item.setItemActive(false)
        this.$nextTick(() => {
          const index = this.idList.findIndex(id => id === val);
          (index !== -1) && this.idList.splice(index, 1)
        })
      }
    }
  },
  mounted () {
    this.updateIsOverflow();
    addResizeListener(this.$el, this.updateIsOverflow);
    setTimeout(() => {
      const curItem = this.items.find(item => item.active);
      this.adjustItemPosition(curItem);
      !this.wrap && this.showItem(curItem);
    }, 100);
  },
  beforeDestroy () {
    removeResizeListener(this.$el, this.updateIsOverflow);
  },
  methods: {
    onItemAdd (item) {
      if (this.items.includes(item)) return;
      this.items.push(item);
      this.$nextTick( this.updateIsOverflow);
    },
    onItemRemove (item) {
      const index = this.items.indexOf(item);
      if (index !== -1) this.items.splice(index, 1);
      this.$nextTick(this.updateIsOverflow);
    },
    onItemClick (item) {
      if (item.name == 0) {
        this.$emit("change", 0)
        return
      }
      if (this.idList.length >= this.count && !item.active) {
        if (this.count === 1) {
          this.current = item.name
          const originItem = this.items.find(_item => _item.name ===  this.idList[0])
          originItem.setItemActive(false)
          item.setItemActive(true)
          this.idList.splice(0, 1, item.name)
        } else {
          this.$toast(this.$t('78450', {unit_name: this.name}))
        }
        return
      }

      if (!this.idList.includes(item.name)) {
        this.idList.push(item.name)
      } else {
        const index = this.idList.findIndex(id =>  id  === item.name)
        this.idList.splice(index, 1)
      }

      if (this.current === item.name) {
        item.setItemActive(!item.active)
        this.$emit("chooseChange", item.name, item.active)
        return
      }

      const flag = this.idList.includes(item.name)
      item.setItemActive(flag)
      this.$emit("chooseChange", item.name, flag)
      this.current = item.name
    },
    adjustItemPosition (item) {
      if (!item || !item.$el) return;
      const { offsetLeft, offsetWidth } = item.$el;
      const { offsetLeft: itemsOffsetLeft, offsetWidth: itemsOffsetWidth, scrollLeft } = this.$refs.items;
      const newOffsetLeft = (offsetLeft - itemsOffsetLeft - scrollLeft);
      const isLeftBoundary = newOffsetLeft < 0 && (newOffsetLeft + offsetWidth) > 0;
      const isRightBoundary = newOffsetLeft < itemsOffsetWidth && (newOffsetLeft + offsetWidth) > itemsOffsetWidth;
      /* console.log({
        isLeftBoundary, isRightBoundary
      }); */
      if (isLeftBoundary) {
        this.$refs.items.scrollTo({
          left: offsetLeft - itemsOffsetLeft,
          behavior: 'smooth',
        });
      } else if (isRightBoundary) {
        this.$refs.items.scrollTo({
          left: offsetLeft - itemsOffsetLeft + offsetWidth - itemsOffsetWidth,
          behavior: 'smooth',
        });
      }
    },
    showCurrentItem (center = false) {
      const curItem = this.items.find(item => item.active);
      curItem && this.showItem(curItem, center);
    },
    toggleWrap (wrap) {
      wrap = typeof wrap === 'boolean' ? wrap : !this.wrap;
      if (this.wrap !== wrap) {
        this.wrap = wrap;
        this.$emit('toggle-wrap', this.wrap);
      }
    },
    // should be updated when item added or removed
    updateIsOverflow () {
      if (!this.$refs.items || this.wrap) return;
      const { scrollWidth, clientWidth } = this.$refs.items;
      this.isOverflow = scrollWidth > clientWidth;
    },
    showItem (item, center = true) {
      if (!item) return;
      const { offsetLeft, offsetWidth } = item.$el;
      const { offsetLeft: itemsOffsetLeft, offsetWidth: itemsOffsetWidth } = this.$refs.items;
      const left = center
        ? (offsetLeft - itemsOffsetLeft) - (itemsOffsetWidth - offsetWidth) / 2
        : (offsetLeft - itemsOffsetLeft);
      /* console.log({
        offsetLeft, offsetWidth, itemsOffsetWidth, left
      }); */
      this.$refs.items.scrollTo({
        left,
        behavior: 'smooth'
      });
    },
    clearActiveStatus() {
      this.items.forEach((item) => item.setItemActive(false))
      this.idList = []
    },
    setIdList(list) {
      this.idList = list
      this.items.forEach(item => item.setItemActive(list.includes(item.name)))
    }
  },
};
</script>

