<template>
  <div :class="cClass" @click="onClick">
    <span><slot></slot></span>
  </div>
</template>

<script>
import { findComponentUpward } from '@klook/klook-ui/lib/utils/comp';

export default {
  name: 'klk-tag-select-item',
  props: {
    name: [String, Number],
    icon: String,
    disabled: Boolean
  },
  data () {
    return {
      parent: findComponentUpward(this, 'klk-tag-select'),
      active: false
    };
  },
  computed: {
    cClass () {
      return {
        'klk-tag-select-item': true,
        'klk-tag-select-item-disabled': this.disabled,
        'klk-tag-select-item-active': this.active,
      };
    }
  },
  mounted () {
    this.parent.onItemAdd(this);
  },
  beforeDestroy () {
    this.parent.onItemRemove(this);
  },
  methods: {
    setItemActive(boolean) {
      this.$set(this, 'active', boolean)
    },
    onClick () {
      if (this.disabled) return;
      this.parent.onItemClick(this);
    },
  }
};
</script>
