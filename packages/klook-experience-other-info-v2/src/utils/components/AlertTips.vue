<template>
  <transition name="alert-tips-slide">
    <div
      v-if="visible"
      class="alert-tips"
      :style="{ 'top': top }"
    >
      <div>
        <WarningSvg class="alert-tips-icon"/>
      </div>
      <span>
        {{ text }}
      </span>
    </div>
  </transition>
</template>

<script>
import WarningSvg from '@src/imgs/warning.svg'
import { isServer } from '@src/utils'

export default {
  name: 'AlertTips',
  components: {
    WarningSvg
  },
  props: {
    top: {
      type: String,
      default: '0px'
    }
  },
  data() {
    return {
      visible: false,
      text: '',
      timeout: 3000,
      timer: null
    }
  },
  methods: {
    open(text) {
      if (!isServer) {
        this.text = text
        this.visible = true

        if (this.timer) {
          clearTimeout(this.timer)
        }

        this.timer = setTimeout(() => {
          this.visible = false
        }, this.timeout)
      }
    }
  }
}
</script>

<style lang='scss'>
/* before the element is shown, start off the screen to the right */
.alert-tips-slide-enter-active,
.alert-tips-slide-leave-active {
  // transition: transform .45s cubic-bezier(0.23, 1, 0.32, 1), opacity .45s cubic-bezier(0.23, 1, 0.32, 1);
  transition: transform .45s cubic-bezier(0.23, 1, 0.32, 1);
  // backface-visibility: hidden;
}

.alert-tips-slide-enter,
.alert-tips-slide-leave-active {
  transform: scaleY(0) !important;
  // transform: translate3d(0, -100%, 0);
  // opacity: 0;
}
</style>

<style lang='scss' scoped>
.alert-tips {
  padding: 12px 20px 12px 44px;
  position: absolute;
  top: 0;
  left: 0;

  display: flex;
  align-items: center;
  flex-wrap: wrap;
  width: 100%;
  z-index: 2000;

  @include font-body-s-regular;
  color: $color-text-primary;
  background-color: $color-caution-background;
  transform: scaleY(1);
  transform-origin: top;

  &-icon {
    position: absolute;
    top: 12px;
    left: 16px;
    width: 20px;
    height: 20px;
  }
}
</style>
