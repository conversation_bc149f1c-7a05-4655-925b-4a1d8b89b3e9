import dayjs from 'dayjs'
import 'dayjs/locale/de'
import 'dayjs/locale/en'
import 'dayjs/locale/es'
import 'dayjs/locale/fr'
import 'dayjs/locale/id'
import 'dayjs/locale/it'
import 'dayjs/locale/ja'
import 'dayjs/locale/ko'
import 'dayjs/locale/ru'
import 'dayjs/locale/th'
import 'dayjs/locale/vi'
import 'dayjs/locale/zh-cn'

export const languageDayjsLocaleMap = {
  'en-AU': 'en',
  'en-CA': 'en',
  'en-GB': 'en',
  'en-HK': 'en',
  'en-IN': 'en',
  'en-MY': 'en',
  'en-NZ': 'en',
  'en-PH': 'en',
  'en-SG': 'en',
  'en-US': 'en',
  'zh-CN': 'zh-cn',
  'zh-HK': 'zh-cn',
  'zh-TW': 'zh-cn'
}

export const timeFormatLanguageMap = {
  'h:mma': [
    'en',
    'en-AU',
    'en-CA',
    'en-GB',
    'en-HK',
    'en-IN',
    'en-MY',
    'en-NZ',
    'en-PH',
    'en-SG',
    'en-US'
  ],
  'h:mm A': ['zh-CN', 'zh-TW', 'zh-HK'],
  'A h:mm': ['ko'],
  'HH:mm': ['th', 'vi', 'id', 'ja']
}

/**
 * 获取 Dayjs 的语言
 */
function getDayjsLocale(language) {
  return languageDayjsLocaleMap[language] || language
}

/**
 * 通过语言获取时间格式
 */
function getTimeFormatByLanguage(langauge) {
  const keys = Object.keys(timeFormatLanguageMap)

  for (let index = 0; index < keys.length; index += 1) {
    const key = keys[index]
    const langauges = timeFormatLanguageMap[key]

    if (langauges.includes(langauge)) {
      return key
    }
  }

  return ''
}

/**
 * 获取标准日期格式
 *
 * type:
 * 1: 2018年08月08日 (default),
 * 2: 2018年08月08日 18:08,
 * 3: 08月08日,
 * 4: 下午 06:08,
 * 5: 2018年08月08日 下午 06:08
 *
 * week: d || dd || ddd || dddd 星期几的展示  默认不带
 */
export function getStandardDateFormat(date, $t, language, type, week = '') {
  let datetime = date

  dayjs.locale(getDayjsLocale(language))

  // Fix safari
  if (typeof datetime === 'string') {
    datetime = new Date(datetime.replace(/-/g, '/'))
  }

  // type 1, YYYY-MM-DD
  let formatStr = $t('global.standard.date.format')

  if (type === 2) {
    // Not 00:00
    if (datetime.getHours() > 0 || datetime.getMinutes() > 0) {
      formatStr += ' HH:mm'
    }
  }

  if (type === 3) {
    formatStr = $t('global.standard.date.format_hide_year')
  }

  if (type === 4) {
    formatStr = getTimeFormatByLanguage(language)
  }

  if (type === 5) {
    formatStr += ` ${getTimeFormatByLanguage(language)}`
  }

  if (type === 6) {
    formatStr = $t('global.standard.date.format')
  }
  // 增加星期几
  if (week && /d+/.test(week)) {
    formatStr += week
  }

  if (type === 6) {
    // support 00:00
    if (datetime.getHours() >= 0 || datetime.getMinutes() >= 0) {
      formatStr += ' HH:mm'
    }
  }

  return dayjs(datetime).format(formatStr)
}
