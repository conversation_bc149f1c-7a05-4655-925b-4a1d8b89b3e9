import languageMap from './map'
import { isServer, getKlkLanguage } from '@src/utils'
import localStorageEx from '../localstorage'
const localStorage = localStorageEx()

class CodeManager {
  constructor() {
    // 语言码
    this._languageCode = []
    this.languageList = []
  }

  async asyncGetLanguages({ $axios }) {
    if ($axios && $axios.$get) {
      const language = languageMap[getKlkLanguage()]
      const cacheKey = `${language}_AllLanguages`
      const cache = localStorage.getItem(cacheKey)

      if (cache) {
        this._languageCode = cache
        this.languageList = this.parseCode('language')
      }

      try {
        const { result } = await $axios.$get('/v1/experiencesrv/common/global_service/get_all_language', {
          params: { language }
        })
        if (!Array.isArray(result)) {
          console.warn('warn: codeManager.initGetLanguages')
          return false
        }
        result.forEach((item) => {
          item.code5 = item.language
          item.i18ns = { [language]: item.content }
        }) // 保证结构体和以前数据结构一致
        localStorage.setItem(cacheKey, result, 24 * 3600)
        this._languageCode = result
        this.languageList = this.parseCode('language')
      } catch (error) {
        console.error('error: codeManager.initGetLanguages')
      }
    }
  }

  initCountryCode(countryCode) {
    if (countryCode && countryCode.length > 0) {
      this._countryCode = countryCode
      this.countryList = this.parseCode('country')
      this.countryCodeList = this.parseCountryCode()
      this.isCountryDefault = false
    }
  }

  parseCode(type) {
    const data = type === 'country' ? this._countryCode : this._languageCode

    if (!isServer) {
      if (type === 'country') {
        data.forEach(item => {
          item.areaCode = `${item.name} (+${item.country_number})`
          item.field_key = item.country_code
        })
      } else {
        data.forEach(item => {
          item.name = JSON.parse(
            JSON.stringify(item.i18ns[languageMap[getKlkLanguage()]] || item.i18ns['en_US']),
          )
          item.areaCode = ''
          item.field_key = item.code5
          delete item.i18ns
        })
      }
    }

    return data
  }

  parseCountryCode() {
    return this.countryList.reduce((accu, curr) => {
      accu[curr.country_code] = curr
      return accu
    }, {})
  }
}

export default new CodeManager()
