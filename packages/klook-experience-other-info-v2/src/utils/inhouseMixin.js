export default {
  provide() {
    return {
      updateDataSpm: this.updateDataSpm,
      sendDataSpm: this.sendDataSpm,
    }
  },
  methods: {
    updateDataSpm(type, elementSelector, dataSpmString) {
      const selector = this.$el.querySelector(elementSelector)
      selector && selector.setAttribute(type, `${dataSpmString || ''}`)
    },
    sendDataSpm(type, elementSelector, customExtra) {
      const inhouse = window.inhouse || this.$inhouse
      setTimeout(() => {
        inhouse && inhouse.track(type, elementSelector, customExtra)
      }, 0)
    },
  },
}
