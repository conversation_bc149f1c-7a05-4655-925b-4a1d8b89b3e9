import { CHECKBOX_CHECKED_VALUES } from '@src/utils'

// 身份证校验
export function testId(id) {
  // 注意：这里如果改了，需要同步给admin端的 Poyan Zhu
  // 1 "验证通过!", 0 //校验不通过
  const format = /^(([1][1-5])|([2][1-3])|([3][1-7])|([4][1-6])|([5][0-4])|([6][1-5])|([7][1])|([8][1-2]))\d{4}(([1][9]\d{2})|([2]\d{3}))(([0][1-9])|([1][0-2]))(([0][1-9])|([1-2][0-9])|([3][0-1]))\d{3}[0-9xX]$/
  //号码规则校验
  if (!format.test(id)) {
    return false
  }
  //区位码校验
  //出生年月日校验   前正则限制起始年份为1900;
  const year = id.substr(6, 4), //身份证年
    month = id.substr(10, 2), //身份证月
    date = id.substr(12, 2), //身份证日
    time = Date.parse(month + '-' + date + '-' + year), //身份证日期时间戳date
    now_time = Date.parse(new Date()), //当前时间戳
    dates = new Date(year, month, 0).getDate() //身份证当月天数
  if (time > now_time || date > dates) {
    return false
  }
  //校验码判断
  const c = new Array(7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2) //系数
  const b = new Array('1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2') //校验码对照表
  const id_array = id.split('')
  let sum = 0
  for (let k = 0; k < 17; k++) {
    sum += parseInt(id_array[k]) * parseInt(c[k])
  }
  if (id_array[17].toUpperCase() != b[sum % 11].toUpperCase()) {
    return false
  }
  return true
}

export function getValidateRes(status, errorType) {
  return {
    success: status,
    type: errorType,
  }
}

export function validateDynamicFormItem(value, data) {
  const isString = typeof value === 'string'
  const isNumber = typeof value === 'number'
  const { required, match_rule, type: formType } = data.style
  const { type = 0, min_len, max_len, regex, min_num, max_num } = match_rule || {}

  // 必填校验
  if (required) {
    // 非空
    if (!value || value.length === 0) {
      return getValidateRes(false, 'accomplish')
    }

    // checkbox 必填
    if (formType === 5 && !CHECKBOX_CHECKED_VALUES.includes(value)) {
      return getValidateRes(false, 'accomplish')
    }
  }

  // 长度校验
  if ((min_len || max_len) && isString) {
    const len = value.length
    const minLen = min_len || 0
    const maxLen = max_len || 1000000
    let type = ''

    if (len < minLen) {
      type = 'content'
    } else if (len > maxLen) {
      type = 'maxLength'
    }

    if (type) {
      return getValidateRes(false, type)
    }
  }

  // 数字范围校验
  if (type === 2 && (isString || isNumber)) {
    if (isString && value.includes('.')) {
      return getValidateRes(false, 'content')
    }
    if (Number(value) < min_num || Number(value) > max_num) {
      return getValidateRes(false, 'content')
    }
  }

  // 正则校验
  if (type === 3 && (isString || isNumber)) {
    const reg = new RegExp(regex)
    if (!reg.test(value)) {
      return getValidateRes(false, 'content')
    }
  }

  // 大陆身份证校验（前端校验）
  if (type === 4 && (isString || isNumber) && !testId(value)) {
    return getValidateRes(false, 'content')
  }

  return getValidateRes(true)
}

// 表单校验
export function formRules() {
  // 16186: 请输入；17850：请选择；17440：请完成必填项
  const accomplishMap = {
    0: this.$t('16186'),
    1: this.$t('17850'),
    2: this.$t('17850'),
    3: this.$t('17850'),
    4: this.$t('17850'),
    5: this.$t('28685'),
    6: this.$t('17440'),
    7: this.$t('17440'),
    8: this.$t('16186'),
    9: this.$t('17850'),
    10: this.$t('28685'),
    13: this.$t('16186')
  }

  const { data } = this
  const { hint, max_len = 100000 } = data.style.match_rule || {}
  const wrongContentMsg = hint || this.$t('17441')
  const accomplishMsg = accomplishMap[data.style.type] || this.$t('17440')
  const maxLengthErrorMsg = this.$t('105983', { num: max_len }) || this.$t('17441')

  // 1、 先校验ruleMatch; 2、在校验长度； 3、在兜底报错 17441
  const errMsgMap = {
    accomplish: accomplishMsg,
    content: wrongContentMsg,
    maxLength: maxLengthErrorMsg
  }

  return [
    {
      // change不再做校验，只有blur才做校验
      // trigger: ['change', 'blur'],
      trigger: ['blur'],
      validator: (_rule, value, callback) => {
        const { success, type } = validateDynamicFormItem(value, data) || {}
        return success ? callback() : callback(new Error(errMsgMap[type]))
      }
    }
  ]
}
