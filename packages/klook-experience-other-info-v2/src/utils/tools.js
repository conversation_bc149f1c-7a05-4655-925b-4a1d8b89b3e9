export const copyToClipboard = (value) => {
  return new Promise((resolve) => {
    const el = document.createElement('textarea') // temporary element
    el.value = value

    el.style.position = 'absolute'
    el.style.left = '-9999px'
    el.readOnly = true // avoid iOs keyboard opening
    el.contentEditable = 'true'

    document.body.appendChild(el)

    selectText(el, 0, value.length)

    if (document.execCommand('copy')) {
      document.body.removeChild(el)
      resolve(true)
    } else {
      resolve(false)
    }
  })
}


const selectText = (
  editableEl,
  selectionStart,
  selectionEnd
) => {
  const isIOS = navigator.userAgent.match(/ipad|iPod|iphone/i)
  if (isIOS) {
    const range = document.createRange()
    range.selectNodeContents(editableEl)

    const selection = window.getSelection() // current text selection
    selection.removeAllRanges()
    selection.addRange(range)
    editableEl.setSelectionRange(selectionStart, selectionEnd)
  } else {
    editableEl.select()
  }
}
