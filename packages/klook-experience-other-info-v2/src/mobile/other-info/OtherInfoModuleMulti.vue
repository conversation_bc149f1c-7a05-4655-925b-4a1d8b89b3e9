<template>
  <div v-if="data && data.length > 0" class="other-info-module-multi-mobile">
    <OtherInfoModule
      v-for="(item, index) in data"
      :key="`${item.section_type}-${index}`"
      :data="item"
      :origin-form-infos="item.origin_form_infos"
      v-bind="$attrs"
      @add="data => $emit('add', index, data)"
      @remove="$emit('remove', index)"
      @change="data => handleChange(index, data)"
      @input="$emit('input')"
      @validate="handleValidate"
      @updateTravellerData="$emit('updateTravellerData', $event)"
    />
  </div>
</template>

<script>
import OtherInfoModule from '@src/mobile/module'


export default {
  name: 'OtherInfoModuleMulti',
  inheritAttrs: false,
  components: {
    OtherInfoModule,
  },
  props: {
    data: {
      type: Array,
      default: () => [],
    }
  },
  methods: {
    handleChange(index, data) {
      const result = [...this.data]
      result.splice(index, 1, data)
      this.$emit('change', result)
    },
    handleValidate(...args) {
      this.$emit('validate', ...args)
    }
  }
}
</script>
