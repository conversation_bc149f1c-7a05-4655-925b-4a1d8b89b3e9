<template>
  <div v-if="data" class="other-info-checkout">
    <div
      v-for="(section, sectionIndex) in data"
      :key="sectionIndex"
      class="other-info-checkout-wrap"
    >
      <SectionTitle v-if="section.title">
        {{ section.title }}
      </SectionTitle>
      <div
        v-for="(info, infoIndex) in (section.form_infos || [])"
        :key="infoIndex"
        class="other-info-checkout-section"
      >
        <div v-if="info.name" class="other-info-checkout-section-title">
          {{ info.name }}
        </div>
        <div class="other-info-checkout-section-content">
          <template>
            <CheckoutItem
              v-for="(item, itemIndex) in (info.info_items || [])"
              :key="itemIndex"
              :data="item"
              class="other-info-checkout-section-item"
            />
          </template>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import CheckoutItem from '@src/mobile/components/CheckoutItem.vue'
import SectionTitle from '@src/mobile/components/SectionTitle.vue'

export default {
  name: 'OtherInfoCheckout',
  components: {
    CheckoutItem,
    SectionTitle
  },
  props: {
    data: {
      type: Array,
      default: null,
    }
  },
  computed: {
    realTitle () {
      const { title } = this.data || {}
      return title || this.title || ''
    }
  }
}
</script>

<style lang="scss" scoped>
.other-info-checkout {
  width: 100%;
  background-color: $color-bg-widget-normal;

  &-wrap {
    padding: 16px;
    width: 100%;
    border-bottom: 12px solid $color-border-dim;

    &:last-child {
      border-bottom: none;
    }
  }

  &-title {
    font-weight: $fontWeight-bold;
    font-size: $fontSize-body-l;
    line-height: 24px;
    color: $color-text-primary;
  }

  &-section {
    width: 100%;
  }

  &-shipment {
    margin-top: 16px;
  }

  &-section-title {
    margin-top: 20px;
    font-size: $fontSize-body-l;
    line-height: 18px;
    color: $color-text-primary;
  }

  &-section-content {
    width: 100%;
    // display: flex;
    // align-items: center;
    // justify-content: space-between;
    // flex-wrap: wrap;
  }

  &-section-item {
    margin-top: 16px;
  }
}
</style>
