<template>
  <!--公共表单，为绑定值未拆模块-->
  <div class="other-info-traveller-editable">
    <p v-if="data.showCount" class="other-info-traveller-editable-title">
      {{ data.name }}
      <span :class="[filledCount === data.count ? 'filled' : 'not-filled']">
        ({{ filledCount }}/{{ data.count }})
      </span>
    </p>

    <TravellerSelector
      v-if="showTravellerSelector"
      ref="travellerSelector"
      :traveller-id="travellerId"
      :section-type="sectionType"
      :traveller-list="travellerList"
      :count="data.count || 0"
      :name="data.name"
      :cancle-traveller-id="cancleTravellerId"
      class="other-info-traveller-editable-selector"
      @selectTraveller="handleSelectTraveller"
      @changeItemSelectStatus="handleItemSelectedStatus"
    />

    <k-form
      v-bind="formatFormProps"
      ref="form"
      class="other-info-traveller-editable-drawer-form"
      :class="formClass"
      @validate="handleValidate"
    >
      <DynamicForm
        v-for="(item, index) in otherInfoData.data || []"
        :key="`${item.field_key}-${item.id}`"
        :data="item"
        :prop="`data[${index}]`"
        :disabled="disabled"
        :original-phone="originalPhone"
        :should-check-date-valid="shouldCheckDateValid"
        :sectionType="sectionType"
        :action="action"
        :use-editable-traveller-form="useEditableTravellerForm"
        @input="handleInput"
        @change="handleChange"
        @validate="handleValidate"
      />
    </k-form>
  </div>
</template>

<script>
import { Form } from '@klook/klook-ui/lib/form'

import '@klook/klook-ui/lib/styles/components/form.scss'

import TravellerSelector from '@src/mobile/components/TravellerSelector.vue'
import DynamicForm from '@src/mobile/dynamic-form/index.vue'
import inhouseMixin from '@src/utils/mixins/inhouse'
import {
  CONTACT_TYPE,
} from '@src/utils'

/* other-info 出行人特殊展示和交互 */
export default {
  name: 'OtherInfoFormTravellerEditable',
  inheritAttrs: false,
  mixins: [inhouseMixin],
  components: {
    KForm: Form,
    TravellerSelector,
    DynamicForm,
  },
  props: {
    travellerId: {
      type: [Number, String],
      default: -1,
    },
    cancleTravellerId: {
      type: [Number, String],
      default: -1,
    },
    sectionType: {
      type: Number,
      default: 1,
    },
    data: {
      type: Object,
      default: null,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    travellerList: {
      type: Array,
      default: () => [],
    },
    // 日历组件是否自动检测日期是否有效，如果无效则置空
    shouldCheckDateValid: {
      type: Boolean,
      default: true,
    },
    updateTravellerListFunc: {
      type: Function,
      default: null,
    },
    // hoc 的传值
    otherInfoData: {
      type: Object,
      default: null,
    },
    // 新增的时候需要加一些额外的表单
    addRequiredFormData: {
      type: Object,
      default: () => ({}),
    },
    // 用于新建
    otherInfoBlankForm: {
      type: Array,
      default: () => [],
    },
    recordOtherinfoData: {
      type: Object,
      default: () => ({}),
    },
    filledCount: {
      type: Number,
      default: 0,
    },
    noNeedUpdateData: {
      type: Boolean,
      default: false,
    },
    formProps: {
      type: Object,
      default: () => ({}),
    },
    useEditableTravellerForm: {
      type: Boolean,
      default: false,
    },
  },
  watch: {
    otherInfoData: {
      immediate: true,
      handler() {
        this.$nextTick(() => {
        if (this.travellerId > 0) {
          this.validate()
        } else {
          this.clearValidate()
        }
      })
      },
    },
  },
  computed: {
    showTravellerSelector() {
      return !this.data?.custom_info?.simple_checkout
    },
    formatFormProps() {
      const attrs = this.formProps || {}
      if (!attrs.hasOwnProperty('styleType')) {
        attrs.styleType = 'outlined'
      }

      return { ...attrs, model: this.otherInfoData }
    },
    isContact() {
      return this.sectionType === CONTACT_TYPE
    },
    originalPhone() {
      const target = (this.data.info_items || []).find(item => item.style.type === 8)
      const content = (target || {}).content || ''
      const operation = (target || {}).operation || []
      return content && operation.length > 0 ? `${operation[0].field_key}-${content}` : null
    },
  },
  methods: {
    handleSelectTraveller(id) {
      this.$emit('selectTraveller', id)
    },
    handleItemSelectedStatus(id, status) {
      this.$emit('changeItemSelectStatus', id, status)
    },
    validate() {
      return this.$refs.form.validate().catch((err) => {
        console.warn(err)
        return false
      })
    },
    validateForm() {
      return this.$refs.form.validate().catch(err => {
        return false
      })
    },
    clearValidate() {
      this.$refs.form.clearValidate()
    },
    handleChange() {
      this.$emit('change')
    },
    handleInput() {
      this.$emit('input')
    },
    handleValidate(...args) {
      this.$emit('validate', ...args)
    },
  },
}
</script>

<style lang="scss" scoped>
.other-info-traveller-editable {
  &-title {
    @include font-body-s-bold;
    margin-bottom: 0 !important;
    color: $color-text-primary;
    padding: 8px 10px 12px;
  }

  &-selector {
    padding: 0 10px;
    margin-bottom: 16px;
  }

  &-drawer-form {
    ::v-deep .dynamic-form-module {
      .label-title {
        font-size: 14px;
        margin-bottom: 8px;
      }
    }
  }
}

.not-filled {
  color: $color-red-500;
}

.filled {
  color: $color-neutral-900;
}
</style>
