<template>
  <div
    v-if="data"
    class="other-info-module-mobile"
    :class="classList"
    :data-spm-module="spmModule"
  >
    <div
      v-if="data.is_ai_translation"
      class="other-info-module-mobile-translate-tips"
    >
      {{ $t('80863') }}
    </div>
    <div class="other-info-module-mobile-header">
      <SectionTitle>
        {{ data.title }}
      </SectionTitle>
      <div>
        <div
          v-if="shouldShowShipment"
          class="other-info-module-mobile-header-address"
          data-spm-module="ChooseAddress"
          data-spm-virtual-item="__virtual"
          @click="handleOpenShipment"
        >
          {{ $t('28652') }}
        </div>
      </div>
    </div>

    <p v-if="data.grey_tips" class="other-info-module-mobile-tips">
      <span>{{ data.grey_tips }}</span>
      <span>{{ data.right_title || '' }}</span>
    </p>

    <k-alert class="other-info-module-mobile-warning" v-if="data.tips" type="warning" show-icon>
      {{ data.tips }}
    </k-alert>

    <OtherInfoFormMulti
      ref="otherInfoFormMulti"
      :data="data.form_infos"
      :section-type="data.section_type"
      :shipment-list="shipmentList"
      :origin-form-infos="originFormInfos"
      v-bind="$attrs"
      @change="handleChange"
      @input="$emit('input')"
      @validate="handleValidate"
      @updateTravellerData="$emit('updateTravellerData', $event)"
    />
  </div>
</template>

<script>
import Alert from '@klook/klook-ui/lib/alert'
import '@klook/klook-ui/lib/styles/components/alert.scss'

import { SHIPMENT_TYPE, CONTACT_TYPE } from '@src/utils'
import OtherInfoFormMulti from '@src/mobile/form-multi'
import SectionTitle from '@src/mobile/components/SectionTitle.vue'
import childEmitMixin from '@src/utils/mixins/child-emit'

export default {
  name: 'OtherInfoModule',
  inheritAttrs: false,
  mixins: [childEmitMixin],
  components: {
    KAlert: Alert,
    OtherInfoFormMulti,
    SectionTitle,
  },
  props: {
    data: {
      type: Object,
      default: null,
    },
    shipmentList: {
      type: Array,
      default: () => [],
    },
    originFormInfos: {
      type: Array,
      default: () => []
    }
  },
  computed: {
    classList() {
      return {
        'is-contact': this.isContact,
        'is-shipment': this.isShipment,
        'is-other': this.isOtherInfo
      }
    },
    spmModule() {
      return `OtherInfo?ext=${JSON.stringify({ InfoType: this.data.section_type })}`
    },
    shouldShowShipment() {
      return this.isShipment && this.shipmentList.length > 0
    },
    isShipment() {
      return SHIPMENT_TYPE === this.data.section_type
    },
    isOtherInfo() {
      return 0 === this.data.section_type
    },
    isContact() {
      return CONTACT_TYPE === this.data.section_type
    },
  },
  methods: {
    async validate() {
      let isValid = await this.$refs.otherInfoFormMulti.validate()
      return isValid
    },
    async getValidateKeys() {
      const keyList = await this.$refs.otherInfoFormMulti.getValidateKeys()
      return keyList
    },
    clearValidate() {
      this.$refs.otherInfoFormMulti.clearValidate()
    },
    clearGlobalErrorStatus() {
      this.$refs.otherInfoFormMulti.clearGlobalErrorStatus()
    },
    getModifiedData() {
      const formInfos = this.$refs.otherInfoFormMulti.getModifiedData()
      return {
        ...this.data,
        form_infos: formInfos,
      }
    },

    cacheData() {
      this.$refs.otherInfoFormMulti.cacheData()
    },
    clearCache() {
      this.$refs.otherInfoFormMulti.clearCache()
    },
    recoverData() {
      this.$refs.otherInfoFormMulti.recoverData()
    },
    refreshTraveller() {
      this.$refs.otherInfoFormMulti.refreshTraveller()
    },
    handleChange(formData) {
      this.$emit('change', {
        ...this.data,
        form_infos: formData,
      })
    },
    handleOpenShipment() {
      this.$refs.otherInfoFormMulti.openShipment()
    },
    handleValidate(...args) {
      this.$emit('validate', this.data, ...args)
    },
  },
}
</script>

<style lang="scss" scoped>
.other-info-module-mobile {
  margin-top: 12px;
  padding: 0 20px;
  background-color: $color-bg-widget-normal;

  &.is-traveller {
    .other-info-module-mobile-tips {
      @include font-caption-m-regular;

      margin: -14px 0 16px 12px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      color: $color-text-primary;
    }
  }

  &-translate-tips {
    @include font-caption-m-regular;
    padding-top: 16px;
    color: $color-text-secondary;
  }

  &-header {
    padding-top: 16px;
    padding-bottom: 8px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    // margin-bottom: 18px;

    &-address {
      color: $color-info;
      font-weight: $fontWeight-regular;
      font-size: $fontSize-body-s;
      line-height: 22px;
      cursor: pointer;
    }
  }

  &-tips {
    margin: -8px 0 16px 0;
    font-size: $fontSize-caption-m;
    line-height: 16px;
    color: $color-text-placeholder;
  }

  &-warning {
    margin-bottom: 16px;
  }
}
</style>
