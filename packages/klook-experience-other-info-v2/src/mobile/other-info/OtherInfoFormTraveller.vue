<template>
  <!--公共表单，为绑定值未拆模块-->
  <div class="other-info-traveller">
    <p  v-if="data.showCount" class="other-info-traveller-title">
      {{ data.name }}
      <span
          :class="[filledCount === data.count ? 'filled' : 'not-filled']"
        >
          ({{ filledCount }}/{{ data.count }})
        </span>
    </p>

    <TravellerSelector
      v-if="showTravellerSelector"
      ref="travellerSelector"
      :traveller-id="travellerId"
      :section-type="sectionType"
      :traveller-list="travellerList"
      :count="data.count || 0"
      :name="data.name"
      :cancle-traveller-id="cancleTravellerId"
      class="other-info-traveller-selector"
      @selectTraveller="handleSelectTraveller"
      @changeItemSelectStatus="handleItemSelectedStatus"
    />


    <TravellerPanel
      v-if="isContact"
      ref="travellerPanel"
      :data="otherInfoData.data"
      :traveller-list="travellerList"
      :section-type="sectionType"
      :original-phone="originalPhone"
      :traveller-id="travellerId"
      class="other-info-traveller-panel"
      @edit="openDrawer"
    />

    <TravellerPanelNew
      v-else
      ref="travellerPanelNew"
      :data="otherInfoData.data"
      :traveller-list="travellerList"
      :section-type="sectionType"
      :original-phone="originalPhone"
      :traveller-id="travellerId"
      :origin-data="data"
      :noNeedUpdateData="noNeedUpdateData"
      :cancle-traveller-id="cancleTravellerId"
      class="other-info-traveller-panel-new"
      @changeItemSelectStatus="handleItemSelectedStatus"
      @edit="newOpenDrawer"
      @updateTravellerData="$emit('updateTravellerData', $event)"
    />


    <EditSheet
      :visible.sync="drawerVisible"
      :loading="isLoading"
      :title="drawerTitle"
      :action="action"
      :section-type="sectionType"
      :data="drawerData.data || []"
      :sensitive-terms="sensitiveTerms"
      ref="EditSheet"
      class="other-info-traveller-drawer"
      @confirm="handleConfirm"
    >
      <DynamicFormSkeleton v-if="showSkeleton" />
      <k-form
        v-else
        v-bind="formatFormProps"
        ref="form"
        class="other-info-traveller-drawer-form"
        :class="formClass"
      >
        <DynamicForm
          v-for="(item, index) in (drawerData.data || [])"
          :key="`${item.field_key}-${item.id}`"
          :data="item"
          :prop="`data[${index}]`"
          :disabled="disabled"
          :original-phone="originalPhone"
          :should-check-date-valid="shouldCheckDateValid"
          :sectionType="sectionType"
          :action="action"
        />
      </k-form>
    </EditSheet>
  </div>
</template>

<script>
import cloneDeep from 'lodash/cloneDeep'
import { Form } from '@klook/klook-ui/lib/form'
import Checkbox from '@klook/klook-ui/lib/checkbox'
import Alert from '@klook/klook-ui/lib/alert'

import '@klook/klook-ui/lib/styles/components/icon.scss'
import '@klook/klook-ui/lib/styles/components/form.scss'
import '@klook/klook-ui/lib/styles/components/checkbox.scss'
import '@klook/klook-ui/lib/styles/components/alert.scss'

import TravellerSelector from '@src/mobile/components/TravellerSelector.vue'
import TravellerPanel from '@src/mobile/components/TravellerPanel.vue'
import EditSheet from '@src/mobile/components/EditSheet.vue'
import DynamicForm from '@src/mobile/dynamic-form/index.vue'
import DynamicFormSkeleton from '@src/mobile/dynamic-form/DynamicFormSkeleton.vue'
import inhouseMixin from '@src/utils/mixins/inhouse'
import TravellerPanelNew from "@src/mobile/components/TravellerPanelNew.vue"
import { TRAVELLER_TYPE, getUpdateTravellerListParams, DRAW_TITLE_MAPS,CONTACT_TYPE ,cleanDynamicFormList} from '@src/utils'


/* other-info 出行人特殊展示和交互 */
export default {
  name: 'OtherInfoFormTraveller',
  inheritAttrs: false,
  mixins: [inhouseMixin],
  components: {
    KForm: Form,
    KCheckbox: Checkbox,
    KAlert: Alert,
    TravellerSelector,
    DynamicForm,
    TravellerPanel,
    EditSheet,
    DynamicFormSkeleton,
    TravellerPanelNew
  },
  props: {
    travellerId: {
      type: [Number,String],
      default: -1
    },
    cancleTravellerId: {
      type: [Number, String],
      default: -1
    },
    sectionType: {
      type: Number,
      default: 1,
    },
    data: {
      type: Object,
      default: null,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    travellerList: {
      type: Array,
      default: () => [],
    },
    // 日历组件是否自动检测日期是否有效，如果无效则置空
    shouldCheckDateValid: {
      type: Boolean,
      default: true,
    },
    updateTravellerListFunc: {
      type: Function,
      default: null
    },
    // hoc 的传值
    otherInfoData: {
      type: Object,
      default: null
    },
    // 新增的时候需要加一些额外的表单
    addRequiredFormData: {
      type: Object,
      default: () => ({}),
    },
    // 用于新建
    otherInfoBlankForm: {
      type: Array,
      default: () => ([])
    },
    recordOtherinfoData: {
      type: Object,
      default: () => ({})
    },
    filledCount: {
      type: Number,
      default: 0
    },
    noNeedUpdateData: {
      type: Boolean,
      default: false
    },
   formProps: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      isLoading: false,
      isDrawerAdd: false,
      drawerVisible: false,
      drawerData: {
        data: []
      },
      isDefaultChecked: false
    }
  },
  watch: {
    otherInfoData: {
      immediate: true,
      handler() {
        this.$nextTick(() => {
          this.validateOtherinfoData()
        })
      }
    },
    drawerVisible(val) {
      if (!val) {
        this.$refs.form.clearValidate()
      }
    }
  },
  computed: {
    showTravellerSelector() {
      return !this.data?.custom_info?.simple_checkout
    },
    formatFormProps(){
      const attrs = this.formProps || {}
      if (!attrs.hasOwnProperty('styleType')) {
        attrs.styleType = 'lined'
      }

      return { ...attrs, model: this.drawerData }
    },
    formClass() {
      const styleType = this.formProps?.styleType
      const isb = ['filled', 'outlined'].includes(styleType)
      return {
        'custom-form-style': isb
      }
    },
    action() {
      return this.isDrawerAdd ? 'Add' : "Edit"
    },
    isTraveller() {
      return this.sectionType === TRAVELLER_TYPE
    },
    isContact() {
      return this.sectionType === CONTACT_TYPE
    },
    drawerTitle() {
      const flag = this.isDrawerAdd ? 1 : 0
      return this.$t(DRAW_TITLE_MAPS.get(`${this.sectionType}_${flag}`))
    },
    showSkeleton() {
      return !this.drawerData.data || this.drawerData.data.length === 0
    },
    originalPhone() {
      const target = (this.data.info_items || []).find(item => item.style.type === 8)
      const content = (target || {}).content || ''
      const operation = (target || {}).operation || []
      return content && operation.length > 0 ? `${operation[0].field_key}-${content}` : null
    },
    additionalFormData() {
       const { custom_info } = this.data || {}

      if (custom_info && Object.keys(custom_info).length > 0) {
        const { add_concat_required_form = [] } = custom_info

        if (add_concat_required_form && add_concat_required_form.length) {
          return add_concat_required_form
        }

        return []
      }

      return []
    },
    sensitiveTerms() {
      const { custom_info } = this.data || {}

      return custom_info?.terms || []
    }
  },
  methods: {
    newOpenDrawer(id) {
      const isAddAction = id === -1
      !isAddAction && this.$emit('changeTravellerId', id)
      const data = (this.recordOtherinfoData[id] && this.recordOtherinfoData[id]['value']) || null
      this.openDrawer(isAddAction, data)
    },
    openDrawer(isAdd = false, data = null) {
      this.isDrawerAdd = isAdd
      this.drawerVisible = true
      if (this.showSkeleton) {
        // 因为渲染国家选项的时候创建了很多html，会阻塞ui渲染，所以给一个延时
        setTimeout(() => {
          this.setDrawerData(data)
        }, 300)
      } else {
        this.setDrawerData(data)
      }
    },
    setDrawerData(data) {
      const formData = this.isDrawerAdd ? [...this.additionalFormData, ...cleanDynamicFormList(cloneDeep(this.otherInfoBlankForm))] : (data || this.otherInfoData.data)
      this.drawerData.data = cloneDeep(formData)
      this.$nextTick(() => {
        // 不在主动做校验
        this.$refs.form.clearValidate()

        // if (!this.isDrawerAdd) {
        //   this.validateForm()
        //   this.scrollToFormError()
        // } else {
        //   this.$refs.form.clearValidate()
        //   this.scrollToFormError(true)
        // }
      })
    },
    scrollDrawerToTop() {
      const drawerContentDom = this.$el.querySelector('.edit-sheet-content')
      drawerContentDom && (drawerContentDom.scrollTop = 0)
    },
    handleSelectTraveller(id, status) {
      this.isDefaultChecked = !!status
      // 如果是新增的情况，则直接打开弹窗
      if (id === 0) {
        this.openDrawer(true)
      } else {
        this.$emit('selectTraveller', id)
      }
    },
    handleItemSelectedStatus(id, status) {
      this.$emit("changeItemSelectStatus", id, status)
    },
    getMethodAndValidate() {
      const method = 'validate'
      const ref = this.isTraveller ? 'travellerPanelNew' : 'travellerPanel'

      return {
        method,
        ref
      }
    },
    validate() {
      let method = 'validate', ref = 'travellerPanel'
      if (this.isTraveller) {
        method = 'validateDataList'
        ref = 'travellerPanelNew'
      }
      return this.$refs[ref][method]().catch(err => {
        // console.warn(err)
        return false
      })
    },
    validateOtherinfoData() {
      if (this.travellerId !== -1) {
        const { method, ref } = this.getMethodAndValidate()
        this.$refs[ref][method]().then(res => {
          if (this.isTraveller) {
            // 不需要更新数据，则说明是校验通过的出行人
           !this.noNeedUpdateData && this.$emit('validatePass')
          }
        }).catch(_ => {
          // 默认选中的出行人，第一次不需要打开弹窗
          !this.isDefaultChecked && this.openDrawer()
        })
      }
    },
    validateForm() {
      return this.$refs.form.validate().catch((err) => {
        // console.warn(err)
        return false
      })
    },
    scrollToFormError(isAdd) {
      this.$nextTick(() => {
        const formEl = this.$refs.form.$el
        const dom = isAdd ? '.dynamic-form-wrap' : '.dynamic-form-item.klk-form-item-has-error'
        const errorDom = formEl && formEl.querySelector(dom)
        errorDom && errorDom.scrollIntoView({
          block: isAdd ? 'center' : 'start',
          behavior: 'smooth'
        })
      })
    },
    clearValidate() {
      const { ref } = this.getMethodAndValidate()
      this.$refs[ref].clearValidate()
    },
    clearGlobalErrorStatus() {
      const { ref } = this.getMethodAndValidate()
      this.$refs[ref].clearGlobalErrorStatus && this.$refs[ref].clearGlobalErrorStatus()
    },
    async handleSave() {
      if (!this.updateTravellerListFunc) {
        return
      }
      // 新增默认不需要跳转到选择的项
      this.isLoading = true
      const res = await this.updateTravellerListFunc({
        traveller_id: this.isDrawerAdd ? 0 : this.travellerId,
        items: getUpdateTravellerListParams(this.drawerData.data)
      })
      this.isLoading = false
      if (res.success) {
        // 这里为了性能，暂时不用深复制
        const start = this.isDrawerAdd ? this.additionalFormData.length : 0
        this.$emit('handleUpdateTravellerList', res.result, this.drawerData.data.slice(start))
        this.drawerVisible = false
        this.$refs.EditSheet && this.$refs.EditSheet.closeAllModal()
      }
    },
    drawerAlert(text) {
      const drawerElement = this.$refs.EditSheet
      if (drawerElement) {
        drawerElement.alert(text)
      }
    },
    async handleConfirm() {
      const isValid = await this.validateForm()

      if (isValid) {
        return this.handleSave()
      }

      setTimeout(() => {
        this.drawerAlert(this.$t('72688'))
        const formEl = this.$refs.form.$el
        const errorDom = formEl && formEl.querySelector('.dynamic-form-item.klk-form-item-has-error')
        errorDom && errorDom.scrollIntoView({
          block: "center",
          behavior: 'smooth'
        })
      }, 20)
    },
    cacheDataList() {
      return cloneDeep(this.$refs.travellerPanelNew?.cacheDataList())
    },
    clearSelectorList() {
      this.$refs.travellerSelector.clearIdList()
    },
    setSelectorList(list) {
      this.$refs.travellerSelector.setIdList(list)
    },
    replaceDataList(data) {
      this.$refs.travellerPanelNew.replaceDataList(data)
    }
  }
}
</script>

<style lang="scss" scoped>
.other-info-traveller {
  margin: 0 -10px 20px;

  &:last-child {
    margin-bottom: 24px;
  }

  &-title {
    @include font-body-s-bold;
    margin-bottom: 0 !important;
    color: $color-text-primary;
    padding: 8px 10px 12px;
  }

  &-selector {
    padding: 0 10px;
    margin-bottom: 16px;
  }

  &-panel {
    margin-top: 16px;
    border: 1px solid $color-neutral-200;
    border-radius: $radius-l;
    padding: 16px;
  }

  &-panel-new {
    margin-top: 16px;
  }

}

.other-info-traveller-drawer {
  ::v-deep .is-verified {
    display: none;
  }
}

.not-filled  {
  color: $color-red-500;
}

.filled {
  color: $color-neutral-900;
}

.custom-form-style {
  ::v-deep .dynamic-form-item {
    margin-top: 8px
  }
}
</style>
