<template>
  <p v-if="show">
    <slot :label="item.name" :value="value" :required="required" :type="type"/>
  </p>
</template>

<script>
import {
  PREVIEW_VISIBLE_TYPES,
  isMapPointSelect,
  getPickUpPointFormItem,
  getPickUpScopeFormItem
} from '@src/utils'

export default {
  name: 'DynamicFormPreview',
  props: {
    item: {
      type: Object,
      default: () => ({})
    },
  },
  computed: {
    type() {
      const { style = {} } = this.item || {}
      return Number(style.type)
    },
    show() {
      // 平铺组件不需要展示
      return PREVIEW_VISIBLE_TYPES.includes(this.type)
    },
    value() {
      const content = this.item.content || ''
      const operation = this.item.operation || []
      const { style } = this.item || {}

      // 地图点选择器
      if (isMapPointSelect(style)) {
        const data = getPickUpPointFormItem(operation) || {}
        return data.location_name || ''
      }

      // select、multi_select
      // TODO: 多语言分隔符（因为自助修改订单没有这个场景，所以产品没有给出显示规则）
      if ([3, 4].includes(this.type)) {
        return operation.map(v => v.name).join(', ')
      }

      // checkbox
      // TODO: 因为自助修改订单没有这个场景，所以产品没有给出显示规则
      if (this.type === 5) {
        return content === '1' ? true : false
      }

      // mobile
      if (this.type === 8) {
        const selection = operation[0] || {}
        let countryCode = selection ? selection.country_number : ''

        // countryCode 兼容处理
        if (!countryCode && selection && selection.name) {
          try {
            countryCode = selection.name.split('+')[1].split(')')[0]
          } catch (error) {
            countryCode = ''
          }
        }

        if (!countryCode && !content) {
          return ''
        }

        return `${countryCode}-${content}`
      }

      // pick up scope
      if (this.type === 13) {
        const data = getPickUpScopeFormItem(content) || {}
        return data.location_name || ''
      }

      // text、date、datetime、input、time
      return content
    },
    required() {
      const { required } = this.item.style || {}
      return !!required
    }
  },
}
</script>
