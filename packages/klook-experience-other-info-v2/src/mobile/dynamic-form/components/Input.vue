<template>
  <component :is="componentType" :data="data" v-bind="$attrs" v-on="$listeners" />
</template>

<script>
import DynamicFormEmailSuffix from './input-item/email.vue'
import DynamicFormNormalInput from './input-item/input.vue'

export default {
  name: 'DynamicFormInput',
  components: {
    DynamicFormEmailSuffix,
    DynamicFormNormalInput,
  },
  props: {
    data: {
      type: Object,
      required: true,
    },
  },
  computed: {
    componentType() {
      // this.data?.style?.option_all_type === 1
      if (this.data.field_key === 'email') {
        return 'DynamicFormEmailSuffix'
      }
      return 'DynamicFormNormalInput'
    },
  },
}
</script>
