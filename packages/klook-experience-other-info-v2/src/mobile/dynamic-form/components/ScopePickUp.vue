<template>
  <div class="dynamic-form-scope-pick-up-input">
    <div class="form">
      <FakeSelect
        :value="inputVal"
        :placeholder="$t('104032')"
        :suffix="!autoSelect"
        @click.native="showMap"
      />
      <div
        v-for="(item, index) in autoGenForm"
        :key="index"
        class="form-field-wrap"
        @click="showMap"
      >
        <div>{{ item.title }}</div>
        <div>{{ item.value }}</div>
      </div>
    </div>

    <div v-if="tipsText" class="dynamic-form-scope-pick-up-input-tips">
      <InfoSvg class="dynamic-form-scope-pick-up-input-tips-icon" />
      <span class="dynamic-form-scope-pick-up-input-tips-text">
        {{ tipsText }}
      </span>
    </div>

    <PickUpInfo
      ref="pickUpInfoRef"
      :init-location-info="locationInfo"
      :map-data="jsonDatas"
      :package-id="packageId"
      :is-scope="true"
      @confirm="handleConfirm"
    />
  </div>
</template>

<script>
import Input from '@klook/klook-ui/lib/input'
import '@klook/klook-ui/lib/styles/components/input.scss'
import InfoSvg from '@src/imgs/icon-info.svg'
import PickUpInfo from '@src/mobile/components/pick-up-points/index.vue'
import cloneDeep from 'lodash/cloneDeep'
import PickupScopeMixin from '@src/mixins/pick-up-scope.js'

export default {
  name: 'DynamicFormScopePickUp',
  mixins: [PickupScopeMixin],
  components: {
    KInput: Input,
    InfoSvg,
    PickUpInfo
  },
  inject: {
    $formItem: {
      default: null,
    }
  },
  props: {
    value: {
      type: String,
      default: '',
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      locationInfo: null
    }
  },
  methods: {
    showMap() {
      if (this.disabled || this.autoSelect) {
        return
      }
      this.$refs.pickUpInfoRef.showMap()
    },
    handleConfirm(info) {
      info.isFreeTextLocation && delete info.isFreeTextLocation

      const formatInfo = this.formatInfo(cloneDeep(info))
      this.$emit('input', formatInfo)
      this.$emit('change', formatInfo)

      // 这里要去看klook-ui/form-item的源码才知道
      if (this.$formItem) {
        this.$formItem.$emit('change')
      }
    },
    formatInfo(info) {
      return JSON.stringify(info)
    }
  },
  watch: {
    formItemValue: {
      immediate: true,
      deep: true,
      handler(val) {
        if (val) {
          const cloneData = cloneDeep(val)
          this.locationInfo = val.location ? cloneData : null
        }
      }
    }
  },
  mounted() {
    if (this.autoSelect) {
      this.handleConfirm(this.jsonDatas[0])
    }
  }
}
</script>

<style lang="scss" scoped>
.dynamic-form-scope-pick-up-input {
  position: relative;

  &-tips {
    margin-top: 8px;
    display: flex;
    align-items: flex-start;

    &-icon {
      flex: none;
      margin-right: 8px;
      width: 16px;
      height: 16px;
    }

    &-text {
      color: $color-neutral-700;
      font-size: 12px;
      line-height: 1.5;
    }
  }
}

.form {
  .form-item {
    margin-bottom: 0;
  }

  .form-field-wrap {
    padding: 16px 0;
    display: flex;
    justify-content: space-between;
    border-bottom: 1px solid $color-neutral-300;
    @include font-body-s-regular;

    &.tips-wrap {
      color: $color-text-secondary;
      border-bottom: none;
      padding-bottom: 0;
    }

    .tips-content {
      flex: 1;
      padding-left: 8px;
      @include font-caption-m-regular;
    }
  }

  .form-head {
    padding: 16px 0 8px 12px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    @include font-body-m-bold;

    &:before {
      content: "";
      position: absolute;
      left: 0;
      top: 20px;
      width: 4px;
      height: 14px;
      border-radius: $radius-s;
      background-color: $color-orange-500;
    }
  }
}
</style>