<template>
  <div class="dynamic-form-textarea">
    <k-input
      v-model="textareaVal"
      type="textarea"
      words-count
      :placeholder="data.hint"
      :maxlength="maxLen"
      :disabled="disabled"
      :words-count-function="wordsCountFunction"
      @change="val => $emit('change', val)"
    />
  </div>
</template>

<script>
import Input from '@klook/klook-ui/lib/input'
import '@klook/klook-ui/lib/styles/components/input.scss'

export default {
  name: 'DynamicFormTextarea',
  components: {
    KInput: Input,
  },
  props: {
    value: {
      type: String,
      default: '',
    },
    data: {
      type: Object,
      default: () => ({}),
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    maxLen() {
      const { max_len } = this.data.style.match_rule || {}
      return max_len || 100
    },
    textareaVal: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      },
    },
  },
  methods: {
    wordsCountFunction(wordCount) {
      return `${(wordCount || '').length}`
    },
  },
}
</script>

<style lang="scss" scoped>
.dynamic-form-textarea {
  margin-top: 6px;
}
</style>
