<template>
  <div class="dynamic-form-pick-up-select">
    <FakeSelect
      :value="inputVal"
      :placeholder="$t('104032')"
      :suffix="!autoSelect"
      @click.native="showMap"
    />
    <div
      v-for="(item, index) in autoGenForm"
      :key="index"
      class="form-field-wrap"
      @click="showMap"
    >
      <div>{{ item.title }}</div>
      <div>{{ item.value }}</div>
    </div>
    <div v-if="tipsText" class="dynamic-form-pick-up-select-tips">
      <InfoSvg class="dynamic-form-pick-up-select-tips-icon" />
      <span class="dynamic-form-pick-up-select-tips-text">
        {{ tipsText }}
      </span>
    </div>

    <PickUpInfo
      ref="pickUpInfoRef"
      :init-location-info="locationInfo"
      :map-data="data.json_datas || []"
      :package-id="packageId"
      @confirm="handleConfirm"
    />
  </div>
</template>

<script>
import { Select, Option } from '@klook/klook-ui/lib/select'
import '@klook/klook-ui/lib/styles/components/select.scss'
import InfoSvg from '@src/imgs/icon-info.svg'
import PickUpInfo from '@src/mobile/components/pick-up-points/index.vue'
import { getPickUpPointFormItem } from '@src/utils'
import PickupScopeMixin from '@src/mixins/pick-up-scope.js'

export default {
  name: 'DynamicFormPickUpSelect',
  mixins: [PickupScopeMixin],
  components: {
    KSelect: Select,
    InfoSvg,
    PickUpInfo,
    KOption: Option
  },
  props: {
    value: {
      type: Array,
      default: () => [],
    },
    data: {
      type: Object,
      default: () => ({}),
    },
    disabled: {
      type: Boolean,
      default: false,
    }
  },
  data() {
    return {
      mapData: [],
      locationInfo: null,
    }
  },
  computed: {
    formItemValue() {
      return getPickUpPointFormItem(this.value)
    }
  },
  methods: {
    showMap() {
      if (this.disabled || this.autoSelect) {
        return
      }
      this.$refs.pickUpInfoRef.showMap()
    },
    handleConfirm(info) {
      const target = this.jsonDatas.find(item => item.location_name === info.location_name)
      this.locationInfo = target
      this.$emit('input', [target])
      this.$emit('change', [target])
    }
  },
  watch: {
    formItemValue: {
      immediate: true,
      deep: true,
      handler(val) {
        val && val.location_name && (this.locationInfo = val)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.dynamic-form-pick-up-select {
  &-tips {
    margin-top: 8px;
    display: flex;
    align-items: flex-start;

    &-icon {
      flex: none;
      margin-right: 8px;
      width: 16px;
      height: 16px;
    }

    &-text {
      color: $color-neutral-700;
      font-size: 12px;
      line-height: 1.5;
    }
  }

  &-option {
    visibility: hidden;
  }
}

.form-field-wrap {
  padding: 16px 0;
  display: flex;
  justify-content: space-between;
  border-bottom: 1px solid $color-neutral-300;
  @include font-body-s-regular;
}
</style>
