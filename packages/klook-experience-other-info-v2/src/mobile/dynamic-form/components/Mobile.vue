<template>
  <div class="dynamic-form-mobile">
    <DynamicFormItem :data="data" :prop="prop" value-type="operation" class="area-code" v-bind="$attrs" @validate="handleValidate">
      <DynamicFormSelect
        v-model="data.operation"
        :data="data"
        :disabled="disabled"
        label-type="areaCode"
        @change="val => $emit('change', val)"
      />
    </DynamicFormItem>

    <DynamicFormItem :data="data" :prop="prop" value-type="content" class="phone-number" v-bind="$attrs" @validate="handleValidate">
      <DynamicFormInput
        v-model="data.content"
        :data="data"
        :disabled="disabled"
        @change="val => $emit('change', val)"
        @input="val => $emit('input', val)"
      />
    </DynamicFormItem>
  </div>
</template>

<script>
import DynamicFormItem from '../DynamicFormItem.vue'
import DynamicFormInput from './Input.vue'
import DynamicFormSelect from './Select.vue'

export default {
  name: 'DynamicFormMobile',
  components: {
    DynamicFormItem,
    DynamicFormInput,
    DynamicFormSelect,
  },
  props: {
    // 由于有dynamic-form-item，所以需要传prop
    prop: {
      type: String,
      default: '',
    },
    data: {
      type: Object,
      default: () => ({}),
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    originalPhone: {
      type: String,
      default: null,
    },
  },
  computed: {
    currentPhone() {
      const content = this.data.content || ''
      const operation = this.data.operation || []
      return content && operation.length > 0 ? `${operation[0].field_key}-${content}` : null
    },
    verified() {
      const { verify_state } = this.data.style || {}
      return !!verify_state && this.originalPhone && this.originalPhone === this.currentPhone
    },
  },
  watch: {
    verified: {
      immediate: true,
      handler(val) {
        this.$emit('verifiedChange', val)
      },
    },
  },
  methods: {
    handleValidate(...args) {
      this.$emit('validate', ...args)
    },
  },
}
</script>

<style lang="scss" scoped>
.dynamic-form-mobile {
  display: flex;
  justify-content: space-between;
}

.area-code {
  margin-right: 10px;
  width: 50%;
}

.phone-number {
  width: 50%;
  margin-left: 10px;
}
</style>
