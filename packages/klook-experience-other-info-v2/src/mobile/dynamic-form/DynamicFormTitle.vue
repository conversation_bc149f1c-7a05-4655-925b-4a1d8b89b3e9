<template>
  <div v-if="data.name" class="label-title">
    <span class="label-title-text" v-html="getTitleHtml(data.name)"></span>
    <span v-if="phoneVerified" class="is-verified">({{ $t('71766') }})</span>
    <span v-if="data.style.required" class="is-required">*</span>
    <TipsXsSvg
      v-if="data.hover"
      href="icon_tips_tips_xs"
      class="traveler-tips-icon"
      @click.native="bottomSheetVisible = true"
    />
    <klk-bottom-sheet  :visible.sync="bottomSheetVisible" title="" :show-close="true">
      <klk-button slot="footer" block @click.native="bottomSheetVisible = false">
        {{ $t('11976') }}
      </klk-button>
      <div class="demo-content">
        {{ data.hover }}
      </div>
    </klk-bottom-sheet>
  </div>
</template>

<script>
import BottomSheet from '@klook/klook-ui/lib/bottom-sheet'
import Button from '@klook/klook-ui/lib/button'
import TipsXsSvg from '@src/imgs/tips-xs.svg'
import { getTitleHtml } from '@src/utils'

export default {
  name: 'DynamicFormTitle',
  components: {
    'klk-bottom-sheet': BottomSheet,
    'klk-button': Button,
    TipsXsSvg,
  },
  props: {
    data: {
      type: Object,
      default: () => ({}),
    },
    phoneVerified: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      bottomSheetVisible: false,
    }
  },
  methods: {
    getTitleHtml
  }
}
</script>

<style lang="scss" scoped>
.label-title {
  line-height: 16px;
  font-size: $fontSize-caption-m;
  color: $color-text-primary;
  display: flex;
  justify-content: flex-start;
  align-items: center;

  &-text ::v-deep a {
    cursor: pointer;
    color: $color-text-link;
    text-decoration: none;
  }
}

.is-verified {
  margin-left: 5px;
  color: $color-success;
}



.is-required {
  color: $color-brand-primary;
  margin-left: 3px;
}

.traveler-tips-icon {
  width: 16px;
  height: 16px;
  color: $color-text-primary;
  margin-left: 5px;
}

.demo-content {
  min-height: 40vh;
}
</style>
