<template>
  <klk-map ref="mapRef" v-bind="mapConfig">
    <klk-map-geo-fence
      v-for="(conf, index) in geoFenceConf"
      :key="index"
      :geo-fence-conf="conf"
      :isWgs84="true"
    />
    <klk-map-marker
      v-for="(marker, index) in markerList"
      :key="index"
      :center="marker.center"
      :options="marker.options"
      :class="{
        'marker-active': currentMarker === index,
        'is-area': isArea(marker)
      }"
      :z-index="isArea(marker) ? 200 : currentMarker === index ? 202 : 100"
      v-bind="getBindTracker(marker.tracker)"
    >
      <template v-if="!isArea(marker)">
        <img
          v-show="currentMarker != index"
          class="icon-img"
          :src="marker.groupIcon.unselect"
          @click="markerClick(index)"
        />
        <div v-show="currentMarker === index" class="icon-img-wrap">
          <div
            v-if="marker.isTemporary && marker.tips_text"
            class="pick-up-map-area-marker-wrap"
          >
            <div
              class="pick-up-map-area-marker"
              :style="marker.style"
            >
              {{ marker.tips_text }}
            </div>
            <div class="marker-after" :style="marker.style"></div>
          </div>
          <img
            class="icon-img"
            :class="{ active: currentMarker === index }"
            :src="marker.groupIcon.select"
          />
        </div>
      </template>
      <div
        class="pick-up-map-area-marker-wrap"
        v-else-if="marker.poptip"
      >
        <div
          class="pick-up-map-area-marker"
          :style="marker.style"
        >
          {{ marker.poptip }}
        </div>
        <div class="marker-after" :style="marker.style"></div>
      </div>
      <div v-else></div>
    </klk-map-marker>

    <div
      v-if="showMapCard"
      class="map-card-box"
    >
      <MapCard
        :info="currentSelectedLocation"
        :init-location-info="initLocationInfo"
        @confirm="handleConfirm"
      />
    </div>

    <div class="top-operator-box">
      <KlkMapCircleButton
        v-show="onlyPoints || !showMapCard"
        class="close-button"
        data-spm-module="CloseMap?trg=manual"
        data-spm-virtual-item="__virtual"
        @click.native="$emit('close')"
      >
        <klk-icon type="icon_navigation_close_m" size="20"></klk-icon>
      </KlkMapCircleButton>
      <KlkMapCircleButton
        v-show="!onlyPoints && showMapCard"
        @click.native="handleBack"
      >
        <klk-icon type="icon_navigation_chevron_left_xs" size="18"></klk-icon>
      </KlkMapCircleButton>
      <klk-button
        v-show="!showMapCard"
        :disabled="!currentSelectedLocation"
        size="small"
        @click="handleConfirm(currentSelectedLocation)"
      >
        {{ $t('161203') }}
      </klk-button>
    </div>

    <!-- 只有点的时候，不需要搜索功能 -->
    <!-- v-if="!onlyPoints" -->
    <template>
      <div
        v-show="!showMapCard"
        :class="[visible && 'active', 'search-full-srceen-modal']"
      >
        <KeyWordsSearch
          ref="keywordsSearchRef"
          :selected-item="currentSelectedLocation"
          :package-id="packageId"
          :is-scope="isScope"
          :mapData="searchData"
          @selected="handleSelected"
          @setFreeTextAsPickupPoint="point => $emit('confirm', point)"
          @clear="handleClear"
          @keywordsChange="handleKeywordsChange"
        />
      </div>
    </template>

    <KlkMapUserLocation
      v-show="!showMapCard"
      v-bind="getBindTracker({
        type: 'module',
        spm: 'OtherInfoMapFindMyPositionButton',
        exposure: false
      })"
      class="user-location"
      :style="locationBottom"
    />
  </klk-map>
</template>

<script>

import {
  KlkMap,
  KlkMapMarker,
  KlkMapCircleButton,
  KlkMapGeoFence,
  mapUtils,
  KlkMapUserLocation
} from "@klook/map";
import "@klook/map/dist/esm/index.css";

import KlkIcon from "@klook/klook-ui/lib/icon";
import "@klook/klook-ui/lib/styles/components/icon.scss";

import '@klook/klook-ui/lib/styles/components/button.scss'
import KlkButton from '@klook/klook-ui/lib/button'

import MapCard from "./map-card.vue";

import SearchGlassIcon from "@src/imgs/search-glass-icon.svg";
import SearchClearIcon from "@src/imgs/search-clear-icon.svg";
import KeyWordsSearch from "./keywords-search.vue"

import MapBase from '@src/mixins/map'

import cloneDeep from 'lodash/cloneDeep'

export default {
  mixins: [MapBase],
  name: 'PickUpPointMap',
  props: {
    mapData: {
      type: Array,
      default: () => []
    },
    initLocationInfo: {
      type: Object,
      default: null
    },
    packageId: {
      type: Number,
      default: 0
    },
    isScope: {
      type: Boolean,
      default: false
    }
  },
  inject: {
    mapType: { default: 'google' },
    customAxios: { default: () => null }
  },
  components: {
    KlkMap,
    KlkMapMarker,
    KlkMapCircleButton,
    MapCard,
    SearchGlassIcon,
    SearchClearIcon,
    KeyWordsSearch,
    KlkIcon,
    KlkMapGeoFence,
    KlkMapUserLocation,
    KlkButton
  },
  computed: {
    mapConfig() {
      const mapType = this.mapType;
      const center = this.mapData[0]?.location || '';

      if (center) {
        const [lat = "", lng = ""] = center.split(",");

        return {
          type: mapType,
          // 本地调试地图需要打开该token, 开发环境记得注释掉
          // googleConf: {
          //   token: "AIzaSyByoaOJMATcSHo6iZ-cofp9vlHU8t64ukw",
          //   libraries: 'drawing,places'
          // },
          height: "100%",
          interactive: "greedy",
          zoom: 16,
          center: `${lng},${lat}`,
        }
      }
    },
    locationPointList() {
      return this.mapData.map(item => mapUtils.formatLatLng(item.location))
    },
    fencePointList() {
      const list = this.geoFenceConf.reduce((accu, curr) => {
        (curr.polygonList || []).forEach(list => {
          if (Array.isArray(list)) {
            if (Array.isArray(list[0])) {
              accu.push(...list.map(item => mapUtils.formatLatLng(item.reverse().join(','))))
            } else if (list.length === 2) {
              accu.push(mapUtils.formatLatLng(list.reverse().join(',')))
            }
          }
        })
        return accu
      }, [])
      return Object.freeze(list)
    },
    locationBottom() {
      const bottom = this.onlyPoints ? '16px' : 'calc(40vh + 16px)'
      return {
        bottom
      }
    },
    // 只存在点，没有围栏数据
    onlyPoints() {
      return !this.geoFenceConf.length
    }
  },
  data() {
    return {
      visible: false,
      currentSelectedLocation: null,
      currentMarker: -1,
      $map: undefined,
      showMap: true,
      geoFenceConf: [],
      markerList: [],

      showMapCard: false,
      unabledMarkerClick: false
    }
  },
  methods: {
    handleKeywordsChange(v) {
      this.unabledMarkerClick = !!v
    },
    init() {
      const initLocationInfo = this.initLocationInfo
      const mapData = cloneDeep(this.mapData || [])
      let index = -1
      if (initLocationInfo) {
        index = (this.mapData || []).findIndex(item => item.location === initLocationInfo.location)
        if (index === -1) {
          mapData.unshift(initLocationInfo)
        } else {
          const [delData] = mapData.splice(index, 1)
          mapData.unshift(delData)
        }
        index = 0
      }
      this.currentMarker = index
      this.currentSelectedLocation = initLocationInfo
      this.searchData = mapData
      return mapData.map(item => this.formatMarker(item, this.isArea(item)))
    },
    handleBack() {
      this.currentMarker = -1
      this.currentSelectedLocation = null
      this.showMapCard = false
    },
    initMap() {
      if (this.$refs.mapRef) {
        this.$refs.mapRef.$getMapTool.then(async ($map) => {
          this.$map = $map;

          // 隐藏默认点marker，其他地图元素也可以用这个方法
          if (typeof this.$map?.map?.setOptions === "function") {
            const noPoi = [
              {
                featureType: "poi",
                elementType: "labels.icon",
                stylers: [{ visibility: "off" }],
              },
            ];
            this.$map.map.setOptions({ styles: noPoi });
          }

          await this.fitMapBounds()
          this.flyTo(this.currentMarker)
        });
      }
    },
    async fitMapBounds(center = null) {
      if (this.$map) {
        const points = center ? [center] : [...this.fencePointList, ...this.markerList.map((item) => item.center)]
        if (points.length) {
          const box = await mapUtils.turfBbox(points, this.mapType)
          const bounds = this.$map.createBounds(box.sw, box.ne, { formatLngLat: false })
          this.$map.fitBounds(bounds, {
            padding: {
              top: 40,
              left: 40,
              bottom: (!this.onlyPoints || center) ? 340 : 40,
              right: 40,
            },
          })
        }
      }
    },
    markerClick(index) {
      if (this.unabledMarkerClick) {
        return
      }
      this.currentMarker = index;
      this.flyTo(index);

      const { data } = this.markerList[index]
      this.currentSelectedLocation = data
      // this.showMapCard = true
    },
    flyTo(index) {
      const marker = this.markerList[index];
      if (!marker || !this.$map) {
        return;
      }
      const latLng = marker.center;
      this.$map.flyTo(latLng)
    },
    handleClear() {
      this.markerList = this.markerList.filter(marker => !marker.isTemporary)
      this.currentSelectedLocation = null
      this.currentMarker = -1
      this.$nextTick(() => {
        this.fitMapBounds()
      })
    },
    showModal() {
      // 动画
      this.$nextTick(() => {
        this.visible = true
        // 自动聚焦，防止页面抖动
        setTimeout(() => {
          this.$refs.keywordsSearchRef?.focusInput?.()
        }, 400)
      }, 400)
    },
    handleSelected(data) {
      this.currentSelectedLocation = data
      this.addMarker(data)
      // const index = this.markerList.findIndex(item => item.location === data.location)
      // if (index >= 0) {
      //   this.flyTo(index)
      //   this.currentMarker = index
      // }
    },
    addMarker(data) {
      this.markerList = this.markerList.filter(marker => !marker.isTemporary)
      let index = this.markerList.findIndex(item => item.location === data.location)

      if (index === -1) {
        const target = this.formatMarker(data, true)
        this.markerList.push(target)
        index = this.markerList.length - 1
      }
      // this.fitMapBounds(this.markerList[index]?.center ?? null)
      this.currentMarker = index
      this.flyTo(index)
    },
    handleConfirm(info) {
      this.$emit('close')
      this.$emit("confirm", info)
    },
    async getGeoFenceConf() {
      const areaIdList = this.getValidAreaId(this.mapData || [])
      if (areaIdList.length) {
        const res = await this.getGeomByIdList({ area_id_list: areaIdList.join(',') })
        this.geoFenceConf =  this.initFenceData(res?.result?.geom_list || [], this.mapData)
      }
    },
    async initMarkerAndGeoFence() {
      await this.getGeoFenceConf()
      this.markerList = this.init()
    },
    formatMarker(data, isTemporary = false) {
      return this.initMarker(data, isTemporary)
    }
  },
  async mounted() {
    await this.initMarkerAndGeoFence()
    this.initMap()
  }
}
</script>

<style lang="scss" scoped>
@import '../../../scss/map.scss';

.icon-img {
  width: 38px;
  height: 44px;
  object-fit: contain;

  &.active {
    transform: scale(1.4);
    transform-origin: 50% bottom;
    transition: transform ease-in-out 0.25s;
  }
}

.top-operator-box {
  top: 0;
  padding: 20px;
  width: 100%;
  position: absolute;
  display: flex;
  align-items: center;
  justify-content: space-between;

  .close-button {
    flex: none;
  }
}

.marker-item-outer {
  box-shadow: $shadow-normal-4;
  border: 1px solid $color-border-normal;
  border-radius: $radius-circle;
  position: relative;
  &.active {
    border: 2px solid $color-border-normal;
    transform: scale(1.4);
    transform-origin: 50% bottom;
    transition: transform ease-in-out 0.25s;
    .marker-item-outer-foot {
      border: 2px solid $color-border-normal;
    }
  }
  .marker-item-outer-foot {
    width: 12px;
    height: 12px;
    transform: translateX(-50%) rotate(45deg);
    transform-origin: center;
    position: absolute;
    bottom: -4px;
    left: 50%;
    z-index: 1;
    border: 1px solid $color-border-normal;
    border-radius: 1px;
  }
  .marker-item {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    border-radius: $radius-circle;
    z-index: 2;
  }
}

::v-deep .klkMap_marker.marker-active {
  z-index: 202 !important;
}

::v-deep .klkMap_marker.is-area {
  z-index: 200 !important;
}

// 新版本搜索样式
.search-full-srceen-modal {
  position: fixed;
  width: 100vw;
  bottom: 0;
  left: 0;
  z-index: 3333;
  min-height: 40vh;
  max-height: 75vh;
  background: $color-bg-1;
  border-radius: $radius-xxl $radius-xxl 0 0;
  transition: transform 0.3s ease-in-out;
  transform: translateY(200%);

  &.active {
    transform: translateY(0);
  }
}

.icon-img-wrap {
  position: relative;

  .pick-up-map-area-marker-wrap {
    top: -56px;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
  }
}

.user-location {
  bottom: 174px;
  left: unset !important;
  right: 16px;
}
</style>
