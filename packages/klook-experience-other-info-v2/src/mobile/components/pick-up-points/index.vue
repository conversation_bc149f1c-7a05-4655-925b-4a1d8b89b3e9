<template>
  <FullScreenModal
    ref="fullScreenModalRef"
    :visible.sync="visible"
    :data-spm-page="dataPageSpm"
  >
    <Map
      ref="mapRef"
      v-if="createdMap"
      :map-data="mapData"
      :map-type="mapType"
      :init-location-info="initLocationInfo"
      :package-id="packageId"
      :is-scope="isScope"
      @close="visible = false"
      @confirm="info => $emit('confirm', info)"
    />
  </FullScreenModal>
</template>

<script>
import Map from './map.vue'
import FullScreenModal from './full-screen-modal.vue'
import inhouseMixin from '@src/utils/mixins/inhouse'

export default {
  name: 'PickUpMobile',
  mixins: [inhouseMixin],
  props: {
    mapData: {
      type: Array,
      default: () => []
    },
    initLocationInfo: {
      type: Object,
      default: null
    },
    packageId: {
      type: Number,
      default: 0
    },
    isScope: {
      type: Boolean,
      default: false
    }
  },
  components: {
    Map,
    FullScreenModal
  },
  computed: {
   dataPageSpm() {
      return `PickUpMap?trg=manual&ext=${encodeURIComponent(JSON.stringify({
        Source: 'CheckoutPage',
        Type: this.isScope ? 'pick_up_scope': 'pick_up_fixed'
      }))}`
    }
  },
  data() {
    return {
      createdMap: false,
      visible: false
    }
  },
  methods: {
    showMap() {
      this.visible = true
      this.createdMap = true

      this.$nextTick(() => {
        const mapRef = this.$refs.mapRef
        if (mapRef) {
          mapRef.showModal()
        }
      })
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.$nextTick(() => {
          this.sendDataSpm('pageview', this.$refs.fullScreenModalRef?.$el, { force: true })
        })
      }
    }
  }
}
</script>