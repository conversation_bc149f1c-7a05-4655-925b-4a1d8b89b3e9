<template>
  <div class="map-card">
    <LocationInfoCard
      class="card-info" :info="info || initLocationInfo"
      :show-copy-icon="true"
    />

    <klk-button
      v-bind="buttonAttrs"
      block
      class="map-card-btn"
      :disabled="isSelected"
      @click.native.stop="handleClick"
    >
      <div class="map-card-btn-content">
        <span class="map-card-btn-content-text">
          {{ $t(btnText) }}
        </span>
      </div>
    </klk-button>
  </div>
</template>

<script>
import isEmpty from 'lodash/isEmpty'
import LocationInfoCard from "./location-info.vue";
import ConfirmLoctionArrow from '@src/imgs/confirm-location-arrow.svg';
import Button from '@klook/klook-ui/lib/button'
import '@klook/klook-ui/lib/styles/components/button.scss'

export default {
  name: 'PickUpInfoMapCard',
  props: {
    info: {
      type: Object,
      default: null
    },
    initLocationInfo: {
      type: Object,
      default: null
    }
  },
  components: {
    LocationInfoCard,
    ConfirmLoctionArrow,
    KlkButton: Button
  },
  data() {
    return {
      btnText: '',
      disabled: false,
      isSelected: false
    }
  },
  computed: {
    buttonAttrs() {
      let type = 'primary'

      if (this.info && !isEmpty(this.info)) {
        const { data_type, location } = this.info

        if (this.initLocationInfo && location === this.initLocationInfo?.location) {
          this.btnText = '104311'
          this.isSelected = true

          return {
            type: 'primary'
          }
        }

        // data_type: 1:free_pick_up 2:extra_fee_need  3:not offered
        this.disabled = data_type === 3
        this.btnText = this.disabled ? '104310' : '104309'
        this.isSelected = false

        return {
          disabled: this.disabled,
          type
        }
      } else if (this.initLocationInfo) {
        this.btnText = '104311'
        this.isSelected = true

        return {
          type: 'primary'
        }
      }

      return {
        disabled: false,
        type
      }
    }
  },
  methods: {
    handleClick() {
      if (this.disabled) {
        return
      }

      if (this.info || this.initLocationInfo) {
        this.$emit('confirm', this.info || this.initLocationInfo)
      }
    }
  }
}

</script>

<style lang="scss" scoped>
.map-card {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  border-radius: 20px 20px 0px 0px;
  box-shadow: 0px -1px 8px 0px rgba(0, 0, 0, 0.11);
  padding: 20px 20px 8px;
  z-index: 20001;

  &-btn {
    margin-top: 16px;
    border-radius: 12px;
    line-height: 21px;

    &-content {
      display: flex;
      align-items: center;
      justify-content: center;

      &-text {
        font-size: $fontSize-body-m;
        font-weight: $fontWeight-bold;
        line-height: 1.5;
        display: inline-block;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      &-icon {
        flex: none;
        margin-left: 8px;
      }
    }
  }
}

</style>

