<template>
  <div class="keywords-search-modal" data-spm-page="LocationSearch_ResultPage">
   <div class="keywords-search-modal-header">
      <div class="keywords-search-modal-header_right">
        <SearchGlassIcon class="search-icon"/>
        <input
          ref="searchInput"
          v-model.trim="keywords"
          class="search-input"
          :placeholder="$t('104304')"
          @input="handleInput"
          @focus="handleFocus"
          @blur="handleBlur"
        />
        <div class="icon-box">
          <SearchClearIcon
            v-show="keywords"
            class="search-clear-icon"
            @click.native.stop="handleClear"
          />
        </div>
      </div>
      <div
        v-show="isFocused"
        class="keywords-search-modal-header_cancel-button"
        @click.stop="cancel"
      >
        {{ $t('161204') }}
      </div>
    </div>

    <div class="keywords-search-modal-body">
      <div v-if="status === 'fetching'" class="loading-box">
        <klk-loading
          show-overlay
          :overlay-color="'rgba(255, 255, 255, 0.7)'"
        ></klk-loading>
      </div>
      <div v-else>
        <div v-if="isNotMatch" class="search-keywords-none">
          <div v-if="isScope" class="search-keywords-none-scope">
            <p class="search-keywords-none-text"> {{ $t('104307') }}</p>
            <!-- <div class="search-keywords-none-scope-btn">
              <klk-button
                type="outlined"
                :size="'small'"
                data-spm-module="LocationSearch_NoResultFreeText?trg=manual"
                data-spm-virtual-item="__virtual"
                @click.native.stop="handleConfirmBtnClick"
              >
                <span>{{ $t('106269') }}</span>
              </klk-button>
            </div> -->
          </div>
          <div v-else class="search-keywords-none-point">
            <div class="search-keywords-none-text">
              {{  $t('104307') }}
            </div>
            <div v-if="hasResult" class="search-keywords-none-point-below">
              <div class="gap" />
              <p class="sugguest"> {{  $t('104308') }}</p>
            </div>
          </div>

        </div>
        <div class="search-keywords-result-list">
          <div
            v-for="(item, index) in renderList"
            :key="index"
            class="search-keywords-result-list-item"
            :data-spm-module="getSearchResultSpm(item)"
            data-spm-virtual-item="__virtual"
            :class="{'selected': checkIsActive(item) }"
            @click.stop="handleItemClick(item, index)"
          >
            <div class="search-keywords-result-list-item-info">
              <LocationInfoCard :info="item" />
            </div>
            <div class="search-keywords-result-list-item-right">
              <IconSelected v-if="checkIsActive(item)" class="icon-selected"/>
              <klk-loading
                v-if="currentLoadingIndex === index"
                show-overlay
                :overlay-color="'rgba(255, 255, 255, 0.7)'"
              ></klk-loading>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import ArrowBackIcon from "@src/imgs/arrow-back.svg";
import SearchClearIcon from "@src/imgs/search-clear-icon.svg";
import debounce from "lodash/debounce";
import KlkLoading from "@klook/klook-ui/lib/loading";
import Button from '@klook/klook-ui/lib/button'
import '@klook/klook-ui/lib/styles/components/button.scss'

import LocationInfoCard from "./location-info.vue";
import eventBus from '@src/utils/event-bus'

import SearchGlassIcon from "@src/imgs/search-glass-icon.svg";
import IconSelected from "@src/imgs/icon_selected.svg";
import PoiSearchMixin from "@src/mixins/poi-search.js";

export default {
  name: 'KeywordsSearch',
  mixins: [PoiSearchMixin],
  props: {
    selectedItem: {
      type: Object,
      default: null
    },
    packageId: {
      type: Number,
      default: 0
    },
    isScope: {
      type: Boolean,
      default: false
    },
    fillKeywords: {
      type: Boolean,
      default: false
    }
  },
  components: {
    SearchClearIcon,
    ArrowBackIcon,
    KlkLoading,
    LocationInfoCard,
    KlkButton: Button,
    SearchGlassIcon,
    IconSelected
  },
  computed: {
    hasResult() {
      return this.searchResultList?.length > 0
    }
  },
  data() {
    return {
      status: "default",
      isFocused: false
    }
  },
  methods: {
    checkIsActive(item) {
      const selectedItem = this.selectedItem
      if (!selectedItem) {
        return false
      }
      const location = item?.detail_data?.location ?? ''
      const selectedLocation = selectedItem?.location ?? ''
      return selectedLocation && location === selectedLocation
    },
    handleKeyWordChange: debounce(function() {
      this.$emit('clear')
      this.getSearchResult()
    }, 500),
    handleFocus() {
      setTimeout(() => {
        this.isFocused = true
      }, 0)
    },
    handleBlur() {
      setTimeout(() => {
        this.isFocused = false
      }, 0)
    },
    handleInput() {
      this.handleKeyWordChange()
    },
    reset() {
      this.keywords = ''
      this.searchResultList = []
      this.isNotMatch = false
    },
    async getSearchResult() {
      this.status = "fetching";
      this.isNotMatch = false

      const params = {
        input: this.keywords,
        package_id: this.packageId,
        show_meet_up: 1,
        current_google_place_id: this.selectedItem?.google_place_id || ''
      }

      this.searchResultList = await this.getSearchFun(params)
      this.isNotMatch = !this.searchResultList.length && !!this.keywords
      // const list = await this.getSearchFun(params)
      // this.searchResultList = this.setOrder([...list])
      this.status = 'success'
    },
    setOrder(data = []) {
      const selectedItem = this.selectedItem
      if (selectedItem) {
        const location = selectedItem.location
        const list = data.reduce((acc, curr) => {
          const currLoaction = curr.detail_data?.location ?? ''
          if (currLoaction === location) {
            acc.unshift(this.initResult(curr))
          } else {
            acc.push(curr)
          }
          return acc
        }, [])

        return list
      }

      return data
    },
    autoFocus() {
      if (this.keywords) {
        return
      }

      this.focusInput()
    },
    focusInput() {
      const searchInput = this.$refs.searchInput
      searchInput?.focus?.()
    },
    handleClear() {
      this.autoFocus()
      this.reset()
      this.$emit('clear')
    },
    cancel() {
      this.reset()
      this.$emit('cancel')
      this.$emit('clear')
    },
    async handleItemClick(item, index) {
      const { detail_data = null, place_id, is_default } = item || {}

      let detail = null
      this.currentLoadingIndex = index
      if (is_default || detail_data) {
        detail = detail_data
      } else {
        detail = await this.getPoiDetail({
          package_id: this.packageId,
          place_id,
        })
        
        this.$set(item, 'detail_data', detail)
      }
      this.currentLoadingIndex = -1
      const data_type = detail?.data_type ?? -1

      if (data_type === 3) {
        return this.$toast(this.$t('161196'))
      }
  
      this.stopSearchWatch = true
      this.$emit('selected', detail)
    },
    handleConfirmBtnClick() {
      const point = {
        pick_up_type: 2,
        location_name: this.keywords,
        supply_api_mapping_key: this.keywords,
        map_type: 1,
        isFreeTextLocation: true
      }

      eventBus.$emit('closeFullScreenModal')
      this.$emit('setFreeTextAsPickupPoint', point)
    },
    getSearchResultSpm(item) {
      const { data_type } = item
      let val = 'Available'

      if (data_type === 2) {
        val = 'Extra fee'
      } else if (data_type === 3) {
        val = 'Not offered'
      }

      return`LocationSearch_SelectResult?trg=manual&ext=${JSON.stringify({ InScope: val })}`
    }
  },
  watch: {
    selectedItem: {
      immediate: true,
      deep: true,
      handler(v) {
        const location_name = v?.location_name ?? ''
        if (location_name && !this.stopSearchWatch) {
          // this.keywords = location_name
          this.stopSearchWatch = false
          // this.handleKeyWordChange()
        }
      }
    }
  },
}
</script>

<style lang="scss" scoped>
.keywords-search-modal {
  display: flex;
  flex-direction: column;
  height: 40vh;

  &-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    height: 60px;
    align-items: center;
    padding: 0 20px;

    &_cancel-button {
      margin-left: 12px;
      @include font-body-s-bold;
    }

    &_left {
      .arrow-back-icon {
        flex: none;
        width: 20px;
        height: 20px;
        vertical-align: middle;
      }
    }

    &_right {
      display: flex;
      height: 36px;
      padding: 3px 3px 3px 12px;
      box-sizing: border-box;
      align-items: center;
      justify-content: space-between;
      flex: 1;
      padding: 0 10px;
      background: $color-bg-3;
      border-radius: $radius-xxl;
      border: 1px solid transparent;

      &.active {
        border: 1px solid $color-orange-500;
      }

      .search-icon {
        width: 20px;
        height: 20px;
        margin-right: 8px;
      }

      .search-input {
        outline: none;
        caret-color: $color-orange-500;
        border: none;
        width: 100%;
        background: transparent;
        width: calc(100% - 30px);
        white-space: nowrap;
      }

      .icon-box {
        margin-left: 12px;
        flex: none;
        display: flex;
        align-items: center;
        justify-content: flex-end;

        .search-clear-icon {
          width: 16px;
          height: 16px;
          flex: none;
        }
      }
    }
  }

  &-body {
    position: relative;
    height: calc(100% - 60px);
    overflow: hidden scroll;

    .loading-box {
      position: relative;
      height: 260px;
    }

    .search-keywords {
      &-result-list {
        padding-top: 12px;

        &-item {
          padding: 8px 20px;
          display: flex;
          align-items: center;

          &-info {
            flex: 1;
          }

          &-right {
            width: 20px;
            height: 20px;
            position: relative;
          }

          .icon-selected {
            width: 20px;
            height: 20px;
            flex: none;
          }

          &.selected {
            background: $color-brand-primary-light-2;
            // border-radius: $radius-m;
          }
        }
      }

      &-none {

        &-text {
          padding: 20px 12px;
          color: $color-neutral-700;
          font-size: $fontSize-body-s;
          line-height: 21px;
        }

        &-scope {

          &-btn {
            margin: 4px 20px 20px;
            text-align: center;
          }
        }

        &-point {
          &-below {
            .gap {
              width: 100vw;
              height: 8px;
              margin-left: -8px;
              background: $color-neutral-100;
            }

            .sugguest {
              font-weight: $fontWeight-semibold;
              font-size: $fontSize-body-s;
              line-height: 21px;
              color: $color-neutral-900;
              padding: 24px 12px 0;
            }
          }
        }
      }
    }
  }
}
</style>
