<template>
  <div
    class="traveller-panel"
    data-need-replace-dom="true"
    data-replace-node=".is-contact .other-info-module-mobile-header"
    :class="{ 'is-error': !!errorText && isGlobalCheck }"
  >
    <div
      class="traveller-panel-wrap"
      @click="handleClickEvent"
      :data-spm-module="
        `EditPage?ext=${JSON.stringify({
          ModuleType: sectionType,
          infotype: sectionType
        })}`
      "
      data-spm-virtual-item="__virtual"
    >
      <div class="traveller-panel-content">
        <DynamicFormPreview
          v-for="(item, itemIndex) in flattenedData"
          :key="`${itemIndex}-${item.id}`"
          :item="item"
          class="traveller-panel-row"
        >
          <template slot-scope="slotProps">
            <span
              :style="{
                'max-width': maxWidth === 0 ? 'auto' : `${maxWidth}px`,
                'width': minWidth === 0 ? 'auto' : `${minWidth}px`
              }"
              class="traveller-panel-row-label"
            >
              {{ slotProps.label }}
            </span>
            <span
              class="traveller-panel-row-value"
              :class="{
                'is-red': renderRedColor(slotProps),
                'is-grey':!slotProps.value
              }"
            >
             <span class="traveller-panel-row-value-content">{{ getText(slotProps) }}</span>
            </span>
          </template>
        </DynamicFormPreview>
      </div>
      <div class="traveller-panel-right">
        <div class="traveller-panel-edit">
          <span class="traveller-panel-edit-text">
            {{ $t('72687') }}
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import DynamicFormPreview from '@src/web/dynamic-form/DynamicFormPreview.vue'
import EditXsSvg from '@src/imgs/edit-xs.svg'
import ErrorSvg from '@src/imgs/error.svg'
import {
  isServer,
  traverseDynamicFormItem,
  validateFormItem,
  TRAVELLER_TYPE,
  CONTACT_TYPE,
  SELECT_ITEMS,
  INIT_TRAVELLER_ID,
  clearAllFilledData,
  checkGlobalFlag
} from '@src/utils'

export default {
  name: 'TravellerPanel',
  components: {
    EditXsSvg,
    ErrorSvg,
    DynamicFormPreview
  },
  props: {
    data: {
      type: Array,
      default: null
    },
    travellerList: {
      type: Array,
      default: null,
    },
    sectionType: {
      type: Number,
      default: 1,
    },
     originalPhone: {
      type: String,
      default: null,
    },
    travellerId: {
      type: Number,
      default: -1
    },
    originData: {
      type: Object,
      default: () => ({}),
    }
  },
  data() {
    return {
      minWidth: 0,
      maxWidth: 0,
      errorType: 0, // 0: 没有；1：add；2：empty；3：valid
      isGlobalCheck: false
    }
  },
  computed: {
    flattenedData() {
      const cloneData = this.travellerId === INIT_TRAVELLER_ID ? clearAllFilledData(this.data) : this.data
      const res = []
      traverseDynamicFormItem(cloneData || [], formItem => res.push(formItem))
      return res
    },
    errorText() {
      switch (this.errorType) {
        case 0:
          return ''
        case 1:
          return this.$t(this.isContact ? '75839' : '72648')
        case 2:
          return this.$t('72649')
        case 3:
          return this.$t('72650')
        default:
          return ''
      }
    },
    isTravellerListNull() {
      return Array.isArray(this.travellerList) && this.travellerList.length === 0
    },
    isTraveller() {
      return this.sectionType === TRAVELLER_TYPE
    },
    isContact() {
      return this.sectionType === CONTACT_TYPE
    },
    verified() {
      const { content = '', operation = [], style = {}} = this.data.find(item => item.style.type === 8) || {}
      const currentPhone = content && operation.length > 0 ? `${operation[0].field_key}-${content}` : null
      return this.isContact && !!style.verify_state && this.originalPhone && this.originalPhone === currentPhone
    }
  },
  watch: {
    travellerId: {
      immediate: true,
      handler(val) {
        if (val) {
          this.minWidth = 0

          this.$nextTick(() => {
            this.computeMaxWidth()
            this.computeWidth()
          })
        }
      }
    }
  },
  mounted() {
    this.createObserver()
  },
  methods: {
    renderRedColor(slotProps) {
      // 以下两种情况需要渲染为红色
      // 1、购买sku数量超过出行人数量
      // 2、选择未填写完整的出行人，直接关闭弹窗，未填写且必填的信息需要标红
      const { value, required } = slotProps || {}
      return (!value && required) && (this.isTravellerListNull || this.errorText)
    },
    handleClickEvent() {
      const action = this.travellerId === INIT_TRAVELLER_ID ? true : false
      this.$emit('edit', action)
    },
    clearValidate() {
      this.errorType = 0
    },
    validate() {
      this.isGlobalCheck = checkGlobalFlag()
      this.clearValidate()

      if (this.isTravellerListNull) {
        this.errorType = 1
        return Promise.reject()
      }

      traverseDynamicFormItem(this.data || [], formItem => {
        const { isEmpty, isInvalid } = validateFormItem(formItem)
        const { required } = formItem.style || {}

        if (isInvalid) {
          this.errorType = 3
        }

        if (required && isEmpty) {
          this.errorType = 2
        }
      })
      return this.errorType === 0 ? Promise.resolve(true) : Promise.reject()
    },
    getText({ value, required , type}) {
      if (value === '') {
        return required ?  this.$t(SELECT_ITEMS.includes(type) ? '12576' : '72640') : this.$t('72639')
      }

      return value
    },
    computeWidth() {
      if (!isServer && this.minWidth === 0) {
        const labelDoms = this.$el.querySelectorAll('.traveller-panel-row-label') || []
        const widthList = [].map.call(labelDoms, labelDom => labelDom.offsetWidth || 0)
        this.minWidth = Math.max(...widthList)

        // fix: 修复offsetWidth有小数点的情况
        if (this.minWidth > 0) {
          this.minWidth += 1
        }
      }
    },
    computeMaxWidth() {
      if (!isServer && this.maxWidth === 0) {
        const wrapDom = this.$el.querySelector('.traveller-panel-wrap') || {}
        const width = wrapDom.offsetWidth
        this.maxWidth = width > 45 ? (width - 45) / 2 : 0
      }
    },
    createObserver() {
      // 解决购物车 display:none 获取不到宽度的问题
      if ('IntersectionObserver' in window) {
        const elementObserver = new IntersectionObserver((entries) => {
          entries.forEach((entry) => {
            // 如果元素可见
            if (entry.intersectionRatio > 0) {
              this.computeMaxWidth()
              this.computeWidth()
            }
          })
        })

        elementObserver.observe(this.$el)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.traveller-panel {

  &.is-error{
    border: 1px solid $color-red-500 !important;
  }

  &-wrap {
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
    cursor: pointer;
  }

  &-row {
    margin-top: 8px;
    display: flex;
    align-items: flex-start;

    &:first-child {
      margin-top: 0;
    }

    &-label {
      @include font-body-s-regular;

      margin-right: 24px;
      display: inline-block;
      color: $color-text-primary;
    }

    &-value {
      @include font-body-s-semibold;

      flex: 1;
      color: $color-text-primary;
      word-break: break-word;
      display: flex;
      flex-wrap: wrap;

      &.is-grey {
        color: $color-text-placeholder;
      }

      &.is-red {
        color: $color-red-500 !important;
      }

      &-content {
        margin-right: 4px;
      }

      &-verify-flag {
        color: $color-success;
        font-size: $fontSize-body-s;
        font-weight: $fontWeight-semibold;
        line-height: 20px;
      }
    }
  }

  &-right {
    margin-left: 24px;
    display: flex;
    align-items: flex-end;
    flex: none;
  }

  &-edit {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    height: 20px;

    &-icon {
      margin-right: 4px;
      width: 16px;
      height: 16px;
      color: $color-text-link;
    }

    &-text {
      @include font-body-s-regular;
      color: $color-neutral-900;
      text-decoration: underline;
      font-weight: $fontWeight-semibold;
    }
  }

  &-error {
    margin-top: 8px;
    display: flex;
    align-items: flex-start;
    justify-content: flex-start;

    &-icon-wrap {
      display: flex;
      align-items: center;
      height: 20px;
    }

    &-icon {
      margin-right: 6px;
      width: 16px;
      height: 16px;
    }

    &-text {
      @include font-body-s-regular;
      color: $color-error;
    }
  }
}
</style>
