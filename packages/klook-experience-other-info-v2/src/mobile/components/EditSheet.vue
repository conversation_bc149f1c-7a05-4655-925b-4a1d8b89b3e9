<template>
  <div>
    <k-bottom-sheet
      :visible="sheetVisible"
      :title="title"
      :can-pull-close="false"
      :min-pull-distance="0"
      :mask-closable="false"
      transfer
      class="edit-sheet"
      v-on="$attrs"
      data-spm-module="EditPage"
    >
      <div slot="header-left" @mousedown.stop="closeEvent">
        <CloseSvg />
      </div>
      <div class="edit-sheet-content">
        <slot></slot>

        <div v-if="sensitiveTerms.length" class="edit-sheet-terms-box">
          <p v-html="formatSensitiveTerms()"></p>
        </div>
      </div>

      <k-button
        slot="footer"
        type="primary"
        block
        :loading="loading"
        @click="$emit('confirm')"
        :data-spm-item="
          `Save?ext=${JSON.stringify({
            ModuleType: sectionType,
            Clicktype: action,
            infotype: sectionType
        })}`"
      >
        {{ $t('72653') }}
      </k-button>
    </k-bottom-sheet>

    <klk-modal
      :open.sync="showTipModal"
      :lock-scroll="false"
      data-spm-page="TTD_Payment_LeavePage?trg=manual"
      class="tip-modal"
    >
      <p class="modal-content"> {{ $t('78448') }} </p>
      <div slot="footer" class="footer-box">
        <klk-button
          type="outlined"
          :size="'small'"
          :data-spm-module="`LeavePageAction?ext=${JSON.stringify({
            ActionType: 'Stay'
          })}`"
          data-spm-virtual-item="__virtual"
          @click="showTipModal = false"
        >
          {{ $t('78573') }}
        </klk-button>
        <klk-button
          type="primary"
          @click="closeAllModal"
          :size="'small'"
          :data-spm-module="`LeavePageAction?ext=${JSON.stringify({
            ActionType: 'Leave'
          })}`"
          data-spm-virtual-item="__virtual"
        >
          {{ $t('78572') }}
        </klk-button>
      </div>
  </klk-modal>
  </div>
</template>

<script>
import BottomSheet from '@klook/klook-ui/lib/bottom-sheet'
import Button from '@klook/klook-ui/lib/button'

import '@klook/klook-ui/lib/styles/components/bottom-sheet.scss'
import '@klook/klook-ui/lib/styles/components/button.scss'

import CloseSvg from "@src/imgs/modal-close.svg"
import cloneDeep from 'lodash/cloneDeep'
import { diffObjValue, isServer } from "@src/utils"
import inhouseMixin from '@src/utils/mixins/inhouse'

export default {
  name: 'EditSheet',
  mixins: [inhouseMixin],
  components: {
    KButton: Button,
    KBottomSheet: BottomSheet,
    CloseSvg
  },
  props: {
    title: {
      type: String,
      default: ''
    },
    visible: {
      type: Boolean,
      default: false
    },
    loading: {
      type: Boolean,
      default: false
    },
    sectionType: {
      type: Number,
      default: 1,
    },
    action: {
      type: String,
      default: ''
    },
    data: {
      type: [Object, Array]
    },
    sensitiveTerms: {
      type: [Array],
      default: () => []
    }
  },
  data() {
    return {
      sheetVisible: false,
      originData: null,
      showTipModal: false
    }
  },
  methods: {
    closeEvent() {
      if (this.action =='Edit') {
        const hasChange = diffObjValue(this.originData, this.data)
        if (hasChange) {
          // 有修改,弹出提示
          this.showTipModal = true
        } else {
          this.closeAllModal()
        }
      } else {
        // 正常模式，直接关闭
        this.closeAllModal()
      }
    },
    closeAllModal() {
      this.sheetVisible = false
      this.$emit('update:visible', false)
      this.originData && (this.originData = null)
      this.showTipModal && (this.showTipModal = false)
    },
    alert(text) {
      this.$toast(text)
    },
    formatSensitiveTerms() {
      if (this.sensitiveTerms.length) {
        return this.sensitiveTerms.reduce((p, o) => p += o, '')
      }

      return ''
    }
  },
  watch: {
    visible(val) {
      val && (this.sheetVisible = val)
    },
    data(val) {
      !this.originData && (this.originData = cloneDeep(val))
    },
    showTipModal(val) {
      if (!isServer && val) {
        this.$nextTick(() => {
          const target = this.$el?.querySelector('.tip-modal')
          this.sendDataSpm('pageview', target, { force: true })
        })
      }
    }
  },
}
</script>

<style lang="scss" scoped>
.edit-sheet {
  z-index: 1800;

  &-content {
    position: relative;
  }

  &-terms-box {
    @include font-caption-m-regular;
    color: $color-neutral-700;
  }

  ::v-deep .klk-bottom-sheet-inner {

    .klk-bottom-sheet-body {
      padding-top: 20px !important;
      border-top: 1px solid $color-neutral-200;
    }

    .klk-bottom-sheet-footer  {
      margin-left: 0;
      margin-top:0;
      margin-right: 0;
      padding: 10px 20px;
      border-top: 1px solid $color-neutral-200;
    }
  }
}
.tip-modal {

  &.klk-modal-wrapper {
    background: rgba(0, 0, 0, 0.38);

    .modal-content {
      line-height: 21px;
    }

    .footer-box {
      margin-top: 20px;
      text-align: right;

      .klk-button {
        margin-right: 16px;
        &:last-of-type {
          margin-right: 0;
        }
      }
    }
  }
}
</style>
