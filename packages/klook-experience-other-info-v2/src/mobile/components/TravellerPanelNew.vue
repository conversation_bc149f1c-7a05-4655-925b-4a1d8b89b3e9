<template>
  <div class="traveller-panel" :class="{ 'is-error': !!errorText }">
    <div
      class="traveller-panel-wrap"
      :data-spm-module="`EditPage?ext=${JSON.stringify({
        ModuleType: sectionType,
        infotype: sectionType,
      })}`"
      data-spm-virtual-item="__virtual"
    >
      <div class="traveller-panel-content">
        <div
          v-for="(item, parentIndex) in dataList"
          :key="item.key"
          class="item-box"
          :class="[{ 'is-error': item.invalidate }]"
          :data-need-replace-dom="!parentIndex"
          :data-parent-node="!parentIndex ? '.other-info-traveller-panel-new' : ''"
          :data-replace-node="!parentIndex ? '.other-info-traveller-title' : ''"
          :data-placeholder-node="!parentIndex ? '.other-info-traveller-selector' : ''"
          :data-index="parentIndex"
          :data-spm-module="item.key === -1 ? 'AddParticipant' : 'EditParticipant'"
          data-spm-virtual-item="__virtual"
          @click="$emit('edit', item.key)"
        >
          <div>
            <p
              v-if="showDefaultName(item)"
              class="default-name"
            >
              {{ getDefaultName(item.key) }}
            </p>
            <DynamicFormPreview
              v-for="(val, index) in item.value"
              :key="`${item.key}-${index}`"
              :item="val"
              class="traveller-panel-row"
            >
              <template slot-scope="slotProps">
                <span
                  :style="{
                    'max-width': maxWidth === 0 ? 'auto' : `${maxWidth}px`,
                    width: minWidth === 0 ? 'auto' : `${minWidth}px`,
                  }"
                  class="traveller-panel-row-label"
                >
                  {{ slotProps.label }}
                </span>
                <span
                  class="traveller-panel-row-value"
                  :class="{
                    'is-red': renderRedColor(slotProps, item),
                    'is-grey': !slotProps.value,
                  }"
                >
                  <span class="traveller-panel-row-value-content">{{ getText(slotProps) }}</span>
                </span>
              </template>
            </DynamicFormPreview>
          </div>
          <div class="traveller-panel-right">
            <div class="traveller-panel-edit" :class="[{ 'flex-between': showCloseSvg(item.key)}]">
              <div
                data-spm-module="DeleteTraveller"
                data-spm-virtual-item="__virtual"
                class="close-svg-box"
                @click.stop="closeEvent(item.key)">
                <CloseSvg class="close-svg" />
              </div>
              <span class="traveller-panel-edit-text">
                {{ $t('72687') }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import DynamicFormPreview from '@src/web/dynamic-form/DynamicFormPreview.vue'
import EditXsSvg from '@src/imgs/edit-xs.svg'
import ErrorSvg from '@src/imgs/error.svg'
import CloseSvg from '@src/imgs/icon_edit_close_xs.svg'
import cloneDeep from 'lodash/cloneDeep'

import {
  isServer,
  traverseDynamicFormItem,
  validateFormItem,
  TRAVELLER_TYPE,
  CONTACT_TYPE,
  SELECT_ITEMS,
  INIT_TRAVELLER_ID,
  EXTERNAL_NAME,
  clearAllFilledData,
  checkGlobalFlag
} from '@src/utils'

export default {
  name: 'TravellerPanel',
  components: {
    EditXsSvg,
    ErrorSvg,
    DynamicFormPreview,
    CloseSvg,
  },
  props: {
    data: {
      type: Array,
      default: null,
    },
    travellerList: {
      type: Array,
      default: null,
    },
    sectionType: {
      type: Number,
      default: 1,
    },
    originalPhone: {
      type: String,
      default: null,
    },
    travellerId: {
      type: [Number, String],
      default: -1,
    },
    cancleTravellerId: {
      type: [Number, String],
      default: -1,
    },
    noNeedUpdateData: {
      type: Boolean,
      default: false,
    },
    originData: {
      type: Object,
      default: () => ({}),
    }
  },
  data() {
    return {
      minWidth: 0,
      maxWidth: 0,
      errorType: 0, // 0: 没有；1：add；2：empty；3：valid
      dataList: [],
    }
  },
  computed: {
    errorText() {
      switch (this.errorType) {
        case 0:
          return ''
        case 1:
          return this.$t('72648')
        case 2:
          return this.$t('72649')
        case 3:
          return this.$t('72650')
        default:
          return ''
      }
    },
    isTravellerListNull() {
      return this.travellerList && this.travellerList.length === 0
    },
    isTraveller() {
      return this.sectionType === TRAVELLER_TYPE
    },
    isContact() {
      return this.sectionType === CONTACT_TYPE
    },
    verified() {
      const { content = '', operation = [], style = {} } = this.data.find(item => item.style.type === 8) || {}
      const currentPhone = content && operation.length > 0 ? `${operation[0].field_key}-${content}` : null
      return (
        this.isContact && !!style.verify_state && this.originalPhone && this.originalPhone === currentPhone
      )
    },
    noEnoughTraveller() {
      return this.isTravellerListNull || this.travellerList && this.travellerList.length < this.originData.count
    }
  },
  watch: {
    travellerId: {
      handler(val) {
        this.minWidth = 0
        this.$nextTick(() => {
          this.computeMaxWidth()
          this.computeWidth()
        })
      },
      immediate: true
    },
    dataList: {
      deep: true,
      handler() {
        this.$emit('updateTravellerData', { sectionType: this.sectionType })
      }
    },
    cancleTravellerId: {
      handler(val) {
        if (val === 'NA') return
        if (val) {
          this.$set(this, 'dataList', this.omit(val))
          if (!this.hasKey(INIT_TRAVELLER_ID)) {
            this.dataList.unshift(this.createEmptyItem())
          }
        }
      },
    },
    data: {
      handler(val, oldVal) {
        if (val !== oldVal) {
          this.setDataList(val)
          this.minWidth = 0
          this.$nextTick(() => {
            this.computeMaxWidth()
            this.computeWidth()
          })
        }
      },
      immediate: true
    },
  },
  mounted() {
    this.createObserver()
  },
  methods: {
    getDefaultName(key) {
      key = Number(key)
      if (key === INIT_TRAVELLER_ID) return null
      const target = this.travellerList.find(traveller => traveller.traveller_id === key)
      return target?.name || ''
    },
    showDefaultName({ key, value }) {
      if (key == INIT_TRAVELLER_ID) return false

      for (const val of value) {
        if (EXTERNAL_NAME.includes(val.field_key)) {
          return false
        }
      }

      return true
    },
    renderRedColor(props, item) {
      const { value, required } = props
      // 两种情况需要渲染为红色
      // 1、购买sku数量超过出行人数量
      // 2、选择未填写完整的出行人，直接关闭弹窗，未填写且必填的信息需要标红

      // 注意： 可以多选的项，需要定位到准确的项目，渲染红色。 要兼容-1的情况
      return (!value && required) && (this.noEnoughTraveller || item.validateFailed || item.invalidate)
    },
    showCloseSvg(key) {
      return key !== INIT_TRAVELLER_ID && this.originData.count !== 1
    },
    createEmptyItem() {
      return {
        key: INIT_TRAVELLER_ID,
        value: this.getBaseInfo(this.originData.info_items),
        invalidate: checkGlobalFlag()
      }
    },
    setDataList(val) {
      if (this.noNeedUpdateData) return
      const item = { key: this.travellerId, value: this.flattenedData(val) }

      if (this.travellerId === INIT_TRAVELLER_ID) {
        item.value = this.getBaseInfo(val)
      }
      if (this.originData.count === 1) {
        this.dataList = [item]
      } else {
        let index = this.dataList.findIndex(item => item.key === this.travellerId)

        if (index >= 0) {
          this.$set(this.dataList, index, item)
        } else {

          if (this.hasKey(INIT_TRAVELLER_ID)) {
            this.dataList.splice(1, 0, item)
          } else {
            this.dataList.unshift(item)
          }

          if (this.omit(INIT_TRAVELLER_ID).length === this.originData.count) {
            this.dataList = this.omit(INIT_TRAVELLER_ID)
          }
        }
      }
    },
    cacheDataList() {
      return this.dataList
    },
    replaceDataList(data) {
      this.$set(this, 'dataList', data)
    },
    hasKey(key) {
      return this.dataList.some(item => item.key === key)
    },
    closeEvent(key) {
      this.$emit('changeItemSelectStatus', Number(key), false)
    },
    omit(id, list = this.dataList) {
      const _list = cloneDeep(list)
      return (_list || []).filter(item => item.key !== id)
    },
    getBaseInfo(data) {
      return this.flattenedData(clearAllFilledData(data || []))
    },
    flattenedData(data = this.data) {
      const res = []
      traverseDynamicFormItem(data || [], formItem => res.push(formItem))
      return res
    },
    clearValidate() {
      this.errorType = 0
    },
    clearGlobalErrorStatus() {
      this.clearInvalidateStatus(true)
    },
    validateDataList() {
      // 校验全部数据
      let isValid = true
      let isGlobalCheckFlag = checkGlobalFlag()
      this.dataList.forEach(item => {
        for (const formItem of item.value) {
          const { isEmpty, isInvalid } = validateFormItem(formItem)
          const { required } = formItem.style || {}
          let errorType = 0

          if (isInvalid) {
            errorType = 3
          }

          if (required && isEmpty) {
            errorType = 2
          }

          if (errorType !== 0) {
            isGlobalCheckFlag && this.$set(item, 'invalidate', true)
            isValid = false
            this.errorType = errorType
            break
          } else {
            this.$set(item, 'invalidate', false)
          }
        }
      })

      if (this.dataList.length !== this.originData.count) {
        isValid = false
      }

      return isValid ? Promise.resolve(true) : Promise.reject()
    },
    validate() {
      this.clearValidate()
      traverseDynamicFormItem(this.data || [], formItem => {
        const { isEmpty, isInvalid } = validateFormItem(formItem)
        const { required } = formItem.style || {}

        if (isInvalid) {
          this.errorType = 3
        }

        if (required && isEmpty) {
          this.errorType = 2
        }
      })
      if (this.errorType === 0) {
        this.clearInvalidateStatus()
      }

      this.setItemStatus(this.errorType === 0)

      return this.errorType === 0 ? Promise.resolve(true) : Promise.reject()
    },
    setItemStatus(status) {
      const target = this.dataList.find(item => item.key === this.travellerId)
      const isGlobalCheckFlag = checkGlobalFlag()

      this.$set(target, 'validateFailed', !status)
      if (isGlobalCheckFlag && !status) {
        this.$set(target, 'invalidate', true)
      }
    },
    clearInvalidateStatus(clearAll = false) {
      if (clearAll) {
        this.dataList.forEach(item => {
          this.$set(item, 'invalidate', false)
        })
      } else {
        const item = this.dataList.find(item => item.key === this.travellerId )
        item && (this.$set(item, 'invalidate', false))
      }
    },
    getText({ value, required, type }) {
      if (value === '') {
        return required ? this.$t(SELECT_ITEMS.includes(type) ? '12576' : '72640') : this.$t('72639')
      }

      return value
    },
    computeWidth() {
      if (!isServer && this.minWidth === 0) {
        const labelDoms = this.$el.querySelectorAll('.traveller-panel-row-label') || []
        const widthList = [].map.call(labelDoms, labelDom => labelDom.offsetWidth || 0)
        this.minWidth = Math.max(...widthList)

        // fix: 修复offsetWidth有小数点的情况
        if (this.minWidth > 0) {
          this.minWidth += 1
        }
      }
    },
    computeMaxWidth() {
      if (!isServer && this.maxWidth === 0) {
        const wrapDom = this.$el.querySelector('.traveller-panel-wrap') || {}
        const width = wrapDom.offsetWidth
        this.maxWidth = width > 45 ? (width - 45) / 2 : 0
      }
    },
    createObserver() {
      // 解决购物车 display:none 获取不到宽度的问题
      if ('IntersectionObserver' in window) {
        const elementObserver = new IntersectionObserver(entries => {
          entries.forEach(entry => {
            // 如果元素可见
            if (entry.intersectionRatio > 0) {
              this.computeMaxWidth()
              this.computeWidth()
            }
          })
        })

        elementObserver.observe(this.$el)
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.traveller-panel {
  &-wrap {
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
    cursor: pointer;
  }

  &-content {
    width: 100%;
    .item-box {
      display: flex;
      justify-content: space-between;
      margin-bottom: 16px;
      padding: 16px;
      border: 1px solid #eeeeee;
      border-radius: $radius-l;
      position: relative;

      .default-name {
        height: 20px;
        font-size: 14px;
        font-weight: 500;
        line-height: 20px;
        color: #212121;
        word-break: break-word;
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
      }

      &:last-child {
        margin-bottom: 0;
      }

      &.is-error {
        border: 1px solid $color-red-500;
      }
    }
  }

  &-row {
    margin-top: 8px;
    display: flex;
    align-items: flex-start;

    &:first-child {
      margin-top: 0;
    }

    &-label {
      @include font-body-s-regular;

      margin-right: 24px;
      display: inline-block;
      color: $color-text-primary;
    }

    &-value {
      @include font-body-s-semibold;

      flex: 1;
      color: $color-text-primary;
      word-break: break-word;
      display: flex;
      flex-wrap: wrap;

      &.is-grey {
        color: $color-text-placeholder;
      }

      &.is-red {
        color: $color-red-500 !important;
      }

      &-verify-flag {
        color: $color-success;
        font-size: $fontSize-body-s;
        font-weight: $fontWeight-semibold;
        line-height: 20px;
      }
    }
  }

  &-right {
    margin-left: 24px;
    display: flex;
    align-items: flex-end;
    flex: none;
  }

  &-edit {
    display: flex;
    align-items: flex-end;
    flex-direction: column;
    height: 100%;
    justify-content: flex-end;

    &.flex-between {
      justify-content: space-between;

      .close-svg-box {
        width: 36px;
        height: 36px;
        flex: none;
        position: relative;
        left: 10px;
        top: -10px;
        padding-left: 10px;
        padding-top: 10px;

        .close-svg {
          display: block;
        }
      }
    }

    .close-svg {
      width: 16px;
      height: 16px;
      margin-bottom: 8px;
      display: none;
    }

    &-text {
      @include font-body-s-regular;
      color: $color-neutral-900;
      text-decoration: underline;
      font-weight: $fontWeight-semibold;
    }
  }

  &-error {
    margin-top: 8px;
    display: flex;
    align-items: flex-start;
    justify-content: flex-start;

    &-icon-wrap {
      display: flex;
      align-items: center;
      height: 20px;
    }

    &-icon {
      margin-right: 6px;
      width: 16px;
      height: 16px;
    }

    &-text {
      @include font-body-s-regular;
      color: $color-error;
    }
  }
}
</style>
