module.exports = {
  extends: ['stylelint-config-standard'],
  plugins: ['stylelint-scss', '@klook/stylelint-config'],
  rules: {
    // 'klk/font-color-tokens': [
    //   true,
    //   {
    //     whiteList: [

    //     ],
    //     colorMap: {

    //     }
    //   }
    // ],

    'klk/font-color-tokens': [
      true,
      {
        whiteList: ['#e4004d', '#3f70f5'],
      },
    ],
    'klk/background-color-tokens': [
      true,
      {
        whiteList: [
          'linear-gradient(90deg, #e9e9e9 25%, #e4e4e4 37%, #e9e9e9 63%)',
          'linear-gradient(90deg, #f1f1f1 25%, #f3f3f3 37%, #f1f1f1 63%)',
          'linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, #fff 100%)',
          'linear-gradient(90deg, #e0e0e0 25%, #e9e9e9 37%, #e0e0e0 63%)',
          'linear-gradient(to right, rgba(255, 255, 255, 0), #f5f5f5)',
          'linear-gradient(95.08deg, #ff9557 0%, #ff6b3d 100%)',
          'linear-gradient(0deg, #fff, #fff)',
          'linear-gradient(to bottom, rgba(0, 0, 0, 0), rgba(0, 0, 0, 0.2))',
          'linear-gradient(95.08deg, #ff5d5d 0%, #ff6b3d 100%)',
          'linear-gradient(to right, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.04) 100%)',
          'linear-gradient(to bottom, transparent 50%, rgba(0, 0, 0, 0.5) 100%)',
          'linear-gradient(0, rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0.3))',
          'linear-gradient(92.82deg, #ff9557 1.69%, #ff6b3d 96.06%)',
          'linear-gradient(0deg, #fff6f4, #fff6f4), #fff',
          'linear-gradient(180deg, #ff6635 0%, #ffb126 100%)',
          'linear-gradient(0deg, #fff8f6, #fff8f6), #fff8f6',
          'linear-gradient(to top, rgba(255, 255, 255, 0.4), rgba(255, 255, 255, 0.8))',
          'linear-gradient(to bottom, rgba(255, 255, 255, 0.4), rgba(255, 255, 255, 0.8))',
          'linear-gradient(180deg, #fff 91.15%, #f7f7f7 100%)',
          'linear-gradient(90deg, #ff9d26 0%, #ff5722 100%)',
          'linear-gradient(186.51deg, #fff 87.4%, #fff 87.4%, #f5f5f5 94.88%)',
          'linear-gradient(90deg, #ff9d26 1.65%, #ff5722 100%)',
          'linear-gradient(90deg, #ff9d26 -1.72%, #ff5722 101.15%)',
          'linear-gradient(89.1deg, #ff9d26 -1.38%, #ff5722 99.83%)',
          'linear-gradient(90deg, #ff9d26 2.84%, #ff5722 100%)',
          'linear-gradient(90deg, rgba(255, 255, 255, 0.0001) 0%, #fff 100%)',
          'linear-gradient(90deg, #ff5722 0%, #ff9557 100%)',
          'linear-gradient(180deg, #ffeed3 0%, rgba(255, 238, 211, 0) 100%)',
          'rgba(255, 87, 34, 0.08)',
          '#e4004d',
          '#fff3e8',
        ],
      },
    ],
    'klk/border-color-tokens': [
      true,
      {
        whiteList: ['#24b985', '#ffd9a2', '#16aa77', '#ff5a23', '#0b8d60', '#c5c5c5', '#ffd9a8'],
      },
    ],
    'klk/icon-fill-tokens': true,
    'klk/border-radius-tokens': true,
    'klk/box-shadow-tokens': [
      true,
      {
        whiteList: [
          '0 8px 0 0 #eee inset',
          '0 -1px 6px rgba(0, 0, 0, 0.08)',
          'inset 0 1px 0 0 #eee',
          '0 0 4px 0 rgba(0, 0, 0, 0.2)',
          '0 -2px 4px 0 rgba(0, 0, 0, 0.12)',
          '0 4px 12px 0 rgba(0, 0, 0, 0.12)',
          '0 1px 6px rgba(0, 0, 0, 0.1)',
          '0 1px 7px 0 rgba(0, 0, 0, 0.2)',
          '0 2px 6px rgba(0, 0, 0, 0.12)',
          '0 0 0 1px #e0e0e0',
          '0 1px 3px 0 rgba(0, 0, 0, 0.2)',
          'inset 0 -1px 0 0 rgba(0, 0, 0, 0.15)',
          'inset 0 1px 1px -1px rgba(0, 0, 0, 0.12)',
          'inset 0 1px 0 #eee',
          'inset 0 -1px 0 #eee',
          'inset 0 0.5px 0 0 #eee',
          'inset 0 -0.5px 0 0 #eee',
          'inset 0 0.5px 0 #eee',
          '0 1px 1px rgba(0, 0, 0, 0.12)',
          '0 1px 3px 0 rgba(0, 0, 0, 0.08), 0 0 3px 0 rgba(0, 0, 0, 0.08)',
          '0 1px 6px rgba(0, 0, 0, 0.2)',
          '0 2px 6px rgba(0, 0, 0, 0.05)',
          '0 -2px 4px rgba(0, 0, 0, 0.12)',
          '0 4px 16px 0 rgba(0, 0, 0, 0.08)',
          '0 -4px 20px rgba(0, 0, 0, 0.08)',
          '0 4px 20px rgba(0, 0, 0, 0.04)',
          '0 1px 0 #eee',
          '0 4px 24px rgba(0, 0, 0, 0.16)',
          '0 -0.5px 0 #e0e0e0',
          '0 0 3px 0 #ff5722',
          '0 2px 4px 0 rgba(0, 0, 0, 0.12)',
          'inset 0 1px 0 0 rgba(0, 0, 0, 0.5)',
          '0 1px 12px rgba(0, 0, 0, 0.1)',
          '0 4px 20px 0 rgba(0, 0, 0, 0.08)',
          '0 0 12px rgba(0, 0, 0, 0.03)',
          '0 8px 6px 0 rgba(0, 0, 0, 0.02)',
          '5px 0 10px -5px #eee',
          'inset 0 -0.5px 0 rgba(0, 0, 0, 0.12)',
          '0 1px 3px #e0e0e0',
          '0 2px 4px 0 rgba(0, 0, 0, 0.02)',
          '0 1px 3px 0 #e0e0e0',
          '0 1px 3px 0 rgba(0, 0, 0, 0.12), 0 0 3px 0 rgba(0, 0, 0, 0.08)',
          '0 -2px 4px rgba(0, 0, 0, 0.08)',
          'inset 0 -0.5px 0 rgba(0, 0, 0, 0.87)',
          'inset 0 -0.5px 0 #ff5722',
          'inset 0 0.5px 0 rgba(0, 0, 0, 0.12)',
          '0 1px 2px 0 rgba(0, 0, 0, 0.1)',
          '0 2px 3px -2px gray',
          '0 1px 6px 0 rgba(0, 0, 0, 0.2)',
          'inset 0 0.5px 0 #eee, 0 4px 8px -4px rgba(0, 0, 0, 0.11)',
          '0 4px 4px 0 rgba(0, 0, 0, 0.24), 0 0 4px 0 rgba(0, 0, 0, 0.12)',
          '0 1px 3px 0 rgba(0, 0, 0, 0.04), 0 0 3px 0 rgba(0, 0, 0, 0.04)',
          'inset 0 1px 0 0 #f5f5f5',
          'inset 0 -0.5px 0 #eee',
          '0 1px 3px rgba(0, 0, 0, 0.12), 0 0 3px rgba(0, 0, 0, 0.08)',
          '0 6px 12px 0 rgba(0, 0, 0, 0.12), 0 -1px 2px 0 rgba(0, 0, 0, 0.08)',
          '0 -3px 8px rgba(0, 0, 0, 0.11)',
          'inset 0 -0.5px 0 #e0e0e0',
          '0 2px 4px 0 rgba(0, 0, 0, 0.04)',
          '0 6px 12px rgba(0, 0, 0, 0.12)',
          '0 4px 4px #0000003d, 0 0 4px #0000001e',
          '0 4px 8px #0003',
          '-6px 0 6px -3px rgba(0, 0, 0, 0.08)',
          '0 1px 2px rgba(0, 0, 0, 0.3)',
          'inset 0 1px 2px 0 rgba(0, 0, 0, 0.3)',
          '0 2px 8px 0 rgba(0, 0, 0, 0.08), 0 0 2px 0 rgba(0, 0, 0, 0.02)',
          '0 -4px 12px 0 rgba(0, 0, 0, 0.08)',

          // hotel
          '0 3px 6px 0 rgba(0, 0, 0, 0.12), 0 -1px 3px 0 rgba(0, 0, 0, 0.08)',
          '0 -2px 10px 0 rgba(0, 0, 0, 0.24)',
          '0 2px 6px 0 rgba(0, 0, 0, 0.18)',
          '0 1px 0 0 #e0e0e0',
          '0 2px 10px 0 rgba(0, 0, 0, 0.1)',
          '0 2px 8px 0 rgba(0, 0, 0, 0.12)',
          '0 3px 6px 0 rgba(0, 0, 0, 0.08)',
          'inset 0 -1px 0 0 rgba(0, 0, 0, 0.12)',
          'inset 0 0.5px 0 0 rgba(0, 0, 0, 0.12)',
          'inset 0 -0.5px 0 0 rgba(0, 0, 0, 0.12)',
          '0 1px 4px 0 rgba(0, 0, 0, 0.12)',
          '0 5px 6px 0 rgba(0, 0, 0, 0.12)',
          'inset 0.5px 0 0 0 rgba(0, 0, 0, 0.12)',
          '0 0 6px rgba(0, 0, 0, 0.03)',
          '0 1px 4px 0 rgba(0, 0, 0, 0.08)',
          '0 1px 10px 0 rgba(0, 0, 0, 0.08)',
          '0 4px 10px rgba(0, 0, 0, 0.14)',
          '0 2px 4px 0 rgba(#000, 0.12)',
          '0 0 0 1px #eee',
          'inset 0 0 2px 2px #fff3e0',
          '0 1px 1px 0 rgba(0, 0, 0, 0.12)',
          'inset 0 -1px 1px 0.5px rgba(0, 0, 0, 0.06)',
          '0 6px #fff3e0',
          '0 2px 4px rgba(0, 0, 0, 0.12)',
          '0 0 3px rgba(0, 0, 0, 0.08), 0 1px 3px rgba(0, 0, 0, 0.12)',
          'inset 0 -1px 0 rgba(0, 0, 0, 0.12)',
          '0 0 2px rgba(0, 0, 0, 0.14)',
          '0 0 28px rgba(0, 0, 0, 0.0346646)',
          '1px 1px 6px 2px rgba(0, 0, 0, 0.08)',
          '0 -1px 8px rgba(0, 0, 0, 0.11)',
          '0 1px 2px rgba(0, 0, 0, 0.12)',
          '0 2px 0 rgba(255, 61, 0, 0.25)',
        ],
      },
    ],
    'klk/opacity-tokens': true,
    'klk/font-size-tokens': true,
    'klk/font-weight-tokens': true,
    // 'klk/line-height-tokens': true,
    'property-disallowed-list': [
      ['font-size', 'line-height', 'font-weight'],
      {
        except: ['block'],
        message: '包含字体属性(font-size|line-height|font-weight),请使用designToken mixin',
        severity: 'warning',
      },
    ],
    'at-rule-no-unknown': [
      true,
      {
        ignoreAtRules: [
          'value',
          'include',
          'mixin',
          'function',
          'if',
          'return',
          'else',
          'each',
          'extend',
          'for',
          'at-root',
        ],
      },
    ],
    'scss/at-rule-no-unknown': true,
    'selector-pseudo-element-no-unknown': [
      true,
      {
        ignorePseudoElements: ['v-deep', 'last-of-type', 'first-of-type', 'last-child'],
      },
    ],
    'selector-pseudo-class-no-unknown': [
      true,
      {
        ignorePseudoClasses: ['export', 'import', 'global', 'local', 'external'],
      },
    ],
    'selector-type-no-unknown': [
      true,
      {
        ignoreTypes: ['from'],
      },
    ],
    'property-no-unknown': [
      true,
      {
        ignoreProperties: ['composes', 'compose-with'],
        ignoreSelectors: [':export', /^:import/],
      },
    ],
    'no-descending-specificity': null,
    'no-empty-source': null,
    'block-no-empty': null,
    'no-duplicate-selectors': null,
    'function-linear-gradient-no-nonstandard-direction': null,
    'declaration-block-no-shorthand-property-overrides': null,
    'function-calc-no-unspaced-operator': null,
  },
  ignoreFiles: ['.nuxt/**/*.css', 's/dist_web/*.css', 'lib/**/*.css'],
  overrides: [
    {
      files: ['**/*.scss'],
      customSyntax: 'postcss-scss',
    },
  ],
}
