# klook-experience-other-info

## 项目说明

本项目是 ttd 业务线 other-info 的前端展示表单库，用来对 other-info 的后端数据进行展示，并且带有一些人性化的表单检验、检测表单是否修改、获取修改后的表单数据等功能。

本项目自带一个 ssr 的 **playground**，在 playground 文件夹里面，这是[使用方法](./playground/README.md)。

**出行人信息示例数据**：[数据链接](./playground/src/data/travellerInfo.js)

**联系人信息示例数据**：[数据链接](./playground/src/data/contactInfo.js)

## 如何接入

1.由于历史原因，本项目不自带 type.d.ts 文件(**后面会用 ts 重写**，到时候会带上 types 文件)，所以需要在项目中加上相应的 module 声明：

```js
// types/common/shims.d.ts
declare module '@klook/klook-experience-other-info-new/lib/mobile/index.esm.js'
declare module '@klook/klook-experience-other-info-new/lib/web/index.esm.js'
```

2.在项目中的使用方式如下（这里以mobile为例，desktop的话需要改成```@klook/klook-experience-other-info-new/lib/web/index.esm.js```）：
```vue
<template>
  <OtherInfoFormMulti
    :data="sectionInfo.form_infos"
    :section-type="sectionInfo.section_type"
    @add="ref => addOtherInfoRef(sectionIndex, ref)"
    @remove="removeOtherInfoRef(sectionIndex)"
    @change="handleCheckModified"
    @input="handleCheckModified"
  />
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import { OtherInfoFormMulti, codeManager } from '@klook/klook-experience-other-info-new/lib/mobile/index.esm.js'

@Component({
  components: {
    OtherInfoFormMulti
  }
})
export default class Demo extends Vue {
  // 后端返回的other-info数据
  sectionInfos: any[] = []
  // 用来表单检验
  otherInfoRefList: any[] = []

  async mounted() {
    // $getAllCountryCode 是 nuxt 项目中的方法
    const allCountryCodes = await this.$getAllCountryCode()
    // 需要初始化 other-info 库里面的 countrycode 和 language
    codeManager.initCountryCode(allCountryCodes)
    await codeManager.asyncGetLanguages({ $axios: this.$axios })
  }

  // 用来表单检验
  addOtherInfoRef(index: number, ref: any) {
    this.otherInfoRefList[index] = ref
  }

  // 用来表单检验
  removeOtherInfoRef(index: number) {
    this.otherInfoRefList[index] = null
  }

  // 表单检验
  async validateRules() {
    let isAllSectionsValid = true
    for (let i = 0; i < this.sectionInfos?.length; i += 1) {
      const ref = this.otherInfoRefList[i]
      const valid = ref?.validate ? await ref.validate() : true

      if (!valid) {
        isAllSectionsValid = false
      }
    }

    // 校验不通过
    if (!isAllSectionsValid) {
      // xxx
    }
  }

  // 检测表单是否被用户修改
  checkInfoModified() {
    return this.sectionInfos.some((_, sectionIndex) => {
      const ref = this.otherInfoRefList[sectionIndex]
      return ref && ref.checkIsModified ? ref.checkIsModified() : false
    })
  }

  // 仅获取修改了的表单数据
  getModifiedData() {
    const contactIndex = 4 // 这里的 4 仅是举的一个例子
    const contactInfo = this.sectionInfos[contactIndex]
    const ref = this.otherInfoRefList[contactIndex]
    const formInfos = ref && ref.getModifiedData ? ref.getModifiedData() : []
    return formInfos
  }

  // 获取序列化、扁平化的修改了的数据，仅用于展示
  getDiffItems() {
    const diffSectionInfos: any[] = []

    this.sectionInfos.forEach((sectionInfo, sectionIndex) => {
      const ref = this.otherInfoRefList[sectionIndex]
      const diffFormInfos = ref && ref.getDiffItems ? ref.getDiffItems() : []

      if (diffFormInfos?.length > 0) {
        diffSectionInfos.push({
          ...sectionInfo,
          form_infos: diffFormInfos
        })
      }
    })

    return diffFormInfos
  }
}
</script>
```

## 其它功能

1.web端支持**自定义宽度**：

```js
// 使用 wrapperWidth 和 componentWidth
<OtherInfoModuleMulti
  wrapper-width="742px"
  component-width="360px"
/>
```

2.如果要**展示并修改**出行人信息（或者邮寄信息）选择模块的话，需要传入出行人数据（和邮寄信息）数据：

```vue
<template>
  <div v-if="isInitPage">
    <OtherInfoModuleMulti
      :traveller-list="travellerList"
      :shipment-list="shipmentList"
      :update-traveller-list-func="updateTravellerListFunc"
    />
  </div>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import { OtherInfoFormMulti, codeManager } from '@klook/klook-experience-other-info-new/lib/mobile/index.esm.js'

@Component({
  components: {
    OtherInfoFormMulti
  }
})
export default class Demo extends Vue {
  travellerList: ExpSettlement.TravellerInfo[] = []
  shipmentList: ExpSettlement.ShipmentInfo[] = []

  async mounted() {
    await Promise.all([
      this.getTravellerList(),
      this.getShipmentList()
    ]).then((res: any) => {
      this.isInitPage = res.every((v: boolean) => v)
    })
  }

  // 获取出行人信息
  async getTravellerList() {
    const res = await this.$axios.$get(apis.experienceOtherInfoList, {
      headers: {
        'X-Klook-Request-Id': this.settlementXKlookReqId
      }
    })
    if (res.success) {
      this.travellerList = res.result
    }
    return res?.success
  }

  // 获取邮寄信息
  async getShipmentList() {
    const res = await this.$axios.$get(apis.experienceOrderMailList, {
      headers: {
        'X-Klook-Request-Id': this.settlementXKlookReqId
      }
    })
    if (res.success) {
      this.shipmentList = res.result
    }
    return res?.success
  }

  // 更新出行人信息
  async updateTravellerList(params: object) {
    const res = await this.$axios.$post(
      apis.experienceUpdateOtherInfoList,
      params,
      {
        headers: {
          'X-Klook-Request-Id': this.settlementXKlookReqId
        }
      }
    )
    if (!res.success) {
      // xxxx
    }
    return res
  }
}
</script>
```

3.组件有一种**卡片形式**的表单形式，目前 exp 的出行人信息和联系人信息使用的是这种形式。使用方法如下：

```js
<OtherInfoModuleMulti
  use-new-feature
/>
```

4.本项目对外暴露了**各种粒度**的form组件，按需引入即可：

```bash
OtherInfoForm: 单个form，自带出行人选择器和邮寄信息选择器
OtherInfoFormMulti: 多个form
OtherInfoModule: 单个module，带标题的多个module
OtherInfoModuleMulti: 多个module
```


5.通过设置form表单的属性，统一设置form-item的部分属性。如styleType
不传递styleType属性，默认值如右所示： desktop: outlined  mobile: lined
klk-form组件的其他属性也支持，但是可能要做样式兼容。
使用方法如下：

```js

方式一
<OtherInfoModuleMulti
  :form-props="{
    styleType: 'lined | outlined | filled'
  }"
/>

方式二
<OtherInfoModuleMulti
  v-bind="attrs"
/>

<script>
  get attrs() {
    return {
      formProps: {
        styleType: 'lined | outlined | filled'
      }
    }
  }
</script>

```

6.请在项目多语言中添加如下两个多语言，在日期选择插件中会使用,否则日期显示值会有问题
global.standard.date.format_hide_year
global.standard.date.format


7.点击全局校验按钮的时候，如付款按钮。请做如下设置，否则卡片类型的模块不会有红色边框。
  设置该属性后，只有必填项填写完整，红色边框才会消失。

```js
<script>
  sessionStorage('is-global-check', true)
  for (const otherInfoRef of this.otherInfoRefList) {
    if (!(await otherInfoRef?.validate())) {
     // your code
    }
</script>
、、、

## 开发

1.首先把本项目链接到全局去。在本项目的根目录下面运行：

```bash
npm link
```

2.以开发方式运行此项目，此时会自动开启 rollup 打包并 watch src 文件夹的改动。在本项目的根目录下面运行：

```bash
npm run dev
```

3.在要开发的项目里面链接这个包。在 new web 或者 nuxt web 的根目录下运行：

```bash
npm link @klook/klook-experience-other-info
```

4.在项目里面可以引入这个包了。

## 其它

1.查看这个包是否被链接到全局去了：

```bash
npm ls -g --depth=0
```

2.在开发的项目里面删除这个包的链接。在 new web 或者 nuxt web 的根目录下运行：

```bash
npm unlink @klook/klook-experience-other-info
```

3.删除这个包在全局的链接。在本项目的根目录下面运行：

```bash
npm unlink
```
