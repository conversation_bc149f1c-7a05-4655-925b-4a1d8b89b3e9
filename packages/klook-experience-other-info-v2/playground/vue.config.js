const nodeExternals = require('webpack-node-externals');
const VueSSRServerPlugin = require('vue-server-renderer/server-plugin');

let configureWebpack = {
  entry: './src/main.client.js',
  devtool: 'source-map'
}

if (process.env.SSR) {
  configureWebpack = {
    entry: './src/main.server.js',
    target: 'node',
    devtool: 'source-map',
    output: {
      libraryTarget: 'commonjs2',
    },
    optimization: {
      splitChunks: false,
    },
    externals: nodeExternals({
      whitelist: [/\.css$/, /^@klook/],
    }),
    plugins: [new VueSSRServerPlugin()],
  }
}

module.exports = {
  productionSourceMap: true,
  configureWebpack,
}
