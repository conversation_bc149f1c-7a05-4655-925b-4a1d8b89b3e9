<template>
  <div class="contact-info">
    <div
      v-for="(section, index) in contactInfoData"
      :key="index"
    >
      <h3>{{ section.title }}</h3>
      <OtherInfoFormMulti
        :data="section.form_infos"
        :wrapperWidth="wrapperWidth"
        :componentWidth="componentWidth"
      />
    </div>
  </div>
</template>

<script>
import contactInfoData from '../data/contactInfo'

if (process.env.VUE_APP_SSR_MODULE === 'true') {
  require('../modules/web/index.ssr.esm.css')
}

const { OtherInfoFormMulti } = process.env.VUE_APP_SSR_MODULE === 'true' ? require('../modules/web/index.ssr.esm.js') : require('../modules/web/index.esm.js')

export default {
  name: 'ContactInfo',
  inject: ['wrapperWidth', 'componentWidth'],
  components: {
    OtherInfoFormMulti
  },
  data() {
    return {
      contactInfoData
    }
  }
}
</script>

<style>

</style>
