<template>
  <div class="traveller-info">
    <div
      v-for="(section, index) in travellerInfoData"
      :key="index"
    >
      <h3>{{ section.title }}</h3>
      <OtherInfoFormMulti
        :data="section.form_infos"
      />
    </div>
  </div>
</template>

<script>
import travellerInfoData from '../data/travellerInfo'

if (process.env.VUE_APP_SSR_MODULE === 'true') {
  require('../modules/mobile/index.ssr.esm.css')
}

const { OtherInfoFormMulti } = process.env.VUE_APP_SSR_MODULE === 'true' ? require('../modules/mobile/index.ssr.esm.js') : require('../modules/mobile/index.esm.js')

export default {
  name: 'TravellerInfoMobile',
  components: {
    OtherInfoFormMulti
  },
  data() {
    return {
      travellerInfoData
    }
  }
}
</script>

<style>

</style>
