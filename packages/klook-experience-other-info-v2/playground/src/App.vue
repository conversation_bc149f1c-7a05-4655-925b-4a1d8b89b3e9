<template>
  <div id="app">
    <div class="container">
      <div class="content-web">
        <h2>web展示</h2>
        <TravellerInfo />
        <ContactInfo />
      </div>
      <div class="content-mobile">
        <h2>mobile展示</h2>
        <TravellerInfoMobile />
        <ContactInfoMobile />
      </div>
    </div>
  </div>
</template>

<script>
import TravellerInfo from './views/TravellerInfo.vue'
import TravellerInfoMobile from './views/TravellerInfoMobile.vue'
import ContactInfo from './views/ContactInfo.vue'
import ContactInfoMobile from './views/ContactInfoMobile.vue'

export default {
  provide: {
    wrapperWidth: '760px',
    componentWidth: '365px'
  },
  components: {
    TravellerInfo,
    TravellerInfoMobile,
    ContactInfo,
    ContactInfoMobile
  },
  data() {
    return {
      test: 1
    }
  },
}
</script>

<style lang="scss" scoped>
#app {
  overflow-x: auto;
  background-color: #ccc;
}

.container {
  margin: 0 auto;
  padding: 20px;
  width: 1270px;
  display: flex;
  justify-content: space-between;
}

.content-web {
  padding: 10px 20px;
  width: 800px;
  background-color: #fff;
  box-sizing: border-box;
  border: 1px solid #ccc;
}

.content-mobile {
  padding: 10px 20px;
  width: 375px;
  height: 667px;
  overflow-y: auto;
  background-color: #fff;
  box-sizing: border-box;
  border: 1px solid #ccc;
}

h2 {
  padding: 10px;
  text-align: center;
  border: 1px solid #ccc;
}
</style>
