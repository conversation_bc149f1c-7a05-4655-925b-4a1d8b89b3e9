{"name": "vue-issue-example", "version": "0.1.0", "private": true, "scripts": {"prepare": "ln -s ../../lib src/modules", "serve": "vue-cli-service serve", "build": "export VUE_APP_SSR_MODULE=true && vue-cli-service build", "build:ssr": "export SSR=true VUE_APP_SSR_MODULE=true && vue-cli-service build --no-clean && sed 's/<div id=app><\\/div>/<\\!--vue-ssr-outlet-->/g' dist/index.html > dist/template.html", "preview": "node src/ssr.js", "ssr": "npm run build && npm run build:ssr && npm run preview"}, "dependencies": {"@klook/klook-ui": "1.23.5", "core-js": "^3.4.3", "dayjs": "^1.10.7", "lodash.clonedeep": "^4.5.0", "vue": "2.6.11"}, "devDependencies": {"@vue/cli-plugin-babel": "^4.1.0", "@vue/cli-service": "^4.1.0", "vue-server-renderer": "2.6.11", "vue-template-compiler": "2.6.11", "webpack-node-externals": "^1.7.2"}}