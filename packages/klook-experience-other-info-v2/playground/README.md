# playground

这是本地调试 other-info 的地方。本来打算用来调试 other-info ssr 的渲染，但是遇到一些问题，导致目前不能支持 ssr。（原因后面说）（已支持 ssr）

## 如何运行

1.首先需要把 other-info 软链接过来：

```bash
npm run prepare
```

2.如果是 csr 模式，则直接运行下面的命令即可：

```bash
npm run serve
```

3.如果是 ssr 模式，则按顺序运行如下命令：

```bash
npm run build
npm run build:ssr
npm run preview

## 或者直接运行下面的命令
npm run ssr
```

## 数据在哪儿

数据在[这个文件夹](./src/data)里面，本地调试的时候可以随便改

## 为什么这个库暂时不能支持 ssr

（已支持 ssr）因为这个库在 new web 和 nuxt web 上面都在使用，但是 new web 上面是没有全局安装 klook-ui 库的，这就导致在目前这个库里面按需引入了 klook-ui 组件，同时也按需引入了相应的 scss。就是因为引入了这个**该死的 scss **，导致服务端渲染的时候报错了!!!

## 如果要这个库支持 ssr，应该怎么做？

简单来说，就是 scss 外置，具体做法如下：

1. 在new web引入这个库的所有地方，都引入这个库里面引入的scss。
2. 在这个库里面删掉所有的 klook-ui 相关的 scss。

注意：只是 scss 外置了，组件并不能外置，因为命名会和new web里面的老 ui 库冲突，所以只能在这里库内部导入组件，然后重命名，麻蛋！

（已支持 ssr，方法是写了一个 rollup 插件把 scss 的引入都筛选掉了，所以在使用 ssr 包的时候，需要在包外面自行引入 klook-ui 的 scss）

## 注意：目前的 ssr 版本是beta版，还没有测试过！！！

这个库和其它库有一点不同，就是在库里面直接引入了 klook-ui 的组件，导致在构建的时候需要在 externals 里面把 klook-ui 加入白名单里面去。在 nuxt 项目里面貌似不行？我不太确定啊啊啊啊啊。

