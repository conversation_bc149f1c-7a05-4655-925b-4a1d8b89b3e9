import Vue from 'vue';
import StorybookWrapper from './Wrapper.vue';
import KlookUI from '@/packages/klook-ui/src/index';
import '@klook/klook-icons/styles/index.css'
// import '@/packages/klook-ui/src/styles/index.scss';
import StorybookTheme from './theme';
import { onThemeChange } from './theme/index';
import { onLocaleChange, getLocale, clearLocale, defaultLang, LANGS } from './locale/index';

const RTL_KEY = 'klook-storybook.rtl';
const initialRtl = localStorage.getItem(RTL_KEY) || 'ltr';

Vue.use(KlookUI, { locale: getLocale(), rtl: initialRtl === 'rtl', });

const localeMatch = location.href.match(/globals=.*locale:(\w+);?/)
const initLocale = localeMatch ? localeMatch[1] : 'zh-CN';

if (!initLocale || initLocale === defaultLang) {
  clearLocale();
}

// PERF: 解决 admin-ui 体积过大，影响 storybook 首页加载时间问题
// ref: https://storybook.js.org/docs/react/writing-stories/loaders
export const loaders = [
  async () => {
    if (location.href.includes('klook-admin-ui')){
      return Promise.all([
        import("@klook/admin-ui/lib/index.css"),
        import("@klook/admin-ui").then(({ default: AdminUI }) => {
          Vue.use(AdminUI);
        })
      ]);
    }
    return Promise.resolve();
  }
];

export const parameters = {
  actions: { argTypesRegex: "^on[A-Z].*" },
  controls: {
    matchers: {
      color: /(background|color)$/i,
      date: /Date$/,
    },
  },
  options: {
    storySort: {
      order: [
        'Welcome 欢迎', 'About', 'Design Principles',
        'Foundations', ['Design Tokens', 'Colors 颜色', 'Typography 字体', 'Motion 动效', 'RWD 响应式设计', 'Iconography 图标', 'Illustration 插画'],
        'Klook-UI 基础组件库',['使用指南', ['Web', 'Flutter', '更新日志'], '组件总览', 'Alert', 'Announcement', 'AppBar', 'Badge', 'BottomSheet', 'Breadcrumb', 'Button', 'CardSwiper', 'Carousel'],
        'Klook-Patterns 业务组件库',
        'Patterns', ['Cards 卡片', 'Coupon 优惠券', 'Search 搜索', 'Email 邮件'],
        'DesignSystemOps', ['迭代', '协作', '对齐'],
      ],
    },
  },
  docs: {
    theme: StorybookTheme,
  },
}

function onRTLChange (rtl) {
  if (!rtl) return;
  // console.log('>>> 1', rtl);
  document.body.style.direction = rtl;
  document.body.classList.add(rtl);
  const preRtl = localStorage.getItem(RTL_KEY);
  // console.log('>>> 2', preRtl);
  if (rtl === preRtl) return;
  // console.log('>>> 3');
  localStorage.setItem(RTL_KEY, rtl);
  preRtl && location.reload();
}

export const decorators = [
  /* (story) => ({
    components: { story, wrapper },
    template: '<wrapper><story /></wrapper>'
  }), */
  (Story, context) => {
    const { theme, locale, rtl } = context.globals;
    // console.log('>>> decorators cb', { theme, locale, rtl });
    onRTLChange(rtl);
    onThemeChange(theme);
    if (locale && locale !== initLocale) {
      onLocaleChange(locale);
    }
    return {
      components: { Story, StorybookWrapper },
      template: '<StorybookWrapper><story /></StorybookWrapper>'
    };
  },
];

// 可用的 toolbar 图标: https://storybook.js.org/docs/vue/faq#what-icons-are-available-for-my-toolbar-or-my-addon
export const globalTypes = {
  theme: {
    name: 'Theme',
    defaultValue: 'default',
    description: '切换主题',
    toolbar: {
      icon: 'paintbrush',
      items: [
        { value: 'default', right: '#FF5722', title: 'default' },
        { value: 'verify', right: '#FF50AE', title: 'verify' },
        { value: 'blue', right: '#4985E6', title: 'blue' },
        { value: 'green', right: '#16AA77', title: 'green' },
        { value: 'purple', right: '#9254DE', title: 'purple' },
      ],
    },
  },
  locale: {
    name: 'Locale',
    defaultValue: initLocale,
    description: '切换语言',
    toolbar: {
      icon: 'globe',
      items: LANGS.map(lang => ({ value: lang, right: lang, title: lang })),
    },
  },
  rtl: {
    name: 'RTL',
    defaultValue: initialRtl,
    description: 'RTL',
    toolbar: {
      icon: 'arrowleftalt',
      items: [
        { value: 'ltr', right: 'left to right', title: 'ltr' },
        { value: 'rtl', right: 'right to left', title: 'rtl' },
      ],
    },
  }
};
