// const fs = require('fs');
const path = require('path');
const webpackFinal = require('./webpackFinal.js');
const localStories = (()=> {
  try {
    const localConf = require('./stories.local')
    if (Array.isArray(localConf)) {
      return localConf
    }
  } catch (e) {
    // nothing ...
  }
  return []
})()

const stories = localStories.length ? localStories :  [
  '../stories/*.stories.@(js|mdx)',
  '../stories/klook-ui/*.stories.@(js|mdx)',
  '../stories/klook-patterns/*.stories.@(js|mdx)',

  '../stories/klook-hotel/*.stories.@(js|mdx)',
  '../stories/klook-admin-ui/*.stories.@(js|mdx)',
  '../stories/klook-ui-app/**/*.stories.@(js|mdx)',

  '../stories/design-system-ops/*.stories.@(js|mdx)',
  '../stories/foundations/*.stories.@(js|mdx)',
  '../stories/patterns/*.stories.@(js|mdx)',
]
console.log('stories:', stories)

module.exports = {
  framework: '@storybook/vue',
  stories,
  staticDirs: [path.resolve(__dirname, './static')],
  addons: [
    "@storybook/addon-links",
    "@storybook/addon-essentials",
    // includes: actions, backgrounds, controls, docs, viewport, toolbars
    /* {
      name: "@storybook/addon-essentials",
      options: {
        "docs": false,
        "backgrounds": false,
      }
    }, */
    // "storybook-dark-mode",
    // "storybook-addon-designs"
  ],
  core: {
    builder: 'webpack5',
    options: {
      lazyCompilation: true,
      fsCache: true
    },
  }, 
  features: {
    storyStoreV7: true,
    // buildStoriesJson: true
  }, 
  /* babel: async (options) => {
    fs.writeFileSync(path.join(__dirname, './babel-config.json'), JSON.stringify(options, null, 2));
    return {
      ...options,
      // any extra options you want to set
    };
  }, */
  webpackFinal,
};
