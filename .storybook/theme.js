import { create } from '@storybook/theming';

const KlookTheme = create({
  base: 'light',
  brandTitle: 'Design',
  brandImage: '/logo.png',
  colorPrimary: '#FF5B00',
  colorSecondary: '#FF5B00',
  appBg: 'white',
  appContentBg: 'white',
  appBorderColor: '#eee',
  appBorderRadius: 4,
  fontBase: 'Poppins,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Arial,sans-serif',
  fontCode: 'monospace',
  textColor: '#333',
  textInverseColor: 'rgba(255,255,255,0.9)',
  barTextColor: '#666',
  barSelectedColor: '#FF5B00',
  barBg: 'white',
});

export default KlookTheme;
