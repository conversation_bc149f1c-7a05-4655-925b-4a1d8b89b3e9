import defaultLocale from '@/packages/klook-ui/src/locale/lang/zh-CN.json';

let locale = defaultLocale;
const defaultLang = 'zh-CN';

if (window.sessionStorage.getItem('klk-stb.locale')) {
  locale = JSON.parse(window.sessionStorage.getItem('klk-stb.locale'));
}

function loadLocale (lang) {
  import(`@/packages/klook-ui/src/locale/lang/${lang}.json`).then(({ default: locale }) => {
    window.sessionStorage.setItem('klk-stb.locale', JSON.stringify(locale));
    window.parent.location.reload();
  });
}

function onLocaleChange (lang) {
  if (!lang) return;
  // console.log('>>> onLocaleChange', lang);
  loadLocale(lang);
}

const LANGS = [
  "de",
  "en-AU",
  "en-CA",
  "en-GB",
  "en-HK",
  "en-IN",
  "en-MY",
  "en-NZ",
  "en-PH",
  "en-SG",
  "en-US",
  "en",
  "es",
  "fr",
  "id",
  "it",
  "ja",
  "ko",
  "ru",
  "th",
  "vi",
  "zh-CN",
  "zh-HK",
  "zh-TW"
];

const getLocale = () => locale;

function clearLocale () {
  window.sessionStorage.removeItem('klk-stb.locale');
}

export {
  getLocale,
  clearLocale,
  defaultLang,
  LANGS,
  onLocaleChange
}
