<meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1,minimum-scale=1,viewport-fit=cover">

<style>
  .sb-show-main {
    padding: 0 !important;
  }

  /* @font-face {
    font-family: Poppins;
    src: url(https://fonts.gstatic.com/s/poppins/v20/pxiByp8kv8JHgFVrLFj_Z11lFd2JQEl8qw.woff2) format('woff2');
    font-weight: 200;
  } */

  @font-face {
    font-family: Poppins;
    src: url(https://cdn.klook.com/s/dist_web/ssr_web/fonts/Poppins-Regular.woff2) format("woff2"),url(https://cdn.klook.com/s/dist_web/ssr_web/fonts/Poppins-Regular.woff) format("woff"),url(https://cdn.klook.com/s/dist_web/ssr_web/fonts/Poppins-Regular.ttf) format("truetype");
    font-weight: 400;
  }

  @font-face {
    font-family: Poppins;
    src: url(https://cdn.klook.com/s/dist_web/ssr_web/fonts/Poppins-SemiBold.woff2) format("woff2"),url(https://cdn.klook.com/s/dist_web/ssr_web/fonts/Poppins-SemiBold.woff) format("woff"),url(https://cdn.klook.com/s/dist_web/ssr_web/fonts/Poppins-SemiBold.ttf) format("truetype");
    font-weight: 600;
  }

  @font-face {
    font-family: Poppins;
    src: url(https://cdn.klook.com/s/dist_web/ssr_web/fonts/Poppins-Medium.woff2) format("woff2"),url(https://cdn.klook.com/s/dist_web/ssr_web/fonts/Poppins-Medium.woff) format("woff"),url(https://cdn.klook.com/s/dist_web/ssr_web/fonts/Poppins-Medium.ttf) format("truetype");
    font-weight: 500;
  }

  .sbdocs-h2 {
    margin: 50px 0 8px !important;
  }
  .sbdocs-h3 {
    margin: 40px 0 8px !important;
  }
  .sbdocs-h4 {
    margin: 25px 0 8px !important;
  }
  .sbdocs-h5 {
    margin: 15px 0 8px !important;
  }
  .sbdocs-table tr:nth-of-type(2n) {
    background-color: rgba(255, 255, 255, 0) !important;
  }
</style>
