const fs = require('fs');
const path = require('path');
// const { BundleAnalyzerPlugin } = require('webpack-bundle-analyzer');
const vueMarkdownLoaderConf = require('./vue-markdown/vue-markdown-loader.conf');
const resolve = (...args) => path.resolve(__dirname, ...args);
// const SpeedMeasurePlugin = require("speed-measure-webpack-plugin");
// const smp = new SpeedMeasurePlugin();

// helper functions
function addExclude (rule, excludePath) {
  rule.exclude = Array.isArray(rule.exclude) ? rule.exclude : rule.exclude ? [rule.exclude] : [];
  excludePath = Array.isArray(excludePath) ? excludePath : [excludePath];
  rule.exclude = [...rule.exclude, ...excludePath];
}

function findAndAddRuleUse (rules, ext, useOption) {
  const rule = rules.find(rule => {
    return typeof ext === 'string'
      ? rule.test.test(ext)
      : rule.test.source === ext.source;
  })

  if(rule) {
    rule.use = Array.isArray(rule.use) ? rule.use : rule.use ? [rule.use] : [];
    rule.use.unshift(useOption)
  }
}

function findAndReplaceRule (rules, ext, rule) {
  const index = rules.findIndex(rule => {
    return typeof ext === 'string'
      ? rule.test.test(ext)
      : rule.test.source === ext.source;
  });
  if (index >= 0) {
    rules.splice(index, 1, rule);
  } else rules.push(rule);
}

module.exports = async function (config) {
  const rules = config.module.rules;
  const storiesDir = resolve('../stories');
  const packagesDir = resolve('../packages');
  const hotelSelectorPath = resolve('../packages/klook-hotel-selector')

  // markdown
  // 移除原来的 markdown 配置，替换成新的
  findAndReplaceRule(rules, /\.md$/, {
    test: /\.md$/,
    include: storiesDir,
    use: [
      {
        loader: 'vue-loader'
      },
      {
        loader: 'vue-markdown-loader/lib/markdown-compiler',
        options: vueMarkdownLoaderConf
      }
    ]
  });

  config.module.rules.push({
    test: /\.jsx?$/,
    exclude: hotelSelectorPath,
    // include: packagesDir,
    use: [
      {
        loader: 'babel-loader',
        options: {
          sourceType: 'unambiguous',
          babelrc: false,
          presets: ['@babel/preset-react']
        }
      },
    ]
  });

  findAndAddRuleUse(rules, /\.tsx$/, {
    loader: 'babel-loader',
    options: {
      babelrc: false,
      presets: [
        "@babel/preset-env",
        "@babel/preset-typescript",
        "@vue/babel-preset-jsx"
      ],
      plugins: [
        "@babel/plugin-syntax-jsx",
        "@babel/plugin-transform-async-to-generator",
        ["@babel/plugin-proposal-decorators", {
          version: "legacy"
          // decoratorsBeforeExport: true,
          // "legacy": true
        }],
        ["@babel/plugin-proposal-class-properties", {
          // "loose": true
        }]
      ]
    }
  })

  // sass
  rules.push({
    test: /.s(a|c)ss$/,
    exclude: resolve('./theme'),
    use: [
      'style-loader',
      'css-loader',
      {
        loader: 'sass-loader',
        options: {
          // 可以在 storybook 直接使用 design-token 而无需手动引入
          additionalData: `@import "@/packages/klook-ui/src/styles/token/index.scss";`,
        },
      },
    ],
  });
  rules.push({
    test: /.s(a|c)ss$/,
    include: resolve('./theme'),
    use: ['to-string-loader', 'css-loader', 'sass-loader'],
  });

  // svg
  const includePaths = [
    resolve('../packages/klook-traveller-header'),
    resolve('../packages/klook-traveller-footer'),
    resolve('../packages/klook-traveller-search-recommendations'),
    resolve('../packages/klook-app-download')
  ];
  const svgRule = config.module.rules.find(rule => rule.test.test('.svg'));

  svgRule && addExclude(svgRule, includePaths);
  config.module.rules.push({
    test: /\.svg$/,
    include: includePaths,
    use: ['babel-loader', 'svg-vue-loader'],
  });

  // alias
  config.resolve.alias = config.resolve.alias || {};
  Object.assign(config.resolve.alias, {
    '@': resolve('../')
  });

  // bundler analyse
  /* config.plugins.push(new BundleAnalyzerPlugin({
    analyzerMode: 'static'
  })); */

  // rules.forEach(rule => {
  //   rule.testSource = rule.test && rule.test.source;
  // });

  // fs.writeFileSync(path.join(__dirname, './webpack-config.json'), JSON.stringify(config, null, 2));

  // throw new Error('test');
  // return smp.wrap(config);
  return config
};
