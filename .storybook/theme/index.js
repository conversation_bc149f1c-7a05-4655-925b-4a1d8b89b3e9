let prevThemeStyle;
const themes = {};

function loadTheme (theme) {
  import(`./${theme}.scss`).then(({ default: css }) => {
    themes[theme] = css;
    switchTheme(theme);
  });
}

function removePrevTheme () {
  if (prevThemeStyle) {
    prevThemeStyle.parentNode && prevThemeStyle.parentNode.removeChild(prevThemeStyle);
  }
}

function switchTheme (theme) {
  const css = themes[theme];
  if (!css) return;
  const style = document.createElement('style');
  style.id = `klook-ui-theme-${theme}`;
  style.innerHTML = css;
  document.head.appendChild(style);
  // remove previous theme style
  removePrevTheme();
  prevThemeStyle = style;
}

export function onThemeChange (name) {
  if (!name) return;
  if (name === 'default') {
    removePrevTheme();
    return;
  }
  if (themes[name]) {
    switchTheme(name);
  } else {
    loadTheme(name);
  }
}
