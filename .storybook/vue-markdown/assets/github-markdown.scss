@font-face {
  font-family: octicons-anchor;
  src: url(data:font/woff;charset=utf-8;base64,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) format('woff');
}

.markdown-body {
  -webkit-text-size-adjust: 100%;
  text-size-adjust: 100%;
  color: rgba(0, 0, 0, 0.85);
  font-size: 14px;
  line-height: 1.6;
  word-wrap: break-word;
}

.markdown-body > html > body > h1:hover a.header-anchor,
.markdown-body > html > body > h2:hover a.header-anchor {
  visibility: visible;
}

.markdown-body a.header-anchor {
  display: none;
  text-decoration: none;
  margin-left: -18px;
  // visibility: hidden;
  color: #2196f3;
  opacity: .5;
}
.markdown-body a.header-anchor:hover {
  text-decoration: none;
}

.markdown-body a {
  background-color: transparent;
}

.markdown-body a:active,
.markdown-body a:hover {
  outline: 0;
}

.markdown-body strong {
  font-weight: 500;
}

.markdown-body img {
  border: 0;
}

/* .markdown-body hr {
  box-sizing: content-box;
  height: 0;
} */

.markdown-body pre {
  overflow: auto;
}

.markdown-body code,
.markdown-body kbd,
.markdown-body pre {
  font-family: monospace, monospace;
  font-size: 1em;
}
/*
.markdown-body input {
  color: inherit;
  font: inherit;
  margin: 0;
}

.markdown-body html input[disabled] {
  cursor: default;
}

.markdown-body input {
  line-height: normal;
}

.markdown-body input[type="checkbox"] {
  box-sizing: border-box;
  padding: 0;
} */

.markdown-body > html > body > table {
  border-collapse: collapse;
  border-spacing: 0;
}

.markdown-body td,
.markdown-body th {
  padding: 0;
}

.markdown-body * {
  box-sizing: border-box;
}
/*
.markdown-body input {
  font: 13px/1.4 Helvetica, arial, nimbussansl, liberationsans, freesans, clean, sans-serif, "Segoe UI Emoji", "Segoe UI Symbol";
} */

.markdown-body a {
  /* color: #ff4081; */
  text-decoration: none;
}

.markdown-body a:hover,
.markdown-body a:active {
  text-decoration: underline;
}

/* .markdown-body hr {
  height: 0;
  margin: 15px 0;
  overflow: hidden;
  background: transparent;
  border: 0;
  border-bottom: 1px solid #ddd;
} */

/* .markdown-body hr:before {
  display: table;
  content: "";
}

.markdown-body hr:after {
  display: table;
  clear: both;
  content: "";
} */

.markdown-body > html > body > h1,
.markdown-body > html > body > h2,
.markdown-body > html > body > h3,
.markdown-body > html > body > h4,
.markdown-body > html > body > h5,
.markdown-body > html > body > h6 {
  margin-top: 20px;
  margin-bottom: 20px;
  line-height: 1.1;
}
/*
.markdown-body > html > body > h1 {
  font-size: 30px;
}

.markdown-body > html > body > h2 {
  font-size: 21px;
}

.markdown-body > html > body > h3 {
  font-size: 16px;
}

.markdown-body > html > body > h4 {
  font-size: 14px;
}

.markdown-body > html > body > h5 {
  font-size: 12px;
}

.markdown-body > html > body > h6 {
  font-size: 11px;
}
*/
.markdown-body blockquote {
  margin: 0;
}

.markdown-body ul,
.markdown-body ol {
  padding: 0;
  margin-top: 0;
  margin-bottom: 0;
  list-style-type: circle;
  padding-left: 16px;
}

.markdown-body ol ol,
.markdown-body ul ol {
  list-style-type: lower-roman;
}

.markdown-body ul ul ol,
.markdown-body ul ol ol,
.markdown-body ol ul ol,
.markdown-body ol ol ol {
  list-style-type: lower-alpha;
}

.markdown-body dd {
  margin-left: 0;
}

.markdown-body code {
  font-family: Poppins;
  font-size: 14px;
}

.markdown-body pre {
  margin-top: 0;
  margin-bottom: 0;
  font: 12px Consolas, "Liberation Mono", Menlo, Courier, monospace;
}

.markdown-body .select::-ms-expand {
  opacity: 0;
}

.markdown-body .octicon {
  font: normal normal normal 16px/1 octicons-anchor;
  display: inline-block;
  text-decoration: none;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.markdown-body .octicon-link:before {
  content: '\f05c';
}


.markdown-body>*:last-child {
  margin-bottom: 0 !important;
}

.markdown-body a:not([href]) {
  color: inherit;
  text-decoration: none;
}

.markdown-body .anchor {
  display: inline-block;
  padding-right: 2px;
  margin-left: -18px;
}

.markdown-body .anchor:focus {
  outline: none;
}

.markdown-body > html > body > h1,
.markdown-body > html > body > h2,
.markdown-body > html > body > h3,
.markdown-body > html > body > h4,
.markdown-body > html > body > h5,
.markdown-body > html > body > h6 {
  margin-top: 1.5em;
  margin-bottom: 16px;
  font-weight: 500;
  line-height: 1.4;
  /* color: rgba(0, 0, 0, 0.54); */
}

.markdown-body > html > body > h1 .octicon-link,
.markdown-body > html > body > h2 .octicon-link,
.markdown-body > html > body > h3 .octicon-link,
.markdown-body > html > body > h4 .octicon-link,
.markdown-body > html > body > h5 .octicon-link,
.markdown-body > html > body > h6 .octicon-link {
  color: #000;
  vertical-align: middle;
  visibility: hidden;
}

.markdown-body > html > body > h1:hover .anchor,
.markdown-body > html > body > h2:hover .anchor,
.markdown-body > html > body > h3:hover .anchor,
.markdown-body > html > body > h4:hover .anchor,
.markdown-body > html > body > h5:hover .anchor,
.markdown-body > html > body > h6:hover .anchor {
  text-decoration: none;
}

.markdown-body > html > body > h1:hover .anchor .octicon-link,
.markdown-body > html > body > h2:hover .anchor .octicon-link,
.markdown-body > html > body > h3:hover .anchor .octicon-link,
.markdown-body > html > body > h4:hover .anchor .octicon-link,
.markdown-body > html > body > h5:hover .anchor .octicon-link,
.markdown-body > html > body > h6:hover .anchor .octicon-link {
  visibility: visible;
}

.markdown-body > html > body > h1 {
  padding-bottom: 0.3em;
  font-size: 2.5em;
  font-weight: 600;
  line-height: 1.25;
  /* border-bottom: 1px solid #eee; */
  margin: 0.7em  0;
}

.markdown-body > html > body > h1 .anchor {
  line-height: 1;
}

.markdown-body > html > body > h2 {
  padding-bottom: 0.3em;
  font-size: 2em;
  font-weight: 500;
  line-height: 1.334;
  /* border-bottom: 1px solid #eee; */
}

.markdown-body > html > body > h2 .anchor {
  line-height: 1;
}

.markdown-body > html > body > h3 {
  font-size: 1.5em;
  font-weight: 500;
  line-height: 1.4;
}

.markdown-body > html > body > h3 .anchor {
  line-height: 1.2;
}

.markdown-body > html > body > h4 {
  font-size: 1.25em;
  line-height: 1.5;
  font-weight: 500;
}

.markdown-body > html > body > h4 .anchor {
  line-height: 1.2;
}

.markdown-body > html > body > h5 {
  font-size: 1em;
  line-height: 1.715;
  font-weight: 500;
}

.markdown-body > html > body > h5 .anchor {
  line-height: 0.875;
}

.markdown-body > html > body > h6 {
  font-size: 0.75em;
  font-weight: 500;
  color: #777;
}

.markdown-body > html > body > h6 .anchor {
  line-height: 1.1;
}

.markdown-body > html > body > p{
  font-size: 1rem;
}

.markdown-body > html > body > p,
.markdown-body blockquote,
.markdown-body ul,
.markdown-body ol,
.markdown-body dl,
.markdown-body > html > body > table,
.markdown-body pre {
  margin-top: 0;
  margin-bottom: 16px;
}

.markdown-body {
  p code, li > code, table code {
    background: #757575;
    color: white;
    border-radius: 4px;
    line-height: 1;
    padding: 4px 6px;
    vertical-align: baseline;
  }
}

/* .markdown-body hr {
  height: 4px;
  padding: 0;
  margin: 16px 0;
  background-color: #e7e7e7;
  border: 0 none;
} */

.markdown-body ul ul,
.markdown-body ul ol,
.markdown-body ol ol,
.markdown-body ol ul {
  margin-top: 0;
  margin-bottom: 0;
}

.markdown-body li>p {
  margin-top: 16px;
}

.markdown-body dl {
  padding: 0;
}

.markdown-body dl dt {
  padding: 0;
  margin-top: 16px;
  font-size: 1em;
  font-style: italic;
  font-weight: bold;
}

.markdown-body dl dd {
  padding: 0 16px;
  margin-bottom: 16px;
}

.markdown-body blockquote {
  padding: 12px 16px;
  margin: 16px 0;
  border-left: 4px solid rgba(0, 0, 0, 0.38);
  background-color: #f5f5f5;
  border-radius: 4px;
}

.markdown-body blockquote>:first-child {
  margin-top: 0;
}

.markdown-body blockquote>:last-child {
  margin-bottom: 0;
}

.markdown-body > html > body > table {
  width: 100%;
  word-break: normal;
  word-break: keep-all;
  border-collapse: collapse;
  border-spacing: 0;
  font-size: 14px;
  overflow: hidden;
  overflow-x: auto;
  display: block;
  /* table-layout: fixed; */
}
@media (max-width: 768px ) {
  .markdown-body > html > body > table{
    display: block;
    overflow: hidden;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
  .component-doc-guideline{
    grid-template-columns: 1fr !important;
  }
}

.markdown-body > html > body > table tr{
  color: rgba(0, 0, 0, 0.87);
  height: 48px;
  // word-break: break-word;
}

.markdown-body > html > body > table th{
  font-weight: normal;
  padding-left: 24px;
  padding-right: 24px;
  height: 56px;
  text-align: left;
  color: rgba(0, 0, 0, 0.54);
  position: relative;
  border-bottom: 1px solid #eee;
}

.markdown-body > html > body > table td{
  padding-left: 24px;
  padding-right: 24px;
  height: 48px;
  text-align: left;
  text-overflow: ellipsis;
  border-bottom: 1px solid #eee;
}

.markdown-body > html > body > table td:first-child {
  white-space: nowrap;
}

.markdown-body img {
  max-width: 100%;
  box-sizing: content-box;
  background-color: #fff;
}

.markdown-body code {
  display: inline-block;
  margin: 0;
  font-size: 14px;
  background-color: #fff;
  line-height: 26px;
  vertical-align: top;

}

.markdown-body code:before,
.markdown-body code:after {
  letter-spacing: -0.2em;
  content: "\00a0";
}

.markdown-body pre>code {
  padding: 0;
  margin: 0;
  /* font-size: 100%; */
  word-break: normal;
  white-space: pre;
  background: transparent;
  border: 0;
}

.markdown-body .highlight {
  margin-bottom: 16px;
}

.markdown-body .highlight pre,
.markdown-body pre {
  padding: 16px;
  overflow: auto;
  font-size: 85%;
  line-height: 1.45;
  background-color: #fff;
  /* border-radius: 3px; */
}

.markdown-body .highlight pre {
  margin-bottom: 0;
  word-break: normal;
}

.markdown-body pre {
  word-wrap: normal;
}

.markdown-body pre code {
  display: inline;
  max-width: initial;
  padding: 0;
  margin: 0;
  overflow: initial;
  line-height: inherit;
  word-wrap: normal;
  background-color: transparent;
  border: 0;
  white-space: break-spaces;
}

.markdown-body pre code:before,
.markdown-body pre code:after {
  content: normal;
}

.markdown-body kbd {
  display: inline-block;
  padding: 3px 5px;
  font-size: 11px;
  line-height: 10px;
  color: #555;
  vertical-align: middle;
  background-color: #fcfcfc;
  border: solid 1px #ccc;
  border-bottom-color: #bbb;
  /* border-radius: 3px; */
  box-shadow: inset 0 -1px 0 #bbb;
}

.markdown-body .pl-c {
  color: #969896;
}

.markdown-body .pl-c1,
.markdown-body .pl-s .pl-v {
  color: #0086b3;
}

.markdown-body .pl-e,
.markdown-body .pl-en {
  color: #795da3;
}

.markdown-body .pl-s .pl-s1,
.markdown-body .pl-smi {
  color: #333;
}

.markdown-body .pl-ent {
  color: #63a35c;
}

.markdown-body .pl-k {
  color: #a71d5d;
}

.markdown-body .pl-pds,
.markdown-body .pl-s,
.markdown-body .pl-s .pl-pse .pl-s1,
.markdown-body .pl-sr,
.markdown-body .pl-sr .pl-cce,
.markdown-body .pl-sr .pl-sra,
.markdown-body .pl-sr .pl-sre {
  color: #183691;
}

.markdown-body .pl-v {
  color: #ed6a43;
}

.markdown-body .pl-id {
  color: #b52a1d;
}

.markdown-body .pl-ii {
  background-color: #b52a1d;
  color: #f8f8f8;
}

.markdown-body .pl-sr .pl-cce {
  color: #63a35c;
  font-weight: bold;
}

.markdown-body .pl-ml {
  color: #693a17;
}

.markdown-body .pl-mh,
.markdown-body .pl-mh .pl-en,
.markdown-body .pl-ms {
  color: #1d3e81;
  font-weight: bold;
}

.markdown-body .pl-mq {
  color: #008080;
}

.markdown-body .pl-mi {
  color: #333;
  font-style: italic;
}

.markdown-body .pl-mb {
  color: #333;
  font-weight: bold;
}

.markdown-body .pl-md {
  background-color: #ffecec;
  color: #bd2c00;
}

.markdown-body .pl-mi1 {
  background-color: #eaffea;
  color: #55a532;
}

.markdown-body .pl-mdr {
  color: #795da3;
  font-weight: bold;
}

.markdown-body .pl-mo {
  color: #1d3e81;
}

.markdown-body kbd {
  display: inline-block;
  padding: 3px 5px;
  font: 11px Consolas, "Liberation Mono", Menlo, Courier, monospace;
  line-height: 10px;
  color: #555;
  vertical-align: middle;
  background-color: #fcfcfc;
  border: solid 1px #ccc;
  border-bottom-color: #bbb;
  /* border-radius: 3px; */
  box-shadow: inset 0 -1px 0 #bbb;
}

.markdown-body:before {
  display: table;
  content: "";
}

.markdown-body:after {
  display: table;
  clear: both;
  content: "";
}

.markdown-body .task-list-item {
  list-style-type: none;
}

.markdown-body .task-list-item+.task-list-item {
  margin-top: 3px;
}

.markdown-body .task-list-item input {
  margin: 0 0.35em 0.25em -1.6em;
  vertical-align: middle;
}

.markdown-body :checked+.radio-label {
  z-index: 1;
  position: relative;
  border-color: #4078c0;
}

body.rtl .markdown-body blockquote {
  border-left: none;
  border-right: 4px solid rgba(0, 0, 0, 0.38);
}

body.rtl .markdown-body > html > body > table th,
body.rtl .markdown-body > html > body > table td {
  text-align: right;
}

// .component-doc-highlight{
//   background: #f5f5f5;
//   border-radius:16px;
//   padding:16px 20px;
//   margin-bottom:24px;
// }

.component-doc-guideline{
  margin-bottom:40px;
  display:grid;
  grid-template-columns: repeat(2, 1fr);
  gap:40px;
  grid-auto-rows: minmax(300px,auto)
}
.component-doc-guideline-container{
  display:flex;
  gap:12px;
  flex-direction:column;
  width:100%;
}
.component-doc-do-content{
  display:flex; 
  align-items:center; 
  justify-content:center; 
  gap:8px;
  padding:16px;
  border-radius: 20px;
  border: 1px solid #e0e0e0;
  height:300px;
}
.component-doc-do-text-do{
  border-top:4px solid #00B371;
  font-size:1rem;
  color: #00B371;
  font-weight:600;
  line-height: 1.8;
}
.component-doc-do-text-dont{
  border-top:4px solid #EB4221;
  font-size:1rem;
  color: #EB4221;
  font-weight:600;
  line-height: 1.8;
}