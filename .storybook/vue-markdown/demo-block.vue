<template>
  <div class="demo-block">
    <div class="demo-block-tools">
      <a href="javascript:;" class="show-code" @click="toggle">
        {{ visible ? 'Hide Code' : 'Show Code' }}
      </a>
    </div>
    <klk-expand-transition>
      <div class="meta" v-show="visible">
        <slot name="highlight"></slot>
      </div>
    </klk-expand-transition>
    <div class="demo-block-content">
      <slot name="source"></slot>
    </div>
  </div>
</template>

<script>

export default {
  name: 'demo-block',
  props: {
    jsfiddle: {
      type: Object,
      default: () => ({})
    }
  },
  data () {
    return {
      visible: false
    };
  },
  methods: {
    toggle () {
      this.visible = !this.visible;
    },
  }
};
</script>
<style lang="scss">
.demo-block {
  position: relative;
  background-color: white;
  .highlight {
    margin-bottom: 0 !important;
    border-bottom: 1px solid #eee;
  }
  pre.hljs {
    direction: ltr !important;
  }
  .show-code {
    font-size: 13px;
    color: #757575;
    background-color: #f5f5f5;
    text-decoration: none;
    border-radius: 0 20px 0 0;
    // border-bottom-left-radius: 8px;
  }
}
.demo-block-content {
  display: flex;
  margin-top: 26px;
  justify-content: center;
  .source {
    width: 100%;
    background: transparent;
  }
}
.demo-block-tools {
  border-radius: 0 20px 0 0;
  display: block;
  position: absolute;
  right: 0;
  top: 0;
  z-index: 10;
  background-color: #f5f5f5;
  font-size: 14px;
  a {
    display: inline-block;
    padding: 6px 12px;
    &, &:hover {
      text-decoration: none;
    }
  }
}
.demo-row {
  display: flex;
  margin: 15px 0;
  &-center {
    justify-content: center;
  }
}
@media (min-width: 600px) {
  .demo-block {
    margin-left: 0;
    margin-right: 0;
  }
  .demo-block-content {
    padding-top: 24px;
    padding-left: 24px;
    padding-right: 24px;
    padding-bottom: 24px;
  }
  .demo-block-tools {
    display: block;
  }
}
@media (max-width: 600px) {
  .demo-block-content {
    padding: 10px;
  }
}
</style>
