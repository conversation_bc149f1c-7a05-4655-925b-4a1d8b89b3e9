<template>
  <div class="vue-markdown">
    <div class="vue-markdown-main">
      <!-- content -->
      <div class="vue-markdown-content">
        <slot></slot>
      </div>
      <!-- side menu -->
      <div class="vue-markdown-side">
        <Outline></Outline>
      </div>
    </div>
    <!-- footer -->
    <div class="vue-markdown-footer">
      <!-- <div id="disqus_thread"></div> -->
    </div>
  </div>
</template>

<script>
import Vue from 'vue';
import QRCode from './qrcode';
import DemoBlock from './demo-block.vue';
import Outline from './outline.vue';
import Colors from './colors';
// import ColorPalette from './color-palette.vue';
Vue.component(QRCode.name, QRCode);
Vue.component(DemoBlock.name, DemoBlock);
// Vue.component(ColorPalette.name, ColorPalette);
Vue.component(Colors.name, Colors);

let disqus, disqusCount;
function addDisqus() {
  if (disqus && typeof DISQUS !== 'undefined') {
    DISQUS.reset({
      reload: true,
    });
    return;
  }
  const d = document, s = d.createElement('script'), c = d.createElement('script');
  s.src = 'https://https-storybook-klook-io.disqus.com/embed.js';
  s.setAttribute('data-timestamp', +new Date());
  (d.head || d.body).appendChild(s);
  disqus = s;
  // count
  c.id = 'dsq-count-scr';
  c.async = true;
  c.src = '//https-storybook-klook-io.disqus.com/count.js';
  (d.head || d.body).appendChild(c);
  disqusCount = c;
}

function removeDisqus () {
  if (disqus && disqus.parentNode) {
    disqus.parentNode.removeChild(disqus);
  }
  if (disqusCount && disqusCount.parentNode) {
    disqusCount.parentNode.removeChild(disqusCount);
  }
}

export default {
  name: 'vue-markdown',
  components: {
    Outline,
  },
  mounted () {
    // if (location.href.includes('https://design.klook.io')) {
    //   addDisqus();
    // }
  },
  beforeDestroy () {
    removeDisqus();
  }
};
</script>

<style lang="scss">
@import './assets/github-markdown.scss';
@import './assets/atom-one-light.css';

body {
  font-family: Poppins, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Droid Sans,Helvetica Neue", Helvetica, Arial, "PingFang SC", 微软雅黑, sans-serif;
}

#disqus_thread {
  // max-width: 1000px;
  margin: 0 auto;
  // margin-top: 200px;
}

.vue-markdown {
  padding: 20px;
  // display: flex;
  &-main {
    flex: 1;
    max-width: 100%;
    display: flex;
    flex-direction: row;
  }
  &-content {
    flex: 1;
    z-index: 1;
    max-width: 100%;
    overflow: hidden;
    overflow-x: scroll;
  }
  &-side {
    width: 150px;
    flex-shrink: 0;
    z-index: 0;
  }
  @media screen and (max-width: 768px) {
    & {
      padding: 20px;
    }
    &-side {
      display: none;
    }
    .markdown-body {
      padding: 0 !important;
    }
  }

  .markdown-body {
    width: 100%;
    max-width: 1280px;
    margin: 0 auto;
    padding: 0 20px 96px 20px;
    .demo-block, > html > body > table, .component-doc-top-table {
      // color: #666;
      border-radius: 6px;
      border: 1px solid #e0e0e0;
      .hljs {
        border: none;
      }
    }
    .hljs {
      border: 1px solid #eee;
    }
    .demo-block {
      padding: 20px;
      margin-bottom: 20px;
      border-radius: 20px;
      // text-align: center;
    }
  }
}
</style>
