<template>
  <div class="md-outline" v-if="!isMobile && visible && menus.length >= 2">
    <ul>
      <li v-for="(m, i) in menus" :key="i" @click="show(i)" :ref="`menus${i}`"
        :class="[m.type, activeIndex === i && 'active']">{{ m.name }}</li>
    </ul>
  </div>
</template>

<script>
function isPc() {
  var uaInfo = typeof navigator !== 'undefined' ? navigator.userAgent : '';
  var agents = ['Android', 'iPhone', 'Windows Phone', 'iPad', 'iPod'];
  var flag = true;
  for (var i = 0; i < agents.length; i++) {
    if (uaInfo.indexOf(agents[i]) > 0) {
      flag = false;
      break;
    }
  }
  return flag;
}

export default {
  data() {
    return {
      menus: [],
      visible: false,
      isMobile: !isPc(),
      activeIndex: -1,
    };
  },
  mounted() {
    this.genMenus();
    this.visible = true;

    window.document.addEventListener('scroll', this.scroll);
    // this.onResize();
    // window.addEventListener('resize', this.onResize);
  },
  beforeDestroy() {
    // window.removeEventListener('resize', this.onResize);
  },
  methods: {
    scroll() {
      const bodyScrollTop =
        document.documentElement.scrollTop || document.body.scrollTop;
      this.menus.forEach((item, index) => {
        if (bodyScrollTop+24 > item.offsetTop) {
          this.activeIndex = index
        }
      })
      const el = this.$refs[`menus${this.activeIndex}`]?.[0]
      el && el.scrollIntoView();
    },
    onResize() {
      this.visible = window.innerWidth > 1300;
    },
    genMenus() {
      const t = document.querySelector('.markdown-body html > body');
      if (!t) return;
      this.menus = Array.from(t.children).filter(c => c.tagName.toLowerCase() === 'h2' || c.tagName.toLowerCase() === 'h3').map(h => {
        return {
          offsetTop: h.offsetTop,
          el: h,
          type: h.tagName,
          name: h.innerText,
        };
      });
    },
    show(i) {
      const t = this.menus[i];
      // t.el.scrollIntoView({ behavior: 'smooth' });
      window.scrollTo({
        top: t.offsetTop,
        behavior: 'smooth'
      })
    }
  },
};
</script>

<style lang="scss" scoped>
.md-outline {
  position: fixed;
  top: 10px;
  width: 150px;

  // border-left: 1px solid #eee;
  ul {
    padding: 0;
    margin: 0;
    max-height: 99vh;
    overflow: auto;

    li {
      font-size: 0.875rem;
      list-style: none;
      padding: 4px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;

      &:hover {
        cursor: pointer;
        text-decoration: underline;
        // background-color: #f5f5f5;
      }

      &:visited {
        color: #ff5b00;
      }

      &.H3 {
        margin-left: 12px;
        font-size: 0.75rem;
      }

      &.active {
        color: #ff5b00;
      }
    }
  }
}
</style>
