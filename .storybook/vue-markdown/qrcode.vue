<template>
  <div class="klook-storybook-qrcode" v-if="isPc">
    <slot></slot>
    <p v-if="isLocalhost" class="qrcode-warning">当前是 localhost 环境，不支持二维码生成，请将 localhost 替换成对应域名或 IP</p>
    <div v-else>
      <canvas ref="qrcode" style="margin-top: 12px"></canvas>
    </div>
  </div>
</template>

<script>
  import QRCode from 'qrcode';

  function isPc () {
    var uaInfo = typeof navigator !== 'undefined' ? navigator.userAgent : '';
    var agents = ['Android', 'iPhone', 'Windows Phone', 'iPad', 'iPod'];
    var flag = true;
    for (var i = 0; i < agents.length; i++) {
      if (uaInfo.indexOf(agents[i]) > 0) {
        flag = false;
        break;
      }
    }
    return flag;
  }

  function isLocalhost () {
    const href = window.location.href;
    return href.includes('localhost')
      || href.includes('127.0.0.1')
      || href.includes('0.0.0.0');
  }

  export default {
    name: 'ks-qrcode',
    data () {
      return {
        isPc: isPc(),
        isLocalhost: isLocalhost(),
      };
    },
    mounted () {
      if (!this.isPc || this.isLocalhost) return;
      let url = window.location.href;
      const [prefix, query] = url.split('?path=/story/');
      url = `${prefix}/iframe.html?id=${query}`;
      QRCode.toCanvas(this.$refs.qrcode, url, { width: 120, }, function (error) {
        if (error) console.error(error)
      });
    }
  };
</script>

<style lang="scss">
.klook-storybook-qrcode {
  .qrcode-warning {
    background-color: #FFF3E0;
    color: #FF9D26;
    padding: 5px 15px;
    border-left: 4px solid #FF9D26;
  }
}
body.rtl .qrcode-warning {
  border-left: none;
  border-right: 4px solid #FF9D26;
}
</style>
