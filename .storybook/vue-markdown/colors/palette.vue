<template>
  <div class="color-palette">
    <h3>{{ name }}</h3>
    <div
      v-for="(item,i) in colors"
      :key="i"
      :class="{
        'color-item': true,
        'bg-transparent': !item.bg
      }"
      :style="{
        color: item.textColor,
        background: item.bg,
        border: item.border ? '1px solid #eee' : 'none'
      }"
    >
      <span class="name">{{ item.key }}</span>
      <span class="value">{{ item.value }}</span>
    </div>
  </div>
</template>

<script>
import { contrastTextColor } from './calTextColor';

export default {
  props: {
    name: String,
    palette: Object
  },
  computed: {
    colors () {
      if (!this.palette) return [];
      return Object.keys(this.palette).map(key => {
        const [, group, name] = key.split('-');
        const value = this.palette[key];
        const textColor = value.match(/^#/) ? contrastTextColor(value) : '#333';
        const bg = value.match(/rgba\((255,\s?){3}0\)/) ? '' : value;
        const border = value.match(/^(#fff|#ffffff|white)$/);
        return { group, name, key, value, textColor, bg, border };
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.color-palette {
  margin-bottom: 40px;
}
.color-palette {
  display: flex;
  flex-direction: column;
}
.color-item {
  height: 50px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
}
.bg-transparent {
  background: linear-gradient(45deg,rgba(43,40,38,.1) 25%,transparent 0),linear-gradient(-45deg,rgba(43,40,38,.1) 25%,transparent 0),linear-gradient(45deg,transparent 75%,rgba(43,40,38,.1) 0),linear-gradient(-45deg,transparent 75%,rgba(43,40,38,.1) 0);
  background-size: 12px 12px;
  background-position: 0 0,0 6px,6px -6px,-6px 0
}
</style>
