<template>
  <div class="klook-color-design">
    <ColorPalette
      v-for="name in paletteNames"
      :key="name"
      :palette="palettes[name]"
      :name="name"
    ></ColorPalette>
  </div>
</template>

<script>
import ColorPalette from './palette.vue';
import { tokens } from '../../../packages/klook-ui/src/utils/design-token';

const token = {
  color: {}
};
Object.keys(tokens).filter(key => key.startsWith('$color')).forEach(key => {
  const [first, second/* , third */] = key.split('-');
  if (!first.match(/^\$color/)) return;
  if (!(second/*  && third && /\d{2,3}|(black|white)/.test(third) */)) return;
  if (!token.color[second]) token.color[second] = {};
  token.color[second][key] = tokens[key];
});
// console.log(token);

export default {
  name: 'klook-ui-colors',
  components: {
    ColorPalette,
  },
  data () {
    return {
      palettes: token.color
    };
  },
  computed: {
    paletteNames () {
      return Object.keys(this.palettes);
    }
  }
};
</script>

<style lang="scss">
.klook-color-design {
  h3 {
    margin-bottom: 20px;
  }
  input {
    margin-bottom: 20px;
    border-radius: 2px;
    outline: none;
    border: 1px solid #eee;
    padding: 5px 10px;
  }
}
</style>
